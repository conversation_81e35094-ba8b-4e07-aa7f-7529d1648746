<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 20.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 150 150" style="enable-background:new 0 0 150 150;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#BFCEFF;}
	.st1{font-family:'MyriadPro-Regular';}
	.st2{font-size:9px;}
</style>
<g>
	<g>
		<text transform="matrix(1 0 0 1 74 10)" class="st0 st1 st2">0</text>
	</g>
	<g>
		<text transform="matrix(1 0 0 1 92 11)" class="st0 st1 st2">1</text>
	</g>
	<g>
		<text transform="matrix(1 0 0 1 108 17)" class="st0 st1 st2">2</text>
	</g>
	<g>
		<text transform="matrix(1 0 0 1 121 27)" class="st0 st1 st2">3</text>
	</g>
	<g>
		<text transform="matrix(1 0 0 1 133 43)" class="st0 st1 st2">4</text>
	</g>
	<g>
		<text transform="matrix(1 0 0 1 139 59)" class="st0 st1 st2">5</text>
	</g>
	<g>
		<text transform="matrix(1 0 0 1 142 76)" class="st0 st1 st2">6</text>
	</g>
	<g>
		<text transform="matrix(1 0 0 1 140 95)" class="st0 st1 st2">7</text>
	</g>
	<g>
		<text transform="matrix(1 0 0 1 134 110)" class="st0 st1 st2">8</text>
	</g>
	<g>
		<text transform="matrix(1 0 0 1 122 125)" class="st0 st1 st2">9</text>
	</g>
	<g>
		<text transform="matrix(1 0 0 1 107 137)" class="st0 st1 st2">10</text>
	</g>
	<g>
		<text transform="matrix(1 0 0 1 91 144)" class="st0 st1 st2">11</text>
	</g>
	<g>
		<text transform="matrix(1 0 0 1 72 146)" class="st0 st1 st2">12</text>
	</g>
	<g>
		<text transform="matrix(1 0 0 1 54 145)" class="st0 st1 st2">13</text>
	</g>
	<g>
		<text transform="matrix(1 0 0 1 36 138)" class="st0 st1 st2">14</text>
	</g>
	<g>
		<text transform="matrix(1 0 0 1 21 126)" class="st0 st1 st2">15</text>
	</g>
	<g>
		<text transform="matrix(1 0 0 1 10 112)" class="st0 st1 st2">16</text>
	</g>
	<g>
		<text transform="matrix(1 0 0 1 4 95)" class="st0 st1 st2">17</text>
	</g>
	<g>
		<text transform="matrix(1 0 0 1 2 78)" class="st0 st1 st2">18</text>
	</g>
	<g>
		<text transform="matrix(1 0 0 1 4 59)" class="st0 st1 st2">19</text>
	</g>
	<g>
		<text transform="matrix(1 0 0 1 11 43)" class="st0 st1 st2">20</text>
	</g>
	<g>
		<text transform="matrix(1 0 0 1 23 27)" class="st0 st1 st2">21</text>
	</g>
	<g>
		<text transform="matrix(1 0 0 1 39 17)" class="st0 st1 st2">22</text>
	</g>
	<g>
		<text transform="matrix(1 0 0 1 55 10)" class="st0 st1 st2">23</text>
	</g>
</g>
</svg>
