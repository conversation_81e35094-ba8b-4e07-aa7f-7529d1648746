body {
	margin: 0;
	padding: 0;
	font-family: "Microsoft Yahei", "Helvetica Neue", Helvetica, Arial, sans-serif;
	color: #FFFFFF;
	cursor: pointer;
	background-color: #060922;
}

ul {
	list-style: none;
	margin: 0;
	padding: 0;
}

li {
	display: inline-block;
}
#loading_msk{
    opacity: 0;
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    z-index: 999999;
}
#loading_msk .spinner{
    position: relative;
    margin: 0 auto;
    margin-top: 350px;
    width: 60px;
    height: 60px;
    background: url(../img/loading_o.svg) no-repeat center center;
    background-size: 60px 60px;
}
/* animation rotate 360 */
@-webkit-keyframes rotate360
{
　0% {
    -webkit-transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
}
}
@-moz-keyframes rotate360
{
　0% {
    -moz-transform: rotate(0deg);
}
100% {
    -moz-transform: rotate(360deg);
}
}
@-ms-keyframes rotate360
{
　0% {
    -ms-transform: rotate(0deg);
}
100% {
    -ms-transform: rotate(360deg);
}
}
@keyframes rotate360
{
　0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.spinner_animate{
    -webkit-animation:rotate360 2s linear infinite;
    -moz-animation:rotate360 2s linear infinite;
    -ms-animation:rotate360 2s linear infinite;
    animation:rotate360 2s linear infinite;
    pointer-events: none;
}
.fl {
	float: left;
}

.fr {
	float: right;
}

.fs9 {
	font-size: 9px;
}

.fs10 {
	font-size: 10px;
}

.fs11 {
	font-size: 10px;
}

.fs12 {
	font-size: 12px;
}

.fs16 {
	font-size: 16px;
}

.fs18 {
	font-size: 18px;
}

.fs20 {
	font-size: 20px;
}

.bold {
	font-weight: bold;
}

.green {
	color: #AFE230;
}

.blue1 {
	color: #72BCFF;
}

.blue2 {
	color: #3E6899;
}

.up_arr {
	display: inline-block;
	height: 16px;
	width: 10px;
	background: url(../img/arr_up2.png) no-repeat center;
}

.down_arr {
	display: inline-block;
	height: 16px;
	width: 10px;
	background: url(../img/arr_down2.png) no-repeat center;
}

.tipText{
	height: 30px;
	line-height: 30px;
	margin-right: 10px;
}

@font-face {
  font-family: 'WzpoGlyph';
  src:
    url('../fonts/WzpoGlyph.ttf?4zlt2d') format('truetype'),
    url('../fonts/WzpoGlyph.woff?4zlt2d') format('woff'),
    url('../fonts/WzpoGlyph.svg?4zlt2d#WzpoGlyph') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'WzpoGlyph' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-e700_plus:before {
  content: "\e700";
}
.icon-e732_cross:before {
  content: "\e732";
}
.icon-e733_check:before {
  content: "\e733";
}
.icon-e730_check:before {
  content: "\e730";
}
.icon-e731_bin:before {
  content: "\e731";
}
.icon-e719_search:before {
  content: "\e719";
}
.icon-e617_dust1:before {
  content: "\e617";
}
.icon-e618_dust2:before {
  content: "\e618";
}
.icon-e619_dust3:before {
  content: "\e619";
}
.icon-e620_hail:before {
  content: "\e620";
}
.icon-e615_fog:before {
  content: "\e615";
}
.icon-e603_cloudy_night:before {
  content: "\e603";
}
.icon-e604_gloomy:before {
  content: "\e604";
}
.icon-e606_rain2:before {
  content: "\e606";
}
.icon-e612_wind:before {
  content: "\e612";
}
.icon-e613_haze:before {
  content: "\e613";
}
.icon-e614_haze_night:before {
  content: "\e614";
}
.icon-e600_sunny:before {
  content: "\e600";
}
.icon-e601_sunny_night:before {
  content: "\e601";
}
.icon-e602_cloudy:before {
  content: "\e602";
}
.icon-e605_rain1:before {
  content: "\e605";
}
.icon-e607_rain3:before {
  content: "\e607";
}
.icon-e608_snow1:before {
  content: "\e608";
}
.icon-e609_snow2:before {
  content: "\e609";
}
.icon-e610_snow3:before {
  content: "\e610";
}
.icon-e611_umbrella:before {
  content: "\e611";
}

