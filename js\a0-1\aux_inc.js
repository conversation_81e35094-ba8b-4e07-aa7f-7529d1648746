/** 辅营 */
(function () {

  dfd.done(function () {
    if (!hasAllCompanyPermission()) {
      $(".AUX_INC .detail").remove();
      return;
    }
  })

  $(".AUX_INC .detail").on("click", function () {
    $("#pop_aux_inc").removeClass("hide");
    $(".windowMask").removeClass("hide");
    var dateId = $(".zongshouru").attr("dateKey");
    var dateType = getDateType();
    if (window.auxIncWin == null) {
      initauxIncWin(dateType, dateId);
    } else {
      window.auxIncWin.refreshView(dateType, dateId);
    }
  });
  function initauxIncWin (dateType, dateId) {
    var page = new Vue({
      el: '.aux-inc-win-body',
      template: $("#auxInc_template").html(),
      data: function () {
        return {
          "dateType": "D",
          "weeks": weeks,
          showWeekList: false,
          selectedWeek: null,
          weekCmpActive: false,
          showFirstTab: true,
          companyDataList2: [],
          companyDataList: []
        }
      },
      mounted: function () {
        var me = this;
        if (this.weeks.length > 1) {
          this.selectedWeek = weeks[1]
        }
        $(me.$refs["datetimepicker_D"]).datetimepicker({
          defaultDate: moment().subtract(1, "days")._d,
          format: "YYYY/MM/DD",
          sideBySide: true,
          maxDate: moment().subtract(1, "days")._d,
          widgetPositioning: {
            horizontal: 'right'
          },
          locale: 'zh-cn'
        });
        //月
        $(me.$refs['datetimepicker_M']).datetimepicker({
          defaultDate: new Date(),
          format: "YYYY/MM",
          viewMode: 'months',
          sideBySide: true,
          maxDate: new Date(),
          widgetPositioning: {
            horizontal: 'right'
          },
          locale: 'zh-cn'
        });
        //年
        $(me.$refs['datetimepicker_Y']).datetimepicker({
          defaultDate: new Date(),
          format: "YYYY",
          viewMode: 'years',
          sideBySide: true,
          maxDate: new Date(),
          widgetPositioning: {
            horizontal: 'right'
          },
          locale: 'zh-cn'
        });

        me.setDatePickerValue(dateType, dateId);
        //先查数据,再做下面事件监听,不然可能会触发dp.change事件,多做一次请求
        me.queryData(me.getDate());

        $(me.$refs["datetimepicker_D"]).on("dp.change", function (e) {
          me.queryData(e.date._d);

        });
        $(me.$refs['datetimepicker_M']).on("dp.change", function (e) {
          me.queryData(e.date._d);
        });
        $(me.$refs['datetimepicker_Y']).on("dp.change", function (e) {
          me.queryData(e.date._d);
        });
        me.mounted = true;
        me.hackEChartDom();
      },
      methods: {
        hackEChartDom () {
          var me = this;
          var scale = 1 / pageZoomScale;
          $(me.$el).find(".chartblock").css('zoom', scale);
          $(me.$el).find(".chartblock").css('-moz-transform', 'scale(' + scale + ',' + scale + ')');
          $(me.$el).find(".chartblock").css('-moz-transform-origin', 'left top');
          $(me.$el).find(".chartblock").each(function (index, el) {
            var width = $(this).width();
            var height = $(this).height();
            $(this).css('width', width * pageZoomScale + 'px');
            $(this).css('height', height * pageZoomScale + 'px');
          });
        },
        setDatePickerValue (dateType, dateId) {
          var me = this;
          if (dateType != 'L') {
            $(me.$refs[`datetimepicker_${dateType}`]).data("DateTimePicker").date(me.getDateStr(dateType, dateId));
          } else {
            var selectedWeek = me.weeks.find(v => { return v.DATE_ID == dateId });
            if (me.selectedWeek != selectedWeek) {
              me.selectedWeek = selectedWeek;
              //改变才数据,且在mounted 之后,
              if (me.mounted) {
                me.queryData(selectedWeek);
              }
            }
          }
        },
        refreshView (dateType, dateId) {
          var me = this;
          if (me.dateType != dateType) {
            me.switchDateType(dateType);
          } else {
            me.setDatePickerValue(dateType, dateId);
          }

        },
        getDateStr (dateType, dateId) {
          if (dateType == 'D') {
            return moment(dateId, 'YYYYMMDD').format('YYYY/MM/DD');
          } else if (dateType == 'M') {
            return moment(dateId, 'YYYYMM').format('YYYY/MM');;
          } else if (dateType == 'Y') {
            return dateId;
          }
        },
        getDate () {
          var me = this;
          var dateType = this.dateType;
          if (dateType == 'D') {
            return $(me.$refs['datetimepicker_D']).data("DateTimePicker").date().startOf("day")._d;
          } else if (dateType == 'L') {
            return this.selectedWeek;
          } else if (dateType == 'M') {
            return $(me.$refs['datetimepicker_M']).data("DateTimePicker").date().startOf("day")._d
          } else if (dateType == 'Y') {
            return $(me.$refs['datetimepicker_Y']).data("DateTimePicker").date().startOf("day")._d
          }
        },
        getWeekDesc () {
          return this.selectedWeek ? this.selectedWeek.DATE_DESC_XS : ''
        },
        onWeekMouseOut () {
          this.weekCmpActive = false;
          setTimeout(this.validActive, 300)
        },
        onWeekMouseOver () {
          this.weekCmpActive = true;
        },
        validActive () {
          if (!this.weekCmpActive) {
            this.showWeekList = false;
          }
        },
        dropWeekList () {
          this.showWeekList = true;
        },
        onWeekChange (week) {
          this.selectedWeek = week;
          this.showWeekList = false;
          this.queryData(week);
        },

        getWeekLabel: function (item) {
          if (item) {
            var week = item.DATE_ID.substring(5, 7);
            var year = item.DATE_ID.substring(0, 4);
            return `${year}年第${week}周`;
          }
        },
        closeWin: function () {
          $("#pop_aux_inc").addClass("hide");
          $(".windowMask").addClass("hide");
        },
        switchDateType (dateType) {
          this.dateType = dateType;
          this.queryData(this.getDate());
        },
        isSameDate (d1, d2) {
          if (!d1) {
            return false;
          }
          return moment(d1).diff(moment(d2)) == 0;
        },

        getIncAvg (companyData) {
          if (companyData['TRV_NUM'] && companyData['TRV_NUM'].length > 0) {
            var trvNum = companyData['TRV_NUM'][0].KPI_VALUE;
            return toFixed(companyData['AUX_INC'][0].KPI_VALUE * 10000 / trvNum, 1)
          }
          return '-';

        },
        queryData (date) {
          var me = this;
          var params = {
            "DATE_ID": getDateId(date, this.dateType),
            "DATE_TYPE": this.dateType,
            "COMP_CODE": getAllSubCompany().join(","),
            "KPI_CODE": "AUX_INC,TRV_NUM"
          };

          eking.ui.loading.show();

          $.ajax({
            type: 'post',
            url: "/bi/query/getfaccomkpi",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(params),
            success: function (res) {
              eking.ui.loading.hide();
              var data = res.data;
              var companyDataList = [];
              for (var p in data) {
                var companyData = data[p];
                if (companyData['AUX_INC']) {
                  companyDataList.push({
                    companyCode: p,
                    companyName: companyCode2Nameabbr[p],
                    inc: toFixed(companyData['AUX_INC'][0].KPI_VALUE, 1),
                    incTB: companyData['AUX_INC'][0].KPI_RATIO_TQ,
                    incAvg: me.getIncAvg(companyData),
                  })
                }

              }

              companyDataList.sort(function (a, b) {
                return getCompanyIndex(a.companyCode) - getCompanyIndex(b.companyCode)
              });

              companyDataList.forEach((i, index) => {
                i.color = colors[index];
              })
              me.companyDataList = companyDataList;

              me.drawPie();

            }
          });


          // '10213','AUX_INC_BAG','行李收入'
          // '10218','AUX_INC_UPGRADE','升舱收入',
          // '10240','AUX_INC_CHANGE','改期收入',
          //  '10250','AUX_INC_REFUND','退票收入'

          var params = {
            "DATE_ID": getDateId(date, this.dateType),
            "DATE_TYPE": this.dateType,
            "COMP_CODE": getAllSubCompany().join(","),
            "KPI_CODE": "AUX_INC_BAG,AUX_INC_UPGRADE,AUX_INC_CHANGE,AUX_INC_REFUND",
            "IS_ONE_DATE": true //一个日期的话，输出格式不需要日期只要值的特殊化参数
          };

          $.ajax({
            type: 'post',
            url: "/bi/query/getdatefaccomkpiex?auxinc_detail",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(params),
            success: function (res) {
              var data = res.data;
              if (data == null || data == '') {
                me.companyDataList2 = [];
                me.drawBar();
                return;
              }
              var companyDataList = [];
              for (var p in data) {
                var companyData = data[p];

                companyDataList.push({
                  companyCode: p,
                  companyName: companyCode2Nameabbr[p],
                  '行李收入': toFixed(me.getAuxKpiVaue(companyData['AUX_INC_BAG']), 1),
                  '地面升舱': toFixed(me.getAuxKpiVaue(companyData['AUX_INC_UPGRADE']), 1),
                  '改期收入': toFixed(me.getAuxKpiVaue(companyData['AUX_INC_CHANGE']), 1),
                  '退票收入': toFixed(me.getAuxKpiVaue(companyData['AUX_INC_REFUND']), 1)
                })


              }

              companyDataList.sort(function (a, b) {
                return getCompanyIndex(a.companyCode) - getCompanyIndex(b.companyCode)
              });
              me.companyDataList2 = companyDataList;
              me.drawBar();
            }
          });
        },

        getAuxKpiVaue (kpi) {
          if (kpi) {
            return kpi.KPI_VALUE
          }
          return 0;
        },

        getTqRadio (value) {
          if (value < 0) {
            return toFixed(value * 100, 2) + '%' + "↓"
          } else if (value > 0) {
            return toFixed(value * 100, 2) + '%' + "↑"
          } else {
            return "0.00%"
          }
        },
        drawPie () {
          var me = this;
          echarts.dispose(this.$refs.edom);
          let chart = echarts.init(this.$refs.edom, null, { renderer: 'svg' });

          var option = {
            dataset: [
              { source: me.companyDataList }
            ],
            tooltip: {
              trigger: 'item',
              position: 'inside',
              formatter: function (param) {
                return `${param.data.companyName}:${param.data.inc}`
              }
            },
            color: colors,
            series: [{
              name: '',
              type: 'pie',
              radius: ['65%', '90%'],
              center: ['50%', '50%'],
              dimensions: ["companyName", "inc"],
              clockwise: false,
              label: {
                normal: {
                  show: false,
                }
              },
              labelLine: {
                normal: {
                  show: false,
                  smooth: 0.2,
                  length: 0,
                  length2: 0
                }
              },
              animationType: 'scale',
              animationEasing: 'elasticOut',
              animationDelay: function (idx) {
                return Math.random() * 200;
              }
            }]
          };
          chart.setOption(option);
          chart.resize();
        },
        drawBar () {
          var me = this;
          echarts.dispose(this.$refs.edom2);
          let chart = echarts.init(this.$refs.edom2, null, { renderer: 'svg' });
          chart.setOption({
            title: {
              text: "四大辅营产品收入对比",
              left: "center",
              top: "top",
              textStyle: {
                fontSize: getChartFontSize(20),
                color: '#ffffff',
                fontWeight: 'normal',
                height: getChartFontSize(50),
                lineHeight: getChartFontSize(50)
              }
            },
            dataset: [
              { source: me.companyDataList2 }
            ],
            legend: {
              textStyle: { color: '#44a3f4' },
              top: getChartFontSize(50)
            },
            color: colors,
            grid: {
              left: '5%', // 与容器左侧的距离
              right: '5%', // 与容器右侧的距离
              top: '25%', // 与容器顶部的距离
              bottom: '20%', // 与容器底部的距离,
              containLabel: true
            },
            tooltip: {
              trigger: "axis",
              axisPointer: {
                type: "cross",
                crossStyle: {
                  color: "#00a2ff"
                }
              }
            },
            dataZoom: {
              type: 'slider',
              show: true,
              start: 0,
              end: 35,
              height: 20,
              borderColor: 'transparent',
              backgroundColor: '#009fec33',
              fillerColor: '#009fec66',
              handleIcon: 'M0 0 L0 5 L5 5 L5 0 Z',
              handleColor: '#26bafd',
              handleSize: 22
            },
            yAxis: [{
              type: 'value',
              name: '万元',
              nameTextStyle: {
                color: '#41a8ff',
                fontSize: getChartFontSize(16)
              },
              axisLabel: {
                textStyle: {
                  color: '#41a8ff',
                  fontSize: getChartFontSize(16)
                },
                formatter: '{value}'
              },
              axisLine: {
                lineStyle: {
                  color: 'rgba(0,0,0,0)'
                }
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
              },
            }],
            xAxis: [
              {
                type: "category",
                axisLabel: {
                  color: "#00a2ff",
                  fontSize: getChartFontSize(16)
                },
                color: "#00a2ff",
                axisLine: {
                  lineStyle: {
                    color: "#00a2ff",
                    width: "2"
                  }
                }
              }
            ],
            series: [
              {
                type: "bar",
                label: {
                  show: true,
                  color: '#FFFFFF',
                  position: 'top'
                },
                dimensions: ["companyName", "行李收入"],
                name: "行李收入",
                datasetIndex: 0

              },
              {
                type: "bar",
                label: {
                  show: true,
                  color: '#FFFFFF',
                  position: 'top'
                },
                dimensions: ["companyName", '地面升舱'],
                name: "地面升舱",
                datasetIndex: 0
              }, {
                type: "bar",
                label: {
                  show: true,
                  color: '#FFFFFF',
                  position: 'top'
                },
                dimensions: ["companyName", '改期收入'],
                name: "改期收入",
                datasetIndex: 0
              }, {
                type: "bar",
                label: {
                  show: true,
                  color: '#FFFFFF',
                  position: 'top'
                },
                dimensions: ["companyName", '退票收入'],
                name: "退票收入",
                datasetIndex: 0
              }
            ]
          });
          chart.resize();
        }

      }
    });

    window.auxIncWin = page;

  }





})()