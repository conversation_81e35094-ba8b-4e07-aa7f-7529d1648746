<svg id="VS" xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 36 36">
  <metadata><?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c138 79.159824, 2016/09/14-01:09:01        ">
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
      <rdf:Description rdf:about=""/>
   </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?></metadata>
<defs>
    <style>
      .cls-1 {
        fill: #eb6100;
      }

      .cls-1, .cls-2 {
        fill-rule: evenodd;
      }

      .cls-2 {
        fill: #00a9ff;
      }
    </style>
  </defs>
  <path id="形状_4" data-name="形状 4" class="cls-1" d="M1260,908.772a5.334,5.334,0,0,1-.93,3.4,2.343,2.343,0,0,1-1.17.906,1.464,1.464,0,0,1-1.63-.227,3.489,3.489,0,0,1-.94-0.9,9.521,9.521,0,0,0-2.1-1.132c-0.23,0-.23-0.227-0.46,0a0.865,0.865,0,0,0-.7.226l-0.24.226c0,0.227,0,.227-0.23.227-0.47.226-.7,0.453-1.17,0.679a3.038,3.038,0,0,0-.93,2.49,1.884,1.884,0,0,0,1.17,1.132,0.806,0.806,0,0,1,.46.227c0.94,0,1.64.226,2.57,0.226h1.17a4.7,4.7,0,0,1,3.73,1.585,2.014,2.014,0,0,1,.7,1.584,0.822,0.822,0,0,1-.23.679v0.68a0.226,0.226,0,0,0-.24.226c-0.23.226-.46,0.679-0.7,0.906l-0.23.226a7.293,7.293,0,0,1-2.1,1.585c-0.23.226-.47,0.226-0.7,0.452h-0.7a2.343,2.343,0,0,0-1.17.906,0.223,0.223,0,0,0-.23.226,1.238,1.238,0,0,1-.7.453h-0.23l0.93-.226h-0.23c-0.94.453-1.87,1.132-2.8,1.584-0.47.227-1.17,0.68-1.64,0.906-0.23,0-.23.226-0.46,0.226a1.825,1.825,0,0,1-1.17.453c-0.7,0-1.17.227-1.87,0.227h-0.46l0.23-.227a1.468,1.468,0,0,0,1.4-.226c0.23-.227.7-0.227,0.93-0.453l0.24-.226h-0.47a2.521,2.521,0,0,1-1.17.452c-0.23.227-.7,0.227-0.93,0.453a5.2,5.2,0,0,0-.93.227,1.172,1.172,0,0,1-.94.226,3.425,3.425,0,0,0-1.4.453c-0.23.226-.7,0.226-0.93,0.452l0.7-.452a2.173,2.173,0,0,0-1.17.226c-1.16.453-2.33,1.132-3.5,1.585-0.46.226-1.16,0.679-1.63,0.905a1.238,1.238,0,0,1-.7.453h-0.7c-0.23,0-.7.226-0.93,0.226H1232a3.256,3.256,0,0,1,.93-0.679c0.47-.226,1.17-0.452,1.64-0.679a0.223,0.223,0,0,0,.23-0.226,2.513,2.513,0,0,0-1.17.453,5.24,5.24,0,0,1,1.17-.68c0.47-.226.93-0.226,1.4-0.452a3.378,3.378,0,0,1,1.4-.453c-0.23,0-.23.226-0.47,0.226a0.806,0.806,0,0,0-.46.227h-0.24c-0.23,0,0,0,0,.226a1.457,1.457,0,0,0,.94-0.226,1.275,1.275,0,0,0,.7-0.453,0.461,0.461,0,0,1,.46-0.227s0.24,0,0-.226V929.6a0.231,0.231,0,0,0,.24-0.227,2.487,2.487,0,0,1,1.16-.452c0.24,0,.47-0.227.47,0h0.23a0.862,0.862,0,0,0,.7-0.227c0.24-.226.7-0.226,0.94-0.453,0.23,0,.23-0.226.46-0.226l0.24-.226h-0.47a1.783,1.783,0,0,0-.93.452,3.542,3.542,0,0,1-.94.453h-0.46c0.23-.226.7-0.226,0.93-0.453s0.47-.226.7-0.452l0.93-.906a1.172,1.172,0,0,0,.94-0.226l0.23-.227a1.424,1.424,0,0,0-.93.227,3.574,3.574,0,0,0-1.64.679,0.859,0.859,0,0,0-.46.453h-0.24c-0.23,0-.23,0-0.23.226h-0.23c-0.94.226-1.87,0.679-2.8,0.906h-0.24c0.7-.68,1.64-1.132,2.34-1.812a0.665,0.665,0,0,1,.7-0.226h0.7a5.258,5.258,0,0,0,1.86-.226,0.883,0.883,0,0,1,.7-0.227,0.985,0.985,0,0,0,.94-0.679v-0.226h-0.24c0.24-.227.47-0.227,0.7-0.453,0.24,0,.24-0.226.47-0.226a8.908,8.908,0,0,1,3.03-2.038c0.24-.226.7-0.226,0.94-0.453,0.23,0,.23-0.226.46-0.226l1.4-.679c0.7-.453,1.64-0.906,2.34-1.359a5.448,5.448,0,0,0,1.16-1.131c0.24-.227,0-0.453-0.23-0.453a0.893,0.893,0,0,1-.47-0.453,1.429,1.429,0,0,0-.93-0.226,7.62,7.62,0,0,0-3.03-.453,5.291,5.291,0,0,1-1.87-.227,2.336,2.336,0,0,1-1.17-.9l-0.23-.226a1.733,1.733,0,0,1-.47-0.906,1.423,1.423,0,0,1,0-1.358,5.607,5.607,0,0,1,1.4-3.17l1.4-1.358a13.32,13.32,0,0,1,1.64-.906,15.961,15.961,0,0,1,1.86-.679,3.337,3.337,0,0,1,2.1,0,1.818,1.818,0,0,1,.94.227h1.16c0.47-.227.94-0.227,1.4-0.453,0.24-.226.47-0.226,0.7-0.453v0.227l0.94-.68c0,0.227.23,0.227,0.23,0.453v1.811Zm0,0" transform="translate(-1228 -897)"/>
  <path id="形状_5" data-name="形状 5" class="cls-2" d="M1251.37,897.906h0a1.722,1.722,0,0,1-.47,1.132,10.89,10.89,0,0,1-.7,1.585l0.93-1.132a1.343,1.343,0,0,1-.23.9c-0.23.227-.23,0.453-0.47,0.679V901.3a0.824,0.824,0,0,1-.23.679v0.226l0.23-.226a0.846,0.846,0,0,0,.47-0.453,0.224,0.224,0,0,0,.23-0.226h0.24v0.679a1.657,1.657,0,0,0-.47.9,0.224,0.224,0,0,1-.23.227c0,0.226-.24.452-0.24,0.679a18.516,18.516,0,0,1-1.4,2.264,25.752,25.752,0,0,0-2.33,4.3c-0.7,2.037-1.4,4.075-2.1,6.339-0.23.9-.23,1.811-0.47,2.943v2.037a0.805,0.805,0,0,1-.93.679h-1.17a1.17,1.17,0,0,0-.46-0.9c-0.24-.227-0.7-0.453-0.94-0.679a1.07,1.07,0,0,1-.46-1.132V918.96a2.729,2.729,0,0,0-.24-1.359l-0.7-2.264,0.47,1.132v-0.452a6.372,6.372,0,0,0-.47-2.264,9.2,9.2,0,0,0-1.16-2.038c0-.226-0.24-0.453-0.24-0.679v0.453a0.785,0.785,0,0,0-.23-0.453c-0.23-.9-0.7-1.811-0.93-2.49a1.218,1.218,0,0,0-.47-0.679v-0.453a0.851,0.851,0,0,1-.47-0.226c0-.227-0.23-0.227-0.46-0.227a1.3,1.3,0,0,0-.24-0.9V905.6a1.049,1.049,0,0,0,.24-0.906v-0.226c0-.226,0-0.226.23-0.226s0.23-.227.23-0.453a0.443,0.443,0,0,1,.24-0.453l0.23-.226V902.66a0.483,0.483,0,0,1,.47.226,0.772,0.772,0,0,0,.93-0.226c0.23-.227.23-0.227,0.47,0a0.446,0.446,0,0,0,.7,0c0.23,0,.23,0,0.23.226v0.227h0.23a0.777,0.777,0,0,0,.24.452,1.718,1.718,0,0,1,.46,1.132c0,0.68.24,1.132,0.24,1.811,0.23,1.132.7,2.264,0.93,3.4s0.7,2.264.93,3.4v0.226l0.24-.226c0.7-1.811,1.4-3.4,1.86-4.981,0.7-1.584,1.17-3.169,1.87-4.754a5.7,5.7,0,0,1,.93-1.584,0.856,0.856,0,0,0,.47-0.453v0.226l1.4-1.358V901.3l0.23-.227a6.713,6.713,0,0,0,1.17-1.584c0.47-.68.7-1.359,1.17-2.038l0.46-.453v0.227c-0.23.226-.23,0.452-0.46,0.679h0Zm0,0" transform="translate(-1228 -897)"/>
</svg>
