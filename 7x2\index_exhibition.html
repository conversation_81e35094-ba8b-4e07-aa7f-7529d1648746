<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    
    <title>航空运控大屏</title>

    <meta http-equiv="refresh" content="3600">
	<link href="../css/nprogress.css" rel="stylesheet">
	<script src="../js/nprogress.js"></script>
	<script src="../js/babel.min.js"></script>
	<script src="../js/polyfill.min.js"></script>
    <script src="../js/tingyun.js?ver=20211218"></script>

    <link href="../css/bootstrap.css" rel="stylesheet">
    <link href="../css/bootstrap-theme.css" rel="stylesheet">
    <link href="css/common.css?ver=20211218" rel="stylesheet">
    <link href="css/company.css?ver=20211218" rel="stylesheet">

    <script src="../js/jquery-1.11.1.js"></script>
	<script src="../js/jquery-3.1.0.min.js"></script>
	<script src="../js/bootstrap.min.js"></script>
	<script src="../js/echarts.min.js?ver=20211218"></script>
	<script src="../js/echarts-gl.min.js?ver=20211218"></script>
	<script src="../js/json.js"></script>
	<script src="../js/util.js"></script>
    <script src="../js/jquery.powertip.min.js"></script>
    <script src="../js/jquery.nicescroll.min.js"></script>
    <script src="../js/moment.min.js"></script>
    <script src="../js/slider.js"></script>
	
	<!-- <style type="text/css">
		#sider{
		      	height:20px;
			    box-sizing: border-box;
			    position: relative;
			    overflow: hidden;
		      }
		#box{
		position: absolute;
		left: 0;
		height: 20px;
		width: 166px;
		}
		#base_list_unnormal_weather,#base_list_unnormal_weather2{
		display: inline-block;
		}
		#base_list_unnormal_weather,#base_list_unnormal_weather2{
		display: inline-block;
		height: 20px;
		width: 166px;
		}
		
	</style> -->
	
	<!-- <style type="text/css">
      .sider{
      	height: 20px;
	    box-sizing: border-box;
	    position: relative;
	    overflow: hidden;
      }
      .box{
      	position: absolute;
      	left: 0;
      	width: 166px;
      	height: 20px;
      }
      .uls,.uls2{
      	display: inline-block;
      }
      .uls li,.uls2 li{
      	display: inline-block;
      	width: 1666px;
      	height: 20px;
		margin-left: 30px;
      	background: red;
      }
	</style> -->

	
	<!-- <script>
    	$(function(){
    		var i=0;
    		var sizess = $("#base_list_unnormal_weather").length;
    		var sizesspx = sizess*330;
    		var clone = $(".base_list_unnormal_weather").html();
    		$(".base_list_unnormal_weather2").html(clone);
    		var t=setInterval(moveL,30);
    		
  		    //封装的动画函数
    		function moveL(){
    			i++;
    			var sizess = $("#base_list_unnormal_weather").length;
    			if(i>sizesspx){
    				$(".box").css({left:0});
    				i=0
    			}
    			$(".box").css({left:-i+'px'});
    		}
    	})
    </script> -->
	
    <!-- <script>
    	$(function(){
    		var i=0;
    		var sizess = $(".uls li").length;
    		var sizesspx = sizess*330;
    		var clone = $(".uls").html();
    		$(".uls2").html(clone);
    		var t=setInterval(moveL,30);
    		
//  		封装的动画函数
    		function moveL(){
    			i++;
    			var sizess = $(".uls li").length;
    			if(i>sizesspx){
    				$(".box").css({left:0});
    				i=0
    			}
    			$(".box").css({left:-i+'px'});
    		}
    	})
    </script> -->

    
</head>
<body>

<iframe id="login" scrolling="no" width="0" height="0" style="display:none;" ></iframe>
<script src="js/config.js?ver=20211218"></script>
<script type="text/javascript">
	$('body').hide();
</script>

<div class="page-bgimg" style=" -moz-transform-origin:left top; pointer-events: none;">
	<div style="position: absolute; width:678px; height:678px; left: 1035px; top: 110px; " >
		<div id="earth3d" style="width:678px; height: 678px;"></div>
	</div>
</div>


<div class="page-wrapper" style=" -moz-transform-origin:left top; pointer-events: none;">


<div class="earthmask" style="pointer-events: none;"></div>

<div class="earthlight" style="pointer-events: none;"></div>

<div id="map_switch_lb" class="fs15 center lang" lang='lb_ac_loc' style="width:200px;height:22px;left: 1274px;top: 699px;"></div>
<div id="map_switch_btn" style="width: 60px;height: 60px;left: 1343px;top: 725px;cursor:pointer;pointer-events: auto;"><img src="img/earth-small.png" style="width:60px; height:60px;"></div>



<!-- ********************************* -->
<!-- english -->
<!-- ********************************* -->
<div class="logo_txt"></div>
<div id="companycombo" code=""  style="left:618px; top:40px; padding-left:5px; border-radius:4px; border:1px solid #2874c1; cursor:pointer; pointer-events: auto;">
	<div class="box1"></div>
	<div id="companylist"></div>
</div>
<div id="btn_english" class="fs12 blue2 lang" lang='lb_lang' style="width:66px; height:24px; line-height:20px; left:748px; top:40px; padding-left:5px; border-radius:4px; border:1px solid #2874c1; cursor:pointer; pointer-events: auto; background:url(img/arr.png) no-repeat 51px center;"></div>


<div class="fs16 title_tl lang" lang='tit_tl' >
</div>
<div class="config_link"></div>
<div class="fs16 title_tr lang" lang='tit_tr' ></div>
<div class="fs16 title_br lang" lang='tit_br' ></div>

<!-- ********************************* -->
<!-- 航班总量 -->
<!-- ********************************* -->
<canvas id="cvs_flt_count" width="200" height="200" style="left:82px; top:100px; background:rgba(0,0,0,0);"></canvas>

<div class="blue2 fs10" style="width:22px; height:22px; left:103px; top:240px; ">0</div>
<div class="blue2 fs10" style="width:22px; height:22px; left:88px; top:172px; ">20</div>
<div class="blue2 fs10" style="width:22px; height:22px; left:117px; top:115px; ">40</div>
<div class="blue2 fs10" style="width:22px; height:22px; left:222px; top:115px; ">60</div>
<div class="blue2 fs10" style="width:22px; height:22px; left:263px; top:172px; ">80</div>
<div class="blue2 fs10" style="width:32px; height:22px; left:256px; top:240px; ">100</div>

<div class="blue2 left fs16 lang" lang='tit_flt_total' style="text-align:center; width:180px; height:22px; left:94px; top:163px; ">

</div>
<div class="center fs28" style="width:100px; height:22px; left:135px; top:181px; ">
<span id="val_flt_total" class="ffnum"></span><span class="sub blue2 fs10 lang" lang='lb_flts'></span>
</div>

<div class="blue2 center " style="text-align:center; width:180px; height:20px; left:97px; top:225px; ">
<span class="lang" lang='lb_flt_exec' ></span>
<span id="val_flt_exec_rate" class="ffnum fs17"></span>
</div>




<div class="blue2 fs16 lang" lang='lb_domestic_flt' style="width:166px; height:21px; left:88px; top:262px; ">
</div>

<div style="width:80px; height:21px; left:86px; top:284px; ">
<span id="val_flt_total_china" class="ffnum fs17"></span><span class="sub blue2 fs10 lang" lang='lb_flts'></span>
</div>

<div style="width:80px; height:21px; left:89px; top:303px; ">
<span class="blue2 lang" lang='lb_flt_exec'></span><span id="val_flt_exec_rate_china" class="ffnum"></span>
</div>


<div class="blue2 fs16 lang" lang='lb_int_flights' style="width:166px; height:21px; left:216px; top:262px; ">
</div>

<div style="width:80px; height:21px; left:216px; top:284px; ">
<span id="val_flt_total_int" class="ffnum fs17"></span><span class="sub blue2 fs10 lang" lang='lb_flts'></span>
</div>

<div style="width:80px; height:21px; left:216px; top:303px; ">
<span class="blue2 lang" lang='lb_flt_exec'></span><span id="val_flt_exec_rate_int" class="ffnum"></span>
</div>


<!-- ********************************* -->
<!-- 航班正常率 -->
<!-- ********************************* -->
<canvas id="cvs_normal_rate" width="220" height="220" style="left:337px; top:116px; background:rgba(255,0,0,0);"></canvas>

<div class="blue2 fs11" style="width:22px; height:22px; left:355px; top:208px; opacity:0.9; ">25</div>
<div class="blue2 fs11" style="width:22px; height:22px; left:439px; top:134px; opacity:0.9; ">50</div>
<div class="blue2 fs11" style="width:22px; height:22px; left:522px; top:208px; opacity:0.9; ">75</div>
<div class="blue2 fs11" style="width:32px; height:22px; left:504px; top:272px; opacity:0.9; ">100</div>

<div class="center" style="width:100px; height:50px; left:402px; top:197px; ">
<span id="today_normal_rate" class="fs30 ffnum"></span><span class="sub ffnum">%</span>
</div>

<div class="blue2 center lang" lang='lb_normal_rate' style="width:80px; height:21px; left:406px; top:236px; ">
</div>



<!-- ********************************* -->
<!-- 旅客量 -->
<!-- ********************************* -->
<canvas id="cvs_trv_num" width="110" height="110" style="left:647px; top:182px; background:rgba(255,0,0,0);"></canvas>
<div class="" style="background: #034781; width: 86px; height:86px; left:659px; top:194px; overflow: hidden; border-radius: 43px; transform: rotate(0deg); ">
	<div class="div_trv_num" style="background: #00A7FF; width: 86px; height:86px; left:0px; transform: rotate(0deg); "></div>
	<div class="gradient-ball" style="width: 126px; height:126px; top:-40px; right:0px; transform: rotate(0deg); "></div>
	<div class="" style="background: url(img/people3.png) no-repeat center center; width: 86px; height:86px; left:0px; bottom:0px; transform: rotate(0deg); "></div>
</div>

<div class="blue2 fs16 center lang" lang='lb_total_psr' style="width:160px; height:25px; left:624px; top:122px; ">
</div>

<div style="width:160px; height:25px; left:651px; top:144px; ">
<span id="val_trv_num_plan" class="ffnum fs30"></span><span class="sub blue2 fs12 lang" lang='lb_person'></span>
</div>

<div style="width:160px; height:25px; left:654px; top:300px; ">
<span class="blue2 lang" lang='lb_flt_completed'></span><span id="val_trv_num_completed" class="ffnum fs17"></span>
</div>


<!-- ********************************* -->
<!-- 不正常航班 -->
<!-- ********************************* -->
<div class="fs17 center lang" lang='lb_abnormal_flt' style="width:170px; height:25px; left:60px; top:350px; ">
</div>


<div class="blue1 fs13 lang" lang='lb_aog' style="width:170px; height:25px; left:56px; top:390px; ">
</div>
<div class="right" style="width:60px; height:25px; left:170px; top:390px; ">
<span class="val_aog ffnum fs16" id="aog_plane_num"></span><span class="sub blue2 fs10 lang" lang='lb_flts'></span>
</div>


<div class="blue2 fs10" style="width:74px; height:25px; left:56px; top:417px; ">
	<span class="lang" lang='lb_ac_no'></span>&nbsp;
	
</div>

<div class="blue2 fs10" style="width:120px; height:25px; left:200px; top:417px; ">
	<span class="lang" lang='lb_ac_type2'></span>
</div>

<!-- flt scroll list -->
<div class="fs12" style="width:177px; height:96px; left:56px; top:437px; overflow:hidden; pointer-events: auto;" id="aog_plane_list">
	<div id="aog_plane_list1" style="position:relative; height:auto;"></div>
	<div id="aog_plane_list2" style="position:relative; height:auto;"></div>
</div>


<div class="blue1 fs13 lang" lang='lb_return_divert' style="width:170px; height:25px; left:56px; top:546px; ">
</div>
<div class="right" style="width:60px; height:25px; left:170px; top:546px; ">
<span id="flt_return_back" class="ffnum fs16"></span><span class="sub blue2 fs10 lang" lang='lb_flts'></span>
</div>

<div class="blue1 fs13 lang" lang='lb_cancel_flt' style="width:170px; height:25px; left:56px; top:576px; ">
</div>
<div class="right" style="width:60px; height:25px; left:170px; top:576px; ">
<span id="flt_cancel" class="ffnum fs16"></span><span class="sub blue2 fs10 lang" lang='lb_flts'></span>
</div>

<div class="blue1 fs13 lang" lang='lb_delayed_flt' style="width:170px; height:25px; left:56px; top:606px; ">
</div>

<div class="blue2 fs12 lang" lang='lb_delay_1to2' style="width:60px; height:25px; left:60px; top:633px; ">
</div>
<div class="right" style="width:60px; height:25px; left:170px; top:630px; ">
<span id="flt_delay_12" class="ffnum fs16"></span><span class="sub blue2 fs10 lang" lang='lb_flts'></span>
</div>

<div class="blue2 fs12 lang" lang='lb_delay_2to4' style="width:60px; height:25px; left:60px; top:653px; ">
</div>
<div class="right" style="width:60px; height:25px; left:170px; top:650px; ">
<span id="flt_delay_24" class="ffnum fs16"></span><span class="sub blue2 fs10 lang" lang='lb_flts'></span>
</div>

<div class="blue2 fs12 lang" lang='lb_delay_4' style="width:60px; height:25px; left:60px; top:673px; ">
</div>
<div class="right" style="width:60px; height:25px; left:170px; top:670px; ">
<span id="flt_delay_4" class="ffnum fs16"></span><span class="sub blue2 fs10 lang" lang='lb_flts'></span>
</div>

<div class="blue2 fs12 lang" lang='lb_int' style="width:120px; height:25px; left:60px; top:693px; ">
</div>
<div class="right" style="width:60px; height:25px; left:170px; top:690px; ">
<span id="flt_delay_int" class="ffnum fs16"></span><span class="sub blue2 fs10 lang" lang='lb_flts'></span>
</div>




<!-- ********************************* -->
<!-- 延误原因 -->
<!-- ********************************* -->
<canvas id="cvs_delay_cause" width="130" height="130" style="left:464px; top:396px; background:rgba(255,0,0,0);"></canvas>

<div class="fs17 center lang" lang='lb_delay_cause' style="text-align:center;width:180px; height:25px; left:440px; top:350px; ">

</div>

<div class="center" style="width:160px; height:20px; left:448px; top:427px; ">
<span class="green fs12 lang" lang='lb_comp_cause2'></span><br>
<span class="fs18">VS</span><br>
<span class="blue2 fs12 lang" lang='lb_none_cause2'></span>
</div>


<div class="" style="width:220px; height:25px; left:256px; top:346px; ">
	<div class="blue1 fs13 lang" lang='lb_comp_cause' style="position: relative; display: inline-block; vertical-align: middle;"></div>
	<span id="per_delay_cause_comp" class="green fs22" style="vertical-align: middle;"></span><span class="green sub fs12" style="vertical-align: middle;">%</span>
</div>

<div id="holder_delay_cause_comp" class="fs12" style="width:180px; height:140px; left:257px; top:385px; ">
	<!--
	<div class="baritmrow"><span class="blue2">运行</span> <span class="bar greenbar" style="width: 70px; "></span> <span class="ffnum">20%</span> </div>
	-->
</div>


<div class="" style="width:220px; height:25px; left:586px; top:346px; text-align: right;">
	<div class="blue1 fs13 lang" lang='lb_none_cause' style="position: relative; display: inline-block; vertical-align: middle;"></div>
	<span id="per_delay_cause_none" class="flex_none green fs22" style="vertical-align: middle;"></span><span class="green sub fs12" style="vertical-align: middle;">%</span>
</div>

<div id="holder_delay_cause_none" class="fs12" style="width:180px; height:140px; left:649px; top:385px; ">
	<!--
	<div class="baritmrow"><span class="blue2">空管</span> <span class="bar bluebar" style="width: 70px; "></span> <span class="ffnum">20%</span> </div>
	-->
</div>



<!-- ********************************* -->
<!-- 运力分布 -->
<!-- ********************************* -->
<div class="fs17 center lang" lang='lb_ac_dist' style="text-align:center; width:180px; height:25px; left:442px; top:548px; ">
</div>
<div class="blue2 fs12 center lang" lang='lb_total_ac' style="width:80px; height:25px; left:450px; top:612px; ">
</div>
<div class="right" style="width:60px; height:25px; left:455px; top:630px; ">
<span class=" ffnum fs20" id="total_plane_num"></span><span class="sub blue2 fs10 lang" lang='lb_jia'></span>
</div>


<div class="blue1 fs13 lang" lang='lb_overnight' style="width:170px; height:25px; left:256px; top:580px; ">
</div>

<div class="blue2 fs10 lang" lang='lb_planned_overnight' style="width:68px; height:25px; left:275px; top:605px; ">
</div>
<div class="left" style="width:60px; height:25px; left:275px; top:616px; ">
<span id="base_over_night_plane_num2" class=" ffnum fs16"></span><span class="sub blue2 fs10 lang" lang='lb_jia'></span>
</div>

<div class="blue2 fs10 lang" lang='lb_landed_overnight' style="width:68px; height:25px; left:370px; top:605px; ">
</div>
<div class="" style="width:60px; height:25px; left:370px; top:616px; ">
<span id="base_over_night_plane_num" class=" ffnum fs16"></span><span class="sub blue2 fs10 lang" lang='lb_jia'></span>
</div>

<div style="width:106px; height:4px; left:280px; top:630px; ">
	<div class="bar bluebar" style="width:106px;">
		<div id="bar_base_over_night" class="innerbar greenbar" style="height:4px; right:0px; "></div>
	</div>
</div>

<div id="base_over_night_plane" class="fs12" style="width:166px; height:80px; left:230px; top:656px; overflow-y: hidden; pointer-events: auto; ">
	<div id="base_over_night_plane1" style="position:relative; height:auto;"></div>
	<div id="base_over_night_plane2" style="position:relative; height:auto;"></div>
</div>

<div class="blue1 fs13 lang" lang='lb_fleet_count' style="width:160px; height:25px; left:620px; top:580px; ">
</div>

<div class="blue2 fs10 lang" lang='lb_ac_air' style="width:68px; height:25px; left:620px; top:605px; ">
</div>
<div class="left" style="width:60px; height:25px; left:620px; top:616px; ">
<span id="plane_over_air" class=" ffnum fs16"></span><span class="sub blue2 fs10 lang" lang='lb_jia'></span>
</div>

<div class="blue2 fs10 lang" lang='lb_ac_ground' style="width:68px; height:25px; left:700px; top:605px; ">
</div>
<div class="" style="width:60px; height:25px; left:700px; top:616px; ">
<span id="plane_on_ground" class=" ffnum fs16"></span><span class="sub blue2 fs10 lang" lang='lb_jia'></span>
</div>

<div class="blue2 fs10 lang" lang='lb_ac_stop' style="width:68px; height:25px; left:773px; top:605px; ">
</div>
<div class="" style="width:60px; height:25px; left:773px; top:616px; ">
<span id="plane_on_stop" class=" ffnum fs16"></span><span class="sub blue2 fs10 lang" lang='lb_jia'></span>
</div>
<!--div style="width:106px; height:4px; left:675px; top:630px; ">
	<div class="bar bluebar" style="width:106px;">
		<div id="bar_plane_on_ground" class="innerbar greenbar" style="height:4px; right:0px; "></div>
	</div>
</div-->

<div id="air_aclist" class="aclist fs12" style="width:230px; height:80px; left:586px; top:646px; ">

    <div id="air_aclist1" style="position:relative; height:auto;">
	</div>

    <div id="air_aclist2" style="position:relative; height:auto;">
    </div>
	
</div>


<!-- ********************************* -->
<!-- 新闻 news -->
<!-- ********************************* -->
<div id="welcome_msg" class="blue1 fs32 center" style="width:1000px; height:50px; left:864px; top:10px; overflow:hidden;">
</div>


<!-- ********************************* -->
<!-- 月度正常率 -->
<!-- ********************************* -->
<!-- 暂时隐藏掉 2017-11-01-->
<!-- <div class="fs13 center lang" lang='lb_normal_rate_m' style="width:180px; height:25px; left:840px; top:80px; ">
</div>
<div class="fs28 center" style="width:80px; height:45px; left:890px; top:98px; ">
<span id="normal_rate_month" class="fs30 ffnum"></span><span class="sub ffnum fs16">%</span>
</div> -->



<!-- ********************************* -->
<!-- 年度正常率 -->
<!-- ********************************* -->
<!-- <div class="fs13 center lang" lang='lb_normal_rate_y' style="width:180px; height:25px; left:1714px; top:80px; ">
</div>
<div class="fs28 center" style="width:80px; height:45px; left:1764px; top:98px; ">
<span id="normal_rate_year" class="fs30 ffnum"></span><span class="sub ffnum fs16">%</span>
</div> -->


<!-- ********************************* -->
<!-- 运行正常率 -->
<!-- ********************************* -->
<div id="title_earth" class="blue1 fs18 center lang" lang='tit_mid' style="width:290px; height:25px; left:1225px; top:75px; "></div>



<!-- ********************************* -->
<!-- 中间地图 -->
<!-- ********************************* -->



<!-- ********************************* -->
<!-- 飞机位置图例 -->
<!-- ********************************* -->
<div class="blue1 fs13 planeLocationLegend lang" lang='lb_ac_pos_legend' style="width:220px; height:25px; left:1720px; top:170px; display:none;">
</div>
<div class="blue1 fs12 planeLocationLegend" style="width:120px; height:45px; left:1720px; top:198px; display:none;">
<span style="color:rgb(85, 210, 246);">●</span><span class="lang" lang='lb_air'></span><!-- 空中 实际为：正常--><br>
<span style="color:rgb(240, 140, 5);">●</span><span class="lang" lang='lb_ground'></span><!-- 地面 实际为：延误-->
</div>

<div id="china_map" class="hidden" style="width:500px; height:400px; left: 1150px; top:204px; transform:scale(1.23, 1.23); transform-origin:left top; -moz-transform:scale(1.23, 1.23); -moz-transform-origin:left top; -webkit-transform:scale(1.23, 1.23); -webkit-transform-origin:left top; -ms-transform:scale(1.23, 1.23); -ms-transform-origin:left top;">

<!-- 新疆 -->
<div style="width:128px; height:90px; left: 16px; top:77px;">
<svg id="area_100007" viewBox="0 0 128 90" class="area_stroke hidden " version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" >
<path d="M126.6,53.8c0.1-0.9,1.3-1.8,1.3-1.8c-0.3-0.4-0.6-1.5-1.1-2.3c-0.6-1-0.9-1.1-1.1-1.5c-0.2-0.5,0-1.2-0.4-1.9
		c-0.4-0.7-0.1-1.6-0.4-2.8c-0.3-1.2-0.6-0.6-1.3-1s0.2-0.9,0.6-1.4c0.4-0.5-1.5-0.4-2.7-0.6c-1.2-0.2-1.1-0.9-1.2-1.4
		c-0.2-0.4-0.7-0.4-1-0.5c-0.3-0.1-0.1-0.3-0.2-0.7c-0.1-0.4-0.6-0.3-1.2-0.4c-0.6-0.1-1-1.2-1.4-1.9c-0.4-0.7-1.3-0.6-1.9-0.9
		c-0.6-0.2-1,0-1.4-0.6c-0.4-0.5-2.2-0.3-2.5-0.7c-0.3-0.4-1.7-0.2-2.8-0.3c-1.1-0.1-2-1.1-2.6-1.5c-0.7-0.4-1-0.5-1.8-0.7
		s-1-1.3-1.1-2.4c-0.1-1.1,1.2-2.2,2.2-2.7c1.1-0.6,0.2-1.7,0-2.2c-0.2-0.5,0.4-0.9,1.1-1.5c0.8-0.6,0.1-1.8,0.2-2.2
		c0-0.4-0.2-0.7-0.6-0.8c-0.4-0.1-0.3-0.5-0.2-0.9c0.1-0.4-0.4-0.7-0.7-1.5c-0.4-0.7-0.1-0.9,0.3-1.5c0.4-0.7-0.8-1.4-1.1-1.7
		c-0.3-0.3-0.5-0.8-0.6-1.2s-0.9,0.1-1.2,0.3c-0.3,0.3-0.9-1.5-1.1-1.9c-0.2-0.4-1,0-2.5,0c-1.4,0-0.9-0.7-1.1-1.2
		c-0.2-0.5-0.3-0.5-0.5-0.7C96,8.7,95.9,8.1,96,7.4c0.1-0.6-0.9-1.4-1.3-1.7S94.4,5,94.4,4.6c0-0.4-0.6-0.5-1-0.8
		c-0.4-0.3-0.2-0.7,0.2-1S94,1.4,94,1.4s-1,0.3-2.6,0.3s-1-1.3-1-1.3S89.2,0.7,88.7,1c-0.5,0.2-0.5,2.1-1.2,2.8
		c-0.6,0.7-2.4,0.6-3.1,0.4C83.6,4,82.5,4.9,82,6.3c-0.5,1.4-0.9,3.4-0.6,4.1c0.3,0.8-0.2,0.9-0.7,1.7c-0.5,0.8-2.2,0.4-3.2,0.7
		c-1,0.3-1-0.3-1.1-0.7s-2.3-0.8-3.5-1s-2.2-1.5-3-1.9c-0.8-0.4-1.4,0.5-1.9,1.5c-0.5,1-3,3.6-3.2,4.1c-0.2,0.5-0.4,0.8-0.4,1.5
		c0,0.7-1.4,1.8-1.4,2.2c0,0.5,0.5,0.6,1,1c0.5,0.4-0.2,0.9-0.4,1.7c-0.2,0.9-1.2-0.2-1.5-0.5s-0.4-0.1-1.2-0.2
		c-0.8-0.1-0.8-0.5-1.1-1s-2.2-0.2-2.8,0c-0.6,0.2-1.5,0-2.1-0.2c-0.6-0.2-3.2,0.1-4,0c-0.8-0.1-1.1,0.6-1,1.2
		c0.1,0.6,1.7,0.4,2.5,0.7c0.9,0.3-0.6,0.9-1,1.5s-0.1,1.7-0.2,2.1c-0.1,0.5,0.5,5.9,0.4,6.5c-0.1,0.6-1.7,0.6-2,0.5
		c-0.4,0-0.1,0.4,0,0.8c0.1,0.4-1,0.5-1.5,0.7c-0.5,0.2-0.6,3.2-0.6,3.8c0,0.7-1,0.4-1.7,0.4c-0.7,0-0.9,0.4-1.4,0.7
		c-0.5,0.3-1.1,0.3-1.6,0.2c-0.5-0.1-1.3-0.4-2.4-0.3c-1.1,0.1-1,0.4-1.3,0.8c-0.4,0.4-1.3,0.4-2.4,0.4c-1.1,0-1.7,1.2-2.3,1.6
		c-0.6,0.3-0.8,0.4-1.6,0.2c-0.7-0.2-3.5-0.4-3.8-0.6c-0.4-0.2-2.7-0.4-3.7-0.5c-1-0.1-1.4,1.9-1.8,2.8c-0.4,0.8-2.7,0.1-3,0.1
		c-0.3,0-2,0.2-2.6,0.2c-0.6,0,0-0.9,0.2-1.1c0.2-0.2-0.7-0.8-0.9-1.3c-0.2-0.4-0.3-0.3-0.7,0.1c-0.4,0.4-1,0.5-2.2,0.6
		c-1.2,0.1-1.1-0.4-1.3-0.7c-0.2-0.3-0.6,0.7-0.5,1.1c0.1,0.5-1.2-0.1-1.8-0.2c-0.6-0.2-1.3,0.6-1.9,0.8c-0.6,0.2-0.8-0.1-1-0.3
		c-0.2-0.3-1-0.1-1.6-0.1c-0.7-0.1-1.1,1.1-2,1.1c-0.9,0.1-0.9,0.9-0.9,1.5c-0.1,0.6-1.4,1-2.2,0.9c-0.8-0.1-0.4,2.3-0.3,2.8
		s-0.4,0.5-0.7,0.7c-0.3,0.1,0.3,2.1,0.8,2.7C1.3,52,1.5,51.8,2,51.1c0.5-0.7,2.4,1,3.4,1.7c1,0.7,0.2,1.1-0.2,1.6
		c-0.4,0.5-0.1,2.7,0.1,3.2c0.2,0.5-0.6,0.4-1,0.8s0.3,1.1,0.6,1.3c0.3,0.2,0.1,0.3,0,1c-0.1,0.7-1.6,0.2-2.1,0.1
		c-0.5-0.2-1.6-0.2-1.8,0.6c-0.2,0.8,3,1.7,3.8,1.9c0.8,0.1,0.2,0.3,0.3,0.7c0.1,0.5,0.2,0.9,0.6,1.2c0.4,0.3,0.5,1.2,1,1.8l0.1,0.2
		c0.3,0.5,0.1,1.1-0.1,2.1c-0.2,1.2,0.8,3,1.6,3.2C9,72.8,8.8,73,9.9,74c1.1,1.1,2.4,1.8,3,2.4c0.2,0.2,0.5,0.3,0.8,0.4
		c1.5,0.4,2.6-0.1,2.9,0.3c0.2,0.3-0.4,0.8-0.7,2c-0.1,0.3-0.2,0.6-0.3,2.4c-0.1,0.8-0.1,2-0.1,3.3c0.3,0.5,0.9,1.3,1.8,2
		c0.8,0.7,2.4,1.9,3.5,1.5c1.2-0.5,1.2-2.4,2.3-2.5c0.5,0,0.5,0.3,1.1,0.3c1.4,0.1,2.6-2,2.8-2.4c0.1-0.1,0.7-1.1,1.2-1.8
		c0.1-0.1,0.3-0.4,0.6-0.7c0.3-0.2,0.5-0.3,0.5-0.4c0.2-0.1,0.4-0.2,0.5-0.2c0,0,0.7,0.2,1.5,0.8c0.8,0.6,1.7,1.9,2.4,1.7
		c0.7-0.2,1.2-0.5,1.5-0.1s0.2,0.6,0.5,0.6c0.3,0,0.8-0.1,1,0.3c0.1,0.4,0.2,0.9,0.8,1s1.1-0.1,1.3-0.4c0.2-0.3,0.6-0.8,1.1-0.9
		c0.5-0.1,0.8-0.1,1.1-0.3c0.3-0.2,1.2-0.5,1.3-0.2c0.1,0.2,0.5,0.4,1.1,0.5s1.3-0.2,1.7,0c0.4,0.3,0.1,0.5,0.9,0.6
		c0.8,0,0.8-0.4,1.2,0.2c0.4,0.6,0.8,2,1.7,2c0.8,0.1,1.7,0.1,2,0.4c0.3,0.3,1.3,0.9,1.6,0.7c0.3-0.3-0.2-0.2,0.9-0.3
		c1.2-0.1,2.2,0,2.8-0.4c0.6-0.3,0.6-0.3,1.3-0.3c0.7,0,2.4,0.7,3,0.4c0.6-0.3,1.5-1.5,3.5-1.6c2,0,2-0.3,2.7-0.1
		c0.7,0.2,2.4,0.5,2.8,0.2c0.4-0.3,0.3-0.8,2.2-0.6c1.9,0.2,3.6,0.2,5.1,1.2c1.5,1,2.1,2.5,3.7,2.6c1.3,0.1,2.5,0.1,2.9,0.3
		c0.6-0.3,1,0,1.7,0.4c0.7,0.5,0.8,0.4,1.5,0.7c0.8,0.3,1.1-0.1,1.6-0.4c0.4-0.3,0.4-0.1,1.3,0c0.9,0.1,0.7-0.3,1.4-0.7
		c0.7-0.4-0.8-0.3-1.8-0.6c-0.9-0.2-0.4-1.7,0.1-2.2c0.5-0.6,1.3-0.2,2-0.9c0.7-0.7,0.5-0.9,0.6-1.5c0.1-0.6,0.5-0.6,0.8-1
		c0.2-0.4-0.9-0.7-1.5-0.9c-0.6-0.2-0.9-0.9-1.3-1.6c-0.4-0.7-0.5-0.4-1-0.8c-0.5-0.3-0.1-2.1,0-2.5c0.1-0.5-0.2-1.8-0.3-2.2
		c-0.1-0.4,1.1-0.4,1.5-0.4c0.5,0,0.7-0.2,1.3-0.6c0.5-0.4,1.7-0.2,2.1-0.2c0.4,0,0.6,0.3,1,0.6c0.5,0.3,1.6-0.2,2-0.3
		c0.4-0.1,0.7-0.1,1.4,0.1c0.7,0.1,1.2,0.1,1.8-0.2c0.6-0.3,2.3-0.3,3.5-0.7c1.1-0.4,3.2,0.1,3.7,0.3c0.5,0.2,0.3-0.3,0.3-0.7
		c0-0.4,0.5-0.2,1-0.2c0.5,0,0.3,0.2,0.5,0.4c0.2,0.2,0.3,0.2,0.8,0l0.1,0h0l0.2,0c0.7-0.2-0.3-0.6-0.5-1c-0.2-0.4-0.1-0.3,0.3-0.5
		c0.4-0.2,0.5-0.9,0.7-1.7c0.2-0.8-0.1-1.4-0.3-2.6c-0.2-1.1-0.3-2.7-0.1-4.1c0.2-1.4,1.7-1.1,3.1-1.4c1.4-0.3,0.6-0.2,1.4-1.1
		c0.8-0.9,1.5-1.4,2.9-2s1.7-0.6,3-0.7c1.3-0.1,0.9-0.3,1.5-0.8c0.6-0.5,2.4,0,3.3-0.2c0.9-0.2,0.6-0.5,1-1
		C126.4,55.2,126.5,54.6,126.6,53.8z"/>
</svg>
</div>


<!-- 西南 -->
<div style="width:183px; height:119px; left: 29px; top:157px;">
<svg id="area_100004" viewBox="0 0 184.17 119.56" class="area_stroke hidden " version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" >
<path d="M110.39,38.41a3.09,3.09,0,0,0-.46.24.9.9,0,0,1-1-.11c-.33-.23-.89.25-1.26.27s-.07.41.23.7.06.37.12,1.15-.6.82-.91,1-.19.56-.39,1.08a5.31,5.31,0,0,1-1.68,1.58,1.28,1.28,0,0,0-.63,1c0,.32-.23.36-.8.29s-1-.77-1.53-.95-.69.15-1.48.48a2.33,2.33,0,0,1-2.15-.57,3.69,3.69,0,0,0-1.36-.83c-.3-.07-.12-1.07-.07-1.62s-.4-.47-.68-.49-1-1.19-1.64-1.53-.56-.85-1-1.57a3.12,3.12,0,0,0-2.8-.78,1,1,0,0,1-1.28-.64c-.22-.47-3.17-.26-3.56-.22s-.62-.3-1.1-.72-1.1,0-2-.31-.29-.19-.7-.87-.58-.09-1.55.11-2.24-1-2.59-1.67-.87-.63-1.7-1.13-3.36-1.49-4-2-2.08-.24-2.71-.34-.6-1.81-.78-2.57-.83-1.32-1.12-2-.38-.84-.2-1.49S66.79,22.5,66.31,22s-.16-1.78,0-2.18-.17-.66-.19-1.44.91-.47,1.57-.27.88-1.37.62-1.75-.12-.38.14-1a6,6,0,0,1,1.23-2.15,3.37,3.37,0,0,0,.88-1.79c.22-.54-.52-.61-1-.65a.92.92,0,0,1-.8-1c0-.56,1-.28,1.47-.37s.44-.44.49-1a.27.27,0,0,0-.15-.24,10.61,10.61,0,0,0-2.93-.34C66,7.78,65.36,6.3,63.91,5.32s-3.21-1-5.07-1.18-1.77.34-2.2.6a5.26,5.26,0,0,1-2.78-.2c-.72-.25-.76,0-2.74.07s-2.85,1.25-3.46,1.57-2.29-.33-3-.38a1.62,1.62,0,0,0-1.3.29,9.07,9.07,0,0,1-2.81.37c-1.16.09-.62.05-.94.32s-1.33-.39-1.6-.65a4.18,4.18,0,0,0-1.95-.39c-.84-.07-1.21-1.42-1.65-2s-.44-.16-1.22-.2-.52-.28-.92-.55-1,.07-1.68,0-1-.27-1.15-.51-1,0-1.3.21a2,2,0,0,1-1.1.3,1.73,1.73,0,0,0-1.15.87c-.22.34-.69.42-1.3.4s-.66-.56-.77-1-.68-.33-1-.3-.13-.24-.5-.6-.8,0-1.52.14S19.19,1.35,18.44.78A5,5,0,0,0,17,0c0,.95-1.34,2.51-1.82,3a12.38,12.38,0,0,0-1,1.7c-.39.57-1.12.53-1.41,1a1.44,1.44,0,0,1-1.83.44c-1-.32-1.13,1.21-1.79,2S8,8.49,7,7.95a1.39,1.39,0,0,0-1.4,0,2.77,2.77,0,0,0,.54,1.48c.28.31-.07.53-.76.76s-.87,1.4-.87,1.4l2,1.61s-.52,1.12-.68,1.42,0,.88.88,1.28.4,1.51-.07,1.95-1.33-.28-1.33-.28l-1.85,1L2.67,18,2.44,16s-.78.4-.83.39S.56,15.8.56,15.8l.11,1.13L0,17.78l1.25,2a8.85,8.85,0,0,1-.48,1S1,22.6,1,22.6l-.86.86s.43,1.75.43,1.84A12.27,12.27,0,0,0,2,24.45s1.91,3.89,1.89,3.93,1.25,0,1.25,0,1.27,1.51,1.7,2-.06.48-.22.75S8,32.47,8.87,32.93s2,2.5,2.56,3.36,1.09-.47,1.76-1.17,2.57.51,3.46,1.18.29,1.37,1.46,2.39,1.76,2.24,2.41,2.76a5.31,5.31,0,0,0,1.32.83c.56.28.63.69.9,1.16a5.49,5.49,0,0,1,.68,1.88c.12.77,1.21.07,1.53-.19s1.19.18,1.88.68,0,1.57,0,2.2,2.26,2.89,2.69,3.21.53.24,1.23-.11.16,1.37,0,1.91,3.06.87,3.54,1,.31,2,.61,2.88S36,56.55,36.31,56s.51,1.17,1.1,1.78,1.23-.7,1.76-1.09,2.29,2.13,2.61,2.61,7.63.79,8.16.91.55-.14,1.11-.47.49.31,1.1.71.35,1.1.27,1.75-.33.58-.68,1a3.79,3.79,0,0,0-.46,2.12c0,.86,1.45-1.31,1.45-1.31a32.28,32.28,0,0,0,3.42-2.6c.87-.9,1.09-.19,1.51,0s1.07-.7,2-.87,1.57.43,2.4.92.16,1.12,0,1.5,1.06.23,1.62.2,1.23.87,1.73,1.18.72-.12,1-.38,1.09.73,1.66,1.14.1.26.11.86.79.44,1.31.82c0,0,.38.14.5.21,1.1.62-.2,4.42.63,4.83.4.2.79-.64,2-.67.36,0,.53.06,1.42.08.68,0,.65,0,.92,0,1,.1,1.12.65,1.92.75.41,0,.39-.1,2-.58,1.17-.35,1.25-.3,1.5-.5.64-.52.33-1.1.85-2.15a5.12,5.12,0,0,1,2.4-2.27,3.21,3.21,0,0,0,1.67-.92c.16-.22.2-.36.42-.5.44-.29.76,0,1.43-.19.48-.14.5-.35.93-.4a1.94,1.94,0,0,1,.85.14c.53.16,1.08.21,1.62.33,2.55.57,2.64.25,3.11.65.7.59.53,1.33,1.46,1.9a8.58,8.58,0,0,0,.78.41,4.07,4.07,0,0,0,1.11.43,4.32,4.32,0,0,0,1.76-.3c.36-.12.38.14.95.53a2.79,2.79,0,0,0,1.55.44c.44,0,1,.64,1.65,1.22s.81,0,.89-.79,1-1.5,1.47-2,.44.15.61.44.62.16,1.13.19.51.59,1.34,1.37a1.35,1.35,0,0,1,.35.62l0,0a26.6,26.6,0,0,1,.17,3.15c0,.2.11.3.19.34.42.19,1.05-.58,1.38-.45s.36,1-.25,3.72c.15,2.42-.46,3.48-1.08,4a6.47,6.47,0,0,0-1.58,1.5c-.59.84-.47,1.22-.42,1.33.15.32.52.37.5.5s-1,0-1.75.5a1.84,1.84,0,0,0-.67.75c-.24.52,0,.75-.25,1.33-.16.37-.3.35-.5.83s-.11.45-.25.75a3.25,3.25,0,0,1-.58.8,1.88,1.88,0,0,0-.51,1.64c.06.5-1.25,1.72-1.58,2.14s.75,1,.89,1.48-.67,1.43-.66,2.3,2.13-.34,3-.76a3.61,3.61,0,0,1,2.69.11,2.08,2.08,0,0,0,2-.48c.59-.45.23.1.4.72s-.68.53-1,1.08-.08,2.82.14,3.49-.15.39-.56.83.49.69,1,1.21,1.86.49,2.53.53.34.64.52,1.43-.64.83-1,1.13-.21,1.77-.24,2.34-1.15,1.29-1.34,1.7,1.29.34,1.95.27.58.23.79.5.84-.12,1.37-.3.66.12.77.49a2.08,2.08,0,0,1-.46,1.42c-.32.53.52.6.86.93s0,.8.11,1.47,1.15-.1,1.56-.39.27.29.66.74,3.27-1.51,3.63-1.81.59,1.8.82,2.13,0,.77.2,1.81,1,.46,1.43.21.39-.05.7.45.74.71,1.53.33.2-.43-.1-1.22a4.18,4.18,0,0,1,.3-2.62c.28-.85-.91-2.51-1.09-2.91s.36-.54.59-.52.25-.87.46-1.48.4.55.82.74.56-.58,1.15-.63,1.41,1.31,1.41,1.31.25-.42.61-.23.33-.24.76-.94,1.58-.07,2.3,0,.68.85.73,1.34.57.55,1.1.75,1.33-1.34,1.8-2.25.75.59.91,1.52.63-.11.81-.73,1.24.81,1.56,1.51,1.05-1,1.66-1.92,1.13.17,1.69.62.61-.32.91-.66,1.06-.11,1.65-.37.6-.74.71-1.06a1.9,1.9,0,0,1,.56-.89c.32-.31,1.6-.33,2.43-.72.51-.24,1.06.44,1.45,1.08,0,0,1.43-.87,1.54-1.21a.74.74,0,0,1,.58-.58c.32-.09.54-.18.63-.1s.95.78,1.19,0,0-1.83.26-2,.73,0,.45-.51-.61-.75-.58-.85,0-.86-.44-.68-1,1-1.43.68-.43-.46-.58-.51-.36-.9-.58-.67-.85,1.32-1.12.92-.49-.9-.76-.82-.33.49-.53-.08.19-1-.24-1.63-.22-1.08-.63-.83-.31.5-.69.22a1,1,0,0,0-1.07-.15c-.24.18-.4,1-.72.61s-.3-1.46-.74-1.83a.37.37,0,0,0,.19,0c.39-.07,3.43.11,3.83-.72s.3-1.2,1-1.27,1-.35,1.31.16.18.73.56.78.79-.15,1,.21a1.24,1.24,0,0,0,1.44.62c.72-.21,1.25-.13,1.09-.79s-.41-.66,0-.79,1.29-.25,1.49-.49.27-.48.58-.37.22.45.8.19.78-.4,1-.37,1.36.28,1.62-.25,0-1.05.53-1.27,1.24-1.06,1.39-.67a3.23,3.23,0,0,0,.57,1.06c.24.23.61.92,1,.73s.37-.75.59-.22.38.9.78,1.08.74.37.88,0-.05-.45.67-.42.56.44.82-.17.2-1,.71-1.32.56-.84.92-.61.3.07.56.46,1.13,1.29,1.07.83-.07-.77.1-1,.71-1.14,1-.77.24.73.45.65.67.43.86,0,.78-.77.25-.88-1-.05-.7-.44.53-.67.93-.57,1,.74,1.35.33a13,13,0,0,0,1-1.44s0,0,0-.1c.23-.38-.44-1.65-.78-2s.09-.83.57-1.34,0-.72-.31-.87.06-.83.31-.83.44-.3.54-.57.36-.25.48-.7-.5-1-.48-1.13-.65-.26-.83,0-1.32,0-1.71,0-.37.25-.85.69-.21-.16-.3-.71.42-1,.67-1.71,1.44-.36,2.31-.68.52-.46,1-1.24-.39-.87-.76-1.38,0-.51.26-1,0-.22-.38-.41.15-.68.44-1.12-.08-.3-.43-.47,0-.48.07-.83a.65.65,0,0,0-.09-.46,1.72,1.72,0,0,1-.23-.49c-.11-.4.27-1.15.34-2.36a2.72,2.72,0,0,0-.28-1.47c-.08,0-.19,0-.37-.11a1.94,1.94,0,0,1-.78-1c-.08-.29-.12-.34-.33-.35s-.57-.07-.57-.3.5-1.63-.34-1.93-1-.6-1.09-.87-1.63,0-1-.39.47-.22.6-.71a15.4,15.4,0,0,0,0-1.61c.08-.22.21-.84,0-.9s-.76-.47-.68-.74.94-.16,1.35-.39.88-.13,1.36-.34.42-.43,1-.25,1.78-.2,2,0,.36.38.69,0,1.4-1.75,2.32-1.67,1.18.2,1.53-.18.69-.2.57-.62a1.17,1.17,0,0,1,0-1.06c.2-.4.45-1.07.23-1.46s-.3-.79-.68-.94-.81-.17-.63-.58.73-1.41.1-1.41c-.46,0-1.57-.13-2.13-.2a.44.44,0,0,1-.42.32c-.45,0-.74-.34-.91-.08s-1,.42-1.19.05.2-1.15-.51-1.31a5.58,5.58,0,0,1-1.54-.45,13.34,13.34,0,0,0-1.21-.73,1.12,1.12,0,0,1-.31-.28c-.24-.26-.5-.61-.8-.62a5.87,5.87,0,0,0-1.44.07c-.28.1-.66,0-.89.27a.58.58,0,0,1-.93,0,8.42,8.42,0,0,1-.86-.79c-.16-.24-.11-.81-.45-.75s-1.21.37-1.27-.14,0-1.08-.33-.93-1.48.58-1.46.37a.24.24,0,0,0-.27-.29c-.15,0-.55,0-.28-.32s.75-1.06,0-1.19a3.89,3.89,0,0,0-2.13.12c-.6.24-1.07,0-1.41.19s-.72.42-.94.18-.25,0-.62-.48-.1-.57-.47-.6a3.84,3.84,0,0,1-1.07-.45c-.3-.13-.39-.19-.4-.42-.53-.16-1.66-.45-1.77,0s.09,1-.38,1.22-1.18.72-1.63.68-.72-.38-1.29-.51-1.49,0-1.66-.54-.28-.48-.68-.91-.53-.71-.44-1a.89.89,0,0,0,0-.89c-.23-.4-.44-.56-.48-1s-.29-1.4-.59-1-.61.61-1,.45-.46-.35-.73-.26a1.56,1.56,0,0,1-1.48-.74,19,19,0,0,0-1.45-2c-.37-.37-.87-.61-.88-1.41s.33-1.73-.41-2.17-1.08-1.08-1.67-.44-.52.72-1.19.88-1.08-.15-1.41.26-.86.34-.4,1.06a14,14,0,0,1,.71,1.24,2.34,2.34,0,0,1,.34,1.57c-.25.74-.63,1.92-1.24,1.83s-1.57-.65-1.93-.26-.29,1.37-1.28.75A6.36,6.36,0,0,1,133,38.46c-.3-.32-.53-.56-.92-.35s-.49-.29-.83.3a4,4,0,0,1-1.14,1.26c-.2.08-.23.46-.06.63a.59.59,0,0,1,.1.65c-.08.25-.32,1.66-.92,1.31s-.94-.55-1.41-.37-.73.55-1.17,0,.22-.84-.62-1.31-1-.5-1.29-1.15-1-1-.9-1.63.09-1.2-.57-1-.89.12-1.24.48-.2.4-.66.5-.59-.73-1.07-.63-1,.12-1.16-.2-.45-1.11-1.26-1-1,.43-1.29.12,0-.29-.59-.32-1.29-.25-1.56,0-.13.54-.67.2-.33-.53-.93-.44-.79-.18-.82.41-.07,1-.34.75-.43-1.17-.68-.84a3.3,3.3,0,0,0-.55,1A5,5,0,0,0,110.39,38.41Z"/>
</svg>
</div>


<!-- 西北 -->
<div style="width:125px; height:80px; left: 94px; top:128px;">
<svg id="area_100006" viewBox="0 0 127 82" class="area_stroke hidden " version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" >
<path d="M87.2,41.6L87.2,41.6c-0.4,0-0.9-0.5-1.1-0.6c-0.2,0-0.5,0-0.6-0.4c-0.1-0.4,0.3-0.7-0.4-1.1
		c-0.7-0.3-1.3-0.4-1.3-1.3c0-0.8,0.6-1.1,0.6-1.6c0-0.5,0.2-0.7,0-0.9c-0.2-0.2-0.1-0.6,0.3-0.6c0.4,0,2.5-0.1,2.5-0.9
		c0-0.8,0.3-1.2,0-1.5c-0.3-0.2-0.5-0.5,0.1-0.9c0.6-0.4,0.6-0.8,0.7-1c0-0.2,0.2-0.9-0.5-1.3c-0.7-0.4-1.1-1-2.1-1.1
		s-3-0.5-3.3-0.2c-0.4,0.3-0.5,1-1.1,0.8c-0.6-0.2-1.5-0.5-1.8-0.1c-0.3,0.4-2,2.3-2.5,2.2c-0.5,0-0.9-0.4-1.3-0.4
		c-0.4-0.1-1.3,0.2-1.7-0.2c-0.4-0.4-0.6-0.6-1-0.6s-1.6,0-2-0.5s-0.7-1.7-0.5-2.1s0.5-1.4-0.2-1.5c-0.6-0.1-2.5,0-2.7-0.6
		c-0.2-0.5,0.1-0.8-0.5-1.1c-0.6-0.3-1-0.8-1.4-1.1s-0.9-0.7-0.9-1.1c0-0.4,0-0.7,0.4-0.6c0.4,0,1,0.2,1.1-0.2
		c0-0.5,0.1-1.1,0.3-1.7c0.3-0.5,0.6-1.9-0.2-2c-0.7-0.2-1.7,0-2-0.1c-0.3-0.2-1.3-0.7-1.7-0.3c-0.4,0.4-0.7,1.1-1.3,1.1
		c-0.6,0-0.8-0.2-1.1,0c-0.3,0.2-0.6,0.9-1.1,0.8c-0.5-0.1-1.1,0-1.3-0.5c-0.2-0.6-0.3-1.3-0.5-1.7c-0.2-0.4-1.1-1.3-1-1.7
		c0.1-0.4,0.2-2.1,0.1-2.6c-0.1-0.5-0.6-1.1-0.5-2.1s0.7-1.8,0.8-2.6s-0.1-5.2-0.1-5.2c-0.1,0-1.3,0.4-1.4,0.4
		c-0.1,0-1.4-0.9-1.4-0.9s-2.2-0.6-2.7-0.6c-0.1,0-0.1-0.1-0.2-0.2c0,0-1.2,0.9-1.3,1.8c-0.1,0.9-0.2,1.5-0.6,2
		c-0.4,0.5-0.1,0.8-1,1c-0.9,0.2-2.7-0.3-3.3,0.2c-0.6,0.5-0.2,0.7-1.5,0.8C41.5,7,41.2,7,39.8,7.6s-2.1,1.1-2.9,2
		c-0.7,0.9,0.1,0.8-1.4,1.1c-1.4,0.2-2.9-0.1-3.1,1.4c-0.2,1.4-0.2,3,0.1,4.1c0.2,1.1,0.5,1.8,0.3,2.6c-0.2,0.8-0.3,1.5-0.7,1.7
		c-0.4,0.2-0.5,0-0.4,0.5c0.2,0.4,1.2,0.9,0.5,1l-0.2,0l0,0c-0.5,0.1-0.6,0.2-0.8,0c-0.2-0.3-0.1-0.4-0.5-0.5c-0.5,0-1-0.2-1,0.2
		c0,0.4,0.1,0.9-0.3,0.7c-0.5-0.2-2.6-0.6-3.7-0.3c-1.1,0.4-2.9,0.4-3.5,0.7c-0.6,0.3-1.1,0.4-1.8,0.2c-0.7-0.1-1-0.2-1.4-0.1
		c-0.4,0.1-1.6,0.6-2,0.3c-0.5-0.3-0.6-0.6-1-0.6c-0.4,0-1.6-0.2-2.1,0.2c-0.6,0.4-0.8,0.6-1.3,0.6c-0.4,0-1.7,0-1.5,0.4
		c0.1,0.4,0.5,1.8,0.3,2.2c-0.1,0.5-0.5,2.2,0,2.5c0.5,0.3,0.6,0.1,1,0.8c0.4,0.7,0.7,1.4,1.4,1.6s1.7,0.5,1.5,0.9
		c-0.2,0.4-0.7,0.3-0.8,1s0.1,0.8-0.6,1.5c-0.7,0.7-1.5,0.3-2,0.9c-0.5,0.6-1.1,2-0.1,2.2c0.9,0.3,2.4,0.2,1.8,0.6
		c-0.7,0.4-0.5,0.8-1.4,0.7c-0.9-0.1-0.9-0.3-1.3,0c-0.5,0.3-0.8,0.7-1.6,0.4c-0.8-0.3-0.8-0.2-1.6-0.7c-0.7-0.4-1-0.7-1.7-0.4
		C6,38.1,6,38.2,6,38.3c0,0.5,0,0.9-0.5,0.9C5,39.3,4,39,4.1,39.6c0,0.6,0.3,1,0.8,1c0.5,0,1.2,0.1,1,0.6c-0.2,0.6,0,0.8-0.9,1.8
		c-0.8,1-1,1.5-1.2,2.2c-0.3,0.6-0.4,0.6-0.1,1c0.3,0.4,0,2-0.6,1.8c-0.7-0.2-1.6-0.5-1.6,0.3s0.3,1,0.2,1.4c-0.1,0.4-0.5,1.7,0,2.2
		c0.5,0.5,1.5,1.2,1.3,1.9c-0.2,0.6-0.1,0.8,0.2,1.5c0.3,0.7,0.9,1.3,1.1,2.1C4.4,58,4.4,59.7,5,59.8c0.6,0.1,2.1-0.1,2.7,0.3
		c0.7,0.5,3.2,1.5,4,2s1.3,0.5,1.7,1.1c0.3,0.7,1.6,1.9,2.6,1.7c1-0.2,1.1-0.8,1.5-0.1c0.4,0.7-0.2,0.5,0.7,0.9
		c0.9,0.3,1.5-0.1,2,0.3c0.5,0.4,0.7,0.8,1.1,0.7c0.4,0,3.3-0.2,3.6,0.2c0.2,0.5,0.5,0.8,1.3,0.6c0.7-0.2,2.3,0.1,2.8,0.8
		c0.5,0.7,0.4,1.2,1,1.6c0.6,0.3,1.4,1.5,1.6,1.5c0.3,0,0.7-0.1,0.7,0.5s-0.2,1.6,0.1,1.6s1.2,0.6,1.4,0.8c0.1,0.2,1.4,0.9,2.1,0.6
		c0.8-0.3,0.9-0.7,1.5-0.5c0.6,0.2,1,0.9,1.5,0.9c0.6,0.1,0.8,0,0.8-0.3c0-0.3,0.2-0.7,0.6-1c0.5-0.3,1.5-1.1,1.7-1.6
		c0.2-0.5,0.1-0.9,0.4-1.1c0.3-0.2,1-0.2,0.9-1c-0.1-0.8,0.2-0.9-0.1-1.2c-0.3-0.3-0.6-0.7-0.2-0.7c0.4,0,0.9-0.5,1.3-0.3
		c0.3,0.2,0.8,0.3,1,0.1c0.1-0.1,0.3-0.2,0.5-0.2c-0.1-0.3,0.1-0.4,0.2-0.6c0.1-0.3,0-0.6-0.1-1.1c-0.1-0.5,0.3-0.7,0.5-1.1
		c0.2-0.3,0.4,0.6,0.7,0.8c0.3,0.2,0.3-0.2,0.3-0.8c0-0.6,0.2-0.3,0.8-0.4c0.6-0.1,0.4,0.1,0.9,0.4c0.5,0.3,0.4,0,0.7-0.2
		c0.3-0.2,1,0,1.6,0c0.6,0,0.3,0,0.6,0.3c0.3,0.3,0.5-0.1,1.3-0.1s1.1,0.7,1.3,1c0.2,0.3,0.7,0.3,1.2,0.2c0.5-0.1,0.6,0.7,1.1,0.6
		c0.5-0.1,0.3-0.2,0.7-0.5c0.3-0.4,0.6-0.3,1.2-0.5s0.6,0.4,0.6,1c-0.1,0.6,0.6,1,0.9,1.6c0.3,0.7,0.5,0.7,1.3,1.2s0.2,0.7,0.6,1.3
		c0.4,0.6,0.7,0.2,1.2,0c0.5-0.2,0.8,0,1.4,0.4c0.6,0.3,0.8-1.1,0.9-1.3c0.1-0.2,0.1-0.5-0.1-0.6c-0.2-0.2-0.1-0.6,0.1-0.6
		c0.2-0.1,0.8-0.7,1.1-1.3c0.3-0.6,0.4-0.1,0.8-0.3c0.4-0.2,0.6,0,0.9,0.3c0.3,0.3,0.7,0.8,1.7,1.4c1,0.6,0.9-0.4,1.3-0.8
		c0.4-0.4,1.3,0.2,1.9,0.3c0.6,0.1,1-1.1,1.2-1.8c0.1-0.4-0.1-1-0.3-1.6c-0.2-0.5-0.5-0.9-0.7-1.2c-0.5-0.7,0.1-0.7,0.4-1.1
		c0.3-0.4,0.7-0.1,1.4-0.3c0.7-0.2,0.6-0.2,1.2-0.9c0.6-0.6,0.9,0,1.7,0.4c0.7,0.4,0.4,1.4,0.4,2.2c0,0.8,0.5,1,0.9,1.4
		c0.4,0.4,1,1.3,1.4,1.9c0.4,0.7,1.2,0.8,1.5,0.7c0.3-0.1,0.3,0.1,0.7,0.3c0.4,0.2,0.7-0.1,1-0.4c0.3-0.4,0.6,0.6,0.6,1
		c0,0.4,0.2,0.6,0.5,1c0.2,0.4,0.1,0.6,0,0.9c-0.1,0.3,0,0.6,0.4,1c0.4,0.4,0.5,0.3,0.7,0.9c0.2,0.6,1.1,0.4,1.7,0.5
		c0.6,0.1,0.8,0.5,1.3,0.5c0.4,0,1.2-0.5,1.6-0.7c0.5-0.2,0.2-0.6,0.4-1.2c0.1-0.5,1.2-0.2,1.8-0.1c0,0.2,0.1,0.3,0.4,0.4
		c0.4,0.2,0.7,0.4,1.1,0.5c0.4,0,0.1,0.1,0.5,0.6c0.4,0.5,0.4,0.2,0.6,0.5c0.2,0.2,0.6,0,0.9-0.2c0.3-0.2,0.8,0.1,1.4-0.2
		c0.6-0.2,1.4-0.3,2.1-0.1c0.7,0.1,0.2,0.9,0,1.2c-0.3,0.3,0.1,0.3,0.3,0.3c0.2,0,0.3,0.1,0.3,0.3c0,0.2,1.1-0.2,1.5-0.4
		c0.3-0.2,0.3,0.4,0.3,0.9c0.1,0.5,0.9,0.2,1.3,0.1c0.3-0.1,0.3,0.5,0.4,0.8c0.2,0.2,0.5,0.4,0.9,0.8c0.4,0.4,0.7,0.2,0.9,0
		c0.2-0.2,0.6-0.2,0.9-0.3c0.3-0.1,1-0.1,1.4-0.1c0.3,0,0.6,0.4,0.8,0.6c0.1,0.1,0.2,0.2,0.3,0.3c0.3,0.2,1,0.5,1.2,0.7
		c0.2,0.2,0.8,0.3,1.5,0.5c0.7,0.2,0.3,0.9,0.5,1.3c0.2,0.4,1,0.2,1.2-0.1c0.2-0.3,0.5,0,0.9,0.1c0.2,0,0.3-0.1,0.4-0.3
		c0.1-0.2,0.1-0.5,0.1-0.7c0.1-0.4,0.5-0.5,0.3-1.1c-0.2-0.6-0.3-0.8-0.6-1.2c-0.3-0.4,0.1-0.3,0.1-1.3c0-1,0.2-0.2,0.7-0.5
		c0.4-0.3,0.7-0.1,0.9,0.1c0.2,0.2,0.5-0.3,0.7-0.6c0.2-0.4,0.5,0.1,0.9,0.1c0.4,0,0.3-0.5,0.4-1c0.1-0.4-0.6-0.3-0.7-0.6
		s-0.8-0.2-1.1-0.2c-0.4,0-0.3-0.7-0.2-1.1c0-0.5-1-0.5-1.7-0.6c-0.7-0.1,0.4-0.7,0.7-0.8c0.2-0.1,0.4,0.3,0.7,0.5
		c0.3,0.2,0.3,0,0.6-0.2c0.3-0.2,0.9,0.3,1,0.5c0.2,0.2,0.5-0.1,0.6-0.4c0.1-0.3,1.1,0.5,1.5,0.6c0.4,0.1,0.5-0.2,0.6-0.5
		c0.1-0.3,0.5,0.4,0.8,1c0.2,0.5,0.7-0.3,1.2-0.5c0.5-0.2,0.5,0,0.9-0.7l0.1-0.1c0.4-0.8,0.2-0.4,0.1-0.7c-0.1-0.3-0.2-0.4,0.2-0.9
		s-0.2-0.8-1-1c-0.8-0.3-0.1-0.1-0.2-0.5c-0.1-0.4-0.7-0.4-1.1-0.5c-0.4-0.1-0.1-0.5,0.1-0.9c0.2-0.4-0.2-0.8-0.5-1.3
		c-0.3-0.5-0.3-0.3-0.7-0.5c-0.4-0.2,0.1-0.2,0.4-0.4c0.3-0.2,0-0.4-0.2-0.6c-0.2-0.2-0.1-0.4,0.2-1c0.1-0.1,0.1-0.2,0.1-0.3
		c0-0.4-0.5-0.6-0.8-1.1c-0.5-0.6,0.1-0.8,0.6-1.6c0.6-0.8,0-0.9,0.1-1.5c0.1-0.6,0.4-0.7,0.8-1.1c0.4-0.5,0.1-0.4,0.2-0.9
		c0-0.5-0.1-0.7,0.1-1.5c0.2-0.9-0.6-1.1-0.9-1.6c-0.3-0.5-0.2-2.3-0.3-2.6c-0.1-0.3,0.4-0.9,0.6-1.4c0.2-0.5-0.2-0.5-0.5-0.8
		c-0.3-0.3,0.1-0.3,0.3-0.4c0.2-0.1-0.1-0.5-0.2-0.6c-0.1-0.1-0.1-0.4,0-0.5c0.1-0.1,0.1-0.3-0.1-0.4c-0.2-0.1,0.1-0.2,0.5-0.5
		c0.4-0.2,0-0.2-0.1-0.5c-0.1-0.3,1.3-1.1,1.5-1.4c0.3-0.3,0.2-0.4-0.2-0.7c-0.4-0.3,0.3-0.3,0.8-0.6c0.4-0.3,0.1-0.5,0.3-1.3
		c0.2-0.9-0.8-2.4-1-3.2c-0.1-0.9,1.6-2.1,2.2-2.5c0.6-0.4,0-0.6,0.1-0.8c0-0.2,0.1-0.4,0.4-0.6s0-1.1,0.2-1.6
		c0.2-0.5,0.6-0.5,0.8-0.6c0.2-0.1,0.1,0,0.1-0.4c0.1-0.4,0.2-0.5,0.4-0.8c0.2-0.3-0.2-0.6-0.5-0.9c-0.3-0.3,0.3-0.9,0.3-0.9
		c-0.2-0.3-0.3-0.5-0.6-0.4c-0.5,0.2-3.1,2.3-3.3,2.2c-0.2-0.1-0.4-1-0.7-0.8c-0.3,0.2-0.4,0.5-0.8,0.4c-0.4-0.1-0.6-0.3-0.9,0
		c-0.3,0.3-1.1,1.3-1.6,1.4s-0.4-0.4-0.7,0.3c-0.2,0.6-0.6,1.5-0.3,1.8c0.3,0.3,0.5,0.6,0.3,0.9c-0.1,0.2-0.3,0.4-0.6,0.3
		c-0.3,0-1-0.5-1.3,0.3c-0.3,0.8-1.3,1.6-1.8,2.1c-0.5,0.5-2.5,1.5-2.6,1.9c0,0.5-0.5,1.8-1.3,2.1c-0.8,0.3-1.1,0.2-1.2-0.3
		c-0.1-0.5,0.2-0.6-0.3-0.7c-0.5-0.1-0.6,0.4-1.3,0.3c-0.6,0-1.7,0.1-2.1-0.3c-0.1,0-0.1-0.1-0.1-0.2c-0.2-0.6,0-0.8-0.5-1.1
		c-0.5-0.2-0.5-0.2-0.7-0.6c-0.2-0.4-0.2-0.8-0.8-0.9c-0.5,0-3.7-0.5-3.6-1.2c0.2-0.7,1.2-0.5,1.1-0.9c0-0.4,0.5-1.8,0.7-2.1
		c0.3-0.3,1.5-1.2,1.2-1.9c-0.4-0.7-0.3-0.7-0.4-1.2c-0.1-0.5-0.7-1.2-1.2-1.1c-0.6,0.1-1,0.7-1.4,0.7c-0.4,0-0.8-0.2-0.9,0.2
		c-0.1,0.4-0.4,0.9-0.7,0.7c-0.3-0.1-0.4-0.1-0.4,0.3c0,0.4,0,0.8-0.2,1.2c-0.2,0.3-0.9,0.5-0.9,0.8c0,0.2-0.5,0.9-0.5,1.2
		c0,0.3-0.1,1.4-0.1,1.6c0.1,0.3,0.1,0.6,0.1,1c-0.1,0.4-0.2,1.6-0.1,1.9c0.1,0.3,0.1,0.8-0.4,0.9c-0.5,0.1-1.3,0.3-1.7,0
		c-0.3-0.2-0.6-0.2-1.1,0c-0.5,0.3-1.7,1.2-1.9,1.2c-0.3,0-0.7-0.1-1,0c-0.2,0.2-0.6,0.3-0.7,0c-0.1-0.2-0.4-0.5-0.7-0.3
		C88.4,40.9,87.6,41.5,87.2,41.6z"/>
</svg>
</div>


<!-- 华北 -->
<div style="width:142px; height:129px; left: 147px; top:63px;">
<svg id="area_100001" viewBox="0 0 142 129" class="area_stroke hidden " version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" >
<path d="M141.9,14.6c0.3-0.5-0.2-1.2-0.9-1.5c-0.7-0.3-1.6-1.9-1.9-2.2c-0.2-0.3-0.9-0.5-1.4-0.5s-0.6,1.2-0.9,1.5
	c-0.3,0.3-0.8,0.6-1.2,0.6c-0.4,0-0.7,0.2-1,0.6c-0.2,0.4-0.9-0.2-1.5-0.3c-0.6-0.1-2.7,1-2.7,1l-2.4-0.2c-0.2,0-0.9-1.7-1-1.9
	c-0.1-0.2-0.8-2.3-1-2.7c-0.3-0.4-0.2-0.8,0.3-1c0.4-0.1-0.8-0.8-1.3-0.9c-0.6-0.1-1.2-1.1-1.3-1.4c-0.1-0.3-0.7,0.1-0.9,0.5
	c-0.1,0.4-0.2,0.4-0.5,0.9c-0.4,0.4-0.6,0.3-1-0.2c-0.3-0.5-1.7-1.2-2.4-1.2c-0.7-0.1,0.2-0.9,0.8-1.3c0.6-0.4,0.4-0.7,0.6-1.2
	c0.2-0.5,0.6-1,0.8-1.1c0.2-0.1-1.3-2-1.3-2c-0.5,0.2-0.9,0.4-1.2,0.5c-1,0.4-1.2-0.1-1.9,0c-0.6,0.1-2.7,3.1-3.3,3.3
	c-0.6,0.3,0,1-0.4,1.7c-0.4,0.7,2-0.4,2.7-0.2c0.7,0.1,0.4,1.2,0.2,1.7s0,0.1,0.5,0.6c0.4,0.5,0.1,1.5,0,2.1
	c-0.1,0.6-0.2,0.3-0.7,0.4c-0.6,0.1-0.4,0.2-0.7,0.7c-0.3,0.5-0.6,0.6-1.2,1.1c-0.6,0.4-0.6,1.4-1,2.2c-0.4,0.8-0.2,0.9-0.2,1.5
	c-0.1,0.6-0.9,0.9-1.2,1.2c-0.3,0.3-0.2,0.7-0.2,1.2c0,0.5-0.4,0.7-0.7,1c-0.3,0.3-0.8,1.1-1.1,1.6c-0.2,0.5,0.1,0.6,1,0.6
	c0.8,0-0.2,1.4-0.6,2c-0.4,0.6-0.9,0.3-1.8,0.4c-0.9,0.1-3,2.6-3.7,3.1c-0.7,0.5-1.7,0.6-2-0.4c-0.3-0.9-1.1-0.2-2,0.1
	c-0.9,0.2-1.5-1.1-1.5-1.1s-2,3.4-2.5,4.2c-0.5,0.8-0.7,1.4-0.8,2.2c0,0.8-0.8,1.3-1.2,1.9c-0.5,0.6,0,1.1,0,1.8
	c0,0.7-0.9,1-1.4,1.6c-0.5,0.6,0.7,1.9,1.7,2.6c1,0.7,1.2-0.2,1.8-0.7c0.6-0.6,0.9-0.2,1.5,0.1c0.6,0.3,0.9-0.2,1.7-0.6
	c0.8-0.4,1.5,1.1,2,1.7c0.5,0.5,1-1.3,1.9-2.2c0.9-1,1.9-0.6,2.5-0.8c0.7-0.2,1.2,0.6,2,1c0.7,0.4,0.3,0.6,1.3,1
	c1,0.4,0.8,0.3,1,0.7c0.2,0.4,0.8,0.7,1.2,0.9c0.4,0.3,0.5,1.1,0.7,1.5c0.2,0.3,1.3,0.5,2.2,1.3c0.8,0.7,0.2,0.6,0.2,1.8
	c0.1,1.2-1.7,0.9-2.1,1.3c-0.4,0.4-1.7-0.5-2-1.1c-0.3-0.5-1.5,0.1-2,0.3c-0.5,0.2-0.4,0.2-1.4,0c-1-0.2-2.1,1.4-2.5,1.8
	c-0.4,0.4-0.9-0.1-1.4-0.7c-0.5-0.6-1.1,1.4-1.2,2.2c-0.1,0.8-1,0.1-1.3-0.4c-0.3-0.5-0.9-0.2-1.2-0.3c-0.4-0.2-2.5,2.2-2.8,2.8
	c-0.4,0.5-0.3,0.8,0,1.4c0.4,0.6-0.1,0.7-1.2,0.9c-1.2,0.1-0.6,0.4-1.5,1c-0.8,0.7-1,0.5-1.5,0.5c-0.5,0-1.2,0.2-1.7,0.5
	c-0.5,0.3-0.7-0.1-1.4-0.6c-0.8-0.6-1.9,1.9-2.6,2.8c-0.7,1-1.6,1.5-2.2,1.9c-0.6,0.4-2.3,0-3.6-0.1c-1.3-0.1-1.6-0.6-1.9-1
	c-0.3-0.4-0.8-0.4-1.2-0.5c-0.5-0.1-0.9-0.3-1.8-0.6c-0.8-0.3-0.7,0.2-1.3,1c-0.6,0.8-1,1.7-1.2,2.2c-0.2,0.5-0.5,1.1-0.9,1.5
	c-0.4,0.4,1.1,2.2,2.1,3.2c0.9,1-0.3,1.9-1.1,2.6c-0.9,0.6-1,0.6-2.2,0.9C70,69.3,68.2,72,68,72s-2.2,1.9-2.2,1.9l-2,0
	c0,0-1.3,0.7-1.8,1.2c-0.5,0.4-1.3,0.7-2.4,0.4s-5.4-0.2-6.9-0.3c-1.4,0-5.9,0.9-8.2,1.6c-2.3,0.7-6.2,3.1-6.3,3.1
	c-0.1,0-2.5-0.7-2.5-0.7l-0.1-1.6L32,78.1l-4.3-2.1l-4.4-1.6l-1.4-2.5l-5.4-0.7c-0.1,0-3.5-1.4-3.6-1.4c-0.1,0-4.4,0.7-4.4,0.7
	s-7.7-2.1-7.8-2.1c0,0,0.2,4.4,0.1,5.2C0.7,74.3,0,75,0,76.1s0.4,1.6,0.5,2.1c0.1,0.5,0,2.2-0.1,2.6c-0.1,0.4,0.8,1.2,1,1.7
	c0.2,0.5,0.4,1.2,0.5,1.7c0.2,0.5,0.9,0.4,1.4,0.5c0.5,0.1,0.8-0.6,1.1-0.8c0.3-0.2,0.6-0.1,1.1,0c0.6,0,0.9-0.8,1.3-1.1
	c0.4-0.3,1.4,0.2,1.7,0.3c0.3,0.2,1.3,0,2,0.2c0.7,0.2,0.4,1.5,0.2,2c-0.2,0.5-0.3,1.2-0.3,1.7c0,0.5-0.7,0.3-1.1,0.2S9,87.5,9,87.8
	c0,0.4,0.6,0.7,0.9,1.1c0.4,0.4,0.8,0.8,1.4,1.1c0.6,0.3,0.4,0.6,0.5,1.1c0.2,0.6,2,0.5,2.7,0.6c0.6,0.1,0.4,1.1,0.2,1.5
	c-0.2,0.4,0.1,1.7,0.5,2.2c0.4,0.5,1.7,0.5,2,0.5c0.4,0,0.6,0.2,1,0.6c0.4,0.4,1.3,0.2,1.7,0.2c0.4,0.1,0.8,0.4,1.3,0.4
	c0.5,0,2.2-1.8,2.5-2.2c0.3-0.4,1.1-0.1,1.8,0.1s0.7-0.5,1.1-0.8c0.4-0.4,2.4,0,3.3,0.1c0.9,0.2,1.4,0.7,2.1,1.1
	c0.7,0.4,0.5,1.1,0.5,1.3c0,0.2-0.1,0.6-0.6,1c-0.6,0.4-0.4,0.7-0.1,0.9s0,0.7,0,1.4c0,0.8-2.1,0.9-2.5,0.9c-0.4,0-0.5,0.4-0.3,0.6
	c0.2,0.2,0.1,0.4,0,0.9c0,0.5-0.6,0.8-0.6,1.6c0,0.8,0.6,0.9,1.3,1.3c0.7,0.3,0.3,0.7,0.4,1.1c0.1,0.4,0.4,0.4,0.6,0.4
	c0.2,0,0.7,0.6,1,0.6h0.1c0.4-0.1,1.2-0.6,1.5-0.7c0.2-0.1,0.5,0.1,0.6,0.3c0.1,0.2,0.5,0.2,0.7,0c0.2-0.2,0.7,0,1,0
	c0.3,0,1.5-0.9,1.9-1.2c0.5-0.3,0.7-0.3,1.1,0c0.3,0.2,1.2,0.1,1.7,0c0.5-0.1,0.5-0.6,0.4-0.9c-0.1-0.3,0-1.5,0.1-1.9
	c0.1-0.4,0-0.7-0.1-1c-0.1-0.3,0-1.3,0.1-1.6c0-0.3,0.5-1,0.5-1.2c0-0.2,0.7-0.4,0.9-0.8c0.2-0.3,0.2-0.8,0.2-1.2
	c0-0.4,0.1-0.4,0.5-0.3c0.3,0.1,0.7-0.3,0.7-0.7c0.1-0.4,0.5-0.3,0.9-0.2c0.4,0,0.8-0.5,1.4-0.7c0.6-0.1,1.1,0.5,1.2,1.1
	c0.1,0.6,0.1,0.6,0.4,1.2c0.4,0.7-0.9,1.6-1.2,1.9c-0.3,0.3-0.8,1.7-0.7,2.1c0,0.4-1,0.2-1.1,0.9c-0.2,0.7,3.1,1.2,3.6,1.2
	c0.5,0,0.6,0.4,0.8,0.9c0.2,0.4,0.2,0.4,0.7,0.6c0.5,0.2,0.3,0.5,0.5,1.1c0,0.1,0.1,0.1,0.1,0.2c0.4,0.3,1.5,0.2,2.1,0.3
	c0.7,0,0.7-0.4,1.3-0.3c0.5,0.1,0.2,0.2,0.3,0.7c0.1,0.5,0.4,0.5,1.2,0.3c0.8-0.3,1.2-1.6,1.3-2.1c0-0.5,2.1-1.5,2.5-1.9
	c0.5-0.5,1.5-1.2,1.8-2.1c0.3-0.8,1-0.4,1.3-0.3c0.3,0,0.4-0.1,0.6-0.3c0.1-0.2-0.1-0.6-0.3-0.9c-0.3-0.3,0.1-1.2,0.3-1.8
	c0.2-0.6,0.2-0.2,0.7-0.3c0.5-0.1,1.2-1,1.6-1.4c0.3-0.3,0.5-0.1,0.9,0c0.4,0.1,0.5-0.2,0.8-0.4c0.3-0.2,0.5,0.7,0.7,0.8
	c0.2,0.1,2.8-1.9,3.3-2.2c0.2-0.1,0.4,0.1,0.6,0.4c0,0-0.6,0.6-0.3,0.9c0.3,0.3,0.6,0.6,0.5,0.9c-0.2,0.3-0.3,0.4-0.4,0.8
	c0,0.3,0.1,0.3-0.1,0.4c-0.2,0.2-0.6,0.1-0.8,0.6s0.1,1.4-0.2,1.6c-0.3,0.2-0.4,0.4-0.4,0.6c0,0.2,0.5,0.4-0.1,0.8
	c-0.6,0.4-2.3,1.7-2.2,2.5c0.1,0.9,1.1,2.4,1,3.2c-0.2,0.9,0.2,1-0.3,1.3c-0.5,0.3-1.1,0.3-0.8,0.6c0.4,0.3,0.5,0.5,0.2,0.7
	c-0.3,0.3-1.7,1.1-1.5,1.4c0.1,0.3,0.5,0.3,0.1,0.5c-0.4,0.2-0.7,0.3-0.5,0.4c0.2,0.1,0.2,0.3,0.1,0.4c-0.1,0.1-0.1,0.4,0,0.5
	c0.1,0.1,0.4,0.5,0.2,0.6c-0.2,0.1-0.5,0.1-0.3,0.4c0.3,0.3,0.7,0.3,0.5,0.8c-0.2,0.5-0.8,1-0.6,1.3c0.1,0.3,0,2.1,0.3,2.6
	c0.3,0.5,1.1,0.7,0.9,1.6c-0.2,0.9-0.1,1-0.1,1.5c0,0.5,0.2,0.4-0.2,0.9c-0.4,0.5-0.7,0.5-0.8,1.1c-0.1,0.6,0.5,0.7-0.1,1.5
	c-0.6,0.8-1.1,1-0.6,1.6c0.4,0.5,0.8,0.7,0.8,1.1c0.1,0,1.2,0,1.8,0.1c0.6,0,1.6-0.5,2.1-1.1s1.7-0.2,2.4-0.4c0.8-0.3,1-0.9,1.2-1.2
	c0.2-0.3,0.7-0.3,1.2-0.3c0.5,0,0.5-0.3,0.5-0.8c0-0.5,0.7-0.3,1.1-0.2c0.4,0,0.9,0,1.3-0.4c0.4-0.4,1.1,0.2,1.8,0.3
	c0.8,0.1,1.7-0.5,2.1-1.1c0.4-0.6,1-0.6,1.6-0.9c0.6-0.2,0.5-1,0.7-1.2c0.2-0.2-0.1-0.7,0.1-0.8c0.2-0.1-0.1-0.4,0.2-0.6
	c0.3-0.2,0-0.8,0.2-1.1c0.2-0.3,0.3-1.3,0.4-1.8v-0.2c0-0.3,0.4,0.2,0.7,0.5c0.3,0.3,1.9,0.4,2.3,0.3c0.4-0.1,0.4,0.3,0.7,0.7
	c0.3,0.5,1.1,0.1,1.8,0.2c0.7,0.1,0.7,0.5,0.9,0.7s0.3-0.2,0.4-0.8c0.1-0.6,0.4-0.4,0.6-0.5c0.2-0.1,0.8,0.7,1.3,0.8
	c0.3,0,0.4,0,0.5-0.1c0.1-0.1,0.1-0.3,0.1-0.5c0.1-0.7-0.4-0.6-0.5-1.3c-0.2-0.7,0.4-1.1,0.6-1.9c0.2-0.8,1.4-1.3,1.6-1.9
	c0.2-0.6,0.1-0.5,0.5-1.4c0.4-0.8,0.5-0.4,0.8-0.5c0.3-0.2,0.2-0.3,0.1-0.6c-0.1-0.4,0.3-0.4,0.6-0.5c0.3-0.1,0.2-0.1,0.4-0.6
	c0.2-0.5,1.1-0.7,1.5-0.8c0.4-0.1,0.6-0.3,1-0.9c0.4-0.6,0.7-0.7,1.1-1.1c0.5-0.4,1,0,1.4,0.2c0.4,0.2,0.7-0.2,1.4-0.2
	c0.7,0.1,0.7-0.5,0.7-0.9c0.1-0.4,0.4-0.5,0.7-0.6c0.4-0.1,0.5-0.2,0.8-0.7c0.4-0.5,1.2-0.8,1.2-0.8c-0.1,0-0.2,0-0.2-0.1
	c-0.3-0.1-0.6-0.3-0.8-0.6c-0.2-0.2-0.4-0.4-0.6-0.6c-0.2-0.2-0.4-0.3-0.5-0.6c0-0.1,0-0.2-0.1-0.2c0-0.2,0-0.4-0.1-0.6
	c0-0.3,0-0.6,0.1-0.8c0.1-0.3,0.3-0.5,0.5-0.7c0.2-0.3,0.3-0.5,0.3-0.9c0-0.3,0-0.6,0.1-0.8c0.1-0.2,0.3-0.5,0.5-0.6
	c0.1-0.1,0.2-0.1,0.3-0.2l0.2,0c0.3,0,0.6,0,0.9,0.1c0.2,0.1,0.6,0.1,0.7,0.4c0,0.1,0,0.1,0.1,0.2c0,0.1,0.1,0.1,0.1,0.2
	c0.1,0.1,0.2,0.1,0.3,0.2c0.2,0,0.3,0,0.5,0c0.4,0,0.6-0.3,0.6-0.6c0-0.1,0-0.2,0-0.3c0-0.1,0-0.1,0-0.2c0.1-0.2,0.1-0.2,0.3-0.2
	c0.1,0,0.1,0,0.2,0l0.2,0c0.2,0,0.4,0.1,0.5,0.2c0.1,0.1,0.2,0.3,0.4,0.3c0.1,0,0.1-0.1,0.2-0.2s0.1-0.1,0.2,0c0,0,0.1,0.1,0.1,0.1
	c0.1,0.1,0.3-0.2,0.4-0.3c0.2-0.1,0.3-0.3,0.4-0.4c0.1-0.1,0.2-0.2,0.4-0.3c0.1-0.1,0.3-0.1,0.4-0.1c0.1,0,0.2,0,0.3-0.2
	c0.1-0.1,0-0.3,0-0.4c0-0.2,0.1-0.3,0.1-0.5l0.1-0.6c0-0.4,0.1-0.8,0.3-1.1c0.1-0.1,0.2-0.2,0.4-0.3c0.2-0.2,0.4-0.3,0.5-0.5
	c0.1-0.1,0.2-0.3,0.4-0.4c0.2-0.1,0.3-0.2,0.4-0.3c0.2-0.1,0.3-0.3,0.5-0.5c-0.3-0.4-0.9-1.1-1.3-1.5c-0.6-0.5-0.4-0.4-0.4-1.2
	c0.1-0.8-0.8-0.5-1.3-0.4c-0.4,0.1-0.5-0.4-0.6-0.8c0-0.3-0.5-0.2-0.8-0.1c-0.4,0.1-0.6-0.2-0.8-0.6c-0.2-0.3-0.2,0.2-0.4-1
	c-0.2-0.9,1.4-2.3,2.1-2.9c0.5,0,0.5-0.2,0.8-0.9c0.3-0.8-0.9-2.3-0.9-2.3s-0.2-0.8-0.3-1.2c0-0.4,0.4-0.5,0.6-0.8
	c0.2-0.2-0.3-1.1-0.4-1.5c-0.2-0.3,0-0.4,0.4-0.5c0.4-0.1,0.4-0.2,0.6-0.7c0.2-0.4,0.6,0.6,0.8,1.2c0.2,0.6,0.5,0.1,1,0.2
	c0.6,0.1,0.1,0,0.3,0.6c0.2,0.6,0.6,0.6,1,0.9s0,0.5,0.1,0.9c0.1,0.4,0.9-0.5,1.5-0.8c0.6-0.3,0.3-0.7,0.4-0.8
	c0-0.1,0.3-0.4,0.6-0.5c0.2-0.1,0.9-0.9,1.3-1.3c0.4-0.4,0.4-0.2,0.8-0.3c0.4-0.1,0.4,0.2,0.6,0.5c0.2,0.4,1.4-0.9,1.9-1.4
	c0.5-0.5,0.8-0.5,0.9-0.4c0.2,0,0.9,0,1.4-0.4c0.5-0.4,0.7-0.6,0.7-1c0-0.4,0.9-0.6,1.2-0.5c0.4,0,0.4,0.7,0.9,0.5
	c0.5-0.2-0.3-1-0.2-1.4c0.1-0.5,0.8-0.2,1.2-0.1c0.4,0.1,0.9,0.6,1.6,0.6c0.6,0.1,1.6-1.1,1.9-1.7c0.3-0.6,0.9-0.7,1.5-0.9
	c0.6-0.3,0.6-0.1,0.6-1c0-0.5-0.2-0.8-0.4-1c-0.2-0.2-0.4-0.2-0.5-0.3c-0.3-0.2-0.6-0.6-0.5-1c0.1-0.4-0.2-1.5-0.4-1.8
	c-0.2-0.3-0.7-1.1-0.6-1.8c0.1-0.6-1-0.8-1.3-1c-0.3-0.2-0.3-0.7-0.2-1.4c0.1-0.7-0.8,0.3-1.3,0.8c-0.4,0.5-0.8,0.5-1.6,0.7
	c-0.8,0.1-0.6,0-1,0.6c-0.4,0.6-0.1-0.3-0.3-0.5c-0.2-0.3-0.2-0.7,0.1-0.9c0.3-0.2-0.6-1-1-1.3c-0.4-0.3-0.2-2.4-0.2-2.9
	c0-0.5,0.2-0.5,0.6-0.7c0.4-0.2,0.2-0.5,0-1c-0.2-0.5-0.7-0.3-0.9-0.1c-0.3,0.2-1.2-1.5-1.4-1.8c-0.2-0.3-0.8-0.5-0.8-0.9
	c0-0.4,1-0.4,1.6-0.3c0.5,0.1,1.4-0.5,2.1-0.8s0.7-0.1,1.1,0.4c0.4,0.4,0.7,0.4,1.5-0.2c0.8-0.6,1-1.4,1.1-2.1c0-0.2,0-0.4,0-0.5
	c-0.1-0.3-0.4-0.3-0.7-0.4c-0.4-0.1-0.6-1-0.6-1.5c0-0.5,0.3-0.7,0.6-0.8c0.3,0,0.4,0.3,0.7,0.4c0.3,0.1,1.6-0.9,1.6-1.6
	c0-0.7-0.5-0.4-0.8,0c-0.4,0.4-0.4,0-0.6-0.4c-0.2-0.4-0.6,0.1-1,0.6c-0.4,0.5-1-1.1-1.2-1.5c-0.2-0.4-0.2-0.2-1-0.3
	c-0.8-0.2-0.8-1-1-1.9c-0.1-0.9,0.7-1.7,1-1.9c0.3-0.2,1.8-1.2,2.2-2c0.4-0.8,1.8-1.5,1.9-1.6c0.2-0.1,1.7-2.4,2.2-2.8
	c0.5-0.4,0.6-0.2,0.5,0c0,0.2,0.1,0.8,0.1,0.8c0.1,0.4,0.6,1.1,1,1.4c0.4,0.4,0.4-1.2,0.1-1.7c-0.3-0.5,0.1-1.2,0.1-1.8
	s0.7-2.3,0.7-2.6c0-0.4,0.2-0.8,0.7-1.1s0.4,0,0.7,0.2c0.3,0.2,0.6-0.7,0.8-1.4c0.2-0.7-0.5-1.7-0.6-1.8c-0.1-0.1,0.1-1.1,0-1.3
	c-0.1-0.2-0.1-0.9,0.1-1c0.2-0.1,0.1-0.6,0-0.8c0-0.2,0.4-0.8,0.6-1.1c0.1-0.3,0.3-0.8,0.3-1.3s0.2-0.5,0.5-0.8
	c0.3-0.3,0.4-1.1,0.2-1.6C140.8,15.7,141.6,15,141.9,14.6z"/>
</svg>
</div>


<!-- 东北  暂时用中南的ID 100003-->
<div style="width:75px; height:102px; left: 258px; top:61px;">
<svg id="area_100003" viewBox="0 0 75 102" class="area_stroke hidden " version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" >
<path d="M63.6,64.1c0.1-0.2,0.1-0.3,0-0.5c-0.1-0.1-0.2-0.2-0.3-0.3c-0.1,0-0.2-0.1-0.3-0.2c-0.2-0.2-0.3-0.4-0.4-0.6
	c-0.1-0.4-0.1-0.7-0.1-1.1c0-0.2,0.1-0.3,0.1-0.4c0-0.2,0.1-0.4,0.1-0.6c0.1-0.4,0.2-0.8,0.1-1.2c0-0.3-0.1-0.6-0.3-0.9
	c-0.2-0.3-0.2-0.7-0.4-1c-0.2-0.4-0.3-0.8-0.7-1.1c-0.2-0.1-0.3-0.3-0.5-0.4c-0.1-0.1-0.2-0.3-0.3-0.5c-0.1-0.1-0.2-0.3-0.2-0.5
	c-0.1-0.3,0.1-0.7,0.3-0.9c0.3-0.2,0.6-0.2,0.9-0.2c0.4-0.1,0.8-0.1,1.1-0.4c0.1-0.1,0.2-0.3,0.2-0.5c0.1-0.2,0.2-0.4,0.3-0.5
	c0.2-0.3,0.2-0.7,0.4-1c0-0.1,0-0.2,0.1-0.4c0.1-0.2,0.3-0.3,0.4-0.4c0.2-0.1,0.3,0.1,0.5,0.2c0.2,0.1,0.4,0.1,0.5,0.2
	c0.2,0.1,0.3,0.1,0.5,0.1c0.5,0,1,0.1,1.5,0.1c0.8,0.1,1.8,0.3,2.6,0c0.3-0.1,0.6-0.4,0.7-0.7c0.1-0.3,0-0.7,0-1
	c-0.1-0.4-0.1-0.7,0.1-1c0.2-0.4,0.5-0.6,0.8-1c0.2-0.3,0.3-0.6,0.3-1c0-0.3-0.1-0.7,0-1c0.2-0.7,0.8-1.1,1.1-1.8
	c0.3-0.6,0.4-1.2,0.3-1.9c-0.1-0.3-0.2-0.6-0.3-1c-0.1-0.3,0-0.6,0.1-0.9c0.1-0.3,0.2-0.5,0.2-0.8c0-0.3-0.1-0.6-0.2-0.9
	c0-0.2,0-0.4,0-0.6c0-0.3,0.1-0.7,0.2-1c0.1-0.2,0.3-0.6,0.1-0.9c-0.1-0.1-0.2-0.1-0.3-0.1c0.2-0.4,0.4-0.9,0.8-1.2
	c0.3-0.3,0.8-0.3,1-0.7c0.2-0.4,0.3-0.8,0.4-1.2c0.1-0.4,0.2-0.8-0.2-1.1c-0.3-0.3-0.7-0.5-0.8-0.9c-0.2-0.4,0-0.8,0.1-1.1
	c0.2-0.5,0.4-1,0.2-1.5c-0.1-0.3-0.4-0.6-0.8-0.5c-0.2,0-0.5,0.1-0.7,0.2l-0.7,0.3c-0.2,0.1-0.4,0.2-0.6,0.3
	c-0.3,0.2-0.7,0.5-0.9,0.8c-0.2,0.3-0.7,0.5-1,0.7c-0.2,0.1-0.4,0.4-0.4,0.6c-0.1,0.2-0.1,0.5-0.4,0.5c-0.1,0-0.2,0-0.4,0
	c-0.1,0-0.3,0-0.4-0.1c-0.2-0.1-0.4-0.1-0.6,0c-0.2,0.1-0.4,0.3-0.5,0.4c-0.2,0.2-0.4,0.3-0.6,0.5c-0.2,0.2-0.4,0.4-0.6,0.5
	c-0.2,0.1-0.4,0.1-0.5,0.3C65.2,32.6,65,32.8,65,33c-0.1,0.2-0.1,0.4-0.3,0.6c-0.1,0.1-0.2,0.1-0.3,0.2c-0.1,0.1-0.2,0-0.3,0
	c-0.2,0-0.4-0.1-0.6-0.1c-0.1,0-0.2,0-0.3,0.1C63,33.9,62.9,34,62.8,34c-0.3,0.1-0.5,0.3-0.8,0.4c-0.1,0.1-0.3,0.1-0.4,0.1
	c-0.1,0-0.2,0-0.3,0c-0.2,0.1-0.4,0.3-0.7,0.3c-0.2,0-0.5,0-0.7-0.1c-0.1,0-0.1-0.1-0.2-0.1c0-0.1-0.1-0.2-0.2-0.2
	c-0.1,0-0.2,0.1-0.3,0.1l-0.1,0.1c-0.2,0.2-0.5,0.5-0.8,0.5c-0.1,0-0.2,0-0.4,0.1c-0.2,0-0.5,0-0.8-0.1c-0.1,0-0.2,0-0.2-0.1
	c-0.1-0.1-0.1-0.2-0.2-0.4c-0.1-0.2-0.3-0.4-0.4-0.6c-0.1-0.2-0.3-0.3-0.4-0.5c-0.2-0.3-0.4-0.5-0.5-0.8c-0.1-0.1-0.3-0.3-0.3-0.5
	c0-0.2,0.2-0.2,0.3-0.4c0.3-0.3,0.3-0.7,0.1-1c-0.1-0.2-0.3-0.3-0.5-0.4c-0.1-0.1-0.3-0.1-0.5-0.2c-0.4-0.1-0.5-0.5-0.6-0.9
	c0-0.3,0.1-0.6,0.1-0.8c0-0.2,0.1-0.5,0-0.7c0-0.1,0-0.2-0.1-0.2c0,0-0.2,0-0.2,0c-0.2,0-0.3,0.1-0.4,0.1c-0.2,0-0.4-0.1-0.6-0.1
	c-0.1,0-0.2,0-0.2,0.1c-0.2,0-0.4,0-0.6,0.1c-0.2,0.1-0.4,0-0.5-0.1c-0.2-0.1-0.3-0.3-0.4-0.5c-0.1-0.2-0.3-0.3-0.6-0.4
	c-0.2-0.1-0.5-0.1-0.7-0.2c-0.2-0.1-0.2-0.2-0.3-0.4c0-0.1-0.1-0.2-0.2-0.2c-0.1,0-0.3,0-0.4-0.1c-0.3-0.2-0.3-0.8-0.6-1.1
	c-0.2-0.3-0.6-0.3-0.9-0.1c-0.2,0.1-0.4,0.2-0.5,0.4c-0.1,0.1-0.3,0.3-0.5,0.3c-0.3,0-0.4-0.4-0.7-0.5c-0.1,0-0.3,0-0.4,0
	c-0.2,0-0.3-0.1-0.4-0.2c-0.2-0.2-0.4-0.6-0.7-0.6c-0.2,0-0.4,0.1-0.5,0.1c-0.2,0-0.4,0.1-0.5,0.2c-0.4,0.2-0.5,0.6-0.9,0.8
	c-0.1,0.1-0.3,0.1-0.4,0.1c-0.1-0.1-0.2-0.1-0.3-0.1c-0.3,0-0.5,0.2-0.7,0c-0.1-0.1-0.2-0.2-0.3-0.3c-0.1-0.1-0.3-0.1-0.4-0.2
	c-0.3-0.1-0.5-0.2-0.7-0.5c-0.1-0.3-0.3-0.5-0.5-0.7s-0.4-0.4-0.7-0.6C38.1,22.6,38,22.3,38,22c0-0.3,0.1-0.6,0.2-0.9
	c0-0.3,0-0.5-0.2-0.7c-0.1-0.1-0.2-0.2-0.3-0.3c-0.1-0.1-0.1-0.2-0.1-0.3c-0.1-0.2-0.3-0.3-0.4-0.4c-0.1-0.1-0.2-0.3-0.2-0.4
	c0-0.2-0.1-0.3-0.2-0.5c0-0.1,0-0.3,0-0.4c-0.1-0.2-0.3-0.4-0.4-0.6c-0.2-0.3-0.3-0.6-0.4-0.9c-0.2-0.4-0.6-0.7-1-1
	c-0.3-0.2-0.7-0.4-0.8-0.8c0-0.1,0-0.2,0.1-0.4c0.1-0.1,0.1-0.2,0.1-0.4c0-0.1,0-0.3,0-0.4c0-0.2-0.1-0.2-0.3-0.2
	c-0.1-0.1-0.2-0.1-0.2-0.3c0-0.1,0-0.2,0-0.4c-0.2-0.2-0.4-0.4-0.5-0.6c-0.1-0.2,0-0.6,0-0.8c0-0.1,0.1-0.2,0.1-0.3
	c0-0.1-0.1-0.2-0.2-0.3c-0.4-0.2-0.8-0.4-1.1-0.8c-0.2-0.2-0.3-0.5-0.3-0.9c0-0.2,0.2-0.6,0.1-0.8c-0.1-0.3-0.4-0.3-0.7-0.3
	c-0.1,0-0.2-0.1-0.3-0.2c0-0.1,0-0.2,0.1-0.3c0.1-0.2,0.1-0.5,0-0.7c-0.1-0.2-0.2-0.4-0.4-0.5C30.4,6.1,30.1,6,30,5.7
	c0-0.1,0-0.2,0-0.3c-0.1-0.1-0.3-0.1-0.4-0.1c-0.2,0-0.4-0.1-0.4-0.4c0-0.3,0.2-0.5,0.3-0.8c0.3-0.6-0.2-0.4-0.8-0.2
	c-0.6,0.1-0.3-0.4-0.4-0.7C28.2,3,28,3.2,27.5,3.6C27,4.1,27.1,2.3,26.7,2c-0.4-0.3-0.8-0.4-1.5-0.7c-0.7-0.3-1,0.3-1.1,0.9
	c-0.1,0.5-0.6,0-0.7-0.3c-0.1-0.3-0.6-0.2-0.9-0.2c-0.3,0.1-1-0.5-1.2-0.8c-0.2-0.3-0.8,0-1.2,0.1c-0.4,0.2-0.5-0.3-0.6-0.7
	S17.8,0,17.5,0.1s-0.6,0.2-1,0c-0.4-0.2-0.2,0.1-0.8,0.5c-0.6,0.4-1.5,0.1-2.3,0.4c-0.8,0.3-1.1-0.2-1.5-0.4
	c-0.3-0.2-2.2,0.8-3.5,1.4c0,0,1.5,1.9,1.3,2C9.5,4,9.1,4.5,9,5C8.8,5.5,8.9,5.8,8.4,6.2C7.8,6.6,6.9,7.5,7.6,7.5
	c0.6,0.1,2,0.7,2.4,1.2c0.3,0.5,0.6,0.6,1,0.1s0.4-0.5,0.5-0.9c0.1-0.4,0.8-0.8,0.9-0.5c0.1,0.3,0.7,1.4,1.3,1.4
	C14.2,9,15.4,9.6,15,9.8c-0.4,0.2-0.6,0.6-0.3,1c0.3,0.4,1,2.5,1,2.7c0.1,0.2,0.7,1.9,1,1.9l2.4,0.2c0,0,2.1-1.1,2.7-1
	c0.6,0.1,1.3,0.7,1.5,0.3c0.2-0.4,0.5-0.5,1-0.6c0.4,0,1-0.3,1.2-0.6c0.3-0.3,0.4-1.5,0.9-1.5c0.5,0,1.2,0.2,1.4,0.5
	C28,13,29,14.7,29.7,15c0.6,0.3,1.2,1.1,0.9,1.5c-0.3,0.4-1.1,1.1-1,1.6c0.1,0.5,0,1.2-0.2,1.6c-0.3,0.3-0.5,0.4-0.5,0.8
	c0,0.4-0.2,0.9-0.3,1.3c-0.2,0.4-0.6,0.9-0.6,1.1c0,0.2,0.1,0.7,0,0.8c-0.2,0.1-0.1,0.8-0.1,1c0.1,0.2-0.1,1.2,0,1.3
	c0.1,0.1,0.8,1.1,0.6,1.8c-0.2,0.7-0.5,1.6-0.8,1.4c-0.3-0.2-0.2-0.4-0.7-0.2c-0.5,0.2-0.7,0.7-0.7,1.1c0,0.4-0.7,2-0.7,2.6
	c-0.1,0.6-0.4,1.4-0.1,1.8c0.3,0.5,0.2,2.1-0.1,1.7c-0.4-0.4-0.9-1-1-1.4c0,0-0.1-0.6-0.1-0.8c0-0.2-0.1-0.4-0.5,0
	c-0.5,0.4-2,2.7-2.2,2.8c-0.1,0.1-1.5,0.8-1.9,1.6c-0.4,0.8-1.9,1.8-2.2,2c-0.3,0.2-1.1,1-1,1.9c0.1,0.9,0.2,1.7,1,1.9
	c0.8,0.2,0.8-0.1,1,0.3c0.2,0.4,0.9,2,1.2,1.5c0.4-0.5,0.7-1,0.9-0.6c0.2,0.4,0.3,0.8,0.6,0.4c0.3-0.4,0.9-0.7,0.8,0
	c0,0.7-1.3,1.8-1.6,1.6c-0.3-0.1-0.4-0.5-0.7-0.4c-0.3,0-0.6,0.3-0.6,0.8c0,0.5,0.2,1.3,0.6,1.5c0.3,0.1,0.6,0.1,0.7,0.4
	c0,0.1,0,0.2,0,0.5c-0.1,0.7-0.3,1.6-1.1,2.1c-0.8,0.5-1.1,0.6-1.5,0.2c-0.4-0.5-0.4-0.7-1.1-0.4c-0.7,0.3-1.5,0.9-2.1,0.8
	c-0.5-0.1-1.5-0.1-1.6,0.3c0,0.4,0.5,0.6,0.8,0.9c0.2,0.3,1.2,2,1.4,1.8c0.3-0.2,0.7-0.4,0.9,0.1c0.2,0.5,0.4,0.8,0,1
	c-0.4,0.2-0.6,0.1-0.6,0.7c0,0.5-0.2,2.6,0.2,2.9c0.4,0.3,1.3,1.1,1,1.3c-0.3,0.2-0.3,0.6-0.1,0.9c0.2,0.3,0,1.1,0.3,0.5
	c0.4-0.6,0.2-0.4,1-0.6c0.8-0.2,1.1-0.1,1.6-0.7c0.4-0.5,1.4-1.5,1.3-0.8c-0.1,0.7-0.1,1.2,0.2,1.4c0.3,0.2,1.3,0.4,1.3,1
	c-0.1,0.6,0.4,1.5,0.6,1.8c0.2,0.3,0.6,1.3,0.4,1.8c-0.1,0.4,0.2,0.9,0.5,1c0.1,0.1,0.3,0.2,0.5,0.3c0.2,0.2,0.5,0.5,0.4,1
	c-0.1,0.9-0.1,0.7-0.6,1c-0.6,0.3-1.2,0.3-1.5,0.9c-0.3,0.6-1.3,1.8-1.9,1.7c-0.6-0.1-1.2-0.6-1.6-0.6c-0.4-0.1-1.1-0.3-1.2,0.1
	c-0.1,0.4,0.7,1.2,0.2,1.4s-0.5-0.4-0.9-0.5c-0.4,0-1.3,0.1-1.2,0.5c0,0.4-0.1,0.7-0.7,1.1c-0.5,0.4-1.2,0.4-1.4,0.4
	c-0.2,0-0.4-0.1-0.9,0.4c-0.5,0.5-1.7,1.8-1.9,1.4c-0.2-0.4-0.2-0.6-0.6-0.6s-0.4-0.2-0.8,0.3s-1.1,1.2-1.3,1.3
	c-0.2,0.1-0.5,0.4-0.6,0.5c0,0.1,0.2,0.5-0.4,0.8c-0.5,0.3-1.4,1.2-1.5,0.8c-0.1-0.4,0.3-0.5-0.1-0.9c-0.4-0.4-0.8-0.4-1-1
	c-0.2-0.6,0.3-0.6-0.3-0.6c-0.6-0.1-0.9,0.4-1-0.2c-0.2-0.6-0.6-1.6-0.8-1.2c-0.2,0.4-0.2,0.6-0.6,0.7c-0.4,0.1-0.6,0.2-0.4,0.5
	c0.2,0.4,0.6,1.2,0.4,1.5c-0.2,0.2-0.6,0.4-0.6,0.8c0,0.4,0.3,1.2,0.3,1.2s1.2,1.5,1,2.3c-0.3,0.7-0.2,0.9-0.8,0.9
	c-0.7,0.6-2.2,1.9-2.1,2.9c0.2,1.2,0.2,0.7,0.4,1s0.4,0.7,0.8,0.6c0.4-0.1,0.8-0.2,0.9,0.1c0,0.3,0.1,0.9,0.6,0.8
	c0.4-0.1,1.3-0.3,1.3,0.4c-0.1,0.7-0.2,0.7,0.4,1.2c0.4,0.3,1,1.1,1.3,1.5C5.8,93,5.9,92.9,6,92.8c0.2-0.1,0.4-0.3,0.6-0.4
	c0.1,0,0.2-0.1,0.2-0.1C7,92.2,7.1,92,7.3,91.9c0.2-0.1,0.4-0.2,0.7-0.2c0.1-0.1,0.2-0.1,0.4-0.2c0.2-0.1,0.3-0.2,0.5-0.2
	c0.1,0,0.2-0.2,0.2-0.3c0-0.1,0.1-0.1,0.1-0.2c0,0,0.1-0.1,0.1-0.1c0-0.1,0-0.2,0-0.3c0-0.1,0-0.2,0.1-0.3c0.1-0.1,0.2-0.2,0.2-0.4
	c0.1-0.2,0.2-0.4,0.3-0.5c0.1-0.1,0.3-0.2,0.4-0.3c0.2-0.1,0.3-0.2,0.5-0.4c0.2-0.2,0.4-0.4,0.6-0.6c0.1-0.1,0.1-0.2,0.1-0.2
	c0-0.1,0-0.1,0-0.2c-0.1-0.1-0.1-0.3-0.1-0.4c0-0.2,0.2-0.4,0.3-0.4c0.1-0.1,0.2-0.1,0.4,0c0.2,0.1,0.4,0,0.6,0c0.1,0,0.3,0,0.4,0
	c0.6,0.1,1.2,0.2,1.7,0c0.3-0.1,0.6-0.3,0.7-0.6c0.1-0.2,0.1-0.4,0.2-0.6c0.1-0.2,0.2,0,0.3,0.1c0.4,0.6,0.7,1.2,1.2,1.6
	c0.2,0.1,0.4,0.2,0.6,0.3c0.2,0.1,0.3,0.2,0.3,0.3c0.1,0.2,0.1,0.4,0.1,0.6c0,0.2,0.1,0.5,0.1,0.7c0,0.5-0.2,0.9-0.6,1.3
	c-0.1,0.2-0.3,0.3-0.5,0.5c-0.1,0.2-0.2,0.3-0.3,0.5c-0.2,0.4-0.4,0.9-0.8,1.1c-0.2,0.1-0.4,0.2-0.5,0.4c-0.2,0.1-0.3,0.3-0.4,0.4
	c-0.1,0.1-0.2,0.1-0.3,0.2c-0.1,0.1-0.1,0.2-0.1,0.3c0,0.3,0,0.6-0.3,0.8c-0.1,0.1-0.3,0.3-0.4,0.4c-0.2,0.1-0.3,0.2-0.5,0.4
	c-0.1,0.1-0.2,0.2-0.1,0.4c0.1,0.1,0.4,0.1,0.5,0c0.1-0.1,0.2-0.2,0.3-0.2c0.1-0.1,0.3-0.1,0.4-0.2c0,0.1,0,0.2,0,0.2
	c0,0.1-0.1,0.1-0.1,0.1c-0.1,0.1-0.2,0.2-0.4,0.3c-0.2,0.1-0.4,0.2-0.5,0.3c-0.1,0.1-0.2,0.3,0,0.3c0.1,0,0.4-0.2,0.5-0.1
	c0.1,0.1,0.1,0.3,0.2,0.4c0.1,0.1,0.3-0.1,0.4-0.2c0.1-0.1,0.2-0.2,0.4-0.2c0.2,0,0.3-0.1,0.5-0.2c0.1-0.1,0.3-0.3,0.4-0.4
	c0,0.1,0,0.2,0,0.2c0,0.1,0.1,0.1,0.2,0.1c0.1,0,0.2-0.2,0.3-0.2C17,96.1,17,96,17.1,96c0.1,0,0.1,0.1,0.2,0.1
	c0.1,0.1,0.2,0.2,0.1,0.3c-0.1,0.1-0.3,0.2-0.4,0.2l0,0c-0.2,0.1-0.4,0.3-0.7,0.3c-0.3,0-0.9,0.3-0.8,0.6c0.1,0.2,0.2,0.3,0.3,0.5
	c0.1,0.2,0.2,0.3,0.2,0.5c-0.1,0-0.1,0.1-0.2,0.1l-0.1,0l-0.1,0c0,0,0,0-0.1,0c-0.2,0.1-0.3,0.2-0.5,0.2c-0.1,0-0.2,0-0.3,0
	c0,0-0.1,0-0.1,0c-0.1,0-0.1,0-0.2,0c-0.1,0-0.1,0-0.2,0c-0.1,0-0.1,0.1-0.2,0.1c0,0.1-0.1,0.1-0.1,0.1s0,0.1-0.1,0.2
	c-0.1,0.1-0.2,0.2-0.3,0.2c-0.1,0.1-0.2,0.1-0.4,0.1l-0.2,0.1c0,0-0.1,0-0.1,0c0,0-0.1,0.1-0.1,0.1c0,0,0,0.1-0.1,0.1
	c0,0,0,0.1-0.1,0.2c0,0.1-0.1,0.1-0.1,0.2c0.2,0.2,0.3,0.3,0.3,0.5c0,0.2-0.1,0.5,0.1,0.6c0.2,0.1,0.3-0.1,0.4-0.2
	c0.1-0.1,0.3-0.2,0.5-0.3c0.2-0.1,0.4-0.2,0.6-0.2c0.2,0,0.4,0,0.5-0.1c0.1-0.1,0.3-0.2,0.4-0.3c0.2-0.1,0.3-0.2,0.4-0.3
	c0.1-0.2,0.2-0.4,0.3-0.6c0.1-0.1,0.2-0.2,0.3-0.2c0.1,0,0.1,0,0.2,0c0.1,0,0.1,0.1,0.1,0.2c0.1,0.1,0.2,0.1,0.3,0.1
	c0.3,0,0.4-0.3,0.5-0.6c0.1-0.1,0.1-0.2,0.3-0.3c0.3-0.2,0.4-0.5,0.6-0.7c0.2-0.3,0.4-0.6,0.6-0.8c0.1-0.1,0.3-0.2,0.4-0.4
	c0.1-0.1,0.2-0.2,0.4-0.3c0.3-0.2,0.7-0.4,1-0.7c0.1-0.1,0.2-0.2,0.4-0.3c0.2-0.2,0.5-0.3,0.7-0.4c0.1,0,0.2,0,0.3,0
	c0.1,0,0.2-0.1,0.2-0.2c0-0.1,0-0.2,0.1-0.2c0-0.1,0.1-0.1,0.2-0.1c0.1,0,0.2-0.1,0.2-0.1c0.1,0,0.2,0,0.3,0s0.2,0,0.3,0
	c0.1,0,0.2,0,0.2,0c0.2,0,0.2-0.3,0.2-0.4c0-0.1,0-0.2,0-0.3c0-0.1,0-0.1,0.1-0.2c0.1-0.1,0.2-0.2,0.3-0.2c0.1,0,0.4,0,0.4,0.2
	c0,0.1,0,0.3,0.1,0.3c0.2,0,0.4-0.2,0.5-0.3c0.1-0.1,0.2-0.2,0.2-0.3c0.1-0.1,0.1-0.2,0.2-0.2c0.1-0.1,0.3-0.1,0.5-0.1
	c0.1,0,0.2,0,0.3,0c0.1,0,0.2,0.1,0.3,0.1c0.3,0.1,0.7,0,0.9-0.2c0.1-0.1,0.2-0.2,0.3-0.1c0.1,0,0.2,0.1,0.3,0.1
	c0.1,0,0.2-0.1,0.3-0.1c0.1-0.1,0.1-0.2,0.2-0.3c0.1-0.1,0.1-0.2,0.2-0.2c0,0,0.1,0,0.2-0.1c0,0,0.1-0.1,0.1-0.1
	c0.1-0.1,0.2-0.2,0.3-0.3c0.1,0,0.1-0.1,0.2-0.1c0.2-0.1,0.3-0.3,0.3-0.5c0-0.2-0.1-0.5,0.1-0.7c0.1-0.1,0.2-0.3,0.4-0.4
	c0.2-0.2,0.3-0.5,0.5-0.7c0.2-0.3,0.4-0.6,0.6-0.9c0.2-0.2,0.3-0.2,0.5-0.3c0.2-0.1,0.3-0.2,0.4-0.3c0.1-0.2,0.1-0.3,0.4-0.3
	c0.3-0.1,0.5-0.1,0.7-0.3c0.2-0.2,0.3-0.5,0.5-0.8c0.2-0.4,0.4-0.1,0.7,0.1c0.4,0.2,0.3-0.2,0.5-0.4c0.1-0.1,0.2-0.1,0.3-0.2
	c0.1-0.1,0.1-0.2,0.1-0.4c0-0.2,0.1-0.3,0.2-0.4c0.1-0.1,0.3-0.2,0.4-0.2l0.1,0c0.2,0,0.3,0,0.5,0c0.2,0,0.4,0,0.5-0.1
	c0.3-0.2,0.3-0.5,0.4-0.8c0-0.2,0.1-0.3,0.2-0.3c0.2-0.1,0.4-0.1,0.5-0.3c0.1-0.1,0.1-0.3,0.2-0.5c0-0.2,0.1-0.4,0.2-0.5
	c0.1-0.2,0.2-0.5,0.5-0.6c0.2-0.1,0.4,0,0.7-0.1c0.4-0.2,0.3-0.8,0.3-1.1c-0.1-0.4-0.2-0.7,0.1-1c0.2-0.2,0.5-0.3,0.7-0.6
	c0.1-0.2,0.1-0.3,0.3-0.5c0.1-0.2,0.3-0.2,0.5-0.1c0.5,0.1,0.6,0.4,0.8,0.8c0.1,0.2,0.2,0.4,0.4,0.5c0.2,0.1,0.5,0.2,0.8,0.3
	c0.1,0,0.2,0.1,0.3,0.2C44.9,80,45,80,45.1,80l0.5,0c0.1,0,0.2,0,0.3,0c0.1,0,0.2-0.1,0.2-0.1c0.1-0.1,0.3-0.1,0.4-0.1
	c0.1,0,0.3,0,0.5,0c0.1,0,0.2-0.1,0.3-0.1c0.1,0,0.2-0.1,0.3-0.1c0.1-0.1,0.1-0.2,0.2-0.2c0.1-0.1,0.2-0.1,0.3-0.2
	c0.2-0.1,0.4,0,0.5,0.1c0.1,0.1,0.1,0.2,0.2,0.2c0.1,0.1,0.2,0.1,0.3,0c0.2,0,0.3-0.3,0.3-0.5c0-0.2,0-0.3,0.1-0.5
	c0-0.2,0.1-0.3,0.1-0.5c0-0.1-0.1-0.2-0.1-0.2c-0.1-0.1-0.2-0.2-0.3-0.3c-0.1-0.1-0.2-0.2-0.3-0.3c-0.1-0.1-0.3-0.3-0.4-0.4
	c-0.1-0.2-0.1-0.3-0.1-0.5c0-0.2,0-0.4,0.2-0.5c0.1-0.1,0.2-0.2,0.4-0.2c0.5-0.2,0.9-0.1,1.4-0.1c0.6,0,1.1-0.1,1.7-0.4
	c0.5-0.3,1.1-0.6,1.3-1.1c0.1-0.3,0.2-0.6,0.3-0.9c0.1-0.3,0.2-0.6,0.4-0.8c0.2-0.3,0.7-0.6,1-0.5c0.2,0,0.3,0.2,0.5,0.2
	c0.2,0,0.3-0.3,0.4-0.5c0.1-0.2,0.3-0.4,0.4-0.7c0.1-0.2,0-0.4,0-0.6c0-0.3,0-0.5,0-0.8c0-0.3,0-0.6,0-0.8c0-0.2,0-0.5,0.1-0.7
	c0.1-0.3,0.4-0.2,0.6-0.2c0.4,0.1,0.9,0.1,1.1,0.5c0.1,0.2,0.1,0.4,0.2,0.5c0.1,0.2,0.2,0.5,0.3,0.7c0.1,0.1,0.2,0.2,0.4,0.3
	c0.2,0.1,0.4,0.3,0.6,0.5l0.5,0.4c0.1,0.1,0.4,0.3,0.5,0.1c0.1-0.2,0-0.5,0-0.7c0-0.2-0.1-0.4-0.1-0.6c-0.1-0.2-0.1-0.3,0-0.5
	c0.1-0.2,0.2-0.3,0.3-0.4c0.2-0.1,0.5-0.2,0.7-0.2c0.2-0.1,0.4-0.1,0.7-0.1c0.2-0.1,0.4-0.2,0.6-0.4c0.2-0.2,0.4-0.3,0.4-0.6
	c0-0.2,0-0.4,0.1-0.6c0-0.2,0.1-0.4,0.1-0.6c0-0.3,0-0.5,0.1-0.8c0.1-0.1,0.1-0.3,0.1-0.4C63.5,64.4,63.6,64.3,63.6,64.1z"/>
</svg>
</div>


<!-- 华东 -->
<!--div style="width:51px; height:101px; left: 1371px; top:324px;"-->
<div style="width:52px; height:101px; left: 231px; top:164px;">
<svg id="area_100002" viewBox="0 0 52.4 101.5" class="area_stroke hidden " version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" >
<path d="M15.8,25.52a1.49,1.49,0,0,0-.16.68c.06.22,0,.93.44.82s1.42,0,1.4.37-.09.78.18.85,1.24,1,.54,1.27-.89,0-1.16.34-1.31.55-1.46.85-.21.8-.62.38a15,15,0,0,1-1.23-1.47c-.27-.38.06-.59-.58-.58s-1.11-.16-1.24.07-.48.26-.3.72a9.21,9.21,0,0,1,.11,1.57c.08.32.08,1.18-.42,1s-.64-.23-1,.34a3.72,3.72,0,0,0-.26,1.89c0,.27.06.5-.42.53a3.57,3.57,0,0,1-1.37,0c-.38-.15-1.26-.57-1.27.12a1.09,1.09,0,0,0,1,1.14c.56.14,1.47.15,1.67.92s.16,1.42.79,1.47,1.58,1.42,1.9,1,.93-1,1.14-.36,0,1.14.13,1.44a10,10,0,0,1,.15,2.18c0,.7.18,1.21-.44,1.11s-1-.21-1.43.21A2.61,2.61,0,0,0,10.46,47a1.94,1.94,0,0,1,.67.41c.76.57-.06.75.43.77s.78-.35.86,0,1.05-.05,1.16.22,0,.58.37.81,1,.34.44.63-.71-.2-.93.43-.06.65-.22.81-.71.16-.17.57.85.18.76.69-.53,1.56.07,1.71.66,0,.67.61a18.81,18.81,0,0,0,.64,2c0,.18,0,.29-.09.27-.45-.08-.86,0-.71.3s1,.19.64.61-.44.56-.79.41a2,2,0,0,1-.9-.49c-.28-.31-.5-1.12-.71-.56s-.4.76-.79.6a1.15,1.15,0,0,0-1.17.23c-.16.25-.69,1.53-1.46,1.34s-1.05-.17-1.4.12-.57,1.32-.89,1-.52-.22-.89,0-.81.51-1.16.27-.61-.19-.77.17-.06.94-.42.8-.36-.09-.71.15a8.7,8.7,0,0,1-1,.83l-.07,0c.05,0,1.62,1.48,1.45,2.36s-.21,1.12.09,1.59.52,1.12.23,1.27a2.57,2.57,0,0,0-1.13.68c-.36.43.26.36-.41.76S1,69.73.87,70.16a3.35,3.35,0,0,1-.4,1c-.19.25-.32.08-.37,1s-.29,1.56.24,1.83,1.36-.4,1.07.55S.91,75.77,1.14,76s1,.4.69.94-.59,1.44,0,1.75,1.47.26,1.16,1.06S1.43,82.23,2.4,82s1.67-.27,1.16.13-.86.42-.93.92.25.46,0,.86a2,2,0,0,0-.4,1,1.27,1.27,0,0,1,.14.89c-.09.65-.16,1-.17,1s.12,0,.15.13c.11.45,0,1.74.73,1.32s.93.06,1-.42.26-.85.9-.66,1.8.24,1.81.65.4.63-.1,1.1-.54.25-.93.77S4.71,90,4.65,90.94s-.49,1.59.14,2,.52.45,1.2.09a3.87,3.87,0,0,1,1.54-.58c.05.11.57.34.69,0s.08-.55.48-.56,1.27.22,1.65-.08,1.17-1,1.44-.46S13.17,93.71,13.5,93s.06-1.82.45-2,.75,0,.53-.41-.38-.53-.14-.51l.38.09c.75.23.69.42,1,.66s2.41.26,2.44.61-.52,1,.56.78,1.05-.66,1.48,0a3.8,3.8,0,0,1,.67,1.51c.18.69.55,1,.51,1.66s-.27.5.17,1.1,1.62,2.64,1.62,2.64l.22-.35a4.32,4.32,0,0,1,.69-.89,2.2,2.2,0,0,1,1-.36,4.21,4.21,0,0,0,.87-.28,2.86,2.86,0,0,0,.93-.64,1.63,1.63,0,0,0,.41-.88c.07-.39.41-.55.75-.71s.75-.32.64-.74a.43.43,0,0,0-.23-.32A.84.84,0,0,0,28,94c-.33.06-.91,0-1-.39s.41-.47.65-.61a2.76,2.76,0,0,0,.49-.4,1.32,1.32,0,0,1,.45-.32c.27-.06.45.33.67.42a.94.94,0,0,0,.78-.1.8.8,0,0,1-.06-.3c.05-.15.18-.22.31-.3a1.21,1.21,0,0,1,.47-.23c.38,0,.46.37.49.64s.07.3.25.28a.3.3,0,0,0,.23-.31c0-.16,0-.38.12-.48s.26-.15.3-.34.07-.31,0-.42S32,91,31.87,91s-.23-.1-.21-.27a.41.41,0,0,1,.2-.33c.15-.06.2.11.28.19s.33.08.47,0,.28-.22.46-.27.56-.12.71-.34a.53.53,0,0,0,0-.49c-.11-.2-.27-.19-.46-.17s-.42,0-.5-.21a.68.68,0,0,1,.1-.57,4.83,4.83,0,0,0,.18-.62,4.92,4.92,0,0,1,.21-.65c.05-.11.18-.34.34-.3s.13.49.2.65.33.1.42,0a1.05,1.05,0,0,0,.08-.36,2,2,0,0,1,.1-.32c0-.1.19-.5.34-.37s.09.43.06.6-.22.6-.09.81.33,0,.42-.14a1.76,1.76,0,0,0,.25-.89.36.36,0,0,0-.15-.33c-.06,0-.21-.08-.23-.15s0-.16.09-.19.12,0,.18,0a.24.24,0,0,0,.33-.08c.06-.1.06-.21.13-.31s.18-.18.32-.1a.65.65,0,0,1,.26.43.42.42,0,0,0,.12.26,1.45,1.45,0,0,0,.19.23c.08.06.13,0,.22,0s.12.07.2,0,.05-.1.09-.13,0,0,.07,0,.1-.37.12-.48.13-.47-.11-.53-.66,0-.75-.32a.89.89,0,0,1,.21-.7.75.75,0,0,1,.7-.12,1.4,1.4,0,0,0,.05-.41c0-.11,0-.22,0-.34s.3-.26.46-.4a.67.67,0,0,0,.16-.24c0-.07.1-.13.12-.21a.44.44,0,0,0-.16-.45c-.13-.07-.31,0-.38-.19a.87.87,0,0,1,.07-.55c.08-.16.22-.26.2-.45a.72.72,0,0,0-.29-.42.57.57,0,0,1-.19-.59.72.72,0,0,1,.22-.36.55.55,0,0,1,.19-.12c.07,0,.12-.08.18-.1a.41.41,0,0,1,.22,0,1.16,1.16,0,0,0,.18,0,.64.64,0,0,0,.18-.06c.13-.09,0-.46-.11-.54a.73.73,0,0,0-.33-.08,1.12,1.12,0,0,1-.7-.47c-.14-.26.12-.62.24-.85a2,2,0,0,1,.71-.66c.13-.1.56-.45.7-.2s-.09.45.2.49a1,1,0,0,0,.61-.16.39.39,0,0,0,.21-.26c0-.11,0-.23,0-.34s.43-.18.56-.37a.53.53,0,0,0,0-.25.67.67,0,0,1,.13-.3c.17-.2.45-.18.67-.3s.19-.35,0-.49-.43-.27-.64-.42-.32-.32-.09-.5a1.46,1.46,0,0,1,.77-.21.48.48,0,0,1,.38.15,1.66,1.66,0,0,1,.21.4.38.38,0,0,0,.57.18.58.58,0,0,0,.15-.65c-.07-.22-.09-.46.18-.56s.49,0,.42-.34-.4-.44-.5-.72a2.15,2.15,0,0,1,0-.8.88.88,0,0,1,.39-.83c.25-.15.68-.25.74-.58a.91.91,0,0,1,.12-.35,2.43,2.43,0,0,1,.34-.38c.13-.13.3-.39.52-.31s.15.12.24.14a.36.36,0,0,0,.31-.1.7.7,0,0,0,.23-.31c0-.11,0-.23,0-.34.08-.3.37-.17.56-.32s0-.54-.15-.68-.33-.26-.29-.53.2-.55.46-.54.4.4.56.58.42.22.5,0-.19-.41-.31-.63.12-.36.33-.24.34.29.51.08a2,2,0,0,0,.21-.75,1.46,1.46,0,0,0,0-.69c-.08-.26-.27-.34-.48-.49s-.2-.53.14-.55c.11,0,.21,0,.32,0a1.13,1.13,0,0,0,.39-.21c.17-.12.45-.46.26-.67s-.51,0-.65-.26.13-.32.31-.32.44,0,.47-.27-.16-.32-.36-.38-.17-.23-.2-.41-.28-.16-.46,0-.41.31-.59.11a.47.47,0,0,1-.08-.53c.09-.14.28-.2.36-.35s0-.34.11-.49a.73.73,0,0,1,.58-.23.46.46,0,0,1,.47.18c.1.16.18.39.4.45a.19.19,0,0,0,.27-.22.89.89,0,0,1,0-.51c.08-.15.22-.25.25,0a1.43,1.43,0,0,0,0,.22l.1.25s0,0,.06.05a.41.41,0,0,0,.24,0c.17,0,.26-.2.32-.33a2.27,2.27,0,0,0,0-.58c0-.15.11-.27.11-.42a.45.45,0,0,0-.19-.33c-.13-.11-.21-.2-.14-.37s.17-.33,0-.46a.53.53,0,0,0-.65,0c-.2.19-.13.48-.09.71s0,.28-.26.25a1.47,1.47,0,0,0-.83,0c-.25.11-.43.35-.69.43-.49.16-.77-.6-.39-.87a1.51,1.51,0,0,1,.63-.2c.24-.06.48-.1.72-.14a1.18,1.18,0,0,0,.55-.17.88.88,0,0,0,.25-.4c.1-.31.11-.77.42-1a.39.39,0,0,1,.35,0,.6.6,0,0,1,.28.13,1,1,0,0,1,.12.17c.1.12.33,0,.3-.16s-.09-.23,0-.3.1,0,.13-.09-.11-.37-.32-.34-.19.16-.33.17a.28.28,0,0,1-.24-.11c-.07-.08-.11-.15-.2-.18a.5.5,0,0,0-.17,0,.92.92,0,0,1-.46-.17c-.15-.08-.32-.11-.47-.19a4.48,4.48,0,0,1-.79-.65,3,3,0,0,1-.46-.49,3.7,3.7,0,0,0-.21-.33,1.47,1.47,0,0,1-.29-.32A1.15,1.15,0,0,0,47,52.8a.75.75,0,0,0-.33-.06,4.53,4.53,0,0,0-1.22-.07,1.47,1.47,0,0,0-.87.51c-.22.28-.33.59-.67.76-.64.32-1.79.45-2-.43a1.16,1.16,0,0,0-.22-.62.55.55,0,0,0-.61-.14c-.48.18-.71.79-.94,1.19a.51.51,0,0,1-.3-.07.2.2,0,0,1,0-.28,2.57,2.57,0,0,0,.44-.49,6.05,6.05,0,0,1,.32-.55.89.89,0,0,1,.52-.26,2.41,2.41,0,0,1,1,0c.16,0,.31.1.46.15a1.05,1.05,0,0,1,.26.13.44.44,0,0,0,.22.05,1.27,1.27,0,0,0,.48-.07,1.18,1.18,0,0,0,.25-.19c.08-.07.08-.1.1-.21a1,1,0,0,0,0-.47c0-.08-.13-.16-.12-.25s.11-.14.16-.19a2.37,2.37,0,0,1,.53-.26c.17-.08.34-.18.5-.27a.7.7,0,0,0,.24-.17l.14-.25a.32.32,0,0,1,.34-.13c-.06-1.18-1.17-1-1.51-1.14s0-.87.11-1.54c0-.23.08-.44.1-.61,0-.68.4-.5.9-.51s.32-.69.35-1.18a1,1,0,0,1,.42-.74c-.17-.18-.39-.32-.55-.52s-.26-.4-.52-.51a2.23,2.23,0,0,0-.45-.18,1.29,1.29,0,0,0-.34,0h-.58c-.14,0-.26,0-.39,0a1.42,1.42,0,0,1-.86-.32A1.16,1.16,0,0,1,42,41.48c.29-.46.76-.11,1,.16a2.7,2.7,0,0,0,1.18.85,2.2,2.2,0,0,0,1.29-.15c.33-.11.67-.21.93.08a3.07,3.07,0,0,0,.29.34.73.73,0,0,0,.41.15,1.26,1.26,0,0,1,.47.08,1.29,1.29,0,0,1,.36.34,1.18,1.18,0,0,0,.44.39,1.58,1.58,0,0,0,.43.1c.11,0,.27.06.37,0s.2-.37.21-.55a.37.37,0,0,0-.26-.44c-.23-.11-.17-.27-.22-.47s-.28-.17-.27-.38.14-.6-.09-.78a2.49,2.49,0,0,0-.63-.32,8.6,8.6,0,0,1-.84-.33,3.22,3.22,0,0,0-.68-.17.42.42,0,0,1-.33-.45l0-.39a.78.78,0,0,0-.12-.68,1.73,1.73,0,0,0-.16-.3c-.19-.21-.6-.23-.85-.33s-.29-.13-.44-.18L44.29,38a1.24,1.24,0,0,1-.55-.35,4,4,0,0,1-.49-.42c-.17-.21-.16-.51-.25-.75s-.21-.61-.29-.92-.06-.53-.13-.78a.62.62,0,0,1,0-.23c0-.21-.2-.35-.29-.52a3.09,3.09,0,0,0-.36-.45,1.8,1.8,0,0,1-.31-.74c-.09-.32-.21-.64-.27-1s0-.44-.09-.66a5.51,5.51,0,0,1-.1-.9,1.46,1.46,0,0,0,0-.59.42.42,0,0,0-.37-.2c-.17,0-.29.14-.46.17a.22.22,0,0,1-.25-.21c0-.25.1-.49.1-.74a1,1,0,0,0-.33-.8c-.15-.12-.28-.2-.3-.4a1.24,1.24,0,0,0-.08-.51,4.46,4.46,0,0,0-.34-.42,4.31,4.31,0,0,0-1.19-.92,2.27,2.27,0,0,0-.66-.33,3.05,3.05,0,0,0-.67-.07c-.48,0-.3.69-.76.75,0-.11,0-.21,0-.31,0-.42.21-.92-.23-1.2s-1,0-1.37-.36.17-.9-.29-1c-.23-.06-.48.14-.66.25s-.32.18-.31-.05.13-.42.07-.59-.33-.14-.47-.18c-.55-.16-.07-.9.2-1.12l.06,0c.16-.1.34-.15.5-.24a.75.75,0,0,0,.31-.49A5.78,5.78,0,0,1,34,19.51a1,1,0,0,1,.38-.37.88.88,0,0,0,.38-.63c0-.23-.05-.5,0-.71a1.31,1.31,0,0,1,.55-.43,1,1,0,0,1,.3-.09.76.76,0,0,0,.35-.14.53.53,0,0,0,.11-.38.75.75,0,0,1,.16-.5c.1-.1.24-.16.35-.25a2.09,2.09,0,0,0,.22-.2c.21-.19.52-.27.75-.44a.67.67,0,0,0,.29-.37.56.56,0,0,0-.09-.4,1.79,1.79,0,0,0-.34-.44c-.16-.13-.78-.25-.54-.58a.4.4,0,0,1,.41-.14c.12,0,.24.1.35,0s.17-.24.29-.32a.66.66,0,0,1,.51-.08.54.54,0,0,1,.25.27,1.35,1.35,0,0,0,.19.33.72.72,0,0,0,.68.37,1,1,0,0,0,.58-.25.54.54,0,0,0,.16-.51,1.2,1.2,0,0,0-.08-.5c-.09-.14-.26-.1-.4-.16s-.07-.17,0-.28a2.63,2.63,0,0,1,.06-.39c.09-.24.39-.61.69-.57s.31.75.61.52.19-.62.09-.89-.17-.31-.25-.47a.21.21,0,0,1,0-.31c.22-.2.51-.06.75,0a.62.62,0,0,0,.7-.17,1.16,1.16,0,0,1,.31-.27,4.5,4.5,0,0,1,.44-.16l.43-.16a1.56,1.56,0,0,0,.46-.29c.09-.07.2-.12.29-.19a1.59,1.59,0,0,1,.34-.28c.14-.06.3-.07.44-.13a5.91,5.91,0,0,0,.66-.45,5.72,5.72,0,0,0,.57-.24.45.45,0,0,0,.19-.22,1.55,1.55,0,0,0,0-.21.7.7,0,0,1,.24-.3A.36.36,0,0,1,47,7.12c.22,0,0,.28,0,.38a.43.43,0,0,0,.1.37.25.25,0,0,0,.35.07,1,1,0,0,0,.22-.36.7.7,0,0,1,.34-.36.28.28,0,0,1,.16,0c.07,0,.08.08.08.15a1.34,1.34,0,0,1-.06.44c0,.14-.11.31.07.36s.31.12.48.16a.55.55,0,0,0,.38,0c.21-.13.15-.56.17-.75a8.85,8.85,0,0,1,.12-.88.68.68,0,0,1,.27-.5c.16-.14.42-.24.51-.44A2.61,2.61,0,0,0,50.34,5c0-.22.19-.51.1-.73s-.39-.25-.6-.23a2.61,2.61,0,0,0-.68.17.82.82,0,0,1-.65,0,1.65,1.65,0,0,1-.64-.53c-.16-.19-.38-.47-.68-.34a2.11,2.11,0,0,0-.6.59.71.71,0,0,1-.37.24,5.34,5.34,0,0,0-.8.13,1.33,1.33,0,0,1-.62.13A1.27,1.27,0,0,1,44,4.05a1.63,1.63,0,0,1-.24-.4.56.56,0,0,0-.19-.3c-.22-.14-.54.09-.75.15s-.46-.12-.55-.4-.11-.6-.4-.71-.6-.07-.88-.18-.48-.35-.76-.47a.62.62,0,0,0-.71.2c-.17.17-.27.4-.54.42s-.53-.12-.74,0-.2.46-.4.62a.52.52,0,0,1-.36.11c-.09,0-.23,0-.3,0s0,.19,0,.27a2.64,2.64,0,0,1,.08.49c0,.33-.19.46-.51.48s-.43,0-.57.22a1.71,1.71,0,0,1-.32.44,2.54,2.54,0,0,0-.59.19c-.24.15-.17.35-.09.58a1,1,0,0,1,0,.7c-.14.38-.34.86-.83.86S33.42,6.92,32.9,7c-.2,0-.38.15-.59.18a1,1,0,0,1-.58-.09,6.33,6.33,0,0,1-1.26-.85,6.29,6.29,0,0,1-.67-.48.64.64,0,0,1-.27-.51c0-.2.26-.3.42-.41a.81.81,0,0,0,.35-.44,1.1,1.1,0,0,0,.05-.62c-.06-.22-.25-.37-.31-.58-.12-.43.59-.77.3-1.16A.8.8,0,0,0,30,1.81a11.56,11.56,0,0,0-1.56-.27A4.06,4.06,0,0,1,27.6,1a.6.6,0,0,0-.71.11,2.88,2.88,0,0,0-.72.94,3,3,0,0,0-.13.39,2,2,0,0,1-.21.65c-.18.25-.46,0-.42-.26A1.5,1.5,0,0,0,25.48,2c-.07-.19-.3-.17-.44-.3s-.44-.43-.46-.65a2.1,2.1,0,0,0-.05-.79A.43.43,0,0,0,24.16,0,3.36,3.36,0,0,0,23,.82a1.51,1.51,0,0,1-.82.68c-.35.13-.67.25-.73.61s-.06,1-.74.89-.94.37-1.39.17S18.37,2.58,17.9,3s-.77.49-1.14,1.09a1.67,1.67,0,0,1-1,.9c-.39.15-1.26.29-1.48.81s-.11.51-.4.58-.63.11-.55.46.22.48-.08.63-.42-.32-.79.52A7.64,7.64,0,0,0,12,9.37c-.2.61-1.36,1.17-1.56,2s-.76,1.2-.59,1.89.6.65.53,1.3a1.38,1.38,0,0,1-.12.54s.32,0,0,.53-2,2.93-.68,2.08,2.66-1.34,3.26-1.73,1.7-1.27,1-.26-.57.26-1,1-.74.37-.82.66-.25.48-.47.46-.8-.09-.83.22.23.21-.32.48-.77.5-.8.81,0,.53-.37.61-1.15.05-1.54.75a9.59,9.59,0,0,1-.87,1.26c-.25.33-.84,1,.33.84s1.67-.6,2.06.21,1.89,2.85,2.91,2.58A9.24,9.24,0,0,1,14,25.22c.51-.07.58-.35.87-.25s.66,0,.86.43A.4.4,0,0,0,15.8,25.52Z" /><path d="M47,78.88a4.41,4.41,0,0,0-1.87,1.75c-.47.91-.07,1.19-.5,2.13-.56,1.21-1.34,1-2,2.25-.42.79-.11.9-.62,2.5-.42,1.33-.63,1.23-.87,2.25a23.13,23.13,0,0,0-.25,2.75c-.1,1.19-.14,1.4,0,1.75.29.75.79.68,1.38,1.5s.3,1,.88,2.38c.4,1,1,2.32,1.75,2.38.29,0,.55-.14,1.25-.87a11.22,11.22,0,0,0,2.38-3,21.88,21.88,0,0,0,1.25-3.25c.16-.5.31-1.08.63-2.25a20.52,20.52,0,0,0,.75-3.25c0-1.48-.52-1.57-.37-2.87s.78-1.55.63-2.5-.84-.86-1.25-2.25A3.87,3.87,0,0,1,50,78.08.09.09,0,0,0,50,78C49.91,77.79,48.32,78.1,47,78.88Z" />
</svg>
</div>


<!-- 中南 暂时用海南的ID 100005-->
<div style="width:81px; height:122px; left: 174px; top:178px;">
<svg id="area_100005" viewBox="0 0 81 122" class="area_stroke hidden " version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" >
<path d="M21.4,117.8c0,0.3,0.2,0.6,0.2,0.9c0,0.1-0.1,0.2-0.1,0.3c0,0.1,0,0.3,0,0.4c0,0.1,0.1,0.2,0.1,0.3
	c0.2,0,0.5,0,0.7,0.1c0.3,0.1,0.5,0.2,0.8,0.2c0.6,0.1,1.1,0.6,1.6,1.1c0,0.2,0.9,0.2,1.1,0.3c0.2,0,0.4,0,0.6,0
	c0.3,0,0.5,0.3,0.8,0.4c0.3,0.1,0.7,0,0.9-0.1c0.6-0.2,1.3-0.5,1.9-0.6c0.1-0.1,0.3,0,0.4,0c0.1,0,0.3-0.2,0.4-0.3
	c0.3-0.4,0.2-1,0.8-1.2c0.3-0.1,0.6-0.3,0.9-0.4c0.3-0.1,1-0.2,1.1-0.3c0-0.2-0.1-0.4-0.1-0.6c0.1-0.2,0.3,0,0.5-0.1
	c0.1-0.1,0.1-0.4,0.1-0.5c0-0.3-0.1-0.7,0.1-1c0.2-0.3,0.3-0.5,0.4-0.8c0-0.2,0-0.4,0.1-0.6c0.1-0.2,0.2-0.5,0.2-0.7
	c0.1,0,0.3,0,0.4,0c0.1,0,0.2-0.1,0.2-0.2c0.1-0.2,0.2-0.4,0.3-0.5c0,0,0.2,0,0.2,0c0,0,0.1-0.2,0.1-0.2c0.1-0.2,0.2-0.3,0.2-0.5
	c0-0.1,0-0.1,0-0.2c0.1,0,0.2,0,0.3,0.1c0.3,0.2,0.4,0,0.5-0.2c0.2-0.2,0.3-0.5,0.3-0.8c0.2-1.6-2-2.1-3.2-2.3
	c-0.2,0-0.5-0.1-0.8-0.1c-0.2,0-0.3,0-0.5,0.1c-0.4,0.1-0.9,0.1-1.3,0.1c-0.2,0-0.3,0-0.4,0.2c-0.1,0.1-0.2,0.2-0.3,0.4
	c-0.2,0.2-0.4,0.4-0.7,0.1c-0.1-0.1-0.1-0.3-0.3-0.4c-0.1,0-0.3-0.1-0.4,0c-0.1,0.1-0.1,0.3-0.3,0.2c-0.1,0-0.2-0.1-0.2-0.2
	c-0.1-0.1-0.2-0.1-0.3-0.2c-0.1-0.1-0.2-0.2-0.4-0.1c-0.2,0.1-0.2,0.6-0.3,0.8c-0.1,0.1-0.1,0.5-0.3,0.5c-0.1,0.1-0.3-0.1-0.4-0.1
	c-0.5-0.2-0.9-0.8-1.5-0.4c-0.4,0.3-0.6,0.8-0.8,1.2c-0.2,0.5-0.5,1-1,1.2c-0.6,0.3-1.5,0.2-2,0.7c-0.6,0.5-0.6,1.4-0.6,2.1
	C21.2,116.4,21.2,117.1,21.4,117.8z M31.7,69.4c0,0-0.6,1-1,1.4c-0.4,0.4-0.9-0.2-1.3-0.3c-0.4-0.1-0.6,0.2-0.9,0.6s0.2,0.3,0.7,0.4
	c0.5,0.1-0.1,0.5-0.2,0.9c-0.2,0.4-0.7-0.1-0.9,0c-0.2,0.1-0.2-0.3-0.5-0.6c-0.2-0.4-0.8,0.6-1,0.8c-0.2,0.2-0.2,0.5-0.1,0.9
	c0.1,0.5-0.8-0.4-1.1-0.8c-0.3-0.4-0.2-0.2-0.6-0.5c-0.4-0.2-0.4,0.3-0.9,0.6c-0.5,0.3-0.4,0.7-0.7,1.3c-0.3,0.6-0.1,0.2-0.8,0.2
	c-0.7,0-0.5,0.1-0.7,0.4c-0.1,0.3-0.5,0.2-0.9,0c-0.4-0.2-0.6-0.6-0.8-1.1c-0.2-0.5-0.2,0-0.6,0.2c-0.4,0.2-0.8-0.5-1-0.7
	c-0.2-0.2-0.4-0.7-0.6-1.1c-0.1-0.4-0.9,0.4-1.4,0.7c-0.5,0.2-0.3,0.7-0.5,1.3c-0.3,0.5-1.4,0.3-1.6,0.2c-0.3,0-0.5,0.1-1,0.4
	c-0.6,0.3-0.5-0.1-0.8-0.2c-0.3-0.1-0.4,0.1-0.6,0.4c-0.2,0.2-1.1,0.4-1.5,0.5c-0.4,0.1-0.1,0.1,0,0.8c0.2,0.7-0.4,0.6-1.1,0.8
	c-0.7,0.2-1.3-0.3-1.4-0.6c-0.2-0.4-0.6-0.2-1-0.2c-0.4-0.1-0.3-0.3-0.6-0.8C6.1,74.7,5.8,75,5,75.1s-0.6,0.4-1,1.3
	C3.6,77.2,0.6,77,0.2,77c-0.1,0-0.1,0-0.2,0c0.4,0.4,0.4,1.4,0.7,1.8c0.3,0.4,0.5-0.4,0.7-0.6c0.2-0.2,0.7-0.1,1.1,0.2
	c0.4,0.3,0.3,0,0.7-0.2s0.2,0.2,0.6,0.8s0,1.1,0.2,1.6c0.2,0.6,0.3,0.2,0.5,0.1s0.5,0.4,0.8,0.8c0.3,0.4,0.9-0.7,1.1-0.9
	c0.2-0.2,0.4,0.6,0.6,0.7c0.1,0.1,0.1,0.2,0.6,0.5c0.5,0.3,1-0.5,1.4-0.7c0.4-0.2,0.5,0.6,0.4,0.7c0,0.1,0.3,0.3,0.6,0.8
	c0.3,0.5-0.2,0.3-0.5,0.5c-0.3,0.2,0,1.2-0.3,2c-0.2,0.8-1.1,0.1-1.2,0c-0.1-0.1-0.3,0-0.6,0.1c-0.3,0.1-0.5,0.2-0.6,0.6
	c-0.1,0.3-1.5,1.2-1.5,1.2c0.2,0.4,0.4,0.8,0.5,0.9c0.1,0.3,0.6,0,0.9,0.1c0.3,0,0.5,0.3,1.2,0.9c0.7,0.6,0.8-0.2,1.3-0.3
	c0.5-0.1,0.9,0.7,1.3,1.1c0.4,0.3,1.3-0.2,1.9-0.4c0.6-0.2,0.4,0.5,0.7,1.3s-0.4,0.8-0.9,1.3c-0.4,0.6,0.1,0.7,0.4,1
	c0.3,0.3-0.3,1.4-0.1,2.2c0.2,0.8,1.4,1.3,1.9,1.3c0.5,0.1,0.2,0.3,0.2,0.7c0.1,0.4,0.9,0.2,1.2,0.3c0.3,0,0.4,0.3,0.7,1
	c0.3,0.6,2.8-0.2,2.8-0.2l0.4,0.3c0.3,0.1,0.4,0.4,0.7,0.6c0.2,0.1,0.3,0,0.5,0c0.1,0,0.3,0.1,0.5,0.1c0.1,0,0.2-0.1,0.4-0.2
	c0.2,0,0.3,0.2,0.5,0.1c0.3-0.1,0.4-0.5,0.8-0.4c0.1,0.2,0.3,0.8,0.7,0.4c0.2-0.2,0.4-0.4,0.5-0.7c0-0.2,0.1-0.3,0.1-0.5
	c0-0.1,0-0.3,0-0.4c0-0.1,0.2-0.2,0.3-0.2c0.1,0,0.1,0.2,0.2,0.3c0,0.1,0,0.4,0.1,0.4c0.2,0,0.2-0.2,0.3-0.3c0.1,0,0.2,0.1,0.2,0.2
	c0.1,0.3,0,0.6,0.3,0.7c0.3,0.1,0.7,0.2,1,0.1c0.1,0,0.2-0.1,0.4-0.1c0.2,0,0.3,0.2,0.4,0.3c0.2,0.2,0.5,0.6,0.8,0.6
	c0.2,0,0.3-0.1,0.4,0.1c0.2,0.3,0.4,0.5,0.7,0.5c0.3,0,0.6-0.2,0.9-0.3c0.3-0.1,0.5-0.2,0.5-0.6c0-0.1,0.1-0.8,0.3-0.7
	c0.1,0.1,0.3,0.3,0.3,0.4c0.1,0.2,0.2,0.4,0.3,0.6c0.1,0.2,0.2,0.4,0.4,0.3c0.2,0,0.5,0,0.7,0c0.1,0,0.1,0.1,0.2,0.1
	c0.2,0.2-0.1,0.6-0.3,0.7c-0.2,0.2-0.4,0.3-0.5,0.5c-0.2,0.2-0.4,0.5-0.6,0.7c-0.1,0.1-0.1,0.2-0.1,0.3c0,0.1,0.1,0.1,0.1,0.2
	c0,0.2-0.3,0.4-0.4,0.6c-0.2,0.3-0.2,0.5,0.1,0.7c0.2,0.2,0.4,0.3,0.6,0.6c0.1,0.2,0.4,0.3,0.5,0.5c0.1,0.2-0.2,0.4-0.3,0.6
	c-0.2,0.3-0.1,0.6,0.2,0.8c0.3,0.2,0.7,0.3,0.7,0.6c0,0.3-0.1,0.6-0.1,0.9c0.1,0.3,0.3,0.2,0.6,0.2c0.4-0.1,0.7-0.2,1,0
	c0.3,0.1,0.6,0.3,0.9,0.2c0.3-0.1,0.6-0.4,0.8-0.6c0.4-0.4,0.7-1.3,0.2-1.8c-0.1-0.1-0.3-0.2-0.5-0.2c-0.1,0-0.3,0-0.4-0.2
	c-0.2-0.2,0.1-0.5,0.1-0.7c0-0.7-1.8-0.3-1.5-1.1c0.1-0.3,0.5-0.6,0.7-0.8c0.2-0.2,0.5-0.5,0.6-0.8c0.1-0.3-0.1-0.5-0.1-0.8
	c0-0.3,0.4-0.3,0.6-0.2c0.3,0.1,0.4,0.5,0.7,0.6c0.3,0.2,0.6-0.1,0.8-0.3c0.1-0.1,0.2-0.3,0.2-0.4c0-0.1,0-0.2,0-0.3
	c0.1-0.1,0.4,0.2,0.5,0.2c0.2,0.1,0.4,0.3,0.6,0.4c0.2,0.1,0.5,0,0.7-0.1c0.1-0.1,0.3-0.2,0.4-0.2c0.2-0.1,0.3,0,0.5,0
	c0.3,0,0.5,0.1,0.8-0.1c0.3-0.2,0.5-0.2,0.8,0c0.2,0.2,0.5,0.3,0.8,0.1c0.2-0.2,0.4-0.5,0.7-0.7c0.2-0.1,0.3-0.2,0.5-0.4
	c0.1-0.1,0.2-0.2,0.3-0.3c0.2-0.2,0.4-0.3,0.6-0.5c0.1-0.1,0.5-0.3,0.7-0.2c0.2-0.1,0.5-0.3,0.7-0.4c0.2-0.2,0.5-0.3,0.8-0.3
	c0.2,0,0.3,0,0.5,0c0.2,0,0.2,0,0.4-0.1c0.3-0.1,0.4,0.2,0.6,0.4c0.2,0.2,0.5,0.2,0.7,0.1c0.2,0,0.3-0.1,0.4-0.1
	c0.2-0.1,0.2-0.1,0.2-0.3c0-0.1,0-0.2,0-0.4c-0.1-0.1-0.1-0.3-0.1-0.4c0-0.2-0.1-0.4-0.1-0.6c0-0.1,0-0.3,0.1-0.4
	c0.1-0.1,0.2,0,0.2,0.1c0.1,0.2,0.1,0.3,0.2,0.5c0.2,0.3,0.4,0.5,0.5,0.8c0.1,0.3,0.4,0,0.6-0.1c0.2-0.2,0.5-0.5,0.9-0.4
	c0.2,0,0.3,0.1,0.3,0.3c0,0.1,0,0.3,0.2,0.3c0.1,0,0.2-0.1,0.3-0.1c0.2,0,0.2,0.1,0.3,0.2c0.1,0.2,0.2,0.1,0.3,0
	c0.1-0.2,0.1-0.4,0.1-0.6c0-0.2,0.1-0.4,0.1-0.6c0-0.2,0-0.3,0-0.4c0-0.1,0-0.2,0-0.3c0-0.1-0.1-0.2-0.1-0.3c0-0.1,0.1-0.2,0.1-0.4
	s-0.1-0.3-0.1-0.4c-0.1-0.2-0.3-0.5-0.3-0.7c0-0.1,0-0.3,0.2-0.3c0.1,0,0.3,0,0.5,0c0.1-0.2,0-0.5,0-0.8c0-0.2-0.2-0.4-0.1-0.6
	c0-0.2,0.2-0.4,0.4-0.4c0.2,0,0.3,0.3,0.3,0.4c0.1,0.2,0,0.3,0.1,0.5c0.1,0.2,0.2,0.3,0.3,0.5c0.1,0.2,0.1,0.3,0.2,0.5
	c0.1,0.2,0.1,0.5,0.2,0.7c0.1,0.2,0.2,0.4,0.3,0.5c0.1,0.2,0.3,0.5,0.4,0.7c0.2,0.2,0.4,0.6,0.8,0.5c0.5-0.1,0.4-0.6,0.4-1
	c0-0.2,0-0.3,0.1-0.4c0.1-0.3,0-0.5,0-0.8c-0.1-0.3-0.1-0.6-0.3-0.9c-0.2-0.3-0.4-0.6-0.5-0.9c-0.1-0.1-0.2-0.3-0.2-0.5
	c0-0.2,0.1-0.3,0.2-0.4c0.1-0.2,0.1-0.3,0.1-0.5c0-0.1,0-0.4,0.1-0.4c0.1-0.1,0.3,0,0.4,0.1c0.1,0.1,0,0.2,0,0.4
	c0,0.3,0.2,0.6,0.4,0.9l0.2,0.2c0,0,0.1,0.1,0.1,0.1c0.1,0,0.1,0,0.2,0c0.1,0,0.2,0.1,0.2,0.1c0.1,0.1,0.2,0.1,0.2,0.2
	c0,0.1-0.1,0.2,0,0.3c0.1,0.1,0.2,0.1,0.3,0.2c0.1,0,0.2,0,0.3,0.1c0.2,0.1,0.2,0.4,0.2,0.6c0,0.1,0,0.2,0,0.3c0,0,0,0.1,0,0.2
	c0,0.1,0.1,0.1,0.2,0.1c0.4,0.2-0.2,0.5-0.3,0.7c0,0,0,0.1,0,0.1c0,0.1,0,0.3,0,0.4c0,0.2,0.2,0.3,0.4,0.2c0.1,0,0.4-0.2,0.5,0
	c0,0.1,0,0.2,0,0.2c0,0.2-0.2,0.2-0.4,0.3c-0.1,0.1-0.2,0.2-0.2,0.3c0,0.1,0,0.2,0.1,0.2c0.1,0,0.2,0,0.3-0.1
	c0.1-0.1,0.3-0.2,0.4-0.3c0.1-0.1,0.2-0.2,0.3-0.2c0.1,0,0.1,0.1,0.1,0.2c0,0.1-0.1,0.2-0.2,0.3c-0.1,0.1-0.2,0.3-0.3,0.4
	c-0.2,0.2-0.4,0.4-0.6,0.6c-0.1,0.1-0.3,0.3-0.2,0.5c0,0.1,0.1,0.1,0.1,0.1c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0,0l0.2-0.3
	c0.1-0.2,0.3-0.3,0.4-0.5c0.2-0.2,0.3-0.3,0.5-0.5c0.3-0.3,0.7-0.5,0.9-0.9c0.2-0.3,0.4-0.6,0.5-1c0.1-0.2,0.1-0.5,0.1-0.8
	c0-0.3,0-0.5-0.1-0.7c-0.1-0.2-0.2-0.5-0.2-0.7c0.1-0.3,0.4-0.5,0.6-0.3c0.2,0.1,0.3,0.3,0.4,0.6c0.1,0.3,0.4,0.3,0.7,0.4
	c0.1,0,0.2,0.1,0.3,0.1c0.2,0,0.4-0.2,0.5-0.3c0.1-0.1,0.2-0.2,0.4-0.3c0.3-0.1,0.5-0.4,0.8-0.6c0.1-0.1,0.2-0.2,0.3-0.4
	c0-0.1,0.2-0.2,0.3-0.2c0.2-0.1,0.5,0,0.8-0.1c0.3,0,0.2-0.4,0.5-0.5c0.1-0.1,0.3-0.2,0.5-0.2c0.1,0,0.3,0,0.4,0
	c0.1,0.1,0.2,0.2,0.2,0.3c0,0.1-0.1,0.3,0,0.4c0.1,0.1,0.3,0.1,0.4,0.1c0.2,0,0.3,0,0.5,0c0.3,0,0.5-0.3,0.5-0.6
	c0-0.1,0-0.3-0.1-0.4c0-0.1,0-0.2,0.1-0.3c0.2-0.1,0.5-0.2,0.7-0.2c0.2,0.1,0.2,0.4,0.2,0.5c0,0.1,0.1,0.2,0.2,0.3
	c0.1,0,0.3,0,0.3,0c0.3-0.1,0.6-0.2,0.9-0.4c0.1-0.1,0.2-0.1,0.3-0.1c0.1,0,0.2,0,0.3,0.1c0.1,0,0.1,0.1,0.2,0.1
	c0.1,0,0.2-0.1,0.3-0.2c0.1-0.2,0.2-0.4,0.4-0.6c0.1,0,0.1,0,0.2-0.1c0.1,0,0.1-0.1,0.2-0.1c0.1-0.1,0.2-0.1,0.4-0.1
	c0.3,0,0.2,0.3,0.4,0.3c0.1,0,0.2,0,0.3-0.1c0.1,0,0.2-0.1,0.3-0.1c0.1,0,0.1-0.1,0.1-0.1c0,0,0.1,0,0.1-0.1
	c0.2-0.2,0.2-0.4,0.3-0.6c0.1-0.2,0-0.4,0.2-0.6c0.1-0.1,0.2-0.2,0.3-0.2c0.3-0.2,0.5-0.4,0.5-0.7c0-0.4,0-0.7,0.2-1
	c0.2-0.3,0.4-0.5,0.7-0.7c0.3-0.2,0.6-0.3,0.9-0.4c0.4-0.1,0.7,0.1,1.1,0.1c0.2,0,0.3-0.2,0.5-0.3c0,0-1.2-2-1.6-2.6
	c-0.4-0.6-0.2-0.5-0.2-1.1c0-0.6-0.3-1-0.5-1.7s-0.2-0.8-0.7-1.5c-0.4-0.7-0.4-0.2-1.5,0c-1.1,0.2-0.5-0.4-0.6-0.8
	c0-0.4-2.1-0.4-2.4-0.6c-0.3-0.2-0.3-0.4-1-0.7c-0.2,0-0.3-0.1-0.4-0.1c-0.2,0,0,0.2,0.1,0.5c0.2,0.4-0.1,0.2-0.5,0.4
	c-0.4,0.2-0.1,1.3-0.4,2s-1.4-1-1.7-1.6c-0.3-0.6-1.1,0.2-1.4,0.5c-0.4,0.3-1.2,0.1-1.7,0.1c-0.4,0-0.4,0.3-0.5,0.6
	c-0.1,0.3-0.6,0.1-0.7,0c-0.1-0.1-0.9,0.2-1.5,0.6c-0.7,0.4-0.6,0.3-1.2-0.1c-0.6-0.4-0.2-1.1-0.1-2c0.1-0.9,0.7-0.7,1.1-1.2
	c0.4-0.5,0.4-0.3,0.9-0.8c0.5-0.5,0.1-0.7,0.1-1.1c0-0.4-1.2-0.5-1.8-0.6c-0.6-0.2-0.8,0.2-0.9,0.7c-0.1,0.5-0.3,0-1,0.4
	c-0.7,0.4-0.6-0.9-0.7-1.3c0-0.1-0.1-0.1-0.1-0.1c0,0,0.1-0.4,0.2-1c0.1-0.7-0.1-0.7-0.1-0.9c0-0.2,0.1-0.6,0.4-1
	c0.2-0.4-0.1-0.4,0-0.9c0.1-0.5,0.4-0.5,0.9-0.9c0.5-0.4-0.2-0.3-1.2-0.1c-1,0.2,0.3-1.5,0.6-2.3c0.3-0.8-0.5-0.8-1.2-1.1
	c-0.6-0.3-0.3-1.2,0-1.7c0.3-0.5-0.5-0.7-0.7-0.9c-0.2-0.2,0-0.4,0.3-1.4c0.3-1-0.5-0.3-1.1-0.6c-0.5-0.3-0.3-0.9-0.2-1.8
	c0-1,0.2-0.8,0.4-1c0.2-0.2,0.3-0.6,0.4-1s0.6-0.4,1.2-0.8c0.7-0.4,0-0.3,0.4-0.8c0.4-0.4,0.8-0.5,1.1-0.7c0.3-0.2,0.1-0.8-0.2-1.3
	c-0.3-0.5-0.3-0.7-0.1-1.6c0.2-0.9-1.4-2.3-1.5-2.4c0,0,0,0,0.1,0c0.3-0.1,0.7-0.6,1-0.8c0.4-0.2,0.4-0.3,0.7-0.2
	c0.4,0.1,0.3-0.4,0.4-0.8c0.2-0.4,0.4-0.4,0.8-0.2c0.3,0.2,0.8,0,1.2-0.3c0.4-0.2,0.6-0.3,0.9,0c0.3,0.3,0.5-0.7,0.9-1
	c0.3-0.3,0.6-0.3,1.4-0.1c0.8,0.2,1.3-1.1,1.5-1.3c0.2-0.2,0.8-0.4,1.2-0.2c0.4,0.2,0.6,0,0.8-0.6c0.2-0.6,0.4,0.2,0.7,0.6
	c0.3,0.3,0.5,0.3,0.9,0.5c0.3,0.2,0.4,0,0.8-0.4c0.3-0.4-0.5-0.3-0.6-0.6c-0.1-0.3,0.3-0.4,0.7-0.3c0.1,0,0.1-0.1,0.1-0.3
	c-0.1-0.5-0.6-1.5-0.6-2c0-0.6-0.1-0.5-0.7-0.6c-0.6-0.1-0.2-1.2-0.1-1.7c0.1-0.5-0.2-0.3-0.8-0.7c-0.5-0.4,0-0.4,0.2-0.6
	c0.2-0.2,0-0.2,0.2-0.8c0.2-0.6,0.4-0.1,0.9-0.4s-0.1-0.4-0.4-0.6c-0.4-0.2-0.2-0.5-0.4-0.8c-0.1-0.3-1.1,0.1-1.2-0.2
	c-0.1-0.3-0.4,0.1-0.9,0c-0.5,0,0.3-0.2-0.4-0.8c-0.5-0.4-0.6-0.4-0.7-0.4c0.1-2,1-2.1,1.4-2.6c0.5-0.4,0.8-0.3,1.4-0.2
	c0.6,0.1,0.5-0.4,0.4-1.1c0-0.7,0-1.9-0.2-2.2c-0.2-0.3,0.1-0.8-0.1-1.4c-0.2-0.6-0.8-0.1-1.1,0.4c-0.3,0.5-1.3-0.9-1.9-1
	c-0.6,0-0.6-0.7-0.8-1.5c-0.2-0.8-1.1-0.8-1.7-0.9c-0.6-0.1-1-0.5-1-1.1c0-0.7,0.9-0.3,1.3-0.1c0.4,0.1,0.9,0,1.4,0
	c0.5,0,0.4-0.3,0.4-0.5c0-0.3-0.1-1.3,0.3-1.9c0.3-0.6,0.5-0.5,1-0.3c0.5,0.2,0.5-0.7,0.4-1c-0.1-0.3,0.1-1.1-0.1-1.6
	c-0.2-0.5,0.2-0.5,0.3-0.7c0.1-0.2,0.6-0.1,1.2-0.1c0.6,0,0.3,0.2,0.6,0.6c0.3,0.4,0.8,1,1.2,1.5c0.4,0.4,0.5-0.1,0.6-0.4
	c0.1-0.3,1.2-0.5,1.5-0.9c0.3-0.3,0.4-0.1,1.2-0.3c0.7-0.2-0.3-1.2-0.5-1.3c-0.3-0.1-0.2-0.5-0.2-0.8c0-0.4-0.9-0.5-1.4-0.4
	c-0.5,0.1-0.4-0.6-0.4-0.8c-0.1-0.2,0.2-0.7,0.2-0.7c0,0-0.1-0.1-0.1-0.1c-0.2-0.5-0.6-0.3-0.9-0.4c-0.3-0.1-0.4,0.2-0.9,0.2
	c-0.5,0.1-0.8,0-1.8,0.3c-1,0.3-2.5-1.8-2.9-2.6c-0.4-0.8-0.9-0.3-2.1-0.2c-1.2,0.1-0.6-0.5-0.3-0.8c0.2-0.3,0.5-0.6,0.9-1.3
	c0.4-0.7,1.2-0.7,1.5-0.8c0.3-0.1,0.3-0.3,0.4-0.6c0-0.3,0.2-0.5,0.8-0.8c0.6-0.3,0.3-0.2,0.3-0.5s0.6-0.2,0.8-0.2
	c0.2,0,0.4-0.2,0.5-0.5c0.1-0.3,0.4,0,0.8-0.7c0.4-0.7,0.2,0,1-1c0.7-1-0.4-0.1-1,0.3c-0.6,0.4-2,0.9-3.3,1.7
	c-1.3,0.8,0.4-1.5,0.7-2.1c0.3-0.6,0-0.5,0-0.5C67.8,2,67.7,2,67.4,1.9c-0.5,0-1.1-0.9-1.3-0.8c-0.2,0.1-0.5,0-0.6,0.5
	s-0.2,0.9-0.4,0.8c-0.2-0.1-0.2-0.6-1-0.7c-0.7-0.1-1.5,0.3-1.8-0.2c-0.3-0.5-0.3-0.8-0.7-0.7c-0.4,0.1-2,0.1-2.3-0.3
	c-0.3-0.4-0.7-0.8-0.7-0.5v0.2c0,0.5-0.2,1.5-0.4,1.8c-0.2,0.3,0,0.9-0.2,1.1c-0.2,0.2,0,0.5-0.2,0.6c-0.2,0.1,0.1,0.6-0.1,0.8
	c-0.2,0.2,0,0.9-0.7,1.2C56.6,6,56,6.1,55.6,6.6c-0.5,0.5-1.4,1.2-2.1,1.1C52.7,7.6,52,7,51.6,7.4c-0.4,0.4-0.8,0.4-1.3,0.4
	c-0.5,0-1.1-0.2-1.1,0.2c0,0.4,0,0.7-0.5,0.8c-0.5,0-1,0-1.2,0.3c-0.2,0.4-0.4,1-1.2,1.2s-2-0.1-2.4,0.4c-0.4,0.6-1.5,1.1-2.1,1.1
	c-0.6,0-1.8-0.1-1.8-0.1c0,0.1,0,0.2-0.1,0.4c-0.3,0.6-0.4,0.8-0.2,1c0.2,0.2,0.5,0.5,0.2,0.6c-0.3,0.2-0.8,0.2-0.4,0.4
	c0.4,0.2,0.4,0,0.7,0.5c0.3,0.5,0.7,0.9,0.5,1.3c-0.2,0.4-0.5,0.8-0.1,0.9c0.4,0.1,1,0.1,1.1,0.5c0.1,0.4-0.6,0.2,0.2,0.5
	c0.8,0.3,1.4,0.5,1,1c-0.3,0.6-0.3,0.6-0.2,0.9c0.1,0.3,0.3-0.1-0.1,0.7l-0.1,0.1c-0.4,0.7-0.4,0.5-0.9,0.7c-0.5,0.2-0.9,1-1.2,0.5
	c-0.2-0.5-0.7-1.2-0.8-1c-0.1,0.3-0.2,0.6-0.6,0.5c-0.4-0.1-1.4-0.9-1.5-0.6c-0.1,0.3-0.5,0.7-0.6,0.4c-0.2-0.2-0.7-0.7-1-0.5
	c-0.3,0.2-0.2,0.5-0.6,0.2c-0.3-0.2-0.5-0.6-0.7-0.5c-0.2,0.1-1.3,0.7-0.7,0.8c0.7,0.1,1.7,0.1,1.7,0.6c0,0.5-0.1,1.1,0.2,1.1
	c0.4,0,1,0,1.1,0.2c0.1,0.3,0.9,0.2,0.7,0.6c-0.1,0.4,0,0.9-0.4,1c-0.4,0.1-0.7-0.4-0.9-0.1c-0.2,0.4-0.5,0.8-0.7,0.6
	c-0.2-0.2-0.5-0.3-0.9,0c-0.4,0.3-0.6-0.5-0.7,0.5c0,1-0.5,0.9-0.1,1.3c0.3,0.4,0.5,0.7,0.6,1.2c0.2,0.6-0.2,0.7-0.3,1.1
	c0,0.2-0.1,0.5-0.1,0.7c0.6,0.1,1.7,0.2,2.1,0.2c0.6,0,0.1,1-0.1,1.4c-0.2,0.4,0.2,0.4,0.6,0.6c0.4,0.1,0.5,0.5,0.7,0.9
	c0.2,0.4,0,1.1-0.2,1.5c-0.2,0.4-0.1,0.6,0,1.1c0.1,0.4-0.2,0.2-0.6,0.6c-0.3,0.4-0.6,0.3-1.5,0.2c-0.9-0.1-2,1.3-2.3,1.7
	c-0.3,0.4-0.4,0.2-0.7,0c-0.2-0.2-1.5,0.2-2,0c-0.5-0.2-0.5,0-1,0.2c-0.5,0.2-0.9,0.1-1.4,0.3c-0.4,0.2-1.3,0.1-1.4,0.4
	c-0.1,0.3,0.4,0.7,0.7,0.7s0.1,0.7,0,0.9c-0.1,0.2,0.2,1.1,0,1.6c-0.1,0.5,0,0.4-0.6,0.7c-0.6,0.3,0.9,0.1,1,0.4
	c0.1,0.3,0.2,0.6,1.1,0.9c0.8,0.3,0.3,1.7,0.3,1.9s0.4,0.3,0.6,0.3c0.2,0,0.2,0.1,0.3,0.4c0.1,0.3,0.3,0.5,0.8,1
	c0.2,0.1,0.3,0.1,0.4,0.1c0.1,0.3,0.3,0.8,0.3,1.5c-0.1,1.2-0.4,2-0.3,2.4c0.1,0.2,0.2,0.4,0.2,0.5s0.1,0.3,0.1,0.5
	c-0.1,0.3-0.4,0.7-0.1,0.8c0.4,0.2,0.7,0,0.4,0.5c-0.3,0.4-0.8,0.9-0.4,1.1c0.4,0.2,0.6,0,0.4,0.4c-0.2,0.5-0.6,0.5-0.3,1
	c0.4,0.5,1.2,0.6,0.8,1.4c-0.4,0.8-0.1,0.9-1,1.2c-0.9,0.3-2.1,0-2.3,0.7c-0.2,0.7-0.8,1.2-0.7,1.7c0.1,0.5-0.2,1.1,0.3,0.7
	c0.5-0.4,0.5-0.7,0.8-0.7c0.4,0,1.5,0.3,1.7,0.1c0.2-0.2,0.9-0.1,0.8,0s0.6,0.7,0.5,1.1c-0.1,0.5-0.4,0.4-0.5,0.7
	c-0.1,0.3-0.3,0.6-0.5,0.6c-0.2,0-0.6,0.7-0.3,0.8c0.3,0.1,0.8,0.3,0.3,0.9c-0.5,0.5-0.9,1-0.6,1.3C31.2,67.7,31.9,68.9,31.7,69.4
	C31.6,69.4,31.6,69.4,31.7,69.4z"/>
</svg>
</div>

<!-- 区域名称 -->
<div class="blue4 fs10 lang" lang='lb_Xinjiang' style="width:69px; height:20px; left:67px; top:116px; font-weight: bold; ">
</div>
<div class="ffnum blue4 fs12" style="width:36px; height:28px; left:65px; top:128px; ">
<span id="area_normal_rate_100007"></span><span class="sub ffnum blue4 fs10">%</span>
</div>


<div class="blue4 fs10 lang" lang='lb_Northwest' style="width:69px; height:20px; left:130px; top:153px; font-weight: bold; ">
</div>
<div class="ffnum blue4 fs12" style="width:36px; height:28px; left:129px; top:165px; ">
<span id="area_normal_rate_100006"></span><span class="sub ffnum blue4 fs10">%</span>
</div>

<div class="blue4 fs10 lang" lang='lb_Southwest' style="width:69px; height:20px; left:146px; top:204px; font-weight: bold; ">
</div>
<div class="ffnum blue4 fs12" style="width:36px; height:28px; left:143px; top:216px; ">
<span id="area_normal_rate_100004"></span><span class="sub ffnum blue4 fs10">%</span>
</div>

<div class="blue4 fs10 lang" lang='lb_north_china' style="width:69px; height:20px; left:178px; top:138px; font-weight: bold; ">
</div>
<div class="ffnum blue4 fs12" style="width:36px; height:28px; left:195px; top:149px; ">
<span id="area_normal_rate_100001"></span><span class="sub ffnum blue4 fs10">%</span>
</div>

<div class="blue4 fs10 lang" lang='lb_Northeast' style="width:69px; height:20px; left:288px; top:94px; font-weight: bold; ">
</div>
<div class="ffnum blue4 fs12" style="width:36px; height:28px; left:285px; top:106px; ">
<span id="area_normal_rate_100003"></span><span class="sub ffnum blue4 fs10">%</span>
</div>

<div class="blue4 fs10 lang" lang='lb_east_china' style="width:69px; height:20px; left:248px; top:169px; font-weight: bold; ">
</div>
<div class="ffnum blue4 fs12" style="width:36px; height:28px; left:245px; top:180px; ">
<span id="area_normal_rate_100002"></span><span class="sub ffnum blue4 fs10">%</span>
</div>

<div class="blue4 fs10 lang" lang='lb_south_cent' style="width:69px; height:20px; left:210px; top:223px; font-weight: bold; ">
</div>
<div class="ffnum blue4 fs12" style="width:36px; height:28px; left:208px; top:234px; ">
<span id="area_normal_rate_100005"></span><span class="sub ffnum blue4 fs10">%</span>
</div>


<!-- 图钉 -->

<!-- 北京 -->
<div id="pin_PEK" class="pin_green" style="width:34px; height:32px; left:239px; top:144px; ">
</div>
<div class="fs11 lang" lang='lb_beijing' style="width:80px; height:20px; left:241px; top:129px; font-weight: bold; ">

</div>

<!-- 海口 -->
<div id="pin_HAK" class="pin_green" style="width:34px; height:32px; left:200px; top:269px; ">
</div>
<div class="fs11 lang" lang='lb_haikou' style="width:80px; height:20px; left:204px; top:254px; font-weight: bold; ">

</div>

<!-- 西安 -->
<div id="pin_XIY" class="pin_green" style="width:34px; height:32px; left:203px; top:181px; ">
</div>
<div class="fs11 lang" lang='lb_xian' style="width:80px; height:20px; left:192px; top:170px; font-weight: bold; ">

</div>

<!-- 广州 -->
<div id="pin_CAN" class="pin_green" style="width:34px; height:32px; left:229px; top:249px; ">
</div>
<div class="fs11 lang" lang='lb_guangzhou' style="width:80px; height:20px; left:228px; top:235px; font-weight: bold; ">

</div>

<!-- 大连 -->
<div id="pin_DLC" class="pin_green" style="width:34px; height:32px; left:263px; top:142px; ">
</div>
<div class="fs11 lang" lang='lb_dalian' style="width:80px; height:20px; left:266px; top:122px; font-weight: bold; ">

</div>

<!-- 太原 -->
<div id="pin_TYN" class="pin_green" style="width:34px; height:32px; left:220px; top:150px; ">
</div>
<div class="fs11 lang" lang='lb_taiyuan' style="width:80px; height:20px; left:220px; top:136px; font-weight: bold; ">

</div>

<!-- 深圳 -->
<div id="pin_SZX" class="pin_green" style="width:34px; height:32px; left:233px; top:253px; ">
</div>
<div class="fs11 lang" lang='lb_shenzhen' style="width:80px; height:20px; left:253px; top:248px; font-weight: bold; ">

</div>

<!-- 杭州 -->
<div id="pin_LHW" class="pin_green" style="width:34px; height:32px; left:267px; top:204px; ">
</div>
<div class="fs11 lang" lang='lb_lanzhou' style="width:80px; height:20px; left:269px; top:194px; font-weight: bold; ">

</div>

<!-- 三亚 -->
<div id="pin_SYX" class="pin_green" style="width:34px; height:32px; left:194px; top:282px; ">
</div>
<div class="fs11 lang" lang='lb_sanya' style="width:80px; height:20px; left:182px; top:269px; font-weight: bold; ">

</div>

<!-- 长沙 -->
<div id="pin_CSX" class="pin_green" style="width:34px; height:32px; left:222px; top:210px; ">
</div>
<div class="fs11 lang" lang='lb_Changsha' style="width:80px; height:20px; left:215px; top:196px; font-weight: bold; ">

</div>

<!-- 乌鲁木齐 -->
<div id="pin_URC" class="pin_green" style="width:34px; height:32px; left:100px; top:123px; ">
</div>
<div class="fs11 lang" lang='lb_Urumqi' style="width:80px; height:20px; left:89px; top:109px; font-weight: bold; ">

</div>


<!-- 重庆 -->
<div id="pin_CKG" class="pin_green" style="width:34px; height:32px; left:185px; top:198px; ">
</div>
<div class="fs11 lang" lang='lb_Chongqing' style="width:80px; height:20px; left:185px; top:215px; font-weight: bold; ">

</div>


</div> <!-- #china_map -->



<!-- ********************************* -->
<!-- 卡片 -->
<!-- ********************************* -->

<div id="base_cards">

<div id="arp_code_PEK" class="card card1 bluecard" style="left:1392px; top:126px; ">
	<div class="head" >
		<div class="city lang" lang='lb_beijing'></div>
		<div class="weather " style="pointer-events: auto;">
			<span class=""></span>
		</div>
	</div>
	<div class="cont" >
		<!-- <div class="itm" >
			<span class="fs11 lang" lang='lb_dep'></span><br>
			<span class="normal_rate1 fs20 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div> -->
		<div class="itm" >
			<span class="fs11 lang" lang='lb_in'></span><br>
			<span class="normal_rate2 fs20 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div>
		<div class="itm" >
			<span class="fs11 lang" lang='lb_out'></span><br>
			<span class="normal_rate3 fs20 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div>
		<div class="itm" >
			<span class="fs11 lang" lang='lb_overall'></span><br>
			<span class="normal_rate4 fs20 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div>
	</div>
	
</div>


<div id="arp_code_HAK" class="card card1 bluecard" style="left:1158px; top:126px; ">
	<div class="head" >
		<div class="city lang" lang='lb_haikou'></div>
		<div class="weather " style="pointer-events: auto;">
			<span class=""></span>
		</div>
	</div>
	<div class="cont" >
		<!-- <div class="itm" >
			<span class="fs11 lang" lang='lb_dep'></span><br>
			<span class="normal_rate1 fs20 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div> -->
		<div class="itm" >
			<span class="fs11 lang" lang='lb_in'></span><br>
			<span class="normal_rate2 fs20 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div>
		<div class="itm" >
			<span class="fs11 lang" lang='lb_out'></span><br>
			<span class="normal_rate3 fs20 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div>
		<div class="itm" >
			<span class="fs11 lang" lang='lb_overall'></span><br>
			<span class="normal_rate4 fs20 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div>
	</div>
	
</div>


<div id="arp_code_XIY" class="card card2 bluecard" style="left:963px; top:217px; ">
	<div class="head" >
		<div class="city lang" lang='lb_xian'></div>
		<div class="weather " style="pointer-events: auto;">
			<span class=""></span>
		</div>
	</div>
	<div class="cont" >
		<!-- <div class="itm" >
			<span class="fs11 lang" lang='lb_dep'></span><br>
			<span class="normal_rate1 fs16 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div> -->
		<div class="itm" >
			<span class="fs11 lang" lang='lb_in'></span><br>
			<span class="normal_rate2 fs16 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div>
		<div class="itm" >
			<span class="fs11 lang" lang='lb_out'></span><br>
			<span class="normal_rate3 fs16 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div>
		<div class="itm" >
			<span class="fs11 lang" lang='lb_overall'></span><br>
			<span class="normal_rate4 fs16 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div>
	</div>
	
</div>

<div id="arp_code_CAN" class="card card2 bluecard" style="left:920px; top:322px; ">
	<div class="head" >
		<div class="city lang" lang='lb_guangzhou' ></div>
		<div class="weather " style="pointer-events: auto;">
			<span class=""></span>
		</div>
	</div>
	<div class="cont" >
		<!-- <div class="itm" >
			<span class="fs11 lang" lang='lb_dep'></span><br>
			<span class="normal_rate1 fs16 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div> -->
		<div class="itm" >
			<span class="fs11 lang" lang='lb_in'></span><br>
			<span class="normal_rate2 fs16 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div>
		<div class="itm" >
			<span class="fs11 lang" lang='lb_out'></span><br>
			<span class="normal_rate3 fs16 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div>
		<div class="itm" >
			<span class="fs11 lang" lang='lb_overall'></span><br>
			<span class="normal_rate4 fs16 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div>
	</div>
	
</div>

<div id="arp_code_DLC" class="card card2 bluecard" style="left:912px; top:427px; ">
	<div class="head" >
		<div class="city lang" lang='lb_dalian' ></div>
		<div class="weather " style="pointer-events: auto;">
			<span class=""></span>
		</div>
	</div>
	<div class="cont" >
		<!-- <div class="itm" >
			<span class="fs11 lang" lang='lb_dep'></span><br>
			<span class="normal_rate1 fs16 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div> -->
		<div class="itm" >
			<span class="fs11 lang" lang='lb_in'></span><br>
			<span class="normal_rate2 fs16 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div>
		<div class="itm" >
			<span class="fs11 lang" lang='lb_out'></span><br>
			<span class="normal_rate3 fs16 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div>
		<div class="itm" >
			<span class="fs11 lang" lang='lb_overall'></span><br>
			<span class="normal_rate4 fs16 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div>
	</div>
	
</div>

<div id="arp_code_TYN" class="card card2 bluecard" style="left:1627px; top:223px; ">
	<div class="head" >
		<div class="city lang" lang='lb_taiyuan'></div>
		<div class="weather " style="pointer-events: auto;">
			<span class=""></span>
		</div>
	</div>
	<div class="cont" >
		<!-- <div class="itm" >
			<span class="fs11 lang" lang='lb_dep'></span><br>
			<span class="normal_rate1 fs16 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div> -->
		<div class="itm" >
			<span class="fs11 lang" lang='lb_in'></span><br>
			<span class="normal_rate2 fs16 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div>
		<div class="itm" >
			<span class="fs11 lang" lang='lb_out'></span><br>
			<span class="normal_rate3 fs16 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div>
		<div class="itm" >
			<span class="fs11 lang" lang='lb_overall'></span><br>
			<span class="normal_rate4 fs16 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div>
	</div>
	
</div>


<div id="arp_code_SZX" class="card card2 bluecard" style="left:1667px; top:326px; ">
	<div class="head" >
		<div class="city lang" lang='lb_shenzhen'></div>
		<div class="weather " style="pointer-events: auto;">
			<span class=""></span>
		</div>
	</div>
	<div class="cont" >
		<!-- <div class="itm" >
			<span class="fs11 lang" lang='lb_dep'></span><br>
			<span class="normal_rate1 fs16 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div> -->
		<div class="itm" >
			<span class="fs11 lang" lang='lb_in'></span><br>
			<span class="normal_rate2 fs16 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div>
		<div class="itm" >
			<span class="fs11 lang" lang='lb_out'></span><br>
			<span class="normal_rate3 fs16 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div>
		<div class="itm" >
			<span class="fs11 lang" lang='lb_overall'></span><br>
			<span class="normal_rate4 fs16 ffnum"></span><span class="sub ffnum fs10">%</span>
		</div>
	</div>
	
</div>



<div id="arp_code_LHW" class="card card3 bluecard" style="left:947px; top:536px; ">
	<div class="head" >
		<div class="city lang" lang='lb_lanzhou'></div>
		<div class="weather " style="pointer-events: auto;">
			<span class=""></span>
		</div>
	</div>
	<div class="cont" >
		<!-- <div class="itm" >
			<span class="fs10 lang" lang='lb_dep'></span><br>
			<span class="normal_rate1 fs14 ffnum"></span><span class="sub ffnum fs9">%</span>
		</div> -->
		<div class="itm" >
			<span class="fs10 lang" lang='lb_in'></span><br>
			<span class="normal_rate2 fs14 ffnum"></span><span class="sub ffnum fs9">%</span>
		</div>
		<div class="itm" >
			<span class="fs10 lang" lang='lb_out'></span><br>
			<span class="normal_rate3 fs14 ffnum"></span><span class="sub ffnum fs9">%</span>
		</div>
		<div class="itm" >
			<span class="fs10 lang" lang='lb_overall'></span><br>
			<span class="normal_rate4 fs14 ffnum"></span><span class="sub ffnum fs9">%</span>
		</div>
	</div>
	
</div>


<div id="arp_code_SYX" class="card card2 bluecard" style="left:1684px; top:427px; ">
	<div class="head" >
		<div class="city lang" lang='lb_sanya'></div>
		<div class="weather " style="pointer-events: auto;">
			<span class=""></span>
		</div>
	</div>
	<div class="cont" >
		<!-- <div class="itm" >
			<span class="fs10 lang" lang='lb_dep'></span><br>
			<span class="normal_rate1 fs14 ffnum"></span><span class="sub ffnum fs9">%</span>
		</div> -->
		<div class="itm" >
			<span class="fs10 lang" lang='lb_in'></span><br>
			<span class="normal_rate2 fs14 ffnum"></span><span class="sub ffnum fs9">%</span>
		</div>
		<div class="itm" >
			<span class="fs10 lang" lang='lb_out'></span><br>
			<span class="normal_rate3 fs14 ffnum"></span><span class="sub ffnum fs9">%</span>
		</div>
		<div class="itm" >
			<span class="fs10 lang" lang='lb_overall'></span><br>
			<span class="normal_rate4 fs14 ffnum"></span><span class="sub ffnum fs9">%</span>
		</div>
	</div>
	
</div>


<div id="arp_code_CSX" class="card card3 bluecard" style="left:1660px; top:536px; ">
	<div class="head" >
		<div class="city lang" lang='lb_Changsha'></div>
		<div class="weather " style="pointer-events: auto;">
			<span class=""></span>
		</div>
	</div>
	<div class="cont" >
		<!-- <div class="itm" >
			<span class="fs10 lang" lang='lb_dep'></span><br>
			<span class="normal_rate1 fs14 ffnum"></span><span class="sub ffnum fs9">%</span>
		</div> -->
		<div class="itm" >
			<span class="fs10 lang" lang='lb_in'></span><br>
			<span class="normal_rate2 fs14 ffnum"></span><span class="sub ffnum fs9">%</span>
		</div>
		<div class="itm" >
			<span class="fs10 lang" lang='lb_out'></span><br>
			<span class="normal_rate3 fs14 ffnum"></span><span class="sub ffnum fs9">%</span>
		</div>
		<div class="itm" >
			<span class="fs10 lang" lang='lb_overall'></span><br>
			<span class="normal_rate4 fs14 ffnum"></span><span class="sub ffnum fs9">%</span>
		</div>
	</div>
	
</div>


<div id="arp_code_URC" class="card card3 bluecard" style="left:1000px; top:636px; ">
	<div class="head" >
		<div class="city lang" lang='lb_Urumqi'></div>
		<div class="weather " style="pointer-events: auto;">
			<span class=""></span>
		</div>
	</div>
	<div class="cont" >
		<!-- <div class="itm" >
			<span class="fs10 lang" lang='lb_dep'></span><br>
			<span class="normal_rate1 fs14 ffnum"></span><span class="sub ffnum fs9">%</span>
		</div> -->
		<div class="itm" >
			<span class="fs10 lang" lang='lb_in'></span><br>
			<span class="normal_rate2 fs14 ffnum"></span><span class="sub ffnum fs9">%</span>
		</div>
		<div class="itm" >
			<span class="fs10 lang" lang='lb_out'></span><br>
			<span class="normal_rate3 fs14 ffnum"></span><span class="sub ffnum fs9">%</span>
		</div>
		<div class="itm" >
			<span class="fs10 lang" lang='lb_overall'></span><br>
			<span class="normal_rate4 fs14 ffnum"></span><span class="sub ffnum fs9">%</span>
		</div>
	</div>
	
</div>


<div id="arp_code_CKG" class="card card3 bluecard" style="left:1608px; top:636px; ">
	<div class="head" >
		<div class="city lang" lang='lb_Chongqing'></div>
		<div class="weather " style="pointer-events: auto;">
			<span class=""></span>
		</div>
	</div>
	<div class="cont" >
		<!-- <div class="itm" >
			<span class="fs10 lang" lang='lb_dep'></span><br>
			<span class="normal_rate1 fs14 ffnum"></span><span class="sub ffnum fs9">%</span>
		</div> -->
		<div class="itm" >
			<span class="fs10 lang" lang='lb_in'></span><br>
			<span class="normal_rate2 fs14 ffnum"></span><span class="sub ffnum fs9">%</span>
		</div>
		<div class="itm" >
			<span class="fs10 lang" lang='lb_out'></span><br>
			<span class="normal_rate3 fs14 ffnum"></span><span class="sub ffnum fs9">%</span>
		</div>
		<div class="itm" >
			<span class="fs10 lang" lang='lb_overall'></span><br>
			<span class="normal_rate4 fs14 ffnum"></span><span class="sub ffnum fs9">%</span>
		</div>
	</div>
	
</div>


</div> <!-- #base_cards -->


<!--

<div class="fs11 blue2" style="width:46px; height:160px; left:860px; top:527px; " id="comp_rank_list_month">
</div>
<div class="fs15 blue1" style="width:105px; height:24px; left:860px; top:735px; font-weight: bold;">
<span class="lang" lang='lb_rank_m'></span> <span class="yellow" id="comp_rank_month"></span>
</div>


<div class="fs11 blue2" style="width:46px; height:160px; left:1824px; top:527px; " id="comp_rank_list_year">
</div>
<div class="fs15 blue1 right" style="width:140px; height:24px; left:1727px; top:735px; font-weight: bold;">
<span class="lang" lang='lb_rank_y'></span> <span class="yellow" id="comp_rank_year"></span>
</div>

-->


<!-- ********************************* -->
<!-- 搜索 -->
<!-- ********************************* -->
<div class="searchform" style='display: none;'>
    <div class="tt lang" lang='lb_search_flt'></div>
    <div class="ipt">
    	<input id='ipt_fltno' class="input" type="text" placeholder="" value=""> 
    	<div class="ico_search"></div>
    </div>
    <div class="error lang" lang='lb_search_flt_err'></div>
</div>



<!-- ********************************* -->
<!-- 时间 -->
<!-- ********************************* -->
<div class="fs15 blue1 right" style="width:205px; height:24px; left:1855px; top:37px; font-weight: bold;">
<span class="lang" lang='lb_beijing'></span> <span class="ffnum fs22 blue1" id="time_beijing"></span>
<div class="ffnum fs12 blue1" style="right: 15px; top:24px; width:80px; height:24px; " id="date_beijing"></div>
</div>

<div class="fs15 blue1 right" style="width:205px; height:24px; left:2015px; top:37px; font-weight: bold;">
<span class="lang" lang='lb_london'></span> <span class="ffnum fs22 blue1" id="time_london"></span>
<div class="ffnum fs12 blue1" style="right: 15px; top:24px; width:80px; height:24px; " id="date_london"></div>
</div>

<div class="fs15 blue1 right" style="width:205px; height:24px; left:2175px; top:37px; font-weight: bold;">
<span class="lang" lang='lb_sydney'></span> <span class="ffnum fs22 blue1" id="time_sydney"></span>
<div class="ffnum fs12 blue1" style="right: 15px; top:24px; width:80px; height:24px; " id="date_sydney"></div>
</div>

<div class="fs15 blue1 right" style="width:205px; height:24px; left:2330px; top:37px; font-weight: bold;">
<span class="lang" lang='lb_newyork'></span> <span class="ffnum fs22 blue1" id="time_newyork"></span>
<div class="ffnum fs12 blue1" style="right: 15px; top:24px; width:80px; height:24px; " id="date_newyork"></div>
</div>





<!-- VIP/VVIP 计划总量 -->
<div class="fs14 center" style="width:205px; height:26px; left:2059px; top:172px; font-weight: bold;">
VIP / VVIP
</div>
<canvas id="cvs_vip_people" width="118" height="60" style="left:1965px; top:173px; background:rgba(255,0,0,0);"></canvas>

<div class="center" style="width:205px; height:66px; left:2056px; top:194px; font-weight: bold;">
<span class="blue1 fs14 lang" lang='lb_sch_total'></span><br>
<span class=" fs30 ffnum" id="vvip_people_total"></span><span class="sub blue2 fs12 lang" lang='lb_person'></span>
</div>

<div class="center" style="width:225px; height:26px; left:1905px; top:230px; font-weight: bold;">
<span class="blue1 fs18" >■</span>
<span class="blue1 fs12 lang" lang='lb_completed' ></span>
<span class=" fs16 ffnum" id="vvip_people_complete"></span><span class="sub blue2 fs10 lang" lang='lb_person'></span>
</div>



<!-- VIP/VVIP 保障航班总量 -->
<div class="fs14 center" style="width:205px; height:26px; left:2059px; top:290px; font-weight: bold;">
VIP / VVIP
</div>
<canvas id="cvs_flt_support" width="86" height="86" style="left:1978px; top:282px; background:rgba(255,0,0,0);"></canvas>
<div class="" style="background: #034781; width: 66px; height:66px; left:1988px; top:292px; overflow: hidden; border-radius: 43px; transform: rotate(0deg); ">
	<div class="div_flt_support1" style="background: #00A7FF; width: 66px; height:0px; left:0px; transform: rotate(0deg); "></div>
	<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 69.05 52" style="position: absolute; top:8px;"><path style="fill:none;stroke:#051f50;stroke-width:0.5px;stroke-linecap:round;stroke-linejoin:round;" d="M.1,60H68.15M.1,51.45H68.15M.1,43H68.15M.1,34.45H68.15M.1,26H68.15M.1,17.45H68.15M.1,8.95H68.15" transform="translate(0.4 -8.45)"/></svg>
	<div class="div_flt_support2" style="background: #A3D900; width: 66px; height:0px; left:0px; transform: rotate(0deg); border:1px solid #051f50;"></div>
	<!--div class="gradient-ball" style="width: 126px; height:126px; top:-40px; right:0px; transform: rotate(0deg); "></div-->
</div>

<div class="center" style="width:205px; height:66px; left:2056px; top:314px; font-weight: bold;">
<span class="blue1 fs14 lang" lang='lb_bz_total' ></span><br>
<span class=" fs30 ffnum" id="vvip_flt_total"></span><span class="sub blue2 fs12 lang" lang='lb_flts'></span>
</div>

<div class="center" style="width:125px; height:26px; left:1890px; top:347px; font-weight: bold;">
<span class="blue1 fs18" >■</span>
<span class="blue1 fs12 lang" lang='lb_completed' ></span><br>
<span class=" fs16 ffnum" id="vvip_flt_complete"></span><span class="sub blue2 fs10 lang" lang='lb_flts'></span>
</div>

<div class="center" style="width:125px; height:26px; left:2018px; top:347px; font-weight: bold;">
<span class="green fs18" >■</span>
<span class="blue1 fs12 lang" lang='lb_exec' ></span><br>
<span class=" fs16 ffnum" id="vvip_flt_exec"></span><span class="sub blue2 fs10 lang" lang='lb_flts'></span>
</div>


<!-- 预警航班总量 -->
<canvas id="cvs_flt_warning" width="110" height="110" style="left:1946px; top:408px; background:rgba(255,0,0,0);"></canvas>

<div class="blue2 fs9" style="width:22px; height:22px; left:1955px; top:448px; opacity:0.9; ">25</div>
<div class="blue2 fs9" style="width:22px; height:22px; left:1995px; top:415px; opacity:0.9; ">50</div>
<div class="blue2 fs9" style="width:22px; height:22px; left:2037px; top:448px; opacity:0.9; ">75</div>
<div class="blue2 fs9" style="width:32px; height:22px; left:2027px; top:480px; opacity:0.9; ">100</div>

<div class="center" style="width:205px; height:66px; left:2050px; top:423px; font-weight: bold;">
<span class="blue1 fs14 lang" lang='lb_warning_flt' ></span><br>
<span class=" fs30 ffnum" id="warning_total"></span><span class="sub blue2 fs12 lang" lang='lb_flts'></span>
<div style="width: 100%;"><span class="blue1 fs12 lang" lang='lb_completed_num'></span><span class=" fs16 ffnum" id="warning_complete"></span><span class="sub blue2 fs10 lang" lang='lb_flts'></span></div>
</div>

<div class="center" style="width:50px; height:50px; left:1978px; top:445px; ">
<span class=" fs18 ffnum" id="warning_normal_rate"></span><span class="sub ffnum fs10">%</span>
<div class="blue2 fs10 lang" lang='lb_completed' style="width:100px; left:-27px; top:20px; ">

</div>
</div>


<!-- 中转旅客总量 -->

<div class="left" style="width:185px; height:26px; left:1927px; top:531px; font-weight: bold;">
<span class="blue1 fs14 lang" lang='lb_trans_psr'></span>
<span id="zz_trv_num_total" class="fs20 ffnum"></span><span class="sub blue2 fs10 lang" lang='lb_person'></span>
</div>

<div class="blue1 right fs11 lang" lang='lb_in_out_4' style="width:80px; height:26px; left:2123px; top:538px; font-weight: bold;">

</div>
<div class="blue1 fs11 lang" lang='lb_seat_booked' style="width:76px; height:20px; left:1928px; top:566px; font-weight: bold;">

</div>

<div class="blue2 fs11 lang" lang='lb_zz_normal' style="width:60px; height:20px; left:2018px; top:566px; font-weight: bold;">

</div>

<div class="blue2 fs11 lang" lang='lb_zz_short' style="width:60px; height:20px; left:2090px; top:566px; font-weight: bold;">

</div>

<div class="blue2 fs11 lang" lang='lb_zz_miss' style="width:60px; height:20px; left:2150px; top:566px; font-weight: bold;">

</div>
<img id="sxsjcg_details_loading" src="img/ajax-loader.gif" style="display: none;width:20px; height:20px; position: absolute; left:2060px; top:610px;">
<div id="sxsjcg_details">
		<div id="zz_trv_num_INTOOUT" class="blue2 fs11 con_flex_row" style="width:300px; height:20px; left:1930px; top:587px;">
				<span style="width:77px; height:20px; display: inline-block;" class="flex_none lang" lang='lb_cn_int'></span>
			
				<span class="flex_none left" style="width:30px; height:20px; display: inline-block;">
					<span class="bar greenbar bar_ZZZC" style="width: 0px; "></span>
				</span> 
				<span class="flex_none ffnum ZZZC" style="width:40px; height:20px; display: inline-block; margin-left:5px;">0</span>
			
				<span class="flex_none left" style="width:30px; height:20px; display: inline-block;">
					<span class="bar bluebar bar_ZZJZ" style="width: 0px; "></span>
				</span> 
				<span class="flex_none ffnum ZZJZ" style="width:35px; height:20px; display: inline-block; margin-left:5px;">0</span>
			
				<span class="flex_none left" style="width:27px; height:20px; display: inline-block;">
					<span class="bar bluebar bar_ZZCS" style="width: 0px; "></span>
				</span> 
				<span class="flex_none ffnum ZZCS" style="width:30px; height:20px; display: inline-block; margin-left:5px;">0</span>
			
			</div>
			
			
			<div id="zz_trv_num_INTOIN" class="blue2 fs11 con_flex_row" style="width:300px; height:20px; left:1930px; top:606px;">
				<span style="width:77px; height:20px; display: inline-block;" class="flex_none lang" lang='lb_cn_cn'></span>
			
				<span class="flex_none left" style="width:30px; height:20px; display: inline-block;">
					<span class="bar greenbar bar_ZZZC" style="width: 0px; "></span>
				</span> 
				<span class="flex_none ffnum ZZZC" style="width:40px; height:20px; display: inline-block; margin-left:5px;">0</span>
			
				<span class="flex_none left" style="width:30px; height:20px; display: inline-block;">
					<span class="bar bluebar bar_ZZJZ" style="width: 0px; "></span>
				</span> 
				<span class="flex_none ffnum ZZJZ" style="width:35px; height:20px; display: inline-block; margin-left:5px;">0</span>
			
				<span class="flex_none left" style="width:27px; height:20px; display: inline-block;">
					<span class="bar bluebar bar_ZZCS" style="width: 0px; "></span>
				</span> 
				<span class="flex_none ffnum ZZCS" style="width:30px; height:20px; display: inline-block; margin-left:5px;">0</span>
			
			</div>
			
			
			<div id="zz_trv_num_OUTTOIN" class="blue2 fs11 con_flex_row" style="width:300px; height:20px; left:1930px; top:625px;">
				<span style="width:77px; height:20px; display: inline-block;" class="flex_none lang" lang='lb_int_cn'></span>
			
				<span class="flex_none left" style="width:30px; height:20px; display: inline-block;">
					<span class="bar greenbar bar_ZZZC" style="width: 0px; "></span>
				</span> 
				<span class="flex_none ffnum ZZZC" style="width:40px; height:20px; display: inline-block; margin-left:5px;">0</span>
			
				<span class="flex_none left" style="width:30px; height:20px; display: inline-block;">
					<span class="bar bluebar bar_ZZJZ" style="width: 0px; "></span>
				</span> 
				<span class="flex_none ffnum ZZJZ" style="width:35px; height:20px; display: inline-block; margin-left:-px;">0</span>
			
				<span class="flex_none left" style="width:27px; height:20px; display: inline-block;">
					<span class="bar bluebar bar_ZZCS" style="width: 0px; "></span>
				</span> 
				<span class="flex_none ffnum ZZCS" style="width:30px; height:20px; display: inline-block; margin-left:5px;">0</span>
			
			</div>
			
			
			<div id="zz_trv_num_OUTTOOUT" class="blue2 fs11 con_flex_row" style="width:300px; height:20px; left:1930px; top:644px;">
				<span style="width:77px; height:20px; display: inline-block;" class="flex_none lang" lang='lb_int_int'></span>
			
				<span class="flex_none left" style="width:30px; height:20px; display: inline-block;">
					<span class="bar greenbar bar_ZZZC" style="width: 0px; "></span>
				</span> 
				<span class="flex_none ffnum ZZZC" style="width:40px; height:20px; display: inline-block; margin-left:5px;">0</span>
			
				<span class="flex_none left" style="width:30px; height:20px; display: inline-block;">
					<span class="bar bluebar bar_ZZJZ" style="width: 0px; "></span>
				</span> 
				<span class="flex_none ffnum ZZJZ" style="width:35px; height:20px; display: inline-block; margin-left:5px;">0</span>
			
				<span class="flex_none left" style="width:27px; height:20px; display: inline-block;">
					<span class="bar bluebar bar_ZZCS" style="width: 0px; "></span>
				</span> 
				<span class="flex_none ffnum ZZCS" style="width:30px; height:20px; display: inline-block; margin-left:5px;">0</span>
			
			</div>
</div>






<!-- ********************************* -->
<!-- 重点关注航班 -->
<!-- ********************************* -->
<div class="fs18" style="display:none;width:215px; height:36px; left:2375px; top:148px; font-weight: bold;">
<span class="lang" lang='lb_important_flt'></span> <span class="blue2 fs14 lang" lang='lb_in_2h'></span>
</div>

<!-- TAB -->
<div id="tab_vip" class="fs15 tab_normal tab_selected" style="border-radius: 26px 0 0 0;width:83px; height:39px; left:2230px; top:148px; font-weight: bold; background: url('img/b1.1_tab_ico_1.png') no-repeat 13px 6px; background-size: 26px 23px; padding:7px 0 0 44px; border-top: 1px solid #2391E4;">
VIP
</div>

<!-- vvip不显示 -->
<div id="tab_vvip" class="fs15 tab_normal" style="display:none !important;width:100px; height:39px; left:2330px; top:148px; font-weight: bold; background: url('img/b1.1_tab_ico_2.png') no-repeat 10px 6px; background-size: 26px 23px; padding:7px 0 0 40px; border-left:1px solid #2391E4; border-top: 1px solid #2391E4;">
VVIP
</div>

<div id="tab_warning" class="fs15 tab_normal lang" lang='lb_tab_warning' style="width:90px; height:39px; left:2313px; top:148px; font-weight: bold; background: url('img/b1.1_tab_ico_3.png') no-repeat 9px 6px; background-size: 26px 23px; padding:7px 0 0 42px; border-left:1px solid #2391E4; border-top: 1px solid #2391E4;">

</div>

<!-- 重点关注不显示 -->
<div id="tab_important" class="fs15 tab_normal lang" lang='lb_important' style="display:none !important;width:125px; height:39px; left:2523px; top:148px; font-weight: bold; background: url('img/b1.1_tab_ico_4.png') no-repeat 7px 6px; background-size: 26px 23px; padding:7px 0 0 39px; border-left:1px solid #2391E4; border-top: 1px solid #2391E4;">

</div>

<div id="tab_abnormal" class="fs15 tab_normal lang" lang='lb_tab_abnormal' style="width:130px; height:39px; left:2397px; top:148px; font-weight: bold; background: url('img/b1.1_tab_ico_5.png') no-repeat 7px 6px; background-size: 26px 23px; padding:7px 0 0 39px; border-left:1px solid #2391E4; border-top: 1px solid #2391E4;">

</div>
<div id="tab_securemonitor" class="fs15 tab_normal lang" lang='lb_tab_securemonitor' style="border-radius: 0 24px 0 0;width:120px; height:39px; left:2526px; top:148px; font-weight: bold; background: url('img/b1.1_tab_ico_6.png') no-repeat 7px 6px; background-size: 26px 23px; padding:7px 0 0 39px; border-left:1px solid #2391E4; border-top: 1px solid #2391E4;">

</div>
<!-- tab detail for flights -->
<div id="flight_detail_tab_content">
<!-- left item list -->
<div id="flt_list_holder" style="width:90px; height:438px; left:2240px; top:231px; font-weight: bold; overflow-y: hidden; pointer-events: auto;">
	<div class="wrap">
	<!-- 
	<div class="flt_list_itm selected" >HU9933</div>
	<div class="flt_list_itm" >HU9933</div>
	 -->
	</div>
</div>


<!-- 航班详情 -->
<div class="fs14 center lang" lang='lb_flt_details' style="width:180px; height:22px; left:2329px; top:237px; font-weight: bold;">

</div>

<img id="flight_details_loading" src="img/ajax-loader.gif" style="width:16px; height:16px; position: absolute; left:2413px; top:380px;">
<div id="flight_no_data" class="center lang" lang='lb_no_data_fltinfo' style="opacity:0.8; width:220px; height:32px; position: absolute; left:2306px; top:384px; display: none;"></div>
<div id="flight_details" style="display: none;">
<div class="" style="width:150px; height:360px; left:2353px; top:269px; ">
	<span style="height:20px; display: inline-block;">
		<span id="detail_depCity" class="blue1 fs13" ></span><br>
		<span id="detail_stdChn" class="fs14 ffnum" ></span>
	</span>
	<span class="blue2 center fs28" style="width:30px; height:20px; display: inline-block; ">→</span>
	<span style="height:20px; display: inline-block;">
		<span id="detail_arrCity" class="blue1 fs13" ></span><br>
		<span id="detail_staChn" class="fs14 ffnum" ></span>
	</span>
</div>

<div id="detail_status" class="fs14 center" style="width:40px; height:22px; left:2351px; top:321px; font-weight: bold; border-radius: 3px; background: #6CCEEF; color:#001A4C; padding-top:1px;">

</div>


<div class="fs12" style="width:130px; height:50px; left:2353px; top:356px; font-weight: bold;">
<span class="blue2 lang" lang='lb_pre_flt_take'></span><br>
<span id="detail_prev_stdChn" class="ffnum fs14"></span><br>
</div>

<div class="fs12" style="width:130px; height:50px; left:2353px; top:398px; font-weight: bold;">
<span class="blue2 lang" lang='lb_pre_flt_pass'></span><br>
<span id="detail_prev_passTime" class="ffnum fs14"></span><br>
</div>

<div class="fs12" style="width:130px; height:100px; left:2353px; top:448px; font-weight: bold;">
<span class="blue2 lang" lang='lb_main_ac'></span><br>
<span id="detail_flightNo" lass=""></span><br>
<span id="detail_iataAcType" class=""></span><br>

<span id="detail_backup" class=""></span>
</div>

<div class="fs12" style="width:130px; height:100px; left:2353px; top:565px; font-weight: bold;">
<span class="blue2 lang" lang='lb_crew_info'></span><br>
<span id="detail_crwPilotInf" class=""></span><br>
<span id="detail_crwStatus" class=""></span><br>
</div>

</div><!--flight_details-->


<!-- 航班保障节点 -->
<div class="fs14 center lang" lang='lb_flt_nodes' style="width:200px; height:22px; left:2470px; top:237px; font-weight: bold;">

</div>

<div style="width:2px; height:306px; left:2515px; top:286px; background: #11529D;">
</div>

<div class="fs12" style="width:120px; height:360px; left:2509px; top:272px; font-weight: bold;">
	<div class="reltv linenode">
		<span id="fltnode_cncPilotArrTime" class="blt blue3" style="width:20px; height:20px; display: inline-block;">
		◉
		</span>
		<span class="blue1 lang" lang='lb_node_crew_arr' style="width:92px; height:20px; display: inline-block;">
		
		</span> 
	</div>

	<div class="reltv linenode">
		<span id="fltnode_checkInEnd" class="blt blue3" style="width:20px; height:20px; display: inline-block;">
		◉
		</span>
		<span class="blue1 lang" lang='lb_node_cut_load' style="width:92px; height:20px; display: inline-block;">
		
		</span> 
	</div>

	<div class="reltv linenode">
		<span id="fltnode_cncCabinSupplyEndTime" class="blt blue3" style="width:20px; height:20px; display: inline-block;">
		◉
		</span>
		<span class="blue1 lang" lang='lb_node_cat_supply' style="width:92px; height:20px; display: inline-block;">
		
		</span> 
	</div>

	<div class="reltv linenode">
		<span id="fltnode_cncCleanEndTime" class="blt blue3" style="width:20px; height:20px; display: inline-block;">
		◉
		</span>
		<span class="blue1 lang" lang='lb_node_cleansing' style="width:92px; height:20px; display: inline-block;">
		
		</span> 
	</div>

	<div class="reltv linenode">
		<span id="fltnode_cncMCCReleaseTime" class="blt blue3" style="width:20px; height:20px; display: inline-block;">
		◉
		</span>
		<span class="blue1 lang" lang='lb_node_release' style="width:92px; height:20px; display: inline-block;">
		
		</span> 
	</div>

	<div class="reltv linenode">
		<span id="fltnode_planeReady" class="blt blue3" style="width:20px; height:20px; display: inline-block;">
		◉
		</span>
		<span class="blue1 lang" lang='lb_node_ac_ready' style="width:92px; height:20px; display: inline-block;">
		
		</span> 
	</div>

	<div class="reltv linenode">
		<span id="fltnode_cncInformBoardTime" class="blt blue3" style="width:20px; height:20px; display: inline-block;">
		◉
		</span>
		<span class="blue1 lang" lang='lb_node_boarding' style="width:92px; height:20px; display: inline-block;">
		
		</span> 
	</div>

	<div class="reltv linenode">
		<span id="fltnode_cncBoardOverTime" class="blt blue3" style="width:20px; height:20px; display: inline-block;">
		◉
		</span>
		<span class="blue1 lang" lang='lb_node_boarding_end' style="width:92px; height:20px; display: inline-block;">
		
		</span> 
	</div>

	<div class="reltv linenode">
		<span id="fltnode_cncClosePaxCabinTime" class="blt blue3" style="width:20px; height:20px; display: inline-block;">
		◉
		</span>
		<span class="blue1 lang" lang='lb_node_door_close' style="width:92px; height:20px; display: inline-block;">
		
		</span> 
	</div>

	<div class="reltv linenode">
		<span id="fltnode_cncCloseCargoCabinTime" class="blt blue3" style="width:20px; height:20px; display: inline-block;">
		◉
		</span>
		<span class="blue1 lang" lang='lb_node_cargo_close' style="width:92px; height:20px; display: inline-block;">
		
		</span> 
	</div>

	<div class="reltv linenode">
		<span id="fltnode_cncPushTime" class="blt blue3" style="width:20px; height:20px; display: inline-block;">
		◉
		</span>
		<span class="blue1 lang" lang='lb_node_push' style="width:92px; height:20px; display: inline-block;">
		
		</span> 
	</div>

	<div class="reltv linenode">
		<span id="fltnode_cncACARSTOFF" class="blt blue3" style="width:20px; height:20px; display: inline-block;">
		◉
		</span>
		<span class="blue1 lang" lang='lb_node_takeoff' style="width:92px; height:20px; display: inline-block;">
		
		</span> 
	</div>

</div>
<!-- end flight detail for tab -->
</div>

<!-- abnormal flights for tab -->
<div id="abnormal_flights_tab_content" style="display:none;">
	<div style="background-color:#08163b;left: 2232px;top: 188px;height: 483px;width: 413px;border-radius: 0 0 19px 19px;">
	</div>
		<div id="abnormal_date_select" class="con_flex_row" style="cursor: pointer;pointer-events: auto;left: 2560px;top: 200px;font-size: 12px;text-align:center;border-radius: 4px;border: 1px solid #2391E4;overflow: hidden;transform: rotate(0deg);">
            <div class="tab flex_none tab_L lang" lang='lb_abnormal_week'  data-type="L"></div>
            <div class="tab flex_none tab_M selected lang" lang='lb_abnormal_month' data-type="M"></div>
        </div>
<div id="block_abnormal_flights" class="block_r">
	 <div class="block b5" style="left:2240px;top: 222px;position:relative;width:400px;">
	     <span style="display:none;" class="fs14 lang" lang='lb_tab_abnormal'></span>
	     <div class="flex-box " style="/*margin-top: 10px;*/">
	         <div><span style="color:#54B8FF;" class="blue fs9 lang" lang='lb_abnormal_rf'></span><br><span id="val_TURNBACK_NO" class="white fs16">0</span></div>
	         <div><span style="color:#54B8FF;" class="blue fs9 lang" lang='lb_abnormal_cf'></span><br><span id="val_CANCEL" class="white fs16">0</span></div>
	         <div class="delay12"><span style="color:#54B8FF;" class="blue fs9 lang" lang='lb_abnormal_Delay'>1-2h</span><br><span id="val_DELAY_NO_1_2" class="white fs16">0</span></div>
	         <div class="delay24"><span style="color:#54B8FF;" class="blue fs9 lang" lang='lb_abnormal_Delay'>2-4h</span><br><span id="val_DELAY_NO_2_4" class="white fs16">0</span></div>
	         <div><span style="color:#54B8FF;" class="blue fs9 lang" lang='lb_abnormal_Delay'>&gt;4h</span><br><span id="val_DELAY_NO_240" class="white fs16">0</span></div>
	     </div>
	 </div>
	 <!--块6-->
	 <div id="unnormal_box" class="block b6" style="left:2240px;top: 300px;width:400px;">
	     <span class="fs14 lang" lang='lb_abnormal_abis'></span><br>
	     <!--原始部分-->
	     <div class="b6-cont">
	         <div class="tit blue fs11 lang" lang='lb_abnormal_total'></div>
	         <div class="chart">
	             <div id="chart_abnormal"></div>
	         </div>
	         <div id="abnormal_total"></div>
	         <div id="abnormal_hb" class="abnormal_hb">
	             <span class="fs11 blue lang" lang='lb_abnormal_crr'> </span>
	             <span class="val fs11"></span>
	             <!--or className = down-->
	             <span class="arr fs11" style="padding-right:9px;"></span>
	             <!---->
	         </div>
	         <div id="abnormal_main" class="con_flex_row">
	             
	             <div id="abnormal_main_val" class="">
	                 <span class="val lb fs11 bold"></span>
	                 <span class="name lb fs11 blue"></span>
	             </div>
	             <div id="abnormal_main_hb" class="">
	                 <div class="lb fs11 blue lang" lang='lb_abnormal_crr'></div>
	                 <span class="val fs11"></span>
	                 <!--or className = down-->
	                 <span class="arr fs11" style="padding-right:9px;"></span>
	             </div>
	         </div>
	
             <span id="unnormal_detail" class="detail"></span>
	
	     </div>
	     <!--弹出部分-->
	     <div class="extr">
	         <span class="fs12 blue_l lang" lang='lb_abnormal_ica' style="color: #5fc5ff;"></span>
	         <div id="content_evt_ays" class="tabcontent">
	             <!--echart块-->
	             <div id="chart_l_runTimeEvent" class="chartblock" style="position: absolute;width: 140px;height:121px;margin-top: 10px;" prop-width="110" prop-height="100"></div>
	             <!--统计row群-->
	             <div id="l_runTimeEventChartLegend">
	             </div>
	             <!--翻页-->
	             <div class="btns">
	                 <span id="l-pre-btn" class="pre-btn"></span>
	                 <span id="l-next-btn" class="next-btn"></span>
	             </div>
	         </div>
	     </div>
	 </div>
	 
	 
                    <div class="tabc tabc1 block" style="height: 160px;" >

                        <!--div class='legend'>
                            <span class='lbl'>图例</span>
                            <span class='lst'>
                            </span>
                        </div-->

                        <div id='scrollpane1' class='scrollpane'>
                            <div class='scrollcontent'>
                            </div><!-- /scrollcontent -->
                        </div><!-- /scrollpane -->

                    </div>
</div>

       <div id="block_securemonitor" style="display:none;left:2245px;top:231px;width: 385px;" class="block_l2 block-frame">
            <div class="cotent">
                <div class="row1">
                    <!--块1-->
                    <div class="block b1">
                        <span class="fs12 blue_l lang" lang='lb_abnormal_si'>：</span><span id='signCount' class="fs18 white"style="padding-left: 100px">0</span>
                    </div>
                    <!--块2-->
                    <div id="unsafe_box" class="block b2" style="height:385px;background-color: transparent;">
                        <!--原始部分-->
                        <div class="b2-cont" style="height: 200px;">
                            <span class="fs12 blue_l lang" lang='lb_abnormal_uis'></span><span id="unsafe_detail" class="detail"></span>
                            <div class="r1">
                                <div id='eventCount' class="c fs22" style="height: 60px;"><span></span><span class="fs8 lang" lang='lb_abnormal_qi' ></span></div>
                                <div id='s_evt_2' class="c fs11 up-items" style='cursor:pointer;margin-left: 20px;'>
                                    <div class="items">
                                        <span class="item1 icon icon1">
                                        </span>
                                        <span class="item2">
                                            <span id='humanEventCount' class="fs16"></span>
                                            <span class='fs9 lang' lang='lb_abnormal_qi'></span>
                                        </span>
                                    </div>
                                    <div class="items">
                                        <span class="item1">
                                            <span class="blue lang" lang='lb_abnormal_mm'></span>
                                        </span>
                                        <span class="item2">
                                            <span id='humanEventRate' class="fs11"></span>
                                            <span class='fs8'>%</span><span class="fs8 lang" lang='lb_abnormal_ratio'></span>
                                        </span>
                                    </div>
                                </div>
                                <div id='s_evt_3' class="c fs11 up-items" style='cursor:pointer'>
                                    <div class="items">
                                        <span class="item1 icon icon2">
                                        </span>
                                        <span class="item2">
                                            <span id='machineCount' class="fs16"></span>
                                            <span class='fs9 lang' lang='lb_abnormal_qi' ></span>
                                        </span>
                                    </div>
                                    <div class="items">
                                        <span class="item1">
                                            <span class="blue lang" lang='lb_abnormal_maint'></span>
                                        </span>
                                        <span class="item2">
                                            <span id='machineRate' class="fs11"></span>
                                            <span class='fs8'>%</span><span class="fs8 lang" lang='lb_abnormal_ratio'></span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="r2">
                                <div class="event_hb item1" >
                                    <span class="fs11 blue lang" lang='lb_abnormal_crr'> </span>
                                    <span class="val fs11"></span>
                                    <!--or className = down-->
                                    <span class="arr fs9" style='padding-right:9px;'></span>
                                    <!---->
                                </div>
                                <div id='s_evt_serious' class="item2 fs11 white" style='cursor:pointer'>
                                    <span class="blue  lang" lang='lb_abnormal_se'></span>
                                    <span id='seriousCount' class="red fs16"></span>
                                     <span class='fs9 lang' lang='lb_abnormal_qi' ></span>
                                    <!--span>(<span id='seriousCountOS'></span>)</span-->
                                </div>
                            </div>
                        </div>
                        <!--弹出部分-->
                        <div class="block_r extr">
                            
                            <div class="tabc tabc2">

                                <div class='legend' style="background-color: rgba(19,60,117,0.9); border-radius: 5px;width: 385px;margin-left: -10px;padding: 10px 20px;">
                                    <span style="display:none;" class='lbl lang' lang='lb_abnormal_legend' ></span>
                                    <span class='lst'>
                                        <span class='itm lb lang' lang='lb_abnormal_sa' >：</span>
                                        <span class='itm itm1'><span class='dot'>●</span><span class='lb lang" lang='lb_abnormal_mm'></span></span>
                                        <span class='itm itm2'><span class='dot'>●</span><span class='lb lang" lang='lb_abnormal_acc'></span></span>
                                        <span class='itm itm3'><span class='dot'>●</span><span class='lb lang" lang='maintenance'></span></span>
                                        <span class='itm itm4'><span class='dot'>●</span><span class='lb lang" lang='lb_abnormal_other'></span></span>
                                    </span>
                                    <span class='lst2'>
                                        <span class='itm lb lang' lang='lb_abnormal_fc' >：</span>
                                        <span class='itm itm1 lang' lang='lb_abnormal_ge' ></span>
                                        <span class='itm itm2 lang' lang='lb_abnormal_se'></span>
                                        <span class='itm itm3 lang' lang='lb_abnormal_as'></span>
                                    </span>
                                </div>

                                <div id='scrollpane2' class='scrollpane'>
                                    <div class='scrollcontent'>
                                        

                                    </div><!-- /scrollcontent -->
                                </div><!-- /scrollpane -->

                            </div>


                        </div>
                    </div>
                </div>
            </div>

        </div><!-- /block -->
        
<!-- end abnormal flights for tab -->
</div>




<!-- ********************************* -->
<!-- 重要信息告警 -->
<!-- ********************************* -->

<div class="fs12" style="width:270px; height:20px; left:2048px; top:695px; font-weight: bold;">
<div class="blue2 lang" lang='lb_abnormal_weather' style="width: 120px;"></div>
<div class="sider" style="height: 19px;">
	<div class="box">
		<ul class="uls">
			<li id="base_list_unnormal_weather"></li>
		</ul>
		<!-- <id="base_list_unnormal_weather" style="top:1px; left:50px; width: 166px; height:20px;"></div>
		<div id="base_list_unnormal_weather2" style="top:1px; left:50px; width: 166px; height:20px;"></div> -->
	</div>
</div>
</div>

<div class="fs12" style="width:250px; height:20px; left:2367px; top:695px; font-weight: bold;">
<div class="blue2 con_flex_row" style="width: 100%;">
	<div class="flex_none lang" lang="lb_warning_ff" style='position: relative; margin-right: 5px;' ></div>
	<div id="flt_list_malfunction" class="white flex_none" style='position: relative;' ></div>
</div>
</div>




</div><!-- /.page-wrapper -->



    <div id='pop_tab1' class='pop hide'>
        <div class='arr'></div>
        <div class='head'>
            <div class='fltno'></div>
            <div class='btnx'></div>
        </div>
        <div class='scrollpane'>
            <div class='cont'>
                <div class='row1 row row_w con_flex_row'>
                    <div class='date flex_1'></div>
                    <div class='ac flex_1'></div>
                    <div class='cabin flex_none' style='width:68px;'></div>
                </div>

                <div class='row con_flex_row cities'>
                    <div class='lbl flex_none'>
                        <span class='lb1'>计划</span>
                        <span class='lb2'>调整</span>
                    </div>
                    <div class='city1 flex_1' style='padding-left:10px;'><span class='nm'></span><span class='tm'></span><span class='tm2'></span></div>
                    <div class='stop flex_1'>
                        <span class='line'>
                            <span class='dot dot1'></span>
                            <span class='dot dot2'></span>
                        </span>
                        <span class='nm hide'></span>
                    </div>
                    <div class='city2 flex_1' style='padding-right:10px;'><span class='nm'></span><span class='tm'></span><span class='tm2'></span></div>
                </div>

                <div class='row3 row row_w'>
                    <span class='label'>不正常属性: </span>
                    <span class='reason bzcsx'></span><br>
                    <span class='label'>事件类型: </span>
                    <span class='reason sjlx'></span>
                </div>

                <div class='blktit'>调整情况</div>
                <div class='blkcon adjust'>
                    <div class='blkrow con_flex_row'>
                        <div class='flex_1'>
                            <span class='fltno bold'></span>
                            <span class='fromto bold'></span>
                            <span class='date bold'></span>
                        </div>
                        <!-- <div class='flex_none' style='text-align: right;'>
                            <span class='tag'></span>
                        </div> -->
                    </div>
                    <div class='blkrow'>
                        <span class='label'>事件描述</span>
                        <span class='reason_tz'></span>
                    </div>
                    <div class='blkrow con_flex_row'>
                        <div class='flex_1'>
                            <span class='label'>发布时间</span>
                            <span class='time_fb'></span>
                        </div>
                        <div class='flex_none'>
                            <span class='label'>发布人</span>
                            <span class='people_fb'></span>
                        </div>
                    </div>
                    <!--
                    <div class='blkrow'>
                        <span class='label'>延误原因</span>
                        <span class='reason_yw'></span>
                    </div>
                    -->
                </div>

                <div class='blktit'>调查结果</div>
                <div class='blkcon'>
                    <div class='blkrow'>
                        <span class='surveyResult'></span>
                    </div>
                </div>

                 <div class='blktit'>整改措施/处理结果</div>
                <div class='blkcon process'>
                    <div class='blkrow'>
                        <span class='result'></span>
                        <span class='line2'></span>
                    </div>
                </div>


                <div class='blktit'>惩处情况</div>
                <div class='blkcon'>
                    <div class='blkrow'>
                        <span class='punishInfo'></span>
                    </div>
                </div>

                <div class='commentlist'>
                    
                </div>
                <div class='blktit'>状态跟踪</div>
                <div class='blkcon'>
                    <div class='blkrow'>
                        <span class='statustext'></span>
                    </div>
                </div>
                <!--
                <div class='blktit'>批示意见</div>
                <div class='blkcon'>
                    <div class='blkrow'>
                        <span class='comments'></span>
                    </div>
                </div>
                -->

                <div style='height:20px;'></div>

            </div>
        </div>

    </div><!-- /#pop_tab1 -->




    <div id='pop_tab2' class='pop hide'>
        <div class='arr'></div>
        <div class='head'>
            <div class='fltno'>HU9999</div>
            <div class='btnx'></div>
        </div>
        <div class='scrollpane'>
            <div class='cont'>
                <div class='row row1 row_w con_flex_row'>
                    <div class='date flex_1'></div>
                    <div class='ac flex_none' style='width:80px;'></div>
                </div>

                <div class='row con_flex_row cities'>
                    <div class='lbl flex_none'>
                        <span class='lb1'>计划</span>
                        <span class='lb2'>实际</span>
                    </div>
                    <div class='city1 flex_1' style='padding-left:10px;'><span class='nm'></span><span class='tm'></span><span class='tm2'></span></div>
                    <div class='stop flex_1'>
                        <span class='line'>
                            <span class='dot dot1'></span>
                            <span class='dot dot2'></span>
                        </span>
                        <span class='nm hide'></span>
                    </div>
                    <div class='city2 flex_1' style='padding-right:10px;'><span class='nm'></span><span class='tm'></span><span class='tm2'></span></div>
                </div>

                <div class='row row_w'>
                    <span class='label'>事件定性: </span>
                    <span class='event_type'></span>
                    <br>
                    <span class='label'>上报时间: </span>
                    <span class='time_sb'></span>
                </div>

                <div class='row'>
                    <span class='label'>不正常情况</span>
                    <div class='red eventType'></div>
                </div>

                <div class='row'>
                    <span class='red title '></span>
                </div>

                <div class='row'>
                    <span class='label'>事件描述</span>
                    <div class='description'></div>
                </div>

                <div class='row'>
                    <span class='label'>处理情况</span>
                    <div class='deal'></div>
                </div>

                <div class='row'>
                    <span class='label'>安全措施</span>
                    <div class='precaution'></div>
                </div>

                <div class='row'>
                    <span class='label'>状态跟踪</span>
                    <div class='status'></div>
                </div>

                <div class='row'>
                    <span class='label'>惩处情况</span>
                    <div class='punishInfo'></div>
                </div>

                <div class='commentlist'>
                    
                </div>
                <!--
                <div class='blktit'>批示意见</div>
                <div class='blkcon'>
                    <div class='blkrow'>
                        <span class='comments'></span>
                    </div>
                </div>
                -->

                <div style='height:20px;'></div>

            </div>
        </div>

    </div><!-- /#pop_tab2 -->

<script src="common.js"></script>
<script src="i18n.js?ver=20211218"></script>
<script type="text/babel" src="index_exhibition.js?ver=20211219"></script>

<style type="text/css">
	.sider{
	height: 20px;
	width: 250px;
	box-sizing: border-box;
	position: relative;
	overflow: hidden;
	left: 30px;
	}
	.box{
	position: absolute;
	left: 0;
	width: 160px;
	height: 20px;
	}
	.uls,.uls2{
	display: inline-block;
	}
	.uls li,.uls2 li{
	display: inline-block;
	width: 800px;
	height: 20px;
	}
	.config_link {
	  display: inline-block;
	  width: 23px;
	  height: 23px;
	  left: 589px;
	  top: 43px;
	  cursor: pointer;
	  pointer-events: auto;
	  background: url(../img/ext_link1.png) no-repeat center center;
	}
</style>
	
<script>
	 $(function () {
		 $('.config_link').on('click',function(){
			 let self = document.domain
			 let vis = 'https://vis.hnair.net/vis-admin//#/kpiConfig/companyKpi'
			 let cdp = 'https://cdp-test.hnair.net/vis-admin//#/kpiConfig/companyKpi'
			 let url = self.includes('vis') ? vis : cdp
			 windowOpen(url , '_blank')
		 })
		 
		 function windowOpen(url, target) {
		   var a = document.createElement("a");
		   a.setAttribute("href", url);
		   if (target == null) {
		     target = '';
		   }
		   a.setAttribute("target", target);
		   document.body.appendChild(a);
		   if (a.click) {
		     a.click();
		   } else {
		     try {
		       var evt = document.createEvent('Event');
		       a.initEvent('click', true, true);
		       a.dispatchEvent(evt);
		     } catch (e) {
		       window.open(url);
		     }
		   }
		   document.body.removeChild(a);
		 }
		 
	 })
	 
	/* $(function(){ */
		/* var i=0;
		var sizess = $(".uls li").length;
		var sizesspx = sizess*330;
		var clone = $(".uls").html();
		$(".uls2").html(clone);
		var t=setInterval(moveL,30);
		
		//封装的动画函数
		function moveL(){
			i++;
			var sizess = $(".uls li").length;
			if(i>sizesspx){
				$(".box").css({left:0});
				i=0
			}
			$(".box").css({left:-i+'px'});
		} */
	/* }) */
</script>



</body>
</html>