showLoading();

var comp_code = 'HU';
var comp_id = '100100';


var BASE_CITY_LIST = {
  "PEK": '北京',
  "HAK": '海口',

  "XIY": '西安',
  "CAN": '广州',
  "DLC": '大连',
  "TYN": '太原',
  "SZX": '深圳',

  "HGH": '杭州',
  "SYX": '三亚',
  "HHA": '长沙',
  "URC": '乌鲁木齐',
  "CKG": '重庆',
};

var BASE_CODE_LIST = {
  "PEK": 'ZBAA',
  "HAK": 'ZJHK',

  "XIY": 'ZLXY',
  "CAN": 'ZGGG',
  "DLC": 'ZYTL',
  "TYN": 'ZBYN',
  "SZX": 'ZGSZ',

  "HGH": 'ZSHC',
  "SYX": 'ZJSY',
  "HHA": 'ZGHA',
  "URC": 'ZWWW',
  "CKG": 'ZUCK',
};

// 区域名称
var AREA_LIST = {
  '华北': '100001',
  '华东': '100002',
  '东北': '100003',
  '西南': '100004',
  '中南': '100005',
  '西北': '100006',
  '新疆': '100007',
};
//////// 机场气象告警 //////////
var unnormalBase = [];
var unnormalBaseIndex = 0;
var arpcodes = [];
var airMap = new Map();

var comp_cause = ['飞机故障', '运力调配', '工程机务', '航班计划', '航材保障', '航务保障', '机组保障', '飞行机组保障', '乘务组', '乘务组保障', '安全员保障', '空警安全员', '地面保障', '货运保障', '公司原因', '其他航空公司原因'];
var none_cause = ['公共安全', '机场', '军事活动', '空管', '离港系统', '联检', '旅客', '时刻安排', '民航局航班时刻安排', '天气原因', '油料', '流量控制'];

var comp_cause_en = {
  '飞机故障': 'A/C Mtc',
  '运力调配': 'Fleet Capacity',
  '工程机务': 'Engineering',
  '航班计划': 'Flt Planning',
  '航材保障': 'A/C MAT Suprt',
  '航务保障': 'Dispatching',
  '机组保障': 'Cabin Crew',
  '飞行机组保障': 'Cockpit Crew',
  '乘务组': 'Cabin Crew',
  '乘务组保障': 'Cabin Crew',
  '安全员保障': 'Security Staff',
  '空警安全员': 'Security Staff',
  '地面保障': 'Grnd Handling',
  '货运保障': 'Cargo Service',
  '公司原因': 'Corporate',
  '其他航空公司原因': 'Others',
};
var none_cause_en = {
  '公共安全': 'Pub Security',
  '机场': 'Aerodrome',
  '军事活动': 'Military',
  '空管': 'ATC Service',
  '离港系统': 'DC System',
  '联检': 'Border Check',
  '旅客': 'Passengers',
  '时刻安排': 'ATC Capacity',
  '民航局航班时刻安排': 'ATC Capacity',
  '天气原因': 'Weather',
  '油料': 'Fuel',
  '流量控制': 'Flt Control',
};
// 不正常天气基地
const wxCode2Name = {
  'TS': '干雷',
  'TSRA': '中雷雨',
  '-TSRA': '弱雷雨',
  '+TSRA': '强雷雨',
  'CB': '对流云',
  'TCU': '浓积云',
  'RA': '中雨',
  '+RA': '大雨',
  '+SHRA': '强阵雨',
  'SHRA': '中阵雨',
  'DZ': '毛毛雨',
  'FZRA': '冻雨',
  'GR': '冰雹',
  'GS': '霰',
  'WS': '风切变',
  'FG': '大雾',
  'FU': '烟',
  'HZ': '霾',
  'BR': '轻雾',
  'FZFG': '冻雾',
  'BCFG': '散雾',
  'MIFG': '浅雾',
  'SN': '中雪',
  '+SN': '大雪',
  'SHSN': '阵雪',
  '+SHSN': '强阵雪',
  'BLSN': '高吹雪',
  'DRSA': '低吹雪',
  'SA': '扬沙',
  'SS': '沙暴',
  'BLSA': '高吹沙',
  'DRSA': '低吹沙',
  '+SS': '强沙暴',
  'DU': '浮尘',
}


// 航班保障节点
var fltnodes = ['cncPilotArrTime', 'checkInEnd', 'cncCabinSupplyEndTime', 'cncCleanEndTime', 'cncMCCReleaseTime', 'planeReady', 'cncInformBoardTime', 'cncBoardOverTime', 'cncClosePaxCabinTime', 'cncCloseCargoCabinTime', 'cncPushTime', 'cncACARSTOFF'];

var marquee_itv;
var all_flight_list;
var timeout_next_flt;

// 缓存航班机组人员，保障节点
var fltCrwCache = {};
var fltLegCache = {};


// 所有飞机架数
var total_plane = -1;
// 执行中飞机架数
var exe_total_plane = -1;
// 机型对照表
var actypeMapList;
var ac_type_list = ['330', '737', '767', '787', '350'];
var ac_num_list = {
  '330': 26,
  '737': 160,
  '767': 1,
  '787': 29
};
//var ac_type_list = ['787', '767', '330', '737', '320', '321', '319', '145', '190'];
var ac_data_list_ready;
var ac_data_list;
var exe_ac_data_list;
var marquee_itv_airPlane;

var lang = getQueryString('lang') || 'cn';

var url_scale = getQueryString('scale');
if (url_scale) {
  url_scale = '&scale=' + url_scale;
} else {
  url_scale = '';
}

$('#btn_english').on('click', function() {
  if (lang == 'en') {
    window.location.href = 'index.html?lang=cn' + url_scale;
  } else {
    window.location.href = 'index.html?lang=en' + url_scale;
  }
});


$('#base_cards .card').on('click', function() {
  var id = $(this).attr('id');
  var code = id.split('arp_code_')[1];
  window.location.href = 'base.html?base=' + code + url_scale;
});

function parseDateToStr(dat) {
  var year = dat.getFullYear();
  var month = dat.getMonth() + 1;
  var date = dat.getDate();
  var hour = dat.getHours();
  var minute = dat.getMinutes();
  var seconds = dat.getSeconds();
  var mill = dat.getMilliseconds();
  var str = year+'-'+('0'+month).substr(-2) +'-'+('0'+date).substr(-2)+' '+('0'+hour).substr(-2)+':'+('0'+minute).substr(-2)+':'+('0'+seconds).substr(-2)+'.'+mill;
  return str;
}



function loadAll() {


  var date = new Date();
  var mm = date.getMonth() + 1;
  var dd = date.getDate();
  if (mm < 10) {
    mm = '0' + mm;
  }
  if (dd < 10) {
    dd = '0' + dd;
  }
  var stdEndUtcTime = date.getFullYear() + '-' + mm + '-' + dd + ' 15:59:59';
  var yesterday_ts = date.getTime() - 86400000;
  date.setTime(yesterday_ts);
  mm = date.getMonth() + 1;
  dd = date.getDate();
  if (mm < 10) {
    mm = '0' + mm;
  }
  if (dd < 10) {
    dd = '0' + dd;
  }
  var stdStartUtcTime = date.getFullYear() + '-' + mm + '-' + dd + ' 16:00:00';


  //-------------------------



  fltCrwCache = {};
  fltLegCache = {};



  // ------------------------------------------------------------------------
  // 航班列表
  // ------------------------------------------------------------------------


  // 开始结束时间
  var date = new Date();
  var mm = date.getMonth() + 1;
  var dd = date.getDate();
  if (mm < 10) {
    mm = '0' + mm;
  }
  if (dd < 10) {
    dd = '0' + dd;
  }
  var stdStart = date.getFullYear() + '-' + mm + '-' + dd + ' 00:00:00';
  var stdEnd = date.getFullYear() + '-' + mm + '-' + dd + ' 23:59:59';


  var statusMap = {
    'ARR': '落地',
    'NDR': '落地',
    'ATD': '推出',
    'ATA': '到达',
    'CNL': '取消',
    'DEL': '延误',
    'DEP': '起飞',
    'RTR': '返航',
    'SCH': '计划'
  };

  var param = {
    "stdStart": stdStart,
    "stdEnd": stdEnd,
    "acOwner": comp_code,
    "statusList": '',
  }
  $.ajax({
    type: 'post',
    url: "/bi/web/getStandardFocFlightInfo",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {
      all_flight_list = response.data;
    },
    error: function() {}
  });



  // ------------------------------------------------------------------------
  // 欢迎词
  // ------------------------------------------------------------------------

  if (lang == 'cn') {

    var param = {
      'mode': 'query'
    }

    $.ajax({
      type: 'post',
      url: "/bi/web/7x2_welcome",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function(response) {

        checkLogin(response);

        if (response.title != undefined) {
          var msg = response.title[0].txt;
          // 
          if (msg.length > 30) {
            $('#welcome_msg').html('<marquee direction="left" scrollamount="10">' + msg + '</marquee>');
          } else {
            $('#welcome_msg').text(msg);
          }
        }

      },
      error: function(jqXHR, txtStatus, errorThrown) {}
    });

  } else {

    var param = {
      'mode': 'query'
    }

    $.ajax({
      type: 'post',
      url: "/bi/web/7x2_welcome_en",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function(response) {

        checkLogin(response);

        if (response.title != undefined) {
          var msg = response.title[0].txt;
          // 
          if (msg.length > 30) {
            $('#welcome_msg').html('<marquee direction="left" scrollamount="10">' + msg + '</marquee>');
          } else {
            $('#welcome_msg').text(msg);
          }
        }

      },
      error: function(jqXHR, txtStatus, errorThrown) {}
    });

  }



  // ------------------------------------------------------------------------
  // 正常率颜色配置
  // ------------------------------------------------------------------------

  var normal_rate_colors = {};
  var param = {
    'mode': 'query'
  }

  const normal_rate_color = $.ajax({
    type: 'post',
    url: "/bi/web/7x2_normal_rate_color",
    contentType: 'application/json',
    dataType: 'json',
    async: false,
    data: JSON.stringify(param),
    success: function(response) {
      normal_rate_colors = response.ratecolor[0];
    },
    error: function() {}
  });



  // ------------------------------------------------------------------------
  // 年度正常率
  // ------------------------------------------------------------------------
  var param = {
    "SOLR_CODE": "FAC_COMP_KPI",
    "COMP_CODE": comp_code,
    "KPI_CODE": "NORMAL_NO_T,SCH_NO",
    "VALUE_TYPE": "kpi_value_d",
    "OPTIMIZE": 1,
    "DATE_TYPE": "M",
    "LIMIT": 12
  }

  $.ajax({

    type: 'post',
    url: "/bi/query/getkpi",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {

      var mdat_nor = response.data[comp_code]['NORMAL_NO_T']['M'];
      var mdat_sch = response.data[comp_code]['SCH_NO']['M'];

      //var m_nor = 0;
      //var m_sch = 0;
      var y_nor = 0;
      var y_sch = 0;
      var date = 0;

      var d = new Date();
      var thisyear = d.getFullYear() + '00'

      for (var time in mdat_nor) {
        var val = Number(mdat_nor[time]);
        var val2 = Number(mdat_sch[time]);
        if (val > 0 && val2 > 0) {
          //if(Number(time) > date){
          //  m_nor = val;
          //  m_sch = val2;
          //  date = Number(time);
          //}
          if (Number(time) > thisyear) {
            y_nor += val;
            y_sch += val2;
          }
        }
      }

      //if(m_nor > 0 && m_sch > 0){
      //  $('#normal_rate_month').text(Math.round((m_nor/m_sch)*1000)/10);
      //}else{
      //  $('#normal_rate_month').text('NA');
      //}

      if (y_nor > 0 && y_sch > 0) {
        $('#normal_rate_year').text(Math.round((y_nor / y_sch) * 1000) / 10);
      } else {
        $('#normal_rate_year').text('NA');
      }

    },
    error: function() {}
  });


  // ------------------------------------------------------------------------
  // 月度正常率
  /*
  HNA_PCR_311中隐藏的月度正常率，重新展现，更改正常率统计逻辑：由运行品质直接获取数据，改为展示本月1日至今的航班累计正常率数据，以运行品质网数据来源为准：
  eg.11月1日，展示10月数据；然后，
  11月2日展示11月1日数据；
  11月3日展示1-2日数据
  11月21日展示1-20日数据
  （原方案：中间表：FAC_COMP_KPI，通过NORMAL_NO_T 正常班次
  SCH_NO和计划班次计算NORMAL_NO_T/SCH_NO*100）
  20180329：
  陈通要求每个月1号使用上个月的月度数据（每个月1日，我们展示的都是1日当日的正常率，但是左上角两个正常率数值从运行品质网和运行网获取的结果不一样）
  */
  // ------------------------------------------------------------------------
  var ddd = new Date();
  var limit_d = ddd.getDate();
  var lastmonth = false;
  if (limit_d == 1) {
    limit_d = 32;
    lastmonth = true; //上个月的月度数据
  }

  var param = {
    "SOLR_CODE": "FAC_COMP_KPI",
    "COMP_CODE": comp_code,
    "KPI_CODE": "NORMAL_NO_T,SCH_NO",
    "VALUE_TYPE": "kpi_value_d",
    "OPTIMIZE":1,
    "DATE_TYPE": "D",
    "LIMIT": limit_d
  }

  $.ajax({

    type: 'post',
    url: "/bi/query/getkpi",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {

      var mdat_nor = response.data[comp_code]['NORMAL_NO_T']['D'];
      var mdat_sch = response.data[comp_code]['SCH_NO']['D'];

      var m_nor = 0;
      var m_sch = 0;

      var date = new Date();
      var month = date.getMonth() + 1;
      var day = date.getDate();
      if (month < 10) {
        month = '0' + month;
      }
      if (day < 10) {
        day = '0' + day;
      }
      var today = date.getFullYear() + '' + month + '' + day;

      var lastmonth_num;
      if (lastmonth) {
        if (month < 12) {
          lastmonth_num = Number(month) - 1;
        } else {
          lastmonth_num = 1;
        }
      }

      for (var time in mdat_nor) {
        var val = Number(mdat_nor[time]);
        var val2 = Number(mdat_sch[time]);
        var mo = time.substr(4, 2);
        if (val > 0 && val2 > 0) {
          if (Number(time) < Number(today) && Number(mo) == Number(month) && !lastmonth || Number(mo) == lastmonth_num && lastmonth) {
            m_nor += val;
            m_sch += val2;
          }
        }
      }

      if (m_nor > 0 && m_sch > 0) {
        $('#normal_rate_month').text(Math.round((m_nor / m_sch) * 1000) / 10);
      } else {
        $('#normal_rate_month').text('NA');
      }

    },
    error: function() {}
  });


  // ------------------------------------------------------------------------
  // 各种航班统计信息。。。。
  // ------------------------------------------------------------------------


  var param = {
    "stdStartUtcTime": stdStartUtcTime,
    "stdEndUtcTime": stdEndUtcTime,
    "companyCodes": comp_code,
    "AcTypeList": "",
    "depstns": "",
    "arrstns": ""
  }

  $.ajax({
    type: 'post',
    url: "/bi/redis/7x2_flt_sts",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {


      var sch_total = Number(response.pftc); //计划航班总数
      var sch_normal = Number(response.pfrtc); //计划航班中正常航班总数
      var pfPercent = Number(response.cfdappPercent); //计划航班中的正常航班比率
      todayNormalRate(pfPercent);

      var dftc = response.dftc; //备降航班总数
      var bftc = response.bftc; //返航航班总数
      $('#flt_return_back').text(Number(dftc) + Number(bftc));

      /*
      qftc 取消航班总数
      qftc0 昨日取消航班总数
      qftc1 今日取消航班总数
      qftc2 次日取消航班总数
      */
      $('#flt_cancel').text(response.qftc1); //取消航班总数

      var pfdtc12 = response.pfdtc12; //计划航班中延误1~2小时的航班总数
      var ffdtc12 = response.ffdtc12; //执行航班中延误1~2小时的航班总数
      var rfdtc12 = response.pfdtc12; //未执行航班中延误1~2小时的航班总数
      var cfdtc12 = response.cfdtc12; //已执行航班中延误1~2小时的航班总数
      $('#flt_delay_12').text(Number(pfdtc12));

      var pfdtc24 = response.pfdtc24; //计划航班中延误2-4小时的航班总数
      var ffdtc24 = response.ffdtc24; //执行航班中延误2-4小时的航班总数
      var rfdtc24 = response.pfdtc24; //未执行航班中延误2-4小时的航班总数
      var cfdtc24 = response.cfdtc24; //已执行航班中延误2-4小时的航班总数
      $('#flt_delay_24').text(Number(pfdtc24));

      var pfdtc4 = response.pfdtc4; //计划航班中延误>4小时的航班总数
      var ffdtc4 = response.ffdtc4; //执行航班中延误>4小时的航班总数
      var rfdtc4 = response.pfdtc4; //未执行航班中延误>4小时的航班总数
      var cfdtc4 = response.cfdtc4; //已执行航班中延误>4小时的航班总数
      $('#flt_delay_4').text(Number(pfdtc4));

      // 国际航班延误数量
      var pfdtci = Number(response.pfdtci); //计划航班中延误国际航班总数
      var ffdtci = Number(response.ffdtci); //执行航班中延误国际航班总数
      // $('#flt_delay_int').text(pfdtci + ffdtci);
      $('#flt_delay_int').text(pfdtci);


      var fftc = Number(response.fftc); //执行航班总数
      var cftc = Number(response.cftc); //已执行航班总数
      var exe_rate = cftc / sch_total
      $('#val_flt_total').text(sch_total);
      $('#val_flt_exec_rate').text(Math.round(exe_rate * 100) + '%');

      setExecRate(exe_rate);


      var pftci = Number(response.pftci); //国际计划航班总数
      var pftcl = Number(response.pftcl); //国内计划航班总数
      var fftci = Number(response.fftci); //国际执行航班总数
      var fftcl = Number(response.fftcl); //国内执行航班总数
      var cftci = Number(response.cftci); //国际已执行航班总数
      var cftcl = Number(response.cftcl); //国内已执行航班总数

      $('#val_flt_total_china').text(pftcl);
      $('#val_flt_exec_rate_china').text(Math.round(cftcl / pftcl * 100) + '%');
      $('#val_flt_total_int').text(pftci);
      $('#val_flt_exec_rate_int').text(Math.round(cftci / pftci * 100) + '%');



    },
    error: function() {}
  });



  // ------------------------------------------------------------------------
  // 航班总量 已经执行占比 
  // ------------------------------------------------------------------------
  function setExecRate(rate) {
    var rate_flt_exec = rate;
    var rate = rate_flt_exec;

    var canvas = document.getElementById('cvs_flt_count');
    var context = canvas.getContext('2d');
    var x = canvas.width / 2;
    var y = canvas.height / 2;

    // draw back
    var radius = 70;
    var startAngle = Math.PI - Math.PI / 5;
    var endAngle = startAngle + Math.PI + Math.PI / 5 * 2;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle, endAngle, counterClockwise);
    context.lineWidth = 20;
    context.strokeStyle = '#00438B';
    context.stroke();

    // draw overlay
    var radius = 69;
    var startAngle2 = startAngle;
    var endAngle2 = startAngle + (endAngle - startAngle) * rate;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
    context.lineWidth = 22;

    // add linear gradient
    if (rate < 0.5) {
      var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
    } else {
      var color = context.createLinearGradient(0, 0, canvas.width * rate, 0);
    }
    color.addColorStop(0, '#3A4ABA');
    color.addColorStop(1, '#00AEFA');
    context.strokeStyle = color;
    context.stroke();
  }



  // ------------------------------------------------------------------------
  // 航班正常率
  // ------------------------------------------------------------------------
  function todayNormalRate(ratestr) {

    var ratestr = Math.round(ratestr * 100) / 100;
    $('#today_normal_rate').text(ratestr);

    var rate = ratestr / 100;

    var canvas = document.getElementById('cvs_normal_rate');
    var context = canvas.getContext('2d');
    var x = canvas.width / 2;
    var y = canvas.height / 2;

    // draw back
    var radius = 84;
    var startAngle = Math.PI - Math.PI / 3.6;
    var endAngle = startAngle + Math.PI + Math.PI / 3.6 * 2;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle, endAngle, counterClockwise);
    context.lineWidth = 40;
    context.strokeStyle = '#00438B';
    context.stroke();

    // draw overlay
    var radius = 84;
    var startAngle2 = startAngle;
    var endAngle2 = startAngle + (endAngle - startAngle) * rate;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
    context.lineWidth = 40;

    // linear gradient
    if (rate < 0.5) {
      var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
    } else if (rate < 0.8) {
      var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
    } else {
      var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, canvas.width / 2);
    }
    color.addColorStop(0, '#122A61');

    if (rate < 0.7) {
      color.addColorStop(1, '#A1263E');
    } else if (rate < 0.8) {
      color.addColorStop(1, '#c29700');
    } else {
      color.addColorStop(1, '#0093d1');
    }

    context.strokeStyle = color;
    context.stroke();

    // draw head
    var radius = 84;
    var startAngle2 = startAngle + (endAngle - startAngle) * rate - (endAngle - startAngle) * 0.01;
    var endAngle2 = startAngle + (endAngle - startAngle) * rate;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
    context.lineWidth = 40;

    if (rate < 0.7) {
      context.strokeStyle = '#ff0000';
    } else if (rate < 0.8) {
      context.strokeStyle = '#ffc600';
    } else {
      context.strokeStyle = '#18c6ff';
    }

    context.stroke();

  }



  // ------------------------------------------------------------------------
  // 计划旅客+已完成
  // ------------------------------------------------------------------------

  var planNum; //旅客订票人数
  var ckiNum; //旅客值机人数

  var param = {
    "companyCodes": comp_code,
    "stdStartUtcTime": stdStartUtcTime,
    "stdEndUtcTime": stdEndUtcTime,
    "detailType": "pftc", //统计航班（总计）
    //"psrType":"all"
  }

  $.ajax({
    type: 'post',
    url: "/bi/web/getPsrSummInfo",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {
      planNum = Number(response.planNum); //旅客订票人数
      setTrvNum();
    },
    error: function() {}
  });


  var param = {
    "companyCodes": comp_code,
    "stdStartUtcTime": stdStartUtcTime,
    "stdEndUtcTime": stdEndUtcTime,
    "detailType": "cftc", //已执行
    //"psrType":"all"
  }

  $.ajax({
    type: 'post',
    url: "/bi/web/getPsrSummInfo",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {
      ckiNum = Number(response.ckiNum); //旅客值机人数
      setTrvNum();
    },
    error: function() {}
  });

  function setTrvNum() {
    if (planNum >= 0 && ckiNum >= 0) {

      $('#val_trv_num_plan').text(Number(planNum));
      $('#val_trv_num_completed').text(Number(ckiNum));

      var rate_trv_completed = Number(ckiNum) / Number(planNum);
      $('.div_trv_num').css('top', (1 - rate_trv_completed) * 100 + '%');

      var canvas = document.getElementById('cvs_trv_num');
      var context = canvas.getContext('2d');
      var x = canvas.width / 2;
      var y = canvas.height / 2;

      // draw back
      var radius = 50;
      context.beginPath();
      context.arc(x, y, radius, 0, 2 * Math.PI, false);
      context.fillStyle = '#041946';
      context.fill();
      context.lineWidth = 4;
      context.strokeStyle = '#075C9C';
      context.stroke();

      // draw lines
      var numslice = 12;
      for (var i = 0; i < numslice; i++) {
        context.beginPath();
        var startAngle = i * (Math.PI * 2 / numslice);
        var endAngle = startAngle + Math.PI * 0.01;
        context.arc(x, y, radius, startAngle, endAngle, false);
        context.lineWidth = 4;
        context.strokeStyle = '#041946';
        context.stroke();
      }

    }
  }



  // ------------------------------------------------------------------------
  // 不正常航班一览
  // AOG飞机
  // ------------------------------------------------------------------------
  /*
  var param = {
    'mode': 'query'
    }

    $.ajax({
        type: 'post',
        url:"/bi/web/7x2_aog",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            checkLogin(response);

            if(response.plane != undefined){
              var len = response.plane.length;
              var html = '';
              for(var i=0; i<len; i++){
                var obj = response.plane[i];
                var timestr = obj.hour;
                var html2 = '';
                var backtime = '';
                if(timestr.indexOf(':') == -1){
                  html2 = '<span class="val_aog ffnum fs10">'+timestr+'</span><span class="blue2 fs10">'+langPack['lb_hour'][lang]+'</span>';
                }else{
                  if(timestr.split(':').length == 2){
                    timestr = timestr+':00';
                  }
                  var time = parserDate(timestr);
                  var now = new Date();
                  var timeleft = time.getTime() - now.getTime();
                  var hourleft = 0;

                  var mm = time.getMonth()+1;
                  var dd = time.getDate();
                  backtime = mm+'-'+dd + ' '+formatNum(time.getHours())+':'+formatNum(time.getMinutes());
                  
                  if(timeleft > 0){
                    hourleft = timeleft/(1000*60*60);
                    if(hourleft > 1){
                      hourleft = Math.round(hourleft);
                      html2 = '<span class="val_aog ffnum fs10">'+hourleft+'</span><span class="blue2 fs10">'+langPack['lb_hour'][lang]+'</span>';
                    }else{
                      hourleft = timeleft/(1000*60);
                      hourleft = Math.round(hourleft);
                      html2 = '<span class="val_aog ffnum fs10">'+hourleft+'</span><span class="blue2 fs10">'+langPack['lb_minute'][lang]+'</span>';
                    }
                  }
                }

                var actype='';
                if(obj.ACTYPE){
                  actype = '<span class="blue2">'+obj.ACTYPE+'</span>';
                }
                
                html += '<div class="reltv" style="height:20px;"><span class="left fs10" style="width:70px; display: inline-block;">'+obj.plane+' '+actype+'</span> <span class="left fs10" style="width:55px; display: inline-block;">'+backtime+'</span> <span class="right" style="display: inline-block;">'+html2+'</span></div>';

              }
              $('#aog_plane_num').text(len);
              $('#aog_plane_list1').html(html);

              //大于5条自动滚动
              if(len > 5){
                var speed=60;
                var sec=document.getElementById("aog_plane_list"); 
                var sec2=document.getElementById("aog_plane_list2"); 
                var sec1=document.getElementById("aog_plane_list1"); 
                sec2.innerHTML=sec1.innerHTML;
                function Marquee(){
                    if(sec2.offsetTop-sec.scrollTop<=0)
                      sec.scrollTop-=sec1.offsetHeight
                    else{ 
                      sec.scrollTop++ 
                    } 
                }
                clearInterval(marquee_itv);
                marquee_itv=setInterval(Marquee,speed);
                sec.onmouseover=function() {clearInterval(marquee_itv)}
                sec.onmouseout=function() {marquee_itv=setInterval(Marquee,speed)} 
              }
          

            }
            
        },
        error:function(jqXHR, txtStatus, errorThrown) {

        }
    });
*/



  // ------------------------------------------------------------------------
  // 延误原因 公司／非公司
  // ------------------------------------------------------------------------
  var date_type = "D";
  var param = {
    'SOLR_CODE': 'FAC_COMP_DELAY_CAUSE_RATE_KPI', //FAC_COMP_ARP_NORMAL_RATE_KPI
    'COMP_CODE': comp_code,
    'KPI_CODE': 'DELAY_NO',
    'VALUE_TYPE': 'kpi_value_d',
    'DATE_TYPE': date_type,
    'LIMIT': '1',
    'OPTIMIZE': 1
  }

  $.ajax({
    type: 'post',
    url: "/bi/query/getkpi",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {

      checkLogin(response);

      if (response.data != undefined) {
        var data_dly = response.data[comp_code]['DELAY_NO'][date_type];
        /*
        
        公司原因
        
        军事活动
        
        地面保障
        
        天气原因
        
        旅客
        
        未定义
        
        机场
        
        机组保障
        
        正常航班
        
        民航局航班时刻安排
        
        油料
        
        空管
        
        航班计划
        
        运力调配
        
        飞机故障
        
        */



        var comp_cause_list = [];
        var none_cause_list = [];

        var comp_total = 0;
        var none_total = 0;



        // 公司原因 总数
        for (var time in data_dly) {
          var d = data_dly[time];
          var len = comp_cause.length;
          for (var i = 0; i < len; i++) {
            var causeName = comp_cause[i];
            if (!isNaN(d[causeName])) {
              var val = Number(d[causeName]);
              comp_total += val;
              if (val > 0) {
                if (lang == 'en') {
                  causeName = comp_cause_en[causeName];
                }
                comp_cause_list.push({
                  "name": causeName,
                  "val": val
                });
              }

            }
          }
          break;
        }

        // 非公司原因 总数
        for (var time in data_dly) {
          var d = data_dly[time];
          var len = none_cause.length;
          for (var i = 0; i < len; i++) {
            var causeName = none_cause[i];
            if (!isNaN(d[causeName])) {
              var val = Number(d[causeName]);
              if (causeName == "民航局航班时刻安排") {
                causeName = "时刻安排"
              }
              if (lang == 'en') {
                causeName = none_cause_en[causeName];
              }
              none_total += val;
              if (val > 0) {
                none_cause_list.push({
                  "name": causeName,
                  "val": val
                });
              }
            }
          }
          break;
        }

        // 公司
        var html = '';
        var len = comp_cause_list.length;
        for (var i = 0; i < len; i++) {
          var d = comp_cause_list[i];
          var per = Number(d.val) / (comp_total + none_total);
          var perstr = Math.round(per * 100);
          var barlen = 100 * per;
          if (perstr > 0) {
            html += '<div class="baritmrow"><span class="blue2">' + d.name + '</span> <span class="bar greenbar" style="width: ' + barlen + 'px; "></span> <span class="ffnum">' + perstr + '%</span> </div>';
          }

        }

        $('#holder_delay_cause_comp').html(html);


        // 非公司
        html = '';
        var len = none_cause_list.length;
        for (var i = 0; i < len; i++) {
          var d = none_cause_list[i];
          var per = Number(d.val) / (comp_total + none_total);
          var perstr = Math.round(per * 100);
          var barlen = 100 * per;
          if (perstr > 0) {
            html += '<div class="baritmrow"><span class="blue2">' + d.name + '</span> <span class="bar bluebar" style="width: ' + barlen + 'px; "></span> <span class="ffnum">' + perstr + '%</span> </div>';
          }
        }

        $('#holder_delay_cause_none').html(html);


        // percent
        $('#per_delay_cause_comp').text(Math.round(comp_total / (comp_total + none_total) * 100));
        $('#per_delay_cause_none').text(Math.round(none_total / (comp_total + none_total) * 100));


        // chart

        var rate_delay_cause_comp = comp_total / (comp_total + none_total);
        var rate = rate_delay_cause_comp;

        var canvas = document.getElementById('cvs_delay_cause');
        var context = canvas.getContext('2d');
        var x = canvas.width / 2;
        var y = canvas.height / 2;

        // draw blue circle
        var radius = 54;
        context.beginPath();
        context.arc(x, y, radius, 0, 2 * Math.PI, false);
        context.lineWidth = 7;
        context.strokeStyle = '#02B0F9';
        context.stroke();

        // draw green arc
        var startAngle = Math.PI - (Math.PI * 2 * rate / 2);
        var endAngle = Math.PI + (Math.PI * 2 * rate / 2);
        context.beginPath();
        context.arc(x, y, radius, startAngle, endAngle, false);
        context.lineWidth = 7;
        context.strokeStyle = '#A3D900';
        context.stroke();

        // draw lines
        var numslice = 12;
        for (var i = 0; i < numslice; i++) {
          context.beginPath();
          var startAngle = i * (Math.PI * 2 / numslice);
          var endAngle = startAngle + Math.PI * 0.01;
          context.arc(x, y, radius, startAngle, endAngle, false);
          context.lineWidth = 8;
          context.strokeStyle = '#041946';
          context.stroke();
        }



      }

    },
    error: function() {}
  });


  // ------------------------------------------------------------------------
  // 运力分布 - 可用运力
  // ------------------------------------------------------------------------
  /*
  日运力相关逻辑和处理要点如下 
   
  一：日运力总数调用接口
   
          1.1 请求处理
          String acTypeFilter = "D00,D10";//需要过滤掉的机型
          ApiRequest req = new ApiRequest();
          req.setOption("acOwners", StringUtils.join(airCodeSet));
          req.setOption("acTypeNotIn", acTypeFilter);
          PageParam pageParam = new PageParam();
          pageParam.setPageIndex(1);
          pageParam.setPageSize(MyUtil.pageLimit);//2000
          req.setPageParam(pageParam);
          ApiResponse res = acTypeApi.getPdcAcReg(req);
   
     1.2 结果集处理
   
        金鹏结果集要过滤掉货舱F0的数据
         if ("Y8".equals(airCode) && "F0".equals(cabin)) {
                  continue;
              }
        获取所有的短机号：acreg
   
     有多少个短机号，就有多少架总运力；通过“acOwner”来进行航司分组，可以获取不同航司的总运力。
   
   
  二：日可用运力
     2.1 请求处理
       shortNos不依据不同的航司的机号列表作为参数（这个参数来自计算总运力的接口返回进行加工的）
   
          ApiRequest req = new ApiRequest();
          req.setOption("mntUtcStart", startUtcTime);//北京时间某日 00:00:00转换类UTC时间
          req.setOption("mntUtcEnd", endUtcTime); //北京时间某日 23:59:59转换类UTC时间
          req.setOption("acregs", shortNos);//短机号列表List<String>
          boolean isToday = this.isToday(fltDate);
          if (isToday) {
              String nowStr = DateUtil.getDefaultNowStr();
              req.setOption("nowInMntUtcRange", MyUtil.convertBJ2OhterTime(nowStr, "0000")); // 日区分为当日和非当日，当日要传这个参数，当前时间转为UTC时间
          }
          PageParam param = new PageParam();
          param.setPageIndex(1);
          param.setPageSize(MyUtil.pageLimit);//2000
          req.setPageParam(param);
          ApiResponse res = focMaintApi.getFocMaintInfoByListByPage(req);
   
  2.2    停场计算规则
   
  2.2.1当日：按实时情况查询的逻辑（实时查询当日情况：以实时为准，查询的时间点若有飞机处于计划或非计划停场，则统计计划或非计划停场架次为1；否则，为0；举例：某飞机0700-1200处于非计划停场，1000查询该飞机则统计为非计划停场架次1，1300查询则统计为非计划停场架次0。）
  2.2. 2 非当日的天：当蓝色逻辑（每日0600-2400期间，计划或非计划停场时间小于9小时，计划或非计划停场架次统计为0；9（含）-12小时统计为0.5；大于等于12小时，统计为1。可用运力=总运力-停场。）
  移动取的还是北就时间00：00：00至23：59：59，然后转为utc时间
  2.2.3  接口输出：
  2.2.3.1运力要去重：一架飞机有多个停场计划,取最晚依据结束时间(tEnd)
   
  2.2.3.2 停场类型判断：String type = (String) dataMap.get("distinctcol");//1计划 2非计划
   
  2.3 可用运力=总运力-停场运力

  */

  // ------------------------------------------------------------------------
  var tingchang_ac2num; // 大机型：数量 737:12
  var lost_ac_map = [];
  var available_ac; // 可用运力

  // UTC NOW
  var date = new Date();
  var mm = date.getUTCMonth() + 1;
  var dd = date.getUTCDate();
  var h = date.getUTCHours();
  var m = date.getUTCMinutes();
  var s = date.getUTCSeconds();
  if (mm < 10) {
    mm = '0' + mm;
  }
  if (dd < 10) {
    dd = '0' + dd;
  }
  if (h < 10) {
    h = '0' + h;
  }
  if (m < 10) {
    m = '0' + m;
  }
  if (s < 10) {
    s = '0' + s;
  }
  var utcTimeNow = date.getFullYear() + '-' + mm + '-' + dd + ' ' + h + ':' + m + ':' + s;


  // 总运力
  var param = {
    "acOwners": comp_code,
    "acTypeNotIn": 'D00,D10', //需要过滤掉的机型
  }

  $.ajax({
    type: 'post',
    url: "/bi/web/getPdcAcReg",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {
      var list = response.data;
      var acregs = [];
      var acTypes = [];
      var reg2actyp = {};
      for (var i = list.length - 1; i >= 0; i--) {
        var obj = list[i];
        if (obj.acOwner == 'Y8' && obj.config == 'F0') { //金鹏结果集要过滤掉货舱F0的数据
          continue;
        }
        acregs.push(obj.acreg);
        if (acTypes.indexOf(obj.acType) == -1) {
          acTypes.push(obj.acType);
        }
        reg2actyp[obj.acreg] = obj.acType;
      }

      // 获取机型转换表
      var param = {
        "actypecode": acTypes.join(','),
        "company": comp_code
      }

      $.ajax({
        type: 'post',
        // url:"/bi/web/actypquery",
        url: "/bi/web/actypmappingquery",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
          actypeMapList = response;

          ac_data_list = {};
          for (ac in actypeMapList) {
            var accode = actypeMapList[ac];
            //if(ac_type_list.indexOf(accode) > -1){
            if (ac_data_list[accode] == undefined) {
              ac_data_list[accode] = 0;
            }
            //}
            if (ac_type_list.indexOf(accode) == -1) {
              ac_type_list.push(accode);
            }
          }

          for (var reg in reg2actyp) {
            var ac = reg2actyp[reg];
            var accode = actypeMapList[ac];
            if (accode) {
              ac_data_list[accode]++;
            } else {
              if (lost_ac_map.indexOf(ac) == -1) {
                lost_ac_map.push(ac)
              }
            }
          }
          console.log(lost_ac_map.toString(), '没有大小机型对照数据');

          // 停场运力
          var param = {
            "mntUtcStart": stdStartUtcTime,
            "mntUtcEnd": stdEndUtcTime,
            "nowInMntUtcRange": utcTimeNow,
            "acregs": acregs.join(','),
          }

          $.ajax({
            type: 'post',
            url: "/bi/web/getFocMaintInfoByListByPage",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function(response) {
              var list2 = response.data;
              var acregs2 = [];
              var aog_planes = [];
              tingchang_ac2num = {}
              for (var i = list2.length - 1; i >= 0; i--) {
                var obj = list2[i];

                var time1 = parserDate(obj.tStart).getTime() + 8 * 3600 * 1000;
                var time2 = parserDate(obj.tEnd).getTime() + 8 * 3600 * 1000;
                var date = new Date();
                var timenow = date.getTime();

                if (acregs2.indexOf(obj.acreg) == -1 && timenow > time1 && timenow < time2 && (obj.distinctcol == 1 || obj.distinctcol == 2)) {
                  acregs2.push(obj.acreg);
                  // 对应大机型数量减去维护中的数量
                  var actyp = reg2actyp[obj.acreg];
                  var accode = actypeMapList[actyp];
                  ac_data_list[accode]--;

                  // 大机型对应的停车数量
                  var bigactyp = obj.acType
                  bigactyp = bigactyp.split('-')[0]
                  bigactyp = bigactyp.split('(')[0]
                  bigactyp = bigactyp.replace('A', '');
                  bigactyp = bigactyp.replace('B', '');
                  bigactyp = bigactyp.replace('C', ''); // C919...
                  bigactyp = bigactyp.replace('ERJ', '');
                  if (tingchang_ac2num[bigactyp] == undefined) {
                    tingchang_ac2num[bigactyp] = 0
                  }
                  tingchang_ac2num[bigactyp]++
                }

                //
                //distinctcol=2 为非计划停场
                //今日非计划停场飞机
                //飞机号
                //机型
                //停场结束时间tEnd=预计恢复时间
                //剩余=预计恢复时间-目前时间
                if (obj.distinctcol == 2 && timenow > time1 && timenow < time2) {
                  aog_planes.push(obj);
                }
              }

              ac_data_list_ready = true;
              // acregs2
              // 可用运力=总运力-停场运力
              available_ac = acregs.length - acregs2.length;
              $('#total_plane_num').text(acregs.length); // 2018.7.10 du.xx
              // $('#total_plane_num').text(acregs.length); // 2018.7.10 du.xx

              function setOnGround() {
                if (exe_total_plane == -1) {
                  setTimeout(setOnGround, 0);
                  return;
                }
                //$('#plane_on_ground').text(available_ac-exe_total_plane); // 2018.7.10 du.xx
                //$('#plane_on_ground').text(acregs.length-exe_total_plane);
                $('#plane_on_stop').text(acregs2.length); //停场

                var per = acregs2.length / acregs.length * 100; // 2018.7.10 du.xx
                //var per = (acregs.length-exe_total_plane)/acregs.length * 100; // 2018.7.10 du.xx
                // $('#bar_plane_on_ground').css('width', per+'%');
              }

              setOnGround();


              // AOG 飞机列表
              var len = aog_planes.length;
              var html = '';
              for (var i = 0; i < len; i++) {
                var obj = aog_planes[i];
                var timestr = obj.tEnd;
                if(obj.tEnd != undefined && obj.tEnd.length > 0){
                  timestr = parseDateToStr(new Date(parserDate(obj.tEnd).getTime() + 8 * 3600 * 1000));
                }
                
                // var timestr = obj.tEnd; //2018-04-02 08:00:00.0
                var html2 = '';
                var backtime = '';

                var arry = timestr.split('.');
                timestr = arry[0];
                var time = parserDate(timestr);
                var now = new Date();
                var timeleft = time.getTime() - now.getTime();
                var hourleft = 0;

                var mm = time.getMonth() + 1;
                var dd = time.getDate();
                backtime = mm + '-' + dd + ' ' + formatNum(time.getHours()) + ':' + formatNum(time.getMinutes());

                if (timeleft > 0) {
                  hourleft = timeleft / (1000 * 60 * 60);
                  if (hourleft > 1) {
                    hourleft = Math.round(hourleft);
                    html2 = '<span class="val_aog ffnum" style="font-size:9px;">' + hourleft + '</span><span class="blue2" style="font-size:9px;">' + langPack['lb_hour']['en'] + '</span>';
                  } else {
                    hourleft = timeleft / (1000 * 60);
                    hourleft = Math.round(hourleft);
                    html2 = '<span class="val_aog ffnum" style="font-size:9px;">' + hourleft + '</span><span class="blue2" style="font-size:9px;">' + langPack['lb_minute'][lang] + '</span>';
                  }
                }

                var actype = '';
                if (obj.acType) {
                  actype = '<span class="blue2">' + obj.acType.split('-')[0] + '</span>';
                }

                html += '<div class="reltv" style="height:20px;"><span class="left" style="width:70px; display: inline-block; font-size:9px;">' + obj.longNo + ' ' + actype + '</span> <span class="left" style="width:68px; display: inline-block; font-size:9px;">' + backtime + '</span> <span class="right" style="display: inline-block;">' + html2 + '</span></div>';

              }
              $('#aog_plane_num').text(len);
              $('#aog_plane_list1').html(html);

              //大于5条自动滚动
              if (len > 5) {
                var speed = 60;
                var sec = document.getElementById("aog_plane_list");
                var sec2 = document.getElementById("aog_plane_list2");
                var sec1 = document.getElementById("aog_plane_list1");
                sec2.innerHTML = sec1.innerHTML;

                function Marquee() {
                  if (sec2.offsetTop - sec.scrollTop <= 0)
                    sec.scrollTop -= sec1.offsetHeight
                  else {
                    sec.scrollTop++
                  }
                }
                clearInterval(marquee_itv);
                marquee_itv = setInterval(Marquee, speed);
                sec.onmouseover = function() {
                  clearInterval(marquee_itv)
                }
                sec.onmouseout = function() {
                  marquee_itv = setInterval(Marquee, speed)
                }
              }

              // aog end

            },
            error: function() {}
          });
          // end

        },
        error: function() {}
      });



      //end
    },
    error: function() {}
  });



  // ------------------------------------------------------------------------
  // 运力分布 - 空中地面
  // ------------------------------------------------------------------------
  var acTypeList = {};
  var actypeId2Code;
  var actypeCodeList = [];

  var param = {
    'SOLR_CODE': 'FAC_COMP_ACTYPE_KPI',
    'COMP_CODE': comp_code,
    'KPI_CODE': 'AC_NUM,EXE_AC_NUM', // 飞机架次, 执行中飞机架数
    'VALUE_TYPE': 'kpi_value_d', //本期
    'DATE_TYPE': 'D',
    "OPTIMIZE":1,
    'ACTYPE': 'ALL',
    'LIMIT': 1
  }

  $.ajax({
    type: 'post',
    url: "/bi/query/getackpi",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {

      if (response.data != undefined) {
        var data = response.data;

        // 设置机型数量
        function setAcNum() {
          if (actypeId2Code == undefined || tingchang_ac2num == undefined) {
            setTimeout(setAcNum, 0);
            return;
          }

          total_plane = 0;
          exe_total_plane = 0;
          //ac_data_list = {};
          exe_ac_data_list = {};
          var numofac = 0;

          var compcode = comp_code;
          if (data[compcode] != undefined) {
            var kpiaclst1 = data[compcode]['AC_NUM']['D']['data3'];
            var kpiaclst2 = data[compcode]['EXE_AC_NUM']['D']['data3'];

            // 总飞机架数
            var len = kpiaclst1.length;
            for (var i = 0; i < len; i++) {
              var acdat = kpiaclst1[i];
              var acid = acdat.actype;
              var accode = actypeId2Code[acid];
              if (accode && accode != 'QITA') {
                var acdd = acdat.date;
                var len2 = acdd.length;
                // 每种机型的架数
                var acno = 0;
                for (var j = 0; j < len2; j++) {
                  var dd = acdd[j];
                  var val = isNaN(dd.value) ? 0 : Number(dd.value);
                  total_plane += val;
                  acno += val;
                }

                // 每种机型的架数
                //if(ac_type_list.indexOf(accode) > -1){
                acno = (isNaN(acno) || acno == 0) ? '-' : Math.round(acno);
                //if(ac_data_list[accode] == undefined){
                //    ac_data_list[accode] = 0;
                //}
                //ac_data_list[accode] += acno;

                numofac++;
                //}
              }
            }


            // 执行中飞机架数
            var len = kpiaclst2.length;
            for (var i = 0; i < len; i++) {
              var acdat = kpiaclst2[i];
              var acid = acdat.actype;
              var accode = actypeId2Code[acid];
              if (accode && accode != 'QITA') {
                var acdd = acdat.date;
                var len2 = acdd.length;
                // 每种机型的架数
                var acno = 0;
                for (var j = 0; j < len2; j++) {
                  var dd = acdd[j];
                  var val = isNaN(dd.value) ? 0 : Number(dd.value);
                  if (ac_type_list.indexOf(accode) > -1) {
                    exe_total_plane += val;
                  }
                  acno += val;
                }

                // 执行中 每种机型的架数
                if (ac_type_list.indexOf(accode) > -1) {
                  acno = (isNaN(acno) || acno == 0) ? '-' : Math.round(acno);
                  if (exe_ac_data_list[accode] == undefined) {
                    exe_ac_data_list[accode] = 0;
                  }
                  exe_ac_data_list[accode] += acno;

                }
              }
            }


          }



          //
          function setAcAirGround() {
            if (!ac_data_list_ready) {
              setTimeout(setAcAirGround, 0);
              return;
            }
            var html = '';
            var barwidth = 40;
            for (var accode in ac_data_list) {
              var numac = ac_data_list[accode];
              if (numac > 0) {
                var numexeac = exe_ac_data_list[accode];
                numexeac = isNaN(numexeac) ? 0 : numexeac;

                var ac_ground = numac - numexeac;
                ac_ground = Math.max(ac_ground, 0);

                var ac_tingchang = tingchang_ac2num[accode]
                if (isNaN(ac_tingchang)) {
                  ac_tingchang = 0;
                }

                var ac_keyong = ac_data_list[accode];
                var ac_dimian = 0
                if (isNaN(ac_keyong)) {
                  ac_keyong = 0;
                } else {
                  ac_dimian = ac_keyong - numexeac; // 可用运力-空中运力
                }


                numac = Math.max(numac, ac_dimian, numexeac)

                html += '<div class="baritmrow plane_ac_' + accode + '">';
                html += '<span class="acno">' + accode + '</span>';

                html += '<span class="val val_l">' + numexeac + '</span>';
                html += '<span class="bar darkbar">';
                html += '<span class="bar_ac_air innerbar bluebar" style="width: ' + (numexeac / numac * barwidth) + 'px;"></span>';
                html += '</span>';

                html += '<span class="val val_l" style="width:30px;">' + ac_dimian + '</span> ';
                html += '<span class="bar darkbar">';
                html += '<span class="bar_ac_air innerbar greenbar" style="width: ' + (ac_dimian / numac * barwidth) + 'px;"></span>';
                html += '</span>';

                html += '<span class="val val_l" style="width:30px;">' + ac_tingchang + '</span> ';
                html += '<span class="bar darkbar">';
                html += '<span class="bar_ac_air innerbar brownbar" style="width: ' + (ac_tingchang / numac * barwidth) + 'px;"></span>';
                html += '</span>';

                html += '</div>';
              }
            }

            $('#air_aclist1').html(html);
            // 详细机型列表滚动
            clearInterval(marquee_itv_airPlane);
            $('#air_aclist2').html('');
            if (numofac > 4) {
              var speed = 80;
              var base_sec = document.getElementById("air_aclist");
              var base_sec2 = document.getElementById("air_aclist2");
              var base_sec1 = document.getElementById("air_aclist1");
              base_sec2.innerHTML = base_sec1.innerHTML;

              function base_Marquee() {
                if (base_sec2.offsetTop - base_sec.scrollTop <= 0)
                  base_sec.scrollTop -= base_sec1.offsetHeight;
                else {
                  base_sec.scrollTop += Math.ceil(1 / pageZoomScale);
                }
              }

              marquee_itv_airPlane = setInterval(base_Marquee, speed);
              base_sec.onmouseover = function() {
                clearInterval(marquee_itv_airPlane)
              }
              base_sec.onmouseout = function() {
                marquee_itv_airPlane = setInterval(base_Marquee, speed)
              }
            }

          }

          setAcAirGround();


          total_plane = (isNaN(total_plane) || total_plane == 0) ? '-' : Math.round(total_plane);



          //$('#total_plane_num').text(total_plane);
          $('#plane_over_air').text(exe_total_plane);
          $('#plane_on_ground').text(available_ac - exe_total_plane);
          //$('#plane_on_ground').text(total_plane-exe_total_plane);

          //var per = (total_plane-exe_total_plane)/total_plane * 100;
          //$('#bar_plane_on_ground').css('width', per+'%');



          // 详细机型列表滚动
          /*
          clearInterval(marquee_itv_airPlane);
          $('#air_aclist2').html('');
          if(numofac > 4){
              var speed=80;
              var base_sec=document.getElementById("air_aclist"); 
              var base_sec2=document.getElementById("air_aclist2"); 
              var base_sec1=document.getElementById("air_aclist1"); 
              base_sec2.innerHTML=base_sec1.innerHTML;
              function base_Marquee(){
                  if(base_sec2.offsetTop-base_sec.scrollTop<=0)
                    base_sec.scrollTop-=base_sec1.offsetHeight;
                  else{ 
                    base_sec.scrollTop += Math.ceil(1/pageZoomScale);
                  } 
              }
              
              marquee_itv_airPlane=setInterval(base_Marquee,speed);
              base_sec.onmouseover=function() {clearInterval(marquee_itv_airPlane)}
              base_sec.onmouseout=function() {marquee_itv_airPlane=setInterval(base_Marquee,speed)} 
          }
          */



        }

        setAcNum();


      }

    },
    error: function() {}
  });


  // 获取所有机型
  var param = {}
  $.ajax({
    type: 'post',
    url: "/bi/web/actypeall",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {

      if (response.actype) {
        var list = response.actype;
        actypeId2Code = {};

        list.sort(function(a, b) {
          return a.sort - b.sort
        });

        var len = list.length;
        for (var i = 0; i < len; i++) {
          var obj = list[i];
          actypeId2Code[obj.id] = obj.code;
        }
      }

    },
    error: function() {}
  });



  // ------------------------------------------------------------------------
  // 各个基地过夜飞机架数
  // ------------------------------------------------------------------------
  /*
  var param = {
      'company': comp_code
    }

    $.ajax({
        type: 'post',
        url:"/bi/web/airport",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
          var all_arp_list = [];
            for(var i=response.arp.length-1; i>=0; i--){
              var o = response.arp[i];
              if(all_arp_list.indexOf(o.code) == -1){
                all_arp_list.push(o.code);
              }
            }

            //all_arp_list=["PEK", "HAK", "XIY", "CAN", "DLC", "TYN", "SZX", "HGH", "SYX", "HHA", "URC"];
      getArpOvernightPlane(all_arp_list)
            
            
        },
        error:function() {
        }
    });
  */



  function getArpOvernightPlane() {

    var arplist = [];
    for (var code in BASE_CITY_LIST) {
      if (arplist.indexOf(code) == -1) {
        arplist.push(code);
      }
    }

    var all_arp_kpi_value = {};
    var arp_has_plane_list = [];

    var date = new Date();
    var mm = date.getMonth()+1;
    var dd = date.getDate();
    var yy = date.getFullYear();
    if(mm < 10){
      mm = '0' + mm;
    }
    if(dd < 10){
      dd = '0' + dd;
    }
    var today = ''+ yy + mm + dd;

    var kpis = ['AC_NO', 'AC_ARR_NO'];
    var param = {
      "SOLR_CODE": "FAC_COMP_ARP_FLIGHT_KPI",
      "COMP_CODE": comp_code,
      "AIRPORT_CODE": arplist.join(','),
      "KPI_CODE": kpis.join(','),
      "VALUE_TYPE": "kpi_value_d",
      "DATE_TYPE": "D",
      "LIMIT": 0,
      "DATE_ID":today,
      "OPTIMIZE": 1
    }

    $.ajax({

      type: 'post',
      url: "/bi/query/getackpi",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function(response) {

        var data = response.data[comp_code];
        for (var kpicode in data) {
          var arpdatlist = data[kpicode]["D"];
          for (var arpcode in arpdatlist) {
            var val = arpdatlist[arpcode];
            if (all_arp_kpi_value[arpcode] == undefined) {
              all_arp_kpi_value[arpcode] = {};
            }
            all_arp_kpi_value[arpcode][kpicode] = Number(val);
          }
        }

        // 计算总量
        var AC_ARR_NO_TOTAL = 0; // 所有机场过夜飞机架数
        var AC_NO_TOTAL = 0; // 所有机场预计过夜飞机架数
        var basenum = 0;


        for (var i = arplist.length - 1; i >= 0; i--) {
          var code = arplist[i];

          var AC_NO = 0; // 预计飞机架数
          var AC_ARR_NO = 0; // 过夜飞机架数

          if (all_arp_kpi_value[code]) {
            AC_NO = Number(all_arp_kpi_value[code]['AC_NO']);
            AC_ARR_NO = Number(all_arp_kpi_value[code]['AC_ARR_NO']);

            if (AC_NO > 0 || AC_ARR_NO > 0 || BASE_CITY_LIST[code] != undefined) {
              basenum++;
              AC_ARR_NO_TOTAL += AC_ARR_NO;
              AC_NO_TOTAL += AC_NO;
              arp_has_plane_list.push(code);
            }
          }

        }

        $('#base_over_night_plane_num').text(AC_ARR_NO_TOTAL);
        $('#base_over_night_plane_num2').text(AC_NO_TOTAL);
        $('#bar_base_over_night').css('width', (AC_ARR_NO_TOTAL / AC_NO_TOTAL * 100) + '%');


        setInterval(setArpPlane, 5000);


        hideLoading();


      },
      error: function() {}
    });



    var currentArpPlanePage = 0;
    var arpPlanePageSize = 4;

    function setArpPlane() {

      var html = '';

      var s1 = currentArpPlanePage * arpPlanePageSize;
      var s2 = Math.min((currentArpPlanePage + 1) * arpPlanePageSize, arp_has_plane_list.length);

      for (var i = s1; i < s2; i++) {
        var code = arp_has_plane_list[i];

        var AC_NO = 0; // 预计飞机架数
        var AC_ARR_NO = 0; // 过夜飞机架数

        AC_NO = Number(all_arp_kpi_value[code]['AC_NO']);
        AC_ARR_NO = Number(all_arp_kpi_value[code]['AC_ARR_NO']);

        //html+='<div class="over_night_arp"><span class="code blue2 fs11">'+code+'</span><span class="bar" style="width:'+(AC_ARR_NO/AC_NO*25)+'px; "></span><span class="num white fs11">'+AC_NO+'</span></div>';

        html += '<div id="over_night_' + code + '" class="baritmrow"><span class="blue2 right" style="width:50px; display: inline-block;">' + code + '</span> <span class="ffnum right" style="width:16px; display: inline-block;">' + AC_NO + '</span><span class="bar darkbar" style="width: 80px; "><span class="innerbar bluebar" style="width:100%; "></span><span class="innerbar greenbar" style="width: ' + (AC_ARR_NO / AC_NO * 100) + '%; right:0px;"></span></span><span class="ffnum" style="width:16px; display: inline-block;">' + AC_ARR_NO + '</span>';

      }

      $('#base_over_night_plane1').html(html);

      if (currentArpPlanePage < Math.ceil(arp_has_plane_list.length / arpPlanePageSize) - 1) {
        currentArpPlanePage++;
      } else {
        currentArpPlanePage = 0;
      }



      //大于4条自动滚动
      /*
    if(basenum > 4){

      var speed=60;
      var base_sec=document.getElementById("base_over_night_plane"); 
      var base_sec2=document.getElementById("base_over_night_plane2"); 
      var base_sec1=document.getElementById("base_over_night_plane1"); 
      base_sec2.innerHTML=base_sec1.innerHTML;
      function base_Marquee(){
          if(base_sec2.offsetTop-base_sec.scrollTop<=0)
            base_sec.scrollTop-=base_sec1.offsetHeight
          else{ 
            base_sec.scrollTop++ 
          } 
      } 
      var marquee_itv_base=setInterval(base_Marquee,speed);
      base_sec.onmouseover=function() {clearInterval(marquee_itv_base)}
      base_sec.onmouseout=function() {marquee_itv_base=setInterval(base_Marquee,speed)} 
    }
    */
    }

  }
  getArpOvernightPlane();


  // ------------------------------------------------------------------------
  // 各个基地正常率
  // ------------------------------------------------------------------------

  var area_arps = {};

  // 获取个区域机场
  var param = {
    "company": comp_id,
  }

  $.ajax({
    type: 'post',
    url: "/bi/web/7x2_area_arp",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {
      var arplist = [];
      var arps = response.data;
      for (var i = arps.length - 1; i >= 0; i--) {
        var o = arps[i];
        var code = o.AIRPORT_CODE;
        var area = o.AREA_NAME;
        if (AREA_LIST[area] != undefined) {
          if (area_arps[area] == undefined) {
            area_arps[area] = [];
          }
          if (area_arps[area].indexOf(code) == -1) area_arps[area].push(code);
        }
        arplist.push(code);

      }

      for (var area_name in area_arps) {
        var arp_list = area_arps[area_name];
        getAreaArpsSts(area_name, arp_list)
      }

      //var arplist = [];
      //for(var code in BASE_CITY_LIST){
      //  if(arplist.indexOf(code) == -1){
      //    arplist.push(code);
      //  }
      //}
      //getArpOvernightPlane(arplist);

    },
    error: function() {}
  });

  // 区域正常率
  function getAreaArpsSts(area_name, arps) {
    var loaded = 0;
    var sch_total = 0;
    var sch_normal = 0;


    var param = {
      "stdStartUtcTime": stdStartUtcTime,
      "stdEndUtcTime": stdEndUtcTime,
      "companyCodes": comp_code,
      "AcTypeList": "",
      "depstns": "", //出港
      "arrstns": arps.join(',') //进港
    }

    $.ajax({
      type: 'post',
      url: "/bi/redis/7x2_flt_sts",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function(response) {

        sch_total += Number(response.pftc); //计划航班总数
        sch_normal += Number(response.pfrtc); //计划航班中正常航班总数

        loaded++;
        setOverAll();

      },
      error: function() {

      }
    });



    var param = {
      "stdStartUtcTime": stdStartUtcTime,
      "stdEndUtcTime": stdEndUtcTime,
      "companyCodes": comp_code,
      "AcTypeList": "",
      "depstns": arps.join(','), //出港
      "arrstns": "" //进港
    }

    $.ajax({
      type: 'post',
      url: "/bi/redis/7x2_flt_sts",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function(response) {

        sch_total += Number(response.pftc); //计划航班总数
        sch_normal += Number(response.pfrtc); //计划航班中正常航班总数

        loaded++;
        setOverAll();

      },
      error: function() {

      }
    });


    function setOverAll() {
      if (loaded == 2) {

        var r = Math.round(sch_normal / sch_total * 100)
        if (isNaN(r)) {
          r = 100;
        }
        var area_code = AREA_LIST[area_name];

        // 根据正常率设置颜色
        $('#area_' + area_code).removeClass('area_red');
        $('#area_' + area_code).removeClass('area_orange');
        $('#area_' + area_code).removeClass('area_yellow');
        $('#area_' + area_code).removeClass('area_green1');
        $('#area_' + area_code).removeClass('area_green2');
        $('#area_' + area_code).removeClass('area_green3');

        if (r <= Number(normal_rate_colors['red'])) {
          $('#area_' + area_code).attr('class', 'area_stroke area_red');
        } else if (r <= normal_rate_colors['green1']) {
          $('#area_' + area_code).attr('class', 'area_stroke area_orange');
        } else if (r <= normal_rate_colors['yellow']) {
          $('#area_' + area_code).attr('class', 'area_stroke area_yellow');
        } else if (r <= normal_rate_colors['green3']) {
          $('#area_' + area_code).attr('class', 'area_stroke area_green3');
        } else {
          $('#area_' + area_code).attr('class', 'area_stroke area_green2');
        }

        $('#area_normal_rate_' + area_code).text(r);

        $('#china_map').removeClass('hidden');

      }
    }

  }

  function getArpSts(arp) {

    var loaded = 0;
    var sch_total = 0;
    var sch_normal = 0;

    // 获取机场的进港航班／正常率
    var param = {
      "stdStartUtcTime": stdStartUtcTime,
      "stdEndUtcTime": stdEndUtcTime,
      "companyCodes": comp_code,
      "AcTypeList": "",
      "depstns": "",
      "arrstns": arp //进港
    }

    $.ajax({
      type: 'post',
      url: "/bi/redis/7x2_flt_sts",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function(response) {

        sch_total += Number(response.pfdappPercentD); //计划航班总数
        sch_normal += Number(response.pfdappPercentM); //计划航班中正常航班总数
        var pfPercent = Number(response.cfdappPercent); //计划航班中的正常航班比率

        var r = Math.round(pfPercent);
        $('#arp_code_' + arp + ' .normal_rate2').text(r);
        setNormalRateTextColor($('#arp_code_' + arp + ' .normal_rate2'), r);

        loaded++;
        setOverAll();

      },
      error: function() {
        loaded++;
        setOverAll();
      }
    });

    // 获取机场的出港航班／正常率
    var param = {
      "stdStartUtcTime": stdStartUtcTime,
      "stdEndUtcTime": stdEndUtcTime,
      "companyCodes": comp_code,
      "AcTypeList": "",
      "depstns": arp, //出港
      "arrstns": ""
    }

    $.ajax({
      type: 'post',
      url: "/bi/redis/7x2_flt_sts",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function(response) {

        sch_total += Number(response.pfdappPercentD); //计划航班总数
        sch_normal += Number(response.pfdappPercentM); //计划航班中正常航班总数
        var pfPercent = Number(response.pfdappPercent); //计划航班中的正常航班比率

        var r = Math.round(pfPercent);
        $('#arp_code_' + arp + ' .normal_rate3').text(r);
        setNormalRateTextColor($('#arp_code_' + arp + ' .normal_rate3'), r);

        loaded++;
        setOverAll();

      },
      error: function() {
        loaded++;
        setOverAll();
      }
    });


    function setOverAll() {
      if (loaded == 2) {
        var r = Math.round(sch_normal / sch_total * 100)
        $('#arp_code_' + arp + ' .normal_rate4').text(r);
        setNormalRateTextColor($('#arp_code_' + arp + ' .normal_rate4'), r);

        $('#pin_' + arp).removeClass('pin_red');
        $('#pin_' + arp).removeClass('pin_yellow');
        $('#pin_' + arp).removeClass('pin_green');

        // 根据正常率设置pin颜色
        if (r <= Number(normal_rate_colors['red'])) {
          $('#pin_' + arp).addClass('pin_red');
        } else if (r <= Number(normal_rate_colors['yellow'])) {
          $('#pin_' + arp).addClass('pin_yellow');
        } else {
          $('#pin_' + arp).addClass('pin_green');
        }
      }
    }

  }


  function setNormalRateTextColor(element, r) {
    element.parent().find('.ffnum').removeClass('red');
    element.parent().find('.ffnum').removeClass('orange');
    element.parent().find('.ffnum').removeClass('yellow');

    if (r <= Number(normal_rate_colors['red'])) {
      element.parent().find('.ffnum').addClass('red');
    } else if (r <= Number(normal_rate_colors['green1'])) {
      element.parent().find('.ffnum').addClass('orange');
    } else if (r <= Number(normal_rate_colors['yellow'])) {
      element.parent().find('.ffnum').addClass('yellow');
    }
  }


  var arp_code_list = [];
  var arp_kpi_value = {};
  var arp_kpi_name_list = ['ORI_NORMAL_NO', 'ORI_TOFF_NO'] //['ORI_NORMAL_NO','ORI_NO_SCH']

  for (var arps in BASE_CITY_LIST) {
    arp_code_list = arp_code_list.concat(arps);

    getArpSts(arps)
  }

  var date = new Date();
  var mm = date.getMonth()+1;
  var dd = date.getDate();
  var yy = date.getFullYear();
  if(mm < 10){
    mm = '0' + mm;
  }
  if(dd < 10){
    dd = '0' + dd;
  }
  var today = ''+ yy + mm + dd;
  var param = {
    "SOLR_CODE": "FAC_COMP_ARP_FLIGHT_KPI",
    "COMP_CODE": comp_code,
    "AIRPORT_CODE": arp_code_list.join(','),
    "KPI_CODE": arp_kpi_name_list.join(','),
    "VALUE_TYPE": "kpi_value_d",
    "DATE_TYPE": "D",
    "DATE_ID":today,
    "LIMIT": 0,
    "OPTIMIZE": 1
  }

  $.ajax({

    type: 'post',
    url: "/bi/query/getackpi",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {

      var data = response.data[comp_code];
      for (var kpicode in data) {
        var arpdatlist = data[kpicode]["D"];
        for (var arpcode in arpdatlist) {
          var val = arpdatlist[arpcode];
          if (arp_kpi_value[arpcode] == undefined) {
            arp_kpi_value[arpcode] = {};
          }
          arp_kpi_value[arpcode][kpicode] = Number(val);
        }
      }

      $.when(normal_rate_color).done(setKpiData());

      function setKpiData() {

        var html = '';
        var basenum = 0;

        for (var code in BASE_CITY_LIST) {
          basenum++;

          var ORI_NORMAL_NO = 0;
          var ORI_TOFF_NO = 0;


          if (arp_kpi_value[code] != undefined) {
            ORI_NORMAL_NO = Number(arp_kpi_value[code]['ORI_NORMAL_NO']);
            ORI_TOFF_NO = Number(arp_kpi_value[code]['ORI_TOFF_NO']);

            var r = Math.round(ORI_NORMAL_NO / ORI_TOFF_NO * 100);

            $('#arp_code_' + code + ' .normal_rate1').text(isNaN(r) ? "100" : r);
            setNormalRateTextColor($('#arp_code_' + code + ' .normal_rate1'), r);

          } else {
            console.log('arp_kpi_value[] cannot find airport:' + code);
          }



        }

      }


    },
    error: function() {}
  });



  // ------------------------------------------------------------------------
  // 天气情况
  // ------------------------------------------------------------------------

  var weather_map = {
    '晴': 'icon-e600_sunny',
    '沙': 'icon-e617_dust1',
    '雹': 'icon-e620_hail',
    '雾': 'icon-e615_fog',
    '烟': 'icon-e615_fog',
    '阴': 'icon-e604_gloomy',
    '雷': 'icon-e606_rain2',
    '暴': 'icon-e606_rain2',
    '风': 'icon-e612_wind',
    '霾': 'icon-e613_haze',
    '云': 'icon-e602_cloudy',
    '雨': 'icon-e607_rain3',
    '雪': 'icon-e610_snow3',
  };

  var numOfLoadingBase = 0;

  for (var arpcode in BASE_CITY_LIST) {
    var param = {
      'airport': arpcode
    }

    numOfLoadingBase++;

    $.ajax({
      type: 'post',
      url: "/bi/web/7x2_arp_weather",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function(response) {

        numOfLoadingBase--;

        //console.log('7x2_arp_weather-------');
        //console.log(response);
        if (Number(response.errorcode) == 0) {
          var weather_css = 'icon-e600_sunny';
          var cardcolor_css = '';
          /*
          airport
          airportCode
          cloudInfo 云况
          metUtcTime
          rvr 跑道目视距离
          temperature
          visibility 能见度
          weatherInfo 天气现象
          weatherInfoTxt 翻译后的天气
          windFs 风速
          
          10个基地的标准（除大连外）
          红色范围
          “能见度 小于等于800；跑道视程小于等于700   天气现象（大雾、雷暴等） 云况高度（60-90米） 风速15米/秒”
          黄色范围
          “能见度 小于等于1600米；跑道视程小于等于1400米   天气现象（大雾、雷暴等） 云况高度小于等于150米  风速大于等于10米/秒”
          
          大连的标准
          红色范围
          “能见度 小于等于1200；跑道视程小于等于1200  天气现象（大雾、雷暴、沙尘暴） 云况高度小于等于90米  风速大于等于15米/秒”
          黄色范围
          “能见度  小于等于2000米；跑道视程小于等于1800米  天气现象（大雾、雷暴等） 云况高度小于等于150米  风速大于等于10米/秒”
          
          注：FG代表大雾
                TS或TSRA或+RA 代表雷暴
                 SS或DS沙尘暴
          
          */
          var weatherInfoCodes = ['FG', 'TS', 'TSRA', 'RA', 'SS', 'DS'];
          var code = response.airport;
          var visibility = isNaN(response.visibility) || response.visibility == 0 ? 9999 : Number(response.visibility); //能见度
          var rvr = isNaN(response.rvr) || response.rvr == 0 ? 9999 : Number(response.rvr); //跑道目视距离
          var cloudInfo = isNaN(response.cloudInfo) || response.cloudInfo == 0 ? 9999 : Number(response.cloudInfo); //云况
          var windFs = isNaN(response.windFs) ? 0 : Number(response.windFs); //风速
          var weatherInfo = response.weatherInfo; //天气现象
          var weatherInfoTxt = response.weatherInfoTxt.replace(/<[^>]+>/g, "");

          //console.log('===visibility', visibility);
          //console.log('===rvr', rvr);
          //console.log('===windFs', windFs);

          var weather_css = '';
          if (code != 'DLC') {
            if (visibility <= 800 || rvr <= 700 || windFs >= 15 || cloudInfo <= 90) {
              cardcolor_css = 'redcard';
            } else if (visibility <= 1600 || rvr <= 1400 || windFs >= 10 || cloudInfo <= 150) {
              cardcolor_css = 'yellowcard';
            }
          } else {
            // DLC 大连的标准不一样
            if (visibility <= 1200 || rvr <= 1200 || windFs >= 15 || cloudInfo <= 90) {
              cardcolor_css = 'redcard';
            } else if (visibility <= 2000 || rvr <= 1800 || windFs >= 10 || cloudInfo <= 150) {
              cardcolor_css = 'yellowcard';
            }
          }
          if (weather_css == '') {
            for (var i = weatherInfoCodes.length - 1; i >= 0; i--) {
              var c = weatherInfoCodes[i];
              if (weatherInfo.indexOf(c) > -1) {
                cardcolor_css = 'yellowcard';
              }
            }
          }

          for (var wtxt in weather_map) {
            if (weatherInfoTxt.indexOf(wtxt) > -1) {
              weather_css = weather_map[wtxt];
            }
          }

          // 设置天气状况icon
          $('#arp_code_' + code + ' .weather span').attr('class', weather_css);

          $('#arp_code_' + code).removeClass('redcard');
          $('#arp_code_' + code).removeClass('yellowcard');

          // 设置卡片颜色
          $('#arp_code_' + code).addClass(cardcolor_css);

          //if(cardcolor_css == 'redcard'){
          //  unnormalBase.push('<span style="padding-right:5px;">' + BASE_CITY_LIST[code] + '</span>' + '<span class="'+weather_css+'"></span>' + '<span style="padding-right:18px;">' + weatherInfoTxt + '</span>');
          //}

        } else {
          console.log('7x2_arp_weather Error');
        }



      },
      error: function(jqXHR, txtStatus, errorThrown) {
        numOfLoadingBase--;
      }
    });
  }



  // ------------------------------------------------------------------------
  // 十航月度排名
  // 十航年度排名
  // ------------------------------------------------------------------------

  var logo_map = {
    '春秋': 'rank_logo_CQ.png',
    '东航': 'rank_logo_DH.png',
    '东方': 'rank_logo_DH.png',
    '国航': 'rank_logo_GH.png',
    '国际': 'rank_logo_GH.png',
    '海航': 'rank_logo_HH.png',
    '海南': 'rank_logo_HN.png',
    '控股': 'rank_logo_HN.png',
    '南航': 'rank_logo_NH.png',
    '南方': 'rank_logo_NH.png',
    '川航': 'rank_logo_SC.png',
    '四川': 'rank_logo_SC.png',
    '山航': 'rank_logo_SD.png',
    '山东': 'rank_logo_SD.png',
    '上航': 'rank_logo_SH.png',
    '上海': 'rank_logo_SH.png',
    '深航': 'rank_logo_SZ.png',
    '深圳': 'rank_logo_SZ.png',
    '厦航': 'rank_logo_XM.png',
    '厦门': 'rank_logo_XM.png',
    '天津': 'rank_logo_TH.png',
    '天航': 'rank_logo_TH.png',

    '祥鹏': 'logo_8L.png',
    '香港': 'logo_HX.png',
    '西部': 'logo_PN.png',
    '天津': 'logo_GS.png',
    '首都': 'logo_JD.png',
    '福州': 'logo_FU.png',
    '乌航': 'logo_UQ.png',
    '金鹏': 'logo_Y8.png',
    '北部湾': 'logo_GX.png',
    '长安': 'logo_9H.png',
    '桂林': 'logo_GT.png',
  };

  var param = {
    'mode': 'query',
    'type': 'month',
  }

  $.ajax({
    type: 'post',
    url: "/bi/web/7x2_comp_rank",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {

      checkLogin(response);

      if (response.comp != undefined) {
        var len = response.comp.length;
        var html = '';
        var list = response.comp;
        list.sort(function(a, b) {
          return a.rank - b.rank
        });
        var rank = 1;
        for (var i = 0; i < len; i++) {
          var obj = list[i];
          if (obj.name == '海航' || obj.name == '海南' || obj.name == '海南航空' || obj.name == '控股') {
            rank = obj.rank;
          }
          //
          var img = logo_map[obj.name];
          if (img != undefined) {
            html += '<span style="display: inline-block; width:20px; margin-bottom:3px;">' + obj.rank + '</span><img src=img/' + img + '><br>';
          } else {
            html += '<span style="display: inline-block; width:20px; margin-bottom:3px;">' + obj.rank + '</span>' + obj.name + '<br>';
          }

        }
        $('#comp_rank_month').text(rank);
        $('#comp_rank_list_month').html(html);

      }

    },
    error: function(jqXHR, txtStatus, errorThrown) {

    }
  });


  var param = {
    'mode': 'query',
    'type': 'year',
  }

  $.ajax({
    type: 'post',
    url: "/bi/web/7x2_comp_rank",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {

      checkLogin(response);

      if (response.comp != undefined) {
        var len = response.comp.length;
        var html = '';
        var list = response.comp;
        list.sort(function(a, b) {
          return a.rank - b.rank
        });
        var rank = 1;
        for (var i = 0; i < len; i++) {
          var obj = list[i];
          if (obj.name == '海航' || obj.name == '海南' || obj.name == '海南航空') {
            rank = obj.rank;
          }
          //
          var img = logo_map[obj.name];
          if (img != undefined) {
            html += '<span style="display: inline-block; width:20px; margin-bottom:3px;">' + obj.rank + '</span><img src=img/' + img + '><br>';
          } else {
            html += '<span style="display: inline-block; width:20px; margin-bottom:3px;">' + obj.rank + '</span>' + obj.name + '<br>';
          }
        }
        $('#comp_rank_year').text(rank);
        $('#comp_rank_list_year').html(html);

      }

    },
    error: function(jqXHR, txtStatus, errorThrown) {

    }
  });



  // ------------------------------------------------------------------------
  // VIP/VVIP保障航班总量 已完成占比 
  // ------------------------------------------------------------------------

  // ------------------------
  // 获取VIP航班数量和VIP乘客数量
  /*
  文档：getPsrSummInfoByFoc 按旅客查询航班接口.docx
  描述：旅客按航班汇总查询
  接口类：com.hnair.opcnet.api.ods.psr.PassengerApi
  接口方法：getPsrSummInfoByFoc
  */
  // ------------------------

  var vip_flt_cnt_total; //vip航班
  var vip_psr_cnt_total; //vip乘客
  var vip_flt_cnt_complete; //vip航班 已完成
  var vip_psr_cnt_complete; //vip乘客 已完成

  var vvip_flt_cnt_total; //vvip航班
  var vvip_psr_cnt_total; //vvip乘客
  var vvip_flt_cnt_complete; //vvip航班 已完成
  var vvip_psr_cnt_complete; //vvip乘客 已完成
  var vvip_flt_cnt_exec; //vvip航班 执行中

  // !!!!! 接口中无法查询执行中的VIP航班数量 !!!!!

  var vvip_load_cnt = 0;
  var param = {
    "companyCodes": comp_code,
    "detailType": "pftc", //统计航班（总计）
    "psrType": "vip,m6"
  }

  $.ajax({
    type: 'post',
    url: "/bi/web/7x2_vip_flt_cnt",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {

      vip_flt_cnt_total = response.vip_m6_flt_cnt; //vip航班
      vip_psr_cnt_total = response.vip_m6_psr_cnt; //vip乘客

      vvip_load_cnt++;
      setVVIP();

    },
    error: function() {}
  });



  var param = {
    "companyCodes": comp_code,
    "detailType": "cftc", //已执行航班班次
    "psrType": "vip,m6"
  }

  $.ajax({
    type: 'post',
    url: "/bi/web/7x2_vip_flt_cnt",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {

      vip_flt_cnt_complete = response.vip_m6_flt_cnt; //vip航班
      vip_psr_cnt_complete = response.vip_m6_psr_cnt; //vip乘客

      vvip_load_cnt++;
      setVVIP();

    },
    error: function() {}
  });



  var param = {
    'mode': 'query'
  }


  $.ajax({
    type: 'post',
    url: "/bi/web/7x2_vvip_flt",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {

      checkLogin(response);

      vvip_flt_cnt_total = response.vvip[0].total;
      vvip_psr_cnt_total = response.vvip[0].people_total;
      vvip_flt_cnt_complete = response.vvip[0].complete;
      vvip_psr_cnt_complete = response.vvip[0].people_complete;
      vvip_flt_cnt_exec = response.vvip[0].exec;

      vvip_load_cnt++;
      setVVIP();

    },
    error: function() {}
  });

  function setVVIP() {

    if (vvip_load_cnt == 3) {

      // ------------------------------------------------------------------------
      // VIP/VVIP 乘客
      // ------------------------------------------------------------------------

      var total_people = Number(vip_psr_cnt_total) + Number(vvip_psr_cnt_total);
      var total_complete = Number(vip_psr_cnt_complete) + Number(vvip_psr_cnt_complete);

      $('#vvip_people_total').text(total_people);
      $('#vvip_people_complete').text(total_complete);

      var rate_vip_people = total_complete / total_people;
      var rate = rate_vip_people;

      var canvas = document.getElementById('cvs_vip_people');
      var context = canvas.getContext('2d');

      var col = 12;
      var row = 6;
      var radius = 3.5;
      var grid = 9;

      var x;
      var y;

      // draw circle
      for (var i = 0; i < col; i++) {
        for (var j = 0; j < row; j++) {

          x = 3.5 + i * grid;
          y = 3.5 + j * grid;

          context.beginPath();
          context.arc(x, y, radius, 0, Math.PI * 2, false);
          context.fillStyle = '#034781';
          context.fill();
        }
      }

      // draw rate circles
      var limit = Math.round(col * row * rate);
      for (var i = 0; i < col; i++) {
        for (var j = 0; j < row; j++) {

          if (limit > 0) {
            x = 3.5 + i * grid;
            y = 3.5 + j * grid;

            context.beginPath();
            context.arc(x, y, radius, 0, Math.PI * 2, false);
            context.fillStyle = '#009DEB';
            context.fill();

            limit--;

          } else {

            break;
          }

        }
      }



      // ------------------------------------------------------------------------
      // VIP/VVIP 航班
      // ------------------------------------------------------------------------


      var total = Number(vip_flt_cnt_total) + Number(vvip_flt_cnt_total);
      var complete = Number(vip_flt_cnt_complete) + Number(vvip_flt_cnt_complete);

      $('#vvip_flt_total').text(total);
      $('#vvip_flt_complete').text(complete);
      $('#vvip_flt_exec').text(vvip_flt_cnt_exec);


      var rate_flt_support_completed = complete / total;
      var rate_flt_support_exec = vvip_flt_cnt_exec / total;

      $('.div_flt_support1').css('height', rate_flt_support_completed * 100 + '%');
      $('.div_flt_support1').css('top', (1 - rate_flt_support_completed) * 100 + '%');
      $('.div_flt_support2').css('height', rate_flt_support_exec * 100 + '%');
      $('.div_flt_support2').css('top', (1 - rate_flt_support_completed - rate_flt_support_exec) * 100 + '%');

      var canvas = document.getElementById('cvs_flt_support');
      var context = canvas.getContext('2d');
      var x = canvas.width / 2;
      var y = canvas.height / 2;

      // draw back
      var radius = 40;
      context.beginPath();
      context.arc(x, y, radius, 0, 2 * Math.PI, false);
      context.fillStyle = '#041946';
      context.fill();
      context.lineWidth = 4;
      context.strokeStyle = '#075C9C';
      context.stroke();

      // draw lines
      var numslice = 12;
      for (var i = 0; i < numslice; i++) {
        context.beginPath();
        var startAngle = i * (Math.PI * 2 / numslice);
        var endAngle = startAngle + Math.PI * 0.01;
        context.arc(x, y, radius, startAngle, endAngle, false);
        context.lineWidth = 4;
        context.strokeStyle = '#041946';
        context.stroke();
      }



    }

  }



  // ------------------------------------------------------------------------
  // 预警航班总量 正常率
  // ------------------------------------------------------------------------

  var param = {
    'mode': 'query'
  }

  $.ajax({
    type: 'post',
    url: "/bi/web/7x2_warning_flt",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {

      checkLogin(response);

      var total = response.data[0].total;
      var complete = response.data[0].complete;
      var normal_rate = response.data[0].normal_rate.replace("%", "");;

      $('#warning_total').text(total);
      $('#warning_complete').text(complete);
      $('#warning_normal_rate').text(normal_rate);

      var rate = normal_rate.split('%')[0];
      rate = Number(rate) / 100;

      var rate_flt_warning = rate;
      var rate = rate_flt_warning;

      var canvas = document.getElementById('cvs_flt_warning');
      var context = canvas.getContext('2d');
      var x = canvas.width / 2;
      var y = canvas.height / 2;

      // draw back
      var radius = 42;
      var startAngle = Math.PI - Math.PI / 3.6;
      var endAngle = startAngle + Math.PI + Math.PI / 3.6 * 2;
      var counterClockwise = false;

      context.beginPath();
      context.arc(x, y, radius, startAngle, endAngle, counterClockwise);
      context.lineWidth = 20;
      context.strokeStyle = '#00438B';
      context.stroke();

      // draw overlay
      var radius = 42;
      var startAngle2 = startAngle;
      var endAngle2 = startAngle + (endAngle - startAngle) * rate;
      var counterClockwise = false;

      context.beginPath();
      context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
      context.lineWidth = 20;

      // linear gradient
      if (rate < 0.5) {
        var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
      } else if (rate < 0.8) {
        var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
      } else {
        var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, canvas.width / 2);
      }
      color.addColorStop(0, '#122A61');
      color.addColorStop(1, '#75A5CE');
      context.strokeStyle = color;
      context.stroke();

      // draw head
      var radius = 42;
      var startAngle2 = startAngle + (endAngle - startAngle) * rate - (endAngle - startAngle) * 0.01;
      var endAngle2 = startAngle + (endAngle - startAngle) * rate;
      var counterClockwise = false;

      context.beginPath();
      context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
      context.lineWidth = 20;
      context.strokeStyle = '#FFFF00';
      context.stroke();

    },
    error: function(jqXHR, txtStatus, errorThrown) {}
  });



  // ------------------------------------------------------------------------
  // 中转旅客
  // ------------------------------------------------------------------------

  var param = {
    "companyCodes": comp_code,
    "detailType": "pftc" // 统计中转转入转出航班
  }

  $.ajax({
    type: 'post',
    url: "/bi/web/7x2_zz_trv_num",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {
      $('#zz_trv_num_total').text(response.zzTrvNumTotal); // 日中转总量
    },
    error: function() {}
  });

  var param = {
    'company': comp_code,
    'inOrOut': 'in,out' // 中转旅客转入转出
  };
  $.ajax({
    type: 'post',
    url: "/bi/web/queryPsrjoinSummary",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    beforeSend: function(XMLHttpRequest) {
      $('#sxsjcg_details_loading').show();
      $('#sxsjcg_details').hide();
    },
    success: function(response) {
      var list = response.data;
      var datamap = {};
      var total4hour = 0;
      for (var i in list) {
        var dat = list[i];
        var type = dat.inOrOutType;
        var ZZZC = dat.fourJoinNormal;
        var ZZJZ = dat.fourJoinNervous;
        var ZZCS = dat.fourJoinGone;
        total4hour += Number(ZZZC) + Number(ZZJZ) + Number(ZZCS);
        if (datamap[type] == undefined) {
          datamap[type] = {};
        }
        if (datamap[type]['ZZZC'] == undefined) {
          datamap[type]['ZZZC'] = ZZZC;
        }
        if (datamap[type]['ZZJZ'] == undefined) {
          datamap[type]['ZZJZ'] = ZZJZ;
        }
        if (datamap[type]['ZZCS'] == undefined) {
          datamap[type]['ZZCS'] = ZZCS;
        }
      }

      for (var type in datamap) {
        var ddd = datamap[type];
        for (var sts in ddd) {
          var val = Number(ddd[sts]);
          if (sts.length > 0 && $('#zz_trv_num_' + type + ' .' + sts).length) {
            $('#zz_trv_num_' + type + ' .' + sts).text(val);
            $('#zz_trv_num_' + type + ' .bar_' + sts).css('width', Math.ceil(val / total4hour * 100) + '%');
          }
        }
      }
      $('#sxsjcg_details_loading').hide();
      $('#sxsjcg_details').show();
    },
    error: function(jqXHR, txtStatus, errorThrown) {
      $('#sxsjcg_details_loading').hide();
      $('#sxsjcg_details').show();
    }
  });



  //     var param = {
  //     'COMP_CODE': comp_code,
  //     'KPI_CODE': 'ZZ_NO'// 中转旅客
  //     }

  //     $.ajax({
  //         type: 'post',
  //         url:"/bi/web/7x2_zz_trv_kpi",
  //         contentType: 'application/json',
  //         dataType: 'json',
  //         async: true,
  //         data: JSON.stringify(param),
  //         success: function(response) {
  //            var list = response.date;
  //            var datamap = {};
  //            var total = 0;
  //            var total4hour = 0;
  //            for(var i in list){
  //             var dat = list[i];
  //             var val = dat.KPI_VALUE;
  //             var type = dat.ZZ_TYPE;
  //             var sts = dat.ZZ_STATUS;
  //             if(type == 'RZZL'){
  //               // 全天的总量
  //               total = Number(val);
  //             }

  //             if(!isNaN(val)){

  //               if(datamap[type] == undefined){
  //                 datamap[type] = {};
  //               }
  //               if(datamap[type][sts] == undefined){
  //                 datamap[type][sts] = val;
  //                 if(type != 'RZZL') total4hour += Number(val);
  //               }
  //             }

  //            }

  //            for(var type in datamap){
  //             var ddd = datamap[type];
  //             for(var sts in ddd){
  //               var val = Number(ddd[sts]);
  //               if(sts.length > 0 && $('#zz_trv_num_'+type+' .'+sts).length){
  //                 $('#zz_trv_num_'+type+' .'+sts).text(val);
  //                 $('#zz_trv_num_'+type+' .bar_'+sts).css('width', Math.ceil(val/total4hour*100)+'%');
  //               }
  //             }
  //            }

  // //           $('#zz_trv_num_total').text(total); // 日中转总量
  //         },
  //         error:function(jqXHR, txtStatus, errorThrown) {

  //         }
  //     });



  // VIP 航班列表

  var vip_flt_no_list = [];

  var date = new Date();
  var month = date.getMonth() + 1;
  var day = date.getDate();
  // 测试上没有最新日期的数据，所以下面暂时把日期写死，为了能在测试上取到数据，发布到生产的时候，需要注释掉
  //var month = 10;
  //var day = 1;
  if (month < 10) {
    month = '0' + month;
  }
  if (day < 10) {
    day = '0' + day;
  }
  var today = date.getFullYear() + '-' + month + '-' + day;
  var param = {
    "acOwner": comp_code,
    "vip": "true",
    "datop": today
  }
  $.ajax({
    type: 'post',
    url: "/bi/web/7x2_vip_flt_list",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {
      //
      checkFocFlightLoaded(response);

    },
    error: function() {}
  });


  function checkFocFlightLoaded(response) {
    if (all_flight_list == undefined) {
      setTimeout(checkFocFlightLoaded, 10, response);
      return;
    }

    for (var i in response.flightNo) {
      var fno = response.flightNo[i];
      if (fno.length > 0 && fno.indexOf(comp_code) > -1 && findFltInfo(fno) != undefined && vip_flt_no_list.indexOf(fno) == -1) {

        var obj = findFltInfo(fno);
        // 近2小时进出港航班
        var etdChn = obj.etdChn; //预计起飞时间（北京时间）

        var d_time = parserDate(etdChn);
        var now_time = new Date();

        // 2小时内出发的飞机
        var ost = d_time.getTime() - now_time.getTime();

        if (ost <= 120 * 60 * 1000 && ost >= 0) {
          vip_flt_no_list.push(fno);
        }

      }
    }
    //console.log('vip_flt_no_list', vip_flt_no_list);
    createFltList(vip_flt_no_list, true);
  }


  // VVIP 航班列表
  var vvip_flt_no_list = [];
  var vvip_flt_backup_list = {}; // {主机航班号:备机航班号}
  var param = {
    "mode": "query"
  }
  $.ajax({
    type: 'post',
    url: "/bi/web/7x2_vvip",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {
      //console.log("7x2_vvip");
      for (var i in response.flt) {
        var flt = response.flt[i];
        var fltno = flt.flt_no;
        var backup_flt_no = flt.backup_flt_no;
        vvip_flt_no_list.push(fltno);
        vvip_flt_backup_list[fltno] = backup_flt_no;
      }

    },
    error: function() {}
  });

  // 预警航班 列表
  var warning_flt_no_list = [];
  var param = {
    "mode": "query"
  }
  $.ajax({
    type: 'post',
    url: "/bi/web/7x2_warning",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {
      //console.log("7x2_warning");
      for (var i in response.flt) {
        var flt = response.flt[i];
        var fltno = flt.flt_no;
        warning_flt_no_list.push(fltno);
      }
      //console.log(warning_flt_no_list);

      if (warning_flt_no_list.length > 3) {
        $('#flt_list_malfunction').html('<marquee direction="left" scrollamount="3">' + warning_flt_no_list.join('&nbsp;&nbsp;&nbsp;') + '</marquee>');
      } else {
        $('#flt_list_malfunction').html(warning_flt_no_list.join('&nbsp;&nbsp;&nbsp;'));
      }

    },
    error: function() {}
  });

  // 重点关注 列表
  var important_flt_no_list = [];
  var param = {
    "mode": "query"
  }
  $.ajax({
    type: 'post',
    url: "/bi/web/7x2_important",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {
      //console.log("7x2_important");
      for (var i in response.flt) {
        var flt = response.flt[i];
        var fltno = flt.flt_no;
        important_flt_no_list.push(fltno);
      }
      //console.log(important_flt_no_list);

    },
    error: function() {}
  });



  function createFltList(fltlist, selectFirstItem) {
    var html = '';
    var firstNo;
    for (var i in fltlist) {
      var fltno = fltlist[i];
      if (firstNo == undefined) {
        firstNo = fltno;
      }
      html += '<div class="flt_list_itm fltno_' + fltno + '" fltno="' + fltno + '">' + fltno + '</div>';
    }
    $('#flt_list_holder .wrap').html(html);

    $('.flt_list_itm').on('off', fltItmClick);
    $('.flt_list_itm').on('click', fltItmClick);
    
    if (firstNo != undefined) {

      if (selectFirstItem) {
        selectFlt(firstNo);
      }
    } else {
      selectFlt(firstNo);
    }

  }


  function showNextFlt() {

    clearTimeout(timeout_next_flt);

    var idx;
    var nextfltno;
    var nexttab;

    // alert(flt_tab_id)
    // flt_tab_id = 'tab_vip'; // 暂时锁定在vip组轮播
    if (flt_tab_id == 'tab_vip') {
      // alert(vip_flt_no_list.indexOf(selectedFltno))
      if (vip_flt_no_list.indexOf(selectedFltno) > -1 && vip_flt_no_list.indexOf(selectedFltno) < vip_flt_no_list.length) {
        idx = vip_flt_no_list.indexOf(selectedFltno)
        // alert(selectedFltno, idx)
        if (idx == vip_flt_no_list.length - 1 || idx == -1) {
          idx = 0;
        } else {
          idx++;
        }
        // idx++
        nexttab = flt_tab_id;
        nextfltno = vip_flt_no_list[idx];

      } else if (vvip_flt_no_list.length > 0) {
        nexttab = 'tab_vvip';
        nextfltno = vvip_flt_no_list[0];
      } else if (warning_flt_no_list.length > 0) {
        nexttab = 'tab_warning';
        nextfltno = warning_flt_no_list[0];
      } else if (important_flt_no_list.length > 0) {
        nexttab = 'tab_important';
        nextfltno = important_flt_no_list[0];
      } else {
        nexttab = 'tab_vip';
        nextfltno = vip_flt_no_list[0]
      }

    }

    //
    if (flt_tab_id == 'tab_vvip') {
      if (vvip_flt_no_list.indexOf(selectedFltno) > -1 && vvip_flt_no_list.indexOf(selectedFltno) < vvip_flt_no_list.length - 1) {
        idx = vvip_flt_no_list.indexOf(selectedFltno)
        idx++;
        nexttab = flt_tab_id;
        nextfltno = vvip_flt_no_list[idx];

      } else if (warning_flt_no_list.length > 0) {
        nexttab = 'tab_warning';
        nextfltno = warning_flt_no_list[0];
      } else if (important_flt_no_list.length > 0) {
        nexttab = 'tab_important';
        nextfltno = important_flt_no_list[0];
      } else {
        nexttab = 'tab_vip';
        nextfltno = vip_flt_no_list[0]
      }

    }

    //
    if (flt_tab_id == 'tab_warning') {
      if (warning_flt_no_list.indexOf(selectedFltno) > -1 && warning_flt_no_list.indexOf(selectedFltno) < warning_flt_no_list.length - 1) {
        idx = warning_flt_no_list.indexOf(selectedFltno)
        idx++;
        nexttab = flt_tab_id;
        nextfltno = warning_flt_no_list[idx];

      } else if (important_flt_no_list.length > 0) {
        nexttab = 'tab_important';
        nextfltno = important_flt_no_list[0];
      } else {
        nexttab = 'tab_vip';
        nextfltno = vip_flt_no_list[0]
      }

    }

    //
    if (flt_tab_id == 'tab_important') {
      if (important_flt_no_list.indexOf(selectedFltno) > -1 && important_flt_no_list.indexOf(selectedFltno) < important_flt_no_list.length - 1) {
        idx = important_flt_no_list.indexOf(selectedFltno)
        idx++;
        nexttab = flt_tab_id;
        nextfltno = important_flt_no_list[idx];

      } else if (vip_flt_no_list.length > 0) {
        nexttab = 'tab_vip';
        nextfltno = vip_flt_no_list[0];
      } else {
        nexttab = 'tab_vip';
        nextfltno = vip_flt_no_list[0]
      }

    }

    selectFltTab(nexttab, false);

    selectFlt(nextfltno);

  }

  function fltItmClick(evt) {
    selectFlt($(this).attr('fltno'));
  }


  var selectedFltno;

  function selectFlt(fltno) {

    clearTimeout(timeout_next_flt);

    $('#flight_details_loading').show();
    $('#flight_details').hide();
    $('#flight_no_data').hide();
    //
    selectedFltno = fltno;



    $('.flt_list_itm').removeClass('selected');
    $('.fltno_' + fltno).addClass('selected');

    var flt = findFltInfo(fltno);
    setFltDetails(flt);



    if (vip_flt_no_list.indexOf(fltno) > -1 && flt_tab_id == 'tab_vip') {
      var idx = vip_flt_no_list.indexOf(fltno);

      if (idx > 13) {
        $('#flt_list_holder .wrap').animate({
            top: '-' + ($('.flt_list_itm').height() + 7) * (idx - 13) + 'px'
          },
          300,
          function() {}
        );
      } else {
        $('#flt_list_holder .wrap').animate({
            top: '0px'
          },
          300,
          function() {}
        );
      }

    } else if (vvip_flt_no_list.indexOf(fltno) > -1 && flt_tab_id == 'tab_vvip') {
      $('#flt_list_holder .wrap').animate({
          top: '0px'
        },
        0,
        function() {}
      );
    } else if (warning_flt_no_list.indexOf(fltno) > -1 && flt_tab_id == 'tab_warning') {
      $('#flt_list_holder .wrap').animate({
          top: '0px'
        },
        0,
        function() {}
      );
    } else if (important_flt_no_list.indexOf(fltno) > -1 && flt_tab_id == 'tab_important') {
      $('#flt_list_holder .wrap').animate({
          top: '0px'
        },
        0,
        function() {}
      );
    }



  }


  function loadFltDetails(fltno, callback) {

    /*
    var param = {
    "flightNo":fltno
    }

    $.ajax({
        type: 'post',
        url:"/bi/web/7x2_flt_detail_list",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            loadingFltList.splice(loadingFltList.indexOf(fltno), 1);
            var fltdat = response[fltno];
            flightDetailsList[fltno] = fltdat;
            if(selectedFltno == fltno){
              setFltDetails(fltdat);
            }

            if(callback){
              callback();
            }
            
        },
        error:function() {
        }
    });
    */


  }


  function setFltDetails(fltdat) {

    if (fltdat == undefined) {
      $('#flight_no_data').show();
      $("#flight_details").hide();
      $('#flight_details_loading').hide();
      return;
    }

    if (arp_detail_list == undefined) {
      setTimeout(setFltDetails, 10, fltdat);
      return;
    }

    clearTimeout(timeout_next_flt);
    timeout_next_flt = setTimeout(showNextFlt, 15000);

    var fltno = fltdat.flightNo;
    var acno = fltdat.acLongNo;

    var arp1 = arp_detail_list[fltdat.depStn];
    var arp2 = arp_detail_list[fltdat.arrStn];

    if (arp1 && lang == 'cn') {
      $('#detail_depCity').text(arp1.city_name);
    } else {
      $('#detail_depCity').text(fltdat.depStn);
    }
    if (arp2 && lang == 'cn') {
      $('#detail_arrCity').text(arp2.city_name);
    } else {
      $('#detail_arrCity').text(fltdat.arrStn);
    }

    $('#detail_stdChn').text(fltdat.stdChn.split(' ')[1].substr(0, 5));
    $('#detail_staChn').text(fltdat.staChn.split(' ')[1].substr(0, 5));

    var statusMap = {
      'ARR': '落地',
      'NDR': '落地',
      'ATD': '推出',
      'ATA': '到达',
      'CNL': '取消',
      'DEL': '延误',
      'DEP': '起飞',
      'RTR': '返航',
      'SCH': '计划'
    };
    var status = fltdat.status;
    if (lang == 'cn') {
      $('#detail_status').text(statusMap[status]);
    } else {
      $('#detail_status').text(status);
    }

    $('#detail_flightNo').text(fltno);
    $('#detail_iataAcType').text(langPack['lb_ac_type'][lang] + ': ' + fltdat.acType);

    var tOffChn = fltdat.tOffChn;
    var tDwnChn = fltdat.tDwnChn;

    var dt = new Date();
    var doff = parserDate(tOffChn); // 实际离地北京时间 
    var ddown = parserDate(tDwnChn); // 实际落地北京时间

    if (vvip_flt_backup_list[fltno] != undefined) {
      $('#detail_backup').html('<span class="blue2">' + langPack['lb_backup_ac'][lang] + '</span><br>' + vvip_flt_backup_list[fltno] + '<br>');
    } else {
      $('#detail_backup').html('');
    }


    // 前序航班预计过站时间，算法为本段航班计划离港时间-前序航班预计到达时间
    $('#detail_prev_stdChn').text('-');
    $('#detail_prev_passTime').text('-');

    // 查找前序航班
    var prev_flt;
    for (var i = all_flight_list.length - 1; i >= 0; i--) {
      var flt = all_flight_list[i];
      if (flt.acLongNo == acno && flt.arrStn == fltdat.depStn && flt.stdChn < fltdat.stdChn) {
        prev_flt = flt;
        break;
      }
    }
    if (prev_flt && status != 'CNL') {

      // 前序航班 起飞时间
      var prevd;
      if (prev_flt.status == 'DEP' || prev_flt.status == 'ARR' || prev_flt.status == 'NDR' || prev_flt.status == 'ATA') {
        prevd = prev_flt.atdChn.split(' ')[1].substr(0, 5); // 实际起飞时间 atdChn
      } else {
        prevd = prev_flt.etdChn.split(' ')[1].substr(0, 5); // 预计起飞时间 etdChn
      }
      $('#detail_prev_stdChn').text(prevd);

      // 前序航班 过站时间
      var detaChn; // 前序航班 到达时间
      var detdChn; // 本段航班 离港时间
      if (prev_flt.status == 'ARR' || prev_flt.status == 'NDR' || prev_flt.status == 'ATA' || prev_flt.status == 'ATA') {
        detaChn = prev_flt.ataChn; // 实际到达时间 ataChn
      } else {
        detaChn = prev_flt.etaChn; // 预计到达时间 etaChn
      }
      if (status == 'ARR' || status == 'NDR' || status == 'ATA' || status == 'DEP' || status == 'RTR') {
        detdChn = fltdat.atdChn; // 实际起飞时间 atdChn
      } else {
        detdChn = fltdat.etdChn; // 预计起飞时间 etdChn
      }
      var a_time = parserDate(detaChn); // 前序航班
      var d_time = parserDate(detdChn); // 本段航班
      var sec = d_time.getTime() - a_time.getTime();
      var totalmin = sec / 60000;
      var hour = Math.floor(totalmin / 60);
      var min = totalmin % 60;
      $('#detail_prev_passTime').text(hour + langPack['lb_hour'][lang] + " " + min + langPack['lb_minute'][lang]);

    }



    // ------------------------------------------------------------------------
    // 获取 机组信息
    // ------------------------------------------------------------------------
    $('#detail_crwPilotInf').text('');
    $('#detail_crwStatus').text('');

    if (fltCrwCache[fltno]) {

      setCrw(fltCrwCache[fltno]);

    } else {

      var param = {
        "flightNo": fltno,
      }

      $.ajax({
        type: 'post',
        url: "/bi/web/findFlightReportV2",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

          if (response.data && response.data[0]) {
            var data = response.data[0];

            /*
            // 飞行组
            var crew = data.crwPilotInf;
            crew = crew.replace(/\d+/g,''); // 删除数字
            var arr = crew.split('；');
            var names = [];
            for(var i in arr){
                var t = arr[i];
                var n = t.split(':')[0];
                names.push(n);
            }
            var pilot = names.splice(0,3);
            var captain = pilot.shift();

            // 乘务员
            var crew = data.crwStewardInf;
            crew = crew.replace(/\d+/g,''); // 删除数字
            var arr = crew.split('；');
            var names = [];
            for(var i in arr){
                var t = arr[i];
                var n = t.split(':')[0];
                names.push(n);
            }
            var steward = names.splice(0,6);
            var steward_all = names;

            // 安全员
            var crew = data.safer1.replace(/\d+/g,''); // 删除数字
            var safer = crew.split('/');
            safer = safer.splice(0,2);
            */

            //
            fltCrwCache[fltno] = data;

            if (selectedFltno == fltno) {
              setCrw(fltCrwCache[fltno]);
            }



          }
        },
        error: function() {}
      });
    }

    function setCrw(crew) {
      // 机组信息
      
      if (crew) {
        var names = [];

        var str = '';
        if (crew["captain"] == undefined) {
          crew["captain"] = "";
        }
        if (crew["firstVice1"] == undefined) {
          crew["firstVice1"] = "";
        }
        names = names.concat(crew.captain.split('@'));
        names = names.concat(crew.firstVice1.split('@'));

        for (var i = names.length - 1; i >= 0; i--) {
          if (names[i].indexOf('(') > -1) {
            var aaa = names[i].split('(');
            if (aaa.length > 1) {
              names[i] = aaa[0];
            }
          }
        }
        console.log('captain:', names);
        $('#detail_crwPilotInf').text(names.join(','));
        $('#detail_crwStatus').text('');
      }
    }



    // ------------------------------------------------------------------------
    // 获取 航班保障信息
    // ------------------------------------------------------------------------

    /*
    cncPilotArrTime,//机组到达|飞行
    cncStewardArrTime,//机组到达|乘务
    //航班截载 无法获得 checkInEnd
    cncCabinSupplyEndTime,//客舱供应|结束，机供品配备
    cncCleanEndTime,//客舱清洁
    cncMCCReleaseTime,//机务放行
    //飞机准备好 无法获得 planeReady
    cncInformBoardTime,//通知登机
    cncBoardOverTime,//登机结束
    cncClosePaxCabinTime,//客舱关闭
    cncCloseCargoCabinTime,//货舱关闭
    cncPushTime,//飞机推出
    cncACARSTOFF,//飞机起飞
    */


    for (var i in fltnodes) {
      var node = fltnodes[i];
      if (!$('#fltnode_' + node).hasClass('blue3')) {
        $('#fltnode_' + node).addClass('blue3');
      }
      $('#fltnode_' + node).removeClass('green');
      $('#fltnode_' + node).removeClass('yellow');
      $('#fltnode_' + node).removeClass('red');
    }

    if (status == 'ARR' || status == 'NDR' || status == 'ATA' || status == 'DEP' || status == 'RTR') {
      lightThemUp('cncACARSTOFF');
    } else if (status == 'ATD') {
      lightThemUp('cncPushTime');
    }


    // 只有这几个状态才去获取航班保障信息，其它状态没必要
    if (status == 'SCH' || status == 'DEL') {

      if (fltLegCache[fltno]) {

        setLeg(fltLegCache[fltno]);

      } else {

        var param = {
          "flightNo": fltno
        }
        $.ajax({
          type: 'post',
          url: "/bi/web/getFltmLegsByPage",
          contentType: 'application/json',
          dataType: 'json',
          async: true,
          data: JSON.stringify(param),
          success: function(response) {

            if (selectedFltno == fltno && response) {
              fltLegCache[fltno] = response;
              setLeg(fltLegCache[fltno]);
            }


          },
          error: function() {}
        });

      }

    }

    function setLeg(response) {
      var mlegs = response.getFltmLegsByPage;
      if (response && mlegs) {
        if (mlegs.cncPilotArrTime && mlegs.cncPilotArrTime.length > 0) {
          lightThemUp('cncPilotArrTime');
        }
        if (mlegs.cncCabinSupplyEndTime && mlegs.cncCabinSupplyEndTime.length > 0) {
          lightThemUp('cncCabinSupplyEndTime');
        }
        if (mlegs.cncCleanEndTime && mlegs.cncCleanEndTime.length > 0) {
          lightThemUp('cncCleanEndTime');
        }
        if (mlegs.cncMCCReleaseTime && mlegs.cncMCCReleaseTime.length > 0) {
          lightThemUp('cncMCCReleaseTime');
        }
        if (mlegs.cncInformBoardTime && mlegs.cncInformBoardTime.length > 0) {
          lightThemUp('cncInformBoardTime');
        }
        if (mlegs.cncBoardOverTime && mlegs.cncBoardOverTime.length > 0) {
          lightThemUp('cncBoardOverTime');
        }
        if (mlegs.cncClosePaxCabinTime && mlegs.cncClosePaxCabinTime.length > 0) {
          lightThemUp('cncClosePaxCabinTime');
        }
        if (mlegs.cncCloseCargoCabinTime && mlegs.cncCloseCargoCabinTime.length > 0) {
          lightThemUp('cncCloseCargoCabinTime');
        }
        if (mlegs.cncPushTime && mlegs.cncPushTime.length > 0) {
          lightThemUp('cncPushTime');
        }
        if (mlegs.cncACARSTOFF && mlegs.cncACARSTOFF.length > 0) {
          lightThemUp('cncACARSTOFF');
        }
      }

    }



    $('#flight_details_loading').hide();
    $('#flight_details').show();

  }

  $('.tab_normal').on('click', function() {

    selectFltTab($(this).attr('id'), true);

  });

  var flt_tab_id = 'tab_vip';

  function selectFltTab(id, selectFirstItem) {
    $('.tab_normal').removeClass('tab_selected');
    $('#' + id).addClass('tab_selected');

    flt_tab_id = id;

    if (id == 'tab_vip') {
      createFltList(vip_flt_no_list, selectFirstItem);
    } else if (id == 'tab_vvip') {
      createFltList(vvip_flt_no_list, selectFirstItem);
    } else if (id == 'tab_warning') {
      createFltList(warning_flt_no_list, selectFirstItem);
    } else if (id == 'tab_important') {
      createFltList(important_flt_no_list, selectFirstItem);
    }
  }

} // end of loadAll

	var t;
	var t2;

  function setUnnormalWeatherBase() {
    if (unnormalBase.length == 0) {
      $('#base_list_unnormal_weather').html('--');
      return;
    }

		
		var weatherAll=[];
		var weatherName = '';
		var weatherName2 = '';
		var provinceName = '';
		var proAddWeath = '';
		var provinceName2 = '';
		for(var i=0;i<unnormalBase.length;i++){
			provinceName = unnormalBase[i].ccc;
			provinceName2 = airMap.get(provinceName);
			weatherName = unnormalBase[i].wx;
			
			let wx = weatherName;
			let cont = '';
			wx.split(" ").forEach((v,i)=>{
				cont += wxCode2Name[v] ? ' ' + wxCode2Name[v] : v;
			});
			
			weatherName2 = cont;
			if(weatherName2 == null || weatherName2 == undefined){
				weatherName2 = weatherName;
			}
			proAddWeath = provinceName2 + '  ' + weatherName2 + ';  ';
			weatherAll.push(proAddWeath);
		}

    $('#base_list_unnormal_weather').html(weatherAll);

    var i=0;
    var sizess = $(".uls li").length;
    var sizesspx = sizess*330;
    var clone = $(".uls").html();
    /* $(".uls2").html(clone); */
		clearInterval(t);
    t=setInterval(moveL,80);
    
    //封装的动画函数
    function moveL(){
    	i++;
    	var sizess = $(".uls li").length;
    	if(i>sizesspx){
    		$(".box").css({left:0});
    		i=0
    	}
    	$(".box").css({left:-i+'px'});
    }
		
}
// ------------------------------------------------------------------------
// 时钟
// ------------------------------------------------------------------------
function setTime() {
  var date = new Date();
  var timestamp = date.getTime();
  var timezoneOffset = date.getTimezoneOffset();
  var utc_timestamp = timestamp + timezoneOffset * 60 * 1000;
  var utc_date = new Date();
  utc_date.setTime(utc_timestamp);

  var sydney_date = new Date();
  sydney_date.setTime(utc_timestamp + 11 * 3600 * 1000);

  var newyork_date = new Date();
  newyork_date.setTime(utc_timestamp - 5 * 3600 * 1000);

  $('#time_beijing').text(formatNum(date.getHours()) + ':' + formatNum(date.getMinutes()));
  $('#time_london').text(formatNum(utc_date.getHours()) + ':' + formatNum(utc_date.getMinutes()));
  $('#time_sydney').text(formatNum(sydney_date.getHours()) + ':' + formatNum(sydney_date.getMinutes()));
  $('#time_newyork').text(formatNum(newyork_date.getHours()) + ':' + formatNum(newyork_date.getMinutes()));

  $('#date_beijing').text(date.getDate() + ' ' + getEngMonth(date.getMonth()));
  $('#date_london').text(utc_date.getDate() + ' ' + getEngMonth(utc_date.getMonth()));
  $('#date_sydney').text(sydney_date.getDate() + ' ' + getEngMonth(sydney_date.getMonth()));
  $('#date_newyork').text(newyork_date.getDate() + ' ' + getEngMonth(newyork_date.getMonth()));

}

function formatNum(n) {
  if (n < 10) {
    return ('0' + n);
  } else {
    return n;
  }
}

function getEngMonth(month) {
  var mlist = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Spt", "Oct", "Nov", "Dec"];
  return mlist[month].toUpperCase();
}

setInterval(setTime, 1000);
setTime();



loadAll();
setInterval(loadAll, 5 * 60 * 1000);



// 点亮航班保障节点
function lightThemUp(node) {
  for (var i = fltnodes.indexOf(node); i >= 0; i--) {
    var nd = fltnodes[i];
    $('#fltnode_' + nd).addClass('green');
  }
}



// 获取机场列表
var arp_detail_list;

function getAirportList() {

  var param = {
    //"AIRPORT_CODE": arpcodes, // 可选，传入机场CODE
  }
  $.ajax({
    type: 'post',
    url: "/bi/web/airportdetail",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {
      arp_detail_list = {};
      let list = response.airport.reverse();
      list.forEach((v, i)=>{
          arp_detail_list[v.code] = v;
          if (v.is_weather == "1"){
            arpcodes.push(v.icao_id);
            airMap.set(v.icao_id,v.city_name);
          }
      });
      arpcodes = [...new Set(arpcodes)];

      // 开始结束时间
      var date = new Date();
      var mm = date.getMonth() + 1;
      var dd = date.getDate();
      if (mm < 10) {
        mm = '0' + mm;
      }
      if (dd < 10) {
        dd = '0' + dd;
      }
      var stdStart = date.getFullYear() + '-' + mm + '-' + dd + ' 00:00:00';
      var stdEnd = date.getFullYear() + '-' + mm + '-' + dd + ' 23:59:59';
      var param = {
        "cccsList": arpcodes.join(','),
        "updateDateStart": stdStart,
        "updateDateEnd": stdEnd,
      }
      $.ajax({
        type: 'post',
        url: "/bi/web/findWfMetrepBiaoZhuns",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

          for (var k in response) {
            var obj = response[k];
            if (obj.ivisAlarmValue == 2 || obj.irvrAlarmValue == 2
                || obj.wxCode == "2" || obj.iyunalarmValue == 2 || obj.ittAlarmValue == 2
                || obj.iwindSwitchAlarmValue == 2) { //1 表示黄色; 2 表示红色
              unnormalBase.push(obj);
            }
          }
          clearTimeout(window.itv_unnormalBase);
          setUnnormalWeatherBase();
        },
        error: function() {}
      });
    },
    error: function() {}
  });
}
getAirportList();



function findFltInfo(fltno) {
  for (var i = all_flight_list.length - 1; i >= 0; i--) {
    var flt = all_flight_list[i];
    if (flt.flightNo == fltno) {
      return flt;
    }
  }
  return undefined;
}


var chart_earch;

function crate3DEarth() {
  chart_earch = echarts.init(document.getElementById('earth3d'));

  var option = {
    tooltip: {
      show: false
    },
    backgroundColor: 'rgba(0,0,0,0)',
    globe: {
      baseTexture: 'asset/earth.jpg',
      //heightTexture: '/asset/get/s/data-1491889019097-rJQYikcpl.jpg',

      displacementScale: 0.1,

      shading: 'lambert',

      //environment: 'rgba(0,0,0,0)',
      shading: 'realistic',
      light: {
        main: {
          intensity: 0.3
        },
        ambient: {
          intensity: 1.0
        },
      },

      viewControl: {
        autoRotate: false,
        zoomSensitivity: false,
        targetCoord: [105.5, 32]
      },

      layers: []
    },
    series: []
  }

  chart_earch.setOption(option);
}



var currentMapType = 'china';
var planeLocationList = []; //空中
var planeLocationList2 = []; //地面

$('#map_switch_btn').on('click', function() {
  if (currentMapType == 'china') {
    currentMapType = 'world';
    $('#map_switch_lb').text(langPack['lb_domestic'][lang]);
    $('#title_earth').text(langPack['tit_mid2'][lang]);
    $('#china_map').fadeOut();
    $('#base_cards').fadeOut();
    $('.planeLocationLegend').fadeIn();
    $('.earthmask').fadeOut();
    $('.earthlight').fadeIn();

    $('#earth3d').css('pointer-events', 'auto');

    $('.searchform').fadeIn();

    var option = {
      globe: {
        viewControl: {
          targetCoord: [76, 32]
        }
      }
    }
    chart_earch.setOption(option);

    setPlaneLocation();


  } else {
    currentMapType = 'china';
    $('#map_switch_lb').text(langPack['lb_ac_loc'][lang]);
    $('#title_earth').text(langPack['tit_mid'][lang]);
    $('#china_map').fadeIn();
    $('#base_cards').fadeIn();
    $('.planeLocationLegend').fadeOut();
    $('.earthmask').fadeIn();
    $('.earthlight').fadeOut();
    $('#earth3d').css('pointer-events', 'none');

    $('.searchform').fadeOut();

    crate3DEarth();

  }
})


$('.ico_search').on('click', function() {
  var fltno = $('#ipt_fltno').val();
  if (all_flight_list && planeLocationList) {
    var flt = findFltInfo(fltno);

    var len = planeLocationList.length;
    var dat;
    var found = false;
    for (var i = 0; i < len; i++) {
      var dd = planeLocationList[i];
      if (fltno == dd.fltno) {
        dat = dd;
        found = true;
      }
    }

    if (flt && found) {
      $('.searchform .error').hide();
      showFlightDetails(fltno);
    } else {
      $('.searchform .error').show();
    }

  }


})


function showFlightDetails(flightNo) {
  var url = 'flight.html?fltno=' + flightNo;
  var url_scale = getQueryString('scale');
  if (url_scale) {
    url = url + '&scale=' + url_scale;
  }
  windowOpen(url, '_blank')
}


function windowOpen(url, target) {
  var a = document.createElement("a");
  a.setAttribute("href", url);
  if (target == null) {
    target = '';
  }
  a.setAttribute("target", target);
  document.body.appendChild(a);
  if (a.click) {
    a.click();
  } else {
    try {
      var evt = document.createEvent('Event');
      a.initEvent('click', true, true);
      a.dispatchEvent(evt);
    } catch (e) {
      window.open(url);
    }
  }
  document.body.removeChild(a);
}



function getPlaneLocation() {
  var param = {
    'mode': 'pos'
  }
  $.ajax({
    type: 'post',
    url: "/bi/web/flightMq",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {

      //var list = response.data.data1;

      planeLocationList = [];
      var plist = {};

      //var list = response.data.data1;
      function processData(data1) {
        var lst = {};
        var len = data1.length;
        for (var i = 0; i < len; i++) {
          var dd = data1[i];
          var fi = dd.fi;
          if (lst[fi] == undefined) {
            lst[fi] = {
              data: []
            };
            lst[fi]['data'].push(dd);
          } else {
            lst[fi]['data'].push(dd);
          }
        }

        return lst;
      }

      var list = processData(response.data.data1);

      for (var fltno in list) {

        var fltobj = list[fltno];
        var itmx2 = fltobj.data;

        var itm;

        if (itmx2 && itmx2.length > 1) {
          var itm1 = itmx2[0];
          var itm2 = itmx2[1];


          itm1.UTC = itm1.UTC.replace(' ', '');
          itm2.UTC = itm2.UTC.replace(' ', '');

          if (itm1.UTC > itm2.UTC) {
            itm = itm1
            itm.LON1 = itm2.LON;
            itm.LAT1 = itm2.LAT;
          } else if (itm1.UTC < itm2.UTC) {
            itm = itm2
            itm.LON1 = itm1.LON;
            itm.LAT1 = itm1.LAT;
          } else {

            itm = itm2
            itm.LON1 = itm1.LON;
            itm.LAT1 = itm1.LAT;

            console.log(fltno, '两组经纬度UTC相同');
          }
        } else if (itmx2 && itmx2.length > 0) {
          itm = itmx2[0];

        }


        if (itm) {

          var alt = itm.ALT;
          var cas = itm.CAS;
          var vec;

          var fltno = itm.fi;

          if (fltno.indexOf(comp_code) == 0) {

            var acno = itm.an;
            acno = acno.replace('-', '');

            var lon = formatLonLat(itm.LON);
            var lon1 = formatLonLat(itm.LON1);
            var lat = formatLonLat(itm.LAT);
            var lat1 = formatLonLat(itm.LAT1);

            if (isNaN(itm.LON)) {
              vec = Number(itm.VEC);
            }

            var oil = isNaN(itm.OIL) ? '' : itm.OIL;

            var pdat = {
              fltno: fltno,
              acno: acno,
              alt: alt,
              vec: vec,
              lon: lon,
              lat: lat,
              lon1: lon1,
              lat1: lat1,
              oil: oil,
            };

            var code = acno + '-' + fltno;

            /*
            if(plist[code] == undefined){
                plist[code] = pdat;
            }else if(plist[code].lon1 == undefined){
                plist[code].lon1 = pdat.lon;
                plist[code].lat1 = pdat.lat;
                if(oil > 0){
                    plist[code].oil = oil;
                }
            }else if(oil > 0){
                plist[code].oil = oil;
            }
            */

            if (pdat.vec == undefined) {
              pdat.vec = getGeoAngle(pdat.lat, pdat.lon, pdat.lat1, pdat.lon1);
            }
            planeLocationList.push(pdat);
          }
        }
      }

      /*
      for(var code in plist){
          var pdat = plist[code];
          //if(pdat.vec || pdat.lon1 != undefined){
              if(pdat.vec == undefined){
                  pdat.vec = getGeoAngle(pdat.lat, pdat.lon, pdat.lat1, pdat.lon1);
              }
              planeLocationList.push(pdat);
          //}
      }
      */



    },
    error: function(jqXHR, txtStatus, errorThrown) {

    }
  });
}

function setPlaneLocation() {

  if (all_flight_list == undefined || planeLocationList == undefined) {
    setTimeout(setPlaneLocation, 10);
    return;
  }

  var seriesData = [];


  for (var i = planeLocationList.length - 1; i >= 0; i--) {
    var itm = planeLocationList[i];
    var acno = itm.acno;
    var fltno = itm.fltno;

    var vec = itm.vec;
    var alt = itm.alt;

    var lon = itm.lon;
    var lat = itm.lat;

    var flt = findFltInfo(fltno);

    /*
  黄色：延误 DEL
  紫色：机务工作 ARR NDR ATA CNL
  绿色：飞行中 DEP RTR
  蓝色：未执行 SCH ATD

  'ARR':'落地',
  'NDR':'落地',
  'ATD':'推出',
  'ATA':'到达',
  'CNL':'取消',
  'DEL':'延误',
  'DEP':'起飞',
  'RTR':'返航',
  'SCH':'计划'

  */
    // 在飞的，空中的飞机
    if (flt && (alt > 0 || flt.status == 'DEP')) {
      var img = '';
      var delay_min = Number(flt.dur1) + Number(flt.dur2) + Number(flt.dur3) + Number(flt.dur4); //延误时间 分钟
      var color;
      var border;
      var svg = 'M90.7,4.8c-1.3-1.5-2.8-2.7-4.5-3.5C84.3,0.4,82.4,0,80.3,0l-0.4,0.1L79.5,0c-2,0-4,0.4-5.8,1.4c-1.8,0.8-3.3,2-4.6,3.5c-2.2,2.3-3.2,4.9-3.2,8l0,47.5L0.1,111.7L0,127.4l65.3-21.2l-0.1,38L39,167.2l0,7.9l3-0.8v1.6l37.9-10.9l37.9,10.9v-1.6l2.9,0.8l-0.1-7.9l-26.1-22.9l-0.1-38l65.4,21.2l-0.1-15.8L94,60.3l-0.1-47.5C93.9,9.8,92.9,7.1,90.7,4.8z';
      if (delay_min == 0) {
        color = "#00ff6c";
        border = "#005d09";
      } else {
        color = "#fff60e";
        border = "#2f2a0b";
      }
      seriesData.push({
        name: fltno,
        acno: acno,
        value: [lon, lat, 0],
        //symbolRotate: -vec, //航向   正北是0°，顺时针，0到360°
        //symbol:'path://'+svg,
        itemStyle: {
          color: color,
          borderColor: border,
          borderWidth: 1,
        }
      })
    }

  }


  var series = [];
  series.push({
    type: 'scatter3D',
    coordinateSystem: 'globe',
    symbolSize: 6,
    //blendMode: 'lighter',
    slient: true,
    label: {
      show: false,
    },
    data: seriesData
  });

  var option = {
    series: series
  }
  chart_earch.setOption(option);
}

crate3DEarth();
getPlaneLocation();
setInterval(getPlaneLocation, 5 * 60 * 1000);



function initLang() {

  $('.lang').each(function(index, el) {
    var key = $(this).attr('lang');
    var dd = langPack[key];
    if (dd) {
      if (dd.uppercase) {
        $(this).html(dd[lang].toLowerCase());
      } else {
        $(this).html(dd[lang]);
      }

    } else {
      $(this).html(key);
    }
  });
}

initLang();