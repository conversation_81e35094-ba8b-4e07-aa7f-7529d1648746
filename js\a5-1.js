(function() {

    // 全局的航司二字码
    var COMP_CODE = "HNAHK";
    // 可显示的历史 数量
    var query_limit = 20;
	var YSCLTime = '';
    // 初始化函数
    var init = function() {
        // 调整页面scale
        setPageScale && setPageScale();

        // 注册onCompanyChanged事件
        window.onCompanyChanged = function(code, company) {
            console.log(code, company)
            // 切换公司
            COMP_CODE = code;
            eking.ui.loading.show();
            page.renderTestData();
			//刷新页面的时候默认是整体成本
			$('#selecteCostLi1').addClass('selClick');
			$('#selecteCostLi2').removeClass('selClick');
			$('#selecteCostLi3').removeClass('selClick');
			$('#selectType').hide();
        }
    }

    // 千分位计数函数
    var thousand = function(num) {
        var arr = num.toString().split(".");
        var _inner = function(n) {
            return Number(n).toString().replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,');
        }
        if (arr.length == 1) {
            return _inner(num);
        } else {
            return [_inner(arr[0]), arr[1]].join('.');
        }
    };

    // 执行echarts绘制操作类
    var drawEcharts = {
        costEchart: {
            draw: function(dom, option) {
                var e = echarts.init(dom);
                e.setOption(option);
                e.resize();
            },
            getOption: function() {
                return {
                    legend: {
                        show: true,
                        itemWidth: 20,
                        itemHeight: 10,
                        itemGap: 10,
                        data: [{
                            name: '成本',
                            textStyle: {
                                color: '#00a0e9',
                                fontSize: 12
                            }
                        }, {
                            name: '同比',
                            textStyle: {
                                color: '#00a0e9',
                                fontSize: 12
                            }
                        }, {
                            name: '环比',
                            textStyle: {
                                color: '#00a0e9',
                                fontSize: 12
                            }
                        }]
                    },
                    "grid": {
                        "top": "15%",
                        "left": "5%",
                        "right": "2%",
                        "bottom": "0%",
                        "containLabel": true,
                        show: false
                    },
                    "xAxis": [{
                        "type": "category",
                        "name": "日",
                        "nameLocation": "end",
                        "data": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12日"],
                        axisLine: {
                            lineStyle: {
                                color: "#0c4377"
                            }
                        },
                        axisLabel: {
                            fontSize: 12,
                            color: "#00a0e9",
                            formatter: function(params) {
                                return params.replace(/\d+/g, function(ele) {
                                    return ele + '\n'
                                })
                            }
                        }
                    }],
                    "yAxis": [{
                        "type": "value",
                        "name": "万元",
                        "nameTextStyle": {
                            color: "#00a0e9",
                            fontSize: 12
                        },
                        "position": "left",
                        "splitNumber": 5,
                        axisLine: {
                            lineStyle: {
                                color: "#0c4377"
                            }
                        },
                        axisLabel: {
                            fontSize: 12,
                            color: "#00a0e9"
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: "#0c4377"
                            }
                        }
                    }, {
                        "type": "value",
                        "name": "同比/环比",
                        "nameTextStyle": {
                            color: "#00a0e9",
                            fontSize: 12
                        },
                        "position": "right",
                        "splitNumber": 5,
                        // "min": -100,
                        // "max": 100,
                        axisLine: {
                            lineStyle: {
                                color: "#0c4377"
                            }
                        },
                        axisLabel: {
                            fontSize: 12,
                            color: "#00a0e9",
                            formatter: function(params) {
                                return params + '%';
                            }
                        },
                        splitLine: {
                            show: false,
                            lineStyle: {
                                color: "#0c4377"
                            }
                        }
                    }],
                    "series": [{
                        "name": "成本",
                        "type": "bar",
                        "yAxisIndex": 0,
                        "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                        "barWidth": 8,
                        "itemStyle": {
                            "normal": {
                                "color": new echarts.graphic.LinearGradient(
                                    0, 0, 0, 1, [{
                                        offset: 0,
                                        color: '#456afa'
                                    }, {
                                        offset: 1,
                                        color: '#5bc6fb'
                                    }]
                                )
                            }
                        },
                        "label": {
                            "normal": {
                                "show": true,
                                "position": "top",
                                "fontSize": 8,
                                "color": "#ffffff",
                                "fontWeight": "bolder",
                                "formatter": function(params) {
                                    return thousand(params.data);
                                }
                            }
                        },
                        "barGap": "30%"
                    }, {
                        "name": "同比",
                        "type": "line",
                        "showSymbol": true,
                        "smooth": true,
                        "symbolSize": 5,
                        "symbol": "circle",
                        "itemStyle": {
                            "normal": {
                                "color": "#12cb60"
                            }
                        },
                        "lineStyle": {
                            "width": 1
                        },
                        "yAxisIndex": 1,
                        label: {
                            show: true
                        },
                        "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0].map(function(ele) {
                            return ele * 100;
                        })
                    }, {
                        "name": "环比",
                        "type": "line",
                        "showSymbol": true,
                        "smooth": true,
                        "symbolSize": 5,
                        "symbol": "circle",
                        "itemStyle": {
                            "normal": {
                                "color": "#f39800"
                            }
                        },
                        "lineStyle": {
                            "width": 1
                        },
                        "yAxisIndex": 1,
                        label: {
                            show: true
                        },
                        "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0].map(function(ele) {
                            return ele * 100;
                        })
                    }],
                    "tooltip": {
                        "show": true,
                        "trigger": "axis",
                        "formatter": function(params) {
                            // console.log(params[1])
                            return [
                                '<div class="tips">',
                                '   <div class="data1">{num}万元</div>'.replace(/{num}/ig, params[0].data),
                                '   <div class="data23">',
                                '       <div class="word">同比</div>',
                                '       <div class="value {color}">{num}{arrow}</div>'
                                .replace(/{color}/ig, (new Number(params[1].data) >= 0) ? ((new Number(params[1].data) > 0) ? 'g' : '') : 'r')
                                .replace(/{num}/ig, new Number(params[1].data).toFixed(2) + '%')
                                .replace(/{arrow}/ig, (new Number(params[1].data) >= 0) ? ((new Number(params[1].data) > 0) ? '↑' : '&nbsp;') : '↓'),
                                '   </div>',
                                '   <div class="data23">',
                                '       <div class="word">环比</div>',
                                '       <div class="value {color}">{num}{arrow}</div>'
                                .replace(/{color}/ig, (new Number(params[2].data) >= 0) ? ((new Number(params[2].data) > 0) ? 'g' : '') : 'r')
                                .replace(/{num}/ig, new Number(params[2].data).toFixed(2) + '%')
                                .replace(/{arrow}/ig, (new Number(params[2].data) >= 0) ? ((new Number(params[2].data) > 0) ? '↑' : '&nbsp;') : '↓'),
                                '   </div>',
                                '</div>'
                            ].join("");
                        }
                    }
                };
            },
            init: function(dom, option) {
                option = option || {
                    xAxisData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    barData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    lineData1: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    lineData2: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
                };
                var _option = drawEcharts.costEchart.getOption();
                _option.xAxis[0].data = option.xAxisData;
                _option.series[0].data = option.barData;
                _option.series[1].data = option.lineData1;
                _option.series[2].data = option.lineData2;
                drawEcharts.costEchart.draw(dom, _option);
            }
        },
        ringEchart: {
            draw: function(dom, option) {
                var e = echarts.init(dom);
                e.setOption(option);
                e.resize();
                setTimeout(function() {
                    $(dom).append('<div class="ring_word">整体成本</div>');
                }, 0);
            },
            getOption: function() {
                return {
                    "legend": {
                        "show": false,
                    },
                    "grid": {},
                    "title": {
                        "x": "center",
                        "y": "center"
                    },
                    "calculable": true,
                    "startAngle": 100,
                    "series": [{
                        "type": "pie",
                        "z": 0,
                        label: {
                            normal: {
                                position: "center"
                            }
                        },
                        "center": ["50%", "50%"],
                        "radius": ["95%", "100%"],
                        "data": [{
                            "value": 100,
                            // "name": "占位",
                            "tooltip": {
                                "show": false
                            },
                            "label": {
                                // "formatter": '年度计划执行率',
                                textStyle: {
                                    "fontSize": 12,
                                    "color": "#84878e"
                                }
                            },
                            "itemStyle": {
                                "normal": {
                                    "color": "#0a71c9",
                                    "opacity": "0.3"
                                }
                            }
                        }],
                        "hoverAnimation": false
                    }, {
                        "type": "pie",
                        "z": 0,
                        label: {
                            normal: {
                                position: "center"
                            }
                        },
                        "center": ["50%", "50%"],
                        "radius": ["40%", "90%"],
                        "itemStyle": {
                            "normal": {
                                "color": "#0a71c9",
                                "opacity": "0.3",
                                "borderWidth": "3",
                                "borderColor": "#0f183a",
                                "fontSize": 14
                            },
                            "emphasis": {
                                "color": "#0a71c9",
                                "opacity": "1"
                            }
                        },
                        "label": {
                            "normal": {
                                "position": "inner",
                                "fontSize": "8",
                                "padding": [10, 10, 10, 10]
                            }
                        },
                        "data": [{
                            "value": 0.1,
                            "name": "固定",
                            "tooltip": {
                                "show": false
                            },
                            "label": {
                                // "formatter": '年度计划执行率',
                                textStyle: {
                                    "fontSize": 14,
                                    "color": "#84878e"
                                }
                            }
                        }, {
                            "value": 0.3,
                            "name": "其他",
                            "tooltip": {
                                "show": false
                            },
                            "label": {
                                // "formatter": '年度计划执行率',
                                textStyle: {
                                    "fontSize": 14,
                                    "color": "#84878e"
                                }
                            }
                        }, {
                            "value": 0.6,
                            "name": "变动",
                            "tooltip": {
                                "show": false
                            },
                            "label": {
                                // "formatter": '年度计划执行率',
                                textStyle: {
                                    "fontSize": 14,
                                    "color": "#84878e"
                                }
                            }
                        }],
                        "hoverAnimation": false
                    }],
                    "tooltip": {
                        "show": true
                    }
                }
            },
            init: function(dom, option) {
                option = option || {
                    fix: 0.33,
                    float: 0.33,
                    other: 0.34
                };
                var _option = drawEcharts.ringEchart.getOption();
                _option.series[1].data[0].value = option.fix;
                _option.series[1].data[1].value = option.other;
                _option.series[1].data[2].value = option.float;
                // drawEcharts.ringEchart.draw(dom, _option);
            }
        }
    }

    // 页面vue组件
    // 挂在window上，方便调试
    window.page = new Vue({
        el: '#app',
        template: $("#template").html(),
        data: function() {
            return {
				
			myVal:0 ,//默认选中第一项
			options:[
				{
					name:'贵宾室',
					value:0
				},
				{
					name:'不正常航班',
					value:1
				},
				{
					name:'机供品',
					value:2
				},
				{
					name:'中转联程',
					value:3
				},
				{
					name:'机组',
					value:4
				},
				{
					name:'小时费',
					value:5
				},
				{
					name:'航线资源费',
					value:6
				},
				{
					name:'配餐',
					value:7
				},
				{
					name:'航油',
					value:8
				},
				{
					name:'起降',
					value:9
				},
			],
	

				
                _cacheData: null,
                title: {
                    name: '成本预估分析',
                    date: '2019年09月13日',
                },
                dateSelect: {
                    dateType: [{
                        name: '日',
                        type: 'D',
                        hover: true
                    }, {
                        name: '周',
                        type: 'L',
                        hover: false
                    }, {
                        name: '月',
                        type: 'M',
                        hover: false
                    }, {
                        name: '年',
                        type: 'Y',
                        hover: false
                    }],
                    dataList: [],
                    showUL: false,
                    boxtitle: "",
                    rangeText: "",
                    showRangeText: false
                },
                central: {
                    // 中央圆形状态
                    status: "hide",
                    // // 轮播标志
                    // flag: [
                    //     [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    //     [0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    //     [0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                    //     [0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0],
                    //     [0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0],
                    //     [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0],
                    //     [0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0],
                    //     [0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0],
                    //     [0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0],
                    //     [0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0],
                    //     [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]
                    // ],
                    // // 所有变化状态
                    // allFlag: [
                    //     [
                    //         [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]
                    //     ],
                    //     [
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1],
                    //         [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0]
                    //     ],
                    //     [
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1],
                    //         [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0]
                    //     ],
                    //     [
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1],
                    //         [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0]
                    //     ],
                    //     [
                    //         [0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1],
                    //         [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0]
                    //     ],
                    //     [
                    //         [0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1],
                    //         [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0]
                    //     ],
                    //     [
                    //         [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1],
                    //         [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0]
                    //     ],
                    //     [
                    //         [0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1],
                    //         [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0]
                    //     ],
                    //     [
                    //         [0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1],
                    //         [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0]
                    //     ],
                    //     [
                    //         [0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1],
                    //         [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0]
                    //     ],
                    //     [
                    //         [0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1],
                    //         [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
                    //     ],
                    //     [
                    //         [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0],
                    //         [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]
                    //     ],
                    // ],
                    // 当前状态
                    currentFlag: 0,
                    // 中断循环保存的状态
                    stopCurrentFlag: -1,
                    // 循环句柄
                    intervalHandler: -1,
                    // 执行时间
                    time: 10000,
                    // 隐藏的icon的名字
                    name_hide: ['机组', '起降', '配餐', '航油', 'ASK', '中转联程', '机供品', '航线资源费', '贵宾室', '不正常航班成本', '小时费'],
                    // 隐藏状态的卡片详细数据
                    card_hide_detail_data: {
                        name: '',
                        price: thousand(0),
                        unit: '',
                        tongbi: 0,
                        huanbi: 0
                    },
                    // 全部展开的数据
                    /*
                        className: 全部状态的卡片的类名
                        classNameHide: 隐藏状态的卡片的类名
                        classHoverHide: 隐藏状态是否hover
                        name: 类目名字
                        price: 类目成本
                        unit: 类目成本单位
                        tongbi: 类目成本同比值
                        huanbi: 类目成本环比值
                    */
                    allOpenDatas: [{
                            className: 'card card1',
                            classNameHide: 'block card card1 block1 p1 hover',
                            classHoverHide: true,
                            name: '机组',
                            price: thousand(0),
                            unit: '万元',
                            tongbi: '0',
                            huanbi: '0'
                        }, {
                            className: 'card card2',
                            classNameHide: 'block card card2 block2 p2',
                            classHoverHide: false,
                            name: '起降',
                            price: thousand(0),
                            unit: '万元',
                            tongbi: '0',
                            huanbi: '0'
                        }, {
                            className: 'card card3',
                            classNameHide: 'block card card3 block3 p3',
                            classHoverHide: false,
                            name: '配餐',
                            price: thousand(0),
                            unit: '万元',
                            tongbi: '0',
                            huanbi: '0'
                        }, {
                            className: 'card card4',
                            classNameHide: 'block card card4 block4 p4',
                            classHoverHide: false,
                            name: '航油',
                            price: thousand(0),
                            unit: '万元',
                            tongbi: '0',
                            huanbi: '0'
                        }
                        /*, {
                                                className: 'card card5',
                                                classNameHide: 'block card card5 block5 p5',
                                                classHoverHide: false,
                                                name: 'ASK',
                                                price: thousand(0),
                                                unit: '万元',
                                                tongbi: '0',
                                                huanbi: '0'
                                            }*/
                        , {
                            className: 'card card5',
                            classNameHide: 'block card card5 block5 p5',
                            classHoverHide: false,
                            name: '中转联程',
                            price: thousand(0),
                            unit: '万元',
                            tongbi: '0',
                            huanbi: '0'
                        }, {
                            className: 'card card6',
                            classNameHide: 'block card card6 block6 p6',
                            classHoverHide: false,
                            name: '机供品',
                            price: thousand(0),
                            unit: '万元',
                            tongbi: '0',
                            huanbi: '0'
                        }, {
                            className: 'card card7',
                            classNameHide: 'block card card7 block7 p7',
                            classHoverHide: false,
                            name: '航线资源费',
                            price: thousand(0),
                            unit: '万元',
                            tongbi: '0',
                            huanbi: '0'
                        }, {
                            className: 'card card8',
                            classNameHide: 'block card card8 block8 p8',
                            classHoverHide: false,
                            name: '贵宾室',
                            price: thousand(0),
                            unit: '万元',
                            tongbi: '0',
                            huanbi: '0'
                        }, {
                            className: 'card card9',
                            classNameHide: 'block card card9 block9 p9',
                            classHoverHide: false,
                            name: '不正常航班成本',
                            price: thousand(0),
                            unit: '万元',
                            tongbi: '0',
                            huanbi: '0'
                        }, {
                            className: 'card card10',
                            classNameHide: 'block card card10 block10 p10',
                            classHoverHide: false,
                            name: '小时费',
                            price: thousand(0),
                            unit: '万元',
                            tongbi: '0',
                            huanbi: '0'
                        }
                    ],
                    // 隐藏卡片的详细数据的样式
                    card_detail_class: 'card_hide_status none'
                },
                estimatedCost: {
                    amount: 0,
                    unit: '万元'
                },
                flightVolume: {
                    amount: 0,
                    unit: '架次'
                },
                seatKmCost: [],
                passengerKmCost: [],
                hourlyCost: [],
                flyHours: []
            }
        },
        computed: {
            showEstimatedCost: function() {
                var _this = this;
                var amount = thousand(_this.estimatedCost.amount);
                return _.map(amount.split(''), function(ele) {
                    return (ele == "," || ele == ".") ? ele : ele ;
                }).join('');
            },
            showFlightVolume: function() {
                var _this = this;
                var amount = thousand(_this.flightVolume.amount);
                /* return _.map(amount.split(''), function(ele) {
                    return (ele == "," || ele == ".") ? '<div class="num comma">' + ele + '</div>' : '<div class="num">' + ele + '</div>';
                }).join('') + '<div class="num unit">' + _this.flightVolume.unit + '</div>'; */
				return _.map(amount.split(''), function(ele) {
				    return (ele == "," || ele == ".") ?ele :ele;
				}).join('');
            },
            rangeTextComputed: function() {
                var _this = this;
                var type = $.grep(_this.dateSelect.dateType, function(ele, i) {
                    return ele.hover
                })[0].type;
                if (type == "M") {
                    var dobj = moment(_this.dateSelect.rangeText, "YYYYMM");
                    return [dobj.format("YYYYMMDD"), dobj.format("YYYYMM") + dobj.daysInMonth()].join('-');
                }
                if (type == "L") {

                    var _week = _this.dateSelect.rangeText.slice(5, 7) - 0;
                    var _date = moment(2018, "YYYY").week(_week);
                    return [moment(new Date(new Date(_date.format("YYYY-MM-DD")).getTime() - 3 * 24 * 60 * 60 * 1000), "YYYYMMDD").format("YYYYMMDD"), _date.add(3, 'days').format("YYYYMMDD")].join('-');
                }
                return _this.dateSelect.rangeText + type;
            }
        },
        methods: {
            changeStatus: function(status) {
                var _this = this;
                _this.central.status = status;
                // 如果是全部的话那么取消循环并且回复初始状态
                if (status == "show") {
                    _this.loopStop();
                } else {
                    // 如果是隐藏的话开始循环
                    _this.loopStart();
                }
            },
            loopStart: function(flag) {
                // 圆圈轮播函数
                // var _this = this;
                // // return false;
                // _this.central.intervalHandler = setInterval(function() {
                //     console.log("正在执行interval循环函数体")
                //     _this.central.currentFlag = (_this.central.currentFlag + 1) % _this.central.allFlag.length;
                //     _this.central.flag = _this.central.allFlag[_this.central.currentFlag];

                //     // 找顶上高亮的那个
                //     for (var i = 0; i < 11; i++) {
                //         if(_this.central.flag[i][3] == 1){
                //             _this.central.card_hide_detail_data.name = _this.central.name_hide[i];
                //         }
                //     }

                //     $(".p4").removeClass("hover").addClass("hover");

                // }, _this.central.time);

                // 卡片轮播函数
                var _this = this;
                _this.central.card_detail_class = "card_hide_status detail1";
                // 循环体函数
                var loopFn = function() {
                    // 取消当前的hover
                    var currentClass = _this.central.allOpenDatas[_this.central.currentFlag].classNameHide;
                    _this.central.allOpenDatas[_this.central.currentFlag].classNameHide = currentClass.replace(/hover/ig, '');
                    _this.central.allOpenDatas[_this.central.currentFlag].classHoverHide = false;
                    // 计数器逆时针
                    // if (_this.central.currentFlag == 0) {
                    //     _this.central.currentFlag = _this.central.allOpenDatas.length - 1;
                    //     // _this.central.currentFlag = 0;
                    // } else {
                    //     // if(flag != true)
                    //     _this.central.currentFlag--;
                    // }
                    // 计数器顺时针
                    if (_this.central.currentFlag == _this.central.allOpenDatas.length - 1) {
                        _this.central.currentFlag = 0;
                        // _this.central.currentFlag = 0;
                    } else {
                        // if(flag != true)
                        _this.central.currentFlag++;
                    }
                    // 下一个增加hover
                    var nextClass = _this.central.allOpenDatas[_this.central.currentFlag].classNameHide;
                    _this.central.allOpenDatas[_this.central.currentFlag].classNameHide = nextClass + " hover";
                    _this.central.allOpenDatas[_this.central.currentFlag].classHoverHide = true;
                    // 卡片信息展示
                    var detailData = _this.central.allOpenDatas[_this.central.currentFlag];
                    _this.central.card_hide_detail_data = {
                        name: detailData.name,
                        price: detailData.price,
                        unit: detailData.unit,
                        tongbi: detailData.tongbi,
                        huanbi: detailData.huanbi
                    }
                    // 卡片类名切换
                    _this.central.card_detail_class = "card_hide_status detail" + (_this.central.currentFlag + 1);
                }
                // 执行循环
                _this.central.intervalHandler = setInterval(loopFn, _this.central.time);
                if (flag != true)
                    // 首次执行循环体函数
                    _this.central.currentFlag = 1;
                loopFn();

            },
			
            loopStop: function() {
                // 圆圈轮播停止函数
                var _this = this;
                clearInterval(_this.central.intervalHandler);
                // _this.central.flag = _this.central.allFlag[0];
                _this.central.currentFlag = 0;
            },
            blockMouseover: function(e) {
                var _this = this;
                var target = e.target || e.srcElement;
                var detailClass = target.className.replace(/block card card\d{1,2} block\d{1,2} p/ig, 'detail');
                _this.central.card_detail_class = "card_hide_status " + detailClass;

                // 提取展示详细数据
                var index = e.target.getAttribute("data-index");
                var detailData = this.central.allOpenDatas[index];
                _this.central.card_hide_detail_data = {
                    name: detailData.name,
                    price: detailData.price,
                    unit: detailData.unit,
                    tongbi: detailData.tongbi,
                    huanbi: detailData.huanbi
                }
                // 暂停循环
                clearInterval(_this.central.intervalHandler);
                // 保存一下循环的标记位
                this.central.stopCurrentFlag = this.central.currentFlag;
            },
            blockMouseout: function() {
                this.central.card_detail_class = "card_hide_status none";
                // 开始继续循环      
                // 恢复一下循环的标记位 ，并判断临界值
                // 全部是十一个子项目，0 到 10
                this.central.currentFlag = (this.central.stopCurrentFlag + 1 > this.central.allOpenDatas.length - 1) ? 0 : (this.central.stopCurrentFlag + 1);
                this.loopStart(true);
            },
            onDateShowList: function() {
                var _this = this;
                _this.dateSelect.showUL = !_this.dateSelect.showUL;
            },
            onSelectChange: function(item) {
                var _this = this;
                eking.ui.loading.show();
                _this.dateSelect.showRangeText = false;
                _this.title.date = item.value;
                _this.dateSelect.boxtitle = item.value;
                _.each(_this.dateSelect.dataList, function(ele, i) {
                    ele.selected = false;
                })
                item.selected = true;
                // _this.renderTestData();
                var key = $.grep(_this.dateSelect.dataList, function(ele, i) {
                    return ele.selected
                })[0].key;
                _this.computedData(
                    _this._cacheData,
                    $.grep(_this.dateSelect.dateType, function(ele, i) {
                        return ele.hover
                    })[0].type,
                    key
                );
                setTimeout(function() {
                    eking.ui.loading.hide();
                }, 1000);

                _this.dateSelect.rangeText = key;
				_this.passengerCapa();
            },
            onDateTypeChange: function(item) {
                var _this = this;
                // 如果当前已经高亮那就直接返回啥也不做
                if (item.hover) {
                    return false;
                }
                eking.ui.loading.show();
                // 取消所有高亮
                _.each(_this.dateSelect.dateType, function(ele, i) {
                    ele.hover = false;
                });
                // 高亮当前选项
                item.hover = true;
				//刷新页面的时候默认是整体成本
				$('#selecteCostLi1').addClass('selClick');
				$('#selecteCostLi2').removeClass('selClick');
				$('#selecteCostLi3').removeClass('selClick');
				$('#selectType').hide();
                // 切换dataList的数据源选项
                // alert("to do 切换dataList的数据源选项")
                _this.renderTestData();
            },
			
			passengerCapa : function() {
			    var _this = this;
			    // 请求接口数据
			    var type = _.filter(_this.dateSelect.dateType, function(ele) {
			        return ele.hover;
			    })[0].type;
				//当为HNAHK，查全部，把所有子公司的值全部取出来
			    if(COMP_CODE=='HNAHK'){
					var compaAll = ['HNAHK','HU','JD','GS','HX','8L','PN','FU','GX','UQ','Y8','9H','GT','CN'];
					compaAll[0];
					eking.net.post('/bi/query/getkpi', {
					    'SOLR_CODE': 'FAC_COMP_KPI',
					    'KPI_CODE': 'CKI_NUM',
					    'VALUE_TYPE': 'kpi_value_d', //本期
					    'DATE_TYPE': 'D,L,M,Y',
					    "OPTIMIZE": 1,
					    'LIMIT': 20
					}, function(json) {
					    if (json.errorcode == "0000") {
							/* data.HNAHK.CKI_NUM.D */
							var comp = '';
							var dataCom=[];
							var compNum = '';
							for(i=0;i<compaAll.length;i++){
								comp = compaAll[i];
								compNum = json.data[comp]['CKI_NUM'][type][YSCLTime];
								dataCom.push(compNum);
							}
							var dataComSum = 0;
							for(j=0;j<dataCom.length;j++){
								if(dataCom[j] != undefined){
									dataComSum = dataComSum + (+dataCom[j]);
								}
							}
							if(dataComSum>0){
								dataComSum = Math.round((dataComSum/10000)*100)/100;
								$('#YSCKL').text(dataComSum);
							}else{
								$('#YSCKL').text('0');
							}
					    } else {
					        console.log("非0返回：")
					        console.log(JSON.stringify(json));
					        alert(json.errordesc || "系统错误，请稍后再试");
					        _this.setData();
					    }
					}, function(error) {
					    console.log("网络错误，请稍后再试")
					    console.log("/bi/query/costkpi")
					    console.log(error.responseText)
					    console.log(error.status)
					    _this.setData();
					    eking.ui.loading.hide();
					})
				}else{
					eking.net.post('/bi/query/getkpi', {
					    'SOLR_CODE': 'FAC_COMP_KPI',
					    'COMP_CODE': COMP_CODE,
					    'KPI_CODE': 'CKI_NUM',
					    'VALUE_TYPE': 'kpi_value_d', //本期
					    'DATE_TYPE': 'D,L,M,Y',
					    "OPTIMIZE": 1,
					    'LIMIT': 20
					}, function(json) {
					    if (json.errorcode == "0000") {
							/* data.HNAHK.CKI_NUM.D */
							/* var dataCom = json.data.companyCode.CKI_NUM.dateType; */
							if(COMP_CODE==undefined || COMP_CODE == null || COMP_CODE == '' || COMP_CODE.length == 0){
								COMP_CODE = "HNAHK";
							}
							var dataCom = json.data[COMP_CODE]['CKI_NUM'][type];
							_this.computedData2(dataCom, type);
					    } else {
					        console.log("非0返回：")
					        console.log(JSON.stringify(json));
					        alert(json.errordesc || "系统错误，请稍后再试");
					        _this.setData();
					    }
					}, function(error) {
					    console.log("网络错误，请稍后再试")
					    console.log("/bi/query/costkpi")
					    console.log(error.responseText)
					    console.log(error.status)
					    _this.setData();
					    eking.ui.loading.hide();
					})
				}
			},
			
			computedData2: function(data, type, key) {
			    // data : 所有的数据
			    // type : 日期类型DYML
			    // key : 某个日期类型下的Key
			    var _this = this;
			    console.log("【接口请求返回来的数据】");
			    console.log(data);
				if (_.isUndefined(data) || data.length == 0) {
					$('#YSCKL').text('0');
				    return false;
				}
				var YSCLAll = Math.round((data[YSCLTime]/10000)*100)/100;
				/* if(YSCLAll==null || YSCLAll == undefined || YSCLAll.length==0){
					_this.renderTestData();
				}　 */
				$('#YSCKL').text(YSCLAll);
			},
			
            rangeTextMouseOver: function() {
                var _this = this;
                var hover = $.grep(_this.dateSelect.dateType, function(ele, i) {
                    return ele.hover
                });
                if (hover[0].type == "M" || hover[0].type == "L")
                    _this.dateSelect.showRangeText = true;
            },
            rangeTextMouseOut: function() {
                var _this = this;
                _this.dateSelect.showRangeText = false;
                _this.dateSelect.showUL = false;
            },
            selectChangeMouseover: function() {
                var _this = this;
                _this.dateSelect.showUL = true;
            },
            selectChangeMouseout: function() {
                var _this = this;
                _this.dateSelect.showUL = false;
            },
            // 全局统一设置数据函数
			/* var priceData=[];
			var unitData=[];
			var tongbiData=[];
			var huanbiData=[]; */
            setData: function(data) {
                var _this = this;
                if (_.isUndefined(data)) {
                    _this.dateSelect.dataList = [];
                    _this.dateSelect.boxtitle = "";
                }
                data = data || {};
                // 状态
                _this.central.status = "hide";
                _this.central.currentFlag = 0;
                _this.loopStart();
                // 整体成本
                _this.estimatedCost.amount = data.estimatedCost || 0;
				_this.estimatedCostTb = data.allCostTrendTb || 0;
				var estimatedCostTbs = Math.round(_this.estimatedCostTb * 10000)/100;
				_this.estimatedCostHb = data.allCostTrendHb || 0;
				var estimatedCostHbs = Math.round(_this.estimatedCostHb * 10000)/100;
				//整体成本和变动成本的同比和环比
				$('#estimatedCostTbCom').html(estimatedCostTbs+'%');
				$('#estimatedCostTbCom2').html(estimatedCostTbs+'%');
				//整体成本和变动成本的环比
				$('#estimatedCostHbCom').html(estimatedCostHbs+'%');
				$('#estimatedCostHbCom2').html(estimatedCostHbs+'%');
                // 航班量
                _this.flightVolume.amount = data.flightVolume || 0;
                // 机型成本
                _this.seatKmCost = data.seatKmCost || [];
                _this.passengerKmCost = data.passengerKmCost || [];
                _this.hourlyCost = data.hourlyCost || [];
                _this.flyHours = data.flyHours || [];
                // 中央圆环数据
                data.allOpenDatas = data.allOpenDatas || [];
                _.each(_this.central.allOpenDatas, function(ele, i) {
                    // 先清空数据
                    _this.central.allOpenDatas[i].price = thousand(0);
                    _this.central.allOpenDatas[i].unit = '万元';
                    _this.central.allOpenDatas[i].tongbi = 0;
                    _this.central.allOpenDatas[i].huanbi = 0;
                    _this.central.allOpenDatas[i] = $.extend({}, _this.central.allOpenDatas[i], data.allOpenDatas[i] || {});
                });
                // 绘图
                // 绘制echart图
                drawEcharts.costEchart.init(_this.$refs.echart, {
                    xAxisData: data.costEchartData1 || [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    barData: _.map(data.costEchartData2 || [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], function(ele) {
                        // return Number(Math.random() * 10000).toFixed(2);
                        return ele;
                    }),
                    lineData1: _.map(data.costEchartData3 || [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], function(ele) {
                        // return Number(Math.random() * 100).toFixed(2);
                        return ele;
                    }),
                    lineData2: _.map(data.costEchartData4 || [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], function(ele) {
                        // return Number(Math.random() * 100).toFixed(2) * -1;
                        return ele;
                    })
                });
                // // 绘制饼图
                // drawEcharts.ringEchart.init(_this.$refs.ring, {
                //     fix: data.ringEchartData1 || 0.33,
                //     float: data.ringEchartData2 || 0.33,
                //     other: data.ringEchartData3 || 0.34
                // });   
                // 机型成本四个滚动区域恢复初始位置
                [1, 2, 3, 4].forEach((ele, i) => {
                    $(_this.$refs['cnt' + ele]).scrollTop(0);
                })
            },
			
			setData2: function(data) {
			    drawEcharts.costEchart.init(this.$refs.echart, {
			        xAxisData: data.costEchartData1 || [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
			        barData: _.map(data.costEchartData2 || [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], function(ele) {
			            // return Number(Math.random() * 10000).toFixed(2);
			            return ele;
			        }),
			        lineData1: _.map(data.costEchartData3 || [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], function(ele) {
			            // return Number(Math.random() * 100).toFixed(2);
			            return ele;
			        }),
			        lineData2: _.map(data.costEchartData4 || [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], function(ele) {
			            // return Number(Math.random() * 100).toFixed(2) * -1;
			            return ele;
			        })
			    });
			},
			
			onselecteCostData : function(data,type,seleCostClick,myVal) {
				if(data.length==0 || data == null || data == undefined){
					var allDate2 = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
					var costPrices = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
					var costTBs = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
					var costHBs = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
					this.setData2({
						costEchartData1 : allDate2,
						costEchartData2 : costPrices,
						costEchartData3 : costTBs,
						costEchartData4 : costHBs,
					});
				}else{
					var subvariableCost = [];
					var varDate = [];
					
					if(type=='D'){
						data.forEach((v,i)=>{
							subvariableCost.push(v.data.items[myVal]);
							varDate.push(v.date.substring(6,8));
						});
					}else if(type=='L'){
						data.forEach((v,i)=>{
							subvariableCost.push(v.data.items[myVal]);
							varDate.push(v.date.substring(5,7));
						});
					}else if(type=='M'){
						data.forEach((v,i)=>{
							subvariableCost.push(v.data.items[myVal]);
							varDate.push(v.date.substring(4,6));
						});
					}else if(type=='Y'){
						data.forEach((v,i)=>{
							subvariableCost.push(v.data.items[myVal]);
							varDate.push(v.date);
						});
					}
					
					var allDate = [];
					if(varDate.length>12){
						for(i=0;i<12;i++){
							allDate.push(varDate[i]);
						}
					}else{
						for(i=0;i<varDate.length;i++){
							allDate.push(varDate[i]);
						}
					}
					allDate.reverse();//reverse()用于颠倒数组中元素的顺序
					var a1;
					var allDate2 = [];
					
					if(type=='D'){
						for(i=0 ;i<allDate.length;i++){
							a1=allDate[i] + '日';
							allDate2.push(a1);
						}
					}else if(type=='L'){
						for(i=0 ;i<allDate.length;i++){
							if(allDate[i]<10){
								a1=allDate[i].substr(1,1) + '周';
								allDate2.push(a1);
							}else{
								a1=allDate[i] + '周';
								allDate2.push(a1);
							}
						}
					}else if(type=='M'){
						for(i=0 ;i<allDate.length;i++){
							a1=allDate[i] + '月';
							allDate2.push(a1);
						}
					}else if(type=='Y'){
						for(i=0 ;i<allDate.length;i++){
							a1=allDate[i] + '年';
							allDate2.push(a1);
						}
					}
					
					var costPrice = [];
					var costTB = [];
					var costHB = [];
					var costPrices = [];
					var costTBs = [];
					var costHBs = [];
					var a1,a2,a3;
					subvariableCost.forEach((v,i)=>{
						a1 = a1= Math.round(v.cost * 100) / 100;
						a2 = a2= Math.round(v.kpiRatioSq * 100) / 100;
						a3 = a3= Math.round(v.kpiRatioTq * 100) / 100;
						costPrice.push(a1);
						costTB.push(a3);
						costHB.push(a2);
					});
					
					if(costPrice.length>12){
						for(i=0;i<12;i++){
							costPrices.push(costPrice[i]);
						}
						costPrices.reverse();
					}else{
						for(i=0;i<costPrice.length;i++){
							costPrices.push(costPrice[i]);
						}
						costPrices.reverse();
					}
					
					if(costTB.length>12){
						for(i=0;i<12;i++){
							costTBs.push(costTB[i]);
						}
						costTBs.reverse();
					}else{
						for(i=0;i<costTB.length;i++){
							costTBs.push(costTB[i]);
						}
						costTBs.reverse();
					}
					
					
					if(costHB.length>12){
						for(i=0;i<12;i++){
							costHBs.push(costHB[i]);
						}
						costHBs.reverse();
					}else{
						for(i=0;i<costHB.length;i++){
							costHBs.push(costHB[i]);
						}
						costHBs.reverse();
					}
					
					this.setData2({
						costEchartData1 : allDate2,
						costEchartData2 : costPrices,
						costEchartData3 : costTBs,
						costEchartData4 : costHBs,
						seleCostID : seleCostClick,
					});
				}
				
			},
			
			setTips: function(dom, txt) {
			    var _this = this;
			    $(dom).data('powertip', function() {
			        var t = $.grep(_this.dateSelect.dateType, function(ele, i) {
			            return ele.hover
			        })[0].type;
			        // if(t == 'M')
			        return '<div class="powertip">' + (txt || _this.dateSelect.rangeText) + '</div>';
			        // else                        
			        // return '&nbsp;';
			    });
			},
			
			//成本近期趋势->整体成本、变动成本、子变动成本切换数据使用 devin
			onselecteCost3 : function(item,myVal) {
				//选择的类型的ID
				var seleCostClick = event.currentTarget.id;
				var _this = this;
				// 请求接口数据
				var type = _.filter(_this.dateSelect.dateType, function(ele) {
				    return ele.hover;
				})[0].type;
				eking.net.post('/bi/query/costkpi', {
				    "SOLR_CODE": "FAC_COMP_COST_KPI,FAC_COMP_ACTYPE_CODE_COST_KPI",
				    "COMP_CODE": COMP_CODE,
				    "DATE_TYPE": type,
				    "LIMIT": 20
				}, function(json) {
				    eking.ui.loading.hide();
				    if (json.errorcode == "0000") {
				        // 开始解析赋值数据
				        /* _this._cacheData = json.list;
				        _this.computedData(json.list, type); */
						
						_this.onselecteCostData(json.list, type,seleCostClick,myVal);
						
				        // 范围
				        var arr = $.grep(_this.dateSelect.dataList, function(ele, i) {
				            return ele.selected
				        });
				        if (arr.length != 0) {
				            var key = arr[0].key;
				            _this.dateSelect.rangeText = key;
				        }
				    } else {
				        console.log("非0返回：")
				        console.log(JSON.stringify(json));
				        alert(json.errordesc || "系统错误，请稍后再试");
				        _this.setData();
				    }
				}, function(error) {
				    console.log("网络错误，请稍后再试")
				    console.log("/bi/query/costkpi")
				    console.log(error.responseText)
				    console.log(error.status)
				    _this.setData();
				    eking.ui.loading.hide();
				});
			},
			
            computedData: function(data, type, key) {
                console.log('computedData')
                console.log(arguments)
                // data : 所有的数据
                // type : 日期类型DYML
                // key : 某个日期类型下的Key
                var _this = this;
                console.log("【接口请求返回来的数据】");
                console.log(data);
                if (_.isUndefined(data) || data.length == 0) {
                    _this.setData();
					$('#YSCKL').text('0');
                    return false;
                }
                _this.dateSelect.dataList = data.sort(function(a, b) {
                    return b.date - a.date;
                }).map(function(ele) {
                    return {
                        key: ele.date,
                        value: {
                            'D': function(date) {
                                return moment(date, "YYYYMMDD").format("YYYY年MM月DD日");
                                // return date.slice(0, 4) + '年' + date.slice(4, 6) + '月' + date.slice(6, 8) + '日';
                            },
                            'M': function(date) {
                                return moment(date, "YYYYMMDD").format("YYYY年MM月");
                                // return date.slice(0, 4) + '年' + date.slice(4, 6) + '月';  
                            },
                            'Y': function(date) {
                                return moment(date, "YYYYMMDD").format("YYYY年");
                                // return date.slice(0, 4) + '年' ;  
                            },
                            'L': function(date) {
                                //return date.slice(0, 4) + '年' + date.slice(5, 7) + '周'
								return '第' + date.slice(5, 7) + '周'
                            }
                        }[type](ele.date),
                        selected: false
                    }
                });
                // 默认第一条
                if (_.isUndefined(key) && type == 'L') {
                     _this.dateSelect.dataList[0].selected = true;
                    _this.dateSelect.boxtitle = _this.dateSelect.dataList[0].value;
                } else if (_.isUndefined(key) && type == 'M') {
                    _this.dateSelect.dataList[0].selected = true;
                    _this.dateSelect.boxtitle = _this.dateSelect.dataList[0].value;
                } else if (_.isUndefined(key)) {
                    _this.dateSelect.dataList[0].selected = true;
                    _this.dateSelect.boxtitle = _this.dateSelect.dataList[0].value;
                }
                // 按照key找数据
                key = key || _this.dateSelect.dataList[0].key;
				YSCLTime = key;
                var _find = $.grep(data, function(ele, i) {
                    return ele.date == key;
                });
                if (_find.length == 0) {
                    _this.setData({});
                    return false;
                } else {
                    var _temp = _find[0].data;
                    console.log("====find")
                    console.log(_temp)
                    var chartBasicData = _temp.allCostTrend.sort(function(a, b) {
                        return b.sort - a.sort;
                    });
					var _allCostTrendTbHb = $.grep(chartBasicData, function(ele, i) {
					    return ele.dateId == key;
					});

                    // 处理周
                    // if(type == "L"){
                    //     chartBasicData = chartBasicData.map(function(ele){
                    //         ele.dateDesc = ele.dateId.slice(5, 7)+"周"
                    //         return ele;
                    //     });
                    // }
                    _this.setData({
						allCostTrendTb:_allCostTrendTbHb[0].kpiRatioTq,
						allCostTrendHb:_allCostTrendTbHb[0].kpiRatioSq,
						/* allCostTrendTb:(new Number(_allCostTrendTbHb[0].kpiRatioTq).toFixed(2))*100+"%", */
						/* allCostTrendHb:new Number(_allCostTrendTbHb[0].kpiRatioSq).toFixed(2), */
                        estimatedCost: new Number(_temp.allCost).toFixed(2),
                        flightVolume: new Number(_temp.pFlightNum).toFixed(0),
                        seatKmCost: _temp.modelCost.capKiloCost.map(function(ele) {
                            return {
                                unit: ele.unit,
                                plane: ele.actypeCode,
                                cost: new Number(ele.cost).toFixed(2),
                                compare: new Number(ele.kpiRatioTq * 100).toFixed(2)
                            }
                        }),
                        passengerKmCost: _temp.modelCost.trvKiloCost.map(function(ele) {
                            return {
                                unit: ele.unit,
                                plane: ele.actypeCode,
                                cost: new Number(ele.cost).toFixed(2),
                                compare: new Number(ele.kpiRatioTq * 100).toFixed(2)
                            }
                        }),
                        hourlyCost: _temp.modelCost.hourCost.map(function(ele) {
                            return {
                                unit: ele.unit,
                                plane: ele.actypeCode,
                                cost: new Number(ele.cost).toFixed(2),
                                compare: new Number(ele.kpiRatioTq * 100).toFixed(2)
                            }
                        }),
                        flyHours: _temp.modelCost.flyTime.map(function(ele) {
                            return {
                                unit: ele.unit,
                                plane: ele.actypeCode,
                                cost: new Number(ele.cost).toFixed(2),
                                compare: new Number(ele.kpiRatioTq * 100).toFixed(2)
                            }
                        }),
                        allOpenDatas: _temp.items.map(function(ele) {
                            return {
                                price: new Number(ele.cost).toFixed(2),
                                unit: ele.unit,
                                tongbi: new Number(ele.kpiRatioTq * 100).toFixed(2),
                                huanbi: new Number(ele.kpiRatioSq * 100).toFixed(2),
                                type: {
                                    'pCostC': 0,
                                    'pCostU': 1,
                                    'pCostF': 2,
                                    'pCostO': 3,
                                    // 'CAP_KILO_COST': 4,
                                    'pCostZ': 4,
                                    'pCostP': 5,
                                    'pCostJ': 6,
                                    'pCostH': 7,
                                    'pCostN': 8,
                                    'pCostT': 9
                                }[ele.type]
                            }
                        }).sort(function(a, b) {
                            return a.type - b.type;
                        }),
                        costEchartData1: chartBasicData.map(function(ele) {
                            return ele.dateDesc;
                        }),
                        costEchartData2: chartBasicData.map(function(ele) {
                            return new Number(ele.cost).toFixed(2);
                        }),
                        costEchartData3: chartBasicData.map(function(ele) {
                            return new Number(ele.kpiRatioTq * 100).toFixed(2);
                        }),
                        costEchartData4: chartBasicData.map(function(ele) {
                            return new Number(ele.kpiRatioSq * 100).toFixed(2);
                        })
                    });
                }
            },
			
            renderTestData: function() {
                var _this = this;
                // 请求接口数据
                var type = _.filter(_this.dateSelect.dateType, function(ele) {
                    return ele.hover;
                })[0].type;
                eking.net.post('/bi/query/costkpi', {
                    "SOLR_CODE": "FAC_COMP_COST_KPI,FAC_COMP_ACTYPE_CODE_COST_KPI",
                    "COMP_CODE": COMP_CODE,
                    "DATE_TYPE": type,
                    "LIMIT": 20
                }, function(json) {
                    eking.ui.loading.hide();
                    if (json.errorcode == "0000") {
                        // 开始解析赋值数据
                        _this._cacheData = json.list;
                        _this.computedData(json.list, type);
						_this.passengerCapa();
						/* _this.onselecteCostData(json.list, type); */
                        // 范围
                        var arr = $.grep(_this.dateSelect.dataList, function(ele, i) {
                            return ele.selected
                        });
                        if (arr.length != 0) {
                            var key = arr[0].key;
                            _this.dateSelect.rangeText = key;
                        }
                    } else {
                        console.log("非0返回：")
                        console.log(JSON.stringify(json));
                        alert(json.errordesc || "系统错误，请稍后再试");
                        _this.setData();
                    }
                }, function(error) {
                    console.log("网络错误，请稍后再试")
                    console.log("/bi/query/costkpi")
                    console.log(error.responseText)
                    console.log(error.status)
                    _this.setData();
                    eking.ui.loading.hide();
                })
            },
			
            initTips: function(dom) {
                var _this = this;
                // 初始化tips
                $(dom).powerTip({
                    fadeInTime: 1,
                    fadeOutTime: 1,
                    followMouse: true,
                    offset: 10,
                    manual: true,
                });
                $(dom).on('mouseover', function(evt) {
                    $.powerTip.show($(this));
                });
                $(dom).on('mouseout', function(evt) {
                    $.powerTip.hide();
                });
            }
        },
		
        mounted: function() {
            var _this = this;
            // 加载初始化函数
            init();
            // 初始化tips
            // _this.initTips(_this.$refs.selectUI);
            // _this.setTips(_this.$refs.selectUI, 'sad');
            // _this.initTips(_this.$refs.estimatedLink);
            // _this.setTips(_this.$refs.estimatedLink, "预估成本链接");
            // _this.initTips(_this.$refs.aircraftLink);
            // _this.setTips(_this.$refs.aircraftLink, "机型成本链接");

        },
		
    });

    console.log(page)

})()


//整体成本、变动成本、自变动成本Tab键切换使用--整体成本
$('#selecteCostLi1').on('click', function(event) {
	event.preventDefault();
	$('#selecteCostLi1').addClass('selClick');
	$('#selecteCostLi2').removeClass('selClick');
	$('#selecteCostLi3').removeClass('selClick');
	$('#selectType').hide();
});

//整体成本、变动成本、自变动成本Tab键切换使用--变动成本
$('#selecteCostLi2').on('click', function(event) {
	event.preventDefault();
	$('#selecteCostLi2').addClass('selClick');
	$('#selecteCostLi1').removeClass('selClick');
	$('#selecteCostLi3').removeClass('selClick');
	$('#selectType').hide();
});
//整体成本、变动成本、自变动成本Tab键切换使用--自变动成本
$('#selecteCostLi3').on('click', function(event) {
	event.preventDefault();
	$('#selecteCostLi3').addClass('selClick');
	$('#selecteCostLi2').removeClass('selClick');
	$('#selecteCostLi1').removeClass('selClick');
	$('#selectType').show();
});
