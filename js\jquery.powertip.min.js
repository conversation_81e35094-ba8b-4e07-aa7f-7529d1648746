/*!
 PowerTip v1.3.0 (2017-01-15)
 https://stevenbenner.github.io/jquery-powertip/
 Copyright (c) 2017 <PERSON> (http://stevenbenner.com/).
 Released under MIT license.
 https://raw.github.com/stevenbenner/jquery-powertip/master/LICENSE.txt
*/
!function(a,b){"function"==typeof define&&define.amd?define(["jquery"],b):"object"==typeof module&&module.exports?module.exports=b(require("jquery")):b(a.jQuery)}(this,function(a){function b(){var b=this;b.top="auto",b.left="auto",b.right="auto",b.bottom="auto",b.set=function(c,d){a.isNumeric(d)&&(b[c]=Math.round(d))}}function c(a,b,c){function d(d,e){g(),a.data(u)?h():d?(e&&a.data(v,!0),i(),c.showTip(a)):(E.tipOpenImminent=!0,k=setTimeout(function(){k=null,f()},b.intentPollInterval))}function e(d){l&&(l=E.closeDelayTimeout=clearTimeout(l),E.delayInProgress=!1),g(),E.tipOpenImminent=!1,a.data(u)&&(a.data(v,!1),d?c.hideTip(a):(E.delayInProgress=!0,E.closeDelayTimeout=setTimeout(function(){E.closeDelayTimeout=null,c.hideTip(a),E.delayInProgress=!1,l=null},b.closeDelay),l=E.closeDelayTimeout))}function f(){var e=Math.abs(E.previousX-E.currentX),f=Math.abs(E.previousY-E.currentY),g=e+f;g<b.intentSensitivity?(h(),i(),c.showTip(a)):(E.previousX=E.currentX,E.previousY=E.currentY,d())}function g(a){k=clearTimeout(k),(E.closeDelayTimeout&&l===E.closeDelayTimeout||a)&&h()}function h(){E.closeDelayTimeout=clearTimeout(E.closeDelayTimeout),E.delayInProgress=!1}function i(){E.delayInProgress&&E.activeHover&&!E.activeHover.is(a)&&E.activeHover.data(t).hide(!0)}function j(){c.resetPosition(a)}var k=null,l=null;this.show=d,this.hide=e,this.cancel=g,this.resetPosition=j}function d(){function a(a,e,g,h,i){var j,k=e.split("-")[0],l=new b;switch(j=f(a)?d(a,k):c(a,k),e){case"n":l.set("left",j.left-g/2),l.set("bottom",E.windowHeight-j.top+i);break;case"e":l.set("left",j.left+i),l.set("top",j.top-h/2);break;case"s":l.set("left",j.left-g/2),l.set("top",j.top+i);break;case"w":l.set("top",j.top-h/2),l.set("right",E.windowWidth-j.left+i);break;case"nw":l.set("bottom",E.windowHeight-j.top+i),l.set("right",E.windowWidth-j.left-20);break;case"nw-alt":l.set("left",j.left),l.set("bottom",E.windowHeight-j.top+i);break;case"ne":l.set("left",j.left-20),l.set("bottom",E.windowHeight-j.top+i);break;case"ne-alt":l.set("bottom",E.windowHeight-j.top+i),l.set("right",E.windowWidth-j.left);break;case"sw":l.set("top",j.top+i),l.set("right",E.windowWidth-j.left-20);break;case"sw-alt":l.set("left",j.left),l.set("top",j.top+i);break;case"se":l.set("left",j.left-20),l.set("top",j.top+i);break;case"se-alt":l.set("top",j.top+i),l.set("right",E.windowWidth-j.left)}return l}function c(a,b){var c,d,e=a.offset(),f=a.outerWidth(),g=a.outerHeight();switch(b){case"n":c=e.left+f/2,d=e.top;break;case"e":c=e.left+f,d=e.top+g/2;break;case"s":c=e.left+f/2,d=e.top+g;break;case"w":c=e.left,d=e.top+g/2;break;case"nw":c=e.left,d=e.top;break;case"ne":c=e.left+f,d=e.top;break;case"sw":c=e.left,d=e.top+g;break;case"se":c=e.left+f,d=e.top+g}return{top:d,left:c}}function d(a,b){function c(){o.push(j.matrixTransform(l))}var d,e,f,g,h=a.closest("svg")[0],i=a[0],j=h.createSVGPoint(),k=i.getBBox(),l=i.getScreenCTM(),m=k.width/2,n=k.height/2,o=[],p=["nw","n","ne","e","se","s","sw","w"];if(j.x=k.x,j.y=k.y,c(),j.x+=m,c(),j.x+=m,c(),j.y+=n,c(),j.y+=n,c(),j.x-=m,c(),j.x-=m,c(),j.y-=n,c(),o[0].y!==o[1].y||o[0].x!==o[7].x)for(e=Math.atan2(l.b,l.a)*D,f=Math.ceil((e%360-22.5)/45),f<1&&(f+=8);f--;)p.push(p.shift());for(g=0;g<o.length;g++)if(p[g]===b){d=o[g];break}return{top:d.y+E.scrollTop,left:d.x+E.scrollLeft}}this.compute=a}function e(c){function e(a){a.data(u,!0),y.queue(function(b){f(a),b()})}function f(b){var d;if(b.data(u)){if(E.isTipOpen)return E.isClosing||g(E.activeHover),void y.delay(100).queue(function(a){f(b),a()});b.trigger("powerTipPreRender"),d=n(b),d&&(y.empty().append(d),b.trigger("powerTipRender"),E.activeHover=b,E.isTipOpen=!0,y.data(x,c.mouseOnToPopup),c.followMouse?h():(i(b),E.isFixedTipOpen=!0),y.addClass(c.popupClass),b.data(v)||q.on("click"+C,function(d){var e=d.target;e!==b[0]&&(c.mouseOnToPopup?e===y[0]||a.contains(y[0],e)||a.powerTip.hide():a.powerTip.hide())}),c.mouseOnToPopup&&!c.manual&&(y.on("mouseenter"+C,function(){E.activeHover&&E.activeHover.data(t).cancel()}),y.on("mouseleave"+C,function(){E.activeHover&&E.activeHover.data(t).hide()})),y.fadeIn(c.fadeInTime,function(){E.desyncTimeout||(E.desyncTimeout=setInterval(k,500)),b.trigger("powerTipOpen")}))}}function g(a){E.isClosing=!0,E.isTipOpen=!1,E.desyncTimeout=clearInterval(E.desyncTimeout),a.data(u,!1),a.data(v,!1),q.off("click"+C),y.off(C),y.fadeOut(c.fadeOutTime,function(){var d=new b;E.activeHover=null,E.isClosing=!1,E.isFixedTipOpen=!1,y.removeClass(),d.set("top",E.currentY+c.offset),d.set("left",E.currentX+c.offset),y.css(d),a.trigger("powerTipClose")})}function h(){if(!E.isFixedTipOpen&&(E.isTipOpen||E.tipOpenImminent&&y.data(w))){var a,d,e=y.outerWidth(),f=y.outerHeight(),g=new b;g.set("top",E.currentY+c.offset),g.set("left",E.currentX+c.offset),a=o(g,e,f),a!==F.none&&(d=p(a),1===d?a===F.right?g.set("left",E.windowWidth-e):a===F.bottom&&g.set("top",E.scrollTop+E.windowHeight-f):(g.set("left",E.currentX-e-c.offset),g.set("top",E.currentY-f-c.offset))),y.css(g)}}function i(b){var d,e;c.smartPlacement?(d=a.fn.powerTip.smartPlacementLists[c.placement],a.each(d,function(a,c){var d=o(j(b,c),y.outerWidth(),y.outerHeight());if(e=c,d===F.none)return!1})):(j(b,c.placement),e=c.placement),y.removeClass("w nw sw e ne se n s w se-alt sw-alt ne-alt nw-alt"),y.addClass(e)}function j(a,d){var e,f,g=0,h=new b;h.set("top",0),h.set("left",0),y.css(h);do e=y.outerWidth(),f=y.outerHeight(),h=l.compute(a,d,e,f,c.offset),y.css(h);while(++g<=5&&(e!==y.outerWidth()||f!==y.outerHeight()));return h}function k(){var b=!1;E.isTipOpen&&!E.isClosing&&!E.delayInProgress&&(a.inArray("mouseleave",c.closeEvents)>-1||a.inArray("mouseout",c.closeEvents)>-1||a.inArray("blur",c.closeEvents)>-1||a.inArray("focusout",c.closeEvents)>-1)&&(E.activeHover.data(u)===!1||E.activeHover.is(":disabled")?b=!0:m(E.activeHover)||E.activeHover.is(":focus")||E.activeHover.data(v)||(y.data(x)?m(y)||(b=!0):b=!0),b&&g(E.activeHover))}var l=new d,y=a("#"+c.popupId);0===y.length&&(y=a("<div/>",{id:c.popupId}),0===s.length&&(s=a("body")),s.append(y),E.tooltips=E.tooltips?E.tooltips.add(y):y),c.followMouse&&(y.data(w)||(q.on("mousemove"+C,h),r.on("scroll"+C,h),y.data(w,!0))),this.showTip=e,this.hideTip=g,this.resetPosition=i}function f(a){return Boolean(window.SVGElement&&a[0]instanceof SVGElement)}function g(a){return Boolean(a&&"number"==typeof a.pageX)}function h(){E.mouseTrackingActive||(E.mouseTrackingActive=!0,i(),a(i),q.on("mousemove"+C,l),r.on("resize"+C,j),r.on("scroll"+C,k))}function i(){E.scrollLeft=r.scrollLeft(),E.scrollTop=r.scrollTop(),E.windowWidth=r.width(),E.windowHeight=r.height()}function j(){E.windowWidth=r.width(),E.windowHeight=r.height()}function k(){var a=r.scrollLeft(),b=r.scrollTop();a!==E.scrollLeft&&(E.currentX+=a-E.scrollLeft,E.scrollLeft=a),b!==E.scrollTop&&(E.currentY+=b-E.scrollTop,E.scrollTop=b)}function l(a){E.currentX=a.pageX,E.currentY=a.pageY}function m(a){var b=a.offset(),c=a[0].getBoundingClientRect(),d=c.right-c.left,e=c.bottom-c.top;return E.currentX>=b.left&&E.currentX<=b.left+d&&E.currentY>=b.top&&E.currentY<=b.top+e}function n(b){var c,d,e=b.data(z),f=b.data(A),g=b.data(B);return e?(a.isFunction(e)&&(e=e.call(b[0])),d=e):f?(a.isFunction(f)&&(f=f.call(b[0])),f.length>0&&(d=f.clone(!0,!0))):g&&(c=a("#"+g),c.length>0&&(d=c.html())),d}function o(a,b,c){var d=E.scrollTop,e=E.scrollLeft,f=d+E.windowHeight,g=e+E.windowWidth,h=F.none;return(a.top<d||Math.abs(a.bottom-E.windowHeight)-c<d)&&(h|=F.top),(a.top+c>f||Math.abs(a.bottom-E.windowHeight)>f)&&(h|=F.bottom),(a.left<e||a.right+b>g)&&(h|=F.left),(a.left+b>g||a.right<e)&&(h|=F.right),h}function p(a){for(var b=0;a;)a&=a-1,b++;return b}var q=a(document),r=a(window),s=a("body"),t="displayController",u="hasActiveHover",v="forcedOpen",w="hasMouseMove",x="mouseOnToPopup",y="originalTitle",z="powertip",A="powertipjq",B="powertiptarget",C=".powertip",D=180/Math.PI,E={elements:null,tooltips:null,isTipOpen:!1,isFixedTipOpen:!1,isClosing:!1,tipOpenImminent:!1,activeHover:null,currentX:0,currentY:0,previousX:0,previousY:0,desyncTimeout:null,closeDelayTimeout:null,mouseTrackingActive:!1,delayInProgress:!1,windowWidth:0,windowHeight:0,scrollTop:0,scrollLeft:0},F={none:0,top:1,bottom:2,left:4,right:8};return a.fn.powerTip=function(b,d){var f,i,j=this;return j.length?"string"===a.type(b)&&a.powerTip[b]?a.powerTip[b].call(j,j,d):(f=a.extend({},a.fn.powerTip.defaults,b),i=new e(f),h(),j.each(function(){var b,d=a(this),e=d.data(z),g=d.data(A),h=d.data(B);d.data(t)&&a.powerTip.destroy(d),b=d.attr("title"),e||h||g||!b||(d.data(z,b),d.data(y,b),d.removeAttr("title")),d.data(t,new c(d,f,i))}),f.manual||(a.each(f.openEvents,function(b,c){a.inArray(c,f.closeEvents)>-1?j.on(c+C,function(b){a.powerTip.toggle(this,b)}):j.on(c+C,function(b){a.powerTip.show(this,b)})}),a.each(f.closeEvents,function(b,c){a.inArray(c,f.openEvents)<0&&j.on(c+C,function(b){a.powerTip.hide(this,!g(b))})}),j.on("keydown"+C,function(b){27===b.keyCode&&a.powerTip.hide(this,!0)})),E.elements=E.elements?E.elements.add(j):j,j):j},a.fn.powerTip.defaults={fadeInTime:200,fadeOutTime:100,followMouse:!1,popupId:"powerTip",popupClass:null,intentSensitivity:7,intentPollInterval:100,closeDelay:100,placement:"n",smartPlacement:!1,offset:10,mouseOnToPopup:!1,manual:!1,openEvents:["mouseenter","focus"],closeEvents:["mouseleave","blur"]},a.fn.powerTip.smartPlacementLists={n:["n","ne","nw","s"],e:["e","ne","se","w","nw","sw","n","s","e"],s:["s","se","sw","n"],w:["w","nw","sw","e","ne","se","n","s","w"],nw:["nw","w","sw","n","s","se","nw"],ne:["ne","e","se","n","s","sw","ne"],sw:["sw","w","nw","s","n","ne","sw"],se:["se","e","ne","s","n","nw","se"],"nw-alt":["nw-alt","n","ne-alt","sw-alt","s","se-alt","w","e"],"ne-alt":["ne-alt","n","nw-alt","se-alt","s","sw-alt","e","w"],"sw-alt":["sw-alt","s","se-alt","nw-alt","n","ne-alt","w","e"],"se-alt":["se-alt","s","sw-alt","ne-alt","n","nw-alt","e","w"]},a.powerTip={show:function(b,c){return g(c)?(l(c),E.previousX=c.pageX,E.previousY=c.pageY,a(b).data(t).show()):a(b).first().data(t).show(!0,!0),b},reposition:function(b){return a(b).first().data(t).resetPosition(),b},hide:function(b,c){var d;return c=!b||c,b?d=a(b).first().data(t):E.activeHover&&(d=E.activeHover.data(t)),d&&d.hide(c),b},toggle:function(b,c){return E.activeHover&&E.activeHover.is(b)?a.powerTip.hide(b,!g(c)):a.powerTip.show(b,c),b},destroy:function(b){var c=b?a(b):E.elements;return E.elements&&0!==E.elements.length?(c.off(C).each(function(){var b=a(this),c=[y,t,u,v];b.data(y)&&(b.attr("title",b.data(y)),c.push(z)),b.removeData(c)}),E.elements=E.elements.not(c),0===E.elements.length&&(r.off(C),q.off(C),E.mouseTrackingActive=!1,E.tooltips.remove(),E.tooltips=null),b):b}},a.powerTip.showTip=a.powerTip.show,a.powerTip.closeTip=a.powerTip.hide,a.powerTip});