//定义背景图地址
const imgPlane1 = "./img/plane_1.png?1";
const imgPlane2 = "./img/plane_2.png?2";
const imgPlane3 = "./img/plane_3.png?3";
const imgAmbient1 = "./img/ambient_1.png?1";
const imgAmbient2 = "./img/ambient_2.png?2";
const imgAmbient3 = "./img/ambient_3.png?3";
const imgAmbient1_2 = "./img/ambient_1_2.png?1";
const imgAmbient2_2 = "./img/ambient_2_2.png?2";
const imgAmbient3_2 = "./img/ambient_3_2.png?3";
const imgCrew1 = "./img/crew_1.png?1";
const imgCrew2 = "./img/crew_2.png?2";
const imgCrew3 = "./img/crew_3.png?3";
var mapBoxEchart = echarts.init(document.getElementById('mapBox'));
var leftpie = echarts.init(document.getElementById('leftPie'));
var rightpie = echarts.init(document.getElementById('rightPie'));
let flightNo;
let comp_code;
let stdEndUtcTime;
let stdStartUtcTime;
let stdStart;
let stdEnd;
let stdEnd7;
let cabin;
let sortFlt;
let psr;
let ckiNum;
let crewData;
let flightInfoList;
let flightInfoListObj;
let company_kpi_data;
let flt_kpi_data;
let airportList = new Object();
let ac_aircraft_list;
let depAirport = ""; //出发机场信息
let middleAirport = "";
let arrAirport = ""; //到达机场信息
let planeLocationList = new Array(); //飞机实时位置
let pointlist;//飞行轨迹
let weather = {};
let fltLoadSheetInfo = new Object();

let maxOil; //最大油量
let curOil; //当前油量

let yellow = '#FFE361';
let red = '#FF2D32';
let grey = '#B7BEC6';
let t;
let MyMarhq;
const leftSeries = [{
    name: 'pieData',
    type: 'pie',
    color: [],
    // color: ['#FFE361', '#B7BEC6', '#F6F6F6'],
    hoverAnimation: false,
    animation: false,
    startAngle: [90],
    radius: ['70%', '100%'],
    center: ['50%', '50%'],
    labelLine: {
        show: false
    },
    data: [{
        value: 4
    },
        {
            value: 4
        },
        {
            value: 4
        }
    ]
}];
const rightSeries = [{
    name: 'pieData',
    type: 'pie',
    color: [],
    // color: ['#CFCFCF', '#FF2D32', '#F6F6F6'],
    hoverAnimation: false,
    animation: false,
    startAngle: [90],
    radius: ['70%', '100%'],
    center: ['50%', '50%'],
    labelLine: {
        show: false
    },
    data: [{
        value: 4
    },
        {
            value: 4
        },
        {
            value: 4
        }
    ]
}];
const parent_company = 'HNAHK';
const weather_map = {
    '晴': 'icon-e600_sunny',
    '沙': 'icon-e617_dust1',
    '雹': 'icon-e620_hail',
    '雾': 'icon-e615_fog',
    '烟': 'icon-e615_fog',
    '阴': 'icon-e604_gloomy',
    '雷': 'icon-e606_rain2',
    '暴': 'icon-e606_rain2',
    '风': 'icon-e612_wind',
    '霾': 'icon-e613_haze',
    '云': 'icon-e602_cloudy',
    '雨': 'icon-e607_rain3',
    '雪': 'icon-e610_snow3',
};
const statusMap = {
    'ARR': '落地',
    'NDR': '落地',
    'ATD': '推出',
    'ATA': '到达',
    'CNL': '取消',
    'DEL': '延误',
    'DEP': '起飞',
    'RTR': '返航',
    'SCH': '计划'
};
let option = {
    color: [],
    tooltip: {
        trigger: 'item',
        show: false,
    },
    geo: {
        map: 'world',
        roam: true,
        zoom: 2.7,
        center: [97, 28],
        silent: true,
        label: {
            emphasis: {
                show: false
            }
        },
        itemStyle: {
            normal: {
                areaColor: '#3d7dd0',
                borderColor: '#14224c'
            },
            emphasis: {
                areaColor: '#3d7dd0',
                borderColor: '#14224c'
            }
        },
        //regions:countries
    },
    backgroundColor: '#14224c',
    series: []
};

class flight {
    constructor() {
        flightNo = this._getQueryString('fltno') || this._getQueryString('flightNo') || this._getQueryString('flight');
        comp_code = getQueryString('compCode');
        this._initTime();
    }

    loadAll() {
        if ($('#loading_msk').length == 0 && t == undefined) {
            showLoading();
        }
        this._initMap();
        $('.fltno').text(flightNo);
        $('.fltno').css('background-image', 'url(../img/logo_' + comp_code + '.png)');
        Promise.all([this.getStandardFocFlightInfo(), this.getAirportList(), this.getKpi(), this.getPsr(),
            this.getCrew(), this.getPlanePos(), this.getPlaneTrack(), this.getflt_kpi_data(), this.getFltLoadSheetInfo()]).then((resolve) => {
            this.setCabin(sortFlt[0].acLongNo).then((resolve) => {
                this.setTrvRate();//客座率
            });//座舱布局
            this.setCrew();//机组信息
            this.setNormalRate();//正常率
            this.setOil().then((resolve) => {
                this.setMapData().then((resolve) => {
                    this.setFltInfo(sortFlt);//顶部信息
                }).catch((reject) => {
                    console.log(reject);
                    alert('无该航班实时飞行数据！');
                    hideLoading();
                });//飞机航线
            }).catch((reject)=>{
                console.log(reject);
                alert(reject);
                this._hideLoad();
            });//油量
        }).catch((reject) => {
            console.log(reject);
            alert('数据获取出错！');
            hideLoading();
        });

    }

    getStandardFocFlightInfo() {
        return new Promise((resolve, reject) => {
            var param = {
                "stdStart": stdStart,
                "stdEnd": stdEnd,
                "acOwner": comp_code,
                "statusList": '',
            };
            $.ajax({
                type: 'post',
                url: "/bi/web/getStandardFocFlightInfo",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    var list = response.data;
                    flightInfoList = {};
                    flightInfoListObj = {};
                    for (var i = list.length - 1; i >= 0; i--) {
                        var obj = list[i];
                        flightInfoList[obj.flightNo] = obj;
                        if (flightInfoListObj[obj.flightNo] == undefined) {
                            flightInfoListObj[obj.flightNo] = [];
                            flightInfoListObj[obj.flightNo].push(obj);
                        } else {
                            flightInfoListObj[obj.flightNo].push(obj);
                        }

                        if (obj.flightNo == flightNo) {
                            console.log(obj)
                        }
                    }

                    var flt = flightInfoListObj[flightNo];

                    if (flt == undefined) {
                        $('body').hide();
                        alert('没有查询到航班信息');

                        return;
                    }

                    console.log(flightNo, flt);

                    if (flt.length > 1) {
                        var dep1 = flt[0].depCity,
                            arr1 = flt[0].arrCity,
                            dep2 = flt[1].depCity,
                            arr2 = flt[1].arrCity;
                        if (arr1 == dep2) {
                            sortFlt = flt;
                        } else if (arr2 == dep1) {
                            sortFlt = flt.reverse();
                        }
                    } else {
                        sortFlt = flt;
                    }

                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    getCrew() {
        return new Promise((resolve, reject) => {
            let param = {
                "flightNo": flightNo,
            };
            $.ajax({
                type: 'post',
                url: "/bi/web/findFlightReportV2",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    if (response.data && response.data[0]) {
                        crewData = response.data[0];
                    }
                    console.log("crewData", crewData);
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    getPsr() {
        return new Promise((resolve, reject) => {
            let param = {
                "flightNo": flightNo,
            };
            $.ajax({
                type: 'post',
                url: "/bi/web/findPsrStat",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    if (response.data != undefined) {
                        psr = response.data[0];
                        ckiNum = psr.ckiNum; // 值机人数

                        // 要客		svipNum		ckiImportantConcernNum
                        // 老人		chdNum		ckiEldNum
                        // 儿童		eldNum		ckiChdNum
                        // 特服		spNum		ckiSpNum
                        var svipNum = !isNaN(psr.importantConcernNum) ? Number(psr.ckiImportantConcernNum) : 0;//要客
                        var chdNum = !isNaN(psr.chdNum) ? Number(psr.ckiChdNum) : 0;// 儿童
                        var eldNum = !isNaN(psr.eldNum) ? Number(psr.ckiEldNum) : 0;// 老人
                        var spNum = !isNaN(psr.spNum) ? Number(psr.ckiSpNum) : 0;// 老人

                        var infNum = !isNaN(psr.infNum) ? Number(psr.ckiInfNum) : 0;
                        var vipNum = !isNaN(psr.vipNum) ? Number(psr.ckiVipNum) : 0;
                        var cipNum = !isNaN(psr.cipNum) ? Number(psr.ckiCipNum) : 0;

                        // 中转旅客		ckiInOutNum		ckiInNum + ckiOutNum
                        var ckiInNum = !isNaN(psr.ckiInNum) ? Number(psr.ckiInNum) : 0;
                        var ckiOutNum = !isNaN(psr.ckiOutNum) ? Number(psr.ckiOutNum) : 0;
                        var ckiInOutNum = ckiInNum + ckiOutNum;

                        // F舱		ckiFNum
                        // C舱		ckiCNum
                        // W舱		ckiFNum
                        // Y舱		ckiCNum
                        var fNum = !isNaN(psr.fNum) ? Number(psr.ckiInfNum) : 0;// F舱
                        var cNum = !isNaN(psr.cNum) ? Number(psr.ckiCNum) : 0;
                        var wNum = !isNaN(psr.wNum) ? Number(psr.ckiWNum) : 0;
                        var yNum = !isNaN(psr.yNum) ? Number(psr.ckiYNum) : 0;

                        $('.fNum').text(fNum);
                        $('.cNum').text(cNum);
                        $('.wNum').text(wNum);
                        $('.yNum').text(yNum);
                        $('.svipNum').text(svipNum);
                        $('.chdNum').text(chdNum);
                        $('.eldNum').text(eldNum);
                        $('.spNum').text(spNum);
                        $('.ckiInOutNum').text(ckiInOutNum);
                        $('.ckiInNum').text(ckiInNum);
                        $('.ckiOutNum').text(ckiOutNum);


                    }
                    // console.log("psr", psr);
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });

    }

    getAirportList() {
        return new Promise((resolve, reject) => {
            let param = {
                //"AIRPORT_CODE": arpcodes, // 可选，传入机场CODE
            }
            $.ajax({
                type: 'post',
                url: "/bi/web/airportdetail",
                contentType: 'application/json',
                dataType: 'json',
                async: false,
                data: JSON.stringify(param),
                success: function (response) {
                    var list = response.airport;
                    for (var i = list.length - 1; i >= 0; i--) {
                        var arp = list[i];
                        airportList[arp.code] = arp;
                    }
                    console.log("airportList", airportList);
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });

    }

    getKpi() {
        return new Promise((resolve, reject) => {
            let param = {
                'SOLR_CODE': 'FAC_COMP_KPI',
                'COMP_CODE': comp_code,
                'KPI_CODE': 'TRV_RATE,SCH_NO,NORMAL_NO_T',
                'VALUE_TYPE': 'kpi_value_d',
                "OPTIMIZE": 1,
                'DATE_TYPE': 'M',
                'LIMIT': 1
            };

            $.ajax({
                type: 'post',
                url: "/bi/query/getkpi",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    if (response.data != undefined) {
                        company_kpi_data = response.data;
                    }
                    console.log("company_kpi_data", company_kpi_data);
                    resolve(response.errorcode);

                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    getflt_kpi_data() {
        return new Promise((resolve, reject) => {
            var param = {
                'SOLR_CODE': 'FAC_COMP_FLT_ROUT_RATE_KPI',
                'COMP_CODE': companyCode2Id[comp_code],
                'KPI_CODE': 'SCH_NO,NORMAL_NO_T',
                'VALUE_TYPE': 'kpi_value_d',
                'FLT_NO': flightNo,
                'DATE_TYPE': 'M',
                'LIMIT': 1,
                "OPTIMIZE": 1
            };

            $.ajax({
                type: 'post',
                url: "/bi/web/getfltkpi",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    if (response != undefined) {
                        flt_kpi_data = response;
                    }
                    console.log("flt_kpi_data", flt_kpi_data);
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    getPlaneTrack() {
        return new Promise((resolve, reject) => {
            let param = {
                'mode': 'track',
                'fi': flightNo,
            };
            $.ajax({
                type: 'post',
                url: "/bi/web/flightMq",
                contentType: 'application/json',
                dataType: 'json',
                async: false,
                data: JSON.stringify(param),
                success: function (response) {
                    if (response != undefined) {
                        pointlist = response.data;
                    }
                    console.log("pointlist", pointlist);
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });

    }

    getFltLoadSheetInfo() {
        return new Promise((resolve, reject) => {
            var arr = stdStart.split(' ');
            var param = {
                "flightNo": flightNo,
                "flightDate": arr[0],
            }
            $.ajax({
                type: 'post',
                url: "/bi/web/getFltLoadSheetInfo",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    fltLoadSheetInfo = response.date;
                    console.log("getFltLoadSheetInfo", fltLoadSheetInfo);
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });

    }

    getPlanePos() {
        return new Promise((resolve, reject) => {
            let param = {
                'mode': 'pos'
            };
            $.ajax({
                type: 'post',
                url: "/bi/web/flightMq",
                contentType: 'application/json',
                dataType: 'json',
                async: false,
                data: JSON.stringify(param),
                success: function (response) {
                    let processData = (data1) => {
                        let lst = {};
                        let len = data1.length;
                        for (var i = 0; i < len; i++) {
                            var dd = data1[i];
                            var fi = dd.fi;
                            if (lst[fi] == undefined) {
                                lst[fi] = {
                                    data: []
                                };
                                lst[fi]['data'].push(dd);
                            } else {
                                lst[fi]['data'].push(dd);
                            }
                        }

                        return lst;
                    };
                    let list = processData(response.data.data1);
                    let fltobj = list[flightNo];
                    console.log(flightNo, fltobj);
                    if (fltobj) {
                        let itmx2 = fltobj.data;
                        let itm;
                        if (itmx2 && itmx2.length > 1) {
                            let itm1 = itmx2[0];
                            let itm2 = itmx2[1];
                            itm1.UTC = itm1.UTC.replace(' ', '');
                            itm2.UTC = itm2.UTC.replace(' ', '');
                            if (itm1.UTC > itm2.UTC) {
                                itm = itm1;
                                itm.LON1 = itm2.LON;
                                itm.LAT1 = itm2.LAT;
                            } else if (itm1.UTC < itm2.UTC) {
                                itm = itm2;
                                itm.LON1 = itm1.LON;
                                itm.LAT1 = itm1.LAT;
                            } else {
                                itm = itm2;
                                itm.LON1 = itm1.LON;
                                itm.LAT1 = itm1.LAT;
                            }
                        } else if (itmx2 && itmx2.length > 0) {
                            itm = itmx2[0];

                        }
                        if (itm) {
                            let alt = itm.ALT;
                            let cas = itm.CAS;
                            let vec;
                            let fltno = itm.fi;
                            let acno = itm.an;
                            acno = acno.replace('-', '');
                            let lon = formatLonLat(itm.LON);
                            let lon1 = formatLonLat(itm.LON1);
                            let lat = formatLonLat(itm.LAT);
                            let lat1 = formatLonLat(itm.LAT1);
                            if (isNaN(itm.LON)) {
                                vec = Number(itm.VEC);
                            }
                            let oil = isNaN(itm.OIL) ? '' : itm.OIL;
                            let pdat = {
                                fltno: fltno,
                                acno: acno,
                                alt: alt,
                                vec: vec,
                                lon: lon,
                                lat: lat,
                                lon1: lon1,
                                lat1: lat1,
                                oil: oil,
                            };
                            let code = acno + '-' + fltno;
                            if (pdat.vec == undefined) {
                                pdat.vec = getGeoAngle(pdat.lat, pdat.lon, pdat.lat1, pdat.lon1);
                            }
                            planeLocationList.push(pdat);
                        }
                    }
                    console.log('planeLocationList', planeLocationList);
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    //油量
    setOil() {
        return new Promise((resolve, reject) => {
            if (fltLoadSheetInfo) {
                maxOil = Number(fltLoadSheetInfo.takeOffFuel);
                $('#oil_1').text((fltLoadSheetInfo.takeOffFuel / 1000).toFixed(1) + 't');
                $('#oil_3').text((fltLoadSheetInfo.tripFuel / 1000).toFixed(1) + 't');
            }
            resolve("success");
        });
    }

    //油量警告
    setHorcsAlarmOilFrcfp(sortFlt) {
        let param = {
            "datop": sortFlt.datop,
            "fltId": sortFlt.flightId,
            "depstn": sortFlt.depStn,
            "arrstn": sortFlt.arrStn,
        };
        $.ajax({
            type: 'post',
            url: "/bi/web/omphorcsgethorcsalarmoilfrcfp",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {
                console.log("getHorcsAlarmOilFrcfp", response.data);
                let oil = response.data[0];
                let medFuel = Number(oil.medFuel);
                let lowFuel = Number(oil.lowFuel);
                let nearestaltYellowFuel = Number(oil.nearestaltYellowFuel);
                let nearestaltRedFuel = Number(oil.nearestaltRedFuel);
                if (oil.fuelUnit == "LBS") {
                    medFuel = medFuel * 0.45359;
                    lowFuel = lowFuel * 0.45359;
                    nearestaltYellowFuel = nearestaltYellowFuel * 0.45359;
                    nearestaltRedFuel = nearestaltRedFuel * 0.45359;
                }
                $("#medFuel").text((medFuel / 1000).toFixed(1) + 't');
                $("#lowFuel").text((lowFuel / 1000).toFixed(1) + 't');
                $("#nearestaltYellowFuel").text((nearestaltYellowFuel / 1000).toFixed(1) + 't');
                $("#nearestaltRedFuel").text((nearestaltRedFuel / 1000).toFixed(1) + 't');

                if (maxOil) {
                    let medFuelPrecent = medFuel / maxOil;
                    let lowFuelPrecent = lowFuel / maxOil;
                    let nearestaltYellowFuelPrecent = nearestaltYellowFuel / maxOil;
                    let nearestaltRedPrecent = nearestaltRedFuel / maxOil;
                    this.showOil('water1', lowFuelPrecent * 100, medFuelPrecent * 100, (curOil / maxOil) * 100);
                    this.showOil('water2', nearestaltRedPrecent * 100, nearestaltYellowFuelPrecent * 100, (curOil / maxOil) * 100);
                }

            }.bind(this),
            error: function (response) {
            }
        });
    }

    //计划航路
    setPlanRoute(sortFlt) {
        let hasBjzd = false;
        mapBoxEchart.getOption().series.forEach((v, i) => {
            if (v.name == "bjzd") {
                hasBjzd = true;
            }
        });
        let param = {
            "flightNo": flightNo,
            "std": sortFlt.stdChn,
            "depIataId": sortFlt.depStn,
            "arrIataId": sortFlt.arrStn
        };
        $.ajax({
            type: 'post',
            url: "/bi/web/getdspreleasegetdspreleaseinfo",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {
                console.log("route", response);
                if (response != undefined && response.routes.length > 0) {
                    let option = mapBoxEchart.getOption();
                    let series = option.series;
                    if (!hasBjzd) {
                        let arrAltIataId = response.ArrAltIataId;
                        let airportArr = arrAltIataId.split(" ");
                        let bjzd = new Array();
                        airportArr.forEach((v, i) => {
                            let arp = airportList[v];
                            if (arp) {
                                bjzd.push({"name": arp.city_name, "value": [arp.longitude, arp.latitude]})
                            }
                        });
                        series.push({
                            name: 'bjzd',
                            type: 'effectScatter',
                            coordinateSystem: 'geo',
                            showEffectOn: 'render',
                            symbolSize: 5,
                            rippleEffect: {
                                brushType: 'stroke'
                            },
                            hoverAnimation: true,
                            label: {
                                normal: {
                                    formatter: '{b}',
                                    fontSize: 12,
                                    position: 'right',
                                    show: true
                                }
                            },
                            itemStyle: {
                                normal: {
                                    color: '#FFFF7D',
                                    shadowBlur: 10,
                                    shadowColor: '#333'
                                }
                            },
                            tooltip: {
                                show: false
                            },
                            zlevel: 1,
                            data: bjzd
                        });
                    }

                    let routeData = new Array();
                    let routes = response.routes;
                    if (routes && routes.length > 1) {
                        let len = routes.length;
                        for (let i = 1; i < len; i++) {
                            let p1 = routes[i - 1];
                            let p2 = routes[i];
                            if (p1 && p2) {
                                let lon = this._DegreeConvert(p1.We)[1];
                                let lat = this._DegreeConvert(p1.Ns)[1];
                                let lon2 = this._DegreeConvert(p2.We)[1];
                                let lat2 = this._DegreeConvert(p2.Ns)[1];

                                routeData.push({
                                    fromName: '',
                                    toName: '',
                                    coords: [
                                        [lon, lat],
                                        [lon2, lat2]
                                    ]
                                });
                            } else {
                                continue;
                            }

                        }
                    }
                    series.push({
                        name: 'line1',
                        type: 'lines',
                        zlevel: 1,
                        silent: false, //不响应鼠标点击或事件
                        polyline: true, //支持多点连线
                        effect: {
                            show: false //关闭特效
                        },
                        tooltip: {
                            show: false
                        },
                        lineStyle: {
                            normal: {
                                color: 'yellow',
                                width: 1.5,
                                opacity: 0.9,
                                curveness: 0,
                                type: 'dashed'

                            }
                        },
                        data: routeData
                    });

                    option.series = series;
                    mapBoxEchart.setOption(option);
                    window.onresize = mapBoxEchart.resize;
                    this._hideLoad();
                }
            }.bind(this),
            error: function (response) {
            }
        });
    }

    //转出航班详情
    setInOutCount(sortFlt) {
        let param = {
            "fltNo": flightNo,
            "fltDate": sortFlt.datopChn,
            "inOrOutType": "out",
            "status": sortFlt.status,
            "etaChn": sortFlt.etaChn,
            "ataChn": sortFlt.etaChn,
        };
        $.ajax({
            type: 'post',
            url: "/bi/web/passengergetconnectpsrdatabyflt",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {
                console.log("InOutCount", response.data);
                if (response != undefined && response.data.length > 0) {
                    let items = new Array();
                    response.data.forEach((v, i) => {
                        let obj = new Object();
                        obj["rs"] = v.InOrOutCount;
                        obj["hb"] = v.InOrOutFltNo;
                        /*if(flightInfoListObj[v.InOrOutFltNo]){
                            let sta = sortFlt.sta;
                            sta = sta.replace(/-/g, "/");
                            let staDate = new Date(sta);
                            let std = flightInfoListObj[v.InOrOutFltNo][0].std;
                            std = std.replace(/-/g, "/");
                            let stdDate = new Date(std);

                            if (stdDate < staDate) {
                                obj["gjsj"] = "";
                            } else {
                                let diff = stdDate.getTime() - staDate.getTime();
                                //计算出相差天数
                                var days = Math.floor(diff / (24 * 3600 * 1000));

                                //计算出小时数
                                var leave1 = diff % (24 * 3600 * 1000); //计算天数后剩余的毫秒数
                                var hours = Math.floor(leave1 / (3600 * 1000));
                                //计算相差分钟数
                                var leave2 = leave1 % (3600 * 1000);     //计算小时数后剩余的毫秒数
                                var minutes = Math.floor(leave2 / (60 * 1000));

                                let minutesTotal = minutes + (hours * 60) + (days * 60 * 24);
                                obj["gjsj"] = minutesTotal + "分钟";

                            }
                        } else {
                            obj["gjsj"] = "";
                        }*/
                        if (v.InOutTime)
                        	obj["gjsj"] = v.InOutTime + "分钟";
                        else {
                            obj["gjsj"] = "";
                        }
                        items.push(obj);
                    });
                    console.log("tablesItems", items);
                    this.tableScroll(items);
                }
            }.bind(this),
            error: function (response) {
            }
        });
    }

    //风险因子
    setNode(sortFlt) {
        let depparam = {
            "datop": sortFlt.datop,
            // "datop": "2019-09-13",
            "fltId": sortFlt.flightId,
            "flightDate": sortFlt.datopChn,
            // "flightDate": "2019-09-13",
            "depstn": sortFlt.depStn,
            "arrstn": sortFlt.arrStn,
            "riskType": "DEP",
        };
        $.ajax({
            type: 'post',
            url: "/bi/web/omphorcsgetnode",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(depparam),
            success: function (response) {
                console.log("depNode", response.data);
                if (response != undefined && response.data.length > 0) {
                    let nodeValue = "";
                    let plane = 0;
                    let env = 0;
                    let crew = 0;
                    let hasNodeValue = false;
                    response.data.forEach((v, i) => {
                        if (Number(v.nodeRisk) > 5) {
                            if (v.nodeValue != undefined && v.nodeValue != "") hasNodeValue = true;
                            nodeValue += v.nodeValue + ";";
                        }
                        if (v.srcId == "202") {
                            plane = Number(v.nodeRisk);
                            $("#node" + v.srcId + "_1").text(v.nodeRisk);
                            $("#node" + v.srcId + "_2").text(v.nodeRisk);
                        } else {
                            $("#node" + v.srcId).text(v.nodeRisk);
                        }
                        if (v.srcId == "201") crew = Number(v.nodeRisk);
                        if (v.srcId == "203") env = Number(v.nodeRisk);
                    });
                    this.leftPie(plane, env, crew);
                    $("#depNode").text(hasNodeValue ? nodeValue : "无风险预警");
                    var $depNode = $("#depNode");
                    if ( $("#depNode").height() > 50) {
                        var html = $depNode.get(0).outerHTML;
                        html = html.replace(/^<span/,"<marquee");
                        html = html.replace(/\/span>$/,"/marquee>");
                        $depNode.replaceWith(html);
                    } else {
                        var html = $depNode.get(0).outerHTML;
                        html = html.replace(/^<marquee/,"<span");
                        html = html.replace(/\/marquee>$/,"/span>");
                        $depNode.replaceWith(html);
                    }
                }


            }.bind(this),
            error: function (response) {
            }
        });

        let arrparam = {
            "datop": sortFlt.datop,
            // "datop": "2019-09-13",
            "fltId": sortFlt.flightId,
            "flightDate": sortFlt.datopChn,
            // "flightDate": "2019-09-13",
            "depstn": sortFlt.depStn,
            "arrstn": sortFlt.arrStn,
            "riskType": "ARR",
        };
        $.ajax({
            type: 'post',
            url: "/bi/web/omphorcsgetnode",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(arrparam),
            success: function (response) {
                console.log("arrNode", response.data);
                if (response != undefined && response.data.length > 0) {
                    let nodeValue = "";
                    let plane = 0;
                    let env = 0;
                    let crew = 0;
                    let hasNodeValue = false;
                    response.data.forEach((v, i) => {
                        if (Number(v.nodeRisk) > 5) {
                            if (v.nodeValue != undefined && v.nodeValue != "") hasNodeValue = true;
                            nodeValue += v.nodeValue + ";";
                        }
                        if (v.srcId == "3") {
                            plane = Number(v.nodeRisk);
                            $("#node" + v.srcId + "_1").text(v.nodeRisk);
                            $("#node" + v.srcId + "_2").text(v.nodeRisk);
                        } else {
                            $("#node" + v.srcId).text(v.nodeRisk);
                        }
                        if (v.srcId == "2") crew = Number(v.nodeRisk);
                        if (v.srcId == "4") env = Number(v.nodeRisk);
                    });
                    this.rightPie(plane, env, crew);
                    $("#arrNode").text(hasNodeValue ? nodeValue : "无风险预警");
                    var $arrNode = $("#arrNode");
                    if ( $("#arrNode").height() > 50) {
                        var html = $arrNode .get(0).outerHTML;
                        html = html.replace(/^<span/,"<marquee");
                        html = html.replace(/\/span>$/,"/marquee>");
                        $arrNode .replaceWith(html);
                    } else {
                        var html = $arrNode .get(0).outerHTML;
                        html = html.replace(/^<marquee/,"<span");
                        html = html.replace(/\/marquee>$/,"/span>");
                        $arrNode .replaceWith(html);
                    }
                }

            }.bind(this),
            error: function (response) {
            }
        });
    }

    //缓解措施
    setRiskMark(sortFlt) {
        //起飞
        let depparam = {
            "datop": sortFlt.datop,
            "fltId": sortFlt.flightId,
            "riskPhase": "1",
        };
        $.ajax({
            type: 'post',
            url: "/bi/web/omphorcsgetflighthighriskremark",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(depparam),
            success: function (response) {
                console.log("riskMark", response.data);
                if (response != undefined && response.data.length > 0) {
                    let risklist = response.data;
                    let maxInsertTime = new Object();
                    let currentTime = "";
                    risklist.forEach((v, i) => {
                        if (currentTime == "") {
                            currentTime = v.insertTime;
                            maxInsertTime = v;
                        } else if (v.insertTime > currentTime) {
                            currentTime = v.insertTime;
                            maxInsertTime = v;
                        }
                    });

                    let param = {
                        "MEASURE_CODE": maxInsertTime.riskRemark
                    };

                    $.ajax({
                        type: 'post',
                        url: "/bi/web/horcsriskmeasure",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(param),
                        success: function (response) {
                            if (response != undefined && response.data.length > 0) {
                                console.log("horcsriskmeasure", response.data);
                                response.data[0].MEASURE_CONTENT == "" ? $("#depRiskMark").text(maxInsertTime.riskRemark) : $("#depRiskMark").text(response.data[0].MEASURE_CONTENT);
                            } else {
                                $("#depRiskMark").text(maxInsertTime.riskRemark);
                            }
                        },
                        error: function (response) {
                        }
                    });
                }
            },
            error: function (response) {
            }
        });

        //着陆
        let arrparam = {
            "datop": sortFlt.datop,
            // "datop": "2019-09-13",
            "fltId": sortFlt.flightId,
            "riskPhase": "3",
        };
        $.ajax({
            type: 'post',
            url: "/bi/web/omphorcsgetflighthighriskremark",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(arrparam),
            success: function (response) {
                console.log("riskMark", response.data);
                if (response != undefined && response.data.length > 0) {
                    let risklist = response.data;
                    let maxInsertTime = new Object();
                    let currentTime = "";
                    risklist.forEach((v, i) => {
                        if (currentTime == "") {
                            currentTime = v.insertTime;
                            maxInsertTime = v;
                        } else if (v.insertTime > currentTime) {
                            currentTime = v.insertTime;
                            maxInsertTime = v;
                        }
                    });

                    let param = {
                        "MEASURE_CODE": maxInsertTime.riskRemark
                    };

                    $.ajax({
                        type: 'post',
                        url: "/bi/web/horcsriskmeasure",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(param),
                        success: function (response) {
                            if (response != undefined && response.data.length > 0) {
                                console.log("horcsriskmeasure", response.data);
                                response.data[0].MEASURE_CONTENT == "" ? $("#arrRiskMark").text(maxInsertTime.riskRemark) : $("#arrRiskMark").text(response.data[0].MEASURE_CONTENT);
                            } else {
                                $("#arrRiskMark").text(maxInsertTime.riskRemark);
                            }
                        },
                        error: function (response) {
                        }
                    });
                }
            },
            error: function (response) {
            }
        });
    }

    setCabin(acno) {
        return new Promise((resolve, reject) => {
            let param = {
                "acNo": acno,
            };
            $.ajax({
                type: 'post',
                url: "/bi/web/getAcAircraftList",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    let ac = response.data[0].data;
                    cabin = ac.cabin; // 座舱布局
                    let acType = ac.acType; // 机型
                    // 座舱布局图
                    cabin = cabin.toUpperCase();
                    $('.silosImg').removeClass('CY');
                    $('.silosImg').removeClass('Y');
                    $('.silosImg').removeClass('w_CY');
                    $('.silosImg').removeClass('W_Y');
                    if (acType.indexOf('787') > -1 || acType.indexOf('767') > -1 || acType.indexOf('777') > -1 || acType.indexOf('330') > -1) {
                        if (cabin.indexOf('C') > -1 && cabin.indexOf('Y') > -1) {
                            $('.silosImg').addClass('W_CY');
                        } else {
                            $('.silosImg').addClass('W_Y');
                        }
                    } else {
                        if (cabin.indexOf('C') > -1 && cabin.indexOf('Y') > -1) {
                            $('.silosImg').addClass('CY');
                        } else {
                            $('.silosImg').addClass('Y');
                        }
                    }
                    $('.silosImg .actype').text(acType);
                    $('.silosImg .lb').text(cabin);
                    $('#oil_2').text((ac.mfLb / 1000).toFixed(1) + 't');
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });

    }

    setCrew() {
        let data = crewData;
        // 机长
        let names = data.captain.replace(/\d+/g, '').split('@');
        for (let i = names.length - 1; i >= 0; i--) {
            if (names[i].indexOf('(') > -1) {
                let aaa = names[i].split('(');
                if (aaa.length > 1) {
                    names[i] = aaa[0];
                }
            }
        }
        $('.captain').text(names.join(','));

        // 副驾
        if (data.firstVice1) {
            let names = data.firstVice1.replace(/\d+/g, '').split('@'); // 删除数字
            for (let i = names.length - 1; i >= 0; i--) {
                if (names[i].indexOf('(') > -1) {
                    let aaa = names[i].split('(');
                    if (aaa.length > 1) {
                        names[i] = aaa[0];
                    }
                }
            }
            $('.firstVice1').text(names.join(','));
        }
        // 乘务员
        let crew = data.crwStewardInf;
        if (crew) {
            crew = crew.replace(/\d+/g, ''); // 删除数字
            let arr = crew.split('；');
            let names = [];
            for (let i in arr) {
                let t = arr[i];
                let n = t.split(':')[0];
                names.push(n);
            }
            let steward = names.splice(0, 6);
            let steward_all = names;
            $('.crwStewardInf').text(steward.join(',')); //乘务员
        }

        // 安全员
        if (data.safer1) {
            let crew = data.safer1.replace(/\d+/g, ''); // 删除数字
            let safer = crew.split('/');
            safer = safer.splice(0, 2);
            $('.safer1').text(safer.join(',')); //安全员
        }

    }

    setNormalRate() {
        if (flt_kpi_data && company_kpi_data) {
            var dd = flt_kpi_data[companyCode2Id[comp_code]]['SCH_NO']['M'][flightNo]['date'];
            var flt_sch = 0;
            if (dd) {
                flt_sch = dd[Object.keys(dd)[0]];
            }

            var dd = flt_kpi_data[companyCode2Id[comp_code]]['NORMAL_NO_T']['M'][flightNo]['date'];
            var flt_nor = 0;
            if (dd) {
                flt_nor = dd[Object.keys(dd)[0]];
            }


            var dd = company_kpi_data[comp_code]['SCH_NO']['M'];
            var company_sch = 0;
            if (dd) {
                for (var d in dd) {
                    var company_sch = dd[d];
                }
            }

            var dd = company_kpi_data[comp_code]['NORMAL_NO_T']['M'];
            var company_nor = 0;
            if (dd) {
                for (var d in dd) {
                    var company_nor = dd[d];
                }
            }

            var flt_rate = flt_sch > 0 ? (flt_nor / flt_sch) : 0
            var company_rate = company_sch > 0 ? (company_nor / company_sch) : 0

            $('#cvs_chart1_lb1').text(Math.round(flt_rate * 1000) / 10 + '%');
            $('#cvs_chart1_lb2').text(Math.round(company_rate * 1000) / 10 + '%');

            if (flt_rate > company_rate) {
                $('#cvs_chart1_lb1').addClass('green')
            } else {
                $('#cvs_chart1_lb1').addClass('red')
            }

            this.drawFPRGauge(1, company_rate, flt_rate);


        }
    }

    setFltInfo(flt) {
        if (airportList == undefined) {
            return;
        }
        if (flt.length > 1) {
            $('.city1').text(flt[0].depCity);
            $('.citym').text(flt[0].arrCity);
            $('.city2').text(flt[1].arrCity);
            depAirport = airportList[flt[0].depStn]; //出发机场信息
            middleAirport = airportList[flt[0].arrStn] // 中转机场信息
            arrAirport = airportList[flt[1].arrStn]; //到达机场信息
            $('.fltsts').removeClass('status1');
            $('.fltsts').removeClass('status1');
            // 航段一
            if (flt[0].status == "ATA") {
                $(".leg1").addClass('flightStatus3');
                $('.leg1Status').text(statusMap[flt[0].status]);
                this.setPlaneInfoAndWeather(flt[1]);
                this.setNode(flt[1]);
                this.setRiskMark(flt[1]);
                this.setHorcsAlarmOilFrcfp(flt[1]);
                this.setInOutCount(flt[1]);
                this.setPlanRoute(flt[1]);
                // $('.fltsts').hide();
            } else if (flt[0].delay1 != '' && flt[0].dur1 > 0) {
                $('.leg1').addClass('flightStatus2');
                $('.leg1Status').text('晚点');
                // $('.fltsts').hide();
                $('.fltsts').addClass('status2');
                $('.fltsts').text('晚点');
                this.setPlaneInfoAndWeather(flt[0]);
                this.setNode(flt[0]);
                this.setRiskMark(flt[0]);
                this.setHorcsAlarmOilFrcfp(flt[0]);
                this.setInOutCount(flt[0]);
                this.setPlanRoute(flt[0]);
            } else if (flt[0].status != "ATA") {
                $('.fltsts').addClass('status1');
                // $('.fltsts').hide();
                flt[0].status == "SCH" ? $('.leg1').addClass('flightStatus3') : $('.leg1').addClass('flightStatus1');
                $('.leg1Status').text(statusMap[flt[0].status]);
                $('.fltsts').text(statusMap[flt[0].status]);
                this.setPlaneInfoAndWeather(flt[0]);
                this.setNode(flt[0]);
                this.setRiskMark(flt[0]);
                this.setHorcsAlarmOilFrcfp(flt[0]);
                this.setInOutCount(flt[0]);
                this.setPlanRoute(flt[0]);
            }
            // 航段二
            if (flt[1].status == "ATA") {
                $(".leg2").addClass('flightStatus3');
                $('.leg2Status').text(statusMap[flt[1].status]);
                $('.fltsts').hide();
            } else if (flt[1].delay1 != '' && flt[1].dur1 > 0) {
                $('.leg2').addClass('flightStatus2');
                $('.leg2Status').text('晚点');
                // $('.fltsts').hide();
                if (flt[0].status == "ATA") {
                    $('.fltsts').addClass('status2');
                    $('.fltsts').text('晚点');
                }
            } else if (flt[1].status != "ATA") {
                flt[1].status == "SCH" ? $('.leg2').addClass('flightStatus3') : $('.leg2').addClass('flightStatus1');
                $('.leg2Status').text(statusMap[flt[1].status]);
                // $('.fltsts').hide();
                if (flt[0].status == "ATA") {
                    $('.fltsts').addClass('status1');
                    $('.fltsts').text(statusMap[flt[1].status]);
                }
            }

        }
        if (flt.length != 2) {
            $('.city1').text(flt[0].depCity);
            $('.citym').hide();
            $('.city2').text(flt[0].arrCity);
            depAirport = airportList[flt[0].depStn]; //出发机场信息
            // middleAirport = airportList[flt[0].arrStn] // 中转机场信息
            arrAirport = airportList[flt[0].arrStn]; //到达机场信息
            $('.fltsts').removeClass('status1');
            $('.fltsts').removeClass('status1');
            $(".leg1").hide();
            $(".leg2").hide();
            $(".jingting").hide();
            // 航段一
            if (flt[0].delay1 != '' && flt[0].dur1 > 0) {
                $('.fltsts').addClass('status2');
                $('.fltsts').text('晚点');
            } else {
                $('.fltsts').addClass('status1');
                $('.fltsts').text(statusMap[flt[0].status]);
            }
            this.setPlaneInfoAndWeather(flt[0]);
            this.setNode(flt[0]);
            this.setRiskMark(flt[0]);
            this.setHorcsAlarmOilFrcfp(flt[0]);
            this.setInOutCount(flt[0]);
            this.setPlanRoute(flt[0]);
        }
    }

    setMapData() {
        return new Promise((resolve, reject) => {
            let series = [];
            // 飞机位置
            let planes = planeLocationList;
            let flightList = flightInfoListObj;
            let len = planes.length;
            if (len == 0) {
                reject("fail");
            }
            let acdat;
            for (let i = 0; i < len; i++) {
                var ac = planes[i];
                if (ac.fltno == flightNo) {
                    acdat = ac;
                    if ($('.oil_rt') != undefined) {
                        if (!isNaN(ac.oil)) {
                            curOil = Math.round(ac.oil);
                            $('#oil_rt1').text((Math.round(ac.oil) / 1000).toFixed(1) + 't');
                            $('#oil_rt2').text((Math.round(ac.oil) / 1000).toFixed(1) + 't');
                        } else {
                            curOil = 0;
                            $('#oil_rt1').text('--');
                            $('#oil_rt2').text('--');
                        }
                    }
                }
            }
            let flt;
            let depAirport = ""; //出发机场信息
            let middleAirport = "";
            let arrAirport = ""; //到达机场信息
            if (flightList[flightNo].length > 1) {
                if (flightList[flightNo][0].status == "ATA") {
                    flt = flightList[flightNo][1];
                } else {
                    flt = flightList[flightNo][0];
                }
                depAirport = airportList[flightList[flightNo][0].depStn]; //出发机场信息
                middleAirport = airportList[flightList[flightNo][1].depStn]
                arrAirport = airportList[flightList[flightNo][1].arrStn]; //到达机场信息
            } else {
                flt = flightList[flightNo][0];
                depAirport = airportList[flightList[flightNo][0].depStn]; //出发机场信息
                arrAirport = airportList[flightList[flightNo][0].arrStn]; //到达机场信息
            }

            // 飞行轨迹
            let data = [];
            for(var i= pointlist.length-1; i>=0; i--){
                var d = pointlist[i];
                d.UTC = d.UTC.replace(/\D/g,'')
            }

            // 国内航线
            if (flt.fltType != 'I') {
                // 删除相同时间的坐标点
                let idx = 0;
                for (let i = 0; i < pointlist.length; i++) {
                    pointlist[i].idx = idx;
                    idx++;
                    let d = pointlist[i];
                    for (let j = pointlist.length - 1; j >= 0; j--) {
                        let d2 = pointlist[j];
                        if (d.UTC == d2.UTC && d.idx != d2.idx) {
                            pointlist.splice(j, 1);
                        }
                    }
                }

                // 国内航线删除坐标带E，W，N，S的坐标，避免两个MQ出现航路偏差
                for (let i = pointlist.length - 1; i >= 0; i--) {
                    let d = pointlist[i];
                    if (isNaN(d.LAT) || isNaN(d.LON)) {
                        pointlist.splice(i, 1);
                    }
                }
            }

            // 计划出发时间
            let date = new Date();
            let stdChnTM = parserDate(flt.stdChn); // 计划出发时间
            let ts_dep = stdChnTM.getTime() - (8 * 60 * 60 * 1000);
            date.setTime(ts_dep);
            let mm = date.getMonth() + 1;
            let dd = date.getDate();
            let h = date.getHours();
            let m = date.getMinutes();
            let s = date.getSeconds();
            if (mm < 10) {
                mm = '0' + mm;
            }
            if (dd < 10) {
                dd = '0' + dd;
            }
            if (h < 10) {
                h = '0' + h;
            }
            if (m < 10) {
                m = '0' + m;
            }
            if (s < 10) {
                s = '0' + s;
            }
            let utc_dep = date.getFullYear() + '' + mm + '' + dd + '' + h + '' + m + '' + s; // 现在时间


            date = new Date();
            let ts = date.getTime() - 86400000;
            let ts_now = date.getTime() - (8 * 60 * 60 * 1000);
            date.setTime(ts);
            mm = date.getMonth() + 1;
            dd = date.getDate();
            if (mm < 10) {
                mm = '0' + mm;
            }
            if (dd < 10) {
                dd = '0' + dd;
            }
            let utc_time = date.getFullYear() + '' + mm + '' + dd + '210000'; // UTC 昨天 21点，北京时间今天早上5点
            date.setTime(ts_now);
            mm = date.getMonth() + 1;
            dd = date.getDate();
            h = date.getHours();
            m = date.getMinutes();
            s = date.getSeconds();
            if (mm < 10) {
                mm = '0' + mm;
            }
            if (dd < 10) {
                dd = '0' + dd;
            }
            if (h < 10) {
                h = '0' + h;
            }
            if (m < 10) {
                m = '0' + m;
            }
            if (s < 10) {
                s = '0' + s;
            }
            let utc_now = date.getFullYear() + '' + mm + '' + dd + '' + h + '' + m + '' + s; // 现在时间

            for (let i = pointlist.length - 1; i >= 0; i--) {
                // 删除昨天的航迹坐标
                var d = pointlist[i];
                if (d.UTC < utc_time) {
                    pointlist.splice(i, 1);
                }
            }

            pointlist.sort(function (a, b) {
                return Number(b.UTC) - Number(a.UTC);
            });

            if (pointlist && pointlist.length > 1) {
                len = pointlist.length;
                for (let i = 1; i < len; i++) {
                    let p1 = pointlist[i - 1];
                    let p2 = pointlist[i];
                    let lon = formatLonLat(p1.LON);
                    let lat = formatLonLat(p1.LAT);
                    let lon2 = formatLonLat(p2.LON);
                    let lat2 = formatLonLat(p2.LAT);

                    data.push({
                        fromName: '',
                        toName: '',
                        coords: [
                            [lon, lat],
                            [lon2, lat2]
                        ]
                    });
                }
                series.push({
                    name: 'lines',
                    type: 'lines',
                    coordinateSystem: 'geo',
                    zlevel: 1,
                    silent: false, //不响应鼠标点击或事件
                    polyline: true, //支持多点连线
                    effect: {
                        show: false //关闭特效
                    },
                    tooltip: {
                        show: false
                    },
                    lineStyle: {
                        normal: {
                            color: 'white',
                            width: 1.5,
                            opacity: 0.9,
                            curveness: 0,
                            // type: 'dashed'

                        }
                    },
                    label: {
                        normal: {
                            show: false,
                        }
                    },
                    data: data
                });
            }

            ////// 设置小飞机图标。 城市位置
            let symbol = '';

            if (flt.status == 'DEP' && flt.delay1 == '') {
                symbol = 'image://img/flight.legend_1.png';
            } else {
                symbol = 'image://img/flight.legend_2.png'; // 延误航班
            }

            if (pointlist.length > 1) {
                var ac_lon = formatLonLat(pointlist[0].LON);
                var ac_lat = formatLonLat(pointlist[0].LAT);
                let ac_lon1 = formatLonLat(pointlist[1].LON);
                let ac_lat1 = formatLonLat(pointlist[1].LAT);
                var vec = getGeoAngle(ac_lat, ac_lon, ac_lat1, ac_lon1);
            } else {
                var ac_lon = acdat.lon;
                var ac_lat = acdat.lat;
                var vec = acdat.vec;
            }

            if (flightList[flightNo].length > 1) {
                series.push({
                    name: 'scatter',
                    type: 'effectScatter',
                    showEffectOn: 'render',
                    coordinateSystem: 'geo',
                    zlevel: 2,

                    data: [{
                        name: flightList[flightNo][0].depCity,
                        isAirline: true,
                        isCity: true,
                        cityType: 'dep',
                        value: [depAirport.longitude, depAirport.latitude],
                        symbol: 'circle',
                        symbolSize: 7,
                        itemStyle: {
                            normal: {
                                color: '#3d7dd0',
                                borderColor: '#ffffff',
                                borderWidth: 2,
                            }
                        },
                        label: {
                            normal: {
                                show: true,
                                formatter: '{b}',
                                offset: [0, -16],
                                textStyle: {
                                    fontSize: 12,
                                    color: '#FFFFFF',
                                }
                            }
                        }
                    }, {
                        name: flightList[flightNo][0].arrCity,
                        isAirline: true,
                        isCity: true,
                        cityType: 'dep',
                        value: [middleAirport.longitude, middleAirport.latitude],
                        symbol: 'circle',
                        symbolSize: 7,
                        itemStyle: {
                            normal: {
                                color: '#3d7dd0',
                                borderColor: '#ffffff',
                                borderWidth: 2,
                            }
                        },
                        label: {
                            normal: {
                                show: true,
                                formatter: '{b}',
                                offset: [0, -16],
                                textStyle: {
                                    fontSize: 12,
                                    color: '#FFFFFF',
                                }
                            }
                        }
                    }, {
                        name: flightList[flightNo][1].arrCity,
                        isAirline: true,
                        isCity: true,
                        cityType: 'arr',
                        value: [arrAirport.longitude, arrAirport.latitude],
                        symbol: 'circle',
                        symbolSize: 7,
                        itemStyle: {
                            normal: {
                                color: '#3d7dd0',
                                borderColor: '#ffffff',
                                borderWidth: 2,
                            }
                        },
                        label: {
                            normal: {
                                show: true,
                                formatter: '{b}',
                                offset: [0, -16],
                                textStyle: {
                                    fontSize: 12,
                                    color: '#FFFFFF',
                                }
                            }
                        }
                    }, {
                        name: acdat.fltno,
                        acno: acdat.acno,
                        oil: acdat.oil,
                        flt: flt,
                        isAirline: true,
                        value: [ac_lon, ac_lat],
                        symbol: symbol,
                        symbolSize: 22,
                        label: {
                            normal: {
                                show: false,
                            }
                        },
                        symbolRotate: -vec + 90, //-acdat.vec+90, //航向   正北是0°，顺时针，0到360° // 因为图片机头指向右，所以需要逆时针转90度
                    }]
                });
            } else {
                series.push({
                    name: 'scatter',
                    type: 'effectScatter',
                    coordinateSystem: 'geo',
                    showEffectOn: 'render',
                    zlevel: 2,
                    data: [{
                        name: flt.depCity,
                        isAirline: true,
                        isCity: true,
                        cityType: 'dep',
                        value: [depAirport.longitude, depAirport.latitude],
                        symbol: 'circle',
                        symbolSize: 7,
                        itemStyle: {
                            normal: {
                                color: '#3d7dd0',
                                borderColor: '#ffffff',
                                borderWidth: 2,
                            }
                        },
                        label: {
                            normal: {
                                show: true,
                                formatter: '{b}',
                                offset: [0, -16],
                                textStyle: {
                                    fontSize: 12,
                                    color: '#FFFFFF',
                                }
                            }
                        }
                    }, {
                        name: flt.arrCity,
                        isAirline: true,
                        isCity: true,
                        cityType: 'arr',
                        value: [arrAirport.longitude, arrAirport.latitude],
                        symbol: 'circle',
                        symbolSize: 7,
                        itemStyle: {
                            normal: {
                                color: '#3d7dd0',
                                borderColor: '#ffffff',
                                borderWidth: 2,
                            }
                        },
                        label: {
                            normal: {
                                show: true,
                                formatter: '{b}',
                                offset: [0, -16],
                                textStyle: {
                                    fontSize: 12,
                                    color: '#FFFFFF',
                                }
                            }
                        }
                    }, {
                        name: acdat.fltno,
                        acno: acdat.acno,
                        oil: acdat.oil,
                        flt: flt,
                        isAirline: true,
                        value: [ac_lon, ac_lat],
                        symbol: symbol,
                        symbolSize: 12,
                        label: {
                            normal: {
                                show: false,
                            }
                        },
                        symbolRotate: -vec + 90, //-acdat.vec+90, //航向   正北是0°，顺时针，0到360° // 因为图片机头指向右，所以需要逆时针转90度
                    }]
                });
            }
            option = mapBoxEchart.getOption();
            option.series = series;
            mapBoxEchart.setOption(option);
            resolve("success");
        });

    }

    setTrvRate() {
        let arr = cabin.split('Y');
        let seat1 = !isNaN(arr[1]) ? arr[1] : 0;
        let arr2 = arr[0].split('C');
        let seat2 = !isNaN(arr2[1]) ? arr2[1] : 0;
        let seat = Number(seat1) + Number(seat2);

        // 客座率
        $('#cvs_chart2_lb1').removeClass('red');
        $('#cvs_chart2_lb1').removeClass('green');
        $('.trv_rate .down_arr').hide();
        $('.trv_rate .up_arr').hide();
        let psr_rate = seat > 0 ? Math.round(ckiNum / seat * 1000) / 10 : '-';
        if (psr_rate > 100) {
            psr_rate = 100;
        }
        if (isNaN(psr_rate)) {
            psr_rate = 0;
            this.drawPLFGauge(0);
            $('#cvs_chart2_lb1').text('--');
        } else {
            this.drawPLFGauge(psr_rate / 100);
            $('#cvs_chart2_lb1').text(psr_rate + '%');
        }
        let avg_rate = 0;
        let ddd = company_kpi_data[comp_code]['TRV_RATE']['M']
        for (let d in ddd) {
            avg_rate = Number(ddd[d]) * 100;
        }
        if (avg_rate > psr_rate) {
            $('#cvs_chart2_lb1').addClass('red');
            $('.trv_rate .down_arr').show();
            $('.trv_rate_sub').text('低于航司平均');
        } else {
            $('#cvs_chart2_lb1').addClass('green');
            $('.trv_rate .up_arr').show();
            $('.trv_rate_sub').text('高于航司平均');
        }
    }

    setPlaneInfoAndWeather(flt) {
        let atdChn = flt.atdChn; //实际起飞时间（北京时间）
        let ataChn = flt.ataChn; //实际到达时间（北京时间）
        let stdChn = flt.stdChn; //计划出发
        let staChn = flt.staChn; // 中转计划到达
        $('.atd').text(this._trimTime(atdChn));
        $('.ata').text(this._trimTime(ataChn));
        $('.std').text(this._trimTime(stdChn));
        $('.sta').text(this._trimTime(staChn));

        // 到达机场
        let param = {
            'airport': flt.arrStn // 到达机场三字码
        };

        $.ajax({
            type: 'post',
            url: "/bi/web/7x2_arp_weather",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {
                if (Number(response.errorcode) == 0) {
                    weather[flt.arrCity] = response;
                    this.setWeather(2, response);
                }

            }.bind(this),
            error: function (jqXHR, txtStatus, errorThrown) {
            }
        });
        // 出发机场
        param = {
            'airport': flt.depStn // 出发机场三字码
        }
        $.ajax({
            type: 'post',
            url: "/bi/web/7x2_arp_weather",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {
                if (Number(response.errorcode) == 0) {
                    weather[flt.depCity] = response;
                    this.setWeather(1, response);
                }
            }.bind(this),
            error: function (jqXHR, txtStatus, errorThrown) {
            }
        });

    }

    setWeather(id, response) {
        let weatherInfoTxt = response.weatherInfoTxt ? response.weatherInfoTxt.replace(/<[^>]+>/g, "") : "--";
        let temperature = response.temperature ? isNaN(response.temperature) ? '-' : Number(response.temperature) + '℃' : "--";

        let weather_css = 'icon-e600_sunny';
        for (let wtxt in weather_map) {
            if (weatherInfoTxt.indexOf(wtxt) > -1) {
                weather_css = weather_map[wtxt];
            }
        }

        // 设置天气状况icon
        $('.weather_city' + id + ' .weather_ico span').attr('class', weather_css);
        $('.weather_city' + id + ' .temperature').text(temperature);
        $('.weather_city' + id + ' .condition').text(weatherInfoTxt);
    }

    showOil(id, red, yellow, count) {
        var waterHeight = (100 - count);
        $("#" + id).css("transform", "translateY(" + waterHeight + "%)");

        if (count < red) {
            this.tranColor(id, "red");
        } else if (count < yellow) {
            this.tranColor(id, "yellow");
        } else {
            this.tranColor(id, "blue");
        }

        $("." + id).find(".lin_1").css("position", "absolute");
        $("." + id).find(".lin_1").css("top", (100 - yellow) + "px");
        $("." + id).find(".lin_2").css("position", "absolute");
        $("." + id).find(".lin_2").css("top", waterHeight + "px");
        $("." + id).find(".lin_3").css("position", "absolute");
        $("." + id).find(".lin_3").css("top", (100 - red) + "px");
    }

    tranColor(id, color) {
        switch (color) {
            case "red":
                $("#" + id + " .water-wave--back").css("fill", "#EDB8C3");
                $("#" + id + " .water-wave--front").css("fill", "#FA555B");
                $("#" + id).css("background", "#FA555B");
                break;

            case "yellow":
                $("#" + id + " .water-wave--back").css("fill", "#EDF3B8");
                $("#" + id + " .water-wave--front").css("fill", "#FAF343");
                $("#" + id).css("background", "#FAF343");
                break;

            case "blue":
                $("#" + id + " .water-wave--back").css("fill", "#A0E1FA");
                $("#" + id + " .water-wave--front").css("fill", "#2EC3F1");
                $("#" + id).css("background", "#2EC3F1");
                break;

            default:
                break;
        }
    }

    leftPie(plane, env, crew) {
        if (plane > 5 && plane <= 8) {
            leftSeries[0].color.push(yellow);
            $(".col_left .con_left_pie .plane-wrapper .planeCell1").css("color", yellow);
            $(".col_left .con_left_pie .plane-wrapper").css("background", "url(" + imgPlane2 + ") no-repeat 0 0");
        } else if (plane > 8) {
            leftSeries[0].color.push(red);
            $(".col_left .con_left_pie .plane-wrapper .planeCell1").css("color", red);
            $(".col_left .con_left_pie .plane-wrapper").css("background", "url(" + imgPlane3 + ") no-repeat 0 0");
        } else {
            leftSeries[0].color.push(grey);
            $(".col_left .con_left_pie .plane-wrapper ").css("background", "url(" + imgPlane1 + ") no-repeat 0 0");
        }
        if (env > 5 && env <= 8) {
            leftSeries[0].color.push(yellow);
            $(".col_left .con_left_pie .ambient-wrapper .ambientCell1").css("color", yellow);
            $(".col_left .con_left_pie .ambient-wrapper .ambientCell2").css("color", yellow);
            $(".col_left .con_left_pie .ambient-wrapper .ambientCell3").css("color", yellow);
            $(".col_left .con_left_pie .ambient-wrapper").css("background", "url(" + imgAmbient2 + ") no-repeat 0 0");
        } else if (env > 8) {
            leftSeries[0].color.push(red);
            $(".col_left .con_left_pie .ambient-wrapper .ambientCell1").css("color", red);
            $(".col_left .con_left_pie .ambient-wrapper .ambientCell2").css("color", red);
            $(".col_left .con_left_pie .ambient-wrapper .ambientCell3").css("color", red);
            $(".col_left .con_left_pie .ambient-wrapper").css("background", "url(" + imgAmbient3 + ") no-repeat 0 0");
        } else {
            leftSeries[0].color.push(grey);
            $(".col_left .con_left_pie .ambient-wrapper").css("background", "url(" + imgAmbient1 + ") no-repeat 0 0");
        }
        if (crew > 5 && crew <= 8) {
            leftSeries[0].color.push(yellow);
            $(".col_left .con_left_pie .crew-wrapper .crewCell1").css("color", yellow);
            $(".col_left .con_left_pie .crew-wrapper .crewCell2").css("color", yellow);
            $(".col_left .con_left_pie .crew-wrapper .crewCell3").css("color", yellow);
            $(".col_left .con_left_pie .crew-wrapper").css("background", "url(" + imgCrew2 + ") no-repeat 0 0");
        } else if (crew > 8) {
            leftSeries[0].color.push(red);
            $(".col_left .con_left_pie .crew-wrapper .crewCell1").css("color", red);
            $(".col_left .con_left_pie .crew-wrapper .crewCell2").css("color", red);
            $(".col_left .con_left_pie .crew-wrapper .crewCell3").css("color", red);
            $(".col_left .con_left_pie .crew-wrapper").css("background", "url(" + imgCrew3 + ") no-repeat 0 0");
        } else {
            leftSeries[0].color.push(grey);
            $(".col_left .con_left_pie .crew-wrapper").css("background", "url(" + imgCrew1 + ") no-repeat 0 0");
        }
        // leftSeries[0].data.push({value : plane});
        // leftSeries[0].data.push({value : env});
        // leftSeries[0].data.push({value : crew});

        var leftOption = {
            series: leftSeries
        };
        leftpie.setOption(leftOption, true);
    }

    rightPie(plane, env, crew) {

        if (plane > 5 && plane <= 8) {
            rightSeries[0].color.push(yellow);
            $(".col_right .con_right_pie .plane-wrapper .planeCell1").css("color", yellow);
            $(".col_right .con_right_pie .plane-wrapper").css("background", "url(" + imgPlane2 + ") no-repeat 0 0");
        } else if (plane > 8) {
            rightSeries[0].color.push(red);
            $(".col_right .con_right_pie .plane-wrapper .planeCell1").css("color", red);
            $(".col_right .con_right_pie .plane-wrapper").css("background", "url(" + imgPlane3 + ") no-repeat 0 0");
        } else {
            rightSeries[0].color.push(grey);
            $(".col_right .con_right_pie .plane-wrapper ").css("background", "url(" + imgPlane1 + ") no-repeat 0 0");
        }
        if (env > 5 && env <= 8) {
            rightSeries[0].color.push(yellow);
            $(".col_right .con_right_pie .ambient-wrapper .ambientCell1").css("color", yellow);
            $(".col_right .con_right_pie .ambient-wrapper .ambientCell2").css("color", yellow);
            $(".col_right .con_right_pie .ambient-wrapper .ambientCell3").css("color", yellow);
            $(".col_right .con_right_pie .ambient-wrapper").css("background", "url(" + imgAmbient2_2 + ") no-repeat 0 0");
        } else if (env > 8) {
            rightSeries[0].color.push(red);
            $(".col_right .con_right_pie .ambient-wrapper .ambientCell1").css("color", red);
            $(".col_right .con_right_pie .ambient-wrapper .ambientCell2").css("color", red);
            $(".col_right .con_right_pie .ambient-wrapper .ambientCell3").css("color", red);
            $(".col_right .con_right_pie .ambient-wrapper").css("background", "url(" + imgAmbient3_2 + ") no-repeat 0 0");
        } else {
            rightSeries[0].color.push(grey);
            $(".col_right .con_right_pie .ambient-wrapper").css("background", "url(" + imgAmbient1_2 + ") no-repeat 0 0");
        }
        if (crew > 5 && crew <= 8) {
            rightSeries[0].color.push(yellow);
            $(".col_right .con_right_pie .crew-wrapper .crewCell1").css("color", yellow);
            $(".col_right .con_right_pie .crew-wrapper .crewCell2").css("color", yellow);
            $(".col_right .con_right_pie .crew-wrapper .crewCell3").css("color", yellow);
            $(".col_right .con_right_pie .crew-wrapper").css("background", "url(" + imgCrew2 + ") no-repeat 0 0");
        } else if (crew > 8) {
            rightSeries[0].color.push(red);
            $(".col_right .con_right_pie .crew-wrapper .crewCell1").css("color", red);
            $(".col_right .con_right_pie .crew-wrapper .crewCell2").css("color", red);
            $(".col_right .con_right_pie .crew-wrapper .crewCell3").css("color", red);
            $(".col_right .con_right_pie .crew-wrapper").css("background", "url(" + imgCrew3 + ") no-repeat 0 0");
        } else {
            rightSeries[0].color.push(grey);
            $(".col_right .con_right_pie .crew-wrapper").css("background", "url(" + imgCrew1 + ") no-repeat 0 0");
        }
        // rightSeries[0].data.push({value : plane});
        // rightSeries[0].data.push({value : env});
        // rightSeries[0].data.push({value : crew});

        var rightOption = {
            series: rightSeries
        };
        rightpie.setOption(rightOption, true);

    }

    //正常率
    drawFPRGauge(id, rate, rate2) {
        var canvas = document.getElementById('cvs_chart' + id);
        var context = canvas.getContext('2d');
        context.clearRect(0, 0, canvas.width, canvas.height);
        var x = canvas.width / 2;
        var y = canvas.height / 2;

        // draw back
        var radius = 60;
        var startAngle = Math.PI - Math.PI / 5;
        var endAngle = startAngle + Math.PI + Math.PI / 5 * 2;
        var counterClockwise = false;

        context.beginPath();
        context.arc(x, y, radius, startAngle, endAngle, counterClockwise);
        context.lineWidth = 7;
        context.strokeStyle = '#024491';
        context.stroke();

        // draw overlay 1 ==== 本司本月正常率
        if (rate >= 0) {
            var radius = 44;
            var startAngle2 = startAngle;
            var endAngle2 = startAngle + (endAngle - startAngle) * rate;
            var counterClockwise = false;

            context.beginPath();
            context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
            context.lineWidth = 7;

            // linear gradient
            if (rate < 0.5) {
                var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
            } else if (rate < 0.8) {
                var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
            } else {
                var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, canvas.width / 2);
            }
            color.addColorStop(0, '#193F82');
            color.addColorStop(1, '#FFFFFF');

            context.strokeStyle = color;
            context.stroke();

            // pointer
            var angle = startAngle + (endAngle - startAngle) * (rate);
            $('#cvs_chart' + id + '_pointer').css('transform', 'rotate(' + (angle / Math.PI * 180) + 'deg)');
            $('#cvs_chart' + id + '_pointer').show();
        } else {
            $('#cvs_chart' + id + '_pointer').hide();
        }


        // draw overlay 2 ==== 本航线本月正常率
        if (rate2 >= 0) {
            var radius = 52;
            var startAngle2 = startAngle;
            var endAngle2 = startAngle + (endAngle - startAngle) * rate2;
            var counterClockwise = false;

            context.beginPath();
            context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
            context.lineWidth = 7;

            // linear gradient
            if (rate2 < 0.5) {
                var color = context.createLinearGradient(0, canvas.height, canvas.width * rate2, 0);
            } else if (rate2 < 0.8) {
                var color = context.createLinearGradient(0, canvas.height, canvas.width * rate2, 0);
            } else {
                var color = context.createLinearGradient(0, canvas.height, canvas.width * rate2, canvas.width / 2);
            }
            color.addColorStop(0, '#446295');
            color.addColorStop(1, '#93FF6E');

            context.strokeStyle = color;
            context.stroke();

            // pointer
            var angle = startAngle + (endAngle - startAngle) * (rate2);
            $('#cvs_chart' + id + 'b_pointer').css('transform', 'rotate(' + (angle / Math.PI * 180) + 'deg)');
            $('#cvs_chart' + id + 'b_pointer').show();
        } else {
            $('#cvs_chart' + id + 'b_pointer').hide();
        }


        // draw lines
        var numslice = 10;
        var radius = 48;
        for (var i = 1; i < numslice; i++) {
            context.beginPath();
            var startAngle = Math.PI - Math.PI / 5 + i * ((Math.PI + Math.PI / 5 * 2) / numslice);
            var endAngle = startAngle + Math.PI * 0.01;
            context.arc(x, y, radius, startAngle, endAngle, false);
            context.lineWidth = 16;
            context.strokeStyle = '#041946';
            context.stroke();
        }
    }

    tableScroll(Items) {
        MyMarhq && clearInterval(MyMarhq);
        $('.tbl-body tbody').empty();
        $('.tbl-header tbody').empty();
        var str = '';
        // Items = [
        //     {"rs": "1人", "hb": "HU7123", "gjsj": "60分钟"},
        //     {"rs": "2人", "hb": "HU7123", "gjsj": "60分钟"},
        //     {"rs": "3人", "hb": "HU7123", "gjsj": "60分钟"},
        //     {"rs": "4人", "hb": "HU7123", "gjsj": "60分钟"},
        //     {"rs": "5人", "hb": "HU7123", "gjsj": "60分钟"},
        //     {"rs": "6人", "hb": "HU7123", "gjsj": "60分钟"},
        // ]
        $.each(Items, function (i, item) {
            str = '<tr>' +
                '<td>' + item.rs + '</td>' +
                '<td>' + item.hb + '</td>' +
                '<td>' + item.gjsj + '</td>' +
                '</tr>'

            $('.tbl-body tbody').append(str);
            $('.tbl-header tbody').append(str);
        });

        if (Items.length > 3) {
            $('.tbl-body tbody').html($('.tbl-body tbody').html() + $('.tbl-body tbody').html());
            $('.tbl-body').css('top', '0');
            var tblTop = 0;
            var speedhq = 50; // 数值越大越慢
            var outerHeight = $('.tbl-body tbody').find("tr").outerHeight();

            let Marqueehq = () => {
                if (tblTop <= -outerHeight * Items.length) {
                    tblTop = 0;
                } else {
                    tblTop -= 1;
                }
                $('.tbl-body').css('top', tblTop + 'px');
            }

            MyMarhq = setInterval(Marqueehq, speedhq);

            // 鼠标移上去取消事件
            $(".tbl-header tbody").hover(function () {
                clearInterval(MyMarhq);
            }, function () {
                clearInterval(MyMarhq);
                MyMarhq = setInterval(Marqueehq, speedhq);
            })

        }
    }


    //客座率
    drawPLFGauge(rate) {

        var canvas = document.getElementById('cvs_chart2');
        var context = canvas.getContext('2d');
        context.clearRect(0, 0, canvas.width, canvas.height);
        var x = canvas.width / 2;
        var y = canvas.height / 2;

        var radius = 47;
        var lineWidth = 20;

        // draw back
        var startAngle = Math.PI - Math.PI / 3.6;
        var endAngle = startAngle + Math.PI + Math.PI / 3.6 * 2;
        var counterClockwise = false;

        context.beginPath();
        context.arc(x, y, radius, startAngle, endAngle, counterClockwise);
        context.lineWidth = lineWidth;
        context.strokeStyle = '#00438B';
        context.stroke();


        context.beginPath();
        context.arc(x, y, 33, startAngle, endAngle, counterClockwise);
        context.lineWidth = 1;
        context.strokeStyle = '#2683CA';
        context.stroke();


        // draw overlay
        var startAngle2 = startAngle;
        var endAngle2 = startAngle + (endAngle - startAngle) * rate;
        var counterClockwise = false;

        context.beginPath();
        context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
        context.lineWidth = lineWidth;

        // linear gradient
        if (rate < 0.5) {
            var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
        } else if (rate < 0.8) {
            var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
        } else {
            var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, canvas.width / 2);
        }
        color.addColorStop(0, '#122A61');
        color.addColorStop(1, '#78DAFC');

        context.strokeStyle = color;
        context.stroke();

        // draw head
        var startAngle2 = startAngle + (endAngle - startAngle) * rate - (endAngle - startAngle) * 0.01;
        var endAngle2 = startAngle + (endAngle - startAngle) * rate;
        var counterClockwise = false;

        context.beginPath();
        context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
        context.lineWidth = lineWidth;

        context.strokeStyle = '#FFFFFF';

        context.stroke();

    }

    _initMap() {
        // mapBoxEchart.clear();
        mapBoxEchart.setOption(option);
        window.onresize = mapBoxEchart.resize;
    }

    _initTime() {
        var date = new Date();
        var mm = date.getMonth() + 1;
        var dd = date.getDate();
        if (mm < 10) {
            mm = '0' + mm;
        }
        if (dd < 10) {
            dd = '0' + dd;
        }
        stdStart = date.getFullYear() + '-' + mm + '-' + dd + ' 00:00:00';
        stdEnd = date.getFullYear() + '-' + mm + '-' + dd + ' 23:59:59';
        stdEndUtcTime = date.getFullYear() + '-' + mm + '-' + dd + ' 15:59:59';
        var yesterday_ts = date.getTime() - 86400000;
        date.setTime(yesterday_ts);
        mm = date.getMonth() + 1;
        dd = date.getDate();
        if (mm < 10) {
            mm = '0' + mm;
        }
        if (dd < 10) {
            dd = '0' + dd;
        }
        stdStartUtcTime = date.getFullYear() + '-' + mm + '-' + dd + ' 16:00:00';

        date = new Date();
        var next_ts = date.getTime() + 86400000 * 7;
        date.setTime(next_ts);
        mm = date.getMonth() + 1;
        dd = date.getDate();
        if (mm < 10) {
            mm = '0' + mm;
        }
        if (dd < 10) {
            dd = '0' + dd;
        }
        stdEnd7 = date.getFullYear() + '-' + mm + '-' + dd + ' 23:59:59';
    }

    _getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return '';
    }

    _trimTime(timestr) {
        let arr = timestr.split(' ');
        let arr2 = arr[1].split(':');
        return arr2[0] + ':' + arr2[1];
    }

    _getDistance(lat1, lng1, lat2, lng2) {
        let radLat1 = lat1 * Math.PI / 180.0;
        let radLat2 = lat2 * Math.PI / 180.0;
        let a = radLat1 - radLat2;
        let b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
        let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
            Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
        s = s * 6378.137; // EARTH_RADIUS;
        s = Math.round(s * 10000) / 10000;
        return s;
    }

    _dateFormat(str) {
        let [arr1, arr2, arr3] = str.split('-');
        return arr3.includes(" ") ? arr1 + '年' + arr2 + '月' + arr3.split(" ")[0] + '日' + ' ' + arr3.split(" ")[1] : arr1 + '年' + arr2 + '月' + arr3 + '日';
    }

    _DegreeConvert(gpsStr) {
        gpsStr = gpsStr.toLowerCase();
        gpsStr = gpsStr.replace(/\s+/g, "");
        var tempStrArray = new Array();
        var flag = 1;
        var lastFlag = 0;
        var strLength = gpsStr.length;
        var gpsDu = new Array();
        var gpsDir;
        var tempcount = 0;
        var tempString = "";
        var tempPointFlag = 0;
        if (gpsStr[0] == 'w' || gpsStr[0] == 's') {
            flag = -1;
            lastFlag = 0;
            gpsDir = gpsStr[0];
        } else if (gpsStr[strLength - 1] == 'w' || gpsStr[strLength - 1] == 's') {
            flag = -1;
            lastFlag = 1;
            gpsDir = gpsStr[strLength - 1];
        }
        for (var i = 0; i <= strLength; i++) {
            if (gpsStr[i] >= '0' && gpsStr[i] <= '9') {
                tempString += gpsStr[i];
                continue;
            } else if (gpsStr[i] == '.') {
                tempStrArray[tempcount] = tempString;
                tempString = "";
                tempcount++;
                tempStrArray[tempcount] = '.';
                tempPointFlag = 1;
                tempcount++;
            } else if (tempString.length > 0) {
                tempStrArray[tempcount] = tempString;
                tempString = "";
                tempcount++;
            }
        }
        if (tempPointFlag == 0) {
            var num1 = parseInt(tempStrArray[0], 10);
            var num2 = parseInt(tempStrArray[1], 10);
            var num3 = parseInt(tempStrArray[2], 10);
            console.log(num1 + '  ' + num2 / 60 + ' ' + num3 / (60 * 60));
            gpsDu[1] = num1 + num2 / 60 + num3 / (60 * 60);
            gpsDu[1] = gpsDu[1] * flag;
            gpsDu[0] = gpsDir;
        } else if (tempPointFlag == 1) {
            var num1 = parseInt(tempStrArray[0], 10);
            var num2 = parseFloat(tempStrArray[1] + '.' + tempStrArray[3], 10);
            gpsDu[1] = num1 + num2 / 60;
            gpsDu[1] = gpsDu[1] * flag;
            gpsDu[0] = gpsDir;
        }
        return gpsDu;
    }

    _hideLoad() {
        if ($('#loading_msk').length > 0) {
            hideLoading();
            this._hideLoad();
        }
    }
}

let fl = new flight();
$.when(getCompany()).done(() => {
    fl.loadAll();
    // t = setInterval(function () {
    //     fl.loadAll()
    // }, 5 * 60 * 1000);
});

$(".close-btn").on('click', function (e) {
   window.close()
});
