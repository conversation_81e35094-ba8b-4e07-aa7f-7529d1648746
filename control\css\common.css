body {
  font-family: "Microsoft Yahei", "Helvetica Neue", Helvetica, Arial, sans-serif;
  background: #041337;
  color: #fff;
}

@font-face {
  font-family: 'HelveticaNeueLTCom-MdCn';
  src: url('../../fonts/HelveticaNeueLTCom-MdCn.eot?#iefix') format('embedded-opentype'),  url('../../fonts/HelveticaNeueLTCom-MdCn.woff') format('woff'), url('../../fonts/HelveticaNeueLTCom-MdCn.ttf')  format('truetype'), url('../../fonts/HelveticaNeueLTCom-MdCn.svg#HelveticaNeueLTCom-MdCn') format('svg');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'WzpoGlyph';
  src:
    url('../../fonts/WzpoGlyph.ttf?4zlt2d') format('truetype'),
    url('../../fonts/WzpoGlyph.woff?4zlt2d') format('woff'),
    url('../../fonts/WzpoGlyph.svg?4zlt2d#WzpoGlyph') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'WzpoGlyph' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-e700_plus:before {
  content: "\e700";
}
.icon-e732_cross:before {
  content: "\e732";
}
.icon-e733_check:before {
  content: "\e733";
}
.icon-e730_check:before {
  content: "\e730";
}
.icon-e731_bin:before {
  content: "\e731";
}
.icon-e719_search:before {
  content: "\e719";
}
.icon-e617_dust1:before {
  content: "\e617";
}
.icon-e618_dust2:before {
  content: "\e618";
}
.icon-e619_dust3:before {
  content: "\e619";
}
.icon-e620_hail:before {
  content: "\e620";
}
.icon-e615_fog:before {
  content: "\e615";
}
.icon-e603_cloudy_night:before {
  content: "\e603";
}
.icon-e604_gloomy:before {
  content: "\e604";
}
.icon-e606_rain2:before {
  content: "\e606";
}
.icon-e612_wind:before {
  content: "\e612";
}
.icon-e613_haze:before {
  content: "\e613";
}
.icon-e614_haze_night:before {
  content: "\e614";
}
.icon-e600_sunny:before {
  content: "\e600";
}
.icon-e601_sunny_night:before {
  content: "\e601";
}
.icon-e602_cloudy:before {
  content: "\e602";
}
.icon-e605_rain1:before {
  content: "\e605";
}
.icon-e607_rain3:before {
  content: "\e607";
}
.icon-e608_snow1:before {
  content: "\e608";
}
.icon-e609_snow2:before {
  content: "\e609";
}
.icon-e610_snow3:before {
  content: "\e610";
}
.icon-e611_umbrella:before {
  content: "\e611";
}




/*COLOR*/
.white {
  color: white !important;
}
.blue1 {
  color: #A2DFFF !important;
}
.blue2 {
  color: #5FC5FF !important;
}
.blue3 {
  color: #11529D !important;
}
.blue4 {
  color: #00383B !important;
}
.green {
  color: #A3D900 !important;
}
.green2 {
  color: #00D900 !important;
}
.yellow {
  color: #FFFF00 !important;
}
.orange {
  color: #ff8000 !important;
}
.red {
  color: #FF0000 !important;
}



div {
	position: absolute;
}
.reltv {
	position: relative;
}

canvas {
	position: absolute;
}

.center {
	text-align: center;
}
.left {
	text-align: left;
}
.right {
	text-align: right;
}




.ffnum {
  font-family: "HelveticaNeueLTCom-MdCn";
  color: #fff;
  font-weight: bold;
  padding: 0 2px;
  text-shadow: 0px 0px 18px rgba(255,255,255,0.5);
}

.sub{
	padding-left: 2px;
}


.fs9{
	font-size: 9px;
	text-shadow: 0 0 0 rgba(255,255,255,0) !important;
}
.fs10{
	font-size: 10px;
	text-shadow: 0 0 0 rgba(255,255,255,0) !important;
}
.fs11{
	font-size: 11px;
	text-shadow: 0 0 0 rgba(255,255,255,0) !important;
}
.fs12{
	font-size: 12px;
}
.fs14{
	font-size: 14px;
}
.fs16{
	font-size: 16px;
	font-weight: bold;
}
.fs17{
	font-size: 17px;
	font-weight: bold;
}
.fs18{
	font-size: 18px;
	font-weight: bold;
}
.fs20{
	font-size: 20px;
	font-weight: bold;
}
.fs22{
	font-size: 22px;
	font-weight: bold;
}
.fs28{
	font-size: 28px;
	font-weight: bold;
}
.fs30{
	font-size: 30px;
	font-weight: bold;
}
.fs32{
	font-size: 32px;
	font-weight: bold;
}



.bar {
	position: relative;
	display: inline-block;
	height: 4px;
	vertical-align: middle;
}
.innerbar {
	position: absolute;
	display: block;
	height: 4px;
}
.greenbar {
	background: #A1DA00;
}
.bluebar {
	background: #00AFFD;
}
.darkbar {
	background: #00579E;
}
.baritmrow {
	position: relative;
}






.gradient-ball{
background: -moz-radial-gradient(center, ellipse cover,  rgba(255,255,255,0.8) 14%, rgba(255,255,255,0) 57%, rgba(0,0,0,0.34) 85%); /* FF3.6-15 */
background: -webkit-radial-gradient(center, ellipse cover,  rgba(255,255,255,0.8) 14%,rgba(255,255,255,0) 57%,rgba(0,0,0,0.34) 85%); /* Chrome10-25,Safari5.1-6 */
background: radial-gradient(ellipse at center,  rgba(255,255,255,0.8) 14%,rgba(255,255,255,0) 57%,rgba(0,0,0,0.34) 85%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}



#loading_msk{
  opacity: 0;
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.7);
  z-index: 999999;
}
#loading_msk .spinner{
  position: relative;
  margin: 0 auto;
  margin-top: 350px;
  width: 60px;
  height: 60px;
  background: url(../img/loading_o.svg) no-repeat center center;
  background-size: 60px 60px;
}
/* animation rotate 360 */
@-webkit-keyframes rotate360
{
　0% { 
   -webkit-transform: rotate(0deg); 
    }
  100% { 
    -webkit-transform: rotate(360deg); 
  }
}
@-moz-keyframes rotate360
{
　0% { 
   -moz-transform: rotate(0deg); 
    }
  100% { 
    -moz-transform: rotate(360deg); 
  }
}
@-ms-keyframes rotate360
{
　0% { 
   -ms-transform: rotate(0deg); 
    }
  100% { 
    -ms-transform: rotate(360deg); 
  }
}
@keyframes rotate360
{
　0% { 
   transform: rotate(0deg); 
    }
  100% { 
    transform: rotate(360deg); 
  }
}
.spinner_animate{ 
  -webkit-animation:rotate360 2s linear infinite;
  -moz-animation:rotate360 2s linear infinite;
  -ms-animation:rotate360 2s linear infinite;
  animation:rotate360 2s linear infinite;
  pointer-events: none;
}





