
/**/

.page-wrapper {
  background: #05113a url(../img/a1.2_bg.png) no-repeat top center;
}

.pagetitle{
  position: absolute;
  width: 1306px;
  height: 59px;
  top: 58px;
  left: 30px;
  text-align: center;
}

.maintitle{
  color: #7fc4ff;
  width: 100%;
  font-size: 24px;
  padding: 11px 0 0 0;
  margin: 0 auto;
  font-weight: bold;
}


.blocktitle {
  position: absolute;
  top: 100px;
  font-size: 22px;
  text-align: center;
  width: 100px;
  height: 25px;
}
.blocktitle1 {
  left: 323px;
}
.blocktitle2 {
  left: 1005px;
}

.levellink {
  position: absolute;
  top: 250px;
  right: 643px;
  font-size: 14px;
  text-align: right;
  color: #1e8edc;
  pointer-events: auto;
  z-index: 99;
}
.levellink:hover{
  color: #1e8edc;
}

/* ---- */

#member_slider {
  position: absolute;
  left: 10px;
  top: 250px;
  width: 140px;
  height: 510px;
}

#member_slider .track{
  position: absolute;
  top: 15px;
  right: 20px;
  width: 12px;
  height: 400px;
  background-color: rgba(49, 175, 255, 0.1);
}

#member_slider .node{
  position: absolute;
  right: 10px;
  width: 80px;
  height: 30px;
  cursor: pointer;
  pointer-events: auto;
}

#member_slider .node .dotb{
  position: absolute;
  top: 1px;
  right: 1px;
  background-color: rgba(49, 175, 255, 0.1);
  width: 30px;
  height: 30px;
  border-radius: 15px;
}

#member_slider .node .dot{
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: #248ece;
  width: 16px;
  height: 16px;
  border-radius: 8px;
}

#member_slider .node .label{
  position: absolute;
  width: 100px;
  text-align: right;
  right: 25px;
  top: 0px;
  font-size: 14px;
  color: #1e8edc;
}
#member_slider .node .label .val{
  line-height: 24px;
}

#member_slider .selected{
  color: #34fbf9 !important;
}

#member_slider .cursor{
  position: absolute;
  width: 41px;
  height: 32px;
  right: 0px;
  background: url(../img/a1.2_member_slider_cursor.png) no-repeat center center;
}

#member_slider .bot_num{
  position: absolute;
  bottom: 10px;
  right: 20px;
  color: #7fc4ff;
  text-align: right;
}

#member_slider .node0{
  top: 0px;
}
#member_slider .node1{
  top: 80px;
}
#member_slider .node2{
  top: 160px;
}
#member_slider .node3{
  top: 240px;
}
#member_slider .node4{
  top: 320px;
}
#member_slider .node5{
  top: 400px;
}




/* ---- */

#point_slider {
  position: absolute;
  right: 3px;
  top: 230px;
  width: 140px;
  height: 520px;
}

#point_slider .track{
  position: absolute;
  top: 15px;
  left: 20px;
  width: 12px;
  height: 400px;
  background-color: rgba(49, 175, 255, 0.1);
}

#point_slider .node{
  position: absolute;
  left: 10px;
  width: 80px;
  height: 30px;
  cursor: pointer;
  pointer-events: auto;
}

#point_slider .node .dotb{
  position: absolute;
  top: 1px;
  left: 1px;
  background-color: rgba(49, 175, 255, 0.1);
  width: 30px;
  height: 30px;
  border-radius: 15px;
}

#point_slider .node .dot{
  position: absolute;
  top: 8px;
  left: 8px;
  background-color: #248ece;
  width: 16px;
  height: 16px;
  border-radius: 8px;
}

#point_slider .node .label{
  position: absolute;
  width: 100px;
  text-align: left;
  left: 25px;
  top: 0px;
  font-size: 14px;
  color: #1e8edc;
}
#point_slider .node .label .val{
  line-height: 24px;
}

#point_slider .selected{
  color: #34fbf9 !important;
}

#point_slider .cursor{
  position: absolute;
  width: 41px;
  height: 32px;
  left: 0px;
  background: url(../img/a1.2_point_slider_cursor.png) no-repeat center center;
}

#point_slider .bot_num{
  position: absolute;
  bottom: 20px;
  right: 20px;
  color: #1e8edc;
}

#point_slider .node0{
  top: 0px;
}
#point_slider .node1{
  top: 80px;
}
#point_slider .node2{
  top: 160px;
}
#point_slider .node3{
  top: 240px;
}
#point_slider .node4{
  top: 320px;
}
#point_slider .node5{
  top: 400px;
}

/* ---- */
.block_lt {
  position: absolute;
  width: 815px;
  height: 72px;
  top: 151px;
  left: 33px;
}

.block_lt .col1{
  position: absolute;
  width: 265px;
  height: 100%;
  left: 0px;
}
.block_lt .col2{
  position: absolute;
  width: 265px;
  height: 100%;
  left: 242px;
}
.block_lt .col3{
  position: absolute;
  width: 265px;
  height: 100%;
  left: 481px;
}

.block_lt .col .tit{
  font-size: 16px;
  display: block;
}
.block_lt .col .des{
  font-size: 10px;
  padding-left: 5px;
  opacity: 0.8;
}
.block_lt .col .num{
  font-size: 26px;
}
.block_lt .col .tb,
.block_lt .col .hb{
  font-size: 12px;
  color: #7fc4ff;
  display: inline-block;
  margin-right: 8px;
}

.block_lt .col .tb .lb,
.block_lt .col .hb .lb{
}



/* ---- */
.block_rt {
  position: absolute;
  width: 477px;
  height: 220px;
  top: 151px;
  left: 770px;
}

.block_rt .row1{
  position: absolute;
  width: 100%;
  height: 80px;
  color: #7fc4ff;
}
.block_rt .row2{
  position: absolute;
  width: 100%;
  height: 130px;
  top: 75px;
}

.block_rt .row1 .tit{
  font-size: 14px;
}

.block_rt .row1 .num{
  font-size: 30px;
}
.block_rt .row1 .tb,
.block_rt .row1 .hb{
  margin-left: 20px;
}

.block_rt .row2 .tit{
  font-size: 14px;
  color: #7fc4ff;
}

.block_rt .row2 .col{
  position: absolute;
  width: 130px;
  height: 90px;
  top: 32px;
}
.block_rt .row2 .col2{
  left:160px;
}
.block_rt .row2 .col3{
  left:310px;
}

.block_rt .row2 .col .num{
  font-size: 24px;
  color: #9ccfff;
  line-height: 28px;
}
.block_rt .row2 .col .tb,
.block_rt .row2 .col .hb{
  font-size: 12px;
  color: #7fc4ff;
  display: inline-block;
  margin-right: 8px;
}

.block_rt .row2 .col .lb{
  display: block;
}
.block_rt .row2 .col .val {
  color: #cee3fc;
  font-weight: bold;
}


/* ---- */
.block_lb {
  position: absolute;
  width:580px; 
  height:495px;
  top:250px; 
  left:144px; 
  pointer-events: auto;
}
.block_lb .card{
  position: absolute;
  width:76px; 
  height:70px;
  border-radius: 3px;
  pointer-events: auto;
  cursor: pointer;
}
.block_lb .card .img{
  width:76px; 
  height:50px;
}
.block_lb .card .lb{
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  padding-top: 3px;
}

.block_lb .card0{
  top: 3px;
  left: 252px;
}
.block_lb .card1{
  top: 180px;
  left: 474px;
}
.block_lb .card2{
  top: 418px;
  left: 398px;
}
.block_lb .card3{
  top: 418px;
  left: 104px;
}
.block_lb .card4{
  top: 180px;
  left: 27px;
}

.block_lb .card0 .img{
  background: url(../img/a1.2_card0.png) no-repeat center top;
  background-size: contain;
}
.block_lb .card1 .img{
  background: url(../img/a1.2_card1.png) no-repeat center top;
  background-size: contain;
}
.block_lb .card2 .img{
  background: url(../img/a1.2_card3.png) no-repeat center top;
  background-size: contain;
}
.block_lb .card3 .img{
  background: url(../img/a1.2_card2.png) no-repeat center top;
  background-size: contain;
}
.block_lb .card4 .img{
  background: url(../img/a1.2_card4.png) no-repeat center top;
  background-size: contain;
}


/* ---- */
.block_rb {
  position: absolute;
  width: 440px;
  height: 390px;
  top: 363px;
  left: 770px;
  pointer-events: auto;
}

.block_rb .tab {
  position: absolute;
  width: 219px;
  height: 28px;
  top: 32px;
  background-color: #001b4d;
  color: #2a81d2;
  text-align: center;
  line-height: 28px;
  font-size: 14px;
  cursor: pointer;
  border-top-right-radius: 3px;
  border-top-left-radius: 3px;
}
.block_rb .selected {
  color: #fff;
  background-color: #0f4ac4;
}
.block_rb .tab1 {
  left: 0px;
}
.block_rb .tab2 {
  left: 224px;
}
.block_rb .tabc{
  position: absolute;
  width: 440px;
  height: 310px;
  top: 70px;
}
.block_rb .tabc1 .blk{
  position: absolute;
  width: 50%;
  height:80px;
  text-align: center;
  border-bottom: 1px solid #091d46;
  padding-top: 8px;
}
.block_rb .tabc1 .blk .val{
  line-height: 36px;
}
.block_rb .tabc1 .blk2{
  left: 50%;
  border-left: 1px solid #091d46;
}

.chart_tip {
  min-width: 150px;
  background:#0641bc; 
  box-shadow: 0 1px 5px rgba(0,0,0,0.3); 
  border-radius:3px;
  padding: 1px;
}
.chart_tip .c1 {
  color: #99ccff;
  display: inline-block;
  width: 60px;
  text-align: right;
  padding-right: 6px;
}
.chart_tip .c2 {
  color: #fff;
  display: inline-block;
}
.chart_tip .r1 {
  padding-right: 10px;
}
.chart_tip .r2 {
  background-color: #032c81;
  padding-right: 10px;
}
.chart_tip .tit {
  font-weight: bold;
  text-align: center;
  border-bottom: 1px solid #001552;
  height: 26px;
  line-height: 18px;
  padding: 3px;
}

.chart_tip th,
.chart_tip td {
  padding: 1px 5px !important;
}





/* --- */
.ext_link {
  display: inline-block;
  width: 23px;
  height: 23px;
  cursor: pointer;
  pointer-events: auto;
  background: url(../img/ext_link1.png) no-repeat center center;
}

#ext_link1 {
  margin: 0 0 -5px 10px;
}

