<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">

    <script type='text/javascript'></script>
    
    <title>首都航空运控大屏</title>

    <meta http-equiv="refresh" content="86400">

    <script src="../js/tingyun.js?ver=20230202"></script>

    <link href="css/bootstrap.css" rel="stylesheet">
    <link href="css/bootstrap-theme.css" rel="stylesheet">
    <link href="css/common.css?ver=20230202" rel="stylesheet">
    <link href="css/index.css?ver=20230202" rel="stylesheet">

    <script src="../js/jquery-1.11.1.js"></script>
	<script src="../js/bootstrap.min.js"></script>
	<script src="../js/echarts.min.js?ver=20230202"></script>
	<script src="../js/echarts-gl.min.js?ver=20230202"></script>
	<script src="../js/json.js"></script>
	<script src="../js/util.js"></script>
	
	
</head>
<body>

<iframe id="login" scrolling="no" width="0" height="0" style="display:none;" ></iframe>
<script src="js/config.js?ver=20230202"></script>
<script type="text/javascript">
	$('body').hide();
</script>




<div class="page-wrapper">

	<div id="page-bgimg"></div>

	<div id="top_l"></div>

	<!-- ********************************* -->
	<!-- 新闻 news -->
	<!-- ********************************* -->
	<div id="welcome_msg" class="blue1 fs32 center">
	</div>

	<div id="top_r">
		<div class="timeblk">
			<span id="time_beijing" class="ffnum blue1 fs16"></span>
			<span id="date_beijing" class="blue2 fs12"></span><br>
			<span class="blue2 fs12">北京时间</span>
		</div>
		<div class="timeblk">
			<span id="time_london" class="ffnum blue1 fs16"></span>
			<span id="date_london" class="blue2 fs12"></span><br>
			<span class="blue2 fs12">世界时间</span>
		</div>
		<div class="duty">
			<span class="blue1 fs14">01值班</span>
			<span id="duty_name" class="blue2 fs14"></span>
		</div>
	</div><!-- // -->

	<div id="col_l">

		<div class="titlebar">公司运行一览</div>
		<!-- ********************************* -->
		<!-- 运行一览 -->
		<!-- ********************************* -->
		<div class="frame row1">
			<div id="val_flt_exec_rate" class="kpi1">
				<div class="r1">
					<span class="fs16 blue1">航班总量</span>
					<span id="val_flt_total_sch" class="ffnum fs24"></span>
					<span class="fs12 blue1">架次</span>
				</div>
				<div class="bar">
					<span class="barin"></span>
					<span class="plane"></span>
				</div>
				<div class="r2">
					<span class="blue2">已执行</span>
					<span class="ffnum fs16 rate"></span>
				</div>
			</div>

			<div id="val_flt_exec_rate_china" class="kpi2">
				<div class="r1">
					<span class="fs14 blue1">国内</span>
					<span id="val_flt_total_china_sch" class="ffnum fs20"></span>
					<span class="fs11 blue1">架次</span>
				</div>
				<div class="bar">
					<span class="barin"></span>
					<span class="plane"></span>
				</div>
				<div class="r2">
					<span class="fs11 blue2">已执行</span>
					<span class="ffnum fs14 rate"></span>
				</div>
			</div>

			<div id="val_flt_exec_rate_int" class="kpi3">
				<div class="r1">
					<span class="fs14 blue1">国际</span>
					<span id="val_flt_total_int_sch" class="ffnum fs20"></span>
					<span class="fs11 blue1">架次</span>
				</div>
				<div class="bar">
					<span class="barin"></span>
					<span class="plane"></span>
				</div>
				<div class="r2">
					<span class="fs11 blue2">已执行</span>
					<span class="ffnum fs14 rate"></span>
				</div>
			</div>

			<div class="chart">
				<!-- ********************************* -->
				<!-- 航班正常率 -->
				<!-- ********************************* -->
				<canvas id="cvs_normal_rate" width="177" height="177" style="background:rgba(255,0,0,0.0);"></canvas>

				<div class="fs11" style="position: absolute; width:22px; height:22px; left:34px; top:127px; opacity:0.9; ">0</div>
				<div class="fs11" style="position: absolute; width:22px; height:22px; left:15px; top:58px; opacity:0.9; ">25</div>
				<div class="fs11" style="position: absolute; width:22px; height:22px; left:83px; top:13px; opacity:0.9; ">50</div>
				<div class="fs11" style="position: absolute; width:22px; height:22px; left:146px; top:58px; opacity:0.9; ">75</div>
				<div class="fs11" style="position: absolute; width:32px; height:22px; left:131px; top:127px; opacity:0.9; ">100</div>

				<div class="center" style="position: absolute; width:100px; height:50px; left:43px; top:82px; ">
					<span id="today_normal_rate" class="fs30 ffnum"></span><span class="sub ffnum">%</span>
				</div>

				<div class="blue1 center" style="position: absolute; width:80px; height:21px; left:49px; top:67px; ">
				航班正常率
				</div>
			</div>

		</div>

		<!-- ********************************* -->
		<!-- 延误原因 -->
		<!-- ********************************* -->
		<div class="frame row2">
			<div class="blkbar">
				<div class="fs16">
					延误原因
				</div>
				<div class="left">
					<span class="blue1 fs16">航空公司原因</span><span id="per_delay_cause_comp" class="fs20 ffnum green"></span>
				</div>
				<div class="right">
					<span class="blue1 fs16">非公司原因</span><span id="per_delay_cause_none" class="fs20 ffnum blue2"></span>
				</div>
			</div>

			<div class="chart">
				<canvas id="cvs_delay_cause" width="130" height="130"></canvas>
				<div class="center txt">
					<span class="green fs12">公司原因</span><br>
					<span class="fs18">VS</span><br>
					<span class="blue2 fs12">非公司原因</span>
				</div>
			</div>

			<div id="holder_delay_cause_comp" class="fs12">
				<!--
				<div class="baritmrow"><span class="blue2">运行</span> <span class="bar greenbar" style="width: 70px; "></span> <span class="ffnum">20%</span> </div>
				-->
			</div>

			<div id="holder_delay_cause_none" class="fs12">
				<!--
				<div class="baritmrow"><span class="blue2">空管</span> <span class="bar bluebar" style="width: 70px; "></span> <span class="ffnum">20%</span> </div>
				-->
			</div>
		</div>

		<!-- ********************************* -->
		<!-- 运力分布 -->
		<!-- ********************************* -->
		<div class="frame row3">
			<div class="blkbar">
				<div class="fs16">
					运力分布
				</div>
			</div>

			<div class="mid">
				<div class="center txt">
					<span class="blue2 fs12">总运力</span><br>
					<span id="total_plane_num" class="ffnum fs24"></span><span class="fs11 blue3">架</span>
					
				</div>
			</div>

			<div class="ll">
				<div class="center blue1 fs14 bold">过夜飞机</div>

				<!-- <div class="b1">
					<div class="blue3 fs12">实际</div>
					<span id="base_over_night_plane_num" class="ffnum fs16"></span><span class="fs11 blue3">架</span>
				</div>
				<div class="b2">
					<div class="green fs12">当日计划</div>
					<span id="base_over_night_plane_num2" class="ffnum fs16"></span><span class="fs11 blue3">架</span>
				</div> -->


				<div id="base_over_night_plane" class="barlist" style="overflow-y: hidden; pointer-events: auto; top:35px;height: 120px;">
					<div id="base_over_night_plane1" style="height:auto;">
						<table>
							<tbody>
							<!--
								<tr>
									<td class="blue4 r" width="50">乌鲁木齐</td>
									<td width="18">21</td>
									<td width="45"><span class="bar bluebar2" style="width: 30px; "></span></td>
									<td width="18">22</td>
									<td width="45"><span class="bar greenbar2" style="width: 45px; "></span></td>
								</tr>

								<tr>
									<td class="blue4 r" width="50">武汉</td>
									<td width="18">21</td>
									<td width="45"><span class="bar bluebar2" style="width: 10px; "></span></td>
									<td width="18">22</td>
									<td width="45"><span class="bar greenbar2" style="width: 25px; "></span></td>
								</tr>

								<tr>
									<td class="blue4 r" width="50">乌鲁木齐</td>
									<td width="18">21</td>
									<td width="45"><span class="bar bluebar2" style="width: 20px; "></span></td>
									<td width="18">22</td>
									<td width="45"><span class="bar greenbar2" style="width: 15px; "></span></td>
								</tr>

								<tr>
									<td class="blue4 r" width="50">乌鲁木齐</td>
									<td width="18">21</td>
									<td width="45"><span class="bar bluebar2" style="width: 30px; "></span></td>
									<td width="18">22</td>
									<td width="45"><span class="bar greenbar2" style="width: 45px; "></span></td>
								</tr>
							-->
							</tbody>
						</table>
					</div>
					<div id="base_over_night_plane2" style="height:auto;"></div>
				</div>

			</div>

			<div class="rr">
				<div class="center blue1 fs14 bold">机型架数</div>

				<div class="b1">
					<div class="blue3 fs12">空中</div>
					<span id="plane_over_air" class="ffnum fs16"></span><span class="fs11 blue3">架</span>
				</div>
				<div class="b2">
					<div class="green fs12">地面</div>
					<span id="plane_on_ground" class="ffnum fs16"></span><span class="fs11 blue3">架</span>
				</div>
				<div class="b3">
					<div class="brown fs12">停场</div>
					<span id="plane_on_stop" class="ffnum fs16"></span><span class="fs11 blue3">架</span>
				</div>
				<div class="barlist">
					<table id="plane_num_table">
						<tbody>
						<!--
							<tr>
								<td class="blue4 r" width="50">319</td>
								<td width="18">21</td>
								<td width="45"><span class="bar bluebar2" style="width: 30px; "></span></td>
								<td width="18">22</td>
								<td width="45"><span class="bar greenbar2" style="width: 45px; "></span></td>
							</tr>

							<tr>
								<td class="blue4 r" width="50">320</td>
								<td width="18">21</td>
								<td width="45"><span class="bar bluebar2" style="width: 10px; "></span></td>
								<td width="18">22</td>
								<td width="45"><span class="bar greenbar2" style="width: 25px; "></span></td>
							</tr>

							<tr>
								<td class="blue4 r" width="50">321</td>
								<td width="18">21</td>
								<td width="45"><span class="bar bluebar2" style="width: 20px; "></span></td>
								<td width="18">22</td>
								<td width="45"><span class="bar greenbar2" style="width: 15px; "></span></td>
							</tr>

							<tr>
								<td class="blue4 r" width="50">737</td>
								<td width="18">21</td>
								<td width="45"><span class="bar bluebar2" style="width: 30px; "></span></td>
								<td width="18">22</td>
								<td width="45"><span class="bar greenbar2" style="width: 45px; "></span></td>
							</tr>
						-->
						</tbody>
					</table>
				</div>

			</div>

		</div>
		
	</div><!-- // -->

	<div id="col_m">
		<div class="earthbg"></div>

		<div style="position: absolute; width:578px; height:578px; left: 40px; top: 40px; " >
			<div id="earth3d" style="width:578px; height: 578px; pointer-events: none;"></div>
		</div>

		<div class="earthlight"></div>

		<div id="title_earth" class="title_earth fs18 center" >
		国内运行正常率
		</div>

		<!-- ********************************* -->
		<!-- 月度正常率 -->
		<!-- ********************************* -->
		<!--div class="normal_rate_l " >
			<span class="fs14 bold">公司月度正常率</span>
			<div class="fs28" >
				<span id="normal_rate_month" class="fs30 ffnum"></span><span class="sub ffnum fs16">%</span>
			</div>
		</div-->
		


		<!-- ********************************* -->
		<!-- 年度正常率 -->
		<!-- ********************************* -->
		<div class="normal_rate_r " >
			<span class="fs14 bold">公司年度正常率</span>
			<div class="fs28" >
				<span id="normal_rate_year" class="fs30 ffnum"></span><span class="sub ffnum fs16">%</span>
			</div>
		</div>
		


		<!-- ********************************* -->
		<!-- 中间地图 -->
		<!-- ********************************* -->


		<!-- ********************************* -->
		<!-- 飞机位置图例 -->
		<!-- ********************************* -->
		<div id="map_switch_lb" class="fs12 center bold" >飞机位置</div>
		<div id="map_switch_btn" ><img src="img/earth-small.png"></div>


		<div class="blue1 fs12 planeLocationLegend" >
			<span class="itm1"><span style="color:#33CC00;">●</span> 正常</span>
			<span class="itm2"><span style="color:#EEC100;">●</span> 延误</span>
		</div>


		<div id="china_map" class="" style="width:500px; height:400px; left: 76px; top:100px; transform:scale(1.08, 1.08); transform-origin:left top; -moz-transform:scale(1.08, 1.08); -moz-transform-origin:left top; -webkit-transform:scale(1.08, 1.08); -webkit-transform-origin:left top; -ms-transform:scale(1.08, 1.08); -ms-transform-origin:left top;">

		<!-- 新疆 -->
		<div style="width:128px; height:90px; left: 16px; top:77px;">
		<svg id="area_100007" viewBox="0 0 128 90" class="area_stroke hidden " version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" >
		<path d="M126.6,53.8c0.1-0.9,1.3-1.8,1.3-1.8c-0.3-0.4-0.6-1.5-1.1-2.3c-0.6-1-0.9-1.1-1.1-1.5c-0.2-0.5,0-1.2-0.4-1.9
		c-0.4-0.7-0.1-1.6-0.4-2.8c-0.3-1.2-0.6-0.6-1.3-1s0.2-0.9,0.6-1.4c0.4-0.5-1.5-0.4-2.7-0.6c-1.2-0.2-1.1-0.9-1.2-1.4
		c-0.2-0.4-0.7-0.4-1-0.5c-0.3-0.1-0.1-0.3-0.2-0.7c-0.1-0.4-0.6-0.3-1.2-0.4c-0.6-0.1-1-1.2-1.4-1.9c-0.4-0.7-1.3-0.6-1.9-0.9
		c-0.6-0.2-1,0-1.4-0.6c-0.4-0.5-2.2-0.3-2.5-0.7c-0.3-0.4-1.7-0.2-2.8-0.3c-1.1-0.1-2-1.1-2.6-1.5c-0.7-0.4-1-0.5-1.8-0.7
		s-1-1.3-1.1-2.4c-0.1-1.1,1.2-2.2,2.2-2.7c1.1-0.6,0.2-1.7,0-2.2c-0.2-0.5,0.4-0.9,1.1-1.5c0.8-0.6,0.1-1.8,0.2-2.2
		c0-0.4-0.2-0.7-0.6-0.8c-0.4-0.1-0.3-0.5-0.2-0.9c0.1-0.4-0.4-0.7-0.7-1.5c-0.4-0.7-0.1-0.9,0.3-1.5c0.4-0.7-0.8-1.4-1.1-1.7
		c-0.3-0.3-0.5-0.8-0.6-1.2s-0.9,0.1-1.2,0.3c-0.3,0.3-0.9-1.5-1.1-1.9c-0.2-0.4-1,0-2.5,0c-1.4,0-0.9-0.7-1.1-1.2
		c-0.2-0.5-0.3-0.5-0.5-0.7C96,8.7,95.9,8.1,96,7.4c0.1-0.6-0.9-1.4-1.3-1.7S94.4,5,94.4,4.6c0-0.4-0.6-0.5-1-0.8
		c-0.4-0.3-0.2-0.7,0.2-1S94,1.4,94,1.4s-1,0.3-2.6,0.3s-1-1.3-1-1.3S89.2,0.7,88.7,1c-0.5,0.2-0.5,2.1-1.2,2.8
		c-0.6,0.7-2.4,0.6-3.1,0.4C83.6,4,82.5,4.9,82,6.3c-0.5,1.4-0.9,3.4-0.6,4.1c0.3,0.8-0.2,0.9-0.7,1.7c-0.5,0.8-2.2,0.4-3.2,0.7
		c-1,0.3-1-0.3-1.1-0.7s-2.3-0.8-3.5-1s-2.2-1.5-3-1.9c-0.8-0.4-1.4,0.5-1.9,1.5c-0.5,1-3,3.6-3.2,4.1c-0.2,0.5-0.4,0.8-0.4,1.5
		c0,0.7-1.4,1.8-1.4,2.2c0,0.5,0.5,0.6,1,1c0.5,0.4-0.2,0.9-0.4,1.7c-0.2,0.9-1.2-0.2-1.5-0.5s-0.4-0.1-1.2-0.2
		c-0.8-0.1-0.8-0.5-1.1-1s-2.2-0.2-2.8,0c-0.6,0.2-1.5,0-2.1-0.2c-0.6-0.2-3.2,0.1-4,0c-0.8-0.1-1.1,0.6-1,1.2
		c0.1,0.6,1.7,0.4,2.5,0.7c0.9,0.3-0.6,0.9-1,1.5s-0.1,1.7-0.2,2.1c-0.1,0.5,0.5,5.9,0.4,6.5c-0.1,0.6-1.7,0.6-2,0.5
		c-0.4,0-0.1,0.4,0,0.8c0.1,0.4-1,0.5-1.5,0.7c-0.5,0.2-0.6,3.2-0.6,3.8c0,0.7-1,0.4-1.7,0.4c-0.7,0-0.9,0.4-1.4,0.7
		c-0.5,0.3-1.1,0.3-1.6,0.2c-0.5-0.1-1.3-0.4-2.4-0.3c-1.1,0.1-1,0.4-1.3,0.8c-0.4,0.4-1.3,0.4-2.4,0.4c-1.1,0-1.7,1.2-2.3,1.6
		c-0.6,0.3-0.8,0.4-1.6,0.2c-0.7-0.2-3.5-0.4-3.8-0.6c-0.4-0.2-2.7-0.4-3.7-0.5c-1-0.1-1.4,1.9-1.8,2.8c-0.4,0.8-2.7,0.1-3,0.1
		c-0.3,0-2,0.2-2.6,0.2c-0.6,0,0-0.9,0.2-1.1c0.2-0.2-0.7-0.8-0.9-1.3c-0.2-0.4-0.3-0.3-0.7,0.1c-0.4,0.4-1,0.5-2.2,0.6
		c-1.2,0.1-1.1-0.4-1.3-0.7c-0.2-0.3-0.6,0.7-0.5,1.1c0.1,0.5-1.2-0.1-1.8-0.2c-0.6-0.2-1.3,0.6-1.9,0.8c-0.6,0.2-0.8-0.1-1-0.3
		c-0.2-0.3-1-0.1-1.6-0.1c-0.7-0.1-1.1,1.1-2,1.1c-0.9,0.1-0.9,0.9-0.9,1.5c-0.1,0.6-1.4,1-2.2,0.9c-0.8-0.1-0.4,2.3-0.3,2.8
		s-0.4,0.5-0.7,0.7c-0.3,0.1,0.3,2.1,0.8,2.7C1.3,52,1.5,51.8,2,51.1c0.5-0.7,2.4,1,3.4,1.7c1,0.7,0.2,1.1-0.2,1.6
		c-0.4,0.5-0.1,2.7,0.1,3.2c0.2,0.5-0.6,0.4-1,0.8s0.3,1.1,0.6,1.3c0.3,0.2,0.1,0.3,0,1c-0.1,0.7-1.6,0.2-2.1,0.1
		c-0.5-0.2-1.6-0.2-1.8,0.6c-0.2,0.8,3,1.7,3.8,1.9c0.8,0.1,0.2,0.3,0.3,0.7c0.1,0.5,0.2,0.9,0.6,1.2c0.4,0.3,0.5,1.2,1,1.8l0.1,0.2
		c0.3,0.5,0.1,1.1-0.1,2.1c-0.2,1.2,0.8,3,1.6,3.2C9,72.8,8.8,73,9.9,74c1.1,1.1,2.4,1.8,3,2.4c0.2,0.2,0.5,0.3,0.8,0.4
		c1.5,0.4,2.6-0.1,2.9,0.3c0.2,0.3-0.4,0.8-0.7,2c-0.1,0.3-0.2,0.6-0.3,2.4c-0.1,0.8-0.1,2-0.1,3.3c0.3,0.5,0.9,1.3,1.8,2
		c0.8,0.7,2.4,1.9,3.5,1.5c1.2-0.5,1.2-2.4,2.3-2.5c0.5,0,0.5,0.3,1.1,0.3c1.4,0.1,2.6-2,2.8-2.4c0.1-0.1,0.7-1.1,1.2-1.8
		c0.1-0.1,0.3-0.4,0.6-0.7c0.3-0.2,0.5-0.3,0.5-0.4c0.2-0.1,0.4-0.2,0.5-0.2c0,0,0.7,0.2,1.5,0.8c0.8,0.6,1.7,1.9,2.4,1.7
		c0.7-0.2,1.2-0.5,1.5-0.1s0.2,0.6,0.5,0.6c0.3,0,0.8-0.1,1,0.3c0.1,0.4,0.2,0.9,0.8,1s1.1-0.1,1.3-0.4c0.2-0.3,0.6-0.8,1.1-0.9
		c0.5-0.1,0.8-0.1,1.1-0.3c0.3-0.2,1.2-0.5,1.3-0.2c0.1,0.2,0.5,0.4,1.1,0.5s1.3-0.2,1.7,0c0.4,0.3,0.1,0.5,0.9,0.6
		c0.8,0,0.8-0.4,1.2,0.2c0.4,0.6,0.8,2,1.7,2c0.8,0.1,1.7,0.1,2,0.4c0.3,0.3,1.3,0.9,1.6,0.7c0.3-0.3-0.2-0.2,0.9-0.3
		c1.2-0.1,2.2,0,2.8-0.4c0.6-0.3,0.6-0.3,1.3-0.3c0.7,0,2.4,0.7,3,0.4c0.6-0.3,1.5-1.5,3.5-1.6c2,0,2-0.3,2.7-0.1
		c0.7,0.2,2.4,0.5,2.8,0.2c0.4-0.3,0.3-0.8,2.2-0.6c1.9,0.2,3.6,0.2,5.1,1.2c1.5,1,2.1,2.5,3.7,2.6c1.3,0.1,2.5,0.1,2.9,0.3
		c0.6-0.3,1,0,1.7,0.4c0.7,0.5,0.8,0.4,1.5,0.7c0.8,0.3,1.1-0.1,1.6-0.4c0.4-0.3,0.4-0.1,1.3,0c0.9,0.1,0.7-0.3,1.4-0.7
		c0.7-0.4-0.8-0.3-1.8-0.6c-0.9-0.2-0.4-1.7,0.1-2.2c0.5-0.6,1.3-0.2,2-0.9c0.7-0.7,0.5-0.9,0.6-1.5c0.1-0.6,0.5-0.6,0.8-1
		c0.2-0.4-0.9-0.7-1.5-0.9c-0.6-0.2-0.9-0.9-1.3-1.6c-0.4-0.7-0.5-0.4-1-0.8c-0.5-0.3-0.1-2.1,0-2.5c0.1-0.5-0.2-1.8-0.3-2.2
		c-0.1-0.4,1.1-0.4,1.5-0.4c0.5,0,0.7-0.2,1.3-0.6c0.5-0.4,1.7-0.2,2.1-0.2c0.4,0,0.6,0.3,1,0.6c0.5,0.3,1.6-0.2,2-0.3
		c0.4-0.1,0.7-0.1,1.4,0.1c0.7,0.1,1.2,0.1,1.8-0.2c0.6-0.3,2.3-0.3,3.5-0.7c1.1-0.4,3.2,0.1,3.7,0.3c0.5,0.2,0.3-0.3,0.3-0.7
		c0-0.4,0.5-0.2,1-0.2c0.5,0,0.3,0.2,0.5,0.4c0.2,0.2,0.3,0.2,0.8,0l0.1,0h0l0.2,0c0.7-0.2-0.3-0.6-0.5-1c-0.2-0.4-0.1-0.3,0.3-0.5
		c0.4-0.2,0.5-0.9,0.7-1.7c0.2-0.8-0.1-1.4-0.3-2.6c-0.2-1.1-0.3-2.7-0.1-4.1c0.2-1.4,1.7-1.1,3.1-1.4c1.4-0.3,0.6-0.2,1.4-1.1
		c0.8-0.9,1.5-1.4,2.9-2s1.7-0.6,3-0.7c1.3-0.1,0.9-0.3,1.5-0.8c0.6-0.5,2.4,0,3.3-0.2c0.9-0.2,0.6-0.5,1-1
		C126.4,55.2,126.5,54.6,126.6,53.8z"/>
		</svg>
		</div>


		<!-- 西南 -->
		<div style="width:183px; height:119px; left: 29px; top:157px;">
		<svg id="area_100004" viewBox="0 0 184.17 119.56" class="area_stroke hidden " version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" >
		<path d="M110.39,38.41a3.09,3.09,0,0,0-.46.24.9.9,0,0,1-1-.11c-.33-.23-.89.25-1.26.27s-.07.41.23.7.06.37.12,1.15-.6.82-.91,1-.19.56-.39,1.08a5.31,5.31,0,0,1-1.68,1.58,1.28,1.28,0,0,0-.63,1c0,.32-.23.36-.8.29s-1-.77-1.53-.95-.69.15-1.48.48a2.33,2.33,0,0,1-2.15-.57,3.69,3.69,0,0,0-1.36-.83c-.3-.07-.12-1.07-.07-1.62s-.4-.47-.68-.49-1-1.19-1.64-1.53-.56-.85-1-1.57a3.12,3.12,0,0,0-2.8-.78,1,1,0,0,1-1.28-.64c-.22-.47-3.17-.26-3.56-.22s-.62-.3-1.1-.72-1.1,0-2-.31-.29-.19-.7-.87-.58-.09-1.55.11-2.24-1-2.59-1.67-.87-.63-1.7-1.13-3.36-1.49-4-2-2.08-.24-2.71-.34-.6-1.81-.78-2.57-.83-1.32-1.12-2-.38-.84-.2-1.49S66.79,22.5,66.31,22s-.16-1.78,0-2.18-.17-.66-.19-1.44.91-.47,1.57-.27.88-1.37.62-1.75-.12-.38.14-1a6,6,0,0,1,1.23-2.15,3.37,3.37,0,0,0,.88-1.79c.22-.54-.52-.61-1-.65a.92.92,0,0,1-.8-1c0-.56,1-.28,1.47-.37s.44-.44.49-1a.27.27,0,0,0-.15-.24,10.61,10.61,0,0,0-2.93-.34C66,7.78,65.36,6.3,63.91,5.32s-3.21-1-5.07-1.18-1.77.34-2.2.6a5.26,5.26,0,0,1-2.78-.2c-.72-.25-.76,0-2.74.07s-2.85,1.25-3.46,1.57-2.29-.33-3-.38a1.62,1.62,0,0,0-1.3.29,9.07,9.07,0,0,1-2.81.37c-1.16.09-.62.05-.94.32s-1.33-.39-1.6-.65a4.18,4.18,0,0,0-1.95-.39c-.84-.07-1.21-1.42-1.65-2s-.44-.16-1.22-.2-.52-.28-.92-.55-1,.07-1.68,0-1-.27-1.15-.51-1,0-1.3.21a2,2,0,0,1-1.1.3,1.73,1.73,0,0,0-1.15.87c-.22.34-.69.42-1.3.4s-.66-.56-.77-1-.68-.33-1-.3-.13-.24-.5-.6-.8,0-1.52.14S19.19,1.35,18.44.78A5,5,0,0,0,17,0c0,.95-1.34,2.51-1.82,3a12.38,12.38,0,0,0-1,1.7c-.39.57-1.12.53-1.41,1a1.44,1.44,0,0,1-1.83.44c-1-.32-1.13,1.21-1.79,2S8,8.49,7,7.95a1.39,1.39,0,0,0-1.4,0,2.77,2.77,0,0,0,.54,1.48c.28.31-.07.53-.76.76s-.87,1.4-.87,1.4l2,1.61s-.52,1.12-.68,1.42,0,.88.88,1.28.4,1.51-.07,1.95-1.33-.28-1.33-.28l-1.85,1L2.67,18,2.44,16s-.78.4-.83.39S.56,15.8.56,15.8l.11,1.13L0,17.78l1.25,2a8.85,8.85,0,0,1-.48,1S1,22.6,1,22.6l-.86.86s.43,1.75.43,1.84A12.27,12.27,0,0,0,2,24.45s1.91,3.89,1.89,3.93,1.25,0,1.25,0,1.27,1.51,1.7,2-.06.48-.22.75S8,32.47,8.87,32.93s2,2.5,2.56,3.36,1.09-.47,1.76-1.17,2.57.51,3.46,1.18.29,1.37,1.46,2.39,1.76,2.24,2.41,2.76a5.31,5.31,0,0,0,1.32.83c.56.28.63.69.9,1.16a5.49,5.49,0,0,1,.68,1.88c.12.77,1.21.07,1.53-.19s1.19.18,1.88.68,0,1.57,0,2.2,2.26,2.89,2.69,3.21.53.24,1.23-.11.16,1.37,0,1.91,3.06.87,3.54,1,.31,2,.61,2.88S36,56.55,36.31,56s.51,1.17,1.1,1.78,1.23-.7,1.76-1.09,2.29,2.13,2.61,2.61,7.63.79,8.16.91.55-.14,1.11-.47.49.31,1.1.71.35,1.1.27,1.75-.33.58-.68,1a3.79,3.79,0,0,0-.46,2.12c0,.86,1.45-1.31,1.45-1.31a32.28,32.28,0,0,0,3.42-2.6c.87-.9,1.09-.19,1.51,0s1.07-.7,2-.87,1.57.43,2.4.92.16,1.12,0,1.5,1.06.23,1.62.2,1.23.87,1.73,1.18.72-.12,1-.38,1.09.73,1.66,1.14.1.26.11.86.79.44,1.31.82c0,0,.38.14.5.21,1.1.62-.2,4.42.63,4.83.4.2.79-.64,2-.67.36,0,.53.06,1.42.08.68,0,.65,0,.92,0,1,.1,1.12.65,1.92.75.41,0,.39-.1,2-.58,1.17-.35,1.25-.3,1.5-.5.64-.52.33-1.1.85-2.15a5.12,5.12,0,0,1,2.4-2.27,3.21,3.21,0,0,0,1.67-.92c.16-.22.2-.36.42-.5.44-.29.76,0,1.43-.19.48-.14.5-.35.93-.4a1.94,1.94,0,0,1,.85.14c.53.16,1.08.21,1.62.33,2.55.57,2.64.25,3.11.65.7.59.53,1.33,1.46,1.9a8.58,8.58,0,0,0,.78.41,4.07,4.07,0,0,0,1.11.43,4.32,4.32,0,0,0,1.76-.3c.36-.12.38.14.95.53a2.79,2.79,0,0,0,1.55.44c.44,0,1,.64,1.65,1.22s.81,0,.89-.79,1-1.5,1.47-2,.44.15.61.44.62.16,1.13.19.51.59,1.34,1.37a1.35,1.35,0,0,1,.35.62l0,0a26.6,26.6,0,0,1,.17,3.15c0,.2.11.3.19.34.42.19,1.05-.58,1.38-.45s.36,1-.25,3.72c.15,2.42-.46,3.48-1.08,4a6.47,6.47,0,0,0-1.58,1.5c-.59.84-.47,1.22-.42,1.33.15.32.52.37.5.5s-1,0-1.75.5a1.84,1.84,0,0,0-.67.75c-.24.52,0,.75-.25,1.33-.16.37-.3.35-.5.83s-.11.45-.25.75a3.25,3.25,0,0,1-.58.8,1.88,1.88,0,0,0-.51,1.64c.06.5-1.25,1.72-1.58,2.14s.75,1,.89,1.48-.67,1.43-.66,2.3,2.13-.34,3-.76a3.61,3.61,0,0,1,2.69.11,2.08,2.08,0,0,0,2-.48c.59-.45.23.1.4.72s-.68.53-1,1.08-.08,2.82.14,3.49-.15.39-.56.83.49.69,1,1.21,1.86.49,2.53.53.34.64.52,1.43-.64.83-1,1.13-.21,1.77-.24,2.34-1.15,1.29-1.34,1.7,1.29.34,1.95.27.58.23.79.5.84-.12,1.37-.3.66.12.77.49a2.08,2.08,0,0,1-.46,1.42c-.32.53.52.6.86.93s0,.8.11,1.47,1.15-.1,1.56-.39.27.29.66.74,3.27-1.51,3.63-1.81.59,1.8.82,2.13,0,.77.2,1.81,1,.46,1.43.21.39-.05.7.45.74.71,1.53.33.2-.43-.1-1.22a4.18,4.18,0,0,1,.3-2.62c.28-.85-.91-2.51-1.09-2.91s.36-.54.59-.52.25-.87.46-1.48.4.55.82.74.56-.58,1.15-.63,1.41,1.31,1.41,1.31.25-.42.61-.23.33-.24.76-.94,1.58-.07,2.3,0,.68.85.73,1.34.57.55,1.1.75,1.33-1.34,1.8-2.25.75.59.91,1.52.63-.11.81-.73,1.24.81,1.56,1.51,1.05-1,1.66-1.92,1.13.17,1.69.62.61-.32.91-.66,1.06-.11,1.65-.37.6-.74.71-1.06a1.9,1.9,0,0,1,.56-.89c.32-.31,1.6-.33,2.43-.72.51-.24,1.06.44,1.45,1.08,0,0,1.43-.87,1.54-1.21a.74.74,0,0,1,.58-.58c.32-.09.54-.18.63-.1s.95.78,1.19,0,0-1.83.26-2,.73,0,.45-.51-.61-.75-.58-.85,0-.86-.44-.68-1,1-1.43.68-.43-.46-.58-.51-.36-.9-.58-.67-.85,1.32-1.12.92-.49-.9-.76-.82-.33.49-.53-.08.19-1-.24-1.63-.22-1.08-.63-.83-.31.5-.69.22a1,1,0,0,0-1.07-.15c-.24.18-.4,1-.72.61s-.3-1.46-.74-1.83a.37.37,0,0,0,.19,0c.39-.07,3.43.11,3.83-.72s.3-1.2,1-1.27,1-.35,1.31.16.18.73.56.78.79-.15,1,.21a1.24,1.24,0,0,0,1.44.62c.72-.21,1.25-.13,1.09-.79s-.41-.66,0-.79,1.29-.25,1.49-.49.27-.48.58-.37.22.45.8.19.78-.4,1-.37,1.36.28,1.62-.25,0-1.05.53-1.27,1.24-1.06,1.39-.67a3.23,3.23,0,0,0,.57,1.06c.24.23.61.92,1,.73s.37-.75.59-.22.38.9.78,1.08.74.37.88,0-.05-.45.67-.42.56.44.82-.17.2-1,.71-1.32.56-.84.92-.61.3.07.56.46,1.13,1.29,1.07.83-.07-.77.1-1,.71-1.14,1-.77.24.73.45.65.67.43.86,0,.78-.77.25-.88-1-.05-.7-.44.53-.67.93-.57,1,.74,1.35.33a13,13,0,0,0,1-1.44s0,0,0-.1c.23-.38-.44-1.65-.78-2s.09-.83.57-1.34,0-.72-.31-.87.06-.83.31-.83.44-.3.54-.57.36-.25.48-.7-.5-1-.48-1.13-.65-.26-.83,0-1.32,0-1.71,0-.37.25-.85.69-.21-.16-.3-.71.42-1,.67-1.71,1.44-.36,2.31-.68.52-.46,1-1.24-.39-.87-.76-1.38,0-.51.26-1,0-.22-.38-.41.15-.68.44-1.12-.08-.3-.43-.47,0-.48.07-.83a.65.65,0,0,0-.09-.46,1.72,1.72,0,0,1-.23-.49c-.11-.4.27-1.15.34-2.36a2.72,2.72,0,0,0-.28-1.47c-.08,0-.19,0-.37-.11a1.94,1.94,0,0,1-.78-1c-.08-.29-.12-.34-.33-.35s-.57-.07-.57-.3.5-1.63-.34-1.93-1-.6-1.09-.87-1.63,0-1-.39.47-.22.6-.71a15.4,15.4,0,0,0,0-1.61c.08-.22.21-.84,0-.9s-.76-.47-.68-.74.94-.16,1.35-.39.88-.13,1.36-.34.42-.43,1-.25,1.78-.2,2,0,.36.38.69,0,1.4-1.75,2.32-1.67,1.18.2,1.53-.18.69-.2.57-.62a1.17,1.17,0,0,1,0-1.06c.2-.4.45-1.07.23-1.46s-.3-.79-.68-.94-.81-.17-.63-.58.73-1.41.1-1.41c-.46,0-1.57-.13-2.13-.2a.44.44,0,0,1-.42.32c-.45,0-.74-.34-.91-.08s-1,.42-1.19.05.2-1.15-.51-1.31a5.58,5.58,0,0,1-1.54-.45,13.34,13.34,0,0,0-1.21-.73,1.12,1.12,0,0,1-.31-.28c-.24-.26-.5-.61-.8-.62a5.87,5.87,0,0,0-1.44.07c-.28.1-.66,0-.89.27a.58.58,0,0,1-.93,0,8.42,8.42,0,0,1-.86-.79c-.16-.24-.11-.81-.45-.75s-1.21.37-1.27-.14,0-1.08-.33-.93-1.48.58-1.46.37a.24.24,0,0,0-.27-.29c-.15,0-.55,0-.28-.32s.75-1.06,0-1.19a3.89,3.89,0,0,0-2.13.12c-.6.24-1.07,0-1.41.19s-.72.42-.94.18-.25,0-.62-.48-.1-.57-.47-.6a3.84,3.84,0,0,1-1.07-.45c-.3-.13-.39-.19-.4-.42-.53-.16-1.66-.45-1.77,0s.09,1-.38,1.22-1.18.72-1.63.68-.72-.38-1.29-.51-1.49,0-1.66-.54-.28-.48-.68-.91-.53-.71-.44-1a.89.89,0,0,0,0-.89c-.23-.4-.44-.56-.48-1s-.29-1.4-.59-1-.61.61-1,.45-.46-.35-.73-.26a1.56,1.56,0,0,1-1.48-.74,19,19,0,0,0-1.45-2c-.37-.37-.87-.61-.88-1.41s.33-1.73-.41-2.17-1.08-1.08-1.67-.44-.52.72-1.19.88-1.08-.15-1.41.26-.86.34-.4,1.06a14,14,0,0,1,.71,1.24,2.34,2.34,0,0,1,.34,1.57c-.25.74-.63,1.92-1.24,1.83s-1.57-.65-1.93-.26-.29,1.37-1.28.75A6.36,6.36,0,0,1,133,38.46c-.3-.32-.53-.56-.92-.35s-.49-.29-.83.3a4,4,0,0,1-1.14,1.26c-.2.08-.23.46-.06.63a.59.59,0,0,1,.1.65c-.08.25-.32,1.66-.92,1.31s-.94-.55-1.41-.37-.73.55-1.17,0,.22-.84-.62-1.31-1-.5-1.29-1.15-1-1-.9-1.63.09-1.2-.57-1-.89.12-1.24.48-.2.4-.66.5-.59-.73-1.07-.63-1,.12-1.16-.2-.45-1.11-1.26-1-1,.43-1.29.12,0-.29-.59-.32-1.29-.25-1.56,0-.13.54-.67.2-.33-.53-.93-.44-.79-.18-.82.41-.07,1-.34.75-.43-1.17-.68-.84a3.3,3.3,0,0,0-.55,1A5,5,0,0,0,110.39,38.41Z"/>
		</svg>
		</div>


		<!-- 西北 -->
		<div style="width:125px; height:80px; left: 94px; top:128px;">
		<svg id="area_100006" viewBox="0 0 127 82" class="area_stroke hidden " version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" >
		<path d="M87.2,41.6L87.2,41.6c-0.4,0-0.9-0.5-1.1-0.6c-0.2,0-0.5,0-0.6-0.4c-0.1-0.4,0.3-0.7-0.4-1.1
				c-0.7-0.3-1.3-0.4-1.3-1.3c0-0.8,0.6-1.1,0.6-1.6c0-0.5,0.2-0.7,0-0.9c-0.2-0.2-0.1-0.6,0.3-0.6c0.4,0,2.5-0.1,2.5-0.9
				c0-0.8,0.3-1.2,0-1.5c-0.3-0.2-0.5-0.5,0.1-0.9c0.6-0.4,0.6-0.8,0.7-1c0-0.2,0.2-0.9-0.5-1.3c-0.7-0.4-1.1-1-2.1-1.1
				s-3-0.5-3.3-0.2c-0.4,0.3-0.5,1-1.1,0.8c-0.6-0.2-1.5-0.5-1.8-0.1c-0.3,0.4-2,2.3-2.5,2.2c-0.5,0-0.9-0.4-1.3-0.4
				c-0.4-0.1-1.3,0.2-1.7-0.2c-0.4-0.4-0.6-0.6-1-0.6s-1.6,0-2-0.5s-0.7-1.7-0.5-2.1s0.5-1.4-0.2-1.5c-0.6-0.1-2.5,0-2.7-0.6
				c-0.2-0.5,0.1-0.8-0.5-1.1c-0.6-0.3-1-0.8-1.4-1.1s-0.9-0.7-0.9-1.1c0-0.4,0-0.7,0.4-0.6c0.4,0,1,0.2,1.1-0.2
				c0-0.5,0.1-1.1,0.3-1.7c0.3-0.5,0.6-1.9-0.2-2c-0.7-0.2-1.7,0-2-0.1c-0.3-0.2-1.3-0.7-1.7-0.3c-0.4,0.4-0.7,1.1-1.3,1.1
				c-0.6,0-0.8-0.2-1.1,0c-0.3,0.2-0.6,0.9-1.1,0.8c-0.5-0.1-1.1,0-1.3-0.5c-0.2-0.6-0.3-1.3-0.5-1.7c-0.2-0.4-1.1-1.3-1-1.7
				c0.1-0.4,0.2-2.1,0.1-2.6c-0.1-0.5-0.6-1.1-0.5-2.1s0.7-1.8,0.8-2.6s-0.1-5.2-0.1-5.2c-0.1,0-1.3,0.4-1.4,0.4
				c-0.1,0-1.4-0.9-1.4-0.9s-2.2-0.6-2.7-0.6c-0.1,0-0.1-0.1-0.2-0.2c0,0-1.2,0.9-1.3,1.8c-0.1,0.9-0.2,1.5-0.6,2
				c-0.4,0.5-0.1,0.8-1,1c-0.9,0.2-2.7-0.3-3.3,0.2c-0.6,0.5-0.2,0.7-1.5,0.8C41.5,7,41.2,7,39.8,7.6s-2.1,1.1-2.9,2
				c-0.7,0.9,0.1,0.8-1.4,1.1c-1.4,0.2-2.9-0.1-3.1,1.4c-0.2,1.4-0.2,3,0.1,4.1c0.2,1.1,0.5,1.8,0.3,2.6c-0.2,0.8-0.3,1.5-0.7,1.7
				c-0.4,0.2-0.5,0-0.4,0.5c0.2,0.4,1.2,0.9,0.5,1l-0.2,0l0,0c-0.5,0.1-0.6,0.2-0.8,0c-0.2-0.3-0.1-0.4-0.5-0.5c-0.5,0-1-0.2-1,0.2
				c0,0.4,0.1,0.9-0.3,0.7c-0.5-0.2-2.6-0.6-3.7-0.3c-1.1,0.4-2.9,0.4-3.5,0.7c-0.6,0.3-1.1,0.4-1.8,0.2c-0.7-0.1-1-0.2-1.4-0.1
				c-0.4,0.1-1.6,0.6-2,0.3c-0.5-0.3-0.6-0.6-1-0.6c-0.4,0-1.6-0.2-2.1,0.2c-0.6,0.4-0.8,0.6-1.3,0.6c-0.4,0-1.7,0-1.5,0.4
				c0.1,0.4,0.5,1.8,0.3,2.2c-0.1,0.5-0.5,2.2,0,2.5c0.5,0.3,0.6,0.1,1,0.8c0.4,0.7,0.7,1.4,1.4,1.6s1.7,0.5,1.5,0.9
				c-0.2,0.4-0.7,0.3-0.8,1s0.1,0.8-0.6,1.5c-0.7,0.7-1.5,0.3-2,0.9c-0.5,0.6-1.1,2-0.1,2.2c0.9,0.3,2.4,0.2,1.8,0.6
				c-0.7,0.4-0.5,0.8-1.4,0.7c-0.9-0.1-0.9-0.3-1.3,0c-0.5,0.3-0.8,0.7-1.6,0.4c-0.8-0.3-0.8-0.2-1.6-0.7c-0.7-0.4-1-0.7-1.7-0.4
				C6,38.1,6,38.2,6,38.3c0,0.5,0,0.9-0.5,0.9C5,39.3,4,39,4.1,39.6c0,0.6,0.3,1,0.8,1c0.5,0,1.2,0.1,1,0.6c-0.2,0.6,0,0.8-0.9,1.8
				c-0.8,1-1,1.5-1.2,2.2c-0.3,0.6-0.4,0.6-0.1,1c0.3,0.4,0,2-0.6,1.8c-0.7-0.2-1.6-0.5-1.6,0.3s0.3,1,0.2,1.4c-0.1,0.4-0.5,1.7,0,2.2
				c0.5,0.5,1.5,1.2,1.3,1.9c-0.2,0.6-0.1,0.8,0.2,1.5c0.3,0.7,0.9,1.3,1.1,2.1C4.4,58,4.4,59.7,5,59.8c0.6,0.1,2.1-0.1,2.7,0.3
				c0.7,0.5,3.2,1.5,4,2s1.3,0.5,1.7,1.1c0.3,0.7,1.6,1.9,2.6,1.7c1-0.2,1.1-0.8,1.5-0.1c0.4,0.7-0.2,0.5,0.7,0.9
				c0.9,0.3,1.5-0.1,2,0.3c0.5,0.4,0.7,0.8,1.1,0.7c0.4,0,3.3-0.2,3.6,0.2c0.2,0.5,0.5,0.8,1.3,0.6c0.7-0.2,2.3,0.1,2.8,0.8
				c0.5,0.7,0.4,1.2,1,1.6c0.6,0.3,1.4,1.5,1.6,1.5c0.3,0,0.7-0.1,0.7,0.5s-0.2,1.6,0.1,1.6s1.2,0.6,1.4,0.8c0.1,0.2,1.4,0.9,2.1,0.6
				c0.8-0.3,0.9-0.7,1.5-0.5c0.6,0.2,1,0.9,1.5,0.9c0.6,0.1,0.8,0,0.8-0.3c0-0.3,0.2-0.7,0.6-1c0.5-0.3,1.5-1.1,1.7-1.6
				c0.2-0.5,0.1-0.9,0.4-1.1c0.3-0.2,1-0.2,0.9-1c-0.1-0.8,0.2-0.9-0.1-1.2c-0.3-0.3-0.6-0.7-0.2-0.7c0.4,0,0.9-0.5,1.3-0.3
				c0.3,0.2,0.8,0.3,1,0.1c0.1-0.1,0.3-0.2,0.5-0.2c-0.1-0.3,0.1-0.4,0.2-0.6c0.1-0.3,0-0.6-0.1-1.1c-0.1-0.5,0.3-0.7,0.5-1.1
				c0.2-0.3,0.4,0.6,0.7,0.8c0.3,0.2,0.3-0.2,0.3-0.8c0-0.6,0.2-0.3,0.8-0.4c0.6-0.1,0.4,0.1,0.9,0.4c0.5,0.3,0.4,0,0.7-0.2
				c0.3-0.2,1,0,1.6,0c0.6,0,0.3,0,0.6,0.3c0.3,0.3,0.5-0.1,1.3-0.1s1.1,0.7,1.3,1c0.2,0.3,0.7,0.3,1.2,0.2c0.5-0.1,0.6,0.7,1.1,0.6
				c0.5-0.1,0.3-0.2,0.7-0.5c0.3-0.4,0.6-0.3,1.2-0.5s0.6,0.4,0.6,1c-0.1,0.6,0.6,1,0.9,1.6c0.3,0.7,0.5,0.7,1.3,1.2s0.2,0.7,0.6,1.3
				c0.4,0.6,0.7,0.2,1.2,0c0.5-0.2,0.8,0,1.4,0.4c0.6,0.3,0.8-1.1,0.9-1.3c0.1-0.2,0.1-0.5-0.1-0.6c-0.2-0.2-0.1-0.6,0.1-0.6
				c0.2-0.1,0.8-0.7,1.1-1.3c0.3-0.6,0.4-0.1,0.8-0.3c0.4-0.2,0.6,0,0.9,0.3c0.3,0.3,0.7,0.8,1.7,1.4c1,0.6,0.9-0.4,1.3-0.8
				c0.4-0.4,1.3,0.2,1.9,0.3c0.6,0.1,1-1.1,1.2-1.8c0.1-0.4-0.1-1-0.3-1.6c-0.2-0.5-0.5-0.9-0.7-1.2c-0.5-0.7,0.1-0.7,0.4-1.1
				c0.3-0.4,0.7-0.1,1.4-0.3c0.7-0.2,0.6-0.2,1.2-0.9c0.6-0.6,0.9,0,1.7,0.4c0.7,0.4,0.4,1.4,0.4,2.2c0,0.8,0.5,1,0.9,1.4
				c0.4,0.4,1,1.3,1.4,1.9c0.4,0.7,1.2,0.8,1.5,0.7c0.3-0.1,0.3,0.1,0.7,0.3c0.4,0.2,0.7-0.1,1-0.4c0.3-0.4,0.6,0.6,0.6,1
				c0,0.4,0.2,0.6,0.5,1c0.2,0.4,0.1,0.6,0,0.9c-0.1,0.3,0,0.6,0.4,1c0.4,0.4,0.5,0.3,0.7,0.9c0.2,0.6,1.1,0.4,1.7,0.5
				c0.6,0.1,0.8,0.5,1.3,0.5c0.4,0,1.2-0.5,1.6-0.7c0.5-0.2,0.2-0.6,0.4-1.2c0.1-0.5,1.2-0.2,1.8-0.1c0,0.2,0.1,0.3,0.4,0.4
				c0.4,0.2,0.7,0.4,1.1,0.5c0.4,0,0.1,0.1,0.5,0.6c0.4,0.5,0.4,0.2,0.6,0.5c0.2,0.2,0.6,0,0.9-0.2c0.3-0.2,0.8,0.1,1.4-0.2
				c0.6-0.2,1.4-0.3,2.1-0.1c0.7,0.1,0.2,0.9,0,1.2c-0.3,0.3,0.1,0.3,0.3,0.3c0.2,0,0.3,0.1,0.3,0.3c0,0.2,1.1-0.2,1.5-0.4
				c0.3-0.2,0.3,0.4,0.3,0.9c0.1,0.5,0.9,0.2,1.3,0.1c0.3-0.1,0.3,0.5,0.4,0.8c0.2,0.2,0.5,0.4,0.9,0.8c0.4,0.4,0.7,0.2,0.9,0
				c0.2-0.2,0.6-0.2,0.9-0.3c0.3-0.1,1-0.1,1.4-0.1c0.3,0,0.6,0.4,0.8,0.6c0.1,0.1,0.2,0.2,0.3,0.3c0.3,0.2,1,0.5,1.2,0.7
				c0.2,0.2,0.8,0.3,1.5,0.5c0.7,0.2,0.3,0.9,0.5,1.3c0.2,0.4,1,0.2,1.2-0.1c0.2-0.3,0.5,0,0.9,0.1c0.2,0,0.3-0.1,0.4-0.3
				c0.1-0.2,0.1-0.5,0.1-0.7c0.1-0.4,0.5-0.5,0.3-1.1c-0.2-0.6-0.3-0.8-0.6-1.2c-0.3-0.4,0.1-0.3,0.1-1.3c0-1,0.2-0.2,0.7-0.5
				c0.4-0.3,0.7-0.1,0.9,0.1c0.2,0.2,0.5-0.3,0.7-0.6c0.2-0.4,0.5,0.1,0.9,0.1c0.4,0,0.3-0.5,0.4-1c0.1-0.4-0.6-0.3-0.7-0.6
				s-0.8-0.2-1.1-0.2c-0.4,0-0.3-0.7-0.2-1.1c0-0.5-1-0.5-1.7-0.6c-0.7-0.1,0.4-0.7,0.7-0.8c0.2-0.1,0.4,0.3,0.7,0.5
				c0.3,0.2,0.3,0,0.6-0.2c0.3-0.2,0.9,0.3,1,0.5c0.2,0.2,0.5-0.1,0.6-0.4c0.1-0.3,1.1,0.5,1.5,0.6c0.4,0.1,0.5-0.2,0.6-0.5
				c0.1-0.3,0.5,0.4,0.8,1c0.2,0.5,0.7-0.3,1.2-0.5c0.5-0.2,0.5,0,0.9-0.7l0.1-0.1c0.4-0.8,0.2-0.4,0.1-0.7c-0.1-0.3-0.2-0.4,0.2-0.9
				s-0.2-0.8-1-1c-0.8-0.3-0.1-0.1-0.2-0.5c-0.1-0.4-0.7-0.4-1.1-0.5c-0.4-0.1-0.1-0.5,0.1-0.9c0.2-0.4-0.2-0.8-0.5-1.3
				c-0.3-0.5-0.3-0.3-0.7-0.5c-0.4-0.2,0.1-0.2,0.4-0.4c0.3-0.2,0-0.4-0.2-0.6c-0.2-0.2-0.1-0.4,0.2-1c0.1-0.1,0.1-0.2,0.1-0.3
				c0-0.4-0.5-0.6-0.8-1.1c-0.5-0.6,0.1-0.8,0.6-1.6c0.6-0.8,0-0.9,0.1-1.5c0.1-0.6,0.4-0.7,0.8-1.1c0.4-0.5,0.1-0.4,0.2-0.9
				c0-0.5-0.1-0.7,0.1-1.5c0.2-0.9-0.6-1.1-0.9-1.6c-0.3-0.5-0.2-2.3-0.3-2.6c-0.1-0.3,0.4-0.9,0.6-1.4c0.2-0.5-0.2-0.5-0.5-0.8
				c-0.3-0.3,0.1-0.3,0.3-0.4c0.2-0.1-0.1-0.5-0.2-0.6c-0.1-0.1-0.1-0.4,0-0.5c0.1-0.1,0.1-0.3-0.1-0.4c-0.2-0.1,0.1-0.2,0.5-0.5
				c0.4-0.2,0-0.2-0.1-0.5c-0.1-0.3,1.3-1.1,1.5-1.4c0.3-0.3,0.2-0.4-0.2-0.7c-0.4-0.3,0.3-0.3,0.8-0.6c0.4-0.3,0.1-0.5,0.3-1.3
				c0.2-0.9-0.8-2.4-1-3.2c-0.1-0.9,1.6-2.1,2.2-2.5c0.6-0.4,0-0.6,0.1-0.8c0-0.2,0.1-0.4,0.4-0.6s0-1.1,0.2-1.6
				c0.2-0.5,0.6-0.5,0.8-0.6c0.2-0.1,0.1,0,0.1-0.4c0.1-0.4,0.2-0.5,0.4-0.8c0.2-0.3-0.2-0.6-0.5-0.9c-0.3-0.3,0.3-0.9,0.3-0.9
				c-0.2-0.3-0.3-0.5-0.6-0.4c-0.5,0.2-3.1,2.3-3.3,2.2c-0.2-0.1-0.4-1-0.7-0.8c-0.3,0.2-0.4,0.5-0.8,0.4c-0.4-0.1-0.6-0.3-0.9,0
				c-0.3,0.3-1.1,1.3-1.6,1.4s-0.4-0.4-0.7,0.3c-0.2,0.6-0.6,1.5-0.3,1.8c0.3,0.3,0.5,0.6,0.3,0.9c-0.1,0.2-0.3,0.4-0.6,0.3
				c-0.3,0-1-0.5-1.3,0.3c-0.3,0.8-1.3,1.6-1.8,2.1c-0.5,0.5-2.5,1.5-2.6,1.9c0,0.5-0.5,1.8-1.3,2.1c-0.8,0.3-1.1,0.2-1.2-0.3
				c-0.1-0.5,0.2-0.6-0.3-0.7c-0.5-0.1-0.6,0.4-1.3,0.3c-0.6,0-1.7,0.1-2.1-0.3c-0.1,0-0.1-0.1-0.1-0.2c-0.2-0.6,0-0.8-0.5-1.1
				c-0.5-0.2-0.5-0.2-0.7-0.6c-0.2-0.4-0.2-0.8-0.8-0.9c-0.5,0-3.7-0.5-3.6-1.2c0.2-0.7,1.2-0.5,1.1-0.9c0-0.4,0.5-1.8,0.7-2.1
				c0.3-0.3,1.5-1.2,1.2-1.9c-0.4-0.7-0.3-0.7-0.4-1.2c-0.1-0.5-0.7-1.2-1.2-1.1c-0.6,0.1-1,0.7-1.4,0.7c-0.4,0-0.8-0.2-0.9,0.2
				c-0.1,0.4-0.4,0.9-0.7,0.7c-0.3-0.1-0.4-0.1-0.4,0.3c0,0.4,0,0.8-0.2,1.2c-0.2,0.3-0.9,0.5-0.9,0.8c0,0.2-0.5,0.9-0.5,1.2
				c0,0.3-0.1,1.4-0.1,1.6c0.1,0.3,0.1,0.6,0.1,1c-0.1,0.4-0.2,1.6-0.1,1.9c0.1,0.3,0.1,0.8-0.4,0.9c-0.5,0.1-1.3,0.3-1.7,0
				c-0.3-0.2-0.6-0.2-1.1,0c-0.5,0.3-1.7,1.2-1.9,1.2c-0.3,0-0.7-0.1-1,0c-0.2,0.2-0.6,0.3-0.7,0c-0.1-0.2-0.4-0.5-0.7-0.3
				C88.4,40.9,87.6,41.5,87.2,41.6z"/>
		</svg>
		</div>


		<!-- 华北 -->
		<div style="width:142px; height:129px; left: 147px; top:63px;">
		<svg id="area_100001" viewBox="0 0 142 129" class="area_stroke hidden " version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" >
		<path d="M141.9,14.6c0.3-0.5-0.2-1.2-0.9-1.5c-0.7-0.3-1.6-1.9-1.9-2.2c-0.2-0.3-0.9-0.5-1.4-0.5s-0.6,1.2-0.9,1.5
			c-0.3,0.3-0.8,0.6-1.2,0.6c-0.4,0-0.7,0.2-1,0.6c-0.2,0.4-0.9-0.2-1.5-0.3c-0.6-0.1-2.7,1-2.7,1l-2.4-0.2c-0.2,0-0.9-1.7-1-1.9
			c-0.1-0.2-0.8-2.3-1-2.7c-0.3-0.4-0.2-0.8,0.3-1c0.4-0.1-0.8-0.8-1.3-0.9c-0.6-0.1-1.2-1.1-1.3-1.4c-0.1-0.3-0.7,0.1-0.9,0.5
			c-0.1,0.4-0.2,0.4-0.5,0.9c-0.4,0.4-0.6,0.3-1-0.2c-0.3-0.5-1.7-1.2-2.4-1.2c-0.7-0.1,0.2-0.9,0.8-1.3c0.6-0.4,0.4-0.7,0.6-1.2
			c0.2-0.5,0.6-1,0.8-1.1c0.2-0.1-1.3-2-1.3-2c-0.5,0.2-0.9,0.4-1.2,0.5c-1,0.4-1.2-0.1-1.9,0c-0.6,0.1-2.7,3.1-3.3,3.3
			c-0.6,0.3,0,1-0.4,1.7c-0.4,0.7,2-0.4,2.7-0.2c0.7,0.1,0.4,1.2,0.2,1.7s0,0.1,0.5,0.6c0.4,0.5,0.1,1.5,0,2.1
			c-0.1,0.6-0.2,0.3-0.7,0.4c-0.6,0.1-0.4,0.2-0.7,0.7c-0.3,0.5-0.6,0.6-1.2,1.1c-0.6,0.4-0.6,1.4-1,2.2c-0.4,0.8-0.2,0.9-0.2,1.5
			c-0.1,0.6-0.9,0.9-1.2,1.2c-0.3,0.3-0.2,0.7-0.2,1.2c0,0.5-0.4,0.7-0.7,1c-0.3,0.3-0.8,1.1-1.1,1.6c-0.2,0.5,0.1,0.6,1,0.6
			c0.8,0-0.2,1.4-0.6,2c-0.4,0.6-0.9,0.3-1.8,0.4c-0.9,0.1-3,2.6-3.7,3.1c-0.7,0.5-1.7,0.6-2-0.4c-0.3-0.9-1.1-0.2-2,0.1
			c-0.9,0.2-1.5-1.1-1.5-1.1s-2,3.4-2.5,4.2c-0.5,0.8-0.7,1.4-0.8,2.2c0,0.8-0.8,1.3-1.2,1.9c-0.5,0.6,0,1.1,0,1.8
			c0,0.7-0.9,1-1.4,1.6c-0.5,0.6,0.7,1.9,1.7,2.6c1,0.7,1.2-0.2,1.8-0.7c0.6-0.6,0.9-0.2,1.5,0.1c0.6,0.3,0.9-0.2,1.7-0.6
			c0.8-0.4,1.5,1.1,2,1.7c0.5,0.5,1-1.3,1.9-2.2c0.9-1,1.9-0.6,2.5-0.8c0.7-0.2,1.2,0.6,2,1c0.7,0.4,0.3,0.6,1.3,1
			c1,0.4,0.8,0.3,1,0.7c0.2,0.4,0.8,0.7,1.2,0.9c0.4,0.3,0.5,1.1,0.7,1.5c0.2,0.3,1.3,0.5,2.2,1.3c0.8,0.7,0.2,0.6,0.2,1.8
			c0.1,1.2-1.7,0.9-2.1,1.3c-0.4,0.4-1.7-0.5-2-1.1c-0.3-0.5-1.5,0.1-2,0.3c-0.5,0.2-0.4,0.2-1.4,0c-1-0.2-2.1,1.4-2.5,1.8
			c-0.4,0.4-0.9-0.1-1.4-0.7c-0.5-0.6-1.1,1.4-1.2,2.2c-0.1,0.8-1,0.1-1.3-0.4c-0.3-0.5-0.9-0.2-1.2-0.3c-0.4-0.2-2.5,2.2-2.8,2.8
			c-0.4,0.5-0.3,0.8,0,1.4c0.4,0.6-0.1,0.7-1.2,0.9c-1.2,0.1-0.6,0.4-1.5,1c-0.8,0.7-1,0.5-1.5,0.5c-0.5,0-1.2,0.2-1.7,0.5
			c-0.5,0.3-0.7-0.1-1.4-0.6c-0.8-0.6-1.9,1.9-2.6,2.8c-0.7,1-1.6,1.5-2.2,1.9c-0.6,0.4-2.3,0-3.6-0.1c-1.3-0.1-1.6-0.6-1.9-1
			c-0.3-0.4-0.8-0.4-1.2-0.5c-0.5-0.1-0.9-0.3-1.8-0.6c-0.8-0.3-0.7,0.2-1.3,1c-0.6,0.8-1,1.7-1.2,2.2c-0.2,0.5-0.5,1.1-0.9,1.5
			c-0.4,0.4,1.1,2.2,2.1,3.2c0.9,1-0.3,1.9-1.1,2.6c-0.9,0.6-1,0.6-2.2,0.9C70,69.3,68.2,72,68,72s-2.2,1.9-2.2,1.9l-2,0
			c0,0-1.3,0.7-1.8,1.2c-0.5,0.4-1.3,0.7-2.4,0.4s-5.4-0.2-6.9-0.3c-1.4,0-5.9,0.9-8.2,1.6c-2.3,0.7-6.2,3.1-6.3,3.1
			c-0.1,0-2.5-0.7-2.5-0.7l-0.1-1.6L32,78.1l-4.3-2.1l-4.4-1.6l-1.4-2.5l-5.4-0.7c-0.1,0-3.5-1.4-3.6-1.4c-0.1,0-4.4,0.7-4.4,0.7
			s-7.7-2.1-7.8-2.1c0,0,0.2,4.4,0.1,5.2C0.7,74.3,0,75,0,76.1s0.4,1.6,0.5,2.1c0.1,0.5,0,2.2-0.1,2.6c-0.1,0.4,0.8,1.2,1,1.7
			c0.2,0.5,0.4,1.2,0.5,1.7c0.2,0.5,0.9,0.4,1.4,0.5c0.5,0.1,0.8-0.6,1.1-0.8c0.3-0.2,0.6-0.1,1.1,0c0.6,0,0.9-0.8,1.3-1.1
			c0.4-0.3,1.4,0.2,1.7,0.3c0.3,0.2,1.3,0,2,0.2c0.7,0.2,0.4,1.5,0.2,2c-0.2,0.5-0.3,1.2-0.3,1.7c0,0.5-0.7,0.3-1.1,0.2S9,87.5,9,87.8
			c0,0.4,0.6,0.7,0.9,1.1c0.4,0.4,0.8,0.8,1.4,1.1c0.6,0.3,0.4,0.6,0.5,1.1c0.2,0.6,2,0.5,2.7,0.6c0.6,0.1,0.4,1.1,0.2,1.5
			c-0.2,0.4,0.1,1.7,0.5,2.2c0.4,0.5,1.7,0.5,2,0.5c0.4,0,0.6,0.2,1,0.6c0.4,0.4,1.3,0.2,1.7,0.2c0.4,0.1,0.8,0.4,1.3,0.4
			c0.5,0,2.2-1.8,2.5-2.2c0.3-0.4,1.1-0.1,1.8,0.1s0.7-0.5,1.1-0.8c0.4-0.4,2.4,0,3.3,0.1c0.9,0.2,1.4,0.7,2.1,1.1
			c0.7,0.4,0.5,1.1,0.5,1.3c0,0.2-0.1,0.6-0.6,1c-0.6,0.4-0.4,0.7-0.1,0.9s0,0.7,0,1.4c0,0.8-2.1,0.9-2.5,0.9c-0.4,0-0.5,0.4-0.3,0.6
			c0.2,0.2,0.1,0.4,0,0.9c0,0.5-0.6,0.8-0.6,1.6c0,0.8,0.6,0.9,1.3,1.3c0.7,0.3,0.3,0.7,0.4,1.1c0.1,0.4,0.4,0.4,0.6,0.4
			c0.2,0,0.7,0.6,1,0.6h0.1c0.4-0.1,1.2-0.6,1.5-0.7c0.2-0.1,0.5,0.1,0.6,0.3c0.1,0.2,0.5,0.2,0.7,0c0.2-0.2,0.7,0,1,0
			c0.3,0,1.5-0.9,1.9-1.2c0.5-0.3,0.7-0.3,1.1,0c0.3,0.2,1.2,0.1,1.7,0c0.5-0.1,0.5-0.6,0.4-0.9c-0.1-0.3,0-1.5,0.1-1.9
			c0.1-0.4,0-0.7-0.1-1c-0.1-0.3,0-1.3,0.1-1.6c0-0.3,0.5-1,0.5-1.2c0-0.2,0.7-0.4,0.9-0.8c0.2-0.3,0.2-0.8,0.2-1.2
			c0-0.4,0.1-0.4,0.5-0.3c0.3,0.1,0.7-0.3,0.7-0.7c0.1-0.4,0.5-0.3,0.9-0.2c0.4,0,0.8-0.5,1.4-0.7c0.6-0.1,1.1,0.5,1.2,1.1
			c0.1,0.6,0.1,0.6,0.4,1.2c0.4,0.7-0.9,1.6-1.2,1.9c-0.3,0.3-0.8,1.7-0.7,2.1c0,0.4-1,0.2-1.1,0.9c-0.2,0.7,3.1,1.2,3.6,1.2
			c0.5,0,0.6,0.4,0.8,0.9c0.2,0.4,0.2,0.4,0.7,0.6c0.5,0.2,0.3,0.5,0.5,1.1c0,0.1,0.1,0.1,0.1,0.2c0.4,0.3,1.5,0.2,2.1,0.3
			c0.7,0,0.7-0.4,1.3-0.3c0.5,0.1,0.2,0.2,0.3,0.7c0.1,0.5,0.4,0.5,1.2,0.3c0.8-0.3,1.2-1.6,1.3-2.1c0-0.5,2.1-1.5,2.5-1.9
			c0.5-0.5,1.5-1.2,1.8-2.1c0.3-0.8,1-0.4,1.3-0.3c0.3,0,0.4-0.1,0.6-0.3c0.1-0.2-0.1-0.6-0.3-0.9c-0.3-0.3,0.1-1.2,0.3-1.8
			c0.2-0.6,0.2-0.2,0.7-0.3c0.5-0.1,1.2-1,1.6-1.4c0.3-0.3,0.5-0.1,0.9,0c0.4,0.1,0.5-0.2,0.8-0.4c0.3-0.2,0.5,0.7,0.7,0.8
			c0.2,0.1,2.8-1.9,3.3-2.2c0.2-0.1,0.4,0.1,0.6,0.4c0,0-0.6,0.6-0.3,0.9c0.3,0.3,0.6,0.6,0.5,0.9c-0.2,0.3-0.3,0.4-0.4,0.8
			c0,0.3,0.1,0.3-0.1,0.4c-0.2,0.2-0.6,0.1-0.8,0.6s0.1,1.4-0.2,1.6c-0.3,0.2-0.4,0.4-0.4,0.6c0,0.2,0.5,0.4-0.1,0.8
			c-0.6,0.4-2.3,1.7-2.2,2.5c0.1,0.9,1.1,2.4,1,3.2c-0.2,0.9,0.2,1-0.3,1.3c-0.5,0.3-1.1,0.3-0.8,0.6c0.4,0.3,0.5,0.5,0.2,0.7
			c-0.3,0.3-1.7,1.1-1.5,1.4c0.1,0.3,0.5,0.3,0.1,0.5c-0.4,0.2-0.7,0.3-0.5,0.4c0.2,0.1,0.2,0.3,0.1,0.4c-0.1,0.1-0.1,0.4,0,0.5
			c0.1,0.1,0.4,0.5,0.2,0.6c-0.2,0.1-0.5,0.1-0.3,0.4c0.3,0.3,0.7,0.3,0.5,0.8c-0.2,0.5-0.8,1-0.6,1.3c0.1,0.3,0,2.1,0.3,2.6
			c0.3,0.5,1.1,0.7,0.9,1.6c-0.2,0.9-0.1,1-0.1,1.5c0,0.5,0.2,0.4-0.2,0.9c-0.4,0.5-0.7,0.5-0.8,1.1c-0.1,0.6,0.5,0.7-0.1,1.5
			c-0.6,0.8-1.1,1-0.6,1.6c0.4,0.5,0.8,0.7,0.8,1.1c0.1,0,1.2,0,1.8,0.1c0.6,0,1.6-0.5,2.1-1.1s1.7-0.2,2.4-0.4c0.8-0.3,1-0.9,1.2-1.2
			c0.2-0.3,0.7-0.3,1.2-0.3c0.5,0,0.5-0.3,0.5-0.8c0-0.5,0.7-0.3,1.1-0.2c0.4,0,0.9,0,1.3-0.4c0.4-0.4,1.1,0.2,1.8,0.3
			c0.8,0.1,1.7-0.5,2.1-1.1c0.4-0.6,1-0.6,1.6-0.9c0.6-0.2,0.5-1,0.7-1.2c0.2-0.2-0.1-0.7,0.1-0.8c0.2-0.1-0.1-0.4,0.2-0.6
			c0.3-0.2,0-0.8,0.2-1.1c0.2-0.3,0.3-1.3,0.4-1.8v-0.2c0-0.3,0.4,0.2,0.7,0.5c0.3,0.3,1.9,0.4,2.3,0.3c0.4-0.1,0.4,0.3,0.7,0.7
			c0.3,0.5,1.1,0.1,1.8,0.2c0.7,0.1,0.7,0.5,0.9,0.7s0.3-0.2,0.4-0.8c0.1-0.6,0.4-0.4,0.6-0.5c0.2-0.1,0.8,0.7,1.3,0.8
			c0.3,0,0.4,0,0.5-0.1c0.1-0.1,0.1-0.3,0.1-0.5c0.1-0.7-0.4-0.6-0.5-1.3c-0.2-0.7,0.4-1.1,0.6-1.9c0.2-0.8,1.4-1.3,1.6-1.9
			c0.2-0.6,0.1-0.5,0.5-1.4c0.4-0.8,0.5-0.4,0.8-0.5c0.3-0.2,0.2-0.3,0.1-0.6c-0.1-0.4,0.3-0.4,0.6-0.5c0.3-0.1,0.2-0.1,0.4-0.6
			c0.2-0.5,1.1-0.7,1.5-0.8c0.4-0.1,0.6-0.3,1-0.9c0.4-0.6,0.7-0.7,1.1-1.1c0.5-0.4,1,0,1.4,0.2c0.4,0.2,0.7-0.2,1.4-0.2
			c0.7,0.1,0.7-0.5,0.7-0.9c0.1-0.4,0.4-0.5,0.7-0.6c0.4-0.1,0.5-0.2,0.8-0.7c0.4-0.5,1.2-0.8,1.2-0.8c-0.1,0-0.2,0-0.2-0.1
			c-0.3-0.1-0.6-0.3-0.8-0.6c-0.2-0.2-0.4-0.4-0.6-0.6c-0.2-0.2-0.4-0.3-0.5-0.6c0-0.1,0-0.2-0.1-0.2c0-0.2,0-0.4-0.1-0.6
			c0-0.3,0-0.6,0.1-0.8c0.1-0.3,0.3-0.5,0.5-0.7c0.2-0.3,0.3-0.5,0.3-0.9c0-0.3,0-0.6,0.1-0.8c0.1-0.2,0.3-0.5,0.5-0.6
			c0.1-0.1,0.2-0.1,0.3-0.2l0.2,0c0.3,0,0.6,0,0.9,0.1c0.2,0.1,0.6,0.1,0.7,0.4c0,0.1,0,0.1,0.1,0.2c0,0.1,0.1,0.1,0.1,0.2
			c0.1,0.1,0.2,0.1,0.3,0.2c0.2,0,0.3,0,0.5,0c0.4,0,0.6-0.3,0.6-0.6c0-0.1,0-0.2,0-0.3c0-0.1,0-0.1,0-0.2c0.1-0.2,0.1-0.2,0.3-0.2
			c0.1,0,0.1,0,0.2,0l0.2,0c0.2,0,0.4,0.1,0.5,0.2c0.1,0.1,0.2,0.3,0.4,0.3c0.1,0,0.1-0.1,0.2-0.2s0.1-0.1,0.2,0c0,0,0.1,0.1,0.1,0.1
			c0.1,0.1,0.3-0.2,0.4-0.3c0.2-0.1,0.3-0.3,0.4-0.4c0.1-0.1,0.2-0.2,0.4-0.3c0.1-0.1,0.3-0.1,0.4-0.1c0.1,0,0.2,0,0.3-0.2
			c0.1-0.1,0-0.3,0-0.4c0-0.2,0.1-0.3,0.1-0.5l0.1-0.6c0-0.4,0.1-0.8,0.3-1.1c0.1-0.1,0.2-0.2,0.4-0.3c0.2-0.2,0.4-0.3,0.5-0.5
			c0.1-0.1,0.2-0.3,0.4-0.4c0.2-0.1,0.3-0.2,0.4-0.3c0.2-0.1,0.3-0.3,0.5-0.5c-0.3-0.4-0.9-1.1-1.3-1.5c-0.6-0.5-0.4-0.4-0.4-1.2
			c0.1-0.8-0.8-0.5-1.3-0.4c-0.4,0.1-0.5-0.4-0.6-0.8c0-0.3-0.5-0.2-0.8-0.1c-0.4,0.1-0.6-0.2-0.8-0.6c-0.2-0.3-0.2,0.2-0.4-1
			c-0.2-0.9,1.4-2.3,2.1-2.9c0.5,0,0.5-0.2,0.8-0.9c0.3-0.8-0.9-2.3-0.9-2.3s-0.2-0.8-0.3-1.2c0-0.4,0.4-0.5,0.6-0.8
			c0.2-0.2-0.3-1.1-0.4-1.5c-0.2-0.3,0-0.4,0.4-0.5c0.4-0.1,0.4-0.2,0.6-0.7c0.2-0.4,0.6,0.6,0.8,1.2c0.2,0.6,0.5,0.1,1,0.2
			c0.6,0.1,0.1,0,0.3,0.6c0.2,0.6,0.6,0.6,1,0.9s0,0.5,0.1,0.9c0.1,0.4,0.9-0.5,1.5-0.8c0.6-0.3,0.3-0.7,0.4-0.8
			c0-0.1,0.3-0.4,0.6-0.5c0.2-0.1,0.9-0.9,1.3-1.3c0.4-0.4,0.4-0.2,0.8-0.3c0.4-0.1,0.4,0.2,0.6,0.5c0.2,0.4,1.4-0.9,1.9-1.4
			c0.5-0.5,0.8-0.5,0.9-0.4c0.2,0,0.9,0,1.4-0.4c0.5-0.4,0.7-0.6,0.7-1c0-0.4,0.9-0.6,1.2-0.5c0.4,0,0.4,0.7,0.9,0.5
			c0.5-0.2-0.3-1-0.2-1.4c0.1-0.5,0.8-0.2,1.2-0.1c0.4,0.1,0.9,0.6,1.6,0.6c0.6,0.1,1.6-1.1,1.9-1.7c0.3-0.6,0.9-0.7,1.5-0.9
			c0.6-0.3,0.6-0.1,0.6-1c0-0.5-0.2-0.8-0.4-1c-0.2-0.2-0.4-0.2-0.5-0.3c-0.3-0.2-0.6-0.6-0.5-1c0.1-0.4-0.2-1.5-0.4-1.8
			c-0.2-0.3-0.7-1.1-0.6-1.8c0.1-0.6-1-0.8-1.3-1c-0.3-0.2-0.3-0.7-0.2-1.4c0.1-0.7-0.8,0.3-1.3,0.8c-0.4,0.5-0.8,0.5-1.6,0.7
			c-0.8,0.1-0.6,0-1,0.6c-0.4,0.6-0.1-0.3-0.3-0.5c-0.2-0.3-0.2-0.7,0.1-0.9c0.3-0.2-0.6-1-1-1.3c-0.4-0.3-0.2-2.4-0.2-2.9
			c0-0.5,0.2-0.5,0.6-0.7c0.4-0.2,0.2-0.5,0-1c-0.2-0.5-0.7-0.3-0.9-0.1c-0.3,0.2-1.2-1.5-1.4-1.8c-0.2-0.3-0.8-0.5-0.8-0.9
			c0-0.4,1-0.4,1.6-0.3c0.5,0.1,1.4-0.5,2.1-0.8s0.7-0.1,1.1,0.4c0.4,0.4,0.7,0.4,1.5-0.2c0.8-0.6,1-1.4,1.1-2.1c0-0.2,0-0.4,0-0.5
			c-0.1-0.3-0.4-0.3-0.7-0.4c-0.4-0.1-0.6-1-0.6-1.5c0-0.5,0.3-0.7,0.6-0.8c0.3,0,0.4,0.3,0.7,0.4c0.3,0.1,1.6-0.9,1.6-1.6
			c0-0.7-0.5-0.4-0.8,0c-0.4,0.4-0.4,0-0.6-0.4c-0.2-0.4-0.6,0.1-1,0.6c-0.4,0.5-1-1.1-1.2-1.5c-0.2-0.4-0.2-0.2-1-0.3
			c-0.8-0.2-0.8-1-1-1.9c-0.1-0.9,0.7-1.7,1-1.9c0.3-0.2,1.8-1.2,2.2-2c0.4-0.8,1.8-1.5,1.9-1.6c0.2-0.1,1.7-2.4,2.2-2.8
			c0.5-0.4,0.6-0.2,0.5,0c0,0.2,0.1,0.8,0.1,0.8c0.1,0.4,0.6,1.1,1,1.4c0.4,0.4,0.4-1.2,0.1-1.7c-0.3-0.5,0.1-1.2,0.1-1.8
			s0.7-2.3,0.7-2.6c0-0.4,0.2-0.8,0.7-1.1s0.4,0,0.7,0.2c0.3,0.2,0.6-0.7,0.8-1.4c0.2-0.7-0.5-1.7-0.6-1.8c-0.1-0.1,0.1-1.1,0-1.3
			c-0.1-0.2-0.1-0.9,0.1-1c0.2-0.1,0.1-0.6,0-0.8c0-0.2,0.4-0.8,0.6-1.1c0.1-0.3,0.3-0.8,0.3-1.3s0.2-0.5,0.5-0.8
			c0.3-0.3,0.4-1.1,0.2-1.6C140.8,15.7,141.6,15,141.9,14.6z"/>
		</svg>
		</div>


		<!-- 东北  暂时用中南的ID 100003-->
		<div style="width:75px; height:102px; left: 258px; top:61px;">
		<svg id="area_100003" viewBox="0 0 75 102" class="area_stroke hidden " version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" >
		<path d="M63.6,64.1c0.1-0.2,0.1-0.3,0-0.5c-0.1-0.1-0.2-0.2-0.3-0.3c-0.1,0-0.2-0.1-0.3-0.2c-0.2-0.2-0.3-0.4-0.4-0.6
			c-0.1-0.4-0.1-0.7-0.1-1.1c0-0.2,0.1-0.3,0.1-0.4c0-0.2,0.1-0.4,0.1-0.6c0.1-0.4,0.2-0.8,0.1-1.2c0-0.3-0.1-0.6-0.3-0.9
			c-0.2-0.3-0.2-0.7-0.4-1c-0.2-0.4-0.3-0.8-0.7-1.1c-0.2-0.1-0.3-0.3-0.5-0.4c-0.1-0.1-0.2-0.3-0.3-0.5c-0.1-0.1-0.2-0.3-0.2-0.5
			c-0.1-0.3,0.1-0.7,0.3-0.9c0.3-0.2,0.6-0.2,0.9-0.2c0.4-0.1,0.8-0.1,1.1-0.4c0.1-0.1,0.2-0.3,0.2-0.5c0.1-0.2,0.2-0.4,0.3-0.5
			c0.2-0.3,0.2-0.7,0.4-1c0-0.1,0-0.2,0.1-0.4c0.1-0.2,0.3-0.3,0.4-0.4c0.2-0.1,0.3,0.1,0.5,0.2c0.2,0.1,0.4,0.1,0.5,0.2
			c0.2,0.1,0.3,0.1,0.5,0.1c0.5,0,1,0.1,1.5,0.1c0.8,0.1,1.8,0.3,2.6,0c0.3-0.1,0.6-0.4,0.7-0.7c0.1-0.3,0-0.7,0-1
			c-0.1-0.4-0.1-0.7,0.1-1c0.2-0.4,0.5-0.6,0.8-1c0.2-0.3,0.3-0.6,0.3-1c0-0.3-0.1-0.7,0-1c0.2-0.7,0.8-1.1,1.1-1.8
			c0.3-0.6,0.4-1.2,0.3-1.9c-0.1-0.3-0.2-0.6-0.3-1c-0.1-0.3,0-0.6,0.1-0.9c0.1-0.3,0.2-0.5,0.2-0.8c0-0.3-0.1-0.6-0.2-0.9
			c0-0.2,0-0.4,0-0.6c0-0.3,0.1-0.7,0.2-1c0.1-0.2,0.3-0.6,0.1-0.9c-0.1-0.1-0.2-0.1-0.3-0.1c0.2-0.4,0.4-0.9,0.8-1.2
			c0.3-0.3,0.8-0.3,1-0.7c0.2-0.4,0.3-0.8,0.4-1.2c0.1-0.4,0.2-0.8-0.2-1.1c-0.3-0.3-0.7-0.5-0.8-0.9c-0.2-0.4,0-0.8,0.1-1.1
			c0.2-0.5,0.4-1,0.2-1.5c-0.1-0.3-0.4-0.6-0.8-0.5c-0.2,0-0.5,0.1-0.7,0.2l-0.7,0.3c-0.2,0.1-0.4,0.2-0.6,0.3
			c-0.3,0.2-0.7,0.5-0.9,0.8c-0.2,0.3-0.7,0.5-1,0.7c-0.2,0.1-0.4,0.4-0.4,0.6c-0.1,0.2-0.1,0.5-0.4,0.5c-0.1,0-0.2,0-0.4,0
			c-0.1,0-0.3,0-0.4-0.1c-0.2-0.1-0.4-0.1-0.6,0c-0.2,0.1-0.4,0.3-0.5,0.4c-0.2,0.2-0.4,0.3-0.6,0.5c-0.2,0.2-0.4,0.4-0.6,0.5
			c-0.2,0.1-0.4,0.1-0.5,0.3C65.2,32.6,65,32.8,65,33c-0.1,0.2-0.1,0.4-0.3,0.6c-0.1,0.1-0.2,0.1-0.3,0.2c-0.1,0.1-0.2,0-0.3,0
			c-0.2,0-0.4-0.1-0.6-0.1c-0.1,0-0.2,0-0.3,0.1C63,33.9,62.9,34,62.8,34c-0.3,0.1-0.5,0.3-0.8,0.4c-0.1,0.1-0.3,0.1-0.4,0.1
			c-0.1,0-0.2,0-0.3,0c-0.2,0.1-0.4,0.3-0.7,0.3c-0.2,0-0.5,0-0.7-0.1c-0.1,0-0.1-0.1-0.2-0.1c0-0.1-0.1-0.2-0.2-0.2
			c-0.1,0-0.2,0.1-0.3,0.1l-0.1,0.1c-0.2,0.2-0.5,0.5-0.8,0.5c-0.1,0-0.2,0-0.4,0.1c-0.2,0-0.5,0-0.8-0.1c-0.1,0-0.2,0-0.2-0.1
			c-0.1-0.1-0.1-0.2-0.2-0.4c-0.1-0.2-0.3-0.4-0.4-0.6c-0.1-0.2-0.3-0.3-0.4-0.5c-0.2-0.3-0.4-0.5-0.5-0.8c-0.1-0.1-0.3-0.3-0.3-0.5
			c0-0.2,0.2-0.2,0.3-0.4c0.3-0.3,0.3-0.7,0.1-1c-0.1-0.2-0.3-0.3-0.5-0.4c-0.1-0.1-0.3-0.1-0.5-0.2c-0.4-0.1-0.5-0.5-0.6-0.9
			c0-0.3,0.1-0.6,0.1-0.8c0-0.2,0.1-0.5,0-0.7c0-0.1,0-0.2-0.1-0.2c0,0-0.2,0-0.2,0c-0.2,0-0.3,0.1-0.4,0.1c-0.2,0-0.4-0.1-0.6-0.1
			c-0.1,0-0.2,0-0.2,0.1c-0.2,0-0.4,0-0.6,0.1c-0.2,0.1-0.4,0-0.5-0.1c-0.2-0.1-0.3-0.3-0.4-0.5c-0.1-0.2-0.3-0.3-0.6-0.4
			c-0.2-0.1-0.5-0.1-0.7-0.2c-0.2-0.1-0.2-0.2-0.3-0.4c0-0.1-0.1-0.2-0.2-0.2c-0.1,0-0.3,0-0.4-0.1c-0.3-0.2-0.3-0.8-0.6-1.1
			c-0.2-0.3-0.6-0.3-0.9-0.1c-0.2,0.1-0.4,0.2-0.5,0.4c-0.1,0.1-0.3,0.3-0.5,0.3c-0.3,0-0.4-0.4-0.7-0.5c-0.1,0-0.3,0-0.4,0
			c-0.2,0-0.3-0.1-0.4-0.2c-0.2-0.2-0.4-0.6-0.7-0.6c-0.2,0-0.4,0.1-0.5,0.1c-0.2,0-0.4,0.1-0.5,0.2c-0.4,0.2-0.5,0.6-0.9,0.8
			c-0.1,0.1-0.3,0.1-0.4,0.1c-0.1-0.1-0.2-0.1-0.3-0.1c-0.3,0-0.5,0.2-0.7,0c-0.1-0.1-0.2-0.2-0.3-0.3c-0.1-0.1-0.3-0.1-0.4-0.2
			c-0.3-0.1-0.5-0.2-0.7-0.5c-0.1-0.3-0.3-0.5-0.5-0.7s-0.4-0.4-0.7-0.6C38.1,22.6,38,22.3,38,22c0-0.3,0.1-0.6,0.2-0.9
			c0-0.3,0-0.5-0.2-0.7c-0.1-0.1-0.2-0.2-0.3-0.3c-0.1-0.1-0.1-0.2-0.1-0.3c-0.1-0.2-0.3-0.3-0.4-0.4c-0.1-0.1-0.2-0.3-0.2-0.4
			c0-0.2-0.1-0.3-0.2-0.5c0-0.1,0-0.3,0-0.4c-0.1-0.2-0.3-0.4-0.4-0.6c-0.2-0.3-0.3-0.6-0.4-0.9c-0.2-0.4-0.6-0.7-1-1
			c-0.3-0.2-0.7-0.4-0.8-0.8c0-0.1,0-0.2,0.1-0.4c0.1-0.1,0.1-0.2,0.1-0.4c0-0.1,0-0.3,0-0.4c0-0.2-0.1-0.2-0.3-0.2
			c-0.1-0.1-0.2-0.1-0.2-0.3c0-0.1,0-0.2,0-0.4c-0.2-0.2-0.4-0.4-0.5-0.6c-0.1-0.2,0-0.6,0-0.8c0-0.1,0.1-0.2,0.1-0.3
			c0-0.1-0.1-0.2-0.2-0.3c-0.4-0.2-0.8-0.4-1.1-0.8c-0.2-0.2-0.3-0.5-0.3-0.9c0-0.2,0.2-0.6,0.1-0.8c-0.1-0.3-0.4-0.3-0.7-0.3
			c-0.1,0-0.2-0.1-0.3-0.2c0-0.1,0-0.2,0.1-0.3c0.1-0.2,0.1-0.5,0-0.7c-0.1-0.2-0.2-0.4-0.4-0.5C30.4,6.1,30.1,6,30,5.7
			c0-0.1,0-0.2,0-0.3c-0.1-0.1-0.3-0.1-0.4-0.1c-0.2,0-0.4-0.1-0.4-0.4c0-0.3,0.2-0.5,0.3-0.8c0.3-0.6-0.2-0.4-0.8-0.2
			c-0.6,0.1-0.3-0.4-0.4-0.7C28.2,3,28,3.2,27.5,3.6C27,4.1,27.1,2.3,26.7,2c-0.4-0.3-0.8-0.4-1.5-0.7c-0.7-0.3-1,0.3-1.1,0.9
			c-0.1,0.5-0.6,0-0.7-0.3c-0.1-0.3-0.6-0.2-0.9-0.2c-0.3,0.1-1-0.5-1.2-0.8c-0.2-0.3-0.8,0-1.2,0.1c-0.4,0.2-0.5-0.3-0.6-0.7
			S17.8,0,17.5,0.1s-0.6,0.2-1,0c-0.4-0.2-0.2,0.1-0.8,0.5c-0.6,0.4-1.5,0.1-2.3,0.4c-0.8,0.3-1.1-0.2-1.5-0.4
			c-0.3-0.2-2.2,0.8-3.5,1.4c0,0,1.5,1.9,1.3,2C9.5,4,9.1,4.5,9,5C8.8,5.5,8.9,5.8,8.4,6.2C7.8,6.6,6.9,7.5,7.6,7.5
			c0.6,0.1,2,0.7,2.4,1.2c0.3,0.5,0.6,0.6,1,0.1s0.4-0.5,0.5-0.9c0.1-0.4,0.8-0.8,0.9-0.5c0.1,0.3,0.7,1.4,1.3,1.4
			C14.2,9,15.4,9.6,15,9.8c-0.4,0.2-0.6,0.6-0.3,1c0.3,0.4,1,2.5,1,2.7c0.1,0.2,0.7,1.9,1,1.9l2.4,0.2c0,0,2.1-1.1,2.7-1
			c0.6,0.1,1.3,0.7,1.5,0.3c0.2-0.4,0.5-0.5,1-0.6c0.4,0,1-0.3,1.2-0.6c0.3-0.3,0.4-1.5,0.9-1.5c0.5,0,1.2,0.2,1.4,0.5
			C28,13,29,14.7,29.7,15c0.6,0.3,1.2,1.1,0.9,1.5c-0.3,0.4-1.1,1.1-1,1.6c0.1,0.5,0,1.2-0.2,1.6c-0.3,0.3-0.5,0.4-0.5,0.8
			c0,0.4-0.2,0.9-0.3,1.3c-0.2,0.4-0.6,0.9-0.6,1.1c0,0.2,0.1,0.7,0,0.8c-0.2,0.1-0.1,0.8-0.1,1c0.1,0.2-0.1,1.2,0,1.3
			c0.1,0.1,0.8,1.1,0.6,1.8c-0.2,0.7-0.5,1.6-0.8,1.4c-0.3-0.2-0.2-0.4-0.7-0.2c-0.5,0.2-0.7,0.7-0.7,1.1c0,0.4-0.7,2-0.7,2.6
			c-0.1,0.6-0.4,1.4-0.1,1.8c0.3,0.5,0.2,2.1-0.1,1.7c-0.4-0.4-0.9-1-1-1.4c0,0-0.1-0.6-0.1-0.8c0-0.2-0.1-0.4-0.5,0
			c-0.5,0.4-2,2.7-2.2,2.8c-0.1,0.1-1.5,0.8-1.9,1.6c-0.4,0.8-1.9,1.8-2.2,2c-0.3,0.2-1.1,1-1,1.9c0.1,0.9,0.2,1.7,1,1.9
			c0.8,0.2,0.8-0.1,1,0.3c0.2,0.4,0.9,2,1.2,1.5c0.4-0.5,0.7-1,0.9-0.6c0.2,0.4,0.3,0.8,0.6,0.4c0.3-0.4,0.9-0.7,0.8,0
			c0,0.7-1.3,1.8-1.6,1.6c-0.3-0.1-0.4-0.5-0.7-0.4c-0.3,0-0.6,0.3-0.6,0.8c0,0.5,0.2,1.3,0.6,1.5c0.3,0.1,0.6,0.1,0.7,0.4
			c0,0.1,0,0.2,0,0.5c-0.1,0.7-0.3,1.6-1.1,2.1c-0.8,0.5-1.1,0.6-1.5,0.2c-0.4-0.5-0.4-0.7-1.1-0.4c-0.7,0.3-1.5,0.9-2.1,0.8
			c-0.5-0.1-1.5-0.1-1.6,0.3c0,0.4,0.5,0.6,0.8,0.9c0.2,0.3,1.2,2,1.4,1.8c0.3-0.2,0.7-0.4,0.9,0.1c0.2,0.5,0.4,0.8,0,1
			c-0.4,0.2-0.6,0.1-0.6,0.7c0,0.5-0.2,2.6,0.2,2.9c0.4,0.3,1.3,1.1,1,1.3c-0.3,0.2-0.3,0.6-0.1,0.9c0.2,0.3,0,1.1,0.3,0.5
			c0.4-0.6,0.2-0.4,1-0.6c0.8-0.2,1.1-0.1,1.6-0.7c0.4-0.5,1.4-1.5,1.3-0.8c-0.1,0.7-0.1,1.2,0.2,1.4c0.3,0.2,1.3,0.4,1.3,1
			c-0.1,0.6,0.4,1.5,0.6,1.8c0.2,0.3,0.6,1.3,0.4,1.8c-0.1,0.4,0.2,0.9,0.5,1c0.1,0.1,0.3,0.2,0.5,0.3c0.2,0.2,0.5,0.5,0.4,1
			c-0.1,0.9-0.1,0.7-0.6,1c-0.6,0.3-1.2,0.3-1.5,0.9c-0.3,0.6-1.3,1.8-1.9,1.7c-0.6-0.1-1.2-0.6-1.6-0.6c-0.4-0.1-1.1-0.3-1.2,0.1
			c-0.1,0.4,0.7,1.2,0.2,1.4s-0.5-0.4-0.9-0.5c-0.4,0-1.3,0.1-1.2,0.5c0,0.4-0.1,0.7-0.7,1.1c-0.5,0.4-1.2,0.4-1.4,0.4
			c-0.2,0-0.4-0.1-0.9,0.4c-0.5,0.5-1.7,1.8-1.9,1.4c-0.2-0.4-0.2-0.6-0.6-0.6s-0.4-0.2-0.8,0.3s-1.1,1.2-1.3,1.3
			c-0.2,0.1-0.5,0.4-0.6,0.5c0,0.1,0.2,0.5-0.4,0.8c-0.5,0.3-1.4,1.2-1.5,0.8c-0.1-0.4,0.3-0.5-0.1-0.9c-0.4-0.4-0.8-0.4-1-1
			c-0.2-0.6,0.3-0.6-0.3-0.6c-0.6-0.1-0.9,0.4-1-0.2c-0.2-0.6-0.6-1.6-0.8-1.2c-0.2,0.4-0.2,0.6-0.6,0.7c-0.4,0.1-0.6,0.2-0.4,0.5
			c0.2,0.4,0.6,1.2,0.4,1.5c-0.2,0.2-0.6,0.4-0.6,0.8c0,0.4,0.3,1.2,0.3,1.2s1.2,1.5,1,2.3c-0.3,0.7-0.2,0.9-0.8,0.9
			c-0.7,0.6-2.2,1.9-2.1,2.9c0.2,1.2,0.2,0.7,0.4,1s0.4,0.7,0.8,0.6c0.4-0.1,0.8-0.2,0.9,0.1c0,0.3,0.1,0.9,0.6,0.8
			c0.4-0.1,1.3-0.3,1.3,0.4c-0.1,0.7-0.2,0.7,0.4,1.2c0.4,0.3,1,1.1,1.3,1.5C5.8,93,5.9,92.9,6,92.8c0.2-0.1,0.4-0.3,0.6-0.4
			c0.1,0,0.2-0.1,0.2-0.1C7,92.2,7.1,92,7.3,91.9c0.2-0.1,0.4-0.2,0.7-0.2c0.1-0.1,0.2-0.1,0.4-0.2c0.2-0.1,0.3-0.2,0.5-0.2
			c0.1,0,0.2-0.2,0.2-0.3c0-0.1,0.1-0.1,0.1-0.2c0,0,0.1-0.1,0.1-0.1c0-0.1,0-0.2,0-0.3c0-0.1,0-0.2,0.1-0.3c0.1-0.1,0.2-0.2,0.2-0.4
			c0.1-0.2,0.2-0.4,0.3-0.5c0.1-0.1,0.3-0.2,0.4-0.3c0.2-0.1,0.3-0.2,0.5-0.4c0.2-0.2,0.4-0.4,0.6-0.6c0.1-0.1,0.1-0.2,0.1-0.2
			c0-0.1,0-0.1,0-0.2c-0.1-0.1-0.1-0.3-0.1-0.4c0-0.2,0.2-0.4,0.3-0.4c0.1-0.1,0.2-0.1,0.4,0c0.2,0.1,0.4,0,0.6,0c0.1,0,0.3,0,0.4,0
			c0.6,0.1,1.2,0.2,1.7,0c0.3-0.1,0.6-0.3,0.7-0.6c0.1-0.2,0.1-0.4,0.2-0.6c0.1-0.2,0.2,0,0.3,0.1c0.4,0.6,0.7,1.2,1.2,1.6
			c0.2,0.1,0.4,0.2,0.6,0.3c0.2,0.1,0.3,0.2,0.3,0.3c0.1,0.2,0.1,0.4,0.1,0.6c0,0.2,0.1,0.5,0.1,0.7c0,0.5-0.2,0.9-0.6,1.3
			c-0.1,0.2-0.3,0.3-0.5,0.5c-0.1,0.2-0.2,0.3-0.3,0.5c-0.2,0.4-0.4,0.9-0.8,1.1c-0.2,0.1-0.4,0.2-0.5,0.4c-0.2,0.1-0.3,0.3-0.4,0.4
			c-0.1,0.1-0.2,0.1-0.3,0.2c-0.1,0.1-0.1,0.2-0.1,0.3c0,0.3,0,0.6-0.3,0.8c-0.1,0.1-0.3,0.3-0.4,0.4c-0.2,0.1-0.3,0.2-0.5,0.4
			c-0.1,0.1-0.2,0.2-0.1,0.4c0.1,0.1,0.4,0.1,0.5,0c0.1-0.1,0.2-0.2,0.3-0.2c0.1-0.1,0.3-0.1,0.4-0.2c0,0.1,0,0.2,0,0.2
			c0,0.1-0.1,0.1-0.1,0.1c-0.1,0.1-0.2,0.2-0.4,0.3c-0.2,0.1-0.4,0.2-0.5,0.3c-0.1,0.1-0.2,0.3,0,0.3c0.1,0,0.4-0.2,0.5-0.1
			c0.1,0.1,0.1,0.3,0.2,0.4c0.1,0.1,0.3-0.1,0.4-0.2c0.1-0.1,0.2-0.2,0.4-0.2c0.2,0,0.3-0.1,0.5-0.2c0.1-0.1,0.3-0.3,0.4-0.4
			c0,0.1,0,0.2,0,0.2c0,0.1,0.1,0.1,0.2,0.1c0.1,0,0.2-0.2,0.3-0.2C17,96.1,17,96,17.1,96c0.1,0,0.1,0.1,0.2,0.1
			c0.1,0.1,0.2,0.2,0.1,0.3c-0.1,0.1-0.3,0.2-0.4,0.2l0,0c-0.2,0.1-0.4,0.3-0.7,0.3c-0.3,0-0.9,0.3-0.8,0.6c0.1,0.2,0.2,0.3,0.3,0.5
			c0.1,0.2,0.2,0.3,0.2,0.5c-0.1,0-0.1,0.1-0.2,0.1l-0.1,0l-0.1,0c0,0,0,0-0.1,0c-0.2,0.1-0.3,0.2-0.5,0.2c-0.1,0-0.2,0-0.3,0
			c0,0-0.1,0-0.1,0c-0.1,0-0.1,0-0.2,0c-0.1,0-0.1,0-0.2,0c-0.1,0-0.1,0.1-0.2,0.1c0,0.1-0.1,0.1-0.1,0.1s0,0.1-0.1,0.2
			c-0.1,0.1-0.2,0.2-0.3,0.2c-0.1,0.1-0.2,0.1-0.4,0.1l-0.2,0.1c0,0-0.1,0-0.1,0c0,0-0.1,0.1-0.1,0.1c0,0,0,0.1-0.1,0.1
			c0,0,0,0.1-0.1,0.2c0,0.1-0.1,0.1-0.1,0.2c0.2,0.2,0.3,0.3,0.3,0.5c0,0.2-0.1,0.5,0.1,0.6c0.2,0.1,0.3-0.1,0.4-0.2
			c0.1-0.1,0.3-0.2,0.5-0.3c0.2-0.1,0.4-0.2,0.6-0.2c0.2,0,0.4,0,0.5-0.1c0.1-0.1,0.3-0.2,0.4-0.3c0.2-0.1,0.3-0.2,0.4-0.3
			c0.1-0.2,0.2-0.4,0.3-0.6c0.1-0.1,0.2-0.2,0.3-0.2c0.1,0,0.1,0,0.2,0c0.1,0,0.1,0.1,0.1,0.2c0.1,0.1,0.2,0.1,0.3,0.1
			c0.3,0,0.4-0.3,0.5-0.6c0.1-0.1,0.1-0.2,0.3-0.3c0.3-0.2,0.4-0.5,0.6-0.7c0.2-0.3,0.4-0.6,0.6-0.8c0.1-0.1,0.3-0.2,0.4-0.4
			c0.1-0.1,0.2-0.2,0.4-0.3c0.3-0.2,0.7-0.4,1-0.7c0.1-0.1,0.2-0.2,0.4-0.3c0.2-0.2,0.5-0.3,0.7-0.4c0.1,0,0.2,0,0.3,0
			c0.1,0,0.2-0.1,0.2-0.2c0-0.1,0-0.2,0.1-0.2c0-0.1,0.1-0.1,0.2-0.1c0.1,0,0.2-0.1,0.2-0.1c0.1,0,0.2,0,0.3,0s0.2,0,0.3,0
			c0.1,0,0.2,0,0.2,0c0.2,0,0.2-0.3,0.2-0.4c0-0.1,0-0.2,0-0.3c0-0.1,0-0.1,0.1-0.2c0.1-0.1,0.2-0.2,0.3-0.2c0.1,0,0.4,0,0.4,0.2
			c0,0.1,0,0.3,0.1,0.3c0.2,0,0.4-0.2,0.5-0.3c0.1-0.1,0.2-0.2,0.2-0.3c0.1-0.1,0.1-0.2,0.2-0.2c0.1-0.1,0.3-0.1,0.5-0.1
			c0.1,0,0.2,0,0.3,0c0.1,0,0.2,0.1,0.3,0.1c0.3,0.1,0.7,0,0.9-0.2c0.1-0.1,0.2-0.2,0.3-0.1c0.1,0,0.2,0.1,0.3,0.1
			c0.1,0,0.2-0.1,0.3-0.1c0.1-0.1,0.1-0.2,0.2-0.3c0.1-0.1,0.1-0.2,0.2-0.2c0,0,0.1,0,0.2-0.1c0,0,0.1-0.1,0.1-0.1
			c0.1-0.1,0.2-0.2,0.3-0.3c0.1,0,0.1-0.1,0.2-0.1c0.2-0.1,0.3-0.3,0.3-0.5c0-0.2-0.1-0.5,0.1-0.7c0.1-0.1,0.2-0.3,0.4-0.4
			c0.2-0.2,0.3-0.5,0.5-0.7c0.2-0.3,0.4-0.6,0.6-0.9c0.2-0.2,0.3-0.2,0.5-0.3c0.2-0.1,0.3-0.2,0.4-0.3c0.1-0.2,0.1-0.3,0.4-0.3
			c0.3-0.1,0.5-0.1,0.7-0.3c0.2-0.2,0.3-0.5,0.5-0.8c0.2-0.4,0.4-0.1,0.7,0.1c0.4,0.2,0.3-0.2,0.5-0.4c0.1-0.1,0.2-0.1,0.3-0.2
			c0.1-0.1,0.1-0.2,0.1-0.4c0-0.2,0.1-0.3,0.2-0.4c0.1-0.1,0.3-0.2,0.4-0.2l0.1,0c0.2,0,0.3,0,0.5,0c0.2,0,0.4,0,0.5-0.1
			c0.3-0.2,0.3-0.5,0.4-0.8c0-0.2,0.1-0.3,0.2-0.3c0.2-0.1,0.4-0.1,0.5-0.3c0.1-0.1,0.1-0.3,0.2-0.5c0-0.2,0.1-0.4,0.2-0.5
			c0.1-0.2,0.2-0.5,0.5-0.6c0.2-0.1,0.4,0,0.7-0.1c0.4-0.2,0.3-0.8,0.3-1.1c-0.1-0.4-0.2-0.7,0.1-1c0.2-0.2,0.5-0.3,0.7-0.6
			c0.1-0.2,0.1-0.3,0.3-0.5c0.1-0.2,0.3-0.2,0.5-0.1c0.5,0.1,0.6,0.4,0.8,0.8c0.1,0.2,0.2,0.4,0.4,0.5c0.2,0.1,0.5,0.2,0.8,0.3
			c0.1,0,0.2,0.1,0.3,0.2C44.9,80,45,80,45.1,80l0.5,0c0.1,0,0.2,0,0.3,0c0.1,0,0.2-0.1,0.2-0.1c0.1-0.1,0.3-0.1,0.4-0.1
			c0.1,0,0.3,0,0.5,0c0.1,0,0.2-0.1,0.3-0.1c0.1,0,0.2-0.1,0.3-0.1c0.1-0.1,0.1-0.2,0.2-0.2c0.1-0.1,0.2-0.1,0.3-0.2
			c0.2-0.1,0.4,0,0.5,0.1c0.1,0.1,0.1,0.2,0.2,0.2c0.1,0.1,0.2,0.1,0.3,0c0.2,0,0.3-0.3,0.3-0.5c0-0.2,0-0.3,0.1-0.5
			c0-0.2,0.1-0.3,0.1-0.5c0-0.1-0.1-0.2-0.1-0.2c-0.1-0.1-0.2-0.2-0.3-0.3c-0.1-0.1-0.2-0.2-0.3-0.3c-0.1-0.1-0.3-0.3-0.4-0.4
			c-0.1-0.2-0.1-0.3-0.1-0.5c0-0.2,0-0.4,0.2-0.5c0.1-0.1,0.2-0.2,0.4-0.2c0.5-0.2,0.9-0.1,1.4-0.1c0.6,0,1.1-0.1,1.7-0.4
			c0.5-0.3,1.1-0.6,1.3-1.1c0.1-0.3,0.2-0.6,0.3-0.9c0.1-0.3,0.2-0.6,0.4-0.8c0.2-0.3,0.7-0.6,1-0.5c0.2,0,0.3,0.2,0.5,0.2
			c0.2,0,0.3-0.3,0.4-0.5c0.1-0.2,0.3-0.4,0.4-0.7c0.1-0.2,0-0.4,0-0.6c0-0.3,0-0.5,0-0.8c0-0.3,0-0.6,0-0.8c0-0.2,0-0.5,0.1-0.7
			c0.1-0.3,0.4-0.2,0.6-0.2c0.4,0.1,0.9,0.1,1.1,0.5c0.1,0.2,0.1,0.4,0.2,0.5c0.1,0.2,0.2,0.5,0.3,0.7c0.1,0.1,0.2,0.2,0.4,0.3
			c0.2,0.1,0.4,0.3,0.6,0.5l0.5,0.4c0.1,0.1,0.4,0.3,0.5,0.1c0.1-0.2,0-0.5,0-0.7c0-0.2-0.1-0.4-0.1-0.6c-0.1-0.2-0.1-0.3,0-0.5
			c0.1-0.2,0.2-0.3,0.3-0.4c0.2-0.1,0.5-0.2,0.7-0.2c0.2-0.1,0.4-0.1,0.7-0.1c0.2-0.1,0.4-0.2,0.6-0.4c0.2-0.2,0.4-0.3,0.4-0.6
			c0-0.2,0-0.4,0.1-0.6c0-0.2,0.1-0.4,0.1-0.6c0-0.3,0-0.5,0.1-0.8c0.1-0.1,0.1-0.3,0.1-0.4C63.5,64.4,63.6,64.3,63.6,64.1z"/>
		</svg>
		</div>


		<!-- 华东 -->
		<!--div style="width:51px; height:101px; left: 1371px; top:324px;"-->
		<div style="width:52px; height:101px; left: 231px; top:164px;">
		<svg id="area_100002" viewBox="0 0 52.4 113.5" class="area_stroke hidden " version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" >
		<path d="M15.8,25.52a1.49,1.49,0,0,0-.16.68c.06.22,0,.93.44.82s1.42,0,1.4.37-.09.78.18.85,1.24,1,.54,1.27-.89,0-1.16.34-1.31.55-1.46.85-.21.8-.62.38a15,15,0,0,1-1.23-1.47c-.27-.38.06-.59-.58-.58s-1.11-.16-1.24.07-.48.26-.3.72a9.21,9.21,0,0,1,.11,1.57c.08.32.08,1.18-.42,1s-.64-.23-1,.34a3.72,3.72,0,0,0-.26,1.89c0,.27.06.5-.42.53a3.57,3.57,0,0,1-1.37,0c-.38-.15-1.26-.57-1.27.12a1.09,1.09,0,0,0,1,1.14c.56.14,1.47.15,1.67.92s.16,1.42.79,1.47,1.58,1.42,1.9,1,.93-1,1.14-.36,0,1.14.13,1.44a10,10,0,0,1,.15,2.18c0,.7.18,1.21-.44,1.11s-1-.21-1.43.21A2.61,2.61,0,0,0,10.46,47a1.94,1.94,0,0,1,.67.41c.76.57-.06.75.43.77s.78-.35.86,0,1.05-.05,1.16.22,0,.58.37.81,1,.34.44.63-.71-.2-.93.43-.06.65-.22.81-.71.16-.17.57.85.18.76.69-.53,1.56.07,1.71.66,0,.67.61a18.81,18.81,0,0,0,.64,2c0,.18,0,.29-.09.27-.45-.08-.86,0-.71.3s1,.19.64.61-.44.56-.79.41a2,2,0,0,1-.9-.49c-.28-.31-.5-1.12-.71-.56s-.4.76-.79.6a1.15,1.15,0,0,0-1.17.23c-.16.25-.69,1.53-1.46,1.34s-1.05-.17-1.4.12-.57,1.32-.89,1-.52-.22-.89,0-.81.51-1.16.27-.61-.19-.77.17-.06.94-.42.8-.36-.09-.71.15a8.7,8.7,0,0,1-1,.83l-.07,0c.05,0,1.62,1.48,1.45,2.36s-.21,1.12.09,1.59.52,1.12.23,1.27a2.57,2.57,0,0,0-1.13.68c-.36.43.26.36-.41.76S1,69.73.87,70.16a3.35,3.35,0,0,1-.4,1c-.19.25-.32.08-.37,1s-.29,1.56.24,1.83,1.36-.4,1.07.55S.91,75.77,1.14,76s1,.4.69.94-.59,1.44,0,1.75,1.47.26,1.16,1.06S1.43,82.23,2.4,82s1.67-.27,1.16.13-.86.42-.93.92.25.46,0,.86a2,2,0,0,0-.4,1,1.27,1.27,0,0,1,.14.89c-.09.65-.16,1-.17,1s.12,0,.15.13c.11.45,0,1.74.73,1.32s.93.06,1-.42.26-.85.9-.66,1.8.24,1.81.65.4.63-.1,1.1-.54.25-.93.77S4.71,90,4.65,90.94s-.49,1.59.14,2,.52.45,1.2.09a3.87,3.87,0,0,1,1.54-.58c.05.11.57.34.69,0s.08-.55.48-.56,1.27.22,1.65-.08,1.17-1,1.44-.46S13.17,93.71,13.5,93s.06-1.82.45-2,.75,0,.53-.41-.38-.53-.14-.51l.38.09c.75.23.69.42,1,.66s2.41.26,2.44.61-.52,1,.56.78,1.05-.66,1.48,0a3.8,3.8,0,0,1,.67,1.51c.18.69.55,1,.51,1.66s-.27.5.17,1.1,1.62,2.64,1.62,2.64l.22-.35a4.32,4.32,0,0,1,.69-.89,2.2,2.2,0,0,1,1-.36,4.21,4.21,0,0,0,.87-.28,2.86,2.86,0,0,0,.93-.64,1.63,1.63,0,0,0,.41-.88c.07-.39.41-.55.75-.71s.75-.32.64-.74a.43.43,0,0,0-.23-.32A.84.84,0,0,0,28,94c-.33.06-.91,0-1-.39s.41-.47.65-.61a2.76,2.76,0,0,0,.49-.4,1.32,1.32,0,0,1,.45-.32c.27-.06.45.33.67.42a.94.94,0,0,0,.78-.1.8.8,0,0,1-.06-.3c.05-.15.18-.22.31-.3a1.21,1.21,0,0,1,.47-.23c.38,0,.46.37.49.64s.07.3.25.28a.3.3,0,0,0,.23-.31c0-.16,0-.38.12-.48s.26-.15.3-.34.07-.31,0-.42S32,91,31.87,91s-.23-.1-.21-.27a.41.41,0,0,1,.2-.33c.15-.06.2.11.28.19s.33.08.47,0,.28-.22.46-.27.56-.12.71-.34a.53.53,0,0,0,0-.49c-.11-.2-.27-.19-.46-.17s-.42,0-.5-.21a.68.68,0,0,1,.1-.57,4.83,4.83,0,0,0,.18-.62,4.92,4.92,0,0,1,.21-.65c.05-.11.18-.34.34-.3s.13.49.2.65.33.1.42,0a1.05,1.05,0,0,0,.08-.36,2,2,0,0,1,.1-.32c0-.1.19-.5.34-.37s.09.43.06.6-.22.6-.09.81.33,0,.42-.14a1.76,1.76,0,0,0,.25-.89.36.36,0,0,0-.15-.33c-.06,0-.21-.08-.23-.15s0-.16.09-.19.12,0,.18,0a.24.24,0,0,0,.33-.08c.06-.1.06-.21.13-.31s.18-.18.32-.1a.65.65,0,0,1,.26.43.42.42,0,0,0,.12.26,1.45,1.45,0,0,0,.19.23c.08.06.13,0,.22,0s.12.07.2,0,.05-.1.09-.13,0,0,.07,0,.1-.37.12-.48.13-.47-.11-.53-.66,0-.75-.32a.89.89,0,0,1,.21-.7.75.75,0,0,1,.7-.12,1.4,1.4,0,0,0,.05-.41c0-.11,0-.22,0-.34s.3-.26.46-.4a.67.67,0,0,0,.16-.24c0-.07.1-.13.12-.21a.44.44,0,0,0-.16-.45c-.13-.07-.31,0-.38-.19a.87.87,0,0,1,.07-.55c.08-.16.22-.26.2-.45a.72.72,0,0,0-.29-.42.57.57,0,0,1-.19-.59.72.72,0,0,1,.22-.36.55.55,0,0,1,.19-.12c.07,0,.12-.08.18-.1a.41.41,0,0,1,.22,0,1.16,1.16,0,0,0,.18,0,.64.64,0,0,0,.18-.06c.13-.09,0-.46-.11-.54a.73.73,0,0,0-.33-.08,1.12,1.12,0,0,1-.7-.47c-.14-.26.12-.62.24-.85a2,2,0,0,1,.71-.66c.13-.1.56-.45.7-.2s-.09.45.2.49a1,1,0,0,0,.61-.16.39.39,0,0,0,.21-.26c0-.11,0-.23,0-.34s.43-.18.56-.37a.53.53,0,0,0,0-.25.67.67,0,0,1,.13-.3c.17-.2.45-.18.67-.3s.19-.35,0-.49-.43-.27-.64-.42-.32-.32-.09-.5a1.46,1.46,0,0,1,.77-.21.48.48,0,0,1,.38.15,1.66,1.66,0,0,1,.21.4.38.38,0,0,0,.57.18.58.58,0,0,0,.15-.65c-.07-.22-.09-.46.18-.56s.49,0,.42-.34-.4-.44-.5-.72a2.15,2.15,0,0,1,0-.8.88.88,0,0,1,.39-.83c.25-.15.68-.25.74-.58a.91.91,0,0,1,.12-.35,2.43,2.43,0,0,1,.34-.38c.13-.13.3-.39.52-.31s.15.12.24.14a.36.36,0,0,0,.31-.1.7.7,0,0,0,.23-.31c0-.11,0-.23,0-.34.08-.3.37-.17.56-.32s0-.54-.15-.68-.33-.26-.29-.53.2-.55.46-.54.4.4.56.58.42.22.5,0-.19-.41-.31-.63.12-.36.33-.24.34.29.51.08a2,2,0,0,0,.21-.75,1.46,1.46,0,0,0,0-.69c-.08-.26-.27-.34-.48-.49s-.2-.53.14-.55c.11,0,.21,0,.32,0a1.13,1.13,0,0,0,.39-.21c.17-.12.45-.46.26-.67s-.51,0-.65-.26.13-.32.31-.32.44,0,.47-.27-.16-.32-.36-.38-.17-.23-.2-.41-.28-.16-.46,0-.41.31-.59.11a.47.47,0,0,1-.08-.53c.09-.14.28-.2.36-.35s0-.34.11-.49a.73.73,0,0,1,.58-.23.46.46,0,0,1,.47.18c.1.16.18.39.4.45a.19.19,0,0,0,.27-.22.89.89,0,0,1,0-.51c.08-.15.22-.25.25,0a1.43,1.43,0,0,0,0,.22l.1.25s0,0,.06.05a.41.41,0,0,0,.24,0c.17,0,.26-.2.32-.33a2.27,2.27,0,0,0,0-.58c0-.15.11-.27.11-.42a.45.45,0,0,0-.19-.33c-.13-.11-.21-.2-.14-.37s.17-.33,0-.46a.53.53,0,0,0-.65,0c-.2.19-.13.48-.09.71s0,.28-.26.25a1.47,1.47,0,0,0-.83,0c-.25.11-.43.35-.69.43-.49.16-.77-.6-.39-.87a1.51,1.51,0,0,1,.63-.2c.24-.06.48-.1.72-.14a1.18,1.18,0,0,0,.55-.17.88.88,0,0,0,.25-.4c.1-.31.11-.77.42-1a.39.39,0,0,1,.35,0,.6.6,0,0,1,.28.13,1,1,0,0,1,.12.17c.1.12.33,0,.3-.16s-.09-.23,0-.3.1,0,.13-.09-.11-.37-.32-.34-.19.16-.33.17a.28.28,0,0,1-.24-.11c-.07-.08-.11-.15-.2-.18a.5.5,0,0,0-.17,0,.92.92,0,0,1-.46-.17c-.15-.08-.32-.11-.47-.19a4.48,4.48,0,0,1-.79-.65,3,3,0,0,1-.46-.49,3.7,3.7,0,0,0-.21-.33,1.47,1.47,0,0,1-.29-.32A1.15,1.15,0,0,0,47,52.8a.75.75,0,0,0-.33-.06,4.53,4.53,0,0,0-1.22-.07,1.47,1.47,0,0,0-.87.51c-.22.28-.33.59-.67.76-.64.32-1.79.45-2-.43a1.16,1.16,0,0,0-.22-.62.55.55,0,0,0-.61-.14c-.48.18-.71.79-.94,1.19a.51.51,0,0,1-.3-.07.2.2,0,0,1,0-.28,2.57,2.57,0,0,0,.44-.49,6.05,6.05,0,0,1,.32-.55.89.89,0,0,1,.52-.26,2.41,2.41,0,0,1,1,0c.16,0,.31.1.46.15a1.05,1.05,0,0,1,.26.13.44.44,0,0,0,.22.05,1.27,1.27,0,0,0,.48-.07,1.18,1.18,0,0,0,.25-.19c.08-.07.08-.1.1-.21a1,1,0,0,0,0-.47c0-.08-.13-.16-.12-.25s.11-.14.16-.19a2.37,2.37,0,0,1,.53-.26c.17-.08.34-.18.5-.27a.7.7,0,0,0,.24-.17l.14-.25a.32.32,0,0,1,.34-.13c-.06-1.18-1.17-1-1.51-1.14s0-.87.11-1.54c0-.23.08-.44.1-.61,0-.68.4-.5.9-.51s.32-.69.35-1.18a1,1,0,0,1,.42-.74c-.17-.18-.39-.32-.55-.52s-.26-.4-.52-.51a2.23,2.23,0,0,0-.45-.18,1.29,1.29,0,0,0-.34,0h-.58c-.14,0-.26,0-.39,0a1.42,1.42,0,0,1-.86-.32A1.16,1.16,0,0,1,42,41.48c.29-.46.76-.11,1,.16a2.7,2.7,0,0,0,1.18.85,2.2,2.2,0,0,0,1.29-.15c.33-.11.67-.21.93.08a3.07,3.07,0,0,0,.29.34.73.73,0,0,0,.41.15,1.26,1.26,0,0,1,.47.08,1.29,1.29,0,0,1,.36.34,1.18,1.18,0,0,0,.44.39,1.58,1.58,0,0,0,.43.1c.11,0,.27.06.37,0s.2-.37.21-.55a.37.37,0,0,0-.26-.44c-.23-.11-.17-.27-.22-.47s-.28-.17-.27-.38.14-.6-.09-.78a2.49,2.49,0,0,0-.63-.32,8.6,8.6,0,0,1-.84-.33,3.22,3.22,0,0,0-.68-.17.42.42,0,0,1-.33-.45l0-.39a.78.78,0,0,0-.12-.68,1.73,1.73,0,0,0-.16-.3c-.19-.21-.6-.23-.85-.33s-.29-.13-.44-.18L44.29,38a1.24,1.24,0,0,1-.55-.35,4,4,0,0,1-.49-.42c-.17-.21-.16-.51-.25-.75s-.21-.61-.29-.92-.06-.53-.13-.78a.62.62,0,0,1,0-.23c0-.21-.2-.35-.29-.52a3.09,3.09,0,0,0-.36-.45,1.8,1.8,0,0,1-.31-.74c-.09-.32-.21-.64-.27-1s0-.44-.09-.66a5.51,5.51,0,0,1-.1-.9,1.46,1.46,0,0,0,0-.59.42.42,0,0,0-.37-.2c-.17,0-.29.14-.46.17a.22.22,0,0,1-.25-.21c0-.25.1-.49.1-.74a1,1,0,0,0-.33-.8c-.15-.12-.28-.2-.3-.4a1.24,1.24,0,0,0-.08-.51,4.46,4.46,0,0,0-.34-.42,4.31,4.31,0,0,0-1.19-.92,2.27,2.27,0,0,0-.66-.33,3.05,3.05,0,0,0-.67-.07c-.48,0-.3.69-.76.75,0-.11,0-.21,0-.31,0-.42.21-.92-.23-1.2s-1,0-1.37-.36.17-.9-.29-1c-.23-.06-.48.14-.66.25s-.32.18-.31-.05.13-.42.07-.59-.33-.14-.47-.18c-.55-.16-.07-.9.2-1.12l.06,0c.16-.1.34-.15.5-.24a.75.75,0,0,0,.31-.49A5.78,5.78,0,0,1,34,19.51a1,1,0,0,1,.38-.37.88.88,0,0,0,.38-.63c0-.23-.05-.5,0-.71a1.31,1.31,0,0,1,.55-.43,1,1,0,0,1,.3-.09.76.76,0,0,0,.35-.14.53.53,0,0,0,.11-.38.75.75,0,0,1,.16-.5c.1-.1.24-.16.35-.25a2.09,2.09,0,0,0,.22-.2c.21-.19.52-.27.75-.44a.67.67,0,0,0,.29-.37.56.56,0,0,0-.09-.4,1.79,1.79,0,0,0-.34-.44c-.16-.13-.78-.25-.54-.58a.4.4,0,0,1,.41-.14c.12,0,.24.1.35,0s.17-.24.29-.32a.66.66,0,0,1,.51-.08.54.54,0,0,1,.25.27,1.35,1.35,0,0,0,.19.33.72.72,0,0,0,.68.37,1,1,0,0,0,.58-.25.54.54,0,0,0,.16-.51,1.2,1.2,0,0,0-.08-.5c-.09-.14-.26-.1-.4-.16s-.07-.17,0-.28a2.63,2.63,0,0,1,.06-.39c.09-.24.39-.61.69-.57s.31.75.61.52.19-.62.09-.89-.17-.31-.25-.47a.21.21,0,0,1,0-.31c.22-.2.51-.06.75,0a.62.62,0,0,0,.7-.17,1.16,1.16,0,0,1,.31-.27,4.5,4.5,0,0,1,.44-.16l.43-.16a1.56,1.56,0,0,0,.46-.29c.09-.07.2-.12.29-.19a1.59,1.59,0,0,1,.34-.28c.14-.06.3-.07.44-.13a5.91,5.91,0,0,0,.66-.45,5.72,5.72,0,0,0,.57-.24.45.45,0,0,0,.19-.22,1.55,1.55,0,0,0,0-.21.7.7,0,0,1,.24-.3A.36.36,0,0,1,47,7.12c.22,0,0,.28,0,.38a.43.43,0,0,0,.1.37.25.25,0,0,0,.35.07,1,1,0,0,0,.22-.36.7.7,0,0,1,.34-.36.28.28,0,0,1,.16,0c.07,0,.08.08.08.15a1.34,1.34,0,0,1-.06.44c0,.14-.11.31.07.36s.31.12.48.16a.55.55,0,0,0,.38,0c.21-.13.15-.56.17-.75a8.85,8.85,0,0,1,.12-.88.68.68,0,0,1,.27-.5c.16-.14.42-.24.51-.44A2.61,2.61,0,0,0,50.34,5c0-.22.19-.51.1-.73s-.39-.25-.6-.23a2.61,2.61,0,0,0-.68.17.82.82,0,0,1-.65,0,1.65,1.65,0,0,1-.64-.53c-.16-.19-.38-.47-.68-.34a2.11,2.11,0,0,0-.6.59.71.71,0,0,1-.37.24,5.34,5.34,0,0,0-.8.13,1.33,1.33,0,0,1-.62.13A1.27,1.27,0,0,1,44,4.05a1.63,1.63,0,0,1-.24-.4.56.56,0,0,0-.19-.3c-.22-.14-.54.09-.75.15s-.46-.12-.55-.4-.11-.6-.4-.71-.6-.07-.88-.18-.48-.35-.76-.47a.62.62,0,0,0-.71.2c-.17.17-.27.4-.54.42s-.53-.12-.74,0-.2.46-.4.62a.52.52,0,0,1-.36.11c-.09,0-.23,0-.3,0s0,.19,0,.27a2.64,2.64,0,0,1,.08.49c0,.33-.19.46-.51.48s-.43,0-.57.22a1.71,1.71,0,0,1-.32.44,2.54,2.54,0,0,0-.59.19c-.24.15-.17.35-.09.58a1,1,0,0,1,0,.7c-.14.38-.34.86-.83.86S33.42,6.92,32.9,7c-.2,0-.38.15-.59.18a1,1,0,0,1-.58-.09,6.33,6.33,0,0,1-1.26-.85,6.29,6.29,0,0,1-.67-.48.64.64,0,0,1-.27-.51c0-.2.26-.3.42-.41a.81.81,0,0,0,.35-.44,1.1,1.1,0,0,0,.05-.62c-.06-.22-.25-.37-.31-.58-.12-.43.59-.77.3-1.16A.8.8,0,0,0,30,1.81a11.56,11.56,0,0,0-1.56-.27A4.06,4.06,0,0,1,27.6,1a.6.6,0,0,0-.71.11,2.88,2.88,0,0,0-.72.94,3,3,0,0,0-.13.39,2,2,0,0,1-.21.65c-.18.25-.46,0-.42-.26A1.5,1.5,0,0,0,25.48,2c-.07-.19-.3-.17-.44-.3s-.44-.43-.46-.65a2.1,2.1,0,0,0-.05-.79A.43.43,0,0,0,24.16,0,3.36,3.36,0,0,0,23,.82a1.51,1.51,0,0,1-.82.68c-.35.13-.67.25-.73.61s-.06,1-.74.89-.94.37-1.39.17S18.37,2.58,17.9,3s-.77.49-1.14,1.09a1.67,1.67,0,0,1-1,.9c-.39.15-1.26.29-1.48.81s-.11.51-.4.58-.63.11-.55.46.22.48-.08.63-.42-.32-.79.52A7.64,7.64,0,0,0,12,9.37c-.2.61-1.36,1.17-1.56,2s-.76,1.2-.59,1.89.6.65.53,1.3a1.38,1.38,0,0,1-.12.54s.32,0,0,.53-2,2.93-.68,2.08,2.66-1.34,3.26-1.73,1.7-1.27,1-.26-.57.26-1,1-.74.37-.82.66-.25.48-.47.46-.8-.09-.83.22.23.21-.32.48-.77.5-.8.81,0,.53-.37.61-1.15.05-1.54.75a9.59,9.59,0,0,1-.87,1.26c-.25.33-.84,1,.33.84s1.67-.6,2.06.21,1.89,2.85,2.91,2.58A9.24,9.24,0,0,1,14,25.22c.51-.07.58-.35.87-.25s.66,0,.86.43A.4.4,0,0,0,15.8,25.52Z" transform="translate(0.5 0.5)"/><path d="M39,90.88a4.41,4.41,0,0,0-1.87,1.75c-.47.91-.07,1.19-.5,2.13-.56,1.21-1.34,1-2,2.25-.42.79-.11.9-.62,2.5-.42,1.33-.63,1.23-.87,2.25a23.13,23.13,0,0,0-.25,2.75c-.1,1.19-.14,1.4,0,1.75.29.75.79.68,1.38,1.5s.3,1,.88,2.38c.4,1,1,2.32,1.75,2.38.29,0,.55-.14,1.25-.87a11.22,11.22,0,0,0,2.38-3,21.88,21.88,0,0,0,1.25-3.25c.16-.5.31-1.08.63-2.25a20.52,20.52,0,0,0,.75-3.25c0-1.48-.52-1.57-.37-2.87s.78-1.55.63-2.5-.84-.86-1.25-2.25A3.87,3.87,0,0,1,42,90.08.09.09,0,0,0,42,90C41.91,89.79,40.32,90.1,39,90.88Z" transform="translate(0.5 0.5)"/>
		</svg>
		</div>


		<!-- 中南 暂时用海南的ID 100005-->
		<div style="width:81px; height:122px; left: 174px; top:178px;">
		<svg id="area_100005" viewBox="0 0 81 122" class="area_stroke hidden " version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" >
		<path d="M21.4,117.8c0,0.3,0.2,0.6,0.2,0.9c0,0.1-0.1,0.2-0.1,0.3c0,0.1,0,0.3,0,0.4c0,0.1,0.1,0.2,0.1,0.3
			c0.2,0,0.5,0,0.7,0.1c0.3,0.1,0.5,0.2,0.8,0.2c0.6,0.1,1.1,0.6,1.6,1.1c0,0.2,0.9,0.2,1.1,0.3c0.2,0,0.4,0,0.6,0
			c0.3,0,0.5,0.3,0.8,0.4c0.3,0.1,0.7,0,0.9-0.1c0.6-0.2,1.3-0.5,1.9-0.6c0.1-0.1,0.3,0,0.4,0c0.1,0,0.3-0.2,0.4-0.3
			c0.3-0.4,0.2-1,0.8-1.2c0.3-0.1,0.6-0.3,0.9-0.4c0.3-0.1,1-0.2,1.1-0.3c0-0.2-0.1-0.4-0.1-0.6c0.1-0.2,0.3,0,0.5-0.1
			c0.1-0.1,0.1-0.4,0.1-0.5c0-0.3-0.1-0.7,0.1-1c0.2-0.3,0.3-0.5,0.4-0.8c0-0.2,0-0.4,0.1-0.6c0.1-0.2,0.2-0.5,0.2-0.7
			c0.1,0,0.3,0,0.4,0c0.1,0,0.2-0.1,0.2-0.2c0.1-0.2,0.2-0.4,0.3-0.5c0,0,0.2,0,0.2,0c0,0,0.1-0.2,0.1-0.2c0.1-0.2,0.2-0.3,0.2-0.5
			c0-0.1,0-0.1,0-0.2c0.1,0,0.2,0,0.3,0.1c0.3,0.2,0.4,0,0.5-0.2c0.2-0.2,0.3-0.5,0.3-0.8c0.2-1.6-2-2.1-3.2-2.3
			c-0.2,0-0.5-0.1-0.8-0.1c-0.2,0-0.3,0-0.5,0.1c-0.4,0.1-0.9,0.1-1.3,0.1c-0.2,0-0.3,0-0.4,0.2c-0.1,0.1-0.2,0.2-0.3,0.4
			c-0.2,0.2-0.4,0.4-0.7,0.1c-0.1-0.1-0.1-0.3-0.3-0.4c-0.1,0-0.3-0.1-0.4,0c-0.1,0.1-0.1,0.3-0.3,0.2c-0.1,0-0.2-0.1-0.2-0.2
			c-0.1-0.1-0.2-0.1-0.3-0.2c-0.1-0.1-0.2-0.2-0.4-0.1c-0.2,0.1-0.2,0.6-0.3,0.8c-0.1,0.1-0.1,0.5-0.3,0.5c-0.1,0.1-0.3-0.1-0.4-0.1
			c-0.5-0.2-0.9-0.8-1.5-0.4c-0.4,0.3-0.6,0.8-0.8,1.2c-0.2,0.5-0.5,1-1,1.2c-0.6,0.3-1.5,0.2-2,0.7c-0.6,0.5-0.6,1.4-0.6,2.1
			C21.2,116.4,21.2,117.1,21.4,117.8z M31.7,69.4c0,0-0.6,1-1,1.4c-0.4,0.4-0.9-0.2-1.3-0.3c-0.4-0.1-0.6,0.2-0.9,0.6s0.2,0.3,0.7,0.4
			c0.5,0.1-0.1,0.5-0.2,0.9c-0.2,0.4-0.7-0.1-0.9,0c-0.2,0.1-0.2-0.3-0.5-0.6c-0.2-0.4-0.8,0.6-1,0.8c-0.2,0.2-0.2,0.5-0.1,0.9
			c0.1,0.5-0.8-0.4-1.1-0.8c-0.3-0.4-0.2-0.2-0.6-0.5c-0.4-0.2-0.4,0.3-0.9,0.6c-0.5,0.3-0.4,0.7-0.7,1.3c-0.3,0.6-0.1,0.2-0.8,0.2
			c-0.7,0-0.5,0.1-0.7,0.4c-0.1,0.3-0.5,0.2-0.9,0c-0.4-0.2-0.6-0.6-0.8-1.1c-0.2-0.5-0.2,0-0.6,0.2c-0.4,0.2-0.8-0.5-1-0.7
			c-0.2-0.2-0.4-0.7-0.6-1.1c-0.1-0.4-0.9,0.4-1.4,0.7c-0.5,0.2-0.3,0.7-0.5,1.3c-0.3,0.5-1.4,0.3-1.6,0.2c-0.3,0-0.5,0.1-1,0.4
			c-0.6,0.3-0.5-0.1-0.8-0.2c-0.3-0.1-0.4,0.1-0.6,0.4c-0.2,0.2-1.1,0.4-1.5,0.5c-0.4,0.1-0.1,0.1,0,0.8c0.2,0.7-0.4,0.6-1.1,0.8
			c-0.7,0.2-1.3-0.3-1.4-0.6c-0.2-0.4-0.6-0.2-1-0.2c-0.4-0.1-0.3-0.3-0.6-0.8C6.1,74.7,5.8,75,5,75.1s-0.6,0.4-1,1.3
			C3.6,77.2,0.6,77,0.2,77c-0.1,0-0.1,0-0.2,0c0.4,0.4,0.4,1.4,0.7,1.8c0.3,0.4,0.5-0.4,0.7-0.6c0.2-0.2,0.7-0.1,1.1,0.2
			c0.4,0.3,0.3,0,0.7-0.2s0.2,0.2,0.6,0.8s0,1.1,0.2,1.6c0.2,0.6,0.3,0.2,0.5,0.1s0.5,0.4,0.8,0.8c0.3,0.4,0.9-0.7,1.1-0.9
			c0.2-0.2,0.4,0.6,0.6,0.7c0.1,0.1,0.1,0.2,0.6,0.5c0.5,0.3,1-0.5,1.4-0.7c0.4-0.2,0.5,0.6,0.4,0.7c0,0.1,0.3,0.3,0.6,0.8
			c0.3,0.5-0.2,0.3-0.5,0.5c-0.3,0.2,0,1.2-0.3,2c-0.2,0.8-1.1,0.1-1.2,0c-0.1-0.1-0.3,0-0.6,0.1c-0.3,0.1-0.5,0.2-0.6,0.6
			c-0.1,0.3-1.5,1.2-1.5,1.2c0.2,0.4,0.4,0.8,0.5,0.9c0.1,0.3,0.6,0,0.9,0.1c0.3,0,0.5,0.3,1.2,0.9c0.7,0.6,0.8-0.2,1.3-0.3
			c0.5-0.1,0.9,0.7,1.3,1.1c0.4,0.3,1.3-0.2,1.9-0.4c0.6-0.2,0.4,0.5,0.7,1.3s-0.4,0.8-0.9,1.3c-0.4,0.6,0.1,0.7,0.4,1
			c0.3,0.3-0.3,1.4-0.1,2.2c0.2,0.8,1.4,1.3,1.9,1.3c0.5,0.1,0.2,0.3,0.2,0.7c0.1,0.4,0.9,0.2,1.2,0.3c0.3,0,0.4,0.3,0.7,1
			c0.3,0.6,2.8-0.2,2.8-0.2l0.4,0.3c0.3,0.1,0.4,0.4,0.7,0.6c0.2,0.1,0.3,0,0.5,0c0.1,0,0.3,0.1,0.5,0.1c0.1,0,0.2-0.1,0.4-0.2
			c0.2,0,0.3,0.2,0.5,0.1c0.3-0.1,0.4-0.5,0.8-0.4c0.1,0.2,0.3,0.8,0.7,0.4c0.2-0.2,0.4-0.4,0.5-0.7c0-0.2,0.1-0.3,0.1-0.5
			c0-0.1,0-0.3,0-0.4c0-0.1,0.2-0.2,0.3-0.2c0.1,0,0.1,0.2,0.2,0.3c0,0.1,0,0.4,0.1,0.4c0.2,0,0.2-0.2,0.3-0.3c0.1,0,0.2,0.1,0.2,0.2
			c0.1,0.3,0,0.6,0.3,0.7c0.3,0.1,0.7,0.2,1,0.1c0.1,0,0.2-0.1,0.4-0.1c0.2,0,0.3,0.2,0.4,0.3c0.2,0.2,0.5,0.6,0.8,0.6
			c0.2,0,0.3-0.1,0.4,0.1c0.2,0.3,0.4,0.5,0.7,0.5c0.3,0,0.6-0.2,0.9-0.3c0.3-0.1,0.5-0.2,0.5-0.6c0-0.1,0.1-0.8,0.3-0.7
			c0.1,0.1,0.3,0.3,0.3,0.4c0.1,0.2,0.2,0.4,0.3,0.6c0.1,0.2,0.2,0.4,0.4,0.3c0.2,0,0.5,0,0.7,0c0.1,0,0.1,0.1,0.2,0.1
			c0.2,0.2-0.1,0.6-0.3,0.7c-0.2,0.2-0.4,0.3-0.5,0.5c-0.2,0.2-0.4,0.5-0.6,0.7c-0.1,0.1-0.1,0.2-0.1,0.3c0,0.1,0.1,0.1,0.1,0.2
			c0,0.2-0.3,0.4-0.4,0.6c-0.2,0.3-0.2,0.5,0.1,0.7c0.2,0.2,0.4,0.3,0.6,0.6c0.1,0.2,0.4,0.3,0.5,0.5c0.1,0.2-0.2,0.4-0.3,0.6
			c-0.2,0.3-0.1,0.6,0.2,0.8c0.3,0.2,0.7,0.3,0.7,0.6c0,0.3-0.1,0.6-0.1,0.9c0.1,0.3,0.3,0.2,0.6,0.2c0.4-0.1,0.7-0.2,1,0
			c0.3,0.1,0.6,0.3,0.9,0.2c0.3-0.1,0.6-0.4,0.8-0.6c0.4-0.4,0.7-1.3,0.2-1.8c-0.1-0.1-0.3-0.2-0.5-0.2c-0.1,0-0.3,0-0.4-0.2
			c-0.2-0.2,0.1-0.5,0.1-0.7c0-0.7-1.8-0.3-1.5-1.1c0.1-0.3,0.5-0.6,0.7-0.8c0.2-0.2,0.5-0.5,0.6-0.8c0.1-0.3-0.1-0.5-0.1-0.8
			c0-0.3,0.4-0.3,0.6-0.2c0.3,0.1,0.4,0.5,0.7,0.6c0.3,0.2,0.6-0.1,0.8-0.3c0.1-0.1,0.2-0.3,0.2-0.4c0-0.1,0-0.2,0-0.3
			c0.1-0.1,0.4,0.2,0.5,0.2c0.2,0.1,0.4,0.3,0.6,0.4c0.2,0.1,0.5,0,0.7-0.1c0.1-0.1,0.3-0.2,0.4-0.2c0.2-0.1,0.3,0,0.5,0
			c0.3,0,0.5,0.1,0.8-0.1c0.3-0.2,0.5-0.2,0.8,0c0.2,0.2,0.5,0.3,0.8,0.1c0.2-0.2,0.4-0.5,0.7-0.7c0.2-0.1,0.3-0.2,0.5-0.4
			c0.1-0.1,0.2-0.2,0.3-0.3c0.2-0.2,0.4-0.3,0.6-0.5c0.1-0.1,0.5-0.3,0.7-0.2c0.2-0.1,0.5-0.3,0.7-0.4c0.2-0.2,0.5-0.3,0.8-0.3
			c0.2,0,0.3,0,0.5,0c0.2,0,0.2,0,0.4-0.1c0.3-0.1,0.4,0.2,0.6,0.4c0.2,0.2,0.5,0.2,0.7,0.1c0.2,0,0.3-0.1,0.4-0.1
			c0.2-0.1,0.2-0.1,0.2-0.3c0-0.1,0-0.2,0-0.4c-0.1-0.1-0.1-0.3-0.1-0.4c0-0.2-0.1-0.4-0.1-0.6c0-0.1,0-0.3,0.1-0.4
			c0.1-0.1,0.2,0,0.2,0.1c0.1,0.2,0.1,0.3,0.2,0.5c0.2,0.3,0.4,0.5,0.5,0.8c0.1,0.3,0.4,0,0.6-0.1c0.2-0.2,0.5-0.5,0.9-0.4
			c0.2,0,0.3,0.1,0.3,0.3c0,0.1,0,0.3,0.2,0.3c0.1,0,0.2-0.1,0.3-0.1c0.2,0,0.2,0.1,0.3,0.2c0.1,0.2,0.2,0.1,0.3,0
			c0.1-0.2,0.1-0.4,0.1-0.6c0-0.2,0.1-0.4,0.1-0.6c0-0.2,0-0.3,0-0.4c0-0.1,0-0.2,0-0.3c0-0.1-0.1-0.2-0.1-0.3c0-0.1,0.1-0.2,0.1-0.4
			s-0.1-0.3-0.1-0.4c-0.1-0.2-0.3-0.5-0.3-0.7c0-0.1,0-0.3,0.2-0.3c0.1,0,0.3,0,0.5,0c0.1-0.2,0-0.5,0-0.8c0-0.2-0.2-0.4-0.1-0.6
			c0-0.2,0.2-0.4,0.4-0.4c0.2,0,0.3,0.3,0.3,0.4c0.1,0.2,0,0.3,0.1,0.5c0.1,0.2,0.2,0.3,0.3,0.5c0.1,0.2,0.1,0.3,0.2,0.5
			c0.1,0.2,0.1,0.5,0.2,0.7c0.1,0.2,0.2,0.4,0.3,0.5c0.1,0.2,0.3,0.5,0.4,0.7c0.2,0.2,0.4,0.6,0.8,0.5c0.5-0.1,0.4-0.6,0.4-1
			c0-0.2,0-0.3,0.1-0.4c0.1-0.3,0-0.5,0-0.8c-0.1-0.3-0.1-0.6-0.3-0.9c-0.2-0.3-0.4-0.6-0.5-0.9c-0.1-0.1-0.2-0.3-0.2-0.5
			c0-0.2,0.1-0.3,0.2-0.4c0.1-0.2,0.1-0.3,0.1-0.5c0-0.1,0-0.4,0.1-0.4c0.1-0.1,0.3,0,0.4,0.1c0.1,0.1,0,0.2,0,0.4
			c0,0.3,0.2,0.6,0.4,0.9l0.2,0.2c0,0,0.1,0.1,0.1,0.1c0.1,0,0.1,0,0.2,0c0.1,0,0.2,0.1,0.2,0.1c0.1,0.1,0.2,0.1,0.2,0.2
			c0,0.1-0.1,0.2,0,0.3c0.1,0.1,0.2,0.1,0.3,0.2c0.1,0,0.2,0,0.3,0.1c0.2,0.1,0.2,0.4,0.2,0.6c0,0.1,0,0.2,0,0.3c0,0,0,0.1,0,0.2
			c0,0.1,0.1,0.1,0.2,0.1c0.4,0.2-0.2,0.5-0.3,0.7c0,0,0,0.1,0,0.1c0,0.1,0,0.3,0,0.4c0,0.2,0.2,0.3,0.4,0.2c0.1,0,0.4-0.2,0.5,0
			c0,0.1,0,0.2,0,0.2c0,0.2-0.2,0.2-0.4,0.3c-0.1,0.1-0.2,0.2-0.2,0.3c0,0.1,0,0.2,0.1,0.2c0.1,0,0.2,0,0.3-0.1
			c0.1-0.1,0.3-0.2,0.4-0.3c0.1-0.1,0.2-0.2,0.3-0.2c0.1,0,0.1,0.1,0.1,0.2c0,0.1-0.1,0.2-0.2,0.3c-0.1,0.1-0.2,0.3-0.3,0.4
			c-0.2,0.2-0.4,0.4-0.6,0.6c-0.1,0.1-0.3,0.3-0.2,0.5c0,0.1,0.1,0.1,0.1,0.1c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0,0l0.2-0.3
			c0.1-0.2,0.3-0.3,0.4-0.5c0.2-0.2,0.3-0.3,0.5-0.5c0.3-0.3,0.7-0.5,0.9-0.9c0.2-0.3,0.4-0.6,0.5-1c0.1-0.2,0.1-0.5,0.1-0.8
			c0-0.3,0-0.5-0.1-0.7c-0.1-0.2-0.2-0.5-0.2-0.7c0.1-0.3,0.4-0.5,0.6-0.3c0.2,0.1,0.3,0.3,0.4,0.6c0.1,0.3,0.4,0.3,0.7,0.4
			c0.1,0,0.2,0.1,0.3,0.1c0.2,0,0.4-0.2,0.5-0.3c0.1-0.1,0.2-0.2,0.4-0.3c0.3-0.1,0.5-0.4,0.8-0.6c0.1-0.1,0.2-0.2,0.3-0.4
			c0-0.1,0.2-0.2,0.3-0.2c0.2-0.1,0.5,0,0.8-0.1c0.3,0,0.2-0.4,0.5-0.5c0.1-0.1,0.3-0.2,0.5-0.2c0.1,0,0.3,0,0.4,0
			c0.1,0.1,0.2,0.2,0.2,0.3c0,0.1-0.1,0.3,0,0.4c0.1,0.1,0.3,0.1,0.4,0.1c0.2,0,0.3,0,0.5,0c0.3,0,0.5-0.3,0.5-0.6
			c0-0.1,0-0.3-0.1-0.4c0-0.1,0-0.2,0.1-0.3c0.2-0.1,0.5-0.2,0.7-0.2c0.2,0.1,0.2,0.4,0.2,0.5c0,0.1,0.1,0.2,0.2,0.3
			c0.1,0,0.3,0,0.3,0c0.3-0.1,0.6-0.2,0.9-0.4c0.1-0.1,0.2-0.1,0.3-0.1c0.1,0,0.2,0,0.3,0.1c0.1,0,0.1,0.1,0.2,0.1
			c0.1,0,0.2-0.1,0.3-0.2c0.1-0.2,0.2-0.4,0.4-0.6c0.1,0,0.1,0,0.2-0.1c0.1,0,0.1-0.1,0.2-0.1c0.1-0.1,0.2-0.1,0.4-0.1
			c0.3,0,0.2,0.3,0.4,0.3c0.1,0,0.2,0,0.3-0.1c0.1,0,0.2-0.1,0.3-0.1c0.1,0,0.1-0.1,0.1-0.1c0,0,0.1,0,0.1-0.1
			c0.2-0.2,0.2-0.4,0.3-0.6c0.1-0.2,0-0.4,0.2-0.6c0.1-0.1,0.2-0.2,0.3-0.2c0.3-0.2,0.5-0.4,0.5-0.7c0-0.4,0-0.7,0.2-1
			c0.2-0.3,0.4-0.5,0.7-0.7c0.3-0.2,0.6-0.3,0.9-0.4c0.4-0.1,0.7,0.1,1.1,0.1c0.2,0,0.3-0.2,0.5-0.3c0,0-1.2-2-1.6-2.6
			c-0.4-0.6-0.2-0.5-0.2-1.1c0-0.6-0.3-1-0.5-1.7s-0.2-0.8-0.7-1.5c-0.4-0.7-0.4-0.2-1.5,0c-1.1,0.2-0.5-0.4-0.6-0.8
			c0-0.4-2.1-0.4-2.4-0.6c-0.3-0.2-0.3-0.4-1-0.7c-0.2,0-0.3-0.1-0.4-0.1c-0.2,0,0,0.2,0.1,0.5c0.2,0.4-0.1,0.2-0.5,0.4
			c-0.4,0.2-0.1,1.3-0.4,2s-1.4-1-1.7-1.6c-0.3-0.6-1.1,0.2-1.4,0.5c-0.4,0.3-1.2,0.1-1.7,0.1c-0.4,0-0.4,0.3-0.5,0.6
			c-0.1,0.3-0.6,0.1-0.7,0c-0.1-0.1-0.9,0.2-1.5,0.6c-0.7,0.4-0.6,0.3-1.2-0.1c-0.6-0.4-0.2-1.1-0.1-2c0.1-0.9,0.7-0.7,1.1-1.2
			c0.4-0.5,0.4-0.3,0.9-0.8c0.5-0.5,0.1-0.7,0.1-1.1c0-0.4-1.2-0.5-1.8-0.6c-0.6-0.2-0.8,0.2-0.9,0.7c-0.1,0.5-0.3,0-1,0.4
			c-0.7,0.4-0.6-0.9-0.7-1.3c0-0.1-0.1-0.1-0.1-0.1c0,0,0.1-0.4,0.2-1c0.1-0.7-0.1-0.7-0.1-0.9c0-0.2,0.1-0.6,0.4-1
			c0.2-0.4-0.1-0.4,0-0.9c0.1-0.5,0.4-0.5,0.9-0.9c0.5-0.4-0.2-0.3-1.2-0.1c-1,0.2,0.3-1.5,0.6-2.3c0.3-0.8-0.5-0.8-1.2-1.1
			c-0.6-0.3-0.3-1.2,0-1.7c0.3-0.5-0.5-0.7-0.7-0.9c-0.2-0.2,0-0.4,0.3-1.4c0.3-1-0.5-0.3-1.1-0.6c-0.5-0.3-0.3-0.9-0.2-1.8
			c0-1,0.2-0.8,0.4-1c0.2-0.2,0.3-0.6,0.4-1s0.6-0.4,1.2-0.8c0.7-0.4,0-0.3,0.4-0.8c0.4-0.4,0.8-0.5,1.1-0.7c0.3-0.2,0.1-0.8-0.2-1.3
			c-0.3-0.5-0.3-0.7-0.1-1.6c0.2-0.9-1.4-2.3-1.5-2.4c0,0,0,0,0.1,0c0.3-0.1,0.7-0.6,1-0.8c0.4-0.2,0.4-0.3,0.7-0.2
			c0.4,0.1,0.3-0.4,0.4-0.8c0.2-0.4,0.4-0.4,0.8-0.2c0.3,0.2,0.8,0,1.2-0.3c0.4-0.2,0.6-0.3,0.9,0c0.3,0.3,0.5-0.7,0.9-1
			c0.3-0.3,0.6-0.3,1.4-0.1c0.8,0.2,1.3-1.1,1.5-1.3c0.2-0.2,0.8-0.4,1.2-0.2c0.4,0.2,0.6,0,0.8-0.6c0.2-0.6,0.4,0.2,0.7,0.6
			c0.3,0.3,0.5,0.3,0.9,0.5c0.3,0.2,0.4,0,0.8-0.4c0.3-0.4-0.5-0.3-0.6-0.6c-0.1-0.3,0.3-0.4,0.7-0.3c0.1,0,0.1-0.1,0.1-0.3
			c-0.1-0.5-0.6-1.5-0.6-2c0-0.6-0.1-0.5-0.7-0.6c-0.6-0.1-0.2-1.2-0.1-1.7c0.1-0.5-0.2-0.3-0.8-0.7c-0.5-0.4,0-0.4,0.2-0.6
			c0.2-0.2,0-0.2,0.2-0.8c0.2-0.6,0.4-0.1,0.9-0.4s-0.1-0.4-0.4-0.6c-0.4-0.2-0.2-0.5-0.4-0.8c-0.1-0.3-1.1,0.1-1.2-0.2
			c-0.1-0.3-0.4,0.1-0.9,0c-0.5,0,0.3-0.2-0.4-0.8c-0.5-0.4-0.6-0.4-0.7-0.4c0.1-2,1-2.1,1.4-2.6c0.5-0.4,0.8-0.3,1.4-0.2
			c0.6,0.1,0.5-0.4,0.4-1.1c0-0.7,0-1.9-0.2-2.2c-0.2-0.3,0.1-0.8-0.1-1.4c-0.2-0.6-0.8-0.1-1.1,0.4c-0.3,0.5-1.3-0.9-1.9-1
			c-0.6,0-0.6-0.7-0.8-1.5c-0.2-0.8-1.1-0.8-1.7-0.9c-0.6-0.1-1-0.5-1-1.1c0-0.7,0.9-0.3,1.3-0.1c0.4,0.1,0.9,0,1.4,0
			c0.5,0,0.4-0.3,0.4-0.5c0-0.3-0.1-1.3,0.3-1.9c0.3-0.6,0.5-0.5,1-0.3c0.5,0.2,0.5-0.7,0.4-1c-0.1-0.3,0.1-1.1-0.1-1.6
			c-0.2-0.5,0.2-0.5,0.3-0.7c0.1-0.2,0.6-0.1,1.2-0.1c0.6,0,0.3,0.2,0.6,0.6c0.3,0.4,0.8,1,1.2,1.5c0.4,0.4,0.5-0.1,0.6-0.4
			c0.1-0.3,1.2-0.5,1.5-0.9c0.3-0.3,0.4-0.1,1.2-0.3c0.7-0.2-0.3-1.2-0.5-1.3c-0.3-0.1-0.2-0.5-0.2-0.8c0-0.4-0.9-0.5-1.4-0.4
			c-0.5,0.1-0.4-0.6-0.4-0.8c-0.1-0.2,0.2-0.7,0.2-0.7c0,0-0.1-0.1-0.1-0.1c-0.2-0.5-0.6-0.3-0.9-0.4c-0.3-0.1-0.4,0.2-0.9,0.2
			c-0.5,0.1-0.8,0-1.8,0.3c-1,0.3-2.5-1.8-2.9-2.6c-0.4-0.8-0.9-0.3-2.1-0.2c-1.2,0.1-0.6-0.5-0.3-0.8c0.2-0.3,0.5-0.6,0.9-1.3
			c0.4-0.7,1.2-0.7,1.5-0.8c0.3-0.1,0.3-0.3,0.4-0.6c0-0.3,0.2-0.5,0.8-0.8c0.6-0.3,0.3-0.2,0.3-0.5s0.6-0.2,0.8-0.2
			c0.2,0,0.4-0.2,0.5-0.5c0.1-0.3,0.4,0,0.8-0.7c0.4-0.7,0.2,0,1-1c0.7-1-0.4-0.1-1,0.3c-0.6,0.4-2,0.9-3.3,1.7
			c-1.3,0.8,0.4-1.5,0.7-2.1c0.3-0.6,0-0.5,0-0.5C67.8,2,67.7,2,67.4,1.9c-0.5,0-1.1-0.9-1.3-0.8c-0.2,0.1-0.5,0-0.6,0.5
			s-0.2,0.9-0.4,0.8c-0.2-0.1-0.2-0.6-1-0.7c-0.7-0.1-1.5,0.3-1.8-0.2c-0.3-0.5-0.3-0.8-0.7-0.7c-0.4,0.1-2,0.1-2.3-0.3
			c-0.3-0.4-0.7-0.8-0.7-0.5v0.2c0,0.5-0.2,1.5-0.4,1.8c-0.2,0.3,0,0.9-0.2,1.1c-0.2,0.2,0,0.5-0.2,0.6c-0.2,0.1,0.1,0.6-0.1,0.8
			c-0.2,0.2,0,0.9-0.7,1.2C56.6,6,56,6.1,55.6,6.6c-0.5,0.5-1.4,1.2-2.1,1.1C52.7,7.6,52,7,51.6,7.4c-0.4,0.4-0.8,0.4-1.3,0.4
			c-0.5,0-1.1-0.2-1.1,0.2c0,0.4,0,0.7-0.5,0.8c-0.5,0-1,0-1.2,0.3c-0.2,0.4-0.4,1-1.2,1.2s-2-0.1-2.4,0.4c-0.4,0.6-1.5,1.1-2.1,1.1
			c-0.6,0-1.8-0.1-1.8-0.1c0,0.1,0,0.2-0.1,0.4c-0.3,0.6-0.4,0.8-0.2,1c0.2,0.2,0.5,0.5,0.2,0.6c-0.3,0.2-0.8,0.2-0.4,0.4
			c0.4,0.2,0.4,0,0.7,0.5c0.3,0.5,0.7,0.9,0.5,1.3c-0.2,0.4-0.5,0.8-0.1,0.9c0.4,0.1,1,0.1,1.1,0.5c0.1,0.4-0.6,0.2,0.2,0.5
			c0.8,0.3,1.4,0.5,1,1c-0.3,0.6-0.3,0.6-0.2,0.9c0.1,0.3,0.3-0.1-0.1,0.7l-0.1,0.1c-0.4,0.7-0.4,0.5-0.9,0.7c-0.5,0.2-0.9,1-1.2,0.5
			c-0.2-0.5-0.7-1.2-0.8-1c-0.1,0.3-0.2,0.6-0.6,0.5c-0.4-0.1-1.4-0.9-1.5-0.6c-0.1,0.3-0.5,0.7-0.6,0.4c-0.2-0.2-0.7-0.7-1-0.5
			c-0.3,0.2-0.2,0.5-0.6,0.2c-0.3-0.2-0.5-0.6-0.7-0.5c-0.2,0.1-1.3,0.7-0.7,0.8c0.7,0.1,1.7,0.1,1.7,0.6c0,0.5-0.1,1.1,0.2,1.1
			c0.4,0,1,0,1.1,0.2c0.1,0.3,0.9,0.2,0.7,0.6c-0.1,0.4,0,0.9-0.4,1c-0.4,0.1-0.7-0.4-0.9-0.1c-0.2,0.4-0.5,0.8-0.7,0.6
			c-0.2-0.2-0.5-0.3-0.9,0c-0.4,0.3-0.6-0.5-0.7,0.5c0,1-0.5,0.9-0.1,1.3c0.3,0.4,0.5,0.7,0.6,1.2c0.2,0.6-0.2,0.7-0.3,1.1
			c0,0.2-0.1,0.5-0.1,0.7c0.6,0.1,1.7,0.2,2.1,0.2c0.6,0,0.1,1-0.1,1.4c-0.2,0.4,0.2,0.4,0.6,0.6c0.4,0.1,0.5,0.5,0.7,0.9
			c0.2,0.4,0,1.1-0.2,1.5c-0.2,0.4-0.1,0.6,0,1.1c0.1,0.4-0.2,0.2-0.6,0.6c-0.3,0.4-0.6,0.3-1.5,0.2c-0.9-0.1-2,1.3-2.3,1.7
			c-0.3,0.4-0.4,0.2-0.7,0c-0.2-0.2-1.5,0.2-2,0c-0.5-0.2-0.5,0-1,0.2c-0.5,0.2-0.9,0.1-1.4,0.3c-0.4,0.2-1.3,0.1-1.4,0.4
			c-0.1,0.3,0.4,0.7,0.7,0.7s0.1,0.7,0,0.9c-0.1,0.2,0.2,1.1,0,1.6c-0.1,0.5,0,0.4-0.6,0.7c-0.6,0.3,0.9,0.1,1,0.4
			c0.1,0.3,0.2,0.6,1.1,0.9c0.8,0.3,0.3,1.7,0.3,1.9s0.4,0.3,0.6,0.3c0.2,0,0.2,0.1,0.3,0.4c0.1,0.3,0.3,0.5,0.8,1
			c0.2,0.1,0.3,0.1,0.4,0.1c0.1,0.3,0.3,0.8,0.3,1.5c-0.1,1.2-0.4,2-0.3,2.4c0.1,0.2,0.2,0.4,0.2,0.5s0.1,0.3,0.1,0.5
			c-0.1,0.3-0.4,0.7-0.1,0.8c0.4,0.2,0.7,0,0.4,0.5c-0.3,0.4-0.8,0.9-0.4,1.1c0.4,0.2,0.6,0,0.4,0.4c-0.2,0.5-0.6,0.5-0.3,1
			c0.4,0.5,1.2,0.6,0.8,1.4c-0.4,0.8-0.1,0.9-1,1.2c-0.9,0.3-2.1,0-2.3,0.7c-0.2,0.7-0.8,1.2-0.7,1.7c0.1,0.5-0.2,1.1,0.3,0.7
			c0.5-0.4,0.5-0.7,0.8-0.7c0.4,0,1.5,0.3,1.7,0.1c0.2-0.2,0.9-0.1,0.8,0s0.6,0.7,0.5,1.1c-0.1,0.5-0.4,0.4-0.5,0.7
			c-0.1,0.3-0.3,0.6-0.5,0.6c-0.2,0-0.6,0.7-0.3,0.8c0.3,0.1,0.8,0.3,0.3,0.9c-0.5,0.5-0.9,1-0.6,1.3C31.2,67.7,31.9,68.9,31.7,69.4
			C31.6,69.4,31.6,69.4,31.7,69.4z"/>
		</svg>
		</div>

		<!-- 区域名称 -->
		<div class="blue4 fs10" style="width:29px; height:20px; left:67px; top:116px; font-weight: bold; ">
		新疆
		</div>
		<div class="ffnum blue4 fs12" style="width:36px; height:28px; left:65px; top:128px; ">
		<span id="area_normal_rate_100007"></span><span class="sub ffnum blue4 fs10">%</span>
		</div>


		<div class="blue4 fs10" style="width:29px; height:20px; left:130px; top:153px; font-weight: bold; ">
		西北
		</div>
		<div class="ffnum blue4 fs12" style="width:36px; height:28px; left:129px; top:165px; ">
		<span id="area_normal_rate_100006"></span><span class="sub ffnum blue4 fs10">%</span>
		</div>

		<div class="blue4 fs10" style="width:29px; height:20px; left:146px; top:204px; font-weight: bold; ">
		西南
		</div>
		<div class="ffnum blue4 fs12" style="width:36px; height:28px; left:143px; top:216px; ">
		<span id="area_normal_rate_100004"></span><span class="sub ffnum blue4 fs10">%</span>
		</div>

		<div class="blue4 fs10" style="width:29px; height:20px; left:196px; top:138px; font-weight: bold; ">
		华北
		</div>
		<div class="ffnum blue4 fs12" style="width:36px; height:28px; left:195px; top:149px; ">
		<span id="area_normal_rate_100001"></span><span class="sub ffnum blue4 fs10">%</span>
		</div>

		<div class="blue4 fs10" style="width:29px; height:20px; left:288px; top:94px; font-weight: bold; ">
		东北
		</div>
		<div class="ffnum blue4 fs12" style="width:36px; height:28px; left:285px; top:106px; ">
		<span id="area_normal_rate_100003"></span><span class="sub ffnum blue4 fs10">%</span>
		</div>

		<div class="blue4 fs10" style="width:29px; height:20px; left:248px; top:169px; font-weight: bold; ">
		华东
		</div>
		<div class="ffnum blue4 fs12" style="width:36px; height:28px; left:245px; top:180px; ">
		<span id="area_normal_rate_100002"></span><span class="sub ffnum blue4 fs10">%</span>
		</div>

		<div class="blue4 fs10" style="width:29px; height:20px; left:210px; top:223px; font-weight: bold; ">
		中南
		</div>
		<div class="ffnum blue4 fs12" style="width:36px; height:28px; left:208px; top:234px; ">
		<span id="area_normal_rate_100005"></span><span class="sub ffnum blue4 fs10">%</span>
		</div>


		<!-- 图钉 -->

		<!-- 北京 -->
		<div id="pin_PEK" class="pin_green" style="width:34px; height:32px; left:239px; top:144px; ">
		</div>
		<div class="fs11" style="width:30px; height:20px; left:241px; top:131px; font-weight: bold; ">
		北京
		</div>

		<!-- 海口 -->
		<div id="pin_HAK" class="pin_green" style="width:34px; height:32px; left:200px; top:269px; ">
		</div>
		<div class="fs11" style="width:30px; height:20px; left:204px; top:254px; font-weight: bold; ">
		海口
		</div>

		<!-- 西安 -->
		<div id="pin_XIY" class="pin_green" style="width:34px; height:32px; left:203px; top:181px; ">
		</div>
		<div class="fs11" style="width:30px; height:20px; left:192px; top:170px; font-weight: bold; ">
		西安
		</div>

		<!-- 广州 -->
		<div id="pin_CAN" class="pin_green" style="width:34px; height:32px; left:229px; top:249px; ">
		</div>
		<div class="fs11" style="width:30px; height:20px; left:228px; top:235px; font-weight: bold; ">
		广州
		</div>

		<!-- 杭州 -->
		<div id="pin_HGH" class="pin_green" style="width:34px; height:32px; left:267px; top:204px; ">
		</div>
		<div class="fs11" style="width:30px; height:20px; left:280px; top:213px; font-weight: bold; ">
		杭州
		</div>

		<!-- 三亚 -->
		<div id="pin_SYX" class="pin_green" style="width:34px; height:32px; left:194px; top:282px; ">
		</div>
		<div class="fs11" style="width:30px; height:20px; left:182px; top:269px; font-weight: bold; ">
		三亚
		</div>

		<!-- 丽江 -->
		<div id="pin_LJG" class="pin_green" style="width:34px; height:32px; left:137px; top:223px; ">
		</div>
		<div class="fs11" style="width:30px; height:20px; left:130px; top:241px; font-weight: bold; ">
		丽江
		</div>

		<!-- 青岛 -->
		<div id="pin_TAO" class="pin_green" style="width:34px; height:32px; left:263px; top:158px; ">
		</div>
		<div class="fs11" style="width:30px; height:20px; left:276px; top:171px; font-weight: bold; ">
		青岛
		</div>

		<!-- 沈阳 -->
		<div id="pin_SHE" class="pin_green" style="width:34px; height:32px; left:274px; top:132px; ">
		</div>
		<div class="fs11" style="width:30px; height:20px; left:276px; top:145px; font-weight: bold; ">
		沈阳
		</div>

		<!-- 南京 -->
		<div id="pin_NKG" class="pin_yellow" style="width:34px; height:32px; left:256px; top:189px; ">
		</div>
		<div class="fs11" style="width:50px; height:20px; left:273px; top:192px; font-weight: bold; ">
		南京
		</div>

		<!-- 重庆 -->
		<div id="pin_CKG" class="pin_yellow" style="width:34px;height:32px;left: 182px;top: 192px; ">
		</div>
		<div class="fs11" style="width:50px; height:20px; left:182px; top:210px; font-weight: bold;  ">
		重庆
		</div>




		</div> <!-- #china_map -->


		<!--
		<div class="fs11 blue2" id="comp_rank_list_month">

		</div>
		<div class="fs15 blue1 comp_rank_month" >
		民航月度排名 <span class="yellow" id="comp_rank_month"></span>
		</div>


		<div class="fs11 blue2" id="comp_rank_list_year">

		</div>
		<div class="fs15 blue1 comp_rank_year" >
		民航年度排名 <span class="yellow" id="comp_rank_year"></span>
		</div>
		-->

		<div id="base_cards" >
			<div class="reel" >

				<div id="arp_code_PKX" class="card bluecard" >
					<div class="head" >
						<div class="city" >北京</div>
						<div class="weather " style="pointer-events: auto;">
							<span class=""></span>
						</div>
					</div>
					<div class="cont" >
						<div class="itm itm1" >
							<span class="fs10">始发</span><br>
							<span class="normal_rate1 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm2" >
							<span class="fs10">进港</span><br>
							<span class="normal_rate2 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm3" >
							<span class="fs10">出港</span><br>
							<span class="normal_rate3 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm4" >
							<span class="fs10">整体</span><br>
							<span class="normal_rate4 num"></span><span class="sb">%</span>
						</div>
					</div>

				</div>


				<div id="arp_code_HGH" class="card bluecard" >
					<div class="head" >
						<div class="city" >杭州</div>
						<div class="weather " style="pointer-events: auto;">
							<span class=""></span>
						</div>
					</div>
					<div class="cont" >
						<div class="itm itm1" >
							<span class="fs10">始发</span><br>
							<span class="normal_rate1 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm2" >
							<span class="fs10">进港</span><br>
							<span class="normal_rate2 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm3" >
							<span class="fs10">出港</span><br>
							<span class="normal_rate3 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm4" >
							<span class="fs10">整体</span><br>
							<span class="normal_rate4 num"></span><span class="sb">%</span>
						</div>
					</div>
					
				</div>


				<div id="arp_code_SYX" class="card bluecard" >
					<div class="head" >
						<div class="city" >三亚</div>
						<div class="weather " style="pointer-events: auto;">
							<span class=""></span>
						</div>
					</div>
					<div class="cont" >
						<div class="itm itm1" >
							<span class="fs10">始发</span><br>
							<span class="normal_rate1 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm2" >
							<span class="fs10">进港</span><br>
							<span class="normal_rate2 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm3" >
							<span class="fs10">出港</span><br>
							<span class="normal_rate3 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm4" >
							<span class="fs10">整体</span><br>
							<span class="normal_rate4 num"></span><span class="sb">%</span>
						</div>
					</div>
					
				</div>

				<div id="arp_code_HAK" class="card bluecard" >
					<div class="head" >
						<div class="city" >海口</div>
						<div class="weather " style="pointer-events: auto;">
							<span class=""></span>
						</div>
					</div>
					<div class="cont" >
						<div class="itm itm1" >
							<span class="fs10">始发</span><br>
							<span class="normal_rate1 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm2" >
							<span class="fs10">进港</span><br>
							<span class="normal_rate2 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm3" >
							<span class="fs10">出港</span><br>
							<span class="normal_rate3 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm4" >
							<span class="fs10">整体</span><br>
							<span class="normal_rate4 num"></span><span class="sb">%</span>
						</div>
					</div>
					
				</div>

				<div id="arp_code_XIY" class="card bluecard" >
					<div class="head" >
						<div class="city" >西安</div>
						<div class="weather " style="pointer-events: auto;">
							<span class=""></span>
						</div>
					</div>
					<div class="cont" >
						<div class="itm itm1" >
							<span class="fs10">始发</span><br>
							<span class="normal_rate1 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm2" >
							<span class="fs10">进港</span><br>
							<span class="normal_rate2 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm3" >
							<span class="fs10">出港</span><br>
							<span class="normal_rate3 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm4" >
							<span class="fs10">整体</span><br>
							<span class="normal_rate4 num"></span><span class="sb">%</span>
						</div>
					</div>
					
				</div>

				<div id="arp_code_CAN" class="card bluecard" >
					<div class="head" >
						<div class="city" >广州</div>
						<div class="weather " style="pointer-events: auto;">
							<span class=""></span>
						</div>
					</div>
					<div class="cont" >
						<div class="itm itm1" >
							<span class="fs10">始发</span><br>
							<span class="normal_rate1 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm2" >
							<span class="fs10">进港</span><br>
							<span class="normal_rate2 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm3" >
							<span class="fs10">出港</span><br>
							<span class="normal_rate3 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm4" >
							<span class="fs10">整体</span><br>
							<span class="normal_rate4 num"></span><span class="sb">%</span>
						</div>
					</div>
					
				</div>


				<div id="arp_code_LJG" class="card bluecard" >
					<div class="head" >
						<div class="city" >丽江</div>
						<div class="weather " style="pointer-events: auto;">
							<span class=""></span>
						</div>
					</div>
					<div class="cont" >
						<div class="itm itm1" >
							<span class="fs10">始发</span><br>
							<span class="normal_rate1 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm2" >
							<span class="fs10">进港</span><br>
							<span class="normal_rate2 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm3" >
							<span class="fs10">出港</span><br>
							<span class="normal_rate3 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm4" >
							<span class="fs10">整体</span><br>
							<span class="normal_rate4 num"></span><span class="sb">%</span>
						</div>
					</div>
					
				</div>



				<div id="arp_code_TAO" class="card bluecard" >
					<div class="head" >
						<div class="city" >青岛</div>
						<div class="weather " style="pointer-events: auto;">
							<span class=""></span>
						</div>
					</div>
					<div class="cont" >
						<div class="itm itm1" >
							<span class="fs10">始发</span><br>
							<span class="normal_rate1 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm2" >
							<span class="fs10">进港</span><br>
							<span class="normal_rate2 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm3" >
							<span class="fs10">出港</span><br>
							<span class="normal_rate3 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm4" >
							<span class="fs10">整体</span><br>
							<span class="normal_rate4 num"></span><span class="sb">%</span>
						</div>
					</div>
					
				</div>


				<div id="arp_code_SHE" class="card bluecard" >
					<div class="head" >
						<div class="city" >沈阳</div>
						<div class="weather " style="pointer-events: auto;">
							<span class=""></span>
						</div>
					</div>
					<div class="cont" >
						<div class="itm itm1" >
							<span class="fs10">始发</span><br>
							<span class="normal_rate1 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm2" >
							<span class="fs10">进港</span><br>
							<span class="normal_rate2 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm3" >
							<span class="fs10">出港</span><br>
							<span class="normal_rate3 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm4" >
							<span class="fs10">整体</span><br>
							<span class="normal_rate4 num"></span><span class="sb">%</span>
						</div>
					</div>
					
				</div>


				<div id="arp_code_NKG" class="card bluecard" >
					<div class="head" >
						<div class="city" >南京</div>
						<div class="weather " style="pointer-events: auto;">
							<span class=""></span>
						</div>
					</div>
					<div class="cont" >
						<div class="itm itm1" >
							<span class="fs10">始发</span><br>
							<span class="normal_rate1 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm2" >
							<span class="fs10">进港</span><br>
							<span class="normal_rate2 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm3" >
							<span class="fs10">出港</span><br>
							<span class="normal_rate3 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm4" >
							<span class="fs10">整体</span><br>
							<span class="normal_rate4 num"></span><span class="sb">%</span>
						</div>
					</div>
					
				</div>

				<div id="arp_code_CKG" class="card bluecard" >
					<div class="head" >
						<div class="city" >重庆</div>
						<div class="weather " style="pointer-events: auto;">
							<span class=""></span>
						</div>
					</div>
					<div class="cont" >
						<div class="itm itm1" >
							<span class="fs10">始发</span><br>
							<span class="normal_rate1 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm2" >
							<span class="fs10">进港</span><br>
							<span class="normal_rate2 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm3" >
							<span class="fs10">出港</span><br>
							<span class="normal_rate3 num"></span><span class="sb">%</span>
						</div>
						<div class="itm itm4" >
							<span class="fs10">整体</span><br>
							<span class="normal_rate4 num"></span><span class="sb">%</span>
						</div>
					</div>
					
				</div>



				
			</div>

		</div>
		
	</div><!-- // -->



	<div id="col_r">

		<div class="titlebar">重点关注航班</div>
		<!-- ********************************* -->
		<!-- 运行一览 -->
		<!-- ********************************* -->
		<div id="importantFlight" class="frame row1">

			<div id="tab_vip" class="tab tab1 selected">VIP</div>
			<div id="tab_warning" class="tab tab2">预警</div>
			<div id="tab_important" class="tab tab3">重点关注</div>
			<div class="legend fs12 blue2">
				注：<span class="itm1">延误2-4小时</span><span class="itm2">延误4小时以上</span>
			</div>
			<div id="flt_list_holder" class="flt_tabs">
				<div class="wrap">
					<!--
					<div class="flt_list_itm itm selected" style="left:0px"><span class="flag_red">JD5595</span></div>
					<div class="flt_list_itm itm" style="left:87px"><span class="flag_yellow">JD5595</span></div>
					<div class="flt_list_itm itm" style="left:174px"><span class="flag_yellow">JD5595</span></div>
					-->
				</div>
			</div>
			<div class="flt_info">
				<div class="flt" ><span class="fltno"></span> 航班详情</div>
				<table>
					<thead>
						<tr>
							<th width="30"></th>
							<th width="150">飞机号</th>
							<th width="80">起飞机场</th>
							<th width="120"></th>
							<th width="100">目的机场</th>
							<th></th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td></td>
							<td class="acno"></td>
							<td class="dep_city"></td>
							<td><div class="arr"></div></td>
							<td class="arr_city"></td>
							<td></td>
						</tr>
					</tbody>
				</table>
			</div>

		</div>

		<!-- ********************************* -->
		<!-- 不正常航班一览 -->
		<!-- ********************************* -->
		<div class="frame row2">
			<div class="blkbar">
				<div class="fs16">
					不正常航班一览
				</div>
			</div>

			<div class="box">
				<div class="ll">
					<div class="l">
						<span>返航备降取消总航班</span><br>
						<span id="flt_return_back_cancel" class="ffnum fs22"></span><span class="fs12 blue2">架次</span>
					</div>
					<div class="r">
						<div class="ib1">
							<span class="blue1 ffnum">返航备降</span><br>
							<span class="blue1 ffnum">取消</span>
						</div>
						<div class="ib2">
							<span id="flt_return_back" class="ffnum fs14"></span><span class="fs11 blue2">架次</span><br>
							<span id="flt_cancel" class="ffnum fs14"></span><span class="fs11 blue2">架次</span>
						</div>
					</div>
				</div>
				<div class="rr">
					<div class="l">
						<span>延误总航班</span><br>
						<span id="flt_delay_total" class="ffnum fs22"></span><span class="fs12 blue2">架次</span>
					</div>
					<div class="r">
						<div class="ib1">
							<span class="blue1 ffnum">0-1小时</span><br>
							<span class="blue1 ffnum">1-2小时</span><br>
							<span class="blue1 ffnum">2-4小时</span><br>
							<span class="blue1 ffnum">4小时以上</span>
						</div>
						<div class="ib2">
							<span id="flt_delay_1" class="ffnum fs14"></span><span class="fs11 blue2">架次</span><br>
							<span id="flt_delay_12" class="ffnum fs14"></span><span class="fs11 blue2">架次</span><br>
							<span id="flt_delay_24" class="ffnum fs14"></span><span class="fs11 blue2">架次</span><br>
							<span id="flt_delay_4" class="ffnum fs14"></span><span class="fs11 blue2">架次</span>
						</div>
					</div>
				</div>
			</div>

		</div>

		<!-- ********************************* -->
		<!-- 今日计划运输旅客 -->
		<!-- ********************************* -->
		<div class="frame row3">
			<div class="blkbar">
				<div class="fs16">
					今日计划运输旅客
				</div>
			</div>

			<div class="ll">
				<div class="total">
					<span class="fs12 blue1 bold">计划运输旅客总量</span>
					<span id="val_trv_num_plan" class="ffnum fs20"></span>
					<span class="fs11 blue2">人</span>
				</div>
				<canvas class="chart" id="cvs_chart_people" width="192" height="60"></canvas>
				<div class="complete">
					<span class="fs12 blue1 bold">已完成量</span>
					<span id="val_trv_num_completed" class="ffnum fs20"></span>
					<span class="fs11 blue2">人</span>
				</div>
			</div>

			<div class="rr">
				<div class="chart1">
					<canvas class="chart" id="cvs_trv1" width="100" height="100"></canvas>
					<div class="center fs12 bold">
						计划中转旅客<br>已执行占比
					</div>
					<div class="l">
						<span class="green fs12">已执行</span><span id="trv_zz_complete" class="ffnum fs12"></span>
					</div>
					<div class="r">
						<span class="blue2 fs12">待执行</span><span id="trv_zz_uncomplete" class="ffnum fs12"></span>
					</div>
				</div>

				<div class="chart2">
					<canvas class="chart" id="cvs_trv2" width="100" height="100"></canvas>
					<div class="center fs12 bold">
						VIP执行<br>占比
					</div>
					<div class="l">
						<span class="green fs12">已执行</span><span id="vip_exec_complete" class="ffnum fs12"></span>
					</div>
					<div class="r">
						<span class="blue2 fs12">待执行</span><span id="vip_exec_uncomplete" class="ffnum fs12"></span>
					</div>
				</div>
				
			</div>

		</div>
		
	</div><!-- // -->



</div>






</div><!-- /.page-wrapper -->


<script src="common.js?ver=20230202"></script>
<script src="index.js?ver=20230202"></script>


</body>
</html>