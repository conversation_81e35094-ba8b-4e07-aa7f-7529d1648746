// ------------------------------------------------------------------------
// col_mid  map 
// ------------------------------------------------------------------------


var series = [];

//国际通航城市
var gjthcs=[
	{
		"name": "纽约",
		"value": [-73.87575,40.776854]
	}, {
		"name": "伦敦",
		"value": [-0.45546,51.471278]
	}, {
		"name": "莫斯科",
		"value": [37.620184,55.753556]
	}, {
		"name": "首尔",
		"value": [126.795721,37.55761]
	}, {
		"name": "东京",
		"value": [139.753861,35.686254]
	}, {
		"name": "开罗",
		"value": [31.399803,30.111663]
	}, {
		"name": "迪拜",
		"value": [55.365172,25.251482]
	}, {
		"name": "开普敦",
		"value": [18.602085,-33.971463]
	}
];
		
series.push({
	name: 'gjthcs',
	type: 'scatter3D',
	coordinateSystem: 'geo3D',
	symbol: 'pin',
	symbolSize:32,
	label: {
		normal: {
			formatter: '{b}',
			position: 'top',
			fontSize:14,
			show: true
		}
	},
	itemStyle: {
		normal: {
			color: '#00A0E9',
			shadowBlur: 10,
			shadowColor: '#00A0E9'
		}
	},
	data: gjthcs,
});
//国际场站

var gjcz=[
	{
		"name": "巴黎",
		"value": [2.547925,49.00969]
	}, {
		"name": "新加坡",
		"value": [103.991531,1.36442]
	}
]


series.push({
	name: 'gjthcs',
	type: 'scatter3D',
	coordinateSystem: 'geo3D',
	symbol: 'pin',
	symbolSize:32,
	label: {
		normal: {
			formatter: '{b}',
			position: 'top',
			fontSize:14,
			show: true,
		}
	},
	itemStyle: {
		normal: {
			color: '#22AC38',
			shadowBlur: 10,
			shadowColor: '#22AC38'
		}
	},
	data: gjcz,
});


var option = {
    geo3D: {
        map: 'world',
		silent: true,
        itemStyle: {
            // color: '#0050ae',
			color:'#02356B',
            opacity: 1,
            borderWidth: 0.4,
            borderColor: '#2ed8e9'
        },
        light: { //光照阴影
            ambient: {
                color: '#4880ff',
                intensity: 0.8
            }
        },
		viewControl: {//用于鼠标的旋转，缩放等视角控制
			distance: 90,//默认视角距离主体的距离
			// panMouseButton: 'left',//平移操作使用的鼠标按键
			// rotateMouseButton: 'right',//旋转操作使用的鼠标按键
			alpha:90 ,// 让canvas在x轴有一定的倾斜角度
			panMouseButton:0,//禁止平移
			rotateMouseButton:0,//禁止旋转
			zoomSensitivity:0,//禁止缩放
		},
    },
	boxHeight:20,
	regionHeight:1,
	series: series
};


console.log(JSON.stringify(option));
