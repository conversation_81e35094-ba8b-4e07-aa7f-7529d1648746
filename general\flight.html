<!DOCTYPE html>
<html lang="zh">
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
		<link rel="stylesheet" type="text/css" href="css/flightCommon.css" />
		<link rel="stylesheet" type="text/css" href="css/flight.css" />
		<script src="../js/babel.min.js"></script>
		<script src="../js/polyfill.min.js"></script>
		<script src="js/echarts.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="js/world.js" type="text/javascript" charset="utf-8"></script>
		<script src="js/jquery-3.1.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="../js/util.js"></script>
		<title>航司通用AOC</title>

	</head>
	<body>
		<div id="mapBox" class="page-wrapper"></div>
		<div class="page-bgimg" style=" -moz-transform-origin:left top; pointer-events: none;"></div>
		<div class="page-wrapper" style=" -moz-transform-origin:left top;">
			<!-- head -->
			<div class="leftLogo"></div>
			<div class="pagetitle">
				<div class="col_top con_flex_row">
					<div class="co1 flex_none" style="width:20%">
						<div class="fltno"></div>
						<div class="flightTimes fs14 blue1"></div>
					</div>

					<div class="mid flex_1 con_flex_row" style="width: 60%;">
						<div class="c1 flex_1">
							<div class="t1 city1"></div>
							<div class="t2">出发机场</div>
						</div>
						<div class="c4 flex_1 leg1">
							<div class="fs12"><span class="leg1Status"></span><br />——————></div>
						</div>
						<div class="c2 flex_1 jingting">
							<div class="t1 citym"></div>
							<div class="blue fs12">经停机场</div>
						</div>
						<div class="c4 flex_1 leg2">
							<div class="fs12"><span class="leg2Status"></span><br />——————></div>
						</div>
						<div class="c3 flex_1">
							<div class="t1 city2"></div>
							<div class="t2">目的机场</div>
						</div>
					</div>

					<div class="co3 flex_none" style='width:20%'>
						<div class='fltsts sts'></div>
					</div>

				</div>
			</div>

			<!-- col-left -->
			<div class="col_left">

				<!-- 航线信息 -->
				<div class="col_left_row row_1">
					<div class="fltno fs16 bold"></div>
					<div class="timeLinesBox">
						<div class="tLB_1">
							<div class="rowTitle">
								<div class="col_1"><span></span></div>
								<div class="col_2"><span class="fs16 bold city1 t1"></span></div>
							</div>
							<div class="rowInfo fs12">
								<div class="info_col">
									<div class="blue">计划离港</div>
									<div class="fs14 bold stdChn"></div>
									<div class="blue">预计起飞</div>
									<div class="fs14 bold etdChn"></div>
								</div>
								<div class="info_col">
									<div class="blue">离港</div>
									<div class="fs14 bold atdChn"></div>
									<div class="blue">实际起飞</div>
									<div class="fs14 bold tOffChn"></div>
								</div>
								<div class="info_col">
									<div class="standBG"></div>
									<div class="standInfo">
										<div class="fs14 bold depParkPlace"></div>
										<div class="fs12 blue">停机位</div>
									</div>
								</div>
								<div class="info_delay fs14 isHiden">
									<div class="red bold">延误</div>
									<div>
										<span class="blue">原因:</span>
										<span class="bold delayInfo delayTxt"></span>
									</div>
								</div>
							</div>
						</div>
						<div class="tLB_2">
							<div class="rowTitle">
								<div class="col_1"><span></span></div>
								<div class="col_2"><span class="fs16 bold citym"></span></div>
								<div class="col_3 fs12">
									<span class="tLI_Time"></span>
									<span class="blue">经停</span>
									<span class="jingtingtime"></span>
								</div>
							</div>
							<div class="rowInfo fs12">
								<div class="info_col">
									<div class="blue t1">计划离港</div>
									<div class="fs14 bold stdChnm staChnm"></div>
									<div class="blue t2">预计起飞</div>
									<div class="fs14 bold etdChnm tDwnChnm"></div>
								</div>
								<div class="info_col">
									<div class="blue t3">离港</div>
									<div class="fs14 bold atdChnm etaChnm"></div>
									<div class="blue t4">实际起飞</div>
									<div class="fs14 bold tOffChnm ataChnm"></div>
								</div>
								<div class="info_col">
									<div class="standBG"></div>
									<div class="standInfo">
										<div class="fs14 bold depParkPlacem arrParkPlacem"></div>
										<div class="fs12 blue">停机位</div>
									</div>
								</div>
								<div class="info_delay fs14 isHiden">
									<div class="red bold">延误</div>
									<div>
										<span class="blue">原因:</span>
										<span class="bold delayInfo delayTxtm"></span>
									</div>
								</div>
							</div>
						</div>
						<div class="tLB_3">
							<div class="rowTitle">
								<div class="col_1"><span></span></div>
								<div class="col_2"><span class="fs16 bold city2 t1"></span></div>
							</div>
							<div class="rowInfo fs12">
								<div class="info_col">
									<div class="blue">计划到港</div>
									<div class="fs14 bold staChn"></div>
									<div class="blue">实际落地</div>
									<div class="fs14 bold tDwnChn"></div>
								</div>
								<div class="info_col">
									<div class="blue">预计落地</div>
									<div class="fs14 bold etaChn"></div>
									<div class="blue">到港</div>
									<div class="fs14 bold ataChn"></div>
								</div>
								<div class="info_col">
									<div class="standBG"></div>
									<div class="standInfo">
										<div class="fs14 bold arrParkPlace"></div>
										<div class="fs12 blue">停机位</div>
									</div>
								</div>
								<div class="info_delay fs14 bold isHiden">
									<div class="red">延误</div>
									<div>
										<span class="blue">原因:</span>
										<span class="delayInfo"></span>
									</div>
								</div>
				
				
							</div>
				
						</div>
					</div>
				</div>

				<!-- 天气信息 -->
				<div class="col_left_row row_2">
					<!-- weather_city1 -->
					<div class="wea_city_row weather_city1">
						<div class="bLeft_B">
							<span class="title fs16 bold plane"></span>
							<div class="titleRightLine">
								<span class="titleRightA"></span>
								<span class="titleRightB"></span>
							</div>
						</div>
						<div class="wea_info fs12 city1">
							<div class="col_1 bold">
								<div class="weather_ico"><span></span></div>
								<div class="blue"><span class="condition"></span></div>
								<div class="blue"><span class="temperature"></span></div>
							</div>
							<div class="col_2">
								<div><span class="blue">能见度:</span><span class="visibility">--</span></div>
								<div><span class="blue">风速:</span><span class="windFs">--</span></div>
								<div><span class="blue">天气警告:</span><span class="tsb">--</span></div>
							</div>
							<div class="col_3">
								<div><span class="blue">云况:</span><span class="cloudInfo">--</span></div>
								<div><span class="blue">风向:</span><span class="windFx">--</span></div>
							</div>
						</div>
					</div>
					<!-- weather_city2 -->
					<div class="wea_city_row weather_city2">
						<div class="bLeft_B">
							<span class="title fs16 bold plane"></span>
							<div class="titleRightLine">
								<span class="titleRightA"></span>
								<span class="titleRightB"></span>
							</div>
						</div>
						<div class="wea_info fs12 city1">
							<div class="col_1 bold">
								<div class="weather_ico"><span ></span></div>
								<div class="blue"><span class="condition"></span></div>
								<div class="blue"><span class="temperature"></span></div>
							</div>
							<div class="col_2">
								<div><span class="blue">能见度:</span><span class="visibility"></span></div>
								<div><span class="blue">风速:</span><span class="windFs"></span></div>
								<div><span class="blue">天气警告:</span><span class="tsb">--</span></div>
							</div>
							<div class="col_3">
								<div><span class="blue">云况:</span><span class="cloudInfo"></span></div>
								<div><span class="blue">风向:</span><span class="windFx"></span></div>
							</div>
						</div>
					</div>
				</div>

				<!-- 着陆机场跑道情况 -->
				<div class="col_left_row row_3 fs12">
					<div class="airport_run">
						<div class="bLeft_B">
							<span class="title fs16 bold">着陆机场跑道情况</span>
							<div class="titleRightLine">
								<span class="titleRightA"></span>
								<span class="titleRightB"></span>
							</div>
						</div>
						<div class="cont con_flex_row">
							<table class="table_runway">
								<tbody>
								<tr>
									<th width="50%">跑道编号</th>
									<td width="25%" id="runwaynamea">--</td>
									<td width="25%" id="runwaynameb">--</td>
								</tr>
								<tr>
									<th>天气现象</th>
									<td colspan="2" id="weatherInfo">--</td>
								</tr>
								<tr>
									<th>跑道能见度</th>
									<td><span id="visibilitya">--</span>m</td>
									<td><span id="visibilityb">--</span>m</td>
								</tr>
								<tr>
									<th>跑道视程</th>
									<td colspan="2">
										<table style="width:101%; border-collapse:collapse;margin-left: -2px;">
											<tbody>
											<tr style="border:none;background: #071E40;">
												<td width="33.3%">
													<span style="font-size: 10px;color: #b9bfde;">TDA</span><br><span id="rvrtdz">--</span>
												</td>
												<td width="33.3%">
													<span style="font-size: 10px;color: #b9bfde;">MID</span><br><span id="rvrmid">--</span>
												</td>
												<td width="33.3%">
													<span style="font-size: 10px;color: #b9bfde;">END</span><br><span id="rvrend">--</span>
												</td>
											</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr>
									<th>云底高</th>
									<td><span id="cloudheighta">--</span>m</td>
									<td><span id="cloudheightb">--</span>m</td>
								</tr>
								<tr>
									<th>10分钟风向/风速</th>
									<td><span id="tenwindwaya">--</span>m/s&nbsp;&nbsp;<span id="tenwindspeeda">--</span>°</td>
									<td><span id="tenwindwayb">--</span>m/s&nbsp;&nbsp;<span id="tenwindspeedb">--</span>°</td>
								</tr>
								<tr>
									<th>温度/露点</th>
									<td colspan="2"><span id="temperaturea">--</span>℃/<span id="dewpoint">--</span>℃</td>
								</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>

			</div>
			<!-- col-mid -->
			<div class="col_mid">

				<div class="mid_btm">
					<div class="mid_btm_left isHiden">
						<div class="bLeft_B">
							<span class="title fs16 bold">查看具体航班详情</span>
							<div class="titleRightLine">
								<span class="titleRightA"></span>
								<span class="titleRightB"></span>
							</div>
						</div>
						<div class="inputGroups">
							<input type="text" id="cfd" placeholder="出发地" onfocus="this.placeholder=''" onblur="this.placeholder='出发地'" />
							<input type="text" id="mdd" placeholder="目的地" onfocus="this.placeholder=''" onblur="this.placeholder='目的地'" />
							<select id="flyNo"></select>
							<button type="button">前往航班</button>
						</div>
					</div>

					<div class="mid_btm_right">
						<div class="mid_btm_right_row rowBg fs12">
							<div class="col">
								<div class="row">
									<div class="blue">航线距离UM（计划）</div>
									<div class="bold"><span class="ariline_dis"></span>km</div>
								</div>
								<div class="row">
									<div class="blue">航线油耗（计划）</div>
									<div class="bold oil_3"></div>
								</div>
							</div>
							<div class="col">
								<div class="row">
									<div class="blue">计划飞行时间</div>
									<div class="bold fly_time"></div>
								</div>
								<div class="row">
									<div class="blue">离港油量LB（计划）</div>
									<div class="bold oil_1"></div>
								</div>
							</div>
							<div class="col" style="width: 120px;margin-left: -8px;">
								<div class="row">
									<div class="blue">剩余飞行时间</div>
									<div class="bold"><span class="ariline_min"></span>分钟</div>
								</div>
								<div class="row">
									<div class="blue">实时油量</div>
									<div class="bold" id="oil_rt1"></div>
								</div>
							</div>
						</div>

						<div class="mid_btm_right_row">
							<div class="bLeft_B">
								<span class="title fs16 bold">飞机</span>
								<div class="titleRightLine">
									<span class="titleRightA"></span>
									<span class="titleRightB"></span>
								</div>
							</div>
							<div class="fs14 blue bold tt">座仓布局</div>
							<div class="silosImg">
								<div class='actype blue'></div>
								<div class='lb blue'></div>
							</div>
							<div class="mid_btm_info fs12">
								<div class="col_1"><span class="blue">机号:</span><span id="lb"></span></div>
								<div class="col_1"><span class="blue">机型:</span><span id="actype"></span></div>
								<div class="col_2"><span class="blue">实际起飞重量:</span><span class="takeOffWeight"></span></div>
								<div class="col_2"><span class="blue">最大起飞重量:</span><span class="mtwKg"></span></div>
							</div>
						</div>

					</div>

				</div>

			</div>
			<!-- col-right -->
			<div class="col_right">
				<div class="col_right_row">
					<div class="bLeft_B">
						<span class="title fs16 bold">机组</span>
						<div class="titleRightLine">
							<span class="titleRightA"></span>
							<span class="titleRightB"></span>
						</div>
					</div>
					<div class="row_line fs12">
						<div><span class="blue">责任机长:</span><span class="captain"></span></div>
						<div><span class="blue">其他驾驶:</span><span class="firstVice1"></span></div>
						<div><span class="blue">乘务组:</span><span class="crwStewardInf"></span></div>
						<div><span class="blue">安全员:</span><span class="safer1"></span></div>
					</div>
				</div>

				<div class="col_right_row">
					<!-- 乘客 -->
					<div class="bLeft_B">
						<span class="title fs16 bold">乘客</span>
						<div class="titleRightLine">
							<span class="titleRightA"></span>
							<span class="titleRightB"></span>
						</div>
					</div>
					<div class="icon_dzck"></div>
					<div class="row_line_lk fs14 ">
						<div>
							<span class="bold blue">本航段订座旅客总数 </span><span class="fs18 bold" id="dzck_bd">--</span> 人
						</div>
						<div>
							<span class="bold blue">全航段订座乘客人 </span><span class="fs18 bold" id="dzck_qd">--</span> 人
						</div>
					</div>
				</div>
				<!-- 本航段值机人数 -->

				<div class="col_right_row">
					<div class="row_line_zj">
						<span class="fs14 bold blue">本航段值机人数 </span>
					</div>

					<div class="passenger">
						<ul class="row1">
							<li>
								<div class="li_up fs14"><span class="passNum  bold fNum">--</span><span>人</span></div>
								<div class="li_down fs12 blue1">F舱</div>
							</li>
							<li>
								<div class="li_up fs14"><span class="passNum  bold cNum">--</span><span>人</span></div>
								<div class="li_down fs12 blue1">C舱</div>
							</li>
							<li>
								<div class="li_up fs14"><span class="passNum  bold wNum">--</span><span>人</span></div>
								<div class="li_down fs12 blue1">W舱</div>
							</li>
							<li style="margin-right: 0;">
								<div class="li_up fs14"><span class="passNum  bold yNum">--</span><span>人</span></div>
								<div class="li_down fs12 blue1">Y舱</div>
							</li>
						</ul>
						<ul class="row2">
							<li>
								<div class="li_up fs14"><span class="passNum  bold chdNum">--</span><span>人</span></div>
								<div class="li_down fs12 blue1">儿童</div>
							</li>
							<li>
								<div class="li_up fs14"><span class="passNum  bold eldNum">--</span><span>人</span></div>
								<div class="li_down fs12 blue1">老人</div>
							</li>
							<li>
								<div class="li_up fs14"><span class="passNum  bold svipNum">--</span><span>人</span></div>
								<div class="li_down fs12 blue1">要客</div>
							</li>
							<li>
								<div class="li_up fs14"><span class="passNum  bold ckiInOutNum">--</span><span>人</span></div>
								<div class="li_down fs12 blue1">中转</div>
							</li>
							<li style="margin-right: 0;">
								<div class="li_up fs14"><span class="passNum  bold spNum">--</span><span>人</span></div>
								<div class="li_down fs12 blue1">特服</div>
							</li>
						</ul>
					</div>

				</div>
				<!-- 联程旅客情况 -->
				<div class="col_right_row" style="margin-top: -15px;">
					<div class="bLeft_B">
						<span class="title fs16 bold">联程旅客情况</span>
						<div class="titleRightLine">
							<span class="titleRightA"></span>
							<span class="titleRightB"></span>
						</div>
					</div>
					<!-- 转入 -->
					<div class="transInfo_title_1 fs14 blue bold">
						转入
					</div>
					<div class="icon_ryzr"></div>
					<div class="transInfo">
						<div class="tablebox">
							<div class="tbl-body">
								<table border="0" cellspacing="0" cellpadding="0" id="inCountTable">
									<thead>
									<tr>
										<th>人数</th>
										<th>日期</th>
										<th>航班号</th>
										<th>计划到港</th>
										<th>起飞/到达</th>
									</tr>
									</thead>
									<tbody id="inCount">
									</tbody>
								</table>
							</div>
						</div>
					</div>

				</div>

				<div class="col_right_row">
					<!-- 转出 -->
					<div class="transInfo_title_2 fs14 blue bold">
						转出
					</div>
					<div class="icon_ryzc"></div>
					<div class="transInfo">
						<div class="tablebox">
							<div class="tbl-body">
								<table border="0" cellspacing="0" cellpadding="0">
									<thead>
									<tr>
										<th>人数</th>
										<th>日期</th>
										<th>航班号</th>
										<th>计划到港</th>
										<th>起飞/到达</th>
									</tr>
									</thead>
									<tbody id="outCount">
									</tbody>
								</table>
							</div>
						</div>
					</div>

				</div>

				<div class="col_right_row row_wxrw">
					<div class="bLeft_B">
						<span class="title fs16 bold">近7天工程维修任务</span>
						<div class="titleRightLine">
							<span class="titleRightA"></span>
							<span class="titleRightB"></span>
						</div>
					</div>
					<div class="tabBox fs12">
						<span class="btn btn_prev disabled"></span>
						<span class="btn btn_next disabled"></span>
						<div class="tabCon">
							<div>
								<span class="tt blue">维修类型:</span><span class="seq"></span>
							</div>
							<div>
								<span class="tt blue">维修机场:</span><span class="mntstn"></span>
							</div>
							<div>
								<span class="tt blue">任务描述:</span><span class="mntComment"></span>
							</div>
							<div>
								<span class="tt blue">开始:</span><span class="mnttStart"></span>
							</div>
							<div>
								<span class="tt blue">结束:</span><span class="mnttEnd"></span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<script src="js/config.js?ver=1"></script>
		<script src="js/common.js?ver=1"></script>
		<script type="text/babel" src="js/flight.js" charset="utf-8"></script>
	</body>
</html>
