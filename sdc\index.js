const kpi_list = [
    'FUELT', //吨公里耗油
    'NORMAL_RATE',
    'SCH_NO',
    'BOOK_NUM'
];
const kpi_id_list = {
    'FUELT': '10126', //吨公里耗油
};
const actype_list = ['A319', 'A320', 'A321', 'A330', 'B737', 'B767', 'B787', 'E190', 'A350'];
const ac_type_list = ['787', '767', '330', '737', '320', '321', '319', '350', '145', '190'];
const actypelist = '787,767,330,333,737,320,321,319,145,190,350';
const comp_cause = ['飞机故障', '运力调配', '工程机务', '航班计划', '航材保障', '航务保障', '机组保障', '飞行机组保障', '乘务组', '乘务组保障', '空警安全员', '地面保障',
    '货运保障', '公司原因', '其他航空公司原因'
];
const none_cause = ['公共安全', '机场', '军事活动', '空管', '离港系统', '联检', '旅客', '民航局航班时刻安排', '天气原因', '油料'];
// 可显示的历史 数量
const query_limit = 1;
const parent_company = 'HNAHK';
// 日期类型
const date_type = 'D';
const codelist = [];
const codelist_no_parent = [];
let stdEndUtcTime;
let stdStartUtcTime;
let stdStart;
let stdEnd;
let planNum_d = 0;
let planNum_i = 0;
let companyAcNum = new Object();
let companyAcType = new Object();
let all_company_data = new Object();
let all_company_segment_data = new Object();
let all_company_thcs_data = new Object();
let all_company_base_arp_data = new Object();
let all_company_flt = new Object();
let all_company_plannum = new Object();
let all_comp_actype_data = new Object();
let comp_normal_rate_list = new Object();
let comp_normal_rate = new Object();
let comp_pftc_list = new Object();
let comp_plf_list = new Object();
let all_comp_acregs = new Object();
let all_company_ac_use = new Object();
let actypeId2Code = new Object();
let all_delay_cause_data = new Object();
let all_comp_city = new Object();
let all_comp_city_l = new Object();
let all_hbi2_base = new Object();
let all_hbi2_base_l = new Object();
let ac_num = new Object();
let exe_ac_data_list = new Object();
let ac_list_except_other_type = new Array();
let ac_type_narrow = new Array(); //窄体机
let ac_type_wide = new Array(); //宽体机
let ac_type_regional = new Array(); //支线机
let ac_data_list_total = new Object();
let fltDelay4List = new Array();
let fltIntList = new Array();
let thcsSeries = new Array();
var thcsChart = echarts.init(document.getElementById('thcsMap'));
var thcsCnChart = echarts.init(document.getElementById('thcsCnMap'));
var hbwzChart = echarts.init(document.getElementById('hbwzMap'));
var hbwzChart3D = echarts.init(document.getElementById('hbwzMap3D'));
// 飞机位置信息
var planeLocationList = new Array();
// 飞机航班信息
var flightList = new Object();
var marquee_itv;
// 机场列表
var airportList = new Object();
let esbpdcacreg = new Object();

let asia = new Array();
let american = new Array();
let eur = new Array();
let ocean = new Array();
let thcsStore = new Array();
// 执行中飞机架数
var exe_total_plane = 0;

var websocket = null;
let thcsoption = {
    color: [],
    geo: {
        map: 'world',
        zoom: 1.2,
        scaleLimit: {
            min: 1,
            max: 3
        },
        roam: false,
        silent: true,
        label: {
            emphasis: {
                show: false
            }
        },
        itemStyle: {
            normal: {
                areaColor: '#134A9C',
                borderColor: '#134A9C'
            },
            emphasis: {
                areaColor: '#0E86CB',
                borderColor: '#0E86CB'
            }
        },
    },
    series: []
};

const thcscnoption = {
    tooltip: {
        show: true,
    },
    geo3D: {
        map: 'china',
        roam: true,
        itemStyle: {
            color: '#0050ae',
            opacity: 1,
            borderWidth: 0.4,
            borderColor: '#2ed8e9'
        },
        emphasis: { //当鼠标放上去  地区区域是否显示名称
            label: {
                show: true,
                textStyle: {
                    color: '#fff',
                    fontSize: 3,
                    backgroundColor: 'rgba(0,23,11,0)'
                }
            }
        },
        light: { //光照阴影
            main: {
                color: '#fff', //光照颜色
                intensity: 1.2, //光照强度
                shadowQuality: 'high', //阴影亮度
                shadow: true, //是否显示阴影
                alpha: 55,
                beta: 10

            },
            ambient: {
                color: '#4880ff',
                intensity: 0.8
            }
        }
    }
};

let hbwzoption = {
    color: [],
    tooltip: {},
    geo: {
        map: 'world',
        roam: true,
        zoom: 1.88,
        center: [60, 25],
        silent: true,
        label: {
            emphasis: {
                show: false
            }
        },
        itemStyle: {
            normal: {
                areaColor: '#3d7dd0',
                borderColor: '#14224c'
            },
            emphasis: {
                areaColor: '#3d7dd0',
                borderColor: '#14224c'
            }
        },
        viewControl: {
            zoomSensitivity: 0,
            targetCoord: [115, 32]
        }
    },
    series: []
};
let hbwzoption3d = {
    tooltip: {
        show: false
    },
    backgroundColor: 'rgba(0,0,0,0)',
    globe: {
        baseTexture: "../asset/earth.jpg",
        displacementScale: 0.1,
        shading: 'realistic',
        realisticMaterial: {
            roughness: 0.9
        },
        postEffect: {
            enable: true
        },
        light: {
            main: {
                intensity: 0.3,
                shadow: true
            },
            ambient: {
                intensity: 1.0
            },
        },
        viewControl: {
            autoRotate: true,
            zoomSensitivity: true,
            targetCoord: [115, 32]
        },
    },
    series: []
};

let hbsstooltip = new Object();
let hbssserise = new Array();
let hxseries = new Array();

class sdc {
    constructor() {
        this._initMap();
        this._initTime();
        this._addEvent();
        this.setTime();
        setInterval(this.setTime(), 1000);
    }

    loadAll() {
        if ($('#loading_msk').length == 0) {
            showLoading();
        }
        var len = companylist.length;
        for (var i = 0; i < len; i++) {
            var dat = companylist[i];
            codelist.push(dat.code);
            if (dat.code != parent_company) {
                codelist_no_parent.push(dat.code);
            }
        }
        this.queryAcNum();
        Promise.all([this.loadAcNum(),this.getRegisterdAircraftStat(), this.loadAllCompanyData(), this.loadAllCompanyData_tq_d(), this.loadAllCompActypeData(),
        this.loadCompNormalRateList(), this.loadGetPdcAcReg(), this.loadCompanyAcUse(), this.loadAllDelayCauseData(),
        this.loadSegmentData(), this.loadPlannum_d(), this.loadPlannum_i(), this.loadThcsData(), this.loadCompBaseArpData(),
        this.loadCompCity_i(), this.loadCompCity_l(), this.loadHbi2Base_i(), this.loadHbi2Base_l(), this.getAirportList(), this.getPlaneLocationMq(), this.getStandardFocFlightInfo(),
        this.loadAcNum(), this.getEsbPdcAcReg(), this.getFocLegsStaticDetails()
            // ,this.loadPLF()
        ]).then((resolve) => {
            this.setLeftData();
            this.setThcsMapData();
            this.setThcsCnMapData();
            this.setPlaneLocation();
            this.setCompDataList();
            this.setCrityList();
            this.setAcNum();
            this.setHxData();
            this.setHszcl();
        }).catch((reject) => {
            console.log(reject);
            alert('数据获取出错！');
            hideLoading();
        });
    }

    loadAllCompanyData() {
        return new Promise((resolve, reject) => {
            var param = {
                'SOLR_CODE': 'FAC_COMP_KPI',
                'COMP_CODE': codelist.join(','),
                'KPI_CODE': kpi_list.join(','),
                'VALUE_TYPE': 'kpi_value_d', //本期
                'DATE_TYPE': 'L,M,Y',
                "OPTIMIZE": 1,
                'LIMIT': query_limit
            };
            $.ajax({
                type: 'post',
                url: "/bi/query/getkpi",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    console.log("all_company_data", response.data);
                    if (response.data != undefined) {
                        all_company_data['kpi_value_d'] = response.data;
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });

        });
    }

    loadAllCompanyData_tq_d() {
        return new Promise((resolve, reject) => {
            var param = {
                'SOLR_CODE': 'FAC_COMP_KPI',
                'COMP_CODE': codelist.join(','),
                'KPI_CODE': kpi_list.join(','),
                'VALUE_TYPE': 'kpi_ratio_tq_d', //同比
                'DATE_TYPE': 'L,M,Y',
                "OPTIMIZE": 1,
                'LIMIT': query_limit
            };
            $.ajax({
                type: 'post',
                url: "/bi/query/getkpi",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    console.log("all_company_data_tq_d", response.data);
                    if (response.data != undefined) {
                        all_company_data['kpi_ratio_tq_d'] = response.data;
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });

        });
    }

    loadAllCompActypeData() {
        return new Promise((resolve, reject) => {
            var param = {
                "SOLR_CODE": "FAC_COMP_ACTYPE_CODE_KPI",
                "COMP_CODE": codelist.join(','),
                "KPI_CODE": kpi_list.join(','),
                "ACTYPE": actype_list.join(','),
                "VALUE_TYPE": "kpi_value_d",
                "DATE_TYPE": 'L',
                'OPTIMIZE': 1,
                "LIMIT": query_limit
            }
            $.ajax({
                type: 'post',
                url: "/bi/query/getaccodekpi",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    console.log("all_comp_actype_data", response.data);
                    if (response.data != undefined) {
                        all_comp_actype_data = response.data;
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    loadActypeId2Code() {
        return new Promise((resolve, reject) => {
            $.ajax({
                type: 'post',
                url: "/bi/web/actypeall",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify({}),
                success: function (response) {
                    if (response.actype) {
                        var list = response.actype;
                        list.sort(function (a, b) {
                            return a.sort - b.sort
                        });
                        var len = list.length;
                        for (var i = 0; i < len; i++) {
                            var obj = list[i];
                            actypeId2Code[obj.id] = obj.code;
                            if (obj.actyptyp == 'Wide' || obj.actyptyp == 'Narrow' || obj.actyptyp == 'Regional') {
                                // 宽体机,窄体机,支线机
                                ac_list_except_other_type.push(obj.code);
                            }
                            if (obj.actyptyp == 'Wide') {
                                ac_type_wide.push(obj.code);
                            }
                            if (obj.actyptyp == 'Narrow') {
                                ac_type_narrow.push(obj.code);
                            }
                            if (obj.actyptyp == 'Regional') {
                                ac_type_regional.push(obj.code);
                            }
                        }
                        resolve(response.errorcode);
                    }

                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    loadCompanyAcUse() {
        return new Promise((resolve, reject) => {
            this.loadActypeId2Code().then((resolve1) => {
                var param = {
                    'SOLR_CODE': 'FAC_COMP_ACTYPE_KPI',
                    'COMP_CODE': codelist.join(','),
                    'KPI_CODE': 'FLY_TIME,AC_NUM', // 飞行时间、飞机架次 == 用来计算飞机日利用率
                    'VALUE_TYPE': 'kpi_value_d', //本期
                    'DATE_TYPE': 'D',
                    'ACTYPE': actypelist,
                    "OPTIMIZE": 1,
                    'LIMIT': 1
                }
                $.ajax({
                    type: 'post',
                    url: "/bi/query/getackpi",
                    contentType: 'application/json',
                    dataType: 'json',
                    async: true,
                    data: JSON.stringify(param),
                    success: function (response) {
                        if (response.data != undefined) {
                            var formatedData = {};
                            // 统计所有机型总和的 FLY_TIME AC_NUM 指标
                            for (var compcode in response.data) {
                                for (var kpicode in response.data[compcode]) {
                                    var aclist = response.data[compcode][kpicode][date_type]['data3'];
                                    if (formatedData[compcode] == undefined) {
                                        formatedData[compcode] = {};
                                    }
                                    if (formatedData[compcode][kpicode] == undefined) {
                                        formatedData[compcode][kpicode] = {};
                                    }
                                    if (formatedData[compcode][kpicode][date_type] == undefined) {
                                        formatedData[compcode][kpicode][date_type] = {};
                                    }
                                    if (all_company_ac_use[compcode] == undefined) {
                                        all_company_ac_use[compcode] = {};
                                    }
                                    if (all_company_ac_use[compcode][kpicode] == undefined) {
                                        all_company_ac_use[compcode][kpicode] = {};
                                    }
                                    if (all_company_ac_use[compcode][kpicode][date_type] == undefined) {
                                        all_company_ac_use[compcode][kpicode][date_type] = {};
                                    }

                                    var len = aclist.length;
                                    for (var i = 0; i < len; i++) {
                                        var acdat = aclist[i];
                                        var acid = acdat.actype;
                                        var ac = actypeId2Code[acid];
                                        // 只统计 宽体机,窄体机,支线机 类型的飞机，其它类型不统计
                                        if (ac_list_except_other_type.indexOf(ac) > -1) {
                                            var ddd = acdat.date;
                                            var len2 = ddd.length;

                                            if (all_company_ac_use[compcode][kpicode][date_type][ac] == undefined) {
                                                all_company_ac_use[compcode][kpicode][date_type][ac] = 0;
                                            }

                                            for (var j = 0; j < len2; j++) {
                                                var obj = ddd[j];
                                                var date = obj.date;
                                                var val = obj.value;
                                                if (formatedData[compcode][kpicode][date_type][date] == undefined) {
                                                    formatedData[compcode][kpicode][date_type][date] = 0;
                                                }
                                                if (!isNaN(val)) {
                                                    formatedData[compcode][kpicode][date_type][date] += Number(val);

                                                    all_company_ac_use[compcode][kpicode][date_type][ac] = val
                                                }
                                            }
                                        }
                                    }

                                }

                            }
                            resolve(response.errorcode);
                        }
                    },
                    error: function (response) {
                        reject(response.errorcode);
                    }
                });
            });
        });

    }

    loadCompBaseArpData() {
        return new Promise((resolve, reject) => {
            var param = {
                'COMP_CODE': parent_company,
            };
            $.ajax({
                type: 'post',
                url: "/bi/web/compBaseArp",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    if (response.data != undefined) {
                        all_company_base_arp_data = response.data[0];
                    }
                    console.log("all_company_base_arp_data", all_company_base_arp_data);
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    loadThcsData() {
        return new Promise((resolve, reject) => {
            var date = new Date();
            var mm = date.getMonth() + 1;
            if (mm < 10) {
                mm = '0' + mm;
            }
            var param = {
                'SOLR_CODE': 'FAC_COMP_KPI',
                'COMP_CODE': parent_company,
                'KPI_CODE': ['INL_CITY_COUNT', 'INT_CITY_COUNT'].join(","),
                'VALUE_TYPE': 'kpi_value_d', //本期
                'DATE_TYPE': 'M',
                'DATE_ID': date.getFullYear() + "" + mm,
                "OPTIMIZE": 1,
                'LIMIT': query_limit
            };
            $.ajax({
                type: 'post',
                url: "/bi/query/getkpi",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    if (response.data != undefined) {
                        all_company_thcs_data['kpi_value_d'] = response.data;
                    }
                    console.log("all_company_thcs_data", all_company_thcs_data);
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    loadSegmentData() {
        return new Promise((resolve, reject) => {
            var date = new Date();
            var mm = date.getMonth() + 1;
            if (mm < 10) {
                mm = '0' + mm;
            }
            var param = {
                'SOLR_CODE': 'FAC_COMP_KPI',
                'COMP_CODE': parent_company,
                'KPI_CODE': ['SEGMENT_COUNT', 'SEGMENT_COUNT_I', 'SEGMENT_COUNT_D'].join(","),
                'VALUE_TYPE': 'kpi_value_d', //本期
                'DATE_TYPE': 'M',
                'DATE_ID': date.getFullYear() + "" + mm,
                "OPTIMIZE": 1,
                'LIMIT': query_limit
            };
            $.ajax({
                type: 'post',
                url: "/bi/query/getkpi",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    if (response.data != undefined) {
                        all_company_segment_data['kpi_value_d'] = response.data;
                    }
                    console.log("all_company_segment_data", all_company_segment_data);
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    loadAllDelayCauseData() {
        return new Promise((resolve, reject) => {
            var param = {
                'SOLR_CODE': 'FAC_COMP_DELAY_CAUSE_RATE_KPI',
                'COMP_CODE': codelist.join(','),
                'KPI_CODE': 'DELAY_NO',
                'VALUE_TYPE': 'kpi_value_d',
                'DATE_TYPE': 'D',
                'LIMIT': 1,
                'OPTIMIZE': 1
            }

            $.ajax({
                type: 'post',
                url: "/bi/query/getkpi",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    console.log("all_delay_cause_data", response.data);
                    if (response.data != undefined) {
                        all_delay_cause_data['kpi_value_d'] = response.data;
                    }
                    resolve(response.errorcode);
                },
                error: function () {
                }
            });

        });
    }

    loadPlannum_d() {
        return new Promise((resolve, reject) => {
            var param = {
                "companyCodes": codelist_no_parent.join(','),
                "stdStartUtcTime": stdStartUtcTime,
                "stdEndUtcTime": stdEndUtcTime,
                "detailType": "pftc", //统计航班（总计）
                "fltType": "L", //L: 国内 Local; I: 国际 International
            };
            $.ajax({
                type: 'post',
                url: "/bi/web/BI2GetPsrSummInfoByFocServlet", //实际调用这个DOC接口 getPsrSummInfoByFoc
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    var lst = response.data;
                    for (var i in lst) {
                        var d = lst[i];
                        if (!isNaN(d.planNum)) {
                            planNum_d += Number(d.planNum);
                        }
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            })
        })
    }

    loadPlannum_i() {
        return new Promise((resolve, reject) => {
            var param = {
                "companyCodes": codelist_no_parent.join(','),
                "stdStartUtcTime": stdStartUtcTime,
                "stdEndUtcTime": stdEndUtcTime,
                "detailType": "pftc", //统计航班（总计）
                "fltType": "I", //L: 国内 Local; I: 国际 International
            };
            $.ajax({
                type: 'post',
                url: "/bi/web/BI2GetPsrSummInfoByFocServlet", //实际调用这个DOC接口 getPsrSummInfoByFoc
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    var lst = response.data;
                    for (var i in lst) {
                        var d = lst[i];
                        if (!isNaN(d.planNum)) {
                            planNum_i += Number(d.planNum);
                        }
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            })
        })
    }

    loadHbi2Base_i() {
        return new Promise((resolve, reject) => {
            var param = {
                "COMP_CODE": parent_company,
                "AIRPORT_TYPE": "I", //L: 国内 Local; I: 国际 International
            };
            $.ajax({
                type: 'post',
                url: "/bi/web/hbi2Base",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    console.log("all_hbi2_base", response.data);
                    if (response.data != undefined) {
                        all_hbi2_base = response.data;
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            })
        })
    }

    loadHbi2Base_l() {
        return new Promise((resolve, reject) => {
            var param = {
                "COMP_CODE": parent_company,
                "AIRPORT_TYPE": "L", //L: 国内 Local; I: 国际 International
            };
            $.ajax({
                type: 'post',
                url: "/bi/web/hbi2Base",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    console.log("all_hbi2_base_l", response.data);
                    if (response.data != undefined) {
                        all_hbi2_base_l = response.data;
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            })
        })
    }

    loadCompCity_i() {
        return new Promise((resolve, reject) => {
            var date = new Date();
            var mm = date.getMonth() + 1;
            if (mm < 10) {
                mm = '0' + mm;
            }
            var param = {
                "COMP_ID": "100",
                "DATE_ID": date.getFullYear() + "" + mm,
                "ARP_TYPE": "I", //L: 国内 Local; I: 国际 International
            };
            $.ajax({
                type: 'post',
                url: "/bi/web/compCity",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    console.log("all_comp_city", response.data);
                    if (response.data != undefined) {
                        all_comp_city = response.data;
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            })
        })
    }

    loadCompCity_l() {
        return new Promise((resolve, reject) => {
            var date = new Date();
            var mm = date.getMonth() + 1;
            if (mm < 10) {
                mm = '0' + mm;
            }
            var param = {
                "COMP_ID": "100",
                "DATE_ID": date.getFullYear() + "" + mm,
                "ARP_TYPE": "L", //L: 国内 Local; I: 国际 International
            };
            $.ajax({
                type: 'post',
                url: "/bi/web/compCity",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    console.log("all_comp_city_l", response.data);
                    if (response.data != undefined) {
                        all_comp_city_l = response.data;
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            })
        })
    }

    loadPLF() {
        return new Promise((res, reject) => {
            var len = companylist.length;
            var promise = new Array();
            for (var i = 0; i < len; i++) {
                var dat = companylist[i];
                if (dat.code != parent_company) {
                    this.getPLF(dat.code, i).then((resolve) => {
                        promise.push(resolve);
                        if (promise.length == len - 1) {
                            res("success");
                            console.log("comp_plf_list", comp_plf_list);
                            console.log("all_company_plannum", all_company_plannum);
                        }
                    }).catch((rej) => {
                        reject("psrsumminfo无数据");
                    });
                }
            }
        });
    }

    getFocLegsStaticDetails() {
        return new Promise((resolve, reject) => {
            var param = {
                "stdStartUtcTime": stdStartUtcTime,
                "stdEndUtcTime": stdEndUtcTime,
                "detailType": 'pfdtc4', // 延误4小时航班
                "company": codelist.join(","),
                "fltType": "L" //国内航班

            };
            $.ajax({
                type: 'post',
                url: "/bi/web/getFocLegsStaticDetails",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    var list = response.data;
                    for (var i = list.length - 1; i >= 0; i--) {
                        fltDelay4List.push(list[i].data);
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    getPLF(compCode, index) {
        return new Promise((resolve, reject) => {
            var planNum = 0; //旅客总量
            var plfLongReg = []; //STD+飞机号
            var sLongReg = []; //飞机号去重
            var plfRegNoSTD = []; //飞机号

            var param = {
                "companyCodes": compCode,
                "stdStart": stdStartUtcTime,
                "stdEnd": stdEndUtcTime
            };
            $.ajax({
                type: 'post',
                url: "/bi/web/psrsumminfo",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    var PLFList = response.data;
                    // console.log("飞机号列表数",PLFList.length);
                    if (PLFList.length > 0) {
                        var plfTemparr = PLFList;

                        ////1. 取所有数组，2.逐条拼装日期部分+AC号，然后map到新数组   3.对2进行去重，等到周期的飞机数

                        //STD+飞机号
                        plfLongReg = plfTemparr.map(v => v.STD + v.LONG_REG);
                        // console.log("STD+飞机号",plfLongReg.length,plfLongReg);

                        //飞机号
                        plfRegNoSTD = plfTemparr.map(v => v.LONG_REG);
                        // console.log("飞机号",plfRegNoSTD.length,plfRegNoSTD);
                        //飞机号去重
                        sLongReg = Array.from(new Set(plfRegNoSTD));
                        // console.log('飞机号去重',sLongReg.length,sLongReg);

                        //找出重复的个数
                        var counts = {};
                        plfRegNoSTD.forEach(function (item) {
                            counts[item] = (counts[item] || 0) + 1;
                        });


                        //统计旅客总量
                        var plfListLen = PLFList.length - 1;
                        for (var i = plfListLen; i >= 0; i--) {
                            planNum += PLFList[i]['PLAN_NUM'] * 1;
                        }
                        all_company_plannum[compCode] = planNum;
                        //取布局
                        this.getACNum(index, resolve, compCode, sLongReg, counts, planNum);

                    } else {
                        reject("psrsumminfo无数据");
                    }
                }.bind(this),
                error: function (response) {
                }
            });
        });
    }

    getACNum(index, resolve, compCode, sLongReg, counts, planNum) {
        var totalACNum = 0; //旅客总量
        var totalNum = 0; //座仓数C+W+Y
        var param = {
            "longNoList": sLongReg.join(',')
        }
        $.ajax({
            type: 'post',
            url: "/bi/web/getAcAircraftList",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {
                var acList = response.data;
                if (acList.length > 0) {
                    acList.forEach((v, i) => {
                        if (v.data.cabin != '' && counts[v.data.longNo] != undefined) {
                            var cNum = 0;
                            var wNum = 0;
                            var yNum = 0;

                            var cStr = v.data.cabin.match(/[C]\d+/g);
                            var wStr = v.data.cabin.match(/[W]\d+/g);
                            var yStr = v.data.cabin.match(/[Y]\d+/g);
                            if (cStr !== null) {
                                cNum = cStr[0].split("C")[1] * 1;
                            }
                            if (wStr !== null) {
                                wNum = wStr[0].split("W")[1] * 1;
                            }
                            if (yStr !== null) {
                                yNum = yStr[0].split("Y")[1] * 1;
                            }
                            //将飞机号对应额W、C、Y舱型相加汇总
                            totalNum = cNum + wNum + yNum;
                            if (totalNum > 0) {
                                totalACNum += totalNum * counts[v.data.longNo];
                            }
                        }
                    });
                    var plfRate = totalACNum == 0 ? 0 : (planNum / totalACNum * 100).toFixed(2);
                    comp_plf_list[compCode] = plfRate;
                    resolve(index);
                } else {
                    comp_plf_list[compCode] = 0;
                    resolve(index);
                }
            },
            error: function (response) {
            }
        });
    }

    loadAcNum() {
        return new Promise((resolve, reject) => {
            var param = {
                'SOLR_CODE': 'FAC_COMP_ACTYPE_KPI',
                'COMP_CODE': codelist.join(","),
                'KPI_CODE': 'AC_NUM,EXE_AC_NUM', // 飞机架次, 执行中飞机架数
                'VALUE_TYPE': 'kpi_value_d', //本期
                'DATE_TYPE': 'D',
                'ACTYPE': 'ALL',
                "OPTIMIZE": 1,
                'LIMIT': 1
            };
            $.ajax({
                type: 'post',
                url: "/bi/query/getackpi",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    if (response.data != undefined) {
                        ac_num = response.data;
                        console.log("ac_num", ac_num);
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            })
        });
    }

    loadCompNormalRateList() {
        return new Promise((resolve, reject) => {
            this.getAllCompSts();
            var len = companylist.length;
            for (var i = 0; i < len; i++) {
                var dat = companylist[i];
                this.getCompSts(dat.code);
            }
            console.log("comp_normal_rate_list", comp_normal_rate_list);
            console.log("comp_normal_rate", comp_normal_rate);
            resolve("success");
        });
    }

    queryAcNum() {
        var url = `/bi/spring/aircraft/getAircrafCnt?company=`;
        $.ajax({
            type: 'get',
            url: url,
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            success: function (response) {
                var data = response.data;
                $("#plane").text(data);
                $("#total_plane_num").text(data);
                $("#total_plane_num_pan").text(data);

            },
            error: function () { }
        });
    }

    loadAcNum() {
        return new Promise((resolve, reject) => {
            var url = `/bi/spring/aircraft/getAircrafCntGroupByCompany`;
            $.ajax({
                type: 'get',
                url: url,
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                success: function (response) {
                    companyAcNum = response.data;

                    resolve(response);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    getRegisterdAircraftStat() {
        return new Promise((resolve, reject) => {
            var url = `/bi/spring/aircraft/getAircrafCntGroupByCompanyActype`;
            $.ajax({
                type: 'get',
                url: url,
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                success: function (response) {
                    companyAcType = response.data;

                    resolve(response);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

 
    loadGetPdcAcReg() {
        return new Promise((resolve, reject) => {
            var param = {
                "acOwners": codelist.join(','),
                "acTypeNotIn": 'D00,D10', //需要过滤掉的机型
            }
            $.ajax({
                type: 'post',
                url: "/bi/web/getPdcAcReg",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    var list = response.data;
                    var acregs = [];
                    var acTypes = [];
                    var reg2actyp = {};
                    for (var i = list.length - 1; i >= 0; i--) {
                        var obj = list[i];
                        // if (obj.acOwner == 'Y8' && ['B5057', 'B2826', 'B2820', 'B1339'].indexOf(obj.longReg) > -1) { //金鹏结果集暂不过滤掉货舱F0的数据，只去除特定的机号
                        //     continue;
                        // }
                        acregs.push(obj.acreg);
                        if (acTypes.indexOf(obj.acType) == -1) {
                            acTypes.push(obj.acType);
                        }
                        reg2actyp[obj.acreg] = obj.acType;
                        if (all_comp_acregs[obj.acOwner] == undefined) {
                            let arr = new Array()
                            arr.push(obj.acreg);
                            all_comp_acregs[obj.acOwner] = arr;
                        } else {
                            let arr = all_comp_acregs[obj.acOwner];
                            arr.push(obj.acreg);
                            all_comp_acregs[obj.acOwner] = arr;
                        }
                    }
                    console.log("all_comp_acregs", all_comp_acregs);
                    // 获取机型转换表
                    var param = {
                        "actypecode": acTypes.join(','),
                        "company": codelist.join(',')
                    };
                    $.ajax({
                        type: 'post',
                        url: "/bi/web/actypmappingquery",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(param),
                        success: function (response) {
                            var actypeMapList = response;
                            for (let a in actypeMapList) {
                                var accode = actypeMapList[a];
                                if (ac_data_list_total[accode] == undefined) {
                                    ac_data_list_total[accode] = 0;
                                }
                                if (ac_type_list.indexOf(accode) == -1) {
                                    ac_type_list.push(accode);
                                }
                            }
                            for (var reg in reg2actyp) {
                                var ac = reg2actyp[reg];
                                var accode = actypeMapList[ac];
                                if (accode) {
                                    ac_data_list_total[accode]++;
                                } else {
                                    console.log(ac, '没有大小机型对照数据');
                                }
                            }
                            // 停场运力
                            // UTC NOW
                            var date = new Date();
                            var mm = date.getUTCMonth() + 1;
                            var dd = date.getUTCDate();
                            var h = date.getUTCHours();
                            var m = date.getUTCMinutes();
                            var s = date.getUTCSeconds();
                            if (mm < 10) {
                                mm = '0' + mm;
                            }
                            if (dd < 10) {
                                dd = '0' + dd;
                            }
                            if (h < 10) {
                                h = '0' + h;
                            }
                            if (m < 10) {
                                m = '0' + m;
                            }
                            if (s < 10) {
                                s = '0' + s;
                            }
                            var utcTimeNow = date.getFullYear() + '-' + mm + '-' + dd + ' ' + h + ':' + m + ':' + s;
                            var param = {
                                "mntUtcStart": stdStartUtcTime,
                                "mntUtcEnd": stdEndUtcTime,
                                "nowInMntUtcRange": utcTimeNow,
                                "acregs": acregs.join(','),
                            };
                            $.ajax({
                                type: 'post',
                                url: "/bi/web/getFocMaintInfoByListByPage",
                                contentType: 'application/json',
                                dataType: 'json',
                                async: true,
                                data: JSON.stringify(param),
                                success: function (response) {
                                    var list2 = response.data;
                                    var acregs2 = [];
                                    for (var i = list2.length - 1; i >= 0; i--) {
                                        var obj = list2[i];
                                        var time1 = parserDate(obj.tStart).getTime() + 8 * 3600 * 1000;
                                        var time2 = parserDate(obj.tEnd).getTime() + 8 * 3600 * 1000;
                                        var date = new Date();
                                        var timenow = date.getTime();

                                        if (acregs2.indexOf(obj.acreg) == -1 && timenow > time1 && timenow < time2 && (obj.distinctcol == 1 || obj.distinctcol == 2)) {
                                            acregs2.push(obj.acreg);
                                            // 对应大机型数量减去维护中的数量
                                            var actyp = reg2actyp[obj.acreg];
                                            var accode = actypeMapList[actyp];
                                            ac_data_list_total[accode]--;
                                            // 大机型对应的停车数量
                                            var bigactyp = obj.acType;
                                            bigactyp = bigactyp.split('-')[0];
                                            bigactyp = bigactyp.split('(')[0];
                                            bigactyp = bigactyp.replace('A', '');
                                            bigactyp = bigactyp.replace('B', '');
                                            bigactyp = bigactyp.replace('C', ''); // C919...
                                            bigactyp = bigactyp.replace('ERJ', '');
                                            if (ac_data_list_total[bigactyp]) {
                                                ac_data_list_total[bigactyp]++;
                                            }
                                        }
                                    }
                                    console.log("ac_data_list_total", ac_data_list_total);
                                    resolve(response.errorcode);
                                },
                                error: function (response) {
                                    reject(response.errorcode);
                                }
                            });
                        },
                        error: function (response) {
                            reject(response.errorcode);
                        }
                    });
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    getAllCompSts() {
        var param = {
            "stdStartUtcTime": stdStartUtcTime,
            "stdEndUtcTime": stdEndUtcTime,
            "companyCodes": codelist_no_parent.join(","),
            "AcTypeList": "",
            "depstns": "",
            "arrstns": ""
        };
        $.ajax({
            type: 'post',
            url: "/bi/redis/7x2_flt_sts",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {
                console.log("all_comp_flt_info", response);
                var pfPercent = Number(response.pfdappPercent); //计划航班中的正常航班比率
                comp_normal_rate[parent_company] = pfPercent;
                all_company_flt['pftc'] = Number(response.pftc);
                all_company_flt['pftci'] = Number(response.pftci);
                all_company_flt['pftcl'] = Number(response.pftcl);
                all_company_flt['fftc'] = Number(response.fftc);
            },
            error: function (response) {
            }
        });
    }

    getCompSts(compcode) {
        var param = {
            "stdStartUtcTime": stdStartUtcTime,
            "stdEndUtcTime": stdEndUtcTime,
            "companyCodes": compcode,
            "AcTypeList": "",
            "depstns": "",
            "arrstns": ""
        };
        $.ajax({
            type: 'post',
            url: "/bi/redis/7x2_flt_sts",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {
                var pfPercent = Number(response.pfdappPercent); //计划航班中的正常航班比率
                var pftc = Number(response.pftc);
                comp_normal_rate_list[compcode] = pfPercent;
                comp_pftc_list[compcode] = pftc;

            },
            error: function (response) {
            }
        });
    }

    getStandardFocFlightInfo() {
        return new Promise((resolve, reject) => {
            var param = {
                "stdStart": stdStart,
                "stdEnd": stdEnd,
                "acOwner": '',
                "statusList": 'DEP', // 只返回起飞的，表示在空中
            }
            $.ajax({
                type: 'post',
                url: "/bi/web/getStandardFocFlightInfo",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    var list = response.data;
                    for (var i = list.length - 1; i >= 0; i--) {
                        var obj = list[i];
                        flightList[obj.flightNo] = obj;
                        // 国际航班
                        if (obj.fltType == 'I') {
                            fltIntList.push(obj);
                        }
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        })
    }

    getAirportList() {
        return new Promise((resolve, reject) => {
            var param = {
                //"AIRPORT_CODE": '', // 可选，传入机场CODE
            }
            $.ajax({
                type: 'post',
                url: "/bi/web/airportdetail",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    var list = response.airport;
                    for (var i = list.length - 1; i >= 0; i--) {
                        var arp = list[i];
                        airportList[arp.code] = arp;
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    getEsbPdcAcReg() {
        return new Promise((resolve, reject) => {
            return resolve([]);
            var param = {
                "COMP_CODE": codelist_no_parent.join(",")
            };
            $.ajax({
                type: 'post',
                url: "/bi/web/esbPdcAcReg",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    var list = response.data;
                    for (var i = list.length - 1; i >= 0; i--) {
                        var arp = list[i];
                        if (esbpdcacreg[arp.ac_owner] == undefined) {
                            esbpdcacreg[arp.ac_owner] = {};
                        }
                        if (esbpdcacreg[arp.ac_owner][arp.stas_code] == undefined) {
                            esbpdcacreg[arp.ac_owner][arp.stas_code] = {};
                        }
                        esbpdcacreg[arp.ac_owner][arp.stas_code] = arp.count;

                    }
                    console.log("esbpdcacreg", esbpdcacreg);
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    getPlaneLocationMq() {
        return new Promise((resolve, reject) => {
            var param = {
                'mode': 'pos'
            };
            $.ajax({
                type: 'post',
                url: "/bi/web/flightMq",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    var plist = {};

                    function processData(data1) {
                        var lst = {};
                        var len = data1.length;
                        for (var i = 0; i < len; i++) {
                            var dd = data1[i];
                            var fi = dd.fi;
                            if (lst[fi] == undefined) {
                                lst[fi] = {
                                    data: []
                                };
                                lst[fi]['data'].push(dd);
                            } else {
                                lst[fi]['data'].push(dd);
                            }
                        }
                        return lst;
                    }

                    var list = processData(response.data.data1);

                    console.log('processData', list);

                    for (var fltno in list) {

                        var fltobj = list[fltno];
                        var itmx2 = fltobj.data;

                        var itm;

                        if (itmx2 && itmx2.length > 1) {
                            var itm1 = itmx2[0];
                            var itm2 = itmx2[1];


                            itm1.UTC = itm1.UTC.replace(' ', '');
                            itm2.UTC = itm2.UTC.replace(' ', '');

                            if (itm1.UTC > itm2.UTC) {
                                itm = itm1
                                itm.LON1 = itm2.LON;
                                itm.LAT1 = itm2.LAT;
                            } else if (itm1.UTC < itm2.UTC) {
                                itm = itm2
                                itm.LON1 = itm1.LON;
                                itm.LAT1 = itm1.LAT;
                            } else {
                                itm = itm2
                                itm.LON1 = itm1.LON;
                                itm.LAT1 = itm1.LAT;
                            }
                        } else if (itmx2 && itmx2.length > 0) {
                            itm = itmx2[0];

                        }
                        if (itm) {
                            var alt = itm.ALT;
                            var cas = itm.CAS;
                            let vec;
                            var fltno = itm.fi;
                            if (1) {
                                var acno = itm.an;
                                acno = acno.replace('-', '');
                                var lon = formatLonLat(itm.LON);
                                var lon1 = formatLonLat(itm.LON1);
                                var lat = formatLonLat(itm.LAT);
                                var lat1 = formatLonLat(itm.LAT1);
                                if (isNaN(itm.LON)) {
                                    vec = Number(itm.VEC);
                                }
                                var oil = isNaN(itm.OIL) ? '' : itm.OIL;
                                var pdat = {
                                    fltno: fltno,
                                    acno: acno,
                                    alt: alt,
                                    vec: vec,
                                    lon: lon,
                                    lat: lat,
                                    lon1: lon1,
                                    lat1: lat1,
                                    oil: oil,
                                };
                                var code = acno + '-' + fltno;

                                if (pdat.vec == undefined) {
                                    pdat.vec = getGeoAngle(pdat.lat, pdat.lon, pdat.lat1, pdat.lon1);
                                }
                                planeLocationList.push(pdat);
                            }
                        }
                    }
                    console.log('planeLocationList', planeLocationList);
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    setPlaneLocation() {
        var planes = planeLocationList;

        var series = [];
        var list_1 = []; // 正常航班
        var list_2 = []; // 延误航班
        var len = planes.length;
        for (var i = 0; i < len; i++) {
            var dat = planes[i];

            var flt = flightList[dat.fltno];

            if (flt) {
                if (flt.status == 'DEP' && flt.delay1 == '') { // 正常在飞
                    list_1.push({
                        name: dat.fltno,
                        acno: dat.acno,
                        oil: dat.oil,
                        vec: dat.vec,
                        flt: flt,
                        value: [dat.lon, dat.lat],
                        symbolRotate: -dat.vec + 90, //航向   正北是0°，顺时针，0到360° // 因为图片机头指向右，所以需要逆时针转90度
                    });

                } else if (flt.status == 'DEP' && flt.delay1 != '') {  // 延误航班
                    list_2.push({
                        name: dat.fltno,
                        acno: dat.acno,
                        oil: dat.oil,
                        vec: dat.vec,
                        flt: flt,
                        value: [dat.lon, dat.lat],
                        symbolRotate: -dat.vec + 90, //航向   正北是0°，顺时针，0到360° // 因为图片机头指向右，所以需要逆时针转90度
                    });

                }
            }

        }
        series.push({
            name: 'normal',
            type: 'scatter',
            coordinateSystem: 'geo',
            symbol: 'image://./img/a3.3.legend_1.png',
            symbolSize: 15,
            label: {
                normal: {
                    show: false,
                }
            },
            data: list_1
        });
        hbssserise.push({
            name: 'normal',
            type: 'scatter3D',
            coordinateSystem: 'globe',
            symbol: 'image://./img/a3.3.legend_2.png',
            symbolSize: 15,
            itemStyle: {
                color: '#00ed00'
            },
            label: {
                show: false
            },
            data: list_1
        }
        );

        series.push({
            name: 'delay',
            type: 'scatter',
            coordinateSystem: 'geo',
            symbol: 'image://./img/a3.3.legend_2.png',
            symbolSize: 15,
            label: {
                normal: {
                    show: false,
                }
            },
            data: list_2
        });

        hbssserise.push({
            name: 'delay',
            type: 'scatter3D',
            coordinateSystem: 'globe',
            symbol: 'image://./img/a3.3.legend_2.png',
            symbolSize: 15,
            itemStyle: {
                color: '#f6b100'
            },
            label: {
                show: false
            },
            data: list_2
        });

        hbsstooltip = {
            trigger: 'item',
            show: true,
            formatter: function (params, ticket, callback) {
                var data = params.data;

                var html = '<div style="padding: 5px;">';

                var flt = data.flt;
                var depAirport = airportList[flt.depStn]; //出发机场信息
                var arrAirport = airportList[flt.arrStn]; //到达机场信息

                //航班号、起飞城市-到达城市、航班状态（实际/预计/计划起飞时间-实际/预计/计划到达时间）、机号（机型）、客座率（布局）、实时位置、实施油量
                html += '航班号: ' + data.name + '<br>';
                html += flt.depCity + ' - ' + flt.arrCity + '<br>';
                if (flt.status == 'DEP' || flt.status == 'ARR' || flt.status == 'NDR' || flt.status == 'ATA') {
                    // 起飞,落地,到达
                    // 实际起飞时间 atdChn
                    html += '实际起飞时间: ' + this._trimTime(flt.atdChn) + '<br>';
                } else {
                    // 预计出发
                    html += '预计出发时间: ' + this._trimTime(flt.etdChn) + '<br>';
                }

                if (flt.status == 'ATA') {
                    // 到达
                    // 实际起飞时间 atdChn
                    html += '实际到达时间: ' + this._trimTime(flt.ataChn) + '<br>';
                } else {
                    // 预计到达
                    html += '预计到达时间: ' + this._trimTime(flt.etaChn) + '<br>';
                }

                if (flt.delay1 != '' && flt.dur1 > 0) {
                    html += '延误原因: ' + flt.delay1Name + '<br>';
                    html += '延误时间: ' + (Number(flt.dur1) + Number(flt.dur2) + Number(flt.dur3) + Number(flt.dur4)) + '分钟<br>';
                }

                html += '机号: ' + data.acno + '(' + flt.acType + ')' + '<br>';
                html += '实时位置: ' + Math.round(data.value[0] * 10000) / 10000 + ', ' + Math.round(data.value[1] * 10000) / 10000 + '<br>';

                html += '</div>';

                return html;
            }.bind(this),
            backgroundColor: '#021e55',
        };
        let options = hbwzChart.getOption();
        options.series = series;
        options.tooltip = hbsstooltip;
        hbwzChart.setOption(options, true);
    }

    setThcsCnMapData() {
        // let cityData = all_comp_city_l.map((v, i) => {
        //     return {
        //         name: v.CITY_NAME,
        //         value: [v.AIRPORT_LONGITUDE, v.AIRPORT_LATITUDE, 10],
        //         symbol: 'pin',
        //         symbolSize: [28, 30],
        //         itemStyle: {
        //             color: 'green'
        //         }
        //     }
        // });
        let html = ``;
        let baseData = all_hbi2_base_l.map((v, i) => {
            html += `<div>${v.CITY_NAME}</div>`;
            return {
                name: v.CITY_NAME,
                value: [v.AIRPORT_LONGITUDE, v.AIRPORT_LATITUDE, 10],
                symbol: 'pin',
                symbolSize: [28, 30],
                itemStyle: {
                    color: 'blue'
                }
            }
        });
        $("#gnjdlist").html(html);
        let thcsSeries = new Array();
        // thcsSeries.push({
        //     name: 'gnthcs',
        //     type: "scatter3D",
        //     coordinateSystem: 'geo3D',
        //     zlevel: 3,
        //     tooltip: {
        //         show: false
        //     },
        //     label: {
        //         show: true,
        //         formatter: '{b}'
        //     },
        //     data: cityData
        // });
        thcsSeries.push({
            name: 'gnjd',
            type: "scatter3D",
            coordinateSystem: 'geo3D',
            zlevel: 3,
            tooltip: {
                show: false
            },
            label: {
                show: true,
                formatter: '{b}'
            },
            data: baseData
        });
        let options = thcsCnChart.getOption();
        options.series = thcsSeries;
        thcsCnChart.setOption(options, true);
    }

    setThcsMapData() {
        let cityData = all_comp_city.map((v, i) => {
            return {
                "name": v.CITY_NAME,
                "value": [v.AIRPORT_LONGITUDE, v.AIRPORT_LATITUDE]
            }
        });
        let baseData = all_hbi2_base.map((v, i) => {
            return {
                "name": v.CITY_NAME,
                "value": [v.AIRPORT_LONGITUDE, v.AIRPORT_LATITUDE]
            }
        });
        thcsSeries.push({
            name: 'gjthcs',
            type: 'scatter',
            coordinateSystem: 'geo',
            symbol: 'image://./img/symbol_b.png',
            symbolSize: 32,
            symbolOffset: [0, '-30%'],
            label: {
                normal: {
                    formatter: '{b}',
                    position: 'right',
                    show: false
                },
                emphasis: {
                    show: true
                }
            },
            itemStyle: {
                normal: {
                    color: '#FFFFFF',
                    shadowBlur: 10,
                    shadowColor: '#333'
                }
            },
            data: cityData,
        });
        thcsSeries.push({
            name: 'gjthcs',
            type: 'scatter',
            coordinateSystem: 'geo',
            symbol: 'image://./img/symbol_g.png',
            symbolSize: 32,
            label: {
                normal: {
                    formatter: '{b}',
                    position: 'right',
                    show: false
                },
                emphasis: {
                    show: true
                }
            },
            itemStyle: {
                normal: {
                    color: '#FFFFFF',
                    shadowBlur: 10,
                    shadowColor: '#333'
                }
            },
            data: baseData,
        });
        thcsStore = thcsSeries;
        let options = thcsChart.getOption();
        options.series = thcsSeries;
        thcsChart.setOption(options, true);
    }

    setLeftData() {
        //飞机
        let planeTotal = 0;
        for (let acreg in all_comp_acregs) {
            planeTotal += all_comp_acregs[acreg].length;
        }
        // $("#plane").text(planeTotal);
        // $("#total_plane_num").text(planeTotal);
        // $("#total_plane_num_pan").text(planeTotal);
        //正常率
        $("#ztzcl").text(comp_normal_rate[parent_company].toFixed(2));
        //延误原因
        var comp_total = 0;
        var none_total = 0;
        var date_type = 'D';
        var len = companylist.length;
        for (var i = 0; i < len; i++) {
            var dat = companylist[i];
            var compcode = dat.code;
            if (compcode != parent_company && all_delay_cause_data['kpi_value_d'][compcode]) {
                var data_dly = all_delay_cause_data['kpi_value_d'][compcode]['DELAY_NO'][date_type];
                // 公司原因 总数
                for (var time in data_dly) {
                    var d = data_dly[time];
                    var len2 = comp_cause.length;
                    for (var j = 0; j < len2; j++) {
                        var causeName = comp_cause[j];
                        if (!isNaN(d[causeName])) {
                            var val = Number(d[causeName]);
                            comp_total += val;
                        }
                    }
                }
                // 非公司原因 总数
                for (var time in data_dly) {
                    var d = data_dly[time];
                    var len2 = none_cause.length;
                    for (var j = 0; j < len2; j++) {
                        var causeName = none_cause[j];
                        if (!isNaN(d[causeName])) {
                            var val = Number(d[causeName]);
                            if (causeName == "民航局航班时刻安排") {
                                causeName = "时刻安排"
                            }
                            none_total += val;
                        }
                    }
                }
            }
        }
        var rate_comp = (comp_total + none_total) > 0 ? comp_total / (comp_total + none_total) : 0;
        var rate_none = (comp_total + none_total) > 0 ? none_total / (comp_total + none_total) : 0;
        $('#comp_case').text(Math.round(rate_comp * 100));
        $('#no_comp_case').text(Math.round(rate_none * 100));

        //航线
        var date = new Date();
        var mm_1 = date.getMonth() + 1;
        if (mm_1 < 10) {
            mm_1 = '0' + mm_1;
        }
        $('#hx_count').text(all_company_segment_data['kpi_value_d'][parent_company]['SEGMENT_COUNT']['M'] ? Number(
            all_company_segment_data['kpi_value_d'][parent_company]['SEGMENT_COUNT']['M'][date.getFullYear() + "" + mm_1]) :
            "0");
        $('#hx_count_d').text(all_company_segment_data['kpi_value_d'][parent_company]['SEGMENT_COUNT_D']['M'] ? Number(
            all_company_segment_data['kpi_value_d'][parent_company]['SEGMENT_COUNT_D']['M'][date.getFullYear() + "" + mm_1]) :
            "0");
        $('#hx_count_i').text(all_company_segment_data['kpi_value_d'][parent_company]['SEGMENT_COUNT_I']['M'] ? Number(
            all_company_segment_data['kpi_value_d'][parent_company]['SEGMENT_COUNT_I']['M'][date.getFullYear() + "" + mm_1]) :
            "0");

        //航班
        $('#hb_count').text(all_company_flt['pftc']);
        $('#hb_count_d').text(all_company_flt['pftcl']);
        $('#hb_count_i').text(all_company_flt['pftci']);
        $('#hb_count_d_plannum').text(planNum_d);
        $('#hb_count_i_plannum').text(planNum_i);

        var mm_1 = date.getMonth() + 1;
        if (mm_1 < 10) {
            mm_1 = '0' + mm_1;
        }
        //通航城市
        $('#city_inl').text(all_company_thcs_data['kpi_value_d'][parent_company]['INL_CITY_COUNT']['M'] ? Number(
            all_company_thcs_data['kpi_value_d'][parent_company]['INL_CITY_COUNT']['M'][date.getFullYear() + "" + mm_1]) : "0");
        $('#city_int').text(all_company_thcs_data['kpi_value_d'][parent_company]['INT_CITY_COUNT']['M'] ? Number(
            all_company_thcs_data['kpi_value_d'][parent_company]['INT_CITY_COUNT']['M'][date.getFullYear() + "" + mm_1]) : "0");
        $('#int_base_arp_count').text(all_company_base_arp_data['INT_BASE_ARP_COUNT']);
        $('#inl_base_arp_count').text(all_company_base_arp_data['INL_BASE_ARP_COUNT']);

        //整体正常率详情
        var date = new Date();
        var mm = date.getMonth() + 1;
        if (mm < 10) {
            mm = '0' + mm;
        }
        let _d = all_company_data['kpi_value_d']['HNAHK']['NORMAL_RATE']['L'] ? Object.values(all_company_data['kpi_value_d']['HNAHK']['NORMAL_RATE']['L']) : ['--'];
        let _tq = all_company_data['kpi_ratio_tq_d']['HNAHK']['NORMAL_RATE']['L'] ? Object.values(all_company_data['kpi_ratio_tq_d']['HNAHK']['NORMAL_RATE']['L']) : ['--'];
        $("#nomaral_rate_l").text(all_company_data['kpi_value_d']['HNAHK']['NORMAL_RATE']['L'] ? (Number(_d[0]) * 100).toFixed(2) + "%" : "--");
        $("#nomaral_rate_l_tq").text(all_company_data['kpi_ratio_tq_d']['HNAHK']['NORMAL_RATE']['L'] ? (Number(_tq[0]) * 100).toFixed(2) + "%" : "--");
        $("#nomaral_rate_m").text(all_company_data['kpi_value_d']['HNAHK']['NORMAL_RATE']['M'][date.getFullYear() + "" + mm] ? (Number(all_company_data['kpi_value_d']['HNAHK']['NORMAL_RATE']['M'][date.getFullYear() + "" + mm]) * 100).toFixed(2) + "%" : "--");
        $("#nomaral_rate_m_tq").text(all_company_data['kpi_ratio_tq_d']['HNAHK']['NORMAL_RATE']['M'][date.getFullYear() + "" + mm] ? (Number(all_company_data['kpi_ratio_tq_d']['HNAHK']['NORMAL_RATE']['M'][date.getFullYear() + "" + mm]) * 100).toFixed(2) + "%" : "--");
        $("#nomaral_rate_y").text(all_company_data['kpi_value_d']['HNAHK']['NORMAL_RATE']['Y'][date.getFullYear()] ? (Number(all_company_data['kpi_value_d']['HNAHK']['NORMAL_RATE']['Y'][date.getFullYear()]) * 100).toFixed(2) + "%" : "--");
        $("#nomaral_rate_y_tq").text(all_company_data['kpi_ratio_tq_d']['HNAHK']['NORMAL_RATE']['Y'][date.getFullYear()] ? (Number(all_company_data['kpi_ratio_tq_d']['HNAHK']['NORMAL_RATE']['Y'][date.getFullYear()]) * 100).toFixed(2) + "%" : "--");

        //航班量统计详情
        let sch_no_d = all_company_data['kpi_value_d']['HNAHK']['SCH_NO']['L'] ? Object.values(all_company_data['kpi_value_d']['HNAHK']['SCH_NO']['L']) : ['--'];
        let book_num_d = all_company_data['kpi_value_d']['HNAHK']['BOOK_NUM']['L'] ? Object.values(all_company_data['kpi_value_d']['HNAHK']['BOOK_NUM']['L']) : ['--'];
        let sch_no_tq = all_company_data['kpi_ratio_tq_d']['HNAHK']['SCH_NO']['L'] ? Object.values(all_company_data['kpi_ratio_tq_d']['HNAHK']['SCH_NO']['L']) : ['--'];
        $("#SCH_NO_L").text(all_company_data['kpi_value_d']['HNAHK']['SCH_NO']['L'] ? (Number(sch_no_d[0])).toFixed(0) : "--");
        $("#BOOK_NUM_L").text(all_company_data['kpi_value_d']['HNAHK']['BOOK_NUM']['L'] ? ((Number(book_num_d[0])) / 10000).toFixed(0) + "万" : "--");
        $("#SCH_NO_TQ_L").text(all_company_data['kpi_ratio_tq_d']['HNAHK']['SCH_NO']['L'] ? (Number(sch_no_tq[0]) * 100).toFixed(2) + "%" : "--");
        $("#SCH_NO_M").text(all_company_data['kpi_value_d']['HNAHK']['SCH_NO']['M'][date.getFullYear() + "" + mm] ? (Number(all_company_data['kpi_value_d']['HNAHK']['SCH_NO']['M'][date.getFullYear() + "" + mm])).toFixed(0) : "--");
        $("#BOOK_NUM_M").text(all_company_data['kpi_value_d']['HNAHK']['BOOK_NUM']['M'][date.getFullYear() + "" + mm] ? ((Number(all_company_data['kpi_value_d']['HNAHK']['BOOK_NUM']['M'][date.getFullYear() + "" + mm])) / 10000).toFixed(0) + "万" : "--");
        $("#SCH_NO_TQ_M").text(all_company_data['kpi_ratio_tq_d']['HNAHK']['SCH_NO']['M'][date.getFullYear() + "" + mm] ? (Number(all_company_data['kpi_ratio_tq_d']['HNAHK']['SCH_NO']['M'][date.getFullYear() + "" + mm]) * 100).toFixed(2) + "%" : "--");
        $("#SCH_NO_Y").text(all_company_data['kpi_value_d']['HNAHK']['SCH_NO']['Y'][date.getFullYear()] ? (Number(all_company_data['kpi_value_d']['HNAHK']['SCH_NO']['Y'][date.getFullYear()])).toFixed(0) : "--");
        $("#BOOK_NUM_Y").text(all_company_data['kpi_value_d']['HNAHK']['BOOK_NUM']['Y'][date.getFullYear()] ? ((Number(all_company_data['kpi_value_d']['HNAHK']['BOOK_NUM']['Y'][date.getFullYear()])) / 10000).toFixed(0) + "万" : "--");
        $("#SCH_NO_TQ_Y").text(all_company_data['kpi_ratio_tq_d']['HNAHK']['SCH_NO']['Y'][date.getFullYear()] ? (Number(all_company_data['kpi_ratio_tq_d']['HNAHK']['SCH_NO']['Y'][date.getFullYear()]) * 100).toFixed(2) + "%" : "--");
    }

    setCompDataList() {
        var html = '';
        var len = companylist.length;
        for (var i = 0; i < len; i++) {
            var dat = companylist[i];
            var compcode = dat.code;
            if (compcode != parent_company) {
                var fuel;
                var fuel_arr;

                if (compcode == "CN") {
                    fuel = this.getAcUseFueltOt(100100, 'B737');
                } else if (compcode == "HX") {
                    fuel = this.getFuelt(["HU", "JD", "GS", "8L", "PN", "FU", "GX", "UQ", "Y8", "9H"]);
                    if (fuel != 0) {
                        fuel = (fuel / 11).toFixed(4);
                    }
                } else {
                    fuel = this.getFuelt([compcode]);
                }

                if (fuel < 0) {
                    fuel_arr = "<span class='green'>↓</span>"
                } else if (fuel > 0) {
                    fuel_arr = "<span class='red'>↑</span>"
                } else {
                    fuel_arr = "";
                }
                var rate = comp_normal_rate_list[compcode];
                rate = Math.round(rate * 100) / 100;

                let len = companyAcNum[compcode];
                if (len === undefined) {
                    len = ''
                }
                // html += "<div class='rw' id='complist_id" + i + "' data-code='" + compcode + "' data-id='" + i + "'  data-rate='" +
                //     rate + "' >";
                // html += "<span class='c flex_none c1 center'>" + companyCode2Name[compcode] + "</span>";
                // html += "<span class='c flex_none c2 center'>" + compcode + "</span>";
                // html += "<span class='c flex_none c3 center'>" + len + "</span>";
                // html += "<span class='c flex_none c4 center'>" + comp_pftc_list[compcode] + "</span>";
                // html += "<span class='c flex_none c5 center'>" + rate + "%</span>";
                // // html += "<span class='c flex_none c6 center'>" + comp_plf_list[compcode] + "</span>";//上座率
                // html += "<span class='c flex_none c7 center '>" + fuel + fuel_arr + "</span>";
                // html += "</div>";
                html += "<div class='rw' id='complist_id" + i + "' data-code='" + compcode + "' data-id='" + i + "'  data-rate='" + rate + "' >";
                html += "<div class='c flex_none c1 center'>" + companyCode2Name[compcode] + "</div>";
                html += "<div class='c flex_none c2 center'>" + compcode + "</div>";
                html += "<div class='c flex_none c3 center'>" + len + "</div>";
                html += "<div class='c flex_none c4 center'>" + comp_pftc_list[compcode] + "</div>";
                html += "<div class='c flex_none c5 center'>" + rate + "%</div>";
                //html += "<div class='c flex_none c6 center'>" + comp_plf_list[compcode] + "%</div>";;//上座率
                html += "<div class='c flex_none c7 center'>" + fuel + fuel_arr + "</span></div>";
                html += "</div>";
            }
        }

        $('#comp_table .list').html(html);
        $('#comp_table .list .rw').off('click');
        $('#comp_table .list .rw').on('click', (event) => {
            var id = $(event.currentTarget).attr('data-id');
            this.selectComp(Number(id));
        });

        this.selectComp(1);
        hideLoading();
    }

    setCrityList() {
        let obj = {}
        all_comp_city.forEach(item => {
            let arr = obj[item.CONTINENT_NAME];
            if (arr == undefined) {
                arr = new Array();
                arr.push(item.CITY_NAME);
            } else if (arr.indexOf(item.CITY_NAME) == -1) {
                arr.push(item.CITY_NAME);
            }

            switch (item.CONTINENT_NAME) {
                case '亚洲':
                    asia.push({
                        name: item.CITY_NAME,
                        value: [item.AIRPORT_LONGITUDE, item.AIRPORT_LATITUDE]
                    });
                    break;
                case '北美洲':
                    american.push({
                        name: item.CITY_NAME,
                        value: [item.AIRPORT_LONGITUDE, item.AIRPORT_LATITUDE]
                    });
                    break;
                case '欧洲':
                    eur.push({
                        name: item.CITY_NAME,
                        value: [item.AIRPORT_LONGITUDE, item.AIRPORT_LATITUDE]
                    });
                    break;
                case '大洋洲':
                    ocean.push({
                        name: item.CITY_NAME,
                        value: [item.AIRPORT_LONGITUDE, item.AIRPORT_LATITUDE]
                    });
                    break;
            }

            obj[item.CONTINENT_NAME] = arr;
        })

        for (var o in obj) {
            if (o != "") {
                if (obj[o].length > 0) {

                    switch (o) {
                        case '亚洲':
                            $("#asiaNum").text(obj[o].length);
                            break;
                        case '北美洲':
                            $("#americanNum").text(obj[o].length);
                            break;
                        case '欧洲':
                            $("#eurNum").text(obj[o].length);
                            break;
                        case '大洋洲':
                            $("#oceanNum").text(obj[o].length);
                            break;
                    }

                }
            }
        }

        $(".infoBox").on("click", function (e) {
            if ($('#thcsMap').is(':hidden')) {
                $('#thcsMap').show();
                $('#hbwzMap').hide();
                $('#hbwzMap3D').hide();
                $('.thcsLegend').show();
                $('.hbwzLegend').hide();
                $('.hxLegend').hide();
                $('.hszclLegend').hide();

                $('.midNav li').removeClass('liselected');
                $('.midNav li[mid=1]').addClass('liselected');


            }

            thcsChart.clear();
            thcsChart.setOption(thcsoption);

            if ($(this).hasClass("selected")) {
                thcsoption.series = thcsStore;
                $(this).removeClass("selected");
            } else {
                $(this).siblings(".infoBox").removeClass("selected");
                $(this).addClass("selected");
                let thcs = {
                    name: 'gjthcs',
                    type: 'scatter',
                    coordinateSystem: 'geo',
                    symbol: 'image://./img/symbol_b.png',
                    symbolSize: 32,
                    symbolOffset: [0, '-30%'],
                    label: {
                        normal: {
                            formatter: '{b}',
                            position: 'right',
                            show: false
                        },
                        emphasis: {
                            show: true
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: '#FFFFFF',
                            shadowBlur: 10,
                            shadowColor: '#333'
                        }
                    },
                    data: [],
                };
                switch ($(this).find(".blue1").text().trim()) {
                    case '亚洲':
                        thcs.data = asia;
                        break;
                    case '北美洲':
                        thcs.data = american;
                        break;
                    case '欧洲':
                        thcs.data = eur;
                        break;
                    case '大洋洲':
                        thcs.data = ocean;
                        break;
                }
                thcsoption.series = thcs;
            }
            thcsChart.setOption(thcsoption, true);
        });


        // -----------------------------------------
        //  详情弹窗控制
        // -----------------------------------------

        $('.content_btn').on('click', function (evt) {
            evt.stopPropagation();
            this._popDetail($(evt.currentTarget));
        }.bind(this));

        $('.moreClose').click(function (event) {
            event.stopPropagation();
            this._closeDetail($(event.currentTarget));
        }.bind(this))
    }

    selectComp(id) {
        $('#comp_table .list .rw').removeClass('selected');
        $('#complist_id' + id).addClass('selected');

        var compcode = $('#complist_id' + id).attr('data-code');
        var rate = $('#complist_id' + id).attr('data-rate');

        $('.complogo').attr('src', '../img/logo_' + compcode + '.png');
        $('.compname').text(companyCode2Name[compcode]);

        if (compcode == "HU") {
            $('.compname').addClass("href");
        } else {
            $('.compname').removeClass("href");
        }

        var compid;
        var len = companylist.length;
        for (var i = 0; i < len; i++) {
            var dat = companylist[i];
            if (compcode == dat.code) {
                compid = dat.id;
            }
        }

        var html = '';
        var dddd1;
        var dddd2;
        var fuelacs;
        // 大新华 日利用率、燃效用股份的
        if ("CN" == compcode) {
            dddd1 = all_company_ac_use["HU"]['FLY_TIME'][date_type];
            dddd2 = all_company_ac_use["HU"]['AC_NUM'][date_type];
        } else {
            dddd1 = all_company_ac_use[compcode]['FLY_TIME'][date_type];
            dddd2 = all_company_ac_use[compcode]['AC_NUM'][date_type];
        }




        for (var accode in dddd1) {
            var ftm = dddd1[accode];
            var acnum = companyAcType[compcode +"_"+accode];
            var acnum1 = dddd2[accode];
            var useh = acnum1 > 0 ? Math.round(ftm / acnum1 * 10) / 10 : '-';
            // // 大新华只取股份737的数据
            // if ("CN" == compcode) {
            //     if ("737" != accode) {
            //         continue;
            //     }
            //     acnum = 3; // 大新华737机型架数固定3
            // }
            acnum = acnum ? Math.round(acnum) : "-";
            if(acnum>0){
                let pic = ['737', '787', '330', '350'].indexOf(accode) > -1 ? accode + '.png' : 'plane_pic.png';
                html +=
                    `<li class="planeBox">
                        <div class="plane_modle ffnum fs18 blue1">
                            ${accode}
                        </div>
                        <div class="plane_pic" style="background: url(img/${pic}?0) no-repeat center;"></div>
                        <div class="plane_text1 fs14 blue1">数量</div>
                        <div class="plane_num ffnum fs22">${acnum}<span class="fs14">架</span>
                        </div>
                        <div class="plane_text2 fs14 blue1">日利用率</div>
                        <div class="plane_rate ffnum fs22">${useh}<span class="fs14">小时</span>
                        </div>
                       </li>`;
            }

            


        }
        $('#comp_ac_table').html(html);

    }

    setTime() {
        var date = new Date();
        var timestamp = date.getTime();
        var timezoneOffset = date.getTimezoneOffset();
        var utc_timestamp = timestamp + timezoneOffset * 60 * 1000;
        let beijing_date = new Date();
        beijing_date.setTime(timestamp);

        var utc_date = new Date();
        utc_date.setTime(utc_timestamp + 2 * 3600 * 1000);

        var sydney_date = new Date();
        sydney_date.setTime(utc_timestamp + 10 * 3600 * 1000);

        var newyork_date = new Date();
        newyork_date.setTime(utc_timestamp - 4 * 3600 * 1000);

        let russia_date = new Date();
        russia_date.setTime(timestamp - 5 * 3600 * 1000);

        $('#time_beijing').text(this._formatNum(beijing_date.getHours()) + ':' + this._formatNum(beijing_date.getMinutes()));
        $('#time_london').text(this._formatNum(utc_date.getHours()) + ':' + this._formatNum(utc_date.getMinutes()));
        $('#time_sydney').text(this._formatNum(sydney_date.getHours()) + ':' + this._formatNum(sydney_date.getMinutes()));
        $('#time_newyork').text(this._formatNum(newyork_date.getHours()) + ':' + this._formatNum(newyork_date.getMinutes()));
        $('#time_russia').text(this._formatNum(russia_date.getHours()) + ':' + this._formatNum(russia_date.getMinutes()));

        $('#date_beijing').text('北京' + date.getDate() + ' ' + this._getEngMonth(date.getMonth()));
        $('#date_london').text('伦敦' + utc_date.getDate() + ' ' + this._getEngMonth(utc_date.getMonth()));
        $('#date_sydney').text('悉尼' + sydney_date.getDate() + ' ' + this._getEngMonth(sydney_date.getMonth()));
        $('#date_newyork').text('纽约' + newyork_date.getDate() + ' ' + this._getEngMonth(newyork_date.getMonth()));
        $('#date_russia').text('俄罗斯' + russia_date.getDate() + ' ' + this._getEngMonth(russia_date.getMonth()));
    }

    setHszcl() {
        var html = '';
        var len = 0;
        for (var compcode in comp_normal_rate_list) {
            var rate = comp_normal_rate_list[compcode];
            var color, lv;

            rate = Math.round(rate * 100) / 100;

            if (rate >= 80) {
                color = 'green';
                lv = 9
            } else {
                color = 'red';
                lv = 2
            }

            html += '<div class="rw">'
            html += '<span class="colL">' + companyCode2Name[compcode] + '</span>'
            html += '<span class="colR ' + color + '">' + rate + '%</span>'
            html += '</div>'
            len++;
            $('#map_logo_' + compcode).css('background-image', 'url(../img/logo_normal_rate' + lv + '_' + compcode + '.png)');
            $('#hszclList').html(html);
        }
    }

    setHxData() {
        $('#exec_plane_num').text(all_company_flt['fftc']);
        var html = '';
        for (var i in fltDelay4List) {
            var flt = fltDelay4List[i];
            html += flt.flightNo + '<br>'
        }
        $('#delay_flt_list').html(html);

        var html = '';
        for (var i in fltIntList) {
            var flt = fltIntList[i];
            html += flt.flightNo + '<br>'
        }
        html += '<div style="height:250px;"><div>'
        $('#int_flt_list1').html(html);

        //大于N条自动滚动
        if (fltIntList.length > 26) {
            var speed = 60;
            var sec = document.getElementById("int_flt_list");
            var sec2 = document.getElementById("int_flt_list2");
            var sec1 = document.getElementById("int_flt_list1");
            sec2.innerHTML = sec1.innerHTML;

            function Marquee() {
                if (sec2.offsetTop - sec.scrollTop <= 0)
                    sec.scrollTop -= sec1.offsetHeight
                else {
                    sec.scrollTop++
                }
            }

            clearInterval(marquee_itv);
            marquee_itv = setInterval(Marquee, speed);
            sec.onmouseover = function () {
                clearInterval(marquee_itv)
            }
            sec.onmouseout = function () {
                marquee_itv = setInterval(Marquee, speed)
            }
        }

        var seriesData = [];

        for (var fltno in flightList) {
            flt = flightList[fltno];

            var arp1 = airportList[flt.depStn];
            var arp2 = airportList[flt.arrStn];

            if (arp1 && arp2 && arp1.longitude && arp1.latitude && arp2.longitude && arp2.latitude) {
                var color;

                var statusMap = {
                    'ARR': '落地',
                    'NDR': '落地',
                    'ATD': '推出',
                    'ATA': '到达',
                    'CNL': '取消',
                    'DEL': '延误',
                    'DEP': '起飞',
                    'RTR': '返航',
                    'SCH': '计划'
                };

                if (flt.status == 'DEL' || (flt.delay1 != '' && flt.dur1 > 0)) {
                    color = '#fff663';
                } else if (flt.status == 'ARR' || flt.status == 'NDR' || flt.status == 'ATD' || flt.status == 'ATA' || flt.status == 'DEP') {
                    color = '#0cff00';
                } else if (flt.status == 'CNL' || flt.status == 'RTR') {
                    color = '#FF0000';
                } else { //SCH
                    color = '#00c6ff';
                }

                seriesData.push({
                    fltno: fltno,
                    arrStn: flt.arrStn,
                    depStn: flt.depStn,
                    coords: [
                        [arp1.longitude, arp1.latitude],
                        [arp2.longitude, arp2.latitude]
                    ],
                    value: fltno,
                    lineStyle: {
                        width: 1,
                        color: color,
                        opacity: 1
                    },
                });
            }
        }


        // 航线的 tooltip
        var tooltip = {
            trigger: 'item',
            show: true,
            formatter: function (params, ticket, callback) {
                console.log('params', params);
                var data = params.data;
                var arp1 = airportList[data.arrStn];
                var arp2 = airportList[data.depStn];

                var fltno = data.fltno;
                var flt = flightList[fltno];

                var city_name1 = arp1.city_name;
                var arp_name1 = arp1.chn_name;

                var city_name2 = arp2.city_name;
                var arp_name2 = arp2.chn_name;

                var name1 = arp_name1.indexOf(city_name1) > -1 ? arp_name1 : (city_name1 + arp_name1)
                var name2 = arp_name2.indexOf(city_name2) > -1 ? arp_name2 : (city_name2 + arp_name2)

                var html = '';

                //航班号、起飞城市-到达城市、航班状态（实际/预计/计划起飞时间-实际/预计/计划到达时间）、机号（机型）、客座率（布局）、实时位置、实施油量
                html += '航班号: ' + fltno + '<br>';
                html += city_name1 + ' - ' + city_name2 + '<br>';
                if (flt.status == 'DEP' || flt.status == 'ARR' || flt.status == 'NDR' || flt.status == 'ATA') {
                    // 起飞,落地,到达
                    // 实际起飞时间 atdChn
                    html += '实际起飞时间: ' + trimTime(flt.atdChn) + '<br>';
                } else {
                    // 预计出发
                    html += '预计出发时间: ' + trimTime(flt.etdChn) + '<br>';
                }
                if (flt.status == 'ATA') {
                    // 到达
                    // 实际起飞时间 atdChn
                    html += '实际到达时间: ' + trimTime(flt.ataChn) + '<br>';
                } else {
                    // 预计到达
                    html += '预计到达时间: ' + trimTime(flt.etaChn) + '<br>';
                }
                if (flt.delay1 != '' && flt.dur1 > 0) {
                    html += '延误原因: ' + flt.delay1Name + '<br>';
                    html += '延误时间: ' + (Number(flt.dur1) + Number(flt.dur2) + Number(flt.dur3) + Number(flt.dur4)) + '分钟<br>';
                }
                html += '机号: ' + data.acno + '(' + flt.acType + ')' + '<br>';
                html += '实时位置: ' + Math.round(data.value[0] * 10000) / 10000 + ', ' + Math.round(data.value[1] * 10000) / 10000 + '<br>';
                return html;
            },
            backgroundColor: '#021e55',
        };
        hxseries.push({
            type: 'lines3D',
            coordinateSystem: 'globe',
            effect: {
                show: true,
                period: 6,
                trailLength: 0.3,
                trailColor: '#fff',
                trailWidth: 1
            },
            silent: false,
            tooltip: tooltip,
            data: seriesData
        });
        hbwzChart3D.clear();
        hbwzChart3D.setOption(hbwzoption3d);
        let option = hbwzChart3D.getOption();
        option.series = hxseries;
        hbwzChart3D.setOption(option, true);
    }

    setAcNum() {
        codelist_no_parent.forEach((v, i) => {
            if (ac_num[v] != undefined) {
                const kpiaclst2 = ac_num[v]['EXE_AC_NUM']['D']['data3'];
                // 执行中飞机架数
                const len = kpiaclst2.length;
                for (let i = 0; i < len; i++) {
                    let acdat = kpiaclst2[i];
                    let acid = acdat.actype;
                    let accode = actypeId2Code[acid];
                    if (accode && accode != 'QITA') {
                        let acdd = acdat.date;

                        let len2 = acdd.length;
                        // 每种机型的架数
                        let acno = 0;
                        for (let j = 0; j < len2; j++) {
                            let dd = acdd[j];
                            let val = isNaN(dd.value) ? 0 : Number(dd.value);
                            if (ac_type_list.indexOf(accode) > -1) {
                                exe_total_plane += val;
                            }
                            acno += val;
                        }

                        // 执行中 每种机型的架数
                        if (ac_type_list.indexOf(accode) > -1) {
                            acno = (isNaN(acno) || acno == 0) ? '-' : Math.round(acno);
                            if (exe_ac_data_list[accode] == undefined) {
                                exe_ac_data_list[accode] = 0;
                            }
                            exe_ac_data_list[accode] += acno;
                        }
                    }
                }
            }
        });
        $('#plane_over_air').text(exe_total_plane);
        let html = '';
        var barwidth = 50;
        let wideNumber = 0;
        let narrowNumber = 0;
        let regionalNumber = 0;
        let countWide = 0;
        let countNarrow = 0;
        let countRegional = 0;
        for (var accode in ac_data_list_total) {
            if (ac_type_wide.indexOf(accode) != -1) {
                countWide++;
            }
            if (ac_type_narrow.indexOf(accode) != -1) {
                countNarrow++;
            }
            if (ac_type_regional.indexOf(accode) != -1) {
                countRegional++;
            }
        }
        let wideHtml = countWide > 3 ? `<marquee class="subCon">` : `<div class="subCon">`;
        let narrowHtml = countNarrow > 2 ? `<marquee class="subCon">` : `<div class="subCon">`;
        let regionalHtml = countRegional > 1 ? `<marquee class="subCon" style="width:50%">` : `<div class="subCon">`;
        for (var accode in ac_data_list_total) {
            var numac = ac_data_list_total[accode];
            if (ac_type_wide.indexOf(accode) != -1) {
                wideNumber += numac;
                wideHtml += `<div><span class="blue1">${accode}</span><span class="fs14 bold">${numac}</span>架</div>`;
            }
            if (ac_type_narrow.indexOf(accode) != -1) {
                narrowNumber += numac;
                narrowHtml += `<div><span class="blue1">${accode}</span><span class="fs14 bold">${numac}</span>架</div>`;
            }
            if (ac_type_regional.indexOf(accode) != -1) {
                regionalNumber += numac;
                regionalHtml += `<div><span class="blue1">${accode}</span><span class="fs14 bold">${numac}</span>架</div>`;
            }

            if (numac > 0) {
                var numexeac = exe_ac_data_list[accode];
                numexeac = isNaN(numexeac) ? 0 : numexeac;

                var ac_ground = numac - numexeac;
                ac_ground = Math.max(ac_ground, 0);

                let width = ac_ground / numac * barwidth;
                html += `<div class="rw ${accode}"><span class="c1 blue1">${accode}</span><span class="c2">${numexeac}</span><span class="c3"><span class="c3pro"><span class="bar" style="width: ${width}px;"></span></span></span><span class="c4">${numac}</span></div>`;
            }
        }
        wideHtml += countWide > 3 ? `</marquee>` : `</div>`;
        narrowHtml += countNarrow > 2 ? `</marquee>` : `</div>`;
        regionalHtml += countRegional > 1 ? `</marquee>` : `</div>`;
        $("#wide_number").text(wideNumber);
        $("#narrow_number").text(narrowNumber);
        $("#regional_number").text(regionalNumber);
        $('#air_aclist').html(html);
        $("#wide").append(wideHtml);
        $("#narrow").append(narrowHtml);
        $("#regional").append(regionalHtml);
    }

    getAcUseFueltOt(compid, actype) {
        var fuelacs = all_comp_actype_data[compid][kpi_id_list['FUELT']]['L'];
        var fuel = 0;
        var lll = fuelacs[actype].date;
        for (var k = lll.length - 1; k >= 0; k--) {
            var o = lll[k];
            fuel = o.value;
        }
        return fuel;
    }

    getFuelt(compcodes) {
        var fuel = 0;
        var len = compcodes.length;
        for (var i = 0; i < len; i++) {
            var compcode = compcodes[i];
            var ddd = all_company_data['kpi_ratio_tq_d'][compcode]['FUELT']['L'];
            var count;
            if (ddd != undefined && "" != ddd) {
                for (var week in ddd) {
                    var count = ddd[week];
                    if ("" == count) {
                        count = 0;
                    }
                    break;
                }
            } else {
                count = 0;
            }
            fuel += Number(count);
        }
        return fuel;
    }
    _closeDetail(el) {
        el.parent(".moreBox").hide();
        thcsoption.series = thcsStore;
        thcsChart.clear();
        thcsChart.setOption(thcsoption);
    }
    _popDetail(el) {
        el.siblings(".moreBox").show();
    }
    _popContent(el) {
        switch (el.data("tab")) {
            case 1:
                $('.midNav li').removeClass('liselected');
                $('.midNav li[mid$=1]').addClass('liselected');
                $('#thcsMap').show();
                $('.thcsLegend').show();
                $("#thcsCnMap").hide();
                $('.thcsCnLegend').hide();
                $('#hbwzMap').hide();
                $('#hbwzMap3D').hide();
                $('.hbwzLegend').hide();
                $('.hxLegend').hide();
                $('.hszclLegend').hide();
                break;
            case 2:
                $('.midNav li').removeClass('liselected');
                $('.midNav li[mid$=1]').addClass('liselected');
                $('#thcsMap').hide();
                $('.thcsLegend').hide();
                $("#thcsCnMap").show();
                $('.thcsCnLegend').show();
                $('#hbwzMap').hide();
                $('#hbwzMap3D').hide();
                $('.hbwzLegend').hide();
                $('.hxLegend').hide();
                $('.hszclLegend').hide();
                break;
            case 3:
                $('.midNav li').removeClass('liselected');
                $('.midNav li[mid$=2]').addClass('liselected');
                $(".hbwzChange").data("2d", false);
                $(".hbwzChange").css("background-image", "url(img/3Dto2D.png)");
                $('#thcsMap').hide();
                $('.thcsLegend').hide();
                $("#thcsCnMap").hide();
                $('.thcsCnLegend').hide();
                $('#hbwzMap').hide();
                $('#hbwzMap3D').show();
                $('.hbwzLegend').show();
                $('.hbwzLegend .midMoreInfo').show();
                $('.hxLegend').hide();
                $('.hszclLegend').hide();
                hbwzChart3D.clear();
                hbwzChart3D.setOption(hbwzoption3d);
                let options3D = hbwzChart3D.getOption();
                options3D.series = hbssserise;
                options3D.tooltip = hbsstooltip;
                hbwzChart3D.setOption(options3D, true);
                break;
            case 4:
                $('.midNav li').removeClass('liselected');
                $('.midNav li[mid$=3]').addClass('liselected');
                $('#thcsMap').hide();
                $('.thcsLegend').hide();
                $("#thcsCnMap").hide();
                $('.thcsCnLegend').hide();
                $('#hbwzMap').hide();
                $('#hbwzMap3D').show();
                $('.hbwzLegend').hide();
                $('.hxLegend').show();
                $('.hszclLegend').hide();
                hbwzChart3D.clear();
                hbwzChart3D.setOption(hbwzoption3d);
                var option = hbwzChart3D.getOption();
                option.series = hxseries;
                hbwzChart3D.setOption(option, true);
                break;
            case 5:
                $('.midNav li').removeClass('liselected');
                $('.midNav li[mid$=4]').addClass('liselected');
                $('#thcsMap').hide();
                $('.thcsLegend').hide();
                $("#thcsCnMap").hide();
                $('.thcsCnLegend').hide();
                $('#hbwzMap').hide();
                $('#hbwzMap3D').show();
                $('.hbwzLegend').hide();
                $('.hxLegend').hide();
                $('.hszclLegend').show();
                hbwzChart3D.clear();
                hbwzChart3D.setOption(hbwzoption3d);
                var option = hbwzChart3D.getOption();
                option.globe[0].viewControl.autoRotate = false;
                option.globe[0].viewControl.zoomSensitivity = false;
                hbwzChart3D.setOption(option, true);
                break;
            case 6:
                $('.midNav li').removeClass('liselected');
                $('.midNav li[mid$=2]').addClass('liselected');
                $(".hbwzChange").data("2d", true);
                $(".hbwzChange").css("background-image", "url(img/2Dto3D.png)");
                $('#thcsMap').hide();
                $('.thcsLegend').hide();
                $("#thcsCnMap").hide();
                $('.thcsCnLegend').hide();
                $('#hbwzMap').show();
                $('#hbwzMap3D').hide();
                $('.hbwzLegend').show();
                $('.hbwzLegend .midMoreInfo').hide();
                $('.hxLegend').hide();
                $('.hszclLegend').hide();
                break;
        }
    }
    _gotoAoc(el) {
        if (el.hasClass("href")) {
            window.location.href = '/largescreen/7x2/?scale=auto';
        }
    }

    _initTime() {
        var date = new Date();
        var mm = date.getMonth() + 1;
        var dd = date.getDate();
        if (mm < 10) {
            mm = '0' + mm;
        }
        if (dd < 10) {
            dd = '0' + dd;
        }
        stdStart = date.getFullYear() + '-' + mm + '-' + dd + ' 00:00:00';
        stdEnd = date.getFullYear() + '-' + mm + '-' + dd + ' 23:59:59';
        stdEndUtcTime = date.getFullYear() + '-' + mm + '-' + dd + ' 15:59:59';
        var yesterday_ts = date.getTime() - 86400000;
        date.setTime(yesterday_ts);
        mm = date.getMonth() + 1;
        dd = date.getDate();
        if (mm < 10) {
            mm = '0' + mm;
        }
        if (dd < 10) {
            dd = '0' + dd;
        }
        stdStartUtcTime = date.getFullYear() + '-' + mm + '-' + dd + ' 16:00:00';
    }

    _initMap() {
        thcsChart.setOption(thcsoption);
        thcsCnChart.setOption(thcscnoption);
        hbwzChart.setOption(hbwzoption);
        hbwzChart3D.setOption(hbwzoption3d);
        $("#thcsCnMap").hide();
        $('.thcsCnLegend').hide();
        $('#hbwzMap').hide();
        $('#hbwzMap3D').hide();
        $('.hbwzLegend').hide();
    }

    _addEvent() {
        let initSocket = () => {
            let host = window.location.host.split(":");
            let protocol = window.location.protocol;
            let ws = protocol === 'https:' ? 'wss' : 'ws';

            let initUserinfo = () => {
                if ('WebSocket' in window) {
                    if (userinfo) {
                        if (host[0] === "vis.hnair.net") {
                            websocket = new WebSocket(`${ws}://${host[0]}:8280/websocket/sdc-index-${userinfo.id}`);
                        } else {
                            websocket = new WebSocket(`${ws}://${host[0]}:8888/websocket/sdc-index-${userinfo.id}`);
                        }
                        //连接发生错误的回调方法
                        websocket.onerror = function () {
                            console.log("WebSocket连接发生错误");
                        };

                        //连接成功建立的回调方法
                        websocket.onopen = function () {
                            console.log("WebSocket连接成功");
                        }

                        //接收到消息的回调方法
                        websocket.onmessage = function (event) {
                            console.log(event.data);
                            eval(event.data);
                        }.bind(this)

                        //连接关闭的回调方法
                        websocket.onclose = function () {
                            console.log("WebSocket连接关闭");
                        }

                        //监听窗口关闭事件，当窗口关闭时，主动去关闭websocket连接，防止连接还没断开就关闭窗口，server端会抛异常。
                        window.onbeforeunload = function () {
                            websocket.close();
                        }
                    } else {
                        setTimeout(initUserinfo, 10);
                    }
                } else {
                    alert('当前浏览器 Not support websocket');
                }
            }

            initUserinfo();
        };
        initSocket();

        $('.compname').on("click", function (event) {
            this._gotoAoc($(event.currentTarget));
        }.bind(this));

        $('.col_right_left .list .rw').on("click", function () {
            if (!$(this).hasClass("selected")) {
                $('.rw').removeClass('selected');
                $(this).addClass('selected');
            }
        });

        $('.col_mid .midNav li').on('click', function () {
            //样式切换
            if (!$(this).hasClass("liselected")) {
                $('.midNav li').removeClass('liselected');
                $('.moreBox').hide();
                $(this).addClass('liselected');
            }
            var mid = $(this).attr('mid');
            if (mid == 1) {
                $('#thcsMap').show();
                $('.thcsLegend').show();
                $('#thcsCnMap').hide();
                $('.thcsCnLegend').hide();
                $('#hbwzMap').hide();
                $('#hbwzMap3D').hide();
                $('.hbwzLegend').hide();
                $('.hxLegend').hide();
                $('.hszclLegend').hide();
            } else if (mid == 3) {
                $('#thcsMap').hide();
                $('.thcsLegend').hide();
                $('#thcsCnMap').hide();
                $('.thcsCnLegend').hide();
                $('#hbwzMap').hide();
                $('#hbwzMap3D').show();
                $('.hbwzLegend').hide();
                $('.hxLegend').show();
                $('.hszclLegend').hide();
                hbwzChart3D.clear();
                hbwzChart3D.setOption(hbwzoption3d);
                let option = hbwzChart3D.getOption();
                option.series = hxseries;
                hbwzChart3D.setOption(option, true);
            } else if (mid == 2) {
                $('#thcsMap').hide();
                $('.thcsLegend').hide();
                $('#thcsCnMap').hide();
                $('.thcsCnLegend').hide();
                $('#hbwzMap').show();
                $('#hbwzMap3D').hide();
                $('.hbwzLegend').show();
                $('.hbwzLegend .midMoreInfo').hide();
                $('.hxLegend').hide();
                $('.hszclLegend').hide();
            } else if (mid == 4) {
                $('#thcsMap').hide();
                $('.thcsLegend').hide();
                $('#thcsCnMap').hide();
                $('.thcsCnLegend').hide();
                $('#hbwzMap').hide();
                $('#hbwzMap3D').show();
                $('.hbwzLegend').hide();
                $('.hxLegend').hide();
                $('.hszclLegend').show();
                hbwzChart3D.clear();
                hbwzChart3D.setOption(hbwzoption3d);
                let option = hbwzChart3D.getOption();
                option.globe[0].viewControl.autoRotate = false;
                option.globe[0].viewControl.zoomSensitivity = false;
                hbwzChart3D.setOption(option, true);
            }

        });

        $('.content').on('click', function (event) {
            this._popContent($(event.currentTarget));
        }.bind(this));

        $(".thcsChange").click(function () {
            let isI = $(this).data("i");
            if (isI) {
                $('#thcsMap').hide();
                $('.thcsLegend').hide();
                $("#thcsCnMap").show();
                $('.thcsCnLegend').show();
                $('#hbwzMap').hide();
                $('#hbwzMap3D').hide();
                $('.hbwzLegend').hide();
                $('.hxLegend').hide();
                $('.hszclLegend').hide();
            } else {
                $('#thcsMap').show();
                $('.thcsLegend').show();
                $("#thcsCnMap").hide();
                $('.thcsCnLegend').hide();
                $('#hbwzMap').hide();
                $('#hbwzMap3D').hide();
                $('.hbwzLegend').hide();
                $('.hxLegend').hide();
                $('.hszclLegend').hide();
            }
        })

        $(".hbwzChange").click(function () {
            let is2D = $(this).data("2d");
            if (is2D) {
                $(this).data("2d", false);
                $(this).css("background-image", "url(img/3Dto2D.png)");
                $('#thcsMap').hide();
                $('.thcsLegend').hide();
                $("#thcsCnMap").hide();
                $('.thcsCnLegend').hide();
                $('#hbwzMap').hide();
                $('#hbwzMap3D').show();
                $('.hbwzLegend').show();
                $('.hbwzLegend .midMoreInfo').show();
                $('.hxLegend').hide();
                $('.hszclLegend').hide();
                $(".content_btn[data-id='3']").siblings(".moreBox").show();
                hbwzChart3D.clear();
                hbwzChart3D.setOption(hbwzoption3d);
                let options3D = hbwzChart3D.getOption();
                options3D.series = hbssserise;
                options3D.tooltip = hbsstooltip;
                hbwzChart3D.setOption(options3D, true);
            } else {
                $(this).data("2d", true);
                $(this).css("background-image", "url(img/2Dto3D.png)");
                $('#thcsMap').hide();
                $('.thcsLegend').hide();
                $("#thcsCnMap").hide();
                $('.thcsCnLegend').hide();
                $('#hbwzMap').show();
                $('#hbwzMap3D').hide();
                $('.hbwzLegend').show();
                $('.hbwzLegend .midMoreInfo').hide();
                $('.hxLegend').hide();
                $('.hszclLegend').hide();
                $(".content_btn[data-id='3']").siblings(".moreBox").hide();
            }
        })


    }

    _formatNum(n) {
        if (n < 10) {
            return ('0' + n);
        } else {
            return n;
        }
    }

    _getEngMonth(month) {
        var mlist = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Spt", "Oct", "Nov", "Dec"];
        return mlist[month].toUpperCase();
    }

    _trimTime(timestr) {
        var arr = timestr.split(' ');
        var arr2 = arr[1].split(':');
        return arr2[0] + ':' + arr2[1];
    }
}

let sd = new sdc();
$.when(getCompany()).done(sd.loadAll());

