
/**/

.page-wrapper {
background: rgb(1,35,93);
background: -moz-radial-gradient(center, ellipse cover,  rgba(1,35,93,1) 0%, rgba(1,5,30,1) 100%);
background: -webkit-radial-gradient(center, ellipse cover,  rgba(1,35,93,1) 0%,rgba(1,5,30,1) 100%);
background: radial-gradient(ellipse at center,  rgba(1,35,93,1) 0%,rgba(1,5,30,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#01235d', endColorstr='#01051e',GradientType=1 );
}

.limcomb{
  position: absolute;
  top: 13px;
  right: 185px;
  height: 30px;
  width: 135px !important;
  z-index: 1001;
  border-radius: 4px;
  border: 1px solid rgba(41, 140, 232, 0.6);
  background: #0b3379;
}
.limcomb .combobox_label{
  display: inline-block;
  padding: 0;
  height: 30px;
  line-height: 30px;
  text-align: left;
  background-image: url(../img/combobox_arr.png);
  background-repeat: no-repeat;
  background-position: 115px center;
  border-radius: 4px;
  border-right: none;
  border: 0px !important;
  box-sizing: border-box;
  padding-left: 5px;
  width: 100%;

}
.pagetitle{
  position: absolute;
  width: 1306px;
  height: 59px;
  top: 58px;
  left: 30px;
  text-align: center;
  border-bottom: 2px solid #1765a0;
}
.pagetitle .date_type_select {
  /*width: 160px;*/
  height: 30px;
  border: 1px solid #1a6cbc;
  border-radius: 3px;
  display: inline-block;
  vertical-align: middle;
  position: absolute;
  right: 60px;
  top: 13px;
  z-index: 1001;
  box-sizing: border-box;
}
.hidden{
  display: none !important;
}
.pagetitle .date_type_select .tab {
  display: inline-block;
  vertical-align: middle;
  width: 39px;
  height: 29px;
  float: left;
  box-sizing: border-box;
  text-align: center;
  line-height: 28px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  font-size: 14px;
  color: #44a3f4;
  border-right: 1px solid #2a81d2;
  z-index: 1001;
  pointer-events: auto;
}
.pagetitle .date_type_select .tab:last-child{
  border-right: 0;
}
.pagetitle .date_type_select .tab.hover {
  background-color: #2a81d2;
  color: white;
}
.pagetitle .date_type_select .tab:hover {
  background-color: #2a81d2;
  color: white;
}
.pagetitle .date_type_select .tab:nth-child(1) {
  position: relative;
  /*left: -1px;*/
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}
.pagetitle .date_type_select .tab:nth-child(4) {
  position: relative;
  /*right: -2px;*/
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}

.pagetitle .data_refresh {
  display: inline-block;
  position: absolute;
  height: 24px;
  width: 24px;
  top: 14px;
  right: 20px;
  z-index: 1001;
  background-image: url(../img/data_refresh.png);
  pointer-events: auto;
  cursor: pointer;
}

.maintitle{
  color: #7fc4ff;
  width: 100%;
  font-size: 24px;
  padding: 11px 0 0 0;
  margin: 0 auto;
  font-weight: bold;
}
.submaintitle{
  color: #7fc4ff;
  width: 100%;
  font-size: 22px;
  padding: 0 20px;
  margin: 0 auto;
}

/*#main_cb_week {
  position: absolute;
  top: 15px;
  left: 1118px;
  height: 34px;
  width: 85px;
  z-index: 1000;
}
#main_cb_month {
  position: absolute;
  top: 15px;
  left: 1202px;
  height: 34px;
  width: 104px;
  z-index: 1001;
}

#main_cb_week .combobox_label
{
  padding: 6px 6px;
  background-image: url(../img/combobox_arr.png);
  background-repeat: no-repeat;
  background-position: 63px center;
  border-radius: 4px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}
#main_cb_month .combobox_label
{
  padding: 6px 6px;
  background-image: url(../img/combobox_arr.png);
  background-repeat: no-repeat;
  background-position: 83px center;
  border-radius: 4px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: none;
}
*/
.combotab_selected .combobox_label {
  background-color: #2a81d2;
}
.combotab .combobox_label {
  /*background-color: #0950a2;*/
  /*opacity: 0.5;*/
  color: #ffffff;
}

#week_date_range {
  position: absolute;
  top: 13px;
  right: 328px;
  width: 160px;
  font-size: 14px;
  text-align: center;
  border: 1px solid #1f5fa8;
  border-radius: 2px;
  padding: 3px 0 5px 0px;
  background-color: #021d39;
  box-shadow: 0 1px 8px #021121;

  display: none;
}

/* ----- */

.block_t_tt {
  position: absolute;
  height: 30px;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  top: 387px;
  width: 100%;
}

.block_t {
  position: absolute;
  height: 69px;
  width: 1300px;
  top: 419px;
  left: 33px;
  pointer-events: auto;
}

.block_t .itm {
  position: absolute;
  height: 69px;
  /*width: 182px;*/
  width: 256.5px;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(1,5,29,0.2);
  cursor: default;
}
.block_t .itm:hover {
  box-shadow: 0 1px 9px rgba(1,5,29,0.8);
}

.block_t .itm .ico {
  position: absolute;
  height: 52px;
  width: 52px;
  top: 8px;
  left: 2px;
  background-repeat: no-repeat;
}

.block_t .itm .lb {
  position: absolute;
  height: 12px;
  top: 8px;
  left: 53px;
  background-repeat: no-repeat;
}
.block_t .itm .l2 {
  position: absolute;
  width: 180px;
  height: 12px;
  top: 30px;
  left: 53px;
  background-repeat: no-repeat;
}

.block_t .itm .l2 .val{
  display: inline-block;
}

.block_t .itm .l2 .tb{
  display: inline-block;
  font-size: 11px;
  line-height: 14px;
  padding: 0 12px 0 4px;
}

.block_t .itm .l2 .tb.up{
  background: url(../img/arr_up2.png) no-repeat right 13px;
}
.block_t .itm .l2 .tb.down{
  background: url(../img/arr_down2.png) no-repeat right 13px;
}

.block_t .itm .border{
  position: absolute;
  height: 1px;
  width: 100%;
  background-color: rgba(255,255,255,0.1);
}

.block_t .itm0 {
  left: 0px;
background: rgb(59,56,129);
background: -moz-linear-gradient(top,  rgba(59,56,129,1) 0%, rgba(47,45,128,1) 100%);
background: -webkit-linear-gradient(top,  rgba(59,56,129,1) 0%,rgba(47,45,128,1) 100%);
background: linear-gradient(to bottom,  rgba(59,56,129,1) 0%,rgba(47,45,128,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#3b3881', endColorstr='#2f2d80',GradientType=0 );
}

.block_t .itm1 {
  left: 186px;
background: rgb(84,92,91);
background: -moz-linear-gradient(top,  rgba(84,92,91,1) 0%, rgba(68,79,73,1) 100%);
background: -webkit-linear-gradient(top,  rgba(84,92,91,1) 0%,rgba(68,79,73,1) 100%);
background: linear-gradient(to bottom,  rgba(84,92,91,1) 0%,rgba(68,79,73,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#545c5b', endColorstr='#444f49',GradientType=0 );
}

.block_t .itm2 {
  left: 372px;

background: rgb(70,49,54);
background: -moz-linear-gradient(top,  rgba(70,49,54,1) 0%, rgba(62,39,42,1) 100%);
background: -webkit-linear-gradient(top,  rgba(70,49,54,1) 0%,rgba(62,39,42,1) 100%);
background: linear-gradient(to bottom,  rgba(70,49,54,1) 0%,rgba(62,39,42,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#463136', endColorstr='#3e272a',GradientType=0 );

}

.block_t .itm3 {
  left: 558px;

background: rgb(36,78,136);
background: -moz-linear-gradient(top,  rgba(36,78,136,1) 0%, rgba(17,67,134,1) 100%);
background: -webkit-linear-gradient(top,  rgba(36,78,136,1) 0%,rgba(17,67,134,1) 100%);
background: linear-gradient(to bottom,  rgba(36,78,136,1) 0%,rgba(17,67,134,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#244e88', endColorstr='#114386',GradientType=0 );

}

.block_t .itm4 {
  left: 744px;

background: rgb(120,77,85);
background: -moz-linear-gradient(top,  rgba(120,77,85,1) 0%, rgba(95,58,70,1) 100%);
background: -webkit-linear-gradient(top,  rgba(120,77,85,1) 0%,rgba(95,58,70,1) 100%);
background: linear-gradient(to bottom,  rgba(120,77,85,1) 0%,rgba(95,58,70,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#784d55', endColorstr='#5f3a46',GradientType=0 );

}

.block_t .itm5 {
  left: 930px;

background: rgb(35,93,82);
background: -moz-linear-gradient(top,  rgba(35,93,82,1) 0%, rgba(15,82,70,1) 100%);
background: -webkit-linear-gradient(top,  rgba(35,93,82,1) 0%,rgba(15,82,70,1) 100%);
background: linear-gradient(to bottom,  rgba(35,93,82,1) 0%,rgba(15,82,70,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#235d52', endColorstr='#0f5246',GradientType=0 );

}

.block_t .itm6 {
  width: 185px;
  left: 1116px;

background: rgb(31,87,123);
background: -moz-linear-gradient(top,  rgba(31,87,123,1) 0%, rgba(3,72,114,1) 100%);
background: -webkit-linear-gradient(top,  rgba(31,87,123,1) 0%,rgba(3,72,114,1) 100%);
background: linear-gradient(to bottom,  rgba(31,87,123,1) 0%,rgba(3,72,114,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1f577b', endColorstr='#034872',GradientType=0 );

}

.block_t .itm0 .ico {
  background-image: url(../img/a2.5.ico0.png);
}
.block_t .itm1 .ico {
  background-image: url(../img/a2.5.ico7.png);
}
.block_t .itm2 .ico {
  background-image: url(../img/a2.5.ico8.png);
}
.block_t .itm3 .ico {
  background-image: url(../img/a2.5.ico1.png);
}
.block_t .itm4 .ico {
  background-image: url(../img/a2.5.ico3.png);
}
.block_t .itm5 .ico {
  background-image: url(../img/a2.5.ico4.png);
}
.block_t .itm6 .ico {
  background-image: url(../img/a2.5.ico5.png);
}


/* ----- */
.block_m {
  position: absolute;
  height: 252px;
  width: 1300px;
  top: 505px;
  left: 33px;
  pointer-events: auto;
}


.block_ml {
  position: absolute;
  height: 100%;
  width: 408px;
  border-radius: 5px;
  border: 1px solid #135f9f;
  overflow: hidden;
}

.block_ml table{
  width: 100%;
  height: 252px;
}

.block_ml .tt{
  text-align: right;
  padding-right: 8px;
}

.block_ml .tline{
  border-top: 1px solid #135f9f;
}

.block_ml .col1{
  background-color: #0a3876;
}
.block_ml .col2{
  background-color: #0a2568;
}



/* ---- */


.block_mr {
  position: absolute;
  height: 100%;
  width: 880px;
  left: 420px;
  pointer-events: auto;
}

.block_mr div {
  position: absolute;
}

.block_mr .tab {
  position: absolute;
  width: 202px;
  height: 34px;
  top: 0px;
  line-height: 31px;
  font-weight: bold;
  color: #34a7ff;
  background-color: #052263;
  border: 1px solid #052263;
  text-align: center;
  padding-left: 28px;
  opacity: 0.7;
  cursor: pointer;
}
.block_mr .tab:hover {
  opacity: 1;
}
.block_mr .selected {
  color: #FFFFFF;
  background-color: #1361aa;
  border: 1px solid #1361aa;
  text-align: center;
  opacity: 1;
}
.block_mr .tab1 {
  left: 240px;
  background-image: url(../img/a2.5.tab_ico1.png);
  background-repeat: no-repeat;
  background-position: 23px 5px;
}
.block_mr .tab2 {
  left: 442px;
  background-image: url(../img/a2.5.tab_ico2.png);
  background-repeat: no-repeat;
  background-position: 23px 5px;
}


/* ---- */



.block_b {
  position: absolute;
  height: 270px;
  width: 1300px;
  top: 115px;
  left: 33px;
  pointer-events: auto;
  text-align: center;
}

.block_b .tit{
  margin: 0 auto;
  display: inline-block;
  height: 30px;
  text-align: center;
  padding-left: 40px;
  padding-right: 40px;
  font-size: 18px;
  font-weight: bold;
  background: url(../img/a2.5.icoplane.png) no-repeat left center;
}
.block_b .scrollpane{
  position: absolute;
  height: 238px;
  width: 100%;
  top: 35px;
  overflow-x: hidden;
  overflow-y: hidden;
}
.block_b .scrollpane .content{
  position: absolute;
  height: 100%;
  left: 0px;
  transition: all 0.3s ease-out;
}
.block_b .scrollpane .content .lst{
  position: absolute;
  display: inline-block;
  height: 100%;
}
.block_b .scrollpane .content .list0{
  background: url(../img/a2.5_tt_type0.png) no-repeat center top;
}
.block_b .scrollpane .content .list1{
  background: url(../img/a2.5_tt_type1.png) no-repeat center top;
}
.block_b .scrollpane .content .list2{
  background: url(../img/a2.5_tt_type2.png) no-repeat center top;
}


.block_b .itm {
  position: absolute;
  display: inline-block;
  height: 200px;
  width: 156px;
  top: 29px;
  border-top-right-radius: 3px;
  border-top-left-radius: 3px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(1,5,29,0.2);
  cursor: default;
  text-align: left;
  border-top: 1px solid #1b5799;

  background: rgb(6,36,91);
  background: -moz-linear-gradient(top,  rgba(6,36,91,1) 0%, rgba(0,16,50,1) 100%);
  background: -webkit-linear-gradient(top,  rgba(6,36,91,1) 0%,rgba(0,16,50,1) 100%);
  background: linear-gradient(to bottom,  rgba(6,36,91,1) 0%,rgba(0,16,50,1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#06245b', endColorstr='#001032',GradientType=0 );

  cursor: pointer;
}

.block_b .itm.selected {
  border-top: 1px solid #1b5799;
  background: #00387c;
  box-shadow: inset 0 1px 3px 5px #00316d;
}
.block_b .itm.selected .border {
  border: 1px solid #7ecdf4;
}

.block_b .itm:hover {
  box-shadow: 0 1px 9px rgba(1,5,29,0.8);
}


.block_b .border {
  position: absolute;
  height: 100%;
  width: 100%;
  border: 1px solid #103578;
}

.block_b .itm .ac {
  position: absolute;
  top: 15px;
  left: 15px;
  color:#85b9fc;
  font-size: 20px;
  font-weight: bold;
}
.block_b .itm .num {
  position: absolute;
  top: 16px;
  right: 15px;
  color:#85b9fc;
  font-size: 18px;
  text-align: right;
}
.block_b .itm .num .val {
  font-size: 16px;
  font-weight: bold;
  color: #fff;
}
.block_b .itm .num .sub {
  font-size: 12px;
  color: #85b9fc;
}
.block_b .itm .ico {
  position: absolute;
  width: 100%;
  height: 60px;
  top: 39px;
  background-repeat: no-repeat;
}

.block_b .itm .t {
  display: block;
  color: #57b7fa;
  font-size:13px;
}
.block_b .itm .b .v {
  color: #fff;
  font-size:18px;
  font-weight: bold;
}

.block_b .itm .b1 {
  position: absolute;
  top: 98px;
  left: 15px;
}
.block_b .itm .b2 {
  position: absolute;
  top: 98px;
  left: 95px;
}
.block_b .itm .b3 {
  position: absolute;
  top: 145px;
  left: 15px;
}
.block_b .itm .b4 {
  position: absolute;
  top: 145px;
  left: 95px;
}

.block_b .itm .b2 .b .v {
  font-size: 14px !important;
  font-weight: normal !important;
}
.block_b .itm .b4 .b .v {
  font-size: 14px !important;
  font-weight: normal !important;
}

.block_b .ph .ico {
  background-image: url(../img/a2.5.acnone.png);
}




.block_b .scrollpane .content .list0 .itm {
  border-bottom: 5px solid #593e3d;
}


.block_b .scrollpane .content .list1 .itm {
  border-bottom: 5px solid #0e4691;
}


.block_b .scrollpane .content .list2 .itm {
  border-bottom: 5px solid #134540;
}

.btn_prev {
  position: absolute;
  width: 26px;
  height: 200px;
  background: rgba(6,36,91,0.6) url(../img/a2.5_btn_prev.png) no-repeat center center;
  top: 64px;
  left: -29px;
  cursor: pointer;
}
.btn_prev:hover {
  background-color: #06245b;
}
.btn_next {
  position: absolute;
  width: 26px;
  height: 200px;
  background: rgba(6,36,91,0.6) url(../img/a2.5_btn_next.png) no-repeat center center;
  top: 64px;
  right: -26px;
  cursor: pointer;
}
.btn_next:hover {
  background-color: #06245b;
}



/* --- */
.ext_link {
  display: inline-block;
  width: 23px;
  height: 23px;
  cursor: pointer;
  pointer-events: auto;
  background: url(../img/ext_link1.png) no-repeat center center;
}

#ext_link1 {
  position: absolute;
  right: 200px;
  top: 7px;
  display: none;
}
#ext_link2 {
  position: absolute;
  right: 541px;
  top: 2px;
  
}

.block_mr .remark{
  bottom: 0px;
  text-align: center;
  height: 30px;
  line-height: 30px;
  background: url("../img/reamrkback.png") no-repeat center;
  background-size: 100%; 
  color: #47aafd;
  font-size: 12px;
}