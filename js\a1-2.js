
showLoading();



var current_company_code;
var current_company_id;

// 可显示的历史 数量
var query_limit = 7;
// 日期类型
var date_type = 'M';


$('#companycombo').hide(); // 不显示成员公司


// -------------------------------------------------------

//                          KPI

// -------------------------------------------------------

var kpilist = {
    'FFP_MEMBER_TOTAL':'10106', // 常旅客会员人数
    'FFP_MEMBER_M_ACT':'10107', // 月活
    'FFP_MEMBER_NEW':'10116', // 常旅客新增会员人数

    'FFP_POINT_TOTAL':'10108', // 常旅客积分总量
    'FFP_POINT_M_ADD':'10109', // 常旅客当月积分累计
    'FFP_POINT_M_MINUS':'10110', // 常旅客当月积分消减
    'FFP_POINT_M_EXPIRE':'10111', // 常旅客当月积分过期
}

var cardlist = ['白金卡', '金卡', '飞行卡', '金鹏卡', '银卡'];

var monthList_L = [];
var monthList_R = [];

var selected_month_L;
var selected_month_R;


var all_company_data;



var member_card_data;
var member_point_data;


// 获取会员卡数据
function getMemberKpi(){

    if(companylist.length == 0){
        setTimeout(getMemberKpi,0);
        return;
    }


    // check url hash
    var hash = window.location.hash.substr(1);
    if(hash && companyCode2Name[hash] != undefined){
        current_company_code = hash;
        
        var len = companylist.length;
        var codelist = [];
        for(var i=0; i<len; i++){
            
            var dat = companylist[i];
            if(dat.code == hash){
                current_company_id = dat.id;
            }
        }
    }else{
        current_company_code = parent_company;
        current_company_id = parent_company_id;
    }


    // 会员
    var param = {
        COMP_ID: current_company_id
    }

    $.ajax({
        type: 'post',
        url:"/bi/web/memberkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            member_card_data = response.member;
            monthList_L = [];

            var list = response.member;

            for(var i=list.length-1; i>=0; i--){
                var dat = list[i];
                if(monthList_L.indexOf(dat.DATE_ID)==-1){
                    monthList_L.push(dat.DATE_ID);
                }
            }

            monthList_L.sort();
            monthList_L.reverse();

            buildMonthSliderL();
            buildMonthSliderR();

            hideLoading();
            
        },
        error:function() {
        }
    });


    // 积分产品
    var param = {
        COMP_ID: current_company_id
    }

    $.ajax({
        type: 'post',
        url:"/bi/web/productkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            member_point_data = response.product;
            
        },
        error:function() {
        }
    });


    

}



// 左边 会员 月份滑动条
function buildMonthSliderL(){
    var len = 12;
    var total = 0;
    var d = new Date();
    var thisyear = d.getFullYear() + '00'
    for(var i=0; i<len; i++){
        if(i<monthList_L.length){
            var month = monthList_L[i];
            
            var label = month.substr(0,4) + '-' + month.substr(4,2);
            // 新增
            var inc = calKpi('FFP_MEMBER_NEW', month, 'KPI_VALUE', '', "");
            if(Number(month) > Number(thisyear)){
                total+=inc;
            }
            $('#member_slider .node' + i).attr('data-month', month);
            $('#member_slider .node' + i + ' .month').text(label);
            $('#member_slider .node' + i + ' .val').text(inc == 0 ? '-' : inc);
            $('#member_slider .node' + i).show();
        }else{
            $('#member_slider .node' + i).hide();
        }
        
    }
    //total = Math.round(total/10000);
    // 今年累计总量
    $('#member_slider .year').text(d.getFullYear());
    $('#member_slider .bot_num .val').text(total);

    selectMonthMember(0);
}

// 右边 会员 月份滑动条
function buildMonthSliderR(){
    var len = 6;
    var total = 0;
    for(var i=0; i<len; i++){
        if(i<monthList_L.length){
            var month = monthList_L[i];
            var label = month.substr(0,4) + '-' + month.substr(4,2);
            // 新增
            var inc = calKpi('FFP_POINT_M_ADD', month, 'KPI_VALUE', '', "");
            total+=inc;
            inc = Math.round(inc/10000);
            $('#point_slider .node' + i).attr('data-month', month);
            $('#point_slider .node' + i + ' .month').text(label);
            $('#point_slider .node' + i + ' .val').text(inc);
            $('#point_slider .node' + i).show();
        }else{
            $('#point_slider .node' + i).hide();
        }
        
    }
    total = Math.round(total/10000);
    $('#point_slider .bot_num .val').text(total);

    selectMonthPoint(0);
}


// 左边 会员 顶部 指标
function setBlockTLKpi(){
    var total = calKpi('FFP_MEMBER_TOTAL', selected_month_L, 'KPI_VALUE', '', "");
    var inc = calKpi('FFP_MEMBER_NEW', selected_month_L, 'KPI_VALUE', '', "");
    var mact = calKpi('FFP_MEMBER_M_ACT', selected_month_L, 'KPI_VALUE', '', "");

    var total_val_tq = calKpi('FFP_MEMBER_TOTAL', selected_month_L, 'KPI_VALUE_TQ', '', "");
    // 同比=(本期-同期)÷同期×100%
    var total_tb = total_val_tq > 0 ? Math.round((total-total_val_tq)/total_val_tq * 1000)/10 : 0;

    var inc_tq = calKpi('FFP_MEMBER_NEW', selected_month_L, 'KPI_VALUE_TQ', '', "");
    // 同比=(本期-同期)÷同期×100%
    var inc_tb = inc_tq > 0 ? Math.round((inc-inc_tq)/inc_tq * 1000)/10 : 0;

    var mact_tq = calKpi('FFP_MEMBER_M_ACT', selected_month_L, 'KPI_VALUE_TQ', '', "");
    // 同比=(本期-同期)÷同期×100%
    var mact_tb = mact_tq > 0 ? Math.round((mact-mact_tq)/mact_tq * 1000)/10 : 0;

    var total_val_sq = calKpi('FFP_MEMBER_TOTAL', selected_month_L, 'KPI_VALUE_SQ', '', "");
    // 环比=(本期-上期)/上期×100%
    var total_hb = total_val_sq > 0 ? Math.round((total-total_val_sq)/total_val_sq * 1000)/10 : 0;

    var inc_sq = calKpi('FFP_MEMBER_NEW', selected_month_L, 'KPI_VALUE_SQ', '', "");
    // 环比=(本期-上期)/上期×100%
    var inc_hb = inc_sq > 0 ? Math.round((inc-inc_sq)/inc_sq * 1000)/10 : 0;

    var mact_sq = calKpi('FFP_MEMBER_M_ACT', selected_month_L, 'KPI_VALUE_SQ', '', "");
    // 环比=(本期-上期)/上期×100%
    var mact_hb = mact_sq > 0 ? Math.round((mact-mact_sq)/mact_sq * 1000)/10 : 0;


    function getTHB(v){
        return v == 0 || isNaN(v) ? '-' : v;
    }

    $('.block_lt .col1 .num').text(formatCurrency(Math.round(total/10000),0));
    if(inc > 0){
        $('.block_lt .col2 .num').text(formatCurrency(inc,0));
        $('.block_lt .col2 .unit').show();
    }else{
        $('.block_lt .col2 .num').text('-');
        $('.block_lt .col2 .unit').hide();
    }
    if(mact > 0){
        $('.block_lt .col3 .num').text(formatCurrency(mact,0));
        $('.block_lt .col3 .unit').show();
    }else{
        $('.block_lt .col3 .num').text('-');
        $('.block_lt .col3 .unit').hide();
    }


    $('.block_lt .col1 .tb .val').text(getTHB(total_tb));
    $('.block_lt .col2 .tb .val').text(getTHB(inc_tb));
    $('.block_lt .col3 .tb .val').text(getTHB(mact_tb));

    $('.block_lt .col1 .hb .val').text(getTHB(total_hb));
    $('.block_lt .col2 .hb .val').text(getTHB(inc_hb));
    $('.block_lt .col3 .hb .val').text(getTHB(mact_hb));

    $('.block_lt .col1 .tb .per').show();
    $('.block_lt .col1 .hb .per').show();

    $('.block_lt .col2 .tb .per').show();
    $('.block_lt .col2 .hb .per').show();

    $('.block_lt .col3 .tb .per').show();
    $('.block_lt .col3 .hb .per').show();

    if(total_tb > 0){
        $('.block_lt .col1 .tb .green').show();
        $('.block_lt .col1 .tb .red').hide();
    }else if(total_tb < 0){
        $('.block_lt .col1 .tb .green').hide();
        $('.block_lt .col1 .tb .red').show();
    }else{
        $('.block_lt .col1 .tb .green').hide();
        $('.block_lt .col1 .tb .red').hide();
        $('.block_lt .col1 .tb .per').hide();
    }

    if(total_hb > 0){
        $('.block_lt .col1 .hb .green').show();
        $('.block_lt .col1 .hb .red').hide();
    }else if(total_hb < 0){
        $('.block_lt .col1 .hb .green').hide();
        $('.block_lt .col1 .hb .red').show();
    }else{
        $('.block_lt .col1 .hb .green').hide();
        $('.block_lt .col1 .hb .red').hide();
        $('.block_lt .col1 .hb .per').hide();
    }

    //

    if(inc_tb > 0){
        $('.block_lt .col2 .tb .green').show();
        $('.block_lt .col2 .tb .red').hide();
    }else if(inc_tb < 0){
        $('.block_lt .col2 .tb .green').hide();
        $('.block_lt .col2 .tb .red').show();
    }else{
        $('.block_lt .col2 .tb .green').hide();
        $('.block_lt .col2 .tb .red').hide();
        $('.block_lt .col2 .tb .per').hide();
    }

    if(inc_hb > 0){
        $('.block_lt .col2 .hb .green').show();
        $('.block_lt .col2 .hb .red').hide();
    }else if(inc_hb < 0){
        $('.block_lt .col2 .hb .green').hide();
        $('.block_lt .col2 .hb .red').show();
    }else{
        $('.block_lt .col2 .hb .green').hide();
        $('.block_lt .col2 .hb .red').hide();
        $('.block_lt .col2 .hb .per').hide();
    }

    //

    if(mact_tb > 0){
        $('.block_lt .col3 .tb .green').show();
        $('.block_lt .col3 .tb .red').hide();
    }else if(mact_tb < 0){
        $('.block_lt .col3 .tb .green').hide();
        $('.block_lt .col3 .tb .red').show();
    }else{
        $('.block_lt .col3 .tb .green').hide();
        $('.block_lt .col3 .tb .red').hide();
        $('.block_lt .col3 .tb .per').hide();
    }

    if(mact_hb > 0){
        $('.block_lt .col3 .hb .green').show();
        $('.block_lt .col3 .hb .red').hide();
    }else if(mact_hb < 0){
        $('.block_lt .col3 .hb .green').hide();
        $('.block_lt .col3 .hb .red').show();
    }else{
        $('.block_lt .col3 .hb .green').hide();
        $('.block_lt .col3 .hb .red').hide();
        $('.block_lt .col3 .hb .per').hide();
    }


    // 语音播报
    function onAudioTplLoad(tplobj){
        
        // {COMP} {DATE} 会员总量{MEM_TOTAL}，同比{TB1}，环比{HB1}。增量{MEM_INC}，同比{TB2}，环比{HB2}。月活{MEM_ACT}，同比{TB3}，环比{HB3}。
        var tpl = tplobj.txt;
       
        //--
        var compname = companyCode2Name[current_company_code];
        tpl = tpl.replace(/{COMP}/g, compname);

        //--
        var date = selected_month_L;
        var datelabel = date.substr(0,4) + '年' + Number(date.substr(4,2)) + '月';
        tpl = tpl.replace(/{DATE}/g, datelabel);

        //--
        tpl = tpl.replace(/{MEM_TOTAL}/g, formatCurrency(Math.round(total/10000)*10000, 2));

        //--
        tpl = tpl.replace(/{TB1}/g, ratioTemplete(total_tb));

        //--
        tpl = tpl.replace(/{HB1}/g, ratioTemplete(total_hb));

        //--
        tpl = tpl.replace(/{MEM_INC}/g, formatCurrency(inc, 2));

        //--
        tpl = tpl.replace(/{TB2}/g, ratioTemplete(inc_tb));

        //--
        tpl = tpl.replace(/{HB2}/g, ratioTemplete(inc_hb));

        //--
        tpl = tpl.replace(/{MEM_ACT}/g, formatCurrency(mact, 2));

        //--
        tpl = tpl.replace(/{TB3}/g, ratioTemplete(mact_tb));

        //--
        tpl = tpl.replace(/{HB3}/g, ratioTemplete(mact_hb));

        text2audio(tpl, true);

    }

    stopAudio();
    getAudioTemplate('A1.2-member', onAudioTplLoad);
}


// 右边 积分 顶部 指标
function setBlockTRKpi(){
    // 总积分
    var total = calKpi('FFP_POINT_TOTAL', selected_month_R, 'KPI_VALUE', '', "");

    var total_val_tq = calKpi('FFP_POINT_TOTAL', selected_month_R, 'KPI_VALUE_TQ', '', "");
    // 同比=(本期-同期)÷同期×100%
    var total_tb = total_val_tq > 0 ? Math.round((total-total_val_tq)/total_val_tq * 1000)/10 : 0;
    
    var total_val_sq = calKpi('FFP_POINT_TOTAL', selected_month_R, 'KPI_VALUE_SQ', '', "");
    // 环比=(本期-上期)/上期×100%
    var total_hb = total_val_sq > 0 ? Math.round((total-total_val_sq)/total_val_sq * 1000)/10 : 0;

    function getTHB(v){
        return v == 0 || isNaN(v) ? '-' : v;
    }

    $('.block_rt .row1 .num').text(formatCurrency(total/10000,0));
    $('.block_rt .row1 .tb .val').text(getTHB(total_tb));
    $('.block_rt .row1 .hb .val').text(getTHB(total_hb));

    $('.block_rt .row1 .tb .per').show();
    $('.block_rt .row1 .hb .per').show();

    if(total_tb > 0){
        $('.block_rt .row1 .tb .green').show();
        $('.block_rt .row1 .tb .red').hide();
    }else if(total_tb < 0){
        $('.block_rt .row1 .tb .green').hide();
        $('.block_rt .row1 .tb .red').show();
    }else{
        $('.block_rt .row1 .tb .green').hide();
        $('.block_rt .row1 .tb .red').hide();
        $('.block_rt .row1 .tb .per').hide();
    }

    if(total_hb > 0){
        $('.block_rt .row1 .hb .green').show();
        $('.block_rt .row1 .hb .red').hide();
    }else if(total_hb < 0){
        $('.block_rt .row1 .hb .green').hide();
        $('.block_rt .row1 .hb .red').show();
    }else{
        $('.block_rt .row1 .hb .green').hide();
        $('.block_rt .row1 .hb .red').hide();
        $('.block_rt .row1 .hb .per').hide();
    }


    // 当月累计
    var m_inc = calKpi('FFP_POINT_M_ADD', selected_month_R, 'KPI_VALUE', '', "");

    var m_inc_tq = calKpi('FFP_POINT_M_ADD', selected_month_R, 'KPI_VALUE_TQ', '', "");
    // 同比=(本期-同期)÷同期×100%
    var m_inc_tb = m_inc_tq > 0 ? Math.round((m_inc-m_inc_tq)/m_inc_tq * 1000)/10 : 0;
    
    var m_inc_sq = calKpi('FFP_POINT_M_ADD', selected_month_R, 'KPI_VALUE_SQ', '', "");
    // 环比=(本期-上期)/上期×100%
    var m_inc_hb = m_inc_sq > 0 ? Math.round((m_inc-m_inc_sq)/m_inc_sq * 1000)/10 : 0;

    $('.block_rt .row2 .col1 .num').text(formatCurrency(m_inc/10000,0));
    $('.block_rt .row2 .col1 .tb .val').text(getTHB(m_inc_tb));
    $('.block_rt .row2 .col1 .hb .val').text(getTHB(m_inc_hb));

    $('.block_rt .row2 .col1 .tb .per').show();
    $('.block_rt .row2 .col1 .hb .per').show();

    if(m_inc_tb > 0){
        $('.block_rt .row2 .col1 .tb .green').show();
        $('.block_rt .row2 .col1 .tb .red').hide();
    }else if(m_inc_tb < 0){
        $('.block_rt .row2 .col1 .tb .green').hide();
        $('.block_rt .row2 .col1 .tb .red').show();
    }else{
        $('.block_rt .row2 .col1 .tb .green').hide();
        $('.block_rt .row2 .col1 .tb .red').hide();
        $('.block_rt .row2 .col1 .tb .per').hide();
    }

    if(m_inc_hb > 0){
        $('.block_rt .row2 .col1 .hb .green').show();
        $('.block_rt .row2 .col1 .hb .red').hide();
    }else if(m_inc_hb < 0){
        $('.block_rt .row2 .col1 .hb .green').hide();
        $('.block_rt .row2 .col1 .hb .red').show();
    }else{
        $('.block_rt .row2 .col1 .hb .green').hide();
        $('.block_rt .row2 .col1 .hb .red').hide();
        $('.block_rt .row2 .col1 .hb .per').hide();
    }

    // 当月消减
    var m_min = calKpi('FFP_POINT_M_MINUS', selected_month_R, 'KPI_VALUE', '', "");

    var m_min_tq = calKpi('FFP_POINT_M_MINUS', selected_month_R, 'KPI_VALUE_TQ', '', "");
    // 同比=(本期-同期)÷同期×100%
    var m_min_tb = m_min_tq > 0 ? Math.round((m_min-m_min_tq)/m_min_tq * 1000)/10 : 0;
    
    var m_min_sq = calKpi('FFP_POINT_M_MINUS', selected_month_R, 'KPI_VALUE_SQ', '', "");
    // 环比=(本期-上期)/上期×100%
    var m_min_hb = m_min_sq > 0 ? Math.round((m_min-m_min_sq)/m_min_sq * 1000)/10 : 0;

    $('.block_rt .row2 .col2 .num').text(formatCurrency(m_min/10000,0));
    $('.block_rt .row2 .col2 .tb .val').text(getTHB(m_min_tb));
    $('.block_rt .row2 .col2 .hb .val').text(getTHB(m_min_hb));

    $('.block_rt .row2 .col2 .tb .per').show();
    $('.block_rt .row2 .col2 .hb .per').show();

    if(m_min_tb > 0){
        $('.block_rt .row2 .col2 .tb .green').show();
        $('.block_rt .row2 .col2 .tb .red').hide();
    }else if(m_min_tb < 0){
        $('.block_rt .row2 .col2 .tb .green').hide();
        $('.block_rt .row2 .col2 .tb .red').show();
    }else{
        $('.block_rt .row2 .col2 .tb .green').hide();
        $('.block_rt .row2 .col2 .tb .red').hide();
        $('.block_rt .row2 .col2 .tb .per').hide();
    }

    if(m_min_hb > 0){
        $('.block_rt .row2 .col2 .hb .green').show();
        $('.block_rt .row2 .col2 .hb .red').hide();
    }else if(m_min_hb < 0){
        $('.block_rt .row2 .col2 .hb .green').hide();
        $('.block_rt .row2 .col2 .hb .red').show();
    }else{
        $('.block_rt .row2 .col2 .hb .green').hide();
        $('.block_rt .row2 .col2 .hb .red').hide();
        $('.block_rt .row2 .col2 .hb .per').hide();
    }

    // 当月过期
    var m_exp = calKpi('FFP_POINT_M_EXPIRE', selected_month_R, 'KPI_VALUE', '', "");

    var m_exp_tq = calKpi('FFP_POINT_M_EXPIRE', selected_month_R, 'KPI_VALUE_TQ', '', '');
    // 同比=(本期-同期)÷同期×100%
    var m_exp_tb = m_exp_tq > 0 ? Math.round((m_exp-m_exp_tq)/m_exp_tq * 1000)/10 : 0;
    
    var m_exp_sq = calKpi('FFP_POINT_M_EXPIRE', selected_month_R, 'KPI_VALUE_SQ', '', '');
    // 环比=(本期-上期)/上期×100%
    var m_exp_hb = m_exp_sq > 0 ? Math.round((m_exp-m_exp_sq)/m_exp_sq * 1000)/10 : 0;

    $('.block_rt .row2 .col3 .num').text(formatCurrency(m_exp/10000,0));
    $('.block_rt .row2 .col3 .tb .val').text(getTHB(m_exp_tb));
    $('.block_rt .row2 .col3 .hb .val').text(getTHB(m_exp_hb));

    $('.block_rt .row2 .col3 .tb .per').show();
    $('.block_rt .row2 .col3 .hb .per').show();

    if(m_exp_tb > 0){
        $('.block_rt .row2 .col3 .tb .green').show();
        $('.block_rt .row2 .col3 .tb .red').hide();
    }else if(m_exp_tb < 0){
        $('.block_rt .row2 .col3 .tb .green').hide();
        $('.block_rt .row2 .col3 .tb .red').show();
    }else{
        $('.block_rt .row2 .col3 .tb .green').hide();
        $('.block_rt .row2 .col3 .tb .red').hide();
        $('.block_rt .row2 .col3 .tb .per').hide();
    }

    if(m_exp_hb > 0){
        $('.block_rt .row2 .col3 .hb .green').show();
        $('.block_rt .row2 .col3 .hb .red').hide();
    }else if(m_exp_hb < 0){
        $('.block_rt .row2 .col3 .hb .green').hide();
        $('.block_rt .row2 .col3 .hb .red').show();
    }else{
        $('.block_rt .row2 .col3 .hb .green').hide();
        $('.block_rt .row2 .col3 .hb .red').hide();
        $('.block_rt .row2 .col3 .hb .per').hide();
    }



    // 语音播报
    function onAudioTplLoad(tplobj){
        
        // {DATE} 总积分{POINT_TOTAL}分，同比{TB1}，环比{HB1}。
        // 当月积分累计{M_POINT}分，同比{TB2}，环比{HB2}。消减{M_POINT_MIS}分，同比{TB3}，环比{HB3}。过期{M_POINT_EXP}分，同比{TB4}，环比{HB4}。
        var tpl = tplobj.txt;
       
        //--
        var date = selected_month_R;
        var datelabel = date.substr(0,4) + '年' + Number(date.substr(4,2)) + '月';
        tpl = tpl.replace(/{DATE}/g, datelabel);
        
        //--
        tpl = tpl.replace(/{POINT_TOTAL}/g, formatCurrency(Math.round(total/10000)*10000, 2));

        //--
        tpl = tpl.replace(/{TB1}/g, ratioTemplete(total_tb));

        //--
        tpl = tpl.replace(/{HB1}/g, ratioTemplete(total_hb));



        //--
        tpl = tpl.replace(/{M_POINT}/g, formatCurrency(Math.round(m_inc/10000)*10000, 2));

        //--
        tpl = tpl.replace(/{TB2}/g, ratioTemplete(m_inc_tb));

        //--
        tpl = tpl.replace(/{HB2}/g, ratioTemplete(m_inc_hb));



        //--
        tpl = tpl.replace(/{M_POINT_MIS}/g, formatCurrency(Math.round(m_min/10000)*10000, 2));

        //--
        tpl = tpl.replace(/{TB3}/g, ratioTemplete(m_min_tb));

        //--
        tpl = tpl.replace(/{HB3}/g, ratioTemplete(m_min_hb));



        //--
        tpl = tpl.replace(/{M_POINT_EXP}/g, formatCurrency(Math.round(m_exp/10000)*10000, 2));

        //--
        tpl = tpl.replace(/{TB4}/g, ratioTemplete(m_exp_tb));

        //--
        tpl = tpl.replace(/{HB4}/g, ratioTemplete(m_exp_hb));



        text2audio(tpl, false);

    }

    //stopAudio();
    getAudioTemplate('A1.2-point', onAudioTplLoad);

}


function calKpi(kpi_code, month, value_type, card_type, cal_type){

    var kpi_id = kpilist[kpi_code];
    var len = member_card_data.length;
    var result = 0;
    var result_prev = 0;
    for(var i=0; i<len; i++){
        var d = member_card_data[i];
        if(cal_type == undefined || cal_type == ""){
            // 当月数据
            if(d.DATE_ID == month && d.KPI_ID == kpi_id && (d.TIER_NAME == card_type || card_type == '')){
                var val = d[value_type];
                result += isNaN(val) ? 0 : Number(val);
            }
        }else if(cal_type == "sum"){
            // 累计到当前月份的总数
            if(Number(d.DATE_ID) <= Number(month) && d.KPI_ID == kpi_id && (d.TIER_NAME == card_type || card_type == '')){
                var val = d[value_type];
                result += isNaN(val) ? 0 : Number(val);
            }
        }else if(cal_type == "diff"){ // 当月-上月数值
            var idx = monthList_L.indexOf(month);
            if(idx < monthList_L.length-1){
                var prev_month = monthList_L[idx+1];
                // 当月数据
                if(Number(d.DATE_ID) == Number(month) && d.KPI_ID == kpi_id && (d.TIER_NAME == card_type || card_type == '')){
                    var val = d[value_type];
                    result += isNaN(val) ? 0 : Number(val);
                }
                // 上月数据
                if(Number(d.DATE_ID) == Number(prev_month) && d.KPI_ID == kpi_id && (d.TIER_NAME == card_type || card_type == '')){
                    var val = d[value_type];
                    result_prev += isNaN(val) ? 0 : Number(val);
                }
            }else{
                result = 0;
            }

        }
        
    }
    if(cal_type == "diff"){
        return result-result_prev;
    }else{
        return result;
    }
    
}









// 会员卡 图表
function setBlockLBKpi(){
    // ---------------------------
    var chart_id = 'chart_lb';
    
    var colors = ['#f74649', '#fff02a', '#00becf'];
    // ---------------------------

    
    var data_s1 = [];
    var data_s2 = [];
    var data_s3 = [];
    var indicator = [];

    var len = cardlist.length;
    for(var i=0; i<len; i++){
        var card = cardlist[i];

        var total = calKpi('FFP_MEMBER_TOTAL', selected_month_L, 'KPI_VALUE', card, "");
        var inc = calKpi('FFP_MEMBER_NEW', selected_month_L, 'KPI_VALUE', card, "");
        var mact = calKpi('FFP_MEMBER_M_ACT', selected_month_L, 'KPI_VALUE', card, "");

        data_s1.push(total);
        data_s2.push(inc);
        data_s3.push(mact);

        indicator.push({ text: card });
    }

    // ---------------------------------------------------------
    // 主KPI图表
    var chart = echarts.init(document.getElementById(chart_id));

    var option = {
        tooltip : {
            show: true,
            trigger: 'item',

            formatter: function (params, ticket, callback) {
                var html = '';
                html += '<div class="chart_tip">';
                html += '<table style="width:100%">'
                html += '<thead>'
                html += '<tr class="r2">'
                html += '<th colspan="2">'+params.name+'</th>'
                html += '</tr>'
                html += '</thead>'
                html += '<tbody>'
                var len = cardlist.length;
                for(var i=0; i<len; i++){
                    var card = cardlist[i];
                    html += '<tr>'
                    html += '<td>'+card+'</td>'
                    html += '<td>'+formatCurrency(params.value[i],0)+'</td>'
                    html += '</tr>'
                }
                html += '</tbody>'
                html += '</table>'
                html += '</div>';

                return html;
             },
            backgroundColor: '',
        },
        legend: {
            orient: 'vertical',
            y:50,
            left: 'right',
            itemWidth: 12,
            itemHeight: 12,
            textStyle:{
              color: '#99ccff',
              fontSize: 14+fontSizeDiff()
            },
            data: ['总量','增量', '月活']
        },
        color: colors,
        radar: [
            {
                indicator: indicator,
                center: ['50%', '53%'],
                radius: '70%',
                startAngle: 90,
                splitNumber: 4,
                min:0,
                shape: 'circle',
                name: {
                    formatter: function (value, indicator) {
                        //console.log(indicator);
                        return value;
                    },
                    textStyle: {
                        color:'rgba(0,0,0,0)', // hide text
                        //fontSize: 18+fontSizeDiff()
                    }
                },
                nameGap: 36,
                axisLabel: {
                    show: true,
                    formatter: function (value, index) {
                        if(value <= 20000){
                            return value;
                        }else{
                            return Math.round(value/10000) + '万';
                        }
                        
                    },
                    margin: 10,
                    textStyle:{
                      fontSize: 14+fontSizeDiff()
                    },
                },
                splitArea: {
                    areaStyle: {
                        color: 'rgba(1,48,166,0.5)'
                    }
                },
                axisLine: {
                    lineStyle: {
                        color: '#43feff'
                    }
                },
                splitLine: {
                    lineStyle: {
                        color: '#43feff'
                    }
                }
            }
        ],
        series: [
            {
                name: '雷达图',
                type: 'radar',

                data: [
                    {
                        value: data_s1,
                        name: '总量',
                        areaStyle: {
                            normal: {
                                opacity: 0.5
                            }
                        },
                        lineStyle: {
                            normal: {
                                opacity: 0.5
                            }
                        }
                    },
                    {
                        value: data_s2,
                        name: '增量',
                        areaStyle: {
                            normal: {
                                opacity: 0.5
                            }
                        },
                        lineStyle: {
                            normal: {
                                opacity: 0.5
                            }
                        }
                    },
                    {
                        value: data_s3,
                        name: '月活',
                        areaStyle: {
                            normal: {
                                opacity: 0.5
                            }
                        },
                        lineStyle: {
                            normal: {
                                opacity: 0.5
                            }
                        }
                    }
                ]
            }
        ]
    }

    chart.setOption(option);
}



// 月消积分 航空、非航占比 图表
function setBlockRB1Kpi(){
    if(member_point_data == undefined){
        setTimeout(setBlockRB1Kpi, 10);
        return;
    }



    var data_s1 = [];
    var data_legend = [];


    // 月消积分航空、非航占比
    var month = selected_month_R;
    var value_type = 'KPI_VALUE';
    var kpi_id = '10110'; //月消积分
    var len = member_point_data.length;
    var result1 = 0; //航空
    var result2 = 0; //非航
    var other_dd;
    var other_lg;
    for(var i=0; i<len; i++){
        var d = member_point_data[i];
        // 当月数据
        if(d.DATE_ID == month && d.KPI_ID == kpi_id){
            var val = d[value_type];
            if(d.PROD_TYPE == '航空'){
                result1 += isNaN(val) ? 0 : Number(val);
            }else if(d.PROD_TYPE == '非航'){
                result2 += isNaN(val) ? 0 : Number(val);
            }
            //
            var tb = isNaN(d.KPI_RATIO_TQ) ? '-' : Math.round(d.KPI_RATIO_TQ*1000)/10;
            var hb = isNaN(d.KPI_RATIO_SQ) ? '-' : Math.round(d.KPI_RATIO_SQ*1000)/10;
            var dd = {
                value: Math.round(d.KPI_VALUE),
                name: d.PROD,
                tb: tb,
                hb: hb,
            };

            if(d.PROD != '其他'){
                data_s1.push(dd);
                data_legend.push({name: d.PROD, icon: 'circle'});
            }else{
                other_dd = dd;
                other_lg = {name: d.PROD, icon: 'circle'};
            }
            
        }
    }
    if(other_dd != undefined){
        data_s1.push(other_dd);
        data_legend.push(other_lg);
    }

    if(result1 > 0 && result2 > 0){
        var per1 = Math.round(result1/(result1+result2)*1000)/10;
        var per2 = Math.round(result2/(result1+result2)*1000)/10;
        $('.block_rb .tabc1 .blk1 .val').text(per1);
        $('.block_rb .tabc1 .blk2 .val').text(per2);
        $('.block_rb .tabc1 .blk1 .sub').show();
        $('.block_rb .tabc1 .blk2 .sub').show();
    }else{
        $('.block_rb .tabc1 .blk1 .val').text('-');
        $('.block_rb .tabc1 .blk2 .val').text('-');
        $('.block_rb .tabc1 .blk1 .sub').hide();
        $('.block_rb .tabc1 .blk2 .sub').hide();
    }


    // ---------------------------
    var chart_id = 'chart_rb1';
    
    var colors = ['#e95551', '#f7a449', '#fff353', '#21b3ce', '#176ebf', '#0a3a89', '#8619db', '#aa44e1'];
    // ---------------------------



    // ---------------------------------------------------------
    // 主KPI图表
    var chart = echarts.init(document.getElementById(chart_id));

    var option = {

        tooltip : {
            trigger: 'item',
            formatter: function (params, ticket, callback) {
                var html = '';
                html += '<div class="chart_tip">';
                html += '<div class="tit">'+params.name+'</div>';
                html += '<div class="r1"><span class="c1">占比</span><span class="c2">'+params.percent+'%</span></div>';
                html += '<div class="r1"><span class="c1">兑换量</span><span class="c2">'+formatCurrency(Math.round(params.value/10000),0)+'万分</span></div>';
                if(params.data.tb > 0){
                    //html += '<div class="r1"><span class="c1">同比</span><span class="c2">'+params.data.tb+'%</span><span class="green">↑</span></div>';
                }else if(params.data.tb < 0){
                    //html += '<div class="r1"><span class="c1">同比</span><span class="c2">'+params.data.tb+'%</span><span class="red ">↓</span></div>';
                }else{
                    //html += '<div class="r1"><span class="c1">同比</span><span class="c2">-</span></div>';
                }
                if(params.data.hb > 0){
                    html += '<div class="r1"><span class="c1">环比</span><span class="c2">'+params.data.hb+'%</span><span class="green">↑</span></div>';
                }else if(params.data.hb < 0){
                    html += '<div class="r1"><span class="c1">环比</span><span class="c2">'+params.data.hb+'%</span><span class="red ">↓</span></div>';
                }else{
                    html += '<div class="r1"><span class="c1">环比</span><span class="c2">-</span></div>';
                }
                html += '</div>';

                return html;
             },
             backgroundColor: '',

        },
        legend: {
            orient: 'vertical',
            y:19,
            left: 'left',
            itemWidth: 8,
            itemHeight: 8,
            textStyle: {
                color: '#99ccff',
            },
            data: data_legend
        },
        color: colors,
        series : [
            {
                name:'',
                type:'pie',
                radius : ['20%', '70%'],
                center: ['60%', '50%'],
                data: data_s1,
                //roseType: 'angle',
                clockwise: false,
                label: {
                    normal: {
                        formatter: '{d}%',
                        textStyle: {
                            fontSize: 11+fontSizeDiff()
                        }
                    }
                },
                labelLine: {
                    normal: {
                        smooth: 0.2,
                        length: 12,
                        length2: 8
                    }
                },
                

                animationType: 'scale',
                animationEasing: 'elasticOut',
                animationDelay: function (idx) {
                    return Math.random() * 200;
                }
            }
        ]
    };

    chart.setOption(option);
}


// 月消积分 会员等级分布 图表
function setBlockRB2Kpi(){
    // ---------------------------
    var chart_id = 'chart_rb2';
    
    var colors = ['#e95551', '#f7a449', '#fff353', '#21b3ce', '#176ebf', '#0a3a89', '#8619db', '#aa44e1'];
    // ---------------------------


    var data_s1 = [];
    var data_legend = [];


    // 月消积分航空、非航占比
    var month = selected_month_R;
    var value_type = 'KPI_VALUE';
    var kpi_id = '10110'; //月消积分
    var len = member_card_data.length;

    for(var i=0; i<len; i++){
        var d = member_card_data[i];
        // 当月数据
        if(d.DATE_ID == month && d.KPI_ID == kpi_id){
            var val = d[value_type];
            //
            var tb = isNaN(d.KPI_RATIO_TQ) ? '-' : Math.round(d.KPI_RATIO_TQ*1000)/10;
            var hb = isNaN(d.KPI_RATIO_SQ) ? '-' : Math.round(d.KPI_RATIO_SQ*1000)/10;
            var dd = {
                value: Math.round(d.KPI_VALUE),
                name: d.TIER_NAME,
                tb: tb,
                hb: hb,
            };

            data_s1.push(dd);
            data_legend.push({name: d.TIER_NAME, icon: 'circle'});
            
        }
    }


    // ---------------------------------------------------------
    // 主KPI图表
    var chart = echarts.init(document.getElementById(chart_id));

    var option = {

        tooltip : {
            trigger: 'item',
            formatter: function (params, ticket, callback) {
                var html = '';
                html += '<div class="chart_tip">';
                html += '<div class="tit">'+params.name+'</div>';
                html += '<div class="r1"><span class="c1">占比</span><span class="c2">'+params.percent+'%</span></div>';
                html += '<div class="r1"><span class="c1">兑换量</span><span class="c2">'+formatCurrency(Math.round(params.value/10000),0)+'万分</span></div>';
                if(params.data.tb > 0){
                    //html += '<div class="r1"><span class="c1">同比</span><span class="c2">'+params.data.tb+'%</span><span class="green">↑</span></div>';
                }else if(params.data.tb < 0){
                    //html += '<div class="r1"><span class="c1">同比</span><span class="c2">'+params.data.tb+'%</span><span class="red ">↓</span></div>';
                }else{
                    //html += '<div class="r1"><span class="c1">同比</span><span class="c2">-</span></div>';
                }
                if(params.data.hb > 0){
                    html += '<div class="r1"><span class="c1">环比</span><span class="c2">'+params.data.hb+'%</span><span class="green">↑</span></div>';
                }else if(params.data.hb < 0){
                    html += '<div class="r1"><span class="c1">环比</span><span class="c2">'+params.data.hb+'%</span><span class="red ">↓</span></div>';
                }else{
                    html += '<div class="r1"><span class="c1">环比</span><span class="c2">-</span></div>';
                }
                html += '</div>';

                return html;
             },
             backgroundColor: '',

        },
        legend: {
            orient: 'vertical',
            y:50,
            left: 'left',
            itemWidth: 8,
            itemHeight: 8,
            textStyle: {
                color: '#99ccff',
            },
            data: data_legend
        },
        color: colors,
        series : [
            {
                name:'',
                type:'pie',
                radius : ['17%', '60%'],
                center: ['55%', '50%'],
                data: data_s1,
                //roseType: 'angle',
                clockwise: false,
                label: {
                    normal: {
                        formatter: '{b} {d}%',
                        textStyle: {
                            fontSize: 11+fontSizeDiff()
                        }
                    }
                },
                labelLine: {
                    normal: {
                        smooth: 0.2,
                        length: 12,
                        length2: 8
                    }
                },
                

                animationType: 'scale',
                animationEasing: 'elasticOut',
                animationDelay: function (idx) {
                    return Math.random() * 200;
                }
            }
        ]
    };

    chart.setOption(option);
}





// 月份选择器 会员
function selectMonthMember(id){
    var top = 80 * id;

    selected_month_L = $('#member_slider .node'+id).attr('data-month');

    $('#member_slider .cursor').css('top', top+'px');
    $('#member_slider .node .label').removeClass('selected');
    $('#member_slider .node'+id + ' .label').addClass('selected');

    stopAudio();
    clearAudioQueue();

    setBlockTLKpi();
    setBlockLBKpi();

    setTitleDate();

    setExtLink();
}

$('#member_slider .node').on('click', function(evt){
    var id = $(this).attr('node-id');


    selectMonthMember(id);
    selectMonthPoint(id);
})




// 月份选择器 积分
function selectMonthPoint(id){
    var top = 80 * id;

    selected_month_R = $('#point_slider .node'+id).attr('data-month');

    $('#point_slider .cursor').css('top', top+'px');
    $('#point_slider .node .label').removeClass('selected');
    $('#point_slider .node'+id + ' .label').addClass('selected');

    stopAudio();
    clearAudioQueue();

    setBlockTRKpi();
    setBlockRB1Kpi();
    setBlockRB2Kpi();

}

$('#point_slider .node').on('click', function(evt){
    var id = $(this).attr('node-id');


    selectMonthMember(id);
    selectMonthPoint(id);
})




//切换tab
var selected_tab = 1;
$('.block_rb .tab').on('click', function(evt){
    var id = $(this).attr('tab-id');
    selectTab(id);
    
})


function selectTab(id){
    $('.block_rb .tab').removeClass('selected');
    $('.block_rb .tab'+id).addClass('selected');
    $('.block_rb .tabc').hide();
    $('.block_rb .tabc'+id).show();

    if(id == 1){
        selected_tab = 1;
        setBlockRB1Kpi()
    }else{
        selected_tab = 2;
        setBlockRB2Kpi()
    }

    clearTimeout(itv_autoSwitchTab);
    itv_autoSwitchTab = setTimeout(autoSwitchTab, 10000);
}


function autoSwitchTab(){

    if(!autoSwitch){
        itv_autoSwitchTab = setTimeout(autoSwitchTab, 10);
        return;
    }

    if(selected_tab == 1){
        selectTab(2)
    }else{
        selectTab(1)
    }

}

// 自动循环切换两个TAB
var itv_autoSwitchTab;
clearTimeout(itv_autoSwitchTab);
itv_autoSwitchTab = setTimeout(autoSwitchTab, 10000);








// 会员卡 tootip
$('.block_lb .card').powerTip({
    fadeInTime: 50,
    fadeOutTime: 50,
    followMouse: true,
    offset: 20,
    manual: true,
});

// 雷达图，鼠标移到卡上的tooltip
$('.block_lb .card').data('powertip', function() {

    var id = $(this).attr('data-id');
    var card = cardlist[id];

    var total = calKpi('FFP_MEMBER_TOTAL', selected_month_L, 'KPI_VALUE', '', "");
    var inc = calKpi('FFP_MEMBER_NEW', selected_month_L, 'KPI_VALUE', '', "");
    var mact = calKpi('FFP_MEMBER_M_ACT', selected_month_L, 'KPI_VALUE', '', "");

    var card_total = calKpi('FFP_MEMBER_TOTAL', selected_month_L, 'KPI_VALUE', card, "");
    var card_inc = calKpi('FFP_MEMBER_NEW', selected_month_L, 'KPI_VALUE', card, "");
    var card_mact = calKpi('FFP_MEMBER_M_ACT', selected_month_L, 'KPI_VALUE', card, "");


    var total_val_sq = calKpi('FFP_MEMBER_TOTAL', selected_month_L, 'KPI_VALUE_SQ', card, "");
    // 环比=(本期-上期)/上期×100%
    //var total_hb = total_val_sq > 0 ? Math.round((total-total_val_sq)/total_val_sq * 1000)/10 : 0;
    var total_hb = calKpi('FFP_MEMBER_TOTAL', selected_month_L, 'KPI_RATIO_SQ', card, "");
    var inc_hb = calKpi('FFP_MEMBER_NEW', selected_month_L, 'KPI_RATIO_SQ', card, "");
    var mact_hb = calKpi('FFP_MEMBER_M_ACT', selected_month_L, 'KPI_RATIO_SQ', card, "");

    total_hb = Math.round(total_hb*10000)/100;
    inc_hb = Math.round(inc_hb*10000)/100;
    mact_hb = Math.round(mact_hb*10000)/100;


    var html = '';

    html += '<div class="chart_tip">';
    html += '<table>'
    html += '<thead>'
    html += '<tr class="r2">'
    html += '<th></th>'
    html += '<th class="blue_ll">总量</th>'
    html += '<th class="blue_ll">增量</th>'
    html += '<th class="blue_ll">月活</th>'
    html += '</tr>'
    html += '</thead>'
    html += '<tbody>'
    html += '<tr>'
    html += '<td></td>'
    html += '<td>'+formatCurrency(card_total,0)+'<span class="fs10">张卡</span></td>'
    html += '<td>'+formatCurrency(card_inc,0)+'<span class="fs10">张卡</span></td>'
    html += '<td>'+formatCurrency(card_mact,0)+'<span class="fs10">人</span></td>'
    html += '</tr>'
    html += '<tr class="r2">'
    html += '<td class="blue_ll">占比</td>'
    html += '<td>'+(Math.ceil(card_total/total*10000)/100)+'<span class="fs10">%</span></td>'
    html += '<td>'+(Math.ceil(card_inc/inc*10000)/100)+'<span class="fs10">%</span></td>'
    html += '<td>'+(mact > 0 ? Math.ceil(card_mact/mact*10000)/100 : 0)+'<span class="fs10">%</span></td>'
    html += '</tr>'
    html += '<tr>'
    html += '<td class="blue_ll">环比</td>'
    if(total_hb > 0){
        html += '<td>'+total_hb+'<span class="fs10">%</span><span class="green">↑</span></td>'
    }else if(total_hb < 0){
        html += '<td>'+total_hb+'<span class="fs10">%</span><span class="red">↓</span></td>'
    }else{
        html += '<td>-</td>'
    }
    //

    if(inc_hb > 0){
        html += '<td>'+inc_hb+'<span class="fs10">%</span><span class="green">↑</span></td>'
    }else if(inc_hb < 0){
        html += '<td>'+inc_hb+'<span class="fs10">%</span><span class="red">↓</span></td>'
    }else{
        html += '<td>-</td>'
    }
    //

    if(mact_hb > 0){
        html += '<td>'+mact_hb+'<span class="fs10">%</span><span class="green">↑</span></td>'
    }else if(mact_hb < 0){
        html += '<td>'+mact_hb+'<span class="fs10">%</span><span class="red">↓</span></td>'
    }else{
        html += '<td>-</td>'
    }

    html += '</tr>'
    html += '</tbody>'
    html += '</table>'
    html += '</div>';
    return html;
});

$('.block_lb .card').on('mouseover', function(evt){
    $.powerTip.show($(this));
});
$('.block_lb .card').on('mouseout', function(evt){
    $.powerTip.hide();
});




// ---------------------- 

function onCompanyChanged(comp_code){
    current_company_code = comp_code;
    checkCompanyReay();
}

function checkCompanyReay(){
    if(companyCode2Name[current_company_code] == undefined){
        setTimeout(checkCompanyReay,0);
    }else{
        //updateAllKpi();
    }
}




getMemberKpi();



function setTitleDate(){
    var date = selected_month_L;
    var datelabel = date.substr(0,4) + '年' + Number(date.substr(4,2)) + '月';
    $('.pagetitle .maintitle .date').html(datelabel);// 页面标题-时间
}





// HBI 外部跳转链接
function checkUserInfoLoaded(){
    if(!usersCompayCodeList || usersCompayCodeList.length == 0){
        setTimeout(checkUserInfoLoaded, 10);
        return;
    }

    if(usersCompayCodeList.indexOf(parent_company) > -1){
        $('#ext_link1').show();

    }
}
function setExtLink(){

    $('#ext_link1').attr('href', encodeURI('http://cdp.hnair.net:8080/HBI/rdThird.do?res_id=149904341051896611&paramArr=1日期类型=<月>::2日期=<'+selected_month_L+'>::3类别=<合计>::4是否调账=<调账后>'));
    
}
checkUserInfoLoaded();
regTooltip('.ext_link', '查看关联报表');










