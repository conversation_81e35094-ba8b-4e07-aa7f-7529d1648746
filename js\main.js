var companylist = [];
var companyCode2Name = {};
var companyCode2Sort = {};
var selected_company_code = 'HNAHK';
var companyColors = ['#1fb3ff', '#7d5aee', '#f06234', '#008e01', '#ffaf3d', '#54c6ac', '#f06eaa', '#5379c0', '#a7c835', '#1fb3aa', '#7d5acc', '#f06287', '#008e91', '#ffaf5c', '#54c6ef', '#f06edd', '#5379e6', '#a7c876'];
var companyCode2Color = {};



function getCompany() {
  var data = '';

  $.ajax({
    type: 'POST',
    url: '/bi/query/company',
    async: true,
    dataType: 'json',
    data: data,
    success: function(response) {

      checkLogin(response);

      var html = '';

      companylist = response.comp;
 

      companylist.sort(function(a, b) {
        return a.sort - b.sort
      });
      var len = companylist.length;
      for (var i = 0; i < len; i++) {
        
        var dat = companylist[i];
        companyCode2Name[dat.code] = dat.name;
        companyCode2Sort[dat.code] = dat.sort;
        if (i != 0) {
          companyCode2Color[dat.code] = companyColors[i - 1];
        } else {
          companyCode2Color[dat.code] = '#e60000';
        }

        if (usersCompanyList.indexOf(dat.id) > -1) {
          html += '<div class="itm" code="' + dat.code + '" comp_id="' + dat.id + '">' + dat.name + '</div>';
        }


        if (dat.code == 'HNAHK') {
          $('#companycombo .box').text(dat.name);
          $('#companycombo').attr('code', dat.code);
          $('#companycombo').attr('comp_id', dat.id);
        }
      }

      $('#companylist').html(html);


      // 选择公司

      $('#companycombo .box').on('click', function() {
        if ($('#companylist').is(':visible')) {
          $('#companylist').hide();
        } else {
          $('#companylist').slideDown('200', function() {});
        }
      });

      $('#companylist .itm').on('click', function() {
        $('#companylist').hide();
        var code = $(this).attr('code');
        switchCompany(code);

        stopAutoSwitchCompany();
        autoSwitchCompany();

      });

      $('#companycombo').on('mouseleave', function() {
        $('#companylist').hide();
      });



    },
    error: function(e) {
      console.log('ajax error');
      console.log(e);
    }
  });
}



var intervalSwitchCompany;

function autoSwitchCompany() {
  intervalSwitchCompany = setInterval(switchToNextCompany, 15000);
}

function stopAutoSwitchCompany() {
  clearInterval(intervalSwitchCompany);
}

function switchToNextCompany() {
  var len = companylist.length;
  for (var i = 0; i < len; i++) {
    
    var dat = companylist[i];
    if (usersCompanyList.indexOf(dat.id) > -1) {
      if (dat.code == selected_company_code) {
        var nextcode;
        if (i < len - 1) {
          nextcode = companylist[i + 1].code;
        } else {
          nextcode = companylist[0].code;
        }
        switchCompany(nextcode);
        break;
      }
    }

  }
}

function switchCompany(code) {

  selected_company_code = code;

  var len = companylist.length;

  for (var i = 0; i < len; i++) {
    
    var dat = companylist[i];
    if (dat.code == code) {
      $('#companycombo .box').text(dat.name);
      break;
    }
  }

  $('#companycombo').attr('code', code);


  if ($('#mainframe')[0] && $('#mainframe')[0].contentWindow && $('#mainframe')[0].contentWindow.onCompCodeChanged) {
    $('#mainframe')[0].contentWindow.onCompCodeChanged(code);
  }

  onCompanyChanged(code);
}


function checkFrameReady() {
  if ($('#mainframe')[0] && $('#mainframe')[0].contentWindow && $('#mainframe')[0].contentWindow.main) {
    $('#mainframe')[0].contentWindow.main(companylist);
  } else {
    setTimeout(checkFrameReady, 10);
  }
}

/*
// 获得web url安全码
function getUrlSafeCode(){
  var data = '{"mode":"getsafe","url":"http://vis.hnair.net:8288/map/map3.html?company=HU"}';

  $.ajax({
    type: 'POST',
    url: '/bi/app/safe',
    async: true,
    dataType: 'json',
    data: data,
    success: function(response) {
      console.log(response);
      //http://vis.hnair.net:8288/map/map3.html?company=HU&safe=hyCpH0g0aIwEs3WQ0F4MwpiPtSytolhzbEyN6sVstcPZz9liwCWqeXD6CnJ/AF65hh21sGA2ZSjer4Fd5Zb4W+3oBVdUiUo9j68l4PIIcYE=
      
      getFoc(response.safe);

    },
    error: function(e) {
      console.log('ajax error');
      console.log(e);
    }
  });
}
getUrlSafeCode();
*/


function getFoc() {

  var param = {
    'mode': 'detailcinfo',
    'COMP_CODE': '',
    'AIRPORT_CODE': 'HAK',
  }

  /*
    var param = {
      'mode': 'statisticsinfo',
      'COMP_CODE': '',
    }
  */

  $.ajax({

    type: 'post',
    url: "/bi/query/focflightcache",
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {
      console.log('focflightcache');
      console.log(response);
    },
    error: function() {}
  });
}

function getKpi() {

  /*
    var param = {
      'SOLR_CODE': 'FAC_COMP_ARP_NORMAL_RATE_KPI',
      'COMP_CODE': 'HU,HNAHK',
      'KPI_CODE': 'SCH_NO,EXCUTED_NO,DELAY_NO,NORMAL_NO_T,ABNORMAL_NO',
      'DATE_TYPE': 'D',
      'LIMIT': '1'
    }
  */

  var param = {
    'SOLR_CODE': 'FAC_COMP_KPI',
    'COMP_CODE': 'HU,HNAHK',
    'KPI_CODE': 'EST_INC_FUEL,',
    'DATE_TYPE': 'D,W',
    "OPTIMIZE": 1,
    'LIMIT': '10'
  }


  $.ajax({

    type: 'post',
    url: "/bi/query/getkpi",
    contentType: 'application/json',
    dataType: 'json',
    async: false,
    //data: JSON.stringify(param),
    data: '{"SOLR_CODE":"FAC_COMP_KPI","COMP_CODE":"HU,GS,HX,8L,PN,FU,UQ,GX,JD","KPI_CODE":"TRV_NUM,RATE,EST_INC_FUEL,INC_TASK,TRV_RATE","VALUE_TYPE":"kpi_value_d","DATE_TYPE":"M","LIMIT":6}',
    success: function(response) {
      console.log('getKpi');
      console.log(response);
    },
    error: function() {}
  });

}


$(window).resize(function() {
  //setPageScale();
  location.reload();
});


var pageZoomScale = 1;

function setPageScale() {
  var wapperWidth = 1366;
  var wapperHeight = 768;
  var osw = document.body.offsetWidth;
  var osh = window.innerHeight;

  var scale = pageZoomScale = osw / 1366;

  $('#header').css('zoom', scale);
  $('.page-wrapper').css('zoom', scale);

  // for firefox
  $('#header').css('-moz-transform', 'scale(' + scale + ',' + scale + ')');
  $('#header').css('-moz-transform-origin', 'left top');
  $('.page-wrapper').css('-moz-transform', 'scale(' + scale + ',' + scale + ')');
  $('.page-wrapper').css('-moz-transform-origin', 'left top');


  var scaleInvert = 1 / scale;
  $('.chartblock').css('zoom', scaleInvert);

  // firefox
  $('.chartblock').css('-moz-transform', 'scale(' + scaleInvert + ',' + scaleInvert + ')');
  $('.chartblock').css('-moz-transform-origin', 'left top');

  $('.chartblock').each(function(index, el) {
    $(this).css('width', $(this).attr('prop-width') * scale + 'px');
    $(this).css('height', $(this).attr('prop-height') * scale + 'px');
  });

  var left = 0;
  var top = 0;
  var width = osw;
  var height = width * (wapperHeight / wapperWidth);
  $('.mainframe').attr('style', 'left:' + left + 'px; ' + 'top:' + top + 'px; ' + 'width:' + width + 'px; ' + 'height:' + height + 'px');

}



var hash = window.location.hash.substr(1);
var urlHashMap = [
  'run-flight',
  'marketing-overview',
  'a-2-1',
  'a-2-2-1',
  'a-2-2-2',
];

// 加载页面
if (hash.length > 0 && urlHashMap.indexOf(hash) > -1) {
  goPage(hash);
} else {
  goPage('run-flight');
}



function goPage(name) {
  window.location.hash = name;

  $('#nav .menu .lv1').removeClass('selected');

  $('#nav .menu .lv2 .itm').each(function(index, el) {
    var page = $(this).attr('page');
    if (page == name) {
      $(this).parent().parent().addClass('selected');
    }
  });

  if (companylist.length > 0) {
    stopAutoSwitchCompany();
    switchCompany(companylist[0].code);
  }

  // 加载页面
  $('#mainframe').attr('src', '');

  if ($("#page")) {
    $("#page").html('');

    $("#page").load(name + ".html", function() {
      checkFrameReady()
    });
  }

}

$('#nav .menu .lv1').on('mouseover', function() {
  $(this).find('.lv2').show();
});
$('#nav .menu .lv1').on('mouseout', function() {
  $(this).find('.lv2').hide();
});
$('#nav .menu .lv2 .itm').on('click', function() {
  var page = $(this).attr('page');
  if (page) {
    goPage(page);
    $(this).parent().hide();

  }
});

///
$('#ctrl_user').on('click', function() {
  logout();
});
$('#ctrl_user').powerTip({
  followMouse: true,
  offset: 10,
  manual: true,
});
$('#ctrl_user').data('powertip', function() {
  var html = '';
  html += '<div style="background:#FF0000;">退出登录</div>';
  return html;
});
$('#ctrl_user').on('mouseover', function(evt) {
  $.powerTip.show($(this));
});
$('#ctrl_user').on('mouseout', function(evt) {
  $.powerTip.hide();
});

/////
$('#ctrl_video').on('click', function() {

});
$('#ctrl_video').powerTip({
  followMouse: true,
  offset: 10,
  manual: true,
});
$('#ctrl_video').data('powertip', function() {
  var html = '';
  html += '<div style="background:#FF0000;">介绍视频</div>';
  return html;
});
$('#ctrl_video').on('mouseover', function(evt) {
  $.powerTip.show($(this));
});
$('#ctrl_video').on('mouseout', function(evt) {
  $.powerTip.hide();
});


setPageScale();
getCompany();