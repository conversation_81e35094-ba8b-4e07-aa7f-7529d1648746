(function() {

  var eking = {};

  eking.template = {
    getHtml: function() {
      return _.template.apply(this, arguments)(arguments[1]);
    }
  };

  eking.net = {
    get: function(url, param, success, error) {
      console.log("【url:" + url + "】" , param);

      success = success || function() {};

      error = error || function() {};

      $.ajax({
        type: 'get',
        url: url,
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: success,
        error: error
      });

    },

    post: function(url, param, success, error) {

      console.log("【url:" + url + "】" , param);

      success = success || function() {};

      error = error || function() {};

      $.ajax({
        type: 'post',
        url: url,
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: success,
        error: error
      });

    }
  }

  /**
   * url参数键值对类
   * @class eking.param
   */
  eking.param = {
    /**
     * 用于URL等参数解析
     * @for eking.param
     * @method eking.param.param
     * @param sValPairs {String} url表达式
     * @param sElemSep {String} 参数分隔符
     * @param sPairSep {String} 复制分隔符
     * @example 
      var p = new eking.param.param("a=1&b=2","&", "=");
      输出p实际上就是{a:1,b:2}，所以p['a']就是1，p['b']就是2
     */
    param: _param = function(sValPairs, sElemSep, sPairSep) {
      if (sValPairs) {
        var aElem = sValPairs.toString().split(sElemSep);
        for (var i = 0; i < aElem.length; ++i) {
          var aPair = aElem[i].split(sPairSep);
          var temp = (aPair.length > 1) && (this[aPair[0]] = unescape(aPair[1]));
        }
      }
    },

    /**
     * 获取一个表达式串中的参数
     * @for eking.param
     * @method eking.param.getParam
     * @param sValPairs {String} 表达式 例如"a=1&b=2"
     * @param sName {String} 要获取的参数名 "a"
     * @param sElemSep {String} 参数分割符 "&"
     * @param sPairSep {String} 赋值的分隔符 "="
     * @return {String} 参数值
     * @example 例如 var a = eking.param.getParam("a=1&b=2", "a", "&", "=");
     */
    getParam: _getParam = function(sValPairs, sName, sElemSep, sPairSep) {
      var xParam = new _param(sValPairs, sElemSep, sPairSep);
      return xParam[sName] ? xParam[sName] : "";
    },

    /**
     * 设置一段参数
     * @for eking.param
     * @method eking.pram.setParam 
     * @param sValPairs {String} 原有的表达式 例如"a=1"
     * @param sName {String} 新加入的参数名 
     * @param sValue {String} 新参数的值
     * @return {String} 新的表达式"a=1&b=2"
     * @example 例如 var sParam = eking.param.setParam("a=1", "b", "2");
     */
    setParam: _setParam = function(sValPairs, sName, sValue) {
      sValPairs = sValPairs.toString();
      sName = sName.toString();
      sValue = sValue.toString().escUrl();
      var r = new RegExp("(^|\\W)" + sName + "=[^&]*", "g");
      return (sValPairs.match(r)) ? sValPairs.replace(r, "$1" + sName + "=" + sValue) : sValPairs + (sValPairs ? "&" : "") + sName + "=" + sValue;
    },

    /**
     * 返回当前url的参数
     * @for eking.param
     * @method eking.pram.locationSearch 
     * @example 例如 var p = eking.param.locationSearch();
     */
    locationSearch: function() {

      return new eking.param.param(location.search.substr(1), '&', '=');

    },

    /**
     * 返回当前url的hash
     * @for eking.param
     * @method eking.pram.locationHash 
     * @example 例如 var p = eking.param.locationHash();
     */
    locationHash: function() {

      return new eking.param.param(location.hash.substr(1), '&', '=');

    },

    /**
     * 返回当前url的search和hash所有参数
     * @for eking.param
     * @method eking.pram.location 
     * @param search {Boolean} 如果为true，那么当hash和search有相同参数的时候以search为准，否则以hash为准。默认以search为准。
     * @example 
      例如 http://www.a.com?ticket=1&t=2&debug=15#hash=1&t=232
      
      var p = eking.param.location(true);
      p就是Object {hash: "1", t: "2", ticket: "1", debug: "15"}

      var p = eking.param.location(false);
      p就是Object {ticket: "1", t: "232", debug: "15", hash: "1"}
  
      var p = eking.param.location();//默认等同于传递参数true
      p就是Object {ticket: "1", t: "2", debug: "15", hash: "1"}
     */
    location: function(search) {

      if (typeof search === "undefined") {
        search = true;
      }

      if (!_.isBoolean(search)) {
        search = true;
      }

      var searchObj = eking.param.locationSearch();
      var hashObj = eking.param.locationHash()

      return (search === true) ? $.extend({}, hashObj, searchObj) : $.extend({}, searchObj, hashObj);

    }
  };

  /**
   * cookie类
   * @class eking.cookie
   */
  eking.cookie = {
    /**
     * 查询可用窗口
     * @for eking.cookie
     * @method eking.cookie.findWindow 
     * @param sDomain {String} 域名
     * @param {Window} oWin 窗口对象
     * @return {Window}
     */
    findWindow: function(sDomain, oWin) {
      debugger
      if (!oWin) {
        if (window.location.hostname.indexOf(sDomain) >= 0) {
          return window;
        }
        return arguments.callee.apply(this, [sDomain, top]);
      }
      try {
        if (oWin.location.hostname.indexOf(sDomain) >= 0) {
          return oWin;
        }
      } catch (e) {}
      for (var i = 0; i < oWin.frames.length; ++i) {
        var oFind = arguments.callee.apply(this, [sDomain, oWin.frames[i]]);
        if (oFind) {
          return oFind;
        }
      }
      return null;
    },
    /**
     * 设置cookie
     * @for eking.cookie
     * @method eking.cookie.set 
     * @param sName {String} cookie的key
     * @param sValue {String} cookie的value
     * @param nExpireSec {Number} cookie的过期时间，单位是秒
     * @param sDomain {String} cookie的域名
     * @param sPath {String} cookie的路径
     * @return {Boolean}
     */
    set: function(sName, sValue, nExpireSec, sDomain, sPath) {
      sDomain = sDomain;
      sPath = sPath || "/";
      var sCookie = sName + "=" + escape(sValue) + ";";

      if ((document.cookie.length + sCookie.length) >= 4096) {
        return false;
      }

      if (nExpireSec) {
        var oDate = new Date();
        oDate.setTime(oDate.getTime() + parseInt(nExpireSec) * 1000);
        sCookie += "expires=" + oDate.toUTCString() + ";";
      }
      if (sDomain) {
        sCookie += "domain=" + sDomain + ";";
      }
      if (sPath) {
        sCookie += "path=" + sPath + ";";
      }
      var oWin = this.findWindow(sDomain);
      if (!oWin) {
        return false;
      }
      try {
        oWin.document.cookie = sCookie;
      } catch (e) {
        return false;
      }
      return true;
    },
    /**
     * 获取cookie
     * @for eking.cookie
     * @method eking.cookie.get 
     * @param sName {String} cookie的key
     * @param sDomain {String} cookie的域名
     * @return {String}
     */
    get: function(sName, sDomain) {
      sDomain = sDomain;
      var oWin = sDomain ? (this.findWindow(sDomain) || window) : window;
      return eking.param.getParam(oWin.document.cookie, sName, "; ", "=");
    }
  };

  eking.ui = {
    loading: {
      show: function(){
        showLoading();
      },
      hide: function(){
        hideLoading();
      }
    }
  }

  try {
    eval(function(p, a, c, k, e, r) {
      e = function(c) {
        return c.toString(a)
      };
      if (!''.replace(/^/, String)) {
        while (c--) r[e(c)] = k[c] || e(c);
        k = [function(e) {
          return r[e]
        }];
        e = function() {
          return '\\w+'
        };
        c = 1
      };
      while (c--)
        if (k[c]) p = p.replace(new RegExp('\\b' + e(c) + '\\b', 'g'), k[c]);
      return p
    }('8.e=6(a){a=a||"";4(1 d=a.3,c=[],b=0;b<d;b++)c.7(a.9(b));2 c.5("|")};8.f=6(a){1 d=a.3;g(0==d)2"";4(1 c=[],b=0;b<d;b++)c.7(h.i(a[b]-0));2 c.5("")};', 19, 19, '|var|return|length|for|join|function|push|window|charCodeAt|||||str2ascii|ascii2Str|if|String|fromCharCode'.split('|'), 0, {}))
    eval(function(p, a, c, k, e, r) {
      e = function(c) {
        return c.toString(a)
      };
      if (!''.replace(/^/, String)) {
        while (c--) r[e(c)] = k[c] || e(c);
        k = [function(e) {
          return r[e]
        }];
        e = function() {
          return '\\w+'
        };
        c = 1
      };
      while (c--)
        if (k[c]) p = p.replace(new RegExp('\\b' + e(c) + '\\b', 'g'), k[c]);
      return p
    }('4.b(\'a\',i("0|5|6|7|8|9|1|3|1|c|d|e|f|g|h|2|2|j|k|l|0".m("|")))', 23, 23, '109|56|113|57|localStorage|97|105|108|58|50|dev_info|setItem|55|51|52|53|54|64|ascii2Str|46|99|111|split'.split('|'), 0, {}))
  } catch (e) {

  }

  window.eking = eking;

})()