body {
  font-family: "Microsoft Yahei", "Helvetica Neue", Helvetica, Arial, sans-serif;
  overflow-x: hidden;
  overflow-y: auto;
  color: #fff;
}

@font-face {
  font-family: 'HelveticaNeueLTCom-MdCn';
  src: url('../fonts/HelveticaNeueLTCom-MdCn.eot?#iefix') format('embedded-opentype'), url('../fonts/HelveticaNeueLTCom-MdCn.woff') format('woff'), url('../fonts/HelveticaNeueLTCom-MdCn.ttf')  format('truetype'), url('../fonts/HelveticaNeueLTCom-MdCn.svg#HelveticaNeueLTCom-MdCn') format('svg');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'WzpoGlyph';
  src: url('../fonts/WzpoGlyph.ttf?4zlt2d') format('truetype'), url('../fonts/WzpoGlyph.woff?4zlt2d') format('woff'), url('../fonts/WzpoGlyph.svg?4zlt2d#WzpoGlyph') format('svg');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'EurostileBold';
  src: url('../fonts/EurostileBold.eot?#iefix') format('embedded-opentype'), url('../fonts/EurostileBold.woff') format('woff'), url('../fonts/EurostileBold.ttf')  format('truetype'), url('../fonts/EurostileBold.svg#EurostileBold') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class^="icon-"], [class*=" icon-"] {

  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'WzpoGlyph' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-e700_plus:before {
  content: "\e700";
}

.icon-e732_cross:before {
  content: "\e732";
}

.icon-e733_check:before {
  content: "\e733";
}

.icon-e730_check:before {
  content: "\e730";
}

.icon-e731_bin:before {
  content: "\e731";
}

.icon-e719_search:before {
  content: "\e719";
}

.icon-e617_dust1:before {
  content: "\e617";
}

.icon-e618_dust2:before {
  content: "\e618";
}

.icon-e619_dust3:before {
  content: "\e619";
}

.icon-e620_hail:before {
  content: "\e620";
}

.icon-e615_fog:before {
  content: "\e615";
}

.icon-e603_cloudy_night:before {
  content: "\e603";
}

.icon-e604_gloomy:before {
  content: "\e604";
}

.icon-e606_rain2:before {
  content: "\e606";
}

.icon-e612_wind:before {
  content: "\e612";
}

.icon-e613_haze:before {
  content: "\e613";
}

.icon-e614_haze_night:before {
  content: "\e614";
}

.icon-e600_sunny:before {
  content: "\e600";
}

.icon-e601_sunny_night:before {
  content: "\e601";
}

.icon-e602_cloudy:before {
  content: "\e602";
}

.icon-e605_rain1:before {
  content: "\e605";
}

.icon-e607_rain3:before {
  content: "\e607";
}

.icon-e608_snow1:before {
  content: "\e608";
}

.icon-e609_snow2:before {
  content: "\e609";
}

.icon-e610_snow3:before {
  content: "\e610";
}

.icon-e611_umbrella:before {
  content: "\e611";
}

.classname {
  -webkit-text-size-adjust: none;
}

.page {
  pointer-events: none;
  position: absolute;
  top: 0px;
  width: 100%;
  height: 100%;
}

.page-wrapper {
  pointer-events: none;
  position: absolute;
  top: 0px;
  width: 1366px;
  height: 768px;
  overflow-x: hidden;
  overflow-y: hidden;
  z-index: 2000;
}

#header {
  position: absolute;
  left: 0px;
  top: 0px;
  width: 1366px;
  height: 59px;
  pointer-events: auto;
  z-index: 999;
  background: url(../img/17_navbg.png) repeat-x;
  border-bottom: 1px solid #1f5fa8;
  z-index: 3000;
}

#logo {
  position: absolute;
  left: 0px;
  top: 0px;
  width: 441px;
  height: 58px;
  background: url(../img/logo.svg) no-repeat 10px center;
  background-size: 240px 58px;
  padding: 39px 9px 0 0;
  font-size: 14px;
  text-align: center;
  color: #6d92ba;
  cursor: pointer;
}

#main_page_title {
  position: absolute;
  left: 260px;
  top: 15px;
  height: 18px;
  font-size: 18px;
  color: #fdff9d;
  font-weight: bold;
  letter-spacing: 0px;
}

#main_page_subtitle {
  position: absolute;
  left: 260px;
  top: 35px;
  height: 12px;
  font-size: 12px;
  color: #ee0d1a;
  font-weight: bold;
  display: none;
}

#nav {
  position: absolute;
  width: 720px;
  height: 58px;
  top: 0px;
  left: calc(50% - 360px);
  background: url(../img/17_navtop.png) no-repeat top center;
  text-align: center;
}

#nav .menu {
  display: inline-block;
  min-width: 80px;
  height: 58px;
  font-size: 18px;
  font-weight: bold;
}

#nav .menu .lv1 {
  position: absolute;
  width: 80px;
  height: 58px;
  padding: 20px 0;
  text-align: center;
  cursor: pointer;
  color: #7fcaff;
}

#nav .menu .lv1 a {
  color: #7fcaff;
  text-decoration: none;
}

#nav .menu .lv1:hover, #nav .menu .lv1:hover a {
  color: #FFF;
}

#nav .menu .lv1.selected, #nav .menu .lv1.selected a {
  color: #FFF;
}

#nav .menu .lv1.selected .sbg {
  display: block;
}

#nav .menu .lv1 .sbg {
  position: absolute;
  text-align: left;
  width: 224px;
  height: 32px;
  top: 0px;
  left: -72px;
  display: none;
  background: url(../img/17_navselected.png?9) no-repeat top center;
}

#nav .menu .lv2 {
  position: absolute;
  text-align: left;
  width: 180px;
  top: 58px;
  border: 1px solid #1f5fa8;
  border-radius: 2px;
  padding: 5px 0 5px 0px;
  background-color: #021d39;
  font-size: 16px;
  font-weight: normal;
  box-shadow: 0 1px 8px #021121;
  display: none;
  z-index: 9999;
}

#nav .menu .lv2 .itm {
  display: block;
  color: #fff;
  width: 100%;
  height: 32px;
  padding: 5px 0 0 10px;
  cursor: pointer;
  text-decoration: none;
}

#nav .menu .lv2 .itm:hover {
  background-color: #1f5fa8;
}

#topright {
  position: absolute;
  top: 15px;
  right: 20px;
  width: 286px;
  height: 56px;
}

#companycombo {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 120px;
  height: 32px;
}

#companycombo .box {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 120px;
  height: 32px;
  border: 1px solid #2a81d2;
  border-radius: 3px;
  padding: 5px 0 0 10px;
  cursor: pointer;
  background: url(../img/combo_arr.png) no-repeat 103px 13px;
  background-color: #073b77;
}

#companycombo .box:hover {
  border: 1px solid #2f8be1;
  background-color: #073b77;
}

#companylist {
  position: absolute;
  top: 31px;
  left: 0px;
  width: 120px;
  border: 1px solid #1f5fa8;
  border-radius: 2px;
  padding: 5px 0 5px 0px;
  background-color: #021d39;
  display: none;
}

#companylist .itm {
  width: 100%;
  height: 32px;
  padding: 6px 0 0 10px;
  cursor: pointer;
}

#companylist .itm:hover {
  background-color: #1f5fa8;
}

#ctrl_pause {
  display: none;
  position: absolute;
  top: -3px;
  right: 100px;
  width: 38px;
  height: 38px;
  cursor: pointer;
  background: url(../img/17_nav_ico_pause.png) no-repeat center center, url(../img/17_nav_icobg.png) no-repeat center center;
}

#ctrl_autoswitch {
  position: absolute;
  top: -3px;
  right: 100px;
  width: 38px;
  height: 38px;
  cursor: pointer;
  pointer-events: auto;
  background: url(../img/17_nav_ico_pause.png) no-repeat center center, url(../img/17_nav_icobg.png) no-repeat center center;
}

.switch_off {
  background: url(../img/17_nav_ico_play.png) no-repeat center center, url(../img/17_nav_icobg.png) no-repeat center center !important;
}

#ctrl_mute {
  position: absolute;
  top: -3px;
  right: 53px;
  width: 38px;
  height: 38px;
  cursor: pointer;
  background: url(../img/17_nav_ico_mute.png) no-repeat center center, url(../img/17_nav_icobg.png) no-repeat center center;
}

.audio_off {
  background: url(../img/17_nav_ico_mute2.png) no-repeat center center, url(../img/17_nav_icobg.png) no-repeat center center !important;
}

#ctrl_user {
  position: absolute;
  top: -3px;
  right: 5px;
  width: 38px;
  height: 38px;
  cursor: pointer;
  background: url(../img/17_nav_ico_log.png) no-repeat center center, url(../img/17_nav_icobg.png) no-repeat center center;
}

.user_on {
  background: url(../img/17_nav_ico_useron.png) no-repeat center center, url(../img/17_nav_icobg.png) no-repeat center center !important;
}

/*COLOR*/
.blue {
  color: #1990FF !important;
}

.blue2 {
  color: #47befe !important;
}

.light-blue {
  color: #99CCFF !important;
}

.gray-blue {
  color: #6d90b6 !important;
}

.light-green {
  color: #88c732 !important;
}

.yellow {
  color: #FFFF61 !important;
}

.green {
  color: #008d00 !important;
}

.red {
  color: #FF0000 !important;
}

.hide {
  display: none;
}

/* Flex */
.display-flex {
  display: -webkit-box !important;
  display: -webkit-flex !important;
  display: -ms-flexbox !important;
  display: flex !important;
}

.align-items-center {
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.justify-content-center {
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.prevent-clicks, .prevent-clicks * {
  pointer-events: none !important;
}

.mainframe {
  position: absolute;
  top: 0px;
  border: none;
  padding: 0;
  margin: 0;
  width: 100%;
  overflow-y: hidden;
  overflow-x: hidden;
}

#mapWrapper {
  padding: 0;
  height: 100%;
  overflow-x: hidden;
  overflow-y: hidden;
}

#map {
  height: calc(100% + 30px)
}

.echarts-tooltip {
  z-index: 999999;
}

.pagetitle .maintitle .date {
  margin-left: 5px;
  opacity: 0.6;
}

/*Common*/
.block-frame {
  position: absolute;
  width: 310px;
  pointer-events: auto;
}

.block-header {
  text-align: center;
  font-size: 15px;
  font-weight: bold;
  height: 24px;
  background: url(../img/17_blockheader.png) no-repeat center bottom;
  padding: 2px 0 0 0;
  margin: 0;
  color: #011f4d;
}

.block-frame .cont {
  position: relative;
  border: 1px solid rgba(46, 77, 110, 0.5);
  background-color: rgba(17, 60, 110, 0.5);
}

.block-header span {
  color: #011f4d;
  font-size: 16px;
  font-weight: bold;
  padding: 0 0px 0 0;
}

.block-frame4corner {
  border-top: 1px solid rgba(80, 150, 224, 0.9);
  border-bottom: 1px solid rgba(80, 150, 224, 0.9);
  background-color: rgba(0, 13, 36, 0.8);
}

.block-frame4corner .c0, .block-frame4corner .c1, .block-frame4corner .c2, .block-frame4corner .c3 {
  position: absolute;
  width: 12px;
  height: 12px;
  border: 2px solid #8abede;
}

.block-frame4corner .c0 {
  border-right: none;
  border-bottom: none;
  top: -1px;
  left: 0;
}

.block-frame4corner .c1 {
  border-left: none;
  border-bottom: none;
  top: -1px;
  right: 0;
}

.block-frame4corner .c2 {
  border-left: none;
  border-top: none;
  bottom: -1px;
  right: 0;
}

.block-frame4corner .c3 {
  border-right: none;
  border-top: none;
  bottom: -1px;
  left: 0;
}

.block-frame4corner .header {
  position: absolute;
  width: 100%;
  color: #fff;
  font-size: 16px;
  text-align: center;
  font-weight: bold;
  top: 15px;
}

.block-header-option {
  float: right;
  margin-right: 6px;
}

.chartblock {
  pointer-events: auto;
}

select {
  font-weight: normal;
  font-size: 14px;
  background-color: #02203E;
  border: solid 1px #4D7EAF;
  border-radius: 3px;
  padding: 3px;
}

.white {
  color: #fff;
}

.blue_l {
  color: #47aafd;
}

.blue_ll {
  color: #99ccff;
}

.blue_b {
  color: #409ce1;
}

.green {
  color: #2ad805 !important;
}

.yellow {
  color: #ffde00 !important;
}

.red {
  color: #f52f2c !important;
}

.cyan {
  color: #19fbfe !important;
}

.ffnum {
}

.sub {
  padding-left: 2px;
}

.fs8 {
  font-size: 8px;
}

.fs9 {
  font-size: 9px;
  text-shadow: 0 0 0 rgba(255, 255, 255, 0) !important;
}

.fs10 {
  font-size: 10px;
  text-shadow: 0 0 0 rgba(255, 255, 255, 0) !important;
}

.fs11 {
  font-size: 11px;
  text-shadow: 0 0 0 rgba(255, 255, 255, 0) !important;
}

.fs12 {
  font-size: 12px;
}

.fs14 {
  font-size: 14px;
}

.fs15 {
  font-size: 15px;
}

.fs16 {
  font-size: 16px;
  font-weight: bold;
}

.fs17 {
  font-size: 17px;
  font-weight: bold;
}

.fs18 {
  font-size: 18px;
  font-weight: bold;
}

.fs20 {
  font-size: 20px;
  font-weight: bold;
}

.fs22 {
  font-size: 22px;
  font-weight: bold;
}

.fs24 {
  font-size: 24px;
}

.fs26 {
  font-size: 26px;
  font-weight: bold;
}

.fs28 {
  font-size: 28px;
  font-weight: bold;
}

.fs30 {
  font-size: 30px;
  font-weight: bold;
}

.fs32 {
  font-size: 32px;
  font-weight: bold;
}

.fs34 {
  font-size: 34px;
  font-weight: bold;
}

.fs36 {
  font-size: 36px;
  font-weight: bold;
}

.fs38 {
  font-size: 38px;
  font-weight: bold;
}

.fs40 {
  font-size: 40px;
  font-weight: bold;
}

.bold {
  font-weight: bold;
}

.center {
  text-align: center;
}

.chartctrl {
  position: absolute;
  text-align: right;
  height: 30px;
  width: 100%;
  padding-right: 20px;
}

/* ----combobox start---- */
.combobox {
  text-align: left;
  position: relative;
  display: inline-block;
  width: 100%;
  height: 20px;
  vertical-align: middle;
}

.combobox_label {
  position: relative;
  display: inline-block;
  width: 100%;
  height: 100%;
  border: 1px solid rgba(41, 140, 232, 0.6);
  border-radius: 2px;
  padding: 1px 3px;
  background: url(../img/combobox_arr.png) no-repeat right center;
  cursor: pointer;
  pointer-events: auto;
  vertical-align: middle;
}

.combobox_label:hover {
  border: 1px solid rgba(64, 156, 225, 0.8);
}

.combobox_list {
  position: absolute;
  display: block;
  width: 100%;
  border: 1px solid rgba(41, 140, 232, 0.6);
  border-radius: 2px;
  background-color: rgba(17,48,99);
  background-color: rgba(17,48,99,0.99);
  overflow-y: scroll;
  max-height: 135px !important;
  pointer-events: auto;
  transform: rotate(0deg);
}

.combobox_list .item {
  position: relative;
  display: block;
  padding: 3px;
  cursor: pointer;
  pointer-events: auto;
  letter-spacing: -0.5px;
}

.combobox_list .item:hover {
  background-color: #144a91;
}

/* ----combobox with date---- */
.combobox_date {
  position: absolute;
  width: 109px;
  height: 60px;
}

.combobox_date .tabs {
  width: 100%;
  height: 30px;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  overflow: hidden;
  background-color: #2a81d2;
}

.combobox_date .tab {
  position: absolute;
  top: 1px;
  width: 35px;
  height: 29px;
  background-color: #0a4589;
  color: #5d9ae3;
  text-align: center;
  padding-top: 4px;
  cursor: pointer;
  pointer-events: auto;
}

.combobox_date .selected {
  color: #fff;
  background-color: #2a81d2;
}

.combobox_date .tab1 {
  left: 1px;
  border-top-left-radius: 4px;
}

.combobox_date .tab2 {
  left: 37px;
}

.combobox_date .tab3 {
  right: 1px;
  border-top-right-radius: 4px;
}

.combobox_date .combobox {
  position: absolute;
  top: 30px;
  height: 30px;
}

.combobox_date .combobox_label {
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
  height: 30px;
  border: none;
  padding: 5px 0 0 8px;
  background: url(../img/combobox_arr.png) no-repeat 90px center;
  background-color: #2a81d2;
}

.combobox_date .combobox_list {
  background-color: #136dc1;
  border-radius: 5px;
  border: none;
  position: absolute;
  top: 31px;
}

.combobox_date .combobox_list .item {
  height: 30px;
  padding: 5px 0 0 8px;
}

.combobox_date .combobox_list .item:hover {
  background-color: #2a81d2;
}

/* ----combobox end---- */
.ico_up {
  display: inline-block;
  width: 15px;
  height: 18px;
  background: url(../img/arr_up.png) no-repeat 0 3px;
}

.ico_down {
  display: inline-block;
  width: 15px;
  height: 18px;
  background: url(../img/arr_down.png) no-repeat 0 3px;
}

#loading_msk {
  opacity: 0;
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  z-index: 999999;
}

#loading_msk .spinner {
  margin: 0 auto;
  margin-top: 350px;
  width: 60px;
  height: 60px;
  background: url(../img/loading_o.svg) no-repeat center center;
  background-size: 60px 60px;
}

/* animation rotate 360 */
@-webkit-keyframes rotate360 {
  0% {
    -webkit-transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
  }

}

@-moz-keyframes rotate360 {
  0% {
    -moz-transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(360deg);
  }

}

@-ms-keyframes rotate360 {
  0% {
    -ms-transform: rotate(0deg);
  }

  100% {
    -ms-transform: rotate(360deg);
  }

}

@keyframes rotate360 {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }

}

.spinner_animate {
  -webkit-animation: rotate360 2s linear infinite;
  -moz-animation: rotate360 2s linear infinite;
  -ms-animation: rotate360 2s linear infinite;
  animation: rotate360 2s linear infinite;
  pointer-events: none;
}

/*计数器*/
#counterContainer {
  height: 50px;
}

.number-div > div {
  vertical-align: middle;
}

.number-group {
  float: left;
  height: 45px;
  overflow: hidden;
  margin-left: 5px;
  padding-left: 5px;
  border: 1px solid #467cb4;
  background-color: rgba(1, 41, 106, 0.7)
}

.number-spliter {
  float: left;
  width: 20px;
  height: 50px;
  line-height: 46px;
  font-size: 12px;
  float: left;
  margin: 0 3px;
  padding-top: 8px;
}

.number-div .number {
  float: left;
  position: relative;
  width: 24px;
  height: 45px;
  line-height: 45px;
  font-size: 32px;
  overflow: hidden;
}

.number-div .comma {
  font-size: 30px;
  line-height: 48px;
  height: 28px;
  margin: 0 5px;
}

.numbers-view {
  position: absolute;
}

.numbers-view div {
  position: absolute;
}

/*计数器 end*/
.linkbutton {
  cursor: pointer;
}

.linkbutton:hover .link {
  text-decoration: underline;
}

.linkbutton-disabled {
  pointer-events: none !important;
}

.linkbutton-disabled .link {
  pointer-events: none !important;
}

/**
 * PowerTip
 * https://stevenbenner.github.io/jquery-powertip/
 */
#powerTip {
  cursor: default;
  display: none;
  position: absolute;
  white-space: nowrap;
  z-index: 2147483647;
}

.powertip {
  background: #0f183b;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.6);
  border-radius: 5px;
  padding: 5px 8px;
  border: 1px solid #1a4886;
}

/* PowerTip end */
#modal {
  position: absolute;
  z-index: 99998;
  background: rgba(0, 0, 0, 0.5);
  top: 0px;
  left: 0px;
  pointer-events: auto;
  width: 100%;
  height: 100%;
}

#popvideo {
  position: absolute;
  z-index: 99999;
  background: #000;
  box-shadow: 0 2px 60px rgba(0, 0, 0, 1);
  text-align: center;
  border: 1px solid #02b0f9;
}

#popvideo video {
  position: absolute;
  left: 0px;
  top: 0px;
}

#popvideo .close {
  position: absolute;
  height: 30px;
  width: 30px;
  top: 10px;
  right: 10px;
  border-radius: 15px;
  background: rgba(255, 255, 255, 1) url(../img/pop_x_b.png) no-repeat center center;
}

#popvideo .spinner {
  position: absolute;
  top: calc((100% - 60px)/2);
  left: calc((100% - 60px)/2);
  width: 60px;
  height: 60px;
  background: url(../img/loading_o.svg) no-repeat center center;
  background-size: 60px 60px;
}

#pop_usercenter {
  position: absolute;
  top: 57px;
  right: 10px;
  width: 200px;
  background-color: #1f62b5;
  box-shadow: 0 1px 16px #021121;
  z-index: 8888;
}

#pop_usercenter .arr {
  position: absolute;
  top: -3px;
  right: 26px;
  width: 18px;
  height: 18px;
  background-color: #1f62b5;
  transform: rotate(45deg);
}

#pop_usercenter .btnx {
  pointer-events: auto;
  position: absolute;
  top: 7px;
  right: 8px;
  width: 18px;
  height: 18px;
  cursor: pointer;
  background: url(../img/icox.png) no-repeat center center;
}

#pop_usercenter .tit {
  text-align: center;
  font-size: 16px;
  color: #abd8fe;
  height: 32px;
  padding-top: 5px;
}

#pop_usercenter .blk {
  padding: 8px 10px;
  border: 1px solid #2d7bcb;
  border-top: none;
  background-color: #104584;
}

#pop_usercenter .blk .rw {
  clear: both;
  margin-top: 2px;
}

#pop_usercenter .blk .c1 {
  display: inline-block;
  width: 50px;
  color: #86bdec;
  font-size: 12px;
  float: left;
  margin-top: 2px;
}

#pop_usercenter .blk .c2 {
  display: inline-block;
  color: #fff;
  font-size: 12px;
  width: 120px;
}

#pop_usercenter .blk .username {
  font-size: 14px;
  font-weight: bold;
}

#ctrl_video {
  pointer-events: auto;
  text-align: center;
  font-size: 12px;
  color: #fff;
  height: 62px;
  padding-top: 35px;
  background: url(../img/ico_cam.png) no-repeat center 12px;
  border-bottom: 1px solid #2d7bcb;
  cursor: pointer;
}

#pop_usercenter .bot {
  font-size: 14px;
  text-align: center;
  width: 100%;
}

#ctrl_logout {
  pointer-events: auto;
  display: inline-block;
  width: 182px;
  height: 30px;
  color: #04418c;
  margin: 10px 0;
  border-radius: 15px;
  line-height: 30px;
  cursor: pointer;
  background: #dbe7f6;
  background: -moz-linear-gradient(top, #dbe7f6 0%, #aacff3 100%);
  background: -webkit-linear-gradient(top, #dbe7f6 0%, #aacff3 100%);
  background: linear-gradient(to bottom, #dbe7f6 0%, #aacff3 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#dbe7f6', endColorstr='#aacff3', GradientType=0);
}
