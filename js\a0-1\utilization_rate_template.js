(function () {
    /**
     * 旅客,航班,货运对比
     */

     dfd.done(function () {
        if (!hasAllCompanyPermission()) {
            $(".rlyl .detail, .kzl .detail").remove();
            return;
        }
    })

    $(".rlyl .detail, .kzl .detail").on("click", function () {
        $("#pop_utilization_rate").removeClass("hide");
        $(".windowMask").removeClass("hide");
        var dateId =  $(".rlyl .detail").attr("dateId");
        var dateType = getDateType();
        if (window.utilizationRateWin == null) {
            initNormalRateWin(dateType,dateId);
        } else {
            window.utilizationRateWin.refreshView(dateType,dateId);
        }
    });


    function initNormalRateWin(dateType,dateId) {
        var page = new Vue({
            el: '.utilization-rate',
            template: $("#utilization_rate_template").html(),
            data: function () {
                return {
                    "dateType": dateType,
                    "weeks" : weeks,
                    showWeekList : false,
                    selectedWeek: null,
                    weekCmpActive:false,
                    wideList: [],
                    narrowList: [],
                    rList: [],
                    mounted :false
                }
            },
            mounted: function () {
                //日
                var me = this;

                if(this.weeks.length>1){
                    this.selectedWeek = weeks[1]
                }

                $(me.$refs["datetimepicker_D"]).datetimepicker({
                    defaultDate: new Date(),
                    format: "YYYY/MM/DD",
                    sideBySide: true,
                    maxDate: new Date(),
                    widgetPositioning: {
                        horizontal: 'right'
                    },
                    locale: 'zh-cn'
                });
                //月
                $(me.$refs['datetimepicker_M']).datetimepicker({
                    defaultDate: new Date(),
                    format: "YYYY/MM",
                    viewMode: 'months',
                    sideBySide: true,
                    maxDate: new Date(),
                    widgetPositioning: {
                        horizontal: 'right'
                    },
                    locale: 'zh-cn'
                });
                //年
                $(me.$refs['datetimepicker_Y']).datetimepicker({
                    defaultDate: new Date(),
                    format: "YYYY",
                    viewMode: 'years',
                    sideBySide: true,
                    maxDate: new Date(),
                    widgetPositioning: {
                        horizontal: 'right'
                    },
                    locale: 'zh-cn'
                });

 
                me.setDatePickerValue(dateType,dateId);
                //先查数据,再做下面事件监听,不然可能会触发dp.change事件,多做一次请求
                me.queryData(me.getDate());

                $(me.$refs["datetimepicker_D"]).on("dp.change", function (e) {
                    me.queryData(e.date._d);

                });
                $(me.$refs['datetimepicker_M']).on("dp.change", function (e) {
                    me.queryData(e.date._d);
                });
                $(me.$refs['datetimepicker_Y']).on("dp.change", function (e) {
                    me.queryData(e.date._d);
                });
                me.mounted = true;

            },
          
            methods: {
                setDatePickerValue(dateType,dateId){
                    var me = this;
                    if(dateType!='L'){
                        $(me.$refs[`datetimepicker_${dateType}`]).data("DateTimePicker").date(me.getDateStr(dateType,dateId));
                    }else{
                        var selectedWeek = me.weeks.find(v=>{return v.DATE_ID ==    dateId });
                        if(me.selectedWeek != selectedWeek ){
                            me.selectedWeek = selectedWeek;
                            //改变才数据,且在mounted 之后,
                            if(me.mounted){
                                me.queryData(selectedWeek);
                            }
                           
                        }
                        
                    }
                },
                refreshView(dateType,dateId){
                    var me = this;
                    if(me.dateType!=dateType){
                       me.switchDateType(dateType);
                    }else{
                        me.setDatePickerValue(dateType,dateId);
                    }
                   
                },
                getDateStr(dateType,dateId){
                    if(dateType=='D'){
                        return moment(dateId,'YYYYMMDD').format('YYYY/MM/DD');
                    }else if(dateType=='M'){
                        return moment(dateId,'YYYYMM').format('YYYY/MM');;
                    }else if(dateType=='Y'){
                        return dateId;
                    }
                },
                getStyleWidth(data){
                    var item = data['AC_UTIL_RATE'];
                    if(item.length>0){
                        var value=item[0].KPI_VALUE;
                        let rate = Number(Math.min(value,15)/15 * 100).toFixed(1);
                        return `width:${rate}%`
                    }else{
                        return `width:0%`
                    }
                  
                },
                getWeekDesc(){
                   return  this.selectedWeek  ?  this.selectedWeek.DATE_DESC_XS :''
                },
                onWeekMouseOut(){
                    this.weekCmpActive =false;
                    setTimeout(this.validActive,300)
                },
                onWeekMouseOver(){
                    this.weekCmpActive =true;
                },
                validActive(){
                    if(!this.weekCmpActive){
                        this.showWeekList = false;
                    }
                },
                dropWeekList(){
                    this.showWeekList = true;
                },
                onWeekChange(week){
                    this.selectedWeek= week;
                    this.showWeekList =false;
                    this.queryData(week)
                },
                getWeekLabel:function(item){
                    if(item){
                        var week = item.DATE_ID.substring(5, 7);
                        var year = item.DATE_ID.substring(0, 4);
                        return `${year}年第${week}周`;
                    }
                },
                closeWin: function () {
                    $("#pop_utilization_rate").addClass("hide");
                    $(".windowMask").addClass("hide");
                },
                switchDateType(dateType) {
                    this.dateType = dateType;
                    this.queryData(this.getDate())
                },
                getDate() {
                    var me = this;
                    var dateType = this.dateType;
                    if (dateType == 'D') {
                        return $(me.$refs['datetimepicker_D']).data("DateTimePicker").date().startOf("day")._d;
                    } else if (dateType == 'L') {
                        return this.selectedWeek;
                    } else if (dateType == 'M') {
                        return $(me.$refs['datetimepicker_M']).data("DateTimePicker").date().startOf("day")._d
                    } else if (dateType == 'Y') {
                        return $(me.$refs['datetimepicker_Y']).data("DateTimePicker").date().startOf("day")._d
                    }
                },
                getAcUtilRate(data){
                    var item = data['AC_UTIL_RATE'];
                    if(item.length>0){
                        return Number(item[0].KPI_VALUE).toFixed(2)
                    }
                    return "-";
                },
                getTrvRate(data){
                    var item = data['TRV_RATE'];
                    if(item.length>0){
                        return Number(item[0].KPI_VALUE* 100).toFixed(2) +'%';
                    }
                    return "-";
                },
                queryData(date) {
                    var me = this;
                    eking.ui.loading.show();
                    var kpi_list = [
                        'TRV_RATE', //计划班次
                        "AC_UTIL_RATE"//正常班次
                    ];

                    var param = {
                        "DATE_ID": getDateId(date, this.dateType),
                        "DATE_TYPE": this.dateType,
                        'KPI_CODE': kpi_list.join(','),
                        'COMP_CODE': getAllSubCompany().join(",")
                    }
                    $.ajax({
                        type: 'post',
                        url: "/bi/query/getfaccompactypekpi?utilization_rate",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(param),
                        success: function (response) {
                            eking.ui.loading.hide();
                            var data = response.data;
                            var wideList = [];
                            var narrowList = [];
                            var rList = [];
                            for (var p in data) {
                                var item = data[p];
                                var acItem = data[p];
                                acItem.actype = p;
                                var companyList = [];
                                for(var p1 in acItem.data){
                                    var kpiItem = acItem.data[p1];
                                    kpiItem['companyName'] = companyCode2Nameabbr[p1];
                                    companyList.push(kpiItem)
                                }

                                var newItem = {
                                    actype : p,
                                    companyList:companyList
                                }

                                if (item.acTypeTyp == '宽体机') {
                                    wideList.push(newItem);
                                } else if (item.acTypeTyp == '窄体机') {
                                    narrowList.push(newItem);
                                } else if (item.acTypeTyp == '支线机') {
                                    rList.push(newItem);
                                }
                            }
                            me.wideList = wideList;
                            me.narrowList = narrowList;
                            me.rList = rList;
                            console.log( me.wideList);
                            console.log( me.narrowList);
                            console.log( me.rList);
                         
                             
                        }
                    });
                }

            }
        });

        window.utilizationRateWin = page;

    }

})()