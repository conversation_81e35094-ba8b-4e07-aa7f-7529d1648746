
/**/

.page-wrapper {
  
}


.leftdarkbg {
  position: absolute;
  height: 100%;
  width: 300px;
/* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#000000+0,000000+100&0.68+0,0+100 */
background: -moz-linear-gradient(left,  rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%); /* FF3.6-15 */
background: -webkit-linear-gradient(left,  rgba(0,0,0,0.8) 0%,rgba(0,0,0,0) 100%); /* Chrome10-25,Safari5.1-6 */
background: linear-gradient(to right,  rgba(0,0,0,0.8) 0%,rgba(0,0,0,0) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ad000000', endColorstr='#00000000',GradientType=1 ); /* IE6-9 */

}



.pagetitle{
  position: absolute;
  width: 1306px;
  height: 59px;
  top: 77px;
  left: 30px;
  text-align: center;
}
.maintitle{
  color: #fff;
  width: 100%;
  font-size: 26px;
  padding: 11px 0 0 0;
  margin: 0 auto;
  font-weight: bold;
}
.submaintitle{
  color: #8ccafe;
  width: 100%;
  font-size: 16px;
  padding: 0 20px;
  margin: 0 auto;
}


canvas {
  background-color: rgba(255,0,0,0);
}

.blk {
  display: block;
}




/* ---- Page 1 ---- */



.legend {
  position: absolute;
  right: 123px;
  bottom: 249px;
  color: #c5defe;
}

.legend .itm {
  display: inline-block;
  padding-left: 26px;
  margin-left: 20px;
  background-repeat: no-repeat;
  background-position: left center;
}

.legend .itm1 {
  background-image: url(../img/a3.3.legend_1.png);
}
.legend .itm2 {
  background-image: url(../img/a3.3.legend_2.png);
}


#switch2earth {
  position: absolute;
  color: #c5defe;
  right: 10px;
  bottom: 255px;
  width: 120px;
  height: 110px;
  padding-top: 95px;
  text-align: center;
  cursor: pointer;
  pointer-events: auto;
  z-index: 21;
  background-image: url(../img/map2d3d_big.png?2);
  background-repeat: no-repeat;
}

#switch2earth.bg1 {
  background-position: 0 0;
}
#switch2earth.bg2 {
  background-position: -120px 0;
}


.counter {
  position: absolute;
  left: 638px;/*595px*/
  top: 138px;
}

.counter .itm{
  position: absolute;
  width: 125px;
  height: 77px;
}
.counter .itm .lb{
  font-size: 16px;
  font-weight: bold;
  color: #0d1634;
  padding-left: 12px;
}
.counter .itm .ct{
  position: absolute;
  top: 30px;
  width: 100%;
  height: 50px;
}
.counter .itm2{
  left: 105px;
}
.number-spliter {
  color: #0d1634 !important;
}


/* ---- block ---- */
#page_leve1 .block_tl {
  position: absolute;
  width: 163px;
  height: 430px;
  top: 86px;
  left: 31px;
  color: #65c5ff;
}
#page_leve1 .block_tl .kpi .tit{
  height: 12px;
}
#page_leve1 .block_tl .mk{
  position: absolute;
  font-size: 9px;
  width: 10px;
  height: 10px;
  text-align: center;
}
#page_leve1 .block_tl .chart{
  position: absolute;
  width: 102px;
  height: 102px;
  top: 58px;
  left: 33px;
}
#page_leve1 .block_tl canvas{
  position: absolute;
  top: 0px;
  left: 0px;
}
#page_leve1 .block_tl .pointer{
  position: absolute;
  width: 102px;
  height: 102px;
  top: 0px;
  left: 0px;
}
#page_leve1 .block_tl .chart .lb{
  position: absolute;
  width: 100%;
  height: 50px;
  top: 68px;
}
#page_leve1 .block_tl .chart .tit{
  height: 14px;
}

#page_leve1 .block_tl .row1{
  position: absolute;
  width: 100%;
  height: 174px;
  background: url(../img/a3.3_icon1.png) no-repeat 0 5px;
  padding-left: 40px;
  border-bottom: 1px solid #2f6fb1;
}
#page_leve1 .block_tl .row2{
  position: absolute;
  width: 100%;
  height: 55px;
  top: 187px;
  background: url(../img/a3.3_icon2.png) no-repeat 0 5px;
  padding-left: 40px;
  border-bottom: 1px solid #2f6fb1;
}
#page_leve1 .block_tl .row2 .col1{
  position: absolute;
  width: 66px;
  height: 55px;
}
#page_leve1 .block_tl .row2 .col2{
  position: absolute;
  left: 110px;
  width: 66px;
  height: 55px;
}
#page_leve1 .block_tl .row3{
  position: absolute;
  width: 100%;
  height: 164px;
  top: 260px;
  background: url(../img/a3.3_icon3.png) no-repeat 0 5px;
  padding-left: 40px;
}




/* ---- block ---- */
#page_leve1 .block_l {
  position: absolute;
  width: 282px;
  height: 216px;
  bottom: 25px;
  left: 20px;

  pointer-events: auto;
}
#page_leve1 .block_l table {
  position: absolute;
  top: 48px;
  left: 11px;
  width: 260px;
  height: 146px;
  border: 1px solid #1a4886;
  font-size: 12px;
}
#page_leve1 .block_l table tr {
  border-bottom: 1px solid #1a4886;
}
#page_leve1 .block_l table tr:last-child {
  border-bottom: none;
}
#page_leve1 .block_l table .name {
  font-weight: bold;
  padding-left: 8px;

}



#counterContainer {
  position: absolute;
  top: 66px;
  left: 0;
  padding-left: 20px;
}


/* ---- block ---- */
#page_leve1 .block_m {
  position: absolute;
  width: 370px;
  height: 216px;
  bottom: 25px;
  left: 312px;

  pointer-events: auto;
}
#page_leve1 .block_m .cont{
  position: absolute;
  top: 48px;
}
#page_leve1 .block_m .col1{
  position: absolute;
  width: 110px;
  height: 146px;
  left: 15px;
  background: url(../img/a3.3_icon4.png) no-repeat center bottom;
}
#page_leve1 .block_m .col1 .kpi{
  text-align: center;
  padding-top: 8px;
}
#page_leve1 .block_m .col2{
  position: absolute;
  width: 215px;
  height: 146px;
  left: 140px;
}

#page_leve1 .block_m .tit {
  background-color: #1a4886;
  height: 30px;
  width: 100%;
  line-height: 30px;
  font-weight: bold;
  text-align: center;
}


/* ---- block ---- */
#page_leve1 .block_m2 {
  position: absolute;
  width: 365px;
  height: 216px;
  bottom: 25px;
  left: 692px;

  pointer-events: auto;
}
#page_leve1 .block_m2 .cont{
  position: absolute;
  width: 100%;
  height: 175px;
  top: 35px;
}
#page_leve1 .block_m2 .b1{
  position: absolute;
  left: 15px;
  top: 50px;
}
#page_leve1 .block_m2 .b1 .kpi{
  color: #a3d800;
  display: block;
  line-height: 22px;
  height: 22px;
}
#page_leve1 .block_m2 .b2{
  position: absolute;
  right: 15px;
  top: 50px;
  text-align: right;
}
#page_leve1 .block_m2 .b2 .kpi{
  color: #3ec4fd;
  display: block;
  line-height: 22px;
  height: 22px;
}
#page_leve1 .block_m2 .c{
  position: absolute;
  left: 137px;
  top: 50px;
  text-align: center;
}
#page_leve1 .block_m2 .c .r1{
  color: #a3d800;
}
#page_leve1 .block_m2 .c .r2{
  color: #3ec4fd;
}
#page_leve1 .block_m2 .c .vs{
  display: block;
  line-height: 22px;
  height: 20px;
}

#page_leve1 .block_m2 canvas{
  position: absolute;
  left:96px; 
  top:15px; 
  background:rgba(255,0,0,0);
}

#btn_view_delay_cause1 {
  position: absolute;
  left:15px; 
  bottom:10px; 
  width: 112px !important;
}
#btn_view_delay_cause2 {
  position: absolute;
  right:15px; 
  bottom:10px; 
  width: 112px !important;
}
.btn_view_delay_back {
  position: absolute;
  right:15px; 
  bottom:10px; 
  width: 70px !important;
}

#page_leve1 .block_m2 table{
  width: 100%;
  margin-top: 10px;
  font-size: 12px;
}
#page_leve1 .block_m2 td{
  padding-top: 3px;
}
#page_leve1 .block_m2 .tdc1{
  text-align: right;
  padding-left: 25px;
  color: #47aafd;
  width: 100px;
}
#page_leve1 .block_m2 .tdc2{
  text-align: right;
  padding-left: 3px;
  color: #a8d5ff;
  width: 30px;
}
#page_leve1 .block_m2 .tdc3{
  padding-left: 3px;
}
#page_leve1 .block_m2 .tdc3 .bar{
  display: inline-block;
  height: 3px;
  margin-bottom: 4px;
  margin-right: 3px;
  background: rgb(1,70,139);
  background: -moz-linear-gradient(left,  rgba(1,70,139,1) 0%, rgba(112,218,252,1) 100%);
  background: -webkit-linear-gradient(left,  rgba(1,70,139,1) 0%,rgba(112,218,252,1) 100%);
  background: linear-gradient(to right,  rgba(1,70,139,1) 0%,rgba(112,218,252,1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#01468b', endColorstr='#6fd9fd',GradientType=1 );
}


/* ---- block ---- */
#page_leve1 .block_r {
  position: absolute;
  width: 280px;
  height: 216px;
  bottom: 25px;
  right: 20px;

  pointer-events: auto;
}

#page_leve1 .block_r .head {
  position: absolute;
  top: 48px;
  left: 11px;
  width: 298px;
  height: 30px;
  border: 1px solid #1a4886;
  font-size: 12px;
}
#page_leve1 .block_r table th {
  background-color: #1a4886;
  height: 30px;
  color: #a5ccfd;
}
#page_leve1 .block_r table tr,
#page_leve1 .block_r table th {
  border-bottom: 1px solid #1a4886;
}
#page_leve1 .block_r table .name {
  font-weight: bold;
  padding-left: 8px;
}
#page_leve1 .block_r table .col1 {
  width: 80px;
}
#page_leve1 .block_r table .col2 {
  width: 100px;
}
#alert_airports {
  position: absolute;
  top: 78px;
  left: 11px;
  width: 298px;
  height: 116px;
  font-size: 12px;
  overflow-y: hidden;
}
#alert_airports table {
  width: 298px;
}
#alert_airports table td{
  height: 30px;
}



.btn_view_chart {
  position: absolute;
  width: 122px;
  height: 24px;
  cursor: pointer;
  text-align: center;
  line-height: 24px;
  font-size: 12px;
  color: #fff;
  pointer-events: auto;
  
  background: #2a81d2;
  
  clip-path:
    polygon(
        0 0, calc(100% - 10px) 0, 100% 10px, 
        100% 100%, 10px 100%, 0 calc(100% - 10px), 0 0
    );
}

.btn_view_chart .inside {
  position: absolute;
  width: 100%;
  height: 100%;
  
  background: rgb(28,92,184);
  background: -moz-linear-gradient(top,  rgba(28,92,184,1) 0%, rgba(17,64,138,1) 100%);
  background: -webkit-linear-gradient(top,  rgba(28,92,184,1) 0%,rgba(17,64,138,1) 100%);
  background: linear-gradient(to bottom,  rgba(28,92,184,1) 0%,rgba(17,64,138,1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1c5cb8', endColorstr='#11408a',GradientType=0 );

  clip-path:
    polygon(
        1px 1px, calc(100% - 10px) 1px, calc(100% - 1px) 10px, 
        calc(100% - 1px) calc(100% - 1px), 10px calc(100% - 1px), 1px calc(100% - 10px), 1px 1px
    );
}

.btn_view_chart:hover .inside {
  background: #1954b0;
}
.btn_view_chart:active .inside {
  background: #153970;
}



/* ---- 运力分析 空中地面飞机 ---- */


.plane_sts {
  position: absolute;
  width: 100%;
  top: 35px;
}
.plane_sts .lb{
  font-size: 10px;
  color: #00acf2;
  height: 12px;
}
.plane_sts .sub{
  color: #00acf2;
}
.plane_sts .col_l {
  position: absolute;
  width: 80px;
  height: 36px;
  left: 0px;
  padding-left: 30px;
  padding-top: 3px;
  background: url(../img/ico_plane_sky_new.png) no-repeat left center;
}
.plane_sts .col_r {
  position: absolute;
  width: 80px;
  height: 36px;
  left: 80px;
  padding-left: 30px;
  padding-top: 3px;
  background: url(../img/ico_plane_ground_new.png) no-repeat left center;
}
.plane_sts .col_rr{
  position: absolute;
  width: 80px;
  height: 36px;
  left: 160px;
  padding-left: 30px;
  padding-top: 3px;
  background: url(../img/stopplane_new.png) no-repeat left center;
}
.plane_sts .aclist{
  position: absolute;
  width: 100%;
  height: 80px;
  top: 45px;
  font-size: 12px;
  overflow-y: hidden;
}

.plane_sts .bar {
  position: relative;
  display: inline-block;
  height: 4px;
  vertical-align: middle;
}
.plane_sts .innerbar {
  position: absolute;
  display: block;
  height: 4px;
}
.plane_sts .greenbar {
  background: #A1DA00;
}
.plane_sts .bluebar {
  background: #00AFFD;
}
.plane_sts .brownbar {
  background: #f39800;
}
.plane_sts .darkbar,
.plane_sts .darkbar_g,
.plane_sts .darkbar_b,
.plane_sts .darkbar_r {
  width: 30px;
  border-radius: 2px;
  overflow: hidden;
  transform: rotate(0);
}

.plane_sts .darkbar{
  background: #00579E;
}
.plane_sts .darkbar_g {
  background: rgb(137, 219, 181);
}
.plane_sts .bluebar_new {
  background: rgb(0, 128, 64);
}
.plane_sts .darkbar_b {
background: rgb(0, 140, 255);
}
.plane_sts .greenbar_new {
  background: rgb(0, 0, 255);
}
.plane_sts .darkbar_r {
  background: rgb(247, 110, 110);
}
.plane_sts .brownbar_new {
  background: rgb(255, 0, 0);
}


.plane_sts .baritmrow {
  margin-bottom: 3px;
}
.plane_sts .baritmrow .acno {
  color: #55c2ff;
  font-weight: bold;
  padding-right: 3px;
}
.plane_sts .baritmrow .val {
  display: inline-block;
  font-weight: bold;
  font-size: 11px;
  width: 20px;
}
.plane_sts .baritmrow .val_l {
  text-align: right;
}






/* ---- Page 2 ---- */

.pagetitle .back2level1 {
  color: #a6d3fd;
  cursor: pointer;
  pointer-events: auto;
}
.pagetitle .slash {
  color: #a6d3fd;
}
.backbutton {
  position: absolute;
  display: inline-block;
  top: 93px;
  right: 30px;
  border-radius: 5px;
  padding: 6px 18px;
  color: #14224c;
  font-size: 16px;
  background: rgba(255,255,255,0.9);
  cursor: pointer;
  pointer-events: auto;
}
.backbutton:hover {
  background: rgba(255,255,255,1);
}


/* --- */
.block-frame .tab{
  position: absolute;
  text-align: center;
  font-size: 14px;
  font-weight: bold;
  color: #011f4d;
  padding-top: 1px;
  cursor: pointer;
}
.block-frame .tab{
  width: 138px;
  height: 24px;
  background: url(../img/a3.2_tab_off.png) no-repeat;
}
.block-frame .tab.selected{
  background: url(../img/a3.2_tab_on.png) no-repeat;
}

.block-frame .tab1{
  left: 1px;
}
.block-frame .tab2{
  right: 1px;
}


/* --- */
#page_leve2 .block_l1 {
  height: 300px;
  left: 15px;
  top: 90px;
  width: 280px;
}
#page_leve2 .block_l1 .cont{
  height: 268px;
}
#page_leve2 .block_l1 .fltno{
  position: absolute;
  top: 8px;
  left: 6px;
  font-size: 16px;
  font-weight: bold;
  padding-left: 28px;
  background-position: left center;
  background-repeat: no-repeat;
  background-size: 30px 30px;
}
#page_leve2 .block_l1 .info{
  position: absolute;
  top: 33px;
  left: 10px;
  font-size: 14px;
  font-weight: bold;
}
#page_leve2 .block_l1 .sts{
  position: absolute;
  display: inline-block;
  border-radius: 3px;
  text-align: right;
  padding: 3px 8px;
  top: 23px;
  right: 10px;
  color: #051a48;
  font-size: 14px;
  font-weight: bold;
}
#page_leve2 .block_l1 .green{
  color: #52b600 !important;
}
#page_leve2 .block_l1 .yellow{
  color: #f9bc02 !important;
}
#page_leve2 .block_l1 .greenbg{
  background: #52b600 !important;
}
#page_leve2 .block_l1 .yellowbg{
  background: #f9bc02 !important;
}

#page_leve2 .block_l1 .blk{
  position: absolute;
  top: 63px;
  left: 10px;
  height: 156px;
  width: 256px;
  font-size: 14px;
  background-color: rgba(32, 91, 161, 0.5);
  border-radius: 4px;
  padding: 10px;
}
#page_leve2 .block_l1 .blk span{
  display: block;
}
#page_leve2 .block_l1 .blk .row1{
  height: 46px;
}
#page_leve2 .block_l1 .blk .row2{
  height: 52px;
}
#page_leve2 .block_l1 .blk .row3{
  height: 46px;
}
#page_leve2 .block_l1 .blk .col1{
  display: inline-block;
  height: 100%;
  width: 77px;
  float: left;
}
#page_leve2 .block_l1 .blk .col2{
  position: absolute;
  left: 77px;
  top: 0;
  height: 40px;
  width: 90px;
  background: url(../img/a3.3.arrow.png) no-repeat center bottom;
}
#page_leve2 .block_l1 .blk .col3{
  display: inline-block;
  height: 100%;
  width: 77px;
  text-align: right;
  float: right;
}
#page_leve2 .block_l1 .blk .lb{
  font-size: 12px;
  font-weight: bold;
  color: #78b2e6;
}
#page_leve2 .block_l1 .blk .city{
  font-weight: bold;
}
#page_leve2 .block_l1 .blk .row2 .time{
  font-weight: bold;
  font-size: 18px;
}
#page_leve2 .block_l1 .blk .row3 .time{
  font-weight: bold;
  font-size: 12px;
}

#page_leve2 .block_l1 .oil{
  position: absolute;
  top: 223px;
  left: 8px;
  height: 40px;
  font-size: 14px;
  padding-left: 58px;
  vertical-align: middle;
  line-height: 40px;
  background: url(../img/a3.3.p2_icon4.png) no-repeat left center;
}
#page_leve2 .block_l1 .oil .lb{
  display: inline-block;
  color: #78b2e6;
  margin-right: 3px;

}
#page_leve2 .block_l1 .oil .val{
  display: inline-block;
  font-weight: bold;
  font-size: 15px;
}


/* --- */
#page_leve2 .block_l2{
  height: 300px;
  left: 15px;
  top: 400px;
  width: 280px;
}
#page_leve2 .block_l2 .cont{
  top: 25px;
  height: 136px;
  padding: 5px;
}
#page_leve2 .block_l2 .tabc1{
  padding-left: 100px;
  height: 100%;
  background: url(../img/a3.3.p2_icon1.png) no-repeat 2px center;
}
#page_leve2 .block_l2 .lb{
  color: #78b2e6;
  padding-right: 5px;
}
#page_leve2 .block_l2 .val{
  font-weight: bold;
}
#page_leve2 .block_l2 .tabc1 .row1{
  padding-top: 27px;
}
#page_leve2 .block_l2 .tabc1 .row2{
  padding-top: 8px;
}

#page_leve2 .block_l2 .tabc2{
  padding-left: 100px;
  padding-top: 5px;
  height: 100%;
  background: url(../img/a3.3.p2_icon3.png) no-repeat 2px center;
}

/* --- */
#page_leve2 .block_l3{
  height: 300px;
  left: 15px;
  top: 580px;
  width: 280px;
}
#page_leve2 .block_l3 .cont{
  top: 25px;
  height: 136px;
  padding: 5px;
}
#page_leve2 .block_l3 .tabc1{
  padding-left: 100px;
  height: 100%;
  background: url(../img/a3.3.p2_icon2.png) no-repeat 2px center;
}
#page_leve2 .block_l3 .lb{
  color: #78b2e6;
  padding-right: 5px;
}
#page_leve2 .block_l3 .tabc2 .lb{
  width: 66px;
}
#page_leve2 .block_l3 .val{
  
}
#page_leve2 .block_l3 td{
  vertical-align:top;
}
#page_leve2 .block_l3 .tabc1 td{
  padding-right: 15px !important;
}

#page_leve2 .block_l3 .tabc2{
  padding-left: 100px;
  padding-top: 5px;
  height: 100%;
  background: url(../img/a3.3.p2_icon2.png) no-repeat 2px center;
}


/* --- */
#page_leve2 .block_b1 {
  position: absolute;
  bottom: 26px;
  left: 327px;
  width: 388px;
  height: 110px;
}
#page_leve2 .block_b1 .tit {
  position: absolute;
  bottom: 0px;
  left: 0;
  width: 32px;
  height: 100%;
  background-color: #1a4886;
  padding: 0 5px;
  text-align: center;
  line-height: 17px;
}
#page_leve2 .block_b1 .tit table {
  height: 100%;
}
#page_leve2 .block_b1 .col1 {
  position: absolute;
  top: 0px;
  left: 40px;
  width: 78px;
  height: 100%;
  font-size: 46px;
  line-height: 110px;
  text-align: center;
}
#page_leve2 .block_b1 .col2 {
  position: absolute;
  top: 28px;
  left: 122px;
  width: 118px;
}
#page_leve2 .block_b1 .col2 .temp{
  color: #90d1ff;
  font-size: 36px;
  display: block;
  line-height: 36px;
}
#page_leve2 .block_b1 .col2 .cond{
  
}
#page_leve2 .block_b1 .col3{
  position: absolute;
  top: 48px;
  left: 220px;
  width: 55px;
}
#page_leve2 .block_b1 .col4{
  position: absolute;
  top: 48px;
  left: 282px;
  width: 55px;
}
#page_leve2 .block_b1 .col3 .lb,
#page_leve2 .block_b1 .col4 .lb{
  display: block;
  color: #90d1ff;
  font-size: 12px;
}


/* --- */
#page_leve2 .block_b2 {
  position: absolute;
  bottom: 26px;
  left: 720px;
  width: 388px;
  height: 110px;
}
#page_leve2 .block_b2 .tit {
  position: absolute;
  bottom: 0px;
  left: 0;
  width: 32px;
  height: 100%;
  background-color: #1a4886;
  padding: 0 5px;
  text-align: center;
  line-height: 17px;
}
#page_leve2 .block_b2 .tit table {
  height: 100%;
}
#page_leve2 .block_b2 .layout {
  position: absolute;
  top: 0px;
  left: 50px;
}
#page_leve2 .block_b2 .layout .name{
  position: absolute;
  top: 61px;
  left: 37px;
  font-size: 14px;
}



.power_tip {
  background:#0641bc; 
  box-shadow: 0 1px 5px rgba(0,0,0,0.3); 
  border-radius:3px;
  padding: 5px;
}




/* search */
.searchform {
  position: absolute;
  width: 218px;
  height: 210px;
  right: 0px;
  top: 126px;
}
.searchform .tt {
  font-weight: bold;
  margin-bottom: 4px;
}
.searchform .lb {
  width: 55px;
  font-size: 12px;
  color: #60b7ff;
  font-weight: bold;
}
.searchform .error {
  font-size: 12px;
  color: #60b7ff;
  margin-top: 5px;
  display: none;
}

.searchform .input {
  width: 112px;
  height: 24px;
  line-height: 24px;
  background-color: #004a91;
  border-radius: 3px;
  border: none;
  pointer-events: auto;
  margin: 2px 0;
  text-indent: 3px;
  outline: none;
  font-size: 12px;
}
.searchform .input_dropdown {
  position: relative;
}
.searchform .dropdown_arrow {
  position: absolute;
  top: 2px;
  right: 0px;
  height: 24px;
  width: 24px;
  pointer-events: auto;
  cursor: pointer;
  background: url(../img/combo_arr.png) no-repeat center;
}
#flt_dropdown_list {
  position: absolute;
  top: 106px;
  left: 55px;
  width: 112px;
  max-height: 144px;
  overflow: hidden;
  border-radius: 3px;
  background-color: #004a91;
  pointer-events: auto;
  display: none;
}
#flt_dropdown_list .itm{
  pointer-events: auto;
  cursor: pointer;
  width: 100%;
  height: 24px;
  display: block;
  line-height: 24px;
  padding-left: 3px;
  font-size: 12px;
}
#flt_dropdown_list .itm:hover{
  background-color: #00417f;
}

.btn_go_flt {
  margin: 3px 0 0 1px;
  position: relative;
  width: 108px;
  height: 24px;
  cursor: pointer;
  text-align: center;
  line-height: 24px;
  font-size: 12px;
  color: #fff;
  pointer-events: auto;
  
  background: #2a81d2;
  
  clip-path:
    polygon(
        0 0, calc(100% - 10px) 0, 100% 10px, 
        100% 100%, 10px 100%, 0 calc(100% - 10px), 0 0
    );
}

.btn_go_flt .inside {
  position: absolute;
  width: 100%;
  height: 100%;
  
  background: rgb(28,92,184);
  background: -moz-linear-gradient(top,  rgba(28,92,184,1) 0%, rgba(17,64,138,1) 100%);
  background: -webkit-linear-gradient(top,  rgba(28,92,184,1) 0%,rgba(17,64,138,1) 100%);
  background: linear-gradient(to bottom,  rgba(28,92,184,1) 0%,rgba(17,64,138,1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1c5cb8', endColorstr='#11408a',GradientType=0 );

  clip-path:
    polygon(
        1px 1px, calc(100% - 10px) 1px, calc(100% - 1px) 10px, 
        calc(100% - 1px) calc(100% - 1px), 10px calc(100% - 1px), 1px calc(100% - 10px), 1px 1px
    );
}

.btn_go_flt:hover .inside {
  background: #1954b0;
}
.btn_go_flt:active .inside {
  background: #153970;
}
