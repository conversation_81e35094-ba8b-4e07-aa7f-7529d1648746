
/*营销概览*/

.page-wrapper {
  background: url(../img/a2.1.bg.png) no-repeat top center;
}

.earth_wrap {
  position: absolute; 
  width:690px; 
  height:690px; 
  left: 338px; 
  top: 85px;
  pointer-events: none;
}
#earth3d {
  position: absolute; 
  width:690px; 
  height: 690px; 
  pointer-events: none;
}
.earthlight {
  position: absolute;
  left:344px; 
  top:91px; 
  width:676px; 
  height:676px; 
  background: url(../img/earth_light.png) no-repeat center center; 
  background-size: 520px 520px;
  pointer-events: none;
}

.mapoverlay {
  position: absolute;
  left:344px; 
  top:91px; 
  width:676px; 
  height:676px; 
  pointer-events: auto;
}



.pagetitle{
  position: absolute;
  width: 100%;
  height: 36px;
  top: 67px;
  text-align: center;
  background: url(../img/title-bot-line.png) no-repeat center 25px;
}
.maintitle{
  color: #7fc4ff;
  width: 350px;
  font-size: 27px;
  padding: 5px 20px;
  margin: 0 auto;
  text-shadow: 0 0 10px #0d2452,0 0 10px #0d2452,0 0 20px #0c2d68,0 0 20px #0c2d68;
}
.submaintitle{
  color: #7fc4ff;
  width: 350px;
  font-size: 22px;
  padding: 0 20px;
  margin: 0 auto;
}

#main_cb_type {
  position: absolute;
  top: 51px;
  left: 632px;
  height: 30px;
  width: 100px;
}

#main_cb_type .combobox_label{
  padding: 4px 6px;
  background: url(../img/combobox_arr.png) no-repeat 80px center;
}

.combobox_date {
  left: 900px;
  top: 102px;
}

/* --- */
.block_l1 {
  height: 310px;
  left: 15px;
  top: 110px;
}
.block_l1 .cont{
  height: 242px;
  background: url(../img/a2.1.plane.png) no-repeat center center;
}
.block_l1 .blk1 {
  position: absolute;
  height: 48px;
  top: 17px;
  left: 22px;
}
.block_l1 .blk2 {
  position: absolute;
  text-align: right;
  height: 48px;
  top: 17px;
  right: 22px;
}
.block_l1 .blk3 {
  position: absolute;
  height: 48px;
  bottom: 22px;
  left: 22px;
}
.block_l1 .blk4 {
  position: absolute;
  text-align: right;
  height: 48px;
  bottom: 22px;
  right: 22px;
}



/* --- */
.block_l2 {
  left: 15px;
  top: 407px;
}
.block_l2 .cont{
  height: 293px;
}
.block_l2 .chartctrl{
  top: 20px;
}


/* --- */
.block_r1 {
  right: 15px;
  top: 87px;
  z-index: 92;
}
.block_r1 .cont{
  height: 204px;
}
.block_r1 .chartctrl{
  top: 20px;
}

/* --- */
.block_r2 {
  right: 15px;
  top: 330px;
  z-index: 91;
}
.block_r2 .cont{
  height: 166px;
}
.block_r2 .chartctrl{
  top: 20px;
}

/* --- */
.block_r3 {
  right: 15px;
  top: 535px;
  z-index: 90;
}
.block_r3 .cont{
  height: 185px;
}
.block_r3 .chartctrl{
  top: 20px;
}





.mapoverlay .complogo {
  position: absolute;
  width: 30px;
  height: 30px;
  border-radius: 3px;
  border: 1px solid rgba(50,58,68, 0.6);
  -moz-box-shadow: rgba(100, 177, 230, 0.8) 0px 0px 4px inset;
  -webkit-box-shadow: rgba(100, 177, 230, 0.8) 0px 0px 4px inset;
  box-shadow: rgba(100, 177, 230, 0.8) 0px 0px 4px inset;
  background-color: #fff;
  background-position: center center;
  background-repeat: no-repeat;
  cursor: pointer;
  pointer-events: auto;
}
.mapoverlay .kpilabel {
  position: absolute;
  text-align: center;
  width: 100px;
  height: 31px;
}

.mapoverlay .text {
  display: inline-block;
  padding: 3px 8px 0 8px;
  height: 100%;
  border-radius: 5px;
  overflow: hidden;
  transform: rotate(0deg);
  color: #00327c;
  font-size: 18px;
  border: 1px solid #00327c;
  background: url(../img/a2.1.kpibg.png) repeat-x;
  min-width: 50px;
  -moz-box-shadow: #00327c 0px 0px 2px;
  -webkit-box-shadow: #00327c 0px 0px 2px;
  box-shadow: #00327c 0px 0px 2px;
}

.mapoverlay .line {
  position: absolute;
  background: #7ccfff;

  -moz-box-shadow: #00327c 0px 0px 2px, #00327c 0px 0px 2px, #00327c 0px 0px 2px;
  -webkit-box-shadow: #00327c 0px 0px 2px, #00327c 0px 0px 2px, #00327c 0px 0px 2px;
  box-shadow: #00327c 0px 0px 2px, #00327c 0px 0px 2px, #00327c 0px 0px 2px;
}