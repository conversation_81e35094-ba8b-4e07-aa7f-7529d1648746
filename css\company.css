.lang {
  overflow : hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
  font-weight: normal;
}

.con_flex_column{ display: -webkit-flex; -webkit-flex-flow: column;}
.con_flex_row{ display: -webkit-flex; -webkit-flex-flow: row;}
.flex_none{-webkit-flex:none;}
.flex_1{-webkit-flex:1;}


.page-bgimg {
  position: absolute;
  top: 0px;
  width: 2688px;
  height: 790px;
  background: url(../img/b1.1-bg.jpg?7) no-repeat top center;
  background-size: 100%;
  overflow-x: hidden;
  overflow-y: hidden;
}

.earthmask {
  position: absolute;
  top: 0px;
  width: 2688px;
  height: 790px;
  background: url(../img/b1.1-bg.mapmask.png?1) no-repeat top center;
  overflow-x: hidden;
  overflow-y: hidden;
  pointer-events: none;
}
.earthlight {
  left:1036px; 
  top:112px; 
  width:676px; 
  height:676px; 
  background: url(../img/earth_light.png) no-repeat center center; 
  background-size: 676px 676px;
  display: none;
}

.page-wrapper {
  position: absolute;
  top: 0px;
  width: 2688px;
  height: 790px;
  overflow-x: hidden;
  overflow-y: hidden;
}

.card {
	border: 1px solid;
	border-radius: 8px;
	overflow: hidden;

	-moz-box-shadow:0px 2px 8px #122348; 
	-webkit-box-shadow:0px 2px 8px #122348; 
	box-shadow:0px 2px 8px #122348;

	transform: rotate(0deg);

	pointer-events: auto;
	cursor: pointer;
}

.card div{
	position: relative;
	transform: rotate(0deg);
}
.card .head{
	
	width: 100%;
	text-align: center;
	border-bottom: 1px solid;
	padding: 2px;
	font-weight: bold;
}
.card .head .weather{
	position: absolute;
	top: 3px;
	right: 8px;
	width: 30px;
	height: 20px;
	text-align: right;
}
.card .cont{
	width: 120%;
	margin-left: -10%;
	text-align: center;
}
.card .cont .title{
	text-align: left;
	padding: 1px 0 0 12%;

}
.card .cont .itm{
	display: inline-block;
	padding: 0 px;
	margin: 0 -2px;
  min-width:43px;
}
.card .cont .itm .ffnum{
	line-height: 20px;
}
.card .cont .sub{
	padding: 0px;
}

.card1 {
	width: 190px;
	height: 90px;
}
.card1 .cont{
	margin-top: 4px;
}
.card1 .city {
	font-size: 17px;
}
.card1 .weather {
	font-size: 17px;
}
.card1 .itm {
	width:55px;
	line-height: 25px;
}

.card2 {
	width: 155px;
	height: 80px;
}
.card2 .cont{
	margin-top: 4px;
}
.card2 .city {
	font-size: 15px;
}

.card3 {
	width: 136px;
	height: 70px;
}
.card3 .city {
	font-size: 13px;
}


.bluecard {
	border-color: #238DE6;
	background: rgba(25, 96, 185, 0.8);
}
.bluecard .head {
	border-color: #238DE6;
	background: -moz-linear-gradient(top,  rgba(33,103,190,1) 0%, rgba(22,89,178,1) 100%);
	background: -webkit-linear-gradient(top,  rgba(33,103,190,1) 0%,rgba(22,89,178,1) 100%);
	background: linear-gradient(to bottom,  rgba(33,103,190,1) 0%,rgba(22,89,178,1) 100%);
}

.yellowcard {
	border-color: #238DE6;
	background: rgba(25, 96, 185, 0.8);
}
.yellowcard .head {
	border-color: #ecc95a;
	background: -moz-linear-gradient(top,  #c6a51c 0%, #b78c31 100%);
	background: -webkit-linear-gradient(top,  #c6a51c 0%,#b78c31 100%);
	background: linear-gradient(to bottom,  #c6a51c 0%,#b78c31 100%);
}

.redcard {
	border-color: #238DE6;
	background: rgba(25, 96, 185, 0.8);
}
.redcard .head {
	border-color: #b24359;
	background: -moz-linear-gradient(top,  #850f25 0%, #760a19 100%);
	background: -webkit-linear-gradient(top,  #850f25 0%, #760a19 100%);
	background: linear-gradient(to bottom,  #850f25 0%, #760a19 100%);
}



.tab_normal {
	background-color: #0f3975 !important;
	border-bottom: 1px solid #2391E4;
	cursor: pointer;
	pointer-events: auto;
}
.tab_selected {
	background-color: #041943 !important;
	border-bottom: none;
	cursor: pointer;
	pointer-events: auto;
}

.flt_list_itm {
	position: relative;
	width: 90px;
  	height: 30px;
  	text-align: center;
  	padding-top: 7px;
  	color: #54B8FF;
  	background: url(../img/b1.1_list_bg.png) no-repeat top center;
  	cursor: pointer;
  	pointer-events: auto;
}
.flt_list_itm.selected {
	color: #FFFFFF;
  	background: url(../img/b1.1_list_bg_selected.png) no-repeat top center;
}


.linenode {
	height: 28px;
	vertical-align: middle;
}
.linenode span {
	vertical-align: middle;
}
.linenode .blt {
	text-shadow: 0px 0px 20px #000;
	font-size: 14px;
}




.area_stroke {
	stroke: #003366;
	stroke-width: 1;
}

.area_green1 {
	fill: #216A3C;
}
.area_green2 {
	fill: #348361;
}
.area_green3 {
	fill: #54946F;
}
.area_yellow {
	fill: #cfaa5b !important;
}
.area_orange {
	fill: #db814a !important;
}
.area_red {
	fill: #9F373C !important;
}

.pin_green {
	background: url('../img/pin_green.png?2') no-repeat 0 0;
}
.pin_yellow {
	background: url('../img/pin_yellow.png?2') no-repeat 0 0;
}
.pin_red {
	background: url('../img/pin_red.png?2') no-repeat 0 0;
}

.over_night_arp{
	position: relative;
	display: inline-block;
	height: 20px;
	width: 48px;
	margin: 0;
	padding: 0;
}
.over_night_arp .code{
	display: inline-block;
	height: 100%;
	width: 30px;
	border-right: 1px solid rgba(0,175,253,0.3);
	text-align: right;
	padding-right: 3px;
}
.over_night_arp .bar{
	position: absolute;
	display: inline-block;
	top: 3px;
	left: 32px;
	height: 12px;
	background-color: #00AFFD;
}
.over_night_arp .num{
	position: absolute;
	display: inline-block;
	top: 1.3px;
	left: 33px;
	height: 100%;
	width: 20px;
	font-weight: bold;
}

.title_tl {
	text-align: center;
	width: 200px;
    height: 22px;
    left: 348px;
    top: 74px;
    color: #041326;
}
.title_tr {
	text-align: center;
	width: 200px;
    height: 22px;
    right: 317px;
    top: 102px;
    color: #041326;
}
.title_br {
	width: 200px;
    height: 22px;
    right: 572px;
    bottom: 73px;
    color: #041326;
}


#comp_rank_list_month img,
#comp_rank_list_year img {
	width: 21px;
	height: auto;
}



/* search */
.searchform {
  position: absolute;
  width: 218px;
  height: 210px;
  left: 895px;
  top: 171px;
}
.searchform .tt {
	position: relative;
  font-weight: bold;
  margin-bottom: 4px;
}
.searchform .error {
  position: relative;
  font-size: 12px;
  color: #60b7ff;
  margin-top: 5px;
  display: none;
}
.searchform .ipt {
  position: relative;
  width: 112px;
  height: 28px;
  margin: 2px 0;
}
.searchform .input {
  position: relative;
  width: 112px;
  height: 28px;
  line-height: 28px;
  background-color: #004a91;
  border-radius: 3px;
  border: none;
  pointer-events: auto;
  text-indent: 3px;
  outline: none;
  font-size: 12px;
  pointer-events: auto;
}
.searchform .ico_search {
  position: absolute;
  top: 2px;
  right: 2px;
  height: 24px;
  width: 24px;
  pointer-events: auto;
  cursor: pointer;
  background: url(../img/ico_search.png) no-repeat center;
}


.aclist{
  position: absolute;
  width: 100%;
  height: 80px;
  top: 45px;
  font-size: 12px;
  overflow-y: hidden;
}

.aclist .bar {
  position: relative;
  display: inline-block;
  height: 4px;
  vertical-align: middle;
}
.aclist .innerbar {
  position: absolute;
  display: block;
  height: 4px;
}
.aclist .greenbar {
  background: #A1DA00;
}
.aclist .bluebar {
  background: #00AFFD;
}
.aclist .brownbar {
  background: #f39800;
}
.aclist .darkbar {
  background: #00579E;
  width: 35px;
  border-radius: 2px;
  overflow: hidden;
  transform: rotate(0);
}
.aclist .baritmrow {
  margin-bottom: 3px;
}
.aclist .baritmrow .acno {
  color: #55c2ff;
  font-weight: bold;
  padding-right: 3px;
}
.aclist .baritmrow .val {
  display: inline-block;
  font-weight: bold;
  font-size: 11px;
  width: 20px;
}
.aclist .baritmrow .val_l {
  text-align: right;
}

#abnormal_date_select .selected {
    background: #0f3975;
    color: #fff;
    border-left:1px solid #2391E4;
    border-right:1px solid #2391E4;
    border-radius: 4px;
}
#abnormal_date_select .selected:first-child{
    border-left:0;
}
#abnormal_date_select .selected:last-child{
    border-right:0;
}
#abnormal_date_select div {
    margin: 0 1px 0 0;
    background-color: transparent;
    color: #5d9ae3;
    font-weight: bold;
    line-height: 20px;
    width: 33px;
    cursor: pointer;
    user-select: none;
    position: relative;
}
#abnormal_flights_tab_content .flex-box div {
    flex: 1;
    text-align: center;
    position: relative;
    width: 75px;
    height: 65px;
    background: url(../img/b1.1_abnormal_flights_bg.png) no-repeat;
    background-size: 75px 65px;
    padding: 10px 0;
    display: inline-block;
}
#abnormal_flights_tab_content .b6 .b6-cont {
    position: relative;
    height: 100px;
}
/* #abnormal_flights_tab_content .b6 .b6-cont .tit {
    position: relative;
    left: 2px;
    top: 5px;
    width: 50px;
    height: 12px;
    color: #5fc5ff;
} */
#abnormal_flights_tab_content .b6 .b6-cont .tit {
    position: relative;
    left: 2px;
    top: -4px;
    width: 50px;
    height: 13px;
    color: #5fc5ff;
}
#abnormal_flights_tab_content .block .chart {
    position: relative;
    height: 70px;
    margin: 0 15px;
}
#chart_abnormal {
    position: absolute;
    width: 345px;
    height: 62px;
    top: 3px;
    left: 16px;
    background: url(../img/b1.1_runtimeevnt.png) no-repeat;
    background-size: contain;
}
#abnormal_total {
    position: absolute;
    width: 30px;
    height: 10px;
    top: 43px;
    left: 44px;
    text-align: center;
    color: white;
    font-size: 14px;
}
#abnormal_hb {
    position: absolute;
    width: 150px;
    height: 20px;
    top: 75px;
    left: 5px;
    text-align: center;
}
#abnormal_main {
    position: absolute;
    /* width: 150px; */
		width: 200px;
    height: 40px;
    top: 30px;
    left: 207px;
		text-align: center;
}
#abnormal_main div {
	position: relative;
}
#abnormal_main_val span {
    display: block;
    margin-right: 6px;
}
#abnormal_flights_tab_content .b6 .extr {
    height: 155px;
    border-top: 1px dashed #1f5fa8;
    text-align: left;
    padding-top: 10px;
    position: relative;
}
#abnormal_flights_tab_content .b6 .extr .tabcontent {
    position: absolute;
    margin-top: 10px;
    height: 125px;
    width: 100%;
    top: 0px;
    left: 0px;
    cursor: pointer;pointer-events: auto;
}
/* #l_runTimeEventChartLegend {
    height: 90px;
    overflow: hidden;
    width: 210px;
    left: 160px;
    top: 30px;
} */
#l_runTimeEventChartLegend {
    height: 90px;
    overflow: hidden;
    width: 250px;
    left: 140px;
    top: 30px;
}
#unnormal_detail {
    display: block;
    height: 15px;
    width: 15px;
    background: url(../../img/a0-1_detail.png) no-repeat;
    -webkit-background-size: 15px;
    background-size: 15px;
    cursor: pointer;pointer-events: auto;
    position: absolute;
    right: 5px;
    top: -20px;
}
#content_evt_ays .l-box {
    position: relative;
    top: 0px;
    height: 75px;
    overflow: hidden;
}

#content_evt_ays .legend_row {
    color: #4d9cfd;
    text-align: left;
    cursor: pointer;
    position: relative;
}

#content_evt_ays .legend_row span {
    display: inline-block;
    font-size: 11px;
}

#content_evt_ays .legend_row .name {
    /* width: 68px; */
		width: 140px;
}

#content_evt_ays .legend_row .num {
    width: 20px;
    text-align: right;
}

#content_evt_ays .legend_row .per {
    padding-left: 20px;
    padding-right: 7px;
}
#content_evt_ays .btns {
    position: absolute;
    height: 20px;
    width: 50px;
    right: 5px;
}
#content_evt_ays .btns .pre-btn {
    position: absolute;
    display: block;
    height: 20px;
    width: 20px;
    left: 0;
    background: url(../../img/a0-1_pre.png) no-repeat;
    -webkit-background-size: 20px;
    background-size: 20px;
    cursor: pointer;
}
#content_evt_ays .btns .pre-btn.prev-disabled {
    position: absolute;
    display: block;
    height: 20px;
    width: 20px;
    left: 0;
    background: url(../../img/a0-1_pre-dis.png) no-repeat;
    -webkit-background-size: 20px;
    background-size: 20px;
    cursor: pointer;
}

#content_evt_ays .btns .next-btn {
    position: absolute;
    display: block;
    height: 20px;
    width: 20px;
    right: 0;
    background: url(../../img/a0-1_next.png) no-repeat;
    -webkit-background-size: 20px;
    background-size: 20px;
    cursor: pointer;
}
#content_evt_ays .btns .next-btn.next-disabled {
    position: absolute;
    display: block;
    height: 20px;
    width: 20px;
    right: 0;
    background: url(../../img/a0-1_next-dis.png) no-repeat;
    -webkit-background-size: 20px;
    background-size: 20px;
    cursor: pointer;
}

#block_securemonitor div {
	position: relative;
}

.cotent .row1 .block {
    background-color: rgba(19,60,117,0.9);
    padding: 10px;
    border-radius: 5px;
}
.cotent .row1 .block .fs12 {
    padding: 0 0 0 25px;
}
.block_l2 .cotent .row1 .b1 .fs12 {
    background: url(../../img/a0-1_safe.png) no-repeat left center;
    -webkit-background-size: 15px;
    background-size: 15px;
}

.block_l2 .cotent .row1 .b2 .fs12 {
    background: url(../../img/a0-1_warn.png) no-repeat left center;
    -webkit-background-size: 15px;
    background-size: 15px;
}
.block_l2 .cotent .row1 {
    position: absolute;
    width: 100%;
}

.block_l2 .cotent .row1 .b1 {
    float: left;
    width: 100%;
    height: 40px;
    margin: 10px 0px 5px 0px;
}

.block_l2 .cotent .row1 .b2 {
    position: relative;
    overflow: hidden;
    float: left;
    width: 100%;
    height: 120px;
    margin: 0px 0px 5px 0px;
    transition: 0.5s;
}

.block_l2 .cotent .row1 .b2 .b2-cont {
    position: relative;
    height: 110px;
    text-align: left;
    display: flex;
    flex-direction: column;
    padding-bottom: 10px;
}

.block_l2 .cotent .row1 .b2 .b2-cont div {
    text-align: center;
}

.block_l2 .cotent .row1 .b2 .extr {
    height: 240px;
    text-align: left;
    /* padding-top: 10px; */
    position: relative;
    display: flex;
    flex-direction: column;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 {
    flex: 3;
    display: flex;
    box-align: center;
    align-items: center;
    align-items: center;
    flex-direction: row;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .c {
    height: 100%;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .c:last-child {
    border-left: 1px dashed #1f5fa8;
    margin-left: 10px;
    padding-left: 5px;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .fs22 {
    line-height: 60px;
    background: url(../../img/a0-1_warning.png) center center no-repeat;
    height: 100%;
    vertical-align: middle;
    -webkit-background-size: 60px;
    background-size: 60px;
    padding-top: 5px;
    width: 66px;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .up-items {
    height: 100%;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    flex-direction: column;
    /* width: 108px; */
		width: 150px;
    margin-left: 40px;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .up-items .items {
    width: 100%;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    flex-direction: row;
    flex: 1;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .up-items .items .item1 {
    flex: 1;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .up-items .items .item2 {
    flex: 1;
    text-align: right;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .up-items .items .icon {
    width: 20px;
    height: 20px;
    display: block;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .up-items .items .icon1 {
    background: url(../../img/a0-1_human.png) center center no-repeat;
    -webkit-background-size: 18px;
    background-size: 18px;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .up-items .items .icon2 {
    background: url(../../img/a0-1_mechine.png) center center no-repeat;
    -webkit-background-size: 18px;
    background-size: 18px;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r2 {
    flex: 1;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    flex-direction: row;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r2 .item1 {
    margin-right: 70px;
    padding-top: 4px;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r2 .item2 {
    flex: 2;
    padding-right: 5px;
    text-align: left;
}

.up_red{
  background: url(../../img/arr_up_red.png) no-repeat right 2px;
}
.down_green{
  background: url(../../img/arr_down_green.png) no-repeat right 2px;
}




.block_r .tabc {
  position: absolute;
  width: 100%;
  height: 340px;
  cursor: pointer;pointer-events: auto;
}

.block_r .tabc div{
  text-align: left;
  position: relative;
}
.block_r .tabc span{
  padding: 0;
}

.block_r .tabc1 {
  position:relative; 
  left: 2240px;
  z-index: 10000;
  top: 560px;
  background-color: #08163b;
  height: 160px;
  border-radius: 5px;
}

.block_r .tabc1 .legend {
  font-size: 9px;
  color: #5d9ae3;
}
.block_r .tabc1 .legend .itm {
  margin-right: 3px;
  display: inline-block;
  cursor: pointer;
  user-select: none;
}
.block_r .tabc1 .legend .dot {
  font-size: 20px;
  vertical-align: middle;
}
.block_r .tabc1 .legend .lb {
  color: #5d9ae3;
  vertical-align: middle;
}

.block_r .tabc2 .legend {
  font-size: 11px;
  color: #5d9ae3;
}

.block_r .tabc1 .legend .lbl,
.block_r .tabc2 .legend .lbl {
  text-align: center;
  border-radius: 3px;
  padding: 2px;
}
.block_r .tabc2 .legend .itm {
  margin-right: 6px;
  cursor: pointer;
  user-select: none;
}
.block_r .tabc2 .legend .dot {
  font-size: 20px;
  vertical-align: middle;
}
.block_r .tabc2 .legend .lb {
  color: #5d9ae3;
  vertical-align: middle;
}
.block_r .tabc1 .legend .lst,
.block_r .tabc2 .legend .lst {
  display: block;
  margin-left: -5px;
  line-height: 18px;
}
.block_r .tabc2 .legend .lst2{
  display: block;
  margin-left: -5px;
  line-height: 16px;
}
#scrollpane1 {
  position: absolute;
  top: 8px;
  left: 10px;
  width: 94%;
  height: 260px;
  overflow: hidden;
  background-color: rgba(255,0,0,0);
}
#scrollpane2 {
  position: absolute;
  top: 10px;
  left: 0;
  width: 100%;
  height: 150px;
  overflow: hidden;
  background-color: rgba(255,0,0,0);
}

.block_r .scrollcontent {
  width: 100%;
}
.block_r .scrollcontent .comprow{
  width: 100%;
  height: 156px;
  overflow: hidden;
  transform: rotate(0deg);
}
.block_r .scrollcontent .comprow .head{
  width: 100%;
  height: 32px;
}
.block_r .scrollcontent .comprow .head .tt{
  position: absolute !important;
  left: 0;
  width: 200px;
  height: 24px;
  color: #7dc0ff;
  line-height: 25px;
  background-position: 0 0;
  background-repeat: no-repeat;
  background-size: contain;
}
.block_r .scrollcontent .comprow .head .tt .lb{
  margin-left: 32px;
  font-size: 14px;
  font-weight: bold;
}
.block_r .scrollcontent .comprow .head .tt .num{
  margin-left: 5px;
  font-size: 12px;
}

.block_r .scrollcontent .comprow .head .btns{
  float: right;
  text-align: right;
  position: relative;
  cursor: pointer;pointer-events: auto;
}
.block_r .scrollcontent .comprow .head .btn{
  display: inline-block;
  margin-right:4px;
  height: 20px;
  width: 20px;
  padding: 0;
  cursor: pointer;
  
}



.block_r .scrollcontent .comprow .head .btn_prev {
    background: url(../../img/a0-1_pre.png) no-repeat;
    background-size: contain;
}

.block_r .scrollcontent .comprow .head .btn_prev.prev-disabled {
    background: url(../../img/a0-1_pre-dis.png) no-repeat;
    background-size: contain;
    cursor: default;
}

.block_r .scrollcontent .comprow .head .btn_next {
    background: url(../../img/a0-1_next.png) no-repeat;
    background-size: contain;
}

.block_r .scrollcontent .comprow .head .btn_next.next-disabled {
    background: url(../../img/a0-1_next-dis.png) no-repeat;
    background-size: contain;
    cursor: default;
}


.block_r .scrollcontent .comprow .itmlst{
  position: absolute !important;
  top: 35px;
  left: 0;
  height: 88px;
  width: 200000px;
}

.block_r .scrollcontent .comprow .itmlst .blk{
   position: relative;
   display: inline-block;
   width: 85px;
   height: 106px;
   margin-right: 7px;
   overflow: hidden;
   transform: rotate(0);
   cursor: pointer;
   background: url(../img/b1.1_unsafe_flights_bg.png) no-repeat;
   background-size: 85px 106px;
}
.block_r .tabc1 .scrollcontent .comprow .itmlst .blk {
	margin-right: 10px;
}
.block_r .scrollcontent .comprow .itmlst .blk .time{
  color: #5fc5ff;
  font-size: 6px;
  margin: 6px 8px 3px 8px;
  font-family: "宋体",Arial, sans-serif;
}
.block_r .scrollcontent .comprow .itmlst .blk .time .r{
  float: right;
}
.block_r .scrollcontent .comprow .itmlst .blk .fltno{
  text-align: center;
  font-size: 12px;
  font-weight: bold;
  color: #00e1ff !important;
  margin: 11px 0;
}
.block_r .scrollcontent .comprow .itmlst .blk .city{
  width: 100%;
  text-align: center;
  font-size: 6px;
  color: #5fc5ff;
  height: 22px;
  font-family: "宋体",Arial, sans-serif;
}
.block_r .scrollcontent .comprow .itmlst .blk .bot{
  position: absolute;
  width: 100%;
  bottom: 4px;
  text-align: center;
  font-size: 6px;
  color: #5fc5ff;
  text-overflow:ellipsis;
  white-space: nowrap;
  font-family: "宋体",Arial, sans-serif;
}

/* ------------------------- */
.pop div {
	position: relative;
}
.pop{
  position: absolute;
  width: 390px;
  height: 600px;
  box-shadow: 0 0 2px #000, 0 0 10px rgba(0,0,0,0.5);
  border-radius: 5px;
}
.pop .scrollpane{
  position: absolute;
  width: 100%;
  height: 600px;
  background-color: #bce0fe;
  border-bottom-right-radius: 5px;
  border-bottom-left-radius: 5px;
  transform: rotate(0);
  overflow: hidden;
}
.pop .cont{
  position: relative;
  width: 100%;
}

#pop_tab1 .arr{
  position: absolute;
  height: 16px;
  width: 16px;
  top: 100px;
  right: -6px;
  background-color: #bce0fe;
  transform: rotate(45deg);
}
#pop_tab2 .arr{
  position: absolute;
  height: 16px;
  width: 16px;
  top: 100px;
  right: -6px;
  background-color: #bce0fe;
  transform: rotate(45deg);
}

.pop .head{
  position: relative;
  height: 30px;
  background-color: #90cffe;
  border-bottom: 1px solid #5aa3f1;
  line-height: 30px;
  color: #031c57;
  border-top-right-radius: 5px;
  border-top-left-radius: 5px;
  transform: rotate(0);
  overflow: hidden;
}

.pop .head .fltno{
  display: inline-block;
  font-size: 16px;
  font-weight: bold;
  margin-left: 12px;
  padding-left: 22px;
  line-height: 17px;
  height: 16px;
  background-position: 0 0;
  background-repeat: no-repeat;
  background-size: contain;
}
.pop .head .tag{
  display: inline-block;
  border-radius: 9px;
  height: 16px;
  line-height: 18px;
  padding: 0 6px;
  font-size: 16px;
  font-weight: bold;
  margin-left: 6px;
}
.pop .head .btnx{
  position: absolute;
  display: inline-block;
  width: 18px;
  height: 18px;
  top: 6px;
  right: 7px;
  cursor: pointer;
  background: url(../../img/a4.2_pop_x.png) no-repeat center;
}

.pop .row{
  position: relative;
  padding: 7px 12px;
  margin: 0;
  border-bottom: 1px solid #98c7f7;
  min-height: 30px;
  color: #115096;
  font-size: 14px;
  background-color: #bce0fe;
}
.pop .row_w{
  background-color: #fff;
}
.pop .botrow{
  border-bottom-right-radius: 5px;
  border-bottom-left-radius: 5px;
  transform: rotate(0);
  overflow: hidden;
  border: none;
}

.pop .row1 .date{
  display: inline-block;
  padding-left: 20px;
  margin-right: 10px;
  line-height: 20px;
  background: url(../../img/a4.2_ico_cal.png) no-repeat left center;
}
.pop .row1 .ac{
  display: inline-block;
  padding-left: 22px;
  margin-right: 10px;
  line-height: 20px;
  background: url(../../img/a4.2_ico_plane.png) no-repeat left center;
}
.pop .row1 .cabin{
  display: inline-block;
  padding-left: 16px;
  line-height: 20px;
  background: url(../../img/a4.2_ico_seat.png) no-repeat left center;
}
.pop .label{
  font-size: 16px;
  font-weight: normal;
  color: #508cc9;
  margin: 0;
  padding: 0;
}

.pop .cities{
  text-align: center;
  height: 72px;
}
.pop .cities .lbl{
  position: relative;
  width: 22px;
}
.pop .cities .lbl .lb1{
  position: absolute;
  width: 34px;
  top: 19px;
  left: 0;
  display: inline-block;
  border: 1px solid #73ae03;
  color: #73ae03;
  background-color: #FFF;
  border-radius: 3px;
}
#pop_tab1 .cities .lbl .lb2{
  position: absolute;
  width: 34px;
  top: 40px;
  left: 0;
  display: inline-block;
  border: 1px solid #ab6a00;
  color: #ab6a00;
  background-color: #FFF;
  border-radius: 3px;
}
#pop_tab2 .cities .lbl .lb2{
  position: absolute;
  width: 34px;
  top: 40px;
  left: 0;
  display: inline-block;
  border: 1px solid #0058f8;
  color: #0058f8;
  background-color: #FFF;
  border-radius: 3px;
}
.pop .cities .nm{
  position: relative;
  display: inline-block;
  background: #bce0fe;
  padding: 0 5px;
}
.pop .cities .tm{
  display: block;
  font-weight: bold;
  margin-top: 6px;
}
.pop .cities .tm2{
  color:#ab6a00;
  display: block;
  font-weight: bold;
  margin-top: 3px;
}
.pop .cities .stop{
  position: relative;
}
.pop .cities .line{
  position: absolute;
  display: block;
  width: 100%;
  left: 0px;
  top: 7px;
  border-top: 1px solid #3d98e1;
  z-index: 0;
}
.pop .cities .line .dot{
  position: absolute;
  display: block;
  width: 5px;
  height: 5px;
  background: #3d98e1;
  border-radius: 50%;
}
.pop .cities .line .dot1{
  top: -3px;
  left: -2px;
}
.pop .cities .line .dot2{
  top: -3px;
  right: -2px;
}

.pop .blktit {
  margin: 12px 0 8px 8px;
  border-left: 4px solid #185a9e;
  color: #185a9e;
  height: 14px;
  line-height: 14px;
  padding-left: 8px;
  background-color: #bce0fe;
}
.pop .blkcon {
  margin-left: 8px;
  margin-right: 8px;
  border: 1px solid #98c7f7;
  border-bottom: none;
  margin-bottom: 12px;
  color: #115096;
  font-size: 16px;
  background-color: #dcefff;
}
.pop .blkcon .blkrow{
  width: 100%;
  border-bottom: 1px solid #98c7f7;
  padding: 7px 8px;
}
.pop .blkcon .label {
  display: block;
  text-align: left;
  margin-bottom: 4px;
}
.pop .blkcon .tag{
  text-align: center;
  display: inline-block;
  border-radius: 9px;
  height: 16px;
  line-height: 18px;
  padding: 0 6px;
  font-size: 16px;
  color: #000;
}
.pop .line2{
  display: block;
  font-size: 14px;
  margin-top: 5px;
}

.blue {
    color: #5fc5ff;
}

.logo_txt{
	position: absolute;
	top: 25px;
	left: 122px;
	height: 60px;
	width: 153px;
	background-repeat: no-repeat;
	background-position: center center;
	background-size: 153px 60px;
}
