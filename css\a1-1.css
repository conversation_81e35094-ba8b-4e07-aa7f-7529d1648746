
/**/

#bgcolor {
  position: absolute;
  width: 100%;
  background: rgb(9,72,149);
  background: -moz-radial-gradient(center, ellipse cover,  rgba(17,77,166,1) 0%, rgba(2,12,37,1) 100%);
  background: -webkit-radial-gradient(center, ellipse cover,  rgba(17,77,166,1) 0%,rgba(2,12,37,1) 100%);
  background: radial-gradient(ellipse at center,  rgba(17,77,166,1) 0%,rgba(2,12,37,1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#094895', endColorstr='#020c25',GradientType=1 );
}

.mainchart {
  position: absolute;
  background: transparent !important;
}

.pagetitle{
  position: absolute;
  width: 1306px;
  height: 59px;
  top: 58px;
  left: 30px;
  text-align: center;
}
.maintitle{
  color: #7fc4ff;
  width: 100%;
  font-size: 24px;
  padding: 11px 0 0 0;
  margin: 0 auto;
  font-weight: bold;
}

.top_kpi {
  position: absolute;
  border-left: 4px solid #004f7e;
  height: 49px;
}
.top_kpi .tit{
  padding-left: 8px;
  color: #76bcfd;
  font-size: 14px;
  
}
.top_kpi .kpi{
  padding-left: 7px;
}
.top_kpi .kpi .psg{
  font-size: 28px;
  color: #fff;
}
.top_kpi .kpi .tb{
  font-size: 12px;
  padding-left: 15px;
}

.top_kpi_col1 {
  top: 112px;
  left: 28px;
}
.top_kpi_col2 {
  top: 112px;
  left: 280px;
}

.year_trend {
  position: absolute;
  width: 200px;
  height: 106px;
  bottom: 240px;
  left: 30px;
}

#year_slider {
  position: absolute;
  right: 380px;
  top: 110px;
  width: 110px;
  height: 410px;
}

#year_slider .track{
  position: absolute;
  top: 15px;
  right: 16px;
  width: 12px;
  height: 360px;
  background-color: rgba(49, 175, 255, 0.1);
}

#year_slider .node{
  position: absolute;
  right: 6px;
  width: 80px;
  height: 30px;
  cursor: pointer;
  pointer-events: auto;
}

#year_slider .node .dotb{
  position: absolute;
  top: 1px;
  right: 1px;
  background-color: rgba(49, 175, 255, 0.1);
  width: 30px;
  height: 30px;
  border-radius: 15px;
}

#year_slider .node .dot{
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: #248ece;
  width: 16px;
  height: 16px;
  border-radius: 8px;
}

#year_slider .node .label{
  position: absolute;
  width: 60px;
  text-align: right;
  right: 34px;
  top: 6px;
  font-size: 14px;
  color: #1e8edc;
}

#year_slider .selected{
  top: 5px !important;
  color: #34fbf9 !important;
  font-weight: bold !important;
  font-size: 16px !important;
}

#year_slider .cursor{
  position: absolute;
  width: 41px;
  height: 32px;
  right: 7px;
  background: url(../img/a1.1_year_slider_cursor.png) no-repeat center center;
}

#year_slider .node0{
  top: 0px;
}
#year_slider .node1{
  top: 90px;
}
#year_slider .node2{
  top: 180px;
}
#year_slider .node3{
  top: 270px;
}
#year_slider .node4{
  top: 360px;
}


.block-frame4corner {
  background-color: rgba(0, 13, 36, 0.5);
}



/* ---- block ---- */
.block_bl {
  position: absolute;
  width: 358px;
  height: 222px;
  bottom: 22px;
  left: 20px;

  pointer-events: auto;
}

/* ---- block ---- */
.block_bm {
  position: absolute;
  width: 615px;
  height: 222px;
  bottom: 22px;
  left: 390px;

  pointer-events: auto;
}

.block_bm .col1{
  position: absolute;
  width: 307px;
  height: 222px;
}
.block_bm .col2{
  position: absolute;
  width: 307px;
  height: 222px;
  left: 308px;
}
#chart_bm1_msk {
  pointer-events: none;
  background: url(../img/a1.1_circle1.png) no-repeat center center;
}
#chart_bm2_msk {
  pointer-events: none;
  background: url(../img/a1.1_circle2.png) no-repeat center center;
}

/* ---- block ---- */
.block_br {
  position: absolute;
  width: 330px;
  height: 222px;
  bottom: 22px;
  right: 20px;

  pointer-events: auto;
}


/* ---- block ---- */
.block_tr {
  position: absolute;
  width: 330px;
  height: 404px;
  bottom: 265px;
  right: 20px;

  pointer-events: auto;
  border-top: none;
}

.block_tr .tab {
  position: absolute;
  top: -1px;
  width: 164px;
  height: 36px;
  background-color: #083b74;
  color: #3584d3;
  text-align: center;
  line-height: 36px;
  font-size: 16px;
  cursor: pointer;
}
.block_tr .selected {
  color: #fff;
  background-color: #0e89d2;
}
.block_tr .tab1 {
}
.block_tr .tab2 {
  right: 0;
}

.block_tr table {
  position: absolute;
  top: 37px;
  width: 100%;
  height: 367px;
  font-size: 12px;
  font-weight: normal;
}
.block_tr table thead {
  background-color: #15396f;
  color: #6eb0f5;
}
.block_tr table th {
  height: 32px;
}
.block_tr table td {
  border-bottom: 1px solid #163563;
}
.block_tr td .bar{
  transition: all 0.3s;
  display: inline-block;
  margin-bottom: -2px;
  width: 0px;
  height: 15px;
  margin-right: 3px;
  background: rgb(1,70,139);
  background: -moz-linear-gradient(left,  rgba(1,70,139,1) 0%, rgba(8,130,252,1) 100%);
  background: -webkit-linear-gradient(left,  rgba(1,70,139,1) 0%,rgba(8,130,252,1) 100%);
  background: linear-gradient(to right,  rgba(1,70,139,1) 0%,rgba(8,130,252,1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#01468b', endColorstr='#0882fc',GradientType=1 );
}



/* --- */
.ext_link {
  display: inline-block;
  width: 25px;
  height: 27px;
  cursor: pointer;
  pointer-events: auto;
  background: url(../img/ext_link2.png) no-repeat center center;
}

#ext_link1 {
  position: absolute;
  left: 525px;
  top: 136px;
}
