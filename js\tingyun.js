// window._ty_rum&&window._ty_rum.server||function(t){function e(){}function r(t){return t.charAt(0).toUpperCase()+t.slice(1)}function n(){var t=et(ot);return t||(t=it(),rt(ot,t)),t}function a(){var t=i(st);return t||(t=it(),o(st,t)),t}function i(t){if("string"!=typeof t)return null;var e=A.cookie;if(!e)return null;var r=null,n=e.split(";");return W.each(n,function(e){var n=e.split("=");if(W.trim(n[0])===W.trim(t))return r=n[1],!0}),r}function o(t,e,r){var n=t+"="+e;if(r){var a=new Date;a.setTime(a.getTime()+1e3*r),n+=";expires="+a.toGMTString()}A.cookie=n}function s(t){switch(typeof t){case"object":if(!t)return"null";if(t instanceof Array){for(var e="[",r=0;r<t.length;r++)e+=(r>0?",":"")+s(t[r]);return e+"]"}if(t instanceof Date)return t.getTime().toString();var e="{",r=0;for(var n in t)if("function"!=typeof t[n]){var a=s(t[n]);e+=(r>0?",":"")+s(n)+":"+a,r++}return e+"}";case"string":return'"'+t.replace(/([\"\\])/g,"\\$1").replace(/\n/g,"\\n")+'"';case"number":return t.toString();case"boolean":return t?"true":"false";case"function":return s(t.toString());case"undefined":default:return'"undefined"'}}function u(t){for(var e in t)return!1;return!0}function c(t){return $?$(t):t}function f(){return Date.now?Date.now():(new Date).valueOf()}function l(t,e,r){function n(){var t=W.args.apply(this,arguments);return e(i,t,r)}var a,i=t[t.length-1];if("function"==typeof i){switch(i.length){case 0:a=function(){return n.apply(this,arguments)};break;case 1:a=function(t){return n.apply(this,arguments)};break;case 2:a=function(t,e){return n.apply(this,arguments)};break;case 3:a=function(t,e,r){return n.apply(this,arguments)};break;case 4:a=function(t,e,r,a){return n.apply(this,arguments)};break;case 5:a=function(t,e,r,a,i){return n.apply(this,arguments)};break;default:for(var o=[],s=0,u=i.length;s<u;s++)o.push("_"+s);a=eval("(function(){return function("+o.join(",")+"){var args = [].slice.call(arguments, 0);return e(i, args, r);};})();")}t[t.length-1]=a}return t}function p(t,e){return t&&e&&(t.moduleName=e),t}function d(t,e,r){return function(){try{q=e,r&&h(e),t.apply(this,arguments),r&&m()}catch(n){throw r&&m(),p(n,e)}}}function h(e){W.each(["setTimeout","setInterval"],function(r){W.wrap(!0,t,r,function(t){return function(){var r,n=W.args.apply(this,arguments),a=n[0];return"function"==typeof a&&(r=d(a,e,!0)),r&&(n[0]=r),t.apply?t.apply(this,n):Function.prototype.apply.apply(t,[t,n])}})})}function m(){W.each(["setTimeout","setInterval"],function(e){W.unwrap(t,e)})}function v(t){J&&W.wrap(!1,J.prototype,"addEventListener",function(e){return function(){var r,n=W.args.apply(this,arguments),a=n[1];return"function"==typeof a&&(r=d(a,t,!0)),r&&(n[1]=r),e.apply(this,n)}}),h(t)}function y(){J&&W.unwrap(J.prototype,"addEventListener"),m()}function g(t){return function(t,e){}}function _(){var t=W.parseJSON(et(lt))||{};return delete t[pt],t}function S(){if(this.errs.length){var t=function(t){var e=[],r={};W.each(t,function(t){var e=L(t[1],t[2],t[3],t[6]);r[e]?r[e][4]+=1:r[e]=[t[1],t[2],t[3],"#"==t[4]?A.URL:t[4],1,t[5],t[6],t[7]]});for(var n in r)e.push(r[n]);return e}(this.errs),e=this;W.POST(W.mkurl(Q.server.beacon,"err",{fu:F?F:F++,os:parseInt((f()-(j||Q.st))/1e3)}),W.stringify({datas:t}),{},function(t,r){t||(e.errs=[])})}}function w(){return t.nbperf&&t.nbperf.data}function T(){dt.initend()}function b(){"complete"===A.readyState&&dt.initend()}function E(t,e,r){var n=A.createElement(t);try{for(var a in e)n[a]=e[a]}catch(i){var o="<"+t;for(var a in e)o+=" "+a+'="'+e[a]+'"';o+=">",r||(o+="</"+t+">"),n=A.createElement(o)}return n}function k(t){function e(){dt.send()}return!!Q.load_time||(dt.initend(),Q.load_time=f(),void(9===t?e():setTimeout(e,0)))}function x(){mt||k(9),ht||W.bind(S,dt)(),mt=1}function O(){dt.touch||(dt.touch=f())}function N(t){if(t[6]){var e=t[4],r=t[5];if(r&&"string"==typeof r&&e){r=r.split(/\n/);var n=H.exec(r[0]);n||(n=H.exec(r[1])),n&&n[1]!=e&&(t[4]=n[1]||e,t[2]=n[2]||t[2],t[3]=n[3]||t[3])}}}function L(t,e,r,n){return String(t)+String(e)+String(r)+String(n)}function R(e){var n=arguments,a="unknown",i=t[r(X)+"Event"],o=[f()];if(0!=n.length){if("string"==typeof e){var s=n.length<4?n.length:4;o[1]=n[0],s>2&&(o[2]=n[2],o[3]=0,o[4]=n[1]),s>3&&n[3]&&(o[3]=n[3])}else if(e instanceof Event||i&&e instanceof i){if(o[1]=e.message||(e[X]&&e[X].constructor.name)+(e[X]&&e[X].message)||"",o[2]=e.lineno?e.lineno:0,o[3]=e.colno?e.colno:0,o[4]=e.filename||e[X]&&e[X].fileName||e.target&&e.target.baseURI||"",!o[4]&&K)return;o[4]==A.URL&&(o[4]="#"),e[X]?(o[5]=e[X].stack,o[6]=e[X].moduleName):(o[5]=null,o[6]=null);var u=L(o[1],o[2],o[3],o[6]);if(o[7]=vt[u]?0:1,vt[u]=!0,o[1]===a&&o[4]===a)return;N(o)}dt.errs.push(o)}}function C(t){return function(){var e=arguments;if(!this._ty_wrap){var r=W.args.apply(this,e);this._ty_rum={method:r[0],url:r[1],start:f()}}try{return t.apply(this,e)}catch(n){return Function.prototype.apply.call(t,this,e)}}}function I(e){return"string"==typeof e?e.length:t.ArrayBuffer&&e instanceof ArrayBuffer?e.byteLength:t.Blob&&e instanceof Blob?e.size:e&&e.length?e.length:0}function P(e){return function(){function r(t){var e,r,n=u._ty_rum;if(n){if(4!==n.readyState&&(n.end=f()),n.s=u.status,""==u.responseType||"text"==u.responseType)n.res=I(u.responseText);else if(u.response)n.res=I(u.response);else try{n.res=I(u.responseText)}catch(i){n.res=0}if(n.readyState=u.readyState,n.cb_time=c,e=[n.method+" "+n.url,n.s>0?n.end-n.start:0,c,n.s,n.s>0?0:t,n.res,n.req],n.r&&(r=a(u),r&&(r=r.xData)&&(e.push(r.id),e.push(r.action),e.push(r.time&&r.time.duration),e.push(r.time&&r.time.qu))),Q.aa.push(e),Q.server.custom_urls&&Q.server.custom_urls.length&&!dt.ct){if(!Q.pattern){Q.pattern=[];for(var o=0;o<Q.server.custom_urls.length;o++)Q.pattern.push(new RegExp(Q.server.custom_urls[o]))}for(var o=0;o<Q.pattern.length;o++)if(n.url.match(Q.pattern[o])){dt.ct=n.end+c;break}}dt.sa(),u._ty_rum=null}}function n(){4==u.readyState&&r(0)}function a(e){var r;if(e.getResponseHeader){var n=W.parseJSON(e.getResponseHeader("X-Tingyun-Tx-Data"));n&&n.r&&e._ty_rum&&n.r+""==e._ty_rum.r+""&&(r={name:e._ty_rum.url,xData:n},ft&&t._ty_rum.c_ra.push(r))}return r}function i(t){return function(){var e,r;4==u.readyState&&u._ty_rum&&(u._ty_rum.end=e=f(),u._ty_rum.readyState=4);try{q&&h(q),r=t.apply(this,arguments),q&&m()}catch(a){throw a=p(a,q),q&&m(),q=null,a}return 4==u.readyState&&(c=f()-e),n(),r}}function o(t){return function(){var e=u._ty_rum;return!e||("progress"==t||("abort"==t?r(905):"loadstart"==t?e.start=f():t==X?r(990):"timeout"==t&&r(903),!0))}}function s(t,e){e instanceof Array||(e=[e]);for(var r=0;r<e.length;r++){var n=e[r];W.sh(t,n,o(n),!1)}}if(!this._ty_wrap){this._ty_rum.start=f(),this._ty_rum.req=arguments[0]?I(arguments[0]):0;var u=this,c=0,l=W.wrap(!1,this,"onreadystatechange",i);l||W.sh(this,"readystatechange",n,!1),s(this,[X,"progress","abort","load","loadstart","loadend","timeout"]),l||setTimeout(function(){W.wrap(!1,u,"onreadystatechange",i)},0)}var d=function(){function t(t){var e={},r=/^(?:([A-Za-z]+):)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?/.exec(t);return r&&(e.protocol=r[1]?r[1]+":":"http:",e.hostname=r[3],e.port=r[4]||""),e}return function(e){var r=location;if(e=W.trim(e)){if(e=e.toLowerCase(),e.startsWith("//")&&(e=r.protocol+e),!e.startsWith("http"))return!0;var n=t(e),a=n.protocol===r.protocol&&n.hostname===r.hostname;return a&&(a=n.port===r.port||!r.port&&("http:"===r.protocol&&"80"===n.port||"https:"===r.protocol&&"443"===n.port)),a}return!1}}(),v=arguments;try{var y=Q.server;y&&y.id&&this._ty_rum&&d(this._ty_rum.url)&&(this._ty_rum.r=(new Date).getTime()%1e8,this.setRequestHeader&&this.setRequestHeader("X-Tingyun-Id",y.id+";r="+this._ty_rum.r))}catch(g){}try{return e.apply(this,v)}catch(_){return Function.prototype.apply.call(e,this,v)}}}function D(){var e=Q.agent;if(!e){var r=t._ty_rum;if(r&&r.agent)e=r.agent;else{var n="TINGYUN_DATA",a=i(n);if(a){try{e=W.parseJSON(decodeURIComponent(a))}catch(s){}o(n,"",-1e3)}}e&&(Q.agent=e)}return e}var q,U=t.XMLHttpRequest,A=document,M=Object.defineProperty,B=t.define,J=t.EventTarget,F=0,H=new RegExp("([a-z]+:/{2,3}.*):(\\d+):(\\d+)"),$=t.encodeURIComponent,j=null,X=["err","or"].join(""),G="on"+X,z=t[r(X)],W={wrap:function(t,e,r,n,a){try{var i=e[r]}catch(o){if(!t)return!1}if(!i&&!t)return!1;if(i&&i._ty_wrap)return!1;try{e[r]=n(i,a)}catch(o){return!1}return e[r]._ty_wrap=[i],!0},unwrap:function(t,e){try{var r=t[e]._ty_wrap;r&&(t[e]=r[0])}catch(n){}},each:function(t,e){if(t){var r;for(r=0;r<t.length&&(!t[r]||!e(t[r],r,t));r+=1);}},mkurl:function(t,e){var r=arguments,i=/^https/i.test(A.URL)?"https":"http";if(i=i+"://"+t+"/"+e+"?av=1.2.1.0919&v=1.3.2&key="+c(Q.server.key)+"&ref="+c(A.URL)+"&rand="+f()+"&pvid="+at+"&did="+c(n())+"&sid="+c(a()),"pf"!==e&&Q){var o=D();o&&o.n&&(i+="&n="+c(o.n))}if(r.length>2){var s=r[2];for(var u in s)i+="&"+u+"="+s[u]}for(var l in Y)i+="&"+l+"="+c(Y[l]);return i},GET:function(t,e){function r(){e&&e.apply(this,arguments),n.parentNode&&n.parentNode.removeChild(n)}if(navigator&&navigator.sendBeacon&&nt.test(t))return navigator.sendBeacon(t,null);var n=A.createElement("img");return n.setAttribute("src",t),n.setAttribute("style","display:none"),this.sh(n,"readystatechange",function(){"loaded"!=n.readyState&&4!=n.readyState||r("loaded")},!1),this.sh(n,"load",function(){return r("load"),!0},!1),this.sh(n,X,function(){return r(X),!0},!1),A.body.appendChild(n)},fpt:function(t,e,r){var n=E("div",{style:"display:none"},!1),a=E("iframe",{name:"_ty_rum_frm",width:0,height:0,style:"display:none"},!1),i=E("form",{style:"display:none",action:t,enctype:"application/x-www-form-urlencoded",method:"post",target:"_ty_rum_frm"},!1),o=E("input",{name:"data",type:"hidden"},!0);return o.value=e,i.appendChild(o),n.appendChild(a),n.appendChild(i),A.body.appendChild(n),i.submit(),a.onreadystatechange=function(){"complete"!==a.readyState&&4!==a.readyState||(r(null,a.innerHTML),A.body.removeChild(n))},!0},POST:function(e,r,n,a){if(this.ie)return this.fpt(e,r,a);if(navigator&&navigator.sendBeacon&&nt.test(e)){var i=navigator.sendBeacon(e,r);return a(!i),i}var o;if(t.XDomainRequest)return o=new XDomainRequest,o.open("POST",e),o.onload=function(){a(null,o.responseText)},this.sh(o,"load",function(){a(null,o.responseText)},!1),this.sh(o,X,function(){a("POST("+e+")err")},!1),this.wrap(!0,o,G,function(t){return function(){return a&&a("post err",o.responseText),!0}}),o.send(r),!0;if(!U)return!1;o=new U,o.overrideMimeType&&o.overrideMimeType("text/html");try{o._ty_wrap=1}catch(s){}var u=0;o.onreadystatechange=function(){4==o.readyState&&200==o.status&&(0==u&&a(null,o.responseText),u++)},o[G]&&this.wrap(!0,o,G,function(t){return function(){return a("post err",o.responseText),"function"!=typeof t||t.apply(this,arguments)}});try{o.open("POST",e,!0)}catch(s){return this.fpt(e,r,a)}for(var c in n)o.setRequestHeader(c,n[c]);return o.send(r),!0},sh:function(t,e,r,n){return t.addEventListener?t.addEventListener(e,r,n):!!t.attachEvent&&t.attachEvent("on"+e,r)},args:function(){for(var t=[],e=0;e<arguments.length;e++)t.push(arguments[e]);return t},stringify:s,parseJSON:function(e){if(e&&"string"==typeof e){var r=t.JSON?t.JSON.parse:function(t){return new Function("return "+t)()};return r(e)}return null},trim:tt?function(t){return null==t?"":tt.call(t)}:function(t){return null==t?"":t.toString().replace(/^\s+/,"").replace(/\s+$/,"")},extend:function(t,e){if(t&&e)for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r]);return t},bind:function(t,e){return function(){return t.apply(e,arguments)}}},Y={},Z={host:"cshst",url:"csurl"},Q=t._ty_rum=t.TINGYUN=W.extend({st:f(),ra:[],c_ra:[],aa:[],snd_du:function(){return this.server.adu?1e3*this.server.adu:1e4},cc:function(){return this.server.ac?this.server.ac:10},config:function(t,e){var r;if("object"==typeof t)r=t;else{if("string"!=typeof t||void 0===e)throw new z("illegal arguments");r={},r[t]=e}for(var n in r)n in Z?Y[Z[n]]=r[n]:Y[n]=r[n];return this},ty_set_userprofile:function(t,r){if(!t||!t.id)throw new z("User or user's id is empty.");var n=W.mkurl(Q.server.beacon,"userprofile");W.POST(n,W.stringify({user:t,properties:r||{}}),{},e)},ty_track_event:function(t,r,n){if("string"!=typeof t)throw new z("Event Id is invalid.");var a=W.mkurl(Q.server.beacon,"event");W.POST(a,W.stringify({event_id:t,tag:r||"",properties:n||{},uf:_()}),{},e)},setUserLabel:function(t){if("string"==typeof t)return this.config("ulabel",t.substr(0,128));throw new z("illegal arguments")}},t._ty_rum||{});var ty_rum=Q;ty_rum.server = {id:'p35OnrDoP8k',beacon:'browser_dc_host',beacon_err:'browser_dc_host',key:'p35OnrDoP8k',trace_threshold:5000,custom_urls:[],sr:1.0};if(Q.server&&!(Q.server.sr&&Math.random()>=Q.server.sr)){var V="ignore_err",K=!(V in Q.server)||Q.server[V],tt=String.prototype.trim;String.prototype.startsWith||(String.prototype.startsWith=function(t,e){return e=e||0,this.indexOf(t,e)===e});var et,rt,nt=/^http/i,at=function(){function t(){return(65536*(1+Math.random())|0).toString(16).substring(1)}return t()+"-"+t()+t()}(),it=function(){function t(t){return t<0?NaN:t<=30?0|Math.random()*(1<<t):t<=53?(0|Math.random()*(1<<30))+(0|Math.random()*(1<<t-30))*(1<<30):NaN}function e(t,e){for(var r=t.toString(16),n=e-r.length,a="0";n>0;n>>>=1,a+=a)1&n&&(r=a+r);return r}return function(){return e(t(32),8)+"-"+e(t(16),4)+"-"+e(16384|t(12),4)+"-"+e(32768|t(14),4)+"-"+e(t(48),12)}}(),ot="TY_DISTINCT_ID",st="TY_SESSION_ID",ut=function(){try{return localStorage.setItem(at,at),localStorage.removeItem(at),!0}catch(t){return!1}}();ut?(et=W.bind(localStorage.getItem,localStorage),rt=W.bind(localStorage.setItem,localStorage)):(et=i,rt=o);try{M&&M(t,"define",{get:function(){return B},set:function(t){"function"==typeof t&&(t.amd||t.cmd)?(B=function(){var e=W.args.apply(this,arguments);if(3!==e.length)return t.apply(this,e);var r="string"==typeof e[0]?e[0]:"anonymous";return t.apply(this,l(e,function(t,e,r){var n;try{q=r,v(r),n=t.apply(this,e),y()}catch(a){throw y(),p(a,r)}return n},r))},W.extend(B,t)):B=t},configurable:!0})}catch(ct){}var ft=t.performance?t.performance:t.Performance;ft&&(W.sh(ft,"resourcetimingbufferfull",function(){var t=ft.getEntriesByType("resource");t&&(Q.ra=Q.ra.concat(t),ft.clearResourceTimings())},!1),W.sh(ft,"webkitresourcetimingbufferfull",function(){var t=ft.getEntriesByType("resource");t&&(Q.ra=Q.ra.concat(t),ft.webkitClearResourceTimings())},!1));var lt="_ty_uf_data",pt="_ty_first_day";!function(){function e(t){var e=t[pt];e?(e=parseInt(e),new Date(e).toDateString()===(new Date).toDateString()?t[a]=!0:t[a]=!1,t[i]=!1):(t[pt]=f(),t[a]=!0,t[i]=!0)}function r(t){var e={};if(t=t||location.href,!t)return e;var r=t.indexOf("?");return r>=0&&(t=t.substring(r+1)),t?(W.each(t.split("&"),function(t){var r=t.split("="),a=n(r[0]),i=n(r[1]);a&&i&&(e[a]=i)}),e):e}function n(t){try{return decodeURIComponent(t)}catch(e){}return null}var a="is_first_day",i="is_first_time",o="latest_referrer",s=2592e3,u=W.parseJSON(et(lt))||{};e(u);var c=r(location.search),l=["utm_source","utm_medium","utm_term","utm_content","utm_campaign"];W.each(l,function(t){c[t]&&(u["latest_"+t]=c[t])}),u.pageref=A.referrer||"",u.first_browser_language=navigator.language||navigator.browserLanguage,u[o]=u[o]||"",u.screen_height=t.screen&&t.screen.height,u.screen_width=t.screen&&t.screen.width,rt(lt,W.stringify(u),s)}();for(var dt=Q.metric={ready:function(){return Q.load_time},initend:function(){function t(){dt.sa()}Q.end_time||(Q.end_time=f(),this._h=setInterval(t,2e3))},send:function(){function e(){function e(t){return n[t]>0?n[t]-a:0}var r={};if(!ft&&(ft=w(),"string"==typeof ft&&(ft=W.parseJSON(ft),u(ft))))return r;if(ft&&ft.timing){var n=ft.timing;a=n.navigationStart;var i=e("domainLookupStart"),o=e("domainLookupEnd"),s=e("redirectStart"),f=e("redirectEnd"),l=e("connectStart"),p=e("connectEnd");r={f:e("fetchStart"),qs:e("requestStart"),rs:e("responseStart"),re:e("responseEnd"),os:e("domContentLoadedEventStart"),oe:e("domContentLoadedEventEnd"),oi:e("domInteractive"),oc:e("domComplete"),ls:e("loadEventStart"),le:e("loadEventEnd"),tus:e("unloadEventStart"),tue:e("unloadEventEnd")},p-l>0&&(r.cs=l,r.ce=p),o-i>0&&(r.ds=i,r.de=o),(f-s>0||f>0)&&(r.es=s,r.ee=f),0==r.le&&(r.ue=Q.load_time-(w()?Q.st:a));var d;if(n.msFirstPaint)d=n.msFirstPaint;else if(t.chrome&&chrome.loadTimes){var h=chrome.loadTimes();h&&h.firstPaintTime&&(d=1e3*h.firstPaintTime)}else Q.firstPaint&&(d=Q.firstPaint);d&&(r.fp=Math.round(d-a)),n.secureConnectionStart&&(r.sl=e("secureConnectionStart"))}else r={t:a,os:Q.end_time-a,ls:Q.load_time-a,le:Q.load_time-a};r.je=dt.errs.length,dt.ct&&(r.ct=dt.ct-a),dt.touch&&(r.fi=dt.touch-a);var m=D();return m&&(r.id=c(m.id),r.a=m.a,r.q=m.q,r.tid=c(m.tid),r.n=c(m.n),m.ulabel&&Q.setUserLabel(m.ulabel)),r.sh=t.screen&&t.screen.height,r.sw=t.screen&&t.screen.width,r}function r(e){var r=t._ty_rum.c_ra;if(e)for(var n=r.length-1;n>=0;n--)if(e.indexOf(r[n].name)>-1)return r[n].xData;return null}function n(t){function e(t){return f[t]>0?f[t]:0}var n={tr:!1,tt:c(A.title),charset:A.characterSet,uf:_()};if(t<Q.server.trace_threshold)return n;n.tr=!0;var i=ft;if(i||(i=w(),"string"==typeof i&&(i=W.parseJSON(i))),i){var o=Q.ra,s=null;i.getEntriesByType?s=i.getEntriesByType("resource"):i.getEntries&&(s=i.getEntries),s&&(o=o.concat(s),i.webkitClearResourceTimings&&i.webkitClearResourceTimings(),i.clearResourceTimings&&i.clearResourceTimings()),n.res=[];for(var u=0;u<o.length;u++){var f=o[u],l={o:e("startTime"),rt:f.initiatorType,n:f.name,f:e("fetchStart"),ds:e("domainLookupStart"),de:e("domainLookupEnd"),cs:e("connectStart"),ce:e("connectEnd"),sl:e("secureConnectionStart"),qs:e("requestStart"),rs:e("responseStart"),re:e("responseEnd")};w()&&(l.ec=f[X+"Code"]||0);var p=r(f.name);p&&(l.aid=p.id,l.atd=p.trId,l.an=p.action,l.aq=p.time&&p.time.qu,l.as=p.time&&p.time.duration),n.res.push(l)}if(dt.errs.length){n.err=[];for(var u=0,d=dt.errs,h=d.length;u<h;u++){var m=d[u][0]-(w()?Q.st:a);n.err.push({o:Math.round(m),e:d[u][1],l:d[u][2],c:d[u][3],r:d[u][4],ec:h,s:d[u][5],m:d[u][6],ep:d[u][7]})}}}return n}if(this.sended)return!1;if(!this.ready())return!1;var a=Q.st,i={},o={};try{if(o=e(),!o||u(o))throw new z("err:empty pf");i=n(o.ls>0?o.ls:Q.load_time-a),i=i?W.stringify(i):"";var s=W.mkurl(Q.server.beacon,"pf",o);j=f(),0!=i.length&&W.POST(s,i,{},g("POST"))||W.GET(s),this.sended=!0;var l=W.bind(S,this);l(),setInterval(l,1e4),this.sa(1)}catch(p){ht=!0}return!0},sa:function(t){(this.ready()||t)&&(t||(t=!this._last_send||f()-this._last_send>Q.snd_du()||Q.aa.length>=Q.cc()),Q.aa.length>0&&t&&(this._last_send=f(),W.POST(W.mkurl(Q.server.beacon,"xhr"),W.stringify({xhr:Q.aa}),{},g("POST")),Q.aa=[]))},errs:[]},ht=!1,mt=null,vt={},yt=[["load",k],["beforeunload",x],["pagehide",x],["unload",x]],gt=0;gt<yt.length;gt++)W.sh(t,yt[gt][0],yt[gt][1],!1);t.addEventListener?W.sh(t,X,R,!1):t[G]=function(t,e,r,n,a){if(e||!K){var i=[f(),t,r,n,e==A.URL?"#":e],o=L(t,r,n,a&&a.moduleName);i=i.concat([a&&a.stack,a&&a.moduleName,vt[o]?0:1]),vt[o]=!0,N(i),dt.errs.push(i)}};for(var _t=[["scroll",O],["keypress",O],["click",O],["DOMContentLoaded",T],["readystatechange",b]],gt=0;gt<_t.length;gt++)W.sh(A,_t[gt][0],_t[gt][1],!1);if(W.wrap(!1,t,"requestAnimationFrame",function(e){return function(){return Q.firstPaint=f(),t.requestAnimationFrame=e,e.apply(this,arguments)}}),U)if(U.prototype)W.wrap(!1,U.prototype,"open",C),W.wrap(!1,U.prototype,"send",P);else{W.ie=7;var St=U;t.XMLHttpRequest=function(){var t=new St;return W.wrap(!1,t,"open",C),W.wrap(!1,t,"send",P),t}}else t.ActiveXObject&&(W.ie=6)}}(window);