showLoading();


// ------------------------------------------------------------

$('#mainframe').attr('src', 'map/map-a3-3.html');

// ------------------------------------------------------------



var current_company_code;


// 所有飞机架数
var total_plane = -1;
// 执行中飞机架数
var exe_total_plane = -1;
// 机型对照表
var actypeMapList;
// 空中飞机架数
var plane_over_air = -1;
var ac_type_list = ['787', '767', '330', '737', '320', '321', '319', '350', '145', '190'];
//var ac_type_list = ['787', '767', '330', '737'];
var ac_data_list_ready;
var ac_data_list;
var exe_ac_data_list;

// 飞机位置信息
var planeLocationList;

// 飞机航班信息
var flightInfoList;

// 机场列表
var airportList;

// 机型id／code对应表
var actypeId2Code;

var weather = {};


var marquee_itv_airPlane;
var marquee_itv_alertAirport;



// 在飞航班
var currentCounterNumber1 = 0;
// 延误航班
var currentCounterNumber2 = 0;
// 滚动数字
// elementId DOM ID
// numFrom 开始的数字
// numTo 滚动到的数字
function setCounter (elementId, numFrom, numTo) {

  $.animateToNumber(elementId, numTo, numFrom, null, 'group', {
    0: '架'
  });
  numFrom = numTo;

}



var codelist = [];
var codelist_no_parent = [];



// 获取所有机型
var param = {}

$.ajax({
  type: 'post',
  url: "/bi/web/actypeall",
  contentType: 'application/json',
  dataType: 'json',
  async: true,
  data: JSON.stringify(param),
  success: function (response) {

    if (response.actype) {
      var list = response.actype;
      actypeId2Code = {};


      var len = list.length;
      for (var i = 0; i < len; i++) {
        var obj = list[i];
        actypeId2Code[obj.id] = obj.code;
      }

    }

  },
  error: function () { }
});



// 获取基本数据
function getGeneralKpi () {

  if (companylist.length == 0) {
    setTimeout(getGeneralKpi, 0);
    return;
  }


  // 所有飞机架数
  total_plane = -1;
  // 执行中飞机架数
  exe_total_plane = -1;
  // 机型对照表
  actypeMapList;
  // 空中飞机架数
  plane_over_air = -1;
  ac_data_list_ready = false;



  // check url hash
  var hash = window.location.hash.substr(1);
  if (hash && companyCode2Name[hash] != undefined) {
    current_company_code = hash;
  } else {
    current_company_code = parent_company;
  }



  // 接口用到的当日 开始 结束 时间
  var date = new Date();
  var mm = date.getMonth() + 1;
  var dd = date.getDate();
  if (mm < 10) {
    mm = '0' + mm;
  }
  if (dd < 10) {
    dd = '0' + dd;
  }

  var stdEndUtcTime = date.getFullYear() + '-' + mm + '-' + dd + ' 15:59:59';
  var yesterday_ts = date.getTime() - 86400000;
  var twodayago_ts = date.getTime() - 86400000 * 2;
  date.setTime(yesterday_ts);
  mm = date.getMonth() + 1;
  dd = date.getDate();
  if (mm < 10) {
    mm = '0' + mm;
  }
  if (dd < 10) {
    dd = '0' + dd;
  }
  var stdStartUtcTime = date.getFullYear() + '-' + mm + '-' + dd + ' 16:00:00';
  // --- 时间end


  var len = companylist.length;

  codelist = [];
  codelist_no_parent = [];

  planeLocationList = undefined;


  for (var i = 0; i < len; i++) {

    var dat = companylist[i];
    if (dat.code != parent_company && (current_company_code == parent_company || current_company_code == dat.code)) {
      codelist_no_parent.push(dat.code);
    }
    if (current_company_code == parent_company || current_company_code == dat.code) {
      codelist.push(dat.code);
    }

  }


  // ------------------------------------------------------------------------
  // 获取航班信息
  // ------------------------------------------------------------------------

  // 开始结束时间
  var date = new Date();
  var mm = date.getMonth() + 1;
  var dd = date.getDate();
  if (mm < 10) {
    mm = '0' + mm;
  }
  if (dd < 10) {
    dd = '0' + dd;
  }
  var stdStart = date.getFullYear() + '-' + mm + '-' + dd + ' 00:00:00';
  /*
  var end_ts = date.getTime()+86400000;
  date.setTime(end_ts);
  mm = date.getMonth()+1;
  dd = date.getDate();
  if(mm < 10){
      mm = '0' + mm;
  }
  if(dd < 10){
      dd = '0' + dd;
  }
  */
  var stdEnd = date.getFullYear() + '-' + mm + '-' + dd + ' 23:59:59';
  //



  var companyCode = '';
  if (current_company_code != parent_company) {
    companyCode = current_company_code;
  }
  //var fltIntList = [];
  var param = {
    "stdStart": stdStart,
    "stdEnd": stdEnd,
    "acOwner": companyCode,
    "statusList": 'DEP', // 只返回起飞的，表示在空中
  }
  $.ajax({
    type: 'post',
    url: "/bi/web/getStandardFocFlightInfo",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var list = response.data;
      flightInfoList = {};
      for (var i = list.length - 1; i >= 0; i--) {
        var obj = list[i];
        flightInfoList[obj.flightNo] = obj;

        // 国际航班
        if (obj.fltType == 'I') {
          //fltIntList.push(obj);
        }
      }
      //console.log('fltIntList', fltIntList);
      setPlaneLocation();

      if ($('#earth3d-wrapper').is(':visible')) {
        if (!chart_earch) {
          crate3DEarth();
        }
        set3Dlines();
      }

    },
    error: function () { }
  });

  // ------------------------------------------------------------------------
  // 各种航班统计信息。。。。
  // ------------------------------------------------------------------------
  var param = {
    "stdStartUtcTime": stdStartUtcTime,
    "stdEndUtcTime": stdEndUtcTime,
    "companyCodes": codelist_no_parent.join(','), //
    "returnAbnormalFlightByCompanyReason": "true",
    "AcTypeList": "",
    "depstns": "",
    "arrstns": ""
  }

  $.ajax({
    type: 'post',
    url: "/bi/spring/focStaticApi/flightAmountStaticV2",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (res) {
      var response = res.data;
      var sch_total = Number(response.pftc); //计划航班总数
      var sch_normal = Number(response.pfrtc); //计划航班中正常航班总数
      var pfPercent = Number(response.pfdappPercent); //计划航班中的正常航班比率
      var normal_rate = Math.round(pfPercent * 10) / 10;
      $('.block_tl .row2 .col2 .val').text(normal_rate);

      var dftc = Number(response.dftc); //备降航班总数
      var bftc = Number(response.bftc); //返航航班总数
      /*
      qftc 取消航班总数
      qftc0 昨日取消航班总数
      qftc1 今日取消航班总数
      qftc2 次日取消航班总数
      */
      var qftc = Number(response.qftc1); //今日取消航班总数
      var pfdtc = Number(response.pfdtc); //计划航班中延误航班总数

      var unnormal = sch_total - sch_normal; //dftc + bftc + qftc + pfdtc;
      unnormal = Math.max(unnormal, 1);

      var ftc = dftc + bftc;
      $('#flt_return_back .kpi').text(ftc);
      $('#flt_return_back .per').text(Math.round(ftc / unnormal * 1000) / 10 + '%'); // 占比：N／不正常航班数量

      $('#flt_cancel .kpi').text(qftc);
      $('#flt_cancel .per').text(Math.round(qftc / unnormal * 1000) / 10 + '%'); // 占比：N／不正常航班数量

      var pfdtc1 = Number(response.pfdtc1); //计划航班中延误1~2小时的航班总数
      console.log("1小时内延误数=", pfdtc1)

      var pfdtc12 = Number(response.pfdtc12); //计划航班中延误1~2小时的航班总数
      $('#flt_delay_12 .kpi').text(pfdtc12);
      $('#flt_delay_12 .per').text(Math.round(pfdtc12 / unnormal * 1000) / 10 + '%'); // 占比：N／不正常航班数量

      var pfdtc24 = Number(response.pfdtc24); //计划航班中延误2-4小时的航班总数
      $('#flt_delay_24 .kpi').text(pfdtc24);
      $('#flt_delay_24 .per').text(Math.round(pfdtc24 / unnormal * 1000) / 10 + '%'); // 占比：N／不正常航班数量

      var pfdtc4 = Number(response.pfdtc4); //计划航班中延误>4小时的航班总数
      $('#flt_delay_4 .kpi').text(pfdtc4);
      $('#flt_delay_4 .per').text(Math.round(pfdtc4 / unnormal * 1000) / 10 + '%'); // 占比：N／不正常航班数量

      var facnc = Number(response.facnc); //空中飞机架数
      //plane_over_air = facnc;
      //setPlaneOverAirOnGround();


      var fftc = Number(response.fftc); //执行航班总数
      var cftc = Number(response.cftc); //已执行航班总数
      var exe_rate = (cftc) / sch_total
      $('#val_flt_total .val').text(sch_total);
      $('#val_flt_exec .val').text(cftc);

      // 执行中航班架数
      // $('.block_tl .row2 .col1 .val').text(fftc); //PCR_285

      // 航班 仪表盘
      drawGauge('cvs_shift', 'cvs_shift_pointer', exe_rate);


      //var pftci = Number(response.pftci);//国际计划航班总数
      //var pftcl = Number(response.pftcl);//国内计划航班总数
      //var fftci = Number(response.fftci);//国际执行航班总数
      //var fftcl = Number(response.fftcl);//国内执行航班总数

      // 滚动数字
      //setCounter('counterContainer1', currentCounterNumber1, facnc); // 空中
      //setCounter('counterContainer2', currentCounterNumber2, pfdtc); // 延误
      currentCounterNumber1 = facnc;
      currentCounterNumber2 = pfdtc;

      var pfdtc = Number(response.pfdtc);
      console.log("总数=", pfdtc)
      var abfcr = Number(response.abfcr);
      setDelayCauseNew(pfdtc, abfcr);



      hideLoading();

      // 语音播放
      audio_txt_data.SHIFT1 = Number(sch_total);
      audio_txt_data.SHIFT2 = Number(cftc);
      audio_txt_data.NORMAL_RATE = normal_rate;

      loadAudio();



    },
    error: function () {
      hideLoading();
    }
  });



  // ------------------------------------------------------------------------
  // 计划旅客总量 已完成占比 
  // ------------------------------------------------------------------------

  /*
  BOOK_NUM 计划运输旅客总量
  CKI_NUM 已完成旅客运输总量
  */
  /*
  var param = {
      "SOLR_CODE":"FAC_COMP_KPI",
      "COMP_CODE":codelist.join(','),
      "KPI_CODE":"BOOK_NUM,CKI_NUM",
      "VALUE_TYPE":"kpi_value_d",
      "DATE_TYPE":"D",
      "LIMIT":1
  }

  $.ajax({           

          type: 'post',
          url:"/bi/query/getkpi",
          contentType: 'application/json',
          dataType: 'json',
          async: true,
          data: JSON.stringify(param),
          success: function(response) {
              var book_num_total = 0;
              var cki_num_total = 0;

              for(var comp_code in response.data){
                  var book_nums = response.data[comp_code]['BOOK_NUM']['D'];
                  var book_num;
                  for(var time in book_nums){
                      book_num = book_nums[time];
                      book_num_total += Number(book_num);
                  }
                  var cki_nums = response.data[comp_code]['CKI_NUM']['D'];
                  var cki_num;
                  for(var time in cki_nums){
                      cki_num = cki_nums[time];
                      cki_num_total += Number(cki_num);
                  }
              }
              

              $('#val_trv_num_plan .val').text(Number(book_num_total));
              $('#val_trv_num_completed .val').text(Number(cki_num_total));

              var rate_trv_completed = Number(cki_num_total)/Number(book_num_total);

              
              // 运输旅客 仪表盘
              drawGauge('cvs_psr', 'cvs_psr_pointer', rate_trv_completed);

              // 语音播放
              audio_txt_data.TRV1 = Number(book_num_total);
              audio_txt_data.TRV2 = Number(cki_num_total);
              loadAudio();

          },
          error:function() {
          }
  });
  */

  // ------------------------------------------------------------------------
  // 计划旅客+已完成
  // ------------------------------------------------------------------------
  var planNum; //旅客订票人数
  var ckiNum; //旅客值机人数

  var param = {
    "companyCodes": codelist_no_parent.join(','),
    "stdStartUtcTime": stdStartUtcTime,
    "stdEndUtcTime": stdEndUtcTime,
    "detailType": "pftc", //统计航班（总计）
    //"psrType":"all"
  }

  $.ajax({
    type: 'post',
    url: "/bi/web/getPsrSummInfo",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      planNum = Number(response.planNum); //旅客订票人数
      setTrvNum();
    },
    error: function () { }
  });


  var param = {
    "companyCodes": codelist_no_parent.join(','),
    "stdStartUtcTime": stdStartUtcTime,
    "stdEndUtcTime": stdEndUtcTime,
    "detailType": "cftc", //已执行
    //"psrType":"all"
  }

  $.ajax({
    type: 'post',
    url: "/bi/web/getPsrSummInfo",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      ckiNum = Number(response.ckiNum); //旅客值机人数
      setTrvNum();
    },
    error: function () { }
  });

  function setTrvNum () {
    if (planNum >= 0 && ckiNum >= 0) {
      $('#val_trv_num_plan .val').text(planNum);
      $('#val_trv_num_completed .val').text(ckiNum);

      var rate_trv_completed = ckiNum / planNum;

      // 运输旅客 仪表盘
      drawGauge('cvs_psr', 'cvs_psr_pointer', rate_trv_completed);

      // 语音播放
      audio_txt_data.TRV1 = planNum;
      audio_txt_data.TRV2 = ckiNum;
      loadAudio();
    }
  }



  // ------------------------------------------------------------------------
  // 运力分布 - 可用运力
  // ------------------------------------------------------------------------
  /*
  日运力相关逻辑和处理要点如下 
   
  一：日运力总数调用接口
   
          1.1 请求处理
          String acTypeFilter = "D00,D10";//需要过滤掉的机型
          ApiRequest req = new ApiRequest();
          req.setOption("acOwners", StringUtils.join(airCodeSet));
          req.setOption("acTypeNotIn", acTypeFilter);
          PageParam pageParam = new PageParam();
          pageParam.setPageIndex(1);
          pageParam.setPageSize(MyUtil.pageLimit);//2000
          req.setPageParam(pageParam);
          ApiResponse res = acTypeApi.getPdcAcReg(req);
   
     1.2 结果集处理
   
        金鹏结果集要过滤掉货舱F0的数据
         if ("Y8".equals(airCode) && "F0".equals(cabin)) {
                  continue;
              }
        获取所有的短机号：acreg
   
     有多少个短机号，就有多少架总运力；通过“acOwner”来进行航司分组，可以获取不同航司的总运力。
   
   
  二：日可用运力
     2.1 请求处理
       shortNos不依据不同的航司的机号列表作为参数（这个参数来自计算总运力的接口返回进行加工的）
   
          ApiRequest req = new ApiRequest();
          req.setOption("mntUtcStart", startUtcTime);//北京时间某日 00:00:00转换类UTC时间
          req.setOption("mntUtcEnd", endUtcTime); //北京时间某日 23:59:59转换类UTC时间
          req.setOption("acregs", shortNos);//短机号列表List<String>
          boolean isToday = this.isToday(fltDate);
          if (isToday) {
              String nowStr = DateUtil.getDefaultNowStr();
              req.setOption("nowInMntUtcRange", MyUtil.convertBJ2OhterTime(nowStr, "0000")); // 日区分为当日和非当日，当日要传这个参数，当前时间转为UTC时间
          }
          PageParam param = new PageParam();
          param.setPageIndex(1);
          param.setPageSize(MyUtil.pageLimit);//2000
          req.setPageParam(param);
          ApiResponse res = focMaintApi.getFocMaintInfoByListByPage(req);
   
  2.2    停场计算规则
   
  2.2.1当日：按实时情况查询的逻辑（实时查询当日情况：以实时为准，查询的时间点若有飞机处于计划或非计划停场，则统计计划或非计划停场架次为1；否则，为0；举例：某飞机0700-1200处于非计划停场，1000查询该飞机则统计为非计划停场架次1，1300查询则统计为非计划停场架次0。）
  2.2. 2 非当日的天：当蓝色逻辑（每日0600-2400期间，计划或非计划停场时间小于9小时，计划或非计划停场架次统计为0；9（含）-12小时统计为0.5；大于等于12小时，统计为1。可用运力=总运力-停场。）
  移动取的还是北就时间00：00：00至23：59：59，然后转为utc时间
  2.2.3  接口输出：
  2.2.3.1运力要去重：一架飞机有多个停场计划,取最晚依据结束时间(tEnd)
   
  2.2.3.2 停场类型判断：String type = (String) dataMap.get("distinctcol");//1计划 2非计划
   
  2.3 可用运力=总运力-停场运力

  */

  // ------------------------------------------------------------------------


  var company = current_company_code == parent_company ? '' : current_company_code;


  var url = `/bi/spring/aircraft/getAcStatusStat?company=${company}`;

  $.ajax({
    type: 'get',
    url: url,
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    success: function (response) {
      var data = response.data;
      $('#total_plane_num').text(data.total);
      $('#plane_on_stop').text(data.maintCnt); //停场
      $('#plane_on_ground').text(data.groundCnt);
      $('#plane_over_air').text(data.airCnt);
      $('.block_tl .row2 .col1 .val').text(data.airCnt);
      var detail = data.detail;
      var html = "";
      var barwidth = 30;
      var linenum = 0
      detail.forEach(i => {
        var numac = i.cnt;
        var actype = i.actype;
        var airCnt = i.airCnt;
        var groundCnt = i.groundCnt;
        var maintCnt = i.maintCnt;

        html += '<div class="baritmrow plane_ac_' + actype + '">';
        html += '<span class="acno">' + actype + '</span>';

        html += '<span class="val val_l">' + airCnt + '</span>';
        html += '<span class="bar darkbar_g">';
        html += '<span class="bar_ac_air innerbar bluebar_new" style="width: ' + (airCnt / numac * barwidth) + 'px;"></span>';
        html += '</span>';

        html += '<span class="val val_l" style="width:30px;">' + groundCnt + '</span> ';
        html += '<span class="bar darkbar_b">';
        html += '<span class="bar_ac_air innerbar greenbar_new" style="width: ' + (groundCnt / numac * barwidth) + 'px;"></span>';
        html += '</span>';

        html += '<span class="val val_l" style="width:30px;">' + maintCnt + '</span> ';
        html += '<span class="bar darkbar_r">';
        html += '<span class="bar_ac_air innerbar brownbar_new" style="width: ' + (maintCnt / numac * barwidth) + 'px;"></span>';
        html += '</span>';

        html += '</div>';
        linenum++
      });

      $('#air_aclist1').html(html);
      // 详细机型列表滚动
      clearInterval(marquee_itv_airPlane);
      $('#air_aclist2').html('');
      if (linenum > 4) {
        var speed = 80;
        var base_sec = document.getElementById("air_aclist");
        var base_sec2 = document.getElementById("air_aclist2");
        var base_sec1 = document.getElementById("air_aclist1");
        base_sec2.innerHTML = base_sec1.innerHTML;

        function base_Marquee () {
          if (base_sec2.offsetTop - base_sec.scrollTop <= 0)
            base_sec.scrollTop -= base_sec1.offsetHeight;
          else {
            base_sec.scrollTop += Math.ceil(1 / pageZoomScale);
          }
        }

        marquee_itv_airPlane = setInterval(base_Marquee, speed);
        base_sec.onmouseover = function () {
          clearInterval(marquee_itv_airPlane)
        }
        base_sec.onmouseout = function () {
          marquee_itv_airPlane = setInterval(base_Marquee, speed)
        }
      }

      audio_txt_data.AIR = data.airCnt;
      loadAudio();

    },
    error: function () { }
  });





  // ------------------------------------------------------------------------
  // 今日延误原因分析
  // ------------------------------------------------------------------------
  function getDelayRate (v, vdiv) {
    if (v === 0) {
      return 0;
    }
    if (vdiv === 0) {
      return 50;
    }
    return Math.round(v / vdiv * 1000) / 10
  }

  function setDelayCauseNew (total, compCnt) {
    // 公司原因占比
    var rate_comp = total > 0 ? compCnt / total : 0;

    var none_total = total - compCnt

    // 非公司占比
    var rate_none = total > 0 ? none_total / total : 0;
    setDelayCauseChart(rate_comp);

    // percent
    $('#page_leve1 .block_m2 .b1 .kpi .val').text(getDelayRate(compCnt, total));
    $('#page_leve1 .block_m2 .b2 .kpi .val').text(getDelayRate(none_total, total));

    $('#page_leve1 .block_m2 .b1 .num .val').text(compCnt);
    $('#page_leve1 .block_m2 .b2 .num .val').text(none_total);
  }



  // var comp_cause_list; // 公司原因
  // var none_cause_list; // 非公司原因
  // var all_delay_cause_data;


  // var param = {
  //     'SOLR_CODE': 'FAC_COMP_DELAY_CAUSE_RATE_KPI',
  //     'COMP_CODE': codelist.join(','),
  //     'KPI_CODE': 'DELAY_NO',
  //     'VALUE_TYPE': 'kpi_value_d',
  //     'DATE_TYPE': 'D',
  //     'LIMIT': 1,
  //     'OPTIMIZE': 1
  // }
  // var queryCompany = (current_company_code == parent_company)? '':current_company_code ; 

  // $.ajax({
  //     type: 'get',
  //     url: "/bi/spring/aoc/bj//getDelayReason?compCode="+queryCompany,
  //     contentType: 'application/json',
  //     dataType: 'json',
  //     async: true,
  //     success: function (response) {

  //         checkLogin(response);

  //         if (response.data != undefined) {

  //             if (all_delay_cause_data == undefined) {
  //                 all_delay_cause_data = {};
  //             }
  //             all_delay_cause_data['kpi_value_d'] = response.data;

  //             setDelayCause();

  //         }

  //     },
  //     error: function () { }
  // });



  // function setDelayCause() {
  //     if (all_delay_cause_data['kpi_value_d'] == undefined) {
  //         return;
  //     }
  //     let data = all_delay_cause_data['kpi_value_d'];

  //     comp_cause_list = {}; // 公司原因
  //     none_cause_list = {}; // 非公司原因


  //     var total = data.total;

  //     var comp_total = data.compCauseNum;
  //     var none_total = data.noneCauseNum;


  //     // 公司原因
  //     var html = '';
  //     var cause_arr_list = [];

  //     for(var i=0;i<data.compCauseItemList.length;i++){
  //         let item = data.compCauseItemList[i]
  //         cause_arr_list.push({
  //             "name": item.name,
  //             "val": item.num
  //         });
  //     }

  //     cause_arr_list.sort(function (a, b) {
  //         return b.val - a.val;
  //     });


  //     var len = cause_arr_list.length;
  //     for (var i = 0; i < len; i++) {
  //         var d = cause_arr_list[i];
  //         var per = total > 0 ? Number(d.val) / total : 0;
  //         var perstr = Math.round(per * 1000) / 10;
  //         if (perstr > 0) {
  //             var barlen = 170 * per;
  //             html += '<tr>';
  //             html += '<td class="tdc1">' + d.name + '</td>';
  //             html += '<td class="tdc2">' + perstr + '%</td>';
  //             html += '<td class="tdc3"><span class="bar" style="width:' + barlen + 'px;"></span></td>';
  //             html += '</tr>';
  //         }
  //     }

  //     $('#page_leve1 .block_m2 .cont2 table').html(html); //公司

  //     // 非公司原因
  //     var html = '';
  //     var cause_arr_list = [];
  //     for(var i=0;i<data.noneCauseItemList.length;i++){
  //         let item = data.noneCauseItemList[i]
  //         cause_arr_list.push({
  //             "name": item.name,
  //             "val": item.num
  //         });
  //     }
  //     cause_arr_list.sort(function (a, b) {
  //         return b.val - a.val;
  //     });

  //     var len = cause_arr_list.length;
  //     for (var i = 0; i < len; i++) {
  //         var d = cause_arr_list[i];
  //         var per = (comp_total + none_total) > 0 ? Number(d.val) / (comp_total + none_total) : 0;
  //         var perstr = Math.round(per * 1000) / 10;
  //         if (perstr > 0) {
  //             var barlen = 170 * per;
  //             html += '<tr>';
  //             html += '<td class="tdc1">' + d.name + '</td>';
  //             html += '<td class="tdc2">' + perstr + '%</td>';
  //             html += '<td class="tdc3"><span class="bar" style="width:' + barlen + 'px;"></span></td>';
  //             html += '</tr>';
  //         }
  //     }
  //     $('#page_leve1 .block_m2 .cont3 table').html(html); //非公司



  //     // 公司原因占比
  //     var rate_comp = total > 0 ? comp_total / total : 0;

  //     // 非公司占比
  //     var rate_none = total > 0 ? none_total /total : 0;


  //     setDelayCauseChart(rate_comp);

  //     // percent
  //     $('#page_leve1 .block_m2 .b1 .kpi .val').text(Math.round(rate_comp * 100));
  //     $('#page_leve1 .block_m2 .b2 .kpi .val').text(Math.round(rate_none * 100));

  //     $('#page_leve1 .block_m2 .b1 .num .val').text(comp_total);
  //     $('#page_leve1 .block_m2 .b2 .num .val').text(none_total);


  // }



  // ------------------------------------------------------------------------
  // 获取航班实时位置
  // ------------------------------------------------------------------------

  // 获取集团所有机号列表
  /*
  var date = new Date();
  var month = date.getMonth()+1;
  var day = date.getDate();
  if(month < 10){
      month = '0' + month;
  }
  if(day < 10){
      day = '0' + day;
  }
  var today = date.getFullYear()+'-'+month+'-'+day;
  var param = {
      "acOwner": "",
      "vip": "false",
      "datop": today
  }
  $.ajax({
      type: 'post',
      url:"/bi/web/7x2_vip_flt_list",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function(response) {
          
          getPlaneLocation(response.acLongNo);
          
      },
      error:function() {
      }
  });
  */




  // 获取飞机实时位置
  function getPlaneLocationMq () {

    var param = {
      'mode': 'pos'
    }
    $.ajax({
      type: 'post',
      url: "/bi/web/flightMq",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function (response) {

        //checkLogin(response);

        planeLocationList = [];
        var plist = {};

        //var list = response.data.data1;
        function processData (data1) {
          var lst = {};
          var len = data1.length;
          for (var i = 0; i < len; i++) {
            var dd = data1[i];
            var fi = dd.fi;
            if (lst[fi] == undefined) {
              lst[fi] = {
                data: []
              };
              lst[fi]['data'].push(dd);
            } else {
              lst[fi]['data'].push(dd);
            }
          }

          return lst;
        }

        var list = processData(response.data.data1);

        console.log('processData', list);

        for (var fltno in list) {

          var fltobj = list[fltno];
          var itmx2 = fltobj.data;

          var itm;

          if (itmx2 && itmx2.length > 1) {
            var itm1 = itmx2[0];
            var itm2 = itmx2[1];


            itm1.UTC = itm1.UTC.replace(' ', '');
            itm2.UTC = itm2.UTC.replace(' ', '');

            if (itm1.UTC > itm2.UTC) {
              itm = itm1
              itm.LON1 = itm2.LON;
              itm.LAT1 = itm2.LAT;
            } else if (itm1.UTC < itm2.UTC) {
              itm = itm2
              itm.LON1 = itm1.LON;
              itm.LAT1 = itm1.LAT;
            } else {

              itm = itm2
              itm.LON1 = itm1.LON;
              itm.LAT1 = itm1.LAT;

              //console.log(fltno, '两组经纬度UTC相同');
            }
          } else if (itmx2 && itmx2.length > 0) {
            itm = itmx2[0];

          }


          if (itm) {

            var alt = itm.ALT;
            var cas = itm.CAS;
            let vec;

            var fltno = itm.fi;

            if (current_company_code == parent_company || fltno.indexOf(current_company_code) == 0) {

              var acno = itm.an;
              acno = acno.replace('-', '');

              var lon = formatLonLat(itm.LON);
              var lon1 = formatLonLat(itm.LON1);
              var lat = formatLonLat(itm.LAT);
              var lat1 = formatLonLat(itm.LAT1);

              if (isNaN(itm.LON)) {
                vec = Number(itm.VEC);
              }

              var oil = isNaN(itm.OIL) ? '' : itm.OIL;

              var pdat = {
                fltno: fltno,
                acno: acno,
                alt: alt,
                vec: vec,
                lon: lon,
                lat: lat,
                lon1: lon1,
                lat1: lat1,
                oil: oil,
              };

              var code = acno + '-' + fltno;

              /*
              if(plist[code] == undefined){
                  plist[code] = pdat;
              }else if(plist[code].lon1 == undefined){
                  plist[code].lon1 = pdat.lon;
                  plist[code].lat1 = pdat.lat;
                  if(oil > 0){
                      plist[code].oil = oil;
                  }
              }else if(oil > 0){
                  plist[code].oil = oil;
              }
              */

              if (pdat.vec == undefined) {
                pdat.vec = getGeoAngle(pdat.lat, pdat.lon, pdat.lat1, pdat.lon1);
              }
              planeLocationList.push(pdat);
            }
          }
        }

        /*
        for(var code in plist){
            var pdat = plist[code];
            //if(pdat.vec || pdat.lon1 != undefined){
                if(pdat.vec == undefined){
                    pdat.vec = getGeoAngle(pdat.lat, pdat.lon, pdat.lat1, pdat.lon1);
                }
                planeLocationList.push(pdat);
            //}
        }
        */
        console.log('planeLocationList', planeLocationList);
        setPlaneLocation();

        /*
        if($('#earth3d-wrapper').is(':visible')){
            if(!chart_earch){
                crate3DEarth();
            }
            set3Dlines(); 
            
        }
        */

      },
      error: function (jqXHR, txtStatus, errorThrown) {

      }
    });
  }

  getPlaneLocationMq();



  // 获取飞机实时位置 // Horcs系统不堪重负，这个接口不让用了。。。
  /*
  function getPlaneLocation(aclist){
      var acnos = [];
      for(var k in aclist){
          var acno = aclist[k];
          if(acnos.indexOf(acno)==-1 && acno.length>0){
              acnos.push(acno);
          }
      }

      var param = {
          "aircraftNos": acnos.join(','),
      }
      $.ajax({           
          type: 'post',
          url:"/bi/web/getPositionsInfo",
          contentType: 'application/json',
          dataType: 'json',
          async: true,
          data: JSON.stringify(param),
          success: function(response) {

              checkLogin(response);

              var list = JSON.parse(response.data);


//  "gatewayTime":"网关时间UTC",
//  "flightNo":"航班号",
//  "aircraftNo":"飞机号",
//  "lat":纬度,
//  "lon":经度,
//  "alt":气压高度（单位米）,
//  "cas":地图（海里/h）,
//  "vec":航向角,
//  "oil":油量(单位kg),
//  "distance":距目的机场距离（米）,
//  "virtual":"是否为推演点",
//  "type":"位置报类型",  目前有adsb,acars,acars787,DR，这4种类型
//  "updateTime":"更新时间UTC",
//  "positionDelay":"位置延迟报警判断时间（单位为秒）"


              planeLocationList = [];

              for(var i=list.length-1; i>=0; i--){
                  var itm = list[i];

                  if(itm != null){
                      
                      var alt = itm.alt;
                      var cas = itm.cas;
                      var oil = itm.oil;
                      var distance = itm.distance;

                      if(cas > 0){

                          var fltno = itm.flightNo;
                          var acno = itm.aircraftNo;
                          var vec = itm.vec;
                          var lon = itm.lon;
                          var lat = itm.lat;

                          planeLocationList.push({
                                  fltno: fltno,
                                  acno: acno,
                                  alt: alt,
                                  vec: vec,
                                  lon: lon,
                                  lat: lat,
                                  oil: oil,
                              });
                      }
                  }
                  
              }

              setPlaneLocation();
              
          },
          error:function() {
          }
      });
  }
  */



  // ------------------------------------------------------------------------
  // 获取告警机场
  // ------------------------------------------------------------------------
  /*
  查询最新一天，DATE_TYPE=D
  先从 FAC_COMP_ARP_FLIGHT_KPI 中得出正常率<0.5 的机场
  正常率计算方法 KPI_ID=10045，KPI_ID=10049 这两个指标的 KPI_VALUE 相除
  取出商 < 0.5 的 ARP_ID，
  然后用这些 ARP_ID 查询 FAC_COMP_ARP_DELAY_CAUSE_KPI ，从中找到这些机场的影响总航班量 KPI_ID=10047，
  返回KPI_VALUE>2机场，把KPI_VALUE最大的那个DELAY_CAUSE 传给前台
  */
  var param = {
    "OPTIMIZE": 1
  };
  $.ajax({
    type: 'post',
    url: "/bi/web/alertAirport",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var list = response.data;
      list.sort(function (a, b) {
        return b.KPI_VALUE - a.KPI_VALUE
      });
      setAlertAirport(list);

    },
    error: function () { }
  });



  // -----------------------------
  // 语音播报
  // -----------------------------
  var audio_txt_data = {};

  function onAudioTplLoad (tplobj) {

    // {COMP} 今日计划航班{SHIFT1}班次，已执行{SHIFT2}班次。在飞航班{AIR}架。正常率{NORMAL_RATE}。今日计划运输旅客{TRV1}人，已完成{TRV2}人。
    var tpl = tplobj.txt;

    //--
    var compname = companyCode2Name[current_company_code];
    tpl = tpl.replace(/{COMP}/g, compname);

    //--
    tpl = tpl.replace(/{SHIFT1}/g, formatCurrency(audio_txt_data.SHIFT1, 2));
    tpl = tpl.replace(/{SHIFT2}/g, formatCurrency(audio_txt_data.SHIFT2, 2));

    tpl = tpl.replace(/{TRV1}/g, formatCurrency(audio_txt_data.TRV1, 2));
    tpl = tpl.replace(/{TRV2}/g, formatCurrency(audio_txt_data.TRV2, 2));

    tpl = tpl.replace(/{AIR}/g, audio_txt_data.AIR);
    tpl = tpl.replace(/{NORMAL_RATE}/g, audio_txt_data.NORMAL_RATE + '%');



    text2audio(tpl, true);

  }

  function loadAudio () {
    if (audio_txt_data.SHIFT1 && audio_txt_data.TRV1 && audio_txt_data.AIR >= 0) {
      stopAudio();
      getAudioTemplate('A3.3-general', onAudioTplLoad);
    }
  }

}



function setAlertAirport (list) {
  if (airportList == undefined) {
    setTimeout(setAlertAirport, 0, list);
    return;
  }
  var html = '';
  var len = list.length;
  for (var i = 0; i < len; i++) {
    var itm = list[i];
    if (itm.DELAY_CAUSE != '') {
      var arpname;
      for (var code in airportList) {
        var arp = airportList[code];
        if (arp.id == itm.ARP_ID) {
          arpname = arp.chn_name;
          if (arpname.indexOf('机场') == -1) {
            arpname = arpname + '机场';
          }
          break;
        }
      }
      html += '<tr>'
      html += '<td class="col1 name">' + arpname + '</td>'
      html += '<td class="col2 center bold">' + Number(itm.KPI_VALUE) + '</td>'
      html += '<td class="col3 blue_ll center">' + itm.DELAY_CAUSE + '</td>'
      html += '</tr>'

    }
  }
  $('#alert_airports1 table tbody').html(html);

  if (list.length > 4) {
    // 列表滚动
    var speed = 80;
    var base_sec = document.getElementById("alert_airports");
    var base_sec2 = document.getElementById("alert_airports2");
    var base_sec1 = document.getElementById("alert_airports1");
    base_sec2.innerHTML = base_sec1.innerHTML;

    function base_Marquee () {
      if (base_sec2.offsetTop - base_sec.scrollTop <= 0)
        base_sec.scrollTop -= base_sec1.offsetHeight;
      else {
        base_sec.scrollTop += Math.ceil(1 / pageZoomScale);
      }
    }
    clearInterval(marquee_itv_alertAirport);
    marquee_itv_alertAirport = setInterval(base_Marquee, speed);
    base_sec.onmouseover = function () {
      clearInterval(marquee_itv_alertAirport)
    }
    base_sec.onmouseout = function () {
      marquee_itv_alertAirport = setInterval(base_Marquee, speed)
    }
  }


}


// ------------------------------------------------------------------------
// 获取所有机场列表
// ------------------------------------------------------------------------
function getAirportList () {

  var param = {
    //"AIRPORT_CODE": '', // 可选，传入机场CODE
  }
  $.ajax({
    type: 'post',
    url: "/bi/web/airportdetail",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      airportList = {};
      var list = response.airport;
      for (var i = list.length - 1; i >= 0; i--) {
        var arp = list[i];
        airportList[arp.code] = arp;
      }
    },
    error: function () { }
  });
}



//返回到实时运行前一个页面
$('.back2level1').on('click', function (evt) {
  $('#page_leve1').show();
  $('#page_leve2').hide();

  $('#switch2earth').show();

  setPlaneLocation();
})



// ------------------------------------------------------------------------
// 设置地图上的飞机位置
// ------------------------------------------------------------------------
function setPlaneLocation () {
  if (flightInfoList == undefined || planeLocationList == undefined || $('#page_leve2').is(':visible')) {
    return;
  }

  if ($('#mainframe')[0].contentWindow.setPlaneLocation != undefined) {
    $('#mainframe')[0].contentWindow.setPlaneLocation();
  } else {
    setTimeout(setPlaneLocation, 10);
  }
}



// 显示航班信息页面
function showFlightDetails (data) {
  if (airportList == undefined) {
    setTimeout(showFlightDetails, 10, fltno, acno, oil);
    return;
  }

  var fltno = data.name; //航班号
  var acno = data.acno; //机号
  var oil = data.oil; //油量
  var lon = data.value[0]; //经度
  var lat = data.value[1]; //纬度

  var flt = flightInfoList[fltno];

  var depAirport = airportList[flt.depStn]; //出发机场信息
  var arrAirport = airportList[flt.arrStn]; //到达机场信息

  $('#page_leve1').hide();
  $('#page_leve2').show();
  $('#switch2earth').hide();

  var statusMap = {
    'ARR': '落地',
    'NDR': '落地',
    'ATD': '推出',
    'ATA': '到达',
    'CNL': '取消',
    'DEL': '延误',
    'DEP': '起飞',
    'RTR': '返航',
    'SCH': '计划'
  };


  $('#page_leve2 .block_l1 .fltno').text(fltno);
  $('#page_leve2 .block_l1 .fltno').css('background-image', 'url(img/logo_' + flt.acOwn + '.png)');
  $('#page_leve2 .block_l1 .info').removeClass('green');
  $('#page_leve2 .block_l1 .info').removeClass('yellow');
  $('#page_leve2 .block_l1 .sts').removeClass('greenbg');
  $('#page_leve2 .block_l1 .sts').removeClass('yellowbg');

  $('#page_leve2 .block_l1 .sts').text(statusMap[flt.status]);

  if (flt.delay1 != '' && flt.dur1 > 0) {
    $('#page_leve2 .block_l1 .sts').addClass('yellowbg');
    $('#page_leve2 .block_l1 .info').addClass('yellow');
  } else {
    $('#page_leve2 .block_l1 .sts').addClass('greenbg');
    $('#page_leve2 .block_l1 .info').addClass('green');
  }

  if (flt.delay1 != '' && flt.dur1 > 0) {
    //var time1 = parserDate(flt.staChn);// 计划到达时间
    //var time2 = parserDate(flt.etaChn);// 预计到达时间
    //var mins = (time2.getTime() - time1.getTime())/(1000*60);
    $('#page_leve2 .block_l1 .info').text('已晚点' + flt.dur1 + '分钟');
  } else {
    $('#page_leve2 .block_l1 .info').text('');
  }

  // 出发 城市
  $('#page_leve2 .block_l1 .row1 .col1 .city').text(flt.depCity);
  // 出发 机场
  $('#page_leve2 .block_l1 .row1 .col1 .arp').text(flt.depStnCn);

  // 到达 城市
  $('#page_leve2 .block_l1 .row1 .col3 .city').text(flt.arrCity);
  // 到达 机场
  $('#page_leve2 .block_l1 .row1 .col3 .arp').text(flt.arrStnCn);

  function trimTime (timestr) {
    var arr = timestr.split(' ');
    var arr2 = arr[1].split(':');
    return arr2[0] + ':' + arr2[1];
  }

  // 实际起飞时间 atdChn
  // 实际到达时间 ataChn

  if (flt.status == 'DEP' || flt.status == 'ARR' || flt.status == 'NDR' || flt.status == 'ATA') {
    // 起飞,落地,到达
    // 实际起飞时间 atdChn
    $('#page_leve2 .block_l1 .row2 .col1 .time').text(trimTime(flt.atdChn));
    $('#page_leve2 .block_l1 .row2 .col1 .lb').text('实际起飞');
  } else {
    // 预计出发
    $('#page_leve2 .block_l1 .row2 .col1 .time').text(trimTime(flt.etdChn));
    $('#page_leve2 .block_l1 .row2 .col1 .lb').text('预计出发');
  }

  if (flt.status == 'ATA') {
    // 到达
    // 实际起飞时间 atdChn
    $('#page_leve2 .block_l1 .row2 .col3 .time').text(trimTime(flt.ataChn));
    $('#page_leve2 .block_l1 .row2 .col3 .lb').text('实际到达');
  } else {
    // 预计到达
    $('#page_leve2 .block_l1 .row2 .col3 .time').text(trimTime(flt.etaChn));
    $('#page_leve2 .block_l1 .row2 .col3 .lb').text('预计到达');
  }

  // 计划出发
  $('#page_leve2 .block_l1 .row3 .col1 .time').text(trimTime(flt.stdChn));
  // 计划到达
  $('#page_leve2 .block_l1 .row3 .col3 .time').text(trimTime(flt.staChn));


  // ------------------------------------------------------------------------
  // 油量
  // ------------------------------------------------------------------------
  if (!isNaN(oil)) {
    $('#page_leve2 .oil .val').text(Math.round(oil) + 'cc');
  } else {
    $('#page_leve2 .oil .val').text('-');
  }


  // ------------------------------------------------------------------------
  // 飞机位置
  // ------------------------------------------------------------------------
  $('#page_leve2 .block_l2 .tabc1 .lon').text(trimDecimal(lon, 5));
  $('#page_leve2 .block_l2 .tabc1 .lat').text(trimDecimal(lat, 5));



  // ------------------------------------------------------------------------
  // 获取 座舱布局
  // ------------------------------------------------------------------------
  var cabin;
  var param = {
    "acNo": acno,
  }
  $.ajax({
    type: 'post',
    url: "/bi/web/getAcAircraftList",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var ac = response.data[0].data;
      cabin = ac.cabin; // 座舱布局
      var acType = ac.acType; // 机型
      var mtwKg = ac.mtwKg; //最大起飞重量KG
      var dewKg = ac.dewKg; //空机重量KG

      $('#page_leve2 .block_l2 .tabc2 .type').text(acType);
      $('#page_leve2 .block_l2 .tabc2 .layout').text(cabin);
      $('#page_leve2 .block_l2 .tabc2 .maxw').text(mtwKg);
      $('#page_leve2 .block_l2 .tabc2 .no').text(acno);
      $('#page_leve2 .block_l2 .tabc2 .minw').text(dewKg);

      // 座舱布局图
      var layout = 'img/a3.3.plane_layout_N_CY.png';
      cabin = cabin.toUpperCase();

      if (acType.indexOf('787') > -1 || acType.indexOf('767') > -1 || acType.indexOf('777') > -1 || acType.indexOf('330') > -1) {
        if (cabin.indexOf('C') > -1 && cabin.indexOf('Y') > -1) {
          layout = 'img/a3.3.plane_layout_W_CY.png';
        } else {
          layout = 'img/a3.3.plane_layout_W_Y.png';
        }
      } else {
        if (cabin.indexOf('C') > -1 && cabin.indexOf('Y') > -1) {
          layout = 'img/a3.3.plane_layout_N_CY.png';
        } else {
          layout = 'img/a3.3.plane_layout_N_Y.png';
        }
      }

      $('.block_b2 .layout img').attr('src', layout);
      $('.block_b2 .layout .name').text(cabin);

    },
    error: function () { }
  });


  // ------------------------------------------------------------------------
  // 获取 飞机信息
  // ------------------------------------------------------------------------
  var date = new Date();
  var mm = date.getMonth() + 1;
  var dd = date.getDate();
  if (mm < 10) {
    mm = '0' + mm;
  }
  if (dd < 10) {
    dd = '0' + dd;
  }
  var flightDate = date.getFullYear() + '-' + mm + '-' + dd;
  var param = {
    "flightNo": fltno,
    "flightDate": flightDate,
  }
  $.ajax({
    type: 'post',
    url: "/bi/web/getFltLoadSheetInfo",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      /*
      acNo: "B5636"
      actualPayload: "10573"
      basicWeight: "43187"
      cargoWeight: "1111"
      errorcode: ""
      errordesc: ""
      flightNo: "HU7823"
      maxPayload: "14250"
      maxTakeOffWeight: "71884"
      maxWeightStandard: "LandingWeight"
      maxZeroFuelWeight: "61688"
      otherBagWeight: "1111"
      otherCargoWeight: "0"
      passengerAdults: "129"
      passengerBaby: "3"
      passengerChild: "4"
      passengerCount: "136"
      passengerWeight: "9462"
      takeOffFuel: "14447"
      takeOffWeight: "68207"
      tripFuel: "10094"
      version: "C8Y156"
      */
      var ac = response.date;

      if (ac) {
        $('.takeOffFuel').text(ac.takeOffFuel);
        $('.tripFuel').text(ac.tripFuel);
      }


    },
    error: function () { }
  });


  // ------------------------------------------------------------------------
  // 获取 乘客信息
  // ------------------------------------------------------------------------
  var param = {
    "flightNo": fltno,
  }
  $.ajax({
    type: 'post',
    url: "/bi/web/findPsrStat",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var psr = response.data[0];
      if (!psr) {
        psr = {};
      }

      var ckiNum = psr.ckiNum; // 值机人数


      var chdNum = !isNaN(psr.chdNum) ? Number(psr.ckiChdNum) : 0;
      var infNum = !isNaN(psr.infNum) ? Number(psr.ckiInfNum) : 0;
      var vipNum = !isNaN(psr.vipNum) ? Number(psr.ckiVipNum) : 0;
      var cipNum = !isNaN(psr.cipNum) ? Number(psr.ckiCipNum) : 0;
      var adultNum = ckiNum - chdNum - infNum;

      $('#page_leve2 .block_l3 .tabc1 .val1').text(''); //客座率
      $('#page_leve2 .block_l3 .tabc1 .val2').text(adultNum > 0 ? adultNum : '--'); //成人
      $('#page_leve2 .block_l3 .tabc1 .val3').text(chdNum); //儿童
      $('#page_leve2 .block_l3 .tabc1 .val4').text(infNum); //婴儿
      $('#page_leve2 .block_l3 .tabc1 .val5').text(vipNum); //VIP
      $('#page_leve2 .block_l3 .tabc1 .val6').text(cipNum); //CIP


      function setTrvRate () {
        if (cabin == undefined) {
          setTimeout(setTrvRate, 0);
          return;
        }

        // cabin 座舱布局 C6Y170
        var arr = cabin.split('Y');
        var seat1 = !isNaN(arr[1]) ? arr[1] : 0;
        var arr2 = arr[0].split('C');
        var seat2 = !isNaN(arr2[1]) ? arr2[1] : 0;
        var seat = Number(seat1) + Number(seat2);

        // 客座率
        var psr_rate = seat > 0 ? Math.round(ckiNum / seat * 1000) / 10 : '--';
        if (psr_rate > 100) {
          psr_rate = 100;
        }
        if (!isNaN(psr_rate)) {
          psr_rate = psr_rate + '%';
        }

        $('#page_leve2 .block_l3 .tabc1 .val1').text(psr_rate); //客座率
      }

      setTrvRate();


    },
    error: function () { }
  });



  // ------------------------------------------------------------------------
  // 天气情况
  // ------------------------------------------------------------------------
  $('#city_weather').text(flt.arrCity + '天气');

  var weather_map = {
    '晴': 'icon-e600_sunny',
    '沙': 'icon-e617_dust1',
    '雹': 'icon-e620_hail',
    '雾': 'icon-e615_fog',
    '烟': 'icon-e615_fog',
    '阴': 'icon-e604_gloomy',
    '雷': 'icon-e606_rain2',
    '暴': 'icon-e606_rain2',
    '风': 'icon-e612_wind',
    '霾': 'icon-e613_haze',
    '云': 'icon-e602_cloudy',
    '雨': 'icon-e607_rain3',
    '雪': 'icon-e610_snow3',
  };


  // 到达机场
  var param = {
    'airport': flt.arrStn // 到达机场三字码
  }

  $.ajax({
    type: 'post',
    url: "/bi/web/7x2_arp_weather",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {

      if (Number(response.errorcode) == 0) {

        weather[flt.arrCity] = response;

        /*
        airport
        airportCode
        cloudInfo 云况
        metUtcTime
        rvr 跑道目视距离
        temperature
        visibility 能见度
        weatherInfo 天气现象
        weatherInfoTxt 翻译后的天气
        windFs 风速

        注：FG代表大雾
          TS或TSRA或+RA 代表雷暴
           SS或DS沙尘暴
        */
        var weatherInfoCodes = ['FG', 'TS', 'TSRA', 'RA', 'SS', 'DS'];
        var visibility = isNaN(response.visibility) || response.visibility == 0 ? 9999 : Number(response.visibility); //能见度
        var rvr = isNaN(response.rvr) || response.rvr == 0 ? 9999 : Number(response.rvr); //跑道目视距离
        var cloudInfo = isNaN(response.cloudInfo) || response.cloudInfo == 0 ? 9999 : Number(response.cloudInfo); //云况
        var windFs = isNaN(response.windFs) ? 0 : Number(response.windFs); //风速
        var weatherInfo = response.weatherInfo; //天气现象
        var weatherInfoTxt = response.weatherInfoTxt.replace(/<[^>]+>/g, "");
        var temperature = isNaN(response.temperature) ? '-' : Number(response.temperature) + '℃';

        var weather_css = 'icon-e600_sunny';
        for (var wtxt in weather_map) {
          if (weatherInfoTxt.indexOf(wtxt) > -1) {
            weather_css = weather_map[wtxt];
          }
        }

        // 设置天气状况icon
        $('#page_leve2 .block_b1 .weather span').attr('class', weather_css);
        $('#page_leve2 .block_b1 .col2 .temp').text(temperature);
        $('#page_leve2 .block_b1 .col2 .cond').text(weatherInfoTxt);
        $('#page_leve2 .block_b1 .col3 .val').text(visibility + 'm');
        $('#page_leve2 .block_b1 .col4 .val').text(windFs + 'km/h');

      } else {

      }



    },
    error: function (jqXHR, txtStatus, errorThrown) { }
  });


  // 出发机场
  var param = {
    'airport': flt.depStn // 出发机场三字码
  }
  $.ajax({
    type: 'post',
    url: "/bi/web/7x2_arp_weather",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {

      if (Number(response.errorcode) == 0) {

        weather[flt.depCity] = response;

      } else {

      }



    },
    error: function (jqXHR, txtStatus, errorThrown) { }
  });


  // ------------------------------------------------------------------------
  // 获取 机组信息
  // ------------------------------------------------------------------------
  var param = {
    "flightNo": fltno,
  }
  $.ajax({
    type: 'post',
    url: "/bi/web/findFlightReportV2",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {

      if (response.data && response.data[0]) {

        var data = response.data[0];

        // 飞行组
        var crew = data.crwPilotInf;
        if (crew) {
          crew = crew.replace(/\d+/g, ''); // 删除数字
          var arr = crew.split('；');
          var names = [];
          for (var i in arr) {
            var t = arr[i];
            var n = t.split(':')[0];
            names.push(n);
          }
          var pilot = names.splice(0, 3);
          var captain = pilot.shift();
        } else {
          var pilot = [];
          var captain = [];
        }


        // 乘务员
        var crew = data.crwStewardInf;
        if (crew) {
          crew = crew.replace(/\d+/g, ''); // 删除数字
          var arr = crew.split('；');
          var names = [];
          for (var i in arr) {
            var t = arr[i];
            var n = t.split(':')[0];
            names.push(n);
          }
          var steward = names.splice(0, 6);
          var steward_all = names;
        } else {
          var steward = [];
          var steward_all = [];
        }


        // 安全员
        var crew = data.safer1.replace(/\d+/g, ''); // 删除数字
        var safer = crew.split('/');
        safer = safer.splice(0, 2);

        //console.log(data);
        //console.log(captain);
        //console.log(pilot);
        //console.log(steward);
        //console.log(safer);

        $('#page_leve2 .block_l3 .tabc2 .val1').text(captain); //机长
        $('#page_leve2 .block_l3 .tabc2 .val2').text(pilot.join(',')); //其他驾驶
        $('#page_leve2 .block_l3 .tabc2 .val3').text(steward.join(',')); //乘务员
        $('#page_leve2 .block_l3 .tabc2 .val4').text(safer.join(',')); //安全员

        // 鼠标tooltip
        /*
        $('#page_leve2 .block_l3 .tabc2 .val3').powerTip({
            followMouse: false,
            offset: 20,
            manual: true,
        });
        $('#page_leve2 .block_l3 .tabc2 .val3').data('powertip', function() {
            return '<div class="power_tip">'+steward_all+'</div>'; //乘务员全部
        });
        $('#page_leve2 .block_l3 .tabc2 .val3').on('mouseover', function(evt){
            $.powerTip.show($(this));
        });
        $('#page_leve2 .block_l3 .tabc2 .val3').on('mouseout', function(evt){
            $.powerTip.hide();
        });
        */
      }
    },
    error: function () { }
  });


}


// 空中、地面飞机数量
/*
function setPlaneOverAirOnGround(){
    if(plane_over_air >= 0 && total_plane > 0){
        // 空中飞机架数
        $('.block_tl .row2 .col1 .val').text(plane_over_air);
        $('#plane_over_air').text(plane_over_air);
        $('#plane_on_ground').text(total_plane-plane_over_air);
    }
}
*/



// 绘制仪表盘
function drawGauge (canvasId, pointerId, rate) {

  var canvas = document.getElementById(canvasId);
  var context = canvas.getContext('2d');
  context.clearRect(0, 0, canvas.width, canvas.height);
  var x = canvas.width / 2;
  var y = canvas.height / 2;

  // draw back
  var radius = 45;
  var startAngle = Math.PI - Math.PI / 5;
  var endAngle = startAngle + Math.PI + Math.PI / 5 * 2;
  var counterClockwise = false;

  context.beginPath();
  context.arc(x, y, radius, startAngle, endAngle, counterClockwise);
  context.lineWidth = 12;
  context.strokeStyle = '#004a91';
  context.stroke();

  // draw overlay
  var radius = 45;
  var startAngle2 = startAngle;
  var endAngle2 = startAngle + (endAngle - startAngle) * rate;
  var counterClockwise = false;

  context.beginPath();
  context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
  context.lineWidth = 12;

  if (rate <= 1) {
    // linear gradient
    if (rate < 0.5) {
      var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
    } else if (rate < 0.8) {
      var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
    } else {
      var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, canvas.width / 2);
    }
    color.addColorStop(0, '#5f5fff');
    color.addColorStop(0.5, '#4185e4');
    color.addColorStop(1, '#00d3ff');

    context.strokeStyle = color;
    context.stroke();


    // pointer
    var angle = startAngle + (endAngle - startAngle) * rate;
    $('#' + pointerId).css('transform', 'rotate(' + (angle / Math.PI * 180) + 'deg)');

  } else {
    // pointer
    var angle = startAngle;
    $('#' + pointerId).css('transform', 'rotate(' + (angle / Math.PI * 180) + 'deg)');
  }


}



// 延误原因
function setDelayCauseChart (rate) {

  var canvas = document.getElementById('cvs_delay_cause');
  var context = canvas.getContext('2d');
  context.clearRect(0, 0, canvas.width, canvas.height);
  var x = canvas.width / 2;
  var y = canvas.height / 2;

  // draw blue circle
  var radius = 52;
  context.beginPath();
  context.arc(x, y, radius, 0, 2 * Math.PI, false);
  context.lineWidth = 7;
  context.strokeStyle = '#02b0f9';
  context.stroke();

  // draw green arc
  var startAngle = Math.PI - (Math.PI * 2 * rate / 2);
  var endAngle = Math.PI + (Math.PI * 2 * rate / 2);
  context.beginPath();
  context.arc(x, y, radius, startAngle, endAngle, false);
  context.lineWidth = 7;
  context.strokeStyle = '#a3d800';
  context.stroke();

  // draw lines
  var numslice = 12;
  for (var i = 0; i < numslice; i++) {
    context.beginPath();
    var startAngle = i * (Math.PI * 2 / numslice);
    var endAngle = startAngle + Math.PI * 0.01;
    context.arc(x, y, radius, startAngle, endAngle, false);
    context.lineWidth = 7;
    context.strokeStyle = '#0A176E';
    context.stroke();
  }

}



//显示延误原因
$('#btn_view_delay_cause1').on('click', function (evt) {
  $('.block_m2 .cont1').hide();
  $('.block_m2 .cont2').show();
  $('.block_m2 .cont3').hide();
})
$('#btn_view_delay_cause2').on('click', function (evt) {
  $('.block_m2 .cont1').hide();
  $('.block_m2 .cont2').hide();
  $('.block_m2 .cont3').show();
})
$('.btn_view_delay_back').on('click', function (evt) {
  $('.block_m2 .cont1').show();
  $('.block_m2 .cont2').hide();
  $('.block_m2 .cont3').hide();
})


//切换tab
$('.block_l2 .tab').on('click', function (evt) {
  var id = $(this).attr('tab-id');
  currentTabIndex = id;
  $('.block_l2 .tab').removeClass('selected');
  $('.block_l2 .tab' + id).addClass('selected');
  $('.block_l2 .tabc').hide();
  $('.block_l2 .tabc' + id).show();

  clearTimeout(itv_autoSwitchTab);
  itv_autoSwitchTab = setTimeout(autoSwitchTab, 10000);

})

//切换tab
$('.block_l3 .tab').on('click', function (evt) {
  var id = $(this).attr('tab-id');
  currentTabIndex = id;
  $('.block_l3 .tab').removeClass('selected');
  $('.block_l3 .tab' + id).addClass('selected');
  $('.block_l3 .tabc').hide();
  $('.block_l3 .tabc' + id).show();

  clearTimeout(itv_autoSwitchTab);
  itv_autoSwitchTab = setTimeout(autoSwitchTab, 10000);

})



var currentTabIndex = 1;

function autoSwitchTab () {
  clearTimeout(itv_autoSwitchTab);

  if (!autoSwitch) {
    itv_autoSwitchTab = setTimeout(autoSwitchTab, 10);
    return;
  }

  if (currentTabIndex == 1) {
    $('.block_l2 .tab2').click();
    $('.block_l3 .tab2').click();
  } else {
    $('.block_l2 .tab1').click();
    $('.block_l3 .tab1').click();
  }

  itv_autoSwitchTab = setTimeout(autoSwitchTab, 10000);

}

// 自动循环切换两个TAB
var itv_autoSwitchTab;
clearTimeout(itv_autoSwitchTab);
itv_autoSwitchTab = setTimeout(autoSwitchTab, 10000);



// ---------------------- 

function onCompanyChanged (comp_code) {
  current_company_code = comp_code;
  checkCompanyReay();
}

function checkCompanyReay () {
  if (companyCode2Name[current_company_code] == undefined) {
    setTimeout(checkCompanyReay, 0);
  } else {
    $('.back2level1').click();

    if (chart_earch) {

      clearTimeout(timeout_set3Dlines);
      chart_earch.dispose();
      $('#earth3d').html('');
      chart_earch = undefined;

    }

    flightInfoList = undefined;
    planeLocationList = undefined;

    fetchAllData();
  }

}



// 获取所有数据
function fetchAllData () {
  getGeneralKpi();
}


var interval = 300000; //5分钟刷新一次数据
setInterval(fetchAllData, interval);

getAirportList();



////////


$('#switch2earth').on('click', function (e) {
  if ($('#earth3d-wrapper').is(':visible')) {
    $('#earth3d-wrapper').hide();
    clearTimeout(timeout_set3Dlines);
    chart_earch.dispose();
    $('#earth3d').html('');
    chart_earch = undefined;

    $('.legend').show();
    $('#mainframe').show();
    $('#switch2earth').removeClass('bg2');
    $('#switch2earth').addClass('bg1');
    $('#switch2earth').text('3D地图');

    $('#switch2earth').animate({
      bottom: "255px"
    }, 500);

    $('.block_tl').animate({
      left: "31px"
    }, 500);
    $('.block_l').animate({
      bottom: "25px"
    }, 500);
    $('.block_m').animate({
      bottom: "25px"
    }, 500);
    $('.block_m2').animate({
      bottom: "25px"
    }, 500);
    $('.block_r').animate({
      bottom: "25px"
    }, 500);



  } else {
    $('#earth3d-wrapper').show();
    crate3DEarth();
    set3Dlines();

    $('.legend').hide();
    $('#mainframe').hide();
    $('#switch2earth').removeClass('bg1');
    $('#switch2earth').addClass('bg2');
    $('#switch2earth').text('平面地图');

    $('#switch2earth').animate({
      bottom: "20px"
    }, 500);

    $('.block_tl').animate({
      left: "-300px"
    }, 500);
    $('.block_l').animate({
      bottom: "-300px"
    }, 500);
    $('.block_m').animate({
      bottom: "-300px"
    }, 500);
    $('.block_m2').animate({
      bottom: "-300px"
    }, 500);
    $('.block_r').animate({
      bottom: "-300px"
    }, 500);
  }

});

// ------------------------------------------------------
// 3D 地球
// ------------------------------------------------------
var chart_earch;
var earch_option;
var targetCoord;

function crate3DEarth () {
  chart_earch = echarts.init(document.getElementById('earth3d'));

  if (targetCoord == undefined) {
    targetCoord = [106, 18];
  }

  // 制作地图贴图
  /*
  var canvas = document.createElement('canvas');
  var mapChart = echarts.init(canvas, null, {
      width: 2048,
      height: 1024
  });

  mapChart.setOption({
      backgroundColor: '#999',
      geo: {
          type: 'map',
          map: 'world',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          boundingCoords: [[-180, 90], [180, -90]],
          silent: true,
          itemStyle: {
              normal: {
                  borderColor: '#000'
              }
          },
          label: {
              normal: {
                  textStyle: {
                      color: '#fff',
                      fontSize: 40
                  }
              }
          }
      }
  });
  */

  earch_option = {

    backgroundColor: 'rgba(0,0,0,0)',
    globe: {

      baseTexture: 'asset/earth3d.jpg',
      //baseTexture: mapChart,
      heightTexture: 'asset/height.jpg',

      displacementScale: 0.02,

      environment: 'asset/space.jpg',

      shading: 'lambert',
      //shading: 'realistic',

      viewControl: {
        autoRotate: true,
        autoRotateSpeed: 5,
        zoomSensitivity: 0,
        //alpha:30,
        //beta:0,
        targetCoord: targetCoord
      },

      light: {
        ambient: {
          intensity: 0.1
        },
        main: {
          intensity: 1.5
        }
      },

      layers: [{
        type: 'blend',
        blendTo: 'emission',
        texture: 'asset/night.jpg'
      }]
    },
    series: [{
      /*
      type: 'scatter3D',
      coordinateSystem: 'globe',
      symbolSize: 6,
      //blendMode: 'lighter',
      slient: true,
      label: {
          show: false,
      },
      data: []
      */
      type: 'lines3D',
      coordinateSystem: 'globe',

      //blendMode: 'lighter',

      effect: {
        show: true,
        //period: 6,
        constantSpeed: 7,
        trailLength: 0.15,
        trailColor: 'rgba(255,255,255,0.5)',
        trailWidth: 1.5
      },

      silent: true,

      data: []
    }]
  }

  chart_earch.setOption(earch_option);


}


var prevPageSize = 0;
var timeout_set3Dlines;

function set3Dlines () {

  clearTimeout(timeout_set3Dlines);

  if (airportList == undefined || flightInfoList == undefined) {
    timeout_set3Dlines = setTimeout(set3Dlines, 10);
    return;
  }



  var seriesData = [];


  for (var fltno in flightInfoList) {

    var flt = flightInfoList[fltno];

    /*
    黄色：延误 DEL
    紫色：机务工作 ARR NDR ATA CNL
    绿色：飞行中 DEP RTR
    蓝色：未执行 SCH ATD

    'ARR':'落地',
    'NDR':'落地',
    'ATD':'推出',
    'ATA':'到达',
    'CNL':'取消',
    'DEL':'延误',
    'DEP':'起飞',
    'RTR':'返航',
    'SCH':'计划'

    */
    // 在飞的，空中的飞机
    if (flt && flt.status == 'DEP') {

      var delay_min = Number(flt.dur1) + Number(flt.dur2) + Number(flt.dur3) + Number(flt.dur4); //延误时间 分钟

      //var svg = 'M90.7,4.8c-1.3-1.5-2.8-2.7-4.5-3.5C84.3,0.4,82.4,0,80.3,0l-0.4,0.1L79.5,0c-2,0-4,0.4-5.8,1.4c-1.8,0.8-3.3,2-4.6,3.5c-2.2,2.3-3.2,4.9-3.2,8l0,47.5L0.1,111.7L0,127.4l65.3-21.2l-0.1,38L39,167.2l0,7.9l3-0.8v1.6l37.9-10.9l37.9,10.9v-1.6l2.9,0.8l-0.1-7.9l-26.1-22.9l-0.1-38l65.4,21.2l-0.1-15.8L94,60.3l-0.1-47.5C93.9,9.8,92.9,7.1,90.7,4.8z';

      var arp1 = airportList[flt.depStn];
      var arp2 = airportList[flt.arrStn];

      // 国际航班
      //if(flt.fltType == 'I'){
      //if(arp1.longitude > 150 || arp2.longitude > 150){
      //    console.log(flt.flightNo, arp1.city_name, arp2.city_name, arp1.longitude +':'+ arp1.latitude, arp2.longitude +':'+ arp2.latitude);
      //}

      if (arp1 && arp2 && arp1.longitude && arp1.latitude && arp2.longitude && arp2.latitude) {
        var color;

        if (delay_min == 0) {
          color = 'rgba(0,255,0,0.2)';
        } else {
          color = 'rgba(255,255,0,0.2)';;
        }

        earch_option.series[0].data.push({
          coords: [
            [arp1.longitude, arp1.latitude],
            [arp2.longitude, arp2.latitude]
          ],
          //value: fltno,
          lineStyle: {
            width: 1,
            color: color,
            opacity: 1
          },
        });
      }
    }

  }


  chart_earch.setOption(earch_option);


  //earch_option.globe.viewControl.targetCoord = undefined;


  //timeout_set3Dlines = setTimeout(set3Dlines, updateInterval*1000);

}

function findFltInfo (find_fltno) {
  for (var fltno in flightInfoList) {
    var flt = flightInfoList[fltno];
    if (fltno == find_fltno) {
      return flt;
    }
  }
  return undefined;
}


var searchfltlst;

function searchFlt () {

  var from_name = $.trim($('#ipt_city_from').val());
  var to_name = $.trim($('#ipt_city_to').val());

  searchfltlst = {};

  if (planeLocationList != undefined && flightInfoList != undefined) {
    var planes = planeLocationList;
    var flightList = flightInfoList;

    var len = planes.length;
    for (var i = 0; i < len; i++) {
      var dat = planes[i];
      var flt = flightList[dat.fltno];

      if (flt) {

        var depStnCn = flt.depStnCn; //出发机场
        var depCity = flt.depCity; //出发城市
        var arrStnCn = flt.arrStnCn; //到达机场
        var arrCity = flt.arrCity; //到达城市

        var ok1 = false;
        if (from_name.length > 0) {
          if (depStnCn.indexOf(from_name) > -1 || depCity.indexOf(from_name) > -1 || from_name.indexOf(depStnCn) > -1 || from_name.indexOf(depCity) > -1) {
            ok1 = true;
          }
        } else {
          ok1 = true;
        }

        var ok2 = false;
        if (to_name.length > 0) {
          if (arrStnCn.indexOf(to_name) > -1 || arrCity.indexOf(to_name) > -1 || to_name.indexOf(arrStnCn) > -1 || to_name.indexOf(arrCity) > -1) {
            ok2 = true;
          }
        } else {
          ok2 = true;
        }

        if (ok1 && ok2 && (from_name.length > 0 || to_name.length > 0)) {
          searchfltlst[flt.flightNo] = {
            name: flt.flightNo, //航班号
            acno: flt.acLongNo, //机号
            oil: dat.oil, //油量
            value: [dat.lon, dat.lat], //经度 纬度
          };
        }


      }
    }
  }

  //
  var html = '';
  var idx = 0;
  $('#ipt_fltno').val('');
  for (var flightNo in searchfltlst) {
    var data = searchfltlst[flightNo];
    html += '<span class="itm">' + flightNo + '</span>'
    if (idx == 0) {
      $('#ipt_fltno').val(flightNo);
    }
    idx++;

  }
  $('#flt_dropdown_list').html(html);

  $('#flt_dropdown_list .itm').on('click', function (evt) {
    var fltno = $(this).text();
    $('#ipt_fltno').val(fltno)
    $('#flt_dropdown_list').hide();
  });

  $('.searchform .error').hide();
}

$('#ipt_city_from').on('input', function (evt) {
  searchFlt()
});
$('#ipt_city_to').on('input', function (evt) {
  searchFlt()
});
$('#ipt_fltno').on('input', function (evt) {


});
$('.input_dropdown .dropdown_arrow').on('click', function (evt) {
  var cnt = 0;
  for (var fltno in searchfltlst) {
    cnt++;
  }
  if (!$('#flt_dropdown_list').is(':visible') && cnt > 0) {
    $('#flt_dropdown_list').show();
  } else {
    $('#flt_dropdown_list').hide();
  }
});

$('.btn_go_flt').on('click', function (evt) {

  var fltno = $('#ipt_fltno').val();

  if (searchfltlst && searchfltlst[fltno]) {
    var data = searchfltlst[fltno];
    //
    $('.searchform .error').hide();
    $('#mainframe')[0].contentWindow.setAirline(data);
    showFlightDetails(data);
    //
  } else {
    var len = planeLocationList.length;
    var dat;
    var found = false;
    for (var i = 0; i < len; i++) {
      var dd = planeLocationList[i];
      if (fltno == dd.fltno) {
        dat = dd;
        found = true;
      }
    }
    var flt = flightInfoList[fltno];
    if (flt && found) {
      var data = {
        name: flt.flightNo, //航班号
        acno: flt.acLongNo, //机号
        oil: dat.oil, //油量
        value: [dat.lon, dat.lat], //经度 纬度
      };

      //
      $('.searchform .error').hide();
      $('#mainframe')[0].contentWindow.setAirline(data);
      showFlightDetails(data);
      //
    } else {
      $('.searchform .error').show();
    }

  }

});



function formatNum (n) {
  if (n < 10) {
    return ('0' + n);
  } else {
    return n;
  }
}