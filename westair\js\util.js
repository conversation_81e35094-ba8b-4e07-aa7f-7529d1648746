// min ≤ r ≤ max
function randomNumRange(<PERSON>,<PERSON>){
      var Range = Max - Min;
      var Rand = Math.random();
      var num = Min + Math.round(Rand * Range); //四舍五入
      return num;
}

function countFromZero(element, num, numOfStep, format){
  if(numOfStep == 0){
    numOfStep = 100;
  }
  var step = Math.ceil(num/numOfStep);
  
  var n = Math.ceil(num/step);

  var start = 0;
  var i=0;

  function setNumStr(){

    if(i < n){
        var nnn = start + step * i;
        if(nnn > num){
          nnn = num;
        }
        if(format){
          numStr = formatCurrency(nnn, 0);
        }else{
          numStr = nnn;
        }
        element.text(numStr);

        setTimeout(setNumStr, 10);

        i++;
    }
    
  }

  setNumStr();

}



// 截取小数位数
function trimDecimal(num, len) {
  var nnn = 1;
  for(var i=0; i<len; i++){
    nnn = nnn * 10;
  }
  return Math.round(num*nnn)/nnn;
}


/** 
 * 将数值四舍五入(保留 decimalLen 位小数)后格式化成金额形式 
 * 
 * @param num 数值(Number或者String) 
 * @return 金额格式的字符串,如'1,234,567.45' 
 * @type String 
 */  
function formatCurrency(num, decimalLen) {
    if(isNaN(num))  
        num = "0";
    num = num.toString().replace(/\$|\,/g,'');  
    
    sign = (num == (num = Math.abs(num)));
    var cents = '0';
    if(decimalLen == 2){

        num = Math.floor(num*100+0.50000000001);  
        cents = num%100;  
        num = Math.floor(num/100).toString();
        if(cents<10)  
        cents = "0" + cents; 

    }else if(decimalLen == 1){

        num = Math.floor(num*10+0.50000000001);  
        cents = num%10;  
        num = Math.floor(num/10).toString();  

    }else{

        num = Math.round(num).toString();

    }
     
    for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++){
      num = num.substring(0,num.length-(4*i+3))+','+num.substring(num.length-(4*i+3));
    }

    if(decimalLen > 0){
      return ((sign?'':'-') + num + '.' + cents);
    }else{
      return ((sign?'':'-') + num);
    }
    

}  


function isMac(){
  return navigator.appVersion.indexOf("Mac") != -1;
}

function fontSizeDiff(){
  if(isMac()){
    return 0;
  }else{
    return -2;
  }
}


function chartDateFormatter(value, index) {
    if(value.length == 8 || value.length == 6){
      // 20170316
      // 201703
      var d = value.substr(-4, 4);
      var d1 = d.substr(0, 2);
      var d2 = d.substr(-2, 2);
      return d1 + '/' + d2;
    }
    return value;
}


// 判断 object 是否为空
function objectIsEmpty(obj){
  for(var k in obj){
    return false;
  }
  return true;
}


// 2015-03-12 12:00:00 转换成标准时间
function parserDate(date) {
  return new Date(Number(date.substr(0,4)), Number(date.substr(5,2))-1, Number(date.substr(8,2)), Number(date.substr(11,2)), Number(date.substr(14,2)), Number(date.substr(17,2))); 
};