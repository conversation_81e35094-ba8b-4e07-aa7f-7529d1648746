showLoading();
var nodeIndex = 0;
var comp_code = "PN";
var comp_id = "100500";
var planeLocationList = [];
var current_company_code = "PN";
var flightInfoList = undefined; // 机场列表
var airportList;
var BASE_CITY_LIST = {
  CKG: "重庆",
  CGO: "郑州",
};

var BASE_CODE_LIST = {
  CKG: "ZUCK",
  CGO: "ZHCC",
};

var ARP_NAME_LIST = {
  CKG: "重庆",
  CGO: "郑州",
  SZX: "深圳",
  SYX: "三亚",
  HFE: "合肥",
};

var ARP_ID_LIST = {
  CKG: "2563",
  CGO: "2503",
  SZX: "2499",
  SYX: "2510",
  HFE: "2546",
};

var statusMap = {
  ARR: "落地",
  NDR: "落地",
  ATD: "推出",
  ATA: "到达",
  CNL: "取消",
  DEL: "延误",
  DEP: "起飞",
  RTR: "返航",
  SCH: "计划",
};

var comp_cause = [
  "飞机故障",
  "运力调配",
  "工程机务",
  "航班计划",
  "航材保障",
  "航务保障",
  "机组保障",
  "乘务组",
  "空警安全员",
  "地面保障",
  "货运保障",
  "其他航空公司原因",
];
var none_cause = [
  "流量控制",
  "公共安全",
  "机场",
  "军事活动",
  "空管",
  "离港系统",
  "联检",
  "旅客",
  "民航局航班时刻安排",
  "天气原因",
  "油料",
];

var all_flight_list;
var timeout_next_flt;

// 缓存航班机组人员，保障节点
var fltCrwCache = {};
var fltLegCache = {};

// 航班保障节点
var fltnodes = [
  "cncPilotArrTime",
  "checkInEnd",
  "cncCabinSupplyEndTime",
  "cncCleanEndTime",
  "cncMCCReleaseTime",
  "planeReady",
  "cncInformBoardTime",
  "cncBoardOverTime",
  "cncClosePaxCabinTime",
  "cncCloseCargoCabinTime",
  "cncPushTime",
  "cncACARSTOFF",
];

function loadAll() {
  // ------------------------------------------------------------------------
  // 备份运力
  /*
  接口：
  com.hnair.opcnet.api.ods.foc.FocFltInfoApi
  getStandardFocFlightInfo
  方法：
  获取从今早6:00到明日凌晨2:00所有西部的航班，
  判断逻辑：
  a: 第一班，起飞时间比早6点还晚4小时的航班
  b: 最后一班，到达后离第二天02:00大于4小时
  c: 相同机号，上一班到达时间和下一班起飞时间 间隔大4小时
  统计符合上面条件的所有航班，然后分机型归类。
  */
  // ------------------------------------------------------------------------
  var backup_timespan = 4 * 3600 * 1000; // 编排空档期的时间(毫秒)

  // 开始结束时间
  var date = new Date();
  var mm = date.getMonth() + 1;
  var dd = date.getDate();
  if (mm < 10) {
    mm = "0" + mm;
  }
  if (dd < 10) {
    dd = "0" + dd;
  }
  var stdStartBeijingTime =
    date.getFullYear() + "-" + mm + "-" + dd + " 06:00:00"; // 今日早6点

  var end_ts = date.getTime() + 86400000;
  date.setTime(end_ts);
  mm = date.getMonth() + 1;
  dd = date.getDate();
  if (mm < 10) {
    mm = "0" + mm;
  }
  if (dd < 10) {
    dd = "0" + dd;
  }
  var stdEndBeijingTime =
    date.getFullYear() + "-" + mm + "-" + dd + " 02:00:00"; // 明日凌晨2点

  // 获取航班信息
  var param = {
    stdStart: stdStartBeijingTime,
    stdEnd: stdEndBeijingTime,
    acOwner: comp_code,
    statusList: "",
  };
  $.ajax({
    type: "post",
    url: "/bi/web/getStandardFocFlightInfo",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var list = response.data;

      all_flight_list = response.data;

      // 基地 平均过站时间
      setBaseAvgStayTime(list);

      var acFltList = {}; // 机号:[航班] /////机号对应多个航班
      var ac_list = [];

      for (var i = list.length - 1; i >= 0; i--) {
        var flt = list[i];
        var acLongNo = flt.acLongNo;

        if (acFltList[acLongNo] == undefined) {
          acFltList[acLongNo] = [];
        }
        acFltList[acLongNo].push(flt);

        if (ac_list.indexOf(acLongNo) == -1) {
          ac_list.push(acLongNo);
        }

        if (acLongNo == "B8867") {
          console.log(acLongNo, flt);
        }
      }

      //console.log('-- acFltList: ');
      //console.log(acFltList);

      // 查询出 哪些符合备份运力 的飞机
      var backup_planes = [];
      for (var acLongNo in acFltList) {
        var flt_list = acFltList[acLongNo];

        // 时间从排序 早 -> 晚
        flt_list.sort(function (a, b) {
          var time_a = str2Date(a.stdChn);
          var time_b = str2Date(b.stdChn);

          return time_a.getTime() - time_b.getTime();
        });

        //stdChn 计划起飞时间（北京时间）
        //staChn 计划到达时间（北京时间）

        var len = flt_list.length;

        for (var i = 0; i < len; i++) {
          if (len > 1 && i == 0) {
            // 第一班，起飞时间离早6点还晚 {4}小时
            var flt = flt_list[i];
            var depStn = flt.depStn; //起飞机场三字码
            if (BASE_CITY_LIST[depStn] != undefined) {
              var time_1 = str2Date(stdStartBeijingTime);
              var time_2 = str2Date(flt.stdChn);
              if (time_2.getTime() - time_1.getTime() >= backup_timespan) {
                flt.arp = depStn;
                backup_planes.push(flt);
                console.log(flt_list);
                console.log("第一班", flt);
              }
            }
          } else if (len > 1 && i == len - 1) {
            // 最后一班，到达后离第二天02:00早于 {4}小时
            var flt = flt_list[i];
            var arrStn = flt.arrStn; //降落机场三字码
            if (BASE_CITY_LIST[arrStn] != undefined) {
              var time_1 = str2Date(flt.staChn);
              var time_2 = str2Date(stdEndBeijingTime);
              if (time_2.getTime() - time_1.getTime() >= backup_timespan) {
                flt.arp = arrStn;
                backup_planes.push(flt);
                console.log("最后一班", flt);
              }
            }
          } else if (len > 1 && i > 0) {
            // 单机号 连续航班到达时间和下一班起飞时间 间隔大于 {4}小时
            var flt1 = flt_list[i - 1];
            var flt2 = flt_list[i];

            var arrStn = flt1.arrStn; //降落机场三字码
            var depStn = flt2.depStn; //起飞机场三字码
            if (
              BASE_CITY_LIST[depStn] != undefined ||
              BASE_CITY_LIST[arrStn] != undefined
            ) {
              var time_1 = str2Date(flt1.staChn); // 上一班 到达
              var time_2 = str2Date(flt2.stdChn); // 下一班 出发
              if (time_2.getTime() - time_1.getTime() >= backup_timespan) {
                flt1.arp = arrStn;
                backup_planes.push(flt1);
                console.log("中间班", flt1);
              }
            }
          }
        }
      }

      // 整理数据结构
      var acArpCnt = {}; //机型:{机场:数量}
      var acTypes = [];

      console.log("backup_planes", backup_planes);

      var len = backup_planes.length;
      for (var i = 0; i < len; i++) {
        var flt = backup_planes[i];
        if (acTypes.indexOf(flt.acType) == -1) {
          acTypes.push(flt.acType);
        }
      }

      // 获取机型转换表
      var param = {
        actypecode: acTypes.join(","),
      };

      $.ajax({
        type: "post",
        url: "/bi/web/actypquery",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          var backplane_acTypes2code = response;
          var actypeMapList = response;

          acTypes = [];

          for (var i = 0; i < len; i++) {
            var flt = backup_planes[i];
            var ac = actypeMapList[flt.acType];
            console.log("ac", ac, flt.acType, flt);
            if (ac == undefined) {
              ac = "320";
            }

            if (acArpCnt[ac] == undefined) {
              acArpCnt[ac] = {};
            }
            if (acArpCnt[ac][flt.arp] == undefined) {
              acArpCnt[ac][flt.arp] = 1;
            } else {
              acArpCnt[ac][flt.arp] += 1;
            }
            if (acTypes.indexOf(ac) == -1) {
              acTypes.push(ac);
            }
          }

          // 循环播放 备份及 卡片，2个一组
          backplane_curpage = 0;
          backplane_pagenum = Math.ceil(acTypes.length / backplane_pagesize);
          backplane_acArpCnt = acArpCnt;
          backplane_acTypes = acTypes;

          showBackupPlane();
        },
        error: function () {},
      });

      hideLoading();
    },
    error: function () {},
  });

  var sch_flt_total; // 计划航班数
  var comp_reason_delay_flt = -1; // 公司原因延误航班数

  // ------------------------------------------------------------------------
  // 各种人工指标
  // ------------------------------------------------------------------------

  var normal_rate_day;
  var normal_rate_year;
  var normal_rate_month;

  // 备份运力
  var AC_319_CQ; // 319 重庆
  var AC_319_ZZ; // 319 郑州
  var AC_320_CQ; // 320 重庆
  var AC_320_ZZ; // 320 郑州
  var AC_CREW; // 飞行员
  var AC_PILOTS; // 乘务

  var param = {
    mode: "query",
  };

  $.ajax({
    type: "post",
    url: "/bi/web/west_setting",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      //checkLogin(response);

      if (response.data != undefined) {
        normal_rate_day = response.data[0].NORMAL_RATE_DAY;
        normal_rate_month = response.data[0].NORMAL_RATE_MONTH;
        normal_rate_year = response.data[0].NORMAL_RATE_YEAR;
        var delay_rate = response.data[0].DELAY_RATE;

        $("#normal_rate_today_sch").text(normal_rate_day);
        $("#normal_rate_month_sch").text(normal_rate_month);
        $("#normal_rate_year_sch").text(normal_rate_year);
        $("#val_delay_rate_sch").text(delay_rate);
      }
    },
    error: function (jqXHR, txtStatus, errorThrown) {
      console.log("----error");
    },
  });

  $.ajax({
    type: "post",
    url: "/bi/web/west_capacity",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      //checkLogin(response);

      if (response.data != undefined) {
        AC_319_CQ = response.data[0].AC_319_CQ;
        AC_319_ZZ = response.data[0].AC_319_ZZ;
        AC_320_CQ = response.data[0].AC_320_CQ;
        AC_320_ZZ = response.data[0].AC_320_ZZ;
        AC_CREW = response.data[0].AC_CREW;
        AC_PILOTS = response.data[0].AC_PILOTS;

        $("#AC_319_CQ").text(AC_319_CQ);
        $("#AC_319_ZZ").text(AC_319_ZZ);
        $("#AC_320_CQ").text(AC_320_CQ);
        $("#AC_320_ZZ").text(AC_320_ZZ);
        $("#val_STEWARD_NO").text(AC_CREW);
        $("#val_AVIATOR_NO").text(AC_PILOTS);
      }
    },
    error: function (jqXHR, txtStatus, errorThrown) {
      console.log("----error");
    },
  });

  // ------------------------------------------------------------------------
  // 大面积延误预警信息
  // ------------------------------------------------------------------------

  var param = {
    mode: "query",
  };

  $.ajax({
    type: "post",
    url: "/bi/web/west_delay_warning",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      //checkLogin(response);

      if (response.title != undefined) {
        var msg = response.title[0].txt;
        //
        //if (msg.length > 30){
        //$('#delay_warning_msg').html('<marquee direction="left" scrollamount="10">'+msg+'</marquee>');
        //}else{
        $("#delay_warning_msg").text(msg);
        //}
      }
    },
    error: function (jqXHR, txtStatus, errorThrown) {
      console.log("----error");
    },
  });

  function getNumber(value) {
    if (value != undefined && value != "") {
      var val1 = Number(value);
      if (isNaN(val1)) {
        return 0;
      }
      return val1;
    }
    return 0;
  }

  // ------------------------------------------------------------------------
  // 正常率趋势 每月
  // ------------------------------------------------------------------------
  function getMonthRateTrend() {
    var param = {
      SOLR_CODE: "FAC_COMP_KPI",
      COMP_CODE: comp_code,
      KPI_CODE: "NORMAL_NO_T,EXCUTED_NO,CANCEL_NO",
      VALUE_TYPE: "kpi_value_d",
      DATE_TYPE: "M",
      OPTIMIZE: 1,
      LIMIT: 12,
    };

    $.ajax({
      type: "post",
      url: "/bi/query/getkpi?normalRateTrend",
      contentType: "application/json",
      dataType: "json",
      async: true,
      data: JSON.stringify(param),
      success: function (response) {
        var normalMonth = response.data[comp_code]["NORMAL_NO_T"]["M"];
        var executedMonth = response.data[comp_code]["EXCUTED_NO"]["M"];
        var cancelMonth = response.data[comp_code]["CANCEL_NO"]["M"];

        var normal_rate_list = [];
        // 正常率趋势 每月
        var date = new Date();
        var m = date.getMonth() + 1;
        var yy = date.getFullYear();
        for (var i = 0; i < m; i++) {
          var mm = i + 1;
          if (mm < 10) {
            mm = "0" + mm;
          }
          var month = "" + yy + mm;
          var exceuteNum = getNumber(executedMonth[month]);
          var normalNum = getNumber(normalMonth[month]);
          var cancelNum = getNumber(cancelMonth[month]);
          var rate = 0;
          if (exceuteNum > 0 && normalNum > 0) {
            rate =
              Math.round((normalNum / (exceuteNum + cancelNum)) * 1000) / 10;
          }

          normal_rate_list.push(rate);
        }
        setNormalRateTrendYear(normal_rate_list);
      },
      error: function () {},
    });
  }

  getMonthRateTrend();
  //月度正常率
  function getNormalRateMonth() {
    // 改为从接口获取
    getNormalRateMonthYear("M").then((kpiValue) => {
      $("#normal_rate_month").text(Math.round(kpiValue * 10) / 10);
      setNormalRateChartMonth(kpiValue / 100);
    });
    // var param = {
    //   "SOLR_CODE": "FAC_COMP_KPI",
    //   "COMP_CODE": comp_code,
    //   "KPI_CODE": "NORMAL_NO_T,EXCUTED_NO,CANCEL_NO",
    //   "VALUE_TYPE": "kpi_value_d",
    //   "DATE_TYPE": "M",
    //   "OPTIMIZE": 1,
    //   "LIMIT": 1
    // }

    // $.ajax({

    //   type: 'post',
    //   url: "/bi/query/getkpi?normalRate_month",
    //   contentType: 'application/json',
    //   dataType: 'json',
    //   async: true,
    //   data: JSON.stringify(param),
    //   success: function (response) {
    //     var date = new Date();
    //     var m = date.getMonth() + 1;
    //     var yy = date.getFullYear();
    //     if (m < 10) {
    //       m = '0' + m;
    //     }
    //     var month = '' + yy + m;
    //     var normalMonth = getNumber(response.data[comp_code]['NORMAL_NO_T']['M'][month])
    //     var executedMonth = getNumber(response.data[comp_code]['EXCUTED_NO']['M'][month]);
    //     var cancelMonth = getNumber(response.data[comp_code]['CANCEL_NO']['M'][month]);
    //     var total = executedMonth + cancelMonth;
    //     if (total > 0) {
    //       var normal_rate_month = Math.round((normalMonth / (total)) * 1000) / 10;
    //       setNormalRateChartMonth(normal_rate_month / 100);
    //     } else {
    //       $('#normal_rate_month').text('-');
    //       setNormalRateChartMonth(0 / 100);
    //     }

    //   },
    //   error: function () { }
    // });
  }
  getNormalRateMonth();

  //年度正常率
  function getNormalRateYear() {
    // 改为从接口获取
    getNormalRateMonthYear("Y").then((kpiValue) => {
      $("#normal_rate_year").text(Math.round(kpiValue * 10) / 10);
      setNormalRateChartYear(kpiValue / 100);
    });
    // var param = {
    //   "SOLR_CODE": "FAC_COMP_KPI",
    //   "COMP_CODE": comp_code,
    //   "KPI_CODE": "NORMAL_NO_T,EXCUTED_NO,CANCEL_NO",
    //   "VALUE_TYPE": "kpi_value_d",
    //   "DATE_TYPE": "Y",
    //   "OPTIMIZE": 1,
    //   "LIMIT": 1
    // }

    // $.ajax({

    //   type: 'post',
    //   url: "/bi/query/getkpi?normalRate_year",
    //   contentType: 'application/json',
    //   dataType: 'json',
    //   async: true,
    //   data: JSON.stringify(param),
    //   success: function (response) {
    //     var date = new Date();
    //     var yy = date.getFullYear();
    //     var noraml = Number(response.data[comp_code]['NORMAL_NO_T']['Y'][yy]);
    //     var executed = Number(response.data[comp_code]['EXCUTED_NO']['Y'][yy]);
    //     var cancel = Number(response.data[comp_code]['CANCEL_NO']['Y'][yy]);
    //     normal_rate_year = Math.round((noraml / (cancel + executed)) * 1000) / 10;
    //     console.log('!!!!!!!!!!!!!!!!!!!!normal_rate_year', normal_rate_year);
    //     $('#normal_rate_year').text(normal_rate_year);
    //     setNormalRateChartYear(normal_rate_year / 100);
    //   },
    //   error: function () { }
    // });
  }

  getNormalRateYear();

  // 正常率趋势 每日
  var param = {
    SOLR_CODE: "FAC_COMP_KPI",
    COMP_CODE: comp_code,
    KPI_CODE: "NORMAL_NO_T,EXCUTED_NO,CANCEL_NO",
    VALUE_TYPE: "kpi_value_d",
    DATE_TYPE: "D",
    OPTIMIZE: 1,
    LIMIT: 31,
  };

  $.ajax({
    type: "post",
    url: "/bi/query/getkpi?normalRateDayTrend",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      setNormalRateTrendMonth(response.data);
    },
    error: function () {},
  });

  // ------------------------------------------------------------------------
  // 各种航班统计信息。。。。
  // ------------------------------------------------------------------------
  var date = new Date();
  var mm = date.getMonth() + 1;
  var dd = date.getDate();
  if (mm < 10) {
    mm = "0" + mm;
  }
  if (dd < 10) {
    dd = "0" + dd;
  }
  var stdEndUtcTime = date.getFullYear() + "-" + mm + "-" + dd + " 15:59:59";
  var yesterday_ts = date.getTime() - 86400000;
  date.setTime(yesterday_ts);
  mm = date.getMonth() + 1;
  dd = date.getDate();
  if (mm < 10) {
    mm = "0" + mm;
  }
  if (dd < 10) {
    dd = "0" + dd;
  }
  var stdStartUtcTime = date.getFullYear() + "-" + mm + "-" + dd + " 16:00:00";

  //var utc_timestamp = Math.round(time.getTime()/1000) - 8*3600;
  /*
  focStaticApi.flightAmountStatic

  stdStartUtcTime=2017-02-23 16:00:00
  stdEndUtcTime=2017-02-24 15:59:59
  昨天16:00:00 到今天 15:59:59
  */
  var param = {
    stdStartUtcTime: stdStartUtcTime,
    stdEndUtcTime: stdEndUtcTime,
    companyCodes: comp_code,
    AcTypeList: "",
  };

  $.ajax({
    type: "post",
    url: "/bi/redis/7x2_flt_sts",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var sch_total = Number(response.pftc); //计划航班总数
      var sch_normal = Number(response.pfrtc); //计划航班中正常航班总数
      var pfPercent = Number(response.pfdappPercent); //计划航班中的正常航班比率

      var excuted_total = Number(response.cftc); //已执行航班总数
      var canel_total = Number(response.qftc1); //今日取消航班总数
      var executed_normal = Number(response.cfrtc); //已执行航班中正常航班总数

      sch_flt_total = sch_total;
      setCompReasonDelayRate();

      // var total = excuted_total + canel_total;
      // if (total == 0) {
      //   today_normal_rate_from_api = 1;
      // } else {
      //   today_normal_rate_from_api = Number(Math.round((executed_normal * 1000 / (excuted_total + canel_total))) / 1000)
      // }
      // 改为从接口取
      getNormalRateToday().then((kpiValue) => {
        today_normal_rate_from_api = kpiValue;
      });

      setNormalRateChartToday();

      var cftc = Number(response.cftc); //已执行航班总数
      var exe_rate = cftc / sch_total;
      $("#val_flt_total_sch").text(sch_total);
      $("#val_flt_total").text(cftc);

      $("#val_flt_exec_rate .perbar").css(
        "width",
        Math.round(exe_rate * 100) + "%"
      );
      $("#val_flt_exec_rate .plane").css(
        "left",
        "calc(" + Math.round(exe_rate * 100) + "%" + " - 40px)"
      );

      var pftci = Number(response.pftci); //国际计划航班总数
      var pftcl = Number(response.pftcl); //国内计划航班总数
      var cftci = Number(response.cftci); //国际已执行航班总数
      var cftcl = Number(response.cftcl); //国内已执行航班总数

      $("#val_flt_total_china_sch").text(pftcl);
      $("#val_flt_total_china").text(cftcl);
      var exe_rate = Math.round((cftcl / pftcl) * 100) + "%";
      $("#val_flt_exec_rate_china .perbar").css("width", exe_rate);
      $("#val_flt_exec_rate_china .plane").css(
        "left",
        "calc(" + exe_rate + " - 40px)"
      );

      $("#val_flt_total_int_sch").text(pftci);
      $("#val_flt_total_int").text(cftci);
      var exe_rate = Math.round((cftci / pftci) * 100) + "%";
      $("#val_flt_exec_rate_int .perbar").css("width", exe_rate);
      $("#val_flt_exec_rate_int .plane").css(
        "left",
        "calc(" + exe_rate + " - 40px)"
      );

      var pfdtc = Number(response.pfdtc); //计划航班中延误航班总数
    },
    error: function () {},
  });

  // ------------------------------------------------------------------------
  // 每个基地的 航空原因延误率／正常率
  // ------------------------------------------------------------------------
  //"CKG":'重庆',
  //"CGO":'郑州',
  //"SZX":'深圳',
  //"SYX":'三亚',
  //"HFE":'合肥',

  var base_sch_total = {};
  var base_comp_delay_total = {};
  var base_none_delay_total = {};
  var base_normal_total = {};

  var base_loaded = 0;
  var arplist = [];

  var param = {
    stdStartUtcTime: stdStartUtcTime,
    stdEndUtcTime: stdEndUtcTime,
    companyCodes: comp_code,
    AcTypeList: "",
    depstns: "CKG", //重庆
    arrstns: "",
  };

  $.ajax({
    type: "post",
    url: "/bi/redis/7x2_flt_sts",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var sch_total = Number(response.pftc); //计划航班总数
      var sch_normal = Number(response.pfrtc); //计划航班中正常航班总数
      if (base_normal_total["CKG"] == undefined) {
        base_normal_total["CKG"] = 0;
      }
      base_normal_total["CKG"] += sch_normal;
      //
      if (base_sch_total["CKG"] == undefined) {
        base_sch_total["CKG"] = 0;
      }
      base_sch_total["CKG"] += sch_total;

      base_loaded++;
      checkBaseDataLoaded();
    },
    error: function () {},
  });

  var param = {
    stdStartUtcTime: stdStartUtcTime,
    stdEndUtcTime: stdEndUtcTime,
    companyCodes: comp_code,
    AcTypeList: "",
    depstns: "", //重庆
    arrstns: "CKG",
  };

  $.ajax({
    type: "post",
    url: "/bi/redis/7x2_flt_sts",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var sch_total = Number(response.pftc); //计划航班总数
      var sch_normal = Number(response.pfrtc); //计划航班中正常航班总数
      if (base_normal_total["CKG"] == undefined) {
        base_normal_total["CKG"] = 0;
      }
      base_normal_total["CKG"] += sch_normal;
      //
      if (base_sch_total["CKG"] == undefined) {
        base_sch_total["CKG"] = 0;
      }
      base_sch_total["CKG"] += sch_total;

      base_loaded++;
      checkBaseDataLoaded();
    },
    error: function () {},
  });

  //
  var param = {
    stdStartUtcTime: stdStartUtcTime,
    stdEndUtcTime: stdEndUtcTime,
    companyCodes: comp_code,
    AcTypeList: "",
    depstns: "CGO", //郑州
    arrstns: "",
  };

  $.ajax({
    type: "post",
    url: "/bi/redis/7x2_flt_sts",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var sch_total = Number(response.pftc); //计划航班总数
      var sch_normal = Number(response.pfrtc); //计划航班中正常航班总数
      if (base_normal_total["CGO"] == undefined) {
        base_normal_total["CGO"] = 0;
      }
      base_normal_total["CGO"] += sch_normal;
      //
      if (base_sch_total["CGO"] == undefined) {
        base_sch_total["CGO"] = 0;
      }
      base_sch_total["CGO"] += sch_total;

      base_loaded++;
      checkBaseDataLoaded();
    },
    error: function () {},
  });

  var param = {
    stdStartUtcTime: stdStartUtcTime,
    stdEndUtcTime: stdEndUtcTime,
    companyCodes: comp_code,
    AcTypeList: "",
    depstns: "", //郑州
    arrstns: "CGO",
  };

  $.ajax({
    type: "post",
    url: "/bi/redis/7x2_flt_sts",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var sch_total = Number(response.pftc); //计划航班总数
      var sch_normal = Number(response.pfrtc); //计划航班中正常航班总数
      if (base_normal_total["CGO"] == undefined) {
        base_normal_total["CGO"] = 0;
      }
      base_normal_total["CGO"] += sch_normal;
      //
      if (base_sch_total["CGO"] == undefined) {
        base_sch_total["CGO"] = 0;
      }
      base_sch_total["CGO"] += sch_total;

      base_loaded++;
      checkBaseDataLoaded();
    },
    error: function () {},
  });

  //
  var param = {
    stdStartUtcTime: stdStartUtcTime,
    stdEndUtcTime: stdEndUtcTime,
    companyCodes: comp_code,
    AcTypeList: "",
    depstns: "SZX", //深圳
    arrstns: "",
  };

  $.ajax({
    type: "post",
    url: "/bi/redis/7x2_flt_sts",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var sch_total = Number(response.pftc); //计划航班总数
      var sch_normal = Number(response.pfrtc); //计划航班中正常航班总数
      if (base_normal_total["SZX"] == undefined) {
        base_normal_total["SZX"] = 0;
      }
      base_normal_total["SZX"] += sch_normal;
      //
      if (base_sch_total["SZX"] == undefined) {
        base_sch_total["SZX"] = 0;
      }
      base_sch_total["SZX"] += sch_total;

      base_loaded++;
      checkBaseDataLoaded();
    },
    error: function () {},
  });

  var param = {
    stdStartUtcTime: stdStartUtcTime,
    stdEndUtcTime: stdEndUtcTime,
    companyCodes: comp_code,
    AcTypeList: "",
    depstns: "", //深圳
    arrstns: "SZX",
  };

  $.ajax({
    type: "post",
    url: "/bi/redis/7x2_flt_sts",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var sch_total = Number(response.pftc); //计划航班总数
      var sch_normal = Number(response.pfrtc); //计划航班中正常航班总数
      if (base_normal_total["SZX"] == undefined) {
        base_normal_total["SZX"] = 0;
      }
      base_normal_total["SZX"] += sch_normal;
      //
      if (base_sch_total["SZX"] == undefined) {
        base_sch_total["SZX"] = 0;
      }
      base_sch_total["SZX"] += sch_total;

      base_loaded++;
      checkBaseDataLoaded();
    },
    error: function () {},
  });

  //
  var param = {
    stdStartUtcTime: stdStartUtcTime,
    stdEndUtcTime: stdEndUtcTime,
    companyCodes: comp_code,
    AcTypeList: "",
    depstns: "SYX", //三亚
    arrstns: "",
  };

  $.ajax({
    type: "post",
    url: "/bi/redis/7x2_flt_sts",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var sch_total = Number(response.pftc); //计划航班总数
      var sch_normal = Number(response.pfrtc); //计划航班中正常航班总数
      if (base_normal_total["SYX"] == undefined) {
        base_normal_total["SYX"] = 0;
      }
      base_normal_total["SYX"] += sch_normal;
      //
      if (base_sch_total["SYX"] == undefined) {
        base_sch_total["SYX"] = 0;
      }
      base_sch_total["SYX"] += sch_total;

      base_loaded++;
      checkBaseDataLoaded();
    },
    error: function () {},
  });

  var param = {
    stdStartUtcTime: stdStartUtcTime,
    stdEndUtcTime: stdEndUtcTime,
    companyCodes: comp_code,
    AcTypeList: "",
    depstns: "", //三亚
    arrstns: "SYX",
  };

  $.ajax({
    type: "post",
    url: "/bi/redis/7x2_flt_sts",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var sch_total = Number(response.pftc); //计划航班总数
      var sch_normal = Number(response.pfrtc); //计划航班中正常航班总数
      if (base_normal_total["SYX"] == undefined) {
        base_normal_total["SYX"] = 0;
      }
      base_normal_total["SYX"] += sch_normal;
      //
      if (base_sch_total["SYX"] == undefined) {
        base_sch_total["SYX"] = 0;
      }
      base_sch_total["SYX"] += sch_total;

      base_loaded++;
      checkBaseDataLoaded();
    },
    error: function () {},
  });

  //
  var param = {
    stdStartUtcTime: stdStartUtcTime,
    stdEndUtcTime: stdEndUtcTime,
    companyCodes: comp_code,
    AcTypeList: "",
    depstns: "HFE", //合肥
    arrstns: "",
  };

  $.ajax({
    type: "post",
    url: "/bi/redis/7x2_flt_sts",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var sch_total = Number(response.pftc); //计划航班总数
      var sch_normal = Number(response.pfrtc); //计划航班中正常航班总数
      if (base_normal_total["HFE"] == undefined) {
        base_normal_total["HFE"] = 0;
      }
      base_normal_total["HFE"] += sch_normal;
      //
      if (base_sch_total["HFE"] == undefined) {
        base_sch_total["HFE"] = 0;
      }
      base_sch_total["HFE"] += sch_total;

      base_loaded++;
      checkBaseDataLoaded();
    },
    error: function () {},
  });

  var param = {
    stdStartUtcTime: stdStartUtcTime,
    stdEndUtcTime: stdEndUtcTime,
    companyCodes: comp_code,
    AcTypeList: "",
    depstns: "", //合肥
    arrstns: "HFE",
  };

  $.ajax({
    type: "post",
    url: "/bi/redis/7x2_flt_sts",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var sch_total = Number(response.pftc); //计划航班总数
      var sch_normal = Number(response.pfrtc); //计划航班中正常航班总数
      if (base_normal_total["HFE"] == undefined) {
        base_normal_total["HFE"] = 0;
      }
      base_normal_total["HFE"] += sch_normal;
      //
      if (base_sch_total["HFE"] == undefined) {
        base_sch_total["HFE"] = 0;
      }
      base_sch_total["HFE"] += sch_total;

      base_loaded++;
      checkBaseDataLoaded();
    },
    error: function () {},
  });

  // 基地的延误原因

  for (var arp in ARP_NAME_LIST) {
    arplist.push(arp);
  }
  var date = new Date();
  var mm = date.getMonth() + 1;
  var dd = date.getDate();
  if (mm < 10) {
    mm = "0" + mm;
  }
  if (dd < 10) {
    dd = "0" + dd;
  }
  var today = date.getFullYear() + "" + mm + "" + dd;
  var param = {
    SOLR_CODE: "FAC_COMP_ARP_DELAY_CAUSE_KPI",
    COMP_CODE: comp_code,
    KPI_CODE: "DELAY_NO",
    AIRPORT_CODE: arplist.join(","),
    VALUE_TYPE: "kpi_value_d",
    DATE_TYPE: "D",
    DATE_ID: today,
    LIMIT: "1",
    OPTIMIZE: 1,
  };

  $.ajax({
    type: "post",
    url: "/bi/query/getkpi?FAC_COMP_ARP_DELAY_CAUSE_KPI&DISABLE_CACHE=true",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var ddd = response.data[comp_code]["DELAY_NO"]["D"];
      for (var arp in ddd) {
        base_comp_delay_total[arp] = 0;
        base_none_delay_total[arp] = 0;
        var dlst = ddd[arp];
        if (dlst != "") {
          for (var date in dlst) {
            var lst = dlst[date];
            for (var k = 0; k < lst.length; k++) {
              var causeName = lst[k]["delay_cause_s"];
              var val = Number(lst[k]["value"]);
              if (comp_cause.indexOf(causeName) > -1) {
                base_comp_delay_total[arp] += val;
              } else {
                base_none_delay_total[arp] += val;
              }
            }
          }
        }
      }
      base_loaded++;
      checkBaseDataLoaded();
    },
    error: function () {},
  });

  function checkBaseDataLoaded() {
    if (base_loaded == arplist.length * 2 + 1) {
      // ------------------
      // 基地  公司原因延误率
      // ------------------
      var chart = echarts.init(
        document.getElementById("chart_base_delay_rate")
      );

      var xAxisData = [];
      var chartData = [];
      var maxval = 0;
      console.log("base_sch_total", base_sch_total);
      for (var arp in ARP_NAME_LIST) {
        xAxisData.push(ARP_NAME_LIST[arp]);

        var total = base_sch_total[arp];
        var delay = base_comp_delay_total[arp];
        var val = total > 0 ? Math.round((delay / total) * 1000) / 10 : 0;
        var color;
        if (val > 1) {
          color = "#FF8000";
        } else {
          color = "#69DB00";
        }

        var dat = {
          value: val,
          itemStyle: {
            normal: {
              color: color,
            },
          },
        };
        chartData.push(dat);
        if (val > maxval) {
          maxval = val;
        }
      }

      var y_max = Math.ceil(maxval);
      y_max = Math.max(4, y_max);
      var y_interval = Math.ceil(y_max / 4);

      var option = {};
      option = {
        tooltip: {
          show: true,
        },
        legend: {
          show: false,
          data: [""],
        },
        grid: {
          top: 15,
          left: 46,
          right: 15,
          bottom: 30,
        },
        xAxis: [
          {
            type: "category",
            data: xAxisData,
            nameTextStyle: {
              color: "#BDCCFF",
            },
            axisLine: {
              lineStyle: {
                color: "rgba(255,255,255,0)", // 轴线颜色
              },
            },
            axisLabel: {
              textStyle: {
                color: "#BDCCFF", // 轴标签颜色大小
                fontSize: 11,
              },
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "",
            min: 0,
            max: y_max,
            interval: y_interval,
            nameTextStyle: {
              color: "#516BBA",
              fontSize: 10,
            },
            axisLabel: {
              formatter: "{value}%",
              textStyle: {
                color: "#516BBA",
                fontSize: 10,
              },
            },
            axisLine: {
              lineStyle: {
                color: "rgba(255,255,255,0)",
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: ["#243474"], // 分割线颜色
              },
            },
          },
        ],
        series: [
          {
            name: "",
            type: "bar",
            barWidth: 12,
            data: chartData,
            itemStyle: {
              normal: {
                color: "#2693FF",
              },
            },
            label: {
              normal: {
                show: true,
                position: "top",
                formatter: "{c}%",
                textStyle: {
                  fontSize: 11,
                  color: "#BDCCFF",
                },
              },
            },
          },
        ],
      };

      chart.setOption(option);

      // ------------------
      // 基地  正常率
      // ------------------
      var chart = echarts.init(
        document.getElementById("chart_base_normal_rate")
      );

      var xAxisData = [];
      var chartData = [];
      for (var arp in ARP_NAME_LIST) {
        var total = base_sch_total[arp];

        if (total > 0) {
          xAxisData.push(ARP_NAME_LIST[arp]);

          //var normal = base_normal_total[arp];
          var delay1 = base_comp_delay_total[arp] || 0;
          var delay2 = base_none_delay_total[arp] || 0;
          var normal = total - delay1 - delay2;
          normal = Math.max(normal, 0);

          var val = Math.round((normal / total) * 1000) / 10;
          var color;
          if (val < 85) {
            color = "#FF8000";
          } else {
            color = "#69DB00";
          }

          var dat = {
            value: val,
            itemStyle: {
              normal: {
                color: color,
              },
            },
          };
          chartData.push(dat);
        }
      }

      var option = {};
      option = {
        tooltip: {
          show: true,
        },
        legend: {
          show: false,
          data: [""],
        },
        grid: {
          top: 15,
          left: 46,
          right: 15,
          bottom: 30,
        },
        xAxis: [
          {
            type: "category",
            data: xAxisData,
            nameTextStyle: {
              color: "#BDCCFF",
            },
            axisLine: {
              lineStyle: {
                color: "rgba(255,255,255,0)", // 轴线颜色
              },
            },
            axisLabel: {
              textStyle: {
                color: "#BDCCFF", // 轴标签颜色大小
                fontSize: 11,
              },
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "",
            min: 0,
            max: 100,
            interval: 25,
            nameTextStyle: {
              color: "#516BBA",
              fontSize: 11,
            },
            axisLabel: {
              formatter: "{value}%",
              textStyle: {
                color: "#516BBA",
                fontSize: 11,
              },
            },
            axisLine: {
              lineStyle: {
                color: "rgba(255,255,255,0)",
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: ["#243474"], // 分割线颜色
              },
            },
          },
        ],
        series: [
          {
            name: "",
            type: "bar",
            barWidth: 12,
            data: chartData,
            itemStyle: {
              normal: {
                color: "#2693FF",
              },
            },
            label: {
              normal: {
                show: true,
                position: "top",
                formatter: "{c}%",
                textStyle: {
                  fontSize: 11,
                  color: "#BDCCFF",
                },
              },
            },
          },
        ],
      };

      chart.setOption(option);
    }
  }

  // 基地 平均过站时间
  function setBaseAvgStayTime(list) {
    var base_stay_time = {};
    var base_avg_stay_time = {};
    for (var arp in ARP_NAME_LIST) {
      base_stay_time[arp] = [];
    }
    var len = list.length;
    for (var i = 0; i < len; i++) {
      var flt = list[i];
      if (flt.acOwn == comp_code) {
        var arrStn = flt.arrStn;
        if (ARP_NAME_LIST[arrStn] != undefined) {
          var acno = flt.acLongNo;
          var flt2 = findNextFlt(acno, arrStn);
          if (flt2 != null) {
            var time1, time2;
            var etaChn = flt.etaChn; //预计到达时间（北京时间）
            var ataChn = flt.ataChn; //实际到达时间（北京时间）
            if (ataChn.length > 0) {
              time1 = ataChn;
            } else {
              time1 = etaChn;
            }

            var etdChn = flt2.etdChn; //预计起飞时间（北京时间）
            var atdChn = flt2.atdChn; //实际起飞时间（北京时间）
            if (atdChn.length > 0) {
              time2 = atdChn;
            } else {
              time2 = etdChn;
            }

            var t1 = parserDate(time1);
            var t2 = parserDate(time2);

            var mins = (t2.getTime() - t1.getTime()) / (1000 * 60);
            if (mins > 0) {
              base_stay_time[arrStn].push(mins);
            }
          }
        }
      }
    }

    // 查找后续航班
    function findNextFlt(acno, arrStn) {
      var len = list.length;
      for (var i = 0; i < len; i++) {
        var flt = list[i];
        if (flt.acLongNo == acno && flt.depStn == arrStn) {
          return flt;
        }
      }
      return null;
    }

    var maxval = 0;
    for (var arp in base_stay_time) {
      var timelist = base_stay_time[arp];
      var ln = timelist.length;
      var totaltm = 0;
      for (var j = 0; j < ln; j++) {
        var tm = timelist[j];
        totaltm += tm;
      }
      var avg = Math.round(totaltm / ln);
      base_avg_stay_time[arp] = avg;
      if (avg > maxval) {
        maxval = avg;
      }
    }

    var y_min = 20;
    var y_max = maxval;
    var y_interval = Math.ceil((y_max - y_min) / 4);

    // ------------------
    // 基地  平均过站时间
    // ------------------
    var chart = echarts.init(
      document.getElementById("chart_base_avg_pass_time")
    );

    var xAxisData = [];
    var chartData = [];
    for (var arp in ARP_NAME_LIST) {
      xAxisData.push(ARP_NAME_LIST[arp]);

      var val = base_avg_stay_time[arp];
      var color;
      //if(val < 85){
      color = "#FF8000";
      //}else{
      //color = '#69DB00';
      //}

      var dat = {
        value: val,
        itemStyle: {
          normal: {
            color: color,
          },
        },
      };
      chartData.push(dat);
    }
    //todo Slin
    xAxisData = ["重庆", "郑州", "深圳", "三亚", "合肥"];
    chartData = [
      { value: 55, itemStyle: { normal: { color: "#FF8000" } } },
      { value: 60, itemStyle: { normal: { color: "#FF8000" } } },
      { value: 65, itemStyle: { normal: { color: "#FF8000" } } },
      { value: 50, itemStyle: { normal: { color: "#FF8000" } } },
      { value: 55, itemStyle: { normal: { color: "#FF8000" } } },
    ];
    // todo end
    var option = {};
    option = {
      tooltip: {
        show: true,
      },
      legend: {
        show: false,
        data: [""],
      },
      grid: {
        top: 15,
        left: 46,
        right: 15,
        bottom: 30,
      },
      xAxis: [
        {
          type: "category",
          data: xAxisData,
          nameTextStyle: {
            color: "#BDCCFF",
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0)", // 轴线颜色
            },
          },
          axisLabel: {
            textStyle: {
              color: "#BDCCFF", // 轴标签颜色大小
              fontSize: 11,
            },
          },
        },
      ],
      yAxis: [
        {
          type: "value",
          name: "",
          min: y_min,
          max: y_max,
          interval: y_interval,
          nameTextStyle: {
            color: "#516BBA",
            fontSize: 10,
          },
          axisLabel: {
            formatter: "{value}m",
            textStyle: {
              color: "#516BBA",
              fontSize: 10,
            },
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0)",
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ["#243474"], // 分割线颜色
            },
          },
        },
      ],
      series: [
        {
          name: "",
          type: "bar",
          barWidth: 12,
          data: chartData,
          itemStyle: {
            normal: {
              color: "#2693FF",
            },
          },
          label: {
            normal: {
              show: true,
              position: "top",
              formatter: "{c}m",
              textStyle: {
                fontSize: 11,
                color: "#BDCCFF",
              },
            },
          },
        },
      ],
    };

    chart.setOption(option);
  }

  // ------------------------------------------------------------------------
  // 客座率
  // ------------------------------------------------------------------------
  /*
  TRV_RATE 客座率
  */
  var param = {
    SOLR_CODE: "FAC_COMP_KPI",
    COMP_CODE: comp_code,
    KPI_CODE: "TRV_RATE",
    VALUE_TYPE: "kpi_value_d",
    DATE_TYPE: "D",
    OPTIMIZE: 1,
    LIMIT: 1,
  };

  $.ajax({
    type: "post",
    url: "/bi/query/getkpi",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var trv_rates = response.data[comp_code]["TRV_RATE"]["D"];
      var trv_rate;
      for (var time in trv_rates) {
        trv_rate = trv_rates[time];
      }
      $("#val_trv_rate").text(Math.round(Number(trv_rate) * 100));
    },
    error: function () {},
  });

  // ------------------------------------------------------------------------
  // 计划旅客+已完成
  // ------------------------------------------------------------------------
  var param = {
    companyCodes: comp_code,
    stdStartUtcTime: stdStartUtcTime,
    stdEndUtcTime: stdEndUtcTime,
    detailType: "cftc", //已执行
    //"psrType":"all"
  };

  $.ajax({
    type: "post",
    url: "/bi/web/getPsrSummInfo",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      //var planNum = Number(response.planNum); //旅客订票人数
      var ckiNum = Number(response.ckiNum); //旅客值机人数

      //$('#val_trv_num_plan').text(Number(planNum));
      $("#val_trv_num_completed").text(Number(ckiNum));
    },
    error: function () {},
  });

  // ------------------------------------------------------------------------
  // 中转旅客量
  // ------------------------------------------------------------------------
  var param = {
    companyCodes: comp_code,
    detailType: "pftc", //统计航班（总计）
    psrType: "all",
  };

  $.ajax({
    type: "post",
    url: "/bi/web/7x2_vip_flt_cnt",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var zztrv = response.ckiGroupOutNum; //中转转出值机人数
      $("#val_zz_trv_num").text(zztrv);
    },
    error: function () {},
  });

  // ------------------------------------------------------------------------
  // 延误原因 公司／非公司
  // ------------------------------------------------------------------------

  var date_type = "D";
  var param = {
    SOLR_CODE: "FAC_COMP_DELAY_CAUSE_RATE_KPI",
    COMP_CODE: comp_code,
    KPI_CODE: "DELAY_NO",
    VALUE_TYPE: "kpi_value_d",
    DATE_TYPE: date_type,
    //'DATE_ID': today, // 传了日期会拿不到数据
    LIMIT: "1",
    OPTIMIZE: 1,
  };

  $.ajax({
    type: "post",
    url: "/bi/query/getkpi",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      checkLogin(response);

      if (response.data != undefined) {
        var data_dly = response.data[comp_code]["DELAY_NO"][date_type];

        var comp_cause_list = [];
        var none_cause_list = [];

        var comp_total = 0;
        var none_total = 0;

        var date = new Date();
        var mm = date.getMonth() + 1;
        var dd = date.getDate();
        if (mm < 10) {
          mm = "0" + mm;
        }
        if (dd < 10) {
          dd = "0" + dd;
        }
        var today = date.getFullYear() + "" + mm + "" + dd;

        var d = {};
        for (var time in data_dly) {
          if (time == today) {
            d = data_dly[time];
          }
          break;
        }

        // 公司原因 总数
        var len = comp_cause.length;
        for (var i = 0; i < len; i++) {
          var causeName = comp_cause[i];
          var val = Number(d[causeName]);
          val = isNaN(val) ? 0 : val;
          comp_total += val;
          comp_cause_list.push({
            name: causeName,
            val: val,
          });
        }

        comp_cause_list.sort(function (a, b) {
          return b.val - a.val;
        });

        // 非公司原因 总数
        var len = none_cause.length;
        for (var i = 0; i < len; i++) {
          var causeName = none_cause[i];
          var val = Number(d[causeName]);
          if (causeName == "民航局航班时刻安排") {
            causeName = "时刻安排";
          }
          val = isNaN(val) ? 0 : val;
          none_total += val;
          none_cause_list.push({
            name: causeName,
            val: val,
          });
        }

        none_cause_list.sort(function (a, b) {
          return b.val - a.val;
        });

        var maxbarsize = 50;

        var total2 = comp_total + none_total;
        total2 = Math.max(total2, 1);

        // 公司
        var html = "";
        var len = comp_cause_list.length;
        for (var i = 0; i < len; i++) {
          var d = comp_cause_list[i];
          var per = Number(d.val) / total2;
          var perstr = Math.round(per * 100);
          var barlen = maxbarsize * per;
          html +=
            '<div class="baritmrow"><span class="">' +
            d.name +
            '</span> <span class="bar yellowbar" style="width: ' +
            barlen +
            'px; "></span> <span class="ffnum">' +
            perstr +
            "%</span> </div>";
        }

        $("#holder_delay_cause_comp").html(html);

        // 非公司
        html = "";
        var len = none_cause_list.length;
        for (var i = 0; i < len; i++) {
          var d = none_cause_list[i];
          var per = Number(d.val) / total2;
          var perstr = Math.round(per * 100);
          var barlen = maxbarsize * per;
          html +=
            '<div class="baritmrow"><span class="">' +
            d.name +
            '</span> <span class="bar purplebar" style="width: ' +
            barlen +
            'px; "></span> <span class="ffnum">' +
            perstr +
            "%</span> </div>";
        }

        $("#holder_delay_cause_none").html(html);

        // percent
        $("#per_delay_cause_comp").text(
          Math.round((comp_total / total2) * 100)
        );
        $("#per_delay_cause_none").text(
          Math.round((none_total / total2) * 100)
        );

        // chart
        var rate = comp_total / total2;

        drawDelayChart(rate);

        comp_reason_delay_flt = comp_total;
        setCompReasonDelayRate();
      }
    },
    error: function () {},
  });

  function setCompReasonDelayRate() {
    if (sch_flt_total && comp_reason_delay_flt >= 0) {
      // 延误率
      var delay_rate = comp_reason_delay_flt / sch_flt_total;
      $("#val_delay_rate").text(Math.round(delay_rate * 1000) / 10);
    }
  }

  function drawDelayChart(rate) {
    // chart
    var canvas = document.getElementById("cvs_delay_cause");
    var context = canvas.getContext("2d");
    context.clearRect(0, 0, canvas.width, canvas.height);
    var x = canvas.width / 2;
    var y = canvas.height / 2;

    // draw bg circle
    var radius = 60;
    context.beginPath();
    context.arc(x, y, radius, 0, 2 * Math.PI, false);
    context.lineWidth = 10;
    context.strokeStyle = "#9E99FF";
    context.stroke();

    // draw arc
    var startAngle = Math.PI - (Math.PI * 2 * rate) / 2;
    var endAngle = Math.PI + (Math.PI * 2 * rate) / 2;
    context.beginPath();
    context.arc(x, y, radius, startAngle, endAngle, false);
    context.lineWidth = 10;
    context.strokeStyle = "#FFE900";
    context.stroke();

    // draw lines
    var numslice = 12;
    for (var i = 0; i < numslice; i++) {
      context.beginPath();
      var startAngle = i * ((Math.PI * 2) / numslice);
      var endAngle = startAngle + Math.PI * 0.01;
      context.arc(x, y, radius, startAngle, endAngle, false);
      context.lineWidth = 11;
      context.strokeStyle = "#041946";
      context.stroke();
    }
  }

  // ------------------------------------------------------------------------
  // 天气情况
  // ------------------------------------------------------------------------

  var weather_map = {
    晴: "icon-e600_sunny",
    沙: "icon-e617_dust1",
    雹: "icon-e620_hail",
    雾: "icon-e615_fog",
    烟: "icon-e615_fog",
    阴: "icon-e604_gloomy",
    雷: "icon-e606_rain2",
    暴: "icon-e606_rain2",
    风: "icon-e612_wind",
    霾: "icon-e613_haze",
    云: "icon-e602_cloudy",
    雨: "icon-e607_rain3",
    雪: "icon-e610_snow3",
  };

  var numOfLoadingBase = 0;
  var city_weather = [];

  for (var arpcode in BASE_CITY_LIST) {
    var param = {
      airport: arpcode,
    };

    numOfLoadingBase++;

    $.ajax({
      type: "post",
      url: "/bi/web/7x2_arp_weather",
      contentType: "application/json",
      dataType: "json",
      async: true,
      data: JSON.stringify(param),
      success: function (response) {
        numOfLoadingBase--;

        //console.log('7x2_arp_weather-------');
        //console.log(response);
        if (Number(response.errorcode) == 0) {
          var wdat = {};
          var weather_css = "icon-e600_sunny";
          var cardcolor_css = "";
          /*
          airport
          airportCode
          cloudInfo 云况
          metUtcTime
          rvr 跑道目视距离
          temperature
          visibility 能见度
          weatherInfo 天气现象
          weatherInfoTxt 翻译后的天气
          windFs 风速

          红色范围
          “能见度 小于等于800；跑道视程小于等于700   天气现象（大雾、雷暴等） 云况高度（60-90米） 风速15米/秒”
          黄色范围
          “能见度 小于等于1600米；跑道视程小于等于1400米   天气现象（大雾、雷暴等） 云况高度小于等于150米  风速大于等于10米/秒”


          注：FG代表大雾
                TS或TSRA或+RA 代表雷暴
                 SS或DS沙尘暴

          */
          var weatherInfoCodes = ["FG", "TS", "TSRA", "RA", "SS", "DS"];
          var code = response.airport;
          var temperature = response.temperature;
          var airPressureQ =
            response.airPressureQ == undefined ? "" : response.airPressureQ;
          var dewpoint = response.dewpoint;
          var visibility = isNaN(response.visibility)
            ? ""
            : Number(response.visibility); //能见度
          var rvr =
            isNaN(response.rvr) || response.rvr == 0
              ? ""
              : Number(response.rvr); //跑道目视距离
          var cloudInfo = isNaN(response.cloudInfo)
            ? ""
            : Number(response.cloudInfo); //云况
          var windFs = isNaN(response.windFs) ? 0 : Number(response.windFs); //风速
          var windFx = response.windFx == undefined ? "" : response.windFx; // 风向
          var weatherInfo = response.weatherInfo; //天气现象
          var weatherInfoTxt = response.weatherInfoTxt.replace(/<[^>]+>/g, "");

          var weather_css = "";

          for (var wtxt in weather_map) {
            if (weatherInfoTxt.indexOf(wtxt) > -1) {
              weather_css = weather_map[wtxt];
            }
          }

          wdat.code = code;
          wdat.temperature = temperature;
          wdat.airPressureQ = airPressureQ;
          wdat.dewpoint = dewpoint;
          wdat.visibility = visibility;
          wdat.rvr = rvr;
          wdat.cloudInfo = cloudInfo;
          wdat.windFs = windFs;
          wdat.windFx = windFx;
          wdat.weatherInfoTxt = weatherInfoTxt;
          wdat.css = weather_css;

          city_weather.push(wdat);

          if (numOfLoadingBase == 0) {
            setWeather();
          }
        } else {
          console.log("7x2_arp_weather Error");
        }
      },
      error: function (jqXHR, txtStatus, errorThrown) {
        numOfLoadingBase--;
      },
    });

    // 雷达图
    getRadarImage(arpcode);
  }

  //////// 机场气象告警 //////////

  var date = new Date();
  var mm = date.getMonth() + 1;
  var dd = date.getDate();
  if (mm < 10) {
    mm = "0" + mm;
  }
  if (dd < 10) {
    dd = "0" + dd;
  }
  var stdStart = date.getFullYear() + "-" + mm + "-" + dd + " 00:00:00";
  var stdEnd = date.getFullYear() + "-" + mm + "-" + dd + " 23:59:59";

  var unnormalBase;
  var arpcodes = [];
  for (var c in BASE_CODE_LIST) {
    var cc = BASE_CODE_LIST[c];
    arpcodes.push(cc);
  }
  var param = {
    cccsList: arpcodes.join(","),
    updateDateStart: stdStart,
    updateDateEnd: stdEnd,
  };
  $.ajax({
    type: "post",
    url: "/bi/web/findWfMetrepBiaoZhuns",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      for (var k in response) {
        var obj = response[k];
        if (obj.alarmValue == 2) {
          //1 表示黄色; 2 表示红色
          if (unnormalBase == undefined) {
            unnormalBase = {};
          }
          unnormalBase[obj.ccc] = obj;
        }
      }
    },
    error: function () {},
  });

  // 不正常天气基地
  var wxCode2Name = {
    TS: "干雷",
    TSRA: "中雷雨",
    "-TSRA": "弱雷雨",
    "+TSRA": "强雷雨",
    CB: "对流云",
    TCU: "浓积云",
    RA: "中雨",
    "+RA": "大雨",
    "+SHRA": "强阵雨",
    SHRA: "中阵雨",
    DZ: "毛毛雨",
    FZRA: "冻雨",
    GR: "冰雹",
    GS: "霰",
    WS: "风切变",
    FG: "大雾",
    FU: "烟",
    HZ: "霾",
    BR: "轻雾",
    FZFG: "冻雾",
    BCFG: "散雾",
    MIFG: "浅雾",
    SN: "中雪",
    "+SN": "大雪",
    SHSN: "阵雪",
    "+SHSN": "强阵雪",
    BLSN: "高吹雪",
    DRSA: "低吹雪",
    SA: "扬沙",
    SS: "沙暴",
    BLSA: "高吹沙",
    DRSA: "低吹沙",
    "+SS": "强沙暴",
    DU: "浮尘",
  };

  function setUnnormalWeatherBase(arpcode) {
    /*
    if(!isNaN(wdat.visibility) && wdat.visibility <= 1600 && wdat.visibility > 0){
      $('#weather_warning_info').text(city+'今日能见度'+wdat.visibility+'m，请做好保障措施');
      $('#weather_warning_info').show();
    }else if(!isNaN(wdat.rvr) && wdat.rvr <= 1400 && wdat.rvr > 0){
      $('#weather_warning_info').text(city+'今日跑道视程'+wdat.rvr+'m，请做好保障措施');
      $('#weather_warning_info').show();
    }else if(!isNaN(wdat.windFs) && wdat.windFs >= 10){
      $('#weather_warning_info').text(city+'今日风速'+wdat.windFs+'m/s，请做好保障措施');
      $('#weather_warning_info').show();
    }else if(!isNaN(wdat.cloudInfo) && wdat.cloudInfo <= 150 && wdat.cloudInfo > 0){
      $('#weather_warning_info').text(city+'今日云况'+wdat.cloudInfo+'m，请做好保障措施');
      $('#weather_warning_info').show();
    }
    */

    var city = BASE_CITY_LIST[arpcode];
    var ccc = BASE_CODE_LIST[arpcode];

    if (unnormalBase == undefined || unnormalBase[ccc] == undefined) {
      $("#weather_warning_info").html("机场气象告警");
      return;
    }

    var obj = unnormalBase[ccc];

    var cont = wxCode2Name[obj.wx] ? wxCode2Name[obj.wx] : obj.wx;
    $("#weather_warning_info").html(city + "今日" + cont + "，请做好保障措施");
  }

  // ------------------------------------------------------------------------
  // 雷达图
  // ------------------------------------------------------------------------
  var radarPicList;

  function getRadarImage(arpcode) {
    var param = {
      airport: arpcode,
    };
    $.ajax({
      type: "post",
      url: "/bi/web/7x2_arp_weather_radar",
      contentType: "application/json",
      dataType: "json",
      async: true,
      data: JSON.stringify(param),
      success: function (response) {
        if (radarPicList == undefined) radarPicList = {};
        var list = response.data;
        var cndtradartime = "1970-01-01 00:00:00";
        var cnvcradarfile = "";
        if (list) {
          for (var i = list.length - 1; i >= 0; i--) {
            var d = list[i];

            var d1 = parserDate(d.cndtradartime);
            var d2 = parserDate(cndtradartime);

            if (d1.getTime() > d2.getTime()) {
              cndtradartime = d.cndtradartime;
              cnvcradarfile = d.cnvcradarfile;
            }
          }
          radarPicList[arpcode] = cnvcradarfile;
        } else {
          console.log("雷达接口无数据返回");
        }
      },
      error: function () {},
    });
  }

  var weather_idx = 0;

  function setWeather() {
    if (city_weather.length == 0 || radarPicList == undefined) {
      return;
    }
    var wdat = city_weather[weather_idx];

    var city = BASE_CITY_LIST[wdat.code];
    $("#weather_city").text(city + "天气");
    $("#weather_icon").attr("class", wdat.css);
    $(".weather_block .info .txt").text(wdat.weatherInfoTxt);
    $(".weather_block .info .tmp").text(wdat.temperature + "℃");
    $(".weather_block .temperature").text(wdat.temperature + "℃");
    $(".weather_block .airPressureQ").text(
      wdat.airPressureQ.substring(1) + "hPa"
    );
    $(".weather_block .dewpoint").text(
      wdat.dewpoint == "" ? "无" : wdat.dewpoint + "℃"
    );
    $(".weather_block .visibility").text(wdat.visibility + "m");
    $(".weather_block .windFx").text(wdat.windFx + "°");
    $(".weather_block .windFs").text(wdat.windFs + "m/s");

    /*
    if(!isNaN(wdat.visibility) && wdat.visibility <= 1600 && wdat.visibility > 0){
      $('#weather_warning_info').text(city+'今日能见度'+wdat.visibility+'m，请做好保障措施');
      $('#weather_warning_info').show();
    }else if(!isNaN(wdat.rvr) && wdat.rvr <= 1400 && wdat.rvr > 0){
      $('#weather_warning_info').text(city+'今日跑道视程'+wdat.rvr+'m，请做好保障措施');
      $('#weather_warning_info').show();
    }else if(!isNaN(wdat.windFs) && wdat.windFs >= 10){
      $('#weather_warning_info').text(city+'今日风速'+wdat.windFs+'m/s，请做好保障措施');
      $('#weather_warning_info').show();
    }else if(!isNaN(wdat.cloudInfo) && wdat.cloudInfo <= 150 && wdat.cloudInfo > 0){
      $('#weather_warning_info').text(city+'今日云况'+wdat.cloudInfo+'m，请做好保障措施');
      $('#weather_warning_info').show();
    }
    */

    var cnvcradarfile = radarPicList[wdat.code];
    if (cnvcradarfile && cnvcradarfile.length > 0) {
      $(".weather_atlas").css(
        "background-image",
        "url(/bi/web/getRadarpic?filename=" + cnvcradarfile + ")"
      );
    }

    setUnnormalWeatherBase(wdat.code);
  }

  setInterval(function () {
    if (weather_idx < city_weather.length - 1) {
      weather_idx++;
    } else {
      weather_idx = 0;
    }
    setWeather();
  }, 10000);

  $("#weather_city_prev").on("click", function (evt) {
    if (weather_idx < city_weather.length - 1) {
      weather_idx++;
    } else {
      weather_idx = 0;
    }
    setWeather();
  });

  $("#weather_city_next").on("click", function (evt) {
    if (weather_idx > 0) {
      weather_idx--;
    } else {
      weather_idx = city_weather.length - 1;
    }
    setWeather();
  });

  // ------------------------------------------------------------------------
  // 正常率 日
  // ------------------------------------------------------------------------
  // rate 通过esb接口获得正常率
  // rate_from_db 中间表获得正常率
  var today_normal_rate_from_api;
  var today_normal_rate_from_db;

  // ------------------------------------------------------------------------
  // 获取日实际正常率 数据
  // ------------------------------------------------------------------------
  function getNormalRateToday() {
    const today = new Date();
    // 获取昨天的日期
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1); // 设置为昨天
    yesterday.setUTCHours(16, 0, 0, 0); // 设置为昨天16:00:00 UTC
    // 获取今天的日期
    today.setUTCHours(15, 59, 59, 999); // 设置为今天15:59:59 UTC
    // 格式化为字符串
    const stdStartUtcTime = yesterday
      .toISOString()
      .slice(0, 19)
      .replace("T", " ");
    const stdEndUtcTime = today.toISOString().slice(0, 19).replace("T", " ");

    return new Promise((resolve, reject) => {
      var params = {
        companyCodes: comp_code,
        isLongRegsNotIn: false,
        stdEndUtcTime: stdEndUtcTime,
        stdStartUtcTime: stdStartUtcTime,
      };
      $.ajax({
        type: "post",
        url: "/bi/web/flightAmountStaticV2?normal_rate",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(params),
        success: function (res) {
          var kpiValue = 0;
          if (res && res.pfdappPercent) {
            kpiValue = Number(res.pfdappPercent);
          }
          resolve(Number(kpiValue) / 100);
        },
        error: function () {
          resolve(0); // 发生错误时解析为 0
        },
      });
    });
  }

  function setNormalRateChartToday() {
    if (
      normal_rate_day == undefined ||
      today_normal_rate_from_api == undefined
    ) {
      setTimeout(setNormalRateChartToday, 10);
      return;
    }

    var id = 1;
    var rate = today_normal_rate_from_api;
    var rate2 = normal_rate_day / 100;

    $("#normal_rate_today").text(Math.round(rate * 1000) / 10);

    drawNormalRateChart(id, rate, rate2);
  }

  // ------------------------------------------------------------------------
  // 正常率 月
  // ------------------------------------------------------------------------

  function setNormalRateChartMonth(rate) {
    if (normal_rate_month == undefined) {
      setTimeout(setNormalRateChartMonth, 10, rate);
      return;
    }

    var id = 2;
    var rate2 = normal_rate_month / 100;
    drawNormalRateChart(id, rate, rate2);
  }

  // ------------------------------------------------------------------------
  // 正常率 年
  // ------------------------------------------------------------------------

  function setNormalRateChartYear(rate) {
    if (normal_rate_year == undefined) {
      setTimeout(setNormalRateChartYear, 10, rate);
      return;
    }

    var id = 3;
    var rate2 = normal_rate_year / 100;
    console.log(id, rate, rate2);
    drawNormalRateChart(id, rate, rate2);
  }
  // ------------------------------------------------------------------------
  // 获取月、年实际正常率 数据
  // ------------------------------------------------------------------------
  function getNormalRateMonthYear(dateType) {
    var dateId = "";
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, "0");
    if (dateType === "Y") {
      dateId = `${year}`;
    } else if (dateType === "M") {
      dateId = `${year}${month}`;
    }

    return new Promise((resolve, reject) => {
      var params = {
        dateType: dateType,
        compCode: comp_code,
        kpiCodes: ["NORMAL_RATE_ZT"],
        dateId: dateId,
      };
      $.ajax({
        type: "post",
        url: "/bi/spring/facCompKpi/queryKpi.json",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(params),
        success: function (res) {
          var kpiValue = 0;
          if (res && res.data && res.data["NORMAL_RATE_ZT"]) {
            kpiValue = res.data["NORMAL_RATE_ZT"].kpiValue;
          }
          resolve(Number(kpiValue * 100));
        },
        error: function () {
          resolve(0); // 发生错误时解析为 0
        },
      });
    });
  }

  function drawNormalRateChart(id, rate, rate2) {
    $("#col_l .row1 .chart" + id).show();

    var canvas = document.getElementById("cvs_normal_rate" + id);
    var context = canvas.getContext("2d");
    context.clearRect(0, 0, canvas.width, canvas.height);
    var x = canvas.width / 2;
    var y = canvas.height / 2;

    // draw back
    var radius = 66;
    var startAngle = Math.PI - Math.PI / 5;
    var endAngle = startAngle + Math.PI + (Math.PI / 5) * 2;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle, endAngle, counterClockwise);
    context.lineWidth = 7;
    context.strokeStyle = "#222864";
    context.stroke();

    // draw overlay 1 ==== 实际
    var radius = 50;
    var startAngle2 = startAngle;
    var endAngle2 = startAngle + (endAngle - startAngle) * rate;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
    context.lineWidth = 7;

    // linear gradient
    if (rate < 0.5) {
      var color = context.createLinearGradient(
        0,
        canvas.height,
        canvas.width * rate,
        0
      );
    } else if (rate < 0.8) {
      var color = context.createLinearGradient(
        0,
        canvas.height,
        canvas.width * rate,
        0
      );
    } else {
      var color = context.createLinearGradient(
        0,
        canvas.height,
        canvas.width * rate,
        canvas.width / 2
      );
    }
    color.addColorStop(0, "#65498B");
    color.addColorStop(1, "#FFE801");

    context.strokeStyle = color;
    context.stroke();

    // pointer
    var angle = startAngle + (endAngle - startAngle) * rate;
    $("#cvs_normal_rate" + id + "_pointer").css(
      "transform",
      "rotate(" + (angle / Math.PI) * 180 + "deg)"
    );

    // draw overlay 2 ==== 计划
    var radius = 58;
    var startAngle2 = startAngle;
    var endAngle2 = startAngle + (endAngle - startAngle) * rate2;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
    context.lineWidth = 7;

    // linear gradient
    if (rate2 < 0.5) {
      var color = context.createLinearGradient(
        0,
        canvas.height,
        canvas.width * rate2,
        0
      );
    } else if (rate2 < 0.8) {
      var color = context.createLinearGradient(
        0,
        canvas.height,
        canvas.width * rate2,
        0
      );
    } else {
      var color = context.createLinearGradient(
        0,
        canvas.height,
        canvas.width * rate2,
        canvas.width / 2
      );
    }
    color.addColorStop(0, "#446295");
    color.addColorStop(1, "#93FF6E");

    context.strokeStyle = color;
    context.stroke();

    // pointer
    var angle = startAngle + (endAngle - startAngle) * rate2;
    $("#cvs_normal_rate" + id + "b_pointer").css(
      "transform",
      "rotate(" + (angle / Math.PI) * 180 + "deg)"
    );

    // draw lines
    var numslice = 10;
    var radius = 54;
    for (var i = 1; i < numslice; i++) {
      context.beginPath();
      var startAngle =
        Math.PI - Math.PI / 5 + i * ((Math.PI + (Math.PI / 5) * 2) / numslice);
      var endAngle = startAngle + Math.PI * 0.01;
      context.arc(x, y, radius, startAngle, endAngle, false);
      context.lineWidth = 16;
      context.strokeStyle = "#041946";
      context.stroke();
    }
  }

  // ------------------------------------------------------------------------
  // 正常率走势
  // ------------------------------------------------------------------------

  var normal_rate_date_type = "M";
  var normal_rate_rank_type = "ALL";

  $("#tab_normal_rate_month").on("off");
  $("#tab_normal_rate_month").on("click", function (evt) {
    currentTabIndex = 0;

    normal_rate_date_type = "M";
    $("#tab_normal_rate_month").addClass("selected");
    $("#tab_normal_rate_year").removeClass("selected");
    $("#chart_normal_trend_month").show();
    $("#chart_normal_trend_year").hide();
    setNormalRateTabSelection();

    clearTimeout(itv_autoSwitchTab);
    itv_autoSwitchTab = setTimeout(autoSwitchTab, time_autoSwitchTab);
  });

  $("#tab_normal_rate_year").on("off");
  $("#tab_normal_rate_year").on("click", function (evt) {
    currentTabIndex = 1;

    normal_rate_date_type = "Y";
    $("#tab_normal_rate_month").removeClass("selected");
    $("#tab_normal_rate_year").addClass("selected");
    $("#chart_normal_trend_month").hide();
    $("#chart_normal_trend_year").show();
    setNormalRateTabSelection();

    clearTimeout(itv_autoSwitchTab);
    itv_autoSwitchTab = setTimeout(autoSwitchTab, time_autoSwitchTab);
  });

  // 从接口获取月度正常率走势数据
  function setNormalRateTrendMonth() {
    getNormalRateTrendFromApi("D");
  }

  function drawNormalRateTrendmMonth(xAxisData, chartData) {
    var chart = echarts.init(
      document.getElementById("chart_normal_trend_month")
    );
    var option = {};
    option = {
      tooltip: {
        show: true,
      },
      legend: {
        show: false,
        data: [""],
      },
      grid: {
        top: 10,
        left: 40,
        right: 10,
        bottom: 30,
      },
      xAxis: [
        {
          type: "category",
          //data: ['1月','2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月'],
          data: xAxisData,
          nameTextStyle: {
            color: "#516BBA",
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0)", // 轴线颜色
            },
          },
          axisLabel: {
            textStyle: {
              color: "#516BBA", // 轴标签颜色大小
              fontSize: 11,
            },
          },
        },
      ],
      yAxis: [
        {
          type: "value",
          name: "",
          min: 0,
          max: 100,
          interval: 25,
          nameTextStyle: {
            color: "#516BBA",
            fontSize: 10,
          },
          axisLabel: {
            formatter: "{value}%",
            textStyle: {
              color: "#516BBA",
              fontSize: 10,
            },
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0)",
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ["#243474"], // 分割线颜色
            },
          },
        },
      ],
      series: [
        {
          name: "",
          type: "bar",
          barWidth: 6,
          data: chartData,
          itemStyle: {
            normal: {
              color: "#2693FF",
            },
          },
        },
      ],
    };

    chart.setOption(option);
  }

  // var normal_rate_trend_month_data;

  // function setNormalRateTrendMonth(datalist) {

  //   if (today_normal_rate_from_api == undefined) {
  //     setTimeout(setNormalRateTrendMonth, 10, datalist);
  //     return;
  //   }

  //   var responsedata;
  //   if (datalist) {
  //     responsedata = datalist;
  //     normal_rate_trend_month_data = responsedata;
  //   } else {
  //     responsedata = normal_rate_trend_month_data;
  //   }

  //   var mdat_nor = responsedata[comp_code]['NORMAL_NO_T']['D'];
  //   var mdat_executed = responsedata[comp_code]['EXCUTED_NO']['D'];
  //   var mdat_cancel = responsedata[comp_code]['CANCEL_NO']['D'];

  //   // 正常率趋势 每日
  //   var date = new Date();
  //   var mm = date.getMonth() + 1;
  //   var d = date.getDate();
  //   var yy = date.getFullYear();

  //   if (mm < 10) {
  //     mm = '0' + mm;
  //   }
  //   if (d < 10) {
  //     d = '0' + d;
  //   }

  //   var normal_rate_list = [];

  //   // today
  //   var today = yy + '' + mm + d;

  //   var rate_from_db = -1;

  //   for (var i = 0; i < d; i++) {
  //     var dd = i + 1;
  //     if (dd < 10) {
  //       dd = '0' + dd;
  //     }
  //     var month = yy + '' + mm + dd;
  //     var val = getNumber(mdat_nor[month]);
  //     var val2 = getNumber(mdat_executed[month]);
  //     var val3 = getNumber(mdat_cancel[month]);
  //     var rate = 0;
  //     if (val > 0 && val2 > 0) {
  //       rate = Math.round((val / (val2 + val3)) * 1000) / 10;
  //     }

  //     if (month.toString() == today.toString()) {
  //       normal_rate_list.push(today_normal_rate_from_api * 100);
  //     } else {
  //       normal_rate_list.push(rate);
  //     }

  //   }

  //   var list = normal_rate_list;

  //   var chart = echarts.init(document.getElementById('chart_normal_trend_month'));

  //   var colorrange = ['#FF0000', '#FF8000', '#FFFF26', '#00B22D'];
  //   var d = new Date();
  //   var numofday = new Date(d.getFullYear(), d.getMonth() + 1, 0).getDate();

  //   var xAxisData = function () {
  //     var arr = [];
  //     for (var i = 0; i < numofday; i++) {
  //       arr.push(i + 1);
  //     }
  //     return arr;
  //   };
  //   var chartData = function () {
  //     var arr = [];
  //     for (var i = 0; i < numofday; i++) {
  //       var val = list[i];
  //       val = isNaN(val) ? 0 : val;
  //       var color;
  //       if (val < 85) {
  //         color = '#FF8000';
  //       } else {
  //         color = '#69DB00';
  //       }

  //       var dat = {
  //         value: val,
  //         itemStyle: {
  //           normal: {
  //             color: color
  //           }
  //         }
  //       }
  //       arr.push(dat);
  //     }
  //     return arr;
  //   };

  //   var option = {};
  //   option = {
  //     tooltip: {
  //       show: true
  //     },
  //     legend: {
  //       show: false,
  //       data: ['']
  //     },
  //     grid: {
  //       top: 16,
  //       left: 36,
  //       right: 3,
  //       bottom: 30,
  //     },
  //     xAxis: [{
  //       type: 'category',
  //       //data: ['1月','2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月'],
  //       data: xAxisData(),
  //       nameTextStyle: {
  //         color: '#516BBA'
  //       },
  //       axisLine: {
  //         lineStyle: {
  //           color: 'rgba(255,255,255,0)' // 轴线颜色
  //         }
  //       },
  //       axisLabel: {
  //         textStyle: {
  //           color: '#516BBA', // 轴标签颜色大小
  //           fontSize: 11,
  //         }
  //       }
  //     }],
  //     yAxis: [{
  //       type: 'value',
  //       name: '',
  //       min: 0,
  //       max: 100,
  //       interval: 25,
  //       nameTextStyle: {
  //         color: '#516BBA',
  //         fontSize: 10
  //       },
  //       axisLabel: {
  //         formatter: '{value}%',
  //         textStyle: {
  //           color: '#516BBA',
  //           fontSize: 10,
  //         }
  //       },
  //       axisLine: {
  //         lineStyle: {
  //           color: 'rgba(255,255,255,0)'
  //         }
  //       },
  //       splitLine: {
  //         show: true,
  //         lineStyle: {
  //           color: ['#243474'] // 分割线颜色
  //         }
  //       }
  //     }],
  //     series: [{
  //       name: '',
  //       type: 'bar',
  //       barWidth: 6,
  //       data: chartData(),
  //       itemStyle: {
  //         normal: {
  //           color: '#2693FF'
  //         }
  //       }
  //     }]
  //   };

  //   chart.setOption(option);
  // }

  // 年
  /*
  var param = {
      'mode': "query"
    }

    $.ajax({
        type: 'post',
        url:"/bi/web/west_normal_rates_year",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            //checkLogin(response);

            setNormalRateTrendYear(response.data);
            
        },
        error:function(jqXHR, txtStatus, errorThrown) {
          console.log('----error');
        }
    });
  */

  // var normal_rate_trend_year_data;

  // function setNormalRateTrendYear(datalist) {
  //   var list;
  //   if (datalist) {
  //     list = datalist;
  //     normal_rate_trend_year_data = list;
  //   } else {
  //     list = normal_rate_trend_year_data;
  //   }
  //   var chart = echarts.init(
  //     document.getElementById("chart_normal_trend_year")
  //   );

  //   var colorrange = ["#FF0000", "#FF8000", "#FFFF26", "#00B22D"];

  //   var numofday = 12;

  //   var xAxisData = function () {
  //     var arr = [];
  //     for (var i = 0; i < numofday; i++) {
  //       arr.push(i + 1);
  //     }
  //     return arr;
  //   };
  //   var chartData = function () {
  //     var arr = [];
  //     for (var i = 0; i < numofday; i++) {
  //       var val = list[i];
  //       val = isNaN(val) ? 0 : val;
  //       var color;
  //       if (val < 85) {
  //         color = "#FF8000";
  //       } else {
  //         color = "#69DB00";
  //       }

  //       var dat = {
  //         value: val,
  //         itemStyle: {
  //           normal: {
  //             color: color,
  //           },
  //         },
  //       };
  //       arr.push(dat);
  //     }
  //     return arr;
  //   };

  //   var option = {};
  //   option = {
  //     tooltip: {
  //       show: true,
  //     },
  //     legend: {
  //       show: false,
  //       data: [""],
  //     },
  //     grid: {
  //       top: 16,
  //       left: 36,
  //       right: 3,
  //       bottom: 30,
  //     },
  //     xAxis: [
  //       {
  //         type: "category",
  //         data: [
  //           "1月",
  //           "2月",
  //           "3月",
  //           "4月",
  //           "5月",
  //           "6月",
  //           "7月",
  //           "8月",
  //           "9月",
  //           "10月",
  //           "11月",
  //           "12月",
  //         ],
  //         //data: xAxisData(),
  //         nameTextStyle: {
  //           color: "#516BBA",
  //         },
  //         axisLine: {
  //           lineStyle: {
  //             color: "rgba(255,255,255,0)", // 轴线颜色
  //           },
  //         },
  //         axisLabel: {
  //           textStyle: {
  //             color: "#516BBA", // 轴标签颜色大小
  //             fontSize: 11,
  //           },
  //         },
  //       },
  //     ],
  //     yAxis: [
  //       {
  //         type: "value",
  //         name: "",
  //         min: 0,
  //         max: 100,
  //         interval: 25,
  //         nameTextStyle: {
  //           color: "#516BBA",
  //           fontSize: 10,
  //         },
  //         axisLabel: {
  //           formatter: "{value}%",
  //           textStyle: {
  //             color: "#516BBA",
  //             fontSize: 10,
  //           },
  //         },
  //         axisLine: {
  //           lineStyle: {
  //             color: "rgba(255,255,255,0)",
  //           },
  //         },
  //         splitLine: {
  //           show: true,
  //           lineStyle: {
  //             color: ["#243474"], // 分割线颜色
  //           },
  //         },
  //       },
  //     ],
  //     series: [
  //       {
  //         name: "",
  //         type: "bar",
  //         barWidth: 12,
  //         data: chartData(),
  //         itemStyle: {
  //           normal: {
  //             color: "#2693FF",
  //           },
  //         },
  //       },
  //     ],
  //   };

  //   chart.setOption(option);
  // }

  // 从接口获取年度正常率走势数据
  function setNormalRateTrendYear() {
    getNormalRateTrendFromApi("M");
  }

  function drawNormalRateTrendmYear(chartData) {
    var chart = echarts.init(
      document.getElementById("chart_normal_trend_year")
    );
    var option = {};
    option = {
      tooltip: {
        show: true,
      },
      legend: {
        show: false,
        data: [""],
      },
      grid: {
        top: 10,
        left: 40,
        right: 10,
        bottom: 30,
      },
      xAxis: [
        {
          type: "category",
          data: [
            "1月",
            "2月",
            "3月",
            "4月",
            "5月",
            "6月",
            "7月",
            "8月",
            "9月",
            "10月",
            "11月",
            "12月",
          ],
          //data: xAxisData(),
          nameTextStyle: {
            color: "#516BBA",
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0)", // 轴线颜色
            },
          },
          axisLabel: {
            textStyle: {
              color: "#516BBA", // 轴标签颜色大小
              fontSize: 11,
            },
          },
        },
      ],
      yAxis: [
        {
          type: "value",
          name: "",
          min: 0,
          max: 100,
          interval: 25,
          nameTextStyle: {
            color: "#516BBA",
            fontSize: 10,
          },
          axisLabel: {
            formatter: "{value}%",
            textStyle: {
              color: "#516BBA",
              fontSize: 10,
            },
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0)",
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ["#243474"], // 分割线颜色
            },
          },
        },
      ],
      series: [
        {
          name: "",
          type: "bar",
          barWidth: 12,
          data: chartData,
          itemStyle: {
            normal: {
              color: "#2693FF",
            },
          },
        },
      ],
    };

    chart.setOption(option);
  }

  // 从接口获取月、年正常率走势
  function getNormalRateTrendFromApi(dateType) {
    var limit;
    var maxDateId = "";

    // 获取当前日期
    var currentDate = new Date(); // 自动获取当前日期
    var year = currentDate.getFullYear();
    var month = currentDate.getMonth() + 1; // 月份从0开始，所以需要加1
    var day = currentDate.getDate();

    var xAxisData = [];
    for (var i = 0; i < day; i++) {
      xAxisData.push(i + 1);
    }

    if (dateType === "D") {
      maxDateId =
        year.toString() +
        (month < 10 ? "0" + month : month) +
        (day < 10 ? "0" + (day - 1) : day - 1);
      limit = day - 1;
    } else if (dateType === "M") {
      maxDateId = year.toString() + (month < 10 ? "0" + month : month);
      limit = month; // 当前月份
    } else {
      return;
    }

    var param = {
      compCode: comp_code,
      kpiCode: "NORMAL_RATE_ZT",
      dateType: dateType,
      limit: limit,
      maxDateId: maxDateId,
    };

    $.ajax({
      type: "post",
      url: "/bi/spring/facCompKpi/queryKpiTrend.json",
      contentType: "application/json",
      dataType: "json",
      async: true,
      data: JSON.stringify(param),
      success: function (response) {
        var chartData = [];
        for (var i = 0; i < response.data.length; i++) {
          let kpiValue = response.data[i].kpiValue;
          kpiValue = isNaN(kpiValue) ? 0 : kpiValue * 100;
          let color = "";
          if (kpiValue < 85) {
            color = "#FF8000";
          } else {
            color = "#69DB00";
          }
          chartData.push({
            value: kpiValue.toFixed(1),
            itemStyle: {
              normal: {
                color: color,
              },
            },
          });
        }
        if (dateType === "D") {
          getNormalRateToday().then((kpiValue) => {
            let todayVal = (kpiValue * 100).toFixed(1);
            let todayColor = Number(todayVal) < 85 ? "#FF8000" : "#69DB00";
            chartData.push({
              value: todayVal,
              itemStyle: {
                normal: {
                  color: todayColor,
                },
              },
            });
            drawNormalRateTrendmMonth(xAxisData, chartData);
          });
        } else if (dateType === "M") {
          drawNormalRateTrendmYear(chartData);
        }
      },
      error: function () {
        // 处理错误情况
      },
    });
  }

  function getRandNumber() {
    var newnum = parseInt(Math.random() * 100);
    if (newnum >= 0 && newnum <= 100) {
      return newnum;
    } else {
      return getRandNumber();
    }
  }

  // ------------------------------------------------------------------------
  // 获取所有子公司
  // ------------------------------------------------------------------------
  var companylist;
  var companyNormalRateList = [];

  function getCompany() {
    $.ajax({
      type: "POST",
      url: "/bi/query/company",
      async: true,
      dataType: "json",
      data: "",
      success: function (response) {
        //checkLogin(response);

        companylist = response.comp;
        getAllCompNormalRate();
      },
      error: function (e) {
        console.log("ajax error");
        console.log(e);
      },
    });
  }

  getCompany();

  function getAllCompNormalRate() {
    var compcodes = [];
    var compnames = {};
    var len = companylist.length;
    for (var i = 0; i < len; i++) {
      var obj = companylist[i];
      compcodes.push(obj.code);
      compnames[obj.code] = obj.nameabbr;
    }

    var param = {
      SOLR_CODE: "FAC_COMP_KPI",
      COMP_CODE: compcodes.join(","),
      KPI_CODE: "NORMAL_NO_T,SCH_NO",
      VALUE_TYPE: "kpi_value_d",
      DATE_TYPE: "M",
      OPTIMIZE: 1,
      LIMIT: 12,
    };

    $.ajax({
      type: "post",
      url: "/bi/query/getkpi?all-company-normalRate",
      contentType: "application/json",
      dataType: "json",
      async: true,
      data: JSON.stringify(param),
      success: function (response) {
        for (var i = 0; i < compcodes.length; i++) {
          var ccode = compcodes[i];

          if (ccode != "HNAHK") {
            var comp = {};
            comp["code"] = ccode;
            comp["name"] = compnames[ccode];

            var mdat_nor = response.data[ccode]["NORMAL_NO_T"]["M"];
            var mdat_sch = response.data[ccode]["SCH_NO"]["M"];

            var m_nor = 0;
            var m_sch = 0;
            var y_nor = 0;
            var y_sch = 0;
            var date = 0;
            for (var time in mdat_nor) {
              var val = Number(mdat_nor[time]);
              var val2 = Number(mdat_sch[time]);
              if (val > 0 && val2 > 0) {
                if (Number(time) > date) {
                  m_nor = val;
                  m_sch = val2;
                  date = Number(time);
                }
                y_nor += val;
                y_sch += val2;
              }
            }

            var normal_rate_month = 0;
            var normal_rate_year = 0;

            if (m_nor > 0 && m_sch > 0) {
              normal_rate_month = Math.round((m_nor / m_sch) * 1000) / 10;
            }

            if (y_nor > 0 && y_sch > 0) {
              normal_rate_year = Math.round((y_nor / y_sch) * 1000) / 10;
            }

            comp["month"] = normal_rate_month;
            comp["year"] = normal_rate_year;

            companyNormalRateList.push(comp);
          }
        }

        setGRPNormalRank("M");
        setGRPNormalRank("Y");

        // month top 5
        // var list = [];
        // companyNormalRateList.sort(function (a, b) {
        //   return b.month - a.month;
        // });
        // var lastobj;
        // for (var i = 0; i < companyNormalRateList.length; i++) {
        //   var obj = companyNormalRateList[i];
        //   var d = {};
        //   d.rank = i + 1;
        //   d.company = obj.name;
        //   if (i < 5) {
        //     list.push(d);
        //   }
        //   if (obj.code == comp_code) {
        //     lastobj = d;
        //   }
        // }
        // list.push(lastobj);
        // setTop5("rank_grp_month", list);

        // // year top 5
        // var list = [];
        // companyNormalRateList.sort(function (a, b) {
        //   return b.year - a.year;
        // });
        // var lastobj;
        // for (var i = 0; i < companyNormalRateList.length; i++) {
        //   var obj = companyNormalRateList[i];
        //   var d = {};
        //   d.rank = i + 1;
        //   d.company = obj.name;
        //   if (i < 5) {
        //     list.push(d);
        //   }
        //   if (obj.code == comp_code) {
        //     lastobj = d;
        //   }
        // }
        // list.push(lastobj);
        // setTop5("rank_grp_year", list);
      },
      error: function () {},
    });
  }

  // 从接口获取集团正常率TOP5
  function setGRPNormalRank(dateType) {
    var dateId = "";
    // 获取当前日期
    var currentDate = new Date(); // 自动获取当前日期
    var year = currentDate.getFullYear();
    var month = currentDate.getMonth() + 1; // 月份从0开始，所以需要加1
    if (dateType === "M") {
      dateId = year.toString() + (month < 10 ? "0" + month : month);
    } else if (dateType === "Y") {
      dateId = year.toString();
    } else {
      return;
    }

    var param = {
      dateId: dateId,
    };

    $.ajax({
      type: "post",
      url: "/bi/spring/facCompKpi/queryNormalRank.json",
      contentType: "application/json",
      dataType: "json",
      async: true,
      data: JSON.stringify(param),
      success: function (response) {
        var listData = [];
        if (response.data && response.data.length > 0) {
          // 取前5个数据
          listData = response.data.slice(0, 5);
          // 查找 compCode 为 'PN' 的数据
          var additionalData = response.data.find(
            (item) => item.compCode === "PN"
          );
          listData.push(additionalData);
        }
        // 根据 dateType 设置相应的 Top 5 数据
        if (dateType === "M") {
          setTop5("rank_grp_month", listData);
        } else if (dateType === "Y") {
          setTop5("rank_grp_year", listData);
        }
      },
      error: function () {
        // 处理错误情况
      },
    });
  }

  // ------------------------------------------------------------------------
  // TOP5
  // ------------------------------------------------------------------------
  $("#tab_rank_all").on("off");
  $("#tab_rank_all").on("click", function (evt) {
    normal_rate_rank_type = "ALL";

    $("#tab_rank_all").addClass("selected");
    $("#tab_rank_grp").removeClass("selected");
    $("#tab_rank_low").removeClass("selected");

    setNormalRateTabSelection();
  });

  $("#tab_rank_grp").on("off");
  $("#tab_rank_grp").on("click", function (evt) {
    normal_rate_rank_type = "GRP";

    $("#tab_rank_all").removeClass("selected");
    $("#tab_rank_grp").addClass("selected");
    $("#tab_rank_low").removeClass("selected");

    setNormalRateTabSelection();
  });

  $("#tab_rank_low").on("off");
  $("#tab_rank_low").on("click", function (evt) {
    normal_rate_rank_type = "LOW";

    $("#tab_rank_all").removeClass("selected");
    $("#tab_rank_grp").removeClass("selected");
    $("#tab_rank_low").addClass("selected");

    setNormalRateTabSelection();
  });

  function setNormalRateTabSelection() {
    $("#rank_all_month").hide();
    $("#rank_all_year").hide();
    $("#rank_grp_month").hide();
    $("#rank_grp_year").hide();
    $("#rank_low_month").hide();
    $("#rank_low_year").hide();

    if (normal_rate_date_type == "M") {
      if (normal_rate_rank_type == "ALL") {
        $("#rank_all_month").show();
      } else if (normal_rate_rank_type == "GRP") {
        $("#rank_grp_month").show();
      } else if (normal_rate_rank_type == "LOW") {
        $("#rank_low_month").show();
      }
    } else if (normal_rate_date_type == "Y") {
      if (normal_rate_rank_type == "ALL") {
        $("#rank_all_year").show();
      } else if (normal_rate_rank_type == "GRP") {
        $("#rank_grp_year").show();
      } else if (normal_rate_rank_type == "LOW") {
        $("#rank_low_year").show();
      }
    }
  }

  function setTop5(id, list) {
    var html_no = "";
    var html_name = "";
    for (var i = 0; i < list.length; i++) {
      var dat = list[i];
      html_no += '<span class="yellow rank_no">' + dat.rank + "</span>";
      html_name += '<span class="rank_no">' + dat.company + "</span>";
    }

    $("#" + id + " .no").html(html_no);
    $("#" + id + " .name").html(html_name);
  }

  //

  // all_month
  var param = {
    mode: "query",
    type: "month",
  };

  $.ajax({
    type: "post",
    url: "/bi/web/west_comp_rank",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      //checkLogin(response);

      var list = response.comp;

      //list.sort(function(a, b){
      //  return a.rank-b.rank
      //});
      for (var i = 0; i < list.length; i++) {
        var obj = list[i];
        if (obj.company == "西部") {
          //list.splice(i, 1);
          //list.push(obj);
          break;
        }
      }
      setTop5("rank_all_month", list);
    },
    error: function (jqXHR, txtStatus, errorThrown) {},
  });

  // all_year
  var param = {
    mode: "query",
    type: "year",
  };

  $.ajax({
    type: "post",
    url: "/bi/web/west_comp_rank",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      //checkLogin(response);

      var list = response.comp;

      //list.sort(function(a, b){
      //  return a.rank-b.rank
      //});
      for (var i = 0; i < list.length; i++) {
        var obj = list[i];
        if (obj.company == "西部") {
          //list.splice(i, 1);
          //list.push(obj);
          break;
        }
      }
      setTop5("rank_all_year", list);
    },
    error: function (jqXHR, txtStatus, errorThrown) {},
  });

  // low_month
  var param = {
    mode: "query",
    type: "low_month",
  };

  $.ajax({
    type: "post",
    url: "/bi/web/west_comp_rank",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      //checkLogin(response);

      var list = response.comp;

      //list.sort(function(a, b){
      //return a.rank-b.rank
      //});
      for (var i = 0; i < list.length; i++) {
        var obj = list[i];
        if (obj.company == "西部") {
          //list.splice(i, 1);
          //list.push(obj);
          break;
        }
      }
      setTop5("rank_low_month", list);
    },
    error: function (jqXHR, txtStatus, errorThrown) {},
  });

  // low_year
  var param = {
    mode: "query",
    type: "low_year",
  };

  $.ajax({
    type: "post",
    url: "/bi/web/west_comp_rank",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      //checkLogin(response);

      var list = response.comp;

      list.sort(function (a, b) {
        return a.rank - b.rank;
      });
      for (var i = 0; i < list.length; i++) {
        var obj = list[i];
        if (obj.company == "西部") {
          list.splice(i, 1);
          list.push(obj);
          break;
        }
      }
      setTop5("rank_low_year", list);
    },
    error: function (jqXHR, txtStatus, errorThrown) {},
  });

  // ------------------

  var vvip_load_cnt = 0;

  //
  var total_vip_l_flt = 0;
  var total_vip_l_psr = 0;
  var total_vip_i_flt = 0;
  var total_vip_i_psr = 0;

  var exec_vip_l_flt = 0;
  var exec_vip_l_psr = 0;
  var exec_vip_i_flt = 0;
  var exec_vip_i_psr = 0;

  var total_zz_flt = 0;
  var total_zz_psr = 0;
  var exec_zz_flt = 0;
  var exec_zz_psr = 0;

  //
  function setVipPsrFlt() {
    if (vvip_load_cnt == 0) {
      total_zz_flt = Math.max(total_zz_flt, exec_zz_flt);
      total_zz_psr = Math.max(total_zz_psr, exec_zz_psr);

      $("#total_psr_l").text(total_vip_l_psr);
      $("#total_psr_i").text(total_vip_i_psr);
      $("#exec_psr_l").text(exec_vip_l_psr);
      $("#exec_psr_i").text(exec_vip_i_psr);

      $("#total_flt_l").text(total_vip_l_flt);
      $("#total_flt_i").text(total_vip_i_flt);
      $("#exec_flt_l").text(exec_vip_l_flt);
      $("#exec_flt_i").text(exec_vip_i_flt);

      $("#total_flt_zz").text(total_zz_flt);
      $("#total_psr_zz").text(total_zz_psr);
      $("#exec_flt_zz").text(exec_zz_flt);
      $("#exec_psr_zz").text(exec_zz_psr);

      setVipTrvChartChina(
        "cvs_vip_trv_l",
        total_vip_l_psr > 0 ? exec_vip_l_psr / total_vip_l_psr : 0,
        "#B4B0FF"
      );
      setVipTrvChartChina(
        "cvs_vip_trv_i",
        total_vip_i_psr > 0 ? exec_vip_i_psr / total_vip_i_psr : 0,
        "#FF8000"
      );
      setVipTrvChartChina(
        "cvs_vip_trv_zz",
        total_zz_psr > 0 ? exec_zz_psr / total_zz_psr : 0,
        "#70FF00"
      );
      setVipTrvChartChina(
        "cvs_vip_flt_l",
        total_vip_l_flt > 0 ? exec_vip_l_flt / total_vip_l_flt : 0,
        "#B4B0FF"
      );
      setVipTrvChartChina(
        "cvs_vip_flt_i",
        total_vip_i_flt > 0 ? exec_vip_i_flt / total_vip_i_flt : 0,
        "#FF8000"
      );
      setVipTrvChartChina(
        "cvs_vip_flt_zz",
        total_zz_flt > 0 ? exec_zz_flt / total_zz_flt : 0,
        "#70FF00"
      );
    }
  }
  //
  var param = {
    companyCodes: comp_code,
    detailType: "pftc", //统计航班（总计）
    fltType: "L", // "L" 表示Local，"I" 表示International
    psrType: "vip",
  };
  vvip_load_cnt++;
  $.ajax({
    type: "post",
    url: "/bi/web/7x2_vip_flt_cnt",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      vvip_load_cnt--;

      total_vip_l_flt = response.flt_cnt;
      total_vip_l_psr = response.vip_psr_cnt;

      setVipPsrFlt();
    },
    error: function () {},
  });

  //
  var param = {
    companyCodes: comp_code,
    detailType: "cftc", //已执行航班班次
    fltType: "I", // "L" 表示Local，"I" 表示International
    psrType: "vip",
  };
  vvip_load_cnt++;
  $.ajax({
    type: "post",
    url: "/bi/web/7x2_vip_flt_cnt",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      vvip_load_cnt--;

      exec_vip_i_flt = response.flt_cnt;
      exec_vip_i_psr = response.vip_psr_cnt;

      setVipPsrFlt();
    },
    error: function () {},
  });
  //
  var param = {
    companyCodes: comp_code,
    detailType: "pftc", //统计航班（总计）
    fltType: "I", // "L" 表示Local，"I" 表示International
    psrType: "vip",
  };
  vvip_load_cnt++;
  $.ajax({
    type: "post",
    url: "/bi/web/7x2_vip_flt_cnt",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      vvip_load_cnt--;

      total_vip_i_flt = response.flt_cnt;
      total_vip_i_psr = response.vip_psr_cnt;

      setVipPsrFlt();
    },
    error: function () {},
  });
  //
  var param = {
    companyCodes: comp_code,
    detailType: "cftc", //已执行航班班次
    fltType: "L", // "L" 表示Local，"I" 表示International
    psrType: "vip",
  };
  vvip_load_cnt++;
  $.ajax({
    type: "post",
    url: "/bi/web/7x2_vip_flt_cnt",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      vvip_load_cnt--;

      exec_vip_l_flt = response.flt_cnt;
      exec_vip_l_psr = response.vip_psr_cnt;

      setVipPsrFlt();
    },
    error: function () {},
  });
  //
  var param = {
    companyCodes: comp_code,
    detailType: "pftc", //统计航班（总计）
    psrType: "in", // 中转转入
  };
  vvip_load_cnt++;
  $.ajax({
    type: "post",
    url: "/bi/web/7x2_vip_flt_cnt",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      vvip_load_cnt--;

      total_zz_flt = response.vip_flt_cnt;
      total_zz_psr = response.vip_psr_cnt;

      setVipPsrFlt();
    },
    error: function () {},
  });
  //
  var param = {
    companyCodes: comp_code,
    detailType: "cftc", //已执行航班班次
    psrType: "in", // 中转转入
  };
  vvip_load_cnt++;
  $.ajax({
    type: "post",
    url: "/bi/web/7x2_vip_flt_cnt",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      vvip_load_cnt--;

      exec_zz_flt = response.vip_flt_cnt;
      exec_zz_psr = response.vip_psr_cnt;

      setVipPsrFlt();
    },
    error: function () {},
  });

  function setPlaneLocation(planes) {
    if ($("#mainframe")[0].contentWindow.setPlaneLocation != undefined) {
      $("#mainframe")[0].contentWindow.setPlaneLocation(planes);
    } else {
      setTimeout(setPlaneLocation, 10, planes);
    }
  }

  var backplane_acArpCnt;
  var backplane_acTypes;
  var backplane_curpage = 0;
  var backplane_pagesize = 2;
  var backplane_pagenum;

  function showBackupPlane() {
    //console.log('backplane_acArpCnt', backplane_acArpCnt);
    //console.log('backplane_acTypes', backplane_acTypes);

    if (backplane_acTypes.length == 1) {
      var ac = backplane_acTypes[0];
      var actype;
      if (ac == "320") {
        actype = "319";
      } else {
        actype = "320";
      }
      backplane_acTypes[1] = actype;
      backplane_acArpCnt[actype] = {};
      for (var arp in BASE_CITY_LIST) {
        backplane_acArpCnt[actype][arp] = 0;
      }
    } else if (backplane_acTypes.length == 0) {
      var actype;
      actype = "320";
      backplane_acTypes[0] = actype;
      backplane_acArpCnt[actype] = {};
      for (var arp in BASE_CITY_LIST) {
        backplane_acArpCnt[actype][arp] = 0;
      }
      actype = "319";
      backplane_acTypes[1] = actype;
      backplane_acArpCnt[actype] = {};
      for (var arp in BASE_CITY_LIST) {
        backplane_acArpCnt[actype][arp] = 0;
      }
    }

    var len = backplane_acTypes.length;
    var start = backplane_curpage * backplane_pagesize;
    var end = (backplane_curpage + 1) * backplane_pagesize;
    var k = 0;
    if (len > 0) {
      for (var i = start; i < end; i++) {
        if (i < len) {
          var actype = backplane_acTypes[i];
          var arps = backplane_acArpCnt[actype];

          for (var arp in BASE_CITY_LIST) {
            var num = 0;
            if (arps[arp] > 0) {
              num = arps[arp];
            }
            // $('#backupplane_b' + k + ' .ac').text(actype);
            // $('#backupplane_b' + k + ' .' + arp + ' .num .val').text(num);
          }

          $("#backupplane_b" + k).show();
        } else {
          $("#backupplane_b" + k).hide();
        }

        k++;
      }
    } else {
      $("#backupplane_b0 .ac").text("-");
      $("#backupplane_b0 .num .val").text(0);
      $("#backupplane_b0").show();
    }

    if (backplane_curpage < backplane_pagenum - 1) {
      backplane_curpage++;
    } else {
      backplane_curpage = 0;
    }
    setTimeout(showBackupPlane, 5000);
  }

  function str2Date(timestr) {
    var timearr = timestr.split(" ");
    var timearr0 = timearr[0].split("-");
    var timearr1 = timearr[1].split(":");
    var time = new Date(
      timearr0[0],
      timearr0[1] - 1,
      timearr0[2],
      timearr1[0],
      timearr1[1],
      timearr1[2]
    );

    return time;
  }

  // ------------------------------------------------------------------------
  // 机组：飞行／乘务
  // ------------------------------------------------------------------------
  /*
  AVIATOR_NO 飞行员人数
  STEWARD_NO 乘务人数
  */

  // 今日
  var date = new Date();
  var mm = date.getMonth() + 1;
  var dd = date.getDate();
  var yy = date.getFullYear();
  if (mm < 10) {
    mm = "0" + mm;
  }
  if (dd < 10) {
    dd = "0" + dd;
  }
  var today = "" + yy + mm + dd;

  var param = {
    SOLR_CODE: "FAC_COMP_BFYL_KPI",
    COMP_CODE: comp_code,
    KPI_CODE: "AVIATOR_NO,STEWARD_NO",
    VALUE_TYPE: "kpi_value_d",
    DATE_TYPE: "D",
    DATE_ID: today,
    LIMIT: 1,
    OPTIMIZE: 1,
  };

  $.ajax({
    type: "post",
    url: "/bi/query/getkpi",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      //todo
      // var AVIATOR_NO = 0;
      // var obj = response.data[comp_code]['AVIATOR_NO']['D'];
      // for (var time in obj) {
      //   AVIATOR_NO = obj[time];
      //   break;
      // }
      // var STEWARD_NO = 0;
      // var obj = response.data[comp_code]['STEWARD_NO']['D'];
      // for (var time in obj) {
      //   STEWARD_NO = obj[time];
      //   break;
      // }
      // $('#val_AVIATOR_NO').text(Math.round(Number(AVIATOR_NO)));
      // $('#val_STEWARD_NO').text(Math.round(Number(STEWARD_NO)));
    },
    error: function () {},
  });

  // ------------------------------------------------------------------------
  // 航班列表
  // ------------------------------------------------------------------------

  // VIP 航班列表

  var vip_flt_no_list = [];

  var date = new Date();
  var month = date.getMonth() + 1;
  var day = date.getDate();
  if (month < 10) {
    month = "0" + month;
  }
  if (day < 10) {
    day = "0" + day;
  }
  var today = date.getFullYear() + "-" + month + "-" + day;
  var param = {
    acOwner: comp_code,
    vip: "true",
    datop: today,
  };
  $.ajax({
    type: "post",
    url: "/bi/web/7x2_vip_flt_list",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      //
      checkFocFlightLoaded(response);
    },
    error: function () {},
  });

  function checkFocFlightLoaded(response) {
    if (all_flight_list == undefined) {
      setTimeout(checkFocFlightLoaded, 10, response);
      return;
    }

    for (var i in response.flightNo) {
      var fno = response.flightNo[i];

      if (
        fno.length > 0 &&
        fno.indexOf(comp_code) > -1 &&
        findFltInfo(fno) != undefined &&
        vip_flt_no_list.indexOf(fno) == -1
      ) {
        var obj = findFltInfo(fno);
        // 近2小时进出港航班
        var etdChn = obj.etdChn; //预计起飞时间（北京时间）

        var d_time = new Date(etdChn);
        var now_time = new Date();

        // 2小时内出发的飞机
        var ost = d_time.getTime() - now_time.getTime();
        if (ost <= 120 * 60 * 1000 && ost >= 0) {
          vip_flt_no_list.push(fno);
        }
      }
    }

    createFltList(important_flt_no_list, true);
  }

  // VVIP 航班列表
  var vvip_flt_no_list = [];
  var vvip_flt_backup_list = {}; // {主机航班号:备机航班号}
  var param = {
    mode: "query",
  };
  $.ajax({
    type: "post",
    url: "/bi/web/west_vvip",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      //console.log("7x2_vvip");
      for (var i in response.flt) {
        var flt = response.flt[i];
        var fltno = flt.flt_no;
        var backup_flt_no = flt.backup_flt_no;
        vvip_flt_no_list.push(fltno);
        vvip_flt_backup_list[fltno] = backup_flt_no;
      }
    },
    error: function () {},
  });

  // 预警航班 列表
  var warning_flt_no_list = [];
  var param = {
    mode: "query",
  };
  $.ajax({
    type: "post",
    url: "/bi/web/west_warning",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      //console.log("7x2_warning");
      for (var i in response.flt) {
        var flt = response.flt[i];
        var fltno = flt.flt_no;
        warning_flt_no_list.push(fltno);
      }
      //console.log(warning_flt_no_list);

      if (warning_flt_no_list.length > 3) {
        $("#flt_list_malfunction").html(
          '<marquee direction="left" scrollamount="3">' +
            warning_flt_no_list.join("&nbsp;&nbsp;&nbsp;") +
            "</marquee>"
        );
      } else {
        $("#flt_list_malfunction").html(
          warning_flt_no_list.join("&nbsp;&nbsp;&nbsp;")
        );
      }
    },
    error: function () {},
  });

  // 重点关注 列表
  var important_flt_no_list = [];
  var param = {
    mode: "query",
  };
  $.ajax({
    type: "post",
    url: "/bi/web/west_important",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      //console.log("7x2_important");
      for (var i in response.flt) {
        var flt = response.flt[i];
        var fltno = flt.flt_no;
        important_flt_no_list.push(fltno);
      }
      //console.log(important_flt_no_list);
    },
    error: function () {},
  });

  function createFltList(fltlist, selectFirstItem) {
    var html = "";
    var firstNo;
    for (var i in fltlist) {
      var fltno = fltlist[i];
      if (firstNo == undefined) {
        firstNo = fltno;
      }
      html +=
        '<div class="flt_list_itm fltno_' +
        fltno +
        '" fltno="' +
        fltno +
        '">' +
        fltno +
        "</div>";
    }
    $("#flt_list_holder .wrap").html(html);

    $(".flt_list_itm").on("off", fltItmClick);
    $(".flt_list_itm").on("click", fltItmClick);

    if (firstNo != undefined) {
      if (selectFirstItem) {
        selectFlt(firstNo);
      }
    }
  }

  function showNextFlt() {
    clearTimeout(timeout_next_flt);

    var idx;
    var nextfltno;
    var nexttab;
    var nodeArr = ["tab_important", "tab_warning", "tab_vip", "tab_vvip"];

    // $("#important_flt>.tab_normal").each(function(el,index){
    //   if($(el).hasClass("selected")){
    //     nodeIndex = index;
    //   }
    // })
    //
    if (flt_tab_id == "tab_important" && important_flt_no_list.length != 0) {
      if (
        important_flt_no_list.indexOf(selectedFltno) > -1 &&
        important_flt_no_list.indexOf(selectedFltno) <
          important_flt_no_list.length - 1
      ) {
        idx = important_flt_no_list.indexOf(selectedFltno);
        idx++;
        nexttab = flt_tab_id;
        nextfltno = important_flt_no_list[idx];
      } else if (warning_flt_no_list.length > 0) {
        nexttab = "tab_warning";
        nextfltno = warning_flt_no_list[0];
      } else if (vip_flt_no_list.length > 0) {
        nexttab = "tab_vip";
        nextfltno = vip_flt_no_list[0];
      } else if (vvip_flt_no_list.length > 0) {
        nexttab = "tab_vvip";
        nextfltno = vvip_flt_no_list[0];
      } else {
        nexttab = "tab_important";
        nextfltno = important_flt_no_list[0];
      }
    }

    //
    if (flt_tab_id == "tab_warning" && warning_flt_no_list.length != 0) {
      if (
        warning_flt_no_list.indexOf(selectedFltno) > -1 &&
        warning_flt_no_list.indexOf(selectedFltno) <
          warning_flt_no_list.length - 1
      ) {
        idx = warning_flt_no_list.indexOf(selectedFltno);
        idx++;
        nexttab = flt_tab_id;
        nextfltno = warning_flt_no_list[idx];
      } else if (vip_flt_no_list.length > 0) {
        nexttab = "tab_vip";
        nextfltno = vip_flt_no_list[0];
      } else if (vvip_flt_no_list.length > 0) {
        nexttab = "tab_vvip";
        nextfltno = vvip_flt_no_list[0];
      } else {
        nexttab = "tab_important";
        nextfltno = important_flt_no_list[0];
      }
    }

    if (flt_tab_id == "tab_vip" && vip_flt_no_list.length != 0) {
      if (
        vip_flt_no_list.indexOf(selectedFltno) > -1 &&
        vip_flt_no_list.indexOf(selectedFltno) < vip_flt_no_list.length - 1
      ) {
        idx = vip_flt_no_list.indexOf(selectedFltno);
        idx++;
        nexttab = flt_tab_id;
        nextfltno = vip_flt_no_list[idx];
      } else if (vvip_flt_no_list.length > 0) {
        nexttab = "tab_vvip";
        nextfltno = vvip_flt_no_list[0];
      } else {
        nexttab = "tab_important";
        nextfltno = important_flt_no_list[0];
      }
    }

    //
    if (flt_tab_id == "tab_vvip" && vvip_flt_no_list.length != 0) {
      if (
        vvip_flt_no_list.indexOf(selectedFltno) > -1 &&
        vvip_flt_no_list.indexOf(selectedFltno) < vvip_flt_no_list.length - 1
      ) {
        idx = vvip_flt_no_list.indexOf(selectedFltno);
        idx++;
        nexttab = flt_tab_id;
        nextfltno = vvip_flt_no_list[idx];
      } else if (important_flt_no_list.length > 0) {
        nexttab = "tab_important";
        nextfltno = important_flt_no_list[0];
      }
    }

    console.log("nexttab", nexttab);
    console.log("nextfltno", nextfltno);

    selectFltTab(nexttab, false);
    selectFlt(nextfltno);
  }

  function fltItmClick(evt) {
    selectFlt($(this).attr("fltno"));
  }

  var selectedFltno;

  function selectFlt(fltno) {
    clearTimeout(timeout_next_flt);

    $("#flight_details_loading").show();
    $("#flight_details").hide();
    $("#flight_no_data").hide();
    //
    selectedFltno = fltno;

    $(".flt_list_itm").removeClass("selected");
    $(".fltno_" + fltno).addClass("selected");

    var flt = findFltInfo(fltno);
    setFltDetails(flt);

    if (
      important_flt_no_list.indexOf(fltno) > -1 &&
      flt_tab_id == "tab_important"
    ) {
      $("#flt_list_holder .wrap").animate(
        {
          top: "0px",
        },
        0,
        function () {}
      );
    } else if (
      warning_flt_no_list.indexOf(fltno) > -1 &&
      flt_tab_id == "tab_warning"
    ) {
      $("#flt_list_holder .wrap").animate(
        {
          top: "0px",
        },
        0,
        function () {}
      );
    } else if (vip_flt_no_list.indexOf(fltno) > -1 && flt_tab_id == "tab_vip") {
      var idx = vip_flt_no_list.indexOf(fltno);

      if (idx > 6) {
        $("#flt_list_holder .wrap").animate(
          {
            top: "-" + ($(".flt_list_itm").height() + 8) * (idx - 6) + "px",
          },
          300,
          function () {}
        );
      } else {
        $("#flt_list_holder .wrap").animate(
          {
            top: "0px",
          },
          300,
          function () {}
        );
      }
    } else if (
      vvip_flt_no_list.indexOf(fltno) > -1 &&
      flt_tab_id == "tab_vvip"
    ) {
      $("#flt_list_holder .wrap").animate(
        {
          top: "0px",
        },
        0,
        function () {}
      );
    }
  }

  function setFltDetails(fltdat) {
    if (fltdat == undefined) {
      $("#flight_no_data").show();
      $("#flight_details_loading").hide();

      clearTimeout(timeout_next_flt);
      timeout_next_flt = setTimeout(showNextFlt, 15000);

      return;
    }

    if (arp_detail_list == undefined || all_flight_list == undefined) {
      setTimeout(setFltDetails, 10, fltdat);
      return;
    }

    clearTimeout(timeout_next_flt);
    timeout_next_flt = setTimeout(showNextFlt, 15000);

    var fltno = fltdat.flightNo;
    var acno = fltdat.acLongNo;

    var arp1 = arp_detail_list[fltdat.depStn];
    var arp2 = arp_detail_list[fltdat.arrStn];

    if (arp1) {
      $("#detail_depCity").text(arp1.city_name);
    } else {
      $("#detail_depCity").text(fltdat.depStn);
    }
    if (arp2) {
      $("#detail_arrCity").text(arp2.city_name);
    } else {
      $("#detail_arrCity").text(fltdat.arrStn);
    }

    $("#detail_stdChn").text(fltdat.stdChn.split(" ")[1].substr(0, 5));
    $("#detail_staChn").text(fltdat.staChn.split(" ")[1].substr(0, 5));

    var statusMap = {
      ARR: "落地",
      NDR: "落地",
      ATD: "推出",
      ATA: "到达",
      CNL: "取消",
      DEL: "延误",
      DEP: "起飞",
      RTR: "返航",
      SCH: "计划",
    };
    var status = fltdat.status;
    $("#detail_status").text(statusMap[status]);
    $("#detail_flightNo").text(fltno);
    $("#detail_iataAcType").text("机型:" + fltdat.acType);

    var tOffChn = fltdat.tOffChn;
    var tDwnChn = fltdat.tDwnChn;

    var dt = new Date();
    var doff = new Date(tOffChn); // 实际离地北京时间
    var ddown = new Date(tDwnChn); // 实际落地北京时间
    // 并且不是计划中的航班
    if (dt > doff && dt < ddown && status != "SCH") {
      $("#detail_is_in_sky").text("空中");
    } else {
      $("#detail_is_in_sky").text("地面");
    }

    if (vvip_flt_backup_list[fltno] != undefined) {
      $("#detail_backup").html(
        '<span class="blue2">备机</span>' + vvip_flt_backup_list[fltno]
      );
    } else {
      $("#detail_backup").html("");
    }

    // 前序航班预计过站时间，算法为本段航班计划离港时间-前序航班预计到达时间
    $("#detail_prev_stdChn").text("-");
    $("#detail_prev_passTime").text("-");

    // 查找前序航班
    var prev_flt;
    for (var i = all_flight_list.length - 1; i >= 0; i--) {
      var flt = all_flight_list[i];
      if (
        flt.acLongNo == acno &&
        flt.arrStn == fltdat.depStn &&
        flt.stdChn < fltdat.stdChn
      ) {
        prev_flt = flt;
        break;
      }
    }
    if (prev_flt && status != "CNL") {
      // 前序航班 起飞时间
      var prevd;
      if (
        prev_flt.status == "DEP" ||
        prev_flt.status == "ARR" ||
        prev_flt.status == "NDR" ||
        prev_flt.status == "ATA"
      ) {
        prevd = prev_flt.atdChn.split(" ")[1].substr(0, 5); // 实际起飞时间 atdChn
      } else {
        prevd = prev_flt.etdChn.split(" ")[1].substr(0, 5); // 预计起飞时间 etdChn
      }
      $("#detail_prev_stdChn").text(prevd);

      // 前序航班 过站时间
      var detaChn; // 前序航班 到达时间
      var detdChn; // 本段航班 离港时间
      if (
        prev_flt.status == "ARR" ||
        prev_flt.status == "NDR" ||
        prev_flt.status == "ATA" ||
        prev_flt.status == "ATA"
      ) {
        detaChn = prev_flt.ataChn; // 实际到达时间 ataChn
      } else {
        detaChn = prev_flt.etaChn; // 预计到达时间 etaChn
      }
      if (
        status == "ARR" ||
        status == "NDR" ||
        status == "ATA" ||
        status == "DEP" ||
        status == "RTR"
      ) {
        detdChn = fltdat.atdChn; // 实际起飞时间 atdChn
      } else {
        detdChn = fltdat.etdChn; // 预计起飞时间 etdChn
      }
      var a_time = new Date(detaChn); // 前序航班
      var d_time = new Date(detdChn); // 本段航班
      var sec = d_time.getTime() - a_time.getTime();
      var totalmin = sec / 60000;
      var hour = Math.floor(totalmin / 60);
      var min = totalmin % 60;
      $("#detail_prev_passTime").text(hour + "小时 " + min + "分钟");
    }

    // ------------------------------------------------------------------------
    // 获取 机组信息
    // ------------------------------------------------------------------------
    $("#detail_crwPilotInf").text("");
    $("#detail_crwStatus").text("");

    if (fltCrwCache[fltno]) {
      setCrw(fltCrwCache[fltno]);
    } else {
      var param = {
        flightNo: fltno,
      };

      $.ajax({
        type: "post",
        url: "/bi/web/findFlightReportV2",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          if (response.data && response.data[0]) {
            var data = response.data[0];

            //
            fltCrwCache[fltno] = data;

            if (selectedFltno == fltno) {
              setCrw(fltCrwCache[fltno]);
            }
          }
        },
        error: function () {},
      });
    }

    function setCrw(crew) {
      // 机组信息
      if (crew) {
        var names = [];

        var str = "";
        names = names.concat(crew.captain.split("@"));
        names = names.concat(crew.firstVice1.split("@"));

        for (var i = names.length - 1; i >= 0; i--) {
          if (names[i].indexOf("(") > -1) {
            var aaa = names[i].split("(");
            if (aaa.length > 1) {
              names[i] = aaa[0];
            }
          }
        }
        console.log("captain:", names);
        $("#detail_crwPilotInf").text(names.join(","));
        $("#detail_crwStatus").text("");
      }
    }

    // ------------------------------------------------------------------------
    // 获取 航班保障信息
    // ------------------------------------------------------------------------

    /*
    cncPilotArrTime,//机组到达|飞行
    cncStewardArrTime,//机组到达|乘务
    //航班截载 无法获得 checkInEnd
    cncCabinSupplyEndTime,//客舱供应|结束，机供品配备
    cncCleanEndTime,//客舱清洁
    cncMCCReleaseTime,//机务放行
    //飞机准备好 无法获得 planeReady
    cncInformBoardTime,//通知登机
    cncBoardOverTime,//登机结束
    cncClosePaxCabinTime,//客舱关闭
    cncCloseCargoCabinTime,//货舱关闭
    cncPushTime,//飞机推出
    cncACARSTOFF,//飞机起飞
    */

    for (var i in fltnodes) {
      var node = fltnodes[i];
      if (!$("#fltnode_" + node).hasClass("blue3")) {
        $("#fltnode_" + node).addClass("blue3");
      }
      $("#fltnode_" + node).removeClass("green");
      $("#fltnode_" + node).removeClass("yellow");
      $("#fltnode_" + node).removeClass("red");
    }

    if (
      status == "ARR" ||
      status == "NDR" ||
      status == "ATA" ||
      status == "DEP" ||
      status == "RTR"
    ) {
      lightThemUp("cncACARSTOFF");
    } else if (status == "ATD") {
      lightThemUp("cncPushTime");
    }

    // 只有这几个状态才去获取航班保障信息，其它状态没必要
    if (status == "SCH" || status == "DEL") {
      if (fltLegCache[fltno]) {
        setLeg(fltLegCache[fltno]);
      } else {
        var param = {
          flightNo: fltno,
        };
        $.ajax({
          type: "post",
          url: "/bi/web/getFltmLegsByPage",
          contentType: "application/json",
          dataType: "json",
          async: true,
          data: JSON.stringify(param),
          success: function (response) {
            fltLegCache[fltno] = response;

            if (selectedFltno == fltno) {
              setLeg(fltLegCache[fltno]);
            }
          },
          error: function () {},
        });
      }
    }

    function setLeg(response) {
      var mlegs = response.getFltmLegsByPage;
      if (response && mlegs) {
        if (mlegs.cncPilotArrTime && mlegs.cncPilotArrTime.length > 0) {
          lightThemUp("cncPilotArrTime");
        }
        if (
          mlegs.cncCabinSupplyEndTime &&
          mlegs.cncCabinSupplyEndTime.length > 0
        ) {
          lightThemUp("cncCabinSupplyEndTime");
        }
        if (mlegs.cncCleanEndTime && mlegs.cncCleanEndTime.length > 0) {
          lightThemUp("cncCleanEndTime");
        }
        if (mlegs.cncMCCReleaseTime && mlegs.cncMCCReleaseTime.length > 0) {
          lightThemUp("cncMCCReleaseTime");
        }
        if (mlegs.cncInformBoardTime && mlegs.cncInformBoardTime.length > 0) {
          lightThemUp("cncInformBoardTime");
        }
        if (mlegs.cncBoardOverTime && mlegs.cncBoardOverTime.length > 0) {
          lightThemUp("cncBoardOverTime");
        }
        if (
          mlegs.cncClosePaxCabinTime &&
          mlegs.cncClosePaxCabinTime.length > 0
        ) {
          lightThemUp("cncClosePaxCabinTime");
        }
        if (
          mlegs.cncCloseCargoCabinTime &&
          mlegs.cncCloseCargoCabinTime.length > 0
        ) {
          lightThemUp("cncCloseCargoCabinTime");
        }
        if (mlegs.cncPushTime && mlegs.cncPushTime.length > 0) {
          lightThemUp("cncPushTime");
        }
        if (mlegs.cncACARSTOFF && mlegs.cncACARSTOFF.length > 0) {
          lightThemUp("cncACARSTOFF");
        }
      }
    }

    $("#flight_details_loading").hide();
    $("#flight_details").show();
  }

  $(".tab_normal").on("click", function () {
    selectFltTab($(this).attr("id"), true);
  });

  var flt_tab_id = "tab_important";

  function selectFltTab(id, selectFirstItem) {
    if (id == "tab_aog") {
      return;
    }

    $(".tab_normal").removeClass("tab_selected");
    $("#" + id).addClass("tab_selected");

    flt_tab_id = id;
    console.log("hahahha");
    console.log(important_flt_no_list);
    console.log(warning_flt_no_list);
    console.log(vip_flt_no_list);
    console.log(vvip_flt_no_list);
    if (id == "tab_important") {
      createFltList(important_flt_no_list, selectFirstItem);
    } else if (id == "tab_warning") {
      createFltList(warning_flt_no_list, selectFirstItem);
    } else if (id == "tab_vip") {
      createFltList(vip_flt_no_list, selectFirstItem);
    } else if (id == "tab_vvip") {
      createFltList(vvip_flt_no_list, selectFirstItem);
    }
  }
} // end loadAll

function findFltInfo(fltno) {
  for (var i = all_flight_list.length - 1; i >= 0; i--) {
    var flt = all_flight_list[i];
    if (flt.flightNo == fltno) {
      return flt;
    }
  }
  return undefined;
}

// 点亮航班保障节点
function lightThemUp(node) {
  for (var i = fltnodes.indexOf(node); i >= 0; i--) {
    var nd = fltnodes[i];
    $("#fltnode_" + nd).addClass("green");
  }
}

// 获取机场列表
var arp_detail_list;

function getAirportList() {
  var param = {
    //"AIRPORT_CODE": arpcodes, // 可选，传入机场CODE
  };
  $.ajax({
    type: "post",
    url: "/bi/web/airportdetail",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      airportList = {};
      var list = response.airport;
      for (var i = list.length - 1; i >= 0; i--) {
        var arp = list[i];
        airportList[arp.code] = arp;
      }
      arp_detail_list = airportList;
    },
    error: function () {},
  });
}
getAirportList();

// ------------------------------------------------------------------------
// VIP旅客/航班
// ------------------------------------------------------------------------

function setVipTrvChartChina(id, rate, color) {
  var canvas = document.getElementById(id);
  var context = canvas.getContext("2d");
  context.clearRect(0, 0, canvas.width, canvas.height);
  var x = canvas.width / 2;
  var y = canvas.height / 2;

  var radius = 40;

  // draw back
  context.beginPath();
  context.arc(x, y, radius, 0, Math.PI * 2, true);
  context.lineWidth = 7;
  context.strokeStyle = "#2B4596";
  context.stroke();

  // top
  var startAngle = -Math.PI / 2;
  var endAngle = startAngle + Math.PI * 2 * rate;
  var counterClockwise = false;

  context.beginPath();
  context.arc(x, y, radius, startAngle, endAngle, counterClockwise);
  context.lineWidth = 7;
  context.strokeStyle = color;
  context.stroke();

  // draw lines
  var numslice = 10;
  for (var i = 0; i < numslice; i++) {
    context.beginPath();
    var startAngle =
      Math.PI / 2 + i * ((Math.PI * 2) / numslice) - Math.PI * 0.006;
    var endAngle = startAngle + Math.PI * 0.012;
    context.arc(x, y, radius, startAngle, endAngle, false);
    context.lineWidth = 7;
    context.strokeStyle = "#091030";
    context.stroke();
  }
}

// 自动循环切换两个TAB
var itv_autoSwitchTab;
var time_autoSwitchTab = 5 * 60 * 1000; //5分钟切换
var currentTabIndex = 0;

function autoSwitchTab() {
  clearTimeout(itv_autoSwitchTab);

  if (currentTabIndex == 0) {
    $("#tab_normal_rate_year").click();
  } else {
    $("#tab_normal_rate_month").click();
  }

  itv_autoSwitchTab = setTimeout(autoSwitchTab, time_autoSwitchTab);
}

clearTimeout(itv_autoSwitchTab);
itv_autoSwitchTab = setTimeout(autoSwitchTab, time_autoSwitchTab);

// 右下角循环切换内容

var currentVipTabIndex = 0;
$("#tab_vip_psr").on("click", function (evt) {
  currentVipTabIndex = 0;

  $("#tab_vip_psr").addClass("selected");
  $("#tab_vip_flt").removeClass("selected");
  $("#tabc_vip_psr").show();
  $("#tabc_vip_flt").hide();

  clearTimeout(itv_autoSwitchVipTab);
});

$("#tab_vip_flt").on("click", function (evt) {
  currentVipTabIndex = 1;

  $("#tab_vip_psr").removeClass("selected");
  $("#tab_vip_flt").addClass("selected");
  $("#tabc_vip_psr").hide();
  $("#tabc_vip_flt").show();

  clearTimeout(itv_autoSwitchVipTab);
});

var itv_autoSwitchBRTab;
var itv_autoSwitchVipTab;
var time_autoSwitchBRTab = 20 * 1000; //20秒切换
var time_autoSwitchVipTab = time_autoSwitchBRTab / 2;
var currentBRTabIndex = 0;

function autoSwitchBRTab() {
  clearTimeout(itv_autoSwitchBRTab);

  if (currentBRTabIndex == 0) {
    $("#block_right_bottom .block1").show();
    $("#block_right_bottom .block2").hide();
    currentBRTabIndex = 1;
  } else {
    $("#block_right_bottom .block1").hide();
    $("#block_right_bottom .block2").show();
    currentBRTabIndex = 0;

    itv_autoSwitchVipTab = setTimeout(autoSwitchVipTab, time_autoSwitchVipTab);
  }

  itv_autoSwitchBRTab = setTimeout(autoSwitchBRTab, time_autoSwitchBRTab);
}

function autoSwitchVipTab() {
  clearTimeout(itv_autoSwitchVipTab);

  if (currentVipTabIndex == 1) {
    $("#tab_vip_psr").click();
  } else {
    $("#tab_vip_flt").click();
  }
}

clearTimeout(itv_autoSwitchBRTab);
itv_autoSwitchBRTab = setTimeout(autoSwitchBRTab, time_autoSwitchBRTab);

// 加载数据
loadAll();
setInterval(loadAll, 5 * 60 * 1000); // 5分钟刷新一次

// ------------------------------------------------------------------------
// 时钟
// ------------------------------------------------------------------------
function setTime() {
  var date = new Date();
  var time = formatNum(date.getHours()) + ":" + formatNum(date.getMinutes());
  $("#beijingtime").text(
    "北京时间：" +
      date.getFullYear() +
      "年" +
      (date.getMonth() + 1) +
      "月" +
      date.getDate() +
      "日" +
      " " +
      time
  );
}

function formatNum(n) {
  if (n < 10) {
    return "0" + n;
  } else {
    return n;
  }
}

setInterval(setTime, 1000);
setTime();

function updateAlleCharts() {
  setNormalRateTrendMonth();
  setNormalRateTrendYear();
}

if (getQueryString("scale") == 1) {
  $("#link_2comp").attr("href", encodeURI("company.html?scale=1"));
} else if (getQueryString("scale") == "auto") {
  $("#link_2comp").attr("href", encodeURI("company.html?scale=auto"));
} else {
  $("#link_2comp").attr("href", encodeURI("company.html"));
}

function setPlaneLocation() {
  if (
    flightInfoList == undefined ||
    planeLocationList == undefined ||
    $("#page_leve2").is(":visible")
  ) {
    return;
  }

  if ($("#mainframe")[0].contentWindow.setPlaneLocation != undefined) {
    $("#mainframe")[0].contentWindow.setPlaneLocation();
  } else {
    setTimeout(setPlaneLocation, 10);
  }
}

function queryFlight() {
  // 开始结束时间
  var date = new Date();
  var mm = date.getMonth() + 1;
  var dd = date.getDate();
  if (mm < 10) {
    mm = "0" + mm;
  }
  if (dd < 10) {
    dd = "0" + dd;
  }
  var stdStart = date.getFullYear() + "-" + mm + "-" + dd + " 00:00:00";
  var stdEnd = date.getFullYear() + "-" + mm + "-" + dd + " 23:59:59";
  var param = {
    stdStart: stdStart,
    stdEnd: stdEnd,
    acOwner: current_company_code,
    statusList: "DEP", // 只返回起飞的，表示在空中
  };
  var dtd1 = $.ajax({
    type: "post",
    url: "/bi/web/getStandardFocFlightInfo?today",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var list = response.data;
      flightInfoList = {};
      for (var i = list.length - 1; i >= 0; i--) {
        var obj = list[i];
        flightInfoList[obj.flightNo] = obj;
      }
      setPlaneLocation();
    },
    error: function () {},
  });
}

queryFlight();

// 获取飞机实时位置
function getPlaneLocationMq() {
  var param = {
    mode: "pos",
  };
  return $.ajax({
    type: "post",
    url: "/bi/web/flightMq",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      //checkLogin(response);

      planeLocationList = [];
      var plist = {};

      //var list = response.data.data1;
      function processData(data1) {
        var lst = {};
        var len = data1.length;
        for (var i = 0; i < len; i++) {
          var dd = data1[i];
          var fi = dd.fi;
          if (lst[fi] == undefined) {
            lst[fi] = {
              data: [],
            };
            lst[fi]["data"].push(dd);
          } else {
            lst[fi]["data"].push(dd);
          }
        }

        return lst;
      }

      var list = processData(response.data.data1);

      console.log("processData", list);

      for (var fltno in list) {
        var fltobj = list[fltno];
        var itmx2 = fltobj.data;

        var itm;

        if (itmx2 && itmx2.length > 1) {
          var itm1 = itmx2[0];
          var itm2 = itmx2[1];

          itm1.UTC = itm1.UTC.replace(" ", "");
          itm2.UTC = itm2.UTC.replace(" ", "");

          if (itm1.UTC > itm2.UTC) {
            itm = itm1;
            itm.LON1 = itm2.LON;
            itm.LAT1 = itm2.LAT;
          } else if (itm1.UTC < itm2.UTC) {
            itm = itm2;
            itm.LON1 = itm1.LON;
            itm.LAT1 = itm1.LAT;
          } else {
            itm = itm2;
            itm.LON1 = itm1.LON;
            itm.LAT1 = itm1.LAT;

            //console.log(fltno, '两组经纬度UTC相同');
          }
        } else if (itmx2 && itmx2.length > 0) {
          itm = itmx2[0];
        }

        if (itm) {
          var alt = itm.ALT;
          var cas = itm.CAS;
          let vec;

          var fltno = itm.fi;

          if (fltno.indexOf(current_company_code) == 0) {
            var acno = itm.an;
            acno = acno.replace("-", "");

            var lon = formatLonLat(itm.LON);
            var lon1 = formatLonLat(itm.LON1);
            var lat = formatLonLat(itm.LAT);
            var lat1 = formatLonLat(itm.LAT1);

            if (isNaN(itm.LON)) {
              vec = Number(itm.VEC);
            }

            var oil = isNaN(itm.OIL) ? "" : itm.OIL;

            var pdat = {
              fltno: fltno,
              acno: acno,
              alt: alt,
              vec: vec,
              lon: lon,
              lat: lat,
              lon1: lon1,
              lat1: lat1,
              oil: oil,
            };

            var code = acno + "-" + fltno;

            if (pdat.vec == undefined) {
              pdat.vec = getGeoAngle(pdat.lat, pdat.lon, pdat.lat1, pdat.lon1);
            }
            planeLocationList.push(pdat);
          }
        }
      }
      console.log("planeLocationList", planeLocationList);
      setPlaneLocation();
    },
  });
}

getPlaneLocationMq();
getAirportList();
