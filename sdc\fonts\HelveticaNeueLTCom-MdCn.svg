<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg>
<metadata>
Created by FontForge 20090622 at Tue Dec  6 06:37:17 2016
 By deploy user
Part of the digitally encoded machine readable outline data for producing the Typefaces provided is copyrighted &#194;&#169; 2003 - 2006 Linotype GmbH, www.linotype.com. All rights reserved. This software is the property of Linotype GmbH, and may not be reproduced, modified, disclosed or transferred without the express written approval of Linotype GmbH. Copyright &#194;&#169; 1988, 1990, 1993 Adobe Systems Incorporated. All Rights Reserved. Helvetica is a trademark of Heidelberger Druckmaschinen AG, exclusively licensed through Linotype GmbH, and may be registered in certain jurisdictions. This typeface is original artwork of Linotype Design Studio. The design may be protected in certain jurisdictions.
</metadata>
<defs>
<font id="HelveticaNeueLTCom-MdCn" horiz-adv-x="222" >
  <font-face 
    font-family="HelveticaNeueLT Com 67 MdCn"
    font-weight="500"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 11 6 6 3 5 2 3 2 4"
    ascent="714"
    descent="-286"
    x-height="538"
    cap-height="714"
    bbox="-164 -223 1031 950"
    underline-thickness="50"
    underline-position="-50"
    unicode-range="U+0020-U+FB02"
  />
<missing-glyph horiz-adv-x="512" 
d="M222 117v67h56v-67h-56zM225 234l-2 17q0 56 13 81q7 13 18 27.5t28 31.5q16 15 26 26.5t15 21t7 18t2 17.5q0 36 -22.5 60.5t-53.5 24.5t-53.5 -22t-28.5 -73l-50 8q7 72 44 104.5t91 32.5t89 -35.5t35 -95.5q0 -32 -12.5 -58.5t-50.5 -61.5q-37 -33 -43 -52
q-6 -20 -6 -72h-46zM462 50v617h-412v-617h412zM512 718v-718h-512v718h512z" />
    <glyph glyph-name="IJ" unicode="IJ" horiz-adv-x="671" 
d="M54 714h114v-714h-114v714zM351 215v-35q0 -22 3 -41.5t11 -34.5t23 -23.5t39 -8.5q28 0 43.5 12t23 32t8.5 45.5t1 52.5v500h114v-517q0 -59 -14 -99.5t-39.5 -65.5t-60.5 -35.5t-78 -10.5q-55 0 -90 15.5t-55 41t-28 58.5t-9 68v46h108z" />
    <glyph glyph-name="ij" unicode="ij" horiz-adv-x="446" 
d="M57 722h108v-108h-108v108zM57 538h108v-538h-108v538zM389 722v-108h-108v108h108zM389 538v-573q0 -84 -35.5 -116.5t-106.5 -32.5q-23 0 -47 3v83h25q31 0 43.5 15t12.5 58v563h108z" />
    <glyph glyph-name="napostrophe" unicode="&#x22;n" horiz-adv-x="501" 
d="M72 538h102v-63h3q20 36 56.5 56.5t81.5 20.5q31 0 55.5 -7.5t42 -24.5t27 -45t9.5 -69v-406h-108v369q0 54 -16.5 75.5t-58.5 21.5q-33 0 -59.5 -25.5t-26.5 -79.5v-361h-108v538zM28 813h96v-86q0 -45 -23 -63.5t-63 -23.5v33q15 4 24 12.5t11 28.5h-45v99z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="500" 
d="M85 458h-75v80h75v50q0 76 35.5 105t101.5 29q35 0 59 -3v-83h-30q-30 0 -44 -12t-14 -39v-47h88v-80h-88v-458h-108v458zM335 722h108v-108h-108v108zM335 538h108v-538h-108v538z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="500" 
d="M85 458h-75v80h75v50q0 76 35.5 105t101.5 29q35 0 59 -3v-83h-30q-30 0 -44 -12t-14 -39v-47h88v-80h-88v-458h-108v458zM335 714h108v-714h-108v714z" />
    <glyph glyph-name=".notdef" horiz-adv-x="512" 
d="M222 117v67h56v-67h-56zM225 234l-2 17q0 56 13 81q7 13 18 27.5t28 31.5q16 15 26 26.5t15 21t7 18t2 17.5q0 36 -22.5 60.5t-53.5 24.5t-53.5 -22t-28.5 -73l-50 8q7 72 44 104.5t91 32.5t89 -35.5t35 -95.5q0 -32 -12.5 -58.5t-50.5 -61.5q-37 -33 -43 -52
q-6 -20 -6 -72h-46zM462 50v617h-412v-617h412zM512 718v-718h-512v718h512z" />
    <glyph glyph-name=".null" horiz-adv-x="500" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="0" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="240" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="278" 
d="M82 114h114v-114h-114v114zM82 488v226h114v-226l-17 -296h-80z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="426" 
d="M76 714h86v-286h-86v286zM264 714h86v-286h-86v286z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="480" 
d="M-3 274h97l20 146h-88v74h98l28 200h83l-28 -200h106l28 200h83l-28 -200h87v-74h-97l-20 -146h88v-74h-98l-28 -200h-83l28 200h-106l-28 -200h-83l28 200h-87v74zM283 274l20 146h-106l-20 -146h106z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="480" 
d="M216 -14q-64 3 -103 23.5t-60.5 50.5t-29 66.5t-7.5 71.5v16h114v-16q0 -57 19 -87.5t67 -38.5v232l-41 15q-79 28 -114 72.5t-35 120.5q0 43 14.5 79t40 62t60 40.5t75.5 14.5v70h44v-70q57 0 94 -19t58 -46.5t29.5 -59.5t8.5 -59v-14h-114v14q0 40 -16 66.5t-60 31.5
v-221l63 -22q73 -26 107 -69t34 -115q0 -104 -55.5 -153.5t-148.5 -55.5v-86h-44v86zM216 622q-32 -3 -54 -27.5t-22 -72.5q0 -35 17 -60.5t59 -43.5v204zM260 72q45 7 67.5 36.5t22.5 63.5q0 50 -22 75.5t-68 39.5v-215z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="759" 
d="M586 370q30 0 55 -9t43 -31t28.5 -59t10.5 -93t-10.5 -93t-28.5 -59t-43 -31t-55 -9t-55 9t-43 31t-28.5 59t-10.5 93t10.5 93t28.5 59t43 31t55 9zM173 708q30 0 55 -9t43 -31t28.5 -59t10.5 -93t-10.5 -93t-28.5 -59t-43 -31t-55 -9t-55 9t-43 31t-28.5 59t-10.5 93
t10.5 93t28.5 59t43 31t55 9zM586 308q-31 0 -41 -30t-10 -100t10 -100t41 -30t41 30t10 100t-10 100t-41 30zM574 708l-319 -722h-75l317 722h77zM173 646q-31 0 -41 -30t-10 -100t10 -100t41 -30t41 30t10 100t-10 100t-41 30z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="574" 
d="M195 318q-31 -25 -48.5 -56t-17.5 -71q0 -24 9.5 -45t26 -36t38.5 -24t47 -9q36 0 63.5 16.5t47.5 38.5l-145 204zM272 481q19 18 35.5 40.5t16.5 51.5q0 32 -18 49.5t-50 17.5q-11 0 -23 -3t-21.5 -10.5t-16 -19t-6.5 -29.5q0 -27 13.5 -53t28.5 -46l16 -21zM421 216
q13 21 20.5 50.5t7.5 58.5v14h90v-9q0 -15 -3 -37.5t-9.5 -48.5t-18.5 -54t-30 -54l97 -136h-121l-43 61q-35 -32 -77 -53.5t-103 -21.5q-62 0 -102.5 20t-64.5 50t-33.5 64t-9.5 63q0 60 27 106t73 81l45 34l-27 40q-19 29 -32.5 58.5t-13.5 58.5q0 38 13 67t36 48t54 28.5
t67 9.5q20 0 47 -6t51.5 -22t41.5 -44t17 -73q0 -51 -24.5 -86t-62.5 -64l-37 -27z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="240" 
d="M77 714h86v-286h-86v286z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="259" 
d="M259 714q-60 -103 -87 -211t-27 -238q0 -56 6.5 -115.5t20.5 -118t35.5 -113.5t51.5 -102h-77q-30 39 -56.5 88.5t-46 106.5t-31 121t-11.5 133t11.5 133t32 121t47 106.5t56.5 88.5h75z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="259" 
d="M0 -184q60 103 87 211t27 238q0 56 -6.5 115.5t-20.5 118t-35.5 113.5t-51.5 102h77q30 -39 56.5 -88.5t46 -106.5t31 -121t11.5 -133t-11.5 -133t-32 -121t-47 -106.5t-56.5 -88.5h-75z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="370" 
d="M220 714v-124l111 43l23 -66l-114 -33l78 -103l-56 -43l-76 105l-74 -105l-56 43l76 103l-116 33l23 66l113 -44v125h68z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="600" 
d="M257 506h86v-210h210v-86h-210v-210h-86v210h-210v86h210v210z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="240" 
d="M63 132h114v-132q0 -60 -24 -100.5t-90 -53.5v58q23 6 36.5 27t13.5 54v15h-50v132z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="352" 
d="M45 340h262v-102h-262v102z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="240" 
d="M63 132h114v-132h-114v132z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="296" 
d="M217 728h89l-227 -742h-89z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="480" 
d="M28 347q0 103 14 172.5t41 111.5t66.5 59.5t90.5 17.5t90.5 -17.5t66.5 -59.5t41 -111.5t14 -172.5q0 -104 -14 -173t-41 -111t-66.5 -59.5t-90.5 -17.5t-90.5 17.5t-66.5 59.5t-41 111t-14 173zM142 347q0 -77 4.5 -130.5t15.5 -87t30 -48.5t48 -15t48 15t30 48.5
t15.5 87t4.5 130.5t-4.5 130.5t-15.5 87t-30 48.5t-48 15t-48 -15t-30 -48.5t-15.5 -87t-4.5 -130.5z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="480" 
d="M207 0v514h-147v74h11q25 0 51.5 4t49.5 15.5t40.5 32.5t25.5 54v14h83v-708h-114z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="480" 
d="M447 0h-414q0 77 22 130.5t55 93t71.5 70.5t71.5 63t55 70.5t22 93.5q0 22 -5 41.5t-15.5 34t-27 23t-40.5 8.5q-32 0 -51.5 -13t-30 -34t-14 -47.5t-3.5 -53.5h-108q0 52 11.5 94.5t36.5 72t64.5 45.5t96.5 16q54 0 92 -15t62 -41t35 -60t11 -73q0 -59 -17.5 -101
t-44.5 -74.5t-60 -59l-64.5 -54t-58 -59.5t-39.5 -75h287v-96z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="480" 
d="M139 197q0 -23 4.5 -46.5t15.5 -42.5t29.5 -30.5t47.5 -11.5q27 0 45.5 10t30.5 27t17.5 40.5t5.5 49.5q0 66 -25 99t-90 33h-44v80h41q57 0 79.5 34.5t22.5 86.5q0 19 -4.5 37.5t-14.5 32.5t-25.5 23t-37.5 9q-26 0 -43.5 -10.5t-28 -27.5t-14.5 -39.5t-4 -46.5h-108
q0 100 50.5 152t153.5 52q32 0 65.5 -8.5t61.5 -29t45.5 -55.5t17.5 -88q0 -27 -8 -53.5t-22.5 -47.5t-35.5 -35.5t-47 -18.5v-3q55 -5 92 -47t37 -117q0 -106 -52 -162.5t-164 -56.5q-96 0 -149 50t-53 161h108z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="480" 
d="M274 542h-2l-159 -292h161v292zM21 257l245 451h116v-458h77v-91h-77v-159h-108v159h-253v98z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="480" 
d="M32 191h108v-19q0 -40 22.5 -70t70.5 -30q30 0 49.5 13.5t31 36t16 51.5t4.5 60t-4.5 58.5t-15.5 48t-29.5 32.5t-45.5 12q-36 0 -60.5 -18t-32.5 -47l-101 4l35 371h341v-91h-256l-23 -186l2 -2q23 26 53.5 40.5t65.5 14.5q55 0 91 -21t57 -55t29 -75.5t8 -82.5
q0 -53 -12.5 -99t-39 -79.5t-68 -52.5t-98.5 -19q-97 0 -147.5 48t-50.5 142v15z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="480" 
d="M149 213q0 -31 6 -57.5t18 -46.5t30.5 -31.5t43.5 -11.5q26 0 44 12t29 33t16 48.5t5 58.5q0 75 -22.5 114t-71.5 39q-32 0 -51 -12.5t-29.5 -34.5t-14 -50.5t-3.5 -60.5zM328 536q0 15 -4 31.5t-13 30t-23.5 22t-35.5 8.5q-40 0 -62.5 -23.5t-34 -59.5t-14 -79t-2.5 -82
l3 -2q40 75 138 75q36 0 68 -13t56 -39.5t37.5 -66t13.5 -92.5q0 -124 -52.5 -192t-164.5 -68q-67 0 -108.5 24.5t-65 69t-31.5 107t-8 138.5q0 79 9 149t34 122t69 82t114 30q94 0 142.5 -42t48.5 -130h-114z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="480" 
d="M30 694h420v-90q-42 -54 -80 -125.5t-67.5 -151t-49.5 -164t-26 -163.5h-120q6 79 27.5 161t53 160t71 147.5t81.5 123.5h-310v102z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="480" 
d="M240 -14q-109 0 -161.5 53t-52.5 159q0 79 33.5 122.5t87.5 54.5v2q-46 16 -72 52.5t-26 100.5q0 85 50 131.5t141 46.5t141 -46.5t50 -131.5q0 -64 -26 -100.5t-72 -52.5v-2q54 -11 87.5 -54.5t33.5 -122.5q0 -106 -52.5 -159t-161.5 -53zM140 200q0 -28 4.5 -52.5
t15.5 -42.5t30.5 -28.5t49.5 -10.5t49.5 10.5t30.5 28.5t15.5 42.5t4.5 52.5t-4.5 52.5t-15.5 42.5t-30.5 28.5t-49.5 10.5t-49.5 -10.5t-30.5 -28.5t-15.5 -42.5t-4.5 -52.5zM151 522q0 -21 4 -40.5t14 -34.5t27 -24t44 -9t44 9t27 24t14 34.5t4 40.5t-4 40t-14 34
t-27 23.5t-44 8.5t-44 -8.5t-27 -23.5t-14 -34t-4 -40z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="480" 
d="M331 481q0 31 -6 57.5t-18 46.5t-30.5 31.5t-43.5 11.5q-26 0 -44 -12t-29 -33t-16 -48.5t-5 -58.5q0 -75 22.5 -114t71.5 -39q31 0 50.5 12.5t30 34.5t14 50.5t3.5 60.5zM152 158q0 -15 4 -31.5t13 -30t23.5 -22t35.5 -8.5q40 0 62.5 23.5t34 59.5t14 79t2.5 82l-3 2
q-41 -75 -138 -75q-36 0 -68 13t-56 39.5t-37.5 66t-13.5 92.5q0 122 52 191t165 69q67 0 108.5 -24.5t65 -69t31.5 -107t8 -138.5q0 -80 -9 -149.5t-34 -121.5t-69 -82t-114 -30q-94 0 -142.5 42t-48.5 130h114z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="240" 
d="M63 132h114v-132h-114v132zM63 512h114v-132h-114v132z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="240" 
d="M63 512h114v-132h-114v132zM63 132h114v-132q0 -60 -24 -100.5t-90 -53.5v58q23 6 36.5 27t13.5 54v15h-50v132z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="600" 
d="M47 290l506 226v-86l-404 -177l404 -177v-86l-506 226v74z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="600" 
d="M47 192h506v-86h-506v86zM47 400h506v-86h-506v86z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="600" 
d="M553 216l-506 -226v86l404 177l-404 177v86l506 -226v-74z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="463" 
d="M162 114h114v-114h-114v114zM169 252q0 36 10.5 61t27 44.5t35 36.5t35 36.5t27 44.5t10.5 61q0 21 -4 40t-13.5 34t-25 23.5t-38.5 8.5q-51 0 -70.5 -39t-19.5 -102h-108q0 47 10.5 88.5t34 72t61 48.5t91.5 18q94 0 145 -49t51 -143q0 -43 -11.5 -74.5t-29 -56.5
t-38 -45t-38 -39.5t-29 -40t-11.5 -46.5v-42h-102v60z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="800" 
d="M610 549l-75 -270q-3 -12 -7 -25t-4 -24t6.5 -16.5t26.5 -5.5q21 0 43 16t39.5 43.5t29 64t11.5 78.5q0 53 -21.5 97t-58 75.5t-84.5 48.5t-100 17q-65 0 -119.5 -24t-93.5 -64.5t-61 -94t-22 -111.5q0 -66 23.5 -119t63 -90.5t92 -58t109.5 -20.5q81 0 143 23t104 65h84
q-20 -36 -51.5 -67t-73 -53.5t-93.5 -35t-113 -12.5q-80 0 -149 25t-120 73t-80.5 117t-29.5 157q0 78 28 145.5t78.5 117.5t122 78.5t158.5 28.5q76 0 141 -22t112.5 -62.5t74.5 -96.5t27 -124q0 -75 -26 -129t-64.5 -89t-83 -51.5t-80.5 -16.5q-38 0 -54.5 16.5
t-15.5 42.5h-2q-21 -23 -52.5 -41t-71.5 -18q-29 0 -54.5 11.5t-44 32.5t-29.5 51t-11 67q0 53 18.5 101.5t49.5 85.5t71 59t84 22q37 0 66 -18.5t49 -58.5l19 59h71zM490 397q0 51 -21.5 73.5t-59.5 22.5q-31 0 -57 -17t-44.5 -43.5t-29 -58.5t-10.5 -62q0 -46 22.5 -73.5
t70.5 -27.5q26 0 49.5 19t41 47t28 60.5t10.5 59.5z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="519" 
d="M194 714h146l188 -714h-119l-44 175h-209l-46 -175h-119zM346 266l-82 352h-2l-85 -352h169z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="537" 
d="M59 714h231q40 0 75 -9.5t61 -30t41 -53.5t15 -80q0 -129 -105 -162v-2q29 -5 53 -17t40.5 -33.5t25.5 -54t9 -77.5t-15.5 -81t-44 -61.5t-69.5 -39t-91 -13.5h-226v714zM286 86q51 0 78 31.5t27 94.5q0 58 -30.5 89.5t-81.5 31.5h-106v-247h113zM263 413q52 0 78.5 31
t26.5 82q0 32 -9 52t-24.5 31t-36.5 15t-45 4h-80v-215h90z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="519" 
d="M498 256v-15q0 -48 -11 -94t-36.5 -82t-67.5 -57.5t-105 -21.5q-68 0 -113 22t-72.5 68t-39 115.5t-11.5 165.5t11.5 165.5t39 115.5t73 68t113.5 22q76 0 119 -24.5t64.5 -59.5t26.5 -73.5t5 -66.5v-13h-114v12q0 25 -4.5 50.5t-16 45.5t-31 32t-50.5 11
q-32 0 -55 -11.5t-38 -43t-22 -86.5t-7 -141t7 -142t22 -88.5t38 -45t55 -12.5q36 0 57 17.5t32 43.5t14 56t3 55v12h114z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="556" 
d="M59 714h203q84 0 134 -26t76 -73.5t34 -113t8 -144.5t-8 -144.5t-34 -113t-76 -73.5t-134 -26h-203v714zM173 86h94q35 0 60.5 13t41.5 44t23.5 83.5t7.5 130.5q0 75 -7 127t-22.5 84t-41 46t-62.5 14h-94v-542z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="481" 
d="M59 714h386v-96h-272v-198h256v-96h-256v-228h280v-96h-394v714z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="463" 
d="M59 714h381v-96h-267v-198h251v-96h-251v-324h-114v714z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="537" 
d="M412 83h-2q-23 -51 -58 -74t-98 -23q-59 0 -99 22t-65.5 68t-36.5 115.5t-11 165.5q0 191 56.5 281t180.5 90q67 0 108 -23t63.5 -55.5t30 -68t7.5 -59.5v-13h-108v17q0 20 -4.5 40.5t-16 37.5t-31 27.5t-48.5 10.5q-30 0 -53.5 -12t-39 -43.5t-23.5 -86.5t-8 -140
q0 -86 7 -142t21.5 -88.5t36 -45t50.5 -12.5q65 -1 92 50t27 168h-120v86h228v-376h-86v83z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="556" 
d="M59 714h114v-286h210v286h114v-714h-114v332h-210v-332h-114v714z" />
    <glyph glyph-name="I" unicode="I" 
d="M54 714h114v-714h-114v714z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="444" 
d="M124 215v-35q0 -22 3 -41.5t11 -34.5t23 -23.5t39 -8.5q28 0 43.5 12t23 32t8.5 45.5t1 52.5v500h114v-517q0 -59 -14 -99.5t-39.5 -65.5t-60.5 -35.5t-78 -10.5q-55 0 -90 15.5t-55 41t-28 58.5t-9 68v46h108z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="519" 
d="M59 714h114v-311h2l216 311h125l-206 -301l228 -413h-129l-173 321l-63 -90v-231h-114v714z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="444" 
d="M46 714h114v-618h276v-96h-390v714z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="722" 
d="M59 714h178l126 -529h2l124 529h174v-714h-108v594h-2l-144 -594h-96l-144 594h-2v-594h-108v714z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="574" 
d="M59 714h139l207 -532h2v532h108v-714h-139l-207 546h-2v-546h-108v714z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="556" 
d="M278 72q25 0 47.5 10.5t39 41.5t26 87.5t9.5 148.5q0 90 -9.5 145.5t-26 85.5t-39 40.5t-47.5 10.5q-26 0 -48 -10.5t-38.5 -40.5t-26 -85.5t-9.5 -145.5q0 -92 9.5 -148.5t26 -87.5t38.5 -41.5t48 -10.5zM278 -14q-123 0 -179.5 90t-56.5 281t56.5 281t179.5 90
t179.5 -90t56.5 -281t-56.5 -281t-179.5 -90z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="500" 
d="M59 714h226q51 0 88.5 -15.5t61.5 -43t35.5 -65.5t11.5 -84q0 -96 -54.5 -153.5t-154.5 -57.5h-100v-295h-114v714zM173 381h81q23 0 43.5 6.5t36.5 21t25 38t9 57.5q0 58 -27 91t-95 33h-73v-247z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="556" 
d="M330 197l47 -47q23 74 23 210q0 90 -9.5 145.5t-26 85.5t-39 40.5t-47.5 10.5q-26 0 -48 -10.5t-38.5 -40.5t-26 -85.5t-9.5 -145.5q0 -92 9.5 -148.5t26 -87.5t38.5 -41.5t48 -10.5q28 0 41 7l-54 53zM390 10q-22 -13 -50 -18.5t-62 -5.5q-123 0 -179.5 90t-56.5 281
t56.5 281t179.5 90t179.5 -90t56.5 -281q0 -100 -16 -172t-40 -111l73 -77l-68 -61z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="537" 
d="M264 396q55 0 84.5 31t29.5 85q0 57 -25.5 86.5t-82.5 29.5h-97v-232h91zM59 714h252q37 0 70 -10t58 -32t39 -57t14 -85q0 -69 -29.5 -117.5t-89.5 -57.5v-2q54 -5 81.5 -38t30.5 -108q1 -35 1.5 -74t2.5 -71q2 -26 12.5 -40t21.5 -22h-129q-8 10 -12.5 26t-6.5 37
q-3 31 -3 64q0 16 -0.5 33.5t-1.5 37.5q-2 60 -24 86t-80 26h-93v-310h-114v714z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="519" 
d="M147 221v-18q0 -131 117 -131q27 0 47 9t33.5 23.5t20.5 33t7 38.5q0 56 -27 84.5t-75 42.5l-78 27q-39 15 -67 33t-46.5 41.5t-27 53.5t-8.5 68q0 49 16 86.5t45 63t70 39t91 13.5q69 0 109 -19t60.5 -47.5t26.5 -61t6 -60.5v-16h-114v15q0 49 -23 76t-77 27
q-18 0 -35.5 -5.5t-31 -17.5t-21.5 -32t-8 -48q0 -46 25 -72.5t82 -48.5l77 -28q76 -27 110.5 -71t34.5 -118q0 -57 -16.5 -97.5t-47.5 -67t-74.5 -38.5t-97.5 -12q-66 0 -108.5 20t-66.5 51.5t-33 70t-9 76.5v17h114z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="463" 
d="M176 618h-170v96h451v-96h-167v-618h-114v618z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="537" 
d="M164 714v-512q0 -69 26.5 -99.5t78.5 -30.5q51 0 77.5 30.5t26.5 99.5v512h114v-503q0 -62 -15 -105t-43 -69.5t-68.5 -38.5t-91.5 -12t-92 12t-69 38.5t-43 69.5t-15 105v503h114z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="481" 
d="M-6 714h121l121 -570h2l128 570h121l-180 -714h-145z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="741" 
d="M6 714h115l94 -555h2l94 555h120l96 -558h2l93 558h113l-142 -714h-132l-92 538h-2l-91 -538h-132z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="500" 
d="M188 363l-172 351h126l114 -237l112 237h123l-173 -351l182 -363h-126l-124 258l-127 -258h-123z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="481" 
d="M180 279l-184 435h127l120 -308l123 308h119l-191 -435v-279h-114v279z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="463" 
d="M18 90l299 528h-281v96h406v-96l-299 -522h302v-96h-427v90z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="278" 
d="M77 714h203v-80h-101v-738h101v-80h-203v898z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="296" 
d="M-10 728h89l227 -742h-89z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="278" 
d="M-2 -104h101v738h-101v80h203v-898h-203v80z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="600" 
d="M300 608l-157 -328h-86l204 414h76l206 -414h-86z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="500" 
d="M0 -75h500v-50h-500v50z" />
    <glyph glyph-name="grave" unicode="`" 
d="M-14 752h120l60 -144h-74z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="463" 
d="M303 284q-11 -8 -26.5 -14.5t-32 -12.5l-33 -11.5t-28.5 -11.5q-23 -11 -33.5 -34t-10.5 -53q0 -34 16.5 -57.5t50.5 -23.5q49 0 73 34t24 94v90zM405 112q0 -15 8 -26.5t20 -11.5t18 3v-70q-10 -5 -23.5 -9t-29.5 -4q-36 0 -62.5 16t-28.5 55h-2q-22 -40 -54 -59.5
t-80 -19.5q-66 0 -103 36.5t-37 114.5q0 45 9.5 73.5t26 47t39.5 29t50 19.5l84 23q29 8 46 21t17 48q0 37 -15.5 58.5t-58.5 21.5q-27 0 -43 -9t-25 -23t-11.5 -33t-2.5 -38h-102q0 43 9.5 76t31.5 55.5t58.5 34t89.5 11.5q36 0 67 -7.5t54 -23t36.5 -40.5t13.5 -61v-308z
" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="481" 
d="M154 263q0 -44 4 -79t14 -60t27.5 -38.5t45.5 -13.5t46 14t28 40t13.5 62.5t3.5 82.5q0 54 -4.5 91.5t-14.5 60.5t-26 33t-39 10q-32 0 -51 -13.5t-29.5 -39.5t-14 -64t-3.5 -86zM52 714h108v-239h2q15 36 46 56.5t76 20.5q31 0 60.5 -9.5t53 -39t38 -83.5t14.5 -144
q0 -139 -41 -214.5t-135 -75.5q-29 0 -49 7.5t-33.5 19.5t-22 26t-13.5 27h-2v-66h-102v714z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="444" 
d="M415 196q-3 -48 -13.5 -87t-31.5 -66.5t-55 -42t-84 -14.5q-59 0 -97.5 20.5t-61 58t-32 89.5t-9.5 115t9.5 115t32 89.5t61 58t97.5 20.5q60 1 96.5 -19.5t56 -50.5t26 -64t6.5 -59h-108q0 19 -3 39.5t-11.5 37t-23.5 27t-39 9.5q-29 0 -47 -15t-28 -42t-13.5 -64
t-3.5 -82t3.5 -82t13.5 -64t28 -42t47 -15q45 -1 63.5 34t18.5 96h102z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="481" 
d="M327 263q0 48 -3.5 86t-14 64t-30 39.5t-50.5 13.5q-23 0 -39 -10t-26 -33t-14.5 -60.5t-4.5 -91.5q0 -46 3.5 -82.5t13.5 -62.5t27.5 -40t46.5 -14q28 0 45.5 13.5t27.5 38.5t14 60t4 79zM327 66h-2q-5 -13 -13.5 -27t-22 -26t-34 -19.5t-48.5 -7.5q-94 0 -135 76
t-41 214q0 90 14.5 144t38 83.5t53 39t60.5 9.5q45 0 76 -20.5t46 -56.5h2v239h108v-714h-102v66z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="463" 
d="M318 329v29q0 51 -18.5 85.5t-64.5 34.5q-26 0 -43 -11.5t-27.5 -31t-15 -44.5t-4.5 -51v-11h173zM145 255v-31q0 -27 2.5 -56t12 -53t27.5 -39.5t48 -15.5q29 0 46 14t26 33.5t11.5 40t2.5 32.5h102q0 -91 -50.5 -142.5t-138.5 -51.5q-33 0 -68 8t-63.5 36t-47 83
t-18.5 148q0 53 7.5 105t29 93.5t60.5 67t102 25.5q56 0 93 -16.5t58.5 -47.5t30 -75.5t8.5 -100.5v-57h-281z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="278" 
d="M85 458h-75v80h75v50q0 76 35.5 105t101.5 29q35 0 59 -3v-83h-30q-30 0 -44 -12t-14 -39v-47h88v-80h-88v-458h-108v458z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="481" 
d="M152 286q0 -35 2 -69.5t10 -63t25 -46t47 -17.5q31 0 49.5 18t28.5 45.5t12.5 61.5t2.5 66q0 33 -3 66t-12 59.5t-26 43t-45 16.5q-29 0 -47 -14t-28 -38.5t-13 -57.5t-3 -70zM160 -46q0 -25 21 -44.5t57 -19.5q42 0 66.5 26.5t24.5 74.5v87h-2q-15 -35 -49 -54.5
t-73 -19.5q-47 0 -78 21.5t-49.5 58.5t-26 86t-7.5 105q0 44 5 93t23 90t52 67.5t91 26.5q24 0 44 -7t35 -18.5t25 -25.5t14 -29h2v66h102v-512q0 -49 -11 -88t-35 -66t-62.5 -41.5t-92.5 -14.5q-60 0 -95.5 15.5t-53.5 37t-23.5 45t-5.5 40.5h102z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="481" 
d="M52 714h108v-234l3 -2q23 35 56.5 54.5t75.5 19.5q31 0 55.5 -7.5t42 -24.5t27 -45t9.5 -69v-406h-108v369q0 54 -16.5 75.5t-58.5 21.5q-33 0 -59.5 -25.5t-26.5 -79.5v-361h-108v714z" />
    <glyph glyph-name="i" unicode="i" 
d="M57 722h108v-108h-108v108zM57 538h108v-538h-108v538z" />
    <glyph glyph-name="j" unicode="j" 
d="M165 722v-108h-108v108h108zM165 538v-573q0 -84 -35.5 -116.5t-106.5 -32.5q-23 0 -47 3v83h25q31 0 43.5 15t12.5 58v563h108z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="463" 
d="M52 714h108v-401h2l163 225l125 1l-162 -208l183 -331h-118l-132 245l-61 -74v-171h-108v714z" />
    <glyph glyph-name="l" unicode="l" 
d="M57 714h108v-714h-108v714z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="740" 
d="M57 538h102v-63h3q20 36 55.5 56.5t73.5 20.5q29 0 50 -6.5t35.5 -17.5t24 -26t15.5 -32q23 39 55 60.5t81 21.5q57 0 94 -32.5t37 -110.5v-409h-108v369q0 54 -16 75.5t-49 21.5t-59.5 -25.5t-26.5 -79.5v-361h-108v369q0 54 -16 75.5t-50 21.5q-33 0 -59 -25.5
t-26 -79.5v-361h-108v538z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="481" 
d="M52 538h102v-63h3q20 36 56.5 56.5t81.5 20.5q31 0 55.5 -7.5t42 -24.5t27 -45t9.5 -69v-406h-108v369q0 54 -16.5 75.5t-58.5 21.5q-33 0 -59.5 -25.5t-26.5 -79.5v-361h-108v538z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="463" 
d="M139 269q0 -51 3.5 -89.5t13.5 -63.5t28 -37.5t47 -12.5q30 0 48 12.5t28 37.5t13.5 63.5t3.5 89.5q0 45 -3.5 82t-13.5 64t-28 42t-48 15q-29 0 -47 -15t-28 -42t-13.5 -64t-3.5 -82zM231 -14q-56 0 -94 20.5t-61.5 58t-34 89.5t-10.5 115t9.5 115t32 89.5t61 58
t97.5 20.5q58 0 97 -20.5t62 -58t32.5 -89.5t9.5 -115t-10.5 -115t-34 -89.5t-62 -58t-94.5 -20.5z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="481" 
d="M336 267q0 46 -3.5 82.5t-13.5 62.5t-28 40t-46 14t-45.5 -13.5t-27.5 -38.5t-14 -60.5t-4 -78.5q0 -48 3.5 -86t14 -64t28 -39.5t46.5 -13.5q23 0 40 9.5t28 32t16.5 60t5.5 93.5zM450 262q0 -85 -15.5 -138.5t-39.5 -84t-53.5 -42t-57.5 -11.5q-45 0 -76 20.5t-46 56.5
h-2v-239h-108v714h102v-66h2q17 36 46.5 58t74.5 22q92 0 132.5 -75.5t40.5 -214.5z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="481" 
d="M327 275q0 43 -4 78.5t-14 60.5t-27.5 38.5t-45.5 13.5q-29 0 -46.5 -14t-27.5 -40t-13.5 -62.5t-3.5 -82.5q0 -54 5.5 -91.5t16 -60.5t26 -33t36.5 -10q31 0 50.5 13.5t30 39.5t14 64t3.5 86zM321 63h-2q-15 -36 -46.5 -56.5t-76.5 -20.5q-32 0 -61.5 12.5t-52.5 44
t-37 84.5t-14 135q0 138 41 214t135 76q28 0 48.5 -7.5t34 -19.5t22 -26t13.5 -27h2v66h102v-714h-108v239z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="315" 
d="M52 538h108v-84h2q18 45 48.5 71.5t76.5 26.5q19 0 26 -4v-110q-6 2 -18.5 4t-26.5 2q-18 0 -37.5 -5t-35 -18.5t-25.5 -37.5t-10 -61v-322h-108v538z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="426" 
d="M284 381v14q0 16 -3 31t-11.5 26.5t-22 18.5t-34.5 7q-32 0 -54.5 -16t-22.5 -54q0 -32 16.5 -48t59.5 -31l69 -24q60 -20 88.5 -55.5t28.5 -99.5q0 -43 -15 -74t-41 -51t-61.5 -29.5t-75.5 -9.5q-51 0 -85 11.5t-54.5 33t-29 52.5t-8.5 70v20h96v-17q0 -48 18 -72
t66 -24q45 0 66.5 21t21.5 58q0 59 -59 79l-88 31q-61 20 -88 55t-27 100q0 76 49.5 112t134.5 36q51 0 84 -13.5t51.5 -35t25.5 -47.5t7 -51v-24h-102z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="278" 
d="M4 538h74v153h108v-153h88v-80h-88v-321q0 -32 10.5 -44.5t38.5 -12.5q24 0 39 3v-80q-35 -9 -87 -9q-27 0 -47.5 5t-34 19t-20.5 39.5t-7 66.5v334h-74v80z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="481" 
d="M327 63h-3q-20 -36 -56.5 -56.5t-81.5 -20.5q-30 0 -54.5 8t-42.5 28t-27.5 54t-9.5 86v376h108v-386q0 -45 19 -62.5t54 -17.5t61.5 22.5t26.5 67.5v376h108v-538h-102v63z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="426" 
d="M7 538h116l94 -424h2l85 424h115l-142 -538h-126z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="685" 
d="M15 538h112l75 -424h2l80 424h122l80 -424h2l74 424h108l-123 -538h-121l-84 418h-2l-77 -418h-126z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="426" 
d="M150 279l-139 259h118l84 -178l88 178h116l-140 -259l146 -279h-117l-94 199l-94 -199h-115z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="426" 
d="M124 538l93 -404h2l86 404h113l-148 -558q-16 -55 -31.5 -88t-36.5 -50.5t-49.5 -22t-70.5 -3.5q-11 0 -22 1t-21 3v86q15 -4 35 -4q28 0 46 8t27 34l14 44l-153 550h116z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="407" 
d="M25 84l235 363h-221v91h343v-89l-226 -358h226v-91h-357v84z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="278" 
d="M7 298q16 1 30.5 9.5t26 22.5t18 32t6.5 38v183q0 32 9.5 56.5t25 41t35.5 25t41 8.5h72v-62h-33q-29 0 -38.5 -19t-9.5 -50v-183q0 -35 -11 -59.5t-26 -40.5t-30.5 -24t-24.5 -10v-2q9 -2 24.5 -10t30.5 -24t26 -40.5t11 -59.5v-183q0 -31 9.5 -50t38.5 -19h33v-62h-72
q-21 0 -41 8.5t-35.5 25t-25 41t-9.5 56.5v183q0 20 -6.5 38t-18 32t-26 22.5t-30.5 9.5v66z" />
    <glyph glyph-name="bar" unicode="|" 
d="M68 728h86v-742h-86v742z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="278" 
d="M271 232q-16 -1 -30.5 -9.5t-26 -22.5t-18 -32t-6.5 -38v-183q0 -32 -9.5 -56.5t-25 -41t-35.5 -25t-41 -8.5h-72v62h33q29 0 38.5 19t9.5 50v183q0 35 11 59.5t26 40.5t30.5 24t24.5 10v2q-9 2 -24.5 10t-30.5 24t-26 40.5t-11 59.5v183q0 31 -9.5 50t-38.5 19h-33v62
h72q21 0 41 -8.5t35.5 -25t25 -41t9.5 -56.5v-183q0 -20 6.5 -38t18 -32t26 -22.5t30.5 -9.5v-66z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="600" 
d="M69 245q22 43 55.5 65t69.5 22q29 0 58.5 -11.5t57.5 -25.5q29 -14 54 -24.5t43 -10.5q44 0 88 77l36 -76q-23 -40 -56 -63.5t-69 -23.5q-29 0 -58.5 11.5t-57.5 25.5q-29 14 -54 24.5t-43 10.5q-44 0 -88 -77z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="240" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="278" 
d="M196 424h-114v114h114v-114zM196 50v-226h-114v226l17 296h80z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="480" 
d="M224 -14q-51 4 -85 26.5t-54.5 60t-28.5 87.5t-8 109t8 109t28.5 87.5t54.5 60t85 26.5v75h44v-75q54 -3 86 -24.5t49.5 -51t23 -61.5t5.5 -56h-108q0 16 -2.5 34.5t-8.5 34.5t-17 28t-28 16v-406q35 10 48.5 43t13.5 87h102q-3 -45 -12.5 -82.5t-28 -65t-48.5 -43.5
t-75 -19v-88h-44v88zM224 472q-21 -6 -34.5 -24t-21 -44.5t-10 -61t-2.5 -73.5t2.5 -73t10 -60.5t21 -44.5t34.5 -25v406z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="480" 
d="M18 383h69l-17 35q-33 61 -33 111q0 33 12.5 65t38.5 57.5t64.5 41t91.5 15.5q59 0 99.5 -16.5t65.5 -43.5t36 -62t11 -72v-12h-108v11q0 16 -4 36t-15 37.5t-30 29.5t-50 12q-45 0 -71.5 -24t-26.5 -62q0 -28 8.5 -56t23.5 -57l23 -46h166v-68h-140q10 -23 10 -59
q0 -30 -10 -54t-26.5 -45t-37.5 -39t-43 -35l2 -4q35 23 73 23q15 0 31.5 -4.5t32.5 -10.5t31.5 -10.5t29.5 -4.5q19 0 39 8.5t36 17.5l30 16l38 -79l-20 -12q-30 -18 -59.5 -28t-67.5 -9q-25 0 -47.5 6t-43.5 13.5t-41.5 13.5t-41.5 6q-24 0 -48 -9.5t-47 -21.5l-40 77
l18 13q16 12 34.5 28.5t34 37.5t25.5 45.5t10 53.5q0 20 -5.5 36t-12.5 30h-98v68z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="480" 
d="M240 191q31 0 58.5 12t48 33t32 49.5t11.5 61.5t-11.5 61.5t-32 49.5t-48 33t-58.5 12q-32 0 -59 -12t-47.5 -33t-32 -49.5t-11.5 -61.5t11.5 -61.5t32 -49.5t47.5 -33t59 -12zM57 208q-22 29 -34.5 64.5t-12.5 74.5q0 40 12.5 75.5t34.5 63.5l-51 51l45 45l51 -51
q29 21 64 33.5t74 12.5t74 -12.5t64 -33.5l51 51l45 -45l-51 -52q22 -29 34.5 -64t12.5 -74t-12.5 -74t-34.5 -64l51 -52l-45 -45l-52 52q-59 -47 -137 -47q-39 0 -74 12.5t-64 34.5l-51 -52l-45 45z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="480" 
d="M182 162h-141v68h141v47l-10 23h-131v68h101l-140 326h124l114 -284l119 284h119l-147 -326h100v-68h-130l-11 -23v-47h141v-68h-141v-162h-108v162z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" 
d="M68 728h86v-277h-86v277zM68 263h86v-277h-86v277z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="481" 
d="M316 571q0 77 -78 77q-34 0 -55.5 -18t-21.5 -47q0 -24 18 -43t58 -42l135 -75q50 -28 67.5 -66t17.5 -83q0 -43 -22 -79.5t-58 -59.5q27 -23 42 -52.5t15 -63.5q0 -44 -14.5 -76.5t-40 -54.5t-60.5 -33t-75 -11q-50 0 -86 13.5t-60 38t-35.5 58t-11.5 73.5h108
q0 -42 18.5 -69.5t66.5 -27.5q32 0 54 19.5t22 55.5q0 19 -6 33t-16.5 25t-24 19.5l-27.5 16.5l-114 62q-48 26 -78 63t-30 91q0 51 27.5 85.5t65.5 56.5q-31 17 -47.5 47t-16.5 68q0 39 14.5 68t40 48.5t60 29.5t73.5 10q55 0 90.5 -15t56 -38t28.5 -51t8 -53h-108z
M183 415l-13 -11q-18 -13 -34 -31.5t-16 -44.5q0 -27 19 -43.5t57 -38.5l114 -67l16 12q16 14 25.5 31t9.5 40q0 34 -19.5 53t-39.5 31z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" 
d="M-20 734h96v-108h-96v108zM146 734h96v-108h-96v108z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="800" 
d="M29 357q0 77 29 144.5t79.5 118t118 79.5t144.5 29t144.5 -29t118 -79.5t79.5 -118t29 -144.5t-29 -144.5t-79.5 -118t-118 -79.5t-144.5 -29t-144.5 29t-118 79.5t-79.5 118t-29 144.5zM400 66q58 0 109 23t89 62.5t60 92.5t22 113t-22 113t-60 92.5t-89 62.5t-109 23
t-109 -23t-89 -62.5t-60 -92.5t-22 -113t22 -113t60 -92.5t89 -62.5t109 -23zM593 292q-13 -75 -63.5 -116.5t-125.5 -41.5q-45 0 -82.5 15.5t-64 45t-41 71t-14.5 92.5t15 92.5t42 70.5t65.5 44.5t86.5 15.5q69 0 118.5 -39t60.5 -114h-80q-8 35 -33.5 58t-67.5 21
q-29 0 -51.5 -11.5t-38 -32t-23.5 -47.5t-8 -58q0 -32 8 -59.5t24 -47.5t39.5 -31.5t53.5 -11.5q38 0 65 22.5t32 61.5h83z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="316" 
d="M197 547q-7 -5 -17.5 -9t-22 -7.5l-22 -7t-16.5 -7.5q-17 -8 -21 -22.5t-4 -28.5t11 -25t31 -11q28 0 44.5 17.5t16.5 43.5v57zM8 457q0 28 6 45.5t18 28.5t29 17.5t39 12.5l52 13q22 5 33.5 13t11.5 29q0 15 -10 25.5t-36 10.5q-18 0 -28.5 -4.5t-16 -12t-7 -16.5
t-1.5 -17h-74q0 25 6.5 45t22 33.5t42 20.5t65.5 7q51 0 84 -18t33 -61v-177q0 -9 4.5 -16t12.5 -7q5 0 8 0.5t6 1.5v-57q-7 -2 -17 -4.5t-24 -2.5q-25 0 -42.5 11t-19.5 36h-3q-16 -24 -38 -35.5t-58 -11.5q-98 0 -98 90z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="407" 
d="M50 346l144 135v-110l-88 -82l88 -82v-110l-144 135v114zM213 346l144 135v-110l-88 -82l88 -82v-110l-144 135v114z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="600" 
d="M467 314h-420v86h506v-294h-86v208z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="352" 
d="M45 340h262v-102h-262v102z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="800" 
d="M29 357q0 77 29 144.5t79.5 118t118 79.5t144.5 29t144.5 -29t118 -79.5t79.5 -118t29 -144.5t-29 -144.5t-79.5 -118t-118 -79.5t-144.5 -29t-144.5 29t-118 79.5t-79.5 118t-29 144.5zM400 66q58 0 109 23t89 62.5t60 92.5t22 113t-22 113t-60 92.5t-89 62.5t-109 23
t-109 -23t-89 -62.5t-60 -92.5t-22 -113t22 -113t60 -92.5t89 -62.5t109 -23zM390 386q20 0 38 1.5t32 8t22 19t8 35.5q0 18 -9 29.5t-22.5 17.5t-30 8t-32.5 2h-82v-121h76zM377 324h-63v-179h-74v424h165q79 0 119 -28t40 -94q0 -29 -8.5 -50t-23.5 -35t-35 -22.5
t-42 -12.5l116 -182h-86z" />
    <glyph glyph-name="macron" unicode="&#xaf;" 
d="M252 711v-62h-282v62h282z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="400" 
d="M119 559q0 -34 23.5 -57.5t57.5 -23.5t57.5 23.5t23.5 57.5t-23.5 57.5t-57.5 23.5t-57.5 -23.5t-23.5 -57.5zM51 559q0 31 11.5 58t32 47t47.5 32t58 12t58 -12t47 -32t32 -47t12 -58t-12 -58t-32 -47.5t-47 -32t-58 -11.5t-58 11.5t-47.5 32t-32 47.5t-11.5 58z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="600" 
d="M47 86h506v-86h-506v86zM257 506h86v-151h210v-86h-210v-151h-86v151h-210v86h210v151z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="313" 
d="M292 280h-276q0 50 14.5 83.5t36 58t46.5 42t46.5 35.5t36 38.5t14.5 50.5q0 26 -12.5 42t-38.5 16q-18 0 -29 -7.5t-17 -19t-8 -24.5t-2 -25h-86q0 62 32 100t111 38q75 0 105.5 -31.5t30.5 -82.5q0 -34 -11 -58.5t-29 -43.5t-39.5 -34.5l-42.5 -31.5t-39 -34.5
t-28 -43.5h185v-68z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="313" 
d="M116 533h27q35 0 49 19t14 40q0 18 -12 36t-38 18q-15 0 -25 -6t-16.5 -15t-9 -20t-2.5 -20h-86q0 60 39.5 91.5t105.5 31.5q23 0 46.5 -6t42 -19.5t30 -34t11.5 -48.5q0 -33 -23 -61t-57 -33v-2q38 -2 64.5 -28t26.5 -71q0 -63 -42 -97.5t-106 -34.5q-73 0 -109 30
t-36 97h86q0 -9 2.5 -20.5t9.5 -21t18.5 -16.5t28.5 -7q33 0 47.5 21.5t14.5 49.5q0 29 -15.5 47t-55.5 18h-30v62z" />
    <glyph glyph-name="acute" unicode="&#xb4;" 
d="M116 752h120l-106 -144h-74z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="481" 
d="M52 538h108v-369q0 -54 16.5 -75.5t58.5 -21.5q33 0 59.5 25.5t26.5 79.5v361h108v-538h-102v63h-3q-20 -36 -51 -56.5t-63 -20.5q-18 0 -30.5 5.5t-19.5 12.5v-180h-108v714z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="600" 
d="M443 646h-111v-788h-91v486q-39 0 -73 13.5t-59.5 38t-40.5 58t-15 72.5q0 50 17 86t47.5 58.5t73.5 33t95 10.5h248v-856h-91v788z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="240" 
d="M48 308q0 30 21 51t51 21t51 -21t21 -51t-21 -51t-51 -21t-51 21t-21 51z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" 
d="M146 0l-42 -60q11 3 20.5 4t19.5 1q34 0 56 -22t22 -52q0 -42 -33 -67t-84 -27q-34 0 -58.5 5t-42.5 12l16 40q30 -13 69 -13q24 0 38.5 9.5t14.5 32.5q0 17 -13.5 27.5t-32.5 10.5q-11 0 -20 -2t-18 -6l-18 17l56 90h50z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="313" 
d="M130 585h-90v56h7q16 0 33.5 2t32.5 8.5t26.5 18t16.5 30.5v8h60v-428h-86v305z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="316" 
d="M158 429q18 0 29.5 8.5t18 23.5t8.5 34.5t2 40.5q0 22 -2 42t-8.5 35t-18 24t-29.5 9t-29.5 -9t-18 -24t-8.5 -35t-2 -42q0 -21 2 -40.5t8.5 -34.5t18 -23.5t29.5 -8.5zM158 367q-42 0 -70 12.5t-44.5 34.5t-23 53t-6.5 69q0 39 6.5 70.5t23 54t44.5 35t70 12.5
t69.5 -12.5t44 -35t23.5 -54t7 -70.5q0 -38 -7 -69t-23.5 -53t-44 -34.5t-69.5 -12.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="407" 
d="M357 232l-144 -135v110l88 82l-88 82v110l144 -135v-114zM194 232l-144 -135v110l88 82l-88 82v110l144 -135v-114z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="721" 
d="M100 585h-90v56h7q16 0 33.5 2t32.5 8.5t26.5 18t16.5 30.5v8h60v-428h-86v305zM495 725h74l-421 -756h-74zM545 332h-2l-103 -182h105v182zM545 94h-164v61l158 273h86v-278h50v-56h-50v-94h-80v94z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="721" 
d="M411 0q0 50 14.5 83.5t36 58t46.5 42t46.5 35.5t36 38.5t14.5 50.5q0 26 -12.5 42t-38.5 16q-18 0 -29 -7.5t-17 -19t-8 -24.5t-2 -25h-86q0 62 32 100t111 38q75 0 105.5 -31.5t30.5 -82.5q0 -34 -11 -58.5t-29 -43.5t-39.5 -34.5l-42.5 -31.5t-39 -34.5t-28 -43.5h185
v-68h-276zM100 585h-90v56h7q16 0 33.5 2t32.5 8.5t26.5 18t16.5 30.5v8h60v-428h-86v305zM495 725h74l-421 -756h-74z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="721" 
d="M90 533h27q35 0 49 19t14 40q0 18 -12 36t-38 18q-15 0 -25 -6t-16.5 -15t-9 -20t-2.5 -20h-86q0 60 39.5 91.5t105.5 31.5q23 0 46.5 -6t42 -19.5t30 -34t11.5 -48.5q0 -33 -23 -61t-57 -33v-2q38 -2 64.5 -28t26.5 -71q0 -63 -42 -97.5t-106 -34.5q-73 0 -109 30
t-36 97h86q0 -9 2.5 -20.5t9.5 -21t18.5 -16.5t28.5 -7q33 0 47.5 21.5t14.5 49.5q0 29 -15.5 47t-55.5 18h-30v62zM529 725h74l-421 -756h-74zM545 332h-2l-103 -182h105v182zM545 94h-164v61l158 273h86v-278h50v-56h-50v-94h-80v94z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="463" 
d="M301 424h-114v114h114v-114zM294 286q0 -36 -10.5 -61t-27 -44.5t-35 -36.5t-35 -36.5t-27 -44.5t-10.5 -61q0 -21 4 -40t13.5 -34t25 -23.5t38.5 -8.5q51 0 70.5 39t19.5 102h108q0 -47 -10.5 -88.5t-34 -72.5t-61 -48.5t-91.5 -17.5q-94 0 -145 49t-51 143
q0 42 11.5 74t29 57t38 45t38 39.5t29 40t11.5 46.5v42h102v-60z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="519" 
d="M194 714h146l188 -714h-119l-44 175h-209l-46 -175h-119zM346 266l-82 352h-2l-85 -352h169zM153 906h120l60 -144h-74z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="519" 
d="M194 714h146l188 -714h-119l-44 175h-209l-46 -175h-119zM346 266l-82 352h-2l-85 -352h169zM255 906h120l-106 -144h-74z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="519" 
d="M194 714h146l188 -714h-119l-44 175h-209l-46 -175h-119zM346 266l-82 352h-2l-85 -352h169zM213 906h108l89 -144h-91l-54 88l-57 -88h-86z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="519" 
d="M194 714h146l188 -714h-119l-44 175h-209l-46 -175h-119zM346 266l-82 352h-2l-85 -352h169zM413 896q0 -17 -5.5 -37.5t-16 -38.5t-27 -30t-37.5 -12q-17 0 -33.5 5.5t-32 12l-29 12t-25.5 5.5q-17 0 -25.5 -12.5t-8.5 -27.5h-62q0 16 5.5 37t17.5 39.5t31 31t47 12.5
q12 0 25.5 -5.5l27.5 -11.5q14 -7 28.5 -12.5t27.5 -5.5q17 0 23.5 12t6.5 26h62z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="519" 
d="M194 714h146l188 -714h-119l-44 175h-209l-46 -175h-119zM346 266l-82 352h-2l-85 -352h169zM132 888h96v-108h-96v108zM298 888h96v-108h-96v108z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="519" 
d="M194 714h146l188 -714h-119l-44 175h-209l-46 -175h-119zM346 266l-82 352h-2l-85 -352h169zM158 846q0 45 30.5 74.5t75.5 29.5t75.5 -29.5t30.5 -74.5t-30.5 -74.5t-75.5 -29.5t-75.5 29.5t-30.5 74.5zM208 846q0 -23 16.5 -39.5t39.5 -16.5t39.5 16.5t16.5 39.5
t-16.5 39.5t-39.5 16.5t-39.5 -16.5t-16.5 -39.5z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="741" 
d="M261 714h437v-96h-253v-198h239v-96h-239v-228h262v-96h-370v175h-167l-63 -175h-119zM337 266v357h-9l-128 -357h137z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="519" 
d="M498 256v-15q0 -37 -6 -72t-20 -65.5t-36 -55.5t-53 -41q-37 -18 -89 -21l-32 -46q11 3 20.5 4t19.5 1q34 0 56 -22t22 -52q0 -42 -33 -67t-84 -27q-34 0 -58.5 5t-42.5 12l16 40q30 -13 69 -13q24 0 38.5 9.5t14.5 32.5q0 17 -13.5 27.5t-32.5 10.5q-11 0 -20 -2t-18 -6
l-18 17l48 77q-47 4 -81 21q-69 34 -96 119.5t-27 229.5q0 96 11.5 165.5t39 115.5t73 68t113.5 22q76 0 119 -24.5t64.5 -59.5t26.5 -73.5t5 -66.5v-13h-114v12q0 25 -4.5 50.5t-16 45.5t-31 32t-50.5 11q-32 0 -55 -11.5t-38 -43t-22 -86.5t-7 -141t7 -142t22 -88.5
t38 -45t55 -12.5q36 0 57 17.5t32 43.5t14 56t3 55v12h114z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="481" 
d="M59 714h386v-96h-272v-198h256v-96h-256v-228h280v-96h-394v714zM123 906h120l60 -144h-74z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="481" 
d="M59 714h386v-96h-272v-198h256v-96h-256v-228h280v-96h-394v714zM269 906h120l-106 -144h-74z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="481" 
d="M59 714h386v-96h-272v-198h256v-96h-256v-228h280v-96h-394v714zM201 906h108l89 -144h-91l-54 88l-57 -88h-86z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="481" 
d="M59 714h386v-96h-272v-198h256v-96h-256v-228h280v-96h-394v714zM123 888h96v-108h-96v108zM289 888h96v-108h-96v108z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" 
d="M54 714h114v-714h-114v714zM-12 906h120l60 -144h-74z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" 
d="M54 714h114v-714h-114v714zM116 906h120l-106 -144h-74z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" 
d="M54 714h114v-714h-114v714zM58 906h108l89 -144h-91l-54 88l-57 -88h-86z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" 
d="M54 714h114v-714h-114v714zM-20 888h96v-108h-96v108zM146 888h96v-108h-96v108z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="556" 
d="M267 86q35 0 60.5 13t41.5 44t23.5 83.5t7.5 130.5q0 75 -7 127t-22.5 84t-41 46t-62.5 14h-94v-215h119v-80h-119v-247h94zM59 333h-59v80h59v301h203q84 0 134 -26t76 -73.5t34 -113t8 -144.5t-8 -144.5t-34 -113t-76 -73.5t-134 -26h-203v333z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="574" 
d="M59 714h139l207 -532h2v532h108v-714h-139l-207 546h-2v-546h-108v714zM440 896q0 -17 -5.5 -37.5t-16 -38.5t-27 -30t-37.5 -12q-17 0 -33.5 5.5t-32 12l-29 12t-25.5 5.5q-17 0 -25.5 -12.5t-8.5 -27.5h-62q0 16 5.5 37t17.5 39.5t31 31t47 12.5q12 0 25.5 -5.5
l27.5 -11.5q14 -7 28.5 -12.5t27.5 -5.5q17 0 23.5 12t6.5 26h62z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="556" 
d="M278 72q25 0 47.5 10.5t39 41.5t26 87.5t9.5 148.5q0 90 -9.5 145.5t-26 85.5t-39 40.5t-47.5 10.5q-26 0 -48 -10.5t-38.5 -40.5t-26 -85.5t-9.5 -145.5q0 -92 9.5 -148.5t26 -87.5t38.5 -41.5t48 -10.5zM278 -14q-123 0 -179.5 90t-56.5 281t56.5 281t179.5 90
t179.5 -90t56.5 -281t-56.5 -281t-179.5 -90zM140 906h120l60 -144h-74z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="556" 
d="M278 72q25 0 47.5 10.5t39 41.5t26 87.5t9.5 148.5q0 90 -9.5 145.5t-26 85.5t-39 40.5t-47.5 10.5q-26 0 -48 -10.5t-38.5 -40.5t-26 -85.5t-9.5 -145.5q0 -92 9.5 -148.5t26 -87.5t38.5 -41.5t48 -10.5zM278 -14q-123 0 -179.5 90t-56.5 281t56.5 281t179.5 90
t179.5 -90t56.5 -281t-56.5 -281t-179.5 -90zM294 906h120l-106 -144h-74z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="556" 
d="M278 72q25 0 47.5 10.5t39 41.5t26 87.5t9.5 148.5q0 90 -9.5 145.5t-26 85.5t-39 40.5t-47.5 10.5q-26 0 -48 -10.5t-38.5 -40.5t-26 -85.5t-9.5 -145.5q0 -92 9.5 -148.5t26 -87.5t38.5 -41.5t48 -10.5zM278 -14q-123 0 -179.5 90t-56.5 281t56.5 281t179.5 90
t179.5 -90t56.5 -281t-56.5 -281t-179.5 -90zM226 906h108l89 -144h-91l-54 88l-57 -88h-86z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="556" 
d="M278 72q25 0 47.5 10.5t39 41.5t26 87.5t9.5 148.5q0 90 -9.5 145.5t-26 85.5t-39 40.5t-47.5 10.5q-26 0 -48 -10.5t-38.5 -40.5t-26 -85.5t-9.5 -145.5q0 -92 9.5 -148.5t26 -87.5t38.5 -41.5t48 -10.5zM278 -14q-123 0 -179.5 90t-56.5 281t56.5 281t179.5 90
t179.5 -90t56.5 -281t-56.5 -281t-179.5 -90zM430 896q0 -17 -5.5 -37.5t-16 -38.5t-27 -30t-37.5 -12q-17 0 -33.5 5.5t-32 12l-29 12t-25.5 5.5q-17 0 -25.5 -12.5t-8.5 -27.5h-62q0 16 5.5 37t17.5 39.5t31 31t47 12.5q12 0 25.5 -5.5l27.5 -11.5q14 -7 28.5 -12.5
t27.5 -5.5q17 0 23.5 12t6.5 26h62z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="556" 
d="M278 72q25 0 47.5 10.5t39 41.5t26 87.5t9.5 148.5q0 90 -9.5 145.5t-26 85.5t-39 40.5t-47.5 10.5q-26 0 -48 -10.5t-38.5 -40.5t-26 -85.5t-9.5 -145.5q0 -92 9.5 -148.5t26 -87.5t38.5 -41.5t48 -10.5zM278 -14q-123 0 -179.5 90t-56.5 281t56.5 281t179.5 90
t179.5 -90t56.5 -281t-56.5 -281t-179.5 -90zM147 888h96v-108h-96v108zM313 888h96v-108h-96v108z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="600" 
d="M70 427l56 56l173 -174l175 174l56 -56l-175 -174l175 -174l-56 -56l-175 175l-173 -175l-56 56l174 174z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="556" 
d="M85 -28l33 78q-39 45 -57.5 120t-18.5 187q0 191 56.5 281t179.5 90q69 0 115 -28l27 65l51 -24l-33 -77q39 -45 57.5 -120t18.5 -187q0 -191 -56.5 -281t-179.5 -90q-68 0 -114 28l-28 -66zM354 607q-16 21 -35 28t-41 7q-26 0 -48 -10.5t-38.5 -40.5t-26 -85.5
t-9.5 -145.5q0 -120 16 -181h2zM203 107q15 -21 34 -28t41 -7q25 0 47.5 10.5t39 41.5t26 87.5t9.5 148.5q0 60 -4 103t-11 72h-2z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="537" 
d="M164 714v-512q0 -69 26.5 -99.5t78.5 -30.5q51 0 77.5 30.5t26.5 99.5v512h114v-503q0 -62 -15 -105t-43 -69.5t-68.5 -38.5t-91.5 -12t-92 12t-69 38.5t-43 69.5t-15 105v503h114zM130 906h120l60 -144h-74z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="537" 
d="M164 714v-512q0 -69 26.5 -99.5t78.5 -30.5q51 0 77.5 30.5t26.5 99.5v512h114v-503q0 -62 -15 -105t-43 -69.5t-68.5 -38.5t-91.5 -12t-92 12t-69 38.5t-43 69.5t-15 105v503h114zM287 906h120l-106 -144h-74z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="537" 
d="M164 714v-512q0 -69 26.5 -99.5t78.5 -30.5q51 0 77.5 30.5t26.5 99.5v512h114v-503q0 -62 -15 -105t-43 -69.5t-68.5 -38.5t-91.5 -12t-92 12t-69 38.5t-43 69.5t-15 105v503h114zM218 906h108l89 -144h-91l-54 88l-57 -88h-86z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="537" 
d="M164 714v-512q0 -69 26.5 -99.5t78.5 -30.5q51 0 77.5 30.5t26.5 99.5v512h114v-503q0 -62 -15 -105t-43 -69.5t-68.5 -38.5t-91.5 -12t-92 12t-69 38.5t-43 69.5t-15 105v503h114zM138 888h96v-108h-96v108zM304 888h96v-108h-96v108z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="481" 
d="M180 279l-184 435h127l120 -308l123 308h119l-191 -435v-279h-114v279zM264 906h120l-106 -144h-74z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="500" 
d="M59 714h114v-136h112q51 0 88.5 -15.5t61.5 -43t35.5 -65.5t11.5 -84q0 -96 -54.5 -153.5t-154.5 -57.5h-100v-159h-114v714zM173 245h81q23 0 43.5 6.5t36.5 21t25 38t9 57.5q0 58 -27 91t-95 33h-73v-247z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="481" 
d="M209 80h27q51 0 75.5 36.5t24.5 103.5q0 143 -99 143h-26v80h19q10 0 23.5 4.5t26.5 15.5t22 30t9 48q0 45 -17.5 73t-62.5 28q-43 0 -61 -26.5t-18 -74.5v-541h-108v533q0 189 188 189q34 0 67.5 -7.5t60 -26t43 -50.5t16.5 -80q0 -58 -23 -96t-77 -52v-2
q39 -4 64 -21.5t40 -43.5t21 -57.5t6 -63.5q0 -50 -11 -91.5t-34 -72t-59 -47.5t-86 -17h-51v86z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="463" 
d="M303 284q-11 -8 -26.5 -14.5t-32 -12.5l-33 -11.5t-28.5 -11.5q-23 -11 -33.5 -34t-10.5 -53q0 -34 16.5 -57.5t50.5 -23.5q49 0 73 34t24 94v90zM405 112q0 -15 8 -26.5t20 -11.5t18 3v-70q-10 -5 -23.5 -9t-29.5 -4q-36 0 -62.5 16t-28.5 55h-2q-22 -40 -54 -59.5
t-80 -19.5q-66 0 -103 36.5t-37 114.5q0 45 9.5 73.5t26 47t39.5 29t50 19.5l84 23q29 8 46 21t17 48q0 37 -15.5 58.5t-58.5 21.5q-27 0 -43 -9t-25 -23t-11.5 -33t-2.5 -38h-102q0 43 9.5 76t31.5 55.5t58.5 34t89.5 11.5q36 0 67 -7.5t54 -23t36.5 -40.5t13.5 -61v-308z
M112 752h120l60 -144h-74z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="463" 
d="M303 284q-11 -8 -26.5 -14.5t-32 -12.5l-33 -11.5t-28.5 -11.5q-23 -11 -33.5 -34t-10.5 -53q0 -34 16.5 -57.5t50.5 -23.5q49 0 73 34t24 94v90zM405 112q0 -15 8 -26.5t20 -11.5t18 3v-70q-10 -5 -23.5 -9t-29.5 -4q-36 0 -62.5 16t-28.5 55h-2q-22 -40 -54 -59.5
t-80 -19.5q-66 0 -103 36.5t-37 114.5q0 45 9.5 73.5t26 47t39.5 29t50 19.5l84 23q29 8 46 21t17 48q0 37 -15.5 58.5t-58.5 21.5q-27 0 -43 -9t-25 -23t-11.5 -33t-2.5 -38h-102q0 43 9.5 76t31.5 55.5t58.5 34t89.5 11.5q36 0 67 -7.5t54 -23t36.5 -40.5t13.5 -61v-308z
M247 752h120l-106 -144h-74z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="463" 
d="M303 284q-11 -8 -26.5 -14.5t-32 -12.5l-33 -11.5t-28.5 -11.5q-23 -11 -33.5 -34t-10.5 -53q0 -34 16.5 -57.5t50.5 -23.5q49 0 73 34t24 94v90zM405 112q0 -15 8 -26.5t20 -11.5t18 3v-70q-10 -5 -23.5 -9t-29.5 -4q-36 0 -62.5 16t-28.5 55h-2q-22 -40 -54 -59.5
t-80 -19.5q-66 0 -103 36.5t-37 114.5q0 45 9.5 73.5t26 47t39.5 29t50 19.5l84 23q29 8 46 21t17 48q0 37 -15.5 58.5t-58.5 21.5q-27 0 -43 -9t-25 -23t-11.5 -33t-2.5 -38h-102q0 43 9.5 76t31.5 55.5t58.5 34t89.5 11.5q36 0 67 -7.5t54 -23t36.5 -40.5t13.5 -61v-308z
M178 752h108l89 -144h-91l-54 88l-57 -88h-86z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="463" 
d="M303 284q-11 -8 -26.5 -14.5t-32 -12.5l-33 -11.5t-28.5 -11.5q-23 -11 -33.5 -34t-10.5 -53q0 -34 16.5 -57.5t50.5 -23.5q49 0 73 34t24 94v90zM405 112q0 -15 8 -26.5t20 -11.5t18 3v-70q-10 -5 -23.5 -9t-29.5 -4q-36 0 -62.5 16t-28.5 55h-2q-22 -40 -54 -59.5
t-80 -19.5q-66 0 -103 36.5t-37 114.5q0 45 9.5 73.5t26 47t39.5 29t50 19.5l84 23q29 8 46 21t17 48q0 37 -15.5 58.5t-58.5 21.5q-27 0 -43 -9t-25 -23t-11.5 -33t-2.5 -38h-102q0 43 9.5 76t31.5 55.5t58.5 34t89.5 11.5q36 0 67 -7.5t54 -23t36.5 -40.5t13.5 -61v-308z
M380 742q0 -17 -5.5 -37.5t-16 -38.5t-27 -30t-37.5 -12q-17 0 -33.5 5.5t-32 12l-29 12t-25.5 5.5q-17 0 -25.5 -12.5t-8.5 -27.5h-62q0 16 5.5 37t17.5 39.5t31 31t47 12.5q12 0 25.5 -5.5l27.5 -11.5q14 -7 28.5 -12.5t27.5 -5.5q17 0 23.5 12t6.5 26h62z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="463" 
d="M303 284q-11 -8 -26.5 -14.5t-32 -12.5l-33 -11.5t-28.5 -11.5q-23 -11 -33.5 -34t-10.5 -53q0 -34 16.5 -57.5t50.5 -23.5q49 0 73 34t24 94v90zM405 112q0 -15 8 -26.5t20 -11.5t18 3v-70q-10 -5 -23.5 -9t-29.5 -4q-36 0 -62.5 16t-28.5 55h-2q-22 -40 -54 -59.5
t-80 -19.5q-66 0 -103 36.5t-37 114.5q0 45 9.5 73.5t26 47t39.5 29t50 19.5l84 23q29 8 46 21t17 48q0 37 -15.5 58.5t-58.5 21.5q-27 0 -43 -9t-25 -23t-11.5 -33t-2.5 -38h-102q0 43 9.5 76t31.5 55.5t58.5 34t89.5 11.5q36 0 67 -7.5t54 -23t36.5 -40.5t13.5 -61v-308z
M98 734h96v-108h-96v108zM264 734h96v-108h-96v108z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="463" 
d="M303 284q-11 -8 -26.5 -14.5t-32 -12.5l-33 -11.5t-28.5 -11.5q-23 -11 -33.5 -34t-10.5 -53q0 -34 16.5 -57.5t50.5 -23.5q49 0 73 34t24 94v90zM405 112q0 -15 8 -26.5t20 -11.5t18 3v-70q-10 -5 -23.5 -9t-29.5 -4q-36 0 -62.5 16t-28.5 55h-2q-22 -40 -54 -59.5
t-80 -19.5q-66 0 -103 36.5t-37 114.5q0 45 9.5 73.5t26 47t39.5 29t50 19.5l84 23q29 8 46 21t17 48q0 37 -15.5 58.5t-58.5 21.5q-27 0 -43 -9t-25 -23t-11.5 -33t-2.5 -38h-102q0 43 9.5 76t31.5 55.5t58.5 34t89.5 11.5q36 0 67 -7.5t54 -23t36.5 -40.5t13.5 -61v-308z
M123 696q0 45 30.5 74.5t75.5 29.5t75.5 -29.5t30.5 -74.5t-30.5 -74.5t-75.5 -29.5t-75.5 29.5t-30.5 74.5zM173 696q0 -23 16.5 -39.5t39.5 -16.5t39.5 16.5t16.5 39.5t-16.5 39.5t-39.5 16.5t-39.5 -16.5t-16.5 -39.5z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="704" 
d="M296 276q-4 -5 -13 -9.5t-20 -8.5t-23 -7.5l-22 -6.5q-27 -8 -43 -18.5t-24 -23.5t-10 -28t-2 -33q0 -29 16.5 -52t46.5 -23q49 0 71.5 31t22.5 94v85zM398 255v-38q0 -30 3.5 -57.5t12 -49.5t24 -35.5t39.5 -14.5q29 0 46 14t26 33.5t11.5 40t2.5 32.5h102
q0 -91 -50.5 -142.5t-138.5 -51.5q-40 1 -66 12t-42.5 26t-25 31.5t-13.5 28.5q-20 -48 -58.5 -73t-86.5 -25q-30 0 -57.5 8.5t-49 27t-34 47t-12.5 68.5q0 43 10 71t28 46.5t42 29t53 18.5l85 22q26 7 36 21t10 53q0 37 -14.5 58.5t-51.5 21.5q-27 0 -43 -9t-25 -23
t-11.5 -33t-2.5 -38h-102q0 43 9.5 76t31.5 55.5t58.5 34t89.5 11.5q33 0 56 -8t37.5 -19t22 -23t10.5 -19q20 33 54.5 50.5t77.5 18.5q51 0 84.5 -16.5t53.5 -47.5t28.5 -75.5t8.5 -100.5v-57h-269zM559 329v29q0 51 -17.5 85.5t-60.5 34.5q-24 0 -40 -13.5t-25.5 -33
t-13 -41.5t-3.5 -38v-23h160z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="444" 
d="M415 196q-5 -73 -26 -123t-74 -73q-32 -11 -69 -14l-32 -46q11 3 20.5 4t19.5 1q34 0 56 -22t22 -52q0 -42 -33 -67t-84 -27q-34 0 -58.5 5t-42.5 12l16 40q30 -13 69 -13q24 0 38.5 9.5t14.5 32.5q0 17 -13.5 27.5t-32.5 10.5q-11 0 -20 -2t-18 -6l-18 17l48 78
q-37 5 -65 18q-57 32 -79.5 99t-22.5 164q0 63 9.5 115t32 89.5t61 58t97.5 20.5q60 1 96.5 -19.5t56 -50.5t26 -64t6.5 -59h-108q0 19 -3 39.5t-11.5 37t-23.5 27t-39 9.5q-29 0 -47 -15t-28 -42t-13.5 -64t-3.5 -82t3.5 -82t13.5 -64t28 -42t47 -15q45 -1 63.5 34t18.5 96
h102z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="463" 
d="M318 329v29q0 51 -18.5 85.5t-64.5 34.5q-26 0 -43 -11.5t-27.5 -31t-15 -44.5t-4.5 -51v-11h173zM145 255v-31q0 -27 2.5 -56t12 -53t27.5 -39.5t48 -15.5q29 0 46 14t26 33.5t11.5 40t2.5 32.5h102q0 -91 -50.5 -142.5t-138.5 -51.5q-33 0 -68 8t-63.5 36t-47 83
t-18.5 148q0 53 7.5 105t29 93.5t60.5 67t102 25.5q56 0 93 -16.5t58.5 -47.5t30 -75.5t8.5 -100.5v-57h-281zM114 752h120l60 -144h-74z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="463" 
d="M318 329v29q0 51 -18.5 85.5t-64.5 34.5q-26 0 -43 -11.5t-27.5 -31t-15 -44.5t-4.5 -51v-11h173zM145 255v-31q0 -27 2.5 -56t12 -53t27.5 -39.5t48 -15.5q29 0 46 14t26 33.5t11.5 40t2.5 32.5h102q0 -91 -50.5 -142.5t-138.5 -51.5q-33 0 -68 8t-63.5 36t-47 83
t-18.5 148q0 53 7.5 105t29 93.5t60.5 67t102 25.5q56 0 93 -16.5t58.5 -47.5t30 -75.5t8.5 -100.5v-57h-281zM261 752h120l-106 -144h-74z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="463" 
d="M318 329v29q0 51 -18.5 85.5t-64.5 34.5q-26 0 -43 -11.5t-27.5 -31t-15 -44.5t-4.5 -51v-11h173zM145 255v-31q0 -27 2.5 -56t12 -53t27.5 -39.5t48 -15.5q29 0 46 14t26 33.5t11.5 40t2.5 32.5h102q0 -91 -50.5 -142.5t-138.5 -51.5q-33 0 -68 8t-63.5 36t-47 83
t-18.5 148q0 53 7.5 105t29 93.5t60.5 67t102 25.5q56 0 93 -16.5t58.5 -47.5t30 -75.5t8.5 -100.5v-57h-281zM187 752h108l89 -144h-91l-54 88l-57 -88h-86z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="463" 
d="M318 329v29q0 51 -18.5 85.5t-64.5 34.5q-26 0 -43 -11.5t-27.5 -31t-15 -44.5t-4.5 -51v-11h173zM145 255v-31q0 -27 2.5 -56t12 -53t27.5 -39.5t48 -15.5q29 0 46 14t26 33.5t11.5 40t2.5 32.5h102q0 -91 -50.5 -142.5t-138.5 -51.5q-33 0 -68 8t-63.5 36t-47 83
t-18.5 148q0 53 7.5 105t29 93.5t60.5 67t102 25.5q56 0 93 -16.5t58.5 -47.5t30 -75.5t8.5 -100.5v-57h-281zM109 734h96v-108h-96v108zM275 734h96v-108h-96v108z" />
    <glyph glyph-name="igrave" unicode="&#xec;" 
d="M57 538h108v-538h-108v538zM-18 752h120l60 -144h-74z" />
    <glyph glyph-name="iacute" unicode="&#xed;" 
d="M57 538h108v-538h-108v538zM118 752h120l-106 -144h-74z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" 
d="M57 538h108v-538h-108v538zM58 752h108l89 -144h-91l-54 88l-57 -88h-86z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" 
d="M57 538h108v-538h-108v538zM-20 734h96v-108h-96v108zM146 734h96v-108h-96v108z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="463" 
d="M58 634l86 47q-17 16 -33 29t-31 23l79 43q16 -10 32.5 -23.5l33.5 -27.5l91 49l32 -36l-86 -47q49 -47 82 -92t52.5 -94.5t27.5 -106.5t8 -129q0 -63 -10.5 -115t-34 -89.5t-62 -58t-94.5 -20.5t-94 20.5t-61.5 58t-34 89.5t-10.5 115q0 60 10 109.5t32.5 85t57 55
t84.5 19.5q52 0 82 -33l2 2q-20 29 -51.5 66.5t-66.5 72.5l-87 -48zM139 269q0 -51 3.5 -89.5t13.5 -63.5t28 -37.5t47 -12.5q30 0 48 12.5t28 37.5t13.5 63.5t3.5 89.5q0 39 -3.5 73.5t-13.5 60t-28 40.5t-48 15q-29 0 -47 -15t-28 -40.5t-13.5 -60t-3.5 -73.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="481" 
d="M52 538h102v-63h3q20 36 56.5 56.5t81.5 20.5q31 0 55.5 -7.5t42 -24.5t27 -45t9.5 -69v-406h-108v369q0 54 -16.5 75.5t-58.5 21.5q-33 0 -59.5 -25.5t-26.5 -79.5v-361h-108v538zM391 742q0 -17 -5.5 -37.5t-16 -38.5t-27 -30t-37.5 -12q-17 0 -33.5 5.5t-32 12l-29 12
t-25.5 5.5q-17 0 -25.5 -12.5t-8.5 -27.5h-62q0 16 5.5 37t17.5 39.5t31 31t47 12.5q12 0 25.5 -5.5l27.5 -11.5q14 -7 28.5 -12.5t27.5 -5.5q17 0 23.5 12t6.5 26h62z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="463" 
d="M139 269q0 -51 3.5 -89.5t13.5 -63.5t28 -37.5t47 -12.5q30 0 48 12.5t28 37.5t13.5 63.5t3.5 89.5q0 45 -3.5 82t-13.5 64t-28 42t-48 15q-29 0 -47 -15t-28 -42t-13.5 -64t-3.5 -82zM231 -14q-56 0 -94 20.5t-61.5 58t-34 89.5t-10.5 115t9.5 115t32 89.5t61 58
t97.5 20.5q58 0 97 -20.5t62 -58t32.5 -89.5t9.5 -115t-10.5 -115t-34 -89.5t-62 -58t-94.5 -20.5zM97 752h120l60 -144h-74z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="463" 
d="M139 269q0 -51 3.5 -89.5t13.5 -63.5t28 -37.5t47 -12.5q30 0 48 12.5t28 37.5t13.5 63.5t3.5 89.5q0 45 -3.5 82t-13.5 64t-28 42t-48 15q-29 0 -47 -15t-28 -42t-13.5 -64t-3.5 -82zM231 -14q-56 0 -94 20.5t-61.5 58t-34 89.5t-10.5 115t9.5 115t32 89.5t61 58
t97.5 20.5q58 0 97 -20.5t62 -58t32.5 -89.5t9.5 -115t-10.5 -115t-34 -89.5t-62 -58t-94.5 -20.5zM250 752h120l-106 -144h-74z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="463" 
d="M139 269q0 -51 3.5 -89.5t13.5 -63.5t28 -37.5t47 -12.5q30 0 48 12.5t28 37.5t13.5 63.5t3.5 89.5q0 45 -3.5 82t-13.5 64t-28 42t-48 15q-29 0 -47 -15t-28 -42t-13.5 -64t-3.5 -82zM231 -14q-56 0 -94 20.5t-61.5 58t-34 89.5t-10.5 115t9.5 115t32 89.5t61 58
t97.5 20.5q58 0 97 -20.5t62 -58t32.5 -89.5t9.5 -115t-10.5 -115t-34 -89.5t-62 -58t-94.5 -20.5zM178 752h108l89 -144h-91l-54 88l-57 -88h-86z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="463" 
d="M139 269q0 -51 3.5 -89.5t13.5 -63.5t28 -37.5t47 -12.5q30 0 48 12.5t28 37.5t13.5 63.5t3.5 89.5q0 45 -3.5 82t-13.5 64t-28 42t-48 15q-29 0 -47 -15t-28 -42t-13.5 -64t-3.5 -82zM231 -14q-56 0 -94 20.5t-61.5 58t-34 89.5t-10.5 115t9.5 115t32 89.5t61 58
t97.5 20.5q58 0 97 -20.5t62 -58t32.5 -89.5t9.5 -115t-10.5 -115t-34 -89.5t-62 -58t-94.5 -20.5zM382 742q0 -17 -5.5 -37.5t-16 -38.5t-27 -30t-37.5 -12q-17 0 -33.5 5.5t-32 12l-29 12t-25.5 5.5q-17 0 -25.5 -12.5t-8.5 -27.5h-62q0 16 5.5 37t17.5 39.5t31 31
t47 12.5q12 0 25.5 -5.5l27.5 -11.5q14 -7 28.5 -12.5t27.5 -5.5q17 0 23.5 12t6.5 26h62z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="463" 
d="M139 269q0 -51 3.5 -89.5t13.5 -63.5t28 -37.5t47 -12.5q30 0 48 12.5t28 37.5t13.5 63.5t3.5 89.5q0 45 -3.5 82t-13.5 64t-28 42t-48 15q-29 0 -47 -15t-28 -42t-13.5 -64t-3.5 -82zM231 -14q-56 0 -94 20.5t-61.5 58t-34 89.5t-10.5 115t9.5 115t32 89.5t61 58
t97.5 20.5q58 0 97 -20.5t62 -58t32.5 -89.5t9.5 -115t-10.5 -115t-34 -89.5t-62 -58t-94.5 -20.5zM100 734h96v-108h-96v108zM266 734h96v-108h-96v108z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="600" 
d="M228 458q0 30 21 51t51 21t51 -21t21 -51t-21 -51t-51 -21t-51 21t-21 51zM47 296h506v-86h-506v86zM228 48q0 30 21 51t51 21t51 -21t21 -51t-21 -51t-51 -21t-51 21t-21 51z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="463" 
d="M94 38q-35 35 -49 95t-14 136q0 63 9.5 115t32 89.5t61 58t97.5 20.5q55 0 93 -20l30 65l48 -26l-33 -70q35 -35 49 -94.5t14 -137.5q0 -63 -9.5 -115t-32.5 -89.5t-62 -58t-97 -20.5q-56 0 -93 19l-31 -67l-48 26zM177 88q19 -22 54 -22q30 0 48 15t28 42t13.5 64
t3.5 82q0 34 -1.5 63t-6.5 54h-2zM286 450q-22 22 -55 22q-29 0 -47 -15t-28 -42t-13.5 -64t-3.5 -82q0 -33 1.5 -62t5.5 -54h2z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="481" 
d="M327 63h-3q-20 -36 -56.5 -56.5t-81.5 -20.5q-30 0 -54.5 8t-42.5 28t-27.5 54t-9.5 86v376h108v-386q0 -45 19 -62.5t54 -17.5t61.5 22.5t26.5 67.5v376h108v-538h-102v63zM102 752h120l60 -144h-74z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="481" 
d="M327 63h-3q-20 -36 -56.5 -56.5t-81.5 -20.5q-30 0 -54.5 8t-42.5 28t-27.5 54t-9.5 86v376h108v-386q0 -45 19 -62.5t54 -17.5t61.5 22.5t26.5 67.5v376h108v-538h-102v63zM261 752h120l-106 -144h-74z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="481" 
d="M327 63h-3q-20 -36 -56.5 -56.5t-81.5 -20.5q-30 0 -54.5 8t-42.5 28t-27.5 54t-9.5 86v376h108v-386q0 -45 19 -62.5t54 -17.5t61.5 22.5t26.5 67.5v376h108v-538h-102v63zM189 752h108l89 -144h-91l-54 88l-57 -88h-86z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="481" 
d="M327 63h-3q-20 -36 -56.5 -56.5t-81.5 -20.5q-30 0 -54.5 8t-42.5 28t-27.5 54t-9.5 86v376h108v-386q0 -45 19 -62.5t54 -17.5t61.5 22.5t26.5 67.5v376h108v-538h-102v63zM111 734h96v-108h-96v108zM277 734h96v-108h-96v108z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="426" 
d="M124 538l93 -404h2l86 404h113l-148 -558q-16 -55 -31.5 -88t-36.5 -50.5t-49.5 -22t-70.5 -3.5q-11 0 -22 1t-21 3v86q15 -4 35 -4q28 0 46 8t27 34l14 44l-153 550h116zM236 752h120l-106 -144h-74z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="481" 
d="M154 275q0 -48 3.5 -86t14 -64t28 -39.5t46.5 -13.5q23 0 40 9.5t28 32t16.5 60t5.5 93.5q0 46 -3.5 82.5t-13.5 62.5t-28 40t-46 14t-45.5 -13.5t-27.5 -38.5t-14 -60.5t-4 -78.5zM52 714h108v-239h2q15 36 46 56.5t76 20.5q87 0 126.5 -75.5t39.5 -214.5
q0 -85 -15.5 -138.5t-39.5 -84t-53.5 -42t-57.5 -11.5q-45 0 -76 20.5t-46 56.5h-2v-239h-108v890z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="426" 
d="M124 538l93 -404h2l86 404h113l-148 -558q-16 -55 -31.5 -88t-36.5 -50.5t-49.5 -22t-70.5 -3.5q-11 0 -22 1t-21 3v86q15 -4 35 -4q28 0 46 8t27 34l14 44l-153 550h116zM85 734h96v-108h-96v108zM251 734h96v-108h-96v108z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="519" 
d="M194 714h146l188 -714h-119l-44 175h-209l-46 -175h-119zM346 266l-82 352h-2l-85 -352h169zM404 865v-62h-282v62h282z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="463" 
d="M303 284q-11 -8 -26.5 -14.5t-32 -12.5l-33 -11.5t-28.5 -11.5q-23 -11 -33.5 -34t-10.5 -53q0 -34 16.5 -57.5t50.5 -23.5q49 0 73 34t24 94v90zM405 112q0 -15 8 -26.5t20 -11.5t18 3v-70q-10 -5 -23.5 -9t-29.5 -4q-36 0 -62.5 16t-28.5 55h-2q-22 -40 -54 -59.5
t-80 -19.5q-66 0 -103 36.5t-37 114.5q0 45 9.5 73.5t26 47t39.5 29t50 19.5l84 23q29 8 46 21t17 48q0 37 -15.5 58.5t-58.5 21.5q-27 0 -43 -9t-25 -23t-11.5 -33t-2.5 -38h-102q0 43 9.5 76t31.5 55.5t58.5 34t89.5 11.5q36 0 67 -7.5t54 -23t36.5 -40.5t13.5 -61v-308z
M375 711v-62h-282v62h282z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="519" 
d="M194 714h146l188 -714h-119l-44 175h-209l-46 -175h-119zM346 266l-82 352h-2l-85 -352h169zM194 902q0 -9 4.5 -20.5t13.5 -22t22 -17.5t31 -7t31 7t22 17.5t13.5 22t4.5 20.5h62q0 -28 -9 -52.5t-26 -43t-42 -29t-56 -10.5q-32 0 -56.5 10.5t-41.5 29t-26 43t-9 52.5
h62z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="463" 
d="M303 284q-11 -8 -26.5 -14.5t-32 -12.5l-33 -11.5t-28.5 -11.5q-23 -11 -33.5 -34t-10.5 -53q0 -34 16.5 -57.5t50.5 -23.5q49 0 73 34t24 94v90zM405 112q0 -15 8 -26.5t20 -11.5t18 3v-70q-10 -5 -23.5 -9t-29.5 -4q-36 0 -62.5 16t-28.5 55h-2q-22 -40 -54 -59.5
t-80 -19.5q-66 0 -103 36.5t-37 114.5q0 45 9.5 73.5t26 47t39.5 29t50 19.5l84 23q29 8 46 21t17 48q0 37 -15.5 58.5t-58.5 21.5q-27 0 -43 -9t-25 -23t-11.5 -33t-2.5 -38h-102q0 43 9.5 76t31.5 55.5t58.5 34t89.5 11.5q36 0 67 -7.5t54 -23t36.5 -40.5t13.5 -61v-308z
M159 748q0 -9 4.5 -20.5t13.5 -22t22 -17.5t31 -7t31 7t22 17.5t13.5 22t4.5 20.5h62q0 -28 -9 -52.5t-26 -43t-42 -29t-56 -10.5q-32 0 -56.5 10.5t-41.5 29t-26 43t-9 52.5h62z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="519" 
d="M459 0q-39 -38 -53.5 -64.5t-14.5 -55.5q0 -28 15.5 -42.5t41.5 -14.5q9 0 21.5 2t22.5 5l6 -42q-17 -5 -38 -8t-40 -3q-48 0 -78.5 21.5t-30.5 69.5q0 37 28 72.5t70 59.5l-44 175h-209l-46 -175h-119l203 714h146l188 -714h-69zM346 266l-82 352h-2l-85 -352h169z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="463" 
d="M405 112q0 -15 8 -26.5t20 -11.5t18 3v-70q-11 -5 -24 -9q-4 -1 -9.5 -2t-12.5 -2q-36 -38 -47 -58q-14 -30 -14 -56q0 -28 15.5 -42.5t41.5 -14.5q9 0 21.5 2t22.5 5l6 -42q-17 -5 -38 -8t-40 -3q-48 0 -78.5 21.5t-30.5 69.5q0 39 28 73q29 36 68 58q-7 2 -13 4.5
t-12 6.5q-26 16 -28 55h-2q-22 -40 -54 -59.5t-80 -19.5q-66 0 -103 36.5t-37 114.5q0 45 9.5 73.5t26 47t39.5 29t50 19.5l84 23q29 8 46 21t17 48q0 37 -15.5 58.5t-58.5 21.5q-27 0 -43 -9t-25 -23t-11.5 -33t-2.5 -38h-102q0 43 9.5 76t31.5 55.5t58.5 34t89.5 11.5
q36 0 67 -7.5t54 -23t36.5 -40.5t13.5 -61v-308zM303 284q-11 -8 -26.5 -14.5t-32 -12.5l-33 -11.5t-28.5 -11.5q-23 -11 -33.5 -34t-10.5 -53q0 -34 16.5 -57.5t50.5 -23.5q49 0 73 34t24 94v90z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="519" 
d="M498 256v-15q0 -48 -11 -94t-36.5 -82t-67.5 -57.5t-105 -21.5q-68 0 -113 22t-72.5 68t-39 115.5t-11.5 165.5t11.5 165.5t39 115.5t73 68t113.5 22q76 0 119 -24.5t64.5 -59.5t26.5 -73.5t5 -66.5v-13h-114v12q0 25 -4.5 50.5t-16 45.5t-31 32t-50.5 11
q-32 0 -55 -11.5t-38 -43t-22 -86.5t-7 -141t7 -142t22 -88.5t38 -45t55 -12.5q36 0 57 17.5t32 43.5t14 56t3 55v12h114zM302 906h120l-106 -144h-74z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="444" 
d="M415 196q-3 -48 -13.5 -87t-31.5 -66.5t-55 -42t-84 -14.5q-59 0 -97.5 20.5t-61 58t-32 89.5t-9.5 115t9.5 115t32 89.5t61 58t97.5 20.5q60 1 96.5 -19.5t56 -50.5t26 -64t6.5 -59h-108q0 19 -3 39.5t-11.5 37t-23.5 27t-39 9.5q-29 0 -47 -15t-28 -42t-13.5 -64
t-3.5 -82t3.5 -82t13.5 -64t28 -42t47 -15q45 -1 63.5 34t18.5 96h102zM249 752h120l-106 -144h-74z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="519" 
d="M498 256v-15q0 -48 -11 -94t-36.5 -82t-67.5 -57.5t-105 -21.5q-68 0 -113 22t-72.5 68t-39 115.5t-11.5 165.5t11.5 165.5t39 115.5t73 68t113.5 22q76 0 119 -24.5t64.5 -59.5t26.5 -73.5t5 -66.5v-13h-114v12q0 25 -4.5 50.5t-16 45.5t-31 32t-50.5 11
q-32 0 -55 -11.5t-38 -43t-22 -86.5t-7 -141t7 -142t22 -88.5t38 -45t55 -12.5q36 0 57 17.5t32 43.5t14 56t3 55v12h114zM228 906h108l89 -144h-91l-54 88l-57 -88h-86z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="444" 
d="M415 196q-3 -48 -13.5 -87t-31.5 -66.5t-55 -42t-84 -14.5q-59 0 -97.5 20.5t-61 58t-32 89.5t-9.5 115t9.5 115t32 89.5t61 58t97.5 20.5q60 1 96.5 -19.5t56 -50.5t26 -64t6.5 -59h-108q0 19 -3 39.5t-11.5 37t-23.5 27t-39 9.5q-29 0 -47 -15t-28 -42t-13.5 -64
t-3.5 -82t3.5 -82t13.5 -64t28 -42t47 -15q45 -1 63.5 34t18.5 96h102zM179 752h108l89 -144h-91l-54 88l-57 -88h-86z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="519" 
d="M498 256v-15q0 -48 -11 -94t-36.5 -82t-67.5 -57.5t-105 -21.5q-68 0 -113 22t-72.5 68t-39 115.5t-11.5 165.5t11.5 165.5t39 115.5t73 68t113.5 22q76 0 119 -24.5t64.5 -59.5t26.5 -73.5t5 -66.5v-13h-114v12q0 25 -4.5 50.5t-16 45.5t-31 32t-50.5 11
q-32 0 -55 -11.5t-38 -43t-22 -86.5t-7 -141t7 -142t22 -88.5t38 -45t55 -12.5q36 0 57 17.5t32 43.5t14 56t3 55v12h114zM232 888h96v-108h-96v108z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="444" 
d="M415 196q-3 -48 -13.5 -87t-31.5 -66.5t-55 -42t-84 -14.5q-59 0 -97.5 20.5t-61 58t-32 89.5t-9.5 115t9.5 115t32 89.5t61 58t97.5 20.5q60 1 96.5 -19.5t56 -50.5t26 -64t6.5 -59h-108q0 19 -3 39.5t-11.5 37t-23.5 27t-39 9.5q-29 0 -47 -15t-28 -42t-13.5 -64
t-3.5 -82t3.5 -82t13.5 -64t28 -42t47 -15q45 -1 63.5 34t18.5 96h102zM183 734h96v-108h-96v108z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="519" 
d="M498 256v-15q0 -48 -11 -94t-36.5 -82t-67.5 -57.5t-105 -21.5q-68 0 -113 22t-72.5 68t-39 115.5t-11.5 165.5t11.5 165.5t39 115.5t73 68t113.5 22q76 0 119 -24.5t64.5 -59.5t26.5 -73.5t5 -66.5v-13h-114v12q0 25 -4.5 50.5t-16 45.5t-31 32t-50.5 11
q-32 0 -55 -11.5t-38 -43t-22 -86.5t-7 -141t7 -142t22 -88.5t38 -45t55 -12.5q36 0 57 17.5t32 43.5t14 56t3 55v12h114zM137 906h91l54 -88l57 88h86l-91 -144h-108z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="444" 
d="M415 196q-3 -48 -13.5 -87t-31.5 -66.5t-55 -42t-84 -14.5q-59 0 -97.5 20.5t-61 58t-32 89.5t-9.5 115t9.5 115t32 89.5t61 58t97.5 20.5q60 1 96.5 -19.5t56 -50.5t26 -64t6.5 -59h-108q0 19 -3 39.5t-11.5 37t-23.5 27t-39 9.5q-29 0 -47 -15t-28 -42t-13.5 -64
t-3.5 -82t3.5 -82t13.5 -64t28 -42t47 -15q45 -1 63.5 34t18.5 96h102zM88 752h91l54 -88l57 88h86l-91 -144h-108z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="556" 
d="M59 714h203q84 0 134 -26t76 -73.5t34 -113t8 -144.5t-8 -144.5t-34 -113t-76 -73.5t-134 -26h-203v714zM173 86h94q35 0 60.5 13t41.5 44t23.5 83.5t7.5 130.5q0 75 -7 127t-22.5 84t-41 46t-62.5 14h-94v-542zM104 906h91l54 -88l57 88h86l-91 -144h-108z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="573" 
d="M327 263q0 48 -3.5 86t-14 64t-30 39.5t-50.5 13.5q-23 0 -39 -10t-26 -33t-14.5 -60.5t-4.5 -91.5q0 -46 3.5 -82.5t13.5 -62.5t27.5 -40t46.5 -14q28 0 45.5 13.5t27.5 38.5t14 60t4 79zM327 66h-2q-5 -13 -13.5 -27t-22 -26t-34 -19.5t-48.5 -7.5q-94 0 -135 76
t-41 214q0 90 14.5 144t38 83.5t53 39t60.5 9.5q45 0 76 -20.5t46 -56.5h2v239h108v-714h-102v66zM500 714h105l-63 -144h-70z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="556" 
d="M267 86q35 0 60.5 13t41.5 44t23.5 83.5t7.5 130.5q0 75 -7 127t-22.5 84t-41 46t-62.5 14h-94v-215h119v-80h-119v-247h94zM59 333h-59v80h59v301h203q84 0 134 -26t76 -73.5t34 -113t8 -144.5t-8 -144.5t-34 -113t-76 -73.5t-134 -26h-203v333z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="505" 
d="M327 66h-2q-5 -13 -13.5 -27t-22 -26t-34 -19.5t-48.5 -7.5q-94 0 -135 76t-41 214q0 90 14.5 144t38 83.5t53 39t60.5 9.5q45 0 76 -20.5t46 -56.5h2v114h-100v52h100v73h108v-73h84v-52h-84v-589h-102v66zM327 263q0 48 -3.5 86t-14 64t-30 39.5t-50.5 13.5
q-23 0 -39 -10t-26 -33t-14.5 -60.5t-4.5 -91.5q0 -46 3.5 -82.5t13.5 -62.5t27.5 -40t46.5 -14q28 0 45.5 13.5t27.5 38.5t14 60t4 79z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="481" 
d="M59 714h386v-96h-272v-198h256v-96h-256v-228h280v-96h-394v714zM393 865v-62h-282v62h282z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="463" 
d="M318 329v29q0 51 -18.5 85.5t-64.5 34.5q-26 0 -43 -11.5t-27.5 -31t-15 -44.5t-4.5 -51v-11h173zM145 255v-31q0 -27 2.5 -56t12 -53t27.5 -39.5t48 -15.5q29 0 46 14t26 33.5t11.5 40t2.5 32.5h102q0 -91 -50.5 -142.5t-138.5 -51.5q-33 0 -68 8t-63.5 36t-47 83
t-18.5 148q0 53 7.5 105t29 93.5t60.5 67t102 25.5q56 0 93 -16.5t58.5 -47.5t30 -75.5t8.5 -100.5v-57h-281zM381 711v-62h-282v62h282z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="481" 
d="M59 714h386v-96h-272v-198h256v-96h-256v-228h280v-96h-394v714zM180 902q0 -9 4.5 -20.5t13.5 -22t22 -17.5t31 -7t31 7t22 17.5t13.5 22t4.5 20.5h62q0 -28 -9 -52.5t-26 -43t-42 -29t-56 -10.5q-32 0 -56.5 10.5t-41.5 29t-26 43t-9 52.5h62z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="463" 
d="M318 329v29q0 51 -18.5 85.5t-64.5 34.5q-26 0 -43 -11.5t-27.5 -31t-15 -44.5t-4.5 -51v-11h173zM145 255v-31q0 -27 2.5 -56t12 -53t27.5 -39.5t48 -15.5q29 0 46 14t26 33.5t11.5 40t2.5 32.5h102q0 -91 -50.5 -142.5t-138.5 -51.5q-33 0 -68 8t-63.5 36t-47 83
t-18.5 148q0 53 7.5 105t29 93.5t60.5 67t102 25.5q56 0 93 -16.5t58.5 -47.5t30 -75.5t8.5 -100.5v-57h-281zM169 748q0 -9 4.5 -20.5t13.5 -22t22 -17.5t31 -7t31 7t22 17.5t13.5 22t4.5 20.5h62q0 -28 -9 -52.5t-26 -43t-42 -29t-56 -10.5q-32 0 -56.5 10.5t-41.5 29
t-26 43t-9 52.5h62z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="481" 
d="M59 714h386v-96h-272v-198h256v-96h-256v-228h280v-96h-394v714zM207 888h96v-108h-96v108z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="463" 
d="M318 329v29q0 51 -18.5 85.5t-64.5 34.5q-26 0 -43 -11.5t-27.5 -31t-15 -44.5t-4.5 -51v-11h173zM145 255v-31q0 -27 2.5 -56t12 -53t27.5 -39.5t48 -15.5q29 0 46 14t26 33.5t11.5 40t2.5 32.5h102q0 -91 -50.5 -142.5t-138.5 -51.5q-33 0 -68 8t-63.5 36t-47 83
t-18.5 148q0 53 7.5 105t29 93.5t60.5 67t102 25.5q56 0 93 -16.5t58.5 -47.5t30 -75.5t8.5 -100.5v-57h-281zM192 734h96v-108h-96v108z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="481" 
d="M414 0q-39 -38 -53.5 -64.5t-14.5 -55.5q0 -28 15.5 -42.5t41.5 -14.5q9 0 21.5 2t22.5 5l6 -42q-17 -5 -38 -8t-40 -3q-48 0 -78.5 21.5t-30.5 69.5q0 37 28 72.5t70 59.5h-305v714h386v-96h-272v-198h256v-96h-256v-228h280v-96h-39z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="463" 
d="M145 255v-31q0 -27 2.5 -56t12 -53t27.5 -39.5t48 -15.5q29 0 46 14t26 33.5t11.5 40t2.5 32.5h102q0 -91 -51 -143q-40 -40 -103 -49q-32 -32 -42 -52q-14 -30 -14 -56q0 -28 15.5 -42.5t41.5 -14.5q9 0 21.5 2t22.5 5l6 -42q-17 -5 -38 -8t-40 -3q-48 0 -78.5 21.5
t-30.5 69.5q0 39 28 73q20 26 49 46q-11 1 -22 3l-22 4q-26 6 -49 22.5t-41 48t-28.5 79.5t-10.5 117q0 53 7.5 105t29 93.5t60.5 67t102 25.5q56 0 93 -16.5t58.5 -47.5t30 -75.5t8.5 -100.5v-57h-281zM318 329v29q0 51 -18.5 85.5t-64.5 34.5q-26 0 -43 -11.5t-27.5 -31
t-15 -44.5t-4.5 -51v-11h173z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="481" 
d="M59 714h386v-96h-272v-198h256v-96h-256v-228h280v-96h-394v714zM110 906h91l54 -88l57 88h86l-91 -144h-108z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="463" 
d="M318 329v29q0 51 -18.5 85.5t-64.5 34.5q-26 0 -43 -11.5t-27.5 -31t-15 -44.5t-4.5 -51v-11h173zM145 255v-31q0 -27 2.5 -56t12 -53t27.5 -39.5t48 -15.5q29 0 46 14t26 33.5t11.5 40t2.5 32.5h102q0 -91 -50.5 -142.5t-138.5 -51.5q-33 0 -68 8t-63.5 36t-47 83
t-18.5 148q0 53 7.5 105t29 93.5t60.5 67t102 25.5q56 0 93 -16.5t58.5 -47.5t30 -75.5t8.5 -100.5v-57h-281zM94 752h91l54 -88l57 88h86l-91 -144h-108z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="537" 
d="M412 83h-2q-23 -51 -58 -74t-98 -23q-59 0 -99 22t-65.5 68t-36.5 115.5t-11 165.5q0 191 56.5 281t180.5 90q67 0 108 -23t63.5 -55.5t30 -68t7.5 -59.5v-13h-108v17q0 20 -4.5 40.5t-16 37.5t-31 27.5t-48.5 10.5q-30 0 -53.5 -12t-39 -43.5t-23.5 -86.5t-8 -140
q0 -86 7 -142t21.5 -88.5t36 -45t50.5 -12.5q65 -1 92 50t27 168h-120v86h228v-376h-86v83zM228 906h108l89 -144h-91l-54 88l-57 -88h-86z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="481" 
d="M152 286q0 -35 2 -69.5t10 -63t25 -46t47 -17.5q31 0 49.5 18t28.5 45.5t12.5 61.5t2.5 66q0 33 -3 66t-12 59.5t-26 43t-45 16.5q-29 0 -47 -14t-28 -38.5t-13 -57.5t-3 -70zM160 -46q0 -25 21 -44.5t57 -19.5q42 0 66.5 26.5t24.5 74.5v87h-2q-15 -35 -49 -54.5
t-73 -19.5q-47 0 -78 21.5t-49.5 58.5t-26 86t-7.5 105q0 44 5 93t23 90t52 67.5t91 26.5q24 0 44 -7t35 -18.5t25 -25.5t14 -29h2v66h102v-512q0 -49 -11 -88t-35 -66t-62.5 -41.5t-92.5 -14.5q-60 0 -95.5 15.5t-53.5 37t-23.5 45t-5.5 40.5h102zM207 752h108l89 -144h-91
l-54 88l-57 -88h-86z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="537" 
d="M412 83h-2q-23 -51 -58 -74t-98 -23q-59 0 -99 22t-65.5 68t-36.5 115.5t-11 165.5q0 191 56.5 281t180.5 90q67 0 108 -23t63.5 -55.5t30 -68t7.5 -59.5v-13h-108v17q0 20 -4.5 40.5t-16 37.5t-31 27.5t-48.5 10.5q-30 0 -53.5 -12t-39 -43.5t-23.5 -86.5t-8 -140
q0 -86 7 -142t21.5 -88.5t36 -45t50.5 -12.5q65 -1 92 50t27 168h-120v86h228v-376h-86v83zM213 902q0 -9 4.5 -20.5t13.5 -22t22 -17.5t31 -7t31 7t22 17.5t13.5 22t4.5 20.5h62q0 -28 -9 -52.5t-26 -43t-42 -29t-56 -10.5q-32 0 -56.5 10.5t-41.5 29t-26 43t-9 52.5h62z
" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="481" 
d="M152 286q0 -35 2 -69.5t10 -63t25 -46t47 -17.5q31 0 49.5 18t28.5 45.5t12.5 61.5t2.5 66q0 33 -3 66t-12 59.5t-26 43t-45 16.5q-29 0 -47 -14t-28 -38.5t-13 -57.5t-3 -70zM160 -46q0 -25 21 -44.5t57 -19.5q42 0 66.5 26.5t24.5 74.5v87h-2q-15 -35 -49 -54.5
t-73 -19.5q-47 0 -78 21.5t-49.5 58.5t-26 86t-7.5 105q0 44 5 93t23 90t52 67.5t91 26.5q24 0 44 -7t35 -18.5t25 -25.5t14 -29h2v66h102v-512q0 -49 -11 -88t-35 -66t-62.5 -41.5t-92.5 -14.5q-60 0 -95.5 15.5t-53.5 37t-23.5 45t-5.5 40.5h102zM190 748q0 -9 4.5 -20.5
t13.5 -22t22 -17.5t31 -7t31 7t22 17.5t13.5 22t4.5 20.5h62q0 -28 -9 -52.5t-26 -43t-42 -29t-56 -10.5q-32 0 -56.5 10.5t-41.5 29t-26 43t-9 52.5h62z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="537" 
d="M412 83h-2q-23 -51 -58 -74t-98 -23q-59 0 -99 22t-65.5 68t-36.5 115.5t-11 165.5q0 191 56.5 281t180.5 90q67 0 108 -23t63.5 -55.5t30 -68t7.5 -59.5v-13h-108v17q0 20 -4.5 40.5t-16 37.5t-31 27.5t-48.5 10.5q-30 0 -53.5 -12t-39 -43.5t-23.5 -86.5t-8 -140
q0 -86 7 -142t21.5 -88.5t36 -45t50.5 -12.5q65 -1 92 50t27 168h-120v86h228v-376h-86v83zM234 888h96v-108h-96v108z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="481" 
d="M152 286q0 -35 2 -69.5t10 -63t25 -46t47 -17.5q31 0 49.5 18t28.5 45.5t12.5 61.5t2.5 66q0 33 -3 66t-12 59.5t-26 43t-45 16.5q-29 0 -47 -14t-28 -38.5t-13 -57.5t-3 -70zM160 -46q0 -25 21 -44.5t57 -19.5q42 0 66.5 26.5t24.5 74.5v87h-2q-15 -35 -49 -54.5
t-73 -19.5q-47 0 -78 21.5t-49.5 58.5t-26 86t-7.5 105q0 44 5 93t23 90t52 67.5t91 26.5q24 0 44 -7t35 -18.5t25 -25.5t14 -29h2v66h102v-512q0 -49 -11 -88t-35 -66t-62.5 -41.5t-92.5 -14.5q-60 0 -95.5 15.5t-53.5 37t-23.5 45t-5.5 40.5h102zM200 734h96v-108h-96v108
z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="537" 
d="M412 83h-2q-23 -51 -58 -74t-98 -23q-59 0 -99 22t-65.5 68t-36.5 115.5t-11 165.5q0 191 56.5 281t180.5 90q67 0 108 -23t63.5 -55.5t30 -68t7.5 -59.5v-13h-108v17q0 20 -4.5 40.5t-16 37.5t-31 27.5t-48.5 10.5q-30 0 -53.5 -12t-39 -43.5t-23.5 -86.5t-8 -140
q0 -86 7 -142t21.5 -88.5t36 -45t50.5 -12.5q65 -1 92 50t27 168h-120v86h228v-376h-86v83zM219 -50h96v-86q0 -45 -23 -63.5t-63 -23.5v33q15 4 24 12.5t11 28.5h-45v99z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="481" 
d="M152 286q0 -35 2 -69.5t10 -63t25 -46t47 -17.5q31 0 49.5 18t28.5 45.5t12.5 61.5t2.5 66q0 33 -3 66t-12 59.5t-26 43t-45 16.5q-29 0 -47 -14t-28 -38.5t-13 -57.5t-3 -70zM160 -46q0 -25 21 -44.5t57 -19.5q42 0 66.5 26.5t24.5 74.5v87h-2q-15 -35 -49 -54.5
t-73 -19.5q-47 0 -78 21.5t-49.5 58.5t-26 86t-7.5 105q0 44 5 93t23 90t52 67.5t91 26.5q24 0 44 -7t35 -18.5t25 -25.5t14 -29h2v66h102v-512q0 -49 -11 -88t-35 -66t-62.5 -41.5t-92.5 -14.5q-60 0 -95.5 15.5t-53.5 37t-23.5 45t-5.5 40.5h102zM297 615h-96v86
q0 45 23 63.5t63 23.5v-33q-15 -4 -24 -12.5t-11 -28.5h45v-99z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="556" 
d="M59 714h114v-286h210v286h114v-714h-114v332h-210v-332h-114v714zM226 906h108l89 -144h-91l-54 88l-57 -88h-86z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="481" 
d="M52 714h108v-234l3 -2q23 35 56.5 54.5t75.5 19.5q31 0 55.5 -7.5t42 -24.5t27 -45t9.5 -69v-406h-108v369q0 54 -16.5 75.5t-58.5 21.5q-33 0 -59.5 -25.5t-26.5 -79.5v-361h-108v714zM185 906h108l89 -144h-91l-54 88l-57 -88h-86z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="556" 
d="M59 714h114v-162h210v162h114v-714h-114v319h-210v-319h-114v714zM383 415v85h-210v-85h210z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="507" 
d="M78 714h108v-73h96v-52h-96v-109l3 -2q23 35 56.5 54.5t75.5 19.5q31 0 55.5 -7.5t42 -24.5t27 -45t9.5 -69v-406h-108v369q0 54 -16.5 75.5t-58.5 21.5q-33 0 -59.5 -25.5t-26.5 -79.5v-361h-108v589h-78v52h78v73z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" 
d="M54 714h114v-714h-114v714zM263 896q0 -17 -5.5 -37.5t-16 -38.5t-27 -30t-37.5 -12q-17 0 -33.5 5.5t-32 12l-29 12t-25.5 5.5q-17 0 -25.5 -12.5t-8.5 -27.5h-62q0 16 5.5 37t17.5 39.5t31 31t47 12.5q12 0 25.5 -5.5l27.5 -11.5q14 -7 28.5 -12.5t27.5 -5.5
q17 0 23.5 12t6.5 26h62z" />
    <glyph glyph-name="itilde" unicode="&#x129;" 
d="M57 538h108v-538h-108v538zM263 742q0 -17 -5.5 -37.5t-16 -38.5t-27 -30t-37.5 -12q-17 0 -33.5 5.5t-32 12l-29 12t-25.5 5.5q-17 0 -25.5 -12.5t-8.5 -27.5h-62q0 16 5.5 37t17.5 39.5t31 31t47 12.5q12 0 25.5 -5.5l27.5 -11.5q14 -7 28.5 -12.5t27.5 -5.5
q17 0 23.5 12t6.5 26h62z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" 
d="M54 714h114v-714h-114v714zM252 865v-62h-282v62h282z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" 
d="M57 538h108v-538h-108v538zM254 711v-62h-282v62h282z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" 
d="M168 0q-39 -38 -53.5 -64.5t-14.5 -55.5q0 -28 15.5 -42.5t41.5 -14.5q9 0 21.5 2t22.5 5l6 -42q-17 -5 -38 -8t-40 -3q-48 0 -78.5 21.5t-30.5 69.5q0 37 28 72.5t70 59.5h-64v714h114v-714z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" 
d="M165 0q-39 -38 -53.5 -64.5t-14.5 -55.5q0 -28 15.5 -42.5t41.5 -14.5q9 0 21.5 2t22.5 5l6 -42q-17 -5 -38 -8t-40 -3q-48 0 -78.5 21.5t-30.5 69.5q0 37 28 72.5t70 59.5h-58v538h108v-538zM57 722h108v-108h-108v108z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" 
d="M54 714h114v-714h-114v714zM64 888h96v-108h-96v108z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" 
d="M57 538h108v-538h-108v538z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="444" 
d="M124 215v-35q0 -22 3 -41.5t11 -34.5t23 -23.5t39 -8.5q28 0 43.5 12t23 32t8.5 45.5t1 52.5v500h114v-517q0 -59 -14 -99.5t-39.5 -65.5t-60.5 -35.5t-78 -10.5q-55 0 -90 15.5t-55 41t-28 58.5t-9 68v46h108zM280 906h108l89 -144h-91l-54 88l-57 -88h-86z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" 
d="M165 538v-573q0 -84 -35.5 -116.5t-106.5 -32.5q-23 0 -47 3v83h25q31 0 43.5 15t12.5 58v563h108zM58 752h108l89 -144h-91l-54 88l-57 -88h-86z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="519" 
d="M59 714h114v-311h2l216 311h125l-206 -301l228 -413h-129l-173 321l-63 -90v-231h-114v714zM234 -50h96v-86q0 -45 -23 -63.5t-63 -23.5v33q15 4 24 12.5t11 28.5h-45v99z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="463" 
d="M52 714h108v-401h2l163 225l125 1l-162 -208l183 -331h-118l-132 245l-61 -74v-171h-108v714zM200 -50h96v-86q0 -45 -23 -63.5t-63 -23.5v33q15 4 24 12.5t11 28.5h-45v99z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="444" 
d="M46 714h114v-618h276v-96h-390v714zM125 906h120l-106 -144h-74z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" 
d="M57 714h108v-714h-108v714zM118 906h120l-106 -144h-74z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="444" 
d="M46 714h114v-618h276v-96h-390v714zM195 -50h96v-86q0 -45 -23 -63.5t-63 -23.5v33q15 4 24 12.5t11 28.5h-45v99z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" 
d="M57 714h108v-714h-108v714zM64 -50h96v-86q0 -45 -23 -63.5t-63 -23.5v33q15 4 24 12.5t11 28.5h-45v99z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="444" 
d="M46 714h114v-618h276v-96h-390v714zM236 714h105l-63 -144h-70z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="308" 
d="M57 714h108v-714h-108v714zM235 714h105l-63 -144h-70z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="444" 
d="M46 714h114v-618h276v-96h-390v714zM256 539h96v-108h-96v108z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="347" 
d="M57 714h108v-714h-108v714zM221 539h96v-108h-96v108z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="444" 
d="M46 261l-56 -39v98l56 39v355h114v-277l146 101v-98l-146 -101v-243h276v-96h-390v261z" />
    <glyph glyph-name="lslash" unicode="&#x142;" 
d="M57 286l-57 -40v96l57 40v332h108v-258l57 40v-96l-57 -40v-360h-108v286z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="574" 
d="M59 714h139l207 -532h2v532h108v-714h-139l-207 546h-2v-546h-108v714zM303 906h120l-106 -144h-74z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="481" 
d="M52 538h102v-63h3q20 36 56.5 56.5t81.5 20.5q31 0 55.5 -7.5t42 -24.5t27 -45t9.5 -69v-406h-108v369q0 54 -16.5 75.5t-58.5 21.5q-33 0 -59.5 -25.5t-26.5 -79.5v-361h-108v538zM257 752h120l-106 -144h-74z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="574" 
d="M59 714h139l207 -532h2v532h108v-714h-139l-207 546h-2v-546h-108v714zM239 -50h96v-86q0 -45 -23 -63.5t-63 -23.5v33q15 4 24 12.5t11 28.5h-45v99z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="481" 
d="M52 538h102v-63h3q20 36 56.5 56.5t81.5 20.5q31 0 55.5 -7.5t42 -24.5t27 -45t9.5 -69v-406h-108v369q0 54 -16.5 75.5t-58.5 21.5q-33 0 -59.5 -25.5t-26.5 -79.5v-361h-108v538zM190 -50h96v-86q0 -45 -23 -63.5t-63 -23.5v33q15 4 24 12.5t11 28.5h-45v99z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="574" 
d="M59 714h139l207 -532h2v532h108v-714h-139l-207 546h-2v-546h-108v714zM143 906h91l54 -88l57 88h86l-91 -144h-108z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="481" 
d="M52 538h102v-63h3q20 36 56.5 56.5t81.5 20.5q31 0 55.5 -7.5t42 -24.5t27 -45t9.5 -69v-406h-108v369q0 54 -16.5 75.5t-58.5 21.5q-33 0 -59.5 -25.5t-26.5 -79.5v-361h-108v538zM96 752h91l54 -88l57 88h86l-91 -144h-108z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="556" 
d="M278 72q25 0 47.5 10.5t39 41.5t26 87.5t9.5 148.5q0 90 -9.5 145.5t-26 85.5t-39 40.5t-47.5 10.5q-26 0 -48 -10.5t-38.5 -40.5t-26 -85.5t-9.5 -145.5q0 -92 9.5 -148.5t26 -87.5t38.5 -41.5t48 -10.5zM278 -14q-123 0 -179.5 90t-56.5 281t56.5 281t179.5 90
t179.5 -90t56.5 -281t-56.5 -281t-179.5 -90zM419 865v-62h-282v62h282z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="463" 
d="M139 269q0 -51 3.5 -89.5t13.5 -63.5t28 -37.5t47 -12.5q30 0 48 12.5t28 37.5t13.5 63.5t3.5 89.5q0 45 -3.5 82t-13.5 64t-28 42t-48 15q-29 0 -47 -15t-28 -42t-13.5 -64t-3.5 -82zM231 -14q-56 0 -94 20.5t-61.5 58t-34 89.5t-10.5 115t9.5 115t32 89.5t61 58
t97.5 20.5q58 0 97 -20.5t62 -58t32.5 -89.5t9.5 -115t-10.5 -115t-34 -89.5t-62 -58t-94.5 -20.5zM372 711v-62h-282v62h282z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="556" 
d="M278 72q25 0 47.5 10.5t39 41.5t26 87.5t9.5 148.5q0 90 -9.5 145.5t-26 85.5t-39 40.5t-47.5 10.5q-26 0 -48 -10.5t-38.5 -40.5t-26 -85.5t-9.5 -145.5q0 -92 9.5 -148.5t26 -87.5t38.5 -41.5t48 -10.5zM278 -14q-123 0 -179.5 90t-56.5 281t56.5 281t179.5 90
t179.5 -90t56.5 -281t-56.5 -281t-179.5 -90zM206 902q0 -9 4.5 -20.5t13.5 -22t22 -17.5t31 -7t31 7t22 17.5t13.5 22t4.5 20.5h62q0 -28 -9 -52.5t-26 -43t-42 -29t-56 -10.5q-32 0 -56.5 10.5t-41.5 29t-26 43t-9 52.5h62z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="463" 
d="M139 269q0 -51 3.5 -89.5t13.5 -63.5t28 -37.5t47 -12.5q30 0 48 12.5t28 37.5t13.5 63.5t3.5 89.5q0 45 -3.5 82t-13.5 64t-28 42t-48 15q-29 0 -47 -15t-28 -42t-13.5 -64t-3.5 -82zM231 -14q-56 0 -94 20.5t-61.5 58t-34 89.5t-10.5 115t9.5 115t32 89.5t61 58
t97.5 20.5q58 0 97 -20.5t62 -58t32.5 -89.5t9.5 -115t-10.5 -115t-34 -89.5t-62 -58t-94.5 -20.5zM160 748q0 -9 4.5 -20.5t13.5 -22t22 -17.5t31 -7t31 7t22 17.5t13.5 22t4.5 20.5h62q0 -28 -9 -52.5t-26 -43t-42 -29t-56 -10.5q-32 0 -56.5 10.5t-41.5 29t-26 43
t-9 52.5h62z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="556" 
d="M278 72q25 0 47.5 10.5t39 41.5t26 87.5t9.5 148.5q0 90 -9.5 145.5t-26 85.5t-39 40.5t-47.5 10.5q-26 0 -48 -10.5t-38.5 -40.5t-26 -85.5t-9.5 -145.5q0 -92 9.5 -148.5t26 -87.5t38.5 -41.5t48 -10.5zM278 -14q-123 0 -179.5 90t-56.5 281t56.5 281t179.5 90
t179.5 -90t56.5 -281t-56.5 -281t-179.5 -90zM405 906h120l-106 -144h-74zM205 906h120l-106 -144h-74z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="463" 
d="M139 269q0 -51 3.5 -89.5t13.5 -63.5t28 -37.5t47 -12.5q30 0 48 12.5t28 37.5t13.5 63.5t3.5 89.5q0 45 -3.5 82t-13.5 64t-28 42t-48 15q-29 0 -47 -15t-28 -42t-13.5 -64t-3.5 -82zM231 -14q-56 0 -94 20.5t-61.5 58t-34 89.5t-10.5 115t9.5 115t32 89.5t61 58
t97.5 20.5q58 0 97 -20.5t62 -58t32.5 -89.5t9.5 -115t-10.5 -115t-34 -89.5t-62 -58t-94.5 -20.5zM354 752h120l-106 -144h-74zM154 752h120l-106 -144h-74z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="796" 
d="M392 478q0 51 -8.5 83t-22.5 50t-33.5 24.5t-41.5 6.5q-29 0 -52.5 -10.5t-41 -40.5t-27 -85.5t-9.5 -145.5q0 -92 9.5 -148.5t26 -87.5t38.5 -41.5t48 -10.5q25 0 46 6.5t36 24.5t23.5 50t8.5 83v242zM400 51h-2q-21 -34 -58.5 -49.5t-79.5 -15.5q-111 0 -164.5 88.5
t-53.5 282.5q0 192 54.5 281.5t167.5 89.5q97 0 134 -58h2v44h354v-96h-254v-198h238v-96h-238v-228h262v-96h-362v51z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="722" 
d="M139 269q0 -45 3.5 -82t13 -64t26.5 -42t45 -15q30 0 48 15t28 42t13.5 64t3.5 82t-3.5 82t-13.5 64t-28 42t-48 15q-28 0 -45 -15t-26.5 -42t-13 -64t-3.5 -82zM689 180q0 -91 -50.5 -142.5t-138.5 -51.5q-37 0 -61.5 11t-39.5 26t-22 29t-9 20q-23 -46 -60 -66t-88 -20
q-54 0 -90.5 20.5t-58.5 58t-31 89.5t-9 115t9 115t31.5 89.5t60.5 58t95 20.5q37 0 62 -8t42 -20.5t27 -27t17 -26.5q25 42 57.5 62t83.5 20t84.5 -16.5t53.5 -47.5t28.5 -75.5t8.5 -100.5v-57h-269v-38q0 -30 3.5 -57.5t12 -49.5t24 -35.5t39.5 -14.5q29 0 46 14t26 33.5
t11.5 40t2.5 32.5h102zM583 329v29q0 51 -17.5 85.5t-60.5 34.5q-24 0 -40 -13.5t-25.5 -33t-13 -41.5t-3.5 -38v-23h160z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="537" 
d="M264 396q55 0 84.5 31t29.5 85q0 57 -25.5 86.5t-82.5 29.5h-97v-232h91zM59 714h252q37 0 70 -10t58 -32t39 -57t14 -85q0 -69 -29.5 -117.5t-89.5 -57.5v-2q54 -5 81.5 -38t30.5 -108q1 -35 1.5 -74t2.5 -71q2 -26 12.5 -40t21.5 -22h-129q-8 10 -12.5 26t-6.5 37
q-3 31 -3 64q0 16 -0.5 33.5t-1.5 37.5q-2 60 -24 86t-80 26h-93v-310h-114v714zM263 906h120l-106 -144h-74z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="315" 
d="M52 538h108v-84h2q18 45 48.5 71.5t76.5 26.5q19 0 26 -4v-110q-6 2 -18.5 4t-26.5 2q-18 0 -37.5 -5t-35 -18.5t-25.5 -37.5t-10 -61v-322h-108v538zM147 752h120l-106 -144h-74z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="537" 
d="M264 396q55 0 84.5 31t29.5 85q0 57 -25.5 86.5t-82.5 29.5h-97v-232h91zM59 714h252q37 0 70 -10t58 -32t39 -57t14 -85q0 -69 -29.5 -117.5t-89.5 -57.5v-2q54 -5 81.5 -38t30.5 -108q1 -35 1.5 -74t2.5 -71q2 -26 12.5 -40t21.5 -22h-129q-8 10 -12.5 26t-6.5 37
q-3 31 -3 64q0 16 -0.5 33.5t-1.5 37.5q-2 60 -24 86t-80 26h-93v-310h-114v714zM225 -50h96v-86q0 -45 -23 -63.5t-63 -23.5v33q15 4 24 12.5t11 28.5h-45v99z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="315" 
d="M52 538h108v-84h2q18 45 48.5 71.5t76.5 26.5q19 0 26 -4v-110q-6 2 -18.5 4t-26.5 2q-18 0 -37.5 -5t-35 -18.5t-25.5 -37.5t-10 -61v-322h-108v538zM59 -50h96v-86q0 -45 -23 -63.5t-63 -23.5v33q15 4 24 12.5t11 28.5h-45v99z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="537" 
d="M264 396q55 0 84.5 31t29.5 85q0 57 -25.5 86.5t-82.5 29.5h-97v-232h91zM59 714h252q37 0 70 -10t58 -32t39 -57t14 -85q0 -69 -29.5 -117.5t-89.5 -57.5v-2q54 -5 81.5 -38t30.5 -108q1 -35 1.5 -74t2.5 -71q2 -26 12.5 -40t21.5 -22h-129q-8 10 -12.5 26t-6.5 37
q-3 31 -3 64q0 16 -0.5 33.5t-1.5 37.5q-2 60 -24 86t-80 26h-93v-310h-114v714zM110 906h91l54 -88l57 88h86l-91 -144h-108z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="315" 
d="M52 538h108v-84h2q18 45 48.5 71.5t76.5 26.5q19 0 26 -4v-110q-6 2 -18.5 4t-26.5 2q-18 0 -37.5 -5t-35 -18.5t-25.5 -37.5t-10 -61v-322h-108v538zM38 752h91l54 -88l57 88h86l-91 -144h-108z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="519" 
d="M147 221v-18q0 -131 117 -131q27 0 47 9t33.5 23.5t20.5 33t7 38.5q0 56 -27 84.5t-75 42.5l-78 27q-39 15 -67 33t-46.5 41.5t-27 53.5t-8.5 68q0 49 16 86.5t45 63t70 39t91 13.5q69 0 109 -19t60.5 -47.5t26.5 -61t6 -60.5v-16h-114v15q0 49 -23 76t-77 27
q-18 0 -35.5 -5.5t-31 -17.5t-21.5 -32t-8 -48q0 -46 25 -72.5t82 -48.5l77 -28q76 -27 110.5 -71t34.5 -118q0 -57 -16.5 -97.5t-47.5 -67t-74.5 -38.5t-97.5 -12q-66 0 -108.5 20t-66.5 51.5t-33 70t-9 76.5v17h114zM285 906h120l-106 -144h-74z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="426" 
d="M284 381v14q0 16 -3 31t-11.5 26.5t-22 18.5t-34.5 7q-32 0 -54.5 -16t-22.5 -54q0 -32 16.5 -48t59.5 -31l69 -24q60 -20 88.5 -55.5t28.5 -99.5q0 -43 -15 -74t-41 -51t-61.5 -29.5t-75.5 -9.5q-51 0 -85 11.5t-54.5 33t-29 52.5t-8.5 70v20h96v-17q0 -48 18 -72
t66 -24q45 0 66.5 21t21.5 58q0 59 -59 79l-88 31q-61 20 -88 55t-27 100q0 76 49.5 112t134.5 36q51 0 84 -13.5t51.5 -35t25.5 -47.5t7 -51v-24h-102zM236 752h120l-106 -144h-74z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="519" 
d="M147 221v-18q0 -131 117 -131q27 0 47 9t33.5 23.5t20.5 33t7 38.5q0 56 -27 84.5t-75 42.5l-78 27q-39 15 -67 33t-46.5 41.5t-27 53.5t-8.5 68q0 49 16 86.5t45 63t70 39t91 13.5q69 0 109 -19t60.5 -47.5t26.5 -61t6 -60.5v-16h-114v15q0 49 -23 76t-77 27
q-18 0 -35.5 -5.5t-31 -17.5t-21.5 -32t-8 -48q0 -46 25 -72.5t82 -48.5l77 -28q76 -27 110.5 -71t34.5 -118q0 -57 -16.5 -97.5t-47.5 -67t-74.5 -38.5t-97.5 -12q-66 0 -108.5 20t-66.5 51.5t-33 70t-9 76.5v17h114zM215 906h108l89 -144h-91l-54 88l-57 -88h-86z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="426" 
d="M284 381v14q0 16 -3 31t-11.5 26.5t-22 18.5t-34.5 7q-32 0 -54.5 -16t-22.5 -54q0 -32 16.5 -48t59.5 -31l69 -24q60 -20 88.5 -55.5t28.5 -99.5q0 -43 -15 -74t-41 -51t-61.5 -29.5t-75.5 -9.5q-51 0 -85 11.5t-54.5 33t-29 52.5t-8.5 70v20h96v-17q0 -48 18 -72
t66 -24q45 0 66.5 21t21.5 58q0 59 -59 79l-88 31q-61 20 -88 55t-27 100q0 76 49.5 112t134.5 36q51 0 84 -13.5t51.5 -35t25.5 -47.5t7 -51v-24h-102zM164 752h108l89 -144h-91l-54 88l-57 -88h-86z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="519" 
d="M147 221v-18q0 -131 117 -131q27 0 47 9t33.5 23.5t20.5 33t7 38.5q0 56 -27 84.5t-75 42.5l-78 27q-39 15 -67 33t-46.5 41.5t-27 53.5t-8.5 68q0 49 16 86.5t45 63t70 39t91 13.5q69 0 109 -19t60.5 -47.5t26.5 -61t6 -60.5v-16h-114v15q0 49 -23 76t-77 27
q-18 0 -35.5 -5.5t-31 -17.5t-21.5 -32t-8 -48q0 -46 25 -72.5t82 -48.5l77 -28q76 -27 110.5 -71t34.5 -118q0 -85 -36.5 -135t-102.5 -68q-38 -11 -81 -12l-32 -46q11 3 20.5 4t19.5 1q34 0 56 -22t22 -52q0 -42 -33 -67t-84 -27q-34 0 -58.5 5t-42.5 12l16 40
q30 -13 69 -13q24 0 38.5 9.5t14.5 32.5q0 17 -13.5 27.5t-32.5 10.5q-11 0 -20 -2t-18 -6l-18 17l48 78q-45 3 -77 18t-53 37t-33 48t-17 55t-5 58v17h114z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="426" 
d="M284 381v14q0 16 -3 31t-11.5 26.5t-22 18.5t-34.5 7q-32 0 -54.5 -16t-22.5 -54q0 -32 16.5 -48t59.5 -31l69 -24q60 -20 88.5 -55.5t28.5 -99.5t-32.5 -102t-84.5 -52q-28 -7 -59 -9l-32 -47q11 3 20.5 4t19.5 1q34 0 56 -22t22 -52q0 -42 -33 -67t-84 -27
q-34 0 -58.5 5t-42.5 12l16 40q30 -13 69 -13q24 0 38.5 9.5t14.5 32.5q0 17 -13.5 27.5t-32.5 10.5q-11 0 -20 -2t-18 -6l-18 17l48 77q-26 3 -54 11q-52 17 -72 56t-20 99v20h96v-17q0 -48 18 -72t66 -24q45 0 66.5 21t21.5 58q0 59 -59 79l-88 31q-61 20 -88 55t-27 100
q0 76 49.5 112t134.5 36q51 0 84 -13.5t51.5 -35t25.5 -47.5t7 -51v-24h-102z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="519" 
d="M147 221v-18q0 -131 117 -131q27 0 47 9t33.5 23.5t20.5 33t7 38.5q0 56 -27 84.5t-75 42.5l-78 27q-39 15 -67 33t-46.5 41.5t-27 53.5t-8.5 68q0 49 16 86.5t45 63t70 39t91 13.5q69 0 109 -19t60.5 -47.5t26.5 -61t6 -60.5v-16h-114v15q0 49 -23 76t-77 27
q-18 0 -35.5 -5.5t-31 -17.5t-21.5 -32t-8 -48q0 -46 25 -72.5t82 -48.5l77 -28q76 -27 110.5 -71t34.5 -118q0 -57 -16.5 -97.5t-47.5 -67t-74.5 -38.5t-97.5 -12q-66 0 -108.5 20t-66.5 51.5t-33 70t-9 76.5v17h114zM125 906h91l54 -88l57 88h86l-91 -144h-108z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="426" 
d="M284 381v14q0 16 -3 31t-11.5 26.5t-22 18.5t-34.5 7q-32 0 -54.5 -16t-22.5 -54q0 -32 16.5 -48t59.5 -31l69 -24q60 -20 88.5 -55.5t28.5 -99.5q0 -43 -15 -74t-41 -51t-61.5 -29.5t-75.5 -9.5q-51 0 -85 11.5t-54.5 33t-29 52.5t-8.5 70v20h96v-17q0 -48 18 -72
t66 -24q45 0 66.5 21t21.5 58q0 59 -59 79l-88 31q-61 20 -88 55t-27 100q0 76 49.5 112t134.5 36q51 0 84 -13.5t51.5 -35t25.5 -47.5t7 -51v-24h-102zM73 752h91l54 -88l57 88h86l-91 -144h-108z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="463" 
d="M176 618h-170v96h451v-96h-167v-618h-114v618zM185 -50h96v-86q0 -45 -23 -63.5t-63 -23.5v33q15 4 24 12.5t11 28.5h-45v99z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="278" 
d="M4 538h74v153h108v-153h88v-80h-88v-321q0 -32 10.5 -44.5t38.5 -12.5q24 0 39 3v-80q-35 -9 -87 -9q-27 0 -47.5 5t-34 19t-20.5 39.5t-7 66.5v334h-74v80zM128 -50h96v-86q0 -45 -23 -63.5t-63 -23.5v33q15 4 24 12.5t11 28.5h-45v99z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="463" 
d="M176 618h-170v96h451v-96h-167v-618h-114v618zM89 906h91l54 -88l57 88h86l-91 -144h-108z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="363" 
d="M290 714h105l-63 -144h-70zM4 538h74v153h108v-153h88v-80h-88v-321q0 -32 10.5 -44.5t38.5 -12.5q24 0 39 3v-80q-35 -9 -87 -9q-27 0 -47.5 5t-34 19t-20.5 39.5t-7 66.5v334h-74v80z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="463" 
d="M176 618h-170v96h451v-96h-167v-150h130v-52h-130v-416h-114v416h-131v52h131v150z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="278" 
d="M4 538h74v153h108v-153h88v-80h-88v-112h97v-52h-97v-157q0 -32 10.5 -44.5t38.5 -12.5q24 0 39 3v-80q-35 -9 -87 -9q-27 0 -47.5 5t-34 19t-20.5 39.5t-7 66.5v170h-83v52h83v112h-74v80z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="537" 
d="M164 714v-512q0 -69 26.5 -99.5t78.5 -30.5q51 0 77.5 30.5t26.5 99.5v512h114v-503q0 -62 -15 -105t-43 -69.5t-68.5 -38.5t-91.5 -12t-92 12t-69 38.5t-43 69.5t-15 105v503h114zM421 896q0 -17 -5.5 -37.5t-16 -38.5t-27 -30t-37.5 -12q-17 0 -33.5 5.5t-32 12l-29 12
t-25.5 5.5q-17 0 -25.5 -12.5t-8.5 -27.5h-62q0 16 5.5 37t17.5 39.5t31 31t47 12.5q12 0 25.5 -5.5l27.5 -11.5q14 -7 28.5 -12.5t27.5 -5.5q17 0 23.5 12t6.5 26h62z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="481" 
d="M327 63h-3q-20 -36 -56.5 -56.5t-81.5 -20.5q-30 0 -54.5 8t-42.5 28t-27.5 54t-9.5 86v376h108v-386q0 -45 19 -62.5t54 -17.5t61.5 22.5t26.5 67.5v376h108v-538h-102v63zM393 742q0 -17 -5.5 -37.5t-16 -38.5t-27 -30t-37.5 -12q-17 0 -33.5 5.5t-32 12l-29 12
t-25.5 5.5q-17 0 -25.5 -12.5t-8.5 -27.5h-62q0 16 5.5 37t17.5 39.5t31 31t47 12.5q12 0 25.5 -5.5l27.5 -11.5q14 -7 28.5 -12.5t27.5 -5.5q17 0 23.5 12t6.5 26h62z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="537" 
d="M164 714v-512q0 -69 26.5 -99.5t78.5 -30.5q51 0 77.5 30.5t26.5 99.5v512h114v-503q0 -62 -15 -105t-43 -69.5t-68.5 -38.5t-91.5 -12t-92 12t-69 38.5t-43 69.5t-15 105v503h114zM410 865v-62h-282v62h282z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="481" 
d="M327 63h-3q-20 -36 -56.5 -56.5t-81.5 -20.5q-30 0 -54.5 8t-42.5 28t-27.5 54t-9.5 86v376h108v-386q0 -45 19 -62.5t54 -17.5t61.5 22.5t26.5 67.5v376h108v-538h-102v63zM383 711v-62h-282v62h282z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="537" 
d="M164 714v-512q0 -69 26.5 -99.5t78.5 -30.5q51 0 77.5 30.5t26.5 99.5v512h114v-503q0 -62 -15 -105t-43 -69.5t-68.5 -38.5t-91.5 -12t-92 12t-69 38.5t-43 69.5t-15 105v503h114zM198 902q0 -9 4.5 -20.5t13.5 -22t22 -17.5t31 -7t31 7t22 17.5t13.5 22t4.5 20.5h62
q0 -28 -9 -52.5t-26 -43t-42 -29t-56 -10.5q-32 0 -56.5 10.5t-41.5 29t-26 43t-9 52.5h62z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="481" 
d="M327 63h-3q-20 -36 -56.5 -56.5t-81.5 -20.5q-30 0 -54.5 8t-42.5 28t-27.5 54t-9.5 86v376h108v-386q0 -45 19 -62.5t54 -17.5t61.5 22.5t26.5 67.5v376h108v-538h-102v63zM170 748q0 -9 4.5 -20.5t13.5 -22t22 -17.5t31 -7t31 7t22 17.5t13.5 22t4.5 20.5h62
q0 -28 -9 -52.5t-26 -43t-42 -29t-56 -10.5q-32 0 -56.5 10.5t-41.5 29t-26 43t-9 52.5h62z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="537" 
d="M164 714v-512q0 -69 26.5 -99.5t78.5 -30.5q51 0 77.5 30.5t26.5 99.5v512h114v-503q0 -62 -15 -105t-43 -69.5t-68.5 -38.5t-91.5 -12t-92 12t-69 38.5t-43 69.5t-15 105v503h114zM165 846q0 45 30.5 74.5t75.5 29.5t75.5 -29.5t30.5 -74.5t-30.5 -74.5t-75.5 -29.5
t-75.5 29.5t-30.5 74.5zM215 846q0 -23 16.5 -39.5t39.5 -16.5t39.5 16.5t16.5 39.5t-16.5 39.5t-39.5 16.5t-39.5 -16.5t-16.5 -39.5z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="481" 
d="M327 63h-3q-20 -36 -56.5 -56.5t-81.5 -20.5q-30 0 -54.5 8t-42.5 28t-27.5 54t-9.5 86v376h108v-386q0 -45 19 -62.5t54 -17.5t61.5 22.5t26.5 67.5v376h108v-538h-102v63zM136 696q0 45 30.5 74.5t75.5 29.5t75.5 -29.5t30.5 -74.5t-30.5 -74.5t-75.5 -29.5t-75.5 29.5
t-30.5 74.5zM186 696q0 -23 16.5 -39.5t39.5 -16.5t39.5 16.5t16.5 39.5t-16.5 39.5t-39.5 16.5t-39.5 -16.5t-16.5 -39.5z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="537" 
d="M164 714v-512q0 -69 26.5 -99.5t78.5 -30.5q51 0 77.5 30.5t26.5 99.5v512h114v-503q0 -62 -15 -105t-43 -69.5t-68.5 -38.5t-91.5 -12t-92 12t-69 38.5t-43 69.5t-15 105v503h114zM389 906h120l-106 -144h-74zM189 906h120l-106 -144h-74z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="481" 
d="M327 63h-3q-20 -36 -56.5 -56.5t-81.5 -20.5q-30 0 -54.5 8t-42.5 28t-27.5 54t-9.5 86v376h108v-386q0 -45 19 -62.5t54 -17.5t61.5 22.5t26.5 67.5v376h108v-538h-102v63zM360 752h120l-106 -144h-74zM160 752h120l-106 -144h-74z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="537" 
d="M164 714v-512q0 -69 26.5 -99.5t78.5 -30.5q51 0 77.5 30.5t26.5 99.5v512h114v-503q0 -94 -33 -144.5t-94 -68.5q-14 -4 -28 -6.5t-30 -4.5q-31 -31 -41 -51q-14 -30 -14 -56q0 -28 15.5 -42.5t41.5 -14.5q9 0 21.5 2t22.5 5l6 -42q-17 -5 -38 -8t-40 -3
q-48 0 -78.5 21.5t-30.5 69.5q0 39 28 73q20 26 49 46q-18 1 -34.5 3.5t-32.5 7.5q-61 18 -94 68.5t-33 144.5v503h114z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="481" 
d="M372 0q-35 -38 -48 -64.5t-13 -55.5q0 -28 14 -42.5t37 -14.5q8 0 19.5 2t20.5 5l5 -42q-15 -5 -34 -8t-36 -3q-43 0 -70.5 21.5t-27.5 69.5q0 37 25 72.5t63 59.5v63h-3q-20 -36 -56.5 -56.5t-81.5 -20.5q-30 0 -54.5 8t-42.5 28t-27.5 54t-9.5 86v376h108v-386
q0 -45 19 -62.5t54 -17.5t61.5 22.5t26.5 67.5v376h108v-538h-57z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="741" 
d="M6 714h115l94 -555h2l94 555h120l96 -558h2l93 558h113l-142 -714h-132l-92 538h-2l-91 -538h-132zM316 906h108l89 -144h-91l-54 88l-57 -88h-86z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="685" 
d="M15 538h112l75 -424h2l80 424h122l80 -424h2l74 424h108l-123 -538h-121l-84 418h-2l-77 -418h-126zM293 752h108l89 -144h-91l-54 88l-57 -88h-86z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="481" 
d="M180 279l-184 435h127l120 -308l123 308h119l-191 -435v-279h-114v279zM193 906h108l89 -144h-91l-54 88l-57 -88h-86z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="426" 
d="M124 538l93 -404h2l86 404h113l-148 -558q-16 -55 -31.5 -88t-36.5 -50.5t-49.5 -22t-70.5 -3.5q-11 0 -22 1t-21 3v86q15 -4 35 -4q28 0 46 8t27 34l14 44l-153 550h116zM165 752h108l89 -144h-91l-54 88l-57 -88h-86z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="481" 
d="M180 279l-184 435h127l120 -308l123 308h119l-191 -435v-279h-114v279zM114 888h96v-108h-96v108zM280 888h96v-108h-96v108z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="463" 
d="M18 90l299 528h-281v96h406v-96l-299 -522h302v-96h-427v90zM251 906h120l-106 -144h-74z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="407" 
d="M25 84l235 363h-221v91h343v-89l-226 -358h226v-91h-357v84zM227 752h120l-106 -144h-74z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="463" 
d="M18 90l299 528h-281v96h406v-96l-299 -522h302v-96h-427v90zM191 888h96v-108h-96v108z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="407" 
d="M25 84l235 363h-221v91h343v-89l-226 -358h226v-91h-357v84zM161 734h96v-108h-96v108z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="463" 
d="M18 90l299 528h-281v96h406v-96l-299 -522h302v-96h-427v90zM97 906h91l54 -88l57 88h86l-91 -144h-108z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="407" 
d="M25 84l235 363h-221v91h343v-89l-226 -358h226v-91h-357v84zM69 752h91l54 -88l57 88h86l-91 -144h-108z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="480" 
d="M201 434l28 129q7 33 18 61t29.5 48t46 31t68.5 11q22 0 41 -1.5t36 -6.5l-15 -76q-8 1 -17.5 2.5t-20.5 1.5q-34 0 -55.5 -19.5t-32.5 -73.5l-24 -107h112l-11 -68h-114l-76 -371q-9 -42 -21.5 -74t-32.5 -53.5t-49 -32.5t-71 -11q-25 0 -40 1.5t-26 4.5l17 78
q13 -4 42 -4q39 0 54 20t24 63l77 379h-104l11 68h106z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="519" 
d="M147 221v-18q0 -131 117 -131q27 0 47 9t33.5 23.5t20.5 33t7 38.5q0 56 -27 84.5t-75 42.5l-78 27q-39 15 -67 33t-46.5 41.5t-27 53.5t-8.5 68q0 49 16 86.5t45 63t70 39t91 13.5q69 0 109 -19t60.5 -47.5t26.5 -61t6 -60.5v-16h-114v15q0 49 -23 76t-77 27
q-18 0 -35.5 -5.5t-31 -17.5t-21.5 -32t-8 -48q0 -46 25 -72.5t82 -48.5l77 -28q76 -27 110.5 -71t34.5 -118q0 -57 -16.5 -97.5t-47.5 -67t-74.5 -38.5t-97.5 -12q-66 0 -108.5 20t-66.5 51.5t-33 70t-9 76.5v17h114zM211 -50h96v-86q0 -45 -23 -63.5t-63 -23.5v33
q15 4 24 12.5t11 28.5h-45v99z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="426" 
d="M284 381v14q0 16 -3 31t-11.5 26.5t-22 18.5t-34.5 7q-32 0 -54.5 -16t-22.5 -54q0 -32 16.5 -48t59.5 -31l69 -24q60 -20 88.5 -55.5t28.5 -99.5q0 -43 -15 -74t-41 -51t-61.5 -29.5t-75.5 -9.5q-51 0 -85 11.5t-54.5 33t-29 52.5t-8.5 70v20h96v-17q0 -48 18 -72
t66 -24q45 0 66.5 21t21.5 58q0 59 -59 79l-88 31q-61 20 -88 55t-27 100q0 76 49.5 112t134.5 36q51 0 84 -13.5t51.5 -35t25.5 -47.5t7 -51v-24h-102zM164 -50h96v-86q0 -45 -23 -63.5t-63 -23.5v33q15 4 24 12.5t11 28.5h-45v99z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="463" 
d="M176 618h-170v96h451v-96h-167v-618h-114v618zM185 -50h96v-86q0 -45 -23 -63.5t-63 -23.5v33q15 4 24 12.5t11 28.5h-45v99z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="278" 
d="M4 538h74v153h108v-153h88v-80h-88v-321q0 -32 10.5 -44.5t38.5 -12.5q24 0 39 3v-80q-35 -9 -87 -9q-27 0 -47.5 5t-34 19t-20.5 39.5t-7 66.5v334h-74v80zM128 -50h96v-86q0 -45 -23 -63.5t-63 -23.5v33q15 4 24 12.5t11 28.5h-45v99z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" 
d="M58 752h108l89 -144h-91l-54 88l-57 -88h-86z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" 
d="M-33 752h91l54 -88l57 88h86l-91 -144h-108z" />
    <glyph glyph-name="uni02C9" unicode="&#x2c9;" 
d="M252 711v-62h-282v62h282z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" 
d="M40 748q0 -9 4.5 -20.5t13.5 -22t22 -17.5t31 -7t31 7t22 17.5t13.5 22t4.5 20.5h62q0 -28 -9 -52.5t-26 -43t-42 -29t-56 -10.5q-32 0 -56.5 10.5t-41.5 29t-26 43t-9 52.5h62z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" 
d="M63 734h96v-108h-96v108z" />
    <glyph glyph-name="ring" unicode="&#x2da;" 
d="M5 696q0 45 30.5 74.5t75.5 29.5t75.5 -29.5t30.5 -74.5t-30.5 -74.5t-75.5 -29.5t-75.5 29.5t-30.5 74.5zM55 696q0 -23 16.5 -39.5t39.5 -16.5t39.5 16.5t16.5 39.5t-16.5 39.5t-39.5 16.5t-39.5 -16.5t-16.5 -39.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" 
d="M171 0q-39 -38 -53.5 -64.5t-14.5 -55.5q0 -28 15.5 -42.5t41.5 -14.5q9 0 21.5 2t22.5 5l6 -42q-17 -5 -38 -8t-40 -3q-48 0 -78.5 21.5t-30.5 69.5q0 37 28 72.5t70 59.5h50z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" 
d="M262 742q0 -17 -5.5 -37.5t-16 -38.5t-27 -30t-37.5 -12q-17 0 -33.5 5.5t-32 12l-29 12t-25.5 5.5q-17 0 -25.5 -12.5t-8.5 -27.5h-62q0 16 5.5 37t17.5 39.5t31 31t47 12.5q12 0 25.5 -5.5l27.5 -11.5q14 -7 28.5 -12.5t27.5 -5.5q17 0 23.5 12t6.5 26h62z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" 
d="M196 752h120l-106 -144h-74zM-4 752h120l-106 -144h-74z" />
    <glyph glyph-name="uni0394" unicode="&#x394;" horiz-adv-x="682" 
d="M334 597l-187 -504h382zM10 0l273 712h104l285 -712h-662z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" horiz-adv-x="755" 
d="M428 0v187q40 8 70.5 27.5t50.5 48t30.5 64t10.5 75.5q0 47 -16 88t-44.5 71.5t-68.5 48t-88 17.5t-88.5 -17.5t-70 -48t-45.5 -71.5t-16 -89q0 -81 40.5 -138.5t121.5 -75.5v-187h-269v86h186v53q-46 13 -80.5 39t-58 61.5t-35.5 78.5t-12 89q0 69 26 126.5t70.5 99
t103.5 65t126 23.5t126.5 -22.5t103.5 -64t69.5 -98.5t25.5 -126q0 -46 -12 -89.5t-35 -80t-58 -63t-81 -38.5v-53h186v-86h-269z" />
    <glyph glyph-name="uni03BC" unicode="&#x3bc;" horiz-adv-x="481" 
d="M52 538h108v-369q0 -54 16.5 -75.5t58.5 -21.5q33 0 59.5 25.5t26.5 79.5v361h108v-538h-102v63h-3q-20 -36 -51 -56.5t-63 -20.5q-18 0 -30.5 5.5t-19.5 12.5v-180h-108v714z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="585" 
d="M563 13q-20 -10 -39 -15t-42 -5q-32 0 -51.5 10t-29.5 27.5t-13 40.5t-3 50v271h-218v-392h-96v392h-70v90h558v-90h-78v-257q0 -11 0.5 -22t4 -19.5t11.5 -14t22 -5.5q19 0 44 11v-72z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="500" 
d="M0 337h500v-96h-500v96z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1000" 
d="M0 337h1000v-96h-1000v96z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="240" 
d="M63 560q0 60 24 100.5t90 53.5v-58q-23 -6 -36.5 -27t-13.5 -54v-15h50v-132h-114v132z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="240" 
d="M63 714h114v-132q0 -60 -24 -100.5t-90 -53.5v58q23 6 36.5 27t13.5 54v15h-50v132z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="240" 
d="M63 132h114v-132q0 -60 -24 -100.5t-90 -53.5v58q23 6 36.5 27t13.5 54v15h-50v132z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="426" 
d="M245 560q0 60 24 100.5t90 53.5v-58q-23 -6 -36.5 -27t-13.5 -54v-15h50v-132h-114v132zM67 560q0 60 24 100.5t90 53.5v-58q-23 -6 -36.5 -27t-13.5 -54v-15h50v-132h-114v132z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="426" 
d="M67 714h114v-132q0 -60 -24 -100.5t-90 -53.5v58q23 6 36.5 27t13.5 54v15h-50v132zM245 714h114v-132q0 -60 -24 -100.5t-90 -53.5v58q23 6 36.5 27t13.5 54v15h-50v132z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="426" 
d="M67 132h114v-132q0 -60 -24 -100.5t-90 -53.5v58q23 6 36.5 27t13.5 54v15h-50v132zM245 132h114v-132q0 -60 -24 -100.5t-90 -53.5v58q23 6 36.5 27t13.5 54v15h-50v132z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="482" 
d="M187 714h108v-202h171v-91h-171v-563h-108v563h-171v91h171v202z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="482" 
d="M187 714h108v-195h172v-86h-172v-290h172v-86h-172v-199h-108v199h-172v86h172v290h-172v86h172v195z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="500" 
d="M72 357q0 37 14 69.5t38 56.5t56.5 38t69.5 14t69.5 -14t56.5 -38t38 -56.5t14 -69.5t-14 -69.5t-38 -56.5t-56.5 -38t-69.5 -14t-69.5 14t-56.5 38t-38 56.5t-14 69.5z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="1000" 
d="M776 132h114v-132h-114v132zM443 132h114v-132h-114v132zM110 132h114v-132h-114v132z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1056" 
d="M575 370q30 0 55 -9t43 -31t28.5 -59t10.5 -93t-10.5 -93t-28.5 -59t-43 -31t-55 -9t-55 9t-43 31t-28.5 59t-10.5 93t10.5 93t28.5 59t43 31t55 9zM575 308q-31 0 -41 -30t-10 -100t10 -100t41 -30t41 30t10 100t-10 100t-41 30zM162 708q30 0 55 -9t43 -31t28.5 -59
t10.5 -93t-10.5 -93t-28.5 -59t-43 -31t-55 -9t-55 9t-43 31t-28.5 59t-10.5 93t10.5 93t28.5 59t43 31t55 9zM894 370q30 0 55 -9t43 -31t28.5 -59t10.5 -93t-10.5 -93t-28.5 -59t-43 -31t-55 -9t-55 9t-43 31t-28.5 59t-10.5 93t10.5 93t28.5 59t43 31t55 9zM162 646
q-31 0 -41 -30t-10 -100t10 -100t41 -30t41 30t10 100t-10 100t-41 30zM563 708l-319 -722h-75l317 722h77zM894 308q-31 0 -41 -30t-10 -100t10 -100t41 -30t41 30t10 100t-10 100t-41 30z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="240" 
d="M48 346l144 135v-110l-88 -82l88 -82v-110l-144 135v114z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="240" 
d="M192 232l-144 -135v110l88 82l-88 82v110l144 -135v-114z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="167" 
d="M257 725h74l-421 -756h-74z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="480" 
d="M446 547q-7 14 -19 29.5t-33 29.5t-40 18t-40 4q-24 0 -44 -6.5t-37 -26.5q-13 -14 -21 -33t-13 -39t-7 -38.5t-3 -31.5h224l-24 -68h-204q-1 -33 0 -70h185l-24 -68h-157q0 -11 1.5 -29t6.5 -39t13.5 -41.5t22.5 -36.5q17 -19 37 -27t46 -8q20 0 41.5 5t43.5 16
q20 10 35 23.5t28 33.5v-110q-21 -18 -46 -28q-15 -7 -31.5 -11t-30.5 -6t-25 -2.5t-17 -0.5q-58 0 -94 13.5t-56 29.5q-28 22 -45 52.5t-26.5 61.5t-13 59t-4.5 45h-75l24 68h47q-1 37 0 70h-71l24 68h51q6 88 32 137.5t57 74.5q19 15 39 23.5t39.5 13t37 5.5t31.5 1
q24 0 50 -3.5t54 -15.5q38 -17 65 -50z" />
    <glyph glyph-name="afii61289" unicode="&#x2113;" horiz-adv-x="500" 
d="M434 46q-68 -60 -149 -60q-66 0 -105 36.5t-39 100.5v80l-57 -55h-2l-37 54l95 85v249q0 42 11.5 76t32 58t48.5 37.5t61 13.5q58 0 93 -37t35 -101q0 -75 -44.5 -151.5t-135.5 -161.5v-131q0 -23 6 -38t16 -24t22 -12.5t24 -3.5q20 0 42.5 9t45.5 29h2zM339 576
q0 33 -13.5 55t-33.5 22t-35.5 -25t-15.5 -74v-197q49 55 73.5 111.5t24.5 107.5z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="1000" 
d="M828 628h-2l-121 -326h-56l-121 326h-2v-326h-80v412h119l112 -292l112 292h119v-412h-80v326zM155 646h-123v68h332v-68h-123v-344h-86v344z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="755" 
d="M428 0v187q40 8 70.5 27.5t50.5 48t30.5 64t10.5 75.5q0 47 -16 88t-44.5 71.5t-68.5 48t-88 17.5t-88.5 -17.5t-70 -48t-45.5 -71.5t-16 -89q0 -81 40.5 -138.5t121.5 -75.5v-187h-269v86h186v53q-46 13 -80.5 39t-58 61.5t-35.5 78.5t-12 89q0 69 26 126.5t70.5 99
t103.5 65t126 23.5t126.5 -22.5t103.5 -64t69.5 -98.5t25.5 -126q0 -46 -12 -89.5t-35 -80t-58 -63t-81 -38.5v-53h186v-86h-269z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="600" 
d="M157 259v-181q59 -59 145 -59q63 0 109 28.5t84 91.5l35 -21q-23 -36 -46.5 -61.5t-51 -42t-59.5 -24t-71 -7.5q-59 0 -106.5 21.5t-81.5 59t-52 87.5t-18 108t18 108.5t52 87.5t81.5 58.5t106.5 21.5q58 0 105.5 -20.5t81.5 -57t53 -87t19 -111.5h-404zM448 295v145
q-63 60 -147 60t-144 -60v-145h291z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="541" 
d="M395 286q-27 32 -58 51t-76 19q-32 0 -55.5 -12t-39.5 -32t-23.5 -46.5t-7.5 -55.5q0 -28 7 -54t22 -46.5t37.5 -33t53.5 -12.5q41 0 68 22.5t43 56t22.5 72t6.5 71.5zM499 360q0 -62 -10 -127.5t-37 -120t-75.5 -89.5t-125.5 -35q-49 0 -88.5 17.5t-67.5 48.5t-43 72
t-15 89q0 46 15 86t42.5 69.5t66 46t86.5 16.5q45 0 81 -16t68 -46l1 33q1 37 -6.5 79t-26 77.5t-50 58.5t-77.5 23q-34 0 -62.5 -14t-54.5 -34l-54 63q39 30 78 47t90 17q76 0 127 -32.5t81.5 -84.5t43.5 -116t13 -128z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="682" 
d="M334 597l-187 -504h382zM10 0l273 712h104l285 -712h-662z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="733" 
d="M568 -214v907h-402v-907h-102v1000h606v-1000h-102z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="637" 
d="M25 -214v81l309 424l-296 415v80h562v-93h-427l283 -401l-302 -414h458v-92h-587z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="600" 
d="M47 296h506v-86h-506v86z" />
    <glyph glyph-name="uni2215" unicode="&#x2215;" horiz-adv-x="167" 
d="M257 725h74l-421 -756h-74z" />
    <glyph glyph-name="uni2219" unicode="&#x2219;" horiz-adv-x="240" 
d="M48 308q0 30 21 51t51 21t51 -21t21 -51t-21 -51t-51 -21t-51 21t-21 51z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="526" 
d="M327 -214h-97l-155 322l-49 -23l-37 80l136 63l137 -289l173 847h103z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="796" 
d="M346 256l-29.5 28.5t-35 28.5t-38.5 22t-40 9q-36 0 -57 -26.5t-21 -60.5q0 -37 22 -60t60 -23q19 0 38.5 7.5t38 19.5t34.5 26.5t28 28.5zM671 257q0 37 -21 62.5t-60 25.5q-20 0 -39 -9t-37 -22t-34 -29l-29 -29l30 -29t33.5 -27t37 -19.5t40.5 -7.5q38 0 58.5 24
t20.5 60zM752 260q0 -36 -11 -68t-31.5 -55.5t-50.5 -37t-68 -13.5q-30 0 -56.5 9.5t-50 25.5t-45 36t-40.5 41l-41.5 -40.5t-43.5 -35.5t-49.5 -25.5t-57.5 -9.5q-38 0 -68 13t-51 36t-32.5 54.5t-11.5 67.5t11 67.5t31.5 54.5t50 36.5t67.5 13.5q32 0 59 -10t50.5 -26.5
t44.5 -37.5l41 -43q19 22 39.5 43t44 37.5t50.5 27t58 10.5q37 0 67 -13t51 -36.5t32 -54.5t11 -67z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="589" 
d="M461 717q0 -20 -14 -34t-34 -14q-19 0 -29.5 9.5t-16.5 20.5t-10.5 20.5t-11.5 9.5q-14 0 -18 -12t-4 -23q0 -33 1.5 -65.5t1.5 -65.5v-565q0 -36 -4.5 -73.5t-19 -68t-41.5 -50.5t-73 -20q-15 0 -31.5 4t-30.5 12.5t-23 21.5t-9 30q0 20 12 33t32 13q19 0 29 -9.5
t16 -21.5t10 -21.5t12 -9.5q7 0 11.5 5t6 12t2 15t0.5 13q0 24 -1 48t-1 48v543q0 39 0.5 85.5t12.5 86t41 66t85 26.5q15 0 32.5 -4t32.5 -12t24.5 -21t9.5 -32z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="593" 
d="M563 240q-3 -31 -12.5 -60.5t-26.5 -52t-42.5 -36.5t-61.5 -14q-30 0 -66 11t-71.5 23.5l-67.5 23.5t-55 11q-29 0 -46 -22t-21 -48h-65q3 30 12.5 59.5t26.5 52.5t42 37.5t60 14.5q31 0 65 -10.5t68.5 -23.5t67.5 -23.5t62 -10.5q31 0 46 20.5t20 47.5h65zM564 438
q-3 -31 -12.5 -60.5t-26.5 -52t-42.5 -36.5t-60.5 -14q-30 0 -66.5 11t-72 24l-67.5 24t-54 11q-29 0 -46 -22.5t-21 -48.5h-65q3 30 12 59.5t26 52.5t42 37.5t60 14.5q32 0 66 -10.5t68 -23t67 -23t63 -10.5t45.5 20t20.5 47h64z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="593" 
d="M269 102l-78 -150l-83 42l56 108h-126v96h174l61 119h-235v96h284l78 150l83 -42l-55 -108h127v-96h-176l-62 -119h238v-96h-286z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="593" 
d="M38 -34v96h517v-96h-517zM38 271v97l517 180v-101l-387 -127l387 -123v-101z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="593" 
d="M38 -34v96h517v-96h-517zM38 96v101l386 123l-386 127v101l517 -180v-97z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="593" 
d="M296 650l-122 -294l122 -296l123 296zM341 -35h-91l-174 391l174 391h91l176 -391z" />
    <glyph glyph-name="commaaccent" horiz-adv-x="240" 
d="M72 -50h96v-86q0 -45 -23 -63.5t-63 -23.5v33q15 4 24 12.5t11 28.5h-45v99z" />
    <glyph glyph-name="dotlessj" 
d="M165 538v-573q0 -84 -35.5 -116.5t-106.5 -32.5q-23 0 -47 3v83h25q31 0 43.5 15t12.5 58v563h108z" />
    <glyph glyph-name="uniF8FF" unicode="&#xf8ff;" horiz-adv-x="512" 
d="M222 117v67h56v-67h-56zM225 234l-2 17q0 56 13 81q7 13 18 27.5t28 31.5q16 15 26 26.5t15 21t7 18t2 17.5q0 36 -22.5 60.5t-53.5 24.5t-53.5 -22t-28.5 -73l-50 8q7 72 44 104.5t91 32.5t89 -35.5t35 -95.5q0 -32 -12.5 -58.5t-50.5 -61.5q-37 -33 -43 -52
q-6 -20 -6 -72h-46zM462 50v617h-412v-617h412zM512 718v-718h-512v718h512z" />
    <glyph glyph-name="acute.cap" 
d="M116 752h120l-106 -144h-74z" />
    <glyph glyph-name="commaaccent.alt" horiz-adv-x="240" 
d="M170 615h-96v86q0 45 23 63.5t63 23.5v-33q-15 -4 -24 -12.5t-11 -28.5h45v-99z" />
    <glyph glyph-name="grave.cap" 
d="M-14 752h120l60 -144h-74z" />
    <glyph glyph-name="breve.cap" 
d="M40 748q0 -9 4.5 -20.5t13.5 -22t22 -17.5t31 -7t31 7t22 17.5t13.5 22t4.5 20.5h62q0 -28 -9 -52.5t-26 -43t-42 -29t-56 -10.5q-32 0 -56.5 10.5t-41.5 29t-26 43t-9 52.5h62z" />
    <glyph glyph-name="caron.cap" 
d="M-33 752h91l54 -88l57 88h86l-91 -144h-108z" />
    <glyph glyph-name="dieresis.cap" 
d="M-20 734h96v-108h-96v108zM146 734h96v-108h-96v108z" />
    <glyph glyph-name="macron.cap" 
d="M252 711v-62h-282v62h282z" />
    <glyph glyph-name="circumflex.cap" 
d="M58 752h108l89 -144h-91l-54 88l-57 -88h-86z" />
    <glyph glyph-name="tilde.cap" 
d="M262 742q0 -17 -5.5 -37.5t-16 -38.5t-27 -30t-37.5 -12q-17 0 -33.5 5.5t-32 12l-29 12t-25.5 5.5q-17 0 -25.5 -12.5t-8.5 -27.5h-62q0 16 5.5 37t17.5 39.5t31 31t47 12.5q12 0 25.5 -5.5l27.5 -11.5q14 -7 28.5 -12.5t27.5 -5.5q17 0 23.5 12t6.5 26h62z" />
    <glyph glyph-name="ring.cap" 
d="M5 696q0 45 30.5 74.5t75.5 29.5t75.5 -29.5t30.5 -74.5t-30.5 -74.5t-75.5 -29.5t-75.5 29.5t-30.5 74.5zM55 696q0 -23 16.5 -39.5t39.5 -16.5t39.5 16.5t16.5 39.5t-16.5 39.5t-39.5 16.5t-39.5 -16.5t-16.5 -39.5z" />
    <glyph glyph-name="hungarumlaut.cap" 
d="M196 752h120l-106 -144h-74zM-4 752h120l-106 -144h-74z" />
    <glyph glyph-name="dotaccent.cap" 
d="M63 734h96v-108h-96v108z" />
    <glyph glyph-name="caron.alt" 
d="M84 714h105l-63 -144h-70z" />
    <hkern u1="&#x20;" u2="&#x201c;" k="37" />
    <hkern u1="&#x20;" u2="&#x2018;" k="37" />
    <hkern u1="&#x20;" u2="&#x21a;" k="37" />
    <hkern u1="&#x20;" u2="&#x178;" k="37" />
    <hkern u1="&#x20;" u2="&#x176;" k="37" />
    <hkern u1="&#x20;" u2="&#x174;" k="37" />
    <hkern u1="&#x20;" u2="&#x164;" k="37" />
    <hkern u1="&#x20;" u2="&#x162;" k="37" />
    <hkern u1="&#x20;" u2="&#x104;" k="37" />
    <hkern u1="&#x20;" u2="&#x102;" k="37" />
    <hkern u1="&#x20;" u2="&#x100;" k="37" />
    <hkern u1="&#x20;" u2="&#xdd;" k="37" />
    <hkern u1="&#x20;" u2="&#xc5;" k="37" />
    <hkern u1="&#x20;" u2="&#xc4;" k="37" />
    <hkern u1="&#x20;" u2="&#xc3;" k="37" />
    <hkern u1="&#x20;" u2="&#xc2;" k="37" />
    <hkern u1="&#x20;" u2="&#xc1;" k="37" />
    <hkern u1="&#x20;" u2="&#xc0;" k="37" />
    <hkern u1="&#x20;" u2="Y" k="37" />
    <hkern u1="&#x20;" u2="W" k="37" />
    <hkern u1="&#x20;" u2="V" k="37" />
    <hkern u1="&#x20;" u2="T" k="37" />
    <hkern u1="&#x20;" u2="A" k="37" />
    <hkern u1="&#x2c;" u2="&#x201d;" k="74" />
    <hkern u1="&#x2c;" u2="&#x2019;" k="74" />
    <hkern u1="&#x2c;" u2="&#x20;" k="37" />
    <hkern u1="&#x2e;" u2="&#x201d;" k="74" />
    <hkern u1="&#x2e;" u2="&#x2019;" k="74" />
    <hkern u1="&#x3a;" u2="&#x20;" k="37" />
    <hkern u1="A" u2="&#x201d;" k="55" />
    <hkern u1="A" u2="&#x2019;" k="55" />
    <hkern u1="A" u2="&#x21a;" k="55" />
    <hkern u1="A" u2="&#x178;" k="55" />
    <hkern u1="A" u2="&#x177;" k="18" />
    <hkern u1="A" u2="&#x176;" k="55" />
    <hkern u1="A" u2="&#x175;" k="18" />
    <hkern u1="A" u2="&#x174;" k="30" />
    <hkern u1="A" u2="&#x164;" k="55" />
    <hkern u1="A" u2="&#x162;" k="55" />
    <hkern u1="A" u2="&#xff;" k="18" />
    <hkern u1="A" u2="&#xfd;" k="18" />
    <hkern u1="A" u2="&#xdd;" k="55" />
    <hkern u1="A" u2="y" k="18" />
    <hkern u1="A" u2="w" k="18" />
    <hkern u1="A" u2="v" k="18" />
    <hkern u1="A" u2="Y" k="55" />
    <hkern u1="A" u2="W" k="30" />
    <hkern u1="A" u2="V" k="30" />
    <hkern u1="A" u2="T" k="55" />
    <hkern u1="B" u2="&#x2e;" k="30" />
    <hkern u1="B" u2="&#x2c;" k="30" />
    <hkern u1="C" u2="&#x2e;" k="18" />
    <hkern u1="C" u2="&#x2c;" k="18" />
    <hkern u1="D" u2="&#x178;" k="18" />
    <hkern u1="D" u2="&#x176;" k="18" />
    <hkern u1="D" u2="&#xdd;" k="18" />
    <hkern u1="D" u2="Y" k="18" />
    <hkern u1="D" u2="&#x2e;" k="40" />
    <hkern u1="D" u2="&#x2c;" k="40" />
    <hkern u1="F" u2="&#x104;" k="37" />
    <hkern u1="F" u2="&#x102;" k="37" />
    <hkern u1="F" u2="&#x100;" k="37" />
    <hkern u1="F" u2="&#xc5;" k="37" />
    <hkern u1="F" u2="&#xc4;" k="37" />
    <hkern u1="F" u2="&#xc3;" k="37" />
    <hkern u1="F" u2="&#xc2;" k="37" />
    <hkern u1="F" u2="&#xc1;" k="37" />
    <hkern u1="F" u2="&#xc0;" k="37" />
    <hkern u1="F" u2="A" k="37" />
    <hkern u1="F" u2="&#x2e;" k="129" />
    <hkern u1="F" u2="&#x2c;" k="129" />
    <hkern u1="G" u2="&#x2e;" k="18" />
    <hkern u1="G" u2="&#x2c;" k="18" />
    <hkern u1="J" u2="&#x2e;" k="40" />
    <hkern u1="J" u2="&#x2c;" k="40" />
    <hkern u1="L" u2="&#x201d;" k="111" />
    <hkern u1="L" u2="&#x2019;" k="111" />
    <hkern u1="L" u2="&#x21a;" k="92" />
    <hkern u1="L" u2="&#x178;" k="92" />
    <hkern u1="L" u2="&#x177;" k="37" />
    <hkern u1="L" u2="&#x176;" k="92" />
    <hkern u1="L" u2="&#x174;" k="74" />
    <hkern u1="L" u2="&#x164;" k="92" />
    <hkern u1="L" u2="&#x162;" k="92" />
    <hkern u1="L" u2="&#xff;" k="37" />
    <hkern u1="L" u2="&#xfd;" k="37" />
    <hkern u1="L" u2="&#xdd;" k="92" />
    <hkern u1="L" u2="y" k="37" />
    <hkern u1="L" u2="Y" k="92" />
    <hkern u1="L" u2="W" k="74" />
    <hkern u1="L" u2="V" k="74" />
    <hkern u1="L" u2="T" k="92" />
    <hkern u1="N" u2="&#x2e;" k="18" />
    <hkern u1="N" u2="&#x2c;" k="18" />
    <hkern u1="O" u2="&#x21a;" k="30" />
    <hkern u1="O" u2="&#x178;" k="40" />
    <hkern u1="O" u2="&#x176;" k="40" />
    <hkern u1="O" u2="&#x164;" k="30" />
    <hkern u1="O" u2="&#x162;" k="30" />
    <hkern u1="O" u2="&#x104;" k="18" />
    <hkern u1="O" u2="&#x102;" k="18" />
    <hkern u1="O" u2="&#x100;" k="18" />
    <hkern u1="O" u2="&#xdd;" k="40" />
    <hkern u1="O" u2="&#xc5;" k="18" />
    <hkern u1="O" u2="&#xc4;" k="18" />
    <hkern u1="O" u2="&#xc3;" k="18" />
    <hkern u1="O" u2="&#xc2;" k="18" />
    <hkern u1="O" u2="&#xc1;" k="18" />
    <hkern u1="O" u2="&#xc0;" k="18" />
    <hkern u1="O" u2="Y" k="40" />
    <hkern u1="O" u2="X" k="18" />
    <hkern u1="O" u2="V" k="18" />
    <hkern u1="O" u2="T" k="30" />
    <hkern u1="O" u2="A" k="18" />
    <hkern u1="O" u2="&#x2e;" k="40" />
    <hkern u1="O" u2="&#x2c;" k="40" />
    <hkern u1="P" u2="&#x104;" k="37" />
    <hkern u1="P" u2="&#x102;" k="37" />
    <hkern u1="P" u2="&#x100;" k="37" />
    <hkern u1="P" u2="&#xc5;" k="37" />
    <hkern u1="P" u2="&#xc4;" k="37" />
    <hkern u1="P" u2="&#xc3;" k="37" />
    <hkern u1="P" u2="&#xc2;" k="37" />
    <hkern u1="P" u2="&#xc1;" k="37" />
    <hkern u1="P" u2="&#xc0;" k="37" />
    <hkern u1="P" u2="A" k="37" />
    <hkern u1="P" u2="&#x2e;" k="150" />
    <hkern u1="P" u2="&#x2c;" k="150" />
    <hkern u1="Q" u2="&#x2e;" k="18" />
    <hkern u1="Q" u2="&#x2c;" k="18" />
    <hkern u1="R" u2="&#x21a;" k="18" />
    <hkern u1="R" u2="&#x178;" k="18" />
    <hkern u1="R" u2="&#x176;" k="18" />
    <hkern u1="R" u2="&#x164;" k="18" />
    <hkern u1="R" u2="&#x162;" k="18" />
    <hkern u1="R" u2="&#xdd;" k="18" />
    <hkern u1="R" u2="Y" k="18" />
    <hkern u1="R" u2="T" k="18" />
    <hkern u1="S" u2="&#x2e;" k="30" />
    <hkern u1="S" u2="&#x2c;" k="30" />
    <hkern u1="T" u2="&#x177;" k="74" />
    <hkern u1="T" u2="&#x175;" k="74" />
    <hkern u1="T" u2="&#x173;" k="74" />
    <hkern u1="T" u2="&#x171;" k="74" />
    <hkern u1="T" u2="&#x16f;" k="74" />
    <hkern u1="T" u2="&#x16d;" k="74" />
    <hkern u1="T" u2="&#x16b;" k="74" />
    <hkern u1="T" u2="&#x169;" k="74" />
    <hkern u1="T" u2="&#x159;" k="74" />
    <hkern u1="T" u2="&#x157;" k="74" />
    <hkern u1="T" u2="&#x155;" k="74" />
    <hkern u1="T" u2="&#x151;" k="74" />
    <hkern u1="T" u2="&#x150;" k="30" />
    <hkern u1="T" u2="&#x14f;" k="74" />
    <hkern u1="T" u2="&#x14e;" k="30" />
    <hkern u1="T" u2="&#x14d;" k="74" />
    <hkern u1="T" u2="&#x14c;" k="30" />
    <hkern u1="T" g2="ij" k="18" />
    <hkern u1="T" u2="&#x12f;" k="18" />
    <hkern u1="T" u2="&#x11b;" k="74" />
    <hkern u1="T" u2="&#x119;" k="74" />
    <hkern u1="T" u2="&#x117;" k="74" />
    <hkern u1="T" u2="&#x115;" k="74" />
    <hkern u1="T" u2="&#x113;" k="74" />
    <hkern u1="T" u2="&#x105;" k="74" />
    <hkern u1="T" u2="&#x104;" k="55" />
    <hkern u1="T" u2="&#x103;" k="74" />
    <hkern u1="T" u2="&#x102;" k="55" />
    <hkern u1="T" u2="&#x101;" k="74" />
    <hkern u1="T" u2="&#x100;" k="55" />
    <hkern u1="T" u2="&#xff;" k="74" />
    <hkern u1="T" u2="&#xfd;" k="74" />
    <hkern u1="T" u2="&#xfc;" k="74" />
    <hkern u1="T" u2="&#xfb;" k="74" />
    <hkern u1="T" u2="&#xfa;" k="74" />
    <hkern u1="T" u2="&#xf9;" k="74" />
    <hkern u1="T" u2="&#xf6;" k="74" />
    <hkern u1="T" u2="&#xf5;" k="74" />
    <hkern u1="T" u2="&#xf4;" k="74" />
    <hkern u1="T" u2="&#xf3;" k="74" />
    <hkern u1="T" u2="&#xf2;" k="74" />
    <hkern u1="T" u2="&#xeb;" k="74" />
    <hkern u1="T" u2="&#xea;" k="74" />
    <hkern u1="T" u2="&#xe9;" k="74" />
    <hkern u1="T" u2="&#xe8;" k="74" />
    <hkern u1="T" u2="&#xe5;" k="74" />
    <hkern u1="T" u2="&#xe4;" k="74" />
    <hkern u1="T" u2="&#xe3;" k="74" />
    <hkern u1="T" u2="&#xe2;" k="74" />
    <hkern u1="T" u2="&#xe1;" k="74" />
    <hkern u1="T" u2="&#xe0;" k="74" />
    <hkern u1="T" u2="&#xd6;" k="30" />
    <hkern u1="T" u2="&#xd5;" k="30" />
    <hkern u1="T" u2="&#xd4;" k="30" />
    <hkern u1="T" u2="&#xd3;" k="30" />
    <hkern u1="T" u2="&#xd2;" k="30" />
    <hkern u1="T" u2="&#xc5;" k="55" />
    <hkern u1="T" u2="&#xc4;" k="55" />
    <hkern u1="T" u2="&#xc3;" k="55" />
    <hkern u1="T" u2="&#xc2;" k="55" />
    <hkern u1="T" u2="&#xc1;" k="55" />
    <hkern u1="T" u2="&#xc0;" k="55" />
    <hkern u1="T" u2="y" k="74" />
    <hkern u1="T" u2="w" k="74" />
    <hkern u1="T" u2="u" k="74" />
    <hkern u1="T" u2="r" k="74" />
    <hkern u1="T" u2="o" k="74" />
    <hkern u1="T" u2="i" k="18" />
    <hkern u1="T" u2="e" k="74" />
    <hkern u1="T" u2="a" k="74" />
    <hkern u1="T" u2="O" k="30" />
    <hkern u1="T" u2="A" k="55" />
    <hkern u1="T" u2="&#x3b;" k="74" />
    <hkern u1="T" u2="&#x3a;" k="74" />
    <hkern u1="T" u2="&#x2e;" k="92" />
    <hkern u1="T" u2="&#x2d;" k="74" />
    <hkern u1="T" u2="&#x2c;" k="92" />
    <hkern u1="U" u2="&#x2e;" k="30" />
    <hkern u1="U" u2="&#x2c;" k="30" />
    <hkern u1="V" u2="&#x173;" k="18" />
    <hkern u1="V" u2="&#x171;" k="18" />
    <hkern u1="V" u2="&#x16f;" k="18" />
    <hkern u1="V" u2="&#x16d;" k="18" />
    <hkern u1="V" u2="&#x16b;" k="18" />
    <hkern u1="V" u2="&#x169;" k="18" />
    <hkern u1="V" u2="&#x151;" k="18" />
    <hkern u1="V" u2="&#x14f;" k="18" />
    <hkern u1="V" u2="&#x14d;" k="18" />
    <hkern u1="V" u2="&#x11b;" k="18" />
    <hkern u1="V" u2="&#x119;" k="18" />
    <hkern u1="V" u2="&#x117;" k="18" />
    <hkern u1="V" u2="&#x115;" k="18" />
    <hkern u1="V" u2="&#x113;" k="18" />
    <hkern u1="V" u2="&#x105;" k="18" />
    <hkern u1="V" u2="&#x104;" k="37" />
    <hkern u1="V" u2="&#x103;" k="18" />
    <hkern u1="V" u2="&#x102;" k="37" />
    <hkern u1="V" u2="&#x101;" k="18" />
    <hkern u1="V" u2="&#x100;" k="37" />
    <hkern u1="V" u2="&#xfc;" k="18" />
    <hkern u1="V" u2="&#xfb;" k="18" />
    <hkern u1="V" u2="&#xfa;" k="18" />
    <hkern u1="V" u2="&#xf9;" k="18" />
    <hkern u1="V" u2="&#xf6;" k="18" />
    <hkern u1="V" u2="&#xf5;" k="18" />
    <hkern u1="V" u2="&#xf4;" k="18" />
    <hkern u1="V" u2="&#xf3;" k="18" />
    <hkern u1="V" u2="&#xf2;" k="18" />
    <hkern u1="V" u2="&#xeb;" k="18" />
    <hkern u1="V" u2="&#xea;" k="18" />
    <hkern u1="V" u2="&#xe9;" k="18" />
    <hkern u1="V" u2="&#xe8;" k="18" />
    <hkern u1="V" u2="&#xe5;" k="18" />
    <hkern u1="V" u2="&#xe4;" k="18" />
    <hkern u1="V" u2="&#xe3;" k="18" />
    <hkern u1="V" u2="&#xe2;" k="18" />
    <hkern u1="V" u2="&#xe1;" k="18" />
    <hkern u1="V" u2="&#xe0;" k="18" />
    <hkern u1="V" u2="&#xc5;" k="37" />
    <hkern u1="V" u2="&#xc4;" k="37" />
    <hkern u1="V" u2="&#xc3;" k="37" />
    <hkern u1="V" u2="&#xc2;" k="37" />
    <hkern u1="V" u2="&#xc1;" k="37" />
    <hkern u1="V" u2="&#xc0;" k="37" />
    <hkern u1="V" u2="u" k="18" />
    <hkern u1="V" u2="o" k="18" />
    <hkern u1="V" u2="e" k="18" />
    <hkern u1="V" u2="a" k="18" />
    <hkern u1="V" u2="A" k="37" />
    <hkern u1="V" u2="&#x3b;" k="18" />
    <hkern u1="V" u2="&#x3a;" k="18" />
    <hkern u1="V" u2="&#x2e;" k="92" />
    <hkern u1="V" u2="&#x2d;" k="18" />
    <hkern u1="V" u2="&#x2c;" k="92" />
    <hkern u1="W" u2="&#x177;" k="18" />
    <hkern u1="W" u2="&#x151;" k="18" />
    <hkern u1="W" u2="&#x14f;" k="18" />
    <hkern u1="W" u2="&#x14d;" k="18" />
    <hkern u1="W" u2="&#x11b;" k="18" />
    <hkern u1="W" u2="&#x119;" k="18" />
    <hkern u1="W" u2="&#x117;" k="18" />
    <hkern u1="W" u2="&#x115;" k="18" />
    <hkern u1="W" u2="&#x113;" k="18" />
    <hkern u1="W" u2="&#x105;" k="18" />
    <hkern u1="W" u2="&#x104;" k="30" />
    <hkern u1="W" u2="&#x103;" k="18" />
    <hkern u1="W" u2="&#x102;" k="30" />
    <hkern u1="W" u2="&#x101;" k="18" />
    <hkern u1="W" u2="&#x100;" k="30" />
    <hkern u1="W" u2="&#xff;" k="18" />
    <hkern u1="W" u2="&#xfd;" k="18" />
    <hkern u1="W" u2="&#xf6;" k="18" />
    <hkern u1="W" u2="&#xf5;" k="18" />
    <hkern u1="W" u2="&#xf4;" k="18" />
    <hkern u1="W" u2="&#xf3;" k="18" />
    <hkern u1="W" u2="&#xf2;" k="18" />
    <hkern u1="W" u2="&#xeb;" k="18" />
    <hkern u1="W" u2="&#xea;" k="18" />
    <hkern u1="W" u2="&#xe9;" k="18" />
    <hkern u1="W" u2="&#xe8;" k="18" />
    <hkern u1="W" u2="&#xe5;" k="18" />
    <hkern u1="W" u2="&#xe4;" k="18" />
    <hkern u1="W" u2="&#xe3;" k="18" />
    <hkern u1="W" u2="&#xe2;" k="18" />
    <hkern u1="W" u2="&#xe1;" k="18" />
    <hkern u1="W" u2="&#xe0;" k="18" />
    <hkern u1="W" u2="&#xc5;" k="30" />
    <hkern u1="W" u2="&#xc4;" k="30" />
    <hkern u1="W" u2="&#xc3;" k="30" />
    <hkern u1="W" u2="&#xc2;" k="30" />
    <hkern u1="W" u2="&#xc1;" k="30" />
    <hkern u1="W" u2="&#xc0;" k="30" />
    <hkern u1="W" u2="y" k="18" />
    <hkern u1="W" u2="o" k="18" />
    <hkern u1="W" u2="e" k="18" />
    <hkern u1="W" u2="a" k="18" />
    <hkern u1="W" u2="A" k="30" />
    <hkern u1="W" u2="&#x2e;" k="74" />
    <hkern u1="W" u2="&#x2d;" k="18" />
    <hkern u1="W" u2="&#x2c;" k="74" />
    <hkern u1="Y" u2="&#x218;" k="18" />
    <hkern u1="Y" u2="&#x173;" k="50" />
    <hkern u1="Y" u2="&#x171;" k="50" />
    <hkern u1="Y" u2="&#x16f;" k="50" />
    <hkern u1="Y" u2="&#x16d;" k="50" />
    <hkern u1="Y" u2="&#x16b;" k="50" />
    <hkern u1="Y" u2="&#x169;" k="50" />
    <hkern u1="Y" u2="&#x160;" k="18" />
    <hkern u1="Y" u2="&#x15e;" k="18" />
    <hkern u1="Y" u2="&#x15c;" k="18" />
    <hkern u1="Y" u2="&#x15a;" k="18" />
    <hkern u1="Y" u2="&#x151;" k="55" />
    <hkern u1="Y" u2="&#x150;" k="40" />
    <hkern u1="Y" u2="&#x14f;" k="55" />
    <hkern u1="Y" u2="&#x14e;" k="40" />
    <hkern u1="Y" u2="&#x14d;" k="55" />
    <hkern u1="Y" u2="&#x14c;" k="40" />
    <hkern u1="Y" g2="ij" k="18" />
    <hkern u1="Y" u2="&#x12f;" k="18" />
    <hkern u1="Y" u2="&#x11b;" k="55" />
    <hkern u1="Y" u2="&#x119;" k="55" />
    <hkern u1="Y" u2="&#x117;" k="55" />
    <hkern u1="Y" u2="&#x115;" k="55" />
    <hkern u1="Y" u2="&#x113;" k="55" />
    <hkern u1="Y" u2="&#x105;" k="55" />
    <hkern u1="Y" u2="&#x104;" k="55" />
    <hkern u1="Y" u2="&#x103;" k="55" />
    <hkern u1="Y" u2="&#x102;" k="55" />
    <hkern u1="Y" u2="&#x101;" k="55" />
    <hkern u1="Y" u2="&#x100;" k="55" />
    <hkern u1="Y" u2="&#xfc;" k="50" />
    <hkern u1="Y" u2="&#xfb;" k="50" />
    <hkern u1="Y" u2="&#xfa;" k="50" />
    <hkern u1="Y" u2="&#xf9;" k="50" />
    <hkern u1="Y" u2="&#xf6;" k="55" />
    <hkern u1="Y" u2="&#xf5;" k="55" />
    <hkern u1="Y" u2="&#xf4;" k="55" />
    <hkern u1="Y" u2="&#xf3;" k="55" />
    <hkern u1="Y" u2="&#xf2;" k="55" />
    <hkern u1="Y" u2="&#xeb;" k="55" />
    <hkern u1="Y" u2="&#xea;" k="55" />
    <hkern u1="Y" u2="&#xe9;" k="55" />
    <hkern u1="Y" u2="&#xe8;" k="55" />
    <hkern u1="Y" u2="&#xe5;" k="55" />
    <hkern u1="Y" u2="&#xe4;" k="55" />
    <hkern u1="Y" u2="&#xe3;" k="55" />
    <hkern u1="Y" u2="&#xe2;" k="55" />
    <hkern u1="Y" u2="&#xe1;" k="55" />
    <hkern u1="Y" u2="&#xe0;" k="55" />
    <hkern u1="Y" u2="&#xd6;" k="40" />
    <hkern u1="Y" u2="&#xd5;" k="40" />
    <hkern u1="Y" u2="&#xd4;" k="40" />
    <hkern u1="Y" u2="&#xd3;" k="40" />
    <hkern u1="Y" u2="&#xd2;" k="40" />
    <hkern u1="Y" u2="&#xc5;" k="55" />
    <hkern u1="Y" u2="&#xc4;" k="55" />
    <hkern u1="Y" u2="&#xc3;" k="55" />
    <hkern u1="Y" u2="&#xc2;" k="55" />
    <hkern u1="Y" u2="&#xc1;" k="55" />
    <hkern u1="Y" u2="&#xc0;" k="55" />
    <hkern u1="Y" u2="u" k="50" />
    <hkern u1="Y" u2="o" k="55" />
    <hkern u1="Y" u2="i" k="18" />
    <hkern u1="Y" u2="e" k="55" />
    <hkern u1="Y" u2="a" k="55" />
    <hkern u1="Y" u2="S" k="18" />
    <hkern u1="Y" u2="O" k="40" />
    <hkern u1="Y" u2="A" k="55" />
    <hkern u1="Y" u2="&#x3b;" k="37" />
    <hkern u1="Y" u2="&#x3a;" k="37" />
    <hkern u1="Y" u2="&#x2e;" k="111" />
    <hkern u1="Y" u2="&#x2d;" k="74" />
    <hkern u1="Y" u2="&#x2c;" k="111" />
    <hkern u1="a" u2="v" k="6" />
    <hkern u1="b" u2="&#x177;" k="12" />
    <hkern u1="b" u2="&#xff;" k="12" />
    <hkern u1="b" u2="&#xfd;" k="12" />
    <hkern u1="b" u2="y" k="12" />
    <hkern u1="b" u2="&#x2e;" k="37" />
    <hkern u1="b" u2="&#x2c;" k="37" />
    <hkern u1="c" u2="&#x177;" k="18" />
    <hkern u1="c" u2="&#x137;" k="6" />
    <hkern u1="c" u2="&#x125;" k="6" />
    <hkern u1="c" u2="&#xff;" k="18" />
    <hkern u1="c" u2="&#xfd;" k="18" />
    <hkern u1="c" u2="y" k="18" />
    <hkern u1="c" u2="k" k="6" />
    <hkern u1="c" u2="h" k="6" />
    <hkern u1="c" u2="&#x2e;" k="37" />
    <hkern u1="c" u2="&#x2c;" k="37" />
    <hkern u1="d" u2="&#x177;" k="6" />
    <hkern u1="d" u2="&#xff;" k="6" />
    <hkern u1="d" u2="&#xfd;" k="6" />
    <hkern u1="d" u2="y" k="6" />
    <hkern u1="e" u2="x" k="6" />
    <hkern u1="e" u2="&#x2e;" k="37" />
    <hkern u1="e" u2="&#x2c;" k="37" />
    <hkern u1="f" u2="&#x201d;" k="-18" />
    <hkern u1="f" u2="&#x2019;" k="-18" />
    <hkern u1="f" u2="&#x2e;" k="37" />
    <hkern u1="f" u2="&#x2c;" k="37" />
    <hkern u1="g" u2="&#x159;" k="-6" />
    <hkern u1="g" u2="&#x157;" k="-6" />
    <hkern u1="g" u2="&#x155;" k="-6" />
    <hkern u1="g" u2="r" k="-6" />
    <hkern u1="g" u2="&#x2e;" k="18" />
    <hkern u1="g" u2="&#x2c;" k="18" />
    <hkern u1="h" u2="&#x177;" k="6" />
    <hkern u1="h" u2="&#xff;" k="6" />
    <hkern u1="h" u2="&#xfd;" k="6" />
    <hkern u1="h" u2="y" k="6" />
    <hkern u1="k" u2="&#x177;" k="6" />
    <hkern u1="k" u2="&#x151;" k="6" />
    <hkern u1="k" u2="&#x14f;" k="6" />
    <hkern u1="k" u2="&#x14d;" k="6" />
    <hkern u1="k" u2="&#x11b;" k="6" />
    <hkern u1="k" u2="&#x119;" k="6" />
    <hkern u1="k" u2="&#x117;" k="6" />
    <hkern u1="k" u2="&#x115;" k="6" />
    <hkern u1="k" u2="&#x113;" k="6" />
    <hkern u1="k" u2="&#xff;" k="6" />
    <hkern u1="k" u2="&#xfd;" k="6" />
    <hkern u1="k" u2="&#xf6;" k="6" />
    <hkern u1="k" u2="&#xf5;" k="6" />
    <hkern u1="k" u2="&#xf4;" k="6" />
    <hkern u1="k" u2="&#xf3;" k="6" />
    <hkern u1="k" u2="&#xf2;" k="6" />
    <hkern u1="k" u2="&#xeb;" k="6" />
    <hkern u1="k" u2="&#xea;" k="6" />
    <hkern u1="k" u2="&#xe9;" k="6" />
    <hkern u1="k" u2="&#xe8;" k="6" />
    <hkern u1="k" u2="y" k="6" />
    <hkern u1="k" u2="o" k="6" />
    <hkern u1="k" u2="e" k="6" />
    <hkern u1="o" u2="&#x177;" k="6" />
    <hkern u1="o" u2="&#x175;" k="6" />
    <hkern u1="o" u2="&#xff;" k="6" />
    <hkern u1="o" u2="&#xfd;" k="6" />
    <hkern u1="o" u2="y" k="6" />
    <hkern u1="o" u2="w" k="6" />
    <hkern u1="o" u2="v" k="6" />
    <hkern u1="o" u2="&#x2e;" k="18" />
    <hkern u1="o" u2="&#x2c;" k="18" />
    <hkern u1="p" u2="&#x177;" k="6" />
    <hkern u1="p" u2="&#xff;" k="6" />
    <hkern u1="p" u2="&#xfd;" k="6" />
    <hkern u1="p" u2="y" k="6" />
    <hkern u1="p" u2="&#x2e;" k="18" />
    <hkern u1="p" u2="&#x2c;" k="18" />
    <hkern u1="r" u2="&#x2019;" k="-10" />
    <hkern u1="r" u2="&#x177;" k="-18" />
    <hkern u1="r" g2="napostrophe" k="-10" />
    <hkern u1="r" u2="&#x148;" k="-10" />
    <hkern u1="r" u2="&#x146;" k="-10" />
    <hkern u1="r" u2="&#x144;" k="-10" />
    <hkern u1="r" u2="&#x123;" k="6" />
    <hkern u1="r" u2="&#x121;" k="6" />
    <hkern u1="r" u2="&#x11f;" k="6" />
    <hkern u1="r" u2="&#x11d;" k="6" />
    <hkern u1="r" u2="&#x11b;" k="6" />
    <hkern u1="r" u2="&#x119;" k="6" />
    <hkern u1="r" u2="&#x117;" k="6" />
    <hkern u1="r" u2="&#x115;" k="6" />
    <hkern u1="r" u2="&#x113;" k="6" />
    <hkern u1="r" u2="&#x10f;" k="6" />
    <hkern u1="r" u2="&#x10d;" k="6" />
    <hkern u1="r" u2="&#x10b;" k="6" />
    <hkern u1="r" u2="&#x109;" k="6" />
    <hkern u1="r" u2="&#x107;" k="6" />
    <hkern u1="r" u2="&#x105;" k="6" />
    <hkern u1="r" u2="&#x103;" k="6" />
    <hkern u1="r" u2="&#x101;" k="6" />
    <hkern u1="r" u2="&#xff;" k="-18" />
    <hkern u1="r" u2="&#xfd;" k="-18" />
    <hkern u1="r" u2="&#xf1;" k="-10" />
    <hkern u1="r" u2="&#xeb;" k="6" />
    <hkern u1="r" u2="&#xea;" k="6" />
    <hkern u1="r" u2="&#xe9;" k="6" />
    <hkern u1="r" u2="&#xe8;" k="6" />
    <hkern u1="r" u2="&#xe7;" k="6" />
    <hkern u1="r" u2="&#xe5;" k="6" />
    <hkern u1="r" u2="&#xe4;" k="6" />
    <hkern u1="r" u2="&#xe3;" k="6" />
    <hkern u1="r" u2="&#xe2;" k="6" />
    <hkern u1="r" u2="&#xe1;" k="6" />
    <hkern u1="r" u2="&#xe0;" k="6" />
    <hkern u1="r" u2="y" k="-18" />
    <hkern u1="r" u2="v" k="-18" />
    <hkern u1="r" u2="q" k="12" />
    <hkern u1="r" u2="n" k="-10" />
    <hkern u1="r" u2="m" k="-10" />
    <hkern u1="r" u2="g" k="6" />
    <hkern u1="r" u2="e" k="6" />
    <hkern u1="r" u2="d" k="6" />
    <hkern u1="r" u2="c" k="6" />
    <hkern u1="r" u2="a" k="6" />
    <hkern u1="r" u2="&#x2e;" k="92" />
    <hkern u1="r" u2="&#x2d;" k="55" />
    <hkern u1="r" u2="&#x2c;" k="92" />
    <hkern u1="v" u2="&#x105;" k="12" />
    <hkern u1="v" u2="&#x103;" k="12" />
    <hkern u1="v" u2="&#x101;" k="12" />
    <hkern u1="v" u2="&#xe5;" k="12" />
    <hkern u1="v" u2="&#xe4;" k="12" />
    <hkern u1="v" u2="&#xe3;" k="12" />
    <hkern u1="v" u2="&#xe2;" k="12" />
    <hkern u1="v" u2="&#xe1;" k="12" />
    <hkern u1="v" u2="&#xe0;" k="12" />
    <hkern u1="v" u2="a" k="12" />
    <hkern u1="v" u2="&#x2e;" k="74" />
    <hkern u1="v" u2="&#x2c;" k="74" />
    <hkern u1="w" u2="&#x151;" k="6" />
    <hkern u1="w" u2="&#x14f;" k="6" />
    <hkern u1="w" u2="&#x14d;" k="6" />
    <hkern u1="w" u2="&#x105;" k="12" />
    <hkern u1="w" u2="&#x103;" k="12" />
    <hkern u1="w" u2="&#x101;" k="12" />
    <hkern u1="w" u2="&#xf6;" k="6" />
    <hkern u1="w" u2="&#xf5;" k="6" />
    <hkern u1="w" u2="&#xf4;" k="6" />
    <hkern u1="w" u2="&#xf3;" k="6" />
    <hkern u1="w" u2="&#xf2;" k="6" />
    <hkern u1="w" u2="&#xe5;" k="12" />
    <hkern u1="w" u2="&#xe4;" k="12" />
    <hkern u1="w" u2="&#xe3;" k="12" />
    <hkern u1="w" u2="&#xe2;" k="12" />
    <hkern u1="w" u2="&#xe1;" k="12" />
    <hkern u1="w" u2="&#xe0;" k="12" />
    <hkern u1="w" u2="o" k="6" />
    <hkern u1="w" u2="a" k="12" />
    <hkern u1="w" u2="&#x2e;" k="55" />
    <hkern u1="w" u2="&#x2c;" k="55" />
    <hkern u1="y" u2="&#x11b;" k="6" />
    <hkern u1="y" u2="&#x119;" k="6" />
    <hkern u1="y" u2="&#x117;" k="6" />
    <hkern u1="y" u2="&#x115;" k="6" />
    <hkern u1="y" u2="&#x113;" k="6" />
    <hkern u1="y" u2="&#xeb;" k="6" />
    <hkern u1="y" u2="&#xea;" k="6" />
    <hkern u1="y" u2="&#xe9;" k="6" />
    <hkern u1="y" u2="&#xe8;" k="6" />
    <hkern u1="y" u2="e" k="6" />
    <hkern u1="y" u2="&#x2e;" k="74" />
    <hkern u1="y" u2="&#x2c;" k="74" />
    <hkern u1="&#xc0;" u2="&#x201d;" k="55" />
    <hkern u1="&#xc0;" u2="&#x2019;" k="55" />
    <hkern u1="&#xc0;" u2="&#x21a;" k="55" />
    <hkern u1="&#xc0;" u2="&#x178;" k="55" />
    <hkern u1="&#xc0;" u2="&#x177;" k="18" />
    <hkern u1="&#xc0;" u2="&#x176;" k="55" />
    <hkern u1="&#xc0;" u2="&#x175;" k="18" />
    <hkern u1="&#xc0;" u2="&#x174;" k="30" />
    <hkern u1="&#xc0;" u2="&#x164;" k="55" />
    <hkern u1="&#xc0;" u2="&#x162;" k="55" />
    <hkern u1="&#xc0;" u2="&#xff;" k="18" />
    <hkern u1="&#xc0;" u2="&#xfd;" k="18" />
    <hkern u1="&#xc0;" u2="&#xdd;" k="55" />
    <hkern u1="&#xc0;" u2="y" k="18" />
    <hkern u1="&#xc0;" u2="w" k="18" />
    <hkern u1="&#xc0;" u2="v" k="18" />
    <hkern u1="&#xc0;" u2="Y" k="55" />
    <hkern u1="&#xc0;" u2="W" k="30" />
    <hkern u1="&#xc0;" u2="V" k="30" />
    <hkern u1="&#xc0;" u2="T" k="55" />
    <hkern u1="&#xc1;" u2="&#x201d;" k="55" />
    <hkern u1="&#xc1;" u2="&#x2019;" k="55" />
    <hkern u1="&#xc1;" u2="&#x21a;" k="55" />
    <hkern u1="&#xc1;" u2="&#x178;" k="55" />
    <hkern u1="&#xc1;" u2="&#x177;" k="18" />
    <hkern u1="&#xc1;" u2="&#x176;" k="55" />
    <hkern u1="&#xc1;" u2="&#x175;" k="18" />
    <hkern u1="&#xc1;" u2="&#x174;" k="30" />
    <hkern u1="&#xc1;" u2="&#x164;" k="55" />
    <hkern u1="&#xc1;" u2="&#x162;" k="55" />
    <hkern u1="&#xc1;" u2="&#xff;" k="18" />
    <hkern u1="&#xc1;" u2="&#xfd;" k="18" />
    <hkern u1="&#xc1;" u2="&#xdd;" k="55" />
    <hkern u1="&#xc1;" u2="y" k="18" />
    <hkern u1="&#xc1;" u2="w" k="18" />
    <hkern u1="&#xc1;" u2="v" k="18" />
    <hkern u1="&#xc1;" u2="Y" k="55" />
    <hkern u1="&#xc1;" u2="W" k="30" />
    <hkern u1="&#xc1;" u2="V" k="30" />
    <hkern u1="&#xc1;" u2="T" k="55" />
    <hkern u1="&#xc2;" u2="&#x201d;" k="55" />
    <hkern u1="&#xc2;" u2="&#x2019;" k="55" />
    <hkern u1="&#xc2;" u2="&#x21a;" k="55" />
    <hkern u1="&#xc2;" u2="&#x178;" k="55" />
    <hkern u1="&#xc2;" u2="&#x177;" k="18" />
    <hkern u1="&#xc2;" u2="&#x176;" k="55" />
    <hkern u1="&#xc2;" u2="&#x175;" k="18" />
    <hkern u1="&#xc2;" u2="&#x174;" k="30" />
    <hkern u1="&#xc2;" u2="&#x164;" k="55" />
    <hkern u1="&#xc2;" u2="&#x162;" k="55" />
    <hkern u1="&#xc2;" u2="&#xff;" k="18" />
    <hkern u1="&#xc2;" u2="&#xfd;" k="18" />
    <hkern u1="&#xc2;" u2="&#xdd;" k="55" />
    <hkern u1="&#xc2;" u2="y" k="18" />
    <hkern u1="&#xc2;" u2="w" k="18" />
    <hkern u1="&#xc2;" u2="v" k="18" />
    <hkern u1="&#xc2;" u2="Y" k="55" />
    <hkern u1="&#xc2;" u2="W" k="30" />
    <hkern u1="&#xc2;" u2="V" k="30" />
    <hkern u1="&#xc2;" u2="T" k="55" />
    <hkern u1="&#xc3;" u2="&#x201d;" k="55" />
    <hkern u1="&#xc3;" u2="&#x2019;" k="55" />
    <hkern u1="&#xc3;" u2="&#x21a;" k="55" />
    <hkern u1="&#xc3;" u2="&#x178;" k="55" />
    <hkern u1="&#xc3;" u2="&#x177;" k="18" />
    <hkern u1="&#xc3;" u2="&#x176;" k="55" />
    <hkern u1="&#xc3;" u2="&#x175;" k="18" />
    <hkern u1="&#xc3;" u2="&#x174;" k="30" />
    <hkern u1="&#xc3;" u2="&#x164;" k="55" />
    <hkern u1="&#xc3;" u2="&#x162;" k="55" />
    <hkern u1="&#xc3;" u2="&#xff;" k="18" />
    <hkern u1="&#xc3;" u2="&#xfd;" k="18" />
    <hkern u1="&#xc3;" u2="&#xdd;" k="55" />
    <hkern u1="&#xc3;" u2="y" k="18" />
    <hkern u1="&#xc3;" u2="w" k="18" />
    <hkern u1="&#xc3;" u2="v" k="18" />
    <hkern u1="&#xc3;" u2="Y" k="55" />
    <hkern u1="&#xc3;" u2="W" k="30" />
    <hkern u1="&#xc3;" u2="V" k="30" />
    <hkern u1="&#xc3;" u2="T" k="55" />
    <hkern u1="&#xc4;" u2="&#x201d;" k="55" />
    <hkern u1="&#xc4;" u2="&#x2019;" k="55" />
    <hkern u1="&#xc4;" u2="&#x21a;" k="55" />
    <hkern u1="&#xc4;" u2="&#x178;" k="55" />
    <hkern u1="&#xc4;" u2="&#x177;" k="18" />
    <hkern u1="&#xc4;" u2="&#x176;" k="55" />
    <hkern u1="&#xc4;" u2="&#x175;" k="18" />
    <hkern u1="&#xc4;" u2="&#x174;" k="30" />
    <hkern u1="&#xc4;" u2="&#x164;" k="55" />
    <hkern u1="&#xc4;" u2="&#x162;" k="55" />
    <hkern u1="&#xc4;" u2="&#xff;" k="18" />
    <hkern u1="&#xc4;" u2="&#xfd;" k="18" />
    <hkern u1="&#xc4;" u2="&#xdd;" k="55" />
    <hkern u1="&#xc4;" u2="y" k="18" />
    <hkern u1="&#xc4;" u2="w" k="18" />
    <hkern u1="&#xc4;" u2="v" k="18" />
    <hkern u1="&#xc4;" u2="Y" k="55" />
    <hkern u1="&#xc4;" u2="W" k="30" />
    <hkern u1="&#xc4;" u2="V" k="30" />
    <hkern u1="&#xc4;" u2="T" k="55" />
    <hkern u1="&#xc5;" u2="&#x201d;" k="55" />
    <hkern u1="&#xc5;" u2="&#x2019;" k="55" />
    <hkern u1="&#xc5;" u2="&#x21a;" k="55" />
    <hkern u1="&#xc5;" u2="&#x178;" k="55" />
    <hkern u1="&#xc5;" u2="&#x177;" k="18" />
    <hkern u1="&#xc5;" u2="&#x176;" k="55" />
    <hkern u1="&#xc5;" u2="&#x175;" k="18" />
    <hkern u1="&#xc5;" u2="&#x174;" k="30" />
    <hkern u1="&#xc5;" u2="&#x164;" k="55" />
    <hkern u1="&#xc5;" u2="&#x162;" k="55" />
    <hkern u1="&#xc5;" u2="&#xff;" k="18" />
    <hkern u1="&#xc5;" u2="&#xfd;" k="18" />
    <hkern u1="&#xc5;" u2="&#xdd;" k="55" />
    <hkern u1="&#xc5;" u2="y" k="18" />
    <hkern u1="&#xc5;" u2="w" k="18" />
    <hkern u1="&#xc5;" u2="v" k="18" />
    <hkern u1="&#xc5;" u2="Y" k="55" />
    <hkern u1="&#xc5;" u2="W" k="30" />
    <hkern u1="&#xc5;" u2="V" k="30" />
    <hkern u1="&#xc5;" u2="T" k="55" />
    <hkern u1="&#xc7;" u2="&#x2e;" k="18" />
    <hkern u1="&#xc7;" u2="&#x2c;" k="18" />
    <hkern u1="&#xd1;" u2="&#x2e;" k="18" />
    <hkern u1="&#xd1;" u2="&#x2c;" k="18" />
    <hkern u1="&#xd2;" u2="&#x21a;" k="30" />
    <hkern u1="&#xd2;" u2="&#x178;" k="40" />
    <hkern u1="&#xd2;" u2="&#x176;" k="40" />
    <hkern u1="&#xd2;" u2="&#x164;" k="30" />
    <hkern u1="&#xd2;" u2="&#x162;" k="30" />
    <hkern u1="&#xd2;" u2="&#x104;" k="18" />
    <hkern u1="&#xd2;" u2="&#x102;" k="18" />
    <hkern u1="&#xd2;" u2="&#x100;" k="18" />
    <hkern u1="&#xd2;" u2="&#xdd;" k="40" />
    <hkern u1="&#xd2;" u2="&#xc5;" k="18" />
    <hkern u1="&#xd2;" u2="&#xc4;" k="18" />
    <hkern u1="&#xd2;" u2="&#xc3;" k="18" />
    <hkern u1="&#xd2;" u2="&#xc2;" k="18" />
    <hkern u1="&#xd2;" u2="&#xc1;" k="18" />
    <hkern u1="&#xd2;" u2="&#xc0;" k="18" />
    <hkern u1="&#xd2;" u2="Y" k="40" />
    <hkern u1="&#xd2;" u2="X" k="18" />
    <hkern u1="&#xd2;" u2="V" k="18" />
    <hkern u1="&#xd2;" u2="T" k="30" />
    <hkern u1="&#xd2;" u2="A" k="18" />
    <hkern u1="&#xd2;" u2="&#x2e;" k="40" />
    <hkern u1="&#xd2;" u2="&#x2c;" k="40" />
    <hkern u1="&#xd3;" u2="&#x21a;" k="30" />
    <hkern u1="&#xd3;" u2="&#x178;" k="40" />
    <hkern u1="&#xd3;" u2="&#x176;" k="40" />
    <hkern u1="&#xd3;" u2="&#x164;" k="30" />
    <hkern u1="&#xd3;" u2="&#x162;" k="30" />
    <hkern u1="&#xd3;" u2="&#x104;" k="18" />
    <hkern u1="&#xd3;" u2="&#x102;" k="18" />
    <hkern u1="&#xd3;" u2="&#x100;" k="18" />
    <hkern u1="&#xd3;" u2="&#xdd;" k="40" />
    <hkern u1="&#xd3;" u2="&#xc5;" k="18" />
    <hkern u1="&#xd3;" u2="&#xc4;" k="18" />
    <hkern u1="&#xd3;" u2="&#xc3;" k="18" />
    <hkern u1="&#xd3;" u2="&#xc2;" k="18" />
    <hkern u1="&#xd3;" u2="&#xc1;" k="18" />
    <hkern u1="&#xd3;" u2="&#xc0;" k="18" />
    <hkern u1="&#xd3;" u2="Y" k="40" />
    <hkern u1="&#xd3;" u2="X" k="18" />
    <hkern u1="&#xd3;" u2="V" k="18" />
    <hkern u1="&#xd3;" u2="T" k="30" />
    <hkern u1="&#xd3;" u2="A" k="18" />
    <hkern u1="&#xd3;" u2="&#x2e;" k="40" />
    <hkern u1="&#xd3;" u2="&#x2c;" k="40" />
    <hkern u1="&#xd4;" u2="&#x21a;" k="30" />
    <hkern u1="&#xd4;" u2="&#x178;" k="40" />
    <hkern u1="&#xd4;" u2="&#x176;" k="40" />
    <hkern u1="&#xd4;" u2="&#x164;" k="30" />
    <hkern u1="&#xd4;" u2="&#x162;" k="30" />
    <hkern u1="&#xd4;" u2="&#x104;" k="18" />
    <hkern u1="&#xd4;" u2="&#x102;" k="18" />
    <hkern u1="&#xd4;" u2="&#x100;" k="18" />
    <hkern u1="&#xd4;" u2="&#xdd;" k="40" />
    <hkern u1="&#xd4;" u2="&#xc5;" k="18" />
    <hkern u1="&#xd4;" u2="&#xc4;" k="18" />
    <hkern u1="&#xd4;" u2="&#xc3;" k="18" />
    <hkern u1="&#xd4;" u2="&#xc2;" k="18" />
    <hkern u1="&#xd4;" u2="&#xc1;" k="18" />
    <hkern u1="&#xd4;" u2="&#xc0;" k="18" />
    <hkern u1="&#xd4;" u2="Y" k="40" />
    <hkern u1="&#xd4;" u2="X" k="18" />
    <hkern u1="&#xd4;" u2="V" k="18" />
    <hkern u1="&#xd4;" u2="T" k="30" />
    <hkern u1="&#xd4;" u2="A" k="18" />
    <hkern u1="&#xd4;" u2="&#x2e;" k="40" />
    <hkern u1="&#xd4;" u2="&#x2c;" k="40" />
    <hkern u1="&#xd5;" u2="&#x21a;" k="30" />
    <hkern u1="&#xd5;" u2="&#x178;" k="40" />
    <hkern u1="&#xd5;" u2="&#x176;" k="40" />
    <hkern u1="&#xd5;" u2="&#x164;" k="30" />
    <hkern u1="&#xd5;" u2="&#x162;" k="30" />
    <hkern u1="&#xd5;" u2="&#x104;" k="18" />
    <hkern u1="&#xd5;" u2="&#x102;" k="18" />
    <hkern u1="&#xd5;" u2="&#x100;" k="18" />
    <hkern u1="&#xd5;" u2="&#xdd;" k="40" />
    <hkern u1="&#xd5;" u2="&#xc5;" k="18" />
    <hkern u1="&#xd5;" u2="&#xc4;" k="18" />
    <hkern u1="&#xd5;" u2="&#xc3;" k="18" />
    <hkern u1="&#xd5;" u2="&#xc2;" k="18" />
    <hkern u1="&#xd5;" u2="&#xc1;" k="18" />
    <hkern u1="&#xd5;" u2="&#xc0;" k="18" />
    <hkern u1="&#xd5;" u2="Y" k="40" />
    <hkern u1="&#xd5;" u2="X" k="18" />
    <hkern u1="&#xd5;" u2="V" k="18" />
    <hkern u1="&#xd5;" u2="T" k="30" />
    <hkern u1="&#xd5;" u2="A" k="18" />
    <hkern u1="&#xd5;" u2="&#x2e;" k="40" />
    <hkern u1="&#xd5;" u2="&#x2c;" k="40" />
    <hkern u1="&#xd6;" u2="&#x21a;" k="30" />
    <hkern u1="&#xd6;" u2="&#x178;" k="40" />
    <hkern u1="&#xd6;" u2="&#x176;" k="40" />
    <hkern u1="&#xd6;" u2="&#x164;" k="30" />
    <hkern u1="&#xd6;" u2="&#x162;" k="30" />
    <hkern u1="&#xd6;" u2="&#x104;" k="18" />
    <hkern u1="&#xd6;" u2="&#x102;" k="18" />
    <hkern u1="&#xd6;" u2="&#x100;" k="18" />
    <hkern u1="&#xd6;" u2="&#xdd;" k="40" />
    <hkern u1="&#xd6;" u2="&#xc5;" k="18" />
    <hkern u1="&#xd6;" u2="&#xc4;" k="18" />
    <hkern u1="&#xd6;" u2="&#xc3;" k="18" />
    <hkern u1="&#xd6;" u2="&#xc2;" k="18" />
    <hkern u1="&#xd6;" u2="&#xc1;" k="18" />
    <hkern u1="&#xd6;" u2="&#xc0;" k="18" />
    <hkern u1="&#xd6;" u2="Y" k="40" />
    <hkern u1="&#xd6;" u2="X" k="18" />
    <hkern u1="&#xd6;" u2="V" k="18" />
    <hkern u1="&#xd6;" u2="T" k="30" />
    <hkern u1="&#xd6;" u2="A" k="18" />
    <hkern u1="&#xd6;" u2="&#x2e;" k="40" />
    <hkern u1="&#xd6;" u2="&#x2c;" k="40" />
    <hkern u1="&#xd9;" u2="&#x2e;" k="30" />
    <hkern u1="&#xd9;" u2="&#x2c;" k="30" />
    <hkern u1="&#xda;" u2="&#x2e;" k="30" />
    <hkern u1="&#xda;" u2="&#x2c;" k="30" />
    <hkern u1="&#xdb;" u2="&#x2e;" k="30" />
    <hkern u1="&#xdb;" u2="&#x2c;" k="30" />
    <hkern u1="&#xdc;" u2="&#x2e;" k="30" />
    <hkern u1="&#xdc;" u2="&#x2c;" k="30" />
    <hkern u1="&#xdd;" u2="&#x218;" k="18" />
    <hkern u1="&#xdd;" u2="&#x173;" k="50" />
    <hkern u1="&#xdd;" u2="&#x171;" k="50" />
    <hkern u1="&#xdd;" u2="&#x16f;" k="50" />
    <hkern u1="&#xdd;" u2="&#x16d;" k="50" />
    <hkern u1="&#xdd;" u2="&#x16b;" k="50" />
    <hkern u1="&#xdd;" u2="&#x169;" k="50" />
    <hkern u1="&#xdd;" u2="&#x160;" k="18" />
    <hkern u1="&#xdd;" u2="&#x15e;" k="18" />
    <hkern u1="&#xdd;" u2="&#x15c;" k="18" />
    <hkern u1="&#xdd;" u2="&#x15a;" k="18" />
    <hkern u1="&#xdd;" u2="&#x151;" k="55" />
    <hkern u1="&#xdd;" u2="&#x150;" k="40" />
    <hkern u1="&#xdd;" u2="&#x14f;" k="55" />
    <hkern u1="&#xdd;" u2="&#x14e;" k="40" />
    <hkern u1="&#xdd;" u2="&#x14d;" k="55" />
    <hkern u1="&#xdd;" u2="&#x14c;" k="40" />
    <hkern u1="&#xdd;" g2="ij" k="18" />
    <hkern u1="&#xdd;" u2="&#x12f;" k="18" />
    <hkern u1="&#xdd;" u2="&#x11b;" k="55" />
    <hkern u1="&#xdd;" u2="&#x119;" k="55" />
    <hkern u1="&#xdd;" u2="&#x117;" k="55" />
    <hkern u1="&#xdd;" u2="&#x115;" k="55" />
    <hkern u1="&#xdd;" u2="&#x113;" k="55" />
    <hkern u1="&#xdd;" u2="&#x105;" k="55" />
    <hkern u1="&#xdd;" u2="&#x104;" k="55" />
    <hkern u1="&#xdd;" u2="&#x103;" k="55" />
    <hkern u1="&#xdd;" u2="&#x102;" k="55" />
    <hkern u1="&#xdd;" u2="&#x101;" k="55" />
    <hkern u1="&#xdd;" u2="&#x100;" k="55" />
    <hkern u1="&#xdd;" u2="&#xfc;" k="50" />
    <hkern u1="&#xdd;" u2="&#xfb;" k="50" />
    <hkern u1="&#xdd;" u2="&#xfa;" k="50" />
    <hkern u1="&#xdd;" u2="&#xf9;" k="50" />
    <hkern u1="&#xdd;" u2="&#xf6;" k="55" />
    <hkern u1="&#xdd;" u2="&#xf5;" k="55" />
    <hkern u1="&#xdd;" u2="&#xf4;" k="55" />
    <hkern u1="&#xdd;" u2="&#xf3;" k="55" />
    <hkern u1="&#xdd;" u2="&#xf2;" k="55" />
    <hkern u1="&#xdd;" u2="&#xeb;" k="55" />
    <hkern u1="&#xdd;" u2="&#xea;" k="55" />
    <hkern u1="&#xdd;" u2="&#xe9;" k="55" />
    <hkern u1="&#xdd;" u2="&#xe8;" k="55" />
    <hkern u1="&#xdd;" u2="&#xe5;" k="55" />
    <hkern u1="&#xdd;" u2="&#xe4;" k="55" />
    <hkern u1="&#xdd;" u2="&#xe3;" k="55" />
    <hkern u1="&#xdd;" u2="&#xe2;" k="55" />
    <hkern u1="&#xdd;" u2="&#xe1;" k="55" />
    <hkern u1="&#xdd;" u2="&#xe0;" k="55" />
    <hkern u1="&#xdd;" u2="&#xd6;" k="40" />
    <hkern u1="&#xdd;" u2="&#xd5;" k="40" />
    <hkern u1="&#xdd;" u2="&#xd4;" k="40" />
    <hkern u1="&#xdd;" u2="&#xd3;" k="40" />
    <hkern u1="&#xdd;" u2="&#xd2;" k="40" />
    <hkern u1="&#xdd;" u2="&#xc5;" k="55" />
    <hkern u1="&#xdd;" u2="&#xc4;" k="55" />
    <hkern u1="&#xdd;" u2="&#xc3;" k="55" />
    <hkern u1="&#xdd;" u2="&#xc2;" k="55" />
    <hkern u1="&#xdd;" u2="&#xc1;" k="55" />
    <hkern u1="&#xdd;" u2="&#xc0;" k="55" />
    <hkern u1="&#xdd;" u2="u" k="50" />
    <hkern u1="&#xdd;" u2="o" k="55" />
    <hkern u1="&#xdd;" u2="i" k="18" />
    <hkern u1="&#xdd;" u2="e" k="55" />
    <hkern u1="&#xdd;" u2="a" k="55" />
    <hkern u1="&#xdd;" u2="S" k="18" />
    <hkern u1="&#xdd;" u2="O" k="40" />
    <hkern u1="&#xdd;" u2="A" k="55" />
    <hkern u1="&#xdd;" u2="&#x3b;" k="37" />
    <hkern u1="&#xdd;" u2="&#x3a;" k="37" />
    <hkern u1="&#xdd;" u2="&#x2e;" k="111" />
    <hkern u1="&#xdd;" u2="&#x2d;" k="74" />
    <hkern u1="&#xdd;" u2="&#x2c;" k="111" />
    <hkern u1="&#xe0;" u2="v" k="6" />
    <hkern u1="&#xe1;" u2="v" k="6" />
    <hkern u1="&#xe2;" u2="v" k="6" />
    <hkern u1="&#xe3;" u2="v" k="6" />
    <hkern u1="&#xe4;" u2="v" k="6" />
    <hkern u1="&#xe5;" u2="v" k="6" />
    <hkern u1="&#xe7;" u2="&#x177;" k="18" />
    <hkern u1="&#xe7;" u2="&#x137;" k="6" />
    <hkern u1="&#xe7;" u2="&#x125;" k="6" />
    <hkern u1="&#xe7;" u2="&#xff;" k="18" />
    <hkern u1="&#xe7;" u2="&#xfd;" k="18" />
    <hkern u1="&#xe7;" u2="y" k="18" />
    <hkern u1="&#xe7;" u2="k" k="6" />
    <hkern u1="&#xe7;" u2="h" k="6" />
    <hkern u1="&#xe7;" u2="&#x2e;" k="37" />
    <hkern u1="&#xe7;" u2="&#x2c;" k="37" />
    <hkern u1="&#xe8;" u2="x" k="6" />
    <hkern u1="&#xe8;" u2="&#x2e;" k="37" />
    <hkern u1="&#xe8;" u2="&#x2c;" k="37" />
    <hkern u1="&#xe9;" u2="x" k="6" />
    <hkern u1="&#xe9;" u2="&#x2e;" k="37" />
    <hkern u1="&#xe9;" u2="&#x2c;" k="37" />
    <hkern u1="&#xea;" u2="x" k="6" />
    <hkern u1="&#xea;" u2="&#x2e;" k="37" />
    <hkern u1="&#xea;" u2="&#x2c;" k="37" />
    <hkern u1="&#xeb;" u2="x" k="6" />
    <hkern u1="&#xeb;" u2="&#x2e;" k="37" />
    <hkern u1="&#xeb;" u2="&#x2c;" k="37" />
    <hkern u1="&#xf2;" u2="&#x177;" k="6" />
    <hkern u1="&#xf2;" u2="&#x175;" k="6" />
    <hkern u1="&#xf2;" u2="&#xff;" k="6" />
    <hkern u1="&#xf2;" u2="&#xfd;" k="6" />
    <hkern u1="&#xf2;" u2="y" k="6" />
    <hkern u1="&#xf2;" u2="w" k="6" />
    <hkern u1="&#xf2;" u2="v" k="6" />
    <hkern u1="&#xf2;" u2="&#x2e;" k="18" />
    <hkern u1="&#xf2;" u2="&#x2c;" k="18" />
    <hkern u1="&#xf3;" u2="&#x177;" k="6" />
    <hkern u1="&#xf3;" u2="&#x175;" k="6" />
    <hkern u1="&#xf3;" u2="&#xff;" k="6" />
    <hkern u1="&#xf3;" u2="&#xfd;" k="6" />
    <hkern u1="&#xf3;" u2="y" k="6" />
    <hkern u1="&#xf3;" u2="w" k="6" />
    <hkern u1="&#xf3;" u2="v" k="6" />
    <hkern u1="&#xf3;" u2="&#x2e;" k="18" />
    <hkern u1="&#xf3;" u2="&#x2c;" k="18" />
    <hkern u1="&#xf4;" u2="&#x177;" k="6" />
    <hkern u1="&#xf4;" u2="&#x175;" k="6" />
    <hkern u1="&#xf4;" u2="&#xff;" k="6" />
    <hkern u1="&#xf4;" u2="&#xfd;" k="6" />
    <hkern u1="&#xf4;" u2="y" k="6" />
    <hkern u1="&#xf4;" u2="w" k="6" />
    <hkern u1="&#xf4;" u2="v" k="6" />
    <hkern u1="&#xf4;" u2="&#x2e;" k="18" />
    <hkern u1="&#xf4;" u2="&#x2c;" k="18" />
    <hkern u1="&#xf5;" u2="&#x177;" k="6" />
    <hkern u1="&#xf5;" u2="&#x175;" k="6" />
    <hkern u1="&#xf5;" u2="&#xff;" k="6" />
    <hkern u1="&#xf5;" u2="&#xfd;" k="6" />
    <hkern u1="&#xf5;" u2="y" k="6" />
    <hkern u1="&#xf5;" u2="w" k="6" />
    <hkern u1="&#xf5;" u2="v" k="6" />
    <hkern u1="&#xf5;" u2="&#x2e;" k="18" />
    <hkern u1="&#xf5;" u2="&#x2c;" k="18" />
    <hkern u1="&#xf6;" u2="&#x177;" k="6" />
    <hkern u1="&#xf6;" u2="&#x175;" k="6" />
    <hkern u1="&#xf6;" u2="&#xff;" k="6" />
    <hkern u1="&#xf6;" u2="&#xfd;" k="6" />
    <hkern u1="&#xf6;" u2="y" k="6" />
    <hkern u1="&#xf6;" u2="w" k="6" />
    <hkern u1="&#xf6;" u2="v" k="6" />
    <hkern u1="&#xf6;" u2="&#x2e;" k="18" />
    <hkern u1="&#xf6;" u2="&#x2c;" k="18" />
    <hkern u1="&#xfd;" u2="&#x11b;" k="6" />
    <hkern u1="&#xfd;" u2="&#x119;" k="6" />
    <hkern u1="&#xfd;" u2="&#x117;" k="6" />
    <hkern u1="&#xfd;" u2="&#x115;" k="6" />
    <hkern u1="&#xfd;" u2="&#x113;" k="6" />
    <hkern u1="&#xfd;" u2="&#xeb;" k="6" />
    <hkern u1="&#xfd;" u2="&#xea;" k="6" />
    <hkern u1="&#xfd;" u2="&#xe9;" k="6" />
    <hkern u1="&#xfd;" u2="&#xe8;" k="6" />
    <hkern u1="&#xfd;" u2="e" k="6" />
    <hkern u1="&#xfd;" u2="&#x2e;" k="74" />
    <hkern u1="&#xfd;" u2="&#x2c;" k="74" />
    <hkern u1="&#xff;" u2="&#x11b;" k="6" />
    <hkern u1="&#xff;" u2="&#x119;" k="6" />
    <hkern u1="&#xff;" u2="&#x117;" k="6" />
    <hkern u1="&#xff;" u2="&#x115;" k="6" />
    <hkern u1="&#xff;" u2="&#x113;" k="6" />
    <hkern u1="&#xff;" u2="&#xeb;" k="6" />
    <hkern u1="&#xff;" u2="&#xea;" k="6" />
    <hkern u1="&#xff;" u2="&#xe9;" k="6" />
    <hkern u1="&#xff;" u2="&#xe8;" k="6" />
    <hkern u1="&#xff;" u2="e" k="6" />
    <hkern u1="&#xff;" u2="&#x2e;" k="74" />
    <hkern u1="&#xff;" u2="&#x2c;" k="74" />
    <hkern u1="&#x100;" u2="&#x201d;" k="55" />
    <hkern u1="&#x100;" u2="&#x2019;" k="55" />
    <hkern u1="&#x100;" u2="&#x21a;" k="55" />
    <hkern u1="&#x100;" u2="&#x178;" k="55" />
    <hkern u1="&#x100;" u2="&#x177;" k="18" />
    <hkern u1="&#x100;" u2="&#x176;" k="55" />
    <hkern u1="&#x100;" u2="&#x175;" k="18" />
    <hkern u1="&#x100;" u2="&#x174;" k="30" />
    <hkern u1="&#x100;" u2="&#x164;" k="55" />
    <hkern u1="&#x100;" u2="&#x162;" k="55" />
    <hkern u1="&#x100;" u2="&#xff;" k="18" />
    <hkern u1="&#x100;" u2="&#xfd;" k="18" />
    <hkern u1="&#x100;" u2="&#xdd;" k="55" />
    <hkern u1="&#x100;" u2="y" k="18" />
    <hkern u1="&#x100;" u2="w" k="18" />
    <hkern u1="&#x100;" u2="v" k="18" />
    <hkern u1="&#x100;" u2="Y" k="55" />
    <hkern u1="&#x100;" u2="W" k="30" />
    <hkern u1="&#x100;" u2="V" k="30" />
    <hkern u1="&#x100;" u2="T" k="55" />
    <hkern u1="&#x101;" u2="v" k="6" />
    <hkern u1="&#x102;" u2="&#x201d;" k="55" />
    <hkern u1="&#x102;" u2="&#x2019;" k="55" />
    <hkern u1="&#x102;" u2="&#x21a;" k="55" />
    <hkern u1="&#x102;" u2="&#x178;" k="55" />
    <hkern u1="&#x102;" u2="&#x177;" k="18" />
    <hkern u1="&#x102;" u2="&#x176;" k="55" />
    <hkern u1="&#x102;" u2="&#x175;" k="18" />
    <hkern u1="&#x102;" u2="&#x174;" k="30" />
    <hkern u1="&#x102;" u2="&#x164;" k="55" />
    <hkern u1="&#x102;" u2="&#x162;" k="55" />
    <hkern u1="&#x102;" u2="&#xff;" k="18" />
    <hkern u1="&#x102;" u2="&#xfd;" k="18" />
    <hkern u1="&#x102;" u2="&#xdd;" k="55" />
    <hkern u1="&#x102;" u2="y" k="18" />
    <hkern u1="&#x102;" u2="w" k="18" />
    <hkern u1="&#x102;" u2="v" k="18" />
    <hkern u1="&#x102;" u2="Y" k="55" />
    <hkern u1="&#x102;" u2="W" k="30" />
    <hkern u1="&#x102;" u2="V" k="30" />
    <hkern u1="&#x102;" u2="T" k="55" />
    <hkern u1="&#x103;" u2="v" k="6" />
    <hkern u1="&#x104;" u2="&#x201d;" k="55" />
    <hkern u1="&#x104;" u2="&#x2019;" k="55" />
    <hkern u1="&#x104;" u2="&#x21a;" k="55" />
    <hkern u1="&#x104;" u2="&#x178;" k="55" />
    <hkern u1="&#x104;" u2="&#x177;" k="18" />
    <hkern u1="&#x104;" u2="&#x176;" k="55" />
    <hkern u1="&#x104;" u2="&#x175;" k="18" />
    <hkern u1="&#x104;" u2="&#x174;" k="30" />
    <hkern u1="&#x104;" u2="&#x164;" k="55" />
    <hkern u1="&#x104;" u2="&#x162;" k="55" />
    <hkern u1="&#x104;" u2="&#xff;" k="18" />
    <hkern u1="&#x104;" u2="&#xfd;" k="18" />
    <hkern u1="&#x104;" u2="&#xdd;" k="55" />
    <hkern u1="&#x104;" u2="y" k="18" />
    <hkern u1="&#x104;" u2="w" k="18" />
    <hkern u1="&#x104;" u2="v" k="18" />
    <hkern u1="&#x104;" u2="Y" k="55" />
    <hkern u1="&#x104;" u2="W" k="30" />
    <hkern u1="&#x104;" u2="V" k="30" />
    <hkern u1="&#x104;" u2="T" k="55" />
    <hkern u1="&#x105;" u2="v" k="6" />
    <hkern u1="&#x106;" u2="&#x2e;" k="18" />
    <hkern u1="&#x106;" u2="&#x2c;" k="18" />
    <hkern u1="&#x107;" u2="&#x177;" k="18" />
    <hkern u1="&#x107;" u2="&#x137;" k="6" />
    <hkern u1="&#x107;" u2="&#x125;" k="6" />
    <hkern u1="&#x107;" u2="&#xff;" k="18" />
    <hkern u1="&#x107;" u2="&#xfd;" k="18" />
    <hkern u1="&#x107;" u2="y" k="18" />
    <hkern u1="&#x107;" u2="k" k="6" />
    <hkern u1="&#x107;" u2="h" k="6" />
    <hkern u1="&#x107;" u2="&#x2e;" k="37" />
    <hkern u1="&#x107;" u2="&#x2c;" k="37" />
    <hkern u1="&#x108;" u2="&#x2e;" k="18" />
    <hkern u1="&#x108;" u2="&#x2c;" k="18" />
    <hkern u1="&#x109;" u2="&#x177;" k="18" />
    <hkern u1="&#x109;" u2="&#x137;" k="6" />
    <hkern u1="&#x109;" u2="&#x125;" k="6" />
    <hkern u1="&#x109;" u2="&#xff;" k="18" />
    <hkern u1="&#x109;" u2="&#xfd;" k="18" />
    <hkern u1="&#x109;" u2="y" k="18" />
    <hkern u1="&#x109;" u2="k" k="6" />
    <hkern u1="&#x109;" u2="h" k="6" />
    <hkern u1="&#x109;" u2="&#x2e;" k="37" />
    <hkern u1="&#x109;" u2="&#x2c;" k="37" />
    <hkern u1="&#x10a;" u2="&#x2e;" k="18" />
    <hkern u1="&#x10a;" u2="&#x2c;" k="18" />
    <hkern u1="&#x10b;" u2="&#x177;" k="18" />
    <hkern u1="&#x10b;" u2="&#x137;" k="6" />
    <hkern u1="&#x10b;" u2="&#x125;" k="6" />
    <hkern u1="&#x10b;" u2="&#xff;" k="18" />
    <hkern u1="&#x10b;" u2="&#xfd;" k="18" />
    <hkern u1="&#x10b;" u2="y" k="18" />
    <hkern u1="&#x10b;" u2="k" k="6" />
    <hkern u1="&#x10b;" u2="h" k="6" />
    <hkern u1="&#x10b;" u2="&#x2e;" k="37" />
    <hkern u1="&#x10b;" u2="&#x2c;" k="37" />
    <hkern u1="&#x10c;" u2="&#x2e;" k="18" />
    <hkern u1="&#x10c;" u2="&#x2c;" k="18" />
    <hkern u1="&#x10d;" u2="&#x177;" k="18" />
    <hkern u1="&#x10d;" u2="&#x137;" k="6" />
    <hkern u1="&#x10d;" u2="&#x125;" k="6" />
    <hkern u1="&#x10d;" u2="&#xff;" k="18" />
    <hkern u1="&#x10d;" u2="&#xfd;" k="18" />
    <hkern u1="&#x10d;" u2="y" k="18" />
    <hkern u1="&#x10d;" u2="k" k="6" />
    <hkern u1="&#x10d;" u2="h" k="6" />
    <hkern u1="&#x10d;" u2="&#x2e;" k="37" />
    <hkern u1="&#x10d;" u2="&#x2c;" k="37" />
    <hkern u1="&#x10e;" u2="&#x178;" k="18" />
    <hkern u1="&#x10e;" u2="&#x176;" k="18" />
    <hkern u1="&#x10e;" u2="&#xdd;" k="18" />
    <hkern u1="&#x10e;" u2="Y" k="18" />
    <hkern u1="&#x10e;" u2="&#x2e;" k="40" />
    <hkern u1="&#x10e;" u2="&#x2c;" k="40" />
    <hkern u1="&#x10f;" u2="&#x177;" k="6" />
    <hkern u1="&#x10f;" u2="&#xff;" k="6" />
    <hkern u1="&#x10f;" u2="&#xfd;" k="6" />
    <hkern u1="&#x10f;" u2="y" k="6" />
    <hkern u1="&#x113;" u2="x" k="6" />
    <hkern u1="&#x113;" u2="&#x2e;" k="37" />
    <hkern u1="&#x113;" u2="&#x2c;" k="37" />
    <hkern u1="&#x115;" u2="x" k="6" />
    <hkern u1="&#x115;" u2="&#x2e;" k="37" />
    <hkern u1="&#x115;" u2="&#x2c;" k="37" />
    <hkern u1="&#x117;" u2="x" k="6" />
    <hkern u1="&#x117;" u2="&#x2e;" k="37" />
    <hkern u1="&#x117;" u2="&#x2c;" k="37" />
    <hkern u1="&#x119;" u2="x" k="6" />
    <hkern u1="&#x119;" u2="&#x2e;" k="37" />
    <hkern u1="&#x119;" u2="&#x2c;" k="37" />
    <hkern u1="&#x11b;" u2="x" k="6" />
    <hkern u1="&#x11b;" u2="&#x2e;" k="37" />
    <hkern u1="&#x11b;" u2="&#x2c;" k="37" />
    <hkern u1="&#x11c;" u2="&#x2e;" k="18" />
    <hkern u1="&#x11c;" u2="&#x2c;" k="18" />
    <hkern u1="&#x11d;" u2="&#x159;" k="-6" />
    <hkern u1="&#x11d;" u2="&#x157;" k="-6" />
    <hkern u1="&#x11d;" u2="&#x155;" k="-6" />
    <hkern u1="&#x11d;" u2="r" k="-6" />
    <hkern u1="&#x11d;" u2="&#x2e;" k="18" />
    <hkern u1="&#x11d;" u2="&#x2c;" k="18" />
    <hkern u1="&#x11e;" u2="&#x2e;" k="18" />
    <hkern u1="&#x11e;" u2="&#x2c;" k="18" />
    <hkern u1="&#x11f;" u2="&#x159;" k="-6" />
    <hkern u1="&#x11f;" u2="&#x157;" k="-6" />
    <hkern u1="&#x11f;" u2="&#x155;" k="-6" />
    <hkern u1="&#x11f;" u2="r" k="-6" />
    <hkern u1="&#x11f;" u2="&#x2e;" k="18" />
    <hkern u1="&#x11f;" u2="&#x2c;" k="18" />
    <hkern u1="&#x120;" u2="&#x2e;" k="18" />
    <hkern u1="&#x120;" u2="&#x2c;" k="18" />
    <hkern u1="&#x121;" u2="&#x159;" k="-6" />
    <hkern u1="&#x121;" u2="&#x157;" k="-6" />
    <hkern u1="&#x121;" u2="&#x155;" k="-6" />
    <hkern u1="&#x121;" u2="r" k="-6" />
    <hkern u1="&#x121;" u2="&#x2e;" k="18" />
    <hkern u1="&#x121;" u2="&#x2c;" k="18" />
    <hkern u1="&#x122;" u2="&#x2e;" k="18" />
    <hkern u1="&#x122;" u2="&#x2c;" k="18" />
    <hkern u1="&#x123;" u2="&#x159;" k="-6" />
    <hkern u1="&#x123;" u2="&#x157;" k="-6" />
    <hkern u1="&#x123;" u2="&#x155;" k="-6" />
    <hkern u1="&#x123;" u2="r" k="-6" />
    <hkern u1="&#x123;" u2="&#x2e;" k="18" />
    <hkern u1="&#x123;" u2="&#x2c;" k="18" />
    <hkern u1="&#x125;" u2="&#x177;" k="6" />
    <hkern u1="&#x125;" u2="&#xff;" k="6" />
    <hkern u1="&#x125;" u2="&#xfd;" k="6" />
    <hkern u1="&#x125;" u2="y" k="6" />
    <hkern u1="&#x134;" u2="&#x2e;" k="40" />
    <hkern u1="&#x134;" u2="&#x2c;" k="40" />
    <hkern u1="&#x137;" u2="&#x177;" k="6" />
    <hkern u1="&#x137;" u2="&#x151;" k="6" />
    <hkern u1="&#x137;" u2="&#x14f;" k="6" />
    <hkern u1="&#x137;" u2="&#x14d;" k="6" />
    <hkern u1="&#x137;" u2="&#x11b;" k="6" />
    <hkern u1="&#x137;" u2="&#x119;" k="6" />
    <hkern u1="&#x137;" u2="&#x117;" k="6" />
    <hkern u1="&#x137;" u2="&#x115;" k="6" />
    <hkern u1="&#x137;" u2="&#x113;" k="6" />
    <hkern u1="&#x137;" u2="&#xff;" k="6" />
    <hkern u1="&#x137;" u2="&#xfd;" k="6" />
    <hkern u1="&#x137;" u2="&#xf6;" k="6" />
    <hkern u1="&#x137;" u2="&#xf5;" k="6" />
    <hkern u1="&#x137;" u2="&#xf4;" k="6" />
    <hkern u1="&#x137;" u2="&#xf3;" k="6" />
    <hkern u1="&#x137;" u2="&#xf2;" k="6" />
    <hkern u1="&#x137;" u2="&#xeb;" k="6" />
    <hkern u1="&#x137;" u2="&#xea;" k="6" />
    <hkern u1="&#x137;" u2="&#xe9;" k="6" />
    <hkern u1="&#x137;" u2="&#xe8;" k="6" />
    <hkern u1="&#x137;" u2="y" k="6" />
    <hkern u1="&#x137;" u2="o" k="6" />
    <hkern u1="&#x137;" u2="e" k="6" />
    <hkern u1="&#x139;" u2="&#x201d;" k="111" />
    <hkern u1="&#x139;" u2="&#x2019;" k="111" />
    <hkern u1="&#x139;" u2="&#x21a;" k="92" />
    <hkern u1="&#x139;" u2="&#x178;" k="92" />
    <hkern u1="&#x139;" u2="&#x177;" k="37" />
    <hkern u1="&#x139;" u2="&#x176;" k="92" />
    <hkern u1="&#x139;" u2="&#x174;" k="74" />
    <hkern u1="&#x139;" u2="&#x164;" k="92" />
    <hkern u1="&#x139;" u2="&#x162;" k="92" />
    <hkern u1="&#x139;" u2="&#xff;" k="37" />
    <hkern u1="&#x139;" u2="&#xfd;" k="37" />
    <hkern u1="&#x139;" u2="&#xdd;" k="92" />
    <hkern u1="&#x139;" u2="y" k="37" />
    <hkern u1="&#x139;" u2="Y" k="92" />
    <hkern u1="&#x139;" u2="W" k="74" />
    <hkern u1="&#x139;" u2="V" k="74" />
    <hkern u1="&#x139;" u2="T" k="92" />
    <hkern u1="&#x13b;" u2="&#x201d;" k="111" />
    <hkern u1="&#x13b;" u2="&#x2019;" k="111" />
    <hkern u1="&#x13b;" u2="&#x21a;" k="92" />
    <hkern u1="&#x13b;" u2="&#x178;" k="92" />
    <hkern u1="&#x13b;" u2="&#x177;" k="37" />
    <hkern u1="&#x13b;" u2="&#x176;" k="92" />
    <hkern u1="&#x13b;" u2="&#x174;" k="74" />
    <hkern u1="&#x13b;" u2="&#x164;" k="92" />
    <hkern u1="&#x13b;" u2="&#x162;" k="92" />
    <hkern u1="&#x13b;" u2="&#xff;" k="37" />
    <hkern u1="&#x13b;" u2="&#xfd;" k="37" />
    <hkern u1="&#x13b;" u2="&#xdd;" k="92" />
    <hkern u1="&#x13b;" u2="y" k="37" />
    <hkern u1="&#x13b;" u2="Y" k="92" />
    <hkern u1="&#x13b;" u2="W" k="74" />
    <hkern u1="&#x13b;" u2="V" k="74" />
    <hkern u1="&#x13b;" u2="T" k="92" />
    <hkern u1="&#x13d;" u2="&#x201d;" k="111" />
    <hkern u1="&#x13d;" u2="&#x2019;" k="111" />
    <hkern u1="&#x13d;" u2="&#x21a;" k="92" />
    <hkern u1="&#x13d;" u2="&#x178;" k="92" />
    <hkern u1="&#x13d;" u2="&#x177;" k="37" />
    <hkern u1="&#x13d;" u2="&#x176;" k="92" />
    <hkern u1="&#x13d;" u2="&#x174;" k="74" />
    <hkern u1="&#x13d;" u2="&#x164;" k="92" />
    <hkern u1="&#x13d;" u2="&#x162;" k="92" />
    <hkern u1="&#x13d;" u2="&#xff;" k="37" />
    <hkern u1="&#x13d;" u2="&#xfd;" k="37" />
    <hkern u1="&#x13d;" u2="&#xdd;" k="92" />
    <hkern u1="&#x13d;" u2="y" k="37" />
    <hkern u1="&#x13d;" u2="Y" k="92" />
    <hkern u1="&#x13d;" u2="W" k="74" />
    <hkern u1="&#x13d;" u2="V" k="74" />
    <hkern u1="&#x13d;" u2="T" k="92" />
    <hkern u1="&#x13f;" u2="&#x201d;" k="111" />
    <hkern u1="&#x13f;" u2="&#x2019;" k="111" />
    <hkern u1="&#x13f;" u2="&#x21a;" k="92" />
    <hkern u1="&#x13f;" u2="&#x178;" k="92" />
    <hkern u1="&#x13f;" u2="&#x177;" k="37" />
    <hkern u1="&#x13f;" u2="&#x176;" k="92" />
    <hkern u1="&#x13f;" u2="&#x174;" k="74" />
    <hkern u1="&#x13f;" u2="&#x164;" k="92" />
    <hkern u1="&#x13f;" u2="&#x162;" k="92" />
    <hkern u1="&#x13f;" u2="&#xff;" k="37" />
    <hkern u1="&#x13f;" u2="&#xfd;" k="37" />
    <hkern u1="&#x13f;" u2="&#xdd;" k="92" />
    <hkern u1="&#x13f;" u2="y" k="37" />
    <hkern u1="&#x13f;" u2="Y" k="92" />
    <hkern u1="&#x13f;" u2="W" k="74" />
    <hkern u1="&#x13f;" u2="V" k="74" />
    <hkern u1="&#x13f;" u2="T" k="92" />
    <hkern u1="&#x143;" u2="&#x2e;" k="18" />
    <hkern u1="&#x143;" u2="&#x2c;" k="18" />
    <hkern u1="&#x145;" u2="&#x2e;" k="18" />
    <hkern u1="&#x145;" u2="&#x2c;" k="18" />
    <hkern u1="&#x147;" u2="&#x2e;" k="18" />
    <hkern u1="&#x147;" u2="&#x2c;" k="18" />
    <hkern u1="&#x14c;" u2="&#x21a;" k="30" />
    <hkern u1="&#x14c;" u2="&#x178;" k="40" />
    <hkern u1="&#x14c;" u2="&#x176;" k="40" />
    <hkern u1="&#x14c;" u2="&#x164;" k="30" />
    <hkern u1="&#x14c;" u2="&#x162;" k="30" />
    <hkern u1="&#x14c;" u2="&#x104;" k="18" />
    <hkern u1="&#x14c;" u2="&#x102;" k="18" />
    <hkern u1="&#x14c;" u2="&#x100;" k="18" />
    <hkern u1="&#x14c;" u2="&#xdd;" k="40" />
    <hkern u1="&#x14c;" u2="&#xc5;" k="18" />
    <hkern u1="&#x14c;" u2="&#xc4;" k="18" />
    <hkern u1="&#x14c;" u2="&#xc3;" k="18" />
    <hkern u1="&#x14c;" u2="&#xc2;" k="18" />
    <hkern u1="&#x14c;" u2="&#xc1;" k="18" />
    <hkern u1="&#x14c;" u2="&#xc0;" k="18" />
    <hkern u1="&#x14c;" u2="Y" k="40" />
    <hkern u1="&#x14c;" u2="X" k="18" />
    <hkern u1="&#x14c;" u2="V" k="18" />
    <hkern u1="&#x14c;" u2="T" k="30" />
    <hkern u1="&#x14c;" u2="A" k="18" />
    <hkern u1="&#x14c;" u2="&#x2e;" k="40" />
    <hkern u1="&#x14c;" u2="&#x2c;" k="40" />
    <hkern u1="&#x14d;" u2="&#x177;" k="6" />
    <hkern u1="&#x14d;" u2="&#x175;" k="6" />
    <hkern u1="&#x14d;" u2="&#xff;" k="6" />
    <hkern u1="&#x14d;" u2="&#xfd;" k="6" />
    <hkern u1="&#x14d;" u2="y" k="6" />
    <hkern u1="&#x14d;" u2="w" k="6" />
    <hkern u1="&#x14d;" u2="v" k="6" />
    <hkern u1="&#x14d;" u2="&#x2e;" k="18" />
    <hkern u1="&#x14d;" u2="&#x2c;" k="18" />
    <hkern u1="&#x14e;" u2="&#x21a;" k="30" />
    <hkern u1="&#x14e;" u2="&#x178;" k="40" />
    <hkern u1="&#x14e;" u2="&#x176;" k="40" />
    <hkern u1="&#x14e;" u2="&#x164;" k="30" />
    <hkern u1="&#x14e;" u2="&#x162;" k="30" />
    <hkern u1="&#x14e;" u2="&#x104;" k="18" />
    <hkern u1="&#x14e;" u2="&#x102;" k="18" />
    <hkern u1="&#x14e;" u2="&#x100;" k="18" />
    <hkern u1="&#x14e;" u2="&#xdd;" k="40" />
    <hkern u1="&#x14e;" u2="&#xc5;" k="18" />
    <hkern u1="&#x14e;" u2="&#xc4;" k="18" />
    <hkern u1="&#x14e;" u2="&#xc3;" k="18" />
    <hkern u1="&#x14e;" u2="&#xc2;" k="18" />
    <hkern u1="&#x14e;" u2="&#xc1;" k="18" />
    <hkern u1="&#x14e;" u2="&#xc0;" k="18" />
    <hkern u1="&#x14e;" u2="Y" k="40" />
    <hkern u1="&#x14e;" u2="X" k="18" />
    <hkern u1="&#x14e;" u2="V" k="18" />
    <hkern u1="&#x14e;" u2="T" k="30" />
    <hkern u1="&#x14e;" u2="A" k="18" />
    <hkern u1="&#x14e;" u2="&#x2e;" k="40" />
    <hkern u1="&#x14e;" u2="&#x2c;" k="40" />
    <hkern u1="&#x14f;" u2="&#x177;" k="6" />
    <hkern u1="&#x14f;" u2="&#x175;" k="6" />
    <hkern u1="&#x14f;" u2="&#xff;" k="6" />
    <hkern u1="&#x14f;" u2="&#xfd;" k="6" />
    <hkern u1="&#x14f;" u2="y" k="6" />
    <hkern u1="&#x14f;" u2="w" k="6" />
    <hkern u1="&#x14f;" u2="v" k="6" />
    <hkern u1="&#x14f;" u2="&#x2e;" k="18" />
    <hkern u1="&#x14f;" u2="&#x2c;" k="18" />
    <hkern u1="&#x150;" u2="&#x21a;" k="30" />
    <hkern u1="&#x150;" u2="&#x178;" k="40" />
    <hkern u1="&#x150;" u2="&#x176;" k="40" />
    <hkern u1="&#x150;" u2="&#x164;" k="30" />
    <hkern u1="&#x150;" u2="&#x162;" k="30" />
    <hkern u1="&#x150;" u2="&#x104;" k="18" />
    <hkern u1="&#x150;" u2="&#x102;" k="18" />
    <hkern u1="&#x150;" u2="&#x100;" k="18" />
    <hkern u1="&#x150;" u2="&#xdd;" k="40" />
    <hkern u1="&#x150;" u2="&#xc5;" k="18" />
    <hkern u1="&#x150;" u2="&#xc4;" k="18" />
    <hkern u1="&#x150;" u2="&#xc3;" k="18" />
    <hkern u1="&#x150;" u2="&#xc2;" k="18" />
    <hkern u1="&#x150;" u2="&#xc1;" k="18" />
    <hkern u1="&#x150;" u2="&#xc0;" k="18" />
    <hkern u1="&#x150;" u2="Y" k="40" />
    <hkern u1="&#x150;" u2="X" k="18" />
    <hkern u1="&#x150;" u2="V" k="18" />
    <hkern u1="&#x150;" u2="T" k="30" />
    <hkern u1="&#x150;" u2="A" k="18" />
    <hkern u1="&#x150;" u2="&#x2e;" k="40" />
    <hkern u1="&#x150;" u2="&#x2c;" k="40" />
    <hkern u1="&#x151;" u2="&#x177;" k="6" />
    <hkern u1="&#x151;" u2="&#x175;" k="6" />
    <hkern u1="&#x151;" u2="&#xff;" k="6" />
    <hkern u1="&#x151;" u2="&#xfd;" k="6" />
    <hkern u1="&#x151;" u2="y" k="6" />
    <hkern u1="&#x151;" u2="w" k="6" />
    <hkern u1="&#x151;" u2="v" k="6" />
    <hkern u1="&#x151;" u2="&#x2e;" k="18" />
    <hkern u1="&#x151;" u2="&#x2c;" k="18" />
    <hkern u1="&#x154;" u2="&#x21a;" k="18" />
    <hkern u1="&#x154;" u2="&#x178;" k="18" />
    <hkern u1="&#x154;" u2="&#x176;" k="18" />
    <hkern u1="&#x154;" u2="&#x164;" k="18" />
    <hkern u1="&#x154;" u2="&#x162;" k="18" />
    <hkern u1="&#x154;" u2="&#xdd;" k="18" />
    <hkern u1="&#x154;" u2="Y" k="18" />
    <hkern u1="&#x154;" u2="T" k="18" />
    <hkern u1="&#x155;" u2="&#x2019;" k="-10" />
    <hkern u1="&#x155;" u2="&#x177;" k="-18" />
    <hkern u1="&#x155;" g2="napostrophe" k="-10" />
    <hkern u1="&#x155;" u2="&#x148;" k="-10" />
    <hkern u1="&#x155;" u2="&#x146;" k="-10" />
    <hkern u1="&#x155;" u2="&#x144;" k="-10" />
    <hkern u1="&#x155;" u2="&#x123;" k="6" />
    <hkern u1="&#x155;" u2="&#x121;" k="6" />
    <hkern u1="&#x155;" u2="&#x11f;" k="6" />
    <hkern u1="&#x155;" u2="&#x11d;" k="6" />
    <hkern u1="&#x155;" u2="&#x11b;" k="6" />
    <hkern u1="&#x155;" u2="&#x119;" k="6" />
    <hkern u1="&#x155;" u2="&#x117;" k="6" />
    <hkern u1="&#x155;" u2="&#x115;" k="6" />
    <hkern u1="&#x155;" u2="&#x113;" k="6" />
    <hkern u1="&#x155;" u2="&#x10f;" k="6" />
    <hkern u1="&#x155;" u2="&#x10d;" k="6" />
    <hkern u1="&#x155;" u2="&#x10b;" k="6" />
    <hkern u1="&#x155;" u2="&#x109;" k="6" />
    <hkern u1="&#x155;" u2="&#x107;" k="6" />
    <hkern u1="&#x155;" u2="&#x105;" k="6" />
    <hkern u1="&#x155;" u2="&#x103;" k="6" />
    <hkern u1="&#x155;" u2="&#x101;" k="6" />
    <hkern u1="&#x155;" u2="&#xff;" k="-18" />
    <hkern u1="&#x155;" u2="&#xfd;" k="-18" />
    <hkern u1="&#x155;" u2="&#xf1;" k="-10" />
    <hkern u1="&#x155;" u2="&#xeb;" k="6" />
    <hkern u1="&#x155;" u2="&#xea;" k="6" />
    <hkern u1="&#x155;" u2="&#xe9;" k="6" />
    <hkern u1="&#x155;" u2="&#xe8;" k="6" />
    <hkern u1="&#x155;" u2="&#xe7;" k="6" />
    <hkern u1="&#x155;" u2="&#xe5;" k="6" />
    <hkern u1="&#x155;" u2="&#xe4;" k="6" />
    <hkern u1="&#x155;" u2="&#xe3;" k="6" />
    <hkern u1="&#x155;" u2="&#xe2;" k="6" />
    <hkern u1="&#x155;" u2="&#xe1;" k="6" />
    <hkern u1="&#x155;" u2="&#xe0;" k="6" />
    <hkern u1="&#x155;" u2="y" k="-18" />
    <hkern u1="&#x155;" u2="v" k="-18" />
    <hkern u1="&#x155;" u2="q" k="12" />
    <hkern u1="&#x155;" u2="n" k="-10" />
    <hkern u1="&#x155;" u2="m" k="-10" />
    <hkern u1="&#x155;" u2="g" k="6" />
    <hkern u1="&#x155;" u2="e" k="6" />
    <hkern u1="&#x155;" u2="d" k="6" />
    <hkern u1="&#x155;" u2="c" k="6" />
    <hkern u1="&#x155;" u2="a" k="6" />
    <hkern u1="&#x155;" u2="&#x2e;" k="92" />
    <hkern u1="&#x155;" u2="&#x2d;" k="55" />
    <hkern u1="&#x155;" u2="&#x2c;" k="92" />
    <hkern u1="&#x156;" u2="&#x21a;" k="18" />
    <hkern u1="&#x156;" u2="&#x178;" k="18" />
    <hkern u1="&#x156;" u2="&#x176;" k="18" />
    <hkern u1="&#x156;" u2="&#x164;" k="18" />
    <hkern u1="&#x156;" u2="&#x162;" k="18" />
    <hkern u1="&#x156;" u2="&#xdd;" k="18" />
    <hkern u1="&#x156;" u2="Y" k="18" />
    <hkern u1="&#x156;" u2="T" k="18" />
    <hkern u1="&#x157;" u2="&#x2019;" k="-10" />
    <hkern u1="&#x157;" u2="&#x177;" k="-18" />
    <hkern u1="&#x157;" g2="napostrophe" k="-10" />
    <hkern u1="&#x157;" u2="&#x148;" k="-10" />
    <hkern u1="&#x157;" u2="&#x146;" k="-10" />
    <hkern u1="&#x157;" u2="&#x144;" k="-10" />
    <hkern u1="&#x157;" u2="&#x123;" k="6" />
    <hkern u1="&#x157;" u2="&#x121;" k="6" />
    <hkern u1="&#x157;" u2="&#x11f;" k="6" />
    <hkern u1="&#x157;" u2="&#x11d;" k="6" />
    <hkern u1="&#x157;" u2="&#x11b;" k="6" />
    <hkern u1="&#x157;" u2="&#x119;" k="6" />
    <hkern u1="&#x157;" u2="&#x117;" k="6" />
    <hkern u1="&#x157;" u2="&#x115;" k="6" />
    <hkern u1="&#x157;" u2="&#x113;" k="6" />
    <hkern u1="&#x157;" u2="&#x10f;" k="6" />
    <hkern u1="&#x157;" u2="&#x10d;" k="6" />
    <hkern u1="&#x157;" u2="&#x10b;" k="6" />
    <hkern u1="&#x157;" u2="&#x109;" k="6" />
    <hkern u1="&#x157;" u2="&#x107;" k="6" />
    <hkern u1="&#x157;" u2="&#x105;" k="6" />
    <hkern u1="&#x157;" u2="&#x103;" k="6" />
    <hkern u1="&#x157;" u2="&#x101;" k="6" />
    <hkern u1="&#x157;" u2="&#xff;" k="-18" />
    <hkern u1="&#x157;" u2="&#xfd;" k="-18" />
    <hkern u1="&#x157;" u2="&#xf1;" k="-10" />
    <hkern u1="&#x157;" u2="&#xeb;" k="6" />
    <hkern u1="&#x157;" u2="&#xea;" k="6" />
    <hkern u1="&#x157;" u2="&#xe9;" k="6" />
    <hkern u1="&#x157;" u2="&#xe8;" k="6" />
    <hkern u1="&#x157;" u2="&#xe7;" k="6" />
    <hkern u1="&#x157;" u2="&#xe5;" k="6" />
    <hkern u1="&#x157;" u2="&#xe4;" k="6" />
    <hkern u1="&#x157;" u2="&#xe3;" k="6" />
    <hkern u1="&#x157;" u2="&#xe2;" k="6" />
    <hkern u1="&#x157;" u2="&#xe1;" k="6" />
    <hkern u1="&#x157;" u2="&#xe0;" k="6" />
    <hkern u1="&#x157;" u2="y" k="-18" />
    <hkern u1="&#x157;" u2="v" k="-18" />
    <hkern u1="&#x157;" u2="q" k="12" />
    <hkern u1="&#x157;" u2="n" k="-10" />
    <hkern u1="&#x157;" u2="m" k="-10" />
    <hkern u1="&#x157;" u2="g" k="6" />
    <hkern u1="&#x157;" u2="e" k="6" />
    <hkern u1="&#x157;" u2="d" k="6" />
    <hkern u1="&#x157;" u2="c" k="6" />
    <hkern u1="&#x157;" u2="a" k="6" />
    <hkern u1="&#x157;" u2="&#x2e;" k="92" />
    <hkern u1="&#x157;" u2="&#x2d;" k="55" />
    <hkern u1="&#x157;" u2="&#x2c;" k="92" />
    <hkern u1="&#x158;" u2="&#x21a;" k="18" />
    <hkern u1="&#x158;" u2="&#x178;" k="18" />
    <hkern u1="&#x158;" u2="&#x176;" k="18" />
    <hkern u1="&#x158;" u2="&#x164;" k="18" />
    <hkern u1="&#x158;" u2="&#x162;" k="18" />
    <hkern u1="&#x158;" u2="&#xdd;" k="18" />
    <hkern u1="&#x158;" u2="Y" k="18" />
    <hkern u1="&#x158;" u2="T" k="18" />
    <hkern u1="&#x159;" u2="&#x2019;" k="-10" />
    <hkern u1="&#x159;" u2="&#x177;" k="-18" />
    <hkern u1="&#x159;" g2="napostrophe" k="-10" />
    <hkern u1="&#x159;" u2="&#x148;" k="-10" />
    <hkern u1="&#x159;" u2="&#x146;" k="-10" />
    <hkern u1="&#x159;" u2="&#x144;" k="-10" />
    <hkern u1="&#x159;" u2="&#x123;" k="6" />
    <hkern u1="&#x159;" u2="&#x121;" k="6" />
    <hkern u1="&#x159;" u2="&#x11f;" k="6" />
    <hkern u1="&#x159;" u2="&#x11d;" k="6" />
    <hkern u1="&#x159;" u2="&#x11b;" k="6" />
    <hkern u1="&#x159;" u2="&#x119;" k="6" />
    <hkern u1="&#x159;" u2="&#x117;" k="6" />
    <hkern u1="&#x159;" u2="&#x115;" k="6" />
    <hkern u1="&#x159;" u2="&#x113;" k="6" />
    <hkern u1="&#x159;" u2="&#x10f;" k="6" />
    <hkern u1="&#x159;" u2="&#x10d;" k="6" />
    <hkern u1="&#x159;" u2="&#x10b;" k="6" />
    <hkern u1="&#x159;" u2="&#x109;" k="6" />
    <hkern u1="&#x159;" u2="&#x107;" k="6" />
    <hkern u1="&#x159;" u2="&#x105;" k="6" />
    <hkern u1="&#x159;" u2="&#x103;" k="6" />
    <hkern u1="&#x159;" u2="&#x101;" k="6" />
    <hkern u1="&#x159;" u2="&#xff;" k="-18" />
    <hkern u1="&#x159;" u2="&#xfd;" k="-18" />
    <hkern u1="&#x159;" u2="&#xf1;" k="-10" />
    <hkern u1="&#x159;" u2="&#xeb;" k="6" />
    <hkern u1="&#x159;" u2="&#xea;" k="6" />
    <hkern u1="&#x159;" u2="&#xe9;" k="6" />
    <hkern u1="&#x159;" u2="&#xe8;" k="6" />
    <hkern u1="&#x159;" u2="&#xe7;" k="6" />
    <hkern u1="&#x159;" u2="&#xe5;" k="6" />
    <hkern u1="&#x159;" u2="&#xe4;" k="6" />
    <hkern u1="&#x159;" u2="&#xe3;" k="6" />
    <hkern u1="&#x159;" u2="&#xe2;" k="6" />
    <hkern u1="&#x159;" u2="&#xe1;" k="6" />
    <hkern u1="&#x159;" u2="&#xe0;" k="6" />
    <hkern u1="&#x159;" u2="y" k="-18" />
    <hkern u1="&#x159;" u2="v" k="-18" />
    <hkern u1="&#x159;" u2="q" k="12" />
    <hkern u1="&#x159;" u2="n" k="-10" />
    <hkern u1="&#x159;" u2="m" k="-10" />
    <hkern u1="&#x159;" u2="g" k="6" />
    <hkern u1="&#x159;" u2="e" k="6" />
    <hkern u1="&#x159;" u2="d" k="6" />
    <hkern u1="&#x159;" u2="c" k="6" />
    <hkern u1="&#x159;" u2="a" k="6" />
    <hkern u1="&#x159;" u2="&#x2e;" k="92" />
    <hkern u1="&#x159;" u2="&#x2d;" k="55" />
    <hkern u1="&#x159;" u2="&#x2c;" k="92" />
    <hkern u1="&#x15a;" u2="&#x2e;" k="30" />
    <hkern u1="&#x15a;" u2="&#x2c;" k="30" />
    <hkern u1="&#x15c;" u2="&#x2e;" k="30" />
    <hkern u1="&#x15c;" u2="&#x2c;" k="30" />
    <hkern u1="&#x15e;" u2="&#x2e;" k="30" />
    <hkern u1="&#x15e;" u2="&#x2c;" k="30" />
    <hkern u1="&#x160;" u2="&#x2e;" k="30" />
    <hkern u1="&#x160;" u2="&#x2c;" k="30" />
    <hkern u1="&#x162;" u2="&#x177;" k="74" />
    <hkern u1="&#x162;" u2="&#x175;" k="74" />
    <hkern u1="&#x162;" u2="&#x173;" k="74" />
    <hkern u1="&#x162;" u2="&#x171;" k="74" />
    <hkern u1="&#x162;" u2="&#x16f;" k="74" />
    <hkern u1="&#x162;" u2="&#x16d;" k="74" />
    <hkern u1="&#x162;" u2="&#x16b;" k="74" />
    <hkern u1="&#x162;" u2="&#x169;" k="74" />
    <hkern u1="&#x162;" u2="&#x159;" k="74" />
    <hkern u1="&#x162;" u2="&#x157;" k="74" />
    <hkern u1="&#x162;" u2="&#x155;" k="74" />
    <hkern u1="&#x162;" u2="&#x151;" k="74" />
    <hkern u1="&#x162;" u2="&#x150;" k="30" />
    <hkern u1="&#x162;" u2="&#x14f;" k="74" />
    <hkern u1="&#x162;" u2="&#x14e;" k="30" />
    <hkern u1="&#x162;" u2="&#x14d;" k="74" />
    <hkern u1="&#x162;" u2="&#x14c;" k="30" />
    <hkern u1="&#x162;" g2="ij" k="18" />
    <hkern u1="&#x162;" u2="&#x12f;" k="18" />
    <hkern u1="&#x162;" u2="&#x11b;" k="74" />
    <hkern u1="&#x162;" u2="&#x119;" k="74" />
    <hkern u1="&#x162;" u2="&#x117;" k="74" />
    <hkern u1="&#x162;" u2="&#x115;" k="74" />
    <hkern u1="&#x162;" u2="&#x113;" k="74" />
    <hkern u1="&#x162;" u2="&#x105;" k="74" />
    <hkern u1="&#x162;" u2="&#x104;" k="55" />
    <hkern u1="&#x162;" u2="&#x103;" k="74" />
    <hkern u1="&#x162;" u2="&#x102;" k="55" />
    <hkern u1="&#x162;" u2="&#x101;" k="74" />
    <hkern u1="&#x162;" u2="&#x100;" k="55" />
    <hkern u1="&#x162;" u2="&#xff;" k="74" />
    <hkern u1="&#x162;" u2="&#xfd;" k="74" />
    <hkern u1="&#x162;" u2="&#xfc;" k="74" />
    <hkern u1="&#x162;" u2="&#xfb;" k="74" />
    <hkern u1="&#x162;" u2="&#xfa;" k="74" />
    <hkern u1="&#x162;" u2="&#xf9;" k="74" />
    <hkern u1="&#x162;" u2="&#xf6;" k="74" />
    <hkern u1="&#x162;" u2="&#xf5;" k="74" />
    <hkern u1="&#x162;" u2="&#xf4;" k="74" />
    <hkern u1="&#x162;" u2="&#xf3;" k="74" />
    <hkern u1="&#x162;" u2="&#xf2;" k="74" />
    <hkern u1="&#x162;" u2="&#xeb;" k="74" />
    <hkern u1="&#x162;" u2="&#xea;" k="74" />
    <hkern u1="&#x162;" u2="&#xe9;" k="74" />
    <hkern u1="&#x162;" u2="&#xe8;" k="74" />
    <hkern u1="&#x162;" u2="&#xe5;" k="74" />
    <hkern u1="&#x162;" u2="&#xe4;" k="74" />
    <hkern u1="&#x162;" u2="&#xe3;" k="74" />
    <hkern u1="&#x162;" u2="&#xe2;" k="74" />
    <hkern u1="&#x162;" u2="&#xe1;" k="74" />
    <hkern u1="&#x162;" u2="&#xe0;" k="74" />
    <hkern u1="&#x162;" u2="&#xd6;" k="30" />
    <hkern u1="&#x162;" u2="&#xd5;" k="30" />
    <hkern u1="&#x162;" u2="&#xd4;" k="30" />
    <hkern u1="&#x162;" u2="&#xd3;" k="30" />
    <hkern u1="&#x162;" u2="&#xd2;" k="30" />
    <hkern u1="&#x162;" u2="&#xc5;" k="55" />
    <hkern u1="&#x162;" u2="&#xc4;" k="55" />
    <hkern u1="&#x162;" u2="&#xc3;" k="55" />
    <hkern u1="&#x162;" u2="&#xc2;" k="55" />
    <hkern u1="&#x162;" u2="&#xc1;" k="55" />
    <hkern u1="&#x162;" u2="&#xc0;" k="55" />
    <hkern u1="&#x162;" u2="y" k="74" />
    <hkern u1="&#x162;" u2="w" k="74" />
    <hkern u1="&#x162;" u2="u" k="74" />
    <hkern u1="&#x162;" u2="r" k="74" />
    <hkern u1="&#x162;" u2="o" k="74" />
    <hkern u1="&#x162;" u2="i" k="18" />
    <hkern u1="&#x162;" u2="e" k="74" />
    <hkern u1="&#x162;" u2="a" k="74" />
    <hkern u1="&#x162;" u2="O" k="30" />
    <hkern u1="&#x162;" u2="A" k="55" />
    <hkern u1="&#x162;" u2="&#x3b;" k="74" />
    <hkern u1="&#x162;" u2="&#x3a;" k="74" />
    <hkern u1="&#x162;" u2="&#x2e;" k="92" />
    <hkern u1="&#x162;" u2="&#x2d;" k="74" />
    <hkern u1="&#x162;" u2="&#x2c;" k="92" />
    <hkern u1="&#x164;" u2="&#x177;" k="74" />
    <hkern u1="&#x164;" u2="&#x175;" k="74" />
    <hkern u1="&#x164;" u2="&#x173;" k="74" />
    <hkern u1="&#x164;" u2="&#x171;" k="74" />
    <hkern u1="&#x164;" u2="&#x16f;" k="74" />
    <hkern u1="&#x164;" u2="&#x16d;" k="74" />
    <hkern u1="&#x164;" u2="&#x16b;" k="74" />
    <hkern u1="&#x164;" u2="&#x169;" k="74" />
    <hkern u1="&#x164;" u2="&#x159;" k="74" />
    <hkern u1="&#x164;" u2="&#x157;" k="74" />
    <hkern u1="&#x164;" u2="&#x155;" k="74" />
    <hkern u1="&#x164;" u2="&#x151;" k="74" />
    <hkern u1="&#x164;" u2="&#x150;" k="30" />
    <hkern u1="&#x164;" u2="&#x14f;" k="74" />
    <hkern u1="&#x164;" u2="&#x14e;" k="30" />
    <hkern u1="&#x164;" u2="&#x14d;" k="74" />
    <hkern u1="&#x164;" u2="&#x14c;" k="30" />
    <hkern u1="&#x164;" g2="ij" k="18" />
    <hkern u1="&#x164;" u2="&#x12f;" k="18" />
    <hkern u1="&#x164;" u2="&#x11b;" k="74" />
    <hkern u1="&#x164;" u2="&#x119;" k="74" />
    <hkern u1="&#x164;" u2="&#x117;" k="74" />
    <hkern u1="&#x164;" u2="&#x115;" k="74" />
    <hkern u1="&#x164;" u2="&#x113;" k="74" />
    <hkern u1="&#x164;" u2="&#x105;" k="74" />
    <hkern u1="&#x164;" u2="&#x104;" k="55" />
    <hkern u1="&#x164;" u2="&#x103;" k="74" />
    <hkern u1="&#x164;" u2="&#x102;" k="55" />
    <hkern u1="&#x164;" u2="&#x101;" k="74" />
    <hkern u1="&#x164;" u2="&#x100;" k="55" />
    <hkern u1="&#x164;" u2="&#xff;" k="74" />
    <hkern u1="&#x164;" u2="&#xfd;" k="74" />
    <hkern u1="&#x164;" u2="&#xfc;" k="74" />
    <hkern u1="&#x164;" u2="&#xfb;" k="74" />
    <hkern u1="&#x164;" u2="&#xfa;" k="74" />
    <hkern u1="&#x164;" u2="&#xf9;" k="74" />
    <hkern u1="&#x164;" u2="&#xf6;" k="74" />
    <hkern u1="&#x164;" u2="&#xf5;" k="74" />
    <hkern u1="&#x164;" u2="&#xf4;" k="74" />
    <hkern u1="&#x164;" u2="&#xf3;" k="74" />
    <hkern u1="&#x164;" u2="&#xf2;" k="74" />
    <hkern u1="&#x164;" u2="&#xeb;" k="74" />
    <hkern u1="&#x164;" u2="&#xea;" k="74" />
    <hkern u1="&#x164;" u2="&#xe9;" k="74" />
    <hkern u1="&#x164;" u2="&#xe8;" k="74" />
    <hkern u1="&#x164;" u2="&#xe5;" k="74" />
    <hkern u1="&#x164;" u2="&#xe4;" k="74" />
    <hkern u1="&#x164;" u2="&#xe3;" k="74" />
    <hkern u1="&#x164;" u2="&#xe2;" k="74" />
    <hkern u1="&#x164;" u2="&#xe1;" k="74" />
    <hkern u1="&#x164;" u2="&#xe0;" k="74" />
    <hkern u1="&#x164;" u2="&#xd6;" k="30" />
    <hkern u1="&#x164;" u2="&#xd5;" k="30" />
    <hkern u1="&#x164;" u2="&#xd4;" k="30" />
    <hkern u1="&#x164;" u2="&#xd3;" k="30" />
    <hkern u1="&#x164;" u2="&#xd2;" k="30" />
    <hkern u1="&#x164;" u2="&#xc5;" k="55" />
    <hkern u1="&#x164;" u2="&#xc4;" k="55" />
    <hkern u1="&#x164;" u2="&#xc3;" k="55" />
    <hkern u1="&#x164;" u2="&#xc2;" k="55" />
    <hkern u1="&#x164;" u2="&#xc1;" k="55" />
    <hkern u1="&#x164;" u2="&#xc0;" k="55" />
    <hkern u1="&#x164;" u2="y" k="74" />
    <hkern u1="&#x164;" u2="w" k="74" />
    <hkern u1="&#x164;" u2="u" k="74" />
    <hkern u1="&#x164;" u2="r" k="74" />
    <hkern u1="&#x164;" u2="o" k="74" />
    <hkern u1="&#x164;" u2="i" k="18" />
    <hkern u1="&#x164;" u2="e" k="74" />
    <hkern u1="&#x164;" u2="a" k="74" />
    <hkern u1="&#x164;" u2="O" k="30" />
    <hkern u1="&#x164;" u2="A" k="55" />
    <hkern u1="&#x164;" u2="&#x3b;" k="74" />
    <hkern u1="&#x164;" u2="&#x3a;" k="74" />
    <hkern u1="&#x164;" u2="&#x2e;" k="92" />
    <hkern u1="&#x164;" u2="&#x2d;" k="74" />
    <hkern u1="&#x164;" u2="&#x2c;" k="92" />
    <hkern u1="&#x168;" u2="&#x2e;" k="30" />
    <hkern u1="&#x168;" u2="&#x2c;" k="30" />
    <hkern u1="&#x16a;" u2="&#x2e;" k="30" />
    <hkern u1="&#x16a;" u2="&#x2c;" k="30" />
    <hkern u1="&#x16c;" u2="&#x2e;" k="30" />
    <hkern u1="&#x16c;" u2="&#x2c;" k="30" />
    <hkern u1="&#x16e;" u2="&#x2e;" k="30" />
    <hkern u1="&#x16e;" u2="&#x2c;" k="30" />
    <hkern u1="&#x170;" u2="&#x2e;" k="30" />
    <hkern u1="&#x170;" u2="&#x2c;" k="30" />
    <hkern u1="&#x172;" u2="&#x2e;" k="30" />
    <hkern u1="&#x172;" u2="&#x2c;" k="30" />
    <hkern u1="&#x174;" u2="&#x177;" k="18" />
    <hkern u1="&#x174;" u2="&#x151;" k="18" />
    <hkern u1="&#x174;" u2="&#x14f;" k="18" />
    <hkern u1="&#x174;" u2="&#x14d;" k="18" />
    <hkern u1="&#x174;" u2="&#x11b;" k="18" />
    <hkern u1="&#x174;" u2="&#x119;" k="18" />
    <hkern u1="&#x174;" u2="&#x117;" k="18" />
    <hkern u1="&#x174;" u2="&#x115;" k="18" />
    <hkern u1="&#x174;" u2="&#x113;" k="18" />
    <hkern u1="&#x174;" u2="&#x105;" k="18" />
    <hkern u1="&#x174;" u2="&#x104;" k="30" />
    <hkern u1="&#x174;" u2="&#x103;" k="18" />
    <hkern u1="&#x174;" u2="&#x102;" k="30" />
    <hkern u1="&#x174;" u2="&#x101;" k="18" />
    <hkern u1="&#x174;" u2="&#x100;" k="30" />
    <hkern u1="&#x174;" u2="&#xff;" k="18" />
    <hkern u1="&#x174;" u2="&#xfd;" k="18" />
    <hkern u1="&#x174;" u2="&#xf6;" k="18" />
    <hkern u1="&#x174;" u2="&#xf5;" k="18" />
    <hkern u1="&#x174;" u2="&#xf4;" k="18" />
    <hkern u1="&#x174;" u2="&#xf3;" k="18" />
    <hkern u1="&#x174;" u2="&#xf2;" k="18" />
    <hkern u1="&#x174;" u2="&#xeb;" k="18" />
    <hkern u1="&#x174;" u2="&#xea;" k="18" />
    <hkern u1="&#x174;" u2="&#xe9;" k="18" />
    <hkern u1="&#x174;" u2="&#xe8;" k="18" />
    <hkern u1="&#x174;" u2="&#xe5;" k="18" />
    <hkern u1="&#x174;" u2="&#xe4;" k="18" />
    <hkern u1="&#x174;" u2="&#xe3;" k="18" />
    <hkern u1="&#x174;" u2="&#xe2;" k="18" />
    <hkern u1="&#x174;" u2="&#xe1;" k="18" />
    <hkern u1="&#x174;" u2="&#xe0;" k="18" />
    <hkern u1="&#x174;" u2="&#xc5;" k="30" />
    <hkern u1="&#x174;" u2="&#xc4;" k="30" />
    <hkern u1="&#x174;" u2="&#xc3;" k="30" />
    <hkern u1="&#x174;" u2="&#xc2;" k="30" />
    <hkern u1="&#x174;" u2="&#xc1;" k="30" />
    <hkern u1="&#x174;" u2="&#xc0;" k="30" />
    <hkern u1="&#x174;" u2="y" k="18" />
    <hkern u1="&#x174;" u2="o" k="18" />
    <hkern u1="&#x174;" u2="e" k="18" />
    <hkern u1="&#x174;" u2="a" k="18" />
    <hkern u1="&#x174;" u2="A" k="30" />
    <hkern u1="&#x174;" u2="&#x2e;" k="74" />
    <hkern u1="&#x174;" u2="&#x2d;" k="18" />
    <hkern u1="&#x174;" u2="&#x2c;" k="74" />
    <hkern u1="&#x175;" u2="&#x151;" k="6" />
    <hkern u1="&#x175;" u2="&#x14f;" k="6" />
    <hkern u1="&#x175;" u2="&#x14d;" k="6" />
    <hkern u1="&#x175;" u2="&#x105;" k="12" />
    <hkern u1="&#x175;" u2="&#x103;" k="12" />
    <hkern u1="&#x175;" u2="&#x101;" k="12" />
    <hkern u1="&#x175;" u2="&#xf6;" k="6" />
    <hkern u1="&#x175;" u2="&#xf5;" k="6" />
    <hkern u1="&#x175;" u2="&#xf4;" k="6" />
    <hkern u1="&#x175;" u2="&#xf3;" k="6" />
    <hkern u1="&#x175;" u2="&#xf2;" k="6" />
    <hkern u1="&#x175;" u2="&#xe5;" k="12" />
    <hkern u1="&#x175;" u2="&#xe4;" k="12" />
    <hkern u1="&#x175;" u2="&#xe3;" k="12" />
    <hkern u1="&#x175;" u2="&#xe2;" k="12" />
    <hkern u1="&#x175;" u2="&#xe1;" k="12" />
    <hkern u1="&#x175;" u2="&#xe0;" k="12" />
    <hkern u1="&#x175;" u2="o" k="6" />
    <hkern u1="&#x175;" u2="a" k="12" />
    <hkern u1="&#x175;" u2="&#x2e;" k="55" />
    <hkern u1="&#x175;" u2="&#x2c;" k="55" />
    <hkern u1="&#x176;" u2="&#x218;" k="18" />
    <hkern u1="&#x176;" u2="&#x173;" k="50" />
    <hkern u1="&#x176;" u2="&#x171;" k="50" />
    <hkern u1="&#x176;" u2="&#x16f;" k="50" />
    <hkern u1="&#x176;" u2="&#x16d;" k="50" />
    <hkern u1="&#x176;" u2="&#x16b;" k="50" />
    <hkern u1="&#x176;" u2="&#x169;" k="50" />
    <hkern u1="&#x176;" u2="&#x160;" k="18" />
    <hkern u1="&#x176;" u2="&#x15e;" k="18" />
    <hkern u1="&#x176;" u2="&#x15c;" k="18" />
    <hkern u1="&#x176;" u2="&#x15a;" k="18" />
    <hkern u1="&#x176;" u2="&#x151;" k="55" />
    <hkern u1="&#x176;" u2="&#x150;" k="40" />
    <hkern u1="&#x176;" u2="&#x14f;" k="55" />
    <hkern u1="&#x176;" u2="&#x14e;" k="40" />
    <hkern u1="&#x176;" u2="&#x14d;" k="55" />
    <hkern u1="&#x176;" u2="&#x14c;" k="40" />
    <hkern u1="&#x176;" g2="ij" k="18" />
    <hkern u1="&#x176;" u2="&#x12f;" k="18" />
    <hkern u1="&#x176;" u2="&#x11b;" k="55" />
    <hkern u1="&#x176;" u2="&#x119;" k="55" />
    <hkern u1="&#x176;" u2="&#x117;" k="55" />
    <hkern u1="&#x176;" u2="&#x115;" k="55" />
    <hkern u1="&#x176;" u2="&#x113;" k="55" />
    <hkern u1="&#x176;" u2="&#x105;" k="55" />
    <hkern u1="&#x176;" u2="&#x104;" k="55" />
    <hkern u1="&#x176;" u2="&#x103;" k="55" />
    <hkern u1="&#x176;" u2="&#x102;" k="55" />
    <hkern u1="&#x176;" u2="&#x101;" k="55" />
    <hkern u1="&#x176;" u2="&#x100;" k="55" />
    <hkern u1="&#x176;" u2="&#xfc;" k="50" />
    <hkern u1="&#x176;" u2="&#xfb;" k="50" />
    <hkern u1="&#x176;" u2="&#xfa;" k="50" />
    <hkern u1="&#x176;" u2="&#xf9;" k="50" />
    <hkern u1="&#x176;" u2="&#xf6;" k="55" />
    <hkern u1="&#x176;" u2="&#xf5;" k="55" />
    <hkern u1="&#x176;" u2="&#xf4;" k="55" />
    <hkern u1="&#x176;" u2="&#xf3;" k="55" />
    <hkern u1="&#x176;" u2="&#xf2;" k="55" />
    <hkern u1="&#x176;" u2="&#xeb;" k="55" />
    <hkern u1="&#x176;" u2="&#xea;" k="55" />
    <hkern u1="&#x176;" u2="&#xe9;" k="55" />
    <hkern u1="&#x176;" u2="&#xe8;" k="55" />
    <hkern u1="&#x176;" u2="&#xe5;" k="55" />
    <hkern u1="&#x176;" u2="&#xe4;" k="55" />
    <hkern u1="&#x176;" u2="&#xe3;" k="55" />
    <hkern u1="&#x176;" u2="&#xe2;" k="55" />
    <hkern u1="&#x176;" u2="&#xe1;" k="55" />
    <hkern u1="&#x176;" u2="&#xe0;" k="55" />
    <hkern u1="&#x176;" u2="&#xd6;" k="40" />
    <hkern u1="&#x176;" u2="&#xd5;" k="40" />
    <hkern u1="&#x176;" u2="&#xd4;" k="40" />
    <hkern u1="&#x176;" u2="&#xd3;" k="40" />
    <hkern u1="&#x176;" u2="&#xd2;" k="40" />
    <hkern u1="&#x176;" u2="&#xc5;" k="55" />
    <hkern u1="&#x176;" u2="&#xc4;" k="55" />
    <hkern u1="&#x176;" u2="&#xc3;" k="55" />
    <hkern u1="&#x176;" u2="&#xc2;" k="55" />
    <hkern u1="&#x176;" u2="&#xc1;" k="55" />
    <hkern u1="&#x176;" u2="&#xc0;" k="55" />
    <hkern u1="&#x176;" u2="u" k="50" />
    <hkern u1="&#x176;" u2="o" k="55" />
    <hkern u1="&#x176;" u2="i" k="18" />
    <hkern u1="&#x176;" u2="e" k="55" />
    <hkern u1="&#x176;" u2="a" k="55" />
    <hkern u1="&#x176;" u2="S" k="18" />
    <hkern u1="&#x176;" u2="O" k="40" />
    <hkern u1="&#x176;" u2="A" k="55" />
    <hkern u1="&#x176;" u2="&#x3b;" k="37" />
    <hkern u1="&#x176;" u2="&#x3a;" k="37" />
    <hkern u1="&#x176;" u2="&#x2e;" k="111" />
    <hkern u1="&#x176;" u2="&#x2d;" k="74" />
    <hkern u1="&#x176;" u2="&#x2c;" k="111" />
    <hkern u1="&#x177;" u2="&#x11b;" k="6" />
    <hkern u1="&#x177;" u2="&#x119;" k="6" />
    <hkern u1="&#x177;" u2="&#x117;" k="6" />
    <hkern u1="&#x177;" u2="&#x115;" k="6" />
    <hkern u1="&#x177;" u2="&#x113;" k="6" />
    <hkern u1="&#x177;" u2="&#xeb;" k="6" />
    <hkern u1="&#x177;" u2="&#xea;" k="6" />
    <hkern u1="&#x177;" u2="&#xe9;" k="6" />
    <hkern u1="&#x177;" u2="&#xe8;" k="6" />
    <hkern u1="&#x177;" u2="e" k="6" />
    <hkern u1="&#x177;" u2="&#x2e;" k="74" />
    <hkern u1="&#x177;" u2="&#x2c;" k="74" />
    <hkern u1="&#x178;" u2="&#x218;" k="18" />
    <hkern u1="&#x178;" u2="&#x173;" k="50" />
    <hkern u1="&#x178;" u2="&#x171;" k="50" />
    <hkern u1="&#x178;" u2="&#x16f;" k="50" />
    <hkern u1="&#x178;" u2="&#x16d;" k="50" />
    <hkern u1="&#x178;" u2="&#x16b;" k="50" />
    <hkern u1="&#x178;" u2="&#x169;" k="50" />
    <hkern u1="&#x178;" u2="&#x160;" k="18" />
    <hkern u1="&#x178;" u2="&#x15e;" k="18" />
    <hkern u1="&#x178;" u2="&#x15c;" k="18" />
    <hkern u1="&#x178;" u2="&#x15a;" k="18" />
    <hkern u1="&#x178;" u2="&#x151;" k="55" />
    <hkern u1="&#x178;" u2="&#x150;" k="40" />
    <hkern u1="&#x178;" u2="&#x14f;" k="55" />
    <hkern u1="&#x178;" u2="&#x14e;" k="40" />
    <hkern u1="&#x178;" u2="&#x14d;" k="55" />
    <hkern u1="&#x178;" u2="&#x14c;" k="40" />
    <hkern u1="&#x178;" g2="ij" k="18" />
    <hkern u1="&#x178;" u2="&#x12f;" k="18" />
    <hkern u1="&#x178;" u2="&#x11b;" k="55" />
    <hkern u1="&#x178;" u2="&#x119;" k="55" />
    <hkern u1="&#x178;" u2="&#x117;" k="55" />
    <hkern u1="&#x178;" u2="&#x115;" k="55" />
    <hkern u1="&#x178;" u2="&#x113;" k="55" />
    <hkern u1="&#x178;" u2="&#x105;" k="55" />
    <hkern u1="&#x178;" u2="&#x104;" k="55" />
    <hkern u1="&#x178;" u2="&#x103;" k="55" />
    <hkern u1="&#x178;" u2="&#x102;" k="55" />
    <hkern u1="&#x178;" u2="&#x101;" k="55" />
    <hkern u1="&#x178;" u2="&#x100;" k="55" />
    <hkern u1="&#x178;" u2="&#xfc;" k="50" />
    <hkern u1="&#x178;" u2="&#xfb;" k="50" />
    <hkern u1="&#x178;" u2="&#xfa;" k="50" />
    <hkern u1="&#x178;" u2="&#xf9;" k="50" />
    <hkern u1="&#x178;" u2="&#xf6;" k="55" />
    <hkern u1="&#x178;" u2="&#xf5;" k="55" />
    <hkern u1="&#x178;" u2="&#xf4;" k="55" />
    <hkern u1="&#x178;" u2="&#xf3;" k="55" />
    <hkern u1="&#x178;" u2="&#xf2;" k="55" />
    <hkern u1="&#x178;" u2="&#xeb;" k="55" />
    <hkern u1="&#x178;" u2="&#xea;" k="55" />
    <hkern u1="&#x178;" u2="&#xe9;" k="55" />
    <hkern u1="&#x178;" u2="&#xe8;" k="55" />
    <hkern u1="&#x178;" u2="&#xe5;" k="55" />
    <hkern u1="&#x178;" u2="&#xe4;" k="55" />
    <hkern u1="&#x178;" u2="&#xe3;" k="55" />
    <hkern u1="&#x178;" u2="&#xe2;" k="55" />
    <hkern u1="&#x178;" u2="&#xe1;" k="55" />
    <hkern u1="&#x178;" u2="&#xe0;" k="55" />
    <hkern u1="&#x178;" u2="&#xd6;" k="40" />
    <hkern u1="&#x178;" u2="&#xd5;" k="40" />
    <hkern u1="&#x178;" u2="&#xd4;" k="40" />
    <hkern u1="&#x178;" u2="&#xd3;" k="40" />
    <hkern u1="&#x178;" u2="&#xd2;" k="40" />
    <hkern u1="&#x178;" u2="&#xc5;" k="55" />
    <hkern u1="&#x178;" u2="&#xc4;" k="55" />
    <hkern u1="&#x178;" u2="&#xc3;" k="55" />
    <hkern u1="&#x178;" u2="&#xc2;" k="55" />
    <hkern u1="&#x178;" u2="&#xc1;" k="55" />
    <hkern u1="&#x178;" u2="&#xc0;" k="55" />
    <hkern u1="&#x178;" u2="u" k="50" />
    <hkern u1="&#x178;" u2="o" k="55" />
    <hkern u1="&#x178;" u2="i" k="18" />
    <hkern u1="&#x178;" u2="e" k="55" />
    <hkern u1="&#x178;" u2="a" k="55" />
    <hkern u1="&#x178;" u2="S" k="18" />
    <hkern u1="&#x178;" u2="O" k="40" />
    <hkern u1="&#x178;" u2="A" k="55" />
    <hkern u1="&#x178;" u2="&#x3b;" k="37" />
    <hkern u1="&#x178;" u2="&#x3a;" k="37" />
    <hkern u1="&#x178;" u2="&#x2e;" k="111" />
    <hkern u1="&#x178;" u2="&#x2d;" k="74" />
    <hkern u1="&#x178;" u2="&#x2c;" k="111" />
    <hkern u1="&#x218;" u2="&#x2e;" k="30" />
    <hkern u1="&#x218;" u2="&#x2c;" k="30" />
    <hkern u1="&#x21a;" u2="&#x177;" k="74" />
    <hkern u1="&#x21a;" u2="&#x175;" k="74" />
    <hkern u1="&#x21a;" u2="&#x173;" k="74" />
    <hkern u1="&#x21a;" u2="&#x171;" k="74" />
    <hkern u1="&#x21a;" u2="&#x16f;" k="74" />
    <hkern u1="&#x21a;" u2="&#x16d;" k="74" />
    <hkern u1="&#x21a;" u2="&#x16b;" k="74" />
    <hkern u1="&#x21a;" u2="&#x169;" k="74" />
    <hkern u1="&#x21a;" u2="&#x159;" k="74" />
    <hkern u1="&#x21a;" u2="&#x157;" k="74" />
    <hkern u1="&#x21a;" u2="&#x155;" k="74" />
    <hkern u1="&#x21a;" u2="&#x151;" k="74" />
    <hkern u1="&#x21a;" u2="&#x150;" k="30" />
    <hkern u1="&#x21a;" u2="&#x14f;" k="74" />
    <hkern u1="&#x21a;" u2="&#x14e;" k="30" />
    <hkern u1="&#x21a;" u2="&#x14d;" k="74" />
    <hkern u1="&#x21a;" u2="&#x14c;" k="30" />
    <hkern u1="&#x21a;" g2="ij" k="18" />
    <hkern u1="&#x21a;" u2="&#x12f;" k="18" />
    <hkern u1="&#x21a;" u2="&#x11b;" k="74" />
    <hkern u1="&#x21a;" u2="&#x119;" k="74" />
    <hkern u1="&#x21a;" u2="&#x117;" k="74" />
    <hkern u1="&#x21a;" u2="&#x115;" k="74" />
    <hkern u1="&#x21a;" u2="&#x113;" k="74" />
    <hkern u1="&#x21a;" u2="&#x105;" k="74" />
    <hkern u1="&#x21a;" u2="&#x104;" k="55" />
    <hkern u1="&#x21a;" u2="&#x103;" k="74" />
    <hkern u1="&#x21a;" u2="&#x102;" k="55" />
    <hkern u1="&#x21a;" u2="&#x101;" k="74" />
    <hkern u1="&#x21a;" u2="&#x100;" k="55" />
    <hkern u1="&#x21a;" u2="&#xff;" k="74" />
    <hkern u1="&#x21a;" u2="&#xfd;" k="74" />
    <hkern u1="&#x21a;" u2="&#xfc;" k="74" />
    <hkern u1="&#x21a;" u2="&#xfb;" k="74" />
    <hkern u1="&#x21a;" u2="&#xfa;" k="74" />
    <hkern u1="&#x21a;" u2="&#xf9;" k="74" />
    <hkern u1="&#x21a;" u2="&#xf6;" k="74" />
    <hkern u1="&#x21a;" u2="&#xf5;" k="74" />
    <hkern u1="&#x21a;" u2="&#xf4;" k="74" />
    <hkern u1="&#x21a;" u2="&#xf3;" k="74" />
    <hkern u1="&#x21a;" u2="&#xf2;" k="74" />
    <hkern u1="&#x21a;" u2="&#xeb;" k="74" />
    <hkern u1="&#x21a;" u2="&#xea;" k="74" />
    <hkern u1="&#x21a;" u2="&#xe9;" k="74" />
    <hkern u1="&#x21a;" u2="&#xe8;" k="74" />
    <hkern u1="&#x21a;" u2="&#xe5;" k="74" />
    <hkern u1="&#x21a;" u2="&#xe4;" k="74" />
    <hkern u1="&#x21a;" u2="&#xe3;" k="74" />
    <hkern u1="&#x21a;" u2="&#xe2;" k="74" />
    <hkern u1="&#x21a;" u2="&#xe1;" k="74" />
    <hkern u1="&#x21a;" u2="&#xe0;" k="74" />
    <hkern u1="&#x21a;" u2="&#xd6;" k="30" />
    <hkern u1="&#x21a;" u2="&#xd5;" k="30" />
    <hkern u1="&#x21a;" u2="&#xd4;" k="30" />
    <hkern u1="&#x21a;" u2="&#xd3;" k="30" />
    <hkern u1="&#x21a;" u2="&#xd2;" k="30" />
    <hkern u1="&#x21a;" u2="&#xc5;" k="55" />
    <hkern u1="&#x21a;" u2="&#xc4;" k="55" />
    <hkern u1="&#x21a;" u2="&#xc3;" k="55" />
    <hkern u1="&#x21a;" u2="&#xc2;" k="55" />
    <hkern u1="&#x21a;" u2="&#xc1;" k="55" />
    <hkern u1="&#x21a;" u2="&#xc0;" k="55" />
    <hkern u1="&#x21a;" u2="y" k="74" />
    <hkern u1="&#x21a;" u2="w" k="74" />
    <hkern u1="&#x21a;" u2="u" k="74" />
    <hkern u1="&#x21a;" u2="r" k="74" />
    <hkern u1="&#x21a;" u2="o" k="74" />
    <hkern u1="&#x21a;" u2="i" k="18" />
    <hkern u1="&#x21a;" u2="e" k="74" />
    <hkern u1="&#x21a;" u2="a" k="74" />
    <hkern u1="&#x21a;" u2="O" k="30" />
    <hkern u1="&#x21a;" u2="A" k="55" />
    <hkern u1="&#x21a;" u2="&#x3b;" k="74" />
    <hkern u1="&#x21a;" u2="&#x3a;" k="74" />
    <hkern u1="&#x21a;" u2="&#x2e;" k="92" />
    <hkern u1="&#x21a;" u2="&#x2d;" k="74" />
    <hkern u1="&#x21a;" u2="&#x2c;" k="92" />
    <hkern u1="&#x2018;" u2="&#x2018;" k="62" />
    <hkern u1="&#x2018;" u2="&#x104;" k="55" />
    <hkern u1="&#x2018;" u2="&#x102;" k="55" />
    <hkern u1="&#x2018;" u2="&#x100;" k="55" />
    <hkern u1="&#x2018;" u2="&#xc5;" k="55" />
    <hkern u1="&#x2018;" u2="&#xc4;" k="55" />
    <hkern u1="&#x2018;" u2="&#xc3;" k="55" />
    <hkern u1="&#x2018;" u2="&#xc2;" k="55" />
    <hkern u1="&#x2018;" u2="&#xc1;" k="55" />
    <hkern u1="&#x2018;" u2="&#xc0;" k="55" />
    <hkern u1="&#x2018;" u2="A" k="55" />
    <hkern u1="&#x2019;" u2="&#x2019;" k="62" />
    <hkern u1="&#x2019;" u2="&#x219;" k="37" />
    <hkern u1="&#x2019;" u2="&#x161;" k="37" />
    <hkern u1="&#x2019;" u2="&#x15f;" k="37" />
    <hkern u1="&#x2019;" u2="&#x15d;" k="37" />
    <hkern u1="&#x2019;" u2="&#x15b;" k="37" />
    <hkern u1="&#x2019;" u2="&#x159;" k="18" />
    <hkern u1="&#x2019;" u2="&#x157;" k="18" />
    <hkern u1="&#x2019;" u2="&#x155;" k="18" />
    <hkern u1="&#x2019;" u2="&#x10f;" k="37" />
    <hkern u1="&#x2019;" u2="s" k="37" />
    <hkern u1="&#x2019;" u2="r" k="18" />
    <hkern u1="&#x2019;" u2="d" k="37" />
    <hkern u1="&#x201c;" u2="&#x104;" k="55" />
    <hkern u1="&#x201c;" u2="&#x102;" k="55" />
    <hkern u1="&#x201c;" u2="&#x100;" k="55" />
    <hkern u1="&#x201c;" u2="&#xc5;" k="55" />
    <hkern u1="&#x201c;" u2="&#xc4;" k="55" />
    <hkern u1="&#x201c;" u2="&#xc3;" k="55" />
    <hkern u1="&#x201c;" u2="&#xc2;" k="55" />
    <hkern u1="&#x201c;" u2="&#xc1;" k="55" />
    <hkern u1="&#x201c;" u2="&#xc0;" k="55" />
    <hkern u1="&#x201c;" u2="A" k="55" />
    <hkern u1="&#x201d;" u2="&#x20;" k="37" />
  </font>
</defs></svg>
