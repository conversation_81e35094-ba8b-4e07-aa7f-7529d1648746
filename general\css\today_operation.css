.p0{
    padding:0;
}
.p10{
    padding:10px;
}
.container-fluid {
    width:100%;
    padding:0;
}
.w10{
    width:10%;
}
.w20{
    width:20%;
}
.w30{
    width:30%;
}
.w40{
    width:40%;
}
.w50{
    width:50%;
}
.w60{
    width:60%;
}
.w70{
    width:70%;
}
.w80{
    width:80%;
}
.w85{
    width:85%;
}
.w90{
    width:90%;
}
.w100{
    width:100%;
}
.left2{
    left: 2%;
}
.left5{
    left: 5%;
}
.left10{
    left: 10%;
}
.left20{
    left: 20%;
}
.left25{
    left: 25%;
}
.left30{
    left: 30%;
}
.left35{
    left: 35%;
}
.left40{
    left: 40%;
}
.left50{
    left: 50%;
}
.left60{
    left: 60%;
}
.left70{
    left: 70%;
}
.left80{
    left: 80%;
}
.left90{
    left: 90%;
}

.btn_go {
    padding: 6px;
    border-radius: 4px;
    border: 1px solid #2874c1;
    cursor: pointer;
    pointer-events: auto;
    background: url(../img/arr.png) no-repeat 96px center;
    width: 111px;
}
.logo img{
    position: absolute;
    left:90px;
}
/**下拉框**/
#companycombo {
    position: absolute;
    top: 12px;
    left: 0px;
    width: 70px;
    height: 32px;
    font-size: 10px;
}

#companycombo .box {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 70px;
    height: 32px;
    border: 1px solid #2a81d2;
    border-radius: 3px;
    padding: 7px 0 0 9px;
    cursor: pointer;
    background: url(../img/combo_arr.png) no-repeat 57px 13px;
    background-color: #073b77;
    pointer-events: auto;
}

#companycombo .box:hover {
    border: 1px solid #2f8be1;
    background-color: #073b77;
}

#companylist {
    position: absolute;
    top: 31px;
    left: 0px;
    width: 70px;
    border: 1px solid #1f5fa8;
    border-radius: 2px;
    padding: 5px 0 5px 0px;
    background-color: #021d39;
    display: none;
    z-index: 1;
    pointer-events: auto;
}
.combobox_list{
    position: absolute;
    top: 31px;
    left: 0px;
    width: 133px;
    overflow-y: scroll;
    border: 1px solid #1f5fa8;
    border-radius: 2px;
    padding: 5px 0 5px 0px;
    background-color: #021d39;
    display: none;
    z-index: 1;
    pointer-events: auto;
}

#companylist  .itm {
    width: 100%;
    height: 32px;
    padding: 6px 0 0 10px;
    cursor: pointer;
    position:relative;
}

#companylist  .itm:hover {
    background-color: #1f5fa8;
}

.combobox_list .item {
    display: block;
    padding: 3px;
    position: relative;
    cursor: pointer;
    pointer-events: auto;
    letter-spacing: -0.5px;
}

.combobox_list .item:hover {
    background-color: #1f5fa8;
}

.up_arr{
    display: inline-block;
    height: 10px;
    width: 10px;
    background: url(../img/arr_up2.png) no-repeat center;
}
.down_arr{
    display: inline-block;
    height: 10px;
    width: 10px;
    background: url(../img/arr_down2.png) no-repeat center;
}
.label-title{
    border-left:5px solid #2a81d2;
    border-radius: 0;
    font-size: 15px;
    padding-left: 7px;
}
.label-title .line_1{
    padding-left: 10px;
    color:#2a81d2;
    font-weight: 600;
    font-size: 1px;
}
.label-title .line_2_21{
    width: 112px;
    bottom: 4px;
    left: 96%;
    border-bottom:1px solid #2a81d2;
}
.label-title .line_2_46{
    width: 156px;
    bottom: 4px;
    left: 95%;
    border-bottom:1px solid #2a81d2;
}
.label-title .line_2_54{
    width: 176px;
    bottom: 4px;
    left: 95%;
    border-bottom:1px solid #2a81d2;
}
.label-title .line_2_72{
    width: 456px;
    bottom: 4px;
    left: 95%;
    border-bottom:1px solid #2a81d2;
}
.label-title .line_2_429{
    width: 429px;
    bottom: 4px;
    left: 95%;
    border-bottom:1px solid #2a81d2;
}
.label-title .line_2_442{
     width: 442px;
     bottom: 4px;
     left: 95%;
     border-bottom:1px solid #2a81d2;
 }
.label-title .line_2_413{
    width: 413px;
    bottom: 4px;
    left: 95%;
    border-bottom:1px solid #2a81d2;
}
.label-title .line_2_263{
    width: 240px;
    bottom: 4px;
    left: 95%;
    border-bottom:1px solid #2a81d2;
}
.label-title .line_2_315{
    width: 315px;
    bottom: 4px;
    left: 95%;
    border-bottom:1px solid #2a81d2;
}
.label-content{
    width:100%;
    top:30px;
}

.label_content{
    text-align: center;
    width: 100%;
    top: 2px;
}
.label_footer{
    bottom: 1px;
    left: 22px;
}

.footer_label{
    background: url(../img/label_bg.png?1) no-repeat top center;
    height: 40px;
    width: 73px;
    font-size: 13px;
    text-align: center;
}

#title{
    text-align: center;
    color: #0297e7;
    left: 101.6%;
    top: 10px;
}
.hidden{
    display: none !important;
}

/** 背景 **/
.page-bgimg {
    position: absolute;
    top: 0px;
    width: 1920px;
    height: 1080px;
    background: url(../img/overall_operation.bg.png?2) no-repeat top center;
    background-size: 100%;
    overflow-x: hidden;
    overflow-y: hidden;
    pointer-events: none;
}

.page-wrapper {
    position: absolute;
    top: 0px;
    width: 1920px;
    height: 1080px;
    overflow-x: hidden;
    overflow-y: hidden;
    pointer-events: none;
}

.map-wrapper{
    position: absolute;
    top: 201px;
    width: 680px;
    height: 680px;
    left: 621px;
    overflow-x: hidden;
    overflow-y: hidden;
    pointer-events: auto;
}
.indicator{
    text-align: center;
}
.indicator canvas{
    position: relative;
}
.chart{
    left:5px;
    text-align: center;
}
.chart .pointer {
    width: 122px;
    height: 122px;
}
.chart .mk {
    position: absolute;
    font-size: 9px;
    color:#44a3f4
}
.chart .lb {
    position: absolute;
    width: 100%;
    height: 50px;
    top: 78px;
}

.col-l{
    padding: 0px 0 0 33px;
    width: 31%;
}
.col-m{
    padding: 0;
    width: 33%;
}
.col-r{
    padding:0 25px 0 0;
    width: 33%;
}

.col-l .first-row h5{
    color: #0297e7;
    font-weight: 600;
    margin-top: 12px;
    font-size: 18px;
    text-align: center;
}
.col-l .first-row{
    top:25px
}
.col-l .second-row {
    top: 90px;
}
.col-l .third-row {
    top: 90px;
}
.col-l .four-row {
    top: 400px;
}
.col-l .five-row {
    top: 526px;
}
.col-l .six-row {
    top: 808px;
}
.col-l .seven-row {
    top: 795px;
}
.col-l .third-row .title{
    color: #0296e5;
    text-align: center;
    font-size: 15px;
    font-weight: bold;
}
.col-l .third-row .col-1 .chart{
    top: 40px;
    left: 25px;
}
.col-l .third-row .col-1 .total_hbzl{
    top: 160px;
    text-align: center;
}
.col-l .third-row .col-1 .label_hbzl{
    top: 190px;
    left: 30px;
}
.col-l .third-row .col-1 .yzx_hbzl{
    top:237px;
    text-align: center;
}
.col-l .third-row .col-1 .yzx_hbzl .blue-font,
.col-l .third-row .col-3 .hblk_has .blue-font{
    color: #0296e5;
}
.col-l .third-row .col-2 .normal_rate_hb{
    top: 40px;
    left: 25px;
}
.col-l .third-row .col-2 .label_hb{
    top: 190px;
    left: 30px;
}

.col-l .third-row .col-2 .hb
.col-m .legend_position{
    position:absolute;
    top: 770px;
    left: 102%;
}

.col-l .third-row .col-3 .hblk_total{
    text-align: center;
    top: 40px;
}
.col-l .third-row .col-3 .hblk_pic{
    background: url(../img/humans.png?1) no-repeat top center;
    height: 74px;
    width: 98px;
    top: 110px;
    left: 53px;
}
.col-l .third-row .col-3 .hblk_has{
    text-align: center;
    top: 237px;
}
.col-l .five-row .frame .blkbar {
    text-align: center;
    padding-top: 3px;
    width: 100%;
    height: 32px;
    color: #fff;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
}
.col-l .five-row .frame .left {
    position: absolute;
    top: 2px;
    left: 16px;
    text-align: left;
}
.col-l .five-row .frame .right {
    position: absolute;
    top: 2px;
    right: 50px;
    text-align: right;
}
.col-l .five-row .frame .chart {
    position: absolute;
    top: 57px;
    left: 190px;
    width: 200px;
    height: 152px;
    background: url(../img/delay_chart_bg.png?0) no-repeat top left;
}
.col-l .five-row .frame .chart canvas {
    position: absolute;
    top: 10px;
    left: 35px;
    width: 130px;
    height: 130px;
}
.col-l .five-row .frame .chart .txt {
    position: absolute;
    text-align: center;
    top: 40px;
    width: 100%;
}
.col-l .six-row .gypic {
    position: absolute;
    top: 15px;
    left: 60px;
    width: 187px;
    height: 159px;
    background: url(../img/gy.png?0) no-repeat top center;
}
.col-l .six-row .ylpic {
    position: absolute;
    top: 15px;
    left: 85px;
    width: 113px;
    height: 98px;
    background: url(../img/zyl.png?0) no-repeat top center;
}
.col-l .six-row .gyqk_title {
    left: 300px;
    top: 20px;
}
.col-l .six-row .gyqk_content {
    left: 300px;
    top: 50px;
    width: 45%;
}
.col-l .six-row .bar {
    position: relative;
    display: inline-block;
    height: 4px;
    vertical-align: middle;
}
.col-l .six-row .darkbar {
    background: #00579E;
    width: 40px;
    border-radius: 2px;
    overflow: hidden;
    transform: rotate(0);
}
.col-l .six-row .bar {
    height: 8px;
}
.col-l .six-row .bar_ground{
    top:-6px
}
.col-l .six-row .ylpic .zyltitle{
    top: 110px;
    width: 100%;
    text-align: center;
}
.col-l .six-row .ylpic .zylcontent{
    top: 130px;
    width: 100%;
    text-align: center;
}
.col-m .plane_change{
    position: absolute;
    top: 97px;
    left: 102%;
}
.col-m .flighting{
    position: absolute;
    top: 97px;
    left: 180%;
}
.col-m .plane_change .plane_change_pic{
    position: absolute;
    top: 10px;
    width: 135px;
    height: 91px;
    background-image: url(../img/2Dto3D.png);
    pointer-events: auto;
    cursor: pointer;
}
.col-m .flighting .baseMainCToLInfos {
    width: 100%;
    top: 75px;
}

.col-m .flighting .baseMainCToLInfos1 {
    width: 22px;
    height: 24px;
    background: url(../img/zc.png) no-repeat;
}

.col-m .flighting .baseMainCToLInfos2 {
    width: 28px;
    height: 24px;
    left: 30px;
    color: #00a9ff;
    font-size: 14px;
    line-height: 24px;
    text-align: center;
    vertical-align: middle;
}

.col-m .flighting .baseMainCToLInfos3 {
    width: 22px;
    height: 24px;
    left: 74px;
    background: url(../img/yw.png) no-repeat;
}

.col-m .flighting .baseMainCToLInfos4 {
    width: 28px;
    height: 24px;
    left: 105px;
    color: #00a9ff;
    font-size: 14px;
    line-height: 24px;
    text-align: center;
    vertical-align: middle;
}

.col-m .flighting .baseMainCBottRJG {
    width: 140px;
    height: 40px;
    top: 15px;
}

.col-m .flighting .baseMainCBottRJG2 {
    width: 32px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    vertical-align: middle;
    background: url(../img/BC.png) no-repeat center;
}

.col-m .flighting .baseMainCBottRJG3 {
    width: 32px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    vertical-align: middle;
    background: url(../img/BC.png) no-repeat center;
    left: 42px;
}
.col-m .flighting .baseMainCBottRJG5 {
    width: 32px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    vertical-align: middle;
    background: url(../img/BC.png) no-repeat center;
    left: 84px;
}

.col-m .flighting .baseMainCBottRJG4 {
    width: 24px;
    height: 40px;
    left: 122px;
    font-size: 12px;
}
#base_cards {
    position: absolute;
    top: 900px;
    left: 99.1%;
    width: 100%;
    height: 100px;
    overflow-x: hidden;
    overflow-y: hidden;
    pointer-events: auto;
}

.card {
    border: 1px solid;
    border-radius: 8px;
    width: 152px;
    height: 80px;
    overflow: hidden;
    -moz-box-shadow:0px 2px 8px #122348;
    -webkit-box-shadow:0px 2px 8px #122348;
    box-shadow:0px 2px 8px #122348;
    transform: rotate(0deg);
    display: inline-block;
    margin-right: 3px;
    margin-bottom: 20px;
}
.card div{
    position: relative;
    transform: rotate(0deg);
}
.card .head{

    width: 100%;
    text-align: center;
    border-bottom: 1px solid;
    padding: 2px;
    font-weight: bold;
}
.card .head .weather{
    position: absolute;
    top: 3px;
    right: 8px;
    width: 30px;
    height: 20px;
    text-align: right;
}
.card .cont{
    width: 120%;
    margin-left: -10%;
    text-align: center;
}
.card .cont .title{
    text-align: left;
    padding: 1px 0 0 12%;

}
.card .cont .itm{
    position: absolute;
    display: block;
    padding: 5px 2px 0 2px;
}
.card .cont .itm .sb{
    font-size: 9px;
}
.card .cont .itm .num{
    line-height: 20px;
    font-size: 14px;
}
.card .cont .itm1{
    left: 43px;
}
.card .cont .itm2{
    left: 86px;
}
.card .cont .itm3{
    left: 128px;
}
.card .cont .itm4{
    left: 174px;
}


.bluecard {
    border-color: #238DE6;
    background: rgba(25, 96, 185, 0.8);
}
.bluecard .head {
    border-color: #238DE6;
    background: -moz-linear-gradient(top,  rgba(33,103,190,1) 0%, rgba(22,89,178,1) 100%);
    background: -webkit-linear-gradient(top,  rgba(33,103,190,1) 0%,rgba(22,89,178,1) 100%);
    background: linear-gradient(to bottom,  rgba(33,103,190,1) 0%,rgba(22,89,178,1) 100%);
}

.yellowcard {
    border-color: #238DE6;
    background: rgba(25, 96, 185, 0.8);
}
.yellowcard .head {
    border-color: #ecc95a;
    background: -moz-linear-gradient(top,  #c6a51c 0%, #b78c31 100%);
    background: -webkit-linear-gradient(top,  #c6a51c 0%,#b78c31 100%);
    background: linear-gradient(to bottom,  #c6a51c 0%,#b78c31 100%);
}

.redcard {
    border-color: #238DE6;
    background: rgba(25, 96, 185, 0.8);
}
.redcard .head {
    border-color: #b24359;
    background: -moz-linear-gradient(top,  #850f25 0%, #760a19 100%);
    background: -webkit-linear-gradient(top,  #850f25 0%, #760a19 100%);
    background: linear-gradient(to bottom,  #850f25 0%, #760a19 100%);
}



#b1 {
    position: absolute;
    top: 10px;
    left: 25px;
    width: 200px;
    padding-left: 22px;
    background: url(../img/ico_3.png?0) no-repeat center left;
}
#b2 {
    position: absolute;
    top: 10px;
    left: 105px;
    width: 200px;
    padding-left: 22px;
    background: url(../img/ico_4.png?0) no-repeat center left;
}
#b3{
    position: absolute;
    top: 10px;
    left: 180px;
    width: 85px;
    padding-left: 22px;
    background: url(../img/stopplane.png?0) no-repeat center left;
}
.brown {
    color: #f39800;
}

#barlist {
    position: absolute;
    top: 60px;
    width: 255px;
    height: 78px;
    font-size: 12px;
}
#barlist table{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
}
#barlist table .r{
    text-align: center;
    padding-right: 3px;
}
.baritmrow {
    position: relative;
    height: 18px;
}
.button_yl {
    border: 1px solid #2a81d2;
    border-radius: 25px;
    width: 84px;
    text-align: center;
    cursor: pointer;
    pointer-events: auto;
    right: 130px;
}
.button_gy {
    border: 1px solid #2a81d2;
    border-radius: 25px;
    width: 100px;
    text-align: center;
    cursor: pointer;
    pointer-events: auto;
    right: 22px;
}
#holder_delay_cause_comp {
    position: absolute;
    top: 40px;
    width: 180px;
}
#holder_delay_cause_none {
    position: absolute;
    top: 40px;
    right: 40px;
    width: 150px;
}
.unnormal_plan {
    background: url(../img/unnormal_plan.png) no-repeat center;
    height: 68px;
    width: 95px;
    text-align: center;
    top: 10px;
}
.col-m .legend_normal_rate .legend_green{
    top:33px;
}
.col-m .legend_normal_rate .legend_yellow{
    top:33px;
}
.col-m .legend_normal_rate .legend_orange{
    top:66px;
}
.col-m .legend_normal_rate .legend_red{
    top:66px;
}
.col-m .legend_normal_rate .green_pin_bg{
    background: url(../img/green_pin.png?1) no-repeat top center;
    height: 21px;
    width: 21px;
}
.col-m .legend_normal_rate .yellow_pin_bg{
    background: url(../img/yellow_pin.png?1) no-repeat top center;
    height: 21px;
    width: 21px;
}
.col-m .legend_normal_rate .orange_pin_bg{
    background: url(../img/orange_pin.png?1) no-repeat top center;
    height: 21px;
    width: 21px;
}
.col-m .legend_normal_rate .red_pin_bg{
    background: url(../img/red_pin.png?1) no-repeat top center;
    height: 21px;
    width: 21px;
}
.col-m .legend_normal_rate .green_pin_rate,
.col-m .legend_normal_rate .yellow_pin_rate,
.col-m .legend_normal_rate .orange_pin_rate,
.col-m .legend_normal_rate .red_pin_rate{
    color:#44a3f4;
    padding-left: 25px;
}
.col-r .first-row {
    left: 1320px;
    top: 25px;
}
.col-r .second-row {
    left: 1320px;
    top: 90px;
}
.col-r .third-row {
    left: 1320px;
    top: 740px;
}
.col-r .first-row .baseTitleRType {
    top: 15px;
    right: 540px;
}
.col-r .first-row .place {
    top: 15px;
    right: 440px;
}
.col-r .first-row  select {
    width: 90px;
    height: 26px;
    pointer-events: auto;
    border-radius: 4px;
    border: 1px solid rgba(41, 140, 232, 0.6);
    background: #073b77;
    color: #029cee;
}
.col-r .first-row .baseTitleRName {
    top: 18px;
    left: 32%;
}
.col-r .first-row .baseTitleRPhon {
    left: 40%;
    top: 17px;
}
.col-r .first-row .baseTitleRPhoNum {
    left: 45%;
    top: 18px;
}
.col-r .first-row .baseTitleRMobi {
    left: 66%;
    top: 17px;
}
.col-r .first-row .baseTitleRMobiNum {
    left: 70%;
    top: 18px;
}
.col-r .second-row .plane_nos{
    top: 70px;
    width: 497px;
    margin-left: 23px;
    pointer-events: auto;
}
.col-r .second-row .plane_nos .plane_no{
    text-align: center;
    padding-top: 4px;
    width: 80px;
    height: 30px;
    pointer-events: auto;
    border-radius: 4px;
    border: 1px solid rgba(41, 140, 232, 0.6);
    color: #fff;
    cursor: pointer;
}
.col-r .second-row .plane_nos .swiper-slide-active{
    background-color: #0a51b7 !important;
}
.col-r .second-row .swiper-button-prev{
    top: 92px;
    left: -2px;
    width: 20px;
    height: 30px;
    pointer-events: auto;
    background: url(../img/prev.png?1) no-repeat top center;
}
.col-r .second-row .swiper-button-next{
    top: 92px;
    right: 89px;
    width: 20px;
    height: 30px;
    pointer-events: auto;
    background: url(../img/next.png?1) no-repeat top center;
}
.col-r .second-row .plane_border_left{
    width: 247px;
    height: 412px;
    background: url(../img/plane_border_left.png?1) no-repeat top center;
}
.col-r .second-row .plane_border_right{
    width: 247px;
    height: 412px;
    background: url(../img/plane_border_right.png?1) no-repeat top center;
}
.col-r .second-row .plane_detail_left{
    top:120px;
}
.col-r .second-row .plane_detail_right{
    top:120px;
    left: 47%;
}
.col-r .second-row .plane_detail_left .title{
    top: -8px;
    left: 97px;
}
.col-r .second-row .plane_detail_right .title{
    top: -8px;
    left: 83px;
}
.linenode{
    height: 30px;
}
.col-r .second-row .gzsx{
    top:550px;
    width: 546px;
    height: 40px;
    border: 1px solid #2391E4;
}
.col-r .second-row .gzsx .star{
    top: 10px;
    left: 10px;
    width: 18px;
    height: 17px;
    background: url(../img/star.png?1) no-repeat top center;
}
.col-r .second-row .gzsx .title{
    top: 8px;
    left: 33px;
}
.col-r .third-row .warn_pic{
    top: 110px;
    left: 20px;
    width: 48px;
    height: 67px;
    background: url(../img/warn.png?1) no-repeat top center;
}
.col-r .third-row .comprow{
    top: 70px;
    left: 146px;
    width: 400px;
    height: 136px;
    overflow: hidden;
}
.col-r .third-row .comprow .head{
    width: 100%;
    height: 32px;
}

.col-r .third-row .warn_pic .title{
    top: -41px;
    width: 90px;
    left: -18px;
}
.col-r .third-row .warn_pic .content{
    top: 77px;
    width: 100px;
    text-align: center;
    left: -22px;
}
.col-r .third-row .comprow .head .btns{
    right: -10px;
}
.col-r .third-row .comprow .head .btn{
    display: inline-block;
    margin-right: 10px;
    height: 22px;
    width: 22px;
    padding: 0;
    border-radius: 11px;
    border: 1px solid #1a52a5;
    cursor: pointer;
    pointer-events: all;
}
.col-r .third-row .comprow .head .btn_prev{
    background: #012869 url(../img/a4.2_btn_arr1.png) no-repeat center;
}
.col-r .third-row .comprow .head .btn_next{
    background: #012869 url(../img/a4.2_btn_arr2.png) no-repeat center;
}
.col-r .third-row .comprow .head .disabled{
    opacity: 0.4;
    cursor: default;
}
.col-r .third-row .comprow .itmlst{
    position: absolute;
    top: 40px;
    left: 0;
    height: 88px;
    width: 200000px;
}

.col-r .third-row .comprow .itmlst .blk{
    position: relative;
    display: inline-block;
    width: 95px;
    height: 95px;
    margin-right: 5px;
    border-radius: 5px;
    overflow: hidden;
    transform: rotate(0);
    cursor: pointer;

    box-shadow: 0 0px 2px #000616, 0 0px 2px rgba(22,122,217,0.3) inset;

    background: #194285;
    background: -moz-linear-gradient(top,  #194285 0%, #043262 100%);
    background: -webkit-linear-gradient(top,  #194285 0%,#043262 100%);
    background: linear-gradient(to bottom,  #194285 0%,#043262 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#194285', endColorstr='#043262',GradientType=0 );

}
.col-r .third-row .tab_ywjcgj .itmlst .blk{
    width: 195px;
}
.col-r .third-row .comprow .itmlst .blk .time{
    color: #4d9cfd;
    font-size: 11px;
    margin: 6px 5px 3px 5px;
    width: 89%;
}
.col-r .third-row .comprow .itmlst .blk .time .r{
    float: right;
}
.col-r .third-row .comprow .itmlst .blk .fltno{
    text-align: center;
    font-size: 14px;
    font-weight: bold;
    color: #aecffd;
    margin: 3px ;
    top: 21px;
    width: 95%;
}
.col-r .third-row .comprow .itmlst .blk .weather{
    text-align: center;
    font-size: 30px;
    font-weight: bold;
    color: #aecffd;
    margin: 3px;
    top: 27px;
    width: 95%;
}

.col-r .third-row .comprow .itmlst .blk .city{
    width: 100%;
    text-align: center;
    font-size: 11px;
    color: #4d9cfd;
    height: 22px;
    top: 50px;
}
.col-r .third-row .comprow .itmlst .blk .bot{
    position: absolute;
    width: 100%;
    bottom: 4px;
    text-align: center;
    font-size: 10px;
    color: #046cd6;
    text-overflow:ellipsis;
    white-space: nowrap;
}



#flight_details .pic{
    left: 110px;
    width: 24px;
    height: 22px;
    background: url(../img/plane_detail_fight.png?1) no-repeat top center;
}
#flight_details .detail_dep{
    height:20px;
    display: inline-block;
    left: 30px;
    text-align: right;
}
#flight_details .detail_arr{
    height:20px;
    display: inline-block;
    right: 30px;
}

.mr_t5{
    margin-top: 5px;
}
.mr_t10{
    margin-top: 10px;
}
.mr_t15{
    margin-top: 15px;
}
.mr_t20{
    margin-top: 20px;
}
.mr_t25{
    margin-top: 25px;
}
.mr_t30{
    margin-top: 30px;
}
.back_blue{
    background-color: #2a81d2;
}