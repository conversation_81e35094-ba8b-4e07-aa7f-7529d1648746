class ipadcontrol{
	constructor(){
		this.addEvent();
	}
	addEvent(){
		$(".row_btn_1").click(function(){
			console.log("3D地球");
		})

		$(".row_btn_2").click(function(){
			console.log("电视");
		})

		$(".aoc_btn_1").click(function(event){
			$.ajax({
				type: 'post',
				url: `/ipad/socket/push/sdc-index-${userinfo.id}`,
				contentType: 'application/json',
				dataType: 'json',
				async: true,
				data: "this._popContent($('.content[data-tab=1]'))",
				success: function (response) {
					console.log(response);
				},
				error: function (response) {
				}
			});
			if($(event.currentTarget).hasClass("btnActive")){
				$(".aoc_col_2 .btnActive").removeClass("btnActive");
				$.ajax({
					type: 'post',
					url: `/ipad/socket/push/sdc-index-${userinfo.id}`,
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: "this._closeDetail($('.moreClose[data-id=1]'))",
					success: function (response) {
						console.log(response);
					},
					error: function (response) {
					}
				});
			} else {
				$(".aoc_col_2 .btnActive").removeClass("btnActive");
				this.closePopAll([2,3,5,6]);
				$(event.currentTarget).addClass("btnActive");
				$.ajax({
					type: 'post',
					url: `/ipad/socket/push/sdc-index-${userinfo.id}`,
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: "this._popDetail($('.content_btn[data-id=1]'))",
					success: function (response) {
						console.log(response);
					},
					error: function (response) {
					}
				});
			}
			console.log("通航城市 全球");
		}.bind(this));

		$(".aoc_btn_2").click(function(event){
			$.ajax({
				type: 'post',
				url: `/ipad/socket/push/sdc-index-${userinfo.id}`,
				contentType: 'application/json',
				dataType: 'json',
				async: true,
				data: "this._popContent($('.content[data-tab=2]'))",
				success: function (response) {
					console.log(response);
				},
				error: function (response) {
				}
			});
			if($(event.currentTarget).hasClass("btnActive")){
				$(".aoc_col_2 .btnActive").removeClass("btnActive");
				$.ajax({
					type: 'post',
					url: `/ipad/socket/push/sdc-index-${userinfo.id}`,
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: "this._closeDetail($('.moreClose[data-id=2]'))",
					success: function (response) {
						console.log(response);
					},
					error: function (response) {
					}
				});
			} else {
				$(".aoc_col_2 .btnActive").removeClass("btnActive");
				this.closePopAll([1,3,5,6]);
				$(event.currentTarget).addClass("btnActive");
				$.ajax({
					type: 'post',
					url: `/ipad/socket/push/sdc-index-${userinfo.id}`,
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: "this._popDetail($('.content_btn[data-id=2]'))",
					success: function (response) {
						console.log(response);
					},
					error: function (response) {
					}
				});
			}
			console.log("中国");
		}.bind(this));

		$(".aoc_btn_3").click(function(event){
			$.ajax({
				type: 'post',
				url: `/ipad/socket/push/sdc-index-${userinfo.id}`,
				contentType: 'application/json',
				dataType: 'json',
				async: true,
				data: "this._popContent($('.content[data-tab=3]'))",
				success: function (response) {
					console.log(response);
				},
				error: function (response) {
				}
			});
			if($(event.currentTarget).hasClass("btnActive")){
				$(".aoc_col_2 .btnActive").removeClass("btnActive");
				$.ajax({
					type: 'post',
					url: `/ipad/socket/push/sdc-index-${userinfo.id}`,
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: "this._closeDetail($('.moreClose[data-id=3]'))",
					success: function (response) {
						console.log(response);
					},
					error: function (response) {
					}
				});
			} else {
				$(".aoc_col_2 .btnActive").removeClass("btnActive");
				this.closePopAll([1,2,5,6]);
				$(event.currentTarget).addClass("btnActive");
				$.ajax({
					type: 'post',
					url: `/ipad/socket/push/sdc-index-${userinfo.id}`,
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: "this._popDetail($('.content_btn[data-id=3]'))",
					success: function (response) {
						console.log(response);
					},
					error: function (response) {
					}
				});
			}
			console.log("飞机+位置3D");
		}.bind(this));

		$(".aoc_btn_4").click(function(event){
			$(".aoc_col_2 .btnActive").removeClass("btnActive");
			$(event.currentTarget).addClass("btnActive");
			this.closePopAll([1,2,3,5,6]);
			$.ajax({
				type: 'post',
				url: `/ipad/socket/push/sdc-index-${userinfo.id}`,
				contentType: 'application/json',
				dataType: 'json',
				async: true,
				data: "this._popContent($('.content[data-tab=4]'))",
				success: function (response) {
					console.log(response);
				},
				error: function (response) {
				}
			});
			console.log("航线+统计3D");
		}.bind(this));

		$(".aoc_btn_5").click(function(event){
			$.ajax({
				type: 'post',
				url: `/ipad/socket/push/sdc-index-${userinfo.id}`,
				contentType: 'application/json',
				dataType: 'json',
				async: true,
				data: "this._popContent($('.content[data-tab=5]'))",
				success: function (response) {
					console.log(response);
				},
				error: function (response) {
				}
			});
			if($(event.currentTarget).hasClass("btnActive")){
				$(".aoc_col_2 .btnActive").removeClass("btnActive");
				$.ajax({
					type: 'post',
					url: `/ipad/socket/push/sdc-index-${userinfo.id}`,
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: "this._closeDetail($('.moreClose[data-id=5]'))",
					success: function (response) {
						console.log(response);
					},
					error: function (response) {
					}
				});
			} else {
				$(".aoc_col_2 .btnActive").removeClass("btnActive");
				this.closePopAll([1,2,3,6]);
				$(event.currentTarget).addClass("btnActive");
				$.ajax({
					type: 'post',
					url: `/ipad/socket/push/sdc-index-${userinfo.id}`,
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: "this._popDetail($('.content_btn[data-id=5]'))",
					success: function (response) {
						console.log(response);
					},
					error: function (response) {
					}
				});
			}
			console.log("正常率+航司");
		}.bind(this));

		$(".aoc_btn_6").click(function(event){
			$.ajax({
				type: 'post',
				url: `/ipad/socket/push/sdc-index-${userinfo.id}`,
				contentType: 'application/json',
				dataType: 'json',
				async: true,
				data: "this._popContent($('.content[data-tab=6]'))",
				success: function (response) {
					console.log(response);
				},
				error: function (response) {
				}
			});
			if($(event.currentTarget).hasClass("btnActive")){
				$(".aoc_col_2 .btnActive").removeClass("btnActive");
				$.ajax({
					type: 'post',
					url: `/ipad/socket/push/sdc-index-${userinfo.id}`,
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: "this._closeDetail($('.moreClose[data-id=6]'))",
					success: function (response) {
						console.log(response);
					},
					error: function (response) {
					}
				});
			} else {
				$(".aoc_col_2 .btnActive").removeClass("btnActive");
				this.closePopAll([1,2,3,5]);
				$(event.currentTarget).addClass("btnActive");
				$.ajax({
					type: 'post',
					url: `/ipad/socket/push/sdc-index-${userinfo.id}`,
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: "this._popDetail($('.content_btn[data-id=6]'))",
					success: function (response) {
						console.log(response);
					},
					error: function (response) {
					}
				});
			}
			console.log("航班+位置2D");
		}.bind(this));

		$(".row_btn_3_1").click(function(){
			$.ajax({
				type: 'post',
				url: `/ipad/socket/push/sdc-index-${userinfo.id}`,
				contentType: 'application/json',
				dataType: 'json',
				async: true,
				data: "this._gotoAoc($('.compname'))",
				success: function (response) {
					console.log(response);
				},
				error: function (response) {
				}
			});
			console.log("HU 公司整体");
		})

		$(".row_btn_3_2").click(function(){
			console.log("看飞机3D");
		})


		$(".row_btn_3_3").click(function(event){
			this.showComList();
			console.log("天津");
		}.bind(this));



		$(".closeBtn").click(function(){
			$(".mask").hide();
			$(this).parent().hide();
			console.log("弹窗关闭按钮");
		})

		$(".mask").click(function(event){
			$(event.currentTarget).hide();
			$(".companylist").hide();
		}.bind(this));

		$(".companylist span").click(function(event){
			let company=$(event.currentTarget).text();
			company=company.replace("航空","")+" >";
			console.log(company);
			$(".row_btn_3_3").text(company);
			this.hideComList();
		}.bind(this));

		$(".row_btn_4_1").click(function(){
			$.ajax({
				type: 'post',
				url: `/ipad/socket/push/7x2-index-${userinfo.id}`,
				contentType: 'application/json',
				dataType: 'json',
				async: true,
				data: "cardclick($('#arp_code_PEK'))",
				success: function (response) {
					console.log(response);
				},
				error: function (response) {
				}
			});
			console.log("基地（北京）");

		})


		$(".row_btn_4_2").click(function(){
			$.ajax({
				type: 'post',
				url: `/ipad/socket/push/7x2-base-${userinfo.id}`,
				contentType: 'application/json',
				dataType: 'json',
				async: true,
				data: "switchmap()",
				success: function (response) {
					console.log(response);
				},
				error: function (response) {
				}
			});
			console.log("飞机2D");
		})

		$(".row_btn_4_3").click(function(){
			console.log("海口");
			this.showBaseList();
		}.bind(this));

		$(".baselist span").click(function(event){
			let company=$(event.currentTarget).text();
			let code = $(event.currentTarget).data("code");
			console.log(company);
			$(".row_btn_4_3").text(company + " >");
			$.ajax({
				type: 'post',
				url: `/ipad/socket/push/7x2-index-${userinfo.id}`,
				contentType: 'application/json',
				dataType: 'json',
				async: true,
				data: `cardclick($('#arp_code_${code}'))`,
				success: function (response) {
					console.log(response);
				},
				error: function (response) {
				}
			});
			this.hideBaseList()
		}.bind(this));




		$(".row_btn_5_1").click(function(){
			$.ajax({
				type: 'post',
				url: `/ipad/socket/push/7x2-base-${userinfo.id}`,
				contentType: 'application/json',
				dataType: 'json',
				async: true,
				data: `showFlightDetails(planeLocationList[0].fltno)`,
				success: function (response) {
					console.log(response);
				},
				error: function (response) {
				}
			});
			console.log("航班默认");
		})

		$(".row_btn_5_2").click(function(){
			let fltno = $("#fltno").val();
			$.ajax({
				type: 'post',
				url: `/ipad/socket/push/7x2-base-${userinfo.id}`,
				contentType: 'application/json',
				dataType: 'json',
				async: true,
				data: `showFlightDetails("${fltno}")`,
				success: function (response) {
					console.log(response);
				},
				error: function (response) {
				}
			});
			console.log("转到");
		})
	}
	closePopAll(arr){
		arr.forEach((v, i)=>{
			$.ajax({
				type: 'post',
				url: `/ipad/socket/push/sdc-index-${userinfo.id}`,
				contentType: 'application/json',
				dataType: 'json',
				async: true,
				data: `this._closeDetail($('.moreClose[data-id=${v}]'))`,
				success: function (response) {
					console.log(response);
				},
				error: function (response) {
				}
			});
		})
	}
	
	showComList(){
		$(".mask").show();
		$(".companylist").show();
	}
	hideComList(){
		$(".mask").hide();
		$(".companylist").hide();
	}
	showBaseList(){
		$(".mask").show();
		$(".baselist").show();
	}

	hideBaseList(){
		$(".mask").hide();
		$(".baselist").hide();
	}
}
let i = new ipadcontrol();