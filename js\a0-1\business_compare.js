(function () {
  dfd.done(function () {
    if (!hasAllCompanyPermission()) {
      $(".zongshouru .detail").remove();
      return;
    }
  });

  $(".zongshouru .detail").on("click", function () {
    $("#pop_company_shouru").removeClass("hide");
    $(".windowMask").removeClass("hide");
    var dateId = $(".zongshouru").attr("dateKey");
    var dateType = $(".zongshouru").attr("dateType");
    if (dateId != null && dateId.length > 0) {
      if (window.business_comparewin == null) {
        initBusiness_CompareWin(dateType, dateId);
      } else {
        window.business_comparewin.refreshView(dateType, dateId);
      }
    }
  });

  function initBusiness_CompareWin(dateType, dateId) {
    var page = new Vue({
      el: ".shouruWinBody",
      template: $("#business_compare_template").html(),
      data: function () {
        return {
          dateType: dateType,
          weeks: weeks,
          lylExpand: false,
          zglsrExpand: false,
          xssrExpand: false,
          kpi: "PASS_CARGO_SHIFTS",
          kpi2: "CARGO_SHIFTS",
          orderType: "desc",
          showWeekList: false,
          selectedWeek: null,
          weekCmpActive: false,
          dateType: dateType,
          keyunList: [],
          huoyunList: [],
          allItem: {},
          allCargoItem: {},
          companyOrder: {
            HU: 1,
            JD: 2,
            GS: 3,
            HX: 18,
            "8l": 5,
            PN: 6,
            FU: 7,
            GX: 8,
            UQ: 9,
            Y8: 10,
            "9H": 11,
            GT: 12,
            CN: 13,
            100101: 14,
            100400: 15,
            101300: 16,
            101900: 17,
          },
        };
      },
      mounted: function () {
        var me = this;

        if (this.weeks.length > 1) {
          this.selectedWeek = weeks[1];
        }

        $(me.$refs["datetimepicker_D"]).datetimepicker({
          defaultDate: new Date(),
          format: "YYYY/MM/DD",
          sideBySide: true,
          maxDate: new Date(),
          widgetPositioning: {
            horizontal: "right",
          },
          locale: "zh-cn",
        });
        //月
        $(me.$refs["datetimepicker_M"]).datetimepicker({
          defaultDate: new Date(),
          format: "YYYY/MM",
          viewMode: "months",
          sideBySide: true,
          maxDate: new Date(),
          widgetPositioning: {
            horizontal: "right",
          },
          locale: "zh-cn",
        });
        //年
        $(me.$refs["datetimepicker_Y"]).datetimepicker({
          defaultDate: new Date(),
          format: "YYYY",
          viewMode: "years",
          sideBySide: true,
          maxDate: new Date(),
          widgetPositioning: {
            horizontal: "right",
          },
          locale: "zh-cn",
        });

        me.setDatePickerValue(dateType, dateId);
        //先查数据,再做下面事件监听,不然可能会触发dp.change事件,多做一次请求

        me.queryData(me.getDate());

        $(me.$refs["datetimepicker_D"]).on("dp.change", function (e) {
          me.queryData(e.date._d);
        });
        $(me.$refs["datetimepicker_M"]).on("dp.change", function (e) {
          me.queryData(e.date._d);
        });
        $(me.$refs["datetimepicker_Y"]).on("dp.change", function (e) {
          me.queryData(e.date._d);
        });

        $(me.$refs["table"]).niceScroll({
          cursorcolor: "#1b5092", //#CC0071 光标颜色
          cursoropacitymax: 1, //改变不透明度非常光标处于活动状态（scrollabar“可见”状态），范围从1到0
          touchbehavior: false, //使光标拖动滚动像在台式电脑触摸设备
          cursorwidth: "10px", //像素光标的宽度
          cursorborderradius: "5px", //以像素为光标边界半径
          autohidemode: false, //是否隐藏滚动条
          cursorborder: "0px solid #fff",
          background: "rgba(27, 80, 146, 0.3)",
        });
        me.mounted = true;
      },
      methods: {
        expand() {
          this.refreshWidth();
        },
        refreshWidth() {
          var me = this;
          setTimeout(function () {
            $(me.$refs["table"]).getNiceScroll(0).resize();
          }, 300);
        },
        sort(kpi1, kpi2) {
          var me = this;
          if (me.kpi == kpi1) {
            if (me.orderType == "default") {
              me.orderType = "desc";
            } else if (me.orderType == "desc") {
              me.orderType = "asc";
            } else if (me.orderType == "asc") {
              me.orderType = "default";
            }
            me.sortData(me.keyunList, me.kpi, me.orderType);
            if (kpi2 != null) {
              me.sortData(me.huoyunList, kpi2, me.orderType);
            }
          } else {
            me.kpi = kpi1;
            me.kpi2 = kpi2;
            me.orderType = "desc";
            me.sortData(me.keyunList, me.kpi, me.orderType);
            if (kpi2 != null) {
              me.sortData(me.huoyunList, kpi2, me.orderType);
            }
          }
        },
        setDatePickerValue(dateType, dateId) {
          var me = this;
          if (dateType != "L") {
            $(me.$refs[`datetimepicker_${dateType}`])
              .data("DateTimePicker")
              .date(me.getDateStr(dateType, dateId));
          } else {
            var selectedWeek = me.weeks.find((v) => {
              return v.DATE_ID == dateId;
            });
            if (me.selectedWeek != selectedWeek) {
              me.selectedWeek = selectedWeek;
              //改变才数据,且在mounted 之后,
              if (me.mounted) {
                me.queryData(selectedWeek);
              }
            }
          }
        },
        refreshView(dateType, dateId) {
          var me = this;
          if (me.dateType != dateType) {
            me.switchDateType(dateType);
          } else {
            me.setDatePickerValue(dateType, dateId);
          }
        },
        getDateStr(dateType, dateId) {
          if (dateType == "D") {
            return moment(dateId, "YYYYMMDD").format("YYYY/MM/DD");
          } else if (dateType == "M") {
            return moment(dateId, "YYYYMM").format("YYYY/MM");
          } else if (dateType == "Y") {
            return dateId;
          }
        },
        getWeekDesc() {
          return this.selectedWeek ? this.selectedWeek.DATE_DESC_XS : "";
        },
        onWeekMouseOut() {
          this.weekCmpActive = false;
          setTimeout(this.validActive, 300);
        },
        onWeekMouseOver() {
          this.weekCmpActive = true;
        },
        validActive() {
          if (!this.weekCmpActive) {
            this.showWeekList = false;
          }
        },
        dropWeekList() {
          this.showWeekList = true;
        },
        onWeekChange(week) {
          this.selectedWeek = week;
          this.showWeekList = false;
          this.queryData(week);
        },
        getWeekLabel: function (item) {
          if (item) {
            var week = item.DATE_ID.substring(5, 7);
            var year = item.DATE_ID.substring(0, 4);
            return `${year}年第${week}周`;
          }
        },
        closeWin: function () {
          $("#pop_unsafe_detail").addClass("hide");
          $(".windowMask").addClass("hide");
        },
        switchDateType(dateType) {
          this.dateType = dateType;
          var date = this.getDate();
          this.queryData(date);
        },
        getDate() {
          var me = this;
          var dateType = this.dateType;
          if (dateType == "D") {
            return $(me.$refs["datetimepicker_D"])
              .data("DateTimePicker")
              .date()
              .startOf("day")._d;
          } else if (dateType == "L") {
            return this.selectedWeek;
          } else if (dateType == "M") {
            return $(me.$refs["datetimepicker_M"])
              .data("DateTimePicker")
              .date()
              .startOf("day")._d;
          } else if (dateType == "Y") {
            return $(me.$refs["datetimepicker_Y"])
              .data("DateTimePicker")
              .date()
              .startOf("day")._d;
          }
        },
        getValue(item, kpiCode, precision, divNum) {
          var list = item[kpiCode];
          if (list != null && list.length > 0) {
            var value = list[0].KPI_VALUE;
            if (divNum > 0) {
              return toFixed(value / divNum, precision > 0 ? precision : 0);
            } else {
              return toFixed(value, precision > 0 ? precision : 0);
            }
          }
          return "-";
        },
        getPercision(item, allItem, kpiCode, showPersion) {
          if (showPersion) {
            var list = item[kpiCode];
            var list2 = allItem[kpiCode];
            var value = 0;
            var allValue = 0;
            if (list != null && list.length > 0) {
              value = list[0].KPI_VALUE;
            }
            if (list2 != null && list2.length > 0) {
              allValue = list2[0].KPI_VALUE;
            }
            if (value == 0) {
              return "0.0%";
            }
            if (allValue == 0) {
              return "-";
            }
            var rs = Number((value * 100) / allValue).toFixed(1);
            return `${rs}%`;
          }
          return "";
        },
        getCargoCompanyName (cmpId) {
          console.log("----------cargoCompany--------",cargoCompany,cargoCompany.length)
          for (var i = 0; i < cargoCompany.length; i++) {
            if (cmpId == cargoCompany[i].id) {
              return cargoCompany[i].name;
            }
          }
          return "";
        },
        refreshView(dateType, dateId) {
          var me = this;
          if (me.dateType != dateType) {
            me.switchDateType(dateType);
          } else {
            me.setDatePickerValue(dateType, dateId);
          }
        },
        closeWin: function () {
          $("#pop_company_shouru").addClass("hide");
          $(".windowMask").addClass("hide");
        },
        isSameDate(d1, d2) {
          if (!d1) {
            return false;
          }
          return moment(d1).diff(moment(d2)) == 0;
        },
        getCompanyOrder(companyCode) {
          var me = this;
          return me.companyOrder[companyCode];
        },

        sortData(list, kpi, orderType) {
          var me = this;
          list.sort(function (a, b) {
            if (a.isHightList) {
              return 1;
            }
            if (b.isHightList) {
              return -1;
            }

            var v1 = me.getValue(a, kpi, 2, 1);
            var v2 = me.getValue(b, kpi, 2, 1);
            if (v1 == "-") {
              v1 = 0;
            }
            if (v2 == "-") {
              v2 = 0;
            }

            if (v1 == v2) {
              return (
                me.getCompanyOrder(a.companyCode) -
                me.getCompanyOrder(b.companyCode)
              );
            }
            if (orderType == "desc") {
              return v2 - v1;
            } else if (orderType == "asc") {
              return v1 - v2;
            } else {
              return (
                me.getCompanyOrder(a.companyCode) -
                me.getCompanyOrder(b.companyCode)
              );
            }
          });
        },

        queryData(date) {
          var me = this;
          eking.ui.loading.show();
          var dateKey = getDateId(date, me.dateType);
          var len = companylist.length;
          var codelist = [];
          for (var i = 0; i < len; i++) {
            var dat = companylist[i];
            if (dat.code != "HNAIN" && dat.code != "CN") {
              codelist.push(dat.code);
            }
          }

          var companyIds = [];
          for (var i = 0; i < cargoCompany.length; i++) {
            companyIds.push(cargoCompany[i].id);
          }

          var kpi_list = [
            "PASS_CARGO_SHIFTS", //客运班次（含客拉货）
            // "EXCUTED_NO", //客公里收入(含燃油)(元)
            "CAP_KILO_INC_FUEL", //座收(含燃油),
            "PASS_CARGO_INC", //客运-货运收入
            "PASS_CARGO_HOUR_INC", //客运小时收入（含客拉货）
            "EST_INC_FUEL", //客收（元）
            "PASS_CARGO_AC_UTIL_RATE", //客运利用率（含客拉货）
            "ROUTE_SUBSIDY_INC", //航线补贴
            "AUX_INC", //辅营收入
            "AC_NO_QM", //期末在册飞机架数(运行可用运力),
            "W_CAP_KILO_INC_FUEL", //宽体机座收  10220
            "N_CAP_KILO_INC_FUEL", //窄体机座收  10221
            "B_CAP_KILO_INC_FUEL", //支线机座收  10222
            "W_HOUR_INC_FUEL", //宽体机小时收入  10179
            "N_HOUR_INC_FUEL", //窄体机小时收入  10180
            "B_HOUR_INC_FUEL", //支线机小时收入  10219
            "W_AC_UTIL_RATE", //宽体机日利用率  10181
            "N_AC_UTIL_RATE", //窄体机日利用率 N_AC_UTIL_RATE 10182
            "B_AC_UTIL_RATE", //支线机日利用率 B_AC_UTIL_RATE 10202
          ];

          var param = {
            // "LIMIT": 3,
            // "QUERY_DATE": formatDate(date),
            DATE_TYPE_CN: getDateTypeCn(me.dateType), // 例会周L、年Y、月M、日D
            DATE_TYPE: me.dateType,
            KPI_CODE: kpi_list.join(","),
            DATE_ID: dateKey,
            // 'COMP_ID':  companyIds.join(","),
            COMP_CODE: codelist.join(","),
          };
          var d1, d2, data_huoshou, dataCargo;
          d1 = $.ajax({
            type: "post",
            url: "/bi/query/getfaccomkpi?keshou_detail",
            contentType: "application/json",
            dataType: "json",
            async: true,
            data: JSON.stringify(param),
            success: function (response) {
              data_huoshou = response.data;
            },
          });

          var kpi_list2 = [
            "CARGO_AC_NUM", //货运-可用运力（在册运力）
            "CARGO_AC_UTIL_RATE", //货运-利用率
            "CARGO_SHIFTS", //货运-航班班次
            "CARGO_HOUR_INC", //货运-小时收入
            "DETAIL_CARGO_INC", //货运收入
          ];

          var param2 = {
            // "LIMIT": 3,
            // "QUERY_DATE": formatDate(date),
            DATE_ID: dateKey,
            DATE_TYPE_CN: getDateTypeCn(me.dateType), // 例会周L、年Y、月M、日D
            DATE_TYPE: me.dateType,
            KPI_CODE: kpi_list2.join(","),
            COMP_ID: companyIds.join(","),
          };

          d2 = $.ajax({
            type: "post",
            url: "/bi/query/getfaccomkpi?cargo_detail",
            contentType: "application/json",
            dataType: "json",
            async: true,
            data: JSON.stringify(param2),
            success: function (response) {
              dataCargo = response.data;
            },
          });

          $.when(d1, d2).then(function () {
            eking.ui.loading.hide();
            var keyunAllStr = null;
            var list = [];
            for (var p in data_huoshou) {
              list.push({
                p: p,
                data: data_huoshou[p],
              });
            }

            for (var i = 0; i < list.length; i++) {
              var item = list[i].data;
              if (list[i]["p"] == "HNAHK") {
                me.allItem = item;
              }
            }
            var subItemList = [];
            var keyunAllItem = null;
            var keyunList = [];
            for (var i = 0; i < list.length; i++) {
              var item = list[i].data;
              var isHightList = list[i]["p"] == "HNAHK";
              item.isHightList = isHightList;
              item.companyCode = list[i]["p"];
              if (!isHightList) {
                keyunList.push(item);
              } else {
                keyunAllItem = item;
              }
            }
            if (keyunAllItem != null) {
              keyunList.push(keyunAllItem);
            }

            if (me.kpi == null) {
              me.sortData(keyunList, "PASS_CARGO_SHIFTS", "desc");
            } else {
              me.sortData(keyunList, me.kpi, me.orderType);
            }

            me.keyunList = keyunList;

            var list2 = [];
            for (var p in dataCargo) {
              list2.push({
                p: p,
                data: dataCargo[p],
              });
            }

            for (var i = 0; i < list2.length; i++) {
              var item = list2[i].data;
              if (list2[i]["p"] == "100") {
                me.allCargoItem = item;
              }
            }

            var cargoAllItem = null;
            var huoyunList = [];

            for (var i = 0; i < list2.length; i++) {
              var item = list2[i].data;
              var p = list2[i]["p"];
              var isHightList = p == "100";
              item.isHightList = isHightList;
              item.companyCode = p;
              if (!isHightList) {
                huoyunList.push(item);
              } else {
                cargoAllItem = item;
              }
            }
            console.log("huoyunList1-----------", huoyunList);
            if (cargoAllItem != null) {
              huoyunList.push(cargoAllItem);
            }
            console.log("huoyunList2-----------", huoyunList);
            if (me.kpi == null) {
              me.sortData(huoyunList, "CARGO_SHIFTS", "desc");
            } else {
              me.sortData(huoyunList, me.kpi2, me.orderType);
            }
            me.huoyunList = huoyunList;
            console.log("huoyunList3-----------", huoyunList);
            me.refreshWidth();
          });
        },
      },
    });

    window.business_comparewin = page;
  }
})();
