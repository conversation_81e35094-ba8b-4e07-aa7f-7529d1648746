var mapBoxEchart = echarts.init(document.getElementById("mapBox"));
let flightNo;
let comp_code;
let stdEndUtcTime;
let stdStartUtcTime;
let stdStart;
let stdEnd;
let stdEnd7;
let flightInfoList;
let flightInfoListObj;
let sortFlt;
let depAirport = ""; //出发机场信息
let middleAirport = "";
let arrAirport = ""; //到达机场信息
let airportList = new Object();
let planeLocationList = new Array(); //飞机实时位置
let fltLoadSheetInfo = new Object();
let weather = {};
let pointlist; //飞行轨迹
let runaway_data = []; //跑道数据
let scrollInterval;
let crewData;
let psr;
let ckiNum;
let cabin;
let curOil; //当前油量
const parent_company = "HNAHK";
const weather_map = {
  晴: "icon-e600_sunny",
  沙: "icon-e617_dust1",
  雹: "icon-e620_hail",
  雾: "icon-e615_fog",
  烟: "icon-e615_fog",
  阴: "icon-e604_gloomy",
  雷: "icon-e606_rain2",
  暴: "icon-e606_rain2",
  风: "icon-e612_wind",
  霾: "icon-e613_haze",
  云: "icon-e602_cloudy",
  雨: "icon-e607_rain3",
  雪: "icon-e610_snow3",
};
const statusMap = {
  ARR: "落地",
  NDR: "落地",
  ATD: "推出",
  ATA: "到达",
  CNL: "取消",
  DEL: "延误",
  DEP: "起飞",
  RTR: "返航",
  SCH: "计划",
};
const wxCode2Name = {
  TS: "干雷",
  TSRA: "中雷雨",
  "-TSRA": "弱雷雨",
  "+TSRA": "强雷雨",
  CB: "对流云",
  TCU: "浓积云",
  RA: "中雨",
  "+RA": "大雨",
  "+SHRA": "强阵雨",
  SHRA: "中阵雨",
  DZ: "毛毛雨",
  FZRA: "冻雨",
  GR: "冰雹",
  GS: "霰",
  WS: "风切变",
  FG: "大雾",
  FU: "烟",
  HZ: "霾",
  BR: "轻雾",
  FZFG: "冻雾",
  BCFG: "散雾",
  MIFG: "浅雾",
  SN: "中雪",
  "+SN": "大雪",
  SHSN: "阵雪",
  "+SHSN": "强阵雪",
  BLSN: "高吹雪",
  DRSA: "低吹雪",
  SA: "扬沙",
  SS: "沙暴",
  BLSA: "高吹沙",
  DRSA: "低吹沙",
  "+SS": "强沙暴",
  DU: "浮尘",
};
var option = {
  backgroundColor: "#060922",
  tooltip: {
    trigger: "item",
  },
  geo: {
    map: "world",
    roam: true, // 禁止鼠标缩放和平移漫游
    silent: true, //不响应和触发鼠标事件
    aspectScale: 0.75, //地图的长宽比
    center: [106.54, 29.59], //初始视图取中间站点
    zoom: 3,

    itemStyle: {
      normal: {
        borderWidth: 0.5,
        areaColor: "#224884",
        borderColor: "#060922",
      },
    },
    regions: [
      {
        name: "ChinaLine",
        itemStyle: {
          normal: {
            borderWidth: 2,
            areaColor: "#224884",
            borderColor: "#060922",
          },
        },
      },
    ],
  },
  series: [],
};

class flight {
  constructor() {
    flightNo =
      this._getQueryString("fltno") ||
      this._getQueryString("flightNo") ||
      this._getQueryString("flight");
    comp_code = getQueryString("compCode");
    this._initMap();
    this._initTime();
    this._addEvent();
  }

  loadAll() {
    if ($("#loading_msk").length == 0) {
      showLoading();
    }
    $(".fltno").text(flightNo);
    $(".fltno").css(
      "background-image",
      "url(../img/logo_" + comp_code + ".png)"
    );
    $(".leftLogo").css(
      "background-image",
      "url(/largescreen/general/img/logo_" + comp_code + "_CA.png)"
    );
    Promise.all([
      this.getStandardFocFlightInfo(),
      this.getAirportList(),
      this.getPsr(),
      this.getCrew(),
      this.getPlanePos(),
      this.getPlaneTrack(),
      this.getFltLoadSheetInfo(),
    ])
      .then((resolve) => {
        this.setCabin(sortFlt[0].acLongNo);
        this.setCrew();
        this.getPlaneInfo(sortFlt[0].acLongNo);
        this.setMapData()
          .then((resolve) => {
            this.setFltInfo(sortFlt); //顶部信息
          })
          .catch((reject) => {
            console.log(reject);
            alert("无该航班实时飞行数据！");
            this._hideLoad();
          }); //飞机航线
        this._hideLoad();
      })
      .catch((reject) => {
        console.log(reject);
        alert("数据获取出错！");
        this._hideLoad();
      });
  }

  getFltLoadSheetInfo() {
    return new Promise((resolve, reject) => {
      var arr = stdStart.split(" ");
      var param = {
        flightNo: flightNo,
        flightDate: arr[0],
      };
      $.ajax({
        type: "post",
        url: "/bi/web/getFltLoadSheetInfo",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          fltLoadSheetInfo = response.date;
          console.log("getFltLoadSheetInfo", fltLoadSheetInfo);
          if (fltLoadSheetInfo) {
            $(".oil_1").text(fltLoadSheetInfo.takeOffFuel);
            $(".oil_3").text(fltLoadSheetInfo.tripFuel);
            $(".takeOffWeight").text(fltLoadSheetInfo.takeOffWeight);
          }
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }

  getPlaneTrack() {
    return new Promise((resolve, reject) => {
      let param = {
        mode: "track",
        fi: flightNo,
      };
      $.ajax({
        type: "post",
        url: "/bi/web/flightMq",
        contentType: "application/json",
        dataType: "json",
        async: false,
        data: JSON.stringify(param),
        success: function (response) {
          if (response != undefined) {
            pointlist = response.data;
          }
          console.log("pointlist", pointlist);
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }

  getStandardFocFlightInfo() {
    return new Promise((resolve, reject) => {
      var param = {
        stdStart: stdStart,
        stdEnd: stdEnd,
        acOwner: comp_code,
        statusList: "",
      };
      $.ajax({
        type: "post",
        url: "/bi/web/getStandardFocFlightInfo",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          var list = response.data;
          flightInfoList = {};
          flightInfoListObj = {};
          for (var i = list.length - 1; i >= 0; i--) {
            var obj = list[i];
            flightInfoList[obj.flightNo] = obj;
            if (flightInfoListObj[obj.flightNo] == undefined) {
              flightInfoListObj[obj.flightNo] = [];
              flightInfoListObj[obj.flightNo].push(obj);
            } else {
              flightInfoListObj[obj.flightNo].push(obj);
            }

            if (obj.flightNo == flightNo) {
              console.log(obj);
            }
          }

          var flt = flightInfoListObj[flightNo];

          if (flt == undefined) {
            $("body").hide();
            alert("没有查询到航班信息");

            return;
          }

          console.log(flightNo, flt);

          if (flt.length > 1) {
            var dep1 = flt[0].depCity,
              arr1 = flt[0].arrCity,
              dep2 = flt[1].depCity,
              arr2 = flt[1].arrCity;
            if (arr1 == dep2) {
              sortFlt = flt;
            } else if (arr2 == dep1) {
              sortFlt = flt.reverse();
            }
          } else {
            sortFlt = flt;
          }

          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }

  getPlanePos() {
    return new Promise((resolve, reject) => {
      let param = {
        mode: "pos",
      };
      $.ajax({
        type: "post",
        url: "/bi/web/flightMq",
        contentType: "application/json",
        dataType: "json",
        async: false,
        data: JSON.stringify(param),
        success: function (response) {
          let processData = (data1) => {
            let lst = {};
            let len = data1.length;
            for (var i = 0; i < len; i++) {
              var dd = data1[i];
              var fi = dd.fi;
              if (lst[fi] == undefined) {
                lst[fi] = {
                  data: [],
                };
                lst[fi]["data"].push(dd);
              } else {
                lst[fi]["data"].push(dd);
              }
            }

            return lst;
          };
          let list = processData(response.data.data1);
          let fltobj = list[flightNo];
          console.log(flightNo, fltobj);
          if (fltobj) {
            let itmx2 = fltobj.data;
            let itm;
            if (itmx2 && itmx2.length > 1) {
              let itm1 = itmx2[0];
              let itm2 = itmx2[1];
              itm1.UTC = itm1.UTC.replace(" ", "");
              itm2.UTC = itm2.UTC.replace(" ", "");
              if (itm1.UTC > itm2.UTC) {
                itm = itm1;
                itm.LON1 = itm2.LON;
                itm.LAT1 = itm2.LAT;
              } else if (itm1.UTC < itm2.UTC) {
                itm = itm2;
                itm.LON1 = itm1.LON;
                itm.LAT1 = itm1.LAT;
              } else {
                itm = itm2;
                itm.LON1 = itm1.LON;
                itm.LAT1 = itm1.LAT;
              }
            } else if (itmx2 && itmx2.length > 0) {
              itm = itmx2[0];
            }
            if (itm) {
              let alt = itm.ALT;
              let cas = itm.CAS;
              let vec;
              let fltno = itm.fi;
              let acno = itm.an;
              acno = acno.replace("-", "");
              let lon = formatLonLat(itm.LON);
              let lon1 = formatLonLat(itm.LON1);
              let lat = formatLonLat(itm.LAT);
              let lat1 = formatLonLat(itm.LAT1);
              if (isNaN(itm.LON)) {
                vec = Number(itm.VEC);
              }
              let oil = isNaN(itm.OIL) ? "" : itm.OIL;
              let pdat = {
                fltno: fltno,
                acno: acno,
                alt: alt,
                vec: vec,
                lon: lon,
                lat: lat,
                lon1: lon1,
                lat1: lat1,
                oil: oil,
              };
              let code = acno + "-" + fltno;
              if (pdat.vec == undefined) {
                pdat.vec = getGeoAngle(
                  pdat.lat,
                  pdat.lon,
                  pdat.lat1,
                  pdat.lon1
                );
              }
              planeLocationList.push(pdat);
            }
          }
          console.log("planeLocationList", planeLocationList);
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }

  getAirportList() {
    return new Promise((resolve, reject) => {
      let param = {
        //"AIRPORT_CODE": arpcodes, // 可选，传入机场CODE
      };
      $.ajax({
        type: "post",
        url: "/bi/web/airportdetail",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          var list = response.airport;
          for (var i = list.length - 1; i >= 0; i--) {
            var arp = list[i];
            airportList[arp.code] = arp;
          }
          console.log("airportList", airportList);
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }

  getCrew() {
    return new Promise((resolve, reject) => {
      let param = {
        flightNo: flightNo,
      };
      $.ajax({
        type: "post",
        url: "/bi/web/findFlightReportV2",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          if (response.data && response.data[0]) {
            crewData = response.data[0];
          }
          console.log("crewData", crewData);
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }

  getPsr() {
    return new Promise((resolve, reject) => {
      let param = {
        flightNo: flightNo,
      };
      $.ajax({
        type: "post",
        url: "/bi/web/findPsrStat",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          if (response.data != undefined) {
            psr = response.data[0];
            ckiNum = psr.ckiNum; // 值机人数
            let bookNum = psr.bookNum;

            // 要客		svipNum		ckiImportantConcernNum
            // 老人		chdNum		ckiEldNum
            // 儿童		eldNum		ckiChdNum
            // 特服		spNum		ckiSpNum
            var svipNum = !isNaN(psr.importantConcernNum)
              ? Number(psr.ckiImportantConcernNum)
              : 0; //要客
            var chdNum = !isNaN(psr.chdNum) ? Number(psr.ckiChdNum) : 0; // 儿童
            var eldNum = !isNaN(psr.eldNum) ? Number(psr.ckiEldNum) : 0; // 老人
            var spNum = !isNaN(psr.spNum) ? Number(psr.ckiSpNum) : 0; // 老人

            var infNum = !isNaN(psr.infNum) ? Number(psr.ckiInfNum) : 0;
            var vipNum = !isNaN(psr.vipNum) ? Number(psr.ckiVipNum) : 0;
            var cipNum = !isNaN(psr.cipNum) ? Number(psr.ckiCipNum) : 0;

            // 中转旅客		ckiInOutNum		ckiInNum + ckiOutNum
            var ckiInNum = !isNaN(psr.ckiInNum) ? Number(psr.ckiInNum) : 0;
            var ckiOutNum = !isNaN(psr.ckiOutNum) ? Number(psr.ckiOutNum) : 0;
            var ckiInOutNum = ckiInNum + ckiOutNum;

            // F舱		ckiFNum
            // C舱		ckiCNum
            // W舱		ckiFNum
            // Y舱		ckiCNum
            var fNum = !isNaN(psr.fNum) ? Number(psr.ckiFNum) : 0; // F舱
            var cNum = !isNaN(psr.cNum) ? Number(psr.ckiCNum) : 0;
            var wNum = !isNaN(psr.wNum) ? Number(psr.ckiWNum) : 0;
            var yNum = !isNaN(psr.yNum) ? Number(psr.ckiYNum) : 0;

            $("#dzck_qd").text(bookNum);
            $(".fNum").text(fNum);
            $(".cNum").text(cNum);
            $(".wNum").text(wNum);
            $(".yNum").text(yNum);
            $(".svipNum").text(svipNum);
            $(".chdNum").text(chdNum);
            $(".eldNum").text(eldNum);
            $(".spNum").text(spNum);
            $(".ckiInOutNum").text(ckiInOutNum);
            $(".ckiInNum").text(ckiInNum);
            $(".ckiOutNum").text(ckiOutNum);
          }
          // console.log("psr", psr);
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }

  getPlaneInfo(acLongNo) {
    let maintInfoList;
    let maintInfoIndex = 0;
    let param = {
      startDate: stdStart,
      endDate: stdEnd7,
      acLongNo: acLongNo,
    };
    $.ajax({
      type: "post",
      url: "/bi/web/getFocMaintInfoByList",
      contentType: "application/json",
      dataType: "json",
      async: true,
      data: JSON.stringify(param),
      success: function (response) {
        maintInfoList = response.data;
        if (maintInfoList && maintInfoList.length > 0) {
          setMaintPage();
        }
      }.bind(this),
      error: function () {},
    });
    let setMaintPage = () => {
      $(".col_right_row .btn_prev").removeClass("disabled");
      $(".col_right_row .btn_next").removeClass("disabled");
      if (maintInfoIndex == 0) {
        $(".col_right_row .btn_prev").addClass("disabled");
      }
      if (maintInfoIndex == maintInfoList.length - 1) {
        $(".col_right_row .btn_next").addClass("disabled");
      }
      let info = maintInfoList[maintInfoIndex];
      let airport = airportList[info.stn];
      $(".mntstn").text(airport.chn_name);
      $(".seq").text(info.seq);
      $(".mntComment").text(info.mntComment);
      $(".mntComment").attr("title", info.mntComment);
      let tend = info.tEnd;
      let tstart = info.tStart;
      tend = tend.slice(0, tend.lastIndexOf(":"));
      tstart = tstart.slice(0, tstart.lastIndexOf(":"));
      $(".mnttEnd").text(tend);
      $(".mnttStart").text(tstart);
    };
    $(".col_right_row .btn_prev").on("click", function (evt) {
      if (maintInfoIndex > 0) {
        maintInfoIndex--;
        setMaintPage();
      }
    });
    $(".col_right_row .btn_next").on("click", function (evt) {
      if (maintInfoIndex < maintInfoList.length - 1) {
        maintInfoIndex++;
        setMaintPage();
      }
    });
  }

  setPlaneInfoAndWeather(flt) {
    let atdChn = flt.atdChn; //实际起飞时间（北京时间）
    let ataChn = flt.ataChn; //实际到达时间（北京时间）
    let stdChn = flt.stdChn; //计划出发
    let staChn = flt.staChn; // 中转计划到达
    $(".atd").text(this._trimTime(atdChn));
    $(".ata").text(this._trimTime(ataChn));
    $(".std").text(this._trimTime(stdChn));
    $(".sta").text(this._trimTime(staChn));

    // 到达机场
    let param = {
      airport: flt.arrStn, // 到达机场三字码
    };

    $.ajax({
      type: "post",
      url: "/bi/web/7x2_arp_weather",
      contentType: "application/json",
      dataType: "json",
      async: true,
      data: JSON.stringify(param),
      success: function (response) {
        if (Number(response.errorcode) == 0) {
          weather[flt.arrCity] = response;
          this.setWeather(flt.arrCity + flt.arrStnCn + "机场", 2, response);
        }
      }.bind(this),
      error: function (jqXHR, txtStatus, errorThrown) {},
    });
    // 出发机场
    param = {
      airport: flt.depStn, // 出发机场三字码
    };
    $.ajax({
      type: "post",
      url: "/bi/web/7x2_arp_weather",
      contentType: "application/json",
      dataType: "json",
      async: true,
      data: JSON.stringify(param),
      success: function (response) {
        if (Number(response.errorcode) == 0) {
          weather[flt.depCity] = response;
          this.setWeather(flt.depCity + flt.depStnCn + "机场", 1, response);
        }
      }.bind(this),
      error: function (jqXHR, txtStatus, errorThrown) {},
    });

    var date = new Date();
    var ts = date.getTime() - 3600 * 8 * 1000;
    date.setTime(ts);
    var mm = date.getMonth() + 1;
    var dd = date.getDate();
    var hour = date.getHours();
    var min = date.getMinutes();
    var sec = date.getSeconds();
    if (mm < 10) {
      mm = "0" + mm;
    }
    if (dd < 10) {
      dd = "0" + dd;
    }
    if (hour < 10) {
      hour = "0" + hour;
    }
    if (min < 10) {
      min = "0" + min;
    }
    if (sec < 10) {
      sec = "0" + sec;
    }
    var awosTimeEnd =
      date.getFullYear() +
      "-" +
      mm +
      "-" +
      dd +
      " " +
      hour +
      ":" +
      min +
      ":" +
      sec;
    var ts = date.getTime() - 60 * 10 * 1000; //最新10分钟
    date.setTime(ts);
    mm = date.getMonth() + 1;
    dd = date.getDate();
    var hour = date.getHours();
    var min = date.getMinutes();
    var sec = date.getSeconds();
    if (mm < 10) {
      mm = "0" + mm;
    }
    if (dd < 10) {
      dd = "0" + dd;
    }
    if (hour < 10) {
      hour = "0" + hour;
    }
    if (min < 10) {
      min = "0" + min;
    }
    if (sec < 10) {
      sec = "0" + sec;
    }
    var awosTimeBegin =
      date.getFullYear() +
      "-" +
      mm +
      "-" +
      dd +
      " " +
      hour +
      ":" +
      min +
      ":" +
      sec;
    param = {
      airport: flt.arrStn, // 到达机场三字码
      awosTimeBegin: awosTimeBegin, // UTC时间 yyyy-MM-dd HH:mm:ss
      awosTimeEnd: awosTimeEnd, // UTC时间 yyyy-MM-dd HH:mm:ss
    };
    $.ajax({
      type: "post",
      url: "/bi/web/runway_weather",
      contentType: "application/json",
      dataType: "json",
      async: true,
      data: JSON.stringify(param),
      success: function (response) {
        if (response.data && response.data.length) {
          runaway_data = response.data;
          let i = 0;
          if (scrollInterval) clearInterval(scrollInterval);
          scrollInterval = setInterval(() => {
            i = this._scrollRunawayWeathar(i);
          }, 5000);
        }
      }.bind(this),
      error: function (jqXHR, txtStatus, errorThrown) {},
    });
  }

  setPsr(flt) {
    let param = {
      flightNo: flightNo,
      arrIataId: flt.arrStn,
      depIataId: flt.depStn,
    };
    $.ajax({
      type: "post",
      url: "/bi/web/findPsrStat",
      contentType: "application/json",
      dataType: "json",
      async: true,
      data: JSON.stringify(param),
      success: function (response) {
        if (response.data != undefined) {
          let bookNum = response.data[0].bookNum;
          $("#dzck_bd").text(bookNum);
        }
      },
      error: function (response) {},
    });
  }

  getWfMetrepBiaoZhuns(flt) {
    let date = new Date();
    let mm = date.getMonth() + 1;
    let dd = date.getDate();
    if (mm < 10) {
      mm = "0" + mm;
    }
    if (dd < 10) {
      dd = "0" + dd;
    }
    let stdStart = date.getFullYear() + "-" + mm + "-" + dd + " 00:00:00";
    let stdEnd = date.getFullYear() + "-" + mm + "-" + dd + " 23:59:59";
    var param = {
      cccsList: flt.depStnFourCode,
      updateDateStart: stdStart,
      updateDateEnd: stdEnd,
    };
    $.ajax({
      type: "post",
      url: "/bi/web/findWfMetrepBiaoZhuns",
      contentType: "application/json",
      dataType: "json",
      async: true,
      data: JSON.stringify(param),
      success: function (response) {
        for (let k in response) {
          let obj = response[k];
          if (
            obj.ivisAlarmValue == 2 ||
            obj.irvrAlarmValue == 2 ||
            obj.wxCode == "2" ||
            obj.iyunalarmValue == 2 ||
            obj.ittAlarmValue == 2 ||
            obj.iwindSwitchAlarmValue == 2
          ) {
            //1 表示黄色; 2 表示红色
            let cont = wxCode2Name[obj.wx] ? wxCode2Name[obj.wx] : obj.wx;
            $(".weather_city1 .tsb").text(cont);
          }
        }
      }.bind(this),
      error: function () {},
    });

    param = {
      cccsList: flt.arrStnFourCode,
      updateDateStart: stdStart,
      updateDateEnd: stdEnd,
    };

    $.ajax({
      type: "post",
      url: "/bi/web/findWfMetrepBiaoZhuns",
      contentType: "application/json",
      dataType: "json",
      async: true,
      data: JSON.stringify(param),
      success: function (response) {
        for (let k in response) {
          let obj = response[k];
          if (
            obj.ivisAlarmValue == 2 ||
            obj.irvrAlarmValue == 2 ||
            obj.wxCode == "2" ||
            obj.iyunalarmValue == 2 ||
            obj.ittAlarmValue == 2 ||
            obj.iwindSwitchAlarmValue == 2
          ) {
            //1 表示黄色; 2 表示红色
            let cont = wxCode2Name[obj.wx] ? wxCode2Name[obj.wx] : obj.wx;
            $(".weather_city2 .tsb").text(cont);
          }
        }
      }.bind(this),
      error: function () {},
    });
  }

  setWeather(plane_name, id, response) {
    console.log(response);
    let weatherInfoTxt = response.weatherInfoTxt
      ? response.weatherInfoTxt.replace(/<[^>]+>/g, "")
      : "--";
    let weatherInfo = response.weatherInfo ? response.weatherInfo : "--"; //天气现象
    let temperature = response.temperature
      ? isNaN(response.temperature)
        ? "-"
        : Number(response.temperature) + "℃"
      : "--";
    let visibility = response.visibility
      ? isNaN(response.visibility) || response.visibility == 0
        ? 9999
        : Number(response.visibility)
      : "--"; //能见度
    let windFx = response.windFx ? response.windFx : "--"; //风向
    let windFs = response.windFs
      ? isNaN(response.windFs)
        ? 0
        : Number(response.windFs)
      : "--"; //风速
    let cloudInfoTxt = response.cloudInfoTxt
      ? response.cloudInfoTxt.replace(/&nbsp;/g, "")
      : "--";

    let weather_css = "icon-e600_sunny";
    for (let wtxt in weather_map) {
      if (weatherInfoTxt.indexOf(wtxt) > -1) {
        weather_css = weather_map[wtxt];
      }
    }

    $(".weather_city" + id + " .plane").text(plane_name);
    // 设置天气状况icon
    $(".weather_city" + id + " .weather_ico span").attr("class", weather_css);
    $(".weather_city" + id + " .temperature").text(temperature);
    $(".weather_city" + id + " .condition").text(weatherInfoTxt);
    if (id == 2) $("#weatherInfo").text(weatherInfoTxt);
    $(".weather_city" + id + " .visibility").text(visibility + "m");
    $(".weather_city" + id + " .windFs").text(windFs + "km/h");
    $(".weather_city" + id + " .windFx").text(windFx + "°");
    $(".weather_city" + id + " .cloudInfo").text(
      cloudInfoTxt.substring(0, cloudInfoTxt.indexOf("云") + 1)
    );
  }

  setCabin(acno) {
    let param = {
      acNo: acno,
    };
    $.ajax({
      type: "post",
      url: "/bi/web/getAcAircraftList",
      contentType: "application/json",
      dataType: "json",
      async: true,
      data: JSON.stringify(param),
      success: function (response) {
        let ac = response.data[0].data;
        cabin = ac.cabin; // 座舱布局
        let acType = ac.acType; // 机型
        // 座舱布局图
        cabin = cabin.toUpperCase();
        $(".silosImg").removeClass("CY");
        $(".silosImg").removeClass("Y");
        $(".silosImg").removeClass("w_CY");
        $(".silosImg").removeClass("W_Y");
        if (
          acType.indexOf("787") > -1 ||
          acType.indexOf("767") > -1 ||
          acType.indexOf("777") > -1 ||
          acType.indexOf("330") > -1
        ) {
          if (cabin.indexOf("C") > -1 && cabin.indexOf("Y") > -1) {
            $(".silosImg").addClass("W_CY");
          } else {
            $(".silosImg").addClass("W_Y");
          }
        } else {
          if (cabin.indexOf("C") > -1 && cabin.indexOf("Y") > -1) {
            $(".silosImg").addClass("CY");
          } else {
            $(".silosImg").addClass("Y");
          }
        }
        $(".silosImg .actype").text(acType);
        $(".silosImg .lb").text(cabin);
        $("#actype").text(acType);
        $("#lb").text(cabin);
        $(".mtwKg").text(ac.mtwKg);
        // $('#oil_2').text((ac.mfLb / 1000).toFixed(1) + 't');
      },
      error: function (response) {},
    });
  }

  setCrew() {
    let data = crewData;
    // 机长
    let names = data.captain.replace(/\d+/g, "").split("@");
    for (let i = names.length - 1; i >= 0; i--) {
      if (names[i].indexOf("(") > -1) {
        let aaa = names[i].split("(");
        if (aaa.length > 1) {
          names[i] = aaa[0];
        }
      }
    }
    $(".captain").text(names.join(","));

    // 副驾
    if (data.firstVice1) {
      let names = data.firstVice1.replace(/\d+/g, "").split("@"); // 删除数字
      for (let i = names.length - 1; i >= 0; i--) {
        if (names[i].indexOf("(") > -1) {
          let aaa = names[i].split("(");
          if (aaa.length > 1) {
            names[i] = aaa[0];
          }
        }
      }
      $(".firstVice1").text(names.join(","));
    }
    // 乘务员
    let crew = data.crwStewardInf;
    if (crew) {
      crew = crew.replace(/\d+/g, ""); // 删除数字
      let arr = crew.split("；");
      let names = [];
      for (let i in arr) {
        let t = arr[i];
        let n = t.split(":")[0];
        names.push(n);
      }
      let steward = names.splice(0, 6);
      let steward_all = names;
      $(".crwStewardInf").text(steward.join(",")); //乘务员
    }

    // 安全员
    if (data.safer1) {
      let crew = data.safer1.replace(/\d+/g, ""); // 删除数字
      let safer = crew.split("/");
      safer = safer.splice(0, 2);
      $(".safer1").text(safer.join(",")); //安全员
    }
  }

  setInCount(sortFlt) {
    let param = {
      fltNo: flightNo,
      fltDate: sortFlt.datopChn,
      inOrOutType: "in",
      status: sortFlt.status,
      etaChn: sortFlt.etaChn,
      ataChn: sortFlt.etaChn,
    };
    $.ajax({
      type: "post",
      url: "/bi/web/passengergetconnectpsrdatabyflt",
      contentType: "application/json",
      dataType: "json",
      async: true,
      data: JSON.stringify(param),
      success: function (response) {
        if (response != undefined && response.data.length > 0) {
          let html = ``;
          response.data.forEach((v, i) => {
            let atdChn = flightInfoListObj[v.InOrOutFltNo]
              ? this._trimTime(flightInfoListObj[v.InOrOutFltNo][0].atdChn)
              : "";
            let date = this._trimDate(v.InOrOutFltDate);
            html += `<tr><th>${v.InOrOutCount}</th><th>${date}</th><th>${v.InOrOutFltNo}</th><th>${atdChn}</th><th>${v.InOrOutFromPort}/${v.InOrOutToPort}</th></tr>`;
          });
          $("#outCount").html("");
          $("#outCount").html(html);
          let tableInterval = () => {
            var table = document.getElementById("inCount"); //获得表格
            this._change(table); //执行表格change函数，删除第一行，最后增加一行，类似行滚动
          };
          setInterval(tableInterval, 2000);
        }
      }.bind(this),
      error: function (response) {},
    });
  }

  //转出航班详情
  setOutCount(sortFlt) {
    let param = {
      fltNo: flightNo,
      fltDate: sortFlt.datopChn,
      inOrOutType: "out",
      status: sortFlt.status,
      etaChn: sortFlt.etaChn,
      ataChn: sortFlt.etaChn,
    };
    $.ajax({
      type: "post",
      url: "/bi/web/passengergetconnectpsrdatabyflt",
      contentType: "application/json",
      dataType: "json",
      async: true,
      data: JSON.stringify(param),
      success: function (response) {
        if (response != undefined && response.data.length > 0) {
          let html = ``;
          response.data.forEach((v, i) => {
            let staChn = flightInfoListObj[v.InOrOutFltNo]
              ? this._trimTime(flightInfoListObj[v.InOrOutFltNo][0].staChn)
              : "";
            let date = this._trimDate(v.InOrOutFltDate);
            html += `<tr><th>${v.InOrOutCount}</th><th>${date}</th><th>${v.InOrOutFltNo}</th><th>${staChn}</th><th>${v.InOrOutFromPort}/${v.InOrOutToPort}</th></tr>`;
          });
          $("#outCount").html("");
          $("#outCount").html(html);
          let tableInterval = () => {
            var table = document.getElementById("outCount"); //获得表格
            this._change(table); //执行表格change函数，删除第一行，最后增加一行，类似行滚动
          };
          setInterval(tableInterval, 2000);
        }
      }.bind(this),
      error: function (response) {},
    });
  }

  // 获取航班进出港停机位
  getFltmLegsByPageV2(datop, fltNo, arrIataId, depIataId) {
    var res;
    var param = {
      flightDateUTCFrom: datop,
      flightDateUTCTo: datop,
      fltNo: fltNo,
      arrIataId: arrIataId,
      depIataId: depIataId,
    };
    $.ajax({
      type: "post",
      url: "/bi/spring/flight/getFltmLegsByPageV2",
      contentType: "application/json",
      dataType: "json",
      async: false,
      data: JSON.stringify(param),
      success: function (response) {
        res = response.data;
      },
    });
    return res;
  }

  setFltInfo(flt) {
    if (airportList == undefined) {
      return;
    }
    if (flt.length > 1) {
      $(".t1.city1").text(flt[0].depCity);
      $(".citym").text(flt[0].arrCity);
      $(".t1.city2").text(flt[1].arrCity);
      depAirport = airportList[flt[0].depStn]; //出发机场信息
      middleAirport = airportList[flt[0].arrStn]; // 中转机场信息
      arrAirport = airportList[flt[1].arrStn]; //到达机场信息
      $(".fltsts").removeClass("status1");
      // 航段一
      if (flt[0].status == "ATA") {
        $(".tLB_1 .rowTitle .col_1 span").addClass("tLI_B");
        $(".tLB_1 .rowInfo").addClass("rBL_B");
        $(".tLB_2 .rowTitle .col_1 span").addClass("tLI_G");
        $(".tLB_2 .rowInfo").addClass("rBL_G");
        $(".tLB_3 .rowTitle .col_1 span").addClass("tLI_B");
        $(".tLB_1 .rowInfo").addClass("isHiden");

        let stdChn = flt[1].stdChn; //计划出发
        let etdChn = flt[1].etdChn;
        let atdChn = flt[1].atdChn;
        let tOffChn = flt[1].tOffChn;
        let staChn = flt[1].staChn; //计划到达
        let etaChn = flt[1].etaChn; //预计到达时间（北京时间）
        let tDwnChn = flt[1].tDwnChn; //实际落地时间（北京时间）
        let ataChn = flt[1].ataChn; //实际到港时间（北京时间）

        var fltmLegs = this.getFltmLegsByPageV2(
          flt[1].datop,
          flt[1].flightNo,
          flt[1].arrStn,
          flt[1].depStn
        );
        var depParkPlace = "-";
        if (fltmLegs.length > 0) {
          depParkPlace =
            fltmLegs[0].cnvcOutGate == undefined
              ? "-"
              : fltmLegs[0].cnvcOutGate; //出发停机位
        }
        var arrParkPlace = "-";
        if (fltmLegs.length > 0) {
          arrParkPlace =
            fltmLegs[0].cnvcInGATE == undefined ? "-" : fltmLegs[0].cnvcInGATE; //到达停机位
        }
        console.log("4444444444", fltmLegs, arrParkPlace, depParkPlace);
        $(".stdChnm").text(this._trimTime(stdChn));
        $(".etdChnm").text(this._trimTime(etdChn));
        $(".atdChnm").text(this._trimTime(atdChn));
        $(".tOffChn").text(this._trimTime(tOffChn));
        $(".staChn").text(this._trimTime(staChn));
        $(".etaChn").text(this._trimTime(etaChn));
        $(".tDwnChn").text(this._trimTime(tDwnChn));
        $(".ataChn").text(this._trimTime(ataChn));
        $(".depParkPlacem").text(
          depParkPlace && depParkPlace != "" ? depParkPlace : "--"
        );
        $(".arrParkPlace").text(
          arrParkPlace && arrParkPlace != "" ? arrParkPlace : "--"
        );
        if (flt[1].delay1 != "" && flt[1].dur1 > 0) {
          $(".tLB_2 .info_delay").removeClass("isHiden");
          // $('.delayTxtm').text(flt[1].delay1Txt);
          let delayInfo = flt[1].delay1Txt;
          $(".delayTxtm").text(delayInfo);
          if ($(".delayTxtm").width() > 140) {
            $(".delayTxtm").width(140);
            delayInfo = "<marquee>" + delayInfo + "</marquee>";
            $(".delayTxtm").html(delayInfo);
          }
        }

        $(".leg1").addClass("flightStatus3");
        $(".leg1Status").text(statusMap[flt[0].status]);
        this.setPlaneInfoAndWeather(flt[1]);
        this.getWfMetrepBiaoZhuns(flt[1]);
        this.setPsr(flt[1]);
        this.setOutCount(flt[1]);
        this.setInCount(flt[1]);
      } else if (flt[0].delay1 != "" && flt[0].dur1 > 0) {
        $(".tLB_1 .rowTitle .col_1 span").addClass("tLI_G");
        $(".tLB_1 .rowInfo").addClass("rBL_G");
        $(".tLB_2 .rowTitle .col_1 span").addClass("tLI_B");
        $(".tLB_2 .rowInfo").addClass("rBL_B");
        $(".tLB_3 .rowTitle .col_1 span").addClass("tLI_B");
        $(".tLB_3 .rowInfo").addClass("isHiden");
        $(".tLB_2 .t1").text("计划到港");
        $(".tLB_2 .t2").text("实际落地");
        $(".tLB_2 .t3").text("预计落地");
        $(".tLB_2 .t4").text("到港");
        let stdChn = flt[0].stdChn; //计划出发
        let etdChn = flt[0].etdChn;
        let atdChn = flt[0].atdChn;
        let tOffChn = flt[0].tOffChn;
        let staChn = flt[0].staChn; //计划到达
        let etaChn = flt[0].etaChn; //预计到达时间（北京时间）
        let tDwnChn = flt[0].tDwnChn; //实际落地时间（北京时间）
        let ataChn = flt[0].ataChn; //实际到港时间（北京时间）
        var fltmLegs = this.getFltmLegsByPageV2(
          flt[0].datop,
          flt[0].flightNo,
          flt[0].arrStn,
          flt[0].depStn
        );
        var depParkPlace = "-";
        if (fltmLegs.length > 0) {
          depParkPlace =
            fltmLegs[0].cnvcOutGate == undefined
              ? "-"
              : fltmLegs[0].cnvcOutGate; //出发停机位
        }
        var arrParkPlace = "-";
        if (fltmLegs.length > 0) {
          arrParkPlace =
            fltmLegs[0].cnvcInGATE == undefined ? "-" : fltmLegs[0].cnvcInGATE; //到达停机位
        }
        console.log("33333333333", fltmLegs, arrParkPlace, depParkPlace);
        $(".stdChn").text(this._trimTime(stdChn));
        $(".etdChn").text(this._trimTime(etdChn));
        $(".atdChn").text(this._trimTime(atdChn));
        $(".tOffChn").text(this._trimTime(tOffChn));
        $(".staChnm").text(this._trimTime(staChn));
        $(".etaChnm").text(this._trimTime(etaChn));
        $(".tDwnChnm").text(this._trimTime(tDwnChn));
        $(".ataChnm").text(this._trimTime(ataChn));
        $(".depParkPlace").text(
          depParkPlace && depParkPlace != "" ? depParkPlace : "--"
        );
        $(".arrParkPlacem").text(
          arrParkPlace && arrParkPlace != "" ? arrParkPlace : "--"
        );
        $(".tLB_1 .info_delay").removeClass("isHiden");
        // $('.delayTxt').text(flt[0].delay1Txt);
        let delayInfo = flt[1].delay1Txt;
        $(".delayTxt").text(delayInfo);
        if ($(".delayTxt").width() > 140) {
          $(".delayTxt").width(140);
          delayInfo = "<marquee>" + delayInfo + "</marquee>";
          $(".delayTxt").html(delayInfo);
        }

        $(".leg1").addClass("flightStatus2");
        $(".leg1Status").text("晚点");
        // $('.fltsts').hide();
        $(".fltsts").addClass("status2");
        $(".fltsts").text("晚点");
        this.setPlaneInfoAndWeather(flt[0]);
        this.getWfMetrepBiaoZhuns(flt[0]);
        this.setPsr(flt[0]);
        this.setOutCount(flt[0]);
        this.setInCount(flt[0]);
      } else if (flt[0].status != "ATA") {
        $(".tLB_1 .rowTitle .col_1 span").addClass("tLI_G");
        $(".tLB_1 .rowInfo").addClass("rBL_G");
        $(".tLB_2 .rowTitle .col_1 span").addClass("tLI_B");
        $(".tLB_2 .rowInfo").addClass("rBL_B");
        $(".tLB_3 .rowTitle .col_1 span").addClass("tLI_B");
        $(".tLB_3 .rowInfo").addClass("isHiden");
        $(".tLB_2 .t1").text("计划到港");
        $(".tLB_2 .t2").text("实际落地");
        $(".tLB_2 .t3").text("预计落地");
        $(".tLB_2 .t4").text("到港");
        let stdChn = flt[0].stdChn; //计划出发
        let etdChn = flt[0].etdChn;
        let atdChn = flt[0].atdChn;
        let tOffChn = flt[0].tOffChn;
        let staChn = flt[0].staChn; //计划到达
        let etaChn = flt[0].etaChn; //预计到达时间（北京时间）
        let tDwnChn = flt[0].tDwnChn; //实际落地时间（北京时间）
        let ataChn = flt[0].ataChn; //实际到港时间（北京时间）
        var fltmLegs = this.getFltmLegsByPageV2(
          flt[0].datop,
          flt[0].flightNo,
          flt[0].arrStn,
          flt[0].depStn
        );
        var depParkPlace = "-";
        if (fltmLegs.length > 0) {
          depParkPlace =
            fltmLegs[0].cnvcOutGate == undefined
              ? "-"
              : fltmLegs[0].cnvcOutGate; //出发停机位
        }
        var arrParkPlace = "-";
        if (fltmLegs.length > 0) {
          arrParkPlace =
            fltmLegs[0].cnvcInGATE == undefined ? "-" : fltmLegs[0].cnvcInGATE; //到达停机位
        }
        console.log("222222222", fltmLegs, arrParkPlace, depParkPlace);
        $(".stdChn").text(this._trimTime(stdChn));
        $(".etdChn").text(this._trimTime(etdChn));
        $(".atdChn").text(this._trimTime(atdChn));
        $(".tOffChn").text(this._trimTime(tOffChn));
        $(".staChnm").text(this._trimTime(staChn));
        $(".etaChnm").text(this._trimTime(etaChn));
        $(".tDwnChnm").text(this._trimTime(tDwnChn));
        $(".ataChnm").text(this._trimTime(ataChn));
        $(".depParkPlace").text(
          depParkPlace && depParkPlace != "" ? depParkPlace : "--"
        );
        $(".arrParkPlacem").text(
          arrParkPlace && arrParkPlace != "" ? arrParkPlace : "--"
        );

        $(".fltsts").addClass("status1");
        // $('.fltsts').hide();
        flt[0].status == "SCH"
          ? $(".leg1").addClass("flightStatus3")
          : $(".leg1").addClass("flightStatus1");
        $(".leg1Status").text(statusMap[flt[0].status]);
        $(".fltsts").text(statusMap[flt[0].status]);
        this.setPlaneInfoAndWeather(flt[0]);
        this.getWfMetrepBiaoZhuns(flt[0]);
        this.setPsr(flt[0]);
        this.setOutCount(flt[0]);
        this.setInCount(flt[0]);
      }
      // 航段二
      if (flt[1].status == "ATA") {
        $(".leg2").addClass("flightStatus3");
        $(".leg2Status").text(statusMap[flt[1].status]);
        $(".fltsts").hide();
      } else if (flt[1].delay1 != "" && flt[1].dur1 > 0) {
        $(".leg2").addClass("flightStatus2");
        $(".leg2Status").text("晚点");
        // $('.fltsts').hide();
        if (flt[0].status == "ATA") {
          $(".fltsts").addClass("status2");
          $(".fltsts").text("晚点");
        }
      } else if (flt[1].status != "ATA") {
        flt[1].status == "SCH"
          ? $(".leg2").addClass("flightStatus3")
          : $(".leg2").addClass("flightStatus1");
        $(".leg2Status").text(statusMap[flt[1].status]);
        // $('.fltsts').hide();
        if (flt[0].status == "ATA") {
          $(".fltsts").addClass("status1");
          $(".fltsts").text(statusMap[flt[1].status]);
        }
      }
      let d_time1 = parserDate(flt[0].etdChn);
      let a_time1 = parserDate(flt[0].etaChn);
      let atdChn1 = parserDate(flt[0].atdChn); //第一段实际起飞时间
      let ataChn1 = parserDate(flt[0].ataChn); //第一段实际到达时间
      let staChn1 = parserDate(flt[0].staChn); //第一段计划到港时间

      let d_time2 = parserDate(flt[1].etdChn);
      let a_time2 = parserDate(flt[1].etaChn);
      let atdChn2 = parserDate(flt[1].atdChn); //第二段实际起飞时间
      let ataChn2 = parserDate(flt[1].ataChn); //第二段实际到达时间
      let stdChn2 = parserDate(flt[1].stdChn); //第二段计划离港时间

      let tlmsec = stdChn2.getTime() - staChn1.getTime();
      let tlmin = Math.round(tlmsec / (60 * 1000));
      let tltm = Math.floor(tlmin / 60) + "小时";
      if (tlmin % 60 > 0) {
        tltm = tltm + (tlmin % 60) + "分";
      }
      $(".jingtingtime").text(tltm);

      let msec =
        a_time1.getTime() -
        d_time1.getTime() +
        (a_time2.getTime() - d_time2.getTime());
      let min = Math.round(msec / (60 * 1000));
      let tm = Math.floor(min / 60) + "小时";
      if (min % 60 > 0) {
        tm = tm + (min % 60) + "分";
      }
      $(".fly_time").text(tm);
      if (
        flt[0].status == "ATA" ||
        flt[0].status == "ARR" ||
        flt[0].status == "DNR"
      ) {
        if (flt[1].status == "DEP" || flt[1].status == "ATD") {
          $(".ariline_min").text(
            min -
              Math.round(
                ((new Date() > atdChn2 ? new Date() - atdChn2 : 0) +
                  (ataChn1 - atdChn1)) /
                  (60 * 1000)
              )
          );
        } else if (
          flt[1].status == "ATA" ||
          flt[1].status == "ARR" ||
          flt[1].status == "DNR"
        ) {
          $(".ariline_min").text(
            min -
              Math.round(
                (ataChn2 - atdChn2 + (ataChn1 - atdChn1)) / (60 * 1000)
              )
          );
        } else {
          $(".ariline_min").text(
            min - Math.round((ataChn1 - atdChn1) / (60 * 1000))
          );
        }
      } else if (flt[0].status == "DEP" || flt[0].status == "ATD") {
        $(".ariline_min").text(
          min -
            Math.round(
              (new Date() > atdChn1 ? new Date() - atdChn1 : 0) / (60 * 1000)
            )
        );
      }
    }
    if (flt.length != 2) {
      // 起始站
      $(".tLB_1 .rowTitle .col_1 span").addClass("tLI_G");
      $(".tLB_1 .rowInfo").addClass("rBL_G");
      //终点站
      $(".tLB_3 .rowTitle .col_1 span").addClass("tLI_B");

      $(".t1.city1").text(flt[0].depCity);
      $(".tLB_2").addClass("isHiden");
      // $('.citym').hide();
      $(".t1.city2").text(flt[0].arrCity);
      depAirport = airportList[flt[0].depStn]; //出发机场信息
      // middleAirport = airportList[flt[0].arrStn] // 中转机场信息
      arrAirport = airportList[flt[0].arrStn]; //到达机场信息
      $(".fltsts").removeClass("status1");
      $(".leg1").hide();
      $(".leg2").hide();
      $(".jingting").hide();
      // 航段一
      if (flt[0].delay1 != "" && flt[0].dur1 > 0) {
        $(".fltsts").addClass("status2");
        $(".fltsts").text("晚点");
      } else {
        $(".fltsts").addClass("status1");
        $(".fltsts").text(statusMap[flt[0].status]);
      }
      // 飞行时长
      let d_time = parserDate(flt[0].etdChn);
      let a_time = parserDate(flt[0].etaChn);
      let atdChn1 = parserDate(flt[0].atdChn); //第一段实际起飞时间
      let ataChn1 = parserDate(flt[0].ataChn); //第一段实际到达时间
      let msec = a_time.getTime() - d_time.getTime();
      let min = Math.round(msec / (60 * 1000));
      let tm = Math.floor(min / 60) + "小时";
      if (min % 60 > 0) {
        tm = tm + (min % 60) + "分";
      }
      $(".fly_time").text(tm);
      if (
        flt[0].status == "ATA" ||
        flt[0].status == "ARR" ||
        flt[0].status == "DNR"
      ) {
        $(".ariline_min").text(
          min - Math.round((ataChn1 - atdChn1) / (60 * 1000))
        );
      } else if (flt[0].status == "DEP" || flt[0].status == "ATD") {
        $(".ariline_min").text(
          min -
            Math.round(
              (new Date() > atdChn1 ? new Date() - atdChn1 : 0) / (60 * 1000)
            )
        );
      }
      let stdChn = flt[0].stdChn; //计划出发
      let etdChn = flt[0].etdChn;
      let atdChn = flt[0].atdChn;
      let tOffChn = flt[0].tOffChn;
      let staChn = flt[0].staChn; //计划到达
      let etaChn = flt[0].etaChn; //预计到达时间（北京时间）
      let tDwnChn = flt[0].tDwnChn; //实际落地时间（北京时间）
      let ataChn = flt[0].ataChn; //实际到港时间（北京时间）
      var fltmLegs = this.getFltmLegsByPageV2(
        flt[0].datop,
        flt[0].flightNo,
        flt[0].arrStn,
        flt[0].depStn
      );
      var depParkPlace = "-";
      if (fltmLegs.length > 0) {
        depParkPlace =
          fltmLegs[0].cnvcOutGate == undefined ? "-" : fltmLegs[0].cnvcOutGate; //出发停机位
      }
      var arrParkPlace = "-";
      if (fltmLegs.length > 0) {
        arrParkPlace =
          fltmLegs[0].cnvcInGATE == undefined ? "-" : fltmLegs[0].cnvcInGATE; //到达停机位
      }
      console.log("1111111111", fltmLegs, arrParkPlace, depParkPlace);
      $(".stdChn").text(this._trimTime(stdChn));
      $(".etdChn").text(this._trimTime(etdChn));
      $(".atdChn").text(this._trimTime(atdChn));
      $(".tOffChn").text(this._trimTime(tOffChn));
      $(".staChn").text(this._trimTime(staChn));
      $(".etaChn").text(this._trimTime(etaChn));
      $(".tDwnChn").text(this._trimTime(tDwnChn));
      $(".ataChn").text(this._trimTime(ataChn));
      $(".depParkPlace").text(
        depParkPlace && depParkPlace != "" ? depParkPlace : "--"
      );
      $(".arrParkPlace").text(
        arrParkPlace && arrParkPlace != "" ? arrParkPlace : "--"
      );

      this.setPlaneInfoAndWeather(flt[0]);
      this.getWfMetrepBiaoZhuns(flt[0]);
      this.setPsr(flt[0]);
      this.setOutCount(flt[0]);
      this.setInCount(flt[0]);
    }
  }

  setMapData() {
    return new Promise((resolve, reject) => {
      let series = [];
      // 飞机位置
      let planes = planeLocationList;
      let flightList = flightInfoListObj;
      let len = planes.length;
      if (len == 0) {
        reject("fail");
      }
      let acdat;
      for (let i = 0; i < len; i++) {
        var ac = planes[i];
        if (ac.fltno == flightNo) {
          acdat = ac;
          if (!isNaN(ac.oil)) {
            curOil = Math.round(ac.oil);
            $("#oil_rt1").text((Math.round(ac.oil) / 1000).toFixed(1) + "t");
          } else {
            curOil = 0;
            $("#oil_rt1").text("--");
          }
        }
      }
      let flt;
      let depAirport = ""; //出发机场信息
      let middleAirport = "";
      let arrAirport = ""; //到达机场信息
      if (flightList[flightNo].length > 1) {
        if (flightList[flightNo][0].status == "ATA") {
          flt = flightList[flightNo][1];
        } else {
          flt = flightList[flightNo][0];
        }
        depAirport = airportList[flightList[flightNo][0].depStn]; //出发机场信息
        middleAirport = airportList[flightList[flightNo][1].depStn];
        arrAirport = airportList[flightList[flightNo][1].arrStn]; //到达机场信息
      } else {
        flt = flightList[flightNo][0];
        depAirport = airportList[flightList[flightNo][0].depStn]; //出发机场信息
        arrAirport = airportList[flightList[flightNo][0].arrStn]; //到达机场信息
      }

      // 飞行轨迹
      let data = [];
      for (var i = pointlist.length - 1; i >= 0; i--) {
        var d = pointlist[i];
        d.UTC = d.UTC.replace(/\D/g, "");
      }

      // 国内航线
      if (flt.fltType != "I") {
        // 删除相同时间的坐标点
        let idx = 0;
        for (let i = 0; i < pointlist.length; i++) {
          pointlist[i].idx = idx;
          idx++;
          let d = pointlist[i];
          for (let j = pointlist.length - 1; j >= 0; j--) {
            let d2 = pointlist[j];
            if (d.UTC == d2.UTC && d.idx != d2.idx) {
              pointlist.splice(j, 1);
            }
          }
        }

        // 国内航线删除坐标带E，W，N，S的坐标，避免两个MQ出现航路偏差
        for (let i = pointlist.length - 1; i >= 0; i--) {
          let d = pointlist[i];
          if (isNaN(d.LAT) || isNaN(d.LON)) {
            pointlist.splice(i, 1);
          }
        }
      }

      // 计划出发时间
      let date = new Date();
      let stdChnTM = parserDate(flt.stdChn); // 计划出发时间
      let ts_dep = stdChnTM.getTime() - 8 * 60 * 60 * 1000;
      date.setTime(ts_dep);
      let mm = date.getMonth() + 1;
      let dd = date.getDate();
      let h = date.getHours();
      let m = date.getMinutes();
      let s = date.getSeconds();
      if (mm < 10) {
        mm = "0" + mm;
      }
      if (dd < 10) {
        dd = "0" + dd;
      }
      if (h < 10) {
        h = "0" + h;
      }
      if (m < 10) {
        m = "0" + m;
      }
      if (s < 10) {
        s = "0" + s;
      }
      let utc_dep =
        date.getFullYear() + "" + mm + "" + dd + "" + h + "" + m + "" + s; // 现在时间

      date = new Date();
      let ts = date.getTime() - 86400000;
      let ts_now = date.getTime() - 8 * 60 * 60 * 1000;
      date.setTime(ts);
      mm = date.getMonth() + 1;
      dd = date.getDate();
      if (mm < 10) {
        mm = "0" + mm;
      }
      if (dd < 10) {
        dd = "0" + dd;
      }
      let utc_time = date.getFullYear() + "" + mm + "" + dd + "210000"; // UTC 昨天 21点，北京时间今天早上5点
      date.setTime(ts_now);
      mm = date.getMonth() + 1;
      dd = date.getDate();
      h = date.getHours();
      m = date.getMinutes();
      s = date.getSeconds();
      if (mm < 10) {
        mm = "0" + mm;
      }
      if (dd < 10) {
        dd = "0" + dd;
      }
      if (h < 10) {
        h = "0" + h;
      }
      if (m < 10) {
        m = "0" + m;
      }
      if (s < 10) {
        s = "0" + s;
      }
      let utc_now =
        date.getFullYear() + "" + mm + "" + dd + "" + h + "" + m + "" + s; // 现在时间

      for (let i = pointlist.length - 1; i >= 0; i--) {
        // 删除昨天的航迹坐标
        var d = pointlist[i];
        if (d.UTC < utc_time) {
          pointlist.splice(i, 1);
        }
      }

      pointlist.sort(function (a, b) {
        return Number(b.UTC) - Number(a.UTC);
      });

      if (pointlist && pointlist.length > 1) {
        len = pointlist.length;
        for (let i = 1; i < len; i++) {
          let p1 = pointlist[i - 1];
          let p2 = pointlist[i];
          let lon = formatLonLat(p1.LON);
          let lat = formatLonLat(p1.LAT);
          let lon2 = formatLonLat(p2.LON);
          let lat2 = formatLonLat(p2.LAT);

          data.push({
            fromName: "",
            toName: "",
            coords: [
              [lon, lat],
              [lon2, lat2],
            ],
          });
        }
        series.push({
          name: "lines",
          type: "lines",
          coordinateSystem: "geo",
          zlevel: 1,
          silent: false, //不响应鼠标点击或事件
          polyline: true, //支持多点连线
          effect: {
            show: false, //关闭特效
          },
          tooltip: {
            show: false,
          },
          lineStyle: {
            normal: {
              color: "white",
              width: 1.5,
              opacity: 0.9,
              curveness: 0,
              // type: 'dashed'
            },
          },
          label: {
            normal: {
              show: false,
            },
          },
          data: data,
        });
      }

      ////// 设置小飞机图标。 城市位置
      let symbol = "";

      if (flt.status == "DEP" && flt.delay1 == "") {
        symbol = "image://img/flight.legend_1.png";
      } else {
        symbol = "image://img/flight.legend_2.png"; // 延误航班
      }

      if (pointlist.length > 1) {
        var ac_lon = formatLonLat(pointlist[0].LON);
        var ac_lat = formatLonLat(pointlist[0].LAT);
        let ac_lon1 = formatLonLat(pointlist[1].LON);
        let ac_lat1 = formatLonLat(pointlist[1].LAT);
        var vec = getGeoAngle(ac_lat, ac_lon, ac_lat1, ac_lon1);
      } else {
        var ac_lon = acdat.lon;
        var ac_lat = acdat.lat;
        var vec = acdat.vec;
      }

      if (flightList[flightNo].length > 1) {
        series.push({
          name: "scatter",
          type: "effectScatter",
          showEffectOn: "render",
          coordinateSystem: "geo",
          zlevel: 2,

          data: [
            {
              name: flightList[flightNo][0].depCity,
              isAirline: true,
              isCity: true,
              cityType: "dep",
              value: [depAirport.longitude, depAirport.latitude],
              symbol: "circle",
              symbolSize: 7,
              itemStyle: {
                normal: {
                  color: "#3d7dd0",
                  borderColor: "#ffffff",
                  borderWidth: 2,
                },
              },
              label: {
                normal: {
                  show: true,
                  formatter: "{b}",
                  offset: [0, -16],
                  textStyle: {
                    fontSize: 12,
                    color: "#FFFFFF",
                  },
                },
              },
            },
            {
              name: flightList[flightNo][0].arrCity,
              isAirline: true,
              isCity: true,
              cityType: "dep",
              value: [middleAirport.longitude, middleAirport.latitude],
              symbol: "circle",
              symbolSize: 7,
              itemStyle: {
                normal: {
                  color: "#3d7dd0",
                  borderColor: "#ffffff",
                  borderWidth: 2,
                },
              },
              label: {
                normal: {
                  show: true,
                  formatter: "{b}",
                  offset: [0, -16],
                  textStyle: {
                    fontSize: 12,
                    color: "#FFFFFF",
                  },
                },
              },
            },
            {
              name: flightList[flightNo][1].arrCity,
              isAirline: true,
              isCity: true,
              cityType: "arr",
              value: [arrAirport.longitude, arrAirport.latitude],
              symbol: "circle",
              symbolSize: 7,
              itemStyle: {
                normal: {
                  color: "#3d7dd0",
                  borderColor: "#ffffff",
                  borderWidth: 2,
                },
              },
              label: {
                normal: {
                  show: true,
                  formatter: "{b}",
                  offset: [0, -16],
                  textStyle: {
                    fontSize: 12,
                    color: "#FFFFFF",
                  },
                },
              },
            },
            {
              name: acdat.fltno,
              acno: acdat.acno,
              oil: acdat.oil,
              flt: flt,
              isAirline: true,
              value: [ac_lon, ac_lat],
              symbol: symbol,
              symbolSize: 22,
              label: {
                normal: {
                  show: false,
                },
              },
              symbolRotate: -vec + 90, //-acdat.vec+90, //航向   正北是0°，顺时针，0到360° // 因为图片机头指向右，所以需要逆时针转90度
            },
          ],
        });
      } else {
        series.push({
          name: "scatter",
          type: "effectScatter",
          coordinateSystem: "geo",
          showEffectOn: "render",
          zlevel: 2,
          data: [
            {
              name: flt.depCity,
              isAirline: true,
              isCity: true,
              cityType: "dep",
              value: [depAirport.longitude, depAirport.latitude],
              symbol: "circle",
              symbolSize: 7,
              itemStyle: {
                normal: {
                  color: "#3d7dd0",
                  borderColor: "#ffffff",
                  borderWidth: 2,
                },
              },
              label: {
                normal: {
                  show: true,
                  formatter: "{b}",
                  offset: [0, -16],
                  textStyle: {
                    fontSize: 12,
                    color: "#FFFFFF",
                  },
                },
              },
            },
            {
              name: flt.arrCity,
              isAirline: true,
              isCity: true,
              cityType: "arr",
              value: [arrAirport.longitude, arrAirport.latitude],
              symbol: "circle",
              symbolSize: 7,
              itemStyle: {
                normal: {
                  color: "#3d7dd0",
                  borderColor: "#ffffff",
                  borderWidth: 2,
                },
              },
              label: {
                normal: {
                  show: true,
                  formatter: "{b}",
                  offset: [0, -16],
                  textStyle: {
                    fontSize: 12,
                    color: "#FFFFFF",
                  },
                },
              },
            },
            {
              name: acdat.fltno,
              acno: acdat.acno,
              oil: acdat.oil,
              flt: flt,
              isAirline: true,
              value: [ac_lon, ac_lat],
              symbol: symbol,
              symbolSize: 12,
              label: {
                normal: {
                  show: false,
                },
              },
              symbolRotate: -vec + 90, //-acdat.vec+90, //航向   正北是0°，顺时针，0到360° // 因为图片机头指向右，所以需要逆时针转90度
            },
          ],
        });
      }
      option = mapBoxEchart.getOption();
      option.series = series;
      mapBoxEchart.setOption(option);
      // 航线里程
      let dis = this._getDistance(
        depAirport.latitude,
        depAirport.longitude,
        arrAirport.latitude,
        arrAirport.longitude
      );
      $(".ariline_dis").text(Math.round(dis));
      resolve("success");
    });
  }

  _addEvent() {
    // $('.rowTitle').on('click',function(){
    //     // $(this).siblings('.rowInfo').toggle('.isHiden');
    //     var domRowInfo= $(this).siblings('.rowInfo');
    //     domRowInfo.hasClass('isHiden') ? domRowInfo.removeClass('isHiden') : domRowInfo.addClass('isHiden');
    // })
  }

  _change(table) {
    var row = table.insertRow(table.rows.length); //在table的最后增加一行,table.rows.length是表格的总行数
    for (let j = 0; j < table.rows[0].cells.length; j++) {
      //循环第一行的所有单元格的数据，让其加到最后新加的一行数据中（注意下标是从0开始的）
      var cell = row.insertCell(j); //给新插入的行中添加单元格
      cell.height = "24px"; //一个单元格的高度，跟css样式中的line-height高度一样
      cell.innerHTML = table.rows[0].cells[j].innerHTML; //设置新单元格的内容，这个根据需要，自己设置
    }
    table.deleteRow(0); //删除table的第一行
  }

  _initMap() {
    mapBoxEchart.setOption(option);
  }

  _initTime() {
    var date = new Date();
    var mm = date.getMonth() + 1;
    var dd = date.getDate();
    if (mm < 10) {
      mm = "0" + mm;
    }
    if (dd < 10) {
      dd = "0" + dd;
    }
    stdStart = date.getFullYear() + "-" + mm + "-" + dd + " 00:00:00";
    stdEnd = date.getFullYear() + "-" + mm + "-" + dd + " 23:59:59";
    stdEndUtcTime = date.getFullYear() + "-" + mm + "-" + dd + " 15:59:59";
    var yesterday_ts = date.getTime() - 86400000;
    date.setTime(yesterday_ts);
    mm = date.getMonth() + 1;
    dd = date.getDate();
    if (mm < 10) {
      mm = "0" + mm;
    }
    if (dd < 10) {
      dd = "0" + dd;
    }
    stdStartUtcTime = date.getFullYear() + "-" + mm + "-" + dd + " 16:00:00";

    date = new Date();
    var next_ts = date.getTime() + 86400000 * 7;
    date.setTime(next_ts);
    mm = date.getMonth() + 1;
    dd = date.getDate();
    if (mm < 10) {
      mm = "0" + mm;
    }
    if (dd < 10) {
      dd = "0" + dd;
    }
    stdEnd7 = date.getFullYear() + "-" + mm + "-" + dd + " 23:59:59";
  }

  _scrollRunawayWeathar(i) {
    console.log("i = " + i);
    if (runaway_data.length > 0 && i < runaway_data.length) {
      let item = runaway_data[i];
      Object.keys(item).forEach(function (k, index) {
        // console.log("runaway["+i+"]", k, item[k]);
        $("#" + k).html(item[k]);
      });
      i++;
    } else {
      i = 0;
    }
    return i;
  }

  _getQueryString(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]);
    return "";
  }

  _getDistance(lat1, lng1, lat2, lng2) {
    let radLat1 = (lat1 * Math.PI) / 180.0;
    let radLat2 = (lat2 * Math.PI) / 180.0;
    let a = radLat1 - radLat2;
    let b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180.0;
    let s =
      2 *
      Math.asin(
        Math.sqrt(
          Math.pow(Math.sin(a / 2), 2) +
            Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)
        )
      );
    s = s * 6378.137; // EARTH_RADIUS;
    s = Math.round(s * 10000) / 10000;
    return s;
  }

  _trimTime(timestr) {
    let arr = timestr.split(" ");
    let arr2 = arr[1].split(":");
    return arr2[0] + ":" + arr2[1];
  }
  _trimDate(timestr) {
    let arr = timestr.split(" ");
    let arr2 = arr[0].split("-");
    return arr2[1] + "-" + arr2[2];
  }

  _hideLoad() {
    if ($("#loading_msk").length > 0) {
      hideLoading();
      this._hideLoad();
    }
  }
}

let fl = new flight();
$.when(getCompany()).done(() => {
  fl.loadAll();
});
