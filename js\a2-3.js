showLoading();


// ------------------------------------------------------------

$('#mainframe').attr('src', 'map/map-a2-3.html');

// ------------------------------------------------------------




var current_company_code = 'HU'; // 只做股份一家公司的   //parent_company;
var current_company_id = 100100; // 只做股份一家公司的
$('#companycombo').hide(); // 不显示成员公司



var all_city; // 城市
var all_lines_d; // 国内 航线
var all_lines_i; // 国际 航线
var total_mat; // 总销售额
var total_vol; // 总销量
var total_mat_d; // 国内 总销售额
var total_mat_i; // 国际 总销售额
var total_vol_d; // 国内 总销量
var total_vol_i; // 国际 总销量

var total_tickets = 0;

var realtimeticket_routs = [];
var realtimeticket_pageNo = 0;
var realtimeticket_pageSize = 0;
var updateInterval = 6;//3秒


var cblist = [
    {'label':'显示全部', 'data':0},
    {'label':'TOP10的国内城市', 'data':1},
    {'label':'TOP10的国内航线', 'data':2},
    {'label':'TOP10的国际航线', 'data':3},
    {'label':'全部国际航线', 'data':4},
];
createComboBox('legend_cb', cblist, 141, 240, switchLegend, 0);

function selectLegendCb(selectedIndex){
    var id = 'legend_cb';
    $('#'+id + ' .combobox_label').text(cblist[selectedIndex].label);
    $('#'+id).attr('data', cblist[selectedIndex].data);
}


// 获取24小时售票情况，总量
function getTodayTicketSale(){

    if(companylist.length == 0){
        setTimeout(getTodayTicketSale,0);
        return;
    }

    // ------------------------------------------------------------------------
    // 24小时售票情况
    // ------------------------------------------------------------------------
    var param = {
        company: current_company_id,
        "OPTIMIZE": 1
    }

    $.ajax({
        type: 'post',
        url:"/bi/web/todayticketsale",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            setBlockLKpi(response);
            setBlockRKpi(response);

            hideLoading();
            
        },
        error:function() {
        }
    });


    // ------------------------------------------------------------------------
    // 7日售票情况
    // ------------------------------------------------------------------------
    var param = {
        company: current_company_id,
        "OPTIMIZE": 1
    }

    $.ajax({
        type: 'post',
        url:"/bi/web/7dayticketsale",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            setBlockR2Kpi(response);
            
        },
        error:function() {
        }
    });


    // ------------------------------------------------------------------------
    // 渠道销售情况
    // ------------------------------------------------------------------------
    var chaldata = [];
    
    var param = {
        company: current_company_id,
        "OPTIMIZE": 1
    }

    $.ajax({
        type: 'post',
        url:"/bi/web/todaychalsale",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            var chalkeyv = {};

            for(var k in response){
                var dat = response[k];
                if(dat.CHAL_NAME){

                    if(chalkeyv[dat.CHAL_NAME] == undefined){
                        chalkeyv[dat.CHAL_NAME] = {};
                        chalkeyv[dat.CHAL_NAME].SAL_AMT = 0;
                        chalkeyv[dat.CHAL_NAME].SAL_VOL = 0;
                    }
                    if(!isNaN(dat.SAL_AMT)){
                        chalkeyv[dat.CHAL_NAME].SAL_AMT += Number(dat.SAL_AMT);
                    }
                    if(!isNaN(dat.SAL_VOL)){
                        chalkeyv[dat.CHAL_NAME].SAL_VOL += Number(dat.SAL_VOL);
                    }
                    
                }
            }

            for(var chalname in chalkeyv){
                var d = chalkeyv[chalname];
                var obj = {
                    'CHAL_NAME': chalname,
                    'SAL_AMT': d.SAL_AMT,
                    'SAL_VOL': d.SAL_VOL,
                };
                chaldata.push(obj);
            }

            chaldata.sort(function(a,b){
                return b.SAL_VOL - a.SAL_VOL
            });

            //分渠道TOP5客票统计
            setTop5Chal();

            
            
        },
        error:function() {
        }
    });


    function setTop5Chal(){

        if(total_tickets == 0){
            setTimeout(setTop5Chal,10);
            return;
        }

        var tr1 = '<td class="name">排名</td>';
        var tr2 = '<td class="name">渠道</td>';
        var tr3 = '<td class="name">销售量(张)</td>';
        var tr4 = '<td class="name">占比</td>';
        var tr5 = '<td class="name">销售额(万元)</td>';

        var len = Math.min(chaldata.length, 5);
        for(var i=0; i<len; i++){
            var d = chaldata[i];
            var name = d.CHAL_NAME;
            var per = Math.round(Number(d.SAL_VOL)/total_tickets * 1000)/10;
            var sal = Math.round(d.SAL_AMT/10000);
            var cnt = formatCurrency(d.SAL_VOL,0);
            tr1 += '<td class="rank">'+(i+1)+'</td>';
            tr2 += '<td class="chal">'+name+'</td>';
            tr3 += '<td class="cnt">'+cnt+'</td>';
            tr4 += '<td class="per">'+per+'%</td>';
            tr5 += '<td class="sal">'+sal+'</td>';
            
        }

        $('#chal_tr1').html(tr1);
        $('#chal_tr2').html(tr2);
        $('#chal_tr3').html(tr3);
        $('#chal_tr4').html(tr4);
        $('#chal_tr5').html(tr5);

    }


    // ------------------------------------------------------------------------
    // 地图 航线
    // ------------------------------------------------------------------------
    var param = {
        Comp: current_company_id,
        "OPTIMIZE": 1
    }

    $.ajax({
        type: 'post',
        url:"/bi/web/todayticketrout",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            all_city = response.airport; // 城市
            all_lines_d = response.cityD; // 国内 航线
            all_lines_i = response.cityI; // 国际 航线
            total_mat = Math.round(response.mat); // 总销售额
            total_vol = Math.round(response.vol); // 总销量

            total_mat_d = 0; // 国内 总销售额
            total_mat_i = 0; // 国际 总销售额
            total_vol_d = 0; // 国内 总销量
            total_vol_i = 0; // 国际 总销量

            for(var i=all_lines_d.length-1; i>=0; i--){
                var obj = all_lines_d[i];
                if(!isNaN(obj.vol)){
                    total_vol_d += Number(obj.vol);
                }
                if(!isNaN(obj.mat)){
                    total_mat_d += Number(obj.mat);
                }
            }

            for(var i=all_lines_i.length-1; i>=0; i--){
                var obj = all_lines_i[i];
                if(!isNaN(obj.vol)){
                    total_vol_i += Number(obj.vol);
                }
                if(!isNaN(obj.mat)){
                    total_mat_i += Number(obj.mat);
                }
            }


            all_city.sort(function(a, b){
                return b.vol-a.vol
            });

            all_lines_d.sort(function(a, b){
                return b.vol-a.vol
            });

            all_lines_i.sort(function(a, b){
                return b.vol-a.vol
            });
            
            setAirlines();

            //

            //Top10的国内城市 =================================

            var tr1 = '<td class="name">排名</td>';
            var tr2 = '<td class="name">城市</td>';
            var tr3 = '<td class="name">销售量(张)<br>国内占比</td>';
            var tr4 = '<td class="name">销售额(万元)<br>国内占比</td>';

            var len = all_city.length;
            var cnt = 0;
            for(var i=0; i<len; i++){
                var city = all_city[i];
                if(city.mat > 0){
                    var arp = airportList[city.DEP];

                    tr1 += '<td class="rank">'+(i+1)+'</td>';
                    tr2 += '<td class="cnt">'+arp.city_name+'</td>';
                    tr3 += '<td class="cnt">'+Math.round(city.vol)+'<br>'+(Math.round(city.vol/total_vol_d*1000)/10)+'%' + '</td>';
                    tr4 += '<td class="cnt">'+Math.round(city.mat/1000)/10+'<br>'+(Math.round(city.mat/total_mat_d*1000)/10)+'%' + '</td>';

                    cnt++;
                    if(cnt == 10){
                        break;
                    }
                }
            }

            $('.block_m .tabc2 .tr1').html(tr1);
            $('.block_m .tabc2 .tr2').html(tr2);
            $('.block_m .tabc2 .tr3').html(tr3);
            $('.block_m .tabc2 .tr4').html(tr4);


            //Top10的国内航线 =================================
            
            var tr1 = '<td class="name">排名</td>';
            var tr2 = '<td class="name">出发机场</td>';
            var tr3 = '<td class="name">到达机场</td>';
            var tr4 = '<td class="name">销售量(张)<br>国内占比</td>';
            var tr5 = '<td class="name">销售额(万元)<br>国内占比</td>';

            var len = all_lines_d.length;
            var cnt = 0;
            for(var i=0; i<len; i++){
                var line = all_lines_d[i];
                if(line.mat > 0){
                    var arp1 = airportList[line.DEP];
                    var arp2 = airportList[line.DES];

                    tr1 += '<td class="rank">'+(i+1)+'</td>';
                    tr2 += '<td class="cnt">'+arp1.chn_name+'</td>';
                    tr3 += '<td class="cnt">'+arp2.chn_name+'</td>';
                    tr4 += '<td class="cnt">'+Math.round(line.vol)+'<br>'+(Math.round(line.vol/total_vol_d*1000)/10)+'%' + '</td>';
                    tr5 += '<td class="cnt">'+Math.round(line.mat/1000)/10+'<br>'+(Math.round(line.mat/total_mat_d*1000)/10)+'%' + '</td>';

                    cnt++;
                    if(cnt == 10){
                        break;
                    }
                }
            }

            $('.block_m .tabc3 .tr1').html(tr1);
            $('.block_m .tabc3 .tr2').html(tr2);
            $('.block_m .tabc3 .tr3').html(tr3);
            $('.block_m .tabc3 .tr4').html(tr4);
            $('.block_m .tabc3 .tr5').html(tr5);


            //Top10的国际航线 =================================
            
            var tr1 = '<td class="name">排名</td>';
            var tr2 = '<td class="name">出发机场</td>';
            var tr3 = '<td class="name">到达机场</td>';
            var tr4 = '<td class="name">销售量(张)<br>国际占比</td>';
            var tr5 = '<td class="name">销售额(万元)<br>国际占比</td>';

            var len = all_lines_i.length;
            var cnt = 0;
            for(var i=0; i<len; i++){
                var line = all_lines_i[i];
                if(line.mat > 0){
                    var arp1 = airportList[line.DEP];
                    var arp2 = airportList[line.DES];

                    if(arp1 && arp2){
                        var name1 = arp1.chn_name.replace(/国际机场/g, '');
                        var name2 = arp2.chn_name.replace(/国际机场/g, '');
                        name1 = name1.split('-')[0];
                        name2 = name2.split('-')[0];
                        name1 = name1.split('·')[0];
                        name2 = name2.split('·')[0];

                        tr1 += '<td class="rank">'+(i+1)+'</td>';
                        tr2 += '<td class="cnt">'+name1+'</td>';
                        tr3 += '<td class="cnt">'+name2+'</td>';
                        tr4 += '<td class="cnt">'+Math.round(line.vol)+'<br>'+(Math.round(line.vol/total_vol_i*1000)/10)+'%' + '</td>';
                        tr5 += '<td class="cnt">'+Math.round(line.mat/1000)/10+'<br>'+(Math.round(line.mat/total_mat_i*1000)/10)+'%' + '</td>';

                        cnt++;
                        if(cnt == 10){
                            break;
                        }
                    }
                }
            }

            $('.block_m .tabc4 .tr1').html(tr1);
            $('.block_m .tabc4 .tr2').html(tr2);
            $('.block_m .tabc4 .tr3').html(tr3);
            $('.block_m .tabc4 .tr4').html(tr4);
            $('.block_m .tabc4 .tr5').html(tr5);




        },
        error:function() {
        }
    });



    // ------------------------------------------------------------------------
    // 实时客票航线
    // ------------------------------------------------------------------------
    var param = {
        'OPTIMIZE': 1
    }

    $.ajax({
        type: 'post',
        url:"/bi/web/realtimeticket",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            if(response.date && response.date.length > 0){
                realtimeticket_routs = response.date;
                realtimeticket_pageNo = 0;
                //realtimeticket_routs.sort(function(a,b){ return Math.random()>.5 ? -1 : 1;}); // 打乱顺序
                //realtimeticket_pageSize = Math.ceil(realtimeticket_routs.length/(10*60)*updateInterval); // 10分钟的数据量
                realtimeticket_routs.reverse();
                realtimeticket_pageSize = Math.ceil(realtimeticket_routs.length/2); // 5分钟内的数据


                if($('#earth3d-wrapper').is(':visible')){
                   set3Dlines(); 
                }
            }
            
        },
        error:function() {
        }
    });



}


// ------------------------------------------------------------------------
// 设置地图上的飞机位置
// ------------------------------------------------------------------------
function setAirlines(){
    if(airportList == undefined || all_city == undefined){
        return;
    }
    if($('#mainframe')[0].contentWindow.setAirlines != undefined){
        $('#mainframe')[0].contentWindow.setAirlines();
    } else{
        setTimeout(setAirlines, 10);
    }
}


// ------------------------------------------------------------------------
// 获取所有机场列表
// ------------------------------------------------------------------------
var airportList;
function getAirportList(){

    var param = {
        //"AIRPORT_CODE": '', // 可选，传入机场CODE
    }
    $.ajax({           
        type: 'post',
        url:"/bi/web/airportdetail",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
            airportList = {};
            var list = response.airport;
            for(var i=list.length-1; i>=0; i--){
                var arp = list[i];
                airportList[arp.code] = arp;
            }

            setAirlines();
        },
        error:function() {
        }
    });
}


// 今日累计销售情况
var currentCounterNumber = 0;
var ticketsCut = 0;
var salesCut = 0;
var currentTickets = 0;
var currentTickets_d = 0;//国内
var currentTickets_i = 0;//国际
var currentSales = 0;
var ticket_avg;
var ticket_avg_d;
var ticket_avg_i;
var ticket_avg_est;
var cut_per_time;
var itv_auto_increase_tickets;
function setBlockLKpi(data){

    if(total_vol_i == undefined){
        setTimeout(setBlockLKpi, 0, data);
        return;
    }

    var total_sales = Number(data.SAL_AMT.total);
    total_tickets = Number(data.SAL_VOL.total);

    // 国内／国际
    var tk_i = total_vol_i;
    var tk_d = total_tickets - total_vol_i;

    ticket_avg = total_tickets > 0 ? (total_sales/total_tickets) : 0;
    ticket_avg_d = total_mat_d/total_vol_d;
    ticket_avg_i = total_mat_i/total_vol_i;

    var date = new Date();

    var hourdata = data.SAL_VOL.data;
    var hoursalelist = [];
    for(var hour in hourdata){
        if(!isNaN(hour) && !isNaN(hourdata[hour]) && date.getHours() >= Number(hour)){
            hoursalelist.push(hourdata[hour]);
        }
    }

    if(currentCounterNumber == 0){
        if(hoursalelist.length > 1){
            var n = Number(hoursalelist[hoursalelist.length-2]);
            ticketsCut = Math.round(n/12);
        }else{
            ticketsCut = Math.min(total_tickets, 100);
        }
    }else{
        ticketsCut = total_tickets - currentCounterNumber;
    }
    
    // 平均票价随机增减10 x 票数，算出需要动画增长的销售额
    salesCut = (ticket_avg + randomNumRange(-20,20)) * ticketsCut;

    // 每次动画增加的平均数量
    cut_per_time = Math.floor(ticketsCut/(5*60/3)) // 平均3秒跳一次
    cut_per_time = Math.round(cut_per_time * 0.8); // 跳的数量减少一些，免得下一波数据没过来，这波的已经播完了


    currentTickets = total_tickets - ticketsCut;
    currentSales = total_sales - salesCut;

    currentTickets = Math.max(currentTickets, 0)
    currentSales = Math.max(currentSales, 0)


    // 国内／国际
    currentTickets_d = Math.floor(tk_d/total_tickets * currentTickets);
    currentTickets_i = currentTickets - currentTickets_d;

    // 自动增加滚动
    if(audioPaused){
        clearInterval(itv_auto_increase_tickets);
        itv_auto_increase_tickets = setInterval(setTicketsSales, randomNumRange(1000,5000));
        setTicketsSales();
    }


    // 语音播报
    function onAudioTplLoad(tplobj){
        
        // {COMP} 今日累计出票量{VOL}张，国内出票量{VOL_D}张，国际出票量{VOL_I}张。国内平均票价{AVG_D}元。国际平均票价{AVG_I}元。销售额{SAL}元。
        var tpl = tplobj.txt;
       
        //--
        var compname = companyCode2Name[current_company_code];
        tpl = tpl.replace(/{COMP}/g, compname);

        //--
        tpl = tpl.replace(/{VOL}/g, formatCurrency(currentTickets, 2));
        tpl = tpl.replace(/{VOL_D}/g, formatCurrency(currentTickets_d, 2));
        tpl = tpl.replace(/{VOL_I}/g, formatCurrency(currentTickets_i, 2));

        //--
        tpl = tpl.replace(/{AVG_D}/g, formatCurrency(trimDecimal(Math.round(ticket_avg_d),0), 2));
        tpl = tpl.replace(/{AVG_I}/g, formatCurrency(trimDecimal(Math.round(ticket_avg_i),0), 2));

        //--
        tpl = tpl.replace(/{SAL}/g, formatCurrency(trimDecimal(Math.round(currentSales),0), 2));

        


        text2audio(tpl, true, endedCallback);


        // 语音模版加载完才能滚动最开始的数字，不然数字就是滚动后的了
        setTicketsSales();

    }

    function endedCallback(){
        // 语音播放完再滚动，不然没读完数字就变了
        clearInterval(itv_auto_increase_tickets);
        itv_auto_increase_tickets = setInterval(setTicketsSales, randomNumRange(1000,5000));
    }

    stopAudio();
    getAudioTemplate('A2.3-general', onAudioTplLoad);


   
    

}

function setTicketsSales(){

    var avgtk = currentTickets > 0 ? (currentSales/currentTickets) : 0;
    //ticket_avg_est = formatCurrency(Math.round(avgtk),0);
    $('.block_l .tkt_sal .val').text(formatCurrency(currentSales,0));

    $('.block_l .tkt_avg_d').text(formatCurrency(Math.round(ticket_avg_d),0));
    $('.block_l .tkt_avg_i').text(formatCurrency(Math.round(ticket_avg_i),0));

    // 国际 国内
    $('.block_l .tkt_cnt_d').text(formatCurrency(currentTickets_d,0));
    $('.block_l .tkt_cnt_i').text(formatCurrency(currentTickets_i,0));

    // ------------------------------------------------------
    // 累计客票 计数器
    // ------------------------------------------------------

    var numFrom = currentCounterNumber;
    var numTo = currentTickets;
    currentCounterNumber = numTo;

    function ticketCountTo(){
        $.animateToNumber('counterContainer', numTo, numFrom, null, 'group', {0: '张'});
        numFrom = numTo;
    }

    ticketCountTo(numTo);

    var numberWidth = 24;
    var numberOfGap = Math.ceil(numTo.toString().length/3)-1;
    var gapWidth = 5;
    var splitterWidth = 20;
    var counterWidth = numTo.toString().length*numberWidth + numberOfGap*gapWidth + splitterWidth + 130;
    $('#counterContainer').css('width', counterWidth+'px');


    //
    var cut = cut_per_time + randomNumRange(-10,10);

    cut = Math.max(cut, 0);
    cut = Math.min(cut, ticketsCut);
    currentTickets += cut;
    ticketsCut -= cut;

    var avg = ticket_avg + randomNumRange(-100,100);
    currentSales += avg * cut;
    salesCut -= avg * cut;

    var rto = currentTickets_d
    currentTickets_d = Math.floor(currentTickets_d/(currentTickets_d+currentTickets_i) * currentTickets);
    currentTickets_i = currentTickets - currentTickets_d;

    
    //console.log('cut_per_time', cut_per_time);
    //console.log('cut', cut);
    //console.log('currentTickets', currentTickets);
    //console.log('currentSales', currentSales);
    //console.log('ticketsCut', ticketsCut);
    //console.log('salesCut', salesCut);

    //console.log('----------------------------');



}


// 今日出票趋势
function setBlockRKpi(data){

    var hourdata = data.SAL_VOL.data;

    // ---------------------------
    var chart_id = 'chart_r';
    
    var colors = [
        ['#ceeffe', '#4ee443'], //柱状图渐变颜色
    ];
    // ---------------------------

    


    var xAxisData = [];
    var data_s1 = []; //主KPI


    var len = 24;
    var date = new Date();
    for(var i=0; i<len; i++){
        xAxisData.push(i);
        var val = hourdata[i];
        if(isNaN(val) || date.getHours() < Number(i)){
            val = 0;
        }
        data_s1.push(val);
        
    }


    // ---------------------------------------------------------
    // 主KPI图表
    var chart = echarts.init(document.getElementById(chart_id));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                var hour = params.name > 0 ? params.name : "0";
                return hour + '点: ' + formatCurrency(params.value,0) + '张';
             },
             backgroundColor: 'rgba(14, 62, 123, 0.98)',
        },
        legend: {
            show:false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle:{
              color: 'rgba(255,255,255,0.7)',
              fontSize: 11+fontSizeDiff()
            },
            data:[
                {
                    name:'',
                    icon:'circle',
                }
            ]
        },
        grid:{
            top: 20,
            left: 44,
            right: 10,
            bottom: 30,
        },
        xAxis: [
            {
                type: 'category',
                data: xAxisData,
                //boundaryGap: [10,10],
                nameTextStyle:{
                    color: '#fff'
                },
                axisLine:{
                    lineStyle:{
                        color:'#84baf0' // 轴线颜色
                    }
                },
                axisLabel:{
                    interval: 1, // 间隔显示
                    rotate: 0,
                    textStyle:{
                        color:'#fff', // 轴标签颜色大小
                        fontSize: 12+fontSizeDiff(),
                    },
                },
                axisTick: {
                    show: false, // 不显示刻度线
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
                name: '',
                nameTextStyle:{
                    color: '#41a8ff',
                    fontSize: 11+fontSizeDiff()
                },
                axisLabel:{
                    textStyle:{
                        color:'#41a8ff',
                        fontSize: 11+fontSizeDiff(),
                    },
                    formatter: '{value}'
                },
                axisLine:{
                    lineStyle:{
                        color:'rgba(0,0,0,0)'
                    }
                },
                splitLine:{
                    show:true,
                    lineStyle:{
                        color: ['rgba(255,255,255,0.1)'] // 分割线颜色
                    }
                },
            }
        ],
        series: [
            {
                name: '',
                type:'bar',
                barWidth: 6,
                animation: true,
                data: data_s1,
                label: {
                    normal: {
                        show: false,
                        position: 'top'
                    }
                },
                itemStyle: {
                    normal:{
                        color: new echarts.graphic.LinearGradient(
                            0, 0, 0, 1,
                            [
                                {offset: 0, color: colors[0][0]},
                                {offset: 1, color: colors[0][1]}
                            ]),
                    }
                },
            }
        ]
    };

    chart.setOption(option);

    
}



// 7日出票趋势
function setBlockR2Kpi(data){

    // ---------------------------
    var chart_id = 'chart_r2';
    
    var colors = [
        ['#fff799', '#f8e603'], //颜色
    ];
    // ---------------------------
    


    var xAxisData = [];
    var data_s1 = []; //主KPI


    var len = 24;
    for(var date in data){
        if(!isNaN(date)){
            xAxisData.push(date);
            var val = data[date];
            if(isNaN(val)){
                val = 0;
            }
            data_s1.push(val);
        }
    }


    // ---------------------------------------------------------
    // 主KPI图表
    var chart = echarts.init(document.getElementById(chart_id));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                return params.name + ': ' + formatCurrency(params.value,0) + '张';
             },
             backgroundColor: 'rgba(14, 62, 123, 0.98)',
        },
        legend: {
            show:false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle:{
              color: 'rgba(255,255,255,0.7)',
              fontSize: 11+fontSizeDiff()
            },
            data:[
                {
                    name:'',
                    icon:'circle',
                }
            ]
        },
        grid:{
            top: 20,
            left: 58,
            right: 10,
            bottom: 30,
        },
        xAxis: [
            {
                type: 'category',
                data: xAxisData,
                //boundaryGap: [10,10],
                nameTextStyle:{
                    color: '#fff'
                },
                axisLine:{
                    lineStyle:{
                        color:'#84baf0' // 轴线颜色
                    }
                },
                axisLabel:{
                    interval: 0, // 间隔显示
                    rotate: 0,
                    textStyle:{
                        color:'#fff', // 轴标签颜色大小
                        fontSize: 12+fontSizeDiff(),
                    },
                    formatter: function (value, index) {
                        var d = Number(value.substr(4,2))+'/'+Number(value.substr(6,2));
                        return d;
                    }
                },
                axisTick: {
                    show: false, // 不显示刻度线
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
                name: '',
                nameTextStyle:{
                    color: '#41a8ff',
                    fontSize: 11+fontSizeDiff()
                },
                axisLabel:{
                    textStyle:{
                        color:'#41a8ff',
                        fontSize: 11+fontSizeDiff(),
                    },
                    formatter: '{value}'
                },
                axisLine:{
                    lineStyle:{
                        color:'rgba(0,0,0,0)'
                    }
                },
                splitLine:{
                    show:true,
                    lineStyle:{
                        color: ['rgba(255,255,255,0.1)'] // 分割线颜色
                    }
                },
            }
        ],
        series: [
            {
                name: '',
                type:'line',
                symbol:'circle',
                symbolSize:7,
                animation: true,
                data: data_s1,
                label: {
                    normal: {
                        show: false,
                        position: 'top'
                    }
                },
                itemStyle: {
                    normal:{
                        color: colors[0][0],
                    }
                },
            }
        ]
    };

    chart.setOption(option);

    
}




function showAllAirline(){
    $('#back2prevpage').hide();
    $('.legend1').show();
    $('.legend2').hide();
    //$('.submaintitle1').text('TOP10国内航线 和 全部国际航线');
    $('.submaintitle1').text('');
    $('#mainframe')[0].contentWindow.setAirlines();
    selectLegendCb(0);
    $('#legend_cb').show();
}
function showOneCityAirline(city){
    $('#back2prevpage').show();
    $('.legend1').hide();
    $('.legend2').show();
    $('.submaintitle1').text(city+'全部出港航线');
    $('#legend_cb').hide();
}

$('#back2prevpage').on('click', function(event) {
    showAllAirline()
})

$('.submaintitle1').text('');//$('.submaintitle1').text('TOP10国内航线 和 全部国际航线');




// 图例
function switchLegend(){
    var id = $('#legend_cb').attr('data');
    if($('#mainframe')[0].contentWindow.setAirlines != undefined){
        /*
        if(id > 0){
            $('#mainframe')[0].contentWindow.setAirlines({selectedLegend: id});
        }else{
            $('#mainframe')[0].contentWindow.setAirlines(); 
        }
        */
        if(id == 0){
            $('.block_m .tab1').click();
        }else if(id == 1){
            $('.block_m .tab2').click();
        }else if(id == 2){
            $('.block_m .tab3').click();
        }else if(id == 3){
            $('.block_m .tab4').click();
        }else if(id == 4){
            // 全部国际航线
            $('.legend .itm3').click();
        }else{
            $('#mainframe')[0].contentWindow.setAirlines({selectedLegend: id});
        }
    }
}
// 销量TOP10的国内城市
$('.legend .itm1').on('click', function(e) {
    if($('#mainframe')[0].contentWindow.setAirlines != undefined){
        //$('#mainframe')[0].contentWindow.setAirlines({selectedLegend: 1});
        //selectLegendCb(1);
        $('.block_m .tab2').click();
    }
});
// 销量TOP10的国内航线
$('.legend .itm2').on('click', function(e) {
    if($('#mainframe')[0].contentWindow.setAirlines != undefined){
        //$('#mainframe')[0].contentWindow.setAirlines({selectedLegend: 2});
        //selectLegendCb(2);
        $('.block_m .tab3').click();
    }
});
// 全部国际航线
$('.legend .itm3').on('click', function(e) {
    if($('#mainframe')[0].contentWindow.setAirlines != undefined){
        $('#mainframe')[0].contentWindow.setAirlines({selectedLegend: 3});
        selectLegendCb(4);
    }
});


var currentTabIndex = 0;
var currentTabIndex2 = 0;

// 中间 渠道 航线 tab
$('.block_m .tab1').on('click', function(e) {
    $('.block_m .tab').removeClass('selected');
    $('.block_m .tabc').hide();

    $('.block_m .tab1').addClass('selected');
    $('.block_m .tabc1').show();

    currentTabIndex2 = 0;

    // 自动循环切换两个TAB
    clearTimeout(itv_autoSwitchTab2);
    itv_autoSwitchTab2 = setTimeout(autoSwitchTab2, 20000);

    $('#mainframe')[0].contentWindow.setAirlines();
    selectLegendCb(0);
    

});
$('.block_m .tab2').on('click', function(e) {
    $('.block_m .tab').removeClass('selected');
    $('.block_m .tabc').hide();

    $('.block_m .tab2').addClass('selected');
    $('.block_m .tabc2').show();

    currentTabIndex2 = 1;

    // 自动循环切换两个TAB
    clearTimeout(itv_autoSwitchTab2);
    itv_autoSwitchTab2 = setTimeout(autoSwitchTab2, 20000);

    $('#mainframe')[0].contentWindow.setAirlines({selectedLegend: 1});
    selectLegendCb(1);

});
$('.block_m .tab3').on('click', function(e) {
    $('.block_m .tab').removeClass('selected');
    $('.block_m .tabc').hide();

    $('.block_m .tab3').addClass('selected');
    $('.block_m .tabc3').show();

    currentTabIndex2 = 2;

    // 自动循环切换两个TAB
    clearTimeout(itv_autoSwitchTab2);
    itv_autoSwitchTab2 = setTimeout(autoSwitchTab2, 20000);

    $('#mainframe')[0].contentWindow.setAirlines({selectedLegend: 2});
    selectLegendCb(2);

});
$('.block_m .tab4').on('click', function(e) {
    $('.block_m .tab').removeClass('selected');
    $('.block_m .tabc').hide();

    $('.block_m .tab4').addClass('selected');
    $('.block_m .tabc4').show();

    currentTabIndex2 = 3;

    // 自动循环切换两个TAB
    clearTimeout(itv_autoSwitchTab2);
    itv_autoSwitchTab2 = setTimeout(autoSwitchTab2, 20000);

    $('#mainframe')[0].contentWindow.setAirlines({selectedLegend: 4});
    selectLegendCb(3);

});



// 出票趋势tab
$('.block_r .tab1').on('click', function(e) {
    $('.block_r .tabc1').show();
    $('.block_r .tabc2').hide();
    $('.block_r .tab1').addClass('selected');
    $('.block_r .tab2').removeClass('selected');

    currentTabIndex = 0;

    // 自动循环切换两个TAB
    clearTimeout(itv_autoSwitchTab);
    itv_autoSwitchTab = setTimeout(autoSwitchTab, 20000);

});
$('.block_r .tab2').on('click', function(e) {
    $('.block_r .tabc1').hide();
    $('.block_r .tabc2').show();
    $('.block_r .tab1').removeClass('selected');
    $('.block_r .tab2').addClass('selected');

    currentTabIndex = 1;

    // 自动循环切换两个TAB
    clearTimeout(itv_autoSwitchTab);
    itv_autoSwitchTab = setTimeout(autoSwitchTab, 20000);

});

// 自动循环切换两个TAB -----------------
var itv_autoSwitchTab;
function autoSwitchTab(){
    clearTimeout(itv_autoSwitchTab);

    if(!autoSwitch){
        itv_autoSwitchTab = setTimeout(autoSwitchTab, 10);
        return;
    }

    if(currentTabIndex == 0){
        $('.block_r .tab2').click();
    }else{
        $('.block_r .tab1').click();
    }

    itv_autoSwitchTab = setTimeout(autoSwitchTab, 20000);

}


// 自动循环切换 TAB -----------------
var itv_autoSwitchTab2;
function autoSwitchTab2(){
    clearTimeout(itv_autoSwitchTab2);

    if(!autoSwitch){
        itv_autoSwitchTab2 = setTimeout(autoSwitchTab2, 10);
        return;
    }

    if(currentTabIndex2 == 0){
        $('.block_m .tab2').click();
    }else if(currentTabIndex2 == 1){
        $('.block_m .tab3').click();
    }else if(currentTabIndex2 == 2){
        $('.block_m .tab4').click();
    }else if(currentTabIndex2 == 3){
        $('.block_m .tab1').click();
    }

    itv_autoSwitchTab2 = setTimeout(autoSwitchTab2, 20000);

}


itv_autoSwitchTab = setTimeout(autoSwitchTab, 20000);
itv_autoSwitchTab2 = setTimeout(autoSwitchTab2, 20000);

// title

function setTimer(){
    
    function formatNum(n){
        if(n < 10){
            return ('0'+n);
        }else{
            return n;
        }
    }
    
    var date = new Date();
	var titleDate= date.getDate();
	var titleMonth=date.getMonth()+1;
	var titleYear = date.getFullYear();
    var time = formatNum(date.getHours()) + '时' + formatNum(date.getMinutes()) + '分';
	
	var titleNowTime =titleYear + '年' + titleMonth + '月' + titleDate + '日' + "截至" + formatNum(date.getHours()) + '时' + formatNum(date.getMinutes()) + '分销售统计';
    
    $('#title_today_sales').text('今日（截止' + time + '）销售进展');
	
	$('.titTime').text(titleNowTime);

}
setTimer();
setInterval(setTimer, 60000);


// ---------------------- 

function onCompanyChanged(comp_code){
    
}

function checkCompanyReay(){
    
}



$('#switch2earth').on('click', function(e) {
    if($('#earth3d-wrapper').is(':visible')){
        $('#earth3d-wrapper').hide();
        clearTimeout(timeout_set3Dlines);
        chart_earch.dispose();
        $('#earth3d').html('');
        chart_earch = undefined;

        $('#2dhud').delay(200).fadeIn();
        $('#mainframe').show();
        $('.submaintitle1').show();
        $('.submaintitle2').hide();
        $('#switch2earth').removeClass('bg2');
        $('#switch2earth').addClass('bg1');
        $('#switch2earth').text('3D地图');

        $('#switch2earth').animate({
            bottom: "255px"
          }, 500);

        $('.block_l').animate({
            bottom: "25px"
          }, 500);
        $('.block_m').animate({
            bottom: "25px"
          }, 500);
        $('.block_r').animate({
            bottom: "25px"
          }, 500);

        


    }else{
        $('#earth3d-wrapper').show();
        crate3DEarth();
        set3Dlines();

        $('#2dhud').hide();
        $('#mainframe').hide();
        $('.submaintitle1').hide();
        $('.submaintitle2').show();
        $('#switch2earth').removeClass('bg1');
        $('#switch2earth').addClass('bg2');
        $('#switch2earth').text('平面地图');

        $('#switch2earth').animate({
            bottom: "20px"
          }, 500);

        $('.block_l').animate({
            bottom: "-300px"
          }, 500);
        $('.block_m').animate({
            bottom: "-300px"
          }, 500);
        $('.block_r').animate({
            bottom: "-300px"
          }, 500);
    }

});

// ------------------------------------------------------
// 3D 地球
// ------------------------------------------------------
var chart_earch;
var earch_option;
var targetCoord;
function crate3DEarth(){
    chart_earch = echarts.init(document.getElementById('earth3d'));

    if(targetCoord == undefined){
        targetCoord = [106, 18];
    }

    // 制作地图贴图
    /*
    var canvas = document.createElement('canvas');
    var mapChart = echarts.init(canvas, null, {
        width: 2048,
        height: 1024
    });

    mapChart.setOption({
        backgroundColor: '#999',
        geo: {
            type: 'map',
            map: 'world',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            boundingCoords: [[-180, 90], [180, -90]],
            silent: true,
            itemStyle: {
                normal: {
                    borderColor: '#000'
                }
            },
            label: {
                normal: {
                    textStyle: {
                        color: '#fff',
                        fontSize: 40
                    }
                }
            }
        }
    });
    */

    earch_option = {
        tooltip: {
            show: false
        },
        backgroundColor: 'rgba(0,0,0,0)',
        globe: {
            
            baseTexture: 'asset/earth3d.jpg',
            //baseTexture: mapChart,
            heightTexture: 'asset/height.jpg',

            displacementScale: 0.02,

            environment: 'asset/space.jpg',

            shading: 'lambert',
            //shading: 'realistic',

            viewControl: {
                autoRotate: true,
                autoRotateSpeed: 5,
                zoomSensitivity: 0,
                //alpha:30,
                //beta:0,
                targetCoord: targetCoord
            },

            light: {
                ambient: {
                    intensity: 0.1
                },
                main: {
                    intensity: 1.5
                }
            },

            layers: [{
                type: 'blend',
                blendTo: 'emission',
                texture: 'asset/night.jpg'
            }]
        },
        series: [
            {
                type: 'lines3D',
                coordinateSystem: 'globe',
                
                //blendMode: 'lighter',

                effect: {
                    show: true,
                    //period: 6,
                    constantSpeed: 7,
                    trailLength: 0.15,
                    trailColor: '#FFF',
                    trailWidth: 1.5
                },

                silent: true,

                data: []
            }
        ]
    }

    chart_earch.setOption(earch_option);

    
}


var prevPageSize = 0;
var timeout_set3Dlines;
function set3Dlines(){

  clearTimeout(timeout_set3Dlines);

  if(airportList == undefined || realtimeticket_routs.length == 0){
    timeout_set3Dlines = setTimeout(set3Dlines, 10);
    return;
  }

  var lines = [];
  
  if(realtimeticket_pageNo == 0){
    earch_option.series[0].data.splice(0, prevPageSize*2);
    lines = realtimeticket_routs.slice(realtimeticket_pageSize*0, realtimeticket_pageSize*2);
    realtimeticket_pageNo = 2;
  }else{
    earch_option.series[0].data.splice(0, prevPageSize);
    lines = realtimeticket_routs.slice(realtimeticket_pageSize*realtimeticket_pageNo, realtimeticket_pageSize*(realtimeticket_pageNo+1));
    realtimeticket_pageNo++;
  }

  prevPageSize = realtimeticket_pageSize;

  var len = lines.length;
  for(var i=0; i<len; i++){
      var obj = lines[i];

      var arp1 = airportList[obj.DEP_CODE];
      var arp2 = airportList[obj.DES_CODE];

      if(arp1 && arp2 && arp1.longitude && arp1.latitude && arp2.longitude && arp2.latitude){
        var color = 'rgba(255,255,255,0.02)';

        if(Math.random() > 0.5){
            //color = 'rgba(235,97,0,0.2)';
        }else{
            //color = 'rgba(102,255,0,0.2)';
        }

        earch_option.series[0].data.push({
            coords: [[arp1.longitude, arp1.latitude], [arp2.longitude, arp2.latitude]],
            //value: fltno,
            lineStyle: {
                width: 1,
                color: color,
                opacity: 1
            },
        });
      }
  }


  chart_earch.setOption(earch_option);

  earch_option.globe.viewControl.targetCoord = undefined;
  

  //timeout_set3Dlines = setTimeout(set3Dlines, updateInterval*1000);

}




// ------------------------------------




// 获取所有数据
function fetchAllData(){
    getTodayTicketSale();
}

fetchAllData();
getAirportList();


var interval = 300000; //5分钟刷新一次数据
setInterval(fetchAllData, interval);










// HBI 外部跳转链接
// function setExtLink(){

//     $('#ext_link1').attr('href', encodeURI('http://cdp.hnair.net:8080/HBI/rdThird.do?res_id=144523538594977384&paramArr=1公司=<'+current_company_code+'>::4出港城市=<缺省出港城市>::5进港城市=<缺省进港城市>::6航班号=<>::2航班日期=<缺省最大航班日期>::3销售区间=<-1~29>::7时间类型=<本期>'));
    
// }

// setExtLink();

// regTooltip('.ext_link', '查看关联报表');
