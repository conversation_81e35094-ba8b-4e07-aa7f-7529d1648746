/**/
.page-wrapper {
  background: #094895;
  background: -moz-radial-gradient(center, ellipse cover, #094895 0%, #020c25 100%);
  background: -webkit-radial-gradient(center, ellipse cover, #094895 0%, #020c25 100%);
  background: radial-gradient(ellipse at center, #094895 0%, #020c25 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#094895', endColorstr='#020c25', GradientType=1);
}
.pagetitle {
  position: absolute;
  width: 1306px;
  height: 59px;
  top: 58px;
  left: 30px;
  text-align: center;
  border-bottom: 2px solid #1765a0;
}
.maintitle {
  color: #7fc4ff;
  width: 100%;
  font-size: 24px;
  padding: 11px 0 0 0;
  margin: 0 auto;
  font-weight: bold;
}
.submaintitle {
  color: #7fc4ff;
  width: 100%;
  font-size: 22px;
  padding: 0 20px;
  margin: 0 auto;
}
#main_cb_week {
  position: absolute;
  top: 15px;
  left: 1118px;
  height: 34px;
  width: 85px;
  z-index: 1000;
}
#main_cb_month {
  position: absolute;
  top: 15px;
  left: 1202px;
  height: 34px;
  width: 104px;
  z-index: 1001;
}
#main_cb_week .combobox_label {
  padding: 6px 6px;
  background-image: url(../img/combobox_arr.png);
  background-repeat: no-repeat;
  background-position: 63px center;
  border-radius: 4px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}
#main_cb_month .combobox_label {
  padding: 6px 6px;
  background-image: url(../img/combobox_arr.png);
  background-repeat: no-repeat;
  background-position: 83px center;
  border-radius: 4px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: none;
}
.combotab_selected .combobox_label {
  background-color: #2a81d2;
}
.combotab .combobox_label {
  background-color: #0950a2;
  opacity: 0.5;
  color: #8abfff;
}
#week_date_range {
  position: absolute;
  top: 18px;
  left: 955px;
  width: 160px;
  font-size: 14px;
  text-align: center;
  border: 1px solid #1f5fa8;
  border-radius: 2px;
  padding: 3px 0 5px 0px;
  background-color: #021d39;
  box-shadow: 0 1px 8px #021121;
  display: none;
}
.wrap {
  width: 100%;
  height: 100%;
  pointer-events: auto;
}
.wrap * {
  pointer-events: auto !important;
}
.wrap .block_l {
  position: absolute;
  padding: 10px;
  box-sizing: border-box;
  top: 135px;
  left: 32px;
  width: 708px;
  height: 134px;
  overflow-y: scroll;
  border-radius: 10px;
  background-color: lightblue;
}
.wrap .block_l input {
  color: black;
}
.wrap .block_l button {
  color: black;
}
.wrap .block_r {
  position: absolute;
  padding: 10px;
  box-sizing: border-box;
  top: 300px;
  left: 32px;
  width: 708px;
  height: 134px;
  overflow-y: scroll;
  border-radius: 10px;
  background-color: lightblue;
}
