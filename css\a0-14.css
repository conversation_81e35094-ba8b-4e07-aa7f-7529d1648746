html,
body {
  width: 1280px;
  height: 1440px;
  color: #44A3F4;
}

.page-wrapper {
  pointer-events: unset;
  z-index: unset;
}

 

.main {
  width: 1280px;
  height: 1440px;
  background-color: #071243;
}
.block-header {
  text-align: center;
  width: 600px;
  font-size: 24px;
  font-weight: bold;
  height: 40px;
  background: url(../img/a0-14/17_blockheader.png) no-repeat center top;
  padding: 0 0 0 0;
  background-size: 600px 40px;
  margin: 0;
  color: #0f214c;
  line-height: 40px;
}

.leftDiv {
  position: relative;
  width: 614px;
  height: 100%;
  left: 26px;
}

.b-mainKpi {
  background-color: rgba(27, 80, 146, 0.3);
  position: relative;
}
.b-mainKpi .sub_title{
  font-size: 21.33px;
  position: absolute;
  top: 21.33px;
  left:21.33px;
}

/* 公司运行一览样式 */
.b-mainKpi-item-wrap {
  display: flex;
  position: relative;
  padding: 28px 37px;
  justify-content: space-between;
}

.b-mainKpi-item-wrap .title {
  text-align: center;
  font-size: 21.3px;
}

/* .b-mainKpi-item-wrap .b-mainKpi-item {
  width: 128px;
  margin-right: 60px;
} */

.b-mainKpi-item .echTrvNumBg {
  left: 18px;
  top: 45px;
  width: 117px;
  height: 100px;
  background-image: url(../img/a0-14/people.png);
  background-size: 100% auto;
  background-repeat: no-repeat;
  position: absolute;
}

#b_mainKpi_cvs_2,
#b_mainKpi_cvs_3 {
  width: 149.33px;
  height: 133.33px;
}

.b-mainKpi-item .flightNum-wrap {
  position: absolute;
  text-align: center;
  left: 70px;
  top: 75px;
  font-size: 21.3px;
  color: #fff;
}

.b-mainKpi-item .flightNum-wrap .value {
  font-size: 16px;
  font-weight: normal;
}

.b-mainKpi-item .info-wrap {
  display: flex;
  flex-direction: column;
  padding: 0 14px;
}


.b-mainKpi-item .info-item {
  display: flex;
  align-items: center;
}

.b-mainKpi-item .info-item .title {
  font-size: 16px;
}

.b-mainKpi-item .info-item .value {
  margin-left: 2px;
  color: #fff;
}
.leftDiv,.rightDiv {
  padding-top: 106.6667px;
}
.leftDiv .row11 {
  margin-top: 13.333px;
  width: 600px;
  height: 300px;
}

.leftDiv .row12 {
  margin-top: 13.333px;
  width: 600px;
  height: 300px;
}

.delay-layout {
  position: absolute;
  margin-top: 21px;
  left: 21px;
  color: #44A3F4;
  font-size: 21px;
}

.delay-layout .box-content {
  padding: 0.2rem 0.1rem;
  height: 2.88rem;
}

.delay-layout .box-content .delayList {
  padding-top: 0.05rem;
}

.delay-layout .box-content .delayList .company_reason {
  width: 1.8rem;
  height: 1.91rem;
}

.delay-layout .box-content .delayList .chart {
  width: 1.5rem;
  height: 1px;
}

.delay-layout .box-content .delayList .no_company_reason {
  width: 1.8rem;
  height: 1.91rem;
}

.delay-layout .box-content .echartDom {
  width: 1.41rem;
  height: 1.57rem;
  position: absolute;
  top: 0.64rem;
  left: 2.05rem;
  text-align: center;
}

.delay-layout .box-content .echartDom .leftArrow {
  width: 0.12rem;
  height: 0.12rem;
  background-image: url(../img/a0-14/triangle-left.svg);
  background-size: contain;
  background-repeat: no-repeat;
  position: absolute;
  top: 0.78rem;
  left: 0;
}

.delay-layout .box-content .echartDom .rightArrow {
  width: 0.12rem;
  height: 0.12rem;
  background-image: url(../img/a0-14/triangle-right.svg);
  background-size: contain;
  background-repeat: no-repeat;
  position: absolute;
  top: 0.78rem;
  right: 0;
}

.leftDiv .row13 {
  margin-top: 13.333px;
  width: 600px;
  height: 300px;
}

.yunliBox {
  width: 100%;
  height: 100%;
}

.yunliBox .box-header {
  padding-top: 23px;
}

.yunliBox .box-header .box-title {
  margin-left: 21px;
  font-size: 21px;
  color: #44A3F4;
}

.yunliBox .box-header .box-title2 {
  margin-left: 259px;
  font-size: 19px;
  color: white;
}

.yunliBox .box-header .box-title3 {
  margin-left: 27px;
  font-size: 19px;
  color: #44A3F4;
}

.yunliBox .box-content {
  display: flex;
}

.yunliBox .box-content .box1 {
  width: 207px;
  height: 242px;
}

.yunliBox .box-content .box1 .kpi-plane {
  width: 171px;
  height: 44px;
  margin-top: 68px;
  margin-left: 16px;
}

.yunliBox .box-content .box1 .kpi-text {
  width: 106.667px;
  height: 85px;
  background-image: url('../img/a0-14/kpi-text.png');
  background-size: 106.667px 85px;
  margin-top: 15px;
  margin-left: 48px;
  text-align: center;
  font-size: 19px;
}

.yunliBox .box-content .box1 .kpi-text .total-yunli {
  padding-top: 17px;
  font-size: 19px;
  height: 36px;
  color: #44A3F4;
}

.box2 .kpi-plane {
  width: 32px;
  height: 32px;
  margin-top: 8px;
}

.box2 .acNumValue {
  font-size: 19px;
  height: 19px;
}

.box2 .wordFontUnit {
  font-size: 19px;
  height: 19px;
}

.leftDiv .row14 {
  margin-top: 13.333px;
  width: 600px;
  height: 313.33px;
}

.row14 .fs12 {
  height: 70px;
  overflow: auto;
  width: 390px;
}

/** 右侧**/
.rightDiv {
  margin-left: 26px;
  height: 100%;
  width: 614px;
}

.rightDiv .b-mainKpi {
  margin-left: 14px;
}

.rightDiv .row21 {
  margin-left: 14px;
  margin-top: 13.3333px;
  width: 600px;
  height: 300px;
  display: flex;
}

/* 当日航班量 */
.rightDiv .row21 .row211 {
  padding: 21px 16px;
  width: 352px;
  height: 300px;
  background-color: rgb(27, 80, 146, 0.3);
}
.rightDiv .row21 .row211 .title,
.rightDiv .row21 .row212 .title{
font-size: 21.33px;
}

.rightDiv .row21 .row211 .item-top-warp ,
.rightDiv .row21 .row211 .item-bottom-warp {
  display: flex;
  justify-content: space-between;
}

.rightDiv .row21 .row211 .item-top-warp .item-info-wrap,
.rightDiv .row21 .row211 .item-bottom-warp .item-info-wrap{
  display: flex;
  justify-content: space-between;
  width: 266px;
}

.rightDiv .row21 .row211 .item-bottom-warp .item-info-wrap{
  display: unset;
}

.rightDiv .row21 .row211 .item-bottom-warp .item-info-wrap .num{
  color: #fff;
}

.row211 .item-top-warp .item-info-wrap .top,
.row211 .item-bottom-warp .item-info-wrap  .top  {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.row211 .item-top-warp .item-info-wrap  .top .info-left,
.row211 .item-bottom-warp .item-info-wrap  .top .info-left{
  height: 22px;
  font-size: 18px;
  color: #fff;
}
.row211 .item-top-warp .item-info-wrap .top .info-right,
.row211 .item-bottom-warp .item-info-wrap  .top .info-right{
  height: 22px;
  font-size: 16px;
  color: #44A3F4;
} 

.row211 .item-top-warp .item-info-wrap  .top .info-right .num,
.row211 .item-bottom-warp .item-info-wrap  .top .info-right .num{
  color: #fff;
  margin: 0 2px;
}

.rightDiv .row21 .row211  .item-flag {
  width: 32px;
  height: 53.33px;
  font-size: 16.67px;
  color: #fff;
  background: #016666;
  writing-mode: vertical-rl;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.item-top-warp{
  position: relative;
  margin-top: 20px;
  margin-bottom: 13px;
}

.item-top-warp  .processBar,
.item-bottom-warp .processBar{
  position: absolute;
  width: 270px;
  height: 5px;
  left: 50px;
  top: 41px;
  border-radius: 2px;
  background-color: #0062AB;
}
.item-bottom-warp{
  position: relative;;
}

.item-bottom-warp .item-flag {
  height: 152px !important;
}

.item-bottom-warp .bottom {
  font-size: 16px;
  margin-top: 40px;
}

.item-top-warp .processBar .insidebar,
.item-bottom-warp .processBar .insidebar{
  width:0;
  height: 5px;
  background-image: linear-gradient(to left, #FFFFFF 0%, #44A3F4 100%);
  border-radius:2px;
}


.rightDiv .row21 .row212 {
  padding: 21px 16px;
  width: 235px;
  height: 300px;
  margin-left: 13px;
  background-color: rgb(27, 80, 146, 0.3);
}
.rightDiv .row21 .row212 .title-wrap{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.rightDiv .row21 .row212 .subTitle{
  font-size: 21.33px;
  text-align: right;
  float: right;
  color: #FAAF33;
}
.rightDiv .row21 .row212 .subTitle .value{
font-weight: bold;
}

.rightDiv .row21 .row212 .aclist-item .processBar{
  position: relative;
  width: 196px;
  height: 10px;
  margin: 8px;
  border-radius: 6.67px;
  background-color: #0062AB;
}
.rightDiv .row21 .row212 .aclist-item .processBar .insidebar{
  position: absolute;
  top:-4px;
  left: 0;
  width: 22.67px;
  height: 18.67px;
  background-image: url('../img/a0-14/plane.png');
  background-repeat: no-repeat;
  background-size: 22.67px;
}

.rightDiv .row22 {
  margin-top: 13.333px;
  width: 600px;
  height: 300px;
}

.rightDiv .row23 {
  margin-top: 13.333px;
  width: 600px;
  height: 300px;
}

.rightDiv .row24 {
  margin-top: 13.333px;
  width: 600px;
  height: 313px;
}

.abnormal .left {
  width: 200.33px;
  height: 222.67px;
  top: 100px;
  left: 53.33px;
  position: absolute;
  text-align: left;
}
.abnormal .item {
  font-size: 18.67px;
  line-height: 32px;

}
.abnormal .value {
  font-size: 21.33px;
  margin-bottom: 20px;
}
.abnormal .value .unit {
  font-size: 18.67px;
}

.abnormal .center {
  width: 200px;
  height: 70px;
  top: 135px;
  left: 200px;
  text-align: center  ;
  position: absolute;
}

.abnormal .center .cr {
  top: 10px;
  width: 100%;
  font-size: 16px;
  color: #9BD70D;
  text-align: center;
  position: relative;
}

.abnormal .center .ncr {
  top: 190.67px;
  font-size: 16px;
  width: 100%;
  text-align: center;
  position: relative;
}

.abnormal .right {
  width: 120px;
  height: 200.67px;
  top: 100px;
  right: 53.33px;
  position: absolute;
  text-align: right;
}

.abnormal .ecKpiPie {
  width: 180px;
  height: 180px;
  top: 86.67px;
  left: 210px;
  position: absolute;
}

.abnormal .companyPercent {
  font-size: 18.67px;
  position: absolute;
  top: 21.33px;
  left: 260.33px
}

.abnormal .notcompanyPercent {
  position: absolute;
  font-size: 18.67px;
  top: 21.33px;
  left: 435.33px
}

.tab-data-row>div {
  display: inline-block;
  line-height: 21.33px;
  font-size: 16px;
}

.tab-data-row .percent {
  width: 30px;
}

.tab-data-row .item-name {
  width: 100px;
  text-align: right;
  vertical-align: bottom;
}

.tab-data-row .ellipsis {
  cursor: pointer;
}

.tab-data-row .data-bar {
  height: 8px;
  width: 50px;
  border-radius: 4px;
  background-color: #00579e;
}

.yunliBox-body {
  margin-top: 24px;
  height: 70px;
  overflow: auto;
  width: 390px;
}

.yunliBox-body::-webkit-scrollbar {
  width: 2px;
}

.yunli-item .data-bar {
  height: 8px;
  width: 67px;
  margin-top: 4px;
  border-radius: 4px;
  background-color: #00579e;
}

.yunli-item .value {
  width: 30px;
  margin-left: 8px;
}

.yunli-item .data-bar-progress {
  height: 8px;
  border-radius: 4px;
}

.yunli-item .data-bar-blue {
  background: #00affd;
}

.yunli-item .data-bar-yellow {
  background: #a1dd00;
}

.yunli-item .data-bar-orange {
  background: #eb6100;
}

.line {
  height: 1px;
  width: 100%;
  border-bottom: 1px solid #0062AB ;
  margin: 1px 0 0 1px;
}

.el-input__inner {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  color: #fff;
  display: inline-block;
  font-size: 18.67px;
  height: 45.33px;
  line-height: 45.33px;
  width:162.67px ;
  outline: none;
  background: none;
  border: none;
  
}



/** rock start **/

 /** rock end**/



/**  Sherlock start**/
.row22 .fivePie1 {
  width: 96px;
  height: 96px;
  margin-top: 76px;
  margin-left: 21px;
  position: absolute;
}

.row22 .fivePie2 {
  width: 96px;
  height: 96px;
  margin-top: 143px;
  margin-left: 136px;
  position: absolute;
}

.row22 .fivePie3 {
  width: 96px;
  height: 96px;
  margin-top: 76px;
  margin-left: 252px;
  position: absolute;
}

.row22 .fivePie4 {
  width: 96px;
  height: 96px;
  margin-top: 143px;
  margin-left: 367px;
  position: absolute;
}

.row22 .fivePie5 {
  width: 96px;
  height: 96px;
  margin-top: 76px;
  margin-left: 483px;
  position: absolute;
}
.row22 .fiveNum {
  color: white;
  font-size: 21px;
}

.row22 .fiveText {
  font-size: 16px;
}

.row24 .todayPlanPie1 {
  width: 96px;
  height: 96px;
  margin-top: -260px;
  margin-left: 232px;
  position: absolute;
}

.row24 .todayPlanPie2 {
  width: 96px;
  height: 96px;
  margin-top: -260px;
  margin-left: 381px;
  position: absolute;
}

.row24 .todayPlanPie3 {
  width: 96px;
  height: 96px;
  margin-top: -127px;
  margin-left: 232px;
  position: absolute;
}

.row24 .todayPlanPie4 {
  width: 96px;
  height: 96px;
  margin-top: -127px;
  margin-left: 381px;
  position: absolute;
}

.icon-e600_sunny:before {
  content: "\e600";
}
/** Sherlock end**/



/**  slin  start**/
.logo_HU{
  width: 1282.67px;
  height: 82.67px;
  background-image: url(../img/a0-14/banner_HU.svg);
  background-size: contain;
  background-repeat: no-repeat;
  position: absolute;
  top: 0;
  left: 0;
}

.logo_CN{
  width: 1282.67px;
  height: 82.67px;
  background-image: url(../img/a0-14/banner_CN.svg);
  background-size: contain;
  background-repeat: no-repeat;
  position: absolute;
  top: 0;
  left: 0;
}

.logo_JD{
  width: 1282.67px;
  height: 82.67px;
  background-image: url(../img/a0-14/banner_JD.svg);
  background-size: contain;
  background-repeat: no-repeat;
  position: absolute;
  top: 0;
  left: 0;
}
.baseName{
  width: 240px;
  background-size: contain;
  background-repeat: no-repeat;
  color: #0f214c;
  position: relative;
  line-height: 40px;
  font-size: 24px;
  font-weight: bold;
}
.dropDown{
  background-image: url(../img/a0-14/dropdown.png);
  width: 10.67px;
  height: 8px;
  right: 35px;
  top:15px;
  background-size: contain;
  background-repeat: no-repeat;
  position: absolute;
}

.el-dropdown-menu--medium .el-dropdown-menu__item {
  line-height: 40px;
  padding: 0 17px;
  font-size: 18px;
}


/**  slin end**/
