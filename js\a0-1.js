// ------------------------------------------------------------

var dateCmpDtd = $.Deferred();
$('#mainframe').attr('src', 'map/map-a0-1.html');

// ------------------------------------------------------------

//页面标题
var param = {
    'ID': 'WEB_PAGE_SUBTITLE_OP',
}

$.ajax({
    type: 'post',
    url: "/bi/web/querydatalist",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
        var val = response.data[0].name;
        $('#main_page_subtitle_op').html(val);
    },
    error: function (jqXHR, txtStatus, errorThrown) {

    }
});


// 日期类型

var legendColorList1 = ['#e95551', '#f7a449', '#fff353', '#21b3ce', '#176ebf', '#0a3a89', '#8619db', '#aa44e1', '#a5a27d', '#925c86'];
legendColorList1 = legendColorList1.concat(legendColorList1);
var legendColorList2 = ['#aecffd', '#f98a01', '#d11f1e', '#f3f1d5'];

var legendColorList3 = ['#e95551', '#f7a449', '#fff353', '#21b3ce', '#176ebf', '#0a3a89', '#8619db', '#aa44e1', '#a5a27d', '#925c86'];
legendColorList3 = legendColorList3.concat(legendColorList3);
var legendColorList4 = ['#aecffd', '#f98a01', '#d11f1e', '#f3f1d5', '#f98a01'];

var ac_type_list = ['787', '767', '330', '737', '320', '321', '319', '350', '145', '190'];
var weeks = [];



// -------------------------------------------------------

//                          KPI

// -------------------------------------------------------



var monthList = [];
var quarterList = [];

var selected_month;
var selected_quarter;

var kpiDataReady = false;
var fetchingKpiData = false;

var all_company_data = {};
var latestDate;
var flightInfoList = {};

var eventDict = {};

var startBjTime;
var startBjTime2;
var endBjTime;
var endBjTime2;

var all_delay_cause_data;

var yunxingKpiDataList = [];

// 获取例会周对应的日期范围
var weekDateRangeList;


// 机型id／code对应表
var actypeId2Code;

// 获取所有机型
var param = {}

$.ajax({
    type: 'post',
    url: "/bi/web/actypeall",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {

        if (response.actype) {
            var list = response.actype;
            actypeId2Code = {};


            var len = list.length;
            for (var i = 0; i < len; i++) {
                var obj = list[i];
                actypeId2Code[obj.id] = obj.code;
            }

        }

    },
    error: function () { }
});




var current_company_code;


// 所有飞机架数
var total_plane = -1;
// 执行中飞机架数
var exe_total_plane = -1;
// 机型对照表
var actypeMapList;
// 空中飞机架数
var plane_over_air = -1;
var ac_type_list = ['787', '767', '330', '737', '320', '321', '319', '350', '145', '190'];
//var ac_type_list = ['787', '767', '330', '737'];
var ac_data_list_ready;
var ac_data_list;
var exe_ac_data_list;

// 飞机位置信息
var planeLocationList;

// 飞机航班信息
var flightInfoList;

// 机场列表
var airportList;

// 机型id／code对应表
var actypeId2Code;



// 在飞航班
var currentCounterNumber1 = 0;
// 延误航班
var currentCounterNumber2 = 0;
// 滚动数字
// elementId DOM ID
// numFrom 开始的数字
// numTo 滚动到的数字
function setCounter(elementId, numFrom, numTo) {

    $.animateToNumber(elementId, numTo, numFrom, null, 'group', {
        0: '架'
    });
    numFrom = numTo;

}



var codelist = [];
var codelist_no_parent = [];



// 获取基本数据
function getGeneralKpi() {

    // 所有飞机架数
    total_plane = -1;
    // 执行中飞机架数
    exe_total_plane = -1;
    // 机型对照表
    actypeMapList;
    // 空中飞机架数
    plane_over_air = -1;
    ac_data_list_ready = false;





    // 接口用到的当日 开始 结束 时间
    var date = new Date();
    var mm = date.getMonth() + 1;
    var dd = date.getDate();
    if (mm < 10) {
        mm = '0' + mm;
    }
    if (dd < 10) {
        dd = '0' + dd;
    }

    var stdEndUtcTime = date.getFullYear() + '-' + mm + '-' + dd + ' 15:59:59';
    var yesterday_ts = date.getTime() - 86400000;
    var twodayago_ts = date.getTime() - 86400000 * 2;
    date.setTime(yesterday_ts);
    mm = date.getMonth() + 1;
    dd = date.getDate();
    if (mm < 10) {
        mm = '0' + mm;
    }
    if (dd < 10) {
        dd = '0' + dd;
    }
    var stdStartUtcTime = date.getFullYear() + '-' + mm + '-' + dd + ' 16:00:00';
    // --- 时间end


    planeLocationList = undefined;

    // ------------------------------------------------------------------------
    // 获取航班信息
    // ------------------------------------------------------------------------

    // 开始结束时间
    var date = new Date();
    var mm = date.getMonth() + 1;
    var dd = date.getDate();
    if (mm < 10) {
        mm = '0' + mm;
    }
    if (dd < 10) {
        dd = '0' + dd;
    }
    var stdStart = date.getFullYear() + '-' + mm + '-' + dd + ' 00:00:00';
    /*
    var end_ts = date.getTime()+86400000;
    date.setTime(end_ts);
    mm = date.getMonth()+1;
    dd = date.getDate();
    if(mm < 10){
        mm = '0' + mm;
    }
    if(dd < 10){
        dd = '0' + dd;
    }
    */
    var stdEnd = date.getFullYear() + '-' + mm + '-' + dd + ' 23:59:59';
    //



    var companyCode = '';
    if (current_company_code != parent_company) {
        companyCode = current_company_code;
    }
    //var fltIntList = [];
    var param = {
        "stdStart": stdStart,
        "stdEnd": stdEnd,
        "acOwner": companyCode,
        "statusList": 'DEP', // 只返回起飞的，表示在空中
    }
    var dtd1 = $.ajax({
        type: 'post',
        url: "/bi/web/getStandardFocFlightInfo?today",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            var list = response.data;
            flightInfoList = {};
            for (var i = list.length - 1; i >= 0; i--) {
                var obj = list[i];
                flightInfoList[obj.flightNo] = obj;

                // 国际航班
                if (obj.fltType == 'I') {
                    //fltIntList.push(obj);
                }
            }
            //console.log('fltIntList', fltIntList);
            setPlaneLocation();

            if ($('#earth3d-wrapper').is(':visible')) {
                if (!chart_earch) {
                    crate3DEarth();
                }
                set3Dlines();
            }

        },
        error: function () { }
    });


    // ------------------------------------------------------------------------
    // 获取航班实时位置
    // ------------------------------------------------------------------------



    // 获取飞机实时位置
    function getPlaneLocationMq() {

        var param = {
            'mode': 'pos'
        }
        return $.ajax({
            type: 'post',
            url: "/bi/web/flightMq",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {

                //checkLogin(response);

                planeLocationList = [];
                var plist = {};

                //var list = response.data.data1;
                function processData(data1) {
                    var lst = {};
                    var len = data1.length;
                    for (var i = 0; i < len; i++) {
                        var dd = data1[i];
                        var fi = dd.fi;
                        if (lst[fi] == undefined) {
                            lst[fi] = {
                                data: []
                            };
                            lst[fi]['data'].push(dd);
                        } else {
                            lst[fi]['data'].push(dd);
                        }
                    }

                    return lst;
                }

                var list = processData(response.data.data1);

                console.log('processData', list);

                for (var fltno in list) {

                    var fltobj = list[fltno];
                    var itmx2 = fltobj.data;

                    var itm;

                    if (itmx2 && itmx2.length > 1) {
                        var itm1 = itmx2[0];
                        var itm2 = itmx2[1];


                        itm1.UTC = itm1.UTC.replace(' ', '');
                        itm2.UTC = itm2.UTC.replace(' ', '');

                        if (itm1.UTC > itm2.UTC) {
                            itm = itm1
                            itm.LON1 = itm2.LON;
                            itm.LAT1 = itm2.LAT;
                        } else if (itm1.UTC < itm2.UTC) {
                            itm = itm2
                            itm.LON1 = itm1.LON;
                            itm.LAT1 = itm1.LAT;
                        } else {

                            itm = itm2
                            itm.LON1 = itm1.LON;
                            itm.LAT1 = itm1.LAT;

                            //console.log(fltno, '两组经纬度UTC相同');
                        }
                    } else if (itmx2 && itmx2.length > 0) {
                        itm = itmx2[0];

                    }


                    if (itm) {

                        var alt = itm.ALT;
                        var cas = itm.CAS;
                        let vec;

                        var fltno = itm.fi;

                        if (current_company_code == parent_company || fltno.indexOf(current_company_code) == 0) {

                            var acno = itm.an;
                            acno = acno.replace('-', '');

                            var lon = formatLonLat(itm.LON);
                            var lon1 = formatLonLat(itm.LON1);
                            var lat = formatLonLat(itm.LAT);
                            var lat1 = formatLonLat(itm.LAT1);

                            if (isNaN(itm.LON)) {
                                vec = Number(itm.VEC);
                            }

                            var oil = isNaN(itm.OIL) ? '' : itm.OIL;

                            var pdat = {
                                fltno: fltno,
                                acno: acno,
                                alt: alt,
                                vec: vec,
                                lon: lon,
                                lat: lat,
                                lon1: lon1,
                                lat1: lat1,
                                oil: oil,
                            };

                            var code = acno + '-' + fltno;

                            /*
                            if(plist[code] == undefined){
                                plist[code] = pdat;
                            }else if(plist[code].lon1 == undefined){
                                plist[code].lon1 = pdat.lon;
                                plist[code].lat1 = pdat.lat;
                                if(oil > 0){
                                    plist[code].oil = oil;
                                }
                            }else if(oil > 0){
                                plist[code].oil = oil;
                            }
                            */

                            if (pdat.vec == undefined) {
                                pdat.vec = getGeoAngle(pdat.lat, pdat.lon, pdat.lat1, pdat.lon1);
                            }
                            planeLocationList.push(pdat);
                        }
                    }
                }


                console.log('planeLocationList', planeLocationList);
                setPlaneLocation();


            },
            error: function (jqXHR, txtStatus, errorThrown) {

            }
        });
    }

    var dtd2 = getPlaneLocationMq();



    // ------------------------------------------------------------------------
    // 运力分布 - 空中机型架数
    // ------------------------------------------------------------------------
    var len = companylist.length;
    var codelist = [];
    for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        if (dat.code != parent_company) {
            codelist.push(dat.code);
        }
    }
    var comp_codes = current_company_code == parent_company ? codelist.join(',') : current_company_code;

    var param = {
        'SOLR_CODE': 'FAC_COMP_ACTYPE_KPI',
        'COMP_CODE': comp_codes,
        'KPI_CODE': 'EXE_AC_NUM', // AC_NUM 飞机架次, EXE_AC_NUM 执行中飞机架数
        'VALUE_TYPE': 'kpi_value_d', //本期
        'DATE_TYPE': 'D',
        'ACTYPE': 'ALL',
        "OPTIMIZE": 1,
        'LIMIT': 1
    }

    var dtd3 = $.ajax({
        type: 'post',
        url: "/bi/query/getackpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {

            if (response.data != undefined) {
                var data = response.data;

                // 设置机型数量
                function setAcNum() {
                    if (actypeId2Code == undefined) {
                        setTimeout(setAcNum, 0);
                        return;
                    }

                    exe_total_plane = 0;

                    var lenk = companylist.length;
                    for (var kk = 0; kk < lenk; kk++) {

                        var dat = companylist[kk];
                        var compcode = dat.code;
                        if (compcode != parent_company && data[compcode] != undefined) {

                            var kpiaclst2 = data[compcode]['EXE_AC_NUM']['D']['data3'];

                            // 执行中飞机架数
                            var len = kpiaclst2.length;
                            for (var i = 0; i < len; i++) {
                                var acdat = kpiaclst2[i];
                                var acid = acdat.actype;
                                var accode = actypeId2Code[acid];
                                if (accode && accode != 'QITA') {
                                    var acdd = acdat.date;
                                    var len2 = acdd.length;

                                    for (var j = 0; j < len2; j++) {
                                        var dd = acdd[j];
                                        var val = isNaN(dd.value) ? 0 : Number(dd.value);
                                        exe_total_plane += val;

                                    }


                                }
                            }


                        }

                    }

                    // 执行中
                    $('#plane_exec').text(exe_total_plane);

                }

                setAcNum();


            }

        },
        error: function () { }
    });

    return [dtd1, dtd2, dtd3];

}



//返回到实时运行前一个页面
$('.back2level1').on('click', function (evt) {
    $('#page_leve1').show();
    $('#page_leve2').hide();

    $('#switch2earth').show();

    setPlaneLocation();
})



// ------------------------------------------------------------------------
// 设置地图上的飞机位置
// ------------------------------------------------------------------------
function setPlaneLocation() {
    if (flightInfoList == undefined || planeLocationList == undefined || $('#page_leve2').is(':visible')) {
        return;
    }

    if ($('#mainframe')[0].contentWindow.setPlaneLocation != undefined) {
        $('#mainframe')[0].contentWindow.setPlaneLocation();
    } else {
        setTimeout(setPlaneLocation, 10);
    }
}



function setPlaneLocation3D() {

    if (flightInfoList == undefined || planeLocationList == undefined) {
        setTimeout(setPlaneLocation3D, 10);
        return;
    }

    var seriesData = [];


    for (var i = planeLocationList.length - 1; i >= 0; i--) {
        var itm = planeLocationList[i];
        var acno = itm.acno;
        var fltno = itm.fltno;

        var vec = itm.vec;
        var alt = itm.alt;

        var lon = itm.lon;
        var lat = itm.lat;

        var flt = findFltInfo(fltno);

        /*
        黄色：延误 DEL
        紫色：机务工作 ARR NDR ATA CNL
        绿色：飞行中 DEP RTR
        蓝色：未执行 SCH ATD

        'ARR':'落地',
        'NDR':'落地',
        'ATD':'推出',
        'ATA':'到达',
        'CNL':'取消',
        'DEL':'延误',
        'DEP':'起飞',
        'RTR':'返航',
        'SCH':'计划'

        */
        // 在飞的，空中的飞机
        if (flt && (alt > 0 || flt.status == 'DEP')) {
            var img = '';
            var delay_min = Number(flt.dur1) + Number(flt.dur2) + Number(flt.dur3) + Number(flt.dur4); //延误时间 分钟
            var color;
            var border;
            var svg = 'M90.7,4.8c-1.3-1.5-2.8-2.7-4.5-3.5C84.3,0.4,82.4,0,80.3,0l-0.4,0.1L79.5,0c-2,0-4,0.4-5.8,1.4c-1.8,0.8-3.3,2-4.6,3.5c-2.2,2.3-3.2,4.9-3.2,8l0,47.5L0.1,111.7L0,127.4l65.3-21.2l-0.1,38L39,167.2l0,7.9l3-0.8v1.6l37.9-10.9l37.9,10.9v-1.6l2.9,0.8l-0.1-7.9l-26.1-22.9l-0.1-38l65.4,21.2l-0.1-15.8L94,60.3l-0.1-47.5C93.9,9.8,92.9,7.1,90.7,4.8z';
            if (delay_min == 0) {
                color = "#69d0ff";
                border = "#000000";
            } else {
                color = "#fff60e";
                border = "#000000";
            }
            seriesData.push({
                name: fltno,
                acno: acno,
                value: [lon, lat, 0],
                //symbolRotate: -vec, //航向   正北是0°，顺时针，0到360°
                //symbol:'path://'+svg,
                itemStyle: {
                    color: color,
                    borderColor: border,
                    borderWidth: 1,
                }
            })
        }

    }

    var series = [];
    series.push({
        type: 'scatter3D',
        coordinateSystem: 'globe',
        symbolSize: 6,
        //blendMode: 'lighter',
        slient: true,
        label: {
            show: false,
        },
        data: seriesData
    });

    var option = {
        series: series
    }
    chart_earch.setOption(option);
}



// ---------------------- 
//切换公司
function onCompanyChanged(comp_code) {
    current_company_code = comp_code;
    //changeContact(comp_code);
    //程序入口,初始化变量
    dateCmpDtd.done(function () {
        main(getCurrentDayObj());
    })
}

function getCurrentDayObj() {
    var dateType = getDateType();
    if (dateType == 'D') {
        return $('#datetimepicker').data("DateTimePicker").date()._d
    } else if (dateType == 'L') {
        return JSON.parse($("#main_cb_week").attr("data"));
    } else if (dateType == 'M') {
        return $('#datetimepicker_month').data("DateTimePicker").date()._d
    } else if (dateType == 'Y') {
        return $('#datetimepicker_year').data("DateTimePicker").date()._d
    }
}


function checkCompanyReay() {
    if (companyCode2Name[current_company_code] == undefined) {
        setTimeout(checkCompanyReay, 0);
    } else {
        $('.back2level1').click();

        if (chart_earch) {

            clearTimeout(timeout_set3Dlines);
            chart_earch.dispose();
            $('#earth3d').html('');
            chart_earch = undefined;

        }

        flightInfoList = undefined;
        planeLocationList = undefined;

        fetchAllData();

    }

}



$('.map-btns .m2d').on('click', function (e) {
    if ($('#earth3d-wrapper').is(':visible')) {
        $('#earth3d-wrapper').hide();

        chart_earch.dispose();
        $('#earth3d').html('');
        chart_earch = undefined;

        $('.flight-status').show();
        $('#mainframe').show();


    }

});


$('.map-btns .m3d').on('click', function (e) {
    if (!$('#earth3d-wrapper').is(':visible')) {

        $('#earth3d-wrapper').show();
        crate3DEarth();


        $('.flight-status').hide();
        $('#mainframe').hide();

    }

});

// ------------------------------------------------------
// 3D 地球
// ------------------------------------------------------
var chart_earch;
var earch_option;
var targetCoord;

function crate3DEarth() {
    chart_earch = echarts.init(document.getElementById('earth3d'));

    var option = {
        tooltip: {
            show: false
        },
        backgroundColor: 'rgba(0,0,0,0)',
        globe: {
            baseTexture: 'asset/earth.jpg',
            //heightTexture: '/asset/get/s/data-1491889019097-rJQYikcpl.jpg',

            displacementScale: 0.1,

            shading: 'lambert',

            //environment: 'rgba(0,0,0,0)',
            shading: 'realistic',
            light: {
                main: {
                    intensity: 0.3
                },
                ambient: {
                    intensity: 1.0
                },
            },

            viewControl: {
                autoRotate: false,
                zoomSensitivity: false,
                targetCoord: [115, 32]
            },

            layers: []
        },
        series: []
    }

    chart_earch.setOption(option);

    $('#earth3d').css('pointer-events', 'auto');
    setPlaneLocation3D()


}



function findFltInfo(find_fltno) {
    for (var fltno in flightInfoList) {
        var flt = flightInfoList[fltno];
        if (fltno == find_fltno) {
            return flt;
        }
    }
    return undefined;
}



function formatNum(n) {
    if (n < 10) {
        return ('0' + n);
    } else {
        return n;
    }
}


$(".tabs .traffic").on('click', function (e) {
    $(".tabs .traffic").removeClass("selected");
    var target = $(e.currentTarget);
    target.addClass("selected")
    $("#tab-item-container").removeClass("ky hy");
    $("#tab-item-container").addClass(target.attr('data-type'));
});
//可用运力tab
$(".tabs .yongli").on('click', function (e) {
    $(".tabs .yongli").removeClass("selected");
    var target = $(e.currentTarget);
    target.addClass("selected")
    $(".yunliChart").addClass("hide");
    $("." + target.attr('data-type')).removeClass("hide");
});
/**   常旅客 */
var colors = ['#ba4f5b', '#ec7a00', '#fbb238', '#28c842', '#0765f1', '#6f0de2', '#aa44e1', '#f50f81', '#ff43c3', '#b169ff', '#8329ff', '#7309e0', '#1064e2', '#1a9ff6', '#00a61a'];
function initMemberChart() {
    var param = {
        COMP_ID: parent_company_id,// 获取固定传100海航航空
        IS_LATEST: true,// 获取统计最新数据
        KPI_ID: '10106' // 常旅客会员人数
    }
    var htmlStr = "";
    $.ajax({
        type: 'post',
        url: "/bi/web/memberkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            var member_card_data = response.member;// 各种会员数据列表
            var total = 0;
            var len = member_card_data.length;
            // 获取总数
            for (var i = 0; i < len; i++) {
                var d = member_card_data[i];
                var val = d["KPI_VALUE"];
                total += isNaN(val) ? 0 : Number(val);
            }
            // 已经按人数降序排列
            var seriesdata = [];
            for (var i = 0; i < len; i++) {
                // 占比
                var d = member_card_data[i];
                var val = d["KPI_VALUE"];
                seriesdata.push({
                    value: val,
                    name: d['TIER_NAME']
                })
                val = isNaN(val) ? 0 : Number(val);
                var zb = toFixed(val * 100 / total, 2) + "%";

                htmlStr += '<div class="lg-row">';
                htmlStr += '<div class="col card" ><div style="background-color:' + colors[i] + '">' + d['TIER_NAME'] + '</div></div>';
                htmlStr += ' <div class="col nums" >' + formatCurrency(val, 0) + '人</div>';
                htmlStr += '<div class="col percentage"><span>占比</span> ' + zb + '</div>';
                htmlStr += '</div>';
            }

            $("#jinpendLegend").html(htmlStr);
            $("#totalMemberNum").html(toFixed(total / 10000, 0));

            doInitMemberChart(seriesdata);

        }
    });
}

// ------------------------------------------------------------------------
// 获取所有机场列表
// ------------------------------------------------------------------------
function getAirportList() {

    var param = {
        //"AIRPORT_CODE": '', // 可选，传入机场CODE
    }
    $.ajax({
        type: 'post',
        url: "/bi/web/airportdetail",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            airportList = {};
            var list = response.airport;
            for (var i = list.length - 1; i >= 0; i--) {
                var arp = list[i];
                airportList[arp.code] = arp;
            }
        },
        error: function () { }
    });
}

function doInitMemberChart(seriesdata) {
    var chart = echarts.init(document.getElementById("jinpengChartDom"));
    var option = {

        tooltip: {
            trigger: 'item',
        },
        color: colors,
        series: [{
            name: '',
            type: 'pie',
            radius: ['65%', '90%'],
            center: ['50%', '50%'],
            data: seriesdata,
            clockwise: false,
            label: {
                normal: {
                    show: false,
                    position: 'inside',
                    formatter: '{d}%',
                    textStyle: {
                        fontSize: 10 + fontSizeDiff()
                    }
                }
            },
            labelLine: {
                normal: {
                    show: false,
                    smooth: 0.2,
                    length: 0,
                    length2: 0
                }
            },
            animationType: 'scale',
            animationEasing: 'elasticOut',
            animationDelay: function (idx) {
                return Math.random() * 200;
            }
        }]
    };

    chart.setOption(option);

}

initMemberChart();




/** 可用运力 */
function loadKeYongYunli() {


    var company = current_company_code == parent_company ? '' : current_company_code;
    var url = `/bi/spring/aircraft/getUsedAircraftStat?company=${company}`
    var dtd = $.Deferred();
    $.ajax({
        type: 'get',
        url: url,
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        success: function (response) {
            console.log(response);

            var data = response.data;
            $("#totalFlightNum").html(data.total);

            var seriesdata2 = [];
            for (var i = 0; i < data.acStat.length; i++) {
                seriesdata2.push({
                    value: data.acStat[i].cnt,
                    name: data.acStat[i].actype
                })
            }

            doInitYunliChart(seriesdata2, "yunliChartDom");
            var htmlStr2 = "";
            for (var i = 0; i < seriesdata2.length; i++) {
                if (seriesdata2[i].value > 0) {
                    htmlStr2 += ' <div class="lg-row">';
                    htmlStr2 += ' <div class="col actype">';
                    htmlStr2 += ' <div style="background-color:' + colors[i] + '">' + seriesdata2[i].name + '</div>';
                    htmlStr2 += ' </div>';
                    htmlStr2 += ' <div class="col nums">' + seriesdata2[i].value + '架</div>';
                    htmlStr2 += ' </div>';
                }
            }
            $("#yunliLegend").html(htmlStr2);
            dtd.resolve();

        }
    });

}

function loadZaiceYongli() {
    var company = current_company_code == parent_company ? '' : current_company_code;
    var url = `/bi/spring/aircraft/getRegisterdAircraftStat?company=${company}`

    $.ajax({
        type: 'get',
        url: url,
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        success: function (response) {
            console.log(response);

            var data = response.data;
            $("#totalFlightNum2").html(data.total);

            var seriesdata2 = [];
            for (var i = 0; i < data.acStat.length; i++) {
                seriesdata2.push({
                    value: data.acStat[i].cnt,
                    name: data.acStat[i].actype
                })
            }

            doInitYunliChart(seriesdata2, "yunliChartDom2");
            var htmlStr2 = "";
            for (var i = 0; i < seriesdata2.length; i++) {
                if (seriesdata2[i].value > 0) {
                    htmlStr2 += ' <div class="lg-row">';
                    htmlStr2 += ' <div class="col actype">';
                    htmlStr2 += ' <div style="background-color:' + colors[i] + '">' + seriesdata2[i].name + '</div>';
                    htmlStr2 += ' </div>';
                    htmlStr2 += ' <div class="col nums">' + seriesdata2[i].value + '架</div>';
                    htmlStr2 += ' </div>';
                }

            }
            $("#yunliLegend2").html(htmlStr2);

        }
    });
}

function doInitYunliChart(seriesdata, domId) {

    var chart = echarts.init(document.getElementById(domId));
    var option = {

        tooltip: {
            trigger: 'item',
        },
        color: colors,
        series: [{
            name: '',
            type: 'pie',
            radius: ['65%', '90%'],
            center: ['50%', '50%'],
            data: seriesdata,
            clockwise: false,
            label: {
                normal: {
                    show: false,
                    position: 'inside',
                    formatter: '{d}%',
                    textStyle: {
                        fontSize: 10 + fontSizeDiff()
                    }
                }
            },
            labelLine: {
                normal: {
                    show: false,
                    smooth: 0.2,
                    length: 0,
                    length2: 0
                }
            },
            animationType: 'scale',
            animationEasing: 'elasticOut',
            animationDelay: function (idx) {
                return Math.random() * 200;
            }
        }]
    };
    chart.setOption(option);

}

function getWeekLabel(dateId) {
    var week = dateId.substring(5, 7);
    var year = dateId.substring(0, 4);
    return `${year}年第${week}周`;
}



function getWeeks() {

    var param = { "LIMIT": 53, "DATE_TYPE_CN": "例会周" };

    $.ajax({
        type: 'post',
        url: "/bi/web/getdimdates?week",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            weeks = response.data;
            var data = response.data;
            var list = [];
            for (var i = 0; i < data.length; i++) {
                list.push({
                    data: JSON.stringify(data[i]),
                    label: getWeekLabel(data[i].DATE_ID)
                })
            }
            createComboBox('main_cb_week', list, 150, 600, onWeekChange, 1);

            // 显示 week 日期范围
            $('#main_cb_week .combobox_label').on('mouseover', function (e) {
                e.preventDefault();
                var target = $(e.currentTarget).parent();
                var data = JSON.parse(target.attr("data"))
                $('#week_date_range').text(data.DATE_DESC_XS);
                $('#week_date_range').fadeIn();
            });

            // 隐藏 week 日期范围
            $('#main_cb_week .combobox_label').on('mouseout', function (event) {
                event.preventDefault();
                $('#week_date_range').fadeOut();
            });
            $('#main_cb_week .combobox_label').on('click', function (event) {
                event.preventDefault();
                $('#week_date_range').hide();
            });

        }
    });

}

//周
function onWeekChange(weekObj) {
    doMain(JSON.parse(weekObj));
}

$(function () {
    //日
    $('#datetimepicker').datetimepicker({
        defaultDate: new Date(),
        format: "YYYY/MM/DD",
        sideBySide: true,
        minDate: '2019-01-01',
        maxDate: new Date(),
        widgetPositioning: {
            horizontal: 'right'
        },
        locale: 'zh-cn'
    });
    //月
    $('#datetimepicker_month').datetimepicker({
        defaultDate: new Date(),
        format: "YYYY/MM",
        minDate: '2019-01-01',
        viewMode: 'months',
        sideBySide: true,
        maxDate: new Date(),
        widgetPositioning: {
            horizontal: 'right'
        },
        locale: 'zh-cn'
    });
    //年
    $('#datetimepicker_year').datetimepicker({
        defaultDate: new Date(),
        format: "YYYY",
        viewMode: 'years',
        minDate: '2019-01-01',
        sideBySide: true,
        maxDate: new Date(),
        widgetPositioning: {
            horizontal: 'right'
        },
        locale: 'zh-cn'
    });

    dateCmpDtd.resolve();
    getWeeks();

    getAirportList();

    dfd.done(function () {
        $("#date_select .tab").on('click', function (e) {
            var target = $(e.currentTarget);
            if (target.hasClass("selected")) {
                return;
            }
            $("#date_select .tab").removeClass("selected");
            target.addClass("selected");
            var dateType = target.attr("data-type");
            $("#header .dateCmp .datetimepicker").hide();
            $("#header .datetimepicker_" + dateType).show();

            doMain(getCurrentDayObj());

        });

        // $(".zongshouru .detail").on('click', function (e) {

        //     var dateKey = $(".zongshouru").attr("dateKey");
        //     var dateType = $(".zongshouru").attr("dateType");

        //     if (dateKey != null && dateKey.length > 0) {
        //         var selectDate = $('#datetimepicker').data("DateTimePicker").date()._d;
        //         $("#pop_company_shouru").removeClass("hide");
        //         $("#pop_company_shouru_mask").removeClass("hide");
        //         getYunyingDetailWin(selectDate, dateType, dateKey);
        //     }


        // });

        // $("#pop_company_shouru .closeBtn").on('click', function (e) {
        //     $("#pop_company_shouru").addClass("hide");
        //     $("#pop_company_shouru_mask").addClass("hide");
        // });

        $('#datetimepicker').on("dp.change", function (e) {
            var selelctDate = formatDate(e.date._d);
            doMain(e.date._d);

        });
        $('#datetimepicker_month').on("dp.change", function (e) {
            var selelctDate = formatDate(e.date._d);
            doMain(e.date._d);
        });
        $('#datetimepicker_year').on("dp.change", function (e) {
            var selelctDate = formatDate(e.date._d);
            doMain(e.date._d);
        });
    });


});

var interval = 300000; //5分钟刷新一次数据
setInterval(function () {
    doMain($('#datetimepicker').data("DateTimePicker").date()._d);
}, interval);


/**查询所有数据入口 */
function main(DateObj) {
    // check url hash
    var hash = window.location.hash.substr(1);
    if (hash && companyCode2Name[hash] != undefined) {
        current_company_code = hash;
    } else {
        current_company_code = 'HU';
    }

    var comp_code = current_company_code;

    var len = companylist.length;
    codelist_no_parent = [];

    for (var i = 0; i < len; i++) {
        var dat = companylist[i];
        if (dat.code != parent_company && (current_company_code == parent_company || current_company_code == dat.code)) {
            codelist_no_parent.push(dat.code);
        }
    }

    doMain(DateObj);

}
//日,月,年dateObj是一个Date对象,周 dateObj是个Object对象,
//如:{"EFF_START_TIME":"2020-10-01 00:00:00.0","DATE_DESC_XS":"20201001-20201007","DATE_DESC":"20200925-20201001","EFF_END_TIME":"2020-10-07 00:00:00.0","DATE_TYPE":"例会周","DATE_ID":"*********"}

function doMain(DateObj) {
    showLoading();
    var dtd1 = getGeneralKpi();
    var dtd3 = queryShengchangeYunying(DateObj);
    var dtd4 = querySaftEventStatics(DateObj);
    var dtd5 = loadYunxingkongzhi(DateObj);
    var dtds = [dtd3];
    // dtds = dtds.concat(dtd1);
    // dtds = dtds.concat(dtd4);
    dtds = dtds.concat(dtd5);
    $.when.apply(this, dtds).done(function () {
        hideLoading();
    });
    loadKeYongYunli();
    loadZaiceYongli();
}

function formatDate(date) {
    return moment(date).format('YYYY-MM-DD');
}

function getStartDate(currentDateObj, dateType) {
    if ('Y' == dateType) {
        return moment(currentDateObj).startOf("YEAR")._d;
    } else if ("L" == dateType) {
        return moment(currentDateObj.EFF_START_TIME)._d
    } else if ("M" == dateType) {
        return moment(currentDateObj).startOf("MONTH")._d;
    } else if ("D" == dateType) {
        return currentDateObj;
    }
}
function getEndDate(currentDateObj, dateType) {
    if ('Y' == dateType) {
        return moment(currentDateObj).endOf("YEAR")._d;
    } else if ("L" == dateType) {
        return moment(currentDateObj.EFF_END_TIME)._d
    } else if ("M" == dateType) {
        return moment(currentDateObj).endOf("MONTH")._d;
    } else if ("D" == dateType) {
        return currentDateObj;
    }
}

function getDateTypeCn(datetype) {
    var desc = "";
    switch (datetype) {
        case "D":
            desc = "日"
            break;
        case "L":
            desc = "例会周"
            break;
        case "M":
            desc = "月"
            break;
        case "Y":
            desc = "年"
            break;
        default:

    }
    return desc;
}

function getItemData(kpiCode, data) {
    if (data[current_company_code] != null) {
        var list = data[current_company_code][kpiCode];
        if (list != null && list.length > 0) {
            return list[0]
        }
        return null;
    }
    return null;

}

function getItemDataByDateKey(kpiCode, data, dateKey) {
    if (data[current_company_code] != null) {
      var list = data[current_company_code][kpiCode];
      if (list && kpiCode == 'NORMAL_RATE_ZT') {
        return  list[0];
      }else if (list != null && list.length > 0) {
            for (var i = 0; i < list.length; i++) {
                if (list[i].DATE_ID == dateKey) {
                    return list[i];
                }
            }
        }
        return null;
    }
    return null;

}

function renderKpi(kpiCode, data, dateKey, divNum, precision, dateType, unit, showDateDesc) {

    var item = getItemDataByDateKey(kpiCode, data, dateKey);
    $("." + kpiCode + " .huanbiValue").removeClass("down", "up");
    $("." + kpiCode + " .tongbiValue").removeClass("down", "up");
    if (item != null) {
        var value = toFixed(item.KPI_VALUE / (divNum ? divNum : 1), 1);
        var hbValue = item.KPI_RATIO_SQ;
        var tbValue = item.KPI_RATIO_TQ;

        if (hbValue < 0) {
            $("." + kpiCode + " .huanbiValue").addClass("down")
        }
        if (tbValue < 0) {
            $("." + kpiCode + " .tongbiValue").addClass("down")
        }
        var hb = toFixed(hbValue * 100, 1) + "%";
        if (hbValue < 0) {
            hb += "↓"
        } else if (hbValue > 0) {
            hb += "↑"
        }
        var tb = toFixed(tbValue * 100, 1) + "%";
        if (tbValue < 0) {
            tb += "↓"
        } else if (tbValue > 0) {
            tb += "↑"
        }

        $("." + kpiCode + " .singlekpivalue").html(getFormatKpi(dateKey, kpiCode, data, precision, unit, dateType, showDateDesc, false, divNum));
        $("." + kpiCode + " .huanbiValue").html(hb);
        $("." + kpiCode + " .tongbiValue").html(tb);
    } else {
        $("." + kpiCode + " .singlekpivalue").html("-");
        $("." + kpiCode + " .huanbiValue").html("-");
        $("." + kpiCode + " .tongbiValue").html("-");
    }

}

function getKpiValue(kpiCode, data) {
    return getItemDataNonull(kpiCode, data).KPI_VALUE;
}

function getFormatKpi(dateKey, kpiCode, data, precision, unit, dateType, showDateDesc, isRadio, divNum) {
    var item = getItemDataByDateKey(kpiCode, data, dateKey);
    var beishu = 1;
    if (isRadio) {
        beishu = 100;
    }
    if (item != null) {
        var tmpValue = item.KPI_VALUE * beishu;
        if (divNum > 0) {
            tmpValue = tmpValue / divNum;
        }
        var value = toFixed(tmpValue, precision);
        var dateDesc = "";

        if (showDateDesc) {
            if (kpiCode == 'AUX_INC' || kpiCode == 'ROUTE_SUBSIDY_INC') {
                dateDesc = '<span  class="dateDesc"></span>';
            } else {
                if (item.DATA_ORDER !== '1') {
                    dateDesc = '<span  class="dateDesc">(' + getTimeDesc(dateType, item.DATA_ORDER) + ')</span>'
                }
            }

        }

        return value + '<span class="kpiunit">' + unit + '</span>' + dateDesc;
    }
    return '-';

}

function getYunxingFormatKpi(kpiCode, data, precision, unit, dateType, showDateDesc, isRadio, divNum) {
    var item = getItemData(kpiCode, data);
    var beishu = 1;
    if (isRadio) {
        beishu = 100;
    }
    if (item != null) {
        var tmpValue = item.KPI_VALUE * beishu;
        if (divNum > 0) {
            tmpValue = tmpValue / divNum;
        }
        var value = toFixed(tmpValue, precision);
        var dateDesc = "";
        if (showDateDesc && item.DATA_ORDER !== '1') {
            dateDesc = '<span  class="dateDesc">(' + getTimeDesc(dateType, item.DATA_ORDER) + ')</span>'
        }
        return value + '<span class="kpiunit">' + unit + '</span>' + dateDesc;
    }
    return '-';

}
/** 默认返回三条,DATA_ORDER = 1 表示当前日期周期,2 上期,3 上上期 */
function getTimeDesc(dateType, dateOrdder) {

    if (dateOrdder == 2) {
        if (dateType == 'D') {
            return 'T-1'
        } else if (dateType == 'L') {
            return '上周'
        } else if (dateType == 'M') {
            return '上月'
        } else if (dateType == 'Y') {
            return '上年'
        }
    } else if (dateOrdder == 3) {
        if (dateType == 'D') {
            return 'T-2'
        } else if (dateType == 'L') {
            return '上上周'
        } else if (dateType == 'M') {
            return '上上月'
        } else if (dateType == 'Y') {
            return '上上年'
        }
    }
}


/** 查询右边生产运营 */
function queryShengchangeYunying(date) {
    var dateType = getDateType();
    /**
     * 展示要显示的日期,以客收和货收当前最小的有数据的日期为谁
     */
    var getDateKey = function (data, dateType) {
        var d1 = getItemDataNonull('EST_INC_FUEL', data).DATE_ID;
        var d2 = getItemDataNonull('CARGO_INC', data).DATE_ID;
        if (d1 == undefined) {
            return d2;
        }
        if (d2 == undefined) {
            return d1;
        }
        return d1 < d2 ? d1 : d2;

    }
    var kpi_list = [
        "TOTAL_INC",//总收入
        "CARGO_INC",//货运收入
        "ROUTE_SUBSIDY_INC",//补贴收入
        "AUX_INC",//辅营收入
        //同环比
        "TRV_KILO_INC_FUEL", //客公里收入(含燃油)(元)
        'CAP_KILO_INC_FUEL', //座收(含燃油),

        'EST_INC_FUEL', //客收(元),
        'CARGO_INC', //货收(元),

        //新增的只有值
        'AVG_TKT_PRICE', //平均票价
        'INL_AVG_TKT_PRICE', //国内平均票价
        'INT_AVG_TKT_PRICE', //国际平均票价
        'HOUR_INC_FUEL', //小时收(含燃油)
        "W_HOUR_INC_FUEL",// 宽体机小时收入
        "N_HOUR_INC_FUEL",// 窄体机小时收入,

        "CARGO_INC_BELLY",//货运-腹舱收入
        "CARGO_INC_FULL",//货运-全货机收入
        "CARGO_INC_PASS"//货运-客拉货收入


    ];

    var param = {
        "LIMIT": 3,
        "QUERY_DATE": formatDate(getEndDate(date, dateType)),
        "DATE_TYPE_CN": getDateTypeCn(dateType),// 例会周L、年Y、月M、日D
        "DATE_TYPE": dateType,
        'KPI_CODE': kpi_list.join(','),
        'COMP_CODE': current_company_code
    }
    return $.ajax({
        type: 'post',
        url: "/bi/query/getfaccomkpi?yunying",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            var data = response.data;

            var dateKey = getDateKey(data);

            $(".zongshouru").attr("dateKey", dateKey);
            $(".zongshouru").attr("dateType", dateType);
            var item = getItemDataByDateKey('TOTAL_INC', data, dateKey);

            if (item != null) {
                $(".zongshouru .shouruValue").html(toFixed(item.KPI_VALUE, 0));
                var icon = "";
                $(".zongshouru .tongbiValue").removeClass("down", "up");
                if (item.KPI_RATIO_TQ < 0) {
                    icon = "↓"
                    $(".zongshouru .tongbiValue").addClass("down");
                } else if (item.KPI_RATIO_TQ > 0) {
                    icon = "↑"
                    $(".zongshouru .tongbiValue").addClass("up");
                }
                $(".zongshouru .tongbiValue").html(toFixed(item.KPI_RATIO_TQ * 100, 2) + "%" + icon);
            } else {
                $(".zongshouru .shouruValue").html("-");
                $(".zongshouru .tongbiValue").removeClass("down", "up");
                $(".zongshouru .tongbiValue").html("-");
            }
            //客收
            //kpiCode, data,dateKey, divNum,precision,dateType,unit,showDateDesc
            renderKpi("EST_INC_FUEL", data, dateKey, 1, 0, dateType, '万', true);
            renderKpi("CARGO_INC", data, dateKey, 1, 0, dateType, '万', true);
            renderKpi("AUX_INC", data, dateKey, 1, 0, dateType, '万', true);
            renderKpi("ROUTE_SUBSIDY_INC", data, dateKey, 1, 0, dateType, '万', true);


            renderKpi("TRV_KILO_INC_FUEL", data, dateKey, 1, 3, dateType, '元', true);
            renderKpi("CAP_KILO_INC_FUEL", data, dateKey, 1, 3, dateType, '元', true);

            renderKpi("CARGO_INC_BELLY", data, dateKey, 1, 0, dateType, '万元', true);
            renderKpi("CARGO_INC_FULL", data, dateKey, 1, 0, dateType, '万元', true);
            renderKpi("CARGO_INC_PASS", data, dateKey, 1, 0, dateType, '万元', true);

            $("#AVG_TKT_PRICE").html(getFormatKpi(dateKey, 'AVG_TKT_PRICE', data, 0, '元', dateType, true));//平均票价
            $("#INL_AVG_TKT_PRICE").html(getFormatKpi(dateKey, 'INL_AVG_TKT_PRICE', data, 0, '', dateType, false));//国内平均票价
            $("#INT_AVG_TKT_PRICE").html(getFormatKpi(dateKey, 'INT_AVG_TKT_PRICE', data, 0, '', dateType, false));//国际平均票价

            $("#HOUR_INC_FUEL").html(getFormatKpi(dateKey, 'HOUR_INC_FUEL', data, 2, '万', dateType, true));//小时收(含燃油)
            $("#W_HOUR_INC_FUEL").html(getFormatKpi(dateKey, 'W_HOUR_INC_FUEL', data, 2, '', dateType, false));//宽体机小时收入
            $("#N_HOUR_INC_FUEL").html(getFormatKpi(dateKey, 'N_HOUR_INC_FUEL', data, 2, '', dateType, false));//窄体机小时收入票价

        }
    });





}

function getDateType() {
    return $("#date_select .selected").attr("data-type");
}


var websocket = null;
//判断当前浏览器是否支持WebSocket
// initSocket();

function initSocket() {
    let host = window.location.host.split(":");
    let protocol = window.location.protocol;
    let ws = protocol === 'https:' ? 'wss' : 'ws';

    function initUserinfo() {
        if ('WebSocket' in window) {
            if (userinfo) {
                if (host[0] === "vis.hnair.net") {
                    websocket = new WebSocket(`${ws}://${host[0]}:8280/websocket/yunying-${userinfo.id}`);
                } else {
                    websocket = new WebSocket(`${ws}://${host[0]}:8888/websocket/yunying-${userinfo.id}`);
                }
                //连接发生错误的回调方法
                websocket.onerror = function () {
                    console.log("WebSocket连接发生错误");
                };

                //连接成功建立的回调方法
                websocket.onopen = function () {
                    console.log("WebSocket连接成功");
                }

                //接收到消息的回调方法
                websocket.onmessage = function (event) {
                    eval(event.data);
                }

                //连接关闭的回调方法
                websocket.onclose = function () {
                    console.log("WebSocket连接关闭");
                }

                //监听窗口关闭事件，当窗口关闭时，主动去关闭websocket连接，防止连接还没断开就关闭窗口，server端会抛异常。
                window.onbeforeunload = function () {
                    websocket.close();
                }
            } else {
                setTimeout(initUserinfo, 10);
            }
        } else {
            alert('当前浏览器 Not support websocket');
        }
    }

    initUserinfo();
}

function getAllSubCompany() {
    var rs = [];
    var len = companylist.length;
    for (var i = 0; i < len; i++) {
        var dat = companylist[i];
        if (dat.code != parent_company && dat.code != 'HNAIN') {
            rs.push(dat.code);
        }
    }
    return rs
}
/**所有公司,排除金鹏,境内航司 */
function getAllSubCompanyExcludeY8AndHnaIn() {
    var rs = [];
    var len = companylist.length;
    for (var i = 0; i < len; i++) {
        var dat = companylist[i];
        if (dat.code != parent_company && dat.code != 'Y8' && "HNAIN" != dat.code) {
            rs.push(dat.code);
        }
    }
    return rs
}
/**  是否排除境内航司 */
function getAllCompany(excludeHnaIn) {
    var rs = [];
    var len = companylist.length;
    for (var i = 0; i < len; i++) {
        var dat = companylist[i];
        if (excludeHnaIn) {
            if ("HNAIN" != dat.code) {
                rs.push(dat.code);
            }
        } else {
            rs.push(dat.code);
        }

    }
    return rs
}
/** 所有 companyCode 三字码  */
function getCompanyCodes() {
    var currentYhCode = "";

    var len = companylist.length;
    var codelist = [];
    for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        if (dat.code != parent_company) {
            codelist.push(dat.yhscode);
        }
        if (current_company_code == dat.code) {
            currentYhCode = dat.yhscode
        }
    }
    return current_company_code == parent_company ? codelist.join(',') : currentYhCode;
}

/**
 * 展示要显示的日期,以客收和货收当前最小的有数据的日期为谁
 */
var getComplainDateKey = function (data, dateType) {
    var d1 = getItemDataNonull('TRV_NUM', data).DATE_ID;
    var d2 = getItemDataNonull('CAACCOUNT', data).DATE_ID;
    if (d1 == undefined) {
        return d2;
    }
    if (d2 == undefined) {
        return d1;
    }
    return d1 < d2 ? d1 : d2;
}
/** 设置局方投诉率 */
function setComplainValue(data, dateType) {
    var dateKey = getComplainDateKey(data, dateType);
    var item_trv_num = getItemDataByDateKey('TRV_NUM', data, dateKey);
    var item_trv_caac = getItemDataByDateKey('CAACCOUNT', data, dateKey);
    var tousulv = "-";

    if (item_trv_num != null) {
        if (item_trv_num.KPI_VALUE > 0) {
            tousulv = toFixed(item_trv_caac.KPI_VALUE * 10000 / item_trv_num.KPI_VALUE, 2)
        }
        var dateDesc = "";

        if (item_trv_num != null && item_trv_num.DATA_ORDER !== '1') {
            dateDesc = '<span  class="dateDesc">(' + getTimeDesc(dateType, item_trv_num.DATA_ORDER) + ')</span>'
        }
        var htmlstr = `${tousulv}<span class="kpiunit">‱</span>${dateDesc}`;
        $("#val_jf_ts").html(htmlstr);
    } else {
        $("#val_jf_ts").html("-");
    }


}
/** 取dateId */
function getDateIdByKpiCode(data, kpiCode) {
    var item = getItemData(kpiCode, data);
    if (item != null) {
        return item.DATE_ID;
    }
    return null;
}

/**运行控制和 不正常航班 */
function loadYunxingkongzhi(date) {
    var rDtd = $.Deferred();
    $("#EXCUTED_NO_INL").html("-");
    $("#SCH_NO_INL").html("-");
    $("#EXCUTED_NO_INT").html("-");
    $("#SCH_NO_INT").html("-");
    $("#EXCUTED_NO").html("-");
    $("#schFligntNum").html("-");
    $('#val_PLF_ac').html("-");
    $('#val_CKI_NUM').html("-");
    $('#N_AC_UTIL_RATE').html("-");
    $('#W_AC_UTIL_RATE').html("-");
    $('#R_AC_UTIL_RATE').html("-");
    $('#val_NORMAL_RATE').text("-");
    $('#hb_NORMAL_RATE').text("-");
    $("#CARGO_VOLUME").html("-");
    var dateType = getDateType();

    var kpi_list = [
        //同环比
        // "SHIFTS",//航班班次
        "NORMAL_RATE_ZT",//正常班次
        "EXCUTED_NO_INL",//国内已执行
        "SCH_NO_INL",//国内计划班次
        "EXCUTED_NO_INT",//国际已执行
        "SCH_NO_INT",//国际计划班次
        'EXCUTED_NO', //执行班次
        'SCH_NO', //计划班次
        // 'CKI_NUM', //已执行运输旅客量
        // "NORMAL_NO_T",//正常班次
  
        "TRV_RATE",//客座率
        'TRV_NUM', //旅客量,
        'CANCEL_NO', //取消班次
        'DIV_NO', //备降班次
        'TURNBACK_NO', //返航班次
        'CARGO_VOLUME', //货运-货油运输量
        'N_AC_UTIL_RATE', //窄体机日利用率
        'W_AC_UTIL_RATE', //宽体机日利用率
        'B_AC_UTIL_RATE', //支线机日利用率
        // 'FLY_TIME', //飞行小时
        'CAACCOUNT', //局方投诉举报量
        'DELAY_NO_240', //延误4小时
    ];

    var deferYuxing = $.Deferred();
    var dtd_flightBase = $.Deferred();
    var dtd_trvNum = $.Deferred();
    var data = [];

    var param = {
        "LIMIT": 3,
        "QUERY_DATE": formatDate(getEndDate(date, dateType)),
        "DATE_TYPE_CN": getDateTypeCn(dateType),// 例会周L、年Y、月M、日D
        "DATE_TYPE": dateType,
        'KPI_CODE': kpi_list.join(','),
        'COMP_CODE': current_company_code
    }
    $.ajax({
        type: 'post',
        url: "/bi/query/getfaccomkpi?yunxingkongzhi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            data = response.data;
            yunxingKpiDataList = response.data;
            deferYuxing.resolve();
        }
    });




    deferYuxing.done(function () {
        $("#CARGO_VOLUME").html(getYunxingFormatKpi('CARGO_VOLUME', data, 0, 't', dateType, true));
        $('#val_PLF_ac').html(getYunxingFormatKpi('TRV_RATE', data, 2, '%', dateType, true, true));
        $('#N_AC_UTIL_RATE').html(getYunxingFormatKpi('N_AC_UTIL_RATE', data, 2, '', dateType, true, false));
        $('#W_AC_UTIL_RATE').html(getYunxingFormatKpi('W_AC_UTIL_RATE', data, 2, '', dateType, true, false));
        $('#R_AC_UTIL_RATE').html(getYunxingFormatKpi('B_AC_UTIL_RATE', data, 2, '', dateType, true, false));
        var rateDateId = getDateIdByKpiCode(data, "N_AC_UTIL_RATE");
        if (rateDateId != null) {
            $(".rlyl .detail, .kzl .detail ").attr("dateId", rateDateId);
        }
        queryFlightBaseInfo(data, date, dtd_flightBase);

        if (dateType == 'D' && isToday()) {
            queryTrvNum(dtd_trvNum);
        } else {
            $("#EXCUTED_NO_INL").html(getYunxingFormatKpi('EXCUTED_NO_INL', data, 0, '', dateType, false));
            $("#SCH_NO_INL").html(getYunxingFormatKpi('SCH_NO_INL', data, 0, '', dateType, false));
            $("#EXCUTED_NO_INT").html(getYunxingFormatKpi('EXCUTED_NO_INT', data, 0, '', dateType, false));
            $("#SCH_NO_INT").html(getYunxingFormatKpi('SCH_NO_INT', data, 0, '', dateType, false));
            $("#EXCUTED_NO").html(getYunxingFormatKpi('EXCUTED_NO', data, 0, '', dateType, false));
            $("#schFligntNum").html(getYunxingFormatKpi('SCH_NO', data, 0, '', dateType, false));
            $('#val_CKI_NUM').html(getYunxingFormatKpi('TRV_NUM', data, 2, '', dateType, true, false, 10000));
            var trvRateDateId = getDateIdByKpiCode(data, "TRV_RATE");
            if (trvRateDateId != null) {
                $(".passenger .detail,.huoyounums ,detail ").attr("dateId", trvRateDateId);
            }
            dtd_trvNum.resolve();//之前漏了
        }
        try {
            setComplainValue(data, dateType);
        } catch (error) {
            console.error(error);
        }

        rDtd.resolve();
    });
    return [rDtd, dtd_flightBase, dtd_trvNum];
}
/**
 * 查询当日旅客量
 */
// 计划旅客量: bookNum
// 国内计划旅客量：bookNumL
// 国际计划旅客量：bookNumI
// 实际旅客量：ckiNum
// 国内实际旅客量：ckiNumL
// 国际实际旅客量：ckiNumI
function queryTrvNum(dtd_trvNum) {
    var companyCode = current_company_code == 'HNAHK' ? getAllSubCompany().join(",") : current_company_code;
    var startTime = moment().startOf("day").utc().format('YYYY-MM-DD HH:mm:ss');
    var endDate = moment().endOf("day").utc().format('YYYY-MM-DD HH:mm:ss');
    var params = {
        "companyCodes": companyCode,
        "stdStart": startTime,
        "stdEnd": endDate
    }

    $.ajax({
        type: 'post',
        url: "/bi/web/getPsrNumStatistics",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(params),
        success: function (response) {
            var data = response.data;// 按公司分组输出，具体看各个对象输出字段
            if (data == null) {
                dtd_trvNum.resolve();
                return;
            }
            var companyData = data[current_company_code];
            if (companyData != undefined) {
                var clickNum = formatNumber(companyData['ckiNum'] / 10000, 2);
                var bookNum = formatNumber(companyData['bookNum'] / 10000, 2);
                $('#val_CKI_NUM').html(`${clickNum}(${bookNum})`)
                $(".passenger .detail,.huoyounums ,detail ").attr("dateId", moment().format('YYYYMMDD'));
            } else {
                $('#val_CKI_NUM').html(`-`)
            }

            dtd_trvNum.resolve();
        }
    });
}

// ------------------------------------------------------------------------
// 今日客座率=今日旅客总量/今日计划航班布局总量，
// ------------------------------------------------------------------------
function getPLF(date) {
    $('#val_PLF_ac').text('loading...');

    //a.旅客总量
    var mm = date.getMonth() + 1;
    var dd = date.getDate();
    if (mm < 10) {
        mm = '0' + mm;
    }
    if (dd < 10) {
        dd = '0' + dd;
    }
    var h = date.getUTCHours();
    var m = date.getUTCMinutes();
    var s = date.getUTCSeconds();

    var today = date.getFullYear() + '-' + mm + '-' + dd;
    var stdEndUtcTime = date.getFullYear() + '-' + mm + '-' + dd + ' 15:59:59';
    var yesterday_ts = date.getTime() - 86400000;
    date.setTime(yesterday_ts);
    mm = date.getMonth() + 1;
    dd = date.getDate();
    if (mm < 10) {
        mm = '0' + mm;
    }
    if (dd < 10) {
        dd = '0' + dd;
    }
    var stdStartUtcTime = date.getFullYear() + '-' + mm + '-' + dd + ' 16:00:00';
    var utcTimeNow = moment.utc().format(('YYYY-MM-DD HH:mm:ss'));



    var planNum = 0;//旅客总量
    var plfLongReg = [];//STD+飞机号
    var sLongReg = [];//飞机号去重
    var plfRegNoSTD = [];//飞机号

    var param = {
        "companyCodes": codelist_no_parent.join(','),
        "stdStart": stdStartUtcTime,
        "stdEnd": stdEndUtcTime
    }


    $.ajax({
        type: 'post',
        url: "/bi/web/psrsumminfo",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            var PLFList = response.data;
            // console.log("飞机号列表数",PLFList.length);
            if (PLFList.length > 0) {
                var plfTemparr = PLFList;

                ////1. 取所有数组，2.逐条拼装日期部分+AC号，然后map到新数组   3.对2进行去重，等到周期的飞机数

                //STD+飞机号
                plfLongReg = plfTemparr.map(v => v.STD + v.LONG_REG);
                // console.log("STD+飞机号",plfLongReg.length,plfLongReg);

                //飞机号
                plfRegNoSTD = plfTemparr.map(v => v.LONG_REG);
                // console.log("飞机号",plfRegNoSTD.length,plfRegNoSTD);
                //飞机号去重
                sLongReg = Array.from(new Set(plfRegNoSTD));
                // console.log('飞机号去重',sLongReg.length,sLongReg);

                //找出重复的个数
                var counts = {};
                plfRegNoSTD.forEach(function (item) {
                    counts[item] = (counts[item] || 0) + 1;
                });


                //统计旅客总量
                var plfListLen = PLFList.length - 1;
                for (var i = plfListLen; i >= 0; i--) {
                    planNum += PLFList[i]['PLAN_NUM'] * 1;
                }

                //取布局
                getACNum(sLongReg, counts, planNum);

            } else {
                $('#val_PLF_ac').text('-');
            }
        },
        error: function () { }
    });

}
function getACNum(sLongReg, counts, planNum) {

    var totalACNum = 0;//旅客总量
    var totalNum = 0;//座仓数C+W+Y
    var param = {
        "longNoList": sLongReg.join(',')
    }
    $.ajax({
        type: 'post',
        url: "/bi/web/getAcAircraftList",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            var acList = response.data;
            // console.log("acList.length",acList.length);
            if (acList.length > 0) {
                acList.forEach((v, i) => {
                    //取匹配的longNo
                    // if(counts[v.data.longNo]!=undefined){
                    // 	console.log(counts[v.data.longNo]);
                    // }


                    if (v.data.cabin != '' && counts[v.data.longNo] != undefined) {


                        var totalNum = 0;

                        var numStr = v.data.cabin.split(/[A-Za-z]+/);

                        numStr.forEach((valueStr, i) => {
                            if (valueStr != null && valueStr != "") {
                                totalNum += valueStr * 1;
                            }
                        });

                        if (totalNum > 0) {
                            totalACNum += totalNum * counts[v.data.longNo];
                            // console.log(totalNum,counts[v.data.longNo],totalACNum);
                        }
                    }


                });
                // console.log("旅客总量planNum",planNum,"旅客总量totalACNum",totalACNum,"今日客座率",(planNum/totalACNum*100).toFixed(2))
                if (totalACNum > 0) {
                    var plfRate = (planNum / totalACNum * 100).toFixed(2);
                    $('#val_PLF_ac').html(plfRate + '<span class="kpiunit">%</span>');
                } else {
                    $('#val_PLF_ac').html("-");
                }

            } else {
                $('#val_PLF_ac').html('-');
            }
        },
        error: function () { }
    });
}

function getItemDataNonull(kpiCode, data) {
    var r = getItemData(kpiCode, data);
    if (r != null) {
        return r;
    }
    return {};

}


/**航班正常率 */
function flightNormalRate(data, date_type) {
    if (date_type == 'D' & isToday()) {
        var stats = all_company_data['flt_sts'];
        $('#val_NORMAL_RATE').html(toFixed(stats.cfdappPercent, 1) + '% (' + toFixed(stats.pfdappPercent, 1) + '%)');

        var dateKey = moment().subtract(1, 'day').format("YYYYMMDD");
        //取上期正常率
        // var sch_total = getItemDataByDateKey("SCH_NO", data, dateKey).KPI_VALUE;
        // var nor_total = getItemDataByDateKey('NORMAL_NO_T', data, dateKey).KPI_VALUE;
        // var normal_rate_sq = sch_total > 0 ? nor_total * 100 / sch_total : 0.0;
        var normal_rate_sq  = Number(getItemDataByDateKey('NORMAL_RATE_ZT', data, dateKey).KPI_VALUE)*100

        // 环比=本期-上期
        var hb = Number(stats.pfdappPercent) - Number(normal_rate_sq);

        $('#hb_NORMAL_RATE').text(toFixed(hb, 1) + '%' + (hb > 0 ? '↑' : '↓'));

        $('#hb_NORMAL_RATE').removeClass('red green');

        if (hb > 0) {
            $('#hb_NORMAL_RATE').addClass('green');
        } else if (hb < 0) {
            $('#hb_NORMAL_RATE').addClass('red');
        }
        $(".nomflght .detail").attr("dateId", moment().format("YYYYMMDD"));
        $(".nomflght .header .subheader").removeClass("hide")


    } else {
        $(".nomflght .header .subheader").addClass("hide")
        var dateKey = getItemDataNonull('NORMAL_RATE_ZT', data).DATE_ID;
        $(".nomflght .detail").attr("dateId", dateKey);

        var item4DateDesc = getItemDataByDateKey('NORMAL_RATE_ZT', data, dateKey);
        var dateDesc = "";
        if (item4DateDesc != null && item4DateDesc.DATA_ORDER !== '1') {
            dateDesc = '<span  class="dateDesc">(' + getTimeDesc(date_type, item4DateDesc.DATA_ORDER) + ')</span>'
        }

        // var sch_total = getItemDataByDateKey("SCH_NO", data, dateKey).KPI_VALUE;
        // var nor_total = getItemDataByDateKey('NORMAL_NO_T', data, dateKey).KPI_VALUE;

        // var sch_sq = getItemDataByDateKey('SCH_NO', data, dateKey).KPI_VALUE_SQ;
        // var nor_sq = getItemDataByDateKey('NORMAL_NO_T', data, dateKey).KPI_VALUE_SQ;

        
        // 如果是天获取接口数据
        // if (date_type == 'D') {
        //     sch_total = Number(all_company_data['flt_sts'].pftc);
        //     nor_total = Number(all_company_data['flt_sts'].pfrtc);
        //     sch_sq = Number(all_company_data['flt_sts_sq'].pftc);
        //     nor_sq = Number(all_company_data['flt_sts_sq'].pfrtc);
        // }
        // 正常率 ------------------------------------------------------
        // var normal_rate = sch_total > 0 ? nor_total * 100 / sch_total : 0.0;

        var normal_rate =  Number(getItemDataByDateKey('NORMAL_RATE_ZT', data, dateKey).KPI_VALUE*100).toFixed(1);

        // 上期正常率
        // var normal_rate_sq = sch_sq > 0 ? nor_sq * 100 / sch_sq : 0.0;


        // 环比=本期-上期
        var hb = Number(getItemDataByDateKey('NORMAL_RATE_ZT', data, dateKey).KPI_RATIO_SQ*100).toFixed(1);

        $('#val_NORMAL_RATE').html(toFixed(normal_rate, 1) + '%' + dateDesc);
        $('#hb_NORMAL_RATE').text(toFixed(hb, 1) + '%' + (hb > 0 ? '↑' : '↓'));

        $('#hb_NORMAL_RATE').removeClass('red green');

        if (hb > 0) {
            $('#hb_NORMAL_RATE').addClass('green');
        } else if (hb < 0) {
            $('#hb_NORMAL_RATE').addClass('red');
        }
    }





}



/** 航班基本信息 + 航班正常率计算调用 */

function queryFlightBaseInfo(kpiDataList, dateObj, defer1) {

    var dtd = $.Deferred();
    var dateType = getDateType();
    if (dateType == 'D') {
        var selectedDate = dateObj;
        var date = moment(selectedDate);
        var stdEndUtcTime = date.format('YYYY-MM-DD') + ' 15:59:59';
        // 同期 前一天
        date.subtract(1, 'day');
        var stdStartUtcTime = date.format('YYYY-MM-DD') + ' 16:00:00';

        /*
        flightAmountStatic 只能取当日数据
        getFocDailySummaryInfo 只能取历史数据，不包含延误1-2，2-4的数据
        历史日的数据用getFocDailySummaryInfo接口，不显示延误1-2，2-4的
        */

        /*
        qftc 取消航班总数
        qftc0 昨日取消航班总数
        qftc1 今日取消航班总数
        qftc2 次日取消航班总数
        */



        if (isToday()) {

            var param = {
                "stdStartUtcTime": stdStartUtcTime,
                "stdEndUtcTime": stdEndUtcTime,
                "companyCodes": codelist_no_parent.join(','), //
                "isLongRegsNotIn": (current_company_code == parent_company || current_company_code == 'Y8'),
                "acOwners": 'Y8',
                "acTypeNotIn": 'D00,D10', //需要过滤掉的机型
                "AcTypeList": "",
                "depstns": "",
                "arrstns": ""
            }

            $.ajax({
                type: 'post',
                url: "/bi/web/flightAmountStaticV2",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {

                    // 计划航班总数	pftc
                    // 国际计划航班总数	pftci
                    // 国内计划航班总数	pftcl
                    // 执行航班总数	cftc
                    // 国际执行航班总数	cftci
                    // 国内执行航班总数	cftcl
                    $("#EXCUTED_NO_INL").html(response.cftcl);
                    $("#SCH_NO_INL").html(response.pftcl);
                    $("#EXCUTED_NO_INT").html(response.cftci);
                    $("#SCH_NO_INT").html(response.pftci);
                    $("#EXCUTED_NO").html(response.cftc);
                    $("#schFligntNum").html(response.pftc);
                    all_company_data['flt_sts'] = response;

                    dtd.resolve();

                },
                error: function () { }
            });

        } else {

            var startDate = formatDate(getStartDate(selectedDate, dateType));
            var endDate = formatDate(getEndDate(selectedDate, dateType));

            var param = {
                "companyCodes": codelist_no_parent.join(','),
                "fltDateStart": startDate,
                "fltDateEnd": endDate,
                "deleteds": 0,
                "groupByCompany": 'false',
            }

            $.ajax({
                type: 'post',
                url: "/bi/web/getFocDailySummaryInfo",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    response.data[0].qftc1 = response.data[0].qftc; //取消
                    response.data[0].dftc = response.data[0].dbftc; // 返航备降是加在一起的，这里只能这样分开了，返航设为0
                    response.data[0].bftc = 0;
                    all_company_data['flt_sts'] = response.data[0];
                    dtd.resolve();
                    console.log("defer1  resolve>>>>>>>>>>>>>")
                },
                error: function () { }
            });
        }




        // $.when(defer1, defer2).then(function () {
        //     console.log("defer1，defer2 $.when >>>>>>>>>>>>>")
        //     flightNormalRate(kpiDataList, dateType);
        //     yunxingAbnormalFlightStatics(kpiDataList, dateType, dateObj);

        // }).fail(function () { });

    } else {
        dtd.resolve();
    }

    dtd.done(function () {
        try {
            flightNormalRate(kpiDataList, dateType);
        } catch (error) {
            console.log(error);
        }

        yunxingAbnormalFlightStatics(kpiDataList, dateType, dateObj);
        defer1.resolve();

    });

}

function isToday() {
    return moment($('#datetimepicker').data("DateTimePicker").date()._d).format('YYYY-MM-DD') == moment().format('YYYY-MM-DD');
}

function formatNumber(kpivalue, precision, nullAsZero) {
    if (kpivalue != null) {
        return toFixed(kpivalue, precision);
    }

    return nullAsZero ? toFixed(0, precision) : '-';
}
//运行不正常
function yunxingAbnormalFlightStatics(data, date_type, dateObj) {


    // 运行不正常 ------------------------------------------------------
    var cancelNo = getKpiValue("CANCEL_NO", data);
    var divNO = getKpiValue("DIV_NO", data);
    var turnBackNo = getKpiValue("TURNBACK_NO", data);
    var delayNo240 = getKpiValue("DELAY_NO_240", data);
    if (date_type == 'D') {
        cancelNo = Number(all_company_data['flt_sts'].qftc1);
        divNO = Number(all_company_data['flt_sts'].dftc);
        turnBackNo = Number(all_company_data['flt_sts'].bftc);
        delayNo240 = Number(all_company_data['flt_sts'].pfdtc4);
    }

    // 取消 //////////
    var cancel = formatNumber(cancelNo, 0, false);
    $('#val_CANCEL').text(cancel);

    // 返航备降 //////////
    var fhbj = 0 + (divNO ? Number(divNO) : 0) + (turnBackNo ? Number(turnBackNo) : 0)
    $('#val_TURNBACK_NO').text(toFixed(fhbj, 0));


    // 延误1-2 //////////
    if (date_type == 'D' && isToday()) {
        var stsdat = all_company_data['flt_sts'];
        $('#val_DELAY_NO_1_2').text(stsdat.pfdtc12);
        $('#val_DELAY_NO_2_4').text(stsdat.pfdtc24);

        $('.delay12').show()
        $('.delay24').show()
    } else {
        $('#val_DELAY_NO_1_2').text('');
        $('#val_DELAY_NO_2_4').text('');

        $('.delay12').hide()
        $('.delay24').hide()
    }

    // 延误>4 //////////
    var delay4 = formatNumber(delayNo240, 0, false);
    $('#val_DELAY_NO_240').text(delay4);

}
// ------------------------------------------------------------------------
// 运行非正常统计列表
// ------------------------------------------------------------------------
//公司三字码
function getYhCode() {
    if (current_company_code == parent_company) {
        return 'ALL';
    }
    for (var i = 0; i < companylist.length; i++) {
        var dat = companylist[i];
        if (current_company_code == dat.code) {
            return dat.yhscode;;
        }
    }
    return null;
}


var cargoCompany = [{
    id: 100400,
    name: '香港货航'
}, {
    id: 101300,
    name: '金鹏货运'
}, {
    id: 101900,
    name: '天津货航'
}, {
    id: 900,
    name: '客拉货'
}, {
    id: 102200,
    name: '海航货运'
}, {
    id: 100,
    name: '货运合计'
}];
/** 取得所在公司顺序 */
function getCompanyIndex(code) {
    for (var i = 0; i < companylist.length; i++) {
        if (companylist[i].code == code) {
            return i;
        }
    }
    return companylist.length + 1;
}

/** 利用率 */
function getDailyRate(date) {

    var dateType = getDateType();
    var startBjTime = formatDate(getStartDate(date, dateType));
    var endBjTime = formatDate(getEndDate(date, dateType));

    var DRcompCode = '';
    if (current_company_code != parent_company) {
        DRcompCode = current_company_code;
    }

    $('#N_AC_UTIL_RATE').text('loading...');
    $('#W_AC_UTIL_RATE').text('loading...');
    $('#R_AC_UTIL_RATE').text('loading...');
    if (startBjTime == endBjTime) {
        var param = {
            "acOwner": DRcompCode,
            "datop": startBjTime,
            "status": 'ATA,ARR,NDR,ATD,DEP,RTR', // ATA到达ARR 落地 NDR落地ATD推出DEP起飞RTR返航
        }
    } else {
        var param = {
            "acOwner": DRcompCode,
            "datopStart": startBjTime,
            "datopEnd": endBjTime,
            "status": 'ATA,ARR,NDR,ATD,DEP,RTR', // ATA到达ARR 落地 NDR落地ATD推出DEP起飞RTR返航
        }
    }

    return $.ajax({
        type: 'post',
        url: "/bi/web/flighthourusedrate",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            if (response["N"]) {//窄体
                $('#N_AC_UTIL_RATE').text(response["N"]);
            } else {
                $('#N_AC_UTIL_RATE').text('-');
            }
            if (response["W"]) {//宽体
                $('#W_AC_UTIL_RATE').text(response["W"]);
            } else {
                $('#W_AC_UTIL_RATE').text('-');
            }
            if (response["R"]) {//宽体
                $('#R_AC_UTIL_RATE').text(response["R"]);
            } else {
                $('#R_AC_UTIL_RATE').text('-');
            }
        }
    });

}
// 根据传入的日期转成日和年后台需要的格式
function getDateId(date, dateType) {
    let dateId = "";
    if ("D" == dateType) {
        dateId = new moment(date).format("YYYYMMDD")
    } else if ("L" == dateType) {
        return date.DATE_ID;
    } else if ("M" == dateType) {
        dateId = new moment(date).format("YYYYMM");
    } else if ("Y" == dateType) {
        dateId = new moment(date).endOf("month").format("YYYY");
    }
    return dateId;
}

/** 不安全事件统计 */
function querySaftEventStatics(date) {
    var dateType = getDateType();
    let eventNames_1 = ["ALL", "人为", "机械", "天气", "意外", "代理", "其他"];
    let eventMap_1 = {
        "ALL": "eventCount", "人为": "humanEventCount", "机械": "machineCount", "天气": "weatherCount", "意外": "accidentCount", "代理": "agencyCount", "其他": "otherEventCount"
    };
    // 加载不安全事件总数及原因数量
    var dtd1 = getSmsEventCount(date, current_company_code, eventNames_1, dateType, eventMap_1);
    let eventNames_2 = ["事故", "严重征候", "责任征候", "岗位红线", "一类事件", "二类事件"];
    let eventMap_2 = { "事故": "signCount", "严重征候": "seriousSymCount", "责任征候": "generalSymCount", "岗位红线": "securityLineCount", "一类事件": "seriousCount", "二类事件": "generalCount" };
    var yearDate = date;
    if (dateType == 'L') {
        yearDate = moment(date.EFF_END_TIME)._d;
    }
    // 加载不安全事件性质分类，按年累计
    var dtd2 = getSmsEventCount(yearDate, current_company_code, eventNames_2, "Y", eventMap_2);
    //查询当天周期是否有值,年除外
    if (dateType != 'Y') {
        markSms(date, current_company_code, eventNames_2, dateType, eventMap_2);
    } else {
        $(".unsafeEvent .reasonNums ").css({
            color: 'unset'
        })
    }
    return [dtd1, dtd2];

}

/**查询当前周期数据,来标红页面对应位置 */
function markSms(date, compCode, eventNames, dateType, eventMap) {
    let dateId = getDateId(date, dateType);
    let param = {
        "DATE_ID": dateId,
        "COMP_CODE": compCode,
        "EVENT_NAME": eventNames.join(","),
        "DATE_TYPE": dateType
    }
    return $.ajax({
        type: 'post',
        url: "/bi/query/getsmseventcount?markRed",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            let data = response.data;
            let result = {};
            if (data && data[compCode]) {
                result = data[compCode];
            }
            var categorys = {
                '事故': '#ca0000',
                "严重征候": '#ff9f00',
                '责任征候': '#ff9f00',
                "岗位红线": '#ffff00',
                "一类事件": '#ffff00',
                "二类事件": 'blue'
            };

            for (var key in eventMap) {
                let name = eventMap[key];
                let count = 0;
                let objs = result[key];
                if (objs && objs.length > 0) {
                    let obj = objs[0];
                    count = !obj.KPI_VALUE ? 0 : obj.KPI_VALUE;
                }
                if (count > 0) {
                    $(".unsafeEvent ." + name).css({
                        color: categorys[key]
                    })
                } else {
                    $(".unsafeEvent ." + name).css({
                        color: 'unset'
                    })
                }

            }
        }
    });
}

function getSmsEventCount(date, compCode, eventNames, dateType, eventMap) {
    let dateId = getDateId(date, dateType);
    let param = {
        "DATE_ID": dateId,
        "COMP_CODE": compCode,
        "EVENT_NAME": eventNames.join(","),
        "DATE_TYPE": dateType
    }
    return $.ajax({
        type: 'post',
        url: "/bi/query/getsmseventcount",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            let data = response.data;
            let result = {};
            if (data && data[compCode]) {
                result = data[compCode];
            }
            for (var key in eventMap) {
                let name = eventMap[key];
                let count = 0;
                let objs = result[key];
                if (objs && objs.length > 0) {
                    let obj = objs[0];
                    count = !obj.KPI_VALUE ? 0 : obj.KPI_VALUE;
                }
                $(".unsafeEvent ." + name).text(count);
            }
        }
    });
}

function toFixed(num, precision) {
    return new Number(num).toFixed(precision)
}


//以下方法是控制器使用  start
function switchDateType(dateType) {
    $("#date_select .tab[data-type='" + dateType + "']").trigger("click");
}
function changeWeek(weekIndex) {
    var dateType = getDateType();
    if ("L" == dateType) {
        $(`.combobox_list .item[data-index='${weekIndex}']`).trigger("click");
    }
}


/** socket 调用  */
function changeDate(date, dateType) {
    if (dateType == "D") {
        $('#datetimepicker').data("DateTimePicker").date(date);
    } else if (dateType == "L") {

    } else if (dateType == "M") {
        $('#datetimepicker_month').data("DateTimePicker").date(date);
    } else if (dateType == "Y") {
        $('#datetimepicker_year').data("DateTimePicker").date(date);
    }
}

function popJingyingWin() {
    $(".zongshouru .detail").trigger("click");
}
function closeJingyingWin() {
    $(".closeBtn").trigger("click");
}

//方法是控制器使用end