showLoading();



var current_company_code;

// 可显示的历史 数量
var query_limit = 20;

// 日期类型
var date_type = 'L';

// FAC_COMP_KPI 最新有数据的日期 D
var latest_date;

// FAC_COMP_ACTYPE_KPI 最新有数据的日期 D
var latest_date_ac;

// 机型id／code对应表
var actypeId2Code;

var ac_type0 = []; // 宽体机 '330', '767', '787' ...
var ac_type1 = []; // 窄体机 '319', '320', '737' ...
var ac_type2 = []; // 支线机 '145', '190' ...

// 公司拥有的机型
var comp_ac_type0 = [];
var comp_ac_type1 = [];
var comp_ac_type2 = [];

var accode2num = {};

var comp_ac0 = []; // 宽体机公司
var comp_ac1 = []; // 窄体机公司
var comp_ac2 = []; // 支线机公司

// 后台JOB图标显示
function changeDataRefresh() {
    $('.pagetitle .data_refresh').hide();
    var param = {
        'CODE': 'J_HVP_FAC_COMP_ACTYPE_KPI'
    }
    $.ajax({
        type: 'post',
        url: "/bi/sys/isrunning",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
            if (response.isrun == 1) {
                $('.pagetitle .data_refresh').show();
                regTooltip('#btnrefresh', '后台数据更新中');
            }
        },
        error: function() {

        }
    });
}
changeDataRefresh();
setInterval(changeDataRefresh, 1 * 60 * 60 * 1000);

// 获取所有机型
var param = {}

$.ajax({
    type: 'post',
    url: "/bi/web/actypeall",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {

        if (response.actype) {
            var list = response.actype;
            actypeId2Code = {};

            ac_type0 = [];
            ac_type1 = [];
            ac_type2 = [];

            list.sort(function(a, b) {
                return a.sort - b.sort
            });

            var len = list.length;
            for (var i = 0; i < len; i++) {
                var obj = list[i];
                if (obj.actyptyp == 'Wide') {
                    // 宽体机
                    ac_type0.push(obj.code);

                } else if (obj.actyptyp == 'Narrow') {
                    // 窄体机
                    ac_type1.push(obj.code);

                } else if (obj.actyptyp == 'Regional') {
                    // 支线机
                    ac_type2.push(obj.code);

                }

                actypeId2Code[obj.id] = obj.code;
            }

            getAllCompanyKpiData();
        }

    },
    error: function() {

    }
});



// -------------------------------------------------------

//                          KPI

// -------------------------------------------------------



var weekList = [];
var monthList = [];
var dayList = [];
var selected_day;
var selected_month;



var all_company_data;
var all_company_data_ac; //飞机日利用率 计算指标

var weekDateRangeList; //例会周的日期对应表

var fetchingKpiData = false;

// 日期类型选择
function chooseDateType() {
    $(".date_type_select .tab").on('click', function() {

        $(".date_type_select .tab").removeClass('hover');
        $(this).addClass('hover');
        date_type = $(this).data('type');
        $(".limcomb").addClass('hidden');
        if (date_type == "L") {

            $("#main_cb_week").removeClass('hidden');
        } else if (date_type == "M") {
            $("#main_cb_month").removeClass('hidden');
        } else {
            $("#main_cb_day").removeClass('hidden');
        }
        updateAllKpi();
    });
}
chooseDateType();

function getAllCompanyKpiData() {

    if (companylist.length == 0) {
        setTimeout(getAllCompanyKpiData, 0);
        return;
    }

    if (fetchingKpiData) return;


    // check url hash
    var hash = window.location.hash.substr(1);
    if (hash && companyCode2Name[hash] != undefined) {
        current_company_code = hash;
    } else {
        current_company_code = parent_company;
    }



    fetchingKpiData = true;

    all_company_data = {};

    var comp_code = current_company_code;

    var len = companylist.length;
    var codelist = [];
    for (var i = 0; i < len; i++) {
        
        var dat = companylist[i];
        codelist.push(dat.code);
    }


    var loadingInProgress = 0;

    var kpi_list = [
        //'AC_NO', //飞机架数
        //'SHIFTS', //班次
        //'TRV_NUM', //旅客量
        'TRV_RATE', //客座率
        //'HOUR_INC_FUEL', //小时收(含燃油)
        //'CAP_KILO_INC_FUEL', //座收(含燃油)
    ];

    // -------------------------------------------------------
    // 
    // -------------------------------------------------------
    // 本期


    loadingInProgress++;
    var param = {
        'SOLR_CODE': 'FAC_COMP_KPI',
        'COMP_CODE': codelist.join(','),
        'KPI_CODE': kpi_list.join(','),
        'VALUE_TYPE': 'kpi_value_d', //本期
        'DATE_TYPE': 'L,M,D',
        "OPTIMIZE": 1,
        'LIMIT': query_limit
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            if (response.data != undefined) {

                all_company_data['kpi_value_d'] = response.data;


                // L
                var datlist = response.data[comp_code]['TRV_RATE']['L'];

                var week_list = [];
                for (var date in datlist) {
                    week_list.push(date);
                }

                getWeekDateRange(week_list);

                var cblist = [];
                var len = week_list.length;
                var last_date;
                for (var i = 0; i < len; i++) {
                    var date = week_list[i];
                    // Week: *********
                    //var label = date.substr(0,4) + '年第' + Number(date.substr(4,3)) + '周';
                    //20170419 双双要求：显示的周数+1
                    var weeknum = Number(date.substr(4, 3));
                    // 20180111, 因为周数+1，新的一年缺少第1周，所以增加第1周，数据显示上年最后一周的
                    if (weeknum == 1) {
                        var label = '第' + 1 + '周 ';
                        cblist.unshift({
                            'label': label,
                            'data': last_date
                        });
                    }
                    var label = '第' + (weeknum + 1) + '周 ';
                    cblist.unshift({
                        'label': label,
                        'data': date
                    });

                    last_date = date;
                }
                createComboBox('main_cb_week', cblist, 84, 240, updateAllKpi, 1);


                // $('#main_cb_week .combobox_label').on('click', function(event) {
                //     event.preventDefault();

                //     if(date_type == 'L'){
                //         return;
                //     }

                //     $('#main_cb_week').addClass('combotab_selected');
                //     $('#main_cb_week').removeClass('combotab');
                //     $('#main_cb_month').addClass('combotab');
                //     $('#main_cb_month').removeClass('combotab_selected');

                //     date_type = 'L';
                //     updateAllKpi();
                // });

                // 显示 week 日期范围
                $('#main_cb_week .combobox_label').on('mouseover', function(event) {
                    event.preventDefault();
                    if (weekDateRangeList) {
                        var date = $('#main_cb_week').attr('data');
                        $('#week_date_range').text(weekDateRangeList[date]);
                        $('#week_date_range').fadeIn();
                    }
                });

                // 隐藏 week 日期范围
                $('#main_cb_week .combobox_label').on('mouseout', function(event) {
                    event.preventDefault();
                    if (weekDateRangeList) {
                        $('#week_date_range').fadeOut();
                    }
                });



                // M
                var date = new Date();
                var month = date.getMonth() + 1;
                var day = date.getDate();
                if (month < 10) {
                    month = '0' + month;
                }
                var nowmonth = date.getFullYear() + '' + month;

                var datlist = response.data[comp_code]['TRV_RATE']['M'];

                var cblist = [];
                for (var date in datlist) {
                    // 201703
                    if (date <= nowmonth) {
                        var label = date.substr(0, 4) + '年' + date.substr(4, 2) + '月';
                        cblist.unshift({
                            'label': label,
                            'data': date
                        });

                        monthList.push(date);
                    }
                }
                createComboBox('main_cb_month', cblist, 104, 240, updateAllKpi, 1);

                // $('#main_cb_month .combobox_label').on('click', function(event) {
                //     event.preventDefault();

                //     if(date_type == 'M'){
                //         return;
                //     }

                //     $('#main_cb_week').addClass('combotab');
                //     $('#main_cb_week').removeClass('combotab_selected');
                //     $('#main_cb_month').addClass('combotab_selected');
                //     $('#main_cb_month').removeClass('combotab');

                //     date_type = 'M';
                //     updateAllKpi();
                // });


                // 显示 month 日期范围
                $('#main_cb_month .combobox_label').on('mouseover', function(event) {
                    event.preventDefault();
                    var month = $('#main_cb_month').attr('data');
                    var curmonth = moment().format("YYYYMM");
                    var numofdays = moment(month, "YYYYMM").daysInMonth(); // 获取一个月有几天
                    var days = numofdays;
                    if (days < 10) {
                        days = '0' + days;
                    }
                    if (curmonth == month) {
                        days = moment().format("DD");
                    }
                    $('#week_date_range').text(month + '01' + '~' + month + days);
                    $('#week_date_range').fadeIn();
                });

                // 隐藏 month 日期范围
                $('#main_cb_month .combobox_label').on('mouseout', function(event) {
                    event.preventDefault();
                    $('#week_date_range').fadeOut();
                });
                $('#main_cb_month .combobox_label').on('click', function(event) {
                    event.preventDefault();
                    $('#week_date_range').fadeOut();
                });

                // D
                var date2 = new Date();
                var month2 = date2.getMonth() + 1;
                var day2 = date2.getDate();
                if (month2 < 10) {
                    month2 = '0' + month2;
                }
                if (day2 < 10) {
                    day2 = '0' + day2;
                }
                var nowdate = date2.getFullYear() + '' + month2 + '' + day2;
                let t_2day = date2.getDate() - 2;
                if (t_2day < 10) {
                    t_2day = '0' + t_2day;
                }
                let t_2 = date2.getFullYear() + '' + month2 + '' + t_2day;
                let hasValue = false;

                var datlist = response.data[comp_code]['TRV_RATE']['D'];
                console.log("******")
                console.log(datlist)
                var cblist2 = [];
                for (var date in datlist) {
                    // 201703
                    if (date <= nowdate) {
                        var label = date.substr(0, 4) + '年' + date.substr(4, 2) + '月' + date.substr(6, 2) + '日';
                        cblist2.unshift({
                            'label': label,
                            'data': date
                        });

                        dayList.push(date);
                    }
                    if (date == t_2){
                        hasValue = true;
                    }
                }
                let index = 0;
                if (hasValue) {
                    cblist2.some((v, i)=>{
                        if (v.data == t_2) {
                            index = i;
                            return true;
                        }
                    })
                }
                createComboBox('main_cb_day', cblist2, 104, 240, updateAllKpi, index);


                loadingInProgress--;
                checkDataReady();


            }

        },
        error: function() {

        }
    });

    /*
    //同比 
    loadingInProgress++;
    var param = {
    'SOLR_CODE': 'FAC_COMP_KPI',
    'COMP_CODE': codelist.join(','),
    'KPI_CODE': kpi_list.join(','), 
    'VALUE_TYPE': 'kpi_ratio_tq_d', //同比 
    'DATE_TYPE': 'L,M',
    'LIMIT': query_limit
    }

    $.ajax({
        type: 'post',
        url:"/bi/query/getkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            if(response.data != undefined){
                all_company_data['kpi_ratio_tq_d'] = response.data;
                loadingInProgress--;
                checkDataReady();
            }
            
        },
        error:function() {
        }
    });
    */



    // -------------------------------------------------------
    // 机型指标
    // -------------------------------------------------------

    loadingInProgress++;

    var ac_kpi = [
        'FLY_TIME', //飞行时间
        'AC_NUM', //飞机架数
        'TRV_RATE', //客座率
        'EST_INC_FUEL', //预估收入(含燃油)
        'INC_TASK_OIL', //收入任务(含油)
        'SHIFTS', //班次
        'HOUR_INC_FUEL', //小时收(含燃油)
        'CAP_KILO_INC_FUEL', //座收(含燃油)
        'TRV_NUM', //旅客量
    ]
    var all_ac = ac_type0.concat(ac_type1, ac_type2);
    var param = {
        'SOLR_CODE': 'FAC_COMP_ACTYPE_KPI',
        'COMP_CODE': codelist.join(','),
        'KPI_CODE': ac_kpi.join(','),
        'VALUE_TYPE': 'kpi_value_d', //本期
        'DATE_TYPE': 'L,M,D',
        'ACTYPE': all_ac.join(','), //'ALL', //
        "OPTIMIZE": 1,
        // 'LIMIT': query_limit+1
        'LIMIT': query_limit
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getackpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
            console.log("^^^^^^^")
            console.log(response)
            console.log(ac_type0, ac_type1, ac_type2, actypeId2Code)
            if (response.data != undefined) {

                var data = response.data;

                if (all_company_data_ac == undefined) {
                    all_company_data_ac = {};
                }
                all_company_data_ac['kpi_value_d'] = data;

                // 统计哪些公司有宽体机、窄体机、支线机
                var len = companylist.length;
                for (var i = 0; i < len; i++) {
                    
                    var dat = companylist[i];
                    var compcode = dat.code;

                    if (compcode != parent_company) {
                        var allacs = data[compcode]['AC_NUM']['L']['data3'];
                        var len2 = allacs.length;
                        for (var j = 0; j < len2; j++) {
                            var acdat = allacs[j];
                            var acid = acdat.actype;
                            var ac = actypeId2Code[acid];
                            var obj = acdat.date[0];
                            if (!objectIsEmpty(obj)) {
                                var val1 = calAcRate(compcode, ac, 'kpi_value_d');
                                console.log(val1)
                                if (val1 > 0) {
                                    if (ac_type0.indexOf(ac) > -1 && comp_ac0.indexOf(compcode) == -1) {
                                        comp_ac0.push(compcode);
                                    } else if (ac_type1.indexOf(ac) > -1 && comp_ac1.indexOf(compcode) == -1) {
                                        comp_ac1.push(compcode);
                                    } else if (ac_type2.indexOf(ac) > -1 && comp_ac2.indexOf(compcode) == -1) {
                                        comp_ac2.push(compcode);
                                    }
                                }

                            }
                        }
                    }
                }


                loadingInProgress--;
                checkDataReady();

            }

        },
        error: function() {

        }
    });



    // 同期
    loadingInProgress++;

    var all_ac = ac_type0.concat(ac_type1, ac_type2);
    var param = {
        'SOLR_CODE': 'FAC_COMP_ACTYPE_KPI',
        'COMP_CODE': codelist.join(','),
        'KPI_CODE': ac_kpi.join(','),
        'VALUE_TYPE': 'kpi_value_tq_d', //同期
        'DATE_TYPE': 'L,M,D',
        "OPTIMIZE": 1,
        'ACTYPE': all_ac.join(','), //'ALL', //
        'LIMIT': query_limit + 1
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getackpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            if (response.data != undefined) {

                if (all_company_data_ac == undefined) {
                    all_company_data_ac = {};
                }
                all_company_data_ac['kpi_value_tq_d'] = response.data;


                loadingInProgress--;
                checkDataReady();
            }

        },
        error: function() {

        }
    });



    // 同比
    loadingInProgress++;

    var all_ac = ac_type0.concat(ac_type1, ac_type2);
    var param = {
        'SOLR_CODE': 'FAC_COMP_ACTYPE_KPI',
        'COMP_CODE': codelist.join(','),
        'KPI_CODE': ac_kpi.join(','),
        'VALUE_TYPE': 'kpi_ratio_tq_d', //同比
        'DATE_TYPE': 'L,M,D',
        "OPTIMIZE": 1,
        'ACTYPE': all_ac.join(','), //'ALL', //
        'LIMIT': query_limit + 1
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getackpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            if (response.data != undefined) {

                if (all_company_data_ac == undefined) {
                    all_company_data_ac = {};
                }
                all_company_data_ac['kpi_ratio_tq_d'] = response.data;


                loadingInProgress--;
                checkDataReady();
            }

        },
        error: function() {

        }
    });



    // 获取例会周对应的日期范围
    function getWeekDateRange(week_list) {
        var param = {
            "DATE_ID": week_list.join(','),
            "FIELD": "DATE_TYPE" // 对应数据表字段 DATE_DESC_XS
        }

        $.ajax({
            type: 'post',
            url: "/bi/web/datetype",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function(response) {

                weekDateRangeList = response;


                // 获取最新的有数据的日期，用来确定当前例会周截止的日期是哪天
                var param = {
                    'SOLR_CODE': 'FAC_COMP_KPI',
                    'COMP_CODE': parent_company,
                    'KPI_CODE': 'TRV_RATE',
                    'VALUE_TYPE': 'kpi_value_d',
                    'DATE_TYPE': 'D',
                    "OPTIMIZE": 1,
                    'LIMIT': 1
                }

                $.ajax({
                    type: 'post',
                    url: "/bi/query/getkpi",
                    contentType: 'application/json',
                    dataType: 'json',
                    async: true,
                    data: JSON.stringify(param),
                    success: function(response) {

                        if (response.data != undefined) {
                            var ddd = response.data[parent_company]['TRV_RATE']['D'];

                            for (var da in ddd) {
                                latest_date = da;
                                break;
                            }

                            //把最新周的结束日期换成上面查到的最新日期
                            if (latest_date) {
                                var latest_week = 0;
                                for (var week in weekDateRangeList) {
                                    if (Number(week) > Number(latest_week)) {
                                        latest_week = week;
                                    }
                                }
                                if (latest_week != 0) {
                                    var date_range = weekDateRangeList[latest_week];
                                    var arr = date_range.split('-');
                                    if (Number(arr[1]) > Number(latest_date) && Number(arr[0]) <= Number(latest_date)) {
                                        //weekDateRangeList[latest_week] = arr[0]+'-'+latest_date;
                                    }
                                }
                            }
                        }

                    },
                    error: function() {}
                });

            },
            error: function() {

            }
        });
    }



    // 获取 FAC_COMP_ACTYPE_KPI 表最新的有数据的日期
    var param = {
        'SOLR_CODE': 'FAC_COMP_ACTYPE_KPI',
        'COMP_CODE': parent_company,
        'KPI_CODE': 'AC_NUM',
        'VALUE_TYPE': 'kpi_value_d',
        "OPTIMIZE": 1,
        'DATE_TYPE': 'D',
        'ACTYPE': all_ac.join(','), //'ALL', //
        'LIMIT': 1
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getackpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            if (response.data != undefined) {
                var ddd = response.data[parent_company]['AC_NUM']['D']['data3'];
                var len = ddd.length;

                for (var i = 0; i < len; i++) {
                    var ac = ddd[i];
                    var dlist = ac.date
                    var len2 = dlist.length;
                    for (var j = 0; j < len2; j++) {
                        var da = dlist[j];
                        latest_date_ac = da.date;
                        break;
                    }
                }

            }
        },
        error: function() {

        }
    });



    function checkDataReady() {
        if (loadingInProgress == 0) {
            kpiDataReady = true;
            updateAllKpi();
            hideLoading();
        }
    }

}



// 计算飞机日利用率
function calAcRate(compcode, ac_list, data_type) {
    if (all_company_data_ac == undefined) {
        return -1;
    }

    var date = getCurrentDate();

    var kpiac1 = all_company_data_ac[data_type][compcode]['FLY_TIME'][date_type]['data3'];
    var kpiac2 = all_company_data_ac[data_type][compcode]['AC_NUM'][date_type]['data3'];

    var val1 = 0;
    var val2 = 0;

    var len = kpiac1.length;
    for (var i = 0; i < len; i++) {
        var acdat = kpiac1[i];
        var acid = acdat.actype;
        var ac = actypeId2Code[acid];
        if (ac_list.indexOf(ac) > -1) {
            var datelist = acdat.date;
            var len2 = datelist.length;
            for (var j = 0; j < len2; j++) {
                var obj = datelist[j];
                if (obj.date == date) {
                    var vv = isNaN(obj.value) ? 0 : Number(obj.value);
                    val1 += Number(vv);
                    break;
                }
            }
        }
    }


    var len = kpiac2.length;
    for (var i = 0; i < len; i++) {
        var acdat = kpiac2[i];
        var acid = acdat.actype;
        var ac = actypeId2Code[acid];
        if (ac_list.indexOf(ac) > -1) {

            var datelist = acdat.date;
            var len2 = datelist.length;
            for (var j = 0; j < len2; j++) {
                var obj = datelist[j];
                if (obj.date == date) {
                    var vv = isNaN(obj.value) ? 0 : Number(obj.value);
                    val2 += Number(vv);
                    break;
                }
            }
        }
    }

    // 飞机日利用率
    var rate = val2 > 0 ? (val1 / val2) : 0;

    return rate;
}

function getAcKpi(kpicode, compcode, ac_list, data_type) {
    if (all_company_data_ac == undefined) {
        return -1;
    }



    var date = getCurrentDate();

    var kpiac1 = all_company_data_ac[data_type][compcode][kpicode][date_type]['data3'];

    var val1 = 0;
    var cnt = 0;

    var len = kpiac1.length;
    for (var i = 0; i < len; i++) {
        var acdat = kpiac1[i];
        var acid = acdat.actype;
        var ac = actypeId2Code[acid];

        if (ac_list.indexOf(ac) > -1) {
            var datelist = acdat.date;
            var len2 = datelist.length;
            for (var j = 0; j < len2; j++) {
                var obj = datelist[j];
                if (obj.date == date) {
                    var vv = isNaN(obj.value) ? 0 : Number(obj.value);

                    val1 += vv;
                    cnt++;
                    break;

                }
            }
        }
    }

    // 平均
    var kpiper = cnt > 0 ? (val1 / cnt) : 0;

    return kpiper;
}


// 获取在册飞机架数 // 暂时不用了
function getAcCnt() {

    if (!latest_date_ac || !actypeId2Code || !weekDateRangeList) {
        setTimeout(getAcCnt, 10);
        return;
    }

    var date = '';

    if (date_type == 'L') {
        var week = $('#main_cb_week').attr('data');
        var date_range = weekDateRangeList[week];
        date = date_range.split('-')[1];
    } else if (date_type == 'M') {
        var month = $('#main_cb_month').attr('data');
        var date = month + '28'; //获取每月28号的数据，不要考虑大小月和月份问题
    } else if (date_type == 'D') {
        var day = $('#main_cb_day').attr('data');
        var date = day; //获取每月28号的数据，不要考虑大小月和月份问题
    }

    if (Number(latest_date_ac) < Number(date)) {
        date = latest_date_ac;
    }

    var len = companylist.length;
    var codelist = [];
    for (var i = 0; i < len; i++) {
        
        var dat = companylist[i];
        codelist.push(dat.code);
    }

    var all_ac = ac_type0.concat(ac_type1, ac_type2);
    var param = {
        'SOLR_CODE': 'FAC_COMP_ACTYPE_KPI',
        'COMP_CODE': codelist.join(','),
        'KPI_CODE': 'AC_NUM', // 飞机架
        'VALUE_TYPE': 'kpi_value_d', //本期
        'DATE_TYPE': 'D',
        "OPTIMIZE": 1,
        'DATE_ID': date,
        'ACTYPE': 'ALL',
        'LIMIT': 1
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getackpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            if (response.data != undefined) {
                var data = response.data;
                console.log("fenjixing", data)
                // 显示周的最后一天、月的28号的数据
                var kpiaclst = data[current_company_code]['AC_NUM']['D']['data3'];
                var val1 = 0;

                var len = kpiaclst.length;
                for (var i = 0; i < len; i++) {
                    var acdat = kpiaclst[i];
                    var acid = acdat.actype;
                    var accode = actypeId2Code[acid];
                    console.log(actypeId2Code[acid])
                    if (accode && accode != 'QITA') {
                        var acdd = acdat.date;
                        var len2 = acdd.length;
                        // 每种机型的架数
                        var acno = 0;
                        for (var j = 0; j < len2; j++) {
                            var dd = acdd[j];
                            var val = isNaN(dd.value) ? 0 : Number(dd.value);
                            val1 += val;
                            acno += val;
                        }

                        if (acno >= 0) {
                            if (ac_type0.indexOf(accode) > -1) {
                                comp_ac_type0.push(accode);
                            } else if (ac_type1.indexOf(accode) > -1) {
                                comp_ac_type1.push(accode);
                            } else if (ac_type2.indexOf(accode) > -1) {
                                comp_ac_type2.push(accode);
                            }
                        }

                        // 每种机型的架数
                        var acnum = (isNaN(acno) || acno == 0) ? '-' : Math.round(acno);
                        accode2num[accode] = acnum;
                        //
                    }

                }

                //在册飞机架数 总数//所有机型，包括宽体、窄体、支线、其它
                //val1 = (isNaN(val1) || val1==0) ? '-' : Math.round(val1);

            }

        },
        error: function() {

        }
    });
}



// 底部各个机型的指标
function setBlockBKpi() {

    if (comp_ac_type0.length == 0 && comp_ac_type1.length == 0 && comp_ac_type2.length == 0) {
        setTimeout(setBlockBKpi, 0);
        return;
    }

    comp_ac_type0.sort(function(a, b) {
        var n1 = accode2num[a];
        var n2 = accode2num[b];
        return n2 - n1
    });
    comp_ac_type1.sort(function(a, b) {
        var n1 = accode2num[a];
        var n2 = accode2num[b];
        return n2 - n1
    });
    comp_ac_type2.sort(function(a, b) {
        var n1 = accode2num[a];
        var n2 = accode2num[b];
        return n2 - n1
    });



    var list0 = comp_ac_type0;
    var list1 = comp_ac_type1;
    var list2 = comp_ac_type2;

    /*
    if(list0.length == 3){
        var ln = 3-list0.length;
        for(var i=0; i<ln; i++){
            list0.push('');
        }
    }
    if(list1.length < 3){
        var ln = 3-list1.length;
        for(var i=0; i<ln; i++){
            list1.push('');
        }
    }
    if(list2.length < 2){
        var ln = 2-list2.length;
        for(var i=0; i<ln; i++){
            list2.push('');
        }
    }
    */

    var all_ac = list0.concat(list1, list2);

    if (list0.length == 0) {
        $('#ac_kpi_cards .list0').hide();
    } else {
        $('#ac_kpi_cards .list0').show();
    }
    if (list1.length == 0) {
        $('#ac_kpi_cards .list1').hide();
    } else {
        $('#ac_kpi_cards .list1').show();
    }
    if (list2.length == 0) {
        $('#ac_kpi_cards .list2').hide();
    } else {
        $('#ac_kpi_cards .list2').show();
    }

    $('#ac_kpi_cards .list0').html('');
    $('#ac_kpi_cards .list1').html('');
    $('#ac_kpi_cards .list2').html('');
    $('#ac_kpi_cards .list3').html('');

    var itmw = 156;
    var margin = 7;
    var lft = 0;
    $('#ac_kpi_cards .list0').css('width', (list0.length * (itmw + margin) - margin) + 'px');
    $('#ac_kpi_cards .list1').css('width', (list1.length * (itmw + margin) - margin) + 'px');
    $('#ac_kpi_cards .list2').css('width', (list2.length * (itmw + margin) - margin) + 'px');
    $('#ac_kpi_cards .list3').css('width', '0px');

    $('#ac_kpi_cards .list0').css('left', lft + 'px');
    if (list0.length > 0) {
        lft += list0.length * (itmw + margin);
    }
    $('#ac_kpi_cards .list1').css('left', lft + 'px');
    if (list1.length > 0) {
        lft += list1.length * (itmw + margin);
    }
    $('#ac_kpi_cards .list2').css('left', lft + 'px');
    if (list2.length > 0) {
        lft += list2.length * (itmw + margin);
    }
    $('#ac_kpi_cards .list3').css('left', lft + 'px');


    var date = getCurrentDate();
    var compcode = current_company_code;
    var len = all_ac.length;
    for (var i = 0; i < len; i++) {

        // 显示机型
        var ac = all_ac[i];

        var item_dom = $('#item4clone');
        var itm = item_dom.clone();
        itm.addClass('itm' + i);
        itm.attr('id', 'ac_type_' + ac); // 关联机型和卡片的id
        itm.attr('ac', ac);

        if (list0.indexOf(ac) > -1) {
            $('#ac_kpi_cards .list0').append(itm);
            itm.css('left', list0.indexOf(ac) * (itmw + margin) + 'px');
        } else if (list1.indexOf(ac) > -1) {
            $('#ac_kpi_cards .list1').append(itm);
            itm.css('left', list1.indexOf(ac) * (itmw + margin) + 'px');
        } else if (list2.indexOf(ac) > -1) {
            $('#ac_kpi_cards .list2').append(itm);
            itm.css('left', list2.indexOf(ac) * (itmw + margin) + 'px');
        }

        itm.css('display', 'block');

        $('#ac_kpi_cards .itm' + i + ' .num .val').text(accode2num[ac]);
        itm.attr('AC_NUM', accode2num[ac]);

        $('#ac_kpi_cards .itm' + i + ' .ac').text(ac);
        // 飞机图片
        $('#ac_kpi_cards .itm' + i + ' .ico').css('background-image', 'url(img/a2.5.ac' + ac + '.png)');

        // 利用率
        var val1 = calAcRate(compcode, [ac], 'kpi_value_d');
        var val2 = calAcRate(compcode, [ac], 'kpi_value_tq_d');
        // 同比=(本期-同期)÷同期×100%
        var tb = val2 > 0 ? Math.round((val1 - val2) / val2 * 10000) / 100 : 0;

        if (tb > 0) {
            $('#ac_kpi_cards .itm' + i + ' .b2 .green').show();
            $('#ac_kpi_cards .itm' + i + ' .b2 .red').hide();
        } else if (tb < 0) {
            $('#ac_kpi_cards .itm' + i + ' .b2 .green').hide();
            $('#ac_kpi_cards .itm' + i + ' .b2 .red').show();
        } else {
            $('#ac_kpi_cards .itm' + i + ' .b2 .green').hide();
            $('#ac_kpi_cards .itm' + i + ' .b2 .red').hide();
        }

        // 利用率
        if (val1 != 0) {
            $('#ac_kpi_cards .itm' + i + ' .b1 .b .v').text(Math.round(val1 * 10) / 10);
            itm.attr('AC_UTIL_RATE', Math.round(val1 * 10) / 10);
        } else {
            $('#ac_kpi_cards .itm' + i + ' .b1 .b .v').text('-');
            itm.attr('AC_UTIL_RATE', 0);
        }


        // 利用率 同比
        if (tb != 0) {
            $('#ac_kpi_cards .itm' + i + ' .b2 .b .v').text(tb + '%');
        } else {
            $('#ac_kpi_cards .itm' + i + ' .b2 .b .v').text('-');
        }
        itm.attr('AC_UTIL_RATE_tb', tb);


        // 客座率
        var val1 = getAcKpi('TRV_RATE', compcode, [ac], 'kpi_value_d');
        var tb = getAcKpi('TRV_RATE', compcode, [ac], 'kpi_ratio_tq_d');
        tb = !isNaN(tb) ? Math.round(tb * 10000) / 100 : 0;

        if (tb > 0) {
            $('#ac_kpi_cards .itm' + i + ' .b4 .green').show();
            $('#ac_kpi_cards .itm' + i + ' .b4 .red').hide();
        } else if (tb < 0) {
            $('#ac_kpi_cards .itm' + i + ' .b4 .green').hide();
            $('#ac_kpi_cards .itm' + i + ' .b4 .red').show();
        } else {
            $('#ac_kpi_cards .itm' + i + ' .b4 .green').hide();
            $('#ac_kpi_cards .itm' + i + ' .b4 .red').hide();
        }

        // 客座率
        if (val1 != 0) {
            $('#ac_kpi_cards .itm' + i + ' .b3 .b .v').text(Math.round(val1 * 1000) / 10 + '%');
            itm.attr('TRV_RATE', Math.round(val1 * 1000) / 10);
        } else {
            $('#ac_kpi_cards .itm' + i + ' .b3 .b .v').text('-');
            itm.attr('TRV_RATE', 0);
        }


        // 客座率 同比
        if (tb != 0) {
            $('#ac_kpi_cards .itm' + i + ' .b4 .b .v').text(tb + '%');
        } else {
            $('#ac_kpi_cards .itm' + i + ' .b4 .b .v').text('-');
        }
        itm.attr('TRV_RATE_tb', tb);


        if (i == 0 && (selectedAc == undefined || all_ac.indexOf(selectedAc) == -1) || selectedAc == ac) {
            selectCard(ac);
        }


    }


    if (all_ac.length < 8) {

        len = 8 - all_ac.length;

        var left = 0;

        $('#ac_kpi_cards .list3').css('width', (len * (itmw + margin) - margin) + 'px');

        for (var i = 0; i < len; i++) {

            var item_dom = $('#item4clone');
            var itm = item_dom.clone();
            itm.addClass('ph');

            itm.css('left', left + 'px');
            left += itmw + margin;

            itm.css('display', 'block');

            itm.find('.red').hide();
            itm.find('.green').hide();
            itm.find('.canhide').hide();
            itm.find('.ac').text('--');


            itm.css('opacity', 0.5);

            $('#ac_kpi_cards .list3').append(itm);

        }
    }

    var w = $('#ac_kpi_cards .list0').width() + $('#ac_kpi_cards .list1').width() + $('#ac_kpi_cards .list2').width() + $('#ac_kpi_cards .list3').width() + margin * 2;
    $('.block_b .scrollpane .content').css('width', w + 'px');

    $('.block_b .scrollpane .itm').off('click');
    $('.block_b .scrollpane .itm').on('click', function(e) {
        var ac = $(this).attr('ac');
        if (ac) {
            selectCard(ac);
        }
    });
}

var currentpage = 1;

$('.btn_next').on('click', function(e) {
    if (currentpage == 1) {
        currentpage = 2;
        $('.btn_prev').css('opacity', 1);
        $('.btn_prev').css('pointer-events', 'auto');
        $('.btn_next').css('opacity', 0.3);
        $('.btn_next').css('pointer-events', 'none');
        var lft = $('.block_b .scrollpane .content').width() - $('.block_b .scrollpane').width();
        $('.block_b .scrollpane .content').css('left', '-' + lft + 'px');

    }
});
$('.btn_prev').on('click', function(e) {
    if (currentpage == 2) {
        currentpage = 1;
        $('.btn_next').css('opacity', 1);
        $('.btn_next').css('pointer-events', 'auto');
        $('.btn_prev').css('opacity', 0.3);
        $('.btn_prev').css('pointer-events', 'none');
        $('.block_b .scrollpane .content').css('left', '0');

    }
});

function resetPagePrevNext() {
    currentpage = 1;
    $('.btn_prev').css('opacity', 0.3);
    $('.btn_prev').css('pointer-events', 'none');
    if (current_company_code == parent_company) {
        $('.btn_next').css('opacity', 1);
        $('.btn_next').css('pointer-events', 'auto');
    } else {
        $('.btn_next').css('opacity', 0.3);
        $('.btn_next').css('pointer-events', 'none');
    }
    $('.block_b .scrollpane .content').css('left', '0');
}

var selectedAc;

function selectCard(ac) {
    selectedAc = ac;
    $('.block_b .scrollpane .itm').removeClass('selected');
    $('#ac_type_' + ac).addClass('selected');

    $('.block_t_tt').text(ac + '机型相关数据');

    setBlockTKpi(ac);


    // 自动循环切换机型card
    clearTimeout(itv_autoSwitchCard);
    itv_autoSwitchCard = setTimeout(autoSwitchCard, 20000);
}

function fmt0(val) {
    if (!isNaN(val) && val != 0) {
        return val;
    } else {
        return '-';
    }
}

function setBlockTKpi(ac) {
    /*
'FLY_TIME', //飞行时间
'AC_NUM', //飞机架数
'TRV_RATE', //客座率
'EST_INC_FUEL', //预估收入(含燃油)
'INC_TASK_OIL', //收入任务(含油)
'SHIFTS', //班次
'HOUR_INC_FUEL', //小时收(含燃油)
'CAP_KILO_INC_FUEL', //座收(含燃油)
'TRV_NUM', //旅客量
    */

    //kpi_value_d
    //kpi_ratio_tq_d
    //kpi_ratio_sq_d


    audioValueData.AC_NUM = $('#ac_type_' + ac).attr('AC_NUM');
    audioValueData.AC_UTIL_RATE = $('#ac_type_' + ac).attr('AC_UTIL_RATE');
    audioValueData.AC_UTIL_RATE_tb = $('#ac_type_' + ac).attr('AC_UTIL_RATE_tb');
    audioValueData.TRV_RATE = $('#ac_type_' + ac).attr('TRV_RATE');
    audioValueData.TRV_RATE_tb = $('#ac_type_' + ac).attr('TRV_RATE_tb');


    var date = getCurrentDate();
    var compcode = current_company_code;


    // 机型收入(万元)
    var val = getAcKpi('EST_INC_FUEL', compcode, [ac], 'kpi_value_d');
    var tb = getAcKpi('EST_INC_FUEL', compcode, [ac], 'kpi_ratio_tq_d');
    tb = !isNaN(tb) ? Math.round(tb * 1000) / 10 : 0;

    $('#val_EST_INC_FUEL').text(fmt0(Math.round(val)));
    $('#tb_EST_INC_FUEL .val').text(fmt0(tb) + '%');

    audioValueData.EST_INC_FUEL = Math.round(val);
    audioValueData.EST_INC_FUEL_tb = tb;

    if (tb > 0) {
        $('#tb_EST_INC_FUEL').removeClass('down')
        $('#tb_EST_INC_FUEL').addClass('up')
    } else if (tb < 0) {
        $('#tb_EST_INC_FUEL').addClass('down')
        $('#tb_EST_INC_FUEL').removeClass('up')
    } else {
        $('#tb_EST_INC_FUEL').removeClass('down')
        $('#tb_EST_INC_FUEL').removeClass('up')
    }



    // 收入任务(万元)
    var val = 0;
    var tb = 0;
    var task_inc = 0;
    var task_inc_tq = 0;
    if (compcode == parent_company) {
        var len = companylist.length;
        var val_tq = 0;
        for (var i = 0; i < len; i++) {
            
            var dat = companylist[i];
            var ccode = dat.code;
            if (ccode != parent_company) {
                var val1 = getAcKpi('INC_TASK_OIL', ccode, [ac], 'kpi_value_d');
                if (val1 > 0) {
                    val += Math.round(val1);
                }
                var val_tq1 = getAcKpi('INC_TASK_OIL', ccode, [ac], 'kpi_value_tq_d');
                if (val_tq1 > 0) {
                    val_tq += Math.round(val_tq1);
                }
            }
        }
        task_inc_tq = val_tq;
        // 同比=(本期-同期)÷同期×100%
        tb = val > 0 && val_tq > 0 ? ((val - val_tq) / val_tq) : 0;
        tb = !isNaN(tb) ? Math.round(tb * 1000) / 10 : 0;

    } else {
        val = getAcKpi('INC_TASK_OIL', compcode, [ac], 'kpi_value_d');
        val = Math.round(val);
        tb = getAcKpi('INC_TASK_OIL', compcode, [ac], 'kpi_ratio_tq_d');
        tb = !isNaN(tb) ? Math.round(tb * 1000) / 10 : 0;

        task_inc_tq = getAcKpi('INC_TASK_OIL', compcode, [ac], 'kpi_value_tq_d');
    }

    $('#val_INC_TASK_OIL').text(fmt0(val));
    $('#tb_INC_TASK_OIL .val').text(fmt0(tb) + '%');

    task_inc = val;
    audioValueData.INC_TASK_OIL = val;
    audioValueData.INC_TASK_OIL_tb = tb;

    if (tb > 0) {
        $('#tb_INC_TASK_OIL').removeClass('down')
        $('#tb_INC_TASK_OIL').addClass('up')
    } else if (tb < 0) {
        $('#tb_INC_TASK_OIL').addClass('down')
        $('#tb_INC_TASK_OIL').removeClass('up')
    } else {
        $('#tb_INC_TASK_OIL').removeClass('down')
        $('#tb_INC_TASK_OIL').removeClass('up')
    }



    // 任务完成率
    var val1 = getAcKpi('EST_INC_FUEL', compcode, [ac], 'kpi_value_d');
    var val2 = task_inc;
    var val1_tq = getAcKpi('EST_INC_FUEL', compcode, [ac], 'kpi_value_tq_d');
    var val2_tq = task_inc_tq;

    var val = val2 > 0 ? Math.round(val1 / val2 * 1000) / 10 : 0;
    var val_tq = val2_tq > 0 ? Math.round(val1_tq / val2_tq * 10000) / 100 : 0;
    // 同比=(本期-同期)÷同期×100%
    var tb = val > 0 && val_tq > 0 ? ((val - val_tq) / val_tq) : 0;
    tb = !isNaN(tb) ? Math.round(tb * 1000) / 10 : 0;

    $('#val_TASK_RATE').text(fmt0(val) + '%');
    $('#tb_TASK_RATE .val').text(fmt0(tb) + '%');

    audioValueData.TASK_RATE = val;
    audioValueData.TASK_RATE_tb = tb;

    if (tb > 0) {
        $('#tb_TASK_RATE').removeClass('down')
        $('#tb_TASK_RATE').addClass('up')
    } else if (tb < 0) {
        $('#tb_TASK_RATE').addClass('down')
        $('#tb_TASK_RATE').removeClass('up')
    } else {
        $('#tb_TASK_RATE').removeClass('down')
        $('#tb_TASK_RATE').removeClass('up')
    }



    // 飞行班次
    var val = getAcKpi('SHIFTS', compcode, [ac], 'kpi_value_d');
    var tb = getAcKpi('SHIFTS', compcode, [ac], 'kpi_ratio_tq_d');
    tb = !isNaN(tb) ? Math.round(tb * 1000) / 10 : 0;

    $('#val_SHIFTS').text(fmt0(Math.round(val)));
    $('#tb_SHIFTS .val').text(fmt0(tb) + '%');

    audioValueData.SHIFTS = Math.round(val);
    audioValueData.SHIFTS_tb = tb;

    if (tb > 0) {
        $('#tb_SHIFTS').removeClass('down')
        $('#tb_SHIFTS').addClass('up')
    } else if (tb < 0) {
        $('#tb_SHIFTS').addClass('down')
        $('#tb_SHIFTS').removeClass('up')
    } else {
        $('#tb_SHIFTS').removeClass('down')
        $('#tb_SHIFTS').removeClass('up')
    }


    // 小时收入(万元)
    var val = getAcKpi('HOUR_INC_FUEL', compcode, [ac], 'kpi_value_d');
    var tb = getAcKpi('HOUR_INC_FUEL', compcode, [ac], 'kpi_ratio_tq_d');
    tb = !isNaN(tb) ? Math.round(tb * 1000) / 10 : 0;

    $('#val_HOUR_INC_FUEL').text(fmt0(val));
    $('#tb_HOUR_INC_FUEL .val').text(fmt0(tb) + '%');

    audioValueData.HOUR_INC_FUEL = val;
    audioValueData.HOUR_INC_FUEL_tb = tb;

    if (tb > 0) {
        $('#tb_HOUR_INC_FUEL').removeClass('down')
        $('#tb_HOUR_INC_FUEL').addClass('up')
    } else if (tb < 0) {
        $('#tb_HOUR_INC_FUEL').addClass('down')
        $('#tb_HOUR_INC_FUEL').removeClass('up')
    } else {
        $('#tb_HOUR_INC_FUEL').removeClass('down')
        $('#tb_HOUR_INC_FUEL').removeClass('up')
    }


    // 座公里收入(元)
    var val = getAcKpi('CAP_KILO_INC_FUEL', compcode, [ac], 'kpi_value_d');
    var tb = getAcKpi('CAP_KILO_INC_FUEL', compcode, [ac], 'kpi_ratio_tq_d');
    tb = !isNaN(tb) ? Math.round(tb * 1000) / 10 : 0;

    $('#val_CAP_KILO_INC_FUEL').text(fmt0(val));
    $('#tb_CAP_KILO_INC_FUEL .val').text(fmt0(tb) + '%');

    audioValueData.CAP_KILO_INC_FUEL = val;
    audioValueData.CAP_KILO_INC_FUEL_tb = tb;

    if (tb > 0) {
        $('#tb_CAP_KILO_INC_FUEL').removeClass('down')
        $('#tb_CAP_KILO_INC_FUEL').addClass('up')
    } else if (tb < 0) {
        $('#tb_CAP_KILO_INC_FUEL').addClass('down')
        $('#tb_CAP_KILO_INC_FUEL').removeClass('up')
    } else {
        $('#tb_CAP_KILO_INC_FUEL').removeClass('down')
        $('#tb_CAP_KILO_INC_FUEL').removeClass('up')
    }


    // 旅客量
    var val = getAcKpi('TRV_NUM', compcode, [ac], 'kpi_value_d');
    var tb = getAcKpi('TRV_NUM', compcode, [ac], 'kpi_ratio_tq_d');
    tb = !isNaN(tb) ? Math.round(tb * 1000) / 10 : 0;

    $('#val_TRV_NUM').text(fmt0(Math.round(val)));
    $('#tb_TRV_NUM .val').text(fmt0(tb) + '%');

    audioValueData.TRV_NUM = Math.round(val);
    audioValueData.TRV_NUM_tb = tb;

    if (tb > 0) {
        $('#tb_TRV_NUM').removeClass('down')
        $('#tb_TRV_NUM').addClass('up')
    } else if (tb < 0) {
        $('#tb_TRV_NUM').addClass('down')
        $('#tb_TRV_NUM').removeClass('up')
    } else {
        $('#tb_TRV_NUM').removeClass('down')
        $('#tb_TRV_NUM').removeClass('up')
    }



    startLoadAudio();

}


var audioValueData = {};

function startLoadAudio() {

    // 语音播报
    function onAudioTplLoad(tplobj) {

        /* 
        {COMP} {DATE} {AC}机型 
        {AC_NUM}架。
        日利用率{AC_UTIL_RATE}小时，同比{TB8}。
        客座率{TRV_RATE}，同比{TB9}。
        收入{EST_INC_FUEL}万元，同比{TB1}。
        收入任务{INC_TASK_OIL}，同比{TB2}。
        任务完成率{TASK_RATE}，同比{TB3}。
        飞行班次{SHIFTS}，同比{TB4}。
        小时收入{HOUR_INC_FUEL}万元，同比{TB5}。
        座公里收入{CAP_KILO_INC_FUEL}元，同比{TB6}。
        旅客量{TRV_NUM}人，同比{TB7}。
        */
        var tpl = tplobj.txt;

        //--
        var compname = companyCode2Name[current_company_code];
        tpl = tpl.replace(/{COMP}/g, compname);

        //--
        var date = getCurrentDate();
        var datelabel = '';
        if (date_type == 'L') {
            // Week: *********
            //var label = date.substr(0,4) + '年第' + Number(date.substr(4,3)) + '周';
            //20170419 双双要求：显示的周数+1
            datelabel = date.substr(0, 4) + '年' + '第' + (Number(date.substr(4, 3)) + 1) + '周';
        } else if (date_type == 'M') {
            datelabel = date.substr(0, 4) + '年' + Number(date.substr(4, 2)) + '月';
        }
        tpl = tpl.replace(/{DATE}/g, datelabel);

        //--
        tpl = tpl.replace(/{AC}/g, selectedAc);
        tpl = tpl.replace(/{AC_NUM}/g, audioValueData.AC_NUM);

        //--
        var val;
        if (!isNaN(audioValueData.AC_UTIL_RATE)) {
            val = audioValueData.AC_UTIL_RATE;
        } else {
            val = '无';
        }
        tpl = tpl.replace(/{AC_UTIL_RATE}/g, val);
        tpl = tpl.replace(/{TB8}/g, ratioTemplete(audioValueData.AC_UTIL_RATE_tb));

        //--
        var val;
        if (!isNaN(audioValueData.TRV_RATE)) {
            val = audioValueData.TRV_RATE + '%';
        } else {
            val = '无';
        }
        tpl = tpl.replace(/{TRV_RATE}/g, val);
        tpl = tpl.replace(/{TB9}/g, ratioTemplete(audioValueData.TRV_RATE_tb));

        //--
        tpl = tpl.replace(/{EST_INC_FUEL}/g, formatCurrency(audioValueData.EST_INC_FUEL, 0));
        tpl = tpl.replace(/{TB1}/g, ratioTemplete(audioValueData.EST_INC_FUEL_tb));

        //--
        var val;
        if (!isNaN(audioValueData.INC_TASK_OIL)) {
            val = formatCurrency(audioValueData.INC_TASK_OIL, 0) + '万元';
        }
        tpl = tpl.replace(/{INC_TASK_OIL}/g, val);
        tpl = tpl.replace(/{TB2}/g, ratioTemplete(audioValueData.INC_TASK_OIL_tb));

        //--
        var val;
        if (!isNaN(audioValueData.TASK_RATE)) {
            val = audioValueData.TASK_RATE + '%';
        } else {
            val = '无';
        }
        tpl = tpl.replace(/{TASK_RATE}/g, val);
        tpl = tpl.replace(/{TB3}/g, ratioTemplete(audioValueData.TASK_RATE_tb));

        //--
        tpl = tpl.replace(/{SHIFTS}/g, formatCurrency(trimDecimal(audioValueData.SHIFTS, 0), 0));
        tpl = tpl.replace(/{TB4}/g, ratioTemplete(audioValueData.SHIFTS_tb));

        //--
        tpl = tpl.replace(/{HOUR_INC_FUEL}/g, audioValueData.HOUR_INC_FUEL);
        tpl = tpl.replace(/{TB5}/g, ratioTemplete(audioValueData.HOUR_INC_FUEL_tb));

        //--
        tpl = tpl.replace(/{CAP_KILO_INC_FUEL}/g, audioValueData.CAP_KILO_INC_FUEL);
        tpl = tpl.replace(/{TB6}/g, ratioTemplete(audioValueData.CAP_KILO_INC_FUEL_tb));

        //--
        tpl = tpl.replace(/{TRV_NUM}/g, formatCurrency(trimDecimal(audioValueData.TRV_NUM, 0), 0));
        tpl = tpl.replace(/{TB7}/g, ratioTemplete(audioValueData.TRV_NUM_tb));

        text2audio(tpl, true);

    }

    stopAudio();
    getAudioTemplate('A2.5-general', onAudioTplLoad);

}


function setBlockMLKpi() {
    // 利用率
	var acs = [];
    var aclist = [ac_type0, ac_type1, ac_type2];
    var colist = [comp_ac0, comp_ac1, comp_ac2];
    console.log(aclist, colist)
    for (var j = 0; j < aclist.length; j++) {
        acs = aclist[j];
        var vals = [];
        // var len = companylist.length;
        var len = colist[j].length;
        for (var i = 0; i < len; i++) {
            
            // var dat = companylist[i];
            // var compcode = dat.code;
            var compcode = colist[j][i];
            if (compcode != parent_company) {
                var val1 = calAcRate(compcode, acs, 'kpi_value_d');
                var val2 = calAcRate(compcode, acs, 'kpi_value_tq_d');
                // 同比=(本期-同期)÷同期×100%
                var tb = val2 > 0 ? ((val1 - val2) / val2) : 0;

                var obj = {};
                obj.compcode = compcode;
                obj.val = val1;
                obj.tb = tb;
                vals.push(obj);
            }
        }

        // 同比增长最快
        vals.sort(function(a, b) {
            return b.tb - a.tb
        });
        console.log(vals)
        $('#ac_rate_0_' + j).html(companyCode2Nameabbr[vals[0].compcode] + ' <b>' + Math.round(vals[0].val * 10) / 10 + '</b>');

        // 最高
        vals.sort(function(a, b) {
            return b.val - a.val
        });
        $('#ac_rate_1_' + j).html(companyCode2Nameabbr[vals[0].compcode] + ' <b>' + Math.round(vals[0].val * 10) / 10 + '</b>');

        // 最低不能是0 ，因为部分公司没有宽体、支线客机
        for (var i = vals.length - 1; i >= 0; i--) {
            if (vals[i].val > 0) {
                $('#ac_rate_2_' + j).html(companyCode2Nameabbr[vals[i].compcode] + ' <b>' + Math.round(vals[i].val * 10) / 10 + '</b>');
                break;
            }
        }


    }

}

function setBlockMR1Kpi() {


    // ---------------------------
    var chart_id = 'chart_mr1';
    var kpi_code = '';
    var colors = [
        ['#0ec3ff', '#0464b6'],
        ['#d2b93f', '#d2b93f'],
    ];
    var marginx = 30;
    // ---------------------------

    var complist = [];
    complist = complist.concat(comp_ac0);
    var len = companylist.length;
    for (var i = 0; i < len; i++) {
        
        var dat = companylist[i];
        var compcode = dat.code;
        if (compcode != parent_company) {
            complist.push(compcode);
        }
    }
    complist = complist.concat(comp_ac2);

    var dat_length = complist.length;

    var xAxisData = [];
    var data_s1 = [];
    var data_s2 = [];

    // s1 s2
    for (var i = 0; i < dat_length; i++) {
        var compcode = complist[i];
        xAxisData.push(companyCode2Nameabbr[compcode]);

        var acs = [];
        var color;
        var color2;
        if (i < comp_ac0.length) {
            acs = ac_type0;
            color = new echarts.graphic.LinearGradient(
                0, 0, 0, 1, [{
                    offset: 0,
                    color: '#adaaa0'
                }, {
                    offset: 1,
                    color: '#56635c'
                }]);
            color2 = '#adaaa0';
        } else if (i >= dat_length - comp_ac2.length) {
            acs = ac_type2;
            color = new echarts.graphic.LinearGradient(
                0, 0, 0, 1, [{
                    offset: 0,
                    color: '#1f9183'
                }, {
                    offset: 1,
                    color: '#1f5a55'
                }]);
            color2 = '#1f9183';
        } else {
            acs = ac_type1;
            color = new echarts.graphic.LinearGradient(
                0, 0, 0, 1, [{
                    offset: 0,
                    color: '#0ec3ff'
                }, {
                    offset: 1,
                    color: '#0464b6'
                }]);
            color2 = '#0ec3ff';
        }

        var val1 = calAcRate(compcode, acs, 'kpi_value_d');
        var val2 = calAcRate(compcode, acs, 'kpi_value_tq_d');
        // 同比=(本期-同期)÷同期×100%
        var tb = val2 > 0 ? Math.round((val1 - val2) / val2 * 1000) / 10 : 0;


        var obj = {
            value: Math.round(val1 * 100) / 100,
            itemStyle: {
                normal: {
                    color: color,
                }
            },
            label: {
                normal: {
                    color: color2,
                }
            },
        };
        data_s1.push(obj);
        data_s2.push(tb);
    }



    var chart = echarts.init(document.getElementById(chart_id));

    var option = {
        tooltip: {
            show: true,
            formatter: function(params, ticket, callback) {
                if (params.seriesName == '同比') {
                    return params.seriesName + '<br>' + params.name + ': ' + params.value + '%';
                } else {
                    return params.seriesName + '<br>' + params.name + ': ' + params.value + '';
                }
            },
            extraCssText: "color:#021e55; padding:5px 10px 6px 10px; box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);background: rgb(178,225,254);background: -moz-linear-gradient(top,  rgba(178,225,254,1) 0%, rgba(100,192,250,1) 100%);background: -webkit-linear-gradient(top,  rgba(178,225,254,1) 0%,rgba(100,192,250,1) 100%);background: linear-gradient(to bottom,  rgba(178,225,254,1) 0%,rgba(100,192,250,1) 100%);filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#b2e1fe', endColorstr='#64c0fa',GradientType=0 );",
            backgroundColor: '',

        },
        legend: {
            show: true,
            orient: 'horizontal',
            //x: 'center',
            right: 10,
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: '#45a5f6',
                fontSize: 12 + fontSizeDiff()
            },
            data: [{
                name: '利用率',
                icon: 'rect',
            }, {
                name: '同比',
                icon: 'circle',
            }]
        },
        grid: {
            top: 66,
            left: marginx,
            right: marginx,
            bottom: 55,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: true, // 去掉x轴两边的留白, 空着不显示line难看
            nameTextStyle: {
                color: '#45a5f6'
            },
            axisLine: {
                lineStyle: {
                    color: '#45a5f6' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#45a5f6', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
                formatter: function(value, index) {
                    return chartDateFormatter(value, index);
                },
            },
            splitLine: {
                show: false, // 不显示刻度线
            },
            axisTick: {
                show: false, // 不显示刻度线
            }

        }],
        yAxis: [{
            type: 'value',
            name: '', //利用率
            nameLocation: 'end',
            //min: 0,
            //max: 100,
            //interval: 10,
            nameTextStyle: {
                color: '#45a5f6',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#45a5f6',
                    fontSize: 12 + fontSizeDiff(),
                    align: 'left',
                },
                margin: 30,
                formatter: '{value}',
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(69,165,246,0.1)'] // 分割线颜色
                }
            }
        }, {
            type: 'value',
            name: '', //同比
            nameLocation: 'end',
            position: 'right',
            //min: 0,
            //max: 100,
            //interval: 10,
            nameTextStyle: {
                color: '#b39a22',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#b39a22',
                    fontSize: 12 + fontSizeDiff(),
                    align: 'right',
                },
                margin: 30,
                formatter: '{value}%',
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ['rgba(69,165,246,0.1)'] // 分割线颜色
                }
            }
        }],
        series: [{
            name: '利用率',
            type: 'bar',
            barWidth: 12,
            yAxisIndex: 0,
            data: data_s1,
            label: {
                normal: {
                    show: true,
                    position: 'top'
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[0][0]
                        }, {
                            offset: 1,
                            color: colors[0][1]
                        }]),
                }
            },
        }, {
            name: '同比',
            type: 'line',
            smooth: false,
            yAxisIndex: 1,
            data: data_s2,
            symbol: 'circle',
            symbolSize: 9,
            lineStyle: {
                normal: {
                    color: colors[1][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[1][0],
                    opacity: 1, //折线拐点

                }
            },

        }]
    };

    chart.setOption(option);



    // ----------------- 宽体／窄体／直线 分类 组 -----------------

    drawAcTypeSections(marginx, comp_ac0.length, dat_length - comp_ac0.length - comp_ac2.length, comp_ac2.length)



}



function setBlockMR2Kpi() {


    // ---------------------------
    var chart_id = 'chart_mr2';
    var kpi_code = '';
    var colors = [
        ['#0ec3ff', '#0464b6'],
        ['#d2b93f', '#d2b93f'],
    ];
    var marginx = 30;
    // ---------------------------

    var complist = [];
    complist = complist.concat(comp_ac0);
    var len = companylist.length;
    for (var i = 0; i < len; i++) {
        
        var dat = companylist[i];
        var compcode = dat.code;
        if (compcode != parent_company) {
            complist.push(compcode);
        }
    }
    complist = complist.concat(comp_ac2);

    var dat_length = complist.length;

    var xAxisData = [];
    var data_s1 = [];
    var data_s2 = [];

    // s1 s2
    for (var i = 0; i < dat_length; i++) {
        var compcode = complist[i];

        var acs = [];
        var color;
        var color2;
        if (i < comp_ac0.length) {
            acs = ac_type0;
            color = new echarts.graphic.LinearGradient(
                0, 0, 0, 1, [{
                    offset: 0,
                    color: '#adaaa0'
                }, {
                    offset: 1,
                    color: '#56635c'
                }]);
            color2 = '#adaaa0';
        } else if (i >= dat_length - comp_ac2.length) {
            acs = ac_type2;
            color = new echarts.graphic.LinearGradient(
                0, 0, 0, 1, [{
                    offset: 0,
                    color: '#1f9183'
                }, {
                    offset: 1,
                    color: '#1f5a55'
                }]);
            color2 = '#1f9183';
        } else {
            acs = ac_type1;
            color = new echarts.graphic.LinearGradient(
                0, 0, 0, 1, [{
                    offset: 0,
                    color: '#0ec3ff'
                }, {
                    offset: 1,
                    color: '#0464b6'
                }]);
            color2 = '#0ec3ff';
        }

        var val1 = getAcKpi('CAP_KILO_INC_FUEL', compcode, acs, 'kpi_value_d');
        var val2 = getAcKpi('CAP_KILO_INC_FUEL', compcode, acs, 'kpi_value_tq_d');

        // 同比=(本期-同期)÷同期×100%
        var tb = val2 > 0 ? Math.round((val1 - val2) / val2 * 1000) / 10 : 0;


        var obj = {
            value: Math.round(val1 * 1000) / 1000, //元
            itemStyle: {
                normal: {
                    color: color,
                }
            },
            label: {
                normal: {
                    color: color2,
                }
            },
        };


        xAxisData.push(companyCode2Nameabbr[compcode]);
        data_s1.push(obj);

        if (tb != 0) {
            data_s2.push(tb);
        } else {
            data_s2.push(0);
        }

    }



    var chart = echarts.init(document.getElementById(chart_id));

    var option = {
        tooltip: {
            show: true,
            formatter: function(params, ticket, callback) {
                if (params.seriesName == '同比') {
                    return params.seriesName + '<br>' + params.name + ': ' + params.value + '%';
                } else {
                    return params.seriesName + '<br>' + params.name + ': ' + params.value + '';
                }
            },
            extraCssText: "color:#021e55; padding:5px 10px 6px 10px; box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);background: rgb(178,225,254);background: -moz-linear-gradient(top,  rgba(178,225,254,1) 0%, rgba(100,192,250,1) 100%);background: -webkit-linear-gradient(top,  rgba(178,225,254,1) 0%,rgba(100,192,250,1) 100%);background: linear-gradient(to bottom,  rgba(178,225,254,1) 0%,rgba(100,192,250,1) 100%);filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#b2e1fe', endColorstr='#64c0fa',GradientType=0 );",
            backgroundColor: '',

        },
        legend: {
            show: true,
            orient: 'horizontal',
            //x: 'center',
            right: 10,
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: '#45a5f6',
                fontSize: 12 + fontSizeDiff()
            },
            data: [{
                name: '座公里收入',
                icon: 'rect',
            }, {
                name: '同比',
                icon: 'circle',
            }]
        },
        grid: {
            top: 66,
            left: marginx,
            right: marginx,
            bottom: 55,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: true, // 去掉x轴两边的留白, 空着不显示line难看
            nameTextStyle: {
                color: '#45a5f6'
            },
            axisLine: {
                lineStyle: {
                    color: '#45a5f6' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#45a5f6', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
                formatter: function(value, index) {
                    return chartDateFormatter(value, index);
                },
            },
            splitLine: {
                show: false, // 不显示刻度线
            },
            axisTick: {
                show: false, // 不显示刻度线
            }

        }],
        yAxis: [{
            type: 'value',
            name: '', //座公里收入
            nameLocation: 'end',
            //min: 0,
            //max: 100,
            //interval: 10,
            nameTextStyle: {
                color: '#45a5f6',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#45a5f6',
                    fontSize: 12 + fontSizeDiff(),
                    align: 'left',
                },
                margin: 30,
                formatter: '{value}',
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(69,165,246,0.1)'] // 分割线颜色
                }
            }
        }, {
            type: 'value',
            name: '', //同比
            nameLocation: 'end',
            position: 'right',
            //min: 0,
            //max: 100,
            //interval: 10,
            nameTextStyle: {
                color: '#b39a22',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#b39a22',
                    fontSize: 12 + fontSizeDiff(),
                    align: 'right',
                },
                margin: 30,
                formatter: '{value}%',
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ['rgba(69,165,246,0.1)'] // 分割线颜色
                }
            }
        }],
        series: [{
            name: '座公里收入',
            type: 'bar',
            barWidth: 12,
            yAxisIndex: 0,
            data: data_s1,
            label: {
                normal: {
                    show: true,
                    position: 'top'
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[0][0]
                        }, {
                            offset: 1,
                            color: colors[0][1]
                        }]),
                }
            },
        }, {
            name: '同比',
            type: 'line',
            smooth: false,
            yAxisIndex: 1,
            data: data_s2,
            symbol: 'circle',
            symbolSize: 9,
            lineStyle: {
                normal: {
                    color: colors[1][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[1][0],
                    opacity: 1, //折线拐点

                }
            },

        }]
    };

    chart.setOption(option);



    // ----------------- 宽体／窄体／直线 分类 组 -----------------

    drawAcTypeSections(marginx, comp_ac0.length, dat_length - comp_ac0.length - comp_ac2.length, comp_ac2.length);



}

// 宽体／窄体／直线 分类 组
function drawAcTypeSections(marginx, length0, length1, length2) {


    var width = Math.round($('#chart_bg').width());
    var height = Math.round($('#chart_bg').height());
    $('#chart_cvs').attr('width', width);
    $('#chart_cvs').attr('height', height);

    var canvas = document.getElementById('chart_cvs');
    var context = canvas.getContext('2d');
    context.clearRect(0, 0, canvas.width, canvas.height);

    marginx += 0.5;

    var y = height - 10.5;
    var col = length0 + length1 + length2;
    var colw = (width - marginx - marginx) / col;
    var sec1w = Math.round(colw * length0);
    var sec2w = Math.round(colw * length1);
    var sec3w = Math.round(colw * length2);

    var colors = ['#a38763', '#51b3fe', '#0a9184']
    var txtw = 56;

    context.lineWidth = 1;


    // ======== 1
    context.strokeStyle = colors[0];
    context.beginPath();

    var sw = sec1w;
    var w = (sw - txtw) / 2;
    var ll = marginx;

    //|
    context.moveTo(ll, y - 5);
    context.lineTo(ll, y + 5);

    //-
    context.moveTo(ll, y);
    context.lineTo(ll + w, y);

    context.moveTo(ll + w + txtw, y);
    context.lineTo(ll + sw, y);

    //|
    context.moveTo(ll + sw, y - 5);
    context.lineTo(ll + sw, y + 5);

    context.stroke();

    context.font = "12px Microsoft Yahei";
    context.fillStyle = colors[0];
    context.fillText("宽体机", ll + w + 10, y + 3);


    // ======== 2
    context.strokeStyle = colors[1];
    context.beginPath();

    var sw = sec2w;
    var w = (sw - txtw) / 2;
    var ll = marginx + sec1w;

    //-
    context.moveTo(ll, y);
    context.lineTo(ll + w, y);

    context.moveTo(ll + w + txtw, y);
    context.lineTo(ll + sw, y);

    //|
    context.moveTo(ll + sw, y - 5);
    context.lineTo(ll + sw, y + 5);

    context.stroke();

    context.font = "12px Microsoft Yahei";
    context.fillStyle = colors[1];
    context.fillText("窄体机", ll + w + 10, y + 3);


    // ======== 3
    context.strokeStyle = colors[2];
    context.beginPath();

    var sw = sec3w;
    var w = (sw - txtw) / 2;
    var ll = marginx + sec1w + sec2w;

    //-
    context.moveTo(ll, y);
    context.lineTo(ll + w, y);

    context.moveTo(ll + w + txtw, y);
    context.lineTo(ll + sw, y);

    //|
    context.moveTo(ll + sw, y - 5);
    context.lineTo(ll + sw, y + 5);

    context.stroke();

    context.font = "12px Microsoft Yahei";
    context.fillStyle = colors[2];
    context.fillText("支线机", ll + w + 10, y + 3);
}

function getTHB(v) {
    return v == 0 || isNaN(v) ? '-' : v;
}



function getCurrentDate() {
    var date = '';
    if (date_type == 'L') {
        date = $('#main_cb_week').attr('data');
    } else if (date_type == 'M') {
        date = $('#main_cb_month').attr('data');
    } else if (date_type == 'D') {
        date = $('#main_cb_day').attr('data');
    }
    return date;
}


function updateAllKpi(data, label) {
    if (!kpiDataReady) {
        return;
    }

    if (all_company_data == undefined || all_company_data['kpi_value_d'] == undefined || all_company_data_ac == undefined || all_company_data_ac['kpi_value_d'] == undefined || all_company_data_ac['kpi_value_tq_d'] == undefined || all_company_data_ac['kpi_ratio_tq_d'] == undefined) {
        setTimeout(updateAllKpi, 10, data, label)
        return;
    }

    resetUI();

    setBlockBKpi();
    setBlockMLKpi();
    setBlockMR1Kpi();
    setBlockMR2Kpi();


    getAcCnt();


    // 自动循环切换机型card
    clearTimeout(itv_autoSwitchCard);
    itv_autoSwitchCard = setTimeout(autoSwitchCard, 20000);


    // 自动循环切换两个TAB
    clearTimeout(itv_autoSwitchTab);
    itv_autoSwitchTab = setTimeout(autoSwitchTab, 20000);

    setExtLink();

    setTitleDate();
}


function resetUI() {

    // 清零
    comp_ac_type0 = [];
    comp_ac_type1 = [];
    comp_ac_type2 = [];

    accode2num = {};

    /*
    var list0 = ac_type0;
    var list1 = ac_type1;
    var list2 = ac_type2;
    var all_ac = list0.concat(list1, list2);

    var compcode = current_company_code;
    var len = all_ac.length;
    for(var i=0; i<len; i++){

        // 显示机型
        var ac = all_ac[i];

        $('#ac_kpi_cards .itm'+i+' .num .val').text('-');

        // 利用率
        $('#ac_kpi_cards .itm'+i+' .b2 .green').hide();
        $('#ac_kpi_cards .itm'+i+' .b2 .red').hide();

        // 利用率
        $('#ac_kpi_cards .itm'+i+' .b1 .b .v').text('-');

        // 利用率 同比
        $('#ac_kpi_cards .itm'+i+' .b2 .b .v').text('-');
        
        // 客座率
        $('#ac_kpi_cards .itm'+i+' .b4 .green').hide();
        $('#ac_kpi_cards .itm'+i+' .b4 .red').hide();

        // 客座率
        $('#ac_kpi_cards .itm'+i+' .b3 .b .v').text('-');

        // 客座率 同比
        $('#ac_kpi_cards .itm'+i+' .b4 .b .v').text('-');

    }
    */
}



// 自动循环切换机型Card -----------------

var itv_autoSwitchCard;
var itv_checkAudioEnd;

function autoSwitchCard() {
    clearTimeout(itv_autoSwitchCard);

    if (!autoSwitch) {
        itv_autoSwitchCard = setTimeout(autoSwitchCard, 10);
        return;
    }

    if (audioEnded || audioPaused) {

        var list0 = comp_ac_type0;
        var list1 = comp_ac_type1;
        var list2 = comp_ac_type2;
        var all_ac = list0.concat(list1, list2);

        var idx = all_ac.indexOf(selectedAc);
        var nextidx;
        var ac;
        if (idx < all_ac.length - 1) {
            nextidx = idx + 1;
        } else {
            nextidx = 0;
        }
        if (nextidx > 7) {
            $('.btn_next').click();
        } else {
            $('.btn_prev').click();
        }
        ac = all_ac[nextidx];
        selectCard(ac);

    } else {
        itv_checkAudioEnd = setTimeout(autoSwitchCard, 10);
    }

}

// -----------------



var currentTabIndex = 0;

$('.block_mr .tab1').on('click', function(e) {
    currentTabIndex = 0;

    $('#chart_mr1').show();
    $('#chart_mr2').hide();
    $('.block_mr .tab1').addClass('selected');
    $('.block_mr .tab2').removeClass('selected');

    setBlockMR1Kpi();

    clearTimeout(itv_autoSwitchTab);
    itv_autoSwitchTab = setTimeout(autoSwitchTab, 20000);
});

$('.block_mr .tab2').on('click', function(e) {
    currentTabIndex = 1;

    $('#chart_mr1').hide();
    $('#chart_mr2').show();
    $('.block_mr .tab1').removeClass('selected');
    $('.block_mr .tab2').addClass('selected');

    setBlockMR2Kpi();

    clearTimeout(itv_autoSwitchTab);
    itv_autoSwitchTab = setTimeout(autoSwitchTab, 20000);

});


// 自动循环切换两个TAB -----------------

var itv_autoSwitchTab;

function autoSwitchTab() {
    clearTimeout(itv_autoSwitchTab);

    if (!autoSwitch) {
        itv_autoSwitchTab = setTimeout(autoSwitchTab, 10);
        return;
    }

    if (currentTabIndex == 0) {
        $('.block_mr .tab2').click();
    } else {
        $('.block_mr .tab1').click();
    }

    itv_autoSwitchTab = setTimeout(autoSwitchTab, 20000);

}


// ---------------------- 


function onCompanyChanged(comp_code) {
    current_company_code = comp_code;
    checkCompanyReay();
}

function checkCompanyReay() {
    if (companyCode2Name[current_company_code] == undefined) {
        setTimeout(checkCompanyReay, 0);
    } else {
        updateAllKpi();
        resetPagePrevNext();
    }
}



function setTitleDate() {
    var date = '';
    if (date_type == 'L') {
        date = $('#main_cb_week .combobox_label').text();
    } else if (date_type == 'M') {
        date = $('#main_cb_month .combobox_label').text();
    }
    $('.pagetitle .maintitle .date').html(date); // 页面标题-时间
}



// HBI 外部跳转链接
function checkUserInfoLoaded() {
    if (!usersCompayCodeList || usersCompayCodeList.length == 0) {
        setTimeout(checkUserInfoLoaded, 10);
        return;
    }

    if (usersCompayCodeList.indexOf(parent_company) > -1) {
        $('#ext_link1').show();

    }
}

function setExtLink() {
    if (!weekDateRangeList) {
        setTimeout(setExtLink, 0);
        return;
    }
    var date = getCurrentDate();
    var daterange;
    var monthdate1;
    var monthdate2;
    var datetype;
    if (date_type == 'L') {
        datetype = '例会周';
        daterange = weekDateRangeList[date];
    } else if (date_type == 'D') {
        datetype = '日';
        daterange = date;
    } else {
        datetype = '月';

        var m = Number(date.substr(4, 2));
        var m1 = [1, 3, 5, 7, 8, 10, 12];
        var m2 = [2, 4, 6, 9, 11];
        var end;
        if (m1.indexOf(m) > -1) {
            end = 31;
        } else if (m2.indexOf(m) > -1) {
            end = 30;
        } else {
            end = 28;
        }
        // monthdate1 = date+'01';
        // monthdate2 = date+''+end;

        monthdate1 = date;
        monthdate2 = date;

        daterange = date;
    }
    $('#ext_link2').attr('href', encodeURI('http://cdp.hnair.net:8080/HBI/rdThird.do?res_id=144972618983939713&paramArr=4收入类型=<增值税>::3日期=<' + daterange + '>::2报表类型=<' + datetype + '>::1公司=<' + current_company_code + '>::5指标=<小时收入>'));
    $('#ext_link1').attr('href', encodeURI('http://cdp.hnair.net:8080/HBI/rdThird.do?res_id=144349932882834699&paramArr=2日期=<' + daterange + '>::1报表类型=<' + datetype + '>::3收入类型=<增值税>::4机型=<默认机型>'));
    // if (date_type == 'L') {
    //     $('#ext_link1').attr('href', encodeURI('http://cdp.hnair.net:8080/HBI/rdThird.do?res_id=146277592351729810&paramArr=日期=<' + daterange + '>::报表类型=<' + datetype + '>')); //HNA_PCR_427
    // } else {
    //     $('#ext_link1').attr('href', encodeURI('http://cdp.hnair.net:8080/HBI/rdThird.do?res_id=146277595260497218&paramArr=起始日期=<' + monthdate1 + '>::结束日期=<' + monthdate2 + '>')); //20180702
    // }
    // $('#ext_link2').attr('href', encodeURI('http://cdp.hnair.net:8080/HBI/rdThird.do?res_id=144349932882834699&paramArr=2日期=<' + daterange + '>::1报表类型=<' + datetype + '>::3收入类型=<增值税>::4机型=<默认机型>'));

}
checkUserInfoLoaded();
regTooltip('.ext_link', '查看关联报表');

dfd.done(function () {
    if (hasAllCompanyPermission()) {
        $(".allCompanyData").removeClass("hide");
        return;
    }
})
