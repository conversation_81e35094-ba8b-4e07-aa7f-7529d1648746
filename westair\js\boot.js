(function () {

    function resize() {
        $('#main').height($(window).height() - $('#header').height());
    }

    $(window).resize(resize);
    resize();


    $(document).ready(function () {
        loadScript([
            'lib/echarts-x/echarts-x.js',
            'lib/echarts/echarts.js',
            'lib/echarts/chart/map.js'
        ], function () {
            require.config({
                paths: {
                    "lib": 'lib',
                    'echarts-x': 'lib/echarts-x',
                    'echarts': 'lib/echarts'
                }
            });
            boot();
        });
    });

    function boot() {
        require(['js/start']);
    }

    function loadScript(urlList, onload) {
        var count = urlList.length;;
        for (var i = 0; i < urlList.length; i++) {
            var script = document.createElement('script');
            script.async = true;

            script.src = urlList[i];
            script.onload = function () {
                count--;
                if (count === 0) {
                    onload && onload();
                }
            }

            document.getElementsByTagName('head')[0].appendChild(script);
        }
    }
})()