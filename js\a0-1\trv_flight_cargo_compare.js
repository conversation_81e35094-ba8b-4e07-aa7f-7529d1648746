(function () {
    /**
     * 旅客,航班,货运对比
     */

    dfd.done(function () {
        if (!hasAllCompanyPermission()) {
            $(".passenger .detail,.huoyounums ,detail").remove();
            return;
        }
    })

    $(".passenger .detail,.huoyounums ,detail").on("click", function () {
        $("#pop_trv_flight_cargo").removeClass("hide");
        $(".windowMask").removeClass("hide");

        var dateId = $(".passenger .detail").attr("dateId");
        var dateType = getDateType();

        if (window.trvFlightCargoWin == null) {
            inittrvFlightCagoWin(dateType, dateId);
        } else {
            window.trvFlightCargoWin.refreshView(dateType, dateId);
        }
    });


    function inittrvFlightCagoWin(dateType, dateId) {
        var page = new Vue({
            el: '.trv-flight-cargo',
            template: $("#trv_flight_cargo_compare_template").html(),
            data: function () {
                return {
                    "dateType": dateType,
                    "weeks": weeks,
                    showWeekList: false,
                    selectedWeek: null,
                    weekCmpActive: false,
                    flightNums: [],
                    trvNums: [],
                    cargoNums: [],
                    isToday: false,
                    mounted: false,
                    cargoCompany: cargoCompany
                }
            },
            mounted: function () {
                //日
                var me = this;

                if (this.weeks.length > 1) {
                    this.selectedWeek = weeks[1]
                }



                $(me.$refs["datetimepicker_D"]).datetimepicker({
                    defaultDate: new Date(),
                    format: "YYYY/MM/DD",
                    sideBySide: true,
                    maxDate: new Date(),
                    widgetPositioning: {
                        horizontal: 'right'
                    },
                    locale: 'zh-cn'
                });
                //月
                $(me.$refs['datetimepicker_M']).datetimepicker({
                    defaultDate: new Date(),
                    format: "YYYY/MM",
                    viewMode: 'months',
                    sideBySide: true,
                    maxDate: new Date(),
                    widgetPositioning: {
                        horizontal: 'right'
                    },
                    locale: 'zh-cn'
                });
                //年
                $(me.$refs['datetimepicker_Y']).datetimepicker({
                    defaultDate: new Date(),
                    format: "YYYY",
                    viewMode: 'years',
                    sideBySide: true,
                    maxDate: new Date(),
                    widgetPositioning: {
                        horizontal: 'right'
                    },
                    locale: 'zh-cn'
                });


                me.setDatePickerValue(dateType, dateId);
                //先查数据,再做下面事件监听,不然可能会触发dp.change事件,多做一次请求
                me.queryData(me.getDate());

                $(me.$refs["datetimepicker_D"]).on("dp.change", function (e) {
                    me.queryData(e.date._d);

                });
                $(me.$refs['datetimepicker_M']).on("dp.change", function (e) {
                    me.queryData(e.date._d);
                });
                $(me.$refs['datetimepicker_Y']).on("dp.change", function (e) {
                    me.queryData(e.date._d);
                });
                me.mounted = true;
                me.hackEChartDom();


            },
            methods: {
                hackEChartDom() {
                    var me = this;
                    var scale = 1 / pageZoomScale;
                    $(me.$el).find(".chartblock").css('zoom', scale);
                    $(me.$el).find(".chartblock").css('-moz-transform', 'scale(' + scale + ',' + scale + ')');
                    $(me.$el).find(".chartblock").css('-moz-transform-origin', 'left top');
                    $(me.$el).find(".chartblock").each(function (index, el) {
                        var width = $(this).width();
                        var height = $(this).height();
                        $(this).css('width', width * pageZoomScale + 'px');
                        $(this).css('height', height * pageZoomScale + 'px');
                    });
                },
                setDatePickerValue(dateType, dateId) {
                    var me = this;
                    if (dateType != 'L') {
                        $(me.$refs[`datetimepicker_${dateType}`]).data("DateTimePicker").date(me.getDateStr(dateType, dateId));
                    } else {
                        var selectedWeek = me.weeks.find(v => { return v.DATE_ID == dateId });
                        if (me.selectedWeek != selectedWeek) {
                            me.selectedWeek = selectedWeek;
                            //改变才数据,且在mounted 之后,
                            if (me.mounted) {
                                me.queryData(selectedWeek);
                            }

                        }

                    }
                },
                refreshView(dateType, dateId) {
                    var me = this;
                    if (me.dateType != dateType) {
                        me.switchDateType(dateType);
                    } else {
                        me.setDatePickerValue(dateType, dateId);
                    }

                },
                getDateStr(dateType, dateId) {
                    if (dateType == 'D') {
                        return moment(dateId, 'YYYYMMDD').format('YYYY/MM/DD');
                    } else if (dateType == 'M') {
                        return moment(dateId, 'YYYYMM').format('YYYY/MM');;
                    } else if (dateType == 'Y') {
                        return dateId;
                    }
                },
                getWeekDesc() {
                    return this.selectedWeek ? this.selectedWeek.DATE_DESC_XS : ''
                },
                onWeekMouseOut() {
                    this.weekCmpActive = false;
                    setTimeout(this.validActive, 300)
                },
                onWeekMouseOver() {
                    this.weekCmpActive = true;
                },
                validActive() {
                    if (!this.weekCmpActive) {
                        this.showWeekList = false;
                    }
                },
                dropWeekList() {
                    this.showWeekList = true;
                },
                onWeekChange(week) {
                    this.selectedWeek = week;
                    this.showWeekList = false;
                    this.queryData(week);

                },
                getWeekLabel: function (item) {
                    if (item) {
                        var week = item.DATE_ID.substring(5, 7);
                        var year = item.DATE_ID.substring(0, 4);
                        return `${year}年第${week}周`;
                    }
                },
                closeWin: function () {
                    $("#pop_trv_flight_cargo").addClass("hide");
                    $(".windowMask").addClass("hide");
                },
                switchDateType(dateType) {
                    this.dateType = dateType;
                    var date = this.getDate();
                    this.queryData(date);
                },
                getDate() {
                    var me = this;
                    var dateType = this.dateType;
                    if (dateType == 'D') {
                        return $(me.$refs['datetimepicker_D']).data("DateTimePicker").date().startOf("day")._d;
                    } else if (dateType == 'L') {
                        return this.selectedWeek;
                    } else if (dateType == 'M') {
                        return $(me.$refs['datetimepicker_M']).data("DateTimePicker").date().startOf("day")._d
                    } else if (dateType == 'Y') {
                        return $(me.$refs['datetimepicker_Y']).data("DateTimePicker").date().startOf("day")._d
                    }
                },
                formatData(value, divNum, precision) {
                    if (divNum == 0) {
                        return "-"
                    }
                    return toFixed(value / divNum, precision)
                },
                getKpiData(kpiCode, data) {
                    if (data != null) {
                        var list = data[kpiCode];
                        if (list != null && list.length > 0) {
                            return list[0].KPI_VALUE
                        }
                        return null;
                    }
                    return null;
                },
                getRate(num1, divNum, precision) {
                    if (divNum == 0 || divNum == undefined) {
                        return "-";
                    }
                    return toFixed(num1 / divNum * 100, precision) + "%";
                },
                isSameDate(d1, d2) {
                    if (!d1) {
                        return false;
                    }
                    return moment(d1).diff(moment(d2)) == 0;
                },
                checkIsToday() {
                    if (this.dateType == 'D') {
                        this.isToday = this.isSameDate(this.getDate(), moment().startOf('day')._d);
                        return this.isToday;
                    }
                    this.isToday = false;
                    return this.isToday;
                },
                getKpiValue(kpiCode, data) {
                    if (data != null) {
                        return data[kpiCode];
                    }
                    return null;
                },
                queryTodayData() {
                    var me = this;
                    eking.ui.loading.show();

                    var companyCode = getAllSubCompany().join(",");
                    var startTime = moment().startOf("day").utc().format('YYYY-MM-DD HH:mm:ss');
                    var endDate = moment().endOf("day").utc().format('YYYY-MM-DD HH:mm:ss');
                    var params = {
                        "companyCodes": companyCode,
                        "stdStart": startTime,
                        "stdEnd": endDate
                    }
                    me.trvNums = [];
                    var d1 = $.ajax({
                        type: 'post',
                        url: "/bi/web/getPsrNumStatistics",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(params),
                        success: function (response) {
                            var trvNums = [];
                            var data1 = response.data;
                            var parentCompanyCode = 'HNAHK';
                            var hnaData = data1[parentCompanyCode];
                            var HNA_INL_TRV_NUM = me.getKpiValue('ckiNumL', hnaData);
                            var HNA_INT_TRV_NUM = me.getKpiValue('ckiNumI', hnaData);
                            for (var p in data1) {
                                if (p != parentCompanyCode) {
                                    var item = data1[p];
                                    var data = {
                                        INL_TRV_NUM: me.getKpiValue('ckiNumL', item),
                                        INT_TRV_NUM: me.getKpiValue('ckiNumI', item),
                                        INL_TRV_NUM_P: me.getKpiValue('bookNumL', item),
                                        INT_TRV_NUM_P: me.getKpiValue('bookNumI', item),
                                        companyCode: p,
                                        companyName: companyCode2Nameabbr[p]
                                    }
                                    data = $.extend(data, {
                                        INL_TRV_NUM_RATE: me.getRate(data['INL_TRV_NUM'], HNA_INL_TRV_NUM, 1),
                                        INT_TRV_NUM_RATE: me.getRate(data['INT_TRV_NUM'], HNA_INT_TRV_NUM, 1)
                                    })
                                    trvNums.push(data);
                                }
                            }
                            trvNums.sort(function (a, b) {
                                return getCompanyIndex(a.companyCode) - getCompanyIndex(b.companyCode)
                            });
                            me.trvNums = trvNums;


                        }
                    });


                    var allCompanyList = [];
                    var y8 = {};
                    var hnaData = {};

                    var params2 = {
                        "companyCodes": getAllSubCompanyExcludeY8AndHnaIn().join(","), // 公司二字码
                        "isLongRegsNotIn": false,
                        "stdStartUtcTime": moment().startOf('day').utc().format("YYYY-MM-DD HH:mm:ss"),
                        "stdEndUtcTime": moment().endOf('day').utc().format("YYYY-MM-DD HH:mm:ss")
                    };
                    var d2 = $.ajax({
                        type: 'post',
                        url: "/bi/web/flightAmountStatics?allcompanyExcludeY8",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(params2),
                        success: function (response) {
                            allCompanyList = response.data;// 按公司分组输出，具体看各个对象输出字段
                        }
                    });

                    var params3 = {
                        "companyCodes": 'Y8', // 公司二字码
                        "acOwners": "Y8", // 固定传金鹏过滤非全货机
                        "acTypeNotIn": "D00,D10", //需要过滤掉的机型, 
                        "isLongRegsNotIn": true,
                        "stdStartUtcTime": moment().startOf('day').utc().format("YYYY-MM-DD HH:mm:ss"),
                        "stdEndUtcTime": moment().endOf('day').utc().format("YYYY-MM-DD HH:mm:ss")
                    };
                    var d3 = $.ajax({
                        type: 'post',
                        url: "/bi/web/flightAmountStatics?y8",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(params3),
                        success: function (response) {
                            var data = response.data;// 按公司分组输出，具体看各个对象输出字段
                            y8 = data['Y8'];

                        }
                    });

                    var params4 = {
                        "companyCodes": getAllSubCompany(), // 公司二字码
                        "acOwners": "Y8", // 固定传金鹏过滤非全货机
                        "acTypeNotIn": "D00,D10", //需要过滤掉的机型, 
                        "isLongRegsNotIn": true,
                        "stdStartUtcTime": moment().startOf('day').utc().format("YYYY-MM-DD HH:mm:ss"),
                        "stdEndUtcTime": moment().endOf('day').utc().format("YYYY-MM-DD HH:mm:ss")
                    };

                    var d4 = $.ajax({
                        type: 'post',
                        url: "/bi/web/flightAmountStaticV2?total",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(params4),
                        success: function (response) {
                            hnaData = response;

                        }
                    });

                    var d5 = me.queryCargo(new Date());

                    me.flightNums = [];

                    var dtds = [d1, d2, d3, d4, d5];
                    $.when.apply(this, dtds).then(function () {
                        eking.ui.loading.hide();

                        // 国际计划航班总数：pftci
                        // 国内计划航班总数：pftcl
                        // 国际已执行航班总数：cftci
                        // 国内已执行航班总数：cftcl
                        var flightNums = [];
                        allCompanyList['Y8'] = y8;
                        var data1 = allCompanyList;
                        var parentCompanyCode = 'HNAHK';
                        var HNA_EXCUTED_NO_INL = me.getKpiValue('cftcl', hnaData);
                        var HNA_EXCUTED_NO_INT = me.getKpiValue('cftci', hnaData);
                        for (var p in data1) {
                            if (p != parentCompanyCode) {
                                var item = data1[p];
                                var data = {
                                    EXCUTED_NO_INL: me.getKpiValue('cftcl', item),
                                    EXCUTED_NO_INT: me.getKpiValue('cftci', item),
                                    EXCUTED_NO_INL_P: me.getKpiValue('pftcl', item),
                                    EXCUTED_NO_INT_P: me.getKpiValue('pftci', item),
                                    companyCode: p,
                                    companyName: companyCode2Nameabbr[p]
                                }
                                data = $.extend(data, {
                                    EXCUTED_NO_INL_RATE: me.getRate(data['EXCUTED_NO_INL'], HNA_EXCUTED_NO_INL, 1),
                                    EXCUTED_NO_INT_RATE: me.getRate(data['EXCUTED_NO_INT'], HNA_EXCUTED_NO_INT, 1)
                                })
                                flightNums.push(data);

                            }
                        }
                        flightNums.sort(function (a, b) {
                            return getCompanyIndex(a.companyCode) - getCompanyIndex(b.companyCode)
                        });
                        me.flightNums = flightNums;


                    });

                },
                queryData(date) {
                    var me = this;
                    if (me.checkIsToday()) {
                        me.queryTodayData();
                        return;
                    }
                    eking.ui.loading.show();
                    var kpi_list = [
                        "EXCUTED_NO_INL",//国内已执行
                        "EXCUTED_NO_INT",//国际已执行
                    ];

                    var param = {
                        // "LIMIT": 3,
                        // "QUERY_DATE": formatDate(getEndDate(date, this.dateType)),
                        // "DATE_TYPE_CN": getDateTypeCn(this.dateType),// 例会周L、年Y、月M、日D
                        "DATE_ID": getDateId(date, this.dateType),
                        "DATE_TYPE": this.dateType,
                        'KPI_CODE': kpi_list.join(','),
                        'COMP_CODE': getAllCompany(true).join(",")
                    }
                    var d1 = $.ajax({
                        type: 'post',
                        url: "/bi/query/getfaccomkpi?queryFlightNum4CapacityWin",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(param),
                        success: function (response) {
                            var flightNums = [];
                            var data1 = response.data;
                            var parentCompanyCode = 'HNAHK';
                            var hnaData = data1[parentCompanyCode];
                            var HNA_EXCUTED_NO_INL = me.getKpiData('EXCUTED_NO_INL', hnaData);
                            var HNA_EXCUTED_NO_INT = me.getKpiData('EXCUTED_NO_INT', hnaData);
                            for (var p in data1) {
                                if (p != parentCompanyCode) {
                                    var item = data1[p];
                                    var data = {
                                        EXCUTED_NO_INL: me.getKpiData('EXCUTED_NO_INL', item),
                                        EXCUTED_NO_INT: me.getKpiData('EXCUTED_NO_INT', item),
                                        companyCode: p,
                                        companyName: companyCode2Nameabbr[p]
                                    }
                                    data = $.extend(data, {
                                        EXCUTED_NO_INL_RATE: me.getRate(data['EXCUTED_NO_INL'], HNA_EXCUTED_NO_INL, 1),
                                        EXCUTED_NO_INT_RATE: me.getRate(data['EXCUTED_NO_INT'], HNA_EXCUTED_NO_INT, 1)
                                    })
                                    flightNums.push(data);

                                }
                            }
                            flightNums.sort(function (a, b) {
                                return getCompanyIndex(a.companyCode) - getCompanyIndex(b.companyCode)
                            });
                            me.flightNums = flightNums;


                        }
                    });

                    var kpi_list2 = [
                        "INL_TRV_NUM",// 国内旅客量
                        "INT_TRV_NUM" //国际旅客量
                    ];

                    var param2 = {
                        "DATE_ID": getDateId(date, this.dateType),
                        "DATE_TYPE": this.dateType,
                        'KPI_CODE': kpi_list2.join(','),
                        'COMP_CODE': getAllCompany(true).join(",")
                    }
                    var d2 = $.ajax({
                        type: 'post',
                        url: "/bi/query/getfaccomkpi?queryTrv4CapacityWin",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(param2),
                        success: function (response) {
                            var trvNums = [];
                            var data1 = response.data;
                            var parentCompanyCode = 'HNAHK';
                            var hnaData = data1[parentCompanyCode];
                            var HNA_INL_TRV_NUM = me.getKpiData('INL_TRV_NUM', hnaData);
                            var HNA_INT_TRV_NUM = me.getKpiData('INT_TRV_NUM', hnaData);
                            for (var p in data1) {
                                if (p != parentCompanyCode) {
                                    var item = data1[p];
                                    var data = {
                                        INL_TRV_NUM: me.getKpiData('INL_TRV_NUM', item),
                                        INT_TRV_NUM: me.getKpiData('INT_TRV_NUM', item),
                                        companyCode: p,
                                        companyName: companyCode2Nameabbr[p]
                                    }
                                    data = $.extend(data, {
                                        INL_TRV_NUM_RATE: me.getRate(data['INL_TRV_NUM'], HNA_INL_TRV_NUM, 1),
                                        INT_TRV_NUM_RATE: me.getRate(data['INT_TRV_NUM'], HNA_INT_TRV_NUM, 1)
                                    })
                                    trvNums.push(data);


                                }
                            }
                            trvNums.sort(function (a, b) {
                                return getCompanyIndex(a.companyCode) - getCompanyIndex(b.companyCode)
                            });
                            me.trvNums = trvNums;


                        }
                    });


                    var d3 = me.queryCargo(date);

                    var dtds = [d1, d2, d3];
                    $.when.apply(this, dtds).then(function () {
                        eking.ui.loading.hide();
                    });

                },

                queryCargo(date) {
                    var me = this;
                    var companyIds = [];
                    for (var i = 0; i < this.cargoCompany.length; i++) {
                        companyIds.push(this.cargoCompany[i].id);
                    }
                    var param3 = {
                        "DATE_ID": getDateId(date, this.dateType),
                        "DATE_TYPE": this.dateType,
                        'KPI_CODE': 'DETAIL_CARGO_VOLUME',
                        'COMP_ID': companyIds.join(",")
                    }

                    return $.ajax({
                        type: 'post',
                        url: "/bi/query/getfaccomkpi?cargo_num",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(param3),
                        success: function (response) {
                            var dataCargo = response.data;
                            var totalValue = 0;
                            var allItem = dataCargo[100] ? dataCargo[100].DETAIL_CARGO_VOLUME : [];
                            if (allItem.length > 0) {
                                totalValue = allItem[0].KPI_VALUE
                            }

                            var seriesdata = [];
                            var index = 0;

                            me.cargoCompany.forEach(i => {
                                if (i.id != 100) {
                                    var valueItem = dataCargo[i.id] ? dataCargo[i.id].DETAIL_CARGO_VOLUME : [];
                                    var value = 0;
                                    if (valueItem.length > 0) {
                                        value = valueItem[0].KPI_VALUE
                                    }
                                    var item = {
                                        name: i.name,
                                        value: Number(value).toFixed(2),
                                        rate: totalValue == 0 ? "-" : toFixed(value / totalValue * 100, 1) + "%",
                                        color: colors[index]
                                    }

                                    seriesdata.push(item);
                                    index++;
                                }
                            })
                            me.cargoNums = seriesdata;
                            me.drawChart(seriesdata);
                        }
                    });
                },
                drawChart(seriesdata) {
                    var me = this;
                    var chart = echarts.init(me.$refs['cargoNumDom'], null, { renderer: 'svg' });
                    var option = {
                        color: colors,
                        series: [{
                            type: 'pie',
                            radius: ['40%', '60%'],
                            avoidLabelOverlap: true,
                            label: {
                                show: false,
                                position: 'center'
                            },
                            emphasis: {
                                label: {
                                    show: true,
                                    fontSize: '14',
                                    fontWeight: 'bold'
                                }
                            },
                            labelLine: {
                                show: false
                            },
                            data: seriesdata
                        }]

                    };
                    chart.setOption(option);
                }

            }
        });

        window.trvFlightCargoWin = page;

    }





})()