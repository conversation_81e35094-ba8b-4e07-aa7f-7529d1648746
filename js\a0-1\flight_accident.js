(function () {
    /**
     * 旅客,航班,货运对比
     */

    $(".abnormal .detail").on("click", function () {
        $("#pop_flight_accident").removeClass("hide");
        $(".windowMask").removeClass("hide");
        var dateType = getDateType();
        var dateId = getDateId(getCurrentDayObj(), dateType)
        if (window.flightAccidentWin == null) {
            initFlightAccidentWin(dateType, dateId);
        } else {
            window.flightAccidentWin.refreshView(dateType, dateId);
        }
    });


    function initFlightAccidentWin(dateType, dateId) {
        var page = new Vue({
            el: '.flight-accident',
            template: $("#flight_accident_template").html(),
            data: function () {
                return {
                    "dateType": dateType,
                    "weeks": weeks,
                    showWeekList: false,
                    selectedWeek: null,
                    weekCmpActive: false,
                    mounted: false,

                    // 查询参数
                    startTime1: new Date(),
                    endTime1: new Date(),
                    startTime2: new Date(),
                    endTime2: new Date(),

                    flightAccidentData1: [],
                    flightAccidentData2: [],
                    flightAccidentData3: [],    // By Properties
                    flightAccidentCount: 0,     // 本期总数
                    flightAccidentComp: 0,       // 与上期环比
                    flightAccidentCompText: '0',  // 与上期环比（文本显示）
                    eventType: '1',
                    allEventList: [],
                    companyEventList: [],
                    activeItem: {},
                    activeEvent: {},
                    colors: ["#ca0000", "#ff0000", "#ff9f00", "#ffff00", " #ea68a2", "#8957a1"],
                    status: { "1": "调查分析中", "2": "落实措施中", "3": "已关闭", "4": "待定" }
                }
            },
            mounted: function () {
                //日
                var me = this;

                if (this.weeks.length > 1) {
                    this.selectedWeek = weeks[1]
                }

                $(me.$refs["datetimepicker_D"]).datetimepicker({
                    defaultDate: new Date(),
                    format: "YYYY/MM/DD",
                    sideBySide: true,
                    maxDate: new Date(),
                    widgetPositioning: {
                        horizontal: 'right'
                    },
                    locale: 'zh-cn'
                });
                //月
                $(me.$refs['datetimepicker_M']).datetimepicker({
                    defaultDate: new Date(),
                    format: "YYYY/MM",
                    viewMode: 'months',
                    sideBySide: true,
                    maxDate: new Date(),
                    widgetPositioning: {
                        horizontal: 'right'
                    },
                    locale: 'zh-cn'
                });
                //年
                $(me.$refs['datetimepicker_Y']).datetimepicker({
                    defaultDate: new Date(),
                    format: "YYYY",
                    viewMode: 'years',
                    sideBySide: true,
                    maxDate: new Date(),
                    widgetPositioning: {
                        horizontal: 'right'
                    },
                    locale: 'zh-cn'
                });


                me.setDatePickerValue(dateType, dateId);

                $(me.$refs["companyList"]).niceScroll({
                    cursorcolor: "#1b5092",//#CC0071 光标颜色
                    cursoropacitymax: 1, //改变不透明度非常光标处于活动状态（scrollabar“可见”状态），范围从1到0
                    touchbehavior: false, //使光标拖动滚动像在台式电脑触摸设备
                    cursorwidth: "8px", //像素光标的宽度
                    cursorborder: "0px solid #fff",
                    cursorborderradius: "3px",//以像素为光标边界半径
                    autohidemode: false, //是否隐藏滚动条
                    background: 'rgba(27, 80, 146, 0.3)'
                });

                //先查数据,再做下面事件监听,不然可能会触发dp.change事件,多做一次请求
                me.queryData(me.getDate());

                $(me.$refs["datetimepicker_D"]).on("dp.change", function (e) {
                    me.queryData(e.date._d);

                });
                $(me.$refs['datetimepicker_M']).on("dp.change", function (e) {
                    me.queryData(e.date._d);
                });
                $(me.$refs['datetimepicker_Y']).on("dp.change", function (e) {
                    me.queryData(e.date._d);
                });
                me.mounted = true;
                me.hackEChartDom();


            },

            methods: {
                hackEChartDom() {
                    var me = this;
                    var scale = 1 / pageZoomScale;
                    $(me.$el).find(".chartblock").css('zoom', scale);
                    $(me.$el).find(".chartblock").css('-moz-transform', 'scale(' + scale + ',' + scale + ')');
                    $(me.$el).find(".chartblock").css('-moz-transform-origin', 'left top');
                    $(me.$el).find(".chartblock").each(function (index, el) {
                        var width = $(this).width();
                        var height = $(this).height();
                        $(this).css('width', width * pageZoomScale + 'px');
                        $(this).css('height', height * pageZoomScale + 'px');
                    });
                },
                setDatePickerValue(dateType, dateId) {
                    var me = this;
                    if (dateType != 'L') {
                        $(me.$refs[`datetimepicker_${dateType}`]).data("DateTimePicker").date(me.getDateStr(dateType, dateId));
                    } else {
                        var selectedWeek = me.weeks.find(v => { return v.DATE_ID == dateId });
                        if (me.selectedWeek != selectedWeek) {
                            me.selectedWeek = selectedWeek;
                            //改变才数据,且在mounted 之后,
                            if (me.mounted) {
                                me.queryData(selectedWeek);
                            }

                        }

                    }
                },
                refreshView(dateType, dateId) {
                    var me = this;
                    if (me.dateType != dateType) {
                        me.switchDateType(dateType);
                    } else {
                        me.setDatePickerValue(dateType, dateId);
                    }

                },
                getDateStr(dateType, dateId) {
                    if (dateType == 'D') {
                        return moment(dateId, 'YYYYMMDD').format('YYYY/MM/DD');
                    } else if (dateType == 'M') {
                        return moment(dateId, 'YYYYMM').format('YYYY/MM');;
                    } else if (dateType == 'Y') {
                        return dateId;
                    }
                },
                getWeekDesc() {
                    return this.selectedWeek ? this.selectedWeek.DATE_DESC_XS : ''
                },
                onWeekMouseOut() {
                    this.weekCmpActive = false;
                    setTimeout(this.validActive, 300)
                },
                onWeekMouseOver() {
                    this.weekCmpActive = true;
                },
                validActive() {
                    if (!this.weekCmpActive) {
                        this.showWeekList = false;
                    }
                },
                dropWeekList() {
                    this.showWeekList = true;
                },
                onWeekChange(week) {
                    this.selectedWeek = week;
                    this.showWeekList = false;
                    this.queryData(week)
                },
                getWeekLabel: function (item) {
                    if (item) {
                        var week = item.DATE_ID.substring(5, 7);
                        var year = item.DATE_ID.substring(0, 4);
                        return `${year}年第${week}周`;
                    }
                },
                closeWin: function () {
                    $("#pop_flight_accident").addClass("hide");
                    $(".windowMask").addClass("hide");
                },
                switchDateType(dateType) {
                    this.dateType = dateType;
                    this.queryData(this.getDate())
                },
                getDate() {
                    var me = this;
                    var dateType = this.dateType;
                    if (dateType == 'D') {
                        return $(me.$refs['datetimepicker_D']).data("DateTimePicker").date().startOf("day")._d;
                    } else if (dateType == 'L') {
                        return this.selectedWeek;
                    } else if (dateType == 'M') {
                        return $(me.$refs['datetimepicker_M']).data("DateTimePicker").date().startOf("day")._d
                    } else if (dateType == 'Y') {
                        return $(me.$refs['datetimepicker_Y']).data("DateTimePicker").date().startOf("day")._d
                    }
                },
                getQueryParams() {
                    var me = this;
                    var dateType = this.dateType;
                    var selectedDate = me.getDate();

                    this.startTime1 = formatDate(getStartDate(selectedDate, dateType));
                    this.endTime1 = formatDate(getEndDate(selectedDate, dateType));

                    if (dateType == 'D') {
                        var date1 = moment(selectedDate);
                        // 同期 前一天
                        date1.subtract(1, 'day');
                        this.startTime2 = this.endTime2 = date1.format('YYYY-MM-DD');
                    } else if (dateType == 'L') {
                        this.startTime2 = formatDate(moment(getStartDate(selectedDate, dateType)).subtract(7, 'days'));
                        this.endTime2 = formatDate(moment(getEndDate(selectedDate, dateType)).subtract(7, 'days'));
                        if (moment(this.endTime1).isAfter(moment())) {
                            this.endTime1 = moment().format('YYYY-MM-DD');
                        }
                    } else if (dateType == 'M') {
                        var day1 = moment(selectedDate).subtract(1, 'months');
                        this.startTime2 = formatDate(getStartDate(day1, dateType));
                        this.endTime2 = formatDate(getEndDate(day1, dateType));
                    } else if (dateType == 'Y') {
                        var day1 = moment(selectedDate).subtract(1, 'years');
                        this.startTime2 = formatDate(getStartDate(day1, dateType));
                        this.endTime2 = formatDate(getEndDate(day1, dateType));
                    }
                },
                getAcUtilRate(data) {
                    var item = data['AC_UTIL_RATE'];
                    if (item.length > 0) {
                        return Number(item[0].KPI_VALUE).toFixed(2)
                    }
                    return "-";
                },
                getTrvRate(data) {
                    var item = data['TRV_RATE'];
                    if (item.length > 0) {
                        return Number(item[0].KPI_VALUE * 100).toFixed(2) + '%';
                    }
                    return "-";
                },
                calcCompare(data1, data2) {
                    if (data2 <= 0) return 9999;
                    return parseInt((data1 - data2) * 1000 / data2) * 1.0 / 10.0;
                },
                getCompare(name) {
                    var me = this;
                    for (var index = 0; index < me.flightAccidentData1.length; index++) {
                        if (name == me.flightAccidentData1[index].name) return me.flightAccidentData1[index].comp;
                    }
                    return 0;
                },
                getStyle(companyCodeYh) {
                    var companyCode = companyYshcode2Code[companyCodeYh];
                    return `background-image: url(./img/logo_${companyCode}.svg)`
                },
                getAtrrName(flight) {
                    if (flight.eventAttrName != null) {
                        return flight.eventAttrName.split(",")[0]
                    }
                    return ""
                },
                queryEventList() {
                    var me = this;
                    var param1 = {
                        "companyCode": getYhCode(),
                        "startTime": this.startTime1,
                        "endTime": this.endTime1,
                    }
                    return $.ajax({
                        type: 'post',
                        url: "/bi/web/runTimeEventList",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(param1),
                        success: function (response) {
                            me.allEventList = response.records;
                        },
                        error: function () { }
                    });

                },
                hasComment(evt) {
                    if (evt && evt.comments) {
                        return evt.comments.length > 0
                    }
                    return false;
                },

                showDetail(event) {
                    var me = this;
                    eking.ui.loading.show();
                    me.activeEvent = event;
                    var d1 = me.queryComment(event);
                    //var d2 = me.queryCabin(event);
                    var d3 = me.queryFocFlightInfo(event);
                    $.when.apply(this, [d1, d3]).done(function () {
                        eking.ui.loading.hide();
                        me.activeEvent = event;
                        console.log(me.activeEvent);
                        setTimeout(function () {

                            //还是要判断,有可能切换日期等,上面创建的scroll被销毁了
                            if ($(me.$refs["eventDetail"]).getNiceScroll(0)) {
                                $(me.$refs["eventDetail"]).getNiceScroll(0).resize();
                                $(me.$refs["eventDetail"]).getNiceScroll(0).doScrollPos(0, 0);
                            } else {
                                $(me.$refs["eventDetail"]).niceScroll({
                                    cursorcolor: "#1b5092",//#CC0071 光标颜色
                                    cursoropacitymax: 1, //改变不透明度非常光标处于活动状态（scrollabar“可见”状态），范围从1到0
                                    touchbehavior: false, //使光标拖动滚动像在台式电脑触摸设备
                                    cursorwidth: "8px", //像素光标的宽度
                                    cursorborder: "0px solid #fff",
                                    cursorborderradius: "3px",//以像素为光标边界半径
                                    autohidemode: false, //是否隐藏滚动条
                                    background: 'rgba(27, 80, 146, 0.3)'
                                });
                            }
                        }, 300)
                    });

                },
                getStatusText() {
                    var me = this;
                    return me.status[me.activeEvent.status]
                },
                queryComment(evt) {
                    var me = this;
                    return $.ajax({
                        type: 'post',
                        url: "/bi/web/runTimeEventDetail?queryCommentAndStatus",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify({
                            id: evt.id
                        }),
                        success: function (response) {
                            var comments = response.result.comments;
                            var lst = [];
                            for (var i = 0; i < comments.length; i++) {
                                var cmt = comments[i];
                                if (cmt.content != '' && cmt.commentByName != '') {
                                    lst.push(cmt);
                                }
                            }
                            evt.comments = lst;

                        },
                        error: function () { }
                    });

                },
                queryCabin(evt) {
                    var me = this;
                    var param = {
                        "acNo": evt.longReg,
                    }
                    return $.ajax({
                        type: 'post',
                        url: "/bi/web/getAcAircraftList?queryCabin",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(param),
                        success: function (response) {
                            if (response.data && response.data.length > 0) {
                                var ac = response.data[0].data;
                                var cabin = ac.cabin; // 座舱布局
                                evt.cabin = cabin;
                            }

                        },
                        error: function () { }
                    });
                },
                formatTime(time) {
                    if (time) {
                        return moment(time).format('HH:mm')
                    }
                    return "-"
                },
                formatToff(toff) {
                    if (toff) {
                        var hour = toff.substring(0, 2);
                        var mm = toff.substring(2, 4);
                        return `${hour}:${mm}`
                    }
                },
                queryFocFlightInfo(evt) {
                    var me = this;
                    var param = {
                        'datopChn': evt.flightDateStr,
                        'depStn': evt.depstn,
                        'arrstn': evt.arrstn,
                        'flightNo': evt.flightNo
                    }
                    return $.ajax({
                        type: 'post',
                        url: "/bi/web/flightfocinfov2?eventFlightDetail",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(param),
                        success: function (response) {
                            var list = response.data;
                            var fltInfo;
                            if (list && list.length == 1) {
                                fltInfo = list[0];
                            }
                            if (list && list.length > 1) {
                                var fltInfo = list.filter(i => {
                                    return moment(i.t_off_chn).format('HHmm') == evt.std
                                })[0];
                            }
                            if (fltInfo) {
                                evt.std_chn = me.formatTime(fltInfo.std_chn);
                                evt.sta_chn = me.formatTime(fltInfo.sta_chn);
                                evt.t_off_chn = me.formatTime(fltInfo.t_off_chn);
                                evt.t_dwn_chn = me.formatTime(fltInfo.t_dwn_chn);
                            } else {
                                evt.std_chn = '-';
                                evt.sta_chn = '-';
                                evt.t_off_chn = '-';
                                evt.t_dwn_chn = '-';
                            }

                        },
                        error: function () { }
                    });
                },
                queryData(date) {
                    var me = this;
                    eking.ui.loading.show();

                    me.getQueryParams();

                    // Query 
                    var param1 = {
                        "companyCode": getYhCode(), // getAllCompany().join(","),
                        "startTime": this.startTime1,
                        "endTime": this.endTime1,
                    }
                    var d1 = $.ajax({
                        type: 'post',
                        url: "/bi/web/runTimeEventTypeStatic?benqi",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(param1),
                        success: function (response) {
                            if (response.errorcode == '0000') me.flightAccidentData1 = response.result.staticList;
                        },
                        error: function () { }
                    });

                    /* 获取所有上期（按类型统计） */
                    var param2 = {
                        "companyCode": getYhCode(), // getAllCompany().join(","),
                        "startTime": this.startTime2,
                        "endTime": this.endTime2,
                    }
                    var d2 = $.ajax({
                        type: 'post',
                        url: "/bi/web/runTimeEventTypeStatic?shangqi",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(param2),
                        success: function (response) {
                            if (response.errorcode == '0000') me.flightAccidentData2 = response.result.staticList;
                        },
                        error: function () { }
                    });

                    /* 获取所有（按事件属性统计） */
                    var d3 = $.ajax({
                        type: 'post',
                        url: "/bi/web/runTimePropertiesStatic?shuxing",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(param1),
                        success: function (response) {
                            if (response.errorcode == '0000') me.drawChart_FlightAccidentByProperty(response.result.staticList);
                        },
                        error: function () { }
                    });

                    var d4 = me.queryEventList();

                    var dataList = [d1, d2, d3, d4];
                    me.companyEventList = [];
                    $.when.apply(this, dataList).then(function () {
                        var companyEventList = [];
                        // 计算环比
                        me.flightAccidentCount = 0;
                        var sum2 = 0;

                        for (var index = 0; index < me.flightAccidentData1.length; index++) {
                            var item1 = me.flightAccidentData1[index];
                            var item2 = me.flightAccidentData2[index];

                            me.flightAccidentCount += parseInt(item1.count);
                            sum2 += parseInt(item2.count);

                            item1.comp = me.calcCompare(item1.count, item2.count);
                        }

                        me.flightAccidentComp = me.calcCompare(me.flightAccidentCount, sum2);

                        me.flightAccidentData1.forEach((item, index) => {
                            item.color = me.colors[index];
                        })


                        var eventList = me.allEventList;

                        if (!me.activeItem.code) {
                            me.filterEvent(me.flightAccidentData1[0]);
                        } else {
                            me.filterEvent(me.activeItem);
                        }


                        setTimeout(function () {
                            $(me.$refs["companyList"]).getNiceScroll(0).resize();
                        }, 300)

                        eking.ui.loading.hide();
                    });

                },
                hasNext(item) {
                    return item.events.length > item.idx + 4
                },
                hasPre(item) {
                    return item.idx > 0
                },
                prePage(item) {
                    if (!this.hasPre(item)) {
                        return;
                    }
                    let idx = item.idx;
                    let newIdx = idx - 4;
                    let end = idx;
                    item.sliceEvents = item.events.slice(newIdx, end);
                    item.idx = newIdx;
                },
                nextPage(item) {
                    if (!this.hasNext(item)) {
                        return;
                    }
                    let idx = item.idx;
                    let newIdx = idx + 4;
                    let end = idx + 8;
                    item.sliceEvents = item.events.slice(newIdx, end);
                    item.idx = newIdx;
                },
                getEventList(companyEvents, companyCode) {
                    var list = companyEvents[companyCode];
                    if (list) {
                        return list;
                    } else {
                        companyEvents[companyCode] = [];
                        return companyEvents[companyCode];
                    }
                },

                processData(eventList) {
                    var me = this;
                    var companyEventList = [];

                    var companyEvents = {};
                    eventList.forEach(i => {
                        var list = me.getEventList(companyEvents, i.companyCode);
                        list.push(i);
                    });

                    for (var p in companyEvents) {
                        companyEventList.push({
                            companyCode: p,
                            companyName: companyCode2Name[companyYshcode2Code[p]],
                            emptyEvent: false,
                            events: companyEvents[p],
                            sliceEvents: companyEvents[p].slice(0, 4),
                            idx: 0
                        })
                    }

                    companyEventList.sort(function (a, b) {
                        return getCompanyIndex(companyYshcode2Code[a.companyCode]) - getCompanyIndex(companyYshcode2Code[b.companyCode])
                    });

                    if (isAllCompany()) {
                        companylist.forEach(company => {
                            var yhcompanyCode = company.yhscode;
                            if (yhcompanyCode != "") {
                                //公司为空时,也要显示,所以要把其它没有数据的公司加进来
                                if (!companyEvents.hasOwnProperty(yhcompanyCode)) {
                                    companyEventList.push({
                                        companyCode: yhcompanyCode,
                                        companyName: companyCode2Name[companyYshcode2Code[yhcompanyCode]],
                                        emptyEvent: true,
                                        events: [],
                                        sliceEvents: [],
                                        idx: 0
                                    })
                                }
                            }

                        });
                    } else {
                        let yhCode = getYhCode();
                        if (!companyEvents.hasOwnProperty(yhCode)) {
                            companyEventList.push({
                                companyCode: yhCode,
                                companyName: companyCode2Name[companyYshcode2Code[yhCode]],
                                emptyEvent: true,
                                events: [],
                                sliceEvents: [],
                                idx: 0
                            })
                        }
                    }



                    me.companyEventList = companyEventList;
                    me.activeEvent = {};
                    if (me.companyEventList.length > 0) {
                        if (!me.companyEventList[0].emptyEvent) {
                            me.showDetail(me.companyEventList[0].events[0]);
                        }
                    } else {
                        $(me.$refs["eventDetail"]).getNiceScroll().remove();
                    }
                    setTimeout(function () {
                        $(me.$refs["companyList"]).getNiceScroll(0).resize();
                    }, 300)

                },

                filterEvent(item) {
                    var me = this;

                    me.eventType = item.code;
                    var eventList = me.allEventList.filter(i => {
                        return i.eventType == me.eventType;
                    });
                    me.processData(eventList);
                    me.activeItem = item;

                },

                drawChart_FlightAccidentByProperty(inputData) {
                    var me = this;

                    echarts.dispose(me.$refs['flightAccByProperty']);
                    var chart = echarts.init(me.$refs['flightAccByProperty'], null, { renderer: 'svg' });

                    var seriesdata = [];
                    var index = 0;
                    var total = 0;

                    inputData.forEach(i => {
                        var item = {
                            name: i.name,
                            value: parseInt(i.count),
                            color: colors[index],
                            perc: '-'
                        };

                        seriesdata.push(item);
                        index++;
                        total += item.value;
                    })

                    seriesdata.forEach(i => {
                        i.perc = total == 0 ? "-" : toFixed(i.value / total * 100, 1) + "%";
                    });

                    me.flightAccidentData3 = seriesdata;

                    var option = {
                        color: colors,
                        series: [{
                            type: 'pie',
                            radius: ['40%', '60%'],
                            avoidLabelOverlap: true,
                            label: {
                                show: false,
                                position: 'center'
                            },
                            emphasis: {
                                label: {
                                    show: true,
                                    fontSize: '14',
                                    fontWeight: 'bold'
                                }
                            },
                            labelLine: {
                                show: false
                            },
                            data: seriesdata
                        }]

                    };
                    chart.setOption(option);
                }

            }
        });

        window.flightAccidentWin = page;

    }

})()