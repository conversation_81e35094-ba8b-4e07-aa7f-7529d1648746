showLoading();

$(function () {

  var comp_code = 'HU';
  var comp_id = '100100';
  var yhscode = 'HNA';
  companyDtd.done(function () {
    var hash = window.location.hash.substr(1);
    if (!hash) {
      hash = getFirstAuthCompanyCode();
    } else {
      if (!hasCompanyAuth(hash)) {
        showNoPermission();
        return;
      }
    }
    if (!hash) {
      showNoPermission();
      return;
    } else {
      comp_code = hash;
      comp_id = companyCode2CompanyId[hash];
      yhscode = companyCode2YshCode[hash];
    }

    $('#companycombo').attr('code', comp_code);
    $('#companycombo .box1').html(companyCode2Name[comp_code]);
    $(".logo_txt").addClass("logo_" + comp_code);
    var BASE_CITY_LIST = {
      "PEK": '北京',
      "HAK": '海口',

      "XIY": '西安',
      "CAN": '广州',
      "DLC": '大连',
      "TYN": '太原',
      "SZX": '深圳',

      "LHW": '兰州',
      "SYX": '三亚',
      "CSX": '长沙',
      "URC": '乌鲁木齐',
      "CKG": '重庆',
    };

    var BASE_CODE_LIST = {
      "PEK": 'ZBAA',
      "HAK": 'ZJHK',

      "XIY": 'ZLXY',
      "CAN": 'ZGGG',
      "DLC": 'ZYTL',
      "TYN": 'ZBYN',
      "SZX": 'ZGSZ',

      "LHW": 'ZLLL',
      "SYX": 'ZJSY',
      "CSX": 'ZGHA',
      "URC": 'ZWWW',
      "CKG": 'ZUCK',
    };

    // 区域名称
    var AREA_LIST = {
      '华北': '100001',
      '华东': '100002',
      '东北': '100003',
      '西南': '100004',
      '中南': '100005',
      '西北': '100006',
      '新疆': '100007',
    };
    //////// 机场气象告警 //////////
    var unnormalBase = [];
    var unnormalBaseIndex = 0;
    var arpcodes = [];
    var airMap = new Map();

    var comp_cause = ['飞机故障', '运力调配', '工程机务', '航班计划', '航材保障', '航务保障', '机组保障', '飞行机组保障', '乘务组', '乘务组保障', '安全员保障', '空警安全员', '地面保障', '货运保障', '公司原因', '其他航空公司原因'];
    var none_cause = ['公共安全', '机场', '军事活动', '空管', '离港系统', '联检', '旅客', '时刻安排', '民航局航班时刻安排', '天气原因', '油料', '流量控制'];

    var comp_cause_en = {
      '飞机故障': 'A/C Mtc',
      '运力调配': 'Fleet Capacity',
      '工程机务': 'Engineering',
      '航班计划': 'Flt Planning',
      '航材保障': 'A/C MAT Suprt',
      '航务保障': 'Dispatching',
      '机组保障': 'Cabin Crew',
      '飞行机组保障': 'Cockpit Crew',
      '乘务组': 'Cabin Crew',
      '乘务组保障': 'Cabin Crew',
      '安全员保障': 'Security Staff',
      '空警安全员': 'Security Staff',
      '地面保障': 'Grnd Handling',
      '货运保障': 'Cargo Service',
      '公司原因': 'Corporate',
      '其他航空公司原因': 'Others',
    };
    var none_cause_en = {
      '公共安全': 'Pub Security',
      '机场': 'Aerodrome',
      '军事活动': 'Military',
      '空管': 'ATC Service',
      '离港系统': 'DC System',
      '联检': 'Border Check',
      '旅客': 'Passengers',
      '时刻安排': 'ATC Capacity',
      '民航局航班时刻安排': 'ATC Capacity',
      '天气原因': 'Weather',
      '油料': 'Fuel',
      '流量控制': 'Flt Control',
    };
    // 不正常天气基地
    const wxCode2Name = {
      'TS': '干雷',
      'TSRA': '中雷雨',
      '-TSRA': '弱雷雨',
      '+TSRA': '强雷雨',
      'CB': '对流云',
      'TCU': '浓积云',
      'RA': '中雨',
      '+RA': '大雨',
      '+SHRA': '强阵雨',
      'SHRA': '中阵雨',
      'DZ': '毛毛雨',
      'FZRA': '冻雨',
      'GR': '冰雹',
      'GS': '霰',
      'WS': '风切变',
      'FG': '大雾',
      'FU': '烟',
      'HZ': '霾',
      'BR': '轻雾',
      'FZFG': '冻雾',
      'BCFG': '散雾',
      'MIFG': '浅雾',
      'SN': '中雪',
      '+SN': '大雪',
      'SHSN': '阵雪',
      '+SHSN': '强阵雪',
      'BLSN': '高吹雪',
      'DRSA': '低吹雪',
      'SA': '扬沙',
      'SS': '沙暴',
      'BLSA': '高吹沙',
      'DRSA': '低吹沙',
      '+SS': '强沙暴',
      'DU': '浮尘',
    }


    // 航班保障节点
    var fltnodes = ['cncPilotArrTime', 'checkInEnd', 'cncCabinSupplyEndTime', 'cncCleanEndTime', 'cncMCCReleaseTime', 'planeReady', 'cncInformBoardTime', 'cncBoardOverTime', 'cncClosePaxCabinTime', 'cncCloseCargoCabinTime', 'cncPushTime', 'cncACARSTOFF'];

    var marquee_itv;
    var all_flight_list;
    var timeout_next_flt;

    // 缓存航班机组人员，保障节点
    var fltCrwCache = {};
    var fltLegCache = {};


    // 所有飞机架数
    var total_plane = -1;
    // 执行中飞机架数
    var exe_total_plane = -1;
    // 机型对照表
    var actypeMapList;
    var ac_type_list = ['330', '737', '767', '787', '350'];
    var ac_num_list = {
      '330': 26,
      '737': 160,
      '767': 1,
      '787': 29
    };
    //var ac_type_list = ['787', '767', '330', '737', '320', '321', '319', '145', '190'];
    var ac_data_list_ready;
    var ac_data_list;
    var exe_ac_data_list;
    var marquee_itv_airPlane;
    var abnormal_date_type = 'M';//日期类型
    var all_company_data = {};
    // 可显示的历史 数量
    var query_limit = 20;
    var getAllAbnormalKpiData1 = null;
    var flightInfoList = {};
    var legendColorList1 = ['#e95551', '#f7a449', '#fff353', '#21b3ce', '#176ebf', '#0a3a89', '#8619db', '#aa44e1', '#a5a27d', '#925c86'];
    legendColorList1 = legendColorList1.concat(legendColorList1);
    var legendColorList2 = ['#aecffd', '#f98a01', '#d11f1e', '#f3f1d5'];
    var legendColorList3 = ['#e95551', '#f7a449', '#fff353', '#21b3ce', '#176ebf', '#0a3a89', '#8619db', '#aa44e1', '#a5a27d', '#925c86'];
    legendColorList3 = legendColorList3.concat(legendColorList3);
    var legendColorList4 = ['#aecffd', '#f98a01', '#d11f1e', '#f3f1d5', '#f98a01'];

    var lang = getQueryString('lang') || 'cn';

    var url_scale = getQueryString('scale');
    if (url_scale) {
      url_scale = '&scale=' + url_scale;
    } else {
      url_scale = '';
    }

    $('#btn_english').on('click', function () {
      if (lang == 'en') {
        window.location.href = 'index_exhibition.html?lang=cn' + url_scale;
      } else {
        window.location.href = 'index_exhibition.html?lang=en' + url_scale;
      }
    });


    $('#base_cards .card').on('click', function () {
      cardclick($(this));
    });

    function cardclick(el) {
      var id = el.attr('id');
      var code = id.split('arp_code_')[1];
      window.location.href = 'base_exhibition.html?base=' + code + url_scale + "#" + comp_code;

    }

    $('#abnormal_date_select .tab').on('click', function (evt) {
      evt.stopPropagation();
      $('#abnormal_date_select .tab').removeClass('selected');
      $(this).addClass('selected');
      abnormal_date_type = $(this).attr('data-type');
      //$('.main_cb_date').hide();
      //$('#main_cb_' + abnormal_date_type).show();

      if (getAllAbnormalKpiData1) getAllAbnormalKpiData1();
    });

    $('#unnormal_detail').on('click', function () {
      if ($('#block_abnormal_flights .tabc1').is(":visible"))
        $('#block_abnormal_flights .tabc1').slideUp();
      else
        $('#block_abnormal_flights .tabc1').slideDown();
    });

    function parseDateToStr(dat) {
      var year = dat.getFullYear();
      var month = dat.getMonth() + 1;
      var date = dat.getDate();
      var hour = dat.getHours();
      var minute = dat.getMinutes();
      var seconds = dat.getSeconds();
      var mill = dat.getMilliseconds();
      var str = year + '-' + ('0' + month).substr(-2) + '-' + ('0' + date).substr(-2) + ' ' + ('0' + hour).substr(-2) + ':' + ('0' + minute).substr(-2) + ':' + ('0' + seconds).substr(-2) + '.' + mill;
      return str;
    }



    function loadAll() {


      var date = new Date();
      var mm = date.getMonth() + 1;
      var dd = date.getDate();
      if (mm < 10) {
        mm = '0' + mm;
      }
      if (dd < 10) {
        dd = '0' + dd;
      }
      var stdEndUtcTime = date.getFullYear() + '-' + mm + '-' + dd + ' 15:59:59';
      var yesterday_ts = date.getTime() - 86400000;
      date.setTime(yesterday_ts);
      mm = date.getMonth() + 1;
      dd = date.getDate();
      if (mm < 10) {
        mm = '0' + mm;
      }
      if (dd < 10) {
        dd = '0' + dd;
      }
      var stdStartUtcTime = date.getFullYear() + '-' + mm + '-' + dd + ' 16:00:00';


      //-------------------------



      fltCrwCache = {};
      fltLegCache = {};



      // ------------------------------------------------------------------------
      // 航班列表
      // ------------------------------------------------------------------------


      // 开始结束时间
      var date = new Date();
      var mm = date.getMonth() + 1;
      var dd = date.getDate();
      if (mm < 10) {
        mm = '0' + mm;
      }
      if (dd < 10) {
        dd = '0' + dd;
      }
      var stdStart = date.getFullYear() + '-' + mm + '-' + dd + ' 00:00:00';
      var stdEnd = date.getFullYear() + '-' + mm + '-' + dd + ' 23:59:59';


      var statusMap = {
        'ARR': '落地',
        'NDR': '落地',
        'ATD': '推出',
        'ATA': '到达',
        'CNL': '取消',
        'DEL': '延误',
        'DEP': '起飞',
        'RTR': '返航',
        'SCH': '计划'
      };

      var param = {
        "stdStart": stdStart,
        "stdEnd": stdEnd,
        "acOwner": comp_code,
        "statusList": '',
      }
      $.ajax({
        type: 'post',
        url: "/bi/web/getStandardFocFlightInfo",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          all_flight_list = response.data;
        },
        error: function () { }
      });



      // ------------------------------------------------------------------------
      // 欢迎词
      // ------------------------------------------------------------------------

      if (lang == 'cn') {

        var param = {
          'mode': 'query'
        }

        $.ajax({
          type: 'post',
          url: "/bi/web/7x2_welcome",
          contentType: 'application/json',
          dataType: 'json',
          async: true,
          data: JSON.stringify(param),
          success: function (response) {

            checkLogin(response);

            if (response.title != undefined) {
              var msg = response.title[0].txt;
              // 
              if (msg.length > 30) {
                $('#welcome_msg').html('<marquee direction="left" scrollamount="10">' + msg + '</marquee>');
              } else {
                $('#welcome_msg').text(msg);
              }
            }

          },
          error: function (jqXHR, txtStatus, errorThrown) { }
        });

      } else {

        var param = {
          'mode': 'query'
        }

        $.ajax({
          type: 'post',
          url: "/bi/web/7x2_welcome_en",
          contentType: 'application/json',
          dataType: 'json',
          async: true,
          data: JSON.stringify(param),
          success: function (response) {

            checkLogin(response);

            if (response.title != undefined) {
              var msg = response.title[0].txt;
              // 
              if (msg.length > 30) {
                $('#welcome_msg').html('<marquee direction="left" scrollamount="10">' + msg + '</marquee>');
              } else {
                $('#welcome_msg').text(msg);
              }
            }

          },
          error: function (jqXHR, txtStatus, errorThrown) { }
        });

      }



      // ------------------------------------------------------------------------
      // 正常率颜色配置
      // ------------------------------------------------------------------------

      var normal_rate_colors = {};
      var param = {
        'mode': 'query'
      }

      const normal_rate_color = $.ajax({
        type: 'post',
        url: "/bi/web/7x2_normal_rate_color",
        contentType: 'application/json',
        dataType: 'json',
        async: false,
        data: JSON.stringify(param),
        success: function (response) {
          normal_rate_colors = response.ratecolor[0];
        },
        error: function () { }
      });



      // ------------------------------------------------------------------------
      // 各种航班统计信息。。。。
      // ------------------------------------------------------------------------


      var param = {
        "stdStartUtcTime": stdStartUtcTime,
        "stdEndUtcTime": stdEndUtcTime,
        "companyCodes": comp_code,
        "showMore": 1
      }



      $.ajax({
        type: 'post',
        url: "/bi/spring/focStaticApi/flightAmountStaticV2",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (res) {
          var response = res.data;
          var sch_total = Number(response.pftc); //计划航班总数
          var sch_normal = Number(response.pfrtc); //计划航班中正常航班总数
          var pfdappPercent = Number(response.pfdappPercent); //计划航班中的正常航班比率
          // todayNormalRate(pfdappPercent);

          var dftc = response.dftc; //备降航班总数
          var bftc = response.bftc; //返航航班总数
          $('#flt_return_back').text(Number(dftc) + Number(bftc));

          /*
          qftc 取消航班总数
          qftc0 昨日取消航班总数
          qftc1 今日取消航班总数
          qftc2 次日取消航班总数
          */
          $('#flt_cancel').text(response.qftc1); //取消航班总数

          var pfdtc12 = response.pfdtc12; //计划航班中延误1~2小时的航班总数
          var ffdtc12 = response.ffdtc12; //执行航班中延误1~2小时的航班总数
          var rfdtc12 = response.pfdtc12; //未执行航班中延误1~2小时的航班总数
          var cfdtc12 = response.cfdtc12; //已执行航班中延误1~2小时的航班总数
          $('#flt_delay_12').text(Number(pfdtc12));

          var pfdtc24 = response.pfdtc24; //计划航班中延误2-4小时的航班总数
          var ffdtc24 = response.ffdtc24; //执行航班中延误2-4小时的航班总数
          var rfdtc24 = response.pfdtc24; //未执行航班中延误2-4小时的航班总数
          var cfdtc24 = response.cfdtc24; //已执行航班中延误2-4小时的航班总数
          $('#flt_delay_24').text(Number(pfdtc24));

          var pfdtc4 = response.pfdtc4; //计划航班中延误>4小时的航班总数
          var ffdtc4 = response.ffdtc4; //执行航班中延误>4小时的航班总数
          var rfdtc4 = response.pfdtc4; //未执行航班中延误>4小时的航班总数
          var cfdtc4 = response.cfdtc4; //已执行航班中延误>4小时的航班总数
          $('#flt_delay_4').text(Number(pfdtc4));

          // 国际航班延误数量
          var pfdtci = Number(response.pfdtci); //计划航班中延误国际航班总数
          var ffdtci = Number(response.ffdtci); //执行航班中延误国际航班总数
          // $('#flt_delay_int').text(pfdtci + ffdtci);
          $('#flt_delay_int').text(pfdtci);


          var fftc = Number(response.fftc); //执行航班总数
          var cftc = Number(response.cftc); //已执行航班总数
          var exe_rate = cftc / sch_total
          // $('#val_flt_total').text(sch_total);
          $('#val_flt_exec_rate').text(Math.round(exe_rate * 100) + '%');

          setExecRate(exe_rate);


          var pftci = Number(response.pftci); //国际计划航班总数
          var pftcl = Number(response.pftcl); //国内计划航班总数
          var fftci = Number(response.fftci); //国际执行航班总数
          var fftcl = Number(response.fftcl); //国内执行航班总数
          var cftci = Number(response.cftci); //国际已执行航班总数
          var cftcl = Number(response.cftcl); //国内已执行航班总数

          // $('#val_flt_total_china').text(pftcl);
          $('#val_flt_exec_rate_china').text(Math.round(cftcl / pftcl * 100) + '%');
          // $('#val_flt_total_int').text(pftci);
          $('#val_flt_exec_rate_int').text(pftci == 0 ? '0%' : Math.round(cftci / pftci * 100) + '%');



        },
        error: function () { }
      });



      $.ajax({
        type: 'get',
        url: `/bi/spring/sys-company-kpi-value/getByCompanyCode.json?companyCode=${comp_code}`,
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (res) {
          var response = res.data;

          var normalRate = Number(response.normalRate); //计划航班中的正常航班比率
          todayNormalRate(normalRate);

          $('#val_flt_total').text(response.schNo || 0);

          var pftci = Number(response.intSchNo || 0); //国际计划航班总数
          var pftcl = Number(response.inlSchNo || 0); //国内计划航班总数

          $('#val_flt_total_china').text(pftcl);
          $('#val_flt_total_int').text(pftci);

        },
        error: function () { }
      });

      // ------------------------------------------------------------------------
      // 航班总量 已经执行占比 
      // ------------------------------------------------------------------------
      function setExecRate(rate) {
        var rate_flt_exec = rate;
        var rate = rate_flt_exec;

        var canvas = document.getElementById('cvs_flt_count');
        var context = canvas.getContext('2d');
        var x = canvas.width / 2;
        var y = canvas.height / 2;

        // draw back
        var radius = 70;
        var startAngle = Math.PI - Math.PI / 5;
        var endAngle = startAngle + Math.PI + Math.PI / 5 * 2;
        var counterClockwise = false;

        context.beginPath();
        context.arc(x, y, radius, startAngle, endAngle, counterClockwise);
        context.lineWidth = 20;
        context.strokeStyle = '#00438B';
        context.stroke();

        // draw overlay
        var radius = 69;
        var startAngle2 = startAngle;
        var endAngle2 = startAngle + (endAngle - startAngle) * rate;
        var counterClockwise = false;

        context.beginPath();
        context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
        context.lineWidth = 22;

        // add linear gradient
        if (rate < 0.5) {
          var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
        } else {
          var color = context.createLinearGradient(0, 0, canvas.width * rate, 0);
        }
        color.addColorStop(0, '#3A4ABA');
        color.addColorStop(1, '#00AEFA');
        context.strokeStyle = color;
        context.stroke();
      }



      // ------------------------------------------------------------------------
      // 航班正常率
      // ------------------------------------------------------------------------
      function todayNormalRate(ratestr) {

        var ratestr = Math.round(ratestr * 100) / 100;
        $('#today_normal_rate').text(ratestr);

        var rate = ratestr / 100;

        var canvas = document.getElementById('cvs_normal_rate');
        var context = canvas.getContext('2d');
        var x = canvas.width / 2;
        var y = canvas.height / 2;

        // draw back
        var radius = 84;
        var startAngle = Math.PI - Math.PI / 3.6;
        var endAngle = startAngle + Math.PI + Math.PI / 3.6 * 2;
        var counterClockwise = false;

        context.beginPath();
        context.arc(x, y, radius, startAngle, endAngle, counterClockwise);
        context.lineWidth = 40;
        context.strokeStyle = '#00438B';
        context.stroke();

        // draw overlay
        var radius = 84;
        var startAngle2 = startAngle;
        var endAngle2 = startAngle + (endAngle - startAngle) * rate;
        var counterClockwise = false;

        context.beginPath();
        context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
        context.lineWidth = 40;

        // linear gradient
        if (rate < 0.5) {
          var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
        } else if (rate < 0.8) {
          var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
        } else {
          var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, canvas.width / 2);
        }
        color.addColorStop(0, '#122A61');

        if (rate < 0.7) {
          color.addColorStop(1, '#A1263E');
        } else if (rate < 0.8) {
          color.addColorStop(1, '#c29700');
        } else {
          color.addColorStop(1, '#0093d1');
        }

        context.strokeStyle = color;
        context.stroke();

        // draw head
        var radius = 84;
        var startAngle2 = startAngle + (endAngle - startAngle) * rate - (endAngle - startAngle) * 0.01;
        var endAngle2 = startAngle + (endAngle - startAngle) * rate;
        var counterClockwise = false;

        context.beginPath();
        context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
        context.lineWidth = 40;

        if (rate < 0.7) {
          context.strokeStyle = '#ff0000';
        } else if (rate < 0.8) {
          context.strokeStyle = '#ffc600';
        } else {
          context.strokeStyle = '#18c6ff';
        }

        context.stroke();

      }



      // ------------------------------------------------------------------------
      // 计划旅客+已完成
      // ------------------------------------------------------------------------

      var planNum; //旅客订票人数
      var ckiNum; //旅客值机人数

      var param = {
        "companyCodes": comp_code,
        "stdStartUtcTime": stdStartUtcTime,
        "stdEndUtcTime": stdEndUtcTime,
        "detailType": "pftc", //统计航班（总计）
        //"psrType":"all"
      }

      $.ajax({
        type: 'post',
        url: "/bi/web/getPsrSummInfo",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          planNum = Number(response.planNum); //旅客订票人数
          setTrvNum();
        },
        error: function () { }
      });


      var param = {
        "companyCodes": comp_code,
        "stdStartUtcTime": stdStartUtcTime,
        "stdEndUtcTime": stdEndUtcTime,
        "detailType": "cftc"//, //已执行
        //"psrType":"all"
      }

      $.ajax({
        type: 'post',
        url: "/bi/web/getPsrSummInfo",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          ckiNum = Number(response.ckiNum); //旅客值机人数
          setTrvNum();
        },
        error: function () { }
      });

      function setTrvNum() {
        if (planNum >= 0 && ckiNum >= 0) {

          $('#val_trv_num_plan').text(Number(planNum));
          $('#val_trv_num_completed').text(Number(ckiNum));

          var rate_trv_completed = Number(ckiNum) / Number(planNum);
          $('.div_trv_num').css('top', (1 - rate_trv_completed) * 100 + '%');

          var canvas = document.getElementById('cvs_trv_num');
          var context = canvas.getContext('2d');
          var x = canvas.width / 2;
          var y = canvas.height / 2;

          // draw back
          var radius = 50;
          context.beginPath();
          context.arc(x, y, radius, 0, 2 * Math.PI, false);
          context.fillStyle = '#041946';
          context.fill();
          context.lineWidth = 4;
          context.strokeStyle = '#075C9C';
          context.stroke();

          // draw lines
          var numslice = 12;
          for (var i = 0; i < numslice; i++) {
            context.beginPath();
            var startAngle = i * (Math.PI * 2 / numslice);
            var endAngle = startAngle + Math.PI * 0.01;
            context.arc(x, y, radius, startAngle, endAngle, false);
            context.lineWidth = 4;
            context.strokeStyle = '#041946';
            context.stroke();
          }

        }
      }

      var url = `/bi/spring/sys-company-delay-reason/getDelayReasonByCompanyCode.json?companyCode=${comp_code}`;
      $.ajax({
        type: 'get',
        url: url,
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: null,
        success: function (response) {


          var data = response.data;
          var companyReasonList = data.companyReasonList;
          var externalReasonList = data.externalReasonList;
          var all = data.all;
          // 公司
          var html = '';
          var len = companyReasonList.length;
          for (var i = 0; i < len; i++) {
            var d = companyReasonList[i];
            var perstr = d.rate;
            var barlen = d.rate;
            var name = (lang == 'en') ? d.reasonEname : d.reasonName;
            if (perstr > 0) {
              html += '<div class="baritmrow"><span class="blue2">' + name + '</span> <span class="bar greenbar" style="width: ' + barlen + 'px; "></span> <span class="ffnum">' + perstr + '%</span> </div>';
            }
          }
          $('#holder_delay_cause_comp').html(html);



          html = '';
          var len = externalReasonList.length;
          for (var i = 0; i < len; i++) {
            var d = externalReasonList[i];
            var perstr = d.rate;
            var barlen = d.rate;
            var name =(lang == 'en') ? d.reasonEname : d.reasonName;
            if (perstr > 0) {
              html += '<div class="baritmrow"><span class="blue2">' + name + '</span> <span class="bar bluebar" style="width: ' + barlen + 'px; "></span> <span class="ffnum">' + perstr + '%</span> </div>';
            }
          }
          $('#holder_delay_cause_none').html(html);
          // percent
          $('#per_delay_cause_comp').text(all.companyResaonRate);
          $('#per_delay_cause_none').text(all.externalResaonRate);


          // chart

          var rate_delay_cause_comp = all.companyResaonRate;
          var rate = rate_delay_cause_comp;

          var canvas = document.getElementById('cvs_delay_cause');
          var context = canvas.getContext('2d');
          var x = canvas.width / 2;
          var y = canvas.height / 2;

          // draw blue circle
          var radius = 54;
          context.beginPath();
          context.arc(x, y, radius, 0, 2 * Math.PI, false);
          context.lineWidth = 7;
          context.strokeStyle = '#02B0F9';
          context.stroke();

          // draw green arc
          var startAngle = Math.PI - (Math.PI * 2 * rate / 2);
          var endAngle = Math.PI + (Math.PI * 2 * rate / 2);
          context.beginPath();
          context.arc(x, y, radius, startAngle, endAngle, false);
          context.lineWidth = 7;
          context.strokeStyle = '#A3D900';
          context.stroke();

          // draw lines
          var numslice = 12;
          for (var i = 0; i < numslice; i++) {
            context.beginPath();
            var startAngle = i * (Math.PI * 2 / numslice);
            var endAngle = startAngle + Math.PI * 0.01;
            context.arc(x, y, radius, startAngle, endAngle, false);
            context.lineWidth = 8;
            context.strokeStyle = '#041946';
            context.stroke();
          }
        },
        error: function () { }
      });

      //运力分布 - 可用运力

      var url = `/bi/spring/aircraft/getAcStatusStat?company=${comp_code}`
      $.ajax({
        type: 'get',
        url: url,
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        success: function (response) {
          var data = response.data;
          $("#total_plane_num").html(data.total);
          $('#plane_on_stop').text(data.maintCnt); //停场
          $('#plane_over_air').text(data.airCnt);
          $('#plane_on_ground').text(data.groundCnt);

          ac_data_list = data.detail;
          var html = '';
          var barwidth = 40;
          for (var accode in ac_data_list) {
            var item = ac_data_list[accode];
            html += '<div class="baritmrow plane_ac_' + item.actype + '">';
            html += '<span class="acno">' + item.actype + '</span>';

            html += '<span class="val val_l">' + item.airCnt + '</span>';
            html += '<span class="bar darkbar">';
            html += '<span class="bar_ac_air innerbar bluebar" style="width: ' + (item.airCnt / item.cnt * barwidth) + 'px;"></span>';
            html += '</span>';

            html += '<span class="val val_l" style="width:30px;">' + item.groundCnt + '</span> ';
            html += '<span class="bar darkbar">';
            html += '<span class="bar_ac_air innerbar greenbar" style="width: ' + (item.groundCnt / item.cnt * barwidth) + 'px;"></span>';
            html += '</span>';

            html += '<span class="val val_l" style="width:30px;">' + item.maintCnt + '</span> ';
            html += '<span class="bar darkbar">';
            html += '<span class="bar_ac_air innerbar brownbar" style="width: ' + (item.aimaintCntrCnt / item.cnt * barwidth) + 'px;"></span>';
            html += '</span>';

            html += '</div>';
          }

          $('#air_aclist1').html(html);
          // 详细机型列表滚动
          clearInterval(marquee_itv_airPlane);
          $('#air_aclist2').html('');
          if (ac_data_list.length > 4) {
            var speed = 80;
            var base_sec = document.getElementById("air_aclist");
            var base_sec2 = document.getElementById("air_aclist2");
            var base_sec1 = document.getElementById("air_aclist1");
            base_sec2.innerHTML = base_sec1.innerHTML;

            function base_Marquee() {
              if (base_sec2.offsetTop - base_sec.scrollTop <= 0)
                base_sec.scrollTop -= base_sec1.offsetHeight;
              else {
                base_sec.scrollTop += Math.ceil(1 / pageZoomScale);
              }
            }

            marquee_itv_airPlane = setInterval(base_Marquee, speed);
            base_sec.onmouseover = function () {
              clearInterval(marquee_itv_airPlane)
            }
            base_sec.onmouseout = function () {
              marquee_itv_airPlane = setInterval(base_Marquee, speed)
            }
          }

        }
      });

      // 非计划停场
      var url = `/bi/spring/sdmCapacity/queryNotPlanPark?companyCode=${comp_code}`
      $.ajax({
        type: 'get',
        url: url,
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        success: function (response) {
          var aog_planes = response.data;
          // AOG 飞机列表
          var len = aog_planes.length;
          var html = '';
          for (var i = 0; i < len; i++) {
            var item = aog_planes[i];
            html += '<div class="reltv" style="height:20px;"><span class="left" style="width:135px; display: inline-block; font-size:9px;overflow: hidden; text-overflow: ellipsis;white-space: nowrap;">' + item.acNo + '</span>  <span class="right" style="display: inline-block;width:26px;overflow: hidden; text-overflow: ellipsis;white-space: nowrap;">' + item.bigAcType + '</span></div>';

          }
          $('#aog_plane_num').text(len);
          $('#aog_plane_list1').html(html);

          //大于5条自动滚动
          if (len > 5) {
            var speed = 60;
            var sec = document.getElementById("aog_plane_list");
            var sec2 = document.getElementById("aog_plane_list2");
            var sec1 = document.getElementById("aog_plane_list1");
            sec2.innerHTML = sec1.innerHTML;

            function Marquee() {
              if (sec2.offsetTop - sec.scrollTop <= 0)
                sec.scrollTop -= sec1.offsetHeight
              else {
                sec.scrollTop++
              }
            }
            clearInterval(marquee_itv);
            marquee_itv = setInterval(Marquee, speed);
            sec.onmouseover = function () {
              clearInterval(marquee_itv)
            }
            sec.onmouseout = function () {
              marquee_itv = setInterval(Marquee, speed)
            }
          }
          // aog end 
        }
      });






      // ------------------------------------------------------------------------
      // 各个基地过夜飞机架数
      // ------------------------------------------------------------------------
      /*
      var param = {
          'company': comp_code
        }
    
        $.ajax({
            type: 'post',
            url:"/bi/web/airport",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function(response) {
              var all_arp_list = [];
                for(var i=response.arp.length-1; i>=0; i--){
                  var o = response.arp[i];
                  if(all_arp_list.indexOf(o.code) == -1){
                    all_arp_list.push(o.code);
                  }
                }
    
                //all_arp_list=["PEK", "HAK", "XIY", "CAN", "DLC", "TYN", "SZX", "HGH", "SYX", "HHA", "URC"];
          getArpOvernightPlane(all_arp_list)
                
                
            },
            error:function() {
            }
        });
      */



      function getArpOvernightPlane() {

        var arplist = [];
        for (var code in BASE_CITY_LIST) {
          if (arplist.indexOf(code) == -1) {
            arplist.push(code);
          }
        }

        var all_arp_kpi_value = {};
        var arp_has_plane_list = [];

        var date = new Date();
        var mm = date.getMonth() + 1;
        var dd = date.getDate();
        var yy = date.getFullYear();
        if (mm < 10) {
          mm = '0' + mm;
        }
        if (dd < 10) {
          dd = '0' + dd;
        }
        var today = yy + '' + mm + '' + dd + '';

        var kpis = ['AC_NO', 'AC_ARR_NO'];
        var param = {
          "SOLR_CODE": "FAC_COMP_ARP_FLIGHT_KPI",
          "COMP_CODE": comp_code,
          "AIRPORT_CODE": arplist.join(','),
          "KPI_CODE": kpis.join(','),
          "VALUE_TYPE": "kpi_value_d",
          "DATE_TYPE": "D",
          "LIMIT": 0,
          "DATE_ID": today,
          "OPTIMIZE": 1
        }

        $.ajax({

          type: 'post',
          url: "/bi/query/getackpi",
          contentType: 'application/json',
          dataType: 'json',
          async: true,
          data: JSON.stringify(param),
          success: function (response) {

            var data = response.data[comp_code];
            for (var kpicode in data) {
              var arpdatlist = data[kpicode]["D"];
              for (var arpcode in arpdatlist) {
                var val = arpdatlist[arpcode];
                if (all_arp_kpi_value[arpcode] == undefined) {
                  all_arp_kpi_value[arpcode] = {};
                }
                all_arp_kpi_value[arpcode][kpicode] = Number(val);
              }
            }

            // 计算总量
            var AC_ARR_NO_TOTAL = 0; // 所有机场过夜飞机架数
            var AC_NO_TOTAL = 0; // 所有机场预计过夜飞机架数
            var basenum = 0;


            for (var i = arplist.length - 1; i >= 0; i--) {
              var code = arplist[i];

              var AC_NO = 0; // 预计飞机架数
              var AC_ARR_NO = 0; // 过夜飞机架数

              if (all_arp_kpi_value[code]) {
                AC_NO = Number(all_arp_kpi_value[code]['AC_NO']);
                AC_ARR_NO = Number(all_arp_kpi_value[code]['AC_ARR_NO']);

                if (AC_NO > 0 || AC_ARR_NO > 0 || BASE_CITY_LIST[code] != undefined) {
                  basenum++;
                  AC_ARR_NO_TOTAL += AC_ARR_NO;
                  AC_NO_TOTAL += AC_NO;
                  arp_has_plane_list.push(code);
                }
              }

            }

            $('#base_over_night_plane_num').text(AC_ARR_NO_TOTAL);
            $('#base_over_night_plane_num2').text(AC_NO_TOTAL);
            $('#bar_base_over_night').css('width', (AC_ARR_NO_TOTAL / AC_NO_TOTAL * 100) + '%');


            setInterval(setArpPlane, 5000);


            hideLoading();


          },
          error: function () { }
        });



        var currentArpPlanePage = 0;
        var arpPlanePageSize = 4;

        function setArpPlane() {

          var html = '';

          var s1 = currentArpPlanePage * arpPlanePageSize;
          var s2 = Math.min((currentArpPlanePage + 1) * arpPlanePageSize, arp_has_plane_list.length);

          for (var i = s1; i < s2; i++) {
            var code = arp_has_plane_list[i];

            var AC_NO = 0; // 预计飞机架数
            var AC_ARR_NO = 0; // 过夜飞机架数

            AC_NO = Number(all_arp_kpi_value[code]['AC_NO']);
            AC_ARR_NO = Number(all_arp_kpi_value[code]['AC_ARR_NO']);

            //html+='<div class="over_night_arp"><span class="code blue2 fs11">'+code+'</span><span class="bar" style="width:'+(AC_ARR_NO/AC_NO*25)+'px; "></span><span class="num white fs11">'+AC_NO+'</span></div>';

            html += '<div id="over_night_' + code + '" class="baritmrow"><span class="blue2 right" style="width:50px; display: inline-block;">' + code + '</span> <span class="ffnum right" style="width:16px; display: inline-block;">' + AC_NO + '</span><span class="bar darkbar" style="width: 80px; "><span class="innerbar bluebar" style="width:100%; "></span><span class="innerbar greenbar" style="width: ' + (AC_ARR_NO / AC_NO * 100) + '%; right:0px;"></span></span><span class="ffnum" style="width:16px; display: inline-block;">' + AC_ARR_NO + '</span>';

          }

          $('#base_over_night_plane1').html(html);

          if (currentArpPlanePage < Math.ceil(arp_has_plane_list.length / arpPlanePageSize) - 1) {
            currentArpPlanePage++;
          } else {
            currentArpPlanePage = 0;
          }



          //大于4条自动滚动
          /*
        if(basenum > 4){
    
          var speed=60;
          var base_sec=document.getElementById("base_over_night_plane"); 
          var base_sec2=document.getElementById("base_over_night_plane2"); 
          var base_sec1=document.getElementById("base_over_night_plane1"); 
          base_sec2.innerHTML=base_sec1.innerHTML;
          function base_Marquee(){
              if(base_sec2.offsetTop-base_sec.scrollTop<=0)
                base_sec.scrollTop-=base_sec1.offsetHeight
              else{ 
                base_sec.scrollTop++ 
              } 
          } 
          var marquee_itv_base=setInterval(base_Marquee,speed);
          base_sec.onmouseover=function() {clearInterval(marquee_itv_base)}
          base_sec.onmouseout=function() {marquee_itv_base=setInterval(base_Marquee,speed)} 
        }
        */
        }

      }
      getArpOvernightPlane();


      // ------------------------------------------------------------------------
      // 各个基地正常率
      // ------------------------------------------------------------------------

      var area_arps = {};

      // 获取个区域机场
      $.ajax({
        type: 'get',
        url: "/bi/spring/airport/queryAirportByCompanyId?companyId=" + comp_id,
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        success: function (response) {
          var arplist = [];
          var arps = response.data;
          for (var i = arps.length - 1; i >= 0; i--) {
            var o = arps[i];
            var code = o.AIRPORT_CODE;
            var area = o.AREA_NAME;
            if (AREA_LIST[area] != undefined) {
              if (area_arps[area] == undefined) {
                area_arps[area] = [];
              }
              if (area_arps[area].indexOf(code) == -1) area_arps[area].push(code);
            }
            arplist.push(code);

          }
          for (var area_name in area_arps) {
            var arp_list = area_arps[area_name];
            getAreaArpsSts(area_name, arp_list)
          }


        },
        error: function () { }
      });

      // 区域正常率
      function getAreaArpsSts(area_name, arps) {
        var loaded = 0;
        var sch_total = 0;
        var sch_normal = 0;

        var arrstns = arps.join(',');
        var param = {
          "stdStartUtcTime": stdStartUtcTime,
          "stdEndUtcTime": stdEndUtcTime,
          "companyCodes": comp_code,
          "depstns": "", //出港
          "arrstns": arrstns //进港
        }
        var url = "/bi/spring/focStaticApi/flightAmountStaticV2?areaNormal&arrstns=" + arrstns;
        $.ajax({
          type: 'post',
          url: url,
          contentType: 'application/json',
          dataType: 'json',
          async: true,
          data: JSON.stringify(param),
          success: function (res) {
            var response = res.data;
            sch_total += Number(response.pfdappPercentD); 
            sch_normal += Number(response.pfdappPercentM);  

            loaded++;
            setOverAll();

          },
          error: function () {

          }
        });


        var depStns = arps.join(',');
        var param = {
          "stdStartUtcTime": stdStartUtcTime,
          "stdEndUtcTime": stdEndUtcTime,
          "companyCodes": comp_code,
          "depstns": depStns, //出港
          "arrstns": "" //进港
        }
        var url = "/bi/spring/focStaticApi/flightAmountStaticV2?depstns=" + depStns;
        $.ajax({
          type: 'post',
          url: url,
          contentType: 'application/json',
          dataType: 'json',
          async: true,
          data: JSON.stringify(param),
          success: function (res) {
            var response = res.data;
            sch_total += Number(response.pftc); //计划航班总数
            sch_normal += Number(response.pfrtc); //计划航班中正常航班总数

            loaded++;
            setOverAll();

          },
          error: function () {

          }
        });


        function setOverAll() {
          if (loaded == 2) {

            var r = Math.round(sch_normal / sch_total * 100)
            if (isNaN(r)) {
              r = 100;
            }
            var area_code = AREA_LIST[area_name];

            // 根据正常率设置颜色
            $('#area_' + area_code).removeClass('area_red');
            $('#area_' + area_code).removeClass('area_orange');
            $('#area_' + area_code).removeClass('area_yellow');
            $('#area_' + area_code).removeClass('area_green1');
            $('#area_' + area_code).removeClass('area_green2');
            $('#area_' + area_code).removeClass('area_green3');

            if (r <= Number(normal_rate_colors['red'])) {
              $('#area_' + area_code).attr('class', 'area_stroke area_red');
            } else if (r <= normal_rate_colors['green1']) {
              $('#area_' + area_code).attr('class', 'area_stroke area_orange');
            } else if (r <= normal_rate_colors['yellow']) {
              $('#area_' + area_code).attr('class', 'area_stroke area_yellow');
            } else if (r <= normal_rate_colors['green3']) {
              $('#area_' + area_code).attr('class', 'area_stroke area_green3');
            } else {
              $('#area_' + area_code).attr('class', 'area_stroke area_green2');
            }

            $('#area_normal_rate_' + area_code).text(r);

            $('#china_map').removeClass('hidden');

          }
        }

      }
      function getArpStsNew(arp) {
        // 获取机场的进港航班／正常率
        var url = `/bi/spring/sys-company-base-kpi-value/getStnKpi.json?companyCode=${comp_code}&stn=${arp}`;
        $.ajax({
          type: 'get',
          url: url,
          contentType: 'application/json',
          dataType: 'json',
          async: true,
          data: JSON.stringify({}),
          success: function (res) {
            var data = res.data;

            $('#arp_code_' + arp + ' .normal_rate2').text(data.depNormalRate);
            setNormalRateTextColor($('#arp_code_' + arp + ' .normal_rate2'), data.depNormalRate);

            $('#arp_code_' + arp + ' .normal_rate3').text(data.arrNormalRate);
            setNormalRateTextColor($('#arp_code_' + arp + ' .normal_rate3'), data.arrNormalRate);

            $('#arp_code_' + arp + ' .normal_rate4').text(data.normalRate);
            setNormalRateTextColor($('#arp_code_' + arp + ' .normal_rate4'), data.normalRate);


            $('#pin_' + arp).removeClass('pin_red');
            $('#pin_' + arp).removeClass('pin_yellow');
            $('#pin_' + arp).removeClass('pin_green');
            var r = data.normalRate;
            // 根据正常率设置pin颜色
            if (r != "-") {
              if (r <= Number(normal_rate_colors['red'])) {
                $('#pin_' + arp).addClass('pin_red');
              } else if (r <= Number(normal_rate_colors['yellow'])) {
                $('#pin_' + arp).addClass('pin_yellow');
              } else {
                $('#pin_' + arp).addClass('pin_green');
              }
            }

          },
          error: function () {
          }
        });
      }
      function getArpSts(arp) {

        var loaded = 0;
        var sch_total = 0;
        var sch_normal = 0;

        // 获取机场的进港航班／正常率
        var param = {
          "stdStartUtcTime": stdStartUtcTime,
          "stdEndUtcTime": stdEndUtcTime,
          "companyCodes": comp_code,
          "depstns": "",
          "arrstns": arp //进港
        }
        var url = "/bi/spring/focStaticApi/flightAmountStaticV2?arrstns=" + arp;
        $.ajax({
          type: 'post',
          url: url,
          contentType: 'application/json',
          dataType: 'json',
          async: true,
          data: JSON.stringify(param),
          success: function (res) {
            var response = res.data;
            sch_total += Number(response.pfdappPercentD); //航班量 :计划调时正常航班中的正常航班比率pfdappPercent（分母）
            sch_normal += Number(response.pfdappPercentM); //正常航班量:计划调时正常航班中的正常航班比率pfdappPercent（分子）
            var pfdappPercent = Number(response.pfdappPercent); //计划调试正常航班中的正常航班比率
            var r = Math.round(pfdappPercent);
            if (Number(response.pfdappPercentD) == 0) {
              r = "-"
            }
            // $('#arp_code_' + arp + ' .normal_rate2').text(r);
            // setNormalRateTextColor($('#arp_code_' + arp + ' .normal_rate2'), r);

            loaded++;
            setOverAll();

          },
          error: function () {
            loaded++;
            setOverAll();
          }
        });

        var url = "/bi/spring/focStaticApi/flightAmountStaticV2?queryNormalrate&depstns=" + arp;
        // 获取机场的出港航班／正常率
        var param = {
          "stdStartUtcTime": stdStartUtcTime,
          "stdEndUtcTime": stdEndUtcTime,
          "companyCodes": comp_code,
          "depstns": arp, //出港
          "arrstns": ""
        }

        $.ajax({
          type: 'post',
          url: url,
          contentType: 'application/json',
          dataType: 'json',
          async: true,
          data: JSON.stringify(param),
          success: function (res) {
            var response = res.data;
            sch_total += Number(response.pfdappPercentD); //航班量 :计划调时正常航班中的正常航班比率pfdappPercent（分母）
            sch_normal += Number(response.pfdappPercentM); //正常航班量:计划调时正常航班中的正常航班比率pfdappPercent（分子）
            var pfdappPercent = Number(response.pfdappPercent); //计划调试正常航班中的正常航班比率

            var r = Math.round(pfdappPercent);
            if (Number(response.pfdappPercentD) == 0) {
              r = "-"
            }
            // $('#arp_code_' + arp + ' .normal_rate3').text(r);
            // setNormalRateTextColor($('#arp_code_' + arp + ' .normal_rate3'), r);

            loaded++;
            setOverAll();

          },
          error: function () {
            loaded++;
            setOverAll();
          }
        });


        function setOverAll() {
          if (loaded == 2) {
            var r;
            if (sch_total == 0) {
              r = "-"
            } else {
              r = Math.round(sch_normal / sch_total * 100)
            }
            // $('#arp_code_' + arp + ' .normal_rate4').text(r);
            // setNormalRateTextColor($('#arp_code_' + arp + ' .normal_rate4'), r);

            // $('#pin_' + arp).removeClass('pin_red');
            // $('#pin_' + arp).removeClass('pin_yellow');
            // $('#pin_' + arp).removeClass('pin_green');

            // // 根据正常率设置pin颜色
            // if (r != "-") {
            //   if (r <= Number(normal_rate_colors['red'])) {
            //     $('#pin_' + arp).addClass('pin_red');
            //   } else if (r <= Number(normal_rate_colors['yellow'])) {
            //     $('#pin_' + arp).addClass('pin_yellow');
            //   } else {
            //     $('#pin_' + arp).addClass('pin_green');
            //   }
            // }

          }
        }

      }


      function setNormalRateTextColor(element, r) {
        if (r != '-') {
          element.parent().find('.ffnum').removeClass('red');
          element.parent().find('.ffnum').removeClass('orange');
          element.parent().find('.ffnum').removeClass('yellow');

          if (r <= Number(normal_rate_colors['red'])) {
            element.parent().find('.ffnum').addClass('red');
          } else if (r <= Number(normal_rate_colors['green1'])) {
            element.parent().find('.ffnum').addClass('orange');
          } else if (r <= Number(normal_rate_colors['yellow'])) {
            element.parent().find('.ffnum').addClass('yellow');
          }
        }

      }


      var arp_code_list = [];
      var arp_kpi_value = {};
      var arp_kpi_name_list = ['ORI_NORMAL_NO', 'ORI_TOFF_NO'] //['ORI_NORMAL_NO','ORI_NO_SCH']

      for (var arps in BASE_CITY_LIST) {
        arp_code_list = arp_code_list.concat(arps);

        getArpSts(arps)
        getArpStsNew(arps)
      }




      // ------------------------------------------------------------------------
      // 天气情况
      // ------------------------------------------------------------------------

      var weather_map = {
        '晴': 'icon-e600_sunny',
        '沙': 'icon-e617_dust1',
        '雹': 'icon-e620_hail',
        '雾': 'icon-e615_fog',
        '烟': 'icon-e615_fog',
        '阴': 'icon-e604_gloomy',
        '雷': 'icon-e606_rain2',
        '暴': 'icon-e606_rain2',
        '风': 'icon-e612_wind',
        '霾': 'icon-e613_haze',
        '云': 'icon-e602_cloudy',
        '雨': 'icon-e607_rain3',
        '雪': 'icon-e610_snow3',
      };

      var numOfLoadingBase = 0;

      for (var arpcode in BASE_CITY_LIST) {
        var param = {
          'airport': arpcode
        }

        numOfLoadingBase++;

        $.ajax({
          type: 'post',
          url: "/bi/web/7x2_arp_weather",
          contentType: 'application/json',
          dataType: 'json',
          async: true,
          data: JSON.stringify(param),
          success: function (response) {

            numOfLoadingBase--;

            //console.log('7x2_arp_weather-------');
            //console.log(response);
            if (Number(response.errorcode) == 0) {
              var weather_css = 'icon-e600_sunny';
              var cardcolor_css = '';
              /*
              airport
              airportCode
              cloudInfo 云况
              metUtcTime
              rvr 跑道目视距离
              temperature
              visibility 能见度
              weatherInfo 天气现象
              weatherInfoTxt 翻译后的天气
              windFs 风速
              
              10个基地的标准（除大连外）
              红色范围
              “能见度 小于等于800；跑道视程小于等于700   天气现象（大雾、雷暴等） 云况高度（60-90米） 风速15米/秒”
              黄色范围
              “能见度 小于等于1600米；跑道视程小于等于1400米   天气现象（大雾、雷暴等） 云况高度小于等于150米  风速大于等于10米/秒”
              
              大连的标准
              红色范围
              “能见度 小于等于1200；跑道视程小于等于1200  天气现象（大雾、雷暴、沙尘暴） 云况高度小于等于90米  风速大于等于15米/秒”
              黄色范围
              “能见度  小于等于2000米；跑道视程小于等于1800米  天气现象（大雾、雷暴等） 云况高度小于等于150米  风速大于等于10米/秒”
              
              注：FG代表大雾
                    TS或TSRA或+RA 代表雷暴
                     SS或DS沙尘暴
              
              */
              var weatherInfoCodes = ['FG', 'TS', 'TSRA', 'RA', 'SS', 'DS'];
              var code = response.airport;
              var visibility = isNaN(response.visibility) || response.visibility == 0 ? 9999 : Number(response.visibility); //能见度
              var rvr = isNaN(response.rvr) || response.rvr == 0 ? 9999 : Number(response.rvr); //跑道目视距离
              var cloudInfo = isNaN(response.cloudInfo) || response.cloudInfo == 0 ? 9999 : Number(response.cloudInfo); //云况
              var windFs = isNaN(response.windFs) ? 0 : Number(response.windFs); //风速
              var weatherInfo = response.weatherInfo || ''; //天气现象
              var weatherInfoTxt = "";
              if (response.weatherInfoTxt) {
                var weatherInfoTxt = response.weatherInfoTxt.replace(/<[^>]+>/g, "");
              }

              //console.log('===visibility', visibility);
              //console.log('===rvr', rvr);
              //console.log('===windFs', windFs);

              var weather_css = '';
              if (code != 'DLC') {
                if (visibility <= 800 || rvr <= 700 || windFs >= 15 || cloudInfo <= 90) {
                  cardcolor_css = 'redcard';
                } else if (visibility <= 1600 || rvr <= 1400 || windFs >= 10 || cloudInfo <= 150) {
                  cardcolor_css = 'yellowcard';
                }
              } else {
                // DLC 大连的标准不一样
                if (visibility <= 1200 || rvr <= 1200 || windFs >= 15 || cloudInfo <= 90) {
                  cardcolor_css = 'redcard';
                } else if (visibility <= 2000 || rvr <= 1800 || windFs >= 10 || cloudInfo <= 150) {
                  cardcolor_css = 'yellowcard';
                }
              }
              if (weather_css == '') {
                for (var i = weatherInfoCodes.length - 1; i >= 0; i--) {
                  var c = weatherInfoCodes[i];
                  if (weatherInfo.indexOf(c) > -1) {
                    cardcolor_css = 'yellowcard';
                  }
                }
              }

              for (var wtxt in weather_map) {
                if (weatherInfoTxt.indexOf(wtxt) > -1) {
                  weather_css = weather_map[wtxt];
                }
              }

              // 设置天气状况icon
              $('#arp_code_' + code + ' .weather span').attr('class', weather_css);

              $('#arp_code_' + code).removeClass('redcard');
              $('#arp_code_' + code).removeClass('yellowcard');

              // 设置卡片颜色
              $('#arp_code_' + code).addClass(cardcolor_css);

              //if(cardcolor_css == 'redcard'){
              //  unnormalBase.push('<span style="padding-right:5px;">' + BASE_CITY_LIST[code] + '</span>' + '<span class="'+weather_css+'"></span>' + '<span style="padding-right:18px;">' + weatherInfoTxt + '</span>');
              //}

            } else {
              console.log('7x2_arp_weather Error');
            }



          },
          error: function (jqXHR, txtStatus, errorThrown) {
            numOfLoadingBase--;
          }
        });
      }



      // ------------------------------------------------------------------------
      // 十航月度排名
      // 十航年度排名
      // ------------------------------------------------------------------------

      var logo_map = {
        '春秋': 'rank_logo_CQ.png',
        '东航': 'rank_logo_DH.png',
        '东方': 'rank_logo_DH.png',
        '国航': 'rank_logo_GH.png',
        '国际': 'rank_logo_GH.png',
        '海航': 'rank_logo_HH.png',
        '海南': 'rank_logo_HN.png',
        '控股': 'rank_logo_HN.png',
        '南航': 'rank_logo_NH.png',
        '南方': 'rank_logo_NH.png',
        '川航': 'rank_logo_SC.png',
        '四川': 'rank_logo_SC.png',
        '山航': 'rank_logo_SD.png',
        '山东': 'rank_logo_SD.png',
        '上航': 'rank_logo_SH.png',
        '上海': 'rank_logo_SH.png',
        '深航': 'rank_logo_SZ.png',
        '深圳': 'rank_logo_SZ.png',
        '厦航': 'rank_logo_XM.png',
        '厦门': 'rank_logo_XM.png',
        '天津': 'rank_logo_TH.png',
        '天航': 'rank_logo_TH.png',

        '祥鹏': 'logo_8L.png',
        '香港': 'logo_HX.png',
        '西部': 'logo_PN.png',
        '天津': 'logo_GS.png',
        '首都': 'logo_JD.png',
        '福州': 'logo_FU.png',
        '乌航': 'logo_UQ.png',
        '金鹏': 'logo_Y8.png',
        '北部湾': 'logo_GX.png',
        '长安': 'logo_9H.png',
        '桂林': 'logo_GT.png',
      };

      var param = {
        'mode': 'query',
        'type': 'month',
      }

      $.ajax({
        type: 'post',
        url: "/bi/web/7x2_comp_rank",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {

          checkLogin(response);

          if (response.comp != undefined) {
            var len = response.comp.length;
            var html = '';
            var list = response.comp;
            list.sort(function (a, b) {
              return a.rank - b.rank
            });
            var rank = 1;
            for (var i = 0; i < len; i++) {
              var obj = list[i];
              if (obj.name == '海航' || obj.name == '海南' || obj.name == '海南航空' || obj.name == '控股') {
                rank = obj.rank;
              }
              //
              var img = logo_map[obj.name];
              if (img != undefined) {
                html += '<span style="display: inline-block; width:20px; margin-bottom:3px;">' + obj.rank + '</span><img src=img/' + img + '><br>';
              } else {
                html += '<span style="display: inline-block; width:20px; margin-bottom:3px;">' + obj.rank + '</span>' + obj.name + '<br>';
              }

            }
            $('#comp_rank_month').text(rank);
            $('#comp_rank_list_month').html(html);

          }

        },
        error: function (jqXHR, txtStatus, errorThrown) {

        }
      });


      var param = {
        'mode': 'query',
        'type': 'year',
      }

      $.ajax({
        type: 'post',
        url: "/bi/web/7x2_comp_rank",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {

          checkLogin(response);

          if (response.comp != undefined) {
            var len = response.comp.length;
            var html = '';
            var list = response.comp;
            list.sort(function (a, b) {
              return a.rank - b.rank
            });
            var rank = 1;
            for (var i = 0; i < len; i++) {
              var obj = list[i];
              if (obj.name == '海航' || obj.name == '海南' || obj.name == '海南航空') {
                rank = obj.rank;
              }
              //
              var img = logo_map[obj.name];
              if (img != undefined) {
                html += '<span style="display: inline-block; width:20px; margin-bottom:3px;">' + obj.rank + '</span><img src=img/' + img + '><br>';
              } else {
                html += '<span style="display: inline-block; width:20px; margin-bottom:3px;">' + obj.rank + '</span>' + obj.name + '<br>';
              }
            }
            $('#comp_rank_year').text(rank);
            $('#comp_rank_list_year').html(html);

          }

        },
        error: function (jqXHR, txtStatus, errorThrown) {

        }
      });



      // ------------------------------------------------------------------------
      // VIP/VVIP保障航班总量 已完成占比 
      // ------------------------------------------------------------------------

      // ------------------------
      // 获取VIP航班数量和VIP乘客数量
      /*
      文档：getPsrSummInfoByFoc 按旅客查询航班接口.docx
      描述：旅客按航班汇总查询
      接口类：com.hnair.opcnet.api.ods.psr.PassengerApi
      接口方法：getPsrSummInfoByFoc
      */
      // ------------------------

      var vip_flt_cnt_total; //vip航班
      var vip_psr_cnt_total; //vip乘客
      var vip_flt_cnt_complete; //vip航班 已完成
      var vip_psr_cnt_complete; //vip乘客 已完成

      var vvip_flt_cnt_total; //vvip航班
      var vvip_psr_cnt_total; //vvip乘客
      var vvip_flt_cnt_complete; //vvip航班 已完成
      var vvip_psr_cnt_complete; //vvip乘客 已完成
      var vvip_flt_cnt_exec; //vvip航班 执行中

      // !!!!! 接口中无法查询执行中的VIP航班数量 !!!!!

      var vvip_load_cnt = 0;
      var param = {
        "companyCodes": comp_code,
        "detailType": "pftc", //统计航班（总计）
        "psrType": "vip,m6"
      }

      $.ajax({
        type: 'post',
        url: "/bi/web/7x2_vip_flt_cnt",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {

          vip_flt_cnt_total = response.vip_m6_flt_cnt; //vip航班
          vip_psr_cnt_total = response.vip_m6_psr_cnt; //vip乘客

          vvip_load_cnt++;
          setVVIP();

        },
        error: function () { }
      });



      var param = {
        "companyCodes": comp_code,
        "detailType": "cftc", //已执行航班班次
        "psrType": "vip,m6"
      }

      $.ajax({
        type: 'post',
        url: "/bi/web/7x2_vip_flt_cnt",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {

          vip_flt_cnt_complete = response.vip_m6_flt_cnt; //vip航班
          vip_psr_cnt_complete = response.vip_m6_psr_cnt; //vip乘客

          vvip_load_cnt++;
          setVVIP();

        },
        error: function () { }
      });



      var param = {
        'mode': 'query'
      }


      $.ajax({
        type: 'post',
        url: "/bi/web/7x2_vvip_flt",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {

          checkLogin(response);

          vvip_flt_cnt_total = response.vvip[0].total;
          vvip_psr_cnt_total = response.vvip[0].people_total;
          vvip_flt_cnt_complete = response.vvip[0].complete;
          vvip_psr_cnt_complete = response.vvip[0].people_complete;
          vvip_flt_cnt_exec = response.vvip[0].exec;

          vvip_load_cnt++;
          setVVIP();

        },
        error: function () { }
      });

      function setVVIP() {

        if (vvip_load_cnt == 3) {

          // ------------------------------------------------------------------------
          // VIP/VVIP 乘客
          // ------------------------------------------------------------------------

          var total_people = Number(vip_psr_cnt_total) + Number(vvip_psr_cnt_total);
          var total_complete = Number(vip_psr_cnt_complete) + Number(vvip_psr_cnt_complete);

          $('#vvip_people_total').text(total_people);
          $('#vvip_people_complete').text(total_complete);

          var rate_vip_people = total_complete / total_people;
          var rate = rate_vip_people;

          var canvas = document.getElementById('cvs_vip_people');
          var context = canvas.getContext('2d');

          var col = 12;
          var row = 6;
          var radius = 3.5;
          var grid = 9;

          var x;
          var y;

          // draw circle
          for (var i = 0; i < col; i++) {
            for (var j = 0; j < row; j++) {

              x = 3.5 + i * grid;
              y = 3.5 + j * grid;

              context.beginPath();
              context.arc(x, y, radius, 0, Math.PI * 2, false);
              context.fillStyle = '#034781';
              context.fill();
            }
          }

          // draw rate circles
          var limit = Math.round(col * row * rate);
          for (var i = 0; i < col; i++) {
            for (var j = 0; j < row; j++) {

              if (limit > 0) {
                x = 3.5 + i * grid;
                y = 3.5 + j * grid;

                context.beginPath();
                context.arc(x, y, radius, 0, Math.PI * 2, false);
                context.fillStyle = '#009DEB';
                context.fill();

                limit--;

              } else {

                break;
              }

            }
          }



          // ------------------------------------------------------------------------
          // VIP/VVIP 航班
          // ------------------------------------------------------------------------


          var total = Number(vip_flt_cnt_total) + Number(vvip_flt_cnt_total);
          var complete = Number(vip_flt_cnt_complete) + Number(vvip_flt_cnt_complete);

          $('#vvip_flt_total').text(total);
          $('#vvip_flt_complete').text(complete);
          $('#vvip_flt_exec').text(vvip_flt_cnt_exec);


          var rate_flt_support_completed = complete / total;
          var rate_flt_support_exec = vvip_flt_cnt_exec / total;

          $('.div_flt_support1').css('height', rate_flt_support_completed * 100 + '%');
          $('.div_flt_support1').css('top', (1 - rate_flt_support_completed) * 100 + '%');
          $('.div_flt_support2').css('height', rate_flt_support_exec * 100 + '%');
          $('.div_flt_support2').css('top', (1 - rate_flt_support_completed - rate_flt_support_exec) * 100 + '%');

          var canvas = document.getElementById('cvs_flt_support');
          var context = canvas.getContext('2d');
          var x = canvas.width / 2;
          var y = canvas.height / 2;

          // draw back
          var radius = 40;
          context.beginPath();
          context.arc(x, y, radius, 0, 2 * Math.PI, false);
          context.fillStyle = '#041946';
          context.fill();
          context.lineWidth = 4;
          context.strokeStyle = '#075C9C';
          context.stroke();

          // draw lines
          var numslice = 12;
          for (var i = 0; i < numslice; i++) {
            context.beginPath();
            var startAngle = i * (Math.PI * 2 / numslice);
            var endAngle = startAngle + Math.PI * 0.01;
            context.arc(x, y, radius, startAngle, endAngle, false);
            context.lineWidth = 4;
            context.strokeStyle = '#041946';
            context.stroke();
          }



        }

      }



      // ------------------------------------------------------------------------
      // 预警航班总量 正常率
      // ------------------------------------------------------------------------

      var param = {
        'mode': 'query'
      }

      $.ajax({
        type: 'post',
        url: "/bi/web/7x2_warning_flt",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {

          checkLogin(response);

          var total = response.data[0].total;
          var complete = response.data[0].complete;
          var normal_rate = response.data[0].normal_rate.replace("%", "");;

          $('#warning_total').text(total);
          $('#warning_complete').text(complete);
          $('#warning_normal_rate').text(normal_rate);

          var rate = normal_rate.split('%')[0];
          rate = Number(rate) / 100;

          var rate_flt_warning = rate;
          var rate = rate_flt_warning;

          var canvas = document.getElementById('cvs_flt_warning');
          var context = canvas.getContext('2d');
          var x = canvas.width / 2;
          var y = canvas.height / 2;

          // draw back
          var radius = 42;
          var startAngle = Math.PI - Math.PI / 3.6;
          var endAngle = startAngle + Math.PI + Math.PI / 3.6 * 2;
          var counterClockwise = false;

          context.beginPath();
          context.arc(x, y, radius, startAngle, endAngle, counterClockwise);
          context.lineWidth = 20;
          context.strokeStyle = '#00438B';
          context.stroke();

          // draw overlay
          var radius = 42;
          var startAngle2 = startAngle;
          var endAngle2 = startAngle + (endAngle - startAngle) * rate;
          var counterClockwise = false;

          context.beginPath();
          context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
          context.lineWidth = 20;

          // linear gradient
          if (rate < 0.5) {
            var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
          } else if (rate < 0.8) {
            var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
          } else {
            var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, canvas.width / 2);
          }
          color.addColorStop(0, '#122A61');
          color.addColorStop(1, '#75A5CE');
          context.strokeStyle = color;
          context.stroke();

          // draw head
          var radius = 42;
          var startAngle2 = startAngle + (endAngle - startAngle) * rate - (endAngle - startAngle) * 0.01;
          var endAngle2 = startAngle + (endAngle - startAngle) * rate;
          var counterClockwise = false;

          context.beginPath();
          context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
          context.lineWidth = 20;
          context.strokeStyle = '#FFFF00';
          context.stroke();

        },
        error: function (jqXHR, txtStatus, errorThrown) { }
      });



      // ------------------------------------------------------------------------
      // 中转旅客
      // ------------------------------------------------------------------------

      var param = {
        "companyCodes": comp_code,
        "detailType": "pftc" // 统计中转转入转出航班
      }

      $.ajax({
        type: 'post',
        url: "/bi/web/7x2_zz_trv_num",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          $('#zz_trv_num_total').text(response.zzTrvNumTotal); // 日中转总量
        },
        error: function () { }
      });

      var param = {
        'company': comp_code,
        'inOrOut': 'in,out' // 中转旅客转入转出
      };
      $.ajax({
        type: 'post',
        url: "/bi/web/queryPsrjoinSummary",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        beforeSend: function (XMLHttpRequest) {
          $('#sxsjcg_details_loading').show();
          $('#sxsjcg_details').hide();
        },
        success: function (response) {
          var list = response.data;
          var datamap = {};
          var total4hour = 0;
          for (var i in list) {
            var dat = list[i];
            var type = dat.inOrOutType;
            var ZZZC = dat.fourJoinNormal;
            var ZZJZ = dat.fourJoinNervous;
            var ZZCS = dat.fourJoinGone;
            total4hour += Number(ZZZC) + Number(ZZJZ) + Number(ZZCS);
            if (datamap[type] == undefined) {
              datamap[type] = {};
            }
            if (datamap[type]['ZZZC'] == undefined) {
              datamap[type]['ZZZC'] = ZZZC;
            }
            if (datamap[type]['ZZJZ'] == undefined) {
              datamap[type]['ZZJZ'] = ZZJZ;
            }
            if (datamap[type]['ZZCS'] == undefined) {
              datamap[type]['ZZCS'] = ZZCS;
            }
          }

          for (var type in datamap) {
            var ddd = datamap[type];
            for (var sts in ddd) {
              var val = Number(ddd[sts]);
              if (sts.length > 0 && $('#zz_trv_num_' + type + ' .' + sts).length) {
                $('#zz_trv_num_' + type + ' .' + sts).text(val);
                $('#zz_trv_num_' + type + ' .bar_' + sts).css('width', Math.ceil(val / total4hour * 100) + '%');
              }
            }
          }
          $('#sxsjcg_details_loading').hide();
          $('#sxsjcg_details').show();
        },
        error: function (jqXHR, txtStatus, errorThrown) {
          $('#sxsjcg_details_loading').hide();
          $('#sxsjcg_details').show();
        }
      });



      //     var param = {
      //     'COMP_CODE': comp_code,
      //     'KPI_CODE': 'ZZ_NO'// 中转旅客
      //     }

      //     $.ajax({
      //         type: 'post',
      //         url:"/bi/web/7x2_zz_trv_kpi",
      //         contentType: 'application/json',
      //         dataType: 'json',
      //         async: true,
      //         data: JSON.stringify(param),
      //         success: function(response) {
      //            var list = response.date;
      //            var datamap = {};
      //            var total = 0;
      //            var total4hour = 0;
      //            for(var i in list){
      //             var dat = list[i];
      //             var val = dat.KPI_VALUE;
      //             var type = dat.ZZ_TYPE;
      //             var sts = dat.ZZ_STATUS;
      //             if(type == 'RZZL'){
      //               // 全天的总量
      //               total = Number(val);
      //             }

      //             if(!isNaN(val)){

      //               if(datamap[type] == undefined){
      //                 datamap[type] = {};
      //               }
      //               if(datamap[type][sts] == undefined){
      //                 datamap[type][sts] = val;
      //                 if(type != 'RZZL') total4hour += Number(val);
      //               }
      //             }

      //            }

      //            for(var type in datamap){
      //             var ddd = datamap[type];
      //             for(var sts in ddd){
      //               var val = Number(ddd[sts]);
      //               if(sts.length > 0 && $('#zz_trv_num_'+type+' .'+sts).length){
      //                 $('#zz_trv_num_'+type+' .'+sts).text(val);
      //                 $('#zz_trv_num_'+type+' .bar_'+sts).css('width', Math.ceil(val/total4hour*100)+'%');
      //               }
      //             }
      //            }

      // //           $('#zz_trv_num_total').text(total); // 日中转总量
      //         },
      //         error:function(jqXHR, txtStatus, errorThrown) {

      //         }
      //     });



      // VIP 航班列表

      var vip_flt_no_list = [];

      var date = new Date();
      var month = date.getMonth() + 1;
      var day = date.getDate();
      // 测试上没有最新日期的数据，所以下面暂时把日期写死，为了能在测试上取到数据，发布到生产的时候，需要注释掉
      //var month = 10;
      //var day = 1;
      if (month < 10) {
        month = '0' + month;
      }
      if (day < 10) {
        day = '0' + day;
      }
      var today = date.getFullYear() + '-' + month + '-' + day;
      var param = {
        "acOwner": comp_code,
        "vip": "true",
        "datop": today
      }
      $.ajax({
        type: 'post',
        url: "/bi/web/7x2_vip_flt_list",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          //
          checkFocFlightLoaded(response);

        },
        error: function () { }
      });


      function checkFocFlightLoaded(response) {
        if (all_flight_list == undefined) {
          setTimeout(checkFocFlightLoaded, 10, response);
          return;
        }

        for (var i in response.flightNo) {
          var fno = response.flightNo[i];
          if (fno.length > 0 && fno.indexOf(comp_code) > -1 && findFltInfo(fno) != undefined && vip_flt_no_list.indexOf(fno) == -1) {

            var obj = findFltInfo(fno);
            // 近2小时进出港航班
            var etdChn = obj.etdChn; //预计起飞时间（北京时间）

            var d_time = parserDate(etdChn);
            var now_time = new Date();

            // 2小时内出发的飞机
            var ost = d_time.getTime() - now_time.getTime();

            if (ost <= 120 * 60 * 1000 && ost >= 0) {
              vip_flt_no_list.push(fno);
            }

          }
        }
        //console.log('vip_flt_no_list', vip_flt_no_list);
        createFltList(vip_flt_no_list, true);
      }


      // VVIP 航班列表
      var vvip_flt_no_list = [];
      var vvip_flt_backup_list = {}; // {主机航班号:备机航班号}
      var param = {
        "mode": "query"
      }
      $.ajax({
        type: 'post',
        url: "/bi/web/7x2_vvip",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          //console.log("7x2_vvip");
          for (var i in response.flt) {
            var flt = response.flt[i];
            var fltno = flt.flt_no;
            var backup_flt_no = flt.backup_flt_no;
            vvip_flt_no_list.push(fltno);
            vvip_flt_backup_list[fltno] = backup_flt_no;
          }

        },
        error: function () { }
      });

      // 预警航班 列表
      var warning_flt_no_list = [];
      var param = {
        "mode": "query"
      }
      $.ajax({
        type: 'post',
        url: "/bi/web/7x2_warning",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          //console.log("7x2_warning");
          for (var i in response.flt) {
            var flt = response.flt[i];
            var fltno = flt.flt_no;
            warning_flt_no_list.push(fltno);
          }
          //console.log(warning_flt_no_list);

          if (warning_flt_no_list.length > 3) {
            $('#flt_list_malfunction').html('<marquee direction="left" scrollamount="3">' + warning_flt_no_list.join('&nbsp;&nbsp;&nbsp;') + '</marquee>');
          } else {
            $('#flt_list_malfunction').html(warning_flt_no_list.join('&nbsp;&nbsp;&nbsp;'));
          }

        },
        error: function () { }
      });

      // 重点关注 列表
      var important_flt_no_list = [];
      var param = {
        "mode": "query"
      }
      $.ajax({
        type: 'post',
        url: "/bi/web/7x2_important",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          //console.log("7x2_important");
          for (var i in response.flt) {
            var flt = response.flt[i];
            var fltno = flt.flt_no;
            important_flt_no_list.push(fltno);
          }
          //console.log(important_flt_no_list);

        },
        error: function () { }
      });



      function createFltList(fltlist, selectFirstItem) {
        $('#flight_detail_tab_content').show();
        var html = '';
        var firstNo;
        for (var i in fltlist) {
          var fltno = fltlist[i];
          if (firstNo == undefined) {
            firstNo = fltno;
          }
          html += '<div class="flt_list_itm fltno_' + fltno + '" fltno="' + fltno + '">' + fltno + '</div>';
        }
        $('#flt_list_holder .wrap').html(html);

        $('.flt_list_itm').on('off', fltItmClick);
        $('.flt_list_itm').on('click', fltItmClick);

        if (firstNo != undefined) {

          if (selectFirstItem) {
            selectFlt(firstNo);
          }
        } else {
          selectFlt(firstNo);
        }

      }


      function showNextFlt() {

        clearTimeout(timeout_next_flt);

        var idx;
        var nextfltno;
        var nexttab;

        // alert(flt_tab_id)
        // flt_tab_id = 'tab_vip'; // 暂时锁定在vip组轮播
        if (flt_tab_id == 'tab_vip') {
          // alert(vip_flt_no_list.indexOf(selectedFltno))
          if (vip_flt_no_list.indexOf(selectedFltno) > -1 && vip_flt_no_list.indexOf(selectedFltno) < vip_flt_no_list.length) {
            idx = vip_flt_no_list.indexOf(selectedFltno)
            // alert(selectedFltno, idx)
            if (idx == vip_flt_no_list.length - 1 || idx == -1) {
              idx = 0;
            } else {
              idx++;
            }
            // idx++
            nexttab = flt_tab_id;
            nextfltno = vip_flt_no_list[idx];

          }/* else if (vvip_flt_no_list.length > 0) {
          nexttab = 'tab_vvip';
          nextfltno = vvip_flt_no_list[0];
        }*/ else if (warning_flt_no_list.length > 0) {
            nexttab = 'tab_warning';
            nextfltno = warning_flt_no_list[0];
          }/* else if (important_flt_no_list.length > 0) {
          nexttab = 'tab_important';
          nextfltno = important_flt_no_list[0];
        }*//* else if (abnormal_flt_no_list.length > 0) {
            nexttab = 'tab_abnormal';
            nextfltno = abnormal_flt_no_list[0];
        } else if (securemonitor_flt_no_list.length > 0) {
            nexttab = 'tab_securemonitor';
            nextfltno = securemonitor_flt_no_list[0];
        }*/ else {
            nexttab = 'tab_vip';
            nextfltno = vip_flt_no_list[0]
          }

        }

        //
        if (flt_tab_id == 'tab_vvip') {
          if (vvip_flt_no_list.indexOf(selectedFltno) > -1 && vvip_flt_no_list.indexOf(selectedFltno) < vvip_flt_no_list.length - 1) {
            idx = vvip_flt_no_list.indexOf(selectedFltno)
            idx++;
            nexttab = flt_tab_id;
            nextfltno = vvip_flt_no_list[idx];

          } else if (warning_flt_no_list.length > 0) {
            nexttab = 'tab_warning';
            nextfltno = warning_flt_no_list[0];
          }/* else if (important_flt_no_list.length > 0) {
          nexttab = 'tab_important';
          nextfltno = important_flt_no_list[0];
        }*//* else if (abnormal_flt_no_list.length > 0) {
          nexttab = 'tab_abnormal';
          nextfltno = abnormal_flt_no_list[0];
        } else if (securemonitor_flt_no_list.length > 0) {
          nexttab = 'tab_securemonitor';
          nextfltno = securemonitor_flt_no_list[0];
        }*/ else {
            nexttab = 'tab_vip';
            nextfltno = vip_flt_no_list[0]
          }

        }

        //
        if (flt_tab_id == 'tab_warning') {
          if (warning_flt_no_list.indexOf(selectedFltno) > -1 && warning_flt_no_list.indexOf(selectedFltno) < warning_flt_no_list.length - 1) {
            idx = warning_flt_no_list.indexOf(selectedFltno)
            idx++;
            nexttab = flt_tab_id;
            nextfltno = warning_flt_no_list[idx];

          }/* else if (important_flt_no_list.length > 0) {
          nexttab = 'tab_important';
          nextfltno = important_flt_no_list[0];
        }*//* else if (abnormal_flt_no_list.length > 0) {
          nexttab = 'tab_abnormal';
          nextfltno = abnormal_flt_no_list[0];
        } else if (securemonitor_flt_no_list.length > 0) {
          nexttab = 'tab_securemonitor';
          nextfltno = securemonitor_flt_no_list[0];
        }*/ else {
            nexttab = 'tab_vip';
            nextfltno = vip_flt_no_list[0]
          }

        }

        //
        if (flt_tab_id == 'tab_important') {
          if (important_flt_no_list.indexOf(selectedFltno) > -1 && important_flt_no_list.indexOf(selectedFltno) < important_flt_no_list.length - 1) {
            idx = important_flt_no_list.indexOf(selectedFltno)
            idx++;
            nexttab = flt_tab_id;
            nextfltno = important_flt_no_list[idx];

          } else if (vip_flt_no_list.length > 0) {
            nexttab = 'tab_vip';
            nextfltno = vip_flt_no_list[0];
          } else {
            nexttab = 'tab_vip';
            nextfltno = vip_flt_no_list[0]
          }

        }
        if (flt_tab_id == 'tab_abnormal') {
          nexttab = flt_tab_id;
          /*if (abnormal_flt_no_list.indexOf(selectedFltno) > -1 && abnormal_flt_no_list.indexOf(selectedFltno) < abnormal_flt_no_list.length - 1) {
            idx = abnormal_flt_no_list.indexOf(selectedFltno)
            idx++;
            nexttab = flt_tab_id;
            nextfltno = abnormal_flt_no_list[idx];
  
          } else* / if (warning_flt_no_list.length > 0) {
            nexttab = 'tab_warning';
            nextfltno = warning_flt_no_list[0];
          }/* else if (important_flt_no_list.length > 0) {
            nexttab = 'tab_important';
            nextfltno = important_flt_no_list[0];
          }*//* else if (securemonitor_flt_no_list.length > 0) {
            nexttab = 'tab_securemonitor';
            nextfltno = securemonitor_flt_no_list[0];
          }* / else {
            nexttab = 'tab_vip';
            nextfltno = vip_flt_no_list[0]
          }*/

        }
        if (flt_tab_id == 'tab_securemonitor') {
          nexttab = flt_tab_id;
          /*if (securemonitor_flt_no_list.indexOf(selectedFltno) > -1 && securemonitor_flt_no_list.indexOf(selectedFltno) < securemonitor_flt_no_list.length - 1) {
            idx = securemonitor_flt_no_list.indexOf(selectedFltno)
            idx++;
            nexttab = flt_tab_id;
            nextfltno = securemonitor_flt_no_list[idx];
  
          } else* / if (warning_flt_no_list.length > 0) {
            nexttab = 'tab_warning';
            nextfltno = warning_flt_no_list[0];
          }/* else if (important_flt_no_list.length > 0) {
            nexttab = 'tab_important';
            nextfltno = important_flt_no_list[0];
          }*//* else if (abnormal_flt_no_list.length > 0) {
            nexttab = 'tab_abnormal';
            nextfltno = abnormal_flt_no_list[0];
          }* / else {
            nexttab = 'tab_vip';
            nextfltno = vip_flt_no_list[0]
          }*/

        }

        selectFltTab(nexttab, false);

        if (nexttab != 'tab_abnormal' && nexttab != 'tab_securemonitor')
          selectFlt(nextfltno);

      }

      function fltItmClick(evt) {
        selectFlt($(this).attr('fltno'));
      }


      var selectedFltno;

      function selectFlt(fltno) {

        clearTimeout(timeout_next_flt);

        $('#flight_details_loading').show();
        $('#flight_details').hide();
        $('#flight_no_data').hide();
        //
        selectedFltno = fltno;



        $('.flt_list_itm').removeClass('selected');
        $('.fltno_' + fltno).addClass('selected');

        var flt = findFltInfo(fltno);
        setFltDetails(flt);



        if (vip_flt_no_list.indexOf(fltno) > -1 && flt_tab_id == 'tab_vip') {
          var idx = vip_flt_no_list.indexOf(fltno);

          if (idx > 13) {
            $('#flt_list_holder .wrap').animate({
              top: '-' + ($('.flt_list_itm').height() + 7) * (idx - 13) + 'px'
            },
              300,
              function () { }
            );
          } else {
            $('#flt_list_holder .wrap').animate({
              top: '0px'
            },
              300,
              function () { }
            );
          }

        } else if (vvip_flt_no_list.indexOf(fltno) > -1 && flt_tab_id == 'tab_vvip') {
          $('#flt_list_holder .wrap').animate({
            top: '0px'
          },
            0,
            function () { }
          );
        } else if (warning_flt_no_list.indexOf(fltno) > -1 && flt_tab_id == 'tab_warning') {
          $('#flt_list_holder .wrap').animate({
            top: '0px'
          },
            0,
            function () { }
          );
        } else if (important_flt_no_list.indexOf(fltno) > -1 && flt_tab_id == 'tab_important') {
          $('#flt_list_holder .wrap').animate({
            top: '0px'
          },
            0,
            function () { }
          );
        }



      }


      function loadFltDetails(fltno, callback) {

        /*
        var param = {
        "flightNo":fltno
        }
    
        $.ajax({
            type: 'post',
            url:"/bi/web/7x2_flt_detail_list",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function(response) {
    
                loadingFltList.splice(loadingFltList.indexOf(fltno), 1);
                var fltdat = response[fltno];
                flightDetailsList[fltno] = fltdat;
                if(selectedFltno == fltno){
                  setFltDetails(fltdat);
                }
    
                if(callback){
                  callback();
                }
                
            },
            error:function() {
            }
        });
        */


      }


      function setFltDetails(fltdat) {

        if (fltdat == undefined) {
          $('#flight_no_data').show();
          $("#flight_details").hide();
          $('#flight_details_loading').hide();
          return;
        }

        if (arp_detail_list == undefined) {
          setTimeout(setFltDetails, 10, fltdat);
          return;
        }

        clearTimeout(timeout_next_flt);
        timeout_next_flt = setTimeout(showNextFlt, 15000);

        var fltno = fltdat.flightNo;
        var acno = fltdat.acLongNo;

        var arp1 = arp_detail_list[fltdat.depStn];
        var arp2 = arp_detail_list[fltdat.arrStn];

        if (arp1 && lang == 'cn') {
          $('#detail_depCity').text(arp1.city_name);
        } else {
          $('#detail_depCity').text(fltdat.depStn);
        }
        if (arp2 && lang == 'cn') {
          $('#detail_arrCity').text(arp2.city_name);
        } else {
          $('#detail_arrCity').text(fltdat.arrStn);
        }

        $('#detail_stdChn').text(fltdat.stdChn.split(' ')[1].substr(0, 5));
        $('#detail_staChn').text(fltdat.staChn.split(' ')[1].substr(0, 5));

        var statusMap = {
          'ARR': '落地',
          'NDR': '落地',
          'ATD': '推出',
          'ATA': '到达',
          'CNL': '取消',
          'DEL': '延误',
          'DEP': '起飞',
          'RTR': '返航',
          'SCH': '计划'
        };
        var status = fltdat.status;
        if (lang == 'cn') {
          $('#detail_status').text(statusMap[status]);
        } else {
          $('#detail_status').text(status);
        }

        $('#detail_flightNo').text(fltno);
        $('#detail_iataAcType').text(langPack['lb_ac_type'][lang] + ': ' + fltdat.acType);

        var tOffChn = fltdat.tOffChn;
        var tDwnChn = fltdat.tDwnChn;

        var dt = new Date();
        var doff = parserDate(tOffChn); // 实际离地北京时间 
        var ddown = parserDate(tDwnChn); // 实际落地北京时间

        if (vvip_flt_backup_list[fltno] != undefined) {
          $('#detail_backup').html('<span class="blue2">' + langPack['lb_backup_ac'][lang] + '</span><br>' + vvip_flt_backup_list[fltno] + '<br>');
        } else {
          $('#detail_backup').html('');
        }


        // 前序航班预计过站时间，算法为本段航班计划离港时间-前序航班预计到达时间
        $('#detail_prev_stdChn').text('-');
        $('#detail_prev_passTime').text('-');

        // 查找前序航班
        var prev_flt;
        for (var i = all_flight_list.length - 1; i >= 0; i--) {
          var flt = all_flight_list[i];
          if (flt.acLongNo == acno && flt.arrStn == fltdat.depStn && flt.stdChn < fltdat.stdChn) {
            prev_flt = flt;
            break;
          }
        }
        if (prev_flt && status != 'CNL') {

          // 前序航班 起飞时间
          var prevd;
          if (prev_flt.status == 'DEP' || prev_flt.status == 'ARR' || prev_flt.status == 'NDR' || prev_flt.status == 'ATA') {
            prevd = prev_flt.atdChn.split(' ')[1].substr(0, 5); // 实际起飞时间 atdChn
          } else {
            prevd = prev_flt.etdChn.split(' ')[1].substr(0, 5); // 预计起飞时间 etdChn
          }
          $('#detail_prev_stdChn').text(prevd);

          // 前序航班 过站时间
          var detaChn; // 前序航班 到达时间
          var detdChn; // 本段航班 离港时间
          if (prev_flt.status == 'ARR' || prev_flt.status == 'NDR' || prev_flt.status == 'ATA' || prev_flt.status == 'ATA') {
            detaChn = prev_flt.ataChn; // 实际到达时间 ataChn
          } else {
            detaChn = prev_flt.etaChn; // 预计到达时间 etaChn
          }
          if (status == 'ARR' || status == 'NDR' || status == 'ATA' || status == 'DEP' || status == 'RTR') {
            detdChn = fltdat.atdChn; // 实际起飞时间 atdChn
          } else {
            detdChn = fltdat.etdChn; // 预计起飞时间 etdChn
          }
          var a_time = parserDate(detaChn); // 前序航班
          var d_time = parserDate(detdChn); // 本段航班
          var sec = d_time.getTime() - a_time.getTime();
          var totalmin = sec / 60000;
          var hour = Math.floor(totalmin / 60);
          var min = totalmin % 60;
          $('#detail_prev_passTime').text(hour + langPack['lb_hour'][lang] + " " + min + langPack['lb_minute'][lang]);

        }



        // ------------------------------------------------------------------------
        // 获取 机组信息
        // ------------------------------------------------------------------------
        $('#detail_crwPilotInf').text('');
        $('#detail_crwStatus').text('');

        if (fltCrwCache[fltno]) {

          setCrw(fltCrwCache[fltno]);

        } else {

          var param = {
            "flightNo": fltno,
          }

          $.ajax({
            type: 'post',
            url: "/bi/web/findFlightReportV2",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {

              if (response.data && response.data[0]) {
                var data = response.data[0];

                /*
                // 飞行组
                var crew = data.crwPilotInf;
                crew = crew.replace(/\d+/g,''); // 删除数字
                var arr = crew.split('；');
                var names = [];
                for(var i in arr){
                    var t = arr[i];
                    var n = t.split(':')[0];
                    names.push(n);
                }
                var pilot = names.splice(0,3);
                var captain = pilot.shift();
    
                // 乘务员
                var crew = data.crwStewardInf;
                crew = crew.replace(/\d+/g,''); // 删除数字
                var arr = crew.split('；');
                var names = [];
                for(var i in arr){
                    var t = arr[i];
                    var n = t.split(':')[0];
                    names.push(n);
                }
                var steward = names.splice(0,6);
                var steward_all = names;
    
                // 安全员
                var crew = data.safer1.replace(/\d+/g,''); // 删除数字
                var safer = crew.split('/');
                safer = safer.splice(0,2);
                */

                //
                fltCrwCache[fltno] = data;

                if (selectedFltno == fltno) {
                  setCrw(fltCrwCache[fltno]);
                }



              }
            },
            error: function () { }
          });
        }

        function setCrw(crew) {
          // 机组信息

          if (crew) {
            var names = [];

            var str = '';
            if (crew["captain"] == undefined) {
              crew["captain"] = "";
            }
            if (crew["firstVice1"] == undefined) {
              crew["firstVice1"] = "";
            }
            names = names.concat(crew.captain.split('@'));
            names = names.concat(crew.firstVice1.split('@'));

            for (var i = names.length - 1; i >= 0; i--) {
              if (names[i].indexOf('(') > -1) {
                var aaa = names[i].split('(');
                if (aaa.length > 1) {
                  names[i] = aaa[0];
                }
              }
            }
            console.log('captain:', names);
            $('#detail_crwPilotInf').text(names.join(','));
            $('#detail_crwStatus').text('');
          }
        }



        // ------------------------------------------------------------------------
        // 获取 航班保障信息
        // ------------------------------------------------------------------------

        /*
        cncPilotArrTime,//机组到达|飞行
        cncStewardArrTime,//机组到达|乘务
        //航班截载 无法获得 checkInEnd
        cncCabinSupplyEndTime,//客舱供应|结束，机供品配备
        cncCleanEndTime,//客舱清洁
        cncMCCReleaseTime,//机务放行
        //飞机准备好 无法获得 planeReady
        cncInformBoardTime,//通知登机
        cncBoardOverTime,//登机结束
        cncClosePaxCabinTime,//客舱关闭
        cncCloseCargoCabinTime,//货舱关闭
        cncPushTime,//飞机推出
        cncACARSTOFF,//飞机起飞
        */


        for (var i in fltnodes) {
          var node = fltnodes[i];
          if (!$('#fltnode_' + node).hasClass('blue3')) {
            $('#fltnode_' + node).addClass('blue3');
          }
          $('#fltnode_' + node).removeClass('green');
          $('#fltnode_' + node).removeClass('yellow');
          $('#fltnode_' + node).removeClass('red');
        }

        if (status == 'ARR' || status == 'NDR' || status == 'ATA' || status == 'DEP' || status == 'RTR') {
          lightThemUp('cncACARSTOFF');
        } else if (status == 'ATD') {
          lightThemUp('cncPushTime');
        }


        // 只有这几个状态才去获取航班保障信息，其它状态没必要
        if (status == 'SCH' || status == 'DEL') {

          if (fltLegCache[fltno]) {

            setLeg(fltLegCache[fltno]);

          } else {

            var param = {
              "flightNo": fltno
            }
            $.ajax({
              type: 'post',
              url: "/bi/web/getFltmLegsByPage",
              contentType: 'application/json',
              dataType: 'json',
              async: true,
              data: JSON.stringify(param),
              success: function (response) {

                if (selectedFltno == fltno && response) {
                  fltLegCache[fltno] = response;
                  setLeg(fltLegCache[fltno]);
                }


              },
              error: function () { }
            });

          }

        }

        function setLeg(response) {
          var mlegs = response.getFltmLegsByPage;
          if (response && mlegs) {
            if (mlegs.cncPilotArrTime && mlegs.cncPilotArrTime.length > 0) {
              lightThemUp('cncPilotArrTime');
            }
            if (mlegs.cncCabinSupplyEndTime && mlegs.cncCabinSupplyEndTime.length > 0) {
              lightThemUp('cncCabinSupplyEndTime');
            }
            if (mlegs.cncCleanEndTime && mlegs.cncCleanEndTime.length > 0) {
              lightThemUp('cncCleanEndTime');
            }
            if (mlegs.cncMCCReleaseTime && mlegs.cncMCCReleaseTime.length > 0) {
              lightThemUp('cncMCCReleaseTime');
            }
            if (mlegs.cncInformBoardTime && mlegs.cncInformBoardTime.length > 0) {
              lightThemUp('cncInformBoardTime');
            }
            if (mlegs.cncBoardOverTime && mlegs.cncBoardOverTime.length > 0) {
              lightThemUp('cncBoardOverTime');
            }
            if (mlegs.cncClosePaxCabinTime && mlegs.cncClosePaxCabinTime.length > 0) {
              lightThemUp('cncClosePaxCabinTime');
            }
            if (mlegs.cncCloseCargoCabinTime && mlegs.cncCloseCargoCabinTime.length > 0) {
              lightThemUp('cncCloseCargoCabinTime');
            }
            if (mlegs.cncPushTime && mlegs.cncPushTime.length > 0) {
              lightThemUp('cncPushTime');
            }
            if (mlegs.cncACARSTOFF && mlegs.cncACARSTOFF.length > 0) {
              lightThemUp('cncACARSTOFF');
            }
          }

        }



        $('#flight_details_loading').hide();
        $('#flight_details').show();

      }

      $('.tab_normal').on('click', function () {

        selectFltTab($(this).attr('id'), true);

      });

      var flt_tab_id = 'tab_vip';

      function selectFltTab(id, selectFirstItem) {
        $('.tab_normal').removeClass('tab_selected');
        $('#' + id).addClass('tab_selected');

        flt_tab_id = id;

        $('#flight_detail_tab_content').hide();
        $('#abnormal_flights_tab_content').hide();
        if (id == 'tab_vip') {
          createFltList(vip_flt_no_list, selectFirstItem);
        } else if (id == 'tab_vvip') {
          createFltList(vvip_flt_no_list, selectFirstItem);
        } else if (id == 'tab_warning') {
          createFltList(warning_flt_no_list, selectFirstItem);
        } else if (id == 'tab_important') {
          createFltList(important_flt_no_list, selectFirstItem);
        } else if (id == 'tab_abnormal') {
          $('#flight_detail_tab_content').hide();
          $('#abnormal_flights_tab_content').show();
          $('#block_securemonitor').hide();
          $('#block_abnormal_flights').show();
        } else if (id == 'tab_securemonitor') {
          $('#flight_detail_tab_content').hide();
          $('#abnormal_flights_tab_content').show();
          $('#block_securemonitor').show();
          $('#block_abnormal_flights').hide();
        }
      }

      //----------------------------------------- 不正常航班tab，安全监控 start
      var weekDateRangeList = [];
      function getAllCompanyKpiData1() {
        var loadingInProgress = 0;

        /*// check url hash
        var len = companylist.length;*/
        var all_comp_codelist = [];
        //var codelist_no_parent = [];

        all_comp_codelist.push(comp_code);

        var kpi_list = [
          'EXCUTED_NO', //执行班次
          //'TRV_NUM', //旅客量
          //'CKI_NUM', //已执行运输旅客量
          'NORMAL_RATE_ZT_MOLECULE', //正常班次
          'NORMAL_RATE_ZT_DENOMINATOR', //计划班次
          //'EST_INC_FUEL', //预估收入(含油)

          'CANCEL_NO', //取消班次
          'DIV_NO', //备降班次
          'TURNBACK_NO', //返航班次
          'DELAY_NO_240' //延误4小时
        ];


        // 本期
        if (all_company_data['kpi_value_d'] == undefined) {
          loadingInProgress++;
          var param = {
            'SOLR_CODE': 'FAC_COMP_KPI',
            'COMP_CODE': all_comp_codelist.join(','),
            'KPI_CODE': kpi_list.join(','),
            'VALUE_TYPE': 'kpi_value_d', //本期
            'DATE_TYPE': 'L,M',
            "OPTIMIZE": 1,
            'LIMIT': query_limit
          };

          $.ajax({
            type: 'post',
            url: "/bi/query/getkpi",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {

              if (response.data != undefined) {
                all_company_data['kpi_value_d'] = response.data;

                ///////
                var ddd = response.data[comp_code]['NORMAL_RATE_ZT_DENOMINATOR']['L'];
                var week_list = [];
                for (var date in ddd) {
                  week_list.push(date);
                }
                weekDateRangeList = [];

                var param = {
                  "DATE_ID": week_list.join(','),
                  "FIELD": "DATE_DESC" // 对应数据表字段 DATE_DESC_XS
                };

                $.ajax({
                  type: 'post',
                  url: "/bi/web/datetype",
                  contentType: 'application/json',
                  dataType: 'json',
                  async: true,
                  data: JSON.stringify(param),
                  success: function (response) {
                    for (var k in response) {
                      var v = response[k];
                      if (!isNaN(k)) {
                        var arr = v.split('-');
                        var weeknum = Number(k.substr(4, 3));

                        var d1 = arr[0].substr(0, 4) + '-' + arr[0].substr(4, 2) + '-' + arr[0].substr(6, 2);
                        var d2 = arr[1].substr(0, 4) + '-' + arr[1].substr(4, 2) + '-' + arr[1].substr(6, 2);

                        weekDateRangeList.push({
                          date: k,
                          range: [d1, d2],
                          weeknum: weeknum
                        });
                      }
                    }

                    // 从大->小
                    weekDateRangeList.sort(function (a, b) {
                      return b.date - a.date
                    });
                    loadingInProgress--;
                    checkDataReady();
                  },
                  error: function () { }
                });


              }

            },
            error: function () {

            }
          });
        }

        // 上期
        if (all_company_data['kpi_value_sq_d'] == undefined) {
          loadingInProgress++;
          var param = {
            'SOLR_CODE': 'FAC_COMP_KPI',
            'COMP_CODE': all_comp_codelist.join(','),
            'KPI_CODE': kpi_list.join(','),
            'VALUE_TYPE': 'kpi_value_sq_d', //上期
            'DATE_TYPE': 'L,M',
            "OPTIMIZE": 1,
            'LIMIT': query_limit
          };

          $.ajax({
            type: 'post',
            url: "/bi/query/getkpi",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {

              if (response.data != undefined) {
                all_company_data['kpi_value_sq_d'] = response.data;
                loadingInProgress--;
                checkDataReady();
              }

            },
            error: function () {

            }
          });
        }
        function checkDataReady() {
          if (loadingInProgress == 0) {
            getAllCompanyKpiData();
          }
        }
        checkDataReady();

      }//end getAllCompanyKpiData1() 

      function getCurrentDate() {
        if (abnormal_date_type == 'L') {
          return $('#abnormal_date_select .tab_' + abnormal_date_type).attr('curdate');
        } else if (abnormal_date_type == 'M') {
          var curMon = $('#abnormal_date_select .tab_' + abnormal_date_type).attr('curdate');
          if (curMon) return curMon;
          return moment().format('YYYY-MM');
        }
        return moment().format('YYYY-MM-DD');
      }

      function resetFilters() {
        runTimeEventListNameFilter = '';
        eventListReasonFilter = '';
        eventListLevelFilter = '';
        isPunishFilter = 0;
      }

      function getAllCompanyKpiData() {

        var kpiDataReady = false;

        // reset
        resetFilters();

        // 接口用到的当日 开始 结束 时间
        var curDateStr = getCurrentDate();
        var startBjTime, endBjTime, startBjTime2, endBjTime2;

        if (abnormal_date_type == 'D') {
          var date = moment(curDateStr);
          startBjTime = endBjTime = date.format('YYYY-MM-DD');

          // 同期 前一天
          date.subtract(1, 'day');
          startBjTime2 = endBjTime2 = date.format('YYYY-MM-DD');

          // --- 时间end

        } else if (abnormal_date_type == 'L') {
          var week1;
          var week2;

          var len = weekDateRangeList.length;
          var today_ = moment().format('YYYY-MM-DD');
          for (var i = 0; i < len; i++) {
            var wdat = weekDateRangeList[i];
            if ((curDateStr && wdat.date == curDateStr)
              || (wdat.range[0] <= today_ && wdat.range[1] >= today_)) {
              week1 = wdat;
              week2 = weekDateRangeList[i + 1];
              break;
            }
          }

          if (week1 != null) {
            $('#abnormal_date_select .tab_L').attr('curdate', week1.date);
            startBjTime = week1.range[0];
            endBjTime = week1.range[1];

            if (moment(endBjTime).isAfter(moment())) {
              endBjTime = moment().format('YYYY-MM-DD');
            }
          }

          if (week2 != null) {
            startBjTime2 = week2.range[0];
            endBjTime2 = week2.range[1];
          }

          // --- 时间end

        } else if (abnormal_date_type == 'M') {
          var curMon = moment().format('YYYY-MM');
          //curMon='2019-08';
          $('#abnormal_date_select .tab_M').attr('curdate', curMon);
          var day1 = moment(curMon + '-01');
          var days = day1.daysInMonth();
          var day2 = moment(curMon + '-' + days);

          startBjTime = day1.format('YYYY-MM-DD');
          endBjTime = day2.format('YYYY-MM-DD');

          if (curMon == moment().format('YYYY-MM')) {
            endBjTime = moment().format('YYYY-MM-DD');
          }

          day1 = day1.subtract(1, 'months');
          var days = day1.daysInMonth();
          day2 = moment(day1.format('YYYY-MM') + '-' + days);
          startBjTime2 = day1.format('YYYY-MM-DD');
          endBjTime2 = day2.format('YYYY-MM-DD');

          // --- 时间end

        }

        var loadingInProgress = 0;


        // ------------------------------------------------------------------------
        // 运行非正常统计列表
        // ------------------------------------------------------------------------
        //if(abnormal_date_type == 'D'){

        loadingInProgress++;
        var param = {
          "companyCode": yhscode,
          "startTime": startBjTime,
          "endTime": endBjTime
        };

        $.ajax({
          type: 'post',
          url: "/bi/web/runTimeEventList",
          contentType: 'application/json',
          dataType: 'json',
          async: true,
          data: JSON.stringify(param),
          success: function (response) {
            all_company_data['runTimeEventList'] = response.records;

            loadingInProgress--;
            checkDataReady();
          },
          error: function () { }
        });

        //}


        //if(abnormal_date_type == 'L'){

        loadingInProgress++;
        param = {
          "companyCode": yhscode,
          "startTime": startBjTime,
          "endTime": endBjTime
        };

        $.ajax({
          type: 'post',
          url: "/bi/web/runTimeEventTypeStatic",
          contentType: 'application/json',
          dataType: 'json',
          async: true,
          data: JSON.stringify(param),
          success: function (response) {
            if (response.result && response.result.staticList) {
              all_company_data['runTimeEventTypeStatic'] = response.result;
            } else {
              console.log('runTimeEventTypeStatic 接口错误');
            }

            loadingInProgress--;
            checkDataReady();
          },
          error: function () { }
        });


        loadingInProgress++;
        param = {
          "companyCode": yhscode,
          "startTime": startBjTime2,
          "endTime": endBjTime2
        };

        $.ajax({
          type: 'post',
          url: "/bi/web/runTimeEventTypeStatic",
          contentType: 'application/json',
          dataType: 'json',
          async: true,
          data: JSON.stringify(param),
          success: function (response) {
            if (response.result && response.result.staticList) {
              all_company_data['runTimeEventTypeStatic_sq'] = response.result;
            } else {
              console.log('runTimeEventTypeStatic_sq 接口错误');
            }

            loadingInProgress--;
            checkDataReady();
          },
          error: function () { }
        });

        //}



        // ------------------------------------------------------------------------
        // 安全不正常事件统计
        // ------------------------------------------------------------------------
        //if(abnormal_date_type == 'D'){

        all_company_data['findEventStatic'] = {};
        //all_company_data['findEventStatic_sq'] = {};

        var ycode = 'HNA';//comp_code;//'ALL';
        /*for (var yc in companyYshcode2Code) {
            if (companyYshcode2Code[yc] == current_company_code) {
                ycode = yc;
            }
        }*/

        loadingInProgress++;
        param = {
          "companyCode": ycode,
          "startTime": startBjTime + ' 00:00:00',
          "endTime": endBjTime + ' 23:59:59'
        };

        $.ajax({
          type: 'post',
          url: "/bi/web/findEventStatic",
          contentType: 'application/json',
          dataType: 'json',
          async: true,
          data: JSON.stringify(param),
          success: function (response) {
            all_company_data['findEventStatic'][comp_code] = response.result;

            loadingInProgress--;
            checkDataReady();
          },
          error: function () { }
        });



        //}



        // ------------------------------------------------------------------------
        // 安全非正常统计列表
        // ------------------------------------------------------------------------
        //if(abnormal_date_type == 'D'){
        loadingInProgress++;
        param = {
          "companyCode": yhscode,
          "startTime": startBjTime,
          "endTime": endBjTime
        };

        $.ajax({
          type: 'post',
          url: "/bi/web/findEventList",
          contentType: 'application/json',
          dataType: 'json',
          async: true,
          data: JSON.stringify(param),
          success: function (response) {
            all_company_data['findEventList'] = response.result.eventList;

            loadingInProgress--;
            checkDataReady();
          },
          error: function () { }
        });

        //}



        // 运行非正常事件属性情况统计接口
        //if(abnormal_date_type == 'L'){
        loadingInProgress++;
        param = {
          "companyCode": yhscode,
          "startTime": startBjTime,
          "endTime": endBjTime,
        };
        $.ajax({
          type: 'post',
          url: "/bi/web/runTimePropertiesStatic",
          contentType: 'application/json',
          dataType: 'json',
          async: true,
          data: JSON.stringify(param),
          success: function (response) {
            //response.eventCount //不安全事件总数
            all_company_data['runTimePropertiesStatic'] = response.result.staticList;

            loadingInProgress--;
            checkDataReady();
          },
          error: function () { }
        });
        //}



        function checkDataReady() {
          if (loadingInProgress == 0 && !kpiDataReady) {
            kpiDataReady = true;
            //fetchingKpiData = false;

            updateAllKpi();
            //hideLoading();
          }
        }

        checkDataReady();

      }


      function getKpiVal(data_type, kpi_code) {
        var today = moment().format('YYYY-MM-DD');


        var date = getCurrentDate();
        date = date.replace(/\-/g, '');


        var sum = 0;
        var compcode = comp_code;// current_company_code
        /*if (compcode == parent_company) {
  
            var len = companylist.length;
            for (var i = 0; i < len; i++) {
                
                var dat = companylist[i];
                if (dat.code != parent_company) {
                    var ddd = all_company_data[data_type][dat.code][kpi_code][abnormal_date_type];
                    var val = Number(ddd[date]);
                    if (!isNaN(val)) {
                        sum += Number(val);
                    }
                }
            }
  
        } else {*/
        var ddd = all_company_data[data_type][compcode][kpi_code][abnormal_date_type];
        var val = Number(ddd[date]);
        if (!isNaN(val)) {
          sum = Number(val);
        }
        //}

        return sum;
      }//end getKpiVal

      function updateAllKpi() {

        // 左上角

        // 航班量 ------------------------------------------------------
        var sch_total = getKpiVal('kpi_value_d', 'NORMAL_RATE_ZT_DENOMINATOR');
        var sch_sq = getKpiVal('kpi_value_sq_d', 'NORMAL_RATE_ZT_DENOMINATOR');

        // var exc_total = getKpiVal('kpi_value_d', 'EXCUTED_NO');
        // var exc_sq = getKpiVal('kpi_value_sq_d', 'EXCUTED_NO');

        var nor_total = getKpiVal('kpi_value_d', 'NORMAL_RATE_ZT_MOLECULE');
        var nor_sq = getKpiVal('kpi_value_sq_d', 'NORMAL_RATE_ZT_MOLECULE');

        // 正常率 ------------------------------------------------------
        var normal_rate = sch_total > 0 ? Math.round((nor_total / sch_total) * 10000) / 100 : 0;  

        // sq
        var normal_rate_sq = sch_sq > 0 ? Math.round((nor_sq / sch_sq) * 10000) / 100 : 0; 

  

        // 环比=(本期-上期)/上期×100%
        // var hb = normal_rate_sq > 0 ? Math.round((normal_rate-normal_rate_sq)/normal_rate_sq*100)/100 : 0;

        // 环比=本期-上期
        var hb = Number(normal_rate) - Number(normal_rate_sq);

        $('#val_NORMAL_RATE').text(trimDecimal(normal_rate, 1) + '%');
        $('#hb_NORMAL_RATE').text(trimDecimal(hb, 1) + '%');

        $('#hb_NORMAL_RATE').removeClass('red');
        $('#hb_NORMAL_RATE').removeClass('green');

        $('#hb_NORMAL_RATE_arr').removeClass('up');
        $('#hb_NORMAL_RATE_arr').removeClass('down');
        if (hb > 0) {
          $('#hb_NORMAL_RATE').addClass('green');
          $('#hb_NORMAL_RATE_arr').addClass('up');
        } else if (hb < 0) {
          $('#hb_NORMAL_RATE').addClass('red');
          $('#hb_NORMAL_RATE_arr').addClass('down');
        }



        // 运行不正常 ------------------------------------------------------
        // 取消 //////////
        var cancel = getKpiVal('kpi_value_d', 'CANCEL_NO'); //Number(stsdat.qftc);//取消
        $('#val_CANCEL').text(cancel);

        // 返航备降 //////////
        var fhbj = getKpiVal('kpi_value_d', 'DIV_NO') + getKpiVal('kpi_value_d', 'TURNBACK_NO');
        $('#val_TURNBACK_NO').text(fhbj);


        // 延误1-2 //////////
        if (abnormal_date_type == 'D' && moment().format('YYYY-MM-DD') == getCurrentDate()) {
          var stsdat = all_company_data['flt_sts'];
          $('#val_DELAY_NO_1_2').text(stsdat.pfdtc12);
          $('#val_DELAY_NO_2_4').text(stsdat.pfdtc24);

          $('.delay12').show();
          $('.delay24').show();
        } else {
          $('#val_DELAY_NO_1_2').text('');
          $('#val_DELAY_NO_2_4').text('');

          $('.delay12').hide();
          $('.delay24').hide();
        }

        // 延误>4 //////////
        var delay4 = getKpiVal('kpi_value_d', 'DELAY_NO_240');
        $('#val_DELAY_NO_240').text(delay4);


        // 安全不正常 ------------------------------------------------------
        /*
        安全事件总数 eventCount
        意外事件总数 accidentCount
        意外事件占 accidentRate
        机械事件总数 machineCount
        机械事件占 machineRate
        为事件总数 humanEventCount
        为事件占 humanEventRate
        其他事件总数 otherEventCount
        其他事件占 otherEventRate
        事故征候总数 signCount
        一类事件总数 seriousCount
        航空公司三字码 companyCode
        */
        var evtstsdat = all_company_data['findEventStatic'][comp_code];
        console.log('findEventStatic', evtstsdat);
        //var evtstsdat_sq = all_company_data['findEventStatic_sq'][current_company_code];
        var eventCount = 0;
        var eventCount_sq = 0;
        if (evtstsdat) {
          eventCount = Number(evtstsdat.eventCount);
          eventCount_sq = Number(evtstsdat.eventCountPre);
        } else {
          eventCount = 0;
          eventCount_sq = 0;
        }

        // 人为
        var humanEventCount = evtstsdat ? Number(evtstsdat.humanEventCount) : 0;
        var humanEventRate = evtstsdat ? Number(evtstsdat.humanEventRate) : 0;

        // 意外
        var accidentCount = evtstsdat ? Number(evtstsdat.accidentCount) : 0;
        var accidentRate = evtstsdat ? Number(evtstsdat.accidentRate) : 0;

        // 机械
        var machineCount = evtstsdat ? Number(evtstsdat.machineCount) : 0;
        var machineRate = evtstsdat ? Number(evtstsdat.machineRate) : 0;

        // 其它
        var otherEventCount = evtstsdat ? Number(evtstsdat.otherEventCount) : 0;
        var otherEventRate = evtstsdat ? Number(evtstsdat.otherEventRate) : 0;

        if (lang == 'en') {
          var evtlist = [{
            name: 'Man-made',
            value: humanEventCount
          }, {
            name: 'Accident',
            value: accidentCount
          }, {
            name: 'Maintenance',
            value: machineCount
          }, {
            name: 'Other',
            value: otherEventCount
          }];
        } else {
          var evtlist = [{
            name: '人为',
            value: humanEventCount
          }, {
            name: '意外',
            value: accidentCount
          }, {
            name: '机械',
            value: machineCount
          }, {
            name: '其它',
            value: otherEventCount
          }];
        }



        // 

        var runtimests = all_company_data['runTimeEventTypeStatic'];
        var runtimests_sq = all_company_data['runTimeEventTypeStatic_sq'];

        var run_eventCount = runtimests ? Number(runtimests.eventCount) : 0;
        var run_eventCount_sq = runtimests_sq ? Number(runtimests_sq.eventCount) : 0;

        var elist = runtimests ? runtimests.staticList : [];
        var elist_sq = runtimests_sq ? runtimests_sq.staticList : [];
        var human_evt = 0;
        var human_evt_sq = 0;
        for (var i = 0; i < elist.length; i++) {
          var itm = elist[i];
          if (itm.code == 1 || itm.code == 3) { //公司人为 1, 代理人为 3
            human_evt += Number(itm.count);
          }
        }
        for (var i = 0; i < elist_sq.length; i++) {
          var itm = elist_sq[i];
          if (itm.code == 1 || itm.code == 3) { //公司人为 1, 代理人为 3
            human_evt_sq += Number(itm.count);
          }
        }

        //var hb = human_evt_sq > 0 ? Math.round((human_evt-human_evt_sq)/human_evt_sq*10000)/100 : 0;
        var hb = human_evt - human_evt_sq;

        $('#abnormal_main_val .val').text(human_evt);
        if (lang == 'en') {
          $('#abnormal_main_val .name').text('Man-made');
        } else {
          $('#abnormal_main_val .name').text('人为');
        }

        $('#abnormal_main_hb .val').text(hb);
        if (hb > 0) {
          $('#abnormal_main_hb .val').text('+' + hb);
        }

        $('#abnormal_main_hb .val').removeClass('red');
        $('#abnormal_main_hb .val').removeClass('green');

        $('#abnormal_main_hb .arr').removeClass('up_red');
        $('#abnormal_main_hb .arr').removeClass('down_green');
        if (hb > 0) {
          $('#abnormal_main_hb .val').addClass('red');
          $('#abnormal_main_hb .arr').addClass('up_red');
        } else if (hb < 0) {
          $('#abnormal_main_hb .val').addClass('green');
          $('#abnormal_main_hb .arr').addClass('down_green');
        }



        $('#abnormal_total').text(run_eventCount);

        //var hb = run_eventCount_sq > 0 ? Math.round((run_eventCount-run_eventCount_sq)/run_eventCount_sq*10000)/100 : 0;
        var hb = run_eventCount - run_eventCount_sq;
        $('.abnormal_hb .val').text(hb);
        if (hb > 0) {
          $('#abnormal_hb .val').text('+' + hb);
        }

        $('.abnormal_hb .val').removeClass('red');
        $('.abnormal_hb .val').removeClass('green');

        $('.abnormal_hb .arr').removeClass('up_red');
        $('.abnormal_hb .arr').removeClass('down_green');
        if (hb > 0) {
          $('.abnormal_hb .val').addClass('red');
          $('.abnormal_hb .arr').addClass('up_red');
        } else if (hb < 0) {
          $('.abnormal_hb .val').addClass('green');
          $('.abnormal_hb .arr').addClass('down_green');
        }



        $('#eventCount').text(eventCount);

        $('#humanEventCount').text(humanEventCount);
        $('#humanEventRate').text(humanEventRate);

        $('#machineCount').text(machineCount);
        $('#machineRate').text(machineRate);


        // 环比=(本期-上期)/上期×100%
        //var hb = eventCount_sq > 0 ? Math.round((eventCount-eventCount_sq)/eventCount_sq*100)/100 : 0;
        //$('.event_hb .val').text(trimDecimal(hb,2)+'%');

        var hb = eventCount - eventCount_sq;

        $('.event_hb .val').text(hb);
        if (hb > 0) {
          $('.event_hb .val').text('+' + hb);
        }

        $('.event_hb .val').removeClass('red');
        $('.event_hb .val').removeClass('green');

        //$('.event_hb .arr').removeClass('up_red');
        //$('.event_hb .arr').removeClass('down_green');
        if (hb > 0) {
          $('.event_hb .val').addClass('red');
          //$('.event_hb .arr').addClass('up_red');
        } else if (hb < 0) {
          $('.event_hb .val').addClass('green');
          //$('.event_hb .arr').addClass('down_green');
        }

        // var seriousCount = evtstsdat ? Number(evtstsdat.seriousCount) : 0;
        // 一类事件等于一类事件数+岗位红线数
        var seriousCount = evtstsdat ? Number(evtstsdat.seriousCount) : 0;
        if (evtstsdat) {
          seriousCount += Number(evtstsdat.securityLineCount);
        }
        //var seriousCount_sq = evtstsdat_sq ? Number(evtstsdat_sq.seriousCount) : 0;
        $('#seriousCount').text(seriousCount);
        /*
        var os = seriousCount - seriousCount_sq
        if(os > 0){
            os = '+'+os
        }
        $('#seriousCountOS').text(os);
        */

        // 事件属性分析 ------------------------------------------------------
        setChartRunTimeCircle();

        showRunTimeEventList();
        showSecurityEventList();
      }//end updateAllKpi

      function showRunTimeEventList() {
        var list = all_company_data['runTimeEventList'];


        var fltListByComp = {};
        var len = companylist.length;
        for (var i = 0; i < len; i++) {

          var dat = companylist[i];
          if (dat.code != parent_company) {
            fltListByComp[dat.yhscode] = {
              list: [],
              code: dat.code,
              name: dat.name,
              sort: dat.sort
            };
          }
        }

        var legendNameList = [];

        var prolist = all_company_data['runTimePropertiesStatic'];
        prolist.sort(function (a, b) {
          return a.code - b.code;
        });

        for (var i in prolist) {
          var d = prolist[i];
          legendNameList.push(d.name);
        }

        var eventKVList = {};
        for (var i = list.length - 1; i >= 0; i--) {
          var dd = list[i];
          eventKVList[dd.id] = dd;
          if (fltListByComp[dd.companyCode]) {

            if (dd.eventAttrName.indexOf(runTimeEventListNameFilter) == -1 && runTimeEventListNameFilter != '') {
              continue;
            }

            fltListByComp[dd.companyCode].list.push(dd);
          }
        }

        var eventList = [];
        for (var yshcode in fltListByComp) {
          var dd = fltListByComp[yshcode];
          eventList.push(dd);
        }

        eventList.sort(function (a, b) {
          return a.sort - b.sort
        });



        /*
        acType: "A320"
        airlineCn: "济南-三亚"
        airlineEn: "TNA-SYX"
        arrstn: "SYX"
        arrstnName: "三亚"
        comments: "null"
        companyCode: "CHB"
        companyName: "西部航空"
        createdBy: "zh_bai"
        createdByName: "白周浩"
        createdTime: "1516872201000"
        deleted: "false"
        depstn: "TNA"
        depstnName: "济南"
        deptId: ",4,"
        deptName: "维修工程部"
        description: "1月24日，PN6364航班，B6765飞机，执行济南-三亚，计划起飞时间0925，前段航班0806到位，过站机组检查飞机时发现3号主轮有扎上，明显看到有钉子扎入，通知海技重庆MCC，评估需要换轮，0917海技通报暂找不到航材和维修人员，通知机组退场休息，航班延误时间待定，0926西部航材管理询问到东航济南航材库中有A320的轮胎，1200确定轮子可以从东航借，但是确定不了如何领取及搬运，1240轮胎运到飞机下面，开始更换，1342飞机放行，航班1419关舱。最终起飞延误4小时48分。"
        entryTime: "1516872000000"
        eventAttr: ",4,"
        eventAttrName: "工程机务"
        eventType: "2"
        eventTypeName: "公司程序"
        flightDate: "1516723200000"
        flightDateStr: "2018-01-24"
        flightNo: "PN6364"
        id: "21"
        isPunish: "false"
        isRead: "null"
        lastAppUpdatedTime: "null"
        longReg: "B6765"
        noticeTimeStr: "2018-01-25 17:20:00"
        processResult: "1 将类似事件一一记录，向公司反映，评估是否需要取消无过站放行，或者取消保障能力较差的航站的无过站放行政策；?   2 工程部已经制定领导走访方案，重点针对南京、济南保障问题进行现场调研。?   3 建议集团统一协调，提高临时需要维修机务人员支援的工作实效。"
        punishAds: "null"
        punishInfo: "null"
        punishNames: "null"
        punishNos: "null"
        signerName: "汪建劳"
        signerNo: "1000102886"
        sta: "1748"
        status: "3"
        std: "1417"
        surveyResult: "1 公司运行无过站放行，在济南机务只有一般勤务协议，飞机出现故障需要维修或需要机务放行时，均需要临时协调当地机务单位，协调耗时较长，还要看外单位是否支持。?   2 在公司没有取消无过站放行政策以前暂改变不了现状。"
        title: "1月24日PN6364(济南-三亚）航班延误事件"
        updatedBy: "zh_bai"
        updatedByName: "白周浩"
        updatedTime: "1516872201000"
        */



        var html = '';
        var len = eventList.length
        for (var i = 0; i < len; i++) {
          var dd = eventList[i];
          var len2 = dd.list.length;
          if (len2 > 0) {
            html += "<div class='comprow' id='comprow_" + i + "' >";
            // HEAD
            html += "<div class='head'>";
            html += "<div class='tt' style='background-image: url(img/logo_" + dd.code + ".png)'><span class='lb'>" + dd.name + "</span><span class='num'>(" + len2 + "班次)</span></div>";
            if (len2 > 5) {
              html += "<div class='btns'><span class='btn btn_prev disabled' data-row='" + i + "' data-len='" + len2 + "' data-page='0' ></span><span class='btn btn_next' data-row='" + i + "' data-len='" + len2 + "' data-page='0' ></span></div>";
            }
            html += "</div><!-- /head -->";
            // LIST
            html += "<div class='itmlst'>";
            for (var j = 0; j < len2; j++) {
              var evt = dd.list[j];

              var date = evt.flightDateStr;
              var darr = date.split('-');
              date = darr[1] + '/' + darr[2];
              var color = legendColorList1[legendNameList.indexOf(evt.eventAttrName)];

              html += "<div class='blk blk_" + j + "' id='event_id_" + evt.id + "' data-id='" + evt.id + "' data-fltno='" + evt.flightNo + "' data-code='" + dd.code + "' data-date='" + evt.flightDateStr + "' data-depstn='" + evt.depstn + "' >";
              html += "<div class='time'>";
              html += "<span class='l'>" + date + "</span>";
              html += "<span class='r hour'></span>";
              html += "</div>";
              html += "<div class='fltno'>" + evt.flightNo + "</div>";
              var style = '';
              evt.airlineCn = evt.airlineCn.replace(/\//g, '');
              if (evt.airlineCn.length > 7) {
                style = 'position: absolute; line-height:13px; bottom:23px;';
              }
              html += "<div class='city' style='" + style + "' >" + evt.airlineCn + "</div>";
              html += "<div class='bot'><span class='dot' style='color:" + color + "; '>●</span><span class='lb'>" + evt.eventAttrName + "</span></div>";
              html += "</div><!-- /blk -->";

              //
              if (j < 5) {
                fetchFltInfo(dd.code, evt.flightDateStr + ' 00:00:00', evt.flightDateStr + ' 23:59:59', [evt.flightNo], evt.id, evt.depstn);
              }

            }
            html += "</div><!-- /itmlst -->";

            // END
            html += "</div><!-- /comprow -->";
          }
        }


        $('.block_r .tabc1 .scrollcontent').html(html);

        var html = '';
        var len = legendNameList.length;
        for (var i = 0; i < len; i++) {
          var name = legendNameList[i];
          var color;
          var opacity;
          if (runTimeEventListNameFilter == name || runTimeEventListNameFilter == '') {
            color = legendColorList1[i];
            opacity = 1;
          } else {
            color = '#5d9ae3';
            opacity = 0.3;
          }

          html += "<span class='itm' data-name='" + name + "' style='opacity:" + opacity + "' ><span class='dot' style='color:" + color + "; '>●</span><span class='lb'>" + name + "</span></span>";
        }

        $('.block_r .tabc1 .legend .lst').html(html);

        $('.block_r .tabc1 .legend .lst .itm').off('click');
        $('.block_r .tabc1 .legend .lst .itm').on('click', function (evt) {
          if (runTimeEventListNameFilter == '') {
            runTimeEventListNameFilter = $(this).attr('data-name');
          } else {
            runTimeEventListNameFilter = '';
          }

          showRunTimeEventList();
        });


        // scrollbar
        $(".block_r .tabc1 .scrollpane").niceScroll({
          cursorcolor: "rgba(1,75,153,0.8)",
          cursorborder: "rgba(0,0,0,0)"
        });
        $(".block_r .tabc1 .scrollpane").getNiceScroll(0).resize();

        //
        $('.block_r .tabc1 .itmlst .blk').off('click');
        $('.block_r .tabc1 .itmlst .blk').on('click', function (evt) {
          evt.stopPropagation();
          var id = $(this).attr('data-id');
          var code = $(this).attr('data-code');

          showPop($(this), id, code);
        });

        //


        $('.block_r .btn_prev').off('click');
        $('.block_r .btn_prev').on('click', function (evt) {
          evt.stopPropagation();
          if ($(this).hasClass('disabled')) {
            return;
          }
          var id = $(this).attr('data-row');
          var len = $(this).attr('data-len');
          var page = $(this).attr('data-page');
          var perpage = 4;

          page = Number(page) - 1;
          $('#comprow_' + id + ' .btn_next').attr('data-page', page);
          $('#comprow_' + id + ' .btn_prev').attr('data-page', page);

          $('#comprow_' + id + ' .btn_next').removeClass('disabled');
          if (page == 0) {
            $('#comprow_' + id + ' .btn_prev').addClass('disabled');
          }

          for (var i = page * perpage; i < (page + 1) * perpage; i++) {
            var blk = $('#comprow_' + id + ' .blk_' + i);
            var loaded = blk.attr('data-flt-loaded');
            if (isNaN(loaded)) {
              var data_code = blk.attr('data-code');
              var data_id = blk.attr('data-id');
              var data_date = blk.attr('data-date');
              var data_fltno = blk.attr('data-fltno');
              var data_depstn = blk.attr('data-depstn');
              fetchFltInfo(data_code, data_date + ' 00:00:00', data_date + ' 23:59:59', [data_fltno], data_id, data_depstn);
            }
          }

          var width = 376;

          $('#comprow_' + id + ' .itmlst').animate({
            left: -(width * page)
          }, {
            duration: 300
          });
        });


        $('.block_r .btn_next').off('click');
        $('.block_r .btn_next').on('click', function (evt) {
          evt.stopPropagation();
          if ($(this).hasClass('disabled')) {
            return;
          }
          var id = $(this).attr('data-row');
          var len = $(this).attr('data-len');
          var page = $(this).attr('data-page');
          var perpage = 4;

          page = Number(page) + 1;
          $('#comprow_' + id + ' .btn_next').attr('data-page', page);
          $('#comprow_' + id + ' .btn_prev').attr('data-page', page);

          $('#comprow_' + id + ' .btn_prev').removeClass('disabled');
          if (len <= (page + 1) * perpage) {
            $('#comprow_' + id + ' .btn_next').addClass('disabled');
          }

          for (var i = page * perpage; i < (page + 1) * perpage; i++) {
            var blk = $('#comprow_' + id + ' .blk_' + i);
            var loaded = blk.attr('data-flt-loaded');
            if (isNaN(loaded)) {
              var data_code = blk.attr('data-code');
              var data_id = blk.attr('data-id');
              var data_date = blk.attr('data-date');
              var data_fltno = blk.attr('data-fltno');
              var data_depstn = blk.attr('data-depstn');
              fetchFltInfo(data_code, data_date + ' 00:00:00', data_date + ' 23:59:59', [data_fltno], data_id, data_depstn);
            }
          }

          var width = 376;

          $('#comprow_' + id + ' .itmlst').animate({
            left: -(width * page)
          }, {
            duration: 300
          });
        });



        $('#pop_tab1 .btnx').off('click');
        $('#pop_tab1 .btnx').on('click', function (evt) {
          evt.stopPropagation();
          $('#pop_tab1').hide();
        });


        function showPop(elem, id, code) {
          $('#pop_tab2').hide();

          var evt = eventKVList[id];

          var fltinfo = flightInfoList[id];

          var name = evt.eventAttrName;
          var color = legendColorList1[legendNameList.indexOf(evt.eventAttrName)];
          var date = evt.flightDateStr;
          var darr = date.split('-');
          date = darr[1] + '/' + darr[2];


          $('#pop_tab1 .head .fltno').html(evt.companyName + ' ' + evt.flightNo);
          $('#pop_tab1 .head .fltno').css('background-image', 'url(img/logo_' + code + '.png)');
          //

          $('#pop_tab1 .row1 .date').html(date);
          $('#pop_tab1 .row1 .ac').html(evt.acType);
          $('#pop_tab1 .row1 .cabin').html('');


          $('#pop_tab1 .city1 .nm').html(evt.depstnName);
          $('#pop_tab1 .city2 .nm').html(evt.arrstnName);

          if (fltinfo) {
            // 时间
            $('#pop_tab1 .city1 .tm').html(getHMtime(fltinfo.stdChn)); //计划出发
            $('#pop_tab1 .city2 .tm').html(getHMtime(fltinfo.staChn)); //计划到达

            $('#pop_tab1 .city1 .tm2').html(getHMtime(fltinfo.atdChn)); //实际出发
            $('#pop_tab1 .city2 .tm2').html(getHMtime(fltinfo.ataChn)); //实际到达
          }


          var noticeTime = evt.noticeTimeStr;
          var narr = noticeTime.split('-');
          noticeTime = narr[1] + '/' + narr[2];
          noticeTime = noticeTime.substring(0, noticeTime.length - 3);

          // $('#pop_tab1 .row3 .reason').html(evt.eventAttrName);
          $('#pop_tab1 .row3 .bzcsx').html(evt.eventAttrName);
          $('#pop_tab1 .row3 .sjlx').html(evt.eventTypeName);

          $('#pop_tab1 .adjust .fltno').html(evt.flightNo);
          $('#pop_tab1 .adjust .fromto').html(evt.airlineCn);
          $('#pop_tab1 .adjust .date').html(date);
          // $('#pop_tab1 .adjust .tag').html(name);
          // $('#pop_tab1 .adjust .tag').css('background-color', color);

          $('#pop_tab1 .reason_tz').html(evt.description);
          $('#pop_tab1 .time_fb').html(noticeTime);
          $('#pop_tab1 .people_fb').html(evt.createdByName);

          $('#pop_tab1 .reason_yw').html(evt.eventAttrName);

          $('#pop_tab1 .process .result').html(evt.processResult);
          $('#pop_tab1 .process .line2').html(noticeTime + '  发布人: ' + evt.createdByName);

          $('#pop_tab1 .surveyResult').html(evt.surveyResult);

          $('#pop_tab1 .punishInfo').html(evt.isPunish != 'false' ? evt.punishInfo : '无');

          $('#pop_tab1 .comments').html(evt.comments != 'null' ? evt.comments : '无');


          getCabin(evt.longReg, '#pop_tab1 .row1 .cabin');


          var param = {
            "id": id,
          }
          $.ajax({
            type: 'post',
            url: "/bi/web/runTimeEventDetail",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {
              var comments = response.result.comments;
              var lst = [];
              for (var i = 0; i < comments.length; i++) {
                var cmt = comments[i];
                if (cmt.content != '' && cmt.commentByName != '') {
                  lst.push(cmt);
                }
              }
              var statusMap = { "1": "调查分析中", "2": "落实措施中", "3": "已关闭", "4": "待定" };
              var text = "";
              if (statusMap[response.result.status]) {
                text = statusMap[response.result.status];
              }
              $('#pop_tab1 .statustext').text(text);
              var html = '';
              if (lst.length > 0) {
                for (var i = 0; i < lst.length; i++) {
                  var cmt = lst[i];

                  html += "<div class='blktit'>批示意见</div>";
                  html += "<div class='blkcon'>";
                  html += "<div class='blkrow'>";
                  html += "<span class='comments'>" + cmt.content + "</span><br>";
                  html += "<span class='title'>批示人及批示时间</span><br>";
                  html += "<span class=''>" + cmt.commentByName + ' ' + cmt.commentTimeStr + "</span>";
                  html += "</div>";
                  html += "</div>";

                  /*
                  if(cmt.replys.length > 0){
                      for(var j=0; j<cmt.replys.length; j++){
                          var rpy = cmt.replys[j];
                          html += "<div class='blktit'>回复意见</div>";
                          html += "<div class='blkcon'>";
                          html += "<div class='blkrow'>";
                          html += "<span class=''>"+rpy.content+"</span><br>";
                          html += "<span class='title'>回复人及回复时间</span><br>";
                          html += "<span class=''>"+rpy.replyByName+' '+rpy.replyTimeStr+"</span>";
                          html += "</div>";
                          html += "</div>";
                      }
                  }else{
                      html += "<div class='blktit'>回复意见</div>";
                      html += "<div class='blkcon'>";
                      html += "<div class='blkrow'>";
                      html += "<span class=''>无</span><br>";
                      html += "<span class='title'>回复人及回复时间</span><br>";
                      html += "<span class=''>无</span>";
                      html += "</div>";
                      html += "</div>";
                  }
                  */
                }



              } else {
                html += "<div class='blktit'>批示意见</div>";
                html += "<div class='blkcon'>";
                html += "<div class='blkrow'>";
                html += "<span class='comments'>无</span><br>";
                html += "<span class='title'>批示人及批示时间</span><br>";
                html += "<span class=''>无</span>";
                html += "</div>";
                html += "</div>";

                /*
                html += "<div class='blktit'>回复意见</div>";
                html += "<div class='blkcon'>";
                html += "<div class='blkrow'>";
                html += "<span class=''>无</span><br>";
                html += "<span class='title'>回复人及回复时间</span><br>";
                html += "<span class=''>无</span>";
                html += "</div>";
                html += "</div>";
                */
              }

              $('#pop_tab1 .commentlist').html(html);

              $("#pop_tab1 .scrollpane").getNiceScroll(0).resize();
            },
            error: function () { }
          });



          /*
          var param = {
              "eventId": id,
          }
  
          $.ajax({           
              type: 'post',
              url:"/bi/web/runTimeEventComment",
              contentType: 'application/json',
              dataType: 'json',
              async: true,
              data: JSON.stringify(param),
              success: function(response) {
                  
  
              },
              error:function() {
              }
          });
          */


          setXY();

          function setXY() {
            var bx = elem.offset().left;
            var bt = elem.offset().top;

            var left = bx - $('#pop_tab1').width() - 10;
            //var left = bx + elem.width() + 10;
            var arrtop = bt - ($('.page-wrapper').height() - $('#pop_tab1').height() - 20) + elem.height() / 2;

            $('#pop_tab1').css('left', left);
            $('#pop_tab1').css('bottom', 20);
            $('#pop_tab1 .arr').css('top', arrtop);

            $('#pop_tab1').show();


            // scrollbar
            $("#pop_tab1 .scrollpane").niceScroll({
              cursorcolor: "rgba(4,18,62,0.3)",
              cursorborder: "rgba(0,0,0,0)"
            });
            $("#pop_tab1 .scrollpane").getNiceScroll(0).resize();
            $("#pop_tab1 .scrollpane").getNiceScroll(0).doScrollTop(0, 0);
          }

        }

      } //showRunTimeEventList

      // ------------------------------------------------------------------------
      // 获取 航班信息
      // ------------------------------------------------------------------------
      function fetchFltInfo(companyCode, stdStart, stdEnd, fltNoList, elemid, depstn) {

        if (flightInfoList != null) {

          if (flightInfoList[elemid] == undefined) {
            setTimeout(function () {

              var param = {
                "stdStart": stdStart,
                "stdEnd": stdEnd,
                "acOwner": companyCode,
                "statusList": '',
                "fltNoList": fltNoList.join(','),
              }
              $.ajax({
                type: 'post',
                url: "/bi/web/getStandardFocFlightInfo",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                  var list = response.data;
                  if (list) {
                    for (var i = list.length - 1; i >= 0; i--) {
                      var obj = list[i];
                      if (obj.depStn == depstn) {
                        var hourmin = getHMtime(obj.stdChn); // 计划出发
                        $('#event_id_' + elemid + ' .hour').text(hourmin);
                        $('#event_id_' + elemid).attr('data-flt-loaded', '1');
                        flightInfoList[elemid] = obj;
                        break;
                      }
                    }
                  }
                },
                error: function () {
                }
              });

            }, 10);

          } else {
            var obj = flightInfoList[elemid];
            var hourmin = getHMtime(obj.stdChn); // 计划出发
            $('#event_id_' + elemid + ' .hour').text(hourmin);
            $('#event_id_' + elemid).attr('data-flt-loaded', '1');
          }

        }
      }
      function showSecurityEventList() {
        var list = all_company_data['findEventList'];

        var fltListByComp = {};
        var len = companylist.length;
        for (var i = 0; i < len; i++) {

          var dat = companylist[i];
          if (dat.code != parent_company) {
            fltListByComp[dat.yhscode] = {
              list: [],
              code: dat.code,
              name: dat.name,
              sort: dat.sort
            };
          }
        }


        var eventKVList = {};
        for (var i = list.length - 1; i >= 0; i--) {
          var dd = list[i];
          eventKVList[dd.id] = dd;
          if (dd.eventReason != eventListReasonFilter && eventListReasonFilter != '') {
            continue;
          }
          var level = dd.eventLevel;
          if (level == 5) {// 岗位红线归类到严重错误上
            level = 2;
          }
          if (level != eventListLevelFilter && eventListLevelFilter != '') {
            continue;
          }

          if (isPunishFilter == 1 && dd.isPunish == 'false') {
            continue;
          }

          if (fltListByComp[dd.companyCode]) {
            fltListByComp[dd.companyCode].list.push(dd);
          }
        }

        var eventList = [];
        for (var yshcode in fltListByComp) {
          var dd = fltListByComp[yshcode];
          eventList.push(dd);
        }

        eventList.sort(function (a, b) {
          return a.sort - b.sort
        });


        /*
        acType: "E190"
        airlineCn: "临沂-长沙"
        airlineEn: "LYI-HHA"
        arrstn: "HHA"
        arrstnName: "长沙"
        comments: "null"
        companyCode: "CBG"
        companyName: "北部湾航空"
        createdBy: "xt.zhu"
        createdByName: "朱晓彤"
        createdTime: "1515933709000"
        deal: "QAR分析情况：EGPWS警告发生前，飞机状态好，着陆形态建立，速度设置与SOP线相符合，航向道和下滑道无任何偏差；EGPWS警告发生总共21秒，直到接地前7英尺消失，期间下降率正常；警告发生后，飞机始终高于下滑道，未有发生低于下滑道的情况。?工程分析情况：经检查排故，无机械故障情况；经与厂家咨询，可能是外部干扰导致GPS信号丢失。"
        deleted: "false"
        depstn: "LYI"
        depstnName: "临沂"
        description: "2018年1月14日，北部湾航空E190/B3179飞机执行GX8976（临沂-长沙）航班，长沙进近过程中由于GPS信号丢失触发地形警告，经了解，大致从200尺（决断高度）开始响起terrain警告，期间断断续续响了5、6声，机组报告跑道完全能见，预判为假警告，故继续进近，最终于16:58正常落地长沙。"
        eventLevel: "1" // ['安全报告事件', '一类事件', '事故征候', '待定'];
        eventReason: "4" //['意外', '人为', '机械', '其他'];
        eventType: "可控飞行撞地/障碍物"
        flightDate: "1515859200000"
        flightDateStr: "2018-01-14"
        flightNo: "GX8976"
        id: "1117"
        isPunish: "false"
        isRead: "null"
        lastAppUpdatedTime: "null"
        levelDetail: "安全报告事件"
        longReg: "B3179"
        noticeTime: "1515933420000"
        noticeTimeStr: "2018-01-14 20:37:00"
        precaution: "无。"
        punishAds: "null"
        punishInfo: "null"
        punishNames: "null"
        sta: "1658"
        status: "3" //状态跟踪 1:调查分析中、2:落实措施中、3:已 关闭;)
        std: "1517"
        title: "2018年1月14日北部湾航空E190/B3179飞机执行GX8976（临沂-长沙）航班长沙近进触发地形假警告"
        updatedBy: "xt.zhu"
        updatedByName: "朱晓彤"
        updatedTime: "1516064520000"
        */
        // 事件原因: 1:意外; 2:人为; 3:机械; 4: 其他
        if (lang == 'en') {
          var legendNameList = ['Accident', 'Man-made', 'Maintenance', 'Other'];
          var legendNameList2 = ['Non-man-made Issue', 'Serious Error', 'Accident Symptom', 'Security Line', 'Undetermined'];
          var legendName2Key = {
            'Non-man-made Issue': 8,
            'Serious Error': 2,
            'Accident Symptom': 3,
            'Security Line': 5,
            'Undetermined': 3,
          };
          var legendKey2Name = {
            8: 'Non-man-made Issue',
            2: 'Serious Error',
            3: 'Accident Symptom',
            5: 'Security Line',
            3: 'Undetermined',
          };
          var statusList = ['调查分析中', '落实措施中', '已关闭'];
        } else {
          var legendNameList = ['意外', '人为', '机械', '其他'];
          var legendNameList2 = ['安全报告事件', '一类事件', '事故征候', '岗位红线', '待定'];
          var legendName2Key = {
            '安全报告事件': 8,
            '一类事件': 2,
            '事故征候': 3,
            '岗位红线': 5,
            '待定': 3,
          };
          var legendKey2Name = {
            8: '安全报告事件',
            2: '一类事件',
            3: '事故征候',
            5: '岗位红线',
            3: '待定',
          };
          var statusList = ['调查分析中', '落实措施中', '已关闭'];
        }

        var html = '';
        var len = eventList.length
        for (var i = 0; i < len; i++) {
          var dd = eventList[i];
          var len2 = dd.list.length;
          if (len2 > 0) {
            html += "<div class='comprow' id='comprow_" + i + "' >";
            // HEAD
            html += "<div class='head'>";
            html += "<div class='tt' style='background-image: url(img/logo_" + dd.code + ".png)'><span class='lb'>" + dd.name + "</span><span class='num'>(" + len2 + "班次)</span></div>";
            if (len2 > 5) {
              html += "<div class='btns'><span class='btn btn_prev prev-disabled' data-row='" + i + "' data-len='" + len2 + "' data-page='0' ></span><span class='btn btn_next' data-row='" + i + "' data-len='" + len2 + "' data-page='0' ></span></div>";
            }
            html += "</div><!-- /head -->";
            // LIST
            html += "<div class='itmlst'>";
            for (var j = 0; j < len2; j++) {
              var evt = dd.list[j];

              var date = evt.flightDateStr;
              var darr = date.split('-');
              date = darr[1] + '/' + darr[2];
              var color = legendColorList3[Number(evt.eventReason) - 1];
              var color2 = legendColorList4[Number(evt.eventLevel) - 1];

              html += "<div class='blk blk_" + j + "' id='event_id_" + evt.id + "' data-id='" + evt.id + "' data-fltno='" + evt.flightNo + "' data-code='" + dd.code + "' data-date='" + evt.flightDateStr + "' data-depstn='" + evt.depstn + "' >";
              html += "<div class='time'>";
              html += "<span class='l'>" + date + "</span>";
              html += "<span class='r hour'></span>";
              html += "</div>";
              html += "<div class='fltno' style='color:" + color2 + "'>" + evt.flightNo + "</div>";
              var style = '';
              evt.airlineCn = evt.airlineCn.replace(/\//g, '');
              if (evt.airlineCn.length > 7) {
                style = 'position: absolute; line-height:13px; bottom:23px;';
              }
              html += "<div class='city' style='" + style + "' >" + evt.airlineCn + "</div>";
              html += "<div class='bot'><span class='dot' style='color:" + color + "; '>●</span><span class='lb'>" + evt.eventType + "</span></div>";
              html += "</div><!-- /blk -->";

              //
              if (j < 5) {
                fetchFltInfo(dd.code, evt.flightDateStr + ' 00:00:00', evt.flightDateStr + ' 23:59:59', [evt.flightNo], evt.id, evt.depstn);
              }

            }
            html += "</div><!-- /itmlst -->";

            // END
            html += "</div><!-- /comprow -->";
          }
        }


        $('.block_r .tabc2 .scrollcontent').html(html);

        var html = '';
        var len = legendNameList.length;
        for (var i = 0; i < len; i++) {
          var name = legendNameList[i];
          var color;
          var opacity;
          if (eventListReasonFilter == i + 1 || (eventListReasonFilter == '' && eventListLevelFilter == '')) {
            color = legendColorList1[i];
            opacity = 1;
          } else {
            color = '#5d9ae3';
            opacity = 0.3;
          }
          html += "<span class='itm' data-reason='" + (i + 1) + "' style='opacity:" + opacity + "' ><span class='dot' style='color:" + color + "; '>●</span><span class='lb'>" + name + "</span></span>";
        }
        $('.block_r .tabc2 .legend .lst').html(html);

        var html = '';
        var len = legendNameList2.length;
        for (var i = 0; i < len; i++) {
          var name = legendNameList2[i];
          var lev = legendName2Key[name];
          if (lev == "5") {// 如果有岗位红线统一归类到严重错误2上
            continue
          }
          var color;
          var opacity;
          if (eventListLevelFilter == i + 1 || (eventListReasonFilter == '' && eventListLevelFilter == '')) {
            color = legendColorList2[i];
            opacity = 1;
          } else {
            color = '#5d9ae3';
            opacity = 0.3;
          }
          html += "<span class='itm' data-level='" + lev + "' style='opacity:" + opacity + "' ><span class='dot' style='color:" + color + "; '>●</span><span class='lb'>" + name + "</span></span>";
        }
        $('.block_r .tabc2 .legend .lst2').html(html);


        $('.block_r .tabc2 .legend .lst .itm').off('click');
        $('.block_r .tabc2 .legend .lst .itm').on('click', function (evt) {
          if (eventListReasonFilter == '') {
            eventListReasonFilter = $(this).attr('data-reason');
          } else {
            eventListReasonFilter = '';
          }

          showSecurityEventList();

        })

        $('.block_r .tabc2 .legend .lst2 .itm').off('click');
        $('.block_r .tabc2 .legend .lst2 .itm').on('click', function (evt) {
          if (eventListLevelFilter == '') {
            eventListLevelFilter = $(this).attr('data-level');
          } else {
            eventListLevelFilter = '';
          }

          showSecurityEventList();
        })


        // scrollbar
        $(".block_r .tabc2 .scrollpane").niceScroll({
          cursorcolor: "rgba(1,75,153,0.8)",
          cursorborder: "rgba(0,0,0,0)"
        });
        $(".block_r .tabc2 .scrollpane").getNiceScroll(0).resize();

        //
        $('.block_r .tabc2 .itmlst .blk').off('click');
        $('.block_r .tabc2 .itmlst .blk').on('click', function (evt) {
          evt.stopPropagation();
          var id = $(this).attr('data-id');
          var code = $(this).attr('data-code');
          showPop($(this), id, code);
        });

        //

        $('.block_r .btn_prev').off('click');
        $('.block_r .btn_prev').on('click', function (evt) {
          evt.stopPropagation();
          if ($(this).hasClass('prev-disabled')) {
            return;
          }
          var id = $(this).attr('data-row');
          var len = $(this).attr('data-len');
          var page = $(this).attr('data-page');
          var perpage = 4;

          page = Number(page) - 1;
          $('#comprow_' + id + ' .btn_next').attr('data-page', page);
          $('#comprow_' + id + ' .btn_prev').attr('data-page', page);

          $('#comprow_' + id + ' .btn_next').removeClass('next-disabled');
          if (page == 0) {
            $('#comprow_' + id + ' .btn_prev').addClass('prev-disabled');
          }

          for (var i = page * perpage; i < (page + 1) * perpage; i++) {
            var blk = $('#comprow_' + id + ' .blk_' + i);
            var loaded = blk.attr('data-flt-loaded');
            if (isNaN(loaded)) {
              var data_code = blk.attr('data-code');
              var data_id = blk.attr('data-id');
              var data_date = blk.attr('data-date');
              var data_fltno = blk.attr('data-fltno');
              var data_depstn = blk.attr('data-depstn');
              fetchFltInfo(data_code, data_date + ' 00:00:00', data_date + ' 23:59:59', [data_fltno], data_id, data_depstn);
            }
          }

          var width = 365;

          $('#comprow_' + id + ' .itmlst').animate({
            left: -(width * page)
          }, {
            duration: 300
          });
        });


        $('.block_r .btn_next').off('click');
        $('.block_r .btn_next').on('click', function (evt) {
          evt.stopPropagation();
          if ($(this).hasClass('next-disabled')) {
            return;
          }
          var id = $(this).attr('data-row');
          var len = $(this).attr('data-len');
          var page = $(this).attr('data-page');
          var perpage = 4;

          page = Number(page) + 1;
          $('#comprow_' + id + ' .btn_next').attr('data-page', page);
          $('#comprow_' + id + ' .btn_prev').attr('data-page', page);

          $('#comprow_' + id + ' .btn_prev').removeClass('prev-disabled');
          if (len <= (page + 1) * perpage) {
            $('#comprow_' + id + ' .btn_next').addClass('next-disabled');
          }

          for (var i = page * perpage; i < (page + 1) * perpage; i++) {
            var blk = $('#comprow_' + id + ' .blk_' + i);
            var loaded = blk.attr('data-flt-loaded');
            if (isNaN(loaded)) {
              var data_code = blk.attr('data-code');
              var data_id = blk.attr('data-id');
              var data_date = blk.attr('data-date');
              var data_fltno = blk.attr('data-fltno');
              var data_depstn = blk.attr('data-depstn');
              fetchFltInfo(data_code, data_date + ' 00:00:00', data_date + ' 23:59:59', [data_fltno], data_id, data_depstn);
            }
          }

          var width = 365;

          $('#comprow_' + id + ' .itmlst').animate({
            left: -(width * page)
          }, {
            duration: 300
          });
        });


        $('#pop_tab2 .btnx').off('click');
        $('#pop_tab2 .btnx').on('click', function (evt) {
          evt.stopPropagation();
          $('#pop_tab2').hide();
        });


        function showPop(elem, id, code) {
          $('#pop_tab1').hide();

          var evt = eventKVList[id];

          var fltinfo = flightInfoList[id];

          var color = legendColorList1[legendNameList.indexOf(evt.eventAttrName)];
          var date = evt.flightDateStr;
          var darr = date.split('-');
          date = darr[1] + '/' + darr[2];

          $('#pop_tab2 .head .fltno').html(evt.companyName + ' ' + evt.flightNo);
          $('#pop_tab2 .head .fltno').css('background-image', 'url(img/logo_' + code + '.png)');
          //

          $('#pop_tab2 .row1 .date').html(date);
          $('#pop_tab2 .row1 .ac').html(evt.acType);


          $('#pop_tab2 .city1 .nm').html(evt.depstnName);
          $('#pop_tab2 .city2 .nm').html(evt.arrstnName);

          if (fltinfo) {
            // 时间
            $('#pop_tab2 .city1 .tm').html(getHMtime(fltinfo.stdChn)); //计划出发
            $('#pop_tab2 .city2 .tm').html(getHMtime(fltinfo.staChn)); //计划到达

            $('#pop_tab2 .city1 .tm2').html(getHMtime(fltinfo.atdChn)); //实际出发
            $('#pop_tab2 .city2 .tm2').html(getHMtime(fltinfo.ataChn)); //实际到达
          }


          var noticeTime = evt.noticeTimeStr;
          var narr = noticeTime.split('-');
          noticeTime = narr[1] + '/' + narr[2];
          noticeTime = noticeTime.substring(0, noticeTime.length - 3);

          $('#pop_tab2 .event_type').html(evt.levelDetail);
          $('#pop_tab2 .time_sb').html(noticeTime);

          $('#pop_tab2 .eventType').html(evt.eventType);
          $('#pop_tab2 .title').html(evt.title);
          $('#pop_tab2 .description').html(evt.description);
          $('#pop_tab2 .deal').html(evt.deal);
          $('#pop_tab2 .precaution').html(evt.precaution);
          $('#pop_tab2 .status').html(statusList[Number(evt.status) - 1]); // 状态跟踪 1:调查分析中、2:落实措施中、3:已 关闭;)
          $('#pop_tab2 .punishInfo').html(evt.isPunish != 'false' ? evt.punishInfo : '无');
          //$('#pop_tab2 .comments').html(evt.comments != 'null' ? evt.comments : '无');



          var param = {
            "id": id
          }
          $.ajax({
            type: 'post',
            url: "/bi/web/findEventDetail",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {
              var comments = response.result.comments;
              var lst = [];
              for (var i = 0; i < comments.length; i++) {
                var cmt = comments[i];
                if (cmt.content != '' && cmt.commentByName != '') {
                  lst.push(cmt);
                }
              }
              var html = '';
              if (lst.length > 0) {
                for (var i = 0; i < lst.length; i++) {
                  var cmt = lst[i];

                  html += "<div class='row'>";
                  html += "<div class='label'>批示意见</div><br>";
                  html += "<span class=''>" + cmt.content + "</span><br>";
                  html += "<span class='label'>批示人及批示时间</span><br>";
                  html += "<span class=''>" + cmt.commentByName + ' ' + cmt.commentTimeStr + "</span>";
                  html += "</div>";

                  /*
                  if(cmt.replys && cmt.replys.length > 0){
                      for(var j=0; j<cmt.replys.length; j++){
                          var rpy = cmt.replys[j];
                          html += "<div class='row'>";
                          html += "<div class='label'>回复意见</div><br>";
                          html += "<span class=''>"+rpy.content+"</span><br>";
                          html += "<span class='label'>回复人及回复时间</span><br>";
                          html += "<span class=''>"+rpy.replyByName+' '+rpy.replyTimeStr+"</span>";
                          html += "</div>";
                      }
                  }else{
                      html += "<div class='row'>";
                      html += "<div class='label'>回复意见</div><br>";
                      html += "<span class=''>无</span><br>";
                      html += "<span class='label'>回复人及回复时间</span><br>";
                      html += "<span class=''>无</span>";
                      html += "</div>";
                  }
                  */
                }



              } else {

                html += "<div class='row'>";
                html += "<div class='label'>批示意见</div><br>";
                html += "<span class=''>无</span><br>";
                html += "<span class='label'>批示人及批示时间</span><br>";
                html += "<span class=''>无</span>";
                html += "</div>";

                /*
                html += "<div class='row'>";
                html += "<div class='label'>回复意见</div><br>";
                html += "<span class=''>无</span><br>";
                html += "<span class='label'>回复人及回复时间</span><br>";
                html += "<span class=''>无</span>";
                html += "</div>";
                */
              }

              $('#pop_tab2 .commentlist').html(html);

              $("#pop_tab2 .scrollpane").getNiceScroll(0).resize();

            },
            error: function () { }
          });


          /*
          var param = {
              "eventId": id,
          }
  
          $.ajax({           
              type: 'post',
              url:"/bi/web/commentEvent",
              contentType: 'application/json',
              dataType: 'json',
              async: true,
              data: JSON.stringify(param),
              success: function(response) {
                  
  
              },
              error:function() {
              }
          });
          */

          setXY();

          function setXY() {
            var bx = elem.offset().left;
            var bt = elem.offset().top;

            var left = bx - $('#pop_tab2').width() - 10;
            var arrtop = bt - ($('.page-wrapper').height() - $('#pop_tab2').height() - 20) + elem.height() / 2;

            $('#pop_tab2').css('left', left);
            $('#pop_tab2').css('bottom', 20);
            $('#pop_tab2 .arr').css('top', arrtop);

            $('#pop_tab2').show();


            // scrollbar
            $("#pop_tab2 .scrollpane").niceScroll({
              cursorcolor: "rgba(4,18,62,0.3)",
              cursorborder: "rgba(0,0,0,0)"
            });
            $("#pop_tab2 .scrollpane").getNiceScroll(0).resize();
            $("#pop_tab2 .scrollpane").getNiceScroll(0).doScrollTop(0, 0);
          }

        }



      } //showSecurityEventList
      function setChartRunTimeCircle() {

        // ---------------------------
        var chart_id = 'chart_l_runTimeEvent';

        var colors = legendColorList1;
        // ---------------------------



        var data_s1 = [];
        var data_legend = [];

        var comp_cause_s1_en = {
          '地面保障': 'ground service support',
          '航安保障': 'air-marshall support',
          '飞行保障': 'pilot support',
          '航务保障': 'dispatcher support',
          '财务保障': 'financial support',
          '货运保障': 'freight support',
          '安全保障': 'security support',
          '市场保障': 'marketing support',
          '乘务保障': 'flight attendant support',
          '航材保障': 'avaiation material support',
          '工程机务': 'maintenance support',
          '飞航保障': 'ground service support',
          '海技保障': 'HNA tech support',
          '性能保障': 'performance support',
        };


        var lst = all_company_data['runTimePropertiesStatic']
        if (lst) {
          var len = lst.length;
          for (var i = 0; i < len; i++) {
            var d = lst[i];
            var dname = '';
            var dname = (lang == 'en' && comp_cause_s1_en[d.name] !== undefined) ? comp_cause_s1_en[d.name] : d.name;

            data_s1.push({
              value: d.count,
              name: dname,
              rate: d.rate,
              code: d.code
            });
          }
        }



        // ---------------------------------------------------------

        // ---------------------------------------------------------
        var perpage = 4;
        var totalpage = Math.ceil(data_s1.length / perpage);
        var page = 0;

        function setTableDat() {
          var total = 0;
          for (var i = 0; i < data_s1.length; i++) {
            var d = data_s1[i];
            total += Number(d.value);
          }

          var html = '';
          for (var i = page * perpage; i < Math.min(data_s1.length, (page + 1) * perpage); i++) {
            var d = data_s1[i];
            var c = colors[i];
            var per = Math.round(Number(d.value) / total * 1000) / 10;
            if (isNaN(per)) {
              per = 0;
            }
            html += "<div class='legend_row' data-name='" + d.name + "'>";
            html += "<span class='color' style='color:" + c + "'>??</span>";
            html += "<span class='name'>" + d.name + "</span>";
            html += "<span class='num white'>" + d.value + "</span>";
            if (lang !== 'en') {
              html += "<span class='fs10'>起</span>";
            }
            html += "<span class='per'>" + per + "%</span>";
            html += "</div>";
          }
          $('#l_runTimeEventChartLegend').html(html);


          $('#l_runTimeEventChartLegend .legend_row').off('click');
          $('#l_runTimeEventChartLegend .legend_row').on('click', function (evt) {
            if (runTimeEventListNameFilter == '') {
              runTimeEventListNameFilter = $(this).attr('data-name');
            } else {
              runTimeEventListNameFilter = ''
            }
            showRunTimeEventList()
          })


        }
        setTableDat();

        $('#l-pre-btn').on('click', function (event) {
          event.preventDefault();
          if (page == 0) {
            return;
          }

          page--;

          $('#l-next-btn').removeClass('next-disabled');
          if (page == 0) {
            $('#l-pre-btn').addClass('prev-disabled');
          }

          setTableDat();

        });

        $('#l-next-btn').on('click', function (event) {
          event.preventDefault();
          if (page == totalpage - 1) {
            return;
          }

          page++;

          $('#l-pre-btn').removeClass('prev-disabled');
          if (page == totalpage - 1) {
            $('#l-next-btn').addClass('next-disabled');
          }

          setTableDat();

        });


        // ---------------------------------------------------------
        // 主KPI图表
        var chart = echarts.init(document.getElementById(chart_id));

        var option = {

          tooltip: {
            trigger: 'item',
          },
          legend: {
            show: false,
            orient: 'vertical',
            y: 19,
            left: 'left',
            itemWidth: 8,
            itemHeight: 8,
            textStyle: {
              color: '#99ccff',
            },
            data: data_legend
          },
          color: colors,
          series: [{
            name: '',
            type: 'pie',
            radius: ['45%', '80%'],
            center: ['50%', '50%'],
            data: data_s1,
            //roseType: 'angle',
            clockwise: false,
            label: {
              normal: {
                show: false,
                position: 'inside',
                formatter: '{d}%',
                textStyle: {
                  fontSize: 10 + fontSizeDiff()
                }
              }
            },
            labelLine: {
              normal: {
                show: false,
                smooth: 0.2,
                length: 0,
                length2: 0
              }
            },


            animationType: 'scale',
            animationEasing: 'elasticOut',
            animationDelay: function (idx) {
              return Math.random() * 200;
            }
          }]
        };
        console.log(option)

        chart.setOption(option);

      }//end setChartRunTimeCircle

      var runTimeEventListNameFilter = ''; //
      var eventListReasonFilter = ''; //['意外', '人为', '机械', '其他'];
      var eventListLevelFilter = ''; //['安全报告事件', '一类事件', '事故征候', '待定'];
      var isPunishFilter = 0; //0, 1

      getAllAbnormalKpiData1 = getAllCompanyKpiData1;
      getAllCompanyKpiData1();
      //----------------------------------------- 不正常航班tab，安全监控 end


    } // end of loadAll

    var t;
    var t2;

    function setUnnormalWeatherBase() {
      if (unnormalBase.length == 0) {
        $('#base_list_unnormal_weather').html('--');
        return;
      }


      var weatherAll = [];
      var weatherName = '';
      var weatherName2 = '';
      var provinceName = '';
      var proAddWeath = '';
      var provinceName2 = '';
      for (var i = 0; i < unnormalBase.length; i++) {
        provinceName = unnormalBase[i].ccc;
        provinceName2 = airMap.get(provinceName);
        weatherName = unnormalBase[i].wx;

        let wx = weatherName;
        let cont = '';
        wx.split(" ").forEach((v, i) => {
          cont += wxCode2Name[v] ? ' ' + wxCode2Name[v] : v;
        });

        weatherName2 = cont;
        if (weatherName2 == null || weatherName2 == undefined) {
          weatherName2 = weatherName;
        }
        proAddWeath = provinceName2 + '  ' + weatherName2 + ';  ';
        weatherAll.push(proAddWeath);
      }

      $('#base_list_unnormal_weather').html(weatherAll);

      var i = 0;
      var sizess = $(".uls li").length;
      var sizesspx = sizess * 330;
      var clone = $(".uls").html();
      /* $(".uls2").html(clone); */
      clearInterval(t);
      t = setInterval(moveL, 80);

      //封装的动画函数
      function moveL() {
        i++;
        var sizess = $(".uls li").length;
        if (i > sizesspx) {
          $(".box").css({ left: 0 });
          i = 0
        }
        $(".box").css({ left: -i + 'px' });
      }

    }
    // ------------------------------------------------------------------------
    // 时钟
    // ------------------------------------------------------------------------
    function setTime() {
      var date = new Date();
      var timestamp = date.getTime();
      var timezoneOffset = date.getTimezoneOffset();
      var utc_timestamp = timestamp + timezoneOffset * 60 * 1000;
      var utc_date = new Date();
      utc_date.setTime(utc_timestamp);

      var sydney_date = new Date();
      sydney_date.setTime(utc_timestamp + 11 * 3600 * 1000);

      var newyork_date = new Date();
      newyork_date.setTime(utc_timestamp - 5 * 3600 * 1000);

      $('#time_beijing').text(formatNum(date.getHours()) + ':' + formatNum(date.getMinutes()));
      $('#time_london').text(formatNum(utc_date.getHours()) + ':' + formatNum(utc_date.getMinutes()));
      $('#time_sydney').text(formatNum(sydney_date.getHours()) + ':' + formatNum(sydney_date.getMinutes()));
      $('#time_newyork').text(formatNum(newyork_date.getHours()) + ':' + formatNum(newyork_date.getMinutes()));

      $('#date_beijing').text(date.getDate() + ' ' + getEngMonth(date.getMonth()));
      $('#date_london').text(utc_date.getDate() + ' ' + getEngMonth(utc_date.getMonth()));
      $('#date_sydney').text(sydney_date.getDate() + ' ' + getEngMonth(sydney_date.getMonth()));
      $('#date_newyork').text(newyork_date.getDate() + ' ' + getEngMonth(newyork_date.getMonth()));

    }

    function formatNum(n) {
      if (n < 10) {
        return ('0' + n);
      } else {
        return n;
      }
    }

    function getEngMonth(month) {
      var mlist = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Spt", "Oct", "Nov", "Dec"];
      return mlist[month].toUpperCase();
    }

    setInterval(setTime, 1000);
    setTime();



    loadAll();
    setInterval(loadAll, 5 * 60 * 1000);



    // 点亮航班保障节点
    function lightThemUp(node) {
      for (var i = fltnodes.indexOf(node); i >= 0; i--) {
        var nd = fltnodes[i];
        $('#fltnode_' + nd).addClass('green');
      }
    }



    // 获取机场列表
    var arp_detail_list;

    function getAirportList() {

      var param = {
        //"AIRPORT_CODE": arpcodes, // 可选，传入机场CODE
      }
      $.ajax({
        type: 'post',
        url: "/bi/web/airportdetail",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          arp_detail_list = {};
          let list = response.airport.reverse();
          list.forEach((v, i) => {
            arp_detail_list[v.code] = v;
            if (v.is_weather == "1") {
              arpcodes.push(v.icao_id);
              airMap.set(v.icao_id, v.city_name);
            }
          });
          arpcodes = [...new Set(arpcodes)];

          // 开始结束时间
          var date = new Date();
          var mm = date.getMonth() + 1;
          var dd = date.getDate();
          if (mm < 10) {
            mm = '0' + mm;
          }
          if (dd < 10) {
            dd = '0' + dd;
          }
          var stdStart = date.getFullYear() + '-' + mm + '-' + dd + ' 00:00:00';
          var stdEnd = date.getFullYear() + '-' + mm + '-' + dd + ' 23:59:59';
          var param = {
            "cccsList": arpcodes.join(','),
            "updateDateStart": stdStart,
            "updateDateEnd": stdEnd,
          }
          $.ajax({
            type: 'post',
            url: "/bi/web/findWfMetrepBiaoZhuns",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {

              for (var k in response) {
                var obj = response[k];
                if (obj.ivisAlarmValue == 2 || obj.irvrAlarmValue == 2
                  || obj.wxCode == "2" || obj.iyunalarmValue == 2 || obj.ittAlarmValue == 2
                  || obj.iwindSwitchAlarmValue == 2) { //1 表示黄色; 2 表示红色
                  unnormalBase.push(obj);
                }
              }
              clearTimeout(window.itv_unnormalBase);
              setUnnormalWeatherBase();
            },
            error: function () { }
          });
        },
        error: function () { }
      });
    }
    getAirportList();



    function findFltInfo(fltno) {
      for (var i = all_flight_list.length - 1; i >= 0; i--) {
        var flt = all_flight_list[i];
        if (flt.flightNo == fltno) {
          return flt;
        }
      }
      return undefined;
    }


    var chart_earch;

    function crate3DEarth() {
      chart_earch = echarts.init(document.getElementById('earth3d'));

      var option = {
        tooltip: {
          show: false
        },
        backgroundColor: 'rgba(0,0,0,0)',
        globe: {
          baseTexture: 'asset/earth.jpg',
          //heightTexture: '/asset/get/s/data-1491889019097-rJQYikcpl.jpg',

          displacementScale: 0.1,

          shading: 'lambert',

          //environment: 'rgba(0,0,0,0)',
          shading: 'realistic',
          light: {
            main: {
              intensity: 0.3
            },
            ambient: {
              intensity: 1.0
            },
          },

          viewControl: {
            autoRotate: false,
            zoomSensitivity: false,
            targetCoord: [105.5, 32]
          },

          layers: []
        },
        series: []
      }

      chart_earch.setOption(option);
    }



    var currentMapType = 'china';
    var planeLocationList = []; //空中
    var planeLocationList2 = []; //地面

    $('#map_switch_btn').on('click', function () {
      if (currentMapType == 'china') {
        currentMapType = 'world';
        $('#map_switch_lb').text(langPack['lb_domestic'][lang]);
        $('#title_earth').text(langPack['tit_mid2'][lang]);
        $('#china_map').fadeOut();
        $('#base_cards').fadeOut();
        $('.planeLocationLegend').fadeIn();
        $('.earthmask').fadeOut();
        $('.earthlight').fadeIn();

        $('#earth3d').css('pointer-events', 'auto');

        $('.searchform').fadeIn();

        var option = {
          globe: {
            viewControl: {
              targetCoord: [76, 32]
            }
          }
        }
        chart_earch.setOption(option);

        setPlaneLocation();


      } else {
        currentMapType = 'china';
        $('#map_switch_lb').text(langPack['lb_ac_loc'][lang]);
        $('#title_earth').text(langPack['tit_mid'][lang]);
        $('#china_map').fadeIn();
        $('#base_cards').fadeIn();
        $('.planeLocationLegend').fadeOut();
        $('.earthmask').fadeIn();
        $('.earthlight').fadeOut();
        $('#earth3d').css('pointer-events', 'none');

        $('.searchform').fadeOut();

        crate3DEarth();

      }
    })


    $('.ico_search').on('click', function () {
      var fltno = $('#ipt_fltno').val();
      if (all_flight_list && planeLocationList) {
        var flt = findFltInfo(fltno);

        var len = planeLocationList.length;
        var dat;
        var found = false;
        for (var i = 0; i < len; i++) {
          var dd = planeLocationList[i];
          if (fltno == dd.fltno) {
            dat = dd;
            found = true;
          }
        }

        if (flt && found) {
          $('.searchform .error').hide();
          showFlightDetails(fltno);
        } else {
          $('.searchform .error').show();
        }

      }


    })


    function showFlightDetails(flightNo) {
      var url = 'flight.html?fltno=' + flightNo;
      var url_scale = getQueryString('scale');
      if (url_scale) {
        url = url + '&scale=' + url_scale;
      }
      windowOpen(url, '_blank')
    }


    function windowOpen(url, target) {
      var a = document.createElement("a");
      a.setAttribute("href", url);
      if (target == null) {
        target = '';
      }
      a.setAttribute("target", target);
      document.body.appendChild(a);
      if (a.click) {
        a.click();
      } else {
        try {
          var evt = document.createEvent('Event');
          a.initEvent('click', true, true);
          a.dispatchEvent(evt);
        } catch (e) {
          window.open(url);
        }
      }
      document.body.removeChild(a);
    }



    function getPlaneLocation() {
      var param = {
        'mode': 'pos'
      }
      $.ajax({
        type: 'post',
        url: "/bi/web/flightMq",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {

          //var list = response.data.data1;

          planeLocationList = [];
          var plist = {};

          //var list = response.data.data1;
          function processData(data1) {
            var lst = {};
            var len = data1.length;
            for (var i = 0; i < len; i++) {
              var dd = data1[i];
              var fi = dd.fi;
              if (lst[fi] == undefined) {
                lst[fi] = {
                  data: []
                };
                lst[fi]['data'].push(dd);
              } else {
                lst[fi]['data'].push(dd);
              }
            }

            return lst;
          }

          var list = processData(response.data.data1);

          for (var fltno in list) {

            var fltobj = list[fltno];
            var itmx2 = fltobj.data;

            var itm;

            if (itmx2 && itmx2.length > 1) {
              var itm1 = itmx2[0];
              var itm2 = itmx2[1];


              itm1.UTC = itm1.UTC.replace(' ', '');
              itm2.UTC = itm2.UTC.replace(' ', '');

              if (itm1.UTC > itm2.UTC) {
                itm = itm1
                itm.LON1 = itm2.LON;
                itm.LAT1 = itm2.LAT;
              } else if (itm1.UTC < itm2.UTC) {
                itm = itm2
                itm.LON1 = itm1.LON;
                itm.LAT1 = itm1.LAT;
              } else {

                itm = itm2
                itm.LON1 = itm1.LON;
                itm.LAT1 = itm1.LAT;

                console.log(fltno, '两组经纬度UTC相同');
              }
            } else if (itmx2 && itmx2.length > 0) {
              itm = itmx2[0];

            }


            if (itm) {

              var alt = itm.ALT;
              var cas = itm.CAS;
              var vec;

              var fltno = itm.fi;

              if (fltno.indexOf(comp_code) == 0) {

                var acno = itm.an;
                acno = acno.replace('-', '');

                var lon = formatLonLat(itm.LON);
                var lon1 = formatLonLat(itm.LON1);
                var lat = formatLonLat(itm.LAT);
                var lat1 = formatLonLat(itm.LAT1);

                if (isNaN(itm.LON)) {
                  vec = Number(itm.VEC);
                }

                var oil = isNaN(itm.OIL) ? '' : itm.OIL;

                var pdat = {
                  fltno: fltno,
                  acno: acno,
                  alt: alt,
                  vec: vec,
                  lon: lon,
                  lat: lat,
                  lon1: lon1,
                  lat1: lat1,
                  oil: oil,
                };

                var code = acno + '-' + fltno;

                /*
                if(plist[code] == undefined){
                    plist[code] = pdat;
                }else if(plist[code].lon1 == undefined){
                    plist[code].lon1 = pdat.lon;
                    plist[code].lat1 = pdat.lat;
                    if(oil > 0){
                        plist[code].oil = oil;
                    }
                }else if(oil > 0){
                    plist[code].oil = oil;
                }
                */

                if (pdat.vec == undefined) {
                  pdat.vec = getGeoAngle(pdat.lat, pdat.lon, pdat.lat1, pdat.lon1);
                }
                planeLocationList.push(pdat);
              }
            }
          }

          /*
          for(var code in plist){
              var pdat = plist[code];
              //if(pdat.vec || pdat.lon1 != undefined){
                  if(pdat.vec == undefined){
                      pdat.vec = getGeoAngle(pdat.lat, pdat.lon, pdat.lat1, pdat.lon1);
                  }
                  planeLocationList.push(pdat);
              //}
          }
          */



        },
        error: function (jqXHR, txtStatus, errorThrown) {

        }
      });
    }

    function setPlaneLocation() {

      if (all_flight_list == undefined || planeLocationList == undefined) {
        setTimeout(setPlaneLocation, 10);
        return;
      }

      var seriesData = [];


      for (var i = planeLocationList.length - 1; i >= 0; i--) {
        var itm = planeLocationList[i];
        var acno = itm.acno;
        var fltno = itm.fltno;

        var vec = itm.vec;
        var alt = itm.alt;

        var lon = itm.lon;
        var lat = itm.lat;

        var flt = findFltInfo(fltno);

        /*
      黄色：延误 DEL
      紫色：机务工作 ARR NDR ATA CNL
      绿色：飞行中 DEP RTR
      蓝色：未执行 SCH ATD
    
      'ARR':'落地',
      'NDR':'落地',
      'ATD':'推出',
      'ATA':'到达',
      'CNL':'取消',
      'DEL':'延误',
      'DEP':'起飞',
      'RTR':'返航',
      'SCH':'计划'
    
      */
        // 在飞的，空中的飞机
        if (flt && (alt > 0 || flt.status == 'DEP')) {
          var img = '';
          var delay_min = Number(flt.dur1) + Number(flt.dur2) + Number(flt.dur3) + Number(flt.dur4); //延误时间 分钟
          var color;
          var border;
          var svg = 'M90.7,4.8c-1.3-1.5-2.8-2.7-4.5-3.5C84.3,0.4,82.4,0,80.3,0l-0.4,0.1L79.5,0c-2,0-4,0.4-5.8,1.4c-1.8,0.8-3.3,2-4.6,3.5c-2.2,2.3-3.2,4.9-3.2,8l0,47.5L0.1,111.7L0,127.4l65.3-21.2l-0.1,38L39,167.2l0,7.9l3-0.8v1.6l37.9-10.9l37.9,10.9v-1.6l2.9,0.8l-0.1-7.9l-26.1-22.9l-0.1-38l65.4,21.2l-0.1-15.8L94,60.3l-0.1-47.5C93.9,9.8,92.9,7.1,90.7,4.8z';
          if (delay_min == 0) {
            color = "#00ff6c";
            border = "#005d09";
          } else {
            color = "#fff60e";
            border = "#2f2a0b";
          }
          seriesData.push({
            name: fltno,
            acno: acno,
            value: [lon, lat, 0],
            //symbolRotate: -vec, //航向   正北是0°，顺时针，0到360°
            //symbol:'path://'+svg,
            itemStyle: {
              color: color,
              borderColor: border,
              borderWidth: 1,
            }
          })
        }

      }


      var series = [];
      series.push({
        type: 'scatter3D',
        coordinateSystem: 'globe',
        symbolSize: 6,
        //blendMode: 'lighter',
        slient: true,
        label: {
          show: false,
        },
        data: seriesData
      });

      var option = {
        series: series
      }
      chart_earch.setOption(option);
    }

    crate3DEarth();
    getPlaneLocation();
    setInterval(getPlaneLocation, 5 * 60 * 1000);


    //格式化时间 hh:mm
    function getHMtime(fulltime) {
      var arr = fulltime.split(' ');
      var arr2 = arr[1].split(':');
      var hourmin = arr2[0] + ':' + arr2[1];
      return hourmin;
    }

    //------------------------------------------------------------------------
    //获取 座舱布局
    //------------------------------------------------------------------------
    var acCabinList = {};

    function getCabin(acno, elem) {
      if (acCabinList[acno] == undefined) {
        var param = {
          "acNo": acno,
        }
        $.ajax({
          type: 'post',
          url: "/bi/web/getAcAircraftList",
          contentType: 'application/json',
          dataType: 'json',
          async: true,
          data: JSON.stringify(param),
          success: function (response) {
            var ac = response.data[0].data;
            var cabin = ac.cabin; // 座舱布局
            $(elem).text(cabin);
            acCabinList[acno] = cabin
          },
          error: function () { }
        });

      } else {
        var cabin = acCabinList[acno];
        $(elem).text(cabin);
      }

    }

    function initLang() {

      $('.lang').each(function (index, el) {
        var key = $(this).attr('lang');
        var dd = langPack[key];
        if (dd) {
          if (dd.uppercase) {
            $(this).html(dd[lang].toLowerCase());
          } else {
            $(this).html(dd[lang]);
          }

        } else {
          $(this).html(key);
        }
      });
    }

    initLang();

    var websocket = null;
    initSocket();

    function initSocket() {
      let host = window.location.host.split(":");
      let protocol = window.location.protocol;
      let ws = protocol === 'https:' ? 'wss' : 'ws';

      function initUserinfo() {
        if ('WebSocket' in window) {
          if (userinfo) {
            if (host[0] === "vis.hnair.net") {
              websocket = new WebSocket(`${ws}://${host[0]}:8280/websocket/7x2-index-${userinfo.id}`);
            } else {
              websocket = new WebSocket(`${ws}://${host[0]}:8888/websocket/7x2-index-${userinfo.id}`);
            }
            //连接发生错误的回调方法
            websocket.onerror = function () {
              console.log("WebSocket连接发生错误");
            };

            //连接成功建立的回调方法
            websocket.onopen = function () {
              console.log("WebSocket连接成功");
            }

            //接收到消息的回调方法
            websocket.onmessage = function (event) {
              console.log(event.data);
              eval(event.data);
            }

            //连接关闭的回调方法
            websocket.onclose = function () {
              console.log("WebSocket连接关闭");
            }

            //监听窗口关闭事件，当窗口关闭时，主动去关闭websocket连接，防止连接还没断开就关闭窗口，server端会抛异常。
            window.onbeforeunload = function () {
              websocket.close();
            }
          } else {
            setTimeout(initUserinfo, 10);
          }
        } else {
          alert('当前浏览器 Not support websocket');
        }
      }

      initUserinfo();
    }
  });

});


