var APP_ID = "hnahk-dvp2";

var userinfo;

function isAutoLogin() {
  return "true" === getQueryString("autoLogin");
}

function SSO_URL() {
  if (
    window.location.href.indexOf("vis.hnair.net") > -1 ||
    window.location.href.indexOf("bim.hnair.net") > -1 ||
    window.location.href.indexOf("cdp-mobile.hnair.net") > -1
  ) {
    return "https://sso.hnair.net";
  } else {
    return "https://ssotest.hnair.net/opcnet-sso";
  }
}
function SSO_SERVICE() {
  if (
    window.location.href.indexOf("vis.hnair.net") > -1 ||
    window.location.href.indexOf("bim.hnair.net") > -1 ||
    window.location.href.indexOf("cdp-mobile.hnair.net") > -1
  ) {
    if (window.location.href.indexOf("largescreen/7x2") > -1) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen7x2";
    } else if (window.location.href.indexOf("largescreen/west") > -1) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen_west";
    } else if (window.location.href.indexOf("largescreen/jdair") > -1) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen_jd";
    } else if (window.location.href.indexOf("largescreen/hnahk") > -1) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen_hnahk";
    } else if (window.location.href.indexOf("largescreen/general") > -1) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen_general";
    } else if (window.location.href.indexOf("largescreen/sdc") > -1) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen_sdc";
    } else if (window.location.href.indexOf("largescreen") > -1) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen";
    } else {
      return "https://vis.hnair.net/bi/largescreen/redirect_admin";
    }
  }
  if (window.location.href.indexOf("cdp-test.hnair.net") > -1) {
    if (window.location.href.indexOf("largescreen/7x2") > -1) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen7x2";
    } else if (window.location.href.indexOf("largescreen/west") > -1) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen_west";
    } else if (window.location.href.indexOf("largescreen/jdair") > -1) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen_jd";
    } else if (window.location.href.indexOf("largescreen/hnahk") > -1) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen_hnahk";
    } else if (window.location.href.indexOf("largescreen/general") > -1) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen_general";
    } else if (window.location.href.indexOf("largescreen/sdc") > -1) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen_sdc";
    } else if (window.location.href.indexOf("largescreen") > -1) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen";
    } else {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_admin";
    }
  } else if (window.location.href.indexOf("vis-dev.hnair.net") > -1) {
    if (window.location.href.indexOf("largescreen/7x2") > -1) {
      return "https://vis-dev.hnair.net/bi/largescreen/redirect_largescreen7x2";
    } else if (window.location.href.indexOf("largescreen/west") > -1) {
      return "https://vis-dev.hnair.net/bi/largescreen/redirect_largescreen_west";
    } else if (window.location.href.indexOf("largescreen/jdair") > -1) {
      return "https://vis-dev.hnair.net/bi/largescreen/redirect_largescreen_jd";
    } else if (window.location.href.indexOf("largescreen/hnahk") > -1) {
      return "https://vis-dev.hnair.net/bi/largescreen/redirect_largescreen_hnahk";
    } else if (window.location.href.indexOf("largescreen/general") > -1) {
      return "https://vis-dev.hnair.net/bi/largescreen/redirect_largescreen_general";
    } else if (window.location.href.indexOf("largescreen/sdc") > -1) {
      return "https://vis-dev.hnair.net/bi/largescreen/redirect_largescreen_sdc";
    } else if (window.location.href.indexOf("largescreen") > -1) {
      return "https://vis-dev.hnair.net/bi/largescreen/redirect_largescreen";
    } else {
      return "https://vis-dev.hnair.net/bi/largescreen/redirect_admin";
    }
  } else if (window.location.href.indexOf("vis-test.hnair.net") > -1) {
    if (window.location.href.indexOf("largescreen/7x2") > -1) {
      return "https://vis-test.hnair.net/bi/largescreen/redirect_largescreen7x2";
    } else if (window.location.href.indexOf("largescreen/west") > -1) {
      return "https://vis-test.hnair.net/bi/largescreen/redirect_largescreen_west";
    } else if (window.location.href.indexOf("largescreen/jdair") > -1) {
      return "https://vis-test.hnair.net/bi/largescreen/redirect_largescreen_jd";
    } else if (window.location.href.indexOf("largescreen/hnahk") > -1) {
      return "https://vis-test.hnair.net/bi/largescreen/redirect_largescreen_hnahk";
    } else if (window.location.href.indexOf("largescreen/general") > -1) {
      return "https://vis-test.hnair.net/bi/largescreen/redirect_largescreen_general";
    } else if (window.location.href.indexOf("largescreen/sdc") > -1) {
      return "https://vis-test.hnair.net/bi/largescreen/redirect_largescreen_sdc";
    } else if (window.location.href.indexOf("largescreen") > -1) {
      return "https://vis-test.hnair.net/bi/largescreen/redirect_largescreen";
    } else {
      return "https://vis-test.hnair.net/bi/largescreen/redirect_admin";
    }
  } else if (window.location.href.indexOf("localhost") > -1) {
    if (window.location.href.indexOf("largescreen/7x2") > -1) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen7x2";
    } else if (window.location.href.indexOf("largescreen/west") > -1) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen_west";
    } else if (window.location.href.indexOf("largescreen/jdair") > -1) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen_jd";
    } else if (window.location.href.indexOf("largescreen/hnahk") > -1) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen_hnahk";
    } else if (window.location.href.indexOf("largescreen/general") > -1) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen_general";
    } else if (window.location.href.indexOf("largescreen/sdc") > -1) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen_sdc";
    } else if (window.location.href.indexOf("largescreen") > -1) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen";
    } else {
      return "http://localhost:8080/bi/largescreen/redirect_admin";
    }
  } else {
    if (window.location.href.indexOf("largescreen/7x2") > -1) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen7x2";
    } else if (window.location.href.indexOf("largescreen/west") > -1) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen_west";
    } else if (window.location.href.indexOf("largescreen/jdair") > -1) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen_jd";
    } else if (window.location.href.indexOf("largescreen/hnahk") > -1) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen_hnahk";
    } else if (window.location.href.indexOf("largescreen/general") > -1) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen_general";
    } else if (window.location.href.indexOf("largescreen/sdc") > -1) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen_sdc";
    } else if (window.location.href.indexOf("largescreen") > -1) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen";
    } else {
      return "http://localhost:8080/bi/largescreen/redirect_admin";
    }
  }
}

function getHost() {
  if (
    window.location.href.indexOf("vis.hnair.net") > -1 ||
    window.location.href.indexOf("bim.hnair.net") > -1 ||
    window.location.href.indexOf("cdp-mobile.hnair.net") > -1
  ) {
    return "https://vis.hnair.net";
  }
  if (window.location.href.indexOf("cdp-test.hnair.net") > -1) {
    return "https://cdp-test.hnair.net";
  } else if (window.location.href.indexOf("vis-test.hnair.net") > -1) {
    return "https://vis-test.hnair.net";
  } else if (window.location.href.indexOf("vis-dev.hnair.net") > -1) {
    return "https://vis-dev.hnair.net";
  } else {
    return "https://localhost:8080";
  }
}

// 检测是不是登录了
var needlogin = false;
function checkLogin(response) {
  if (response.errorcode == "9999" && !needlogin) {
    needlogin = true;
    alert("请重新登录");
    redirectToSSO();
  }
}

function redirectToSSO() {
  if (isAutoLogin()) {
    localStorage.setItem("session_timeout", 1);
    var url =
      getHost() +
      "/bi/sso/autoLogin/" +
      getQueryString("autoLoginPath") +
      "?ticket=lgauto" +
      new Date().getTime();
    window.location.href = url;
  } else {
    localStorage.setItem("session_timeout", 1);
    window.location.href =
      SSO_URL() + "/login?appid=" + APP_ID + "&service=" + SSO_SERVICE();
  }
}

function logout() {
  // 退出登录
  var param = {};

  $.ajax({
    type: "post",
    url: "/bi/web/logout",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      sso_logout();
    },
    error: function () {},
  });
}

function sso_logout() {
  localStorage.setItem("session_timeout", 1);
  window.location.href = SSO_URL() + "/logout?appid=" + APP_ID; //+ "&service=" + SSO_SERVICE();
}

function getQueryString(name) {
  var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
  var r = window.location.search.substr(1).match(reg);
  if (r != null) return unescape(r[2]);
  return "";
}

var usersCompanyList = [];

var usersMenuList;

var baseInfoInitDtd = $.Deferred();

function processUserInfo(userinfo) {
  console.log("userinfo", userinfo);

  if (localStorage.getItem("session_timeout") == 1) {
    localStorage.setItem("session_timeout", 0);

    // 登录日志
    var date = new Date();
    var mm = date.getMonth() + 1;
    var dd = date.getDate();
    var h = date.getHours();
    var m = date.getMinutes();
    var s = date.getSeconds();

    if (mm < 10) {
      mm = "0" + mm;
    }
    if (dd < 10) {
      dd = "0" + dd;
    }
    if (h < 10) {
      h = "0" + h;
    }
    if (m < 10) {
      m = "0" + m;
    }
    if (s < 10) {
      s = "0" + s;
    }

    var timeNow =
      date.getFullYear() +
      "年" +
      mm +
      "月" +
      dd +
      "日 " +
      h +
      ":" +
      m +
      ":" +
      s;

    var operation = "登陆, 时间:" + timeNow;
    var category = "登陆";

    var param = {
      userid: userinfo.id,
      oper: operation,
      category: category,
    };

    $.ajax({
      type: "post",
      url: "/bi/web/userlog",
      contentType: "application/json",
      dataType: "json",
      async: true,
      data: JSON.stringify(param),
      success: function (response) {},
      error: function () {},
    });
  }

  // 有权限的公司
  usersCompanyList = [];
  var comlist = userinfo.COMPANY.companys;
  if (comlist) {
    var len = comlist.length;
    for (var i = 0; i < len; i++) {
      var obj = comlist[i];
      var comid = obj["company"];
      usersCompanyList.push(comid);
    }
  }

  if (usersCompanyList.length == 0) {
    alert("您没有访问权限，请联系管理员。");
    return;
  }

  //有权限的菜单
  usersMenuList = userinfo.MENU.menus;

  if (usersMenuList.length == 0) {
    alert("您没有访问权限，请联系管理员。");
  }

  createNavMenu();
  getCompany();

  $("body").show();
  setPageScale();
  baseInfoInitDtd.resolve();
  try {
    if (typeof eval("onSizeChange") == "function") {
      onSizeChange();
    }
  } catch (e) {}
}

function getUserInfo() {
  $.ajax({
    type: "post",
    url: "/bi/sso/loginforBI2",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify({}),
    success: function (response) {
      userinfo = response;
      processUserInfo(userinfo);
    },
    error: function (error) {
      console.log(error, "redirect to sso");
      redirectToSSO();
    },
  });
}

function checkBrowser() {
  var support = true;
  var isIE = false;
  var isIE11 = false;
  var isChrome = false;
  var isFirefox = false;
  var isSafari = false;
  var userAgent = navigator.userAgent;
  if (userAgent.indexOf("Opera") > -1 || userAgent.indexOf("OPR") > -1) {
    // 'Opera';
  } else if (
    userAgent.indexOf("compatible") > -1 &&
    userAgent.indexOf("MSIE") > -1
  ) {
    // 'IE';
    isIE = true;
  } else if (userAgent.indexOf("Edge") > -1) {
    // 'Edge';
  } else if (userAgent.indexOf("Firefox") > -1) {
    // 'Firefox';
    isFirefox = true;
  } else if (
    userAgent.indexOf("Safari") > -1 &&
    userAgent.indexOf("Chrome") == -1
  ) {
    // 'Safari';
  } else if (
    userAgent.indexOf("Mozilla") > -1 ||
    userAgent.indexOf("Safari") > -1
  ) {
    // 'Safari';
    isSafari = true;
  } else if (
    userAgent.indexOf("Chrome") > -1 &&
    userAgent.indexOf("Safari") > -1
  ) {
    // 'Chrome';
    isChrome = true;
  } else if (!!window.ActiveXObject || "ActiveXObject" in window) {
    // 'IE>=11';
    isIE11 = true;
  } else {
    // 'Unkonwn';
    support = false;
  }

  console.log(userAgent);

  if (isIE) {
    var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
    reIE.test(userAgent);
    var fIEVersion = parseFloat(RegExp["$1"]);
    if (fIEVersion == 7) {
      support = false;
    } else if (fIEVersion == 8) {
      support = false;
    } else if (fIEVersion == 9) {
      support = false;
    } else if (fIEVersion == 10) {
      support = false;
    } else if (fIEVersion == 11) {
      // "IE11";
      isIE11 = true;
    } else {
      support = false;
    }
  }

  // 检测遨游浏览器
  try {
    if (
      window.external.max_version &&
      window.external.max_version != undefined
    ) {
      support = false;
    }
  } catch (ex) {}

  if (support) {
    getUserInfo();
  } else {
    //window.location.href = "/largescreen/browser.html";
    alert(
      "您的浏览器版本可能存在兼容问题，建议使用 Google Chrome 或 IE11 浏览器。"
    );
    if (typeof jQuery != "undefined") {
      $("body").hide();
    }
  }
}
checkBrowser();

if (typeof jQuery != "undefined") {
  $("body").hide();
}

document.oncontextmenu = function () {
  return false;
};

function removeDangerChar(dat) {
  var res = dat;
  if (typeof dat == "string") {
    res = dat.replace(/[\>\<\']/g, "");
  } else if (typeof dat == "object") {
    for (var k in dat) {
      var v = dat[k];
      dat[k] = removeDangerChar(v);
    }
    res = dat;
  }

  return res;
}

var companylist = [];
var companylistAuth = [];
var usersCompayDataList;
var usersCompayCodeList;

var companyCode2Name = {};
var companyName2Code = {};
var companyCode2Nameabbr = {};
var companyYshcode2Code = {};
var companyCode2YshCode = {};
var companyCode2Sort = {};
var parent_company = "HNAHK";
var parent_company_id = 100;
var selected_company_code = parent_company;
var companyColors = [
  "#1fb3ff",
  "#7d5aee",
  "#f06234",
  "#008e01",
  "#ffaf3d",
  "#54c6ac",
  "#f06eaa",
  "#5379c0",
  "#a7c835",
  "#1fb3aa",
  "#7d5acc",
  "#f06287",
  "#008e91",
  "#ffaf5c",
  "#54c6ef",
  "#f06edd",
  "#5379e6",
  "#a7c876",
];
var companyCode2Color = {};
var kpiDataReady = false;

// 公司轮播
var auto_switch = localStorage.getItem("auto_switch") == 1;
var autoSwitch = auto_switch;

// 播放语音
var audio_muted = localStorage.getItem("audio_muted") == 1;
var audioPaused = audio_muted;

var audioEnded = true;
var audioQueue = [];
var audioEndCallbackQueue = [];

var companyCode2NodeId = {
  HNAHK: 1,
  HU: 9,
  JD: 4477,
  "8L": 1990,
  HX: 8028,
  PN: 5732,
  GS: 4248,
  FU: 102025,
  UQ: 102024,
  Y8: 13,
  GX: 801775,
  "9H": 11,
  GT: 801547,
  UO: 101352,
  AW: 19612, //加纳
  B5: 5157, //金鹿
  CN: 6381, //大新华
};

var comp_cause = [
  "飞机故障",
  "运力调配",
  "工程机务",
  "航班计划",
  "航材保障",
  "航务保障",
  "机组保障",
  "飞行机组保障",
  "乘务组",
  "乘务组保障",
  "空警安全员",
  "地面保障",
  "货运保障",
  "公司原因",
  "其他航空公司原因",
];
var none_cause = [
  "公共安全",
  "机场",
  "军事活动",
  "空管",
  "离港系统",
  "联检",
  "旅客",
  "民航局航班时刻安排",
  "天气原因",
  "油料",
];

// 不同页面需要排除的公司列表
var excludeComps = {
  "a0-1.html": ["Y8P", "Y8C"], // 需要和 a4-2 保持一致，两者数据同步
  "a0-13.html": ["Y8P", "Y8C"], // 需要和 a4-2 保持一致，两者数据同步
  "a1-1.html": ["CN", "Y8P", "Y8C"],
  "a1-2.html": ["CN", "Y8P", "Y8C"],
  "a2-1.html": ["CN", "Y8P", "Y8C"],
  "a2-3.html": ["CN", "Y8P", "Y8C"],
  "a2-4.html": ["CN", "Y8P", "Y8C"],
  "a2-5.html": ["CN", "Y8P", "Y8C"],
  "a3-1.html": ["Y8P", "Y8C"],
  "a3-1_exhibition.html": ["Y8P", "Y8C"],
  "a3-2.html": ["Y8P", "Y8C"],
  "a3-3.html": ["Y8P", "Y8C"], //'HX'
  "a3-6.html": ["CN", "HX", "Y8P", "Y8C"],
  "a4-1.html": ["CN", "Y8P", "Y8C"],
  "a4-2.html": ["Y8P", "Y8C"],
};

// 右边公司下拉列表容器，控制按钮，个人中心
var html = "";
html += '<div id="companycombo" code="">';
html += '<div class="box"></div>';
html += '<div id="companylist">';
html += "</div>";
html += "</div>";
html += '<div id="ctrl_autoswitch"></div>';
html += '<div id="ctrl_mute"></div>';
html += '<div id="ctrl_user"></div>';
$("#topright").html(html);
//

var navMenu = [];

function getUrl(pageUrl) {
  var url = pageUrl;
  if (url.indexOf("?") > -1) {
    url = url + "&ts=" + new Date().getTime();
  } else {
    url = url + "?ts=" + new Date().getTime();
  }
  return url;
}

function createNavMenu() {
  if (usersMenuList.length == 0) {
    $("body").hide();
    return;
  }

  // 创建菜单数组
  usersMenuList.sort(function (a, b) {
    return a.sort - b.sort;
  });

  navMenu = [];
  /*
  {
    'name':'营销',
    'page':'',
    'target':'',
    'sub': [
      {
        'name':'整体生产经营情况分析',
        'page':'a2-1.html',
        'target':'_self',
      },
      {
        'name':'整体直销分析',
        'page':'a2-4.html',
        'target':'_blank',
      },
      {
        'name':'整体分机型经营分析',
        'page':'a2-5.html',
        'target':'_self',
      },
      {
        'name':'实时客票',
        'page':'a2-3.html',
        'target':'_self',
      },
    ]
  }
  */

  var len = usersMenuList.length;

  for (var i = 0; i < len; i++) {
    var obj = usersMenuList[i];
    if (obj.parent_id == 0 || obj.parent_id == "") {
      var id = obj.id;
      var menu = {
        name: obj.name,
        page: obj.url,
        target: obj.target,
        sub: [],
      };

      //查找子菜单
      var len2 = usersMenuList.length;
      for (var j = 0; j < len2; j++) {
        var obj2 = usersMenuList[j];
        if (obj2.parent_id == id) {
          var submenu = {
            name: obj2.name,
            page: obj2.url,
            target: obj2.target,
          };
          menu.sub.push(submenu);
        }
      }

      navMenu.push(menu);
    }
  }

  // 如果ls中有debug标志，那么增加测试菜单选项
  try {
    // if (localStorage.getItem("debug") == 1) {
    //   navMenu.push({
    //     name: "成本预估分析",
    //     page: "a5-1.html",
    //     target: "_self",
    //     sub: []
    //   });
    // }
  } catch (e) {}

  // 重定向到菜单中的第一个页面. index.html 中定义
  if (window.redirectToFirstPage) {
    var len = navMenu.length;
    for (var i = 0; i < len; i++) {
      var obj = navMenu[i];
      if (obj.page != "") {
        window.location.href = getUrl(obj.page);
        return;
      } else {
        var subs = obj.sub;
        var len2 = subs.length;
        for (var j = 0; j < len2; j++) {
          var obj2 = subs[j];
          if (obj2.page != "") {
            window.location.href = getUrl(obj2.page);
            return;
          }
        }
      }
    }
  }

  // 检测是否有权限
  var len = navMenu.length;
  var foundInUsersMenu = false;
  var url = window.location.href;
  url = url.replace("_exhibition", "");
  for (var i = 0; i < len; i++) {
    var obj = navMenu[i];
    if (
      (obj.page.length > 0 && url.indexOf(obj.page) > -1) ||
      url.indexOf("a0-1") > -1
    ) {
      foundInUsersMenu = true;
      break;
    }
    var subs = obj.sub;
    var len2 = subs.length;
    for (var j = 0; j < len2; j++) {
      var obj2 = subs[j];
      if (obj2.page.length > 0 && url.indexOf(obj2.page) > -1) {
        foundInUsersMenu = true;
        break;
      }
    }
  }
  if (!foundInUsersMenu) {
    // 没有找到，跳转到首页
    window.location.href = "index.html";
    return;
  }

  var html = "";
  var len = navMenu.length;
  for (var i = 0; i < len; i++) {
    var obj = navMenu[i];
    var subs = obj.sub;
    var subhtml = "";
    var selected = false;

    if (obj.page.length > 0 && window.location.href.indexOf(obj.page) > -1) {
      selected = true;
      currentNavPage = obj.page;
      // 设置页面标题
      document.title = "HNA数字航空 - " + obj.name;
    }
    for (var j = 0; j < subs.length; j++) {
      var sobj = subs[j];
      var url = getUrl(sobj.page);
      subhtml +=
        '<a class="itm" href="' +
        url +
        '" target="' +
        sobj.target +
        '">' +
        sobj.name +
        "</a>";

      let pageUrl = window.location.href;
      pageUrl = pageUrl.replace("_exhibition", "");

      if (sobj.page.length > 0 && pageUrl.indexOf(sobj.page) > -1) {
        selected = true;
        currentNavPage = sobj.page;
        // 设置页面标题
        document.title = "HNA数字航空 - " + sobj.name;
        $(".pagetitle .maintitle .tit").html(sobj.name); // 页面标题
      }
    }

    html += '<div class="menu m' + (i + 1) + '">';
    if (!selected) {
      html += '<div class="lv1">';
    } else {
      html += '<div class="lv1 selected">';
    }

    html += '<div class="sbg"></div>';
    if (obj.page.length > 0) {
      var url = getUrl(obj.page);
      html +=
        '<a href="' +
        url +
        '" target="' +
        obj.target +
        '">' +
        obj.name +
        "</a>";
    } else {
      html += '<a href="#">' + obj.name + "</a>";
    }

    if (subhtml.length > 0) {
      html += '<div class="lv2 ">';
      html += subhtml;
      html += "</div>";
    }

    html += "</div>";
    html += "</div>";
  }

  $("#nav").html(html);

  $("#nav a").each(function (i, v) {
    if ($(v).attr("href").indexOf("a5-1.html") > -1) {
      $(v).css({
        display: "inline-block",
        "min-width": "120px",
      });
    }
  });

  $("#nav .menu .lv1").on("mouseover", function () {
    $(this).find(".lv2").show();
  });
  $("#nav .menu .lv1").on("mouseout", function () {
    $(this).find(".lv2").hide();
  });

  // 右边公司下拉列表容器，控制按钮，个人中心

  if (!auto_switch) {
    $("#ctrl_autoswitch").addClass("switch_off");
  }
  if (audio_muted) {
    $("#ctrl_mute").addClass("audio_off");
  }

  // 自动轮播／动画控制按钮
  $("#ctrl_autoswitch").on("click", function (evt) {
    if ($("#ctrl_autoswitch").hasClass("switch_off")) {
      $("#ctrl_autoswitch").removeClass("switch_off");
      autoSwitch = true;
      ///////
      localStorage.setItem("auto_switch", 1);
    } else {
      $("#ctrl_autoswitch").addClass("switch_off");
      autoSwitch = false;
      ///////
      localStorage.setItem("auto_switch", 0);
    }
    $.powerTip.hide();
  });
  $("#ctrl_autoswitch").powerTip({
    fadeInTime: 1,
    fadeOutTime: 1,
    followMouse: true,
    offset: 10,
    manual: true,
  });
  $("#ctrl_autoswitch").data("powertip", function () {
    var html = "";
    if ($("#ctrl_autoswitch").hasClass("switch_off")) {
      html += '<div class="powertip">播放开始</div>';
    } else {
      html += '<div class="powertip">播放暂停</div>';
    }

    return html;
  });
  $("#ctrl_autoswitch").on("mouseover", function (evt) {
    $.powerTip.show($(this));
  });
  $("#ctrl_autoswitch").on("mouseout", function (evt) {
    $.powerTip.hide();
  });

  // 语音播报控制按钮
  $("#ctrl_mute").on("click", function (evt) {
    if ($("#ctrl_mute").hasClass("audio_off")) {
      $("#ctrl_mute").removeClass("audio_off");
      audioPaused = false;
      playAudio();
      localStorage.setItem("audio_muted", 0);
    } else {
      $("#ctrl_mute").addClass("audio_off");
      audioPaused = true;
      pauseAudio();
      localStorage.setItem("audio_muted", 1);
    }
    $.powerTip.hide();
  });
  $("#ctrl_mute").powerTip({
    fadeInTime: 1,
    fadeOutTime: 1,
    followMouse: true,
    offset: 10,
    manual: true,
  });
  $("#ctrl_mute").data("powertip", function () {
    var html = "";
    if ($("#ctrl_mute").hasClass("audio_off")) {
      html += '<div class="powertip">开启语音</div>';
    } else {
      html += '<div class="powertip">关闭语音</div>';
    }

    return html;
  });
  $("#ctrl_mute").on("mouseover", function (evt) {
    $.powerTip.show($(this));
  });
  $("#ctrl_mute").on("mouseout", function (evt) {
    $.powerTip.hide();
  });

  // 个人中心
  $("#ctrl_user").on("click", function () {
    if ($("#pop_usercenter").length == 0) {
      showUserCenter();
    } else {
      hideUserCenter();
    }
    $.powerTip.hide();
  });
  regTooltip("#ctrl_user", "用户中心");
}

function showUserCenter() {
  $("#ctrl_user").addClass("user_on");

  var html = "";
  html += '<div id="pop_usercenter" >';
  html += '<div class="arr"></div>';
  html += '<div class="btnx"></div>';
  html += '<div class="tit">个人中心</div>';

  html += '<div class="blk">';
  html += '<div class="rw">';
  html += '<div class="c1">账号</div>';
  html += '<div class="c2 userid">' + userinfo.id + "</div>";
  html += "</div>";
  html += '<div class="rw">';
  html += '<div class="c1">姓名</div>';
  html += '<div class="c2 username">' + userinfo.username + "</div>";
  html += "</div>";
  html += "</div>";

  html += '<div class="blk">';
  html += '<div class="rw">';
  html += '<div class="c1">机构</div>';
  html += '<div class="c2 org">' + userinfo.org + "</div>";
  html += "</div>";
  html += '<div class="rw">';
  html += '<div class="c1">用户组</div>';
  html += '<div class="c2 role">' + userinfo.role + "</div>";
  html += "</div>";
  html += "</div>";

  html += '<div class="blk">';
  html += '<div class="rw">';
  html += '<div class="c1">意见<br>反馈</div>';
  html +=
    '<div class="c2 org"><a href="mailto:<EMAIL>" style="color:#fff;"><EMAIL></a><br>089868875952<br>邝继尖</div>';
  html += "</div>";
  html += "</div>";

  //html += '<div id="ctrl_video">观看演示视频</div>';
  html += '<div class="bot">';
  html += '<div id="ctrl_logout">退出登录</div>';
  html += "</div>";

  html += "</div>";
  $("#header").append(html);

  // 介绍视频按钮
  $("#pop_usercenter .btnx").on("click", function () {
    hideUserCenter();
  });

  // 介绍视频按钮
  $("#ctrl_video").on("click", function () {
    $.powerTip.hide();
    hideUserCenter();
    window.open("video/intro.html");
    //showIntroVideo();
  });

  // logout
  $("#ctrl_logout").on("click", function () {
    logout();
    hideUserCenter();
    $.powerTip.hide();
  });
}

function hideUserCenter() {
  $("#ctrl_user").removeClass("user_on");
  $("#pop_usercenter").remove();
  $.powerTip.hide();
}

var dfd = $.Deferred();

function getCompany() {
  var data = "";

  $.ajax({
    type: "POST",
    url: "/bi/query/company",
    async: true,
    dataType: "json",
    data: data,
    success: function (response) {
      checkLogin(response);

      let url = location.href;
      url = url.replace("_exhibition", "");

      var html = "";
      var _find_right = $.grep(userinfo.MENU.menus, function (ele, i) {
        return url.indexOf(ele.url) > -1 && ele.url != "";
      });
      // if (_find_right.length === 0) {
      //   alert("没有相关权限");
      //   return;
      // }
      // 具有可以查看权限的公司列表
      var _find_company_right = _find_right[0].companys;
      var arr = [];
      if (window.location.href.indexOf("a2-4") > -1) {
        arr = $.grep(response.comp, function (ele, i) {
          return ele.code != "HX";
        });
      } else if (window.location.href.indexOf("a3-1") > -1) {
        arr = $.grep(response.comp, function (ele, i) {
          return ele.code != "HX" && ele.code != "GT";
        });
      } else {
        arr = response.comp;
      }

      companylist = arr;

      // console.log([companylist,_find_company_right])
      // 找到在权限列表_find_company_right中具有的公司，然后push到arr数组中
      var arr = [];
      var arr2 = [];
      $.each(companylist, function (i, ele) {
        // console.log(ele)
        var _find = $.grep(_find_company_right, function (_ele, _i) {
          return _ele.company == ele.id || "";
        });
        if (_find.length != 0) {
          ele["disabled"] = false;
          arr2.push(ele);
        } else {
          ele["disabled"] = true;
        }
        arr.push(ele);
      });
      console.log(arr);

      // 用新的arr数组替换companylist
      companylist = arr;
      companylistAuth = arr2;
      // 排除不显示的公司
      var excodes = [];
      for (var page in excludeComps) {
        if (window.location.href.indexOf(page) > -1) {
          excodes = excludeComps[page];
          break;
        }
      }
      var len = companylist.length;
      for (var i = len - 1; i >= 0; i--) {
        var dat = companylist[i];
        if (excodes.indexOf(dat.code) > -1) {
          companylist.splice(i, 1);
        }
      }

      companylist.sort(function (a, b) {
        return a.sort - b.sort;
      });

      usersCompayDataList = [];
      usersCompayCodeList = [];

      var len = companylist.length;
      for (var i = 0; i < len; i++) {
        var dat = {};
        // if (!companylist[i].disabled) {
        dat = companylist[i];
        // }

        companyCode2Name[dat.code] = dat.name;
        companyName2Code[dat.name] = dat.code;
        companyCode2Nameabbr[dat.code] = dat.nameabbr;
        if (dat.code == parent_company) {
          companyYshcode2Code["ALL"] = dat.code;
        } else {
          companyYshcode2Code[dat.yhscode] = dat.code;
          companyCode2YshCode[dat.code] = dat.yhscode;
        }
        companyCode2Sort[dat.code] = dat.sort;
        if (i != 0) {
          companyCode2Color[dat.code] = companyColors[i - 1];
        } else {
          companyCode2Color[dat.code] = "#e60000";
        }

        //暂时不做用户权限限制，因为部分用户啥都看不到
        if (usersCompanyList.indexOf(dat.id) > -1) {
          if (!dat.disabled) {
            html +=
              '<div class="itm" code="' +
              dat.code +
              '" comp_id="' +
              dat.id +
              '">' +
              dat.name +
              "</div>";
            usersCompayDataList.push(dat);
            usersCompayCodeList.push(dat.code);
          }
        }
      }

      $("#companylist").html(html);

      // 选择公司

      $("#companycombo .box").on("click", function () {
        if ($("#companylist").is(":visible")) {
          $("#companylist").hide();
        } else {
          $("#companylist").slideDown("200", function () {});
        }
      });

      $("#companylist .itm").on("click", function () {
        $("#companylist").hide();
        var code = $(this).attr("code");
        switchCompany(code);

        stopAutoSwitchCompany();
      });

      $("#companycombo").on("mouseleave", function () {
        $("#companylist").hide();
      });

      if (companylist.length != usersCompayCodeList.length) {
        stopAllAutoSwitchCompanyInterval = true;
      }

      console.log("====>>>>>>>>>>dfd .resolve");
      dfd.resolve();
    },
    error: function (e) {
      //console.log('ajax error');
      //console.log(e);
    },
  });
}

var stopAllAutoSwitchCompanyInterval = false; // true时停止所有自动循环切换公司，在地图／六角形蜂窝页面调用

function stopAutoSwitchCompany() {
  $("#ctrl_autoswitch").addClass("switch_off");
  autoSwitch = false;
}
/**
 *
 * @returns 判断是否可以看所有公司
 */
function hasAllCompanyPermission() {
  for (var i = 0; i < companylistAuth.length; i++) {
    if (companylistAuth[i].code === "HNAHK") {
      return true;
    }
  }
  return false;
}

function switchCompany(code) {
  // 判断当前code是否可查询
  var codeArr = [];
  for (var i = 0; i < companylistAuth.length; i++) {
    codeArr.push(companylistAuth[i].code);
  }
  // 如果不存在这个航司编码，就跳转到403页面
  if (codeArr.indexOf(code) == -1) {
    location.href = "../largescreen/403.html";
    return false;
  }
  selected_company_code = code;

  var len = usersCompayDataList.length;

  var comp_id;
  var findCode = false;
  for (var i = 0; i < len; i++) {
    var dat = usersCompayDataList[i];
    if (dat.code == code) {
      comp_id = dat.id;
      $("#companycombo .box").text(dat.name);
      findCode = true;
      break;
    }
  }

  if (findCode) {
    if (
      window.location.href.indexOf("a2-3") == -1 &&
      window.location.href.indexOf("a1-2") == -1
    ) {
      window.location.hash = "#" + code;
    }

    $("#companycombo").attr("code", code);
    if (
      $("#mainframe")[0] &&
      $("#mainframe")[0].contentWindow &&
      $("#mainframe")[0].contentWindow.onCompCodeChanged
    ) {
      $("#mainframe")[0].contentWindow.onCompCodeChanged(code, comp_id);
    }
    $(".pagetitle .maintitle .comp").html(companyCode2Name[code]); // 页面标题-公司名称
    onCompanyChanged(code, companyCode2Name[code]);
  }
}

function selectDefaultCompany() {
  if (usersCompayDataList) {
    // 显示 URL 参数公司的区域
    var company = window.location.hash.substr(1);
    console.log(company);
    if (company) {
      switchCompany(company);
    } else {
      switchCompany(usersCompayDataList[0].code);
    }
  } else {
    setTimeout(selectDefaultCompany, 1);
  }
}

function checkFrameReady() {
  try {
    if (typeof onCompanyChanged == "undefined") {
      setTimeout(checkFrameReady, 1);
      return;
    }
  } catch (e) {
    setTimeout(checkFrameReady, 1);
    return;
  }

  if ($("#mainframe").length > 0) {
    if (
      $("#mainframe")[0] &&
      $("#mainframe")[0].contentWindow &&
      $("#mainframe")[0].contentWindow.main
    ) {
      $("#mainframe")[0].contentWindow.main(companylist, companyCode2Sort);

      selectDefaultCompany();
    } else {
      setTimeout(checkFrameReady, 10);
    }
  } else {
    selectDefaultCompany();
  }
}
dfd.done(function () {
  console.log("====>>>>>>>>>>.checkFrameReady");
  checkFrameReady();
});

// 页面访问日志
function userlog() {
  baseInfoInitDtd.done(function () {
    var pagename;

    var len = navMenu.length;
    for (var i = 0; i < len; i++) {
      var obj = navMenu[i];
      if (
        obj.page.length > 0 &&
        obj.sub.length == 0 &&
        window.location.href.indexOf(obj.page) > -1
      ) {
        pagename = obj.name;
        break;
      }
      var subs = obj.sub;

      for (var j = 0; j < subs.length; j++) {
        var sobj = subs[j];

        if (
          sobj.page.length > 0 &&
          window.location.href.indexOf(sobj.page) > -1
        ) {
          pagename = sobj.name;
        } else if (sobj.subpage && sobj.subpage.length > 0) {
          for (var k = 0; k < sobj.subpage.length; k++) {
            var pp = sobj.subpage[k];
            if (window.location.href.indexOf(pp) > -1) {
              pagename = sobj.name;
              break;
            }
          }
        }
      }
    }

    if (pagename) {
      var date = new Date();
      var mm = date.getMonth() + 1;
      var dd = date.getDate();
      var h = date.getHours();
      var m = date.getMinutes();
      var s = date.getSeconds();

      if (mm < 10) {
        mm = "0" + mm;
      }
      if (dd < 10) {
        dd = "0" + dd;
      }
      if (h < 10) {
        h = "0" + h;
      }
      if (m < 10) {
        m = "0" + m;
      }
      if (s < 10) {
        s = "0" + s;
      }

      var timeNow =
        date.getFullYear() +
        "年" +
        mm +
        "月" +
        dd +
        "日 " +
        h +
        ":" +
        m +
        ":" +
        s;

      var operation = "访问页面:" + pagename + ", 时间" + timeNow;
      var category = "页面";

      var param = {
        userid: userinfo.id,
        oper: operation,
        category: category,
      };

      $.ajax({
        type: "post",
        url: "/bi/web/userlog",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {},
        error: function () {},
      });
    }
  });
}

function isAllCompany() {
  return current_company_code === parent_company;
}

$(window).resize(function () {
  //setPageScale();
  location.reload();
});

var pageZoomScale = 1;

// 控制页面放缩比例，撑满整个浏览器
function setPageScale() {
  var is1920 =
    window.location.href.indexOf("a0-1.html") > 0 ||
    window.location.href.indexOf("a0-12.html") > 0;

  var fillFullScreen =
    window.location.href.indexOf("a0-1.html") > 0 ||
    window.location.href.indexOf("a0-13.html") > 0 ||
    window.location.href.indexOf("a0-14.html") > 0 ||
    window.location.href.indexOf("a0-1.html") > 0 ||
    window.location.href.indexOf("a0-15.html") > 0;

  var wapperWidth = is1920 ? 1920 : 1366;
  var wapperHeight = is1920 ? 1080 : 768;
  if (
    window.location.href.indexOf("a0-13.html") > 0 ||
    window.location.href.indexOf("a0-15.html") > 0
  ) {
    wapperWidth = 1920;
    wapperHeight = 1440;
  }
  if (window.location.href.indexOf("a0-14.html") > 0) {
    wapperWidth = 1280;
    wapperHeight = 1440;
  }

  var osw = window.innerWidth;
  var osh = window.innerHeight;

  var scale = (pageZoomScale = osw / wapperWidth);
  var scaleHeight = osh / wapperHeight;
  if (!fillFullScreen) {
    if (scaleHeight < scale) {
      scale = scaleHeight;
    }
  }

  pageZoomScale = scale;

  var header = $("#header");
  if (!header.hasClass("not-auto-scale-by-commonjs")) {
    // $('#header').css('zoom', scale);
    // for firefox
    $("#header").css("transform", "scale(" + scale + "," + scaleHeight + ")");
    $("#header").css("transform-origin", "left top");
  }

  // $('.page-wrapper').css('zoom', scale);

  $(".page-wrapper").css(
    "transform",
    "scale(" + scale + "," + scaleHeight + ")"
  );
  $(".page-wrapper").css("transform-origin", "left top");
  $(".scale_item").each(function () {
    // $(this).css('zoom', scale);

    // for firefox
    $(this).css("transform", "scale(" + scale + "," + scaleHeight + ")");
    $(this).css("transform-origin", "left top");
  });

  var scaleInvert = 1 / scale;
  var scaleInvertY = 1 / scaleHeight;

  // $('.chartblock').css('zoom', scaleInvert);

  // firefox
  $(".chartblock").css(
    "transform",
    "scale(" + scaleInvert + "," + scaleInvertY + ")"
  );
  $(".chartblock").css("transform-origin", "left top");

  $(".chartblock").each(function (index, el) {
    $(this).css("width", $(this).attr("prop-width") * scale + "px");
    $(this).css("height", $(this).attr("prop-height") * scaleInvertY + "px");
  });

  var left = 0;
  var top = 0;
  var width = osw;
  var height = width * (wapperHeight / wapperWidth);
  if ($(".mainframe").length) {
    $(".mainframe").attr(
      "style",
      "left:" +
        left +
        "px; " +
        "top:" +
        top +
        "px; " +
        "width:" +
        width +
        "px; " +
        "height:" +
        height +
        "px"
    );
  }
}

function getChartFontSize(fontSize) {
  return Math.max(Math.ceil(pageZoomScale * fontSize), 12);
}

function showLoading() {
  var html =
    '<div id="loading_msk"><div class="spinner spinner_animate"></div></div>';
  $(".page-wrapper:last").append(html);
  try {
    NProgress.start();
  } catch (error) {}
}

function hideLoading() {
  $(".page-wrapper #loading_msk").remove();
  try {
    NProgress.done();
    NProgress.remove();
  } catch (error) {}
}

function showIntroVideo() {
  var osw = document.body.offsetWidth;
  var osh = (osw / 16) * 9;
  var w = Math.round(osw * 0.8);
  var h = Math.round((w / 16) * 9);
  var l = Math.round(osw * 0.1);
  var t = Math.round(osh * 0.06);

  var html = "";
  html += '<div id="modal" style="height:' + osh + 'px;"></div>';
  html +=
    '<div id="popvideo" style="width:' +
    (w + 2) +
    "px; height:" +
    (h + 2) +
    "px; top:" +
    t +
    "px; left:" +
    l +
    'px; ">';
  html += '<div class="spinner spinner_animate"></div>';
  html +=
    '<video src="video/intro.mp4" controls="controls" autoplay="true" width="' +
    w +
    '" height="' +
    h +
    '">';
  html += "您的浏览器不支持 video 标签";
  html += "</video>";
  html += '<div class="close"></div>';
  html += "</div>";
  $("body").append(html);

  $("#popvideo .close").on("click", function () {
    $("#modal").remove();
    $("#popvideo").remove();
  });
}

// 当期选中的页面
var currentNavPage;
var pageList = ["a2-1.html", "a2-4.html", "a2-5.html"];
// 从 kinect 浏览器中调用，左右、上下 滑动手势翻页
// direction: LEFT, RIGHT, UP, DOWN
function kinect_flipPage(direction) {
  var len = pageList.length;
  var id = pageList.indexOf(currentNavPage);
  if (id >= 0) {
    if (direction == "RIGHT") {
      if (id > 0) {
        id--;
      } else {
        id = len - 1;
      }
    } else if (direction == "LEFT") {
      if (id < len - 1) {
        id++;
      } else {
        id = 0;
      }
    }
  } else {
    id = 0;
  }
  var newPage = pageList[id];
  window.location.href = newPage;
}

setPageScale();
//createNavMenu();
//getCompany();

// 页面标题
function getPageTitle() {
  var param = {
    ID: "WEB_PAGE_TITLE",
  };

  $.ajax({
    type: "post",
    url: "/bi/web/querydatalist",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var val = response.data[0].name;
      $("#main_page_title").html("大屏可视化平台");
    },
    error: function (jqXHR, txtStatus, errorThrown) {},
  });

  var param = {
    ID: "WEB_PAGE_SUBTITLE",
  };

  $.ajax({
    type: "post",
    url: "/bi/web/querydatalist",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var val = response.data[0].name;
      $("#main_page_subtitle").html(val);
    },
    error: function (jqXHR, txtStatus, errorThrown) {},
  });
}

getPageTitle();

// 页面比例缩放完后显示，避免页面从小放大的跳动
$("body").css("opacity", 1);

// baidu voice token
var access_token; // = '24.c45c6e3a95304011f9a2743297aee499.2592000.1499391641.282335-8714230';

function getBaiduApiToken() {
  var param = {
    grant_type: "client_credentials",
    client_id: "IieVdjzp7lGbg3s7uGkUDdYs",
    client_secret: "3dec00f31f09cdd5b1bb3532675dd543",
  };

  $.ajax({
    type: "post",
    //url:"https://openapi.baidu.com/oauth/2.0/token?grant_type=client_credentials&client_id=IieVdjzp7lGbg3s7uGkUDdYs&client_secret=3dec00f31f09cdd5b1bb3532675dd543",
    url: "/bi/web/getBaiduToken",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      if (response.data != undefined) {
        try {
          var obj = JSON.parse(response.data);
          //console.log(obj);
          access_token = obj.access_token;
        } catch (e) {}
      }
    },
    error: function () {},
  });
}

// text 语音文本
// clearQueue 清空队列
// endedCallback 队列播放完的回调方法
function text2audio(text, clearQueue, endedCallback) {
  if (access_token == undefined) {
    setTimeout(text2audio, 10, text, clearQueue, endedCallback);
    return;
  }

  if (clearQueue) {
    audioQueue = [];
    audioEndCallbackQueue = [];
  }
  if (!audioEnded) {
    audioQueue.push(text);
    audioEndCallbackQueue.push(endedCallback);
    return;
  }

  var audiourl =
    "https://tsn.baidu.com/text2audio?tex=" +
    encodeURI(encodeURI(text)) +
    "&lan=zh&cuid=123&ctp=1&tok=" +
    access_token;
  //var audiourl = "http://tsn.baidu.com/text2audio?tex="+text+"&lan=zh&cuid=123&ctp=1&tok="+access_token;

  $("#text2audio").remove();

  $("body").append('<audio id="text2audio" src="' + audiourl + '"></audio>');

  var audioInstance = document.getElementById("text2audio");
  audioInstance.load();

  if (!audioPaused) {
    audioInstance.play();
    console.log("audioInstance play");
  } else {
    audioInstance.pause();
    console.log("audioInstance pause");
  }
  audioEnded = false;

  audioInstance.onended = function () {
    audioEnded = true;

    console.log("onended");
    console.log("audioQueue", audioQueue);

    if (audioQueue.length > 0) {
      var txt = audioQueue.shift();
      var callback = audioEndCallbackQueue.shift();
      text2audio(txt, false, callback);
    } else {
      // 队列播放完，回调
      if (endedCallback) {
        endedCallback();
      }
    }
  };
}

function clearAudioQueue() {
  audioQueue = [];
}

function stopAudio() {
  var audioInstance = document.getElementById("text2audio");
  audioInstance.pause();
  audioEnded = true;
  //$('#text2audio').attr('src', '');
}

function pauseAudio() {
  var audioInstance = document.getElementById("text2audio");
  audioInstance.pause();
}

function playAudio() {
  var audioInstance = document.getElementById("text2audio");
  audioInstance.play();

  //重新播放
  //audioInstance.currentTime = 0;
  //audioInstance.play();
}

//加载语音文本模版
// tplkey 模版key
// callback 回调函数
function getAudioTemplate(tplkey, callback) {
  var param = {
    mode: "query",
    tplkey: tplkey,
  };

  $.ajax({
    type: "post",
    url: "/bi/web/voice_template",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      if (response.data && response.data.length > 0) {
        callback(response.data[0]);
      }
    },
    error: function () {},
  });
}

// 同比／环比 增加/减少 模版格式化
function ratioTemplete(ratio) {
  var tpl = "";
  if (ratio > 0) {
    tpl = "增加" + ratio + "%";
  } else if (ratio < 0) {
    tpl = "减少" + Math.abs(ratio) + "%";
  } else {
    tpl = "无";
  }
  return tpl;
}

function regTooltip(element, txt) {
  $(element).powerTip({
    fadeInTime: 1,
    fadeOutTime: 1,
    followMouse: true,
    offset: 10,
    manual: true,
  });
  $(element).data("powertip", function () {
    return '<div class="powertip">' + txt + "</div>";
  });
  $(element).on("mouseover", function (evt) {
    $.powerTip.show($(this));
  });
  $(element).on("mouseout", function (evt) {
    $.powerTip.hide();
  });
}

function apiLoadFailed() {
  alert("网络不给力，请尝试刷新浏览器");
}

function randNum(Min, Max) {
  var Range = Max - Min;
  var Rand = Math.random();
  var num = Min + Math.round(Rand * Range); //四舍五入
  return num;
}

// getBaiduApiToken();

userlog();

(function () {
  document.write("<script src='/largescreen/js/lib/slm.js'></script>");
})();
