showLoading();



var current_company_code;

// 可显示的历史 数量
var query_limit = 20;
// 日期类型
var date_type = 'L';



// -------------------------------------------------------

//                          KPI

// -------------------------------------------------------



var weekList = [];
var monthList = [];
var dayList = [];

var selected_day;
var selected_month;

var selected_chal_code;


// 获取所有公司所有渠道信息, 只包含 1级 渠道
var all_company_chal;
var all_company_chal2; // 只含直销
var all_company_data;
var all_company_data2; // 只含直销

// 所有公司OTA渠道数据
var all_company_ota;
var all_ota_data;

var weekDateRangeList; //例会周的日期对应表

// 日期类型选择
function chooseDateType() {
    $(".date_type_select .tab").on('click', function() {

        $(".date_type_select .tab").removeClass('hover');
        $(this).addClass('hover');
        date_type = $(this).data('type');
        $(".limcomb").addClass('hidden');
        if (date_type == "L") {

            $("#main_cb_week").removeClass('hidden');
        } else if (date_type == "M") {
            $("#main_cb_month").removeClass('hidden');
        } else {
            $("#main_cb_day").removeClass('hidden');
        }
        updateAllKpi();
    });
}
chooseDateType();

function getDifDate(date,difNum) {
    var difDate = new Date(date.getTime() - difNum * 24 * 3600 * 1000);
    var difMonth = difDate.getMonth() + 1;
    var difDay = difDate.getDate();
    if (difMonth < 10) {
        difMonth = '0' + difMonth;
    }
    if (difDay < 10) {
        difDay = "0" + difDay;
    }
    difDate = difDate.getFullYear() + "" + difMonth + difDay;
    return difDate;
}

// 后台JOB图标显示
function changeDataRefresh() {
    $('.pagetitle .data_refresh').hide();
    var param = {
        'CODE': 'J_HVP_FAC_CHAL_KPI'
    }
    $.ajax({
        type: 'post',
        url: "/bi/sys/isrunning",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
            if (response.isrun == 1) {
                $('.pagetitle .data_refresh').show();
                regTooltip('#btnrefresh', '后台数据更新中');
            }
        },
        error: function() {

        }
    });
}
changeDataRefresh();
setInterval(changeDataRefresh, 1 * 60 * 60 * 1000);

function getAllChal() {
    var param = {}

    $.ajax({
        type: 'post',
        url: "/bi/web/chal",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
            // console.log(222);
            // console.log(response)
            all_company_chal = {};
            all_company_ota = {};
            var chal_id_list = [];
            var ota_chal_id_list = [];
            for (var compcode in response) {
                if (compcode != 'errorcode' && compcode != 'errordesc') {
                    var list = response[compcode]['CHAL'];
                    all_company_chal[compcode] = {};
                    all_company_ota[compcode] = [];
                    for (var i = list.length - 1; i >= 0; i--) {
                        var obj = list[i];
                        /*
                        CHAL_CODE:"AGENT"
                        CHAL_FLAG:"1"
                        CHAL_LVL:"1"
                        CHAL_NAME:"代理人"
                        COMP_CHAL_ID:"200018"
                        PAR_CHAL_CODE:""
                        SORT:"4"
                        */
                        if (obj.PAR_CHAL_CODE == '') {
                            all_company_chal[compcode][obj.CHAL_CODE] = obj;
                            chal_id_list.push(obj.COMP_CHAL_ID);
                        }
                        if (obj.PAR_CHAL_CODE == 'OTA') {
                            all_company_ota[compcode].push(obj);
                            ota_chal_id_list.push(obj.COMP_CHAL_ID)
                        }

                    }
                }
            }
            getAllChalKpi(chal_id_list);
            getAllOtaKpi(ota_chal_id_list);

        },
        error: function() {

        }
    });

    // 获取所有直销取消，含B2C分解的多个渠道
    var param = {}

    $.ajax({
        type: 'post',
        url: "/bi/web/chal2",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
            all_company_chal2 = {};
            var chal_id_list = [];
            for (var compcode in response) {
                if (compcode != 'errorcode' && compcode != 'errordesc') {
                    var list = response[compcode]['CHAL'];
                    all_company_chal2[compcode] = {};
                    for (var i = list.length - 1; i >= 0; i--) {
                        var obj = list[i];
                        /*
                        CHAL_CODE:"AGENT"
                        CHAL_FLAG:"1"
                        CHAL_LVL:"1"
                        CHAL_NAME:"代理人"
                        COMP_CHAL_ID:"200018"
                        PAR_CHAL_CODE:""
                        SORT:"4"
                        */
                        all_company_chal2[compcode][obj.CHAL_CODE] = obj;
                        chal_id_list.push(obj.COMP_CHAL_ID);
                    }
                }
            }
            getAllChal2Kpi(chal_id_list);

        },
        error: function() {

        }
    });
}

// 获取OTA渠道 KPI
function getAllOtaKpi(chal_id_list) {

    all_ota_data = {}
    all_company_data['kpi_value_d'] = {}

    //ADD_INC 燃油加价
    //SAL_VOL 销售量
    //SAL_AMT 销售额(不含油)
    //SAL_ARP_FEE 机场建设费

    var kpilist = ['ADD_INC', 'SAL_VOL', 'SAL_AMT'];

    var loadCnt = 3;

    //---------- 本期

    var param = {
        'SOLR_CODE': 'FAC_CHAL_KPI',
        'KPI_CODE': kpilist.join(','),
        'CHAL': chal_id_list.join(','),
        'VALUE_TYPE': 'kpi_value_d', //本期
        'DATE_TYPE': 'L,M,D',
        'LIMIT': query_limit
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getchalallkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
            // console.log("ota数据")
            // console.log(response)
            all_ota_data['kpi_value_d'] = response;

        },
        error: function() {}
    });


}


// 获取所有渠道 KPI
function getAllChalKpi(chal_id_list) {

    all_company_data = {}
    all_company_data['kpi_value_d'] = {}
    all_company_data['kpi_value_tq_d'] = {}
    all_company_data['kpi_value_sq_d'] = {}

    //ADD_INC 燃油加价
    //SAL_VOL 销售量
    //SAL_AMT 销售额(不含油)
    //SAL_ARP_FEE 机场建设费

    var kpilist = ['ADD_INC', 'SAL_VOL', 'SAL_AMT'];

    var loadCnt = 3;

    //---------- 本期

    var param = {
        'SOLR_CODE': 'FAC_CHAL_KPI',
        'KPI_CODE': kpilist.join(','),
        'CHAL': chal_id_list.join(','),
        'VALUE_TYPE': 'kpi_value_d', //本期
        'DATE_TYPE': 'L,M,D',
        'LIMIT': query_limit
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getchalallkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            all_company_data['kpi_value_d'] = response;
            
            // L
            var datlist = response['SAL_VOL']['L']['date'];
            var cblist = [];
            weekList = [];

            var len = datlist.length;
            for (var i = 0; i < len; i++) {
                var lst = datlist[i]['date'];
                if (lst && lst.length > 0) {
                    var len2 = lst.length;
                    for (var j = 0; j < len2; j++) {
                        var d = lst[j].date;
                        if (weekList.indexOf(d) == -1) {
                            weekList.push(d);
                        }
                    }
                }
            }
            weekList.sort();
            weekList.reverse();
            weekList = weekList.slice(0, query_limit);
            weekList.reverse();

            getWeekDateRange(weekList);

            // 10天前
            var difDate = getDifDate(new Date(),10);
            var cblist = [];
            var len = weekList.length;
            var last_date;
            var fakeWeekOne = false;
            for (var i = 1; i < len - 1; i++) {
                var date = weekList[i];
                var range = weekDateRangeList[date];
                if(range){
                    var rangeEnd = range.split("-")[1];
                    if(Number(rangeEnd) > Number(difDate)){
                        continue;
                    }
                }
                // Week: *********
                //var label = date.substr(0,4) + '年第' + Number(date.substr(4,3)) + '周';
                //20170419 双双要求：显示的周数+1
                var weeknum = Number(date.substr(4, 3));
                // 20180111, 因为周数+1，新的一年缺少第1周，所以增加第1周，数据显示上年最后一周的
                if (weeknum == 1) {
                    var label = '第' + 1 + '周 ';
                    cblist.unshift({
                        'label': label,
                        'data': last_date
                    });
                    fakeWeekOne = true;
                }
                var label = '第' + (weeknum + 1) + '周 ';
                cblist.unshift({
                    'label': label,
                    'data': date
                });

                last_date = date;
            }
            // if(fakeWeekOne){
            //     cblist = cblist.slice(0,6);
            // }else{
            //     cblist = cblist.slice(0,6);
            // }
            createComboBox('main_cb_week', cblist, 84, 240, updateAllKpi);


            // $('#main_cb_week .combobox_label').on('click', function(event) {
            //     event.preventDefault();

            //     if(date_type == 'L'){
            //         return;
            //     }

            //     $('.block_r2 .tab1').text('直销量占比周走势');

            //     $('#main_cb_week').addClass('combotab_selected');
            //     $('#main_cb_week').removeClass('combotab');
            //     $('#main_cb_month').addClass('combotab');
            //     $('#main_cb_month').removeClass('combotab_selected');

            //     date_type = 'L';
            //     updateAllKpi();

            // });


            // 显示 week 日期范围
            $('#main_cb_week .combobox_label').on('mouseover', function(event) {
                event.preventDefault();
                if (weekDateRangeList) {
                    var date = $('#main_cb_week').attr('data');
                    $('#week_date_range').text(weekDateRangeList[date]);
                    $('#week_date_range').fadeIn();
                }
            });

            // 隐藏 week 日期范围
            $('#main_cb_week .combobox_label').on('mouseout', function(event) {
                event.preventDefault();
                if (weekDateRangeList) {
                    $('#week_date_range').fadeOut();
                }
            });


            // M
            var datlist = response['SAL_VOL']['M']['date'];
            monthList = [];

            var len = datlist.length;
            for (var i = 0; i < len; i++) {
                var lst = datlist[i]['date'];
                if (lst && lst.length > 0) {
                    var len2 = lst.length;
                    for (var j = len2 - 1; j >= 0; j--) {
                        var date = lst[j].date;
                        if (date && monthList.indexOf(date) == -1) {
                            monthList.push(date);
                        }
                    }
                }
            }
            monthList.sort();
            monthList.reverse();
            monthList = monthList.slice(0, query_limit);
            monthList.reverse();

            // var date = new Date();
            // var month = date.getMonth() + 1;
            // var day = date.getDate();
            // if (month < 10) {
            //     month = '0' + month;
            // }
            // var nowmonth = date.getFullYear() + '' + month;
            var difMonth = Number(difDate.substr(4,2));
            var difYear = Number(difDate.substr(0,4))
            if(difMonth == 1){
                difMonth = 12;
                difYear = difYear -1;
            }else{
                difMonth = difMonth -1;
                if(difMonth < 10){
                    difMonth = "0"+ difMonth;
                }
            }
            difMonth = difYear + '' + difMonth;
            var cblist = [];
            var len = monthList.length;
            for (var i = 0; i < len; i++) {
                var date = monthList[i];
                // if (date <= nowmonth) {
                if (date <= difMonth) {
                    var label = date.substr(0, 4) + '年' + Number(date.substr(4, 2)) + '月';
                    cblist.unshift({
                        'label': label,
                        'data': date
                    });
                }

            }
            // cblist = cblist.slice(0,6);
            createComboBox('main_cb_month', cblist, 104, 240, updateAllKpi, 0);

            // $('#main_cb_month .combobox_label').on('click', function(event) {
            //     event.preventDefault();

            //     if(date_type == 'M'){
            //         return;
            //     }

            //     $('.block_r2 .tab1').text('直销量占比月走势');

            //     $('#main_cb_week').addClass('combotab');
            //     $('#main_cb_week').removeClass('combotab_selected');
            //     $('#main_cb_month').addClass('combotab_selected');
            //     $('#main_cb_month').removeClass('combotab');

            //     date_type = 'M';
            //     updateAllKpi();
            // });


            // 显示 month 日期范围
            $('#main_cb_month .combobox_label').on('mouseover', function(event) {
                event.preventDefault();

                var month = $('#main_cb_month').attr('data');
                var curmonth = moment().format("YYYYMM");
                var numofdays = moment(month, "YYYYMM").daysInMonth(); // 获取一个月有几天
                var days = numofdays;
                if (days < 10) {
                    days = '0' + days;
                }
                if (curmonth == month) {
                    days = moment().format("DD");
                }
                $('#week_date_range').text(month + '01' + '~' + month + days);
                $('#week_date_range').fadeIn();
            });

            // 隐藏 month 日期范围
            $('#main_cb_month .combobox_label').on('mouseout', function(event) {
                event.preventDefault();
                $('#week_date_range').fadeOut();
            });
            $('#main_cb_month .combobox_label').on('click', function(event) {
                event.preventDefault();
                $('#week_date_range').fadeOut();
            });

            // D
            var datlist = response['SAL_VOL']['D']['date'];
            dayList = [];

            var len = datlist.length;
            for (var i = 0; i < len; i++) {
                var lst = datlist[i]['date'];
                if (lst && lst.length > 0) {
                    var len2 = lst.length;
                    for (var j = len2 - 1; j >= 0; j--) {
                        var date = lst[j].date;
                        if (date && dayList.indexOf(date) == -1) {
                            dayList.push(date);
                        }
                    }
                }
            }
            console.log(dayList)
            dayList.sort();
            dayList.reverse();
            dayList = dayList.slice(0, query_limit);
            dayList.reverse();

            // var date2 = new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000);
            // var month2 = date2.getMonth() + 1;
            // var day2 = date2.getDate();
            // if (month2 < 10) {
            //     month2 = '0' + month;
            // }
            // if (day2 < 10) {
            //     day2 = "0" + day2;
            // }

            // var nowdate = date2.getFullYear() + '' + month2 + '' + day2;

            var cblist2 = [];
            var len = dayList.length;
            for (var i = 0; i < len; i++) {
                var date = dayList[i];
                if (date <= difDate) {
                var label = date.substr(0, 4) + '年' + date.substr(4, 2) + '月' + date.substr(6, 2) + '日';
                cblist2.unshift({
                    'label': label,
                    'data': date
                });
                }

            }
            // cblist = cblist.slice(0,6);
            createComboBox('main_cb_day', cblist2, 104, 240, updateAllKpi, 0);

            // $('#main_cb_day .combobox_label').on('click', function(event) {
            //     event.preventDefault();

            //     if(date_type == 'D'){
            //         return;
            //     }

            //     $('.block_r2 .tab1').text('直销量占比月走势');

            //     $('#main_cb_week').addClass('combotab');
            //     $('#main_cb_week').removeClass('combotab_selected');
            //     $('#main_cb_month').addClass('combotab_selected');
            //     $('#main_cb_month').removeClass('combotab');

            //     date_type = 'M';
            //     updateAllKpi();
            // });


            // 显示 month 日期范围
            // $('#main_cb_month .combobox_label').on('mouseover', function(event) {
            //     event.preventDefault();

            //     var month = $('#main_cb_month').attr('data');
            //     var curmonth = moment().format("YYYYMM");
            //     var numofdays = moment(month, "YYYYMM").daysInMonth(); // 获取一个月有几天
            //     var days = numofdays;
            //     if(days < 10){
            //         days = '0'+days;
            //     }
            //     if(curmonth == month){
            //         days = moment().format("DD");
            //     }
            //     $('#week_date_range').text(month+'01'+'~'+month+days);
            //     $('#week_date_range').fadeIn();
            // });

            // 隐藏 month 日期范围
            // $('#main_cb_month .combobox_label').on('mouseout', function(event) {
            //     event.preventDefault();
            //     $('#week_date_range').fadeOut();
            // });
            // $('#main_cb_month .combobox_label').on('click', function(event) {
            //     event.preventDefault();
            //     $('#week_date_range').fadeOut();
            // });


            loadCnt--;
            checkLoaded();


        },
        error: function() {}
    });


    //---------- 同期

    var param = {
        'SOLR_CODE': 'FAC_CHAL_KPI',
        'KPI_CODE': kpilist.join(','),
        'CHAL': chal_id_list.join(','),
        'VALUE_TYPE': 'kpi_value_tq_d', //同期
        'DATE_TYPE': 'L,M,D',
        'LIMIT': query_limit
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getchalallkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
            loadCnt--;

            all_company_data['kpi_value_tq_d'] = response;

            checkLoaded();
        },
        error: function() {}
    });


    //---------- 上期

    var param = {
        'SOLR_CODE': 'FAC_CHAL_KPI',
        'KPI_CODE': kpilist.join(','),
        'CHAL': chal_id_list.join(','),
        'VALUE_TYPE': 'kpi_value_sq_d', //上期
        'DATE_TYPE': 'L,M,D',
        'LIMIT': query_limit
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getchalallkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
            loadCnt--;

            all_company_data['kpi_value_sq_d'] = response;

            checkLoaded();
        },
        error: function() {}
    });



    // 获取例会周对应的日期范围
    function getWeekDateRange(week_list) {
        var param = {
            "DATE_ID": week_list.join(','),
            "FIELD": "DATE_TYPE" // 对应数据表字段 DATE_DESC_XS
        }

        $.ajax({
            type: 'post',
            url: "/bi/web/datetype",
            contentType: 'application/json',
            dataType: 'json',
            async: false,
            data: JSON.stringify(param),
            success: function(response) {

                weekDateRangeList = response;

                // // 获取最新的有数据的日期，用来确定当前例会周截止的日期是哪天
                // var param = {
                //     'SOLR_CODE': 'FAC_COMP_KPI',
                //     'COMP_CODE': parent_company,
                //     'KPI_CODE': 'TRV_RATE',
                //     'VALUE_TYPE': 'kpi_value_d',
                //     'DATE_TYPE': 'D',
                //     "OPTIMIZE": 1,
                //     'LIMIT': 1
                // }

                // $.ajax({
                //     type: 'post',
                //     url: "/bi/query/getkpi",
                //     contentType: 'application/json',
                //     dataType: 'json',
                //     async: true,
                //     data: JSON.stringify(param),
                //     success: function(response) {

                //         if (response.data != undefined) {
                //             var ddd = response.data[parent_company]['TRV_RATE']['D'];
                //             var latest_date;
                //             for (var da in ddd) {
                //                 latest_date = da;
                //                 break;
                //             }

                //             //把最新周的结束日期换成上面查到的最新日期
                //             if (latest_date) {
                //                 var latest_week = 0;
                //                 for (var week in weekDateRangeList) {
                //                     if (Number(week) > Number(latest_week)) {
                //                         latest_week = week;
                //                     }
                //                 }
                //                 if (latest_week != 0) {
                //                     var date_range = weekDateRangeList[latest_week];
                //                     var arr = date_range.split('-');
                //                     if (Number(arr[1]) > Number(latest_date) && Number(arr[0]) <= Number(latest_date)) {
                //                         //weekDateRangeList[latest_week] = arr[0]+'-'+latest_date;
                //                     }
                //                 }
                //             }
                //         }

                //     },
                //     error: function() {}
                // });



            },
            error: function() {}
        });
    }



    var checkLoaded = function() {

        if (loadCnt == 0) {
            var a = setInterval(function() {
                if (all_company_data2['kpi_value_d'] != undefined && all_company_data2['kpi_value_tq_d'] != undefined && all_company_data2['kpi_value_d'] != {} && all_company_data2['kpi_value_tq_d'] != {}) {
                    kpiDataReady = true;

                    updateAllKpi();

                    hideLoading();
                    clearInterval(a);
                }
            }, 1000)

        }
    }
}



// 获取直销渠道细分数据
// 获取所有渠道 KPI
function getAllChal2Kpi(chal_id_list) {

    all_company_data2 = {}
    all_company_data2['kpi_value_d'] = {}
    all_company_data2['kpi_value_tq_d'] = {}

    //SAL_VOL 销售量

    var kpilist = ['SAL_VOL', 'SAL_AMT', 'ADD_INC'];

    var loadCnt = 2;

    //---------- 本期
    var _innerBq = function() {
        var param1 = {
            'SOLR_CODE': 'FAC_CHAL_KPI',
            'KPI_CODE': kpilist.join(','),
            'CHAL': chal_id_list.join(','),
            'VALUE_TYPE': 'kpi_value_d', //本期
            'DATE_TYPE': 'L,M,D',
            'LIMIT': query_limit
        }

        $.ajax({
            type: 'post',
            url: "/bi/query/getchalallkpi",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param1),
            success: function(res) {

                all_company_data2['kpi_value_d'] = res;

                loadCnt--;

                checkLoaded();


            },
            error: function(xhr) {

            }
        });
    }



    //---------- 同期

    var param = {
        'SOLR_CODE': 'FAC_CHAL_KPI',
        'KPI_CODE': kpilist.join(','),
        'CHAL': chal_id_list.join(','),
        'VALUE_TYPE': 'kpi_value_tq_d', //同期
        'DATE_TYPE': 'L,M,D',
        'LIMIT': query_limit
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getchalallkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            all_company_data2['kpi_value_tq_d'] = response;
            loadCnt--;
            _innerBq();
            // checkLoaded();
        },
        error: function() {}
    });



    var checkLoaded = function() {

        if (loadCnt != 0) {
            return false;
        }
        if (loadCnt == 0) {
            setTimeout(function() {

                setBlockR1Kpi();
                selectChalType(0, $('.block_r1 .itm0').attr("data-type"));
            }, 3000)

        }
    }
}



function updateAllKpi(data, label) {
    if (!kpiDataReady) {
        return;
    }
    setBlockL1Kpi();
    setBlockL2Kpi();
    setBlockR1Kpi();
    selectChalType(0, $('.block_r1 .itm0').attr("data-type"));
    //setBlockR2Kpi();

    clearTimeout(itv_autoSwitchChal);
    itv_autoSwitchChal = setTimeout(autoSwitchChal, 10000);


    setExtLink();

    setTitleDate();

}


function getCurrentDate() {
    var date = '';
    if (date_type == 'L') {
        date = $('#main_cb_week').attr('data');
    } else if (date_type == 'M') {
        date = $('#main_cb_month').attr('data');
    } else if (date_type == 'D') {
        date = $('#main_cb_day').attr('data');
    }
    return date;
}



// 计算渠道kpi
function getKpi(chal_id, data_type, kpi_code, datetype, date) {
    var challst = all_company_data[data_type][kpi_code][datetype]['date'];
    var len = challst.length;
    for (var i = 0; i < len; i++) {
        var cobj = challst[i];
        if (cobj.chal == chal_id) {
            var datelist = cobj.date;
            var len2 = datelist.length;
            for (var j = 0; j < len2; j++) {
                var ddd = datelist[j];
                if (ddd.date == date) {
                    return ddd.value;
                }
            }
            break;
        }
    }
    return 0;
}

// 计算ota渠道kpi
function getOtaKpi(chal_id, data_type, kpi_code, datetype, date) {
    var challst = all_ota_data[data_type][kpi_code][datetype]['date'];
    var len = challst.length;
    for (var i = 0; i < len; i++) {
        var cobj = challst[i];
        if (cobj.chal == chal_id) {
            var datelist = cobj.date;
            var len2 = datelist.length;
            for (var j = 0; j < len2; j++) {
                var ddd = datelist[j];
                if (ddd.date == date) {
                    return ddd.value;
                }
            }
            break;
        }
    }
    return 0;
}

// 只包含直销的
function getKpi2(chal_id, data_type, kpi_code, datetype, date) {
    // console.log(all_company_data2[data_type][kpi_code], kpi_code)
    if (all_company_data2[data_type][kpi_code] == undefined) {
        return false;
    }
    var challst = all_company_data2[data_type][kpi_code][datetype]['date'];
    var len = challst.length;
    for (var i = 0; i < len; i++) {
        var cobj = challst[i];
        if (cobj.chal == chal_id) {
            var datelist = cobj.date;
            var len2 = datelist.length;
            for (var j = 0; j < len2; j++) {
                var ddd = datelist[j];
                if (ddd.date == date) {
                    return ddd.value;
                }
            }
            break;
        }
    }
    return 0;
}

function calChalKpi(compcode, datetype, date) {
    //ADD_INC 燃油加价
    //SAL_VOL 销售量
    //SAL_AMT 销售额(不含油)
    //SAL_ARP_FEE 机场建设费

    /*
    CHAL_CODE:"AGENT"
    CHAL_FLAG:"1"
    CHAL_LVL:"1"
    CHAL_NAME:"代理人"
    COMP_CHAL_ID:"200018"
    SORT:"4"
    */
    var chals = all_company_chal[compcode];
    var chal_ids_flag0 = [];
    var chal_ids_flag1 = [];


    for (var chal_code in chals) {
        var obj = chals[chal_code];
        var chal_id = obj.COMP_CHAL_ID;

        if (obj.CHAL_FLAG == 0) {
            // 直销
            chal_ids_flag0.push(chal_id);
        } else {
            // 分销
            chal_ids_flag1.push(chal_id);
        }
    }



    var sal_vol0 = 0; //直销 销售量
    var sal_vol0_tq = 0; //直销 销售量 同期
    var sal_vol0_sq = 0; //直销 销售量 上期
    var sal_vol1 = 0; //分销 销售量
    var sal_vol1_tq = 0; //分销 销售量 同期
    var sal_vol1_sq = 0; //分销 销售量 上期

    var sal_amt0 = 0; //直销 销售额(含油)
    var sal_amt0_tq = 0; //直销 销售额(含油) 同期
    var sal_amt0_sq = 0; //直销 销售额(含油) 上期
    var sal_amt1 = 0; //分销 销售额(含油)
    var sal_amt1_tq = 0; //分销 销售额(含油) 同期
    var sal_amt1_sq = 0; //分销 销售额(含油) 上期


    // 直销
    for (var i = chal_ids_flag0.length - 1; i >= 0; i--) {
        var chal_id = chal_ids_flag0[i];

        var val = getKpi(chal_id, 'kpi_value_d', 'SAL_VOL', datetype, date);
        if (!isNaN(val)) {
            sal_vol0 += Number(val);
        }

        var val = getKpi(chal_id, 'kpi_value_tq_d', 'SAL_VOL', datetype, date);
        if (!isNaN(val)) {
            sal_vol0_tq += Number(val);
        }

        var val = getKpi(chal_id, 'kpi_value_sq_d', 'SAL_VOL', datetype, date);
        if (!isNaN(val)) {
            sal_vol0_sq += Number(val);
        }

        var val1 = getKpi(chal_id, 'kpi_value_d', 'SAL_AMT', datetype, date);
        var val2 = getKpi(chal_id, 'kpi_value_d', 'ADD_INC', datetype, date);
        if ((!isNaN(val1)) && (!isNaN(val2))) {
            sal_amt0 += Number(val1) + Number(val2);
        }


        var val1 = getKpi(chal_id, 'kpi_value_tq_d', 'SAL_AMT', datetype, date);
        var val2 = getKpi(chal_id, 'kpi_value_tq_d', 'ADD_INC', datetype, date);
        if ((!isNaN(val1)) && (!isNaN(val2))) {
            sal_amt0_tq += Number(val1) + Number(val2);
        }


        var val1 = getKpi(chal_id, 'kpi_value_sq_d', 'SAL_AMT', datetype, date);
        var val2 = getKpi(chal_id, 'kpi_value_sq_d', 'ADD_INC', datetype, date);
        if ((!isNaN(val1)) && (!isNaN(val2))) {
            sal_amt0_sq += Number(val1) + Number(val2);
        }

    }

    // 分销
    // console.log(chal_ids_flag0, chal_ids_flag1)
    for (var i = chal_ids_flag1.length - 1; i >= 0; i--) {
        var chal_id = chal_ids_flag1[i];

        var val = getKpi(chal_id, 'kpi_value_d', 'SAL_VOL', datetype, date);
        if (!isNaN(val)) {
            sal_vol1 += Number(val);
        }

        var val = getKpi(chal_id, 'kpi_value_tq_d', 'SAL_VOL', datetype, date);
        if (!isNaN(val)) {
            sal_vol1_tq += Number(val);
        }

        var val = getKpi(chal_id, 'kpi_value_sq_d', 'SAL_VOL', datetype, date);
        if (!isNaN(val)) {
            sal_vol1_sq += Number(val);
        }

        //var val = getKpi(chal_id, 'kpi_value_d', 'SAL_AMT', datetype, date);
        var val1 = getKpi(chal_id, 'kpi_value_d', 'SAL_AMT', datetype, date);
        var val2 = getKpi(chal_id, 'kpi_value_d', 'ADD_INC', datetype, date);
        //if(!isNaN(val)){
        //    sal_amt1 += Number(val);
        //}
        if ((!isNaN(val1)) && (!isNaN(val2))) {
            sal_amt1 += Number(val1) + Number(val2);
        }


        //var val = getKpi(chal_id, 'kpi_value_tq_d', 'SAL_AMT', datetype, date);
        //if(!isNaN(val)){
        //    sal_amt1_tq += Number(val);
        //}

        //var val = getKpi(chal_id, 'kpi_value_sq_d', 'SAL_AMT', datetype, date);
        //if(!isNaN(val)){
        //    sal_amt1_sq += Number(val);
        //}

        var val1 = getKpi(chal_id, 'kpi_value_tq_d', 'SAL_AMT', datetype, date);
        var val2 = getKpi(chal_id, 'kpi_value_tq_d', 'ADD_INC', datetype, date);
        if ((!isNaN(val1)) && (!isNaN(val2))) {
            sal_amt1_tq += Number(val1) + Number(val2);
        }

        var val1 = getKpi(chal_id, 'kpi_value_sq_d', 'SAL_AMT', datetype, date);
        var val2 = getKpi(chal_id, 'kpi_value_sq_d', 'ADD_INC', datetype, date);
        if ((!isNaN(val1)) && (!isNaN(val2))) {
            sal_amt1_sq += Number(val1) + Number(val2);
        }

    }

    // 直销量占比 本期
    var per = Math.round(sal_vol0 / (sal_vol0 + sal_vol1) * 1000) / 10;

    // 直销量占比 同期
    var per_tq = Math.round(sal_vol0_tq / (sal_vol0_tq + sal_vol1_tq) * 1000) / 10;

    // 直销量占比 上期
    var per_sq = Math.round(sal_vol0_sq / (sal_vol0_sq + sal_vol1_sq) * 1000) / 10;

    // 同比=(本期-同期)÷同期×100%
    var per_tq_tq = Math.round((per - per_tq) / per_tq * 1000) / 10;

    // 环比=(本期-上期)/上期×100%
    var per_sq_sq = Math.round((per - per_sq) / per_sq * 1000) / 10;

    // 直销额占比 本期
    var per_amt = Math.round(sal_amt0 / (sal_amt0 + sal_amt1) * 1000) / 10;
    // console.log("per_amt:"+per_amt)

    // 直销额占比 同期
    var per_tq_amt = Math.round(sal_amt0_tq / (sal_amt0_tq + sal_amt1_tq) * 1000) / 10;

    // 直销额占比 上期
    var per_sq_amt = Math.round(sal_amt0_sq / (sal_amt0_sq + sal_amt1_sq) * 1000) / 10;
    // console.log("per_sq_amt:"+per_sq_amt)

    // 同比=(本期-同期)÷同期×100%
    var per_tq_tq_amt = Math.round((per_amt - per_tq_amt) / per_tq_amt * 1000) / 10;

    // 环比=(本期-上期)/上期×100%
    var per_sq_sq_amt = Math.round((per_amt - per_sq_amt) / per_sq_amt * 1000) / 10;
    // console.log("per_sq_sq_amt:"+per_sq_sq_amt)

    var res = {};

    res.sal_vol0 = sal_vol0;
    res.sal_vol0_tq = sal_vol0_tq;
    res.sal_vol0_sq = sal_vol0_sq;

    res.sal_vol1 = sal_vol1;
    res.sal_vol1_tq = sal_vol1_tq;
    res.sal_vol1_sq = sal_vol1_sq;

    res.per = per;
    res.per_tq = per_tq;
    res.per_sq = per_sq;
    res.per_tq_tq = per_tq_tq;
    res.per_sq_sq = per_sq_sq;

    res.sal_amt0 = sal_amt0;
    res.sal_amt0_tq = sal_amt0_tq;
    res.sal_amt0_sq = sal_amt0_sq;

    res.per_amt = per_amt;
    res.per_tq_amt = per_tq_amt;
    res.per_sq_amt = per_sq_amt;
    res.per_tq_tq_amt = per_tq_tq_amt;
    res.per_sq_sq_amt = per_sq_sq_amt;

    return res;

}

function setBlockL1Kpi() {

    if (companylist.length == 0) {
        setTimeout(setBlockL1Kpi, 0);
        return;
    }

    // check url hash
    var hash = window.location.hash.substr(1);
    if (hash && companyCode2Name[hash] != undefined) {
        current_company_code = hash;
    } else {
        current_company_code = parent_company;
    }



    var date = getCurrentDate();

    var res = calChalKpi(current_company_code, date_type, date);



    var sal_vol0 = res.sal_vol0; //直销 销售量
    var sal_vol0_tq = res.sal_vol0_tq; //直销 销售量 同期
    var sal_vol0_sq = res.sal_vol0_sq; //直销 销售量 上期
    var sal_vol1 = res.sal_vol1; //分销 销售量
    var sal_vol1_tq = res.sal_vol1_tq; //分销 销售量 同期
    var sal_vol1_sq = res.sal_vol1_sq; //分销 销售量 上期

    var sal_amt0 = res.sal_amt0; //直销 销售额(含油)
    var sal_amt0_tq = res.sal_amt0_tq; //直销 销售额(含油) 同期
    var sal_amt0_sq = res.sal_amt0_sq; //直销 销售额(含油) 上期

    var per_tq_tq = res.per_tq_tq;
    var per_sq_sq = res.per_sq_sq;

    // console.log(current_company_code, ":", res.sal_amt0, res.sal_amt1, res.sal_amt0 + res.sal_amt1)

    // 直销额占比 本期
    var sal_ratio = res.per_amt;
    if (!isNaN(sal_ratio)) {
        $('#per_SAL_VOL .val').text(sal_ratio);
        $('#per_SAL_VOL .sub').show();
    } else {
        $('#per_SAL_VOL .val').text('-');
        $('#per_SAL_VOL .sub').hide();
    }
    $('#per_SAL_VOL').show();

    // 同比=(本期-同期)÷同期×100%
    var rto = res.per_tq_tq_amt; //sal_vol0_tq > 0 ? Math.round((sal_vol0-sal_vol0_tq)/sal_vol0_tq * 10000)/100 : 0;
    var vol_tb = rto;
    if (!isNaN(rto)) {
        $('#per_tq_SAL_VOL .val').text(rto + '%');
    } else {
        $('#per_tq_SAL_VOL .val').text('-');
    }

    if (rto > 0) {
        $('#per_tq_SAL_VOL .ico_up').show();
        $('#per_tq_SAL_VOL .ico_down').hide();
    } else if (rto < 0) {
        $('#per_tq_SAL_VOL .ico_up').hide();
        $('#per_tq_SAL_VOL .ico_down').show();
    } else {
        $('#per_tq_SAL_VOL .ico_up').hide();
        $('#per_tq_SAL_VOL .ico_down').hide();
    }
    $('#per_tq_SAL_VOL').show();

    // 环比=(本期-上期)/上期×100%
    var rto = res.per_sq_sq_amt; //sal_vol0_sq > 0 ? Math.round((sal_vol0-sal_vol0_sq)/sal_vol0_sq * 10000)/100 : 0;
    var vol_hb = rto;
    if (!isNaN(rto)) {
        $('#per_sq_SAL_VOL .val').text(rto + '%');
    } else {
        $('#per_sq_SAL_VOL .val').text('-');
    }
    if (rto > 0) {
        $('#per_sq_SAL_VOL .ico_up').show();
        $('#per_sq_SAL_VOL .ico_down').hide();
    } else if (rto < 0) {
        $('#per_sq_SAL_VOL .ico_up').hide();
        $('#per_sq_SAL_VOL .ico_down').show();
    } else {
        $('#per_sq_SAL_VOL .ico_up').hide();
        $('#per_sq_SAL_VOL .ico_down').hide();
    }
    $('#per_sq_SAL_VOL').show();


    // 直销量
    if (!isNaN(sal_vol0)) {
        $('#val_SAL_VOL .val').text(formatCurrency(sal_vol0), 0);
        $('#val_SAL_VOL .sub').show();
    } else {
        $('#val_SAL_VOL .val').text('-');
        $('#val_SAL_VOL .sub').hide();
    }

    // 环比=(本期-上期)/上期×100%
    var per = sal_vol0_sq > 0 ? Math.round((sal_vol0 - sal_vol0_sq) / sal_vol0_sq * 10000) / 100 : 0;
    var sal_vol_hb = per;
    if (!isNaN(per)) {
        $('#val_SAL_VOL .hb .per').text(per);
        $('#val_SAL_VOL .hb .persub').show();
    } else {
        $('#val_SAL_VOL .hb .per').text('-');
        $('#val_SAL_VOL .hb .persub').hide();
    }
    if (per > 0) {
        $('#val_SAL_VOL .hb .green').show();
        $('#val_SAL_VOL .hb .red').hide();
    } else if (per < 0) {
        $('#val_SAL_VOL .hb .green').hide();
        $('#val_SAL_VOL .hb .red').show();
    } else {
        $('#val_SAL_VOL .hb .green').hide();
        $('#val_SAL_VOL .hb .red').hide();
    }

    // 同比=(本期-同期)/同期×100%
    var per = sal_vol0_tq > 0 ? Math.round((sal_vol0 - sal_vol0_tq) / sal_vol0_tq * 10000) / 100 : 0;
    var sal_vol_tb = per;
    if (!isNaN(per)) {
        $('#val_SAL_VOL .tb .per').text(per);
        $('#val_SAL_VOL .tb .persub').show();
    } else {
        $('#val_SAL_VOL .tb .per').text('-');
        $('#val_SAL_VOL .tb .persub').hide();
    }
    if (per > 0) {
        $('#val_SAL_VOL .tb .green').show();
        $('#val_SAL_VOL .tb .red').hide();
    } else if (per < 0) {
        $('#val_SAL_VOL .tb .green').hide();
        $('#val_SAL_VOL .tb .red').show();
    } else {
        $('#val_SAL_VOL .tb .green').hide();
        $('#val_SAL_VOL .tb .red').hide();
    }

    $('#val_SAL_VOL').show();


    // 直销额
    if (!isNaN(sal_amt0)) {
        $('#val_SAL_AMT .val').text(formatCurrency(sal_amt0), 0);
        $('#val_SAL_AMT .sub').show();
    } else {
        $('#val_SAL_AMT .val').text('-');
        $('#val_SAL_AMT .sub').hide();
    }

    // 环比=(本期-上期)/上期×100%
    var per = sal_amt0_sq > 0 ? Math.round((sal_amt0 - sal_amt0_sq) / sal_amt0_sq * 10000) / 100 : 0;
    var sal_amt_hb = per;
    if (!isNaN(per)) {
        $('#val_SAL_AMT .hb .per').text(per);
        $('#val_SAL_AMT .hb .persub').show();
    } else {
        $('#val_SAL_AMT .hb .per').text('-');
        $('#val_SAL_AMT .hb .persub').hide();
    }
    if (per > 0) {
        $('#val_SAL_AMT .hb .green').show();
        $('#val_SAL_AMT .hb .red').hide();
    } else if (per < 0) {
        $('#val_SAL_AMT .hb .green').hide();
        $('#val_SAL_AMT .hb .red').show();
    } else {
        $('#val_SAL_AMT .hb .green').hide();
        $('#val_SAL_AMT .hb .red').hide();
    }

    // 同比=(本期-同期)/同期×100%
    var per = sal_amt0_tq > 0 ? Math.round((sal_amt0 - sal_amt0_tq) / sal_amt0_tq * 10000) / 100 : 0;
    var sal_amt_tb = per;
    if (!isNaN(per)) {
        $('#val_SAL_AMT .tb .per').text(per);
        $('#val_SAL_AMT .tb .persub').show();
    } else {
        $('#val_SAL_AMT .tb .per').text('-');
        $('#val_SAL_AMT .tb .persub').hide();
    }
    if (per > 0) {
        $('#val_SAL_AMT .tb .green').show();
        $('#val_SAL_AMT .tb .red').hide();
    } else if (per < 0) {
        $('#val_SAL_AMT .tb .green').hide();
        $('#val_SAL_AMT .tb .red').show();
    } else {
        $('#val_SAL_AMT .tb .green').hide();
        $('#val_SAL_AMT .tb .red').hide();
    }

    $('#val_SAL_AMT').show();


    // 语音播报
    function onAudioTplLoad(tplobj) {

        // {COMP} {DATE} 直销量占比{RATIO}，同比{TB1}，环比{HB1}。 直销量{SAL_VOL}张，同比{TB2}，环比{HB2}。 直销额{SAL_AMT}元，同比{TB3}，环比{HB3}。
        var tpl = tplobj.txt;

        //--
        var compname = companyCode2Name[current_company_code];
        tpl = tpl.replace(/{COMP}/g, compname);

        //--
        var date = getCurrentDate();
        var datelabel = '';
        if (date_type == 'L') {
            // Week: *********
            //var label = date.substr(0,4) + '年第' + Number(date.substr(4,3)) + '周';
            //20170419 双双要求：显示的周数+1
            datelabel = date.substr(0, 4) + '年' + '第' + (Number(date.substr(4, 3)) + 1) + '周';
        } else if (date_type == 'M') {
            datelabel = date.substr(0, 4) + '年' + Number(date.substr(4, 2)) + '月';
        } else if (date_type == 'D') {
            datelabel = date.substr(0, 4) + '年' + Number(date.substr(4, 2)) + '月' + Number(date.substr(6, 2)) + '日';
        }
        tpl = tpl.replace(/{DATE}/g, datelabel);

        //--
        tpl = tpl.replace(/{RATIO}/g, sal_ratio + '%');

        //--
        tpl = tpl.replace(/{TB1}/g, ratioTemplete(vol_tb));
        tpl = tpl.replace(/{HB1}/g, ratioTemplete(vol_hb));

        //--
        tpl = tpl.replace(/{SAL_VOL}/g, formatCurrency(trimDecimal(sal_vol0), 2));

        //--
        tpl = tpl.replace(/{TB2}/g, ratioTemplete(sal_vol_tb));
        tpl = tpl.replace(/{HB2}/g, ratioTemplete(sal_vol_hb));

        //--
        tpl = tpl.replace(/{SAL_AMT}/g, formatCurrency(Math.round(sal_amt0) * 10000, 2));

        //--
        tpl = tpl.replace(/{TB3}/g, ratioTemplete(sal_amt_tb));
        tpl = tpl.replace(/{HB3}/g, ratioTemplete(sal_amt_hb));

        text2audio(tpl, true);

    }

    stopAudio();
    getAudioTemplate('A2.4-general', onAudioTplLoad);
}



function setBlockL2Kpi() {


    // ---------------------------
    var chart_id = 'chart_l2';
    var kpi_code = '';

    // ---------------------------

    var avaliable_logo = ['HU', 'JD', 'GS', 'PN', '8L', 'HX', 'FU', 'UQ', 'Y8', 'GX', '9H', 'GT'];

    var xAxisData = [];
    var data_s1 = [];

    var yAxisMaxVal = 0;

    var date = getCurrentDate();

    // s1
    var max_sal_vol0 = 0;
    var max_sal_amt0 = 0;
    var len = companylist.length;
    for (var j = 0; j < len; j++) {
        var obj = companylist[j];
        var compcode = obj.code;
        if (compcode != parent_company) {

            var res = calChalKpi(compcode, date_type, date);
            var sal_vol0 = res.sal_vol0; // 直销量
            if (max_sal_vol0 < sal_vol0) {
                max_sal_vol0 = sal_vol0;
            }
            var sal_amt0 = res.sal_amt0; // 直销量
            if (max_sal_amt0 < sal_amt0) {
                max_sal_amt0 = sal_amt0;
            }
        }
    }

    for (var j = 0; j < len; j++) {
        
        var obj = companylist[j];
        var compcode = obj.code;
        if (compcode != parent_company) {

            var res = calChalKpi(compcode, date_type, date);

            // 直销量占比 本期
            var per = res.per
            // 直销量
            var sal_vol0 = res.sal_vol0;
            // 直销额
            var sal_amt0 = res.sal_amt0;
            // console.log([compcode, sal_amt0])
            if (isNaN(per)) {
                per = 0;
            }

            xAxisData.push(compcode);

            var logoimg;
            var nologo = false;
            if (avaliable_logo.indexOf(compcode) > -1) {
                logoimg = 'image://img/chart_comp_' + compcode + '.png';
            } else {
                logoimg = 'image://img/chart_comp_EMPTY.png';
                nologo = true;
            }

            var size = Math.sqrt(pageZoomScale * (sal_vol0 / max_sal_vol0 * 100) * 200000),
                size = Math.sqrt(size);
            //size = Math.max(size, 30);
            size = Math.min(size, 90);
            if (per == 0) {
                //size = 20;
            }

            var _size = Math.sqrt(pageZoomScale * (sal_amt0 / max_sal_amt0 * 100) * 200000),
                _size = Math.sqrt(_size);
            _size = Math.min(_size, 90);

            yAxisMaxVal = Math.max(yAxisMaxVal, per);
            // console.log("solomonwu===111")
            // console.log([compcode,size,_size])
            data_s1.push({
                name: compcode,
                // value: per,// 直销量占比
                value: res.per_amt, //直销额占比
                inc: res.sal_amt0, // 直销额
                num: res.sal_vol0, // 直销量
                // hb: res.per_sq_sq,// 直销占比 环比
                hb: res.per_sq_sq_amt, //直销额占比 环比

                symbol: logoimg,
                // symbolSize: size,
                symbolSize: _size,
                label: {
                    normal: {
                        show: nologo,
                        position: 'inside',
                        formatter: '{b}',
                        textStyle: {
                            color: '#004994',
                            fontWeight: 'bold',
                            fontSize: '22',
                        }
                    }
                },
            });

        }
    }

    // console.log("solomonwu===========")
    // console.log(data_s1)

    var chart = echarts.init(document.getElementById(chart_id));

    var option = {
        tooltip: {
            show: true,
            formatter: function(params, ticket, callback) {
                var data = params.data;
                var compcode = data.name;
                var comname = companyCode2Name[compcode];

                var inc = formatCurrency(data.inc, 0) + '万元';
                var num = formatCurrency(data.num, 0) + '张';
                var per = data.value + '%';
                var hb = data.hb;
                if (hb > 0) {
                    hb = hb + '%' + '<span class="green" style="color:#00ff0c !important;">↑</span>';
                } else if (hb < 0) {
                    hb = hb + '%' + '<span class="red">↓</span>';
                } else {
                    hb = '0%';
                }

                var html = '';
                html += '<div class="bold fs16" style="margin:5px; min-width: 160px;">' + comname + '</div>';
                html += '<div class="fs14"><span style="display:inline-block; width:70px; text-align:right; margin-right:5px;">直销额 </span>' + inc + '</div>';
                html += '<div class="fs14"><span style="display:inline-block; width:70px; text-align:right; margin-right:5px;">直销量 </span>' + num + '</div>';
                html += '<div class="fs14"><span style="display:inline-block; width:70px; text-align:right; margin-right:5px;">直销额占比 </span>' + per + '</div>';
                html += '<div class="fs14"><span style="display:inline-block; width:70px; text-align:right; margin-right:5px;">占比的环比 </span>' + hb + '</div>';
                return html;
            },
            extraCssText: "color:#021e55; padding:5px 10px 10px 10px; box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);background: rgb(178,225,254);background: -moz-linear-gradient(top,  rgba(178,225,254,1) 0%, rgba(100,192,250,1) 100%);background: -webkit-linear-gradient(top,  rgba(178,225,254,1) 0%,rgba(100,192,250,1) 100%);background: linear-gradient(to bottom,  rgba(178,225,254,1) 0%,rgba(100,192,250,1) 100%);filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#b2e1fe', endColorstr='#64c0fa',GradientType=0 );",
            backgroundColor: '',

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 40,
            left: 60,
            right: 1,
            bottom: 30,
        },
        xAxis: [{
            type: 'category',
            name: '',
            nameLocation: 'start',
            data: xAxisData,
            boundaryGap: true, // 显示x轴两边的留白
            offset: 6,
            nameTextStyle: {
                color: '#45a5f6',
                fontSize: 11 + fontSizeDiff()
            },
            axisLine: {
                lineStyle: {
                    width: 1,
                    color: '#45a5f6' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#ffffff', // 轴标签颜色大小
                    fontSize: 14 + fontSizeDiff(),
                },
                //formatter: function (value, index) {
                //    return chartDateFormatter(value, index);
                //},
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(69,165,246,0.1)'] // 分割线颜色
                }
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '直销额占比       ',
            nameLocation: 'end',
            min: 0,
            //max: 100,
            interval: 10,
            nameTextStyle: {
                color: '#45a5f6',
                fontSize: 16 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#45a5f6',
                    fontSize: 14 + fontSizeDiff(),
                    align: 'left',
                },
                margin: 60,
                formatter: '{value}%',
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(69,165,246,0.1)'] // 分割线颜色
                }
            }
        }],
        series: [{
            name: '',
            type: 'line',
            smooth: false,
            data: data_s1,

            lineStyle: {
                normal: {
                    color: 'rgba(0,0,0,0)', //不显示折线
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: '',
                    opacity: 1 //显示折线拐点
                }
            },
            markLine: {
                silent: true,
                data: [{
                    name: '',
                    yAxis: 30,
                    lineStyle: {
                        normal: {
                            color: '#45a5f6',
                            type: 'dashed',
                            opacity: 0.5
                        }
                    },
                }, {
                    name: '',
                    yAxis: 70,
                    lineStyle: {
                        normal: {
                            color: '#45a5f6',
                            type: 'dashed',
                            opacity: 0.5
                        }
                    },
                }],
                symbol: '',
                symbolSize: 0,
                label: {
                    normal: {
                        show: false,
                        position: 'start',
                        formatter: '{c}%',
                    }
                },
            },
        }, ]
    };

    chart.setOption(option);



    // ----------------- 良好、一般、不良 标记 -----------------
    /*
    var yAxisGrids = Math.ceil(yAxisMaxVal/10);

    var marginTop = 50;
    var marginBot = 20;
    var line_h = 16;
    var font_h = line_h*4;

    var width = Math.round($('#chart_bg').width());
    var height = Math.round($('#chart_bg').height());
    $('#chart_cvs').attr('width', width);
    $('#chart_cvs').attr('height', height);

    var grid_h = (height-marginTop-marginBot)/(yAxisGrids)

    var canvas = document.getElementById('chart_cvs');
    var context = canvas.getContext('2d');

    context.font = "14px Microsoft Yahei";

    var xx = width-18;

    var yy = height-marginBot-grid_h*3+(grid_h*3-font_h)/2;
    context.fillStyle = '#ba4d3b';
    context.fillText("情", xx, yy);
    context.fillText("况", xx, yy+line_h);
    context.fillText("不", xx, yy+line_h*2);
    context.fillText("良", xx, yy+line_h*3);
    
    if(yAxisGrids >= 7){
        yy = height-marginBot-grid_h*7+(grid_h*4-font_h)/2;
    }else{
        yy = height-marginBot-grid_h*yAxisGrids+(grid_h*(yAxisGrids-3)-font_h)/2;
    }
    context.fillStyle = '#389ff7';
    context.fillText("情", xx, yy);
    context.fillText("况", xx, yy+line_h);
    context.fillText("一", xx, yy+line_h*2);
    context.fillText("般", xx, yy+line_h*3);

    if(yAxisGrids > 7){
        yy = height-marginBot-grid_h*yAxisGrids+(grid_h*(yAxisGrids-7)-font_h)/2;
        context.fillStyle = '#2fab4f';
        context.fillText("情", xx, yy);
        context.fillText("况", xx, yy+line_h);
        context.fillText("良", xx, yy+line_h*2);
        context.fillText("好", xx, yy+line_h*3);
    }
    */

    // ----------------- 说明文字 -----------------

    var width = Math.round($('#chart_bg').width());
    var height = Math.round($('#chart_bg').height());
    $('#chart_cvs').attr('width', width);
    $('#chart_cvs').attr('height', height);


    var canvas = document.getElementById('chart_cvs');
    var context = canvas.getContext('2d');

    context.font = "14px Microsoft Yahei";
    context.textAlign = "right";

    var xx = width - 22;
    var yy = 23;
    context.fillStyle = '#45a5f6';
    // context.fillText("注：球大小表示直销量，高低表示直销量占比", xx, yy);
    context.fillText("注：球大小表示直销额，高低表示直销额占比", xx, yy);



    // ----------------- 自动切换指标介绍 -----------------

    var currentDataIndex = 0;
    // 自动循环显示每个公司的详细情况tootip
    function autoSwitchTooltip() {
        clearTimeout(chart.itv_autoswitch);

        if (!autoSwitch) {
            chart.itv_autoswitch = setTimeout(autoSwitchTooltip, 10);
            return;
        }

        chart.dispatchAction({
            type: 'showTip',
            // 系列的 index
            seriesIndex: 0,
            // 数据的 index
            dataIndex: currentDataIndex,
        })

        if (currentDataIndex < data_s1.length - 1) {
            currentDataIndex++;
        } else {
            currentDataIndex = 0;
        }

        chart.itv_autoswitch = setTimeout(autoSwitchTooltip, 5000);

    }


    // 鼠标移到公司上，停止循环显示每个公司的详细情况tootip
    chart.off('mouseover');
    chart.on('mouseover', function(params) {
        clearTimeout(chart.itv_autoswitch);
    });

    // 鼠标移开，继续循环显示每个公司的详细情况tootip
    chart.off('mouseout');
    chart.on('mouseout', function(params) {
        autoSwitchTooltip();
    });

    chart.off('click');
    chart.on('click', function(params) {
        var compcode = params.name;
        if (usersCompayCodeList.indexOf(compcode) > -1) {
            switchCompany(compcode);
        }
        //console.log(params);
    });


    autoSwitchTooltip();
}

// OTA渠道曲线图
function drawOTAChart() {
    // console.log("OTAOTA");
    // console.log(all_company_ota);
    // console.log(all_company_data);
    // console.log("~~~~~");
    var date = getCurrentDate();
    var otalist = all_company_ota[current_company_code];
    var valueArr = []
    for (var i = 0; i < otalist.length; i++) {
        var a = otalist[i];

        var val1 = getOtaKpi(a.COMP_CHAL_ID, 'kpi_value_d', 'SAL_AMT', date_type, date);
        var val2 = getOtaKpi(a.COMP_CHAL_ID, 'kpi_value_d', 'ADD_INC', date_type, date);
        valueArr.push({
            name: a.CHAL_NAME,
            value: Number(val1) + Number(val2),

        })
    }
    var arr = $.grep(valueArr, function(ele, index) {
        return ele.value > 0;
    })
    arr.sort(function(a, b) {
        return b.value - a.value;
    })
    var xAxisData = [];
    var data_s1 = [];
    for (var i = 0; i < arr.length; i++) {
        xAxisData.push(arr[i].name);
        data_s1.push(arr[i].value);
    }
    var colors = [
        ['#00c311', '#007892'], //柱状图渐变颜色
        ['#6ec7f6', '#03a5ed'], //柱状图渐变颜色
        ['#fbc900', ''], //折线颜色
    ];
    var chart = echarts.init(document.getElementById("chart_r2c"));

    var option = {
        tooltip: {
            show: true,
            formatter: function(params, ticket, callback) {
                return params.seriesName + '<br>' + params.name + ': ' + Number(params.value).toFixed(2) + '万元';
            },
            extraCssText: "color:#021e55; padding:5px 10px 6px 10px; box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);background: rgb(178,225,254);background: -moz-linear-gradient(top,  rgba(178,225,254,1) 0%, rgba(100,192,250,1) 100%);background: -webkit-linear-gradient(top,  rgba(178,225,254,1) 0%,rgba(100,192,250,1) 100%);background: linear-gradient(to bottom,  rgba(178,225,254,1) 0%,rgba(100,192,250,1) 100%);filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#b2e1fe', endColorstr='#64c0fa',GradientType=0 );",
            backgroundColor: '',

        },
        legend: {
            show: true,
            orient: 'horizontal',
            //x: 'center',
            right: 10,
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: '#45a5f6',
                fontSize: 12 + fontSizeDiff()
            },
            data: [{
                name: '本期',
                icon: 'circle',
            }]
        },
        grid: {
            top: 36,
            left: 70,
            right: 0,
            bottom: 55,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: true, // 去掉x轴两边的留白, 空着不显示line难看
            nameTextStyle: {
                color: '#45a5f6'
            },
            axisLine: {
                lineStyle: {
                    color: '#45a5f6' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 30,
                textStyle: {
                    color: '#45a5f6', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
                formatter: function(value, index) {
                    return chartDateFormatter(value, index);
                },
            },
            splitLine: {
                show: false, // 不显示刻度线
            },
            axisTick: {
                show: false, // 不显示刻度线
            }

        }],
        yAxis: [{
            type: 'value',
            name: '直销额',
            nameLocation: 'end',
            //min: 0,
            //max: 100,
            //interval: 10,
            nameTextStyle: {
                color: '#45a5f6',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#45a5f6',
                    fontSize: 12 + fontSizeDiff(),
                    align: 'left',
                },
                margin: 30,
                formatter: '{value}万元',
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(69,165,246,0.1)'] // 分割线颜色
                }
            }
        }],
        series: [{
            name: '本期',
            type: 'bar',
            barWidth: 15,
            data: data_s1,
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    formatter: function(param) {
                        return Number(param.value).toFixed(0);
                    },
                    color: colors[0][0],
                    offset: [5, 0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[0][0]
                        }, {
                            offset: 1,
                            color: colors[0][1]
                        }]),
                }
            },
        }]
    };

    chart.setOption(option);

}

var chal0_for_sort; //查询出来所有直销渠道和销售量，然后排序，拿到top5

// 设置销售渠道占比 TOP5
function setBlockR1Kpi() {
    if (!kpiDataReady || all_company_data2 == undefined) {
        return;
    }

    //SAL_VOL 销售量

    /*
    CHAL_CODE:"AGENT"
    CHAL_FLAG:"1"
    CHAL_LVL:"1"
    CHAL_NAME:"代理人"
    COMP_CHAL_ID:"200018"
    SORT:"4"
    */


    var chal_id2code = {};
    var chal_cnt = {};

    // console.log("xingxingxing")
    // console.log(all_company_chal[current_company_code])
    // console.log(all_company_chal2[current_company_code])
    var chals = all_company_chal[current_company_code];
    var chals2 = all_company_chal2[current_company_code];
    var chal_ids_flag0 = [];
    var chal_ids_flag = [];
    // 分销+直销
    for (var chal_code in chals) {
        var obj = chals[chal_code];
        var chal_id = obj.COMP_CHAL_ID;
        chal_ids_flag.push(chal_id);

    }
    // 直销
    for (var chal_code in chals2) {
        var obj = chals2[chal_code];
        var chal_id = obj.COMP_CHAL_ID;
        if (obj.CHAL_FLAG == 0) {
            // 直销
            chal_ids_flag0.push(chal_id);
            chal_id2code[chal_id] = chal_code;
        }

    }


    var sal_vol0 = 0; //直销 销售额
    var sal_vol = 0; //分销+直销 销售额

    var date = getCurrentDate();

    chal0_for_sort = [];

    // 直销
    for (var i = chal_ids_flag0.length - 1; i >= 0; i--) {
        var chal_id = chal_ids_flag0[i];

        var val = getKpi2(chal_id, 'kpi_value_d', 'SAL_AMT', date_type, date);
        var val2 = getKpi2(chal_id, 'kpi_value_d', 'ADD_INC', date_type, date);
        var val5 = getKpi2(chal_id, 'kpi_value_tq_d', 'SAL_AMT', date_type, date);
        var val6 = getKpi2(chal_id, 'kpi_value_tq_d', 'ADD_INC', date_type, date);
        var val3 = getKpi2(chal_id, 'kpi_value_d', 'SAL_VOL', date_type, date);
        var val4 = getKpi2(chal_id, 'kpi_value_tq_d', 'SAL_VOL', date_type, date);
        if ((!isNaN(val)) && (!isNaN(val2))) {
            sal_vol0 += Number(val) + Number(val2);
            //
        }

        if ((!isNaN(val)) && (!isNaN(val2))) {
            var chal_code = chal_id2code[chal_id];
            chal0_for_sort.push({
                code: chal_code,
                name: chals2[chal_code].CHAL_NAME,
                chalcode: chals2[chal_code].CHAL_CODE,
                val: Number(val) + Number(val2), // 本期直销额
                val3: Number(val3), // 本期直销量
                val2: Math.round((Number(val3) - Number(val4)) / Number(val4) * 1000) / 10, // 直销量同比
                val4: Math.round(((Number(val) + Number(val2)) - (Number(val5) + Number(val6))) / (Number(val5) + Number(val6)) * 1000) / 10
            });
        }

    }

    // 分销+直销
    for (var i = chal_ids_flag.length - 1; i >= 0; i--) {
        var chal_id = chal_ids_flag[i];

        var val = getKpi(chal_id, 'kpi_value_d', 'SAL_AMT', date_type, date);
        var val2 = getKpi(chal_id, 'kpi_value_d', 'ADD_INC', date_type, date);
        if ((!isNaN(val)) && (!isNaN(val2))) {
            sal_vol += Number(val) + Number(val2);
        }
        // console.log("top5total", val, val2, sal_vol)

    }

    chal0_for_sort.sort(function(a, b) {
        return b.val - a.val
    });


    // reset
    for (var i = 0; i < 5; i++) {
        $('.block_r1 .itm' + i + ' span').text('');
        $('.chartimg .val' + i).text('');
    }

    var len = Math.min(chal0_for_sort.length, 5);
    for (var i = 0; i < len; i++) {
        var dat = chal0_for_sort[i];
        var chal_code = dat.code;
        var val = dat.val;
        var per = Math.round(val / sal_vol * 10000) / 100;
        // console.log(chal_code, val, sal_vol, per, dat.name, chal_ids_flag, date)
        if (!isNaN(per) && per > 0) {
            $('.chartimg .val' + i).html(per + '<span class="sub">%</span>');
        } else {
            $('.chartimg .val' + i).text('-');
        }
        $('.block_r1 .itm' + i + ' span').text(dat.name);

        $('.block_r1 .itm' + i + '').attr("data-type", dat.chalcode)
        var el = '<div id="dialog' + i + '" class="float-dialog"><span>渠道名称：<span id="item0">' + dat.name + '</span></span><span>本期直销额：<span>' + dat.val.toFixed(2) + '万元</span>同比：<span>' + dat.val4 + '%<i class="' + (dat.val4 > 0 ? 'up"></i></span></span>' : 'down"></i></span></span>') + '<span>本期直销量：<span>' + dat.val3 + '张</span>同比：<span>' + dat.val2 + '%<i class="' + (dat.val2 > 0 ? 'up"></i></span></span>' : 'down"></i></span></span>') + '</div>'
        $('.block_r1 .itm' + i).find(".float-dialog").remove();
        $('.block_r1 .itm' + i).append(el);

    }
    $('.block_r1 .itm').on('mouseover', function() {

        var _data = $(this).data("id");
        $('.block_r1 .float-dialog').hide();
        setTimeout(function() {
            $("#dialog" + _data).show();
        }, 0);
        return false;
    })
    $('.block_r1').on('mouseout', function() {
        $('.block_r1 .float-dialog').hide();
        return false;
    })
    $('.block_r1 .float-dialog').on('mouseover', function() {
        $(this).hide();
        return false;
    });
    $('.block_r2').on('mouseover', function() {
        $('.block_r1 .float-dialog').hide();
    })

}


function setBlockR2Kpi() {
    if (!kpiDataReady || all_company_data2 == undefined) {
        return;
    }

    // --------------------------------- TAB1

    // ---------------------------
    var chart_id = 'chart_r2a';
    var kpi_code = '';
    var colors = [
        ['#54cf18', '#54cf18'],
        ['#1d81cd', '#1d81cd'],
    ];
    // ---------------------------

    var cur_date = getCurrentDate();

    var xAxisData = [];
    var data_s1 = [];
    var data_s2 = [];

    var dlist = [];
    if (date_type == 'L') {
        dlist = weekList;
    } else if (date_type == 'M') {
        dlist = monthList;
    } else if (date_type == 'D') {
        dlist = dayList;
    }

    for (var j = 0; j < dlist.length; j++) {
        var date = dlist[j];

        if (Number(cur_date) >= Number(date)) {

            var chal_id2code = {};

            var chals = all_company_chal[current_company_code];
            var chals2 = all_company_chal2[current_company_code];

            var chal_ids_flag0 = [];
            var chal_ids_flag = [];

            for (var chal_code in chals) {
                var obj = chals[chal_code];
                var chal_id = obj.COMP_CHAL_ID;
                // 分销+直销
                chal_ids_flag.push(chal_id);
            }

            for (var chal_code in chals2) {
                var obj = chals2[chal_code];
                var chal_id = obj.COMP_CHAL_ID;
                if (obj.CHAL_FLAG == 0) {
                    // 直销
                    chal_ids_flag0.push(chal_id);
                    //
                    chal_id2code[chal_id] = chal_code;
                }
            }

            var sal_vol0 = 0; //直销 销售量
            var sal_vol0_tq = 0; //直销 销售量 同期

            var sal_vol = 0; //分销+直销 销售量
            var sal_vol_tq = 0; //分销+直销 销售量 同期

            var sal_vol_code = 0; // 当期
            var sal_vol_code_tq = 0; // 同期

            // 直销
            for (var i = chal_ids_flag0.length - 1; i >= 0; i--) {
                var chal_id = chal_ids_flag0[i];

                var val = getKpi2(chal_id, 'kpi_value_d', 'SAL_AMT', date_type, date);
                var val2 = getKpi2(chal_id, 'kpi_value_d', 'ADD_INC', date_type, date);
                if (!isNaN(val) && !isNaN(val2)) {
                    sal_vol0 += Number(val) + Number(val2);

                    var chal_code = chal_id2code[chal_id];
                    if (selected_chal_code == chal_code) {
                        sal_vol_code = Number(val) + Number(val2);
                    }
                }

                var val = getKpi2(chal_id, 'kpi_value_tq_d', 'SAL_AMT', date_type, date);
                var val2 = getKpi2(chal_id, 'kpi_value_tq_d', 'ADD_INC', date_type, date);
                if (!isNaN(val) && !isNaN(val2)) {
                    sal_vol0_tq += Number(val) + Number(val2);

                    var chal_code = chal_id2code[chal_id];
                    if (selected_chal_code == chal_code) {
                        sal_vol_code_tq = Number(val) + Number(val2);
                    }
                }
            }


            // 分销+直销
            for (var i = chal_ids_flag.length - 1; i >= 0; i--) {
                var chal_id = chal_ids_flag[i];

                var val1 = getKpi(chal_id, 'kpi_value_d', 'SAL_AMT', date_type, date);
                var val2 = getKpi(chal_id, 'kpi_value_d', 'ADD_INC', date_type, date);
                if ((!isNaN(val1)) && (!isNaN(val2))) {
                    sal_vol += Number(val1) + Number(val2);
                }

                var val3 = getKpi(chal_id, 'kpi_value_tq_d', 'SAL_AMT', date_type, date);
                var val4 = getKpi(chal_id, 'kpi_value_tq_d', 'ADD_INC', date_type, date);
                if ((!isNaN(val3)) && (!isNaN(val4))) {
                    sal_vol_tq += Number(val3) + Number(val4);
                }
            }

            // s1
            var per = Math.round(sal_vol_code / sal_vol * 10000) / 100;
            data_s1.push(per);

            // s2
            var per = Math.round(sal_vol_code_tq / sal_vol_tq * 10000) / 100;
            data_s2.push(per);

            var label;
            if (date_type == 'L') {
                // Week: *********
                //var label = date.substr(0,4) + '年第' + Number(date.substr(4,3)) + '周';
                //20170419 双双要求：显示的周数+1
                label = '第' + (Number(date.substr(4, 3)) + 1) + '周\n' + date.substr(0, 4) + '年';
            } else if (date_type == 'M') {
                label = ' ' + Number(date.substr(4, 2)) + '月\n' + date.substr(0, 4) + '年';
            } else if (date_type == 'D') {
                label = ' ' + Number(date.substr(4, 2)) + '月' + Number(date.substr(6, 2)) + '日\n' + date.substr(0, 4) + '年';
            }
            xAxisData.push(label);

        }

    }

    xAxisData = xAxisData.slice(-6);
    data_s1 = data_s1.slice(-6);
    data_s2 = data_s2.slice(-6);


    var chart = echarts.init(document.getElementById(chart_id));

    var option = {
        tooltip: {
            show: true,
            formatter: function(params, ticket, callback) {
                return params.seriesName + '<br>' + params.name + ': ' + params.value + '%';
            },
            extraCssText: "color:#021e55; padding:5px 10px 6px 10px; box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);background: rgb(178,225,254);background: -moz-linear-gradient(top,  rgba(178,225,254,1) 0%, rgba(100,192,250,1) 100%);background: -webkit-linear-gradient(top,  rgba(178,225,254,1) 0%,rgba(100,192,250,1) 100%);background: linear-gradient(to bottom,  rgba(178,225,254,1) 0%,rgba(100,192,250,1) 100%);filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#b2e1fe', endColorstr='#64c0fa',GradientType=0 );",
            backgroundColor: '',

        },
        legend: {
            show: true,
            orient: 'horizontal',
            //x: 'center',
            right: 10,
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: '#45a5f6',
                fontSize: 12 + fontSizeDiff()
            },
            data: [{
                name: '本期',
                icon: 'circle',
            }, {
                name: '同期',
                icon: 'circle',
            }]
        },
        grid: {
            top: 36,
            left: 30,
            right: 0,
            bottom: 55,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: true, // 去掉x轴两边的留白, 空着不显示line难看
            nameTextStyle: {
                color: '#45a5f6'
            },
            axisLine: {
                lineStyle: {
                    color: '#45a5f6' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#45a5f6', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
                formatter: function(value, index) {
                    return chartDateFormatter(value, index);
                },
            },
            splitLine: {
                show: false, // 不显示刻度线
            },
            axisTick: {
                show: false, // 不显示刻度线
            }

        }],
        yAxis: [{
            type: 'value',
            name: '直销额占比',
            nameLocation: 'end',
            //min: 0,
            //max: 100,
            //interval: 10,
            nameTextStyle: {
                color: '#45a5f6',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#45a5f6',
                    fontSize: 12 + fontSizeDiff(),
                    align: 'left',
                },
                margin: 30,
                formatter: '{value}%',
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(69,165,246,0.1)'] // 分割线颜色
                }
            }
        }],
        series: [{
            name: '本期',
            type: 'line',
            smooth: true,
            data: data_s1,
            symbol: 'circle',
            symbolSize: 9,
            lineStyle: {
                normal: {
                    color: colors[0][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[0][0],
                    opacity: 1, //折线拐点
                }
            },
            areaStyle: {
                normal: {
                    color: colors[0][1],
                    opacity: 0.3
                }
            },
        }, {
            name: '同期',
            type: 'line',
            smooth: true,
            data: data_s2,
            symbol: 'circle',
            symbolSize: 9,
            lineStyle: {
                normal: {
                    color: colors[1][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[1][0],
                    opacity: 1, //折线拐点

                }
            },
            areaStyle: {
                normal: {
                    color: colors[1][1],
                    opacity: 0.3
                }
            },
        }]
    };

    chart.setOption(option);



    // --------------------------------- TAB2

    // ---------------------------
    var chart_id = 'chart_r2b';
    var kpi_code = '';
    var colors = [
        ['#54cf18', '#54cf18'],
        ['#1d81cd', '#1d81cd'],
    ];
    // ---------------------------


    var xAxisData = [];
    var data_s1 = [];
    var data_s2 = [];


    var len = companylist.length;
    // console.log(companylist)
    for (var j = 0; j < len; j++) {
        
        var obj = companylist[j];
        var compcode = obj.code;
        if (compcode != parent_company) {

            var date = getCurrentDate();

            var chal_id2code = {};

            var chals = all_company_chal[compcode];
            var chals2 = all_company_chal2[compcode];

            var chal_ids_flag0 = [];
            var chal_ids_flag = [];

            // 直销
            for (var chal_code in chals2) {
                var obj = chals2[chal_code];
                var chal_id = obj.COMP_CHAL_ID;
                if (obj.CHAL_FLAG == 0) {
                    // 直销
                    chal_ids_flag0.push(chal_id);
                    //
                    chal_id2code[chal_id] = chal_code;
                }
            }

            // 分销+直销
            for (var chal_code in chals) {
                var obj = chals[chal_code];
                var chal_id = obj.COMP_CHAL_ID;
                chal_ids_flag.push(chal_id);
            }
            // console.log(chal_ids_flag0, chal_ids_flag)

            var sal_vol0 = 0; //直销 销售量
            var sal_vol0_tq = 0; //直销 销售量 同期

            var sal_vol = 0; //分销 销售量
            var sal_vol_tq = 0; //分销 销售量 同期

            var sal_vol_code = 0; // 当期
            var sal_vol_code_tq = 0; // 同期

            // 直销
            for (var i = chal_ids_flag0.length - 1; i >= 0; i--) {
                var chal_id = chal_ids_flag0[i];

                var val = getKpi2(chal_id, 'kpi_value_d', 'SAL_AMT', date_type, date);
                var val2 = getKpi2(chal_id, 'kpi_value_d', 'ADD_INC', date_type, date);
                if (!isNaN(val) && !isNaN(val2)) {
                    sal_vol0 += Number(val) + Number(val2);

                    //
                    var chal_code = chal_id2code[chal_id];
                    if (selected_chal_code == chal_code) {
                        sal_vol_code = Number(val) + Number(val2);
                    }
                }

                var val = getKpi2(chal_id, 'kpi_value_tq_d', 'SAL_AMT', date_type, date);
                var val2 = getKpi2(chal_id, 'kpi_value_tq_d', 'ADD_INC', date_type, date);
                if (!isNaN(val) && !isNaN(val2)) {
                    sal_vol0_tq += Number(val) + Number(val2);

                    var chal_code = chal_id2code[chal_id];
                    if (selected_chal_code == chal_code) {
                        sal_vol_code_tq = Number(val) + Number(val2);
                    }
                }
            }

            // 分销+直销
            for (var i = chal_ids_flag.length - 1; i >= 0; i--) {
                var chal_id = chal_ids_flag[i];

                var val1 = getKpi(chal_id, 'kpi_value_d', 'SAL_AMT', date_type, date);
                var val2 = getKpi(chal_id, 'kpi_value_d', 'ADD_INC', date_type, date);
                if ((!isNaN(val)) && (!isNaN(val2))) {
                    sal_vol += Number(val1) + Number(val2);

                }
                // console.log("zhexianTotal", val1, val2, sal_vol)
                var val3 = getKpi(chal_id, 'kpi_value_tq_d', 'SAL_AMT', date_type, date);
                var val4 = getKpi(chal_id, 'kpi_value_tq_d', 'ADD_INC', date_type, date);
                if (!isNaN(val)) {
                    sal_vol_tq += Number(val3) + Number(val4);
                }
            }
            // console.log("zhexian", sal_vol, selected_chal_code, date)
            // s1
            var per = Math.round(sal_vol_code / sal_vol * 1000) / 10;
            data_s1.push(per);

            // s2
            var per = Math.round(sal_vol_code_tq / sal_vol_tq * 1000) / 10;
            data_s2.push(per);

            xAxisData.push(compcode);

        }
    }



    var chart = echarts.init(document.getElementById(chart_id));

    var option = {
        tooltip: {
            show: true,
            formatter: function(params, ticket, callback) {
                return params.seriesName + '<br>' + params.name + ': ' + params.value + '%';
            },
            extraCssText: "color:#021e55; padding:5px 10px 6px 10px; box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);background: rgb(178,225,254);background: -moz-linear-gradient(top,  rgba(178,225,254,1) 0%, rgba(100,192,250,1) 100%);background: -webkit-linear-gradient(top,  rgba(178,225,254,1) 0%,rgba(100,192,250,1) 100%);background: linear-gradient(to bottom,  rgba(178,225,254,1) 0%,rgba(100,192,250,1) 100%);filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#b2e1fe', endColorstr='#64c0fa',GradientType=0 );",
            backgroundColor: '',

        },
        legend: {
            show: true,
            orient: 'horizontal',
            //x: 'center',
            right: 10,
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: '#45a5f6',
                fontSize: 12 + fontSizeDiff()
            },
            data: [{
                name: '本期',
                icon: 'circle',
            }, {
                name: '同期',
                icon: 'circle',
            }]
        },
        grid: {
            top: 36,
            left: 30,
            right: 0,
            bottom: 55,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: true, // 去掉x轴两边的留白, 空着不显示line难看
            nameTextStyle: {
                color: '#45a5f6'
            },
            axisLine: {
                lineStyle: {
                    color: '#45a5f6' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#45a5f6', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
                formatter: function(value, index) {
                    return chartDateFormatter(value, index);
                },
            },
            splitLine: {
                show: false, // 不显示刻度线
            },
            axisTick: {
                show: false, // 不显示刻度线
            }

        }],
        yAxis: [{
            type: 'value',
            name: '直销额占比',
            nameLocation: 'end',
            //min: 0,
            //max: 100,
            //interval: 10,
            nameTextStyle: {
                color: '#45a5f6',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#45a5f6',
                    fontSize: 12 + fontSizeDiff(),
                    align: 'left',
                },
                margin: 30,
                formatter: '{value}%',
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(69,165,246,0.1)'] // 分割线颜色
                }
            }
        }],
        series: [{
            name: '本期',
            type: 'line',
            smooth: true,
            data: data_s1,
            symbol: 'circle',
            symbolSize: 9,
            lineStyle: {
                normal: {
                    color: colors[0][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[0][0],
                    opacity: 1, //折线拐点
                }
            },
            areaStyle: {
                normal: {
                    color: colors[0][1],
                    opacity: 0.3
                }
            },
        }, {
            name: '同期',
            type: 'line',
            smooth: true,
            data: data_s2,
            symbol: 'circle',
            symbolSize: 9,
            lineStyle: {
                normal: {
                    color: colors[1][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[1][0],
                    opacity: 1, //折线拐点

                }
            },
            areaStyle: {
                normal: {
                    color: colors[1][1],
                    opacity: 0.3
                }
            },
        }]
    };

    chart.setOption(option);
}


// 设置页面元素缩放比例
function onSizeChange() {
    setBlockL2Kpi()
}


// --------- Block R1 渠道直销量 条目切换------------- 

$('.block_r1 .itm').off('click');
$('.block_r1 .itm').on('click', function(e) {
    if (chal0_for_sort) {
        selectChalType($(this).attr('data-id'), $(this).attr("data-type"));

        clearTimeout(itv_autoSwitchChal);
        itv_autoSwitchChal = setTimeout(autoSwitchChal, 10000);
    }

});

function selectChalType(id, type) {

    if (chal0_for_sort && chal0_for_sort[id]) {
        currentChalIndex = id;

        var code = chal0_for_sort[id].code;
        var name = chal0_for_sort[id].name;
        selected_chal_code = code;

        $('.block_r2 .tit span').text(name);
        //$('.block_r2 .tit span').css('background-image', 'url(img/a2.4.icochal'+id+'.png)');

        $('.block_r1 .itm .line').hide();
        $('.block_r1 .itm' + id + ' .line').show();

        setBlockR2Kpi();
        if (type == "OTA") {
            drawOTAChart();
            $('.block_r2 .tab3').show();
        } else {
            $('.block_r2 .tab3').hide();
            $('.block_r2 .tab1').click();
        }

    }
}


// 自动切换渠道 -----------------
var currentChalIndex = 0;
var itv_autoSwitchChal;

function autoSwitchChal() {
    clearTimeout(itv_autoSwitchChal);

    if (!autoSwitch) {
        itv_autoSwitchChal = setTimeout(autoSwitchChal, 10);
        return;
    }

    var max = Math.min(chal0_for_sort.length, 5);

    selectChalType(currentChalIndex, $('.block_r1 .itm' + currentChalIndex).attr("data-type"));

    if (currentChalIndex < max - 1) {
        currentChalIndex++;
    } else {
        currentChalIndex = 0;
    }

    itv_autoSwitchChal = setTimeout(autoSwitchChal, 10000);

}



$('.block_r2 .tab1').on('click', function(e) {
    $('#chart_r2a').show();
    $('#chart_r2b').hide();
    $('#chart_r2c').hide();
    $('.block_r2 .tab1').addClass('selected');
    $('.block_r2 .tab2').removeClass('selected');
    $('.block_r2 .tab3').removeClass('selected');
});
$('.block_r2 .tab2').on('click', function(e) {
    $('#chart_r2a').hide();
    $('#chart_r2b').show();
    $('#chart_r2c').hide();
    $('.block_r2 .tab1').removeClass('selected');
    $('.block_r2 .tab2').addClass('selected');
    $('.block_r2 .tab3').removeClass('selected');
});
$('.block_r2 .tab3').on('click', function(e) {
    $('#chart_r2a').hide();
    $('#chart_r2b').hide();
    $('#chart_r2c').show();
    $('.block_r2 .tab1').removeClass('selected');
    $('.block_r2 .tab2').removeClass('selected');
    $('.block_r2 .tab3').addClass('selected');
});


// ---------------------- 

function onCompanyChanged(comp_code) {
    current_company_code = comp_code;
    checkCompanyReay();
}

function checkCompanyReay() {
    if (companyCode2Name[current_company_code] == undefined) {
        setTimeout(checkCompanyReay, 0);
    } else {
        updateAllKpi();
    }
}


getAllChal();



function setTitleDate() {
    var date = '';
    if (date_type == 'L') {
        date = $('#main_cb_week .combobox_label').text();
    } else if (date_type == 'M') {
        date = $('#main_cb_month .combobox_label').text();
    } else if (date_type == 'D') {
        date = $('#main_cb_day .combobox_label').text();
    }
    $('.pagetitle .maintitle .date').html(date); // 页面标题-时间
}



// HBI 外部跳转链接
function checkUserInfoLoaded() {
    if (!usersCompayCodeList || usersCompayCodeList.length == 0) {
        setTimeout(checkUserInfoLoaded, 10);
        return;
    }

    if (usersCompayCodeList.indexOf(parent_company) > -1) {
        $('#ext_link2').show();

    }
}

function setExtLink() {
    if (!weekDateRangeList) {
        setTimeout(setExtLink, 0);
        return;
    }
    var date = getCurrentDate();
    var daterange;
    var datetype;
    if (date_type == 'L') {
        datetype = '例会周';
        daterange = weekDateRangeList[date];
    } else {
        datetype = '月';
        /*
        var m = Number(date.substr(4,2));
        var m1 = [1,3,5,7,8,10,12];
        var m2 = [2,4,6,9,11];
        var end;
        if(m1.indexOf(m) > -1){
            end = 31;
        }else if(m2.indexOf(m) > -1){
            end = 30;
        }else{
            end = 28;
        }
        daterange = date+'01'+'-'+date+''+end;
        */
        daterange = date;
    }

    $('#ext_link1').attr('href', encodeURI('http://cdp.hnair.net:8080/HBI/rdThird.do?res_id=144523285194090014&paramArr=4公司=<' + current_company_code + '>::1日期类型=<销售日期>::3日期=<' + daterange + '>::2报表类型=<' + datetype + '>'));
    //$('#ext_link2').attr('href', encodeURI('http://cdp.hnair.net:8080/HBI/rdThird.do?res_id=144523283986001276&paramArr=1日期类型=<销售日期>::3日期=<'+daterange+'>::2报表类型=<'+datetype+'>::4一类渠道=<B2C>::5二类渠道=<全部>'));
    $('#ext_link2').attr('href', encodeURI('http://cdp.hnair.net:8080/HBI/rdThird.do?res_id=144523283986001276&paramArr=1日期类型=<销售日期>::3日期=<' + daterange + '>::2报表类型=<' + datetype + '>::4一类渠道=<线上直销>::5二类渠道=<全部>'));
}

checkUserInfoLoaded();
regTooltip('.ext_link', '查看关联报表');

dfd.done(function () {
    if (hasAllCompanyPermission()) {
        $(".block_l2").removeClass("hide");
        return;
    }
})