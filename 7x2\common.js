document.oncontextmenu = function () {
  return false;
}
var baseInfoInitDtd = $.Deferred();
var comp_cause = ['飞机故障', '运力调配', '工程机务', '航班计划', '航材保障', '航务保障', '机组保障', '飞行机组保障', '乘务组', '乘务组保障', '空警安全员', '地面保障', '货运保障', '公司原因', '其他航空公司原因'];
var none_cause = ['公共安全', '机场', '军事活动', '空管', '离港系统', '联检', '旅客', '民航局航班时刻安排', '天气原因', '油料'];

var companylist = [];
var companyCode2Name = {};
var companyName2Code = {};
var companyCode2Nameabbr = {};
var companyYshcode2Code = {};
var companyCode2YshCode = {};
var companyCode2CompanyId = {};
var companyCode2Sort = {};
var companyCode2Node = {};
var parent_company = 'HNAHK';
var authCompanyList = [];


var return_url = localStorage.getItem('7x2_return_url');
var return_time = localStorage.getItem('7x2_return_time');
var dd = new Date();
var nowtime = dd.getTime();
if (return_url != undefined && nowtime - Number(return_time) < 5000) { //5s
  localStorage.removeItem('7x2_return_url')
  window.location.href = return_url;
}

var userinfo;
function getUserInfo () {

  $.ajax({
    type: 'post',
    url: "/bi/sso/loginforBI2",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify({}),
    success: function (response) {
      userinfo = response;
      $('body').show();
      setPageScale();
      baseInfoInitDtd.resolve();
      // processUserInfo(userinfo)

    },
    error: function (error) {
      console.log(error, "redirect to sso")
      redirectToSSO();
    }
  });

  // if (typeof jQuery != 'undefined' && $('#login').length) {

  //     $('#login').attr('src', '/bi/sso/loginforBI2?ts=' + new Date().getTime());

  //     function checkIFrame() {

  //         if ($('#login')[0].contentWindow != undefined) {
  //             try {
  //                 var doc = $('#login')[0].contentWindow.document.body;
  //                 let result = $(doc).text();

  //                 if (result.indexOf("rejectLogin") > -1) {
  //                     let info = JSON.parse(result);
  //                     alert(info.errordesc)
  //                     return;
  //                 }

  //                 if (result && result.length > 200) {
  //                     userinfo = JSON.parse($(doc).text());
  //                     console.log('userinfo', userinfo);

  //                     $('body').show();
  //                     setPageScale();
  //                     baseInfoInitDtd.resolve();

  //                 } else {
  //                     setTimeout(checkIFrame, 10);
  //                 }
  //             } catch (error) {
  //                 $('#login').remove();
  //                 if (typeof redirectToSSO != 'undefined') {
  //                     redirectToSSO();
  //                 }

  //             } finally {
  //             }
  //         } else {
  //             setTimeout(checkIFrame, 10);
  //         }
  //     }

  //     checkIFrame();

  // }

}

function redirectToSSO () {
  window.location.href = SSO_URL() + "/login?appid=" + APP_ID + "&service=" + SSO_SERVICE();

}


function SSO_URL () {
  if (window.location.href.indexOf("vis.hnair.net") > -1 || window.location.href.indexOf("bim.hnair.net") > -1 || window.location.href.indexOf("cdp-mobile.hnair.net") > -1) {
    return "https://sso.hnair.net";
  } else {
    return "https://ssotest.hnair.net/opcnet-sso";
  }
}

function SSO_SERVICE () {
  if (window.location.href.includes("vis.hnair.net") || window.location.href.includes("bim.hnair.net") || window.location.href.includes("cdp-mobile.hnair.net")) {
    if (window.location.href.includes("largescreen/7x2")) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen7x2";
    } else if (window.location.href.includes("largescreen/west")) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen_west";
    } else if (window.location.href.includes("largescreen/jdair")) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen_jd";
    } else if (window.location.href.includes("largescreen/hnahk")) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen_hnahk";
    } else if (window.location.href.includes("largescreen/general")) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen_general";
    } else if (window.location.href.includes("largescreen/sdc")) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen_sdc";
    } else if (window.location.href.includes("largescreen")) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen";
    } else {
      return "https://vis.hnair.net//bi/spring/redirect?url=" + encodeURIComponent(window.location.href) + "&redict=true";
    }
  } else if (window.location.href.indexOf("vis-dev.hnair.net") > -1) {
    if (window.location.href.indexOf("largescreen/7x2") > -1) {
      return "https://vis-dev.hnair.net/bi/largescreen/redirect_largescreen7x2";
    } else if (window.location.href.indexOf("largescreen/west") > -1) {
      return "https://vis-dev.hnair.net/bi/largescreen/redirect_largescreen_west";
    } else if (window.location.href.indexOf("largescreen/jdair") > -1) {
      return "https://vis-dev.hnair.net/bi/largescreen/redirect_largescreen_jd";
    } else if (window.location.href.indexOf("largescreen/hnahk") > -1) {
      return "https://vis-dev.hnair.net/bi/largescreen/redirect_largescreen_hnahk";
    } else if (window.location.href.indexOf("largescreen/general") > -1) {
      return "https://vis-dev.hnair.net/bi/largescreen/redirect_largescreen_general";
    } else if (window.location.href.indexOf("largescreen/sdc") > -1) {
      return "https://vis-dev.hnair.net/bi/largescreen/redirect_largescreen_sdc";
    } else if (window.location.href.indexOf("largescreen") > -1) {
      return "https://vis-dev.hnair.net/bi/largescreen/redirect_largescreen";
    } else {
      return "https://vis-dev.hnair.net/bi/largescreen/redirect_admin";
    }
  } else if (window.location.href.includes("localhost")) {
    if (window.location.href.includes("largescreen/7x2")) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen7x2";
    } else if (window.location.href.includes("largescreen/west")) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen_west";
    } else if (window.location.href.includes("largescreen/jdair")) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen_jd";
    } else if (window.location.href.includes("largescreen/hnahk")) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen_hnahk";
    } else if (window.location.href.includes("largescreen/general")) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen_general";
    } else if (window.location.href.includes("largescreen/sdc")) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen_sdc";
    } else if (window.location.href.includes("largescreen")) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen";
    } else {
      return "https://localhost:8080//bi/spring/redirect?url=" + encodeURIComponent(window.location.href);
    }
  } else {
    if (window.location.href.includes("largescreen/7x2")) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen7x2";
    } else if (window.location.href.includes("largescreen/west")) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen_west";
    } else if (window.location.href.includes("largescreen/jdair")) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen_jd";
    } else if (window.location.href.includes("largescreen/hnahk")) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen_hnahk";
    } else if (window.location.href.includes("largescreen/general")) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen_general";
    } else if (window.location.href.includes("largescreen/sdc")) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen_sdc";
    } else if (window.location.href.includes("largescreen")) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen";
    } else {
      return "https://cdp-test.hnair.net//bi/spring/redirect?url=" + encodeURIComponent(window.location.href);
    }
  }
}


var companyDtd = $.Deferred();
function getCompany () {
  var data = '';

  $.ajax({
    type: 'POST',
    url: '/bi/query/company',
    async: true,
    dataType: 'json',
    data: data,
    success: function (response) {

      companylist = response.comp;

      companylist.sort(function (a, b) {
        return a.sort - b.sort
      });

      var _find_right = $.grep(userinfo.MENU.menus, function (ele, i) {
        var url = ele.url;
        if (url.startsWith("7x2/")) {
          url = url.substr(0, url.indexOf("?"));
        }
        return location.href.indexOf(url) > -1 && ele.url != "";
      });
      var _find_company_right = _find_right[0].companys;

      $.each(companylist, function (i, ele) {
        // console.log(ele)
        var _find = $.grep(_find_company_right, function (_ele, _i) {
          return _ele.company == ele.id || "";
        });
        if (_find.length != 0) {
          ele["disabled"] = false;
          authCompanyList.push(ele);
        } else {
          ele["disabled"] = true;
        }
      });

      var len = companylist.length;
      var html = '';
      for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        companyCode2Name[dat.code] = dat.name;
        companyName2Code[dat.name] = dat.code;
        companyCode2Nameabbr[dat.code] = dat.nameabbr;
        companyCode2CompanyId[dat.code] = dat.id;
        companyCode2Node[dat.code] = dat.node;
        if (dat.code == parent_company) {
          companyYshcode2Code['ALL'] = dat.code;
        } else {
          companyYshcode2Code[dat.yhscode] = dat.code;
          companyCode2YshCode[dat.code] = dat.yhscode;
        }
        companyCode2Sort[dat.code] = dat.sort;

      }
      var len = authCompanyList.length;
      var html = '';
      for (var i = 0; i < len; i++) {
        var dat = authCompanyList[i];
        //不显示境内航司
        if (dat.node !== '' || dat.code == 'HNAHK') {
          html += '<div class="itm" code="' + dat.code + '" comp_id="' + dat.id + '">' + dat.name + '</div>';
        }
      }

      $('#companylist').html(html);

      // 选择公司

      $('#companycombo .box1').on('click', function () {
        if ($('#companylist').is(':visible')) {
          $('#companylist').hide();
        } else {
          $('#companylist').slideDown('200', function () { });
        }
      });

      $('#companylist .itm').on('click', function () {
        $('#companylist').hide();
        var code = $(this).attr('code');
        switchCompany(code);

        // stopAutoSwitchCompany();

      });

      $('#companycombo').on('mouseleave', function () {
        $('#companylist').hide();
      });

      companyDtd.resolve();

    },
    error: function (e) {
      //console.log('ajax error');
      //console.log(e);
    }
  });


}

baseInfoInitDtd.done(function () {
  getCompany();
});


function createNavMenu (userinfo) {
  var navMenu = [];
  var usersMenuList = userinfo.MENU.menus;
  if (usersMenuList.length == 0) {
    return navMenu;
  }

  var len = usersMenuList.length;

  for (var i = 0; i < len; i++) {
    var obj = usersMenuList[i];
    if (obj.parent_id == 0 || obj.parent_id == '') {
      var id = obj.id;
      var menu = {
        'name': obj.name,
        'page': obj.url,
        'target': obj.target,
        'sub': []
      };

      //查找子菜单
      var len2 = usersMenuList.length;
      for (var j = 0; j < len2; j++) {
        var obj2 = usersMenuList[j];
        if (obj2.parent_id == id) {
          var submenu = {
            'name': obj2.name,
            'page': obj2.url,
            'target': obj2.target
          };
          menu.sub.push(submenu);
        }
      }
      navMenu.push(menu);
    }
  }
  return navMenu;
}

// 页面访问日志
function userlog () {
  if (userinfo && userinfo.id) {
    var pagename;
    var navMenu = createNavMenu(userinfo);
    var len = navMenu.length;
    for (var i = 0; i < len; i++) {
      var obj = navMenu[i];
      var subs = obj.sub;

      for (var j = 0; j < subs.length; j++) {
        var sobj = subs[j];

        if (sobj.page.length > 0 && window.location.href.indexOf(sobj.page) > -1) {
          pagename = sobj.name;

        } else if (sobj.subpage && sobj.subpage.length > 0) {
          for (var k = 0; k < sobj.subpage.length; k++) {
            var pp = sobj.subpage[k];
            if (window.location.href.indexOf(pp) > -1) {
              pagename = sobj.name;
              break;
            }
          }
        }
      }
    }

    if (pagename) {

      var date = new Date();
      var mm = date.getMonth() + 1;
      var dd = date.getDate();
      var h = date.getHours();
      var m = date.getMinutes();
      var s = date.getSeconds();

      if (mm < 10) {
        mm = '0' + mm;
      }
      if (dd < 10) {
        dd = '0' + dd;
      }
      if (h < 10) {
        h = '0' + h;
      }
      if (m < 10) {
        m = '0' + m;
      }
      if (s < 10) {
        s = '0' + s;
      }

      var timeNow = date.getFullYear() + '年' + mm + '月' + dd + '日 ' + h + ':' + m + ':' + s;

      var operation = '访问页面:' + pagename + ', 时间' + timeNow;
      var category = '页面';

      var param = {
        'userid': userinfo.id,
        'oper': operation,
        'category': category,
      }

      $.ajax({
        type: 'post',
        url: "/bi/web/userlog",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {

        },
        error: function () { }
      });

    }


  } else {
    setTimeout(userlog, 100);
  }
}



$(window).resize(function () {
  //location.reload();
  setPageScale();
});

var pageZoomScale = 1;
var pageZoomScaleY = 1;

function setPageScale () {
  var screenWidth = 4080;
  var screenHeight = 1200;
  var wapperWidth = 2688;
  var wapperHeight = 790;
  var osw = document.body.offsetWidth;
  var osh = window.innerHeight;

  if (getQueryString('scale') == 1) {
    $('body').css('overflow-x', 'auto');
    $('body').css('overflow-y', 'auto');
    pageZoomScale = 1;
    pageZoomScaleY = 1;
  } else if (getQueryString('scale') == 'auto') {
    $('body').css('overflow-x', 'hidden');
    $('body').css('overflow-y', 'auto');
    pageZoomScale = osw / wapperWidth;
    pageZoomScaleY = pageZoomScale;
  } else {

    //$('body').css('overflow-x', 'hidden');
    //$('body').css('overflow-y', 'hidden');
    //pageZoomScale = osw/wapperWidth;
    //pageZoomScaleY = osh/wapperHeight;

    $('body').css('overflow-x', 'auto');
    $('body').css('overflow-y', 'auto');
    pageZoomScale = screenWidth / wapperWidth;
    pageZoomScaleY = pageZoomScale;

  }


  // for firefox
  $('.page-bgimg').css('transform', 'scale(' + pageZoomScale + ',' + pageZoomScaleY + ')');
  $('.page-bgimg').css('transform-origin', 'left top');

  $('.page-wrapper').css('transform', 'scale(' + pageZoomScale + ',' + pageZoomScaleY + ')');
  $('.page-wrapper').css('transform-origin', 'left top');


  var scaleInvert = 1 / pageZoomScale;
  var scaleInvertY = 1 / pageZoomScaleY;

  // firefox
  //$('.chartblock').css('transform', 'scale('+scaleInvert+','+scaleInvertY+')');
  //$('.chartblock').css('transform-origin', 'left top');

  //$('.chartblock').each(function(index, el) {
  //  $(this).css('width', $(this).attr('prop-width')*pageZoomScale+'px');
  //  $(this).css('height', $(this).attr('prop-height')*pageZoomScaleY+'px');
  //});

  var left = 0;
  var top = 0;
  var width = osw;
  var height = width * (wapperHeight / wapperWidth);
  $('.mainframe').attr('style', 'left:' + left + 'px; ' + 'top:' + top + 'px; ' + 'width:' + width + 'px; ' + 'height:' + height + 'px');

  //    $("body").height(height);
  // update all echarts
  try {
    if (typeof (eval('updateAlleCharts')) == "function") {
      updateAlleCharts();
    }
  } catch (e) {
  }

}


function showLoading () {
  var html = '<div id="loading_msk"><div class="spinner spinner_animate"></div></div>';
  $('.page-wrapper').append(html);
  try {
    NProgress.start();
  } catch (error) {

  }
}
function hideLoading () {
  $('#loading_msk').remove();
  try {
    NProgress.done();
    NProgress.remove();
  } catch (error) {

  }
}



setPageScale();


function checkBrowser () {
  var support = true;
  var isIE = false;
  var isIE11 = false;
  var isChrome = false;
  var isFirefox = false;
  var userAgent = navigator.userAgent;
  if (userAgent.indexOf("Opera") > -1 || userAgent.indexOf("OPR") > -1) {
    // 'Opera';
  } else if (userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1) {
    // 'IE';
    isIE = true;
  } else if (userAgent.indexOf("Edge") > -1) {
    // 'Edge';
  } else if (userAgent.indexOf("Firefox") > -1) {
    // 'Firefox';
    isFirefox = true;
  } else if (userAgent.indexOf("Safari") > -1 && userAgent.indexOf("Chrome") == -1) {
    // 'Safari';
  } else if (userAgent.indexOf("Chrome") > -1 && userAgent.indexOf("Safari") > -1) {
    // 'Chrome';
    isChrome = true;
  } else if (!!window.ActiveXObject || "ActiveXObject" in window) {
    // 'IE>=11';
  } else {
    // 'Unkonwn';
    support = false;
  }

  if (isIE) {
    var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
    reIE.test(userAgent);
    var fIEVersion = parseFloat(RegExp["$1"]);
    if (fIEVersion == 7) {
      support = false;
    } else if (fIEVersion == 8) {
      support = false;
    } else if (fIEVersion == 9) {
      support = false;
    } else if (fIEVersion == 10) {
      support = false;
    } else if (fIEVersion == 11) {
      // "IE11";
      isIE11 = true;
    } else {
      support = false;
    }
  }

  // 检测遨游浏览器
  try {
    if (window.external.max_version && window.external.max_version != undefined) {
      support = false;
    }
  } catch (ex) {

  }


  if (support) {
    getUserInfo();
  } else {
    //window.location.href = "/largescreen/browser.html";
    alert('您的浏览器版本可能存在兼容问题，建议使用 Google Chrome 或 IE11 浏览器。');
    if (typeof jQuery != 'undefined') {
      $('body').hide();
    }
  }

}
checkBrowser();

userlog();



function getQueryString (name) {
  var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
  var r = window.location.search.substr(1).match(reg);
  if (r != null) return unescape(r[2]); return '';
}

function showNoPermission () {
  location.href = "../403.html"
}

function switchCompany (code) {
  if (!hasCompanyAuth(code)) {
    showNoPermission();
    return;
  }
  // 判断当前code是否可查询
  try { window.stop() } catch (e) { }
  $('#companycombo').attr('code', code);
  $('#companycombo .box1').html(companyCode2Name[code]); // 页面标题-公司名称
  window.location.hash = '#' + code;
  window.location.reload();

}
//取有权限的公司,不包括"HNAHK"
function getFirstAuthCompanyCode () {
  if (authCompanyList.length > 0) {
    var len = authCompanyList.length;
    var html = '';
    for (var i = 0; i < len; i++) {
      if (authCompanyList[i].node != "") {
        return authCompanyList[i].code;
      }
    }
  }
}

function hasCompanyAuth (comp_code) {
  var _find = $.grep(authCompanyList, function (authCompany, _i) {
    return authCompany.code == comp_code;
  });
  return _find.length > 0
}

function formatLonLat (dat) {
  if (!dat) {
    return dat;
  }
  if (isNaN(dat)) {
    if (dat.indexOf('E') > -1) {
      dat = dat.substr(1);
      dat = Number(dat) / 1000000;
    } else if (dat.indexOf('W') > -1) {
      dat = '-' + dat.substr(1);
      dat = Number(dat) / 1000000;
    } else if (dat.indexOf('N') > -1) {
      dat = dat.substr(1);
      dat = Number(dat) / 1000000;
    } else if (dat.indexOf('S') > -1) {
      dat = '-' + dat.substr(1);
      dat = Number(dat) / 1000000;
    }
  }

  return dat;
}

/**
* Calculate the bearing between two positions as a value from 0-360
*
* @param lat1 - The latitude of the first position
* @param lng1 - The longitude of the first position
* @param lat2 - The latitude of the second position
* @param lng2 - The longitude of the second position
*
* @return int - The bearing between 0 and 360
*/
function bearing (latitude, longitude, latitude2, longitude2) {
  let rad = Math.PI / 180;
  let lat1 = latitude * rad;
  let lat2 = latitude2 * rad;
  let lon1 = longitude * rad;
  let lon2 = longitude2 * rad;
  const a = Math.sin(lon2 - lon1) * Math.cos(lat2);
  const b = Math.cos(lat1) * Math.sin(lat2) -
    Math.sin(lat1) * Math.cos(lat2) * Math.cos(lon2 - lon1);

  let r = radiansToDegrees(Math.atan2(a, b));
  return r;
}

/*
* 弧度转换为角度
*/
function radiansToDegrees (radians) {
  const degrees = radians % (2 * Math.PI);
  return degrees * 180 / Math.PI;
}

// 通过2个经纬度算 正北夹角
function getGeoAngle (lat2, lng2, lat1, lng1) {
  return bearing(lat1, lng1, lat2, lng2);
}


(function () {

  document.write("<script src='/largescreen/js/lib/slm.js'></script>")

})();