var langPack = {
	tit_tl: {
		cn: '公司运行一览',
		en: 'General',
		uppercase: true
	},
	tit_tr: {
		cn: '航班信息',
		en: 'Flights',
		uppercase: true
	},
	tit_br: {
		cn: '重要信息告警',
		en: 'Warning',
		uppercase: true
	},
	tit_mid: {
		cn: '运行正常率',
		en: 'Operations Performance',
		uppercase: true
	},
	tit_mid2: {
		cn: '全球飞机位置图',
		en: 'A/C Location',
		uppercase: true
	},
	tit_flt_total: {
		cn: '航班总量',
		en: 'Total Flights',
	},
	lb_flts: {
		cn: '架次',
		en: 'flts',
	},
	lb_flt_exec: {
		cn: '已执行',
		en: '<span style="font-size:12px;">Complt</span>',
	},
	lb_ac_loc: {
		cn: '国际/飞机位置',
		en: 'Aircrafts Location',
	},
	lb_domestic: {
		cn: '国内',
		en: 'Domestic',
	},
	lb_domestic_flt: {
		cn: '国内航班',
		en: '<span style="font-size:14px;">Domestic</span>',
	},
	lb_int_flights: {
		cn: '国际航班',
		en: '<span style="font-size:14px;">International</span>',
	},
	lb_normal_rate: {
		cn: '航班正常率',
		en: 'Punctuality',
	},
	lb_total_psr: {
		cn: '计划运输的旅客总量',
		en: 'Total Passengers',
	},
	lb_flt_completed: {
		cn: '已完成量',
		en: 'Complt',
	},
	lb_abnormal_flt: {
		cn: '不正常航班一览',
		en: 'Abnormal Flights',
	},
	lb_aog: {
		cn: '非计划停场',
		en: 'AOG Unplaned',
	},
	lb_ac_no: {
		cn: '飞机号',
		en: 'A/C No.',
	},
	lb_est_recovery_time: {
		cn: '预计恢复',
		en: 'Est Recovery',
	},
	lb_est_recovery_left: {
		cn: '剩余',
		en: 'Left',
	},
	lb_return_divert: {
		cn: '返航备降',
		en: 'Return&Divert',
	},
	lb_cancel_flt: {
		cn: '取消航班',
		en: 'Cancelled',
	},
	lb_delayed_flt: {
		cn: '延误航班数',
		en: 'Delayed',
	},
	lb_delay_1to2: {
		cn: '1-2小时',
		en: '1-2H',
	},
	lb_delay_2to4: {
		cn: '2-4小时',
		en: '2-4H',
	},
	lb_delay_4: {
		cn: '4小时',
		en: '4H',
	},
	lb_hour: {
		cn: '小时',
		en: 'H',
	},
	lb_minute: {
		cn: '分',
		en: 'M',
	},
	lb_int: {
		cn: '国际航班',
		en: 'International',
	},
	lb_delay_cause: {
		cn: '延误原因',
		en: 'Delay Reasons',
	},
	lb_comp_cause: {
		cn: '公司原因',
		en: 'Corporate Reasons',
	},
	lb_none_cause: {
		cn: '非公司原因',
		en: 'External Reasons',
	},
	lb_comp_cause2: {
		cn: '公司原因',
		en: 'Corporate',
	},
	lb_none_cause2: {
		cn: '非公司原因',
		en: 'External',
	},
	lb_ac_dist: {
		cn: '运力分布',
		en: 'Aircrafts Distribution',
	},
	lb_total_ac: {
		// cn: '可用运力',
		cn: '总运力',
		// en: 'Available Aircrafts',
		en: 'Total Aircrafts',
	},
	lb_overnight: {
		cn: '基地过夜飞机',
		en: 'Overnight at Bases',
	},
	lb_planned_overnight: {
		cn: '当日计划总计',
		en: 'Planned',
	},
	lb_landed_overnight: {
		cn: '实际总计',
		en: 'Landed',
	},
	lb_jia: {
		cn: '架',
		en: '',
	},
	lb_fleet_count: {
		cn: '机型架数',
		en: 'Aircraft Fleet Capacity',
	},
	lb_ac_air: {
		cn: '空中',
		en: 'A/C Flying',
	},
	lb_ac_ground: {
		cn: '地面',
		en: 'On Ground',
	},
	lb_ac_stop: {
		cn: '停场',
		en: 'On Stop',
	},
	lb_normal_rate_m: {
		cn: '月度正常率',
		en: 'Monthly OTP',
	},
	lb_normal_rate_y: {
		cn: '年度正常率',
		en: 'Annually OTP',
	},
	lb_ac_pos_legend: {
		cn: '飞机位置图例',
		en: 'Real-time fleet layout',
	},
	lb_air: {
		cn: '空中',
		en: 'Flying',
	},
	lb_ground: {
		cn: '停场',
		en: 'On Ground',
	},
	lb_ac_type: {
		cn: '机型',
		en: 'A/C Type',
	},
	lb_ac_type2: {
		cn: '机型',
		en: 'Type',
	},
	lb_Xinjiang: {
		cn: '新疆',
		en: 'Xinjiang',
	},
	lb_Northwest: {
		cn: '西北',
		en: 'Northwest',
	},
	lb_Southwest: {
		cn: '西南',
		en: 'Southwest',
	},
	lb_Northeast: {
		cn: '东北',
		en: 'Northeast',
	},
	lb_north_china: {
		cn: '华北',
		en: 'North China',
	},
	lb_east_china: {
		cn: '华东',
		en: 'East China',
	},
	lb_south_cent: {
		cn: '中南',
		en: 'South Central',
	},
	lb_beijing: {
		cn: '北京',
		en: 'Beijing',
	},
	lb_haikou: {
		cn: '海口',
		en: 'Haikou',
	},
	lb_xian: {
		cn: '西安',
		en: "Xi'an",
	},
	lb_guangzhou: {
		cn: '广州',
		en: 'Guangzhou',
	},
	lb_shenzhen: {
		cn: '深圳',
		en: 'Shenzhen',
	},
	lb_dalian: {
		cn: '大连',
		en: 'Dalian',
	},
	lb_Urumqi: {
		cn: '乌鲁木齐',
		en: 'Urumqi',
	},
	lb_taiyuan: {
		cn: '太原',
		en: 'Taiyuan',
	},
	lb_hangzhou: {
		cn: '杭州',
		en: 'Hangzhou',
	},
	lb_lanzhou: {
		cn: '兰州',
		en: 'Lanzhou',
	},
	lb_sanya: {
		cn: '三亚',
		en: 'Sanya',
	},
	lb_Changsha: {
		cn: '长沙',
		en: 'Changsha',
	},
	lb_Chongqing: {
		cn: '重庆',
		en: 'Chongqing',
	},
	lb_dep: {
		cn: '始发',
		en: 'OrgDep',
	},
	lb_in: {
		cn: '进港',
		en: 'In',
	},
	lb_out: {
		cn: '出港',
		en: 'Out',
	},
	lb_overall: {
		cn: '进出港',
		en: 'Total',
	},
	lb_rank_m: {
		cn: '十大航月度排名',
		en: 'Monthly OTP Top10',
	},
	lb_rank_y: {
		cn: '十大航年度排名',
		en: 'Annually OTP Top10',
	},
	lb_london: {
		cn: '伦敦',
		en: 'London',
	},
	lb_sydney: {
		cn: '悉尼',
		en: 'Sydney',
	},
	lb_newyork: {
		cn: '纽约',
		en: 'New York',
	},
	lb_sch_total: {
		cn: '计划总量',
		en: 'Scheduled Today',
	},
	lb_completed: {
		cn: '已完成',
		en: 'Completed',
	},
	lb_bz_total: {
		cn: '保障航班总量',
		en: 'Scheduled Today',
	},
	lb_exec: {
		cn: '执行中',
		en: 'On Task',
	},
	lb_warning_flt: {
		cn: '预警航班总量',
		en: 'Air Early Warning Flts',
	},
	lb_completed_num: {
		cn: '完成进度',
		en: 'Completed',
	},
	lb_trans_psr: {
		cn: '日中转总量',
		en: '<span style="font-size:12px;">Transfer Passengers</span>',
	},
	lb_person: {
		cn: '人',
		en: '',
	},
	lb_in_out_4: {
		cn: '，4小时内',
		en: '<span style="font-size:10px;">,Entry-Exit in 4h</span>',
	},
	lb_seat_booked: {
		cn: '订座人数',
		en: '<span style="font-size:10px;">Seats Booked</span>',
	},
	lb_zz_normal: {
		cn: '中转正常',
		en: '<span style="font-size:10px;">Normal</span>',
	},
	lb_zz_short: {
		cn: '中转紧张',
		en: '<span style="font-size:10px;">Short Cnt</span>',
	},
	lb_zz_miss: {
		cn: '中转错失',
		en: '<span style="font-size:10px;">Missed Cnt</span>',
	},
	lb_cn_int: {
		cn: '国内转国际',
		en: 'CN to INT',
	},
	lb_cn_cn: {
		cn: '国内转国内',
		en: 'CN to CN',
	},
	lb_int_cn: {
		cn: '国际转国内',
		en: 'INT to CN',
	},
	lb_int_int: {
		cn: '国际转国际',
		en: 'INT to INT',
	},
	lb_important_flt: {
		cn: '重点关注航班',
		en: 'Important',
	},
	lb_in_2h: {
		cn: '(后续2小时)',
		en: '(2H)',
	},
	lb_tab_warning: {
		cn: '预警',
		en: 'AEW ',
	},
	lb_tab_abnormal: {
		cn: '不正常航班',
		en: 'Abnormal Flights',
	},
	lb_tab_securemonitor: {
		cn: '安全监控',
		en: 'Security Monitoring',
	},
	lb_important: {
		cn: '重点关注',
		en: '<span style="font-size:14px;">Important</span>',
	},
	lb_flt_details: {
		cn: '航班详情',
		en: 'Flight Details',
	},
	lb_no_data_fltinfo: {
		cn: '无航班信息',
		en: 'No flight info',
	},
	lb_pre_flt_take: {
		cn: '前序航班起飞时间',
		en: 'Dprt Time of Pre-Flight',
	},
	lb_pre_flt_pass: {
		cn: '前序航班预计过站时间',
		en: '<span style="font-size:10px;">Est Groundstop Dur of Pre-Flt</span>',
	},
	lb_backup_ac: {
		cn: '备机',
		en: 'Backup A/C',
	},
	lb_main_ac: {
		cn: '主机',
		en: 'Main A/C ',
	},
	lb_crew_info: {
		cn: '机组信息',
		en: 'Crew Information',
	},
	lb_flt_nodes: {
		cn: '航班保障节点',
		en: 'Handling nodes',
	},
	lb_node_crew_arr: {
		cn: '机组到位',
		en: 'Flight Crew Arr',
	},
	lb_node_cut_load: {
		cn: '航班截载',
		en: 'Cutting Load',
	},
	lb_node_cat_supply: {
		cn: '机供品配备',
		en: 'Catering Supply',
	},
	lb_node_cleansing: {
		cn: '客舱清洁',
		en: 'Cb Cleansing',
	},
	lb_node_release: {
		cn: '机务放行',
		en: 'A/C Release',
	},
	lb_node_ac_ready: {
		cn: '飞机准备好',
		en: 'Ready to Serve',
	},
	lb_node_boarding: {
		cn: '通知登机',
		en: 'Boarding Brdcst',
	},
	lb_node_boarding_end: {
		cn: '登机结束',
		en: 'Brding Complet',
	},
	lb_node_door_close: {
		cn: '客舱关闭',
		en: 'Cabin Closed',
	},
	lb_node_cargo_close: {
		cn: '货舱关闭',
		en: 'Cargo Closed',
	},
	lb_node_push: {
		cn: '飞机推出',
		en: 'Pushback',
	},
	lb_node_takeoff: {
		cn: '飞机起飞',
		en: 'Take Off',
	},
	lb_abnormal_weather: {
		cn: '天气',
		en: 'Abnormal Weather',
	},
	lb_warning_ff: {
		cn: '预警航班',
		en: 'Early Warning Flights',
	},
	lb_search_flt: {
		cn: '具体航班查询',
		en: 'Search Flights',
	},
	lb_search_flt_err: {
		cn: '提醒：该任务非执行中',
		en: 'No flight found',
	},
	lb_abnormal_week: {
		cn: '周',
		en: 'Week',
	},
	lb_abnormal_month: {
		cn: '月',
		en: 'Month',
	},
	lb_abnormal_rf: {
		cn: '返航备降',
		en: 'Return Flts',
	},
	lb_abnormal_cf: {
		cn: '取消航班',
		en: 'Cancelled Flts',
	},
	lb_abnormal_Delay: {
		cn: '延误',
		en: 'Delay',
	},
	lb_abnormal_abis: {
		cn: '不正常事件量',
		en: 'Abnormal Issue',
	},
	lb_abnormal_total: {
		cn: '总计',
		en: 'Total',
	},
	lb_abnormal_crr: {
		cn: '环比',
		en: 'Chain Relative Ratio',
	},
	lb_abnormal_mm: {
		cn: '人为',
		en: 'Man-made',
	},
	lb_abnormal_ica: {
		cn: '事件属性分析',
		en: 'The incident character analyses',
	},
	lb_abnormal_HAH: {
		cn: '海南航空',
		en: 'Hainan airlines holding',
	},
	lb_abnormal_si: {
		cn: '安全事故',
		en: 'Safety Incident',
	},
	lb_abnormal_uis: {
		cn: '不安全事件汇总',
		en: 'Unsafety incident summary',
	},
	lb_abnormal_ratio: {
		cn: '占比',
		en: 'Ratio',
	},
	lb_abnormal_maint: {
		cn: '机械',
		en: 'Maintenance',
	},
	lb_abnormal_se: {
		cn: '一类事件',
		en: 'Serious Error',
	},
	lb_abnormal_accident: {
		cn: '意外',
		en: 'Accident',
	},
	lb_abnormal_other: {
		cn: '其他',
		en: 'Other',
	},
	lb_abnormal_as: {
		cn: '事故征候',
		en: 'Accident Symptom',
	},
	lb_abnormal_flight: {
		cn: '班次',
		en: 'Flight',
	},
	lb_abnormal_legend: {
		cn: '图例',
		en: 'Legend',
	},
	lb_abnormal_sa: {
		cn: '安全不正常',
		en: 'Security Abnormal',
	},
	lb_abnormal_fc: {
		cn: '航班号颜色',
		en: 'Flts Colours',
	},
	lb_abnormal_ge: {
		cn: '一般事件',
		en: 'General Events',
	},
	lb_abnormal_above: {
		cn: '起',
		en: 'Above',
	},
	lb_abnormal_qi: {
		cn: '起',
		en: '',
	},
	lb_abnormal_inves: {
		cn: '调查分析中',
		en: 'Investigatting',
	},
	lb_abnormal_mi: {
		cn: '落实措施中',
		en: 'Measure Implementation',
	},
	lb_abnormal_closed: {
		cn: '已关闭',
		en: 'Closed',
	},
	lb_lang: {
		cn: 'English',
		en: 'Chinese',
	},



}