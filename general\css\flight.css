/* common */

#mapBox {
	position: absolute;
	top: 0px;
	width: 1920px;
	height: 1080px;
	z-index: 0;
}

.blue {
	color: #77BAF8;
}

.red {
	color: #ff0000;
}

.fs14 {
	font-size: 14px;
}

.isHiden {
	display: none;
}


.bLeft_B {
	border-left: 4px solid #00A9FF;
}

.bLeft_B .title {
	margin: 0 10px;
}

.titleRightLine {
	display: inline-block;
}

.titleRightA {
	float: left;
	width: 40px;
	height: 8px;
	background: url(../img/icon_title_righta.png) no-repeat;
}

.titleRightB {
	float: left;
	position: relative;
	top: 5px;
	height: 2px;
	border-bottom: 1px solid #00579e;
}

/* head */
.page-bgimg {
	position: absolute;
	top: 0px;
	width: 1920px;
	height: 1080px;
	background: url(../img/flightBG.png) no-repeat 0 0;
}

.page-wrapper {
	position: absolute;
	top: 0px;
	width: 1920px;
	height: 1080px;
	overflow-x: hidden;
	overflow-y: hidden;
	pointer-events: none;
}

.leftLogo {
	position: absolute;
	top: 20px;
	left: 60px;
	width: 120px;
	height: 47px;
	background-repeat: no-repeat;
	background-position: 0 center;
}


/* top mid */

.companyLogo {
	position: absolute;
	width: 40px;
	height: 40px;
	top: 10px;
	left: 600px;
	background: url(../img/logo_HU.png) no-repeat 0 0;
}


/* topMid */


.con_flex_column {
	display: flex;
	display: -webkit-flex;
	-webkit-flex-flow: column;
}

.con_flex_row {
	display: flex;
	display: -webkit-flex;
	-webkit-flex-flow: row;
}

.con_flex_row div {
	position: static;
	position: initial;
}

.con_flex_row .c4 {
	line-height: 35px;
}

.con_flex_row .c2 {
	justify-content: center;
}

.con_flex_row .c2 .t2,
.con_flex_row .c2 .t1 {
	text-align: center;
}

.flex_none {
	-webkit-flex: none;
}

.flex_1 {
	flex: 1;
	-webkit-flex: 1;
	text-align: center;
}


.col_top {
	pointer-events: auto;
	position: absolute;
	top: 10px;
	left: 570px;
	width: 770px;
	height: 66px;
	background-color: rgba(255, 0, 0, 0.0);
}

.col_top div {
	position: relative;
}

.col_top .co1 {
	text-align: right;
}

.col_top .co3 {
	text-align: center;
}

.col_top .mid .c1 {
	position: relative;
}

.col_top .mid .c2 {
	position: relative;
	text-align: center;
	width: 200px;
}

.col_top .mid .leg1 {
	width: 100%;
	top: -25px;
	/*background: url(../img/flightarr1.png?1) no-repeat top center;*/
}
.col_top .mid .leg1Status,.col_top .mid .leg2Status{
	position: relative;
	top: 18px;
}

.col_top .mid .c3 {
	position: relative;
	text-align: right;
}

.col_top .mid .leg2 {
	width: 100%;
	top: -25px;
	/*background: url(../img/flightarr2.png?1) no-repeat top center;*/
}

.col_top .mid .t1 {
	position: relative;
	padding: 5px 0;
	font-size: 16px;
	font-weight: bold;
}

.col_top .mid .t2 {
	position: relative;
	font-size: 12px;
	color: #77BAF8;
}

.col_top .fltno {
	display: inline-block;
	height: 40px;
	font-size: 22px;
	font-weight: bold;
	line-height: 40px;
	padding-left: 40px;
	background-repeat: no-repeat;
	background-position: 0 center;
	background-size: auto 40px;
}

.col_top .flightTimes {
	position: absolute;
	left: 110px;
	top: 35px;
}

.col_top .sts {
	position: absolute;
	height: 40px;
	font-weight: bold;
	line-height: 40px;
	margin-top: 4px;
	right: 20px;
	padding-left: 40px;
	background-repeat: no-repeat;
	background-position: 0 center;
}

.col_top .status1 {
	color: #AFE230;
	background-image: url(../img/flight.ac_green.png?1);
}

.col_top .status2 {
	color: #FFFF00;
	background-image: url(../img/flight.ac_yellow.png?1);
}


/* colLeft */
/* 航线详情 */
.col_left .col_left_row {
	position: absolute;
	left: 60px;
	width: 320px;
}

.col_left .row_1 {
	top: 95px;
	height: 380px;
	padding-left: 20px;
	width: 280px;
}


.col_left .row_1 .fltno {
	height: 25px;
	line-height: 25px;
	padding-left: 25px;
	background-repeat: no-repeat;
	background-position: 0 center;
	background-size: auto 25px;
}

.col_left .timeLinesBox {
	position: absolute;
	top: 35px;
	/*pointer-events: all;*/
}

.col_left .timeLinesBox span {
	display: inline-block;
}

.col_left .timeLinesBox .rowTitle {
	display: flex;
	width: 290px;
	height: 24px;
	line-height: 24px;
	margin-top: 5px;
}


.col_left .timeLinesBox .rowTitle .tLI_B,
.col_left .timeLinesBox .rowTitle .tLI_G {
	width: 19px;
	height: 24px;
	background-image: url(../img/icon_timeline_b.png);
}

.col_left .timeLinesBox .rowTitle .tLI_G {
	background-image: url(../img/icon_timeline_g.png);
}

.col_left .timeLinesBox .rowTitle .tLI_Time {
	width: 16px;
	height: 16px;
	background-image: url(../img/icon_timeline_time.png);
}


.col_left .timeLinesBox .rowTitle .col_1,
.col_left .timeLinesBox .rowTitle .col_2,
.col_left .timeLinesBox .rowTitle .col_3 {
	display: inline-block;
	text-align: left;
}

.col_left .timeLinesBox .rowTitle .col_1 {
	width: 40px;
}

.col_left .timeLinesBox .rowTitle .col_2 {
	width: 120px;
}

.col_left .timeLinesBox .rowInfo {
	position: relative;
	padding-left: 30px;
	width: 230px;
	min-height: 15px;
	margin-bottom: -5px;
}

.col_left .timeLinesBox .tLB_3 .rowInfo {
	margin-left: 11px;
}

.col_left .timeLinesBox .rBL_B,
.col_left .timeLinesBox .rBL_G {
	border-left: 1px dashed #00A9FF;
	margin-left: 9px;
}

.col_left .timeLinesBox .rBL_G {
	border-left: 1px dashed #8FC31F;
	margin-left: 9px;
}

.col_left .timeLinesBox .rowInfo .info_col {
	display: inline-block;
	width: 50px;
	margin-top: -15px;
	margin-right: 10px;
	text-align: center;
}

.col_left .timeLinesBox .rowInfo .info_col div {
	margin-bottom: 5px;
}

.col_left .timeLinesBox .rowInfo .info_col .standBG {
	position: relative;
	left: 30px;
	top: 30px;
	width: 66px;
	height: 61px;
	background-image: url(../img/icon_timeline_stand.png);
	background-repeat: no-repeat;
}


.col_left .timeLinesBox .rowInfo .info_col .standInfo {
	position: relative;
	top: -25px;
	left: 40px;
}

.info_delay {
	padding: 10px 0;
	background-color: rgba(5, 54, 105, 0.5);
}

.info_delay div {
	display: inline-block;
	text-align: left;
	margin-left: 10px;
}

.delayInfo{
	float: right;
	height: 16px;
	overflow: hidden;
}

/** 天气预报 **/

.col_left .row_2 {
	padding: 10px 0;
	top: 500px;
	width: 320px;
}


.wea_city_row {
	height: 125px;
}

.wea_info {
	margin-left: 10px;
	height: 100px;
}

.wea_info .col_1,
.wea_info .col_2,
.wea_info .col_3 {
	float: left;
	margin-top: 20px;
	height: 80px;
}

.wea_info .col_1 {
	width: 70px;
	margin: 10px 20px 0 0;
	text-align: center;
}

.weather_ico {
	position: relative;
	font-size: 30px;
}

.wea_info .col_2,
.wea_info .col_3 {
	width: 110px;
}

.wea_info .col_2 div,
.wea_info .col_3 div {
	height: 20px;
	line-height: 20px;
}

.wea_info .col_2 span,
.wea_info .col_3 span {
	margin-right: 5px;
}


/* 着陆机场跑道情况 */
.col_left .row_3 {
	top: 760px;
}


.col_left_row .airport_run .table_runway {
	width: 100%;
	margin-top: 20px;
	text-align: center;
	border-collapse: collapse
}

.col_left_row .airport_run .table_runway tr {
	height: 32px;
	background: #071E40;
	border-bottom: 1px solid #071E40;
}

.col_left_row .airport_run .table_runway tr:nth-child(odd) {
	background: #043667;
}

.col_left_row .airport_run .table_runway tr:first-child {
	color: #a5cdfc;
}

.col_left_row .airport_run .table_runway td {
	word-break: break-all;
	border-left: solid #004a86 1px;
}

.col_left_row .airport_run .table_runway th {
	width: 25%;
	text-align: center;
	color: #00a9ff;
	border-right: solid #004a86 1px;
}


/* col_btm */


.mid_btm {
	position: absolute;
	top: 710px;
	left: 420px;
	height: 300px;
}

.mid_btm .mid_btm_left {
position: absolute;
    top: 230px;
    width: 510px;
}

.mid_btm_left .inputGroups {
	position: relative;
	top: 15px;
}

.mid_btm_left input,
.mid_btm_left select,
.mid_btm_left button {
	width: 108px;
	height: 20px;
	background: #082442;
	padding: 5px;
	border-radius: 5px;
	border: 1px solid #044898;
	color: #00a9ff;
	cursor: pointer;
	margin-right: 8px;
}

.mid_btm_left select,
.mid_btm_left button {
	height: 32px;
}

input:focus {
	outline: none;
	border-radius: 5px;
}

/* WebKit browsers */
input::-webkit-input-placeholder {
	color: #00a9ff;
	font-size: 12px;
}

/* Mozilla Firefox 4 to 18 */
input:-moz-placeholder {
	color: #00a9ff;
	font-size: 12px
}

/* Mozilla Firefox 19+ */
input::-moz-placeholder {
	color: #00a9ff;
	font-size: 12px
}

/* Internet Explorer 10+ */
input:-ms-input-placeholder {
	color: #00a9ff;
	font-size: 12px
}


.mid_btm .mid_btm_right {
position: absolute;
    left: 560px;
    height: 90px;
    width: 548px;
}

.mid_btm_right .rowBg {
	background: url(../img/icon_midBtmRight_r1.png) no-repeat top left;
}


.mid_btm_right_row .col {
	display: inline-block;
	width: 190px;
}

.mid_btm_right_row .row {
	padding-left: 30px;
	line-height: 16px;
	margin-bottom: 20px;
}

.mid_btm_right_row .tt {
	margin-top: 20px;
}
.mid_btm_right_row .silosImg.CY{
	background-image: url('../../img/a3.3.plane_layout_N_CY.png');
}
.mid_btm_right_row .silosImg.Y{
	background-image: url('../../img/a3.3.plane_layout_N_Y.png');
}
.mid_btm_right_row .silosImg.W_CY{
	background-image: url('../../img/a3.3.plane_layout_W_CY.png');
}
.mid_btm_right_row .silosImg.W_Y{
	background-image: url('../../img/a3.3.plane_layout_W_Y.png');
}
.mid_btm_right_row .silosImg {
	position: absolute;
	top: 170px;
	width: 500px;
	height: 120px;
	background-size: 100% 100%;
}
.mid_btm_right_row .silosImg .actype {
	position: absolute;
	font-size: 12px;
	top: 55px;
	left: 36px;
}
.mid_btm_right_row .silosImg .lb {
	position: absolute;
	font-size: 12px;
	left: 36px;
	top: 73px;
}

.mid_btm_right_row .zwbj {
	position: relative;
	top: 20px;
	left: 85px;
	width: 280px;
	height: 80px;
	background: url(../img/mid_btm_zcbj.png) no-repeat center;
}

.mid_btm_right_row .mid_btm_info {
	position: relative;
	top: 150px;
}

.mid_btm_right_row .mid_btm_info .col_1 {
	display: inline-block;
	width: 100px;
}

.mid_btm_right_row .mid_btm_info .col_2 {
	display: inline-block;
	width: 150px;
}

.mid_btm_right_row .mid_btm_info span {
	margin-left: 5px;
}


/* col_right */

.col_right {
	position: absolute;
	top: 80px;
	left: 1530px;
	padding: 10px;
	width: 350px;
}

.col_right .col_right_row {
	margin-bottom: 25px;
}

.col_right .row_line {
	margin-top: 10px;
}

.col_right .row_line div {
	height: 20px;
	line-height: 20px;
}

.col_right .row_line_lk {
	position: relative;
	top: 20px;
	margin-left: 90px;
}

.col_right .row_line_lk div {
	margin-bottom: 20px;
}

.col_right_row .icon_dzck {
	position: absolute;
	top: 185px;
	width: 72px;
	height: 72px;
	background: url(../img/icon_right_dzck.png) no-repeat center;
}

.col_right .icon_ryzr,
.col_right .icon_ryzc {
	position: absolute;
	top: 484px;
	width: 25px;
	height: 25px;
	background: url(../img/icon_right_ryzr.png) no-repeat center;
}


.col_right .icon_ryzc {
	background: url(../img/icon_right_ryzc.png) no-repeat center;
	top: 628px;
}

.col_right .row_line_zj {
	position: relative;
	top: 15px;
}

.passenger {
	position: relative;
	top: 14px;
	left: 27px;
	width: 320px;
	height: 150px;
}

.passenger .row1 {
	position: relative;
	top: 15px;
	left: -25px;
	width: 320px;
}

.passenger .row1 li {
	display: inline-block;
	width: 70px;
	height: 48px;
	margin-right: 7px;
	text-align: center;
	background: url(../img/right_zj_bg1.png) no-repeat center;
}

.passenger .row1 .li_up {
	padding-top: 6px;
}

.passenger .row2 {
	position: relative;
	width: 320px;
	top: 27px;
	left: -24px;
}

.passenger .row2 li {
	display: inline-block;
	width: 55px;
	height: 56px;
	margin-right: 6px;
	text-align: center;
	background: url(../img/right_zj_bg2.png) no-repeat center;
}

.passenger .row2 .li_up {
	padding-top: 10px;
}


.transInfo_title_1 {
	position: relative;
	top: 25px;
	left: 30px;
}

.transInfo_title_2 {
	position: relative;
	top: 25px;
	left: 30px;
}

.tablebox {
	position: relative;
	height: 100px;
	width: 320px;
	overflow: hidden;
	top: 40px;
	z-index: 10;
	background-color: #0C1534;
}

.tbl-header {
	width: 100%;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 999;
}

.tbl-body {
	width: 100%;
	position: absolute;
	top: 0;
	left: 0;
}

.tablebox table {
	width: 100%;
}

.tablebox table th,
.tablebox table td {
	font-size: 12px;
	height: 32px;
	line-height: 32px;
	font-weight: normal;
	text-align: center;
}

.tablebox table tr th {
	background-color: #043365;
	cursor: pointer;
}

.tablebox table tr td {
	border-right: 1px solid #014F92;
	background-color: transparent;
}


.tablebox table td:last-child {
	border-right: 0;
}

.tbl-body tr:nth-child(even) td,
.tbl-body1 tr:nth-child(even) td {
	background-color: #061D43;
}

.tablebox table tr td span,
.tablebox table tr td span {
	font-size: 24px;
}

.row_wxrw {
	margin-top: 50px;
	margin-bottom: 0;
}

.tabBox {
	position: relative;
	top: 20px;
	width: 280px;
	height: 80px;
	background: #04316C;
	border: 1px solid #00579e;
	padding: 20px;
}

.tabBox  .btn{
	top: 10px;
	position: absolute;
	display: inline-block;
	margin-right: 10px;
	height: 22px;
	width: 22px;
	padding: 0;
	border-radius: 11px;
	border: 1px solid #1a52a5;
	cursor: pointer;
	pointer-events: all
}


.tabBox .btn_prev{
	left: 260px;
	background: #012869 url(../../img/a4.2_btn_arr1.png) no-repeat center;
}
.tabBox .btn_next{
	left: 290px;
	background: #012869 url(../../img/a4.2_btn_arr2.png) no-repeat center;
}
.tabBox .disabled{
	opacity: 0.4;
	pointer-events: none;
}

.tabBox .tt {
	display: inline-block;
	width: 60px;
	text-align: right;
	margin-right: 5px;
}
