
/*实时客票*/

.page-wrapper {
  background: url(../img/a2.3.bg.png) no-repeat center top;
}

.earth_wrap {
  position: absolute; 
  width:690px; 
  height:690px; 
  left: 338px; 
  top: 114px;
  pointer-events: none;
}
#earth3d {
  position: absolute; 
  width:690px; 
  height: 690px; 
  pointer-events: none;
}
.earthlight {
  position: absolute;
  left:344px; 
  top:120px; 
  width:676px; 
  height:676px; 
  background: url(../img/earth_light.png) no-repeat center center; 
  background-size: 520px 520px;
  pointer-events: none;
}




.pagetitle{
  position: absolute;
  width: 100%;
  height: 36px;
  top: 67px;
  text-align: center;
  background: url(../img/title-bot-line.png) no-repeat center 25px;
}
.maintitle{
  color: #7fc4ff;
  width: 650px;
  font-size: 27px;
  padding: 5px 20px;
  margin: 0 auto;
  text-shadow: 0 0 10px #0d2452,0 0 10px #0d2452,0 0 20px #0c2d68,0 0 20px #0c2d68;
}
.submaintitle{
  color: #7fc4ff;
  width: 350px;
  font-size: 22px;
  padding: 0 20px;
  margin: 0 auto;
}

#main_cb_type {
  position: absolute;
  top: 51px;
  left: 632px;
  height: 30px;
  width: 100px;
}

#main_cb_type .combobox_label{
  padding: 4px 6px;
  background: url(../img/combobox_arr.png) no-repeat 80px center;
}

.combobox_date {
  left: 900px;
  top: 102px;
}

/* --- */
.block_l1 {
  height: 310px;
  left: 15px;
  top: 127px;
}
.block_l1 .cont{
  height: 228px;
}
.block_l1 .blk,
.block_l2 .blk
{
  width: 140px;
  height: 90px;
  border-radius: 4px;
  border: 1px solid #5a9be6;
  overflow: hidden;
  transform: rotate(0deg);
  background-color: #0b55a5;
}
.block_l1 .blk .head,
.block_l2 .blk .head
{
  width: 100%;
  height: 29px;
  background-color: #09488e;
  padding: 4px 0 0 6px;
}
.block_l1 .blk .con,
.block_l2 .blk .con
{
  padding: 0 0 0 6px;
}
.block_l1 .blk1
{
  position: absolute;
  top: 126px;
  left: 8px;
}
.block_l1 .blk2
{
  position: absolute;
  top: 126px;
  left: 160px;
}




/* --- */
.block_l2 {
  left: 15px;
  top: 407px;
}
.block_l2 .cont{
  height: 293px;
}
.block_l2 .blk1
{
  position: absolute;
  top: 192px;
  left: 8px;
}
.block_l2 .blk2
{
  position: absolute;
  top: 192px;
  left: 160px;
}



/* --- */
.block_r1 {
  right: 15px;
  top: 87px;
  z-index: 92;
}
.block_r1 .cont{
  height: 204px;
}
.block_r1 .toptable{
  width: 280px;
  height: 180px;
  margin: 10px 15px 20px 15px;
  font-size: 12px;
}
.block_r1 .toptable th, 
.block_r1 .toptable td{
  padding: 5px 3px 5px 6px;
}

.toptable td{
  background: url(../img/a2.2.2-tablecellbg.png) no-repeat left bottom;
}
.toptable td span{
  height: 12px;
  display: inline-block;
}



/* --- */
.block_r2 {
  right: 15px;
  top: 330px;
  z-index: 91;
}
.block_r2 .cont{
  height: 166px;
}
.block_r2 .chartctrl{
  top: 20px;
}

/* --- */
.block_r3 {
  right: 15px;
  top: 535px;
  z-index: 90;
}
.block_r3 .cont{
  height: 185px;
}





/*计数器*/
#counterContainer {
  height: 40px;
  margin:  0 auto;
  padding-left: 60px;
}

.number-div > div {
  vertical-align: middle;
}

.number-group {
  float: left;
  height: 35px;
  overflow: hidden;
  margin-left: 5px;
  padding-left: 5px;
  border: 1px solid rgba(255,255,255,0.5);
}

.number-spliter {
  float: left;
  width: 20px;
  height: 40px;
  line-height: 36px;
  font-size: 12px;
  float: left;
  margin: 0 3px;
  padding-top: 8px;
}
 
.number-div .number {
  float: left;
  position: relative;
  width: 24px;
  height: 35px;
  line-height: 35px;
  font-size: 32px;
  overflow: hidden;
}
 
.number-div .comma {
  font-size: 30px;
  line-height: 38px;
  height: 28px;
  margin: 0 5px;
}

.numbers-view {
  position: absolute;
}

.numbers-view div {
  position: absolute;
}



/*计数器 end*/



