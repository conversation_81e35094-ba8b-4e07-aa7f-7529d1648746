//showLoading();

var comp_code = "PN";
var comp_id = "100500";

var BASE_CITY_LIST = {
  CKG: "重庆",
  CGO: "郑州",
};

var ARP_NAME_LIST = {
  CKG: "重庆",
  CGO: "郑州",
  SZX: "深圳",
  SYX: "三亚",
  HFE: "合肥",
};

var ARP_COORD_LIST = {
  CKG: [106.557859, 29.569499],
  CGO: [113.634869, 34.754863],
  SZX: [114.063977, 22.548991],
  SYX: [109.421396, 18.311463],
  HFE: [117.241492, 31.824614],
};

var ARP_ID_LIST = {
  CKG: "2563",
  CGO: "2503",
  SZX: "2499",
  SYX: "2510",
  HFE: "2546",
};

var statusMap = {
  ARR: "落地",
  NDR: "落地",
  ATD: "推出",
  ATA: "到达",
  CNL: "取消",
  DEL: "延误",
  DEP: "起飞",
  RTR: "返航",
  SCH: "计划",
};

var comp_cause = [
  "飞机故障",
  "运力调配",
  "工程机务",
  "航班计划",
  "航材保障",
  "航务保障",
  "机组保障",
  "乘务组",
  "空警安全员",
  "地面保障",
  "货运保障",
  "其他航空公司原因",
];
var none_cause = [
  "流量控制",
  "公共安全",
  "机场",
  "军事活动",
  "空管",
  "离港系统",
  "联检",
  "旅客",
  "民航局航班时刻安排",
  "天气原因",
  "油料",
];

var actypelist = ["320", "319"];

var all_flight_list;

var rootObj = {};

function loadAll() {
  // ------------------------------------------------------------------------
  // 获取航班信息
  // ------------------------------------------------------------------------
  var flightInfoList;

  // 开始结束时间
  var date = new Date();
  var mm = date.getMonth() + 1;
  var dd = date.getDate();
  if (mm < 10) {
    mm = "0" + mm;
  }
  if (dd < 10) {
    dd = "0" + dd;
  }
  var stdStart = date.getFullYear() + "-" + mm + "-" + dd + " 00:00:00";
  var stdEnd = date.getFullYear() + "-" + mm + "-" + dd + " 23:59:59";

  var param = {
    stdStart: stdStart,
    stdEnd: stdEnd,
    acOwner: comp_code,
    statusList: "",
  };
  $.ajax({
    type: "post",
    url: "/bi/web/getStandardFocFlightInfo",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var list = response.data;
      flightInfoList = {};

      for (var i = list.length - 1; i >= 0; i--) {
        var obj = list[i];

        flightInfoList[obj.flightNo] = obj;
      }

      setAirlines();
    },
    error: function () {},
  });

  // ------------------------------------------------------------------------
  // 各种人工指标
  // ------------------------------------------------------------------------

  var settings;

  var param = {
    mode: "query",
  };

  $.ajax({
    type: "post",
    url: "/bi/web/west_setting",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      //checkLogin(response);

      if (response.data != undefined) {
        settings = response.data[0];

        //
        var msg = settings.NEWS_MESSAGE;
        if (msg.length > 30) {
          $("#welcome_msg").html(
            '<marquee direction="left" scrollamount="10">' + msg + "</marquee>"
          );
        } else {
          $("#welcome_msg").text(msg);
        }

        for (var key in settings) {
          if ($("#" + key).length) $("#" + key).text(settings[key]);
        }
      }
    },
    error: function (jqXHR, txtStatus, errorThrown) {
      console.log("----error");
    },
  });

  // ------------------------------------------------------------------------
  // 月度／年度正常率
  // ------------------------------------------------------------------------

  var param = {
    SOLR_CODE: "FAC_COMP_KPI",
    COMP_CODE: comp_code,
    KPI_CODE: "NORMAL_NO_T,EXCUTED_NO,CANCEL_NO",
    VALUE_TYPE: "kpi_value_d",
    DATE_TYPE: "M",
    OPTIMIZE: 1,
    LIMIT: 12,
  };

  $.ajax({
    type: "post",
    contentType: "application/json",
    url: "/bi/query/getkpi?normalRate_month",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var normalMonth = response.data[comp_code]["NORMAL_NO_T"]["M"];
      var executedMonth = response.data[comp_code]["EXCUTED_NO"]["M"];
      var cancelMonth = response.data[comp_code]["CANCEL_NO"]["M"];
      var normal_rate_list = [];
      // 正常率趋势 每月
      var date = new Date();
      var m = date.getMonth() + 1;
      var yy = date.getFullYear();
      for (var i = 0; i < m; i++) {
        var mm = i + 1;
        if (mm < 10) {
          mm = "0" + mm;
        }
        var month = "" + yy + mm;
        var exceuteNum = getNumber(executedMonth[month]);
        var normalNum = getNumber(normalMonth[month]);
        var cancelNum = getNumber(cancelMonth[month]);
        var rate = 0;
        if (exceuteNum > 0 && normalNum > 0) {
          rate = Math.round((normalNum / (exceuteNum + cancelNum)) * 1000) / 10;
        }

        normal_rate_list.push(rate);
      }

      setNormalRateTrendYear(normal_rate_list);
    },
    error: function () {},
  });

  // 正常率趋势 每日
  var param = {
    SOLR_CODE: "FAC_COMP_KPI",
    COMP_CODE: comp_code,
    KPI_CODE: "NORMAL_NO_T,EXCUTED_NO,CANCEL_NO",
    VALUE_TYPE: "kpi_value_d",
    DATE_TYPE: "D",
    OPTIMIZE: 1,
    LIMIT: 31,
  };

  $.ajax({
    type: "post",
    url: "/bi/query/getkpi?day_trend",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      setNormalRateTrendMonth(response.data);
    },
    error: function () {},
  });

  function getNumber(value) {
    if (value != undefined && value != "") {
      var val1 = Number(value);
      if (isNaN(val1)) {
        return 0;
      }
      return val1;
    }
    return 0;
  }

  var param = {
    SOLR_CODE: "FAC_COMP_KPI",
    COMP_CODE: comp_code,
    KPI_CODE:
      "NORMAL_NO_T,EXCUTED_NO,CANCEL_NO,EXCUTED_NO_INT,NORMAL_NO_T_INT,ORI_NORMAL_NO,ORI_TOFF_TDWN_NO,CANCEL_NO_INT,ORI_CANCEL_NO",
    VALUE_TYPE: "kpi_value_d",
    DATE_TYPE: "Y",
    OPTIMIZE: 1,
    LIMIT: 1,
  };

  $.ajax({
    type: "post",
    url: "/bi/query/getkpi?normalRate",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var date = new Date();
      var yy = date.getFullYear();

      // ------------------------------------------------------------------------
      // 正常率
      // ------------------------------------------------------------------------
      // 通过esb接口获得国际航线正常率
      getNormalRate(yy);
      // var noraml = Number(response.data[comp_code]['NORMAL_NO_T']['Y'][yy]);
      // var executed = Number(response.data[comp_code]['EXCUTED_NO']['Y'][yy]);
      // var cancel = Number(response.data[comp_code]['CANCEL_NO']['Y'][yy]);

      // normal_rate_year = Math.round((noraml / (cancel + executed)) * 1000) / 10;
      // setNormalRateChartYear(normal_rate_year / 100);

      // ------------------------------------------------------------------------
      // 始发正常率
      // ------------------------------------------------------------------------

      var noraml1 = getNumber(
        response.data[comp_code]["ORI_NORMAL_NO"]["Y"][yy]
      );
      var executed1 = getNumber(
        response.data[comp_code]["ORI_TOFF_TDWN_NO"]["Y"][yy]
      );
      // var cancel1 = getNumber(response.data[comp_code]['ORI_CANCEL_NO']['Y'][yy]);

      var rate1 = noraml1 / executed1;
      if (isNaN(rate1)) {
        rate1 = 0;
      }
      setNormalRateChartOri(rate1);

      // ------------------------------------------------------------------------
      // 国际航线正常率
      // ------------------------------------------------------------------------
      // // 通过esb接口获得国际航线正常率
      // getNormalRate(yy, "I");
      var noraml2 = getNumber(
        response.data[comp_code]["NORMAL_NO_T_INT"]["Y"][yy]
      );
      var executed2 = getNumber(
        response.data[comp_code]["EXCUTED_NO_INT"]["Y"][yy]
      );
      var cancel2 = getNumber(
        response.data[comp_code]["CANCEL_NO_INT"]["Y"][yy]
      );

      var rate2 = noraml2 / (cancel2 + executed2);
      if (isNaN(rate2)) {
        rate2 = 0;
      }
      setNormalRateChartInt(rate2);
    },
    error: function () {},
  });

  // ------------------------------------------------------------------------
  // 通过esb接口获得年度累计正常率
  // ------------------------------------------------------------------------

  function getNormalRate(year, fltType = null) {
    // 开始时间: 当年1月1日 16:00:00 UTC
    var stdStartUtcTime = year + "-01-01 16:00:00";
    // 结束时间: 当年12月31日 15:59:59 UTC
    var stdEndUtcTime = year + "-12-31 15:59:59";
    // var params = {
    //   companyCodes: comp_code,
    //   isLongRegsNotIn: false,
    //   stdEndUtcTime: stdEndUtcTime,
    //   stdStartUtcTime: stdStartUtcTime,
    //   fltType: fltType,
    // };
    // var params = {
    //   companyCodes: comp_code,
    //   dateId: year,
    //   dateType: "Y",
    //   kpiCodes: ["NORMAL_RATE_ZT"],
    // };

    var params = {
      dateType: "Y",
      compCode: comp_code,
      kpiCodes: ["NORMAL_RATE_ZT"],
      dateId: String(year),
    };

    $.ajax({
      type: "post",
      url: "/bi/spring/facCompKpi/queryKpi.json",
      contentType: "application/json",
      dataType: "json",
      async: true,
      data: JSON.stringify(params),
      success: function (res) {
        var kpiValue = 0;
        if (res && res.data && res.data["NORMAL_RATE_ZT"]) {
          kpiValue = res.data["NORMAL_RATE_ZT"].kpiValue;
        }
        setNormalRateChartYear(Number(kpiValue));
        // var response = res;
        // var normal_rate_year = Number(response.pfdappPercent);
        // if (fltType == "I") {
        //   setNormalRateChartInt(normal_rate_year / 100);
        // } else {
        //   setNormalRateChartYear(normal_rate_year / 100);
        // }
      },
      error: function () {},
    });
  }

  function setNormalRateChartInt(rate) {
    if (settings == undefined) {
      setTimeout(setNormalRateChartInt, 10, rate);
      return;
    }

    var id = 2;
    var rate2 = settings.NORMAL_RATE_YEAR_INT / 100;
    if (isNaN(rate2)) {
      rate2 = 0;
    }

    $("#cvs_normal_rate2_lb1").text(Math.round(rate * 1000) / 10 + "%");
    $("#cvs_normal_rate2_lb2").text(rate2 * 100 + "%");

    drawRoundChart(id, rate2, rate);
  }

  function setNormalRateChartOri(rate) {
    if (settings == undefined) {
      setTimeout(setNormalRateChartOri, 10, rate);
      return;
    }

    var id = 3;
    var rate2 = settings.NORMAL_RATE_YEAR_ORI / 100;
    if (isNaN(rate2)) {
      rate2 = 0;
    }

    $("#cvs_normal_rate3_lb1").text(Math.round(rate * 1000) / 10 + "%");
    $("#cvs_normal_rate3_lb2").text(rate2 * 100 + "%");

    drawRoundChart(id, rate2, rate);
  }

  // -----------------------------------
  // 返航备降率  平均延误事件   4小时延误取消率    平均过站时间
  // -----------------------------------

  var param = {
    SOLR_CODE: "FAC_COMP_KPI",
    COMP_CODE: comp_code,
    KPI_CODE:
      "SCH_NO,CANCEL_NO,TURNBACK_NO,DIV_NO,EXCUTED_NO,DELAY_NO_240,DELAY_TIME,CABIN_TIME,CABIN_NO",
    VALUE_TYPE: "kpi_value_d",
    DATE_TYPE: "M",
    OPTIMIZE: 1,
    LIMIT: 12,
  };

  $.ajax({
    type: "post",
    url: "/bi/query/getkpi",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var dd1 = response.data[comp_code]["SCH_NO"]["M"];
      var dd2 = response.data[comp_code]["CANCEL_NO"]["M"];
      var dd3 = response.data[comp_code]["DIV_NO"]["M"];

      var dd4 = response.data[comp_code]["EXCUTED_NO"]["M"];
      var dd5 = response.data[comp_code]["DELAY_NO_240"]["M"];
      var dd6 = response.data[comp_code]["DELAY_TIME"]["M"];

      var dd7 = response.data[comp_code]["TURNBACK_NO"]["M"];
      var dd8 = response.data[comp_code]["CABIN_TIME"]["M"]; // 舱门时间
      var dd9 = response.data[comp_code]["CABIN_NO"]["M"]; // 舱门时间班次

      var d = new Date();
      var thisyear = d.getFullYear() + "00";

      var schno = 0;
      var cancelno = 0;
      var divno = 0;
      var exeno = 0;
      var delay240 = 0;
      var delaytime = 0;
      var turnback = 0;
      var cabin_time = 0;
      var cabin_no = 0;

      var latest_time = 0;

      for (var time in dd1) {
        var val1 = Number(dd1[time]);
        var val2 = Number(dd2[time]);
        var val3 = Number(dd3[time]);
        var val4 = Number(dd4[time]);
        var val5 = Number(dd5[time]);
        var val6 = Number(dd6[time]);
        var val7 = Number(dd7[time]);
        var val8 = Number(dd8[time]);
        var val9 = Number(dd9[time]);

        if (val1 >= 0 && val2 >= 0 && val3 >= 0) {
          if (Number(time) >= Number(thisyear)) {
            schno += val1;
            cancelno += val2;
            divno += val3;
            exeno += val4;
            delay240 += val5;
            delaytime += val6;
            turnback += val7;
            cabin_time += val8;
            cabin_no += val9;
          }
        }

        if (Number(time) > latest_time) {
          latest_time = Number(time);
        }
      }

      var return_rate_y = (turnback + divno) / schno;

      $("#year_avg_val_return").text(Math.round(return_rate_y * 10000) / 100);
      drawRoundChart(8, -1, return_rate_y);

      var avg_time_y = Math.round(delaytime / exeno);
      var dly4_cancel_rate_y = (cancelno + delay240) / schno;

      $("#ACG_DELAY_TIME_Y").text(avg_time_y);
      $("#DELAY_CANCEL_Y").text(Math.round(dly4_cancel_rate_y * 100));

      var pass_time_y = Math.round((cabin_time / cabin_no) * 10) / 10;

      $("#shifts_m").text(Math.round(dd1[latest_time]));
      $("#shifts_y").text(schno);

      // 日 ------------

      var param = {
        SOLR_CODE: "FAC_COMP_KPI",
        COMP_CODE: comp_code,
        KPI_CODE:
          "SCH_NO,CANCEL_NO,DIV_NO,EXCUTED_NO,DELAY_NO_240,DELAY_TIME,CABIN_TIME_AVG",
        VALUE_TYPE: "kpi_value_d",
        DATE_TYPE: "D",
        OPTIMIZE: 1,
        LIMIT: 1,
      };

      $.ajax({
        type: "post",
        url: "/bi/query/getkpi",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          var dd1 = response.data[comp_code]["SCH_NO"]["D"];
          var dd2 = response.data[comp_code]["CANCEL_NO"]["D"];
          var dd3 = response.data[comp_code]["DIV_NO"]["D"];

          var dd4 = response.data[comp_code]["EXCUTED_NO"]["D"];
          var dd5 = response.data[comp_code]["DELAY_NO_240"]["D"];
          var dd6 = response.data[comp_code]["DELAY_TIME"]["D"];

          var dd8 = response.data[comp_code]["CABIN_TIME_AVG"]["D"]; // 平均舱门时间

          var schno = 0;
          var cancelno = 0;
          var divno = 0;
          var exeno = 0;
          var delay240 = 0;
          var delaytime = 0;
          var cabin_avg_time = 0;

          for (var time in dd1) {
            var val1 = Number(dd1[time]);
            var val2 = Number(dd2[time]);
            var val3 = Number(dd3[time]);
            var val4 = Number(dd4[time]);
            var val5 = Number(dd5[time]);
            var val6 = Number(dd6[time]);
            var val8 = Number(dd8[time]);
            if (val1 >= 0 && val2 >= 0 && val3 >= 0) {
              schno += val1;
              cancelno += val2;
              divno += val3;
              exeno += val4;
              delay240 += val5;
              delaytime += val6;
              cabin_avg_time += val8;

              break;
            }
          }

          var avg_time_d = Math.round((delaytime / schno) * 10) / 10;
          var dly4_cancel_rate_d = (cancelno + delay240) / schno;

          $("#ACG_DELAY_TIME_D").text(avg_time_d);
          $("#DELAY_CANCEL_D").text(Math.round(dly4_cancel_rate_d * 1000) / 10);

          var pass_time_d = Math.round(cabin_avg_time * 10) / 10;
          $("#PASS_TIME_D").text(pass_time_d);
          $("#PASS_TIME_Y").text(pass_time_y);

          drawRoundChart2(
            4,
            settings.ACG_DELAY_TIME_YEAR / 1440,
            avg_time_y / 1440,
            avg_time_d / 1440
          ); // 平均延误时间 24*60=1440
          drawRoundChart(
            5,
            settings.DELAY_CANCEL_YEAR / 100,
            dly4_cancel_rate_y,
            dly4_cancel_rate_d
          ); // 4小时延误取消率

          drawRoundChart2(
            6,
            settings.AVG_PASS_TIME_YEAR / 1440,
            pass_time_y / 1440,
            pass_time_d / 1440
          ); // 平均过站时间
        },
        error: function () {},
      });

      // ---------------
    },
    error: function () {},
  });

  // ------------------------------------------------------------------------
  // 各种航班统计信息。。。。
  // ------------------------------------------------------------------------
  var date = new Date();
  var mm = date.getMonth() + 1;
  var dd = date.getDate();
  if (mm < 10) {
    mm = "0" + mm;
  }
  if (dd < 10) {
    dd = "0" + dd;
  }
  var stdEndUtcTime = date.getFullYear() + "-" + mm + "-" + dd + " 15:59:59";
  var yesterday_ts = date.getTime() - 86400000;
  date.setTime(yesterday_ts);
  mm = date.getMonth() + 1;
  dd = date.getDate();
  if (mm < 10) {
    mm = "0" + mm;
  }
  if (dd < 10) {
    dd = "0" + dd;
  }
  var stdStartUtcTime = date.getFullYear() + "-" + mm + "-" + dd + " 16:00:00";

  //var utc_timestamp = Math.round(time.getTime()/1000) - 8*3600;
  /*
  focStaticApi.flightAmountStatic

  stdStartUtcTime=2017-02-23 16:00:00
  stdEndUtcTime=2017-02-24 15:59:59
  昨天16:00:00 到今天 15:59:59
  */
  var param = {
    stdStartUtcTime: stdStartUtcTime,
    stdEndUtcTime: stdEndUtcTime,
    companyCodes: comp_code,
    AcTypeList: "",
  };

  $.ajax({
    type: "post",
    url: "/bi/redis/7x2_flt_sts",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var excuted_total = Number(response.cftc); //已执行航班总数
      var canel_total = Number(response.qftc1); //今日取消航班总数
      var executed_normal = Number(response.cfrtc); //已执行航班中正常航班总数

      var sch_total = Number(response.pftc); //计划航班总数
      var sch_normal = Number(response.pfrtc); //计划航班中正常航班总数
      var pfPercent = Number(response.pfdappPercent); //计划航班中的正常航班比率
      var cfPercent = Number(response.cfdappPercent); //已执行航班中的正常航班比率

      today_normal_rate_from_api = Number(
        Math.round((executed_normal * 1000) / (excuted_total + canel_total)) /
          1000
      );

      $("#shifts_d").text(sch_total);
    },
    error: function () {},
  });

  // ------------------------------------------------------------------------
  // 正常率 日
  // ------------------------------------------------------------------------
  // rate 通过esb接口获得正常率
  // rate_from_db 中间表获得正常率
  var today_normal_rate_from_api;
  var today_normal_rate_from_db;

  // ------------------------------------------------------------------------
  // 正常率 年
  // ------------------------------------------------------------------------

  function setNormalRateChartYear(rate) {
    if (settings == undefined) {
      setTimeout(setNormalRateChartYear, 10, rate);
      return;
    }

    var id = 1;
    var rate2 = settings.NORMAL_RATE_YEAR / 100;

    $("#cvs_normal_rate1_lb1").text(Math.round(rate * 1000) / 10 + "%");
    $("#cvs_normal_rate1_lb2").text(rate2 * 100 + "%");

    drawRoundChart(id, rate2, rate);
  }

  function drawRoundChart(id, rate, rate2, rate3) {
    $("#col_l .chart" + id).show();

    var canvas = document.getElementById("cvs_normal_rate" + id);
    var context = canvas.getContext("2d");
    context.clearRect(0, 0, canvas.width, canvas.height);
    var x = canvas.width / 2;
    var y = canvas.height / 2;

    // draw back
    var radius = 66;
    var startAngle = Math.PI - Math.PI / 5;
    var endAngle = startAngle + Math.PI + (Math.PI / 5) * 2;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle, endAngle, counterClockwise);
    context.lineWidth = 7;
    context.strokeStyle = "#3435A7";
    context.stroke();

    // draw overlay 1 ==== 实际
    if (rate >= 0) {
      var radius = 50;
      var startAngle2 = startAngle;
      var endAngle2 = startAngle + (endAngle - startAngle) * rate;
      var counterClockwise = false;

      context.beginPath();
      context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
      context.lineWidth = 7;

      // linear gradient
      if (rate < 0.5) {
        var color = context.createLinearGradient(
          0,
          canvas.height,
          canvas.width * rate,
          0
        );
      } else if (rate < 0.8) {
        var color = context.createLinearGradient(
          0,
          canvas.height,
          canvas.width * rate,
          0
        );
      } else {
        var color = context.createLinearGradient(
          0,
          canvas.height,
          canvas.width * rate,
          canvas.width / 2
        );
      }
      color.addColorStop(0, "#65498B");
      color.addColorStop(1, "#FFE801");

      context.strokeStyle = color;
      context.stroke();

      // pointer
      var angle = startAngle + (endAngle - startAngle) * rate;
      $("#cvs_normal_rate" + id + "_pointer").css(
        "transform",
        "rotate(" + (angle / Math.PI) * 180 + "deg)"
      );
      $("#cvs_normal_rate" + id + "_pointer").show();
    } else {
      $("#cvs_normal_rate" + id + "_pointer").hide();
    }

    // draw overlay 2 ==== 计划
    if (rate2 >= 0) {
      var radius = 58;
      var startAngle2 = startAngle;
      var endAngle2 = startAngle + (endAngle - startAngle) * rate2;
      var counterClockwise = false;

      context.beginPath();
      context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
      context.lineWidth = 7;

      // linear gradient
      if (rate2 < 0.5) {
        var color = context.createLinearGradient(
          0,
          canvas.height,
          canvas.width * rate2,
          0
        );
      } else if (rate2 < 0.8) {
        var color = context.createLinearGradient(
          0,
          canvas.height,
          canvas.width * rate2,
          0
        );
      } else {
        var color = context.createLinearGradient(
          0,
          canvas.height,
          canvas.width * rate2,
          canvas.width / 2
        );
      }
      color.addColorStop(0, "#446295");
      color.addColorStop(1, "#93FF6E");

      context.strokeStyle = color;
      context.stroke();

      // pointer
      var angle = startAngle + (endAngle - startAngle) * rate2;
      $("#cvs_normal_rate" + id + "b_pointer").css(
        "transform",
        "rotate(" + (angle / Math.PI) * 180 + "deg)"
      );
      $("#cvs_normal_rate" + id + "b_pointer").show();
    } else {
      $("#cvs_normal_rate" + id + "b_pointer").hide();
    }

    if (rate3 >= 0) {
      // draw overlay 3 ====
      var radius = 66;
      var startAngle2 = startAngle;
      var endAngle2 = startAngle + (endAngle - startAngle) * rate3;
      var counterClockwise = false;

      context.beginPath();
      context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
      context.lineWidth = 7;

      // linear gradient
      if (rate3 < 0.5) {
        var color = context.createLinearGradient(
          0,
          canvas.height,
          canvas.width * rate3,
          0
        );
      } else if (rate3 < 0.8) {
        var color = context.createLinearGradient(
          0,
          canvas.height,
          canvas.width * rate3,
          0
        );
      } else {
        var color = context.createLinearGradient(
          0,
          canvas.height,
          canvas.width * rate3,
          canvas.width / 2
        );
      }
      color.addColorStop(0.2, "#3435A7");
      color.addColorStop(1, "#FFFFFF");

      context.strokeStyle = color;
      context.stroke();

      // pointer
      var angle = startAngle + (endAngle - startAngle) * rate3;
      $("#cvs_normal_rate" + id + "c_pointer").css(
        "transform",
        "rotate(" + (angle / Math.PI) * 180 + "deg)"
      );
      $("#cvs_normal_rate" + id + "c_pointer").show();
    } else {
      $("#cvs_normal_rate" + id + "c_pointer").hide();
    }

    // draw lines
    var numslice = 10;
    var radius = 54;
    for (var i = 1; i < numslice; i++) {
      context.beginPath();
      var startAngle =
        Math.PI - Math.PI / 5 + i * ((Math.PI + (Math.PI / 5) * 2) / numslice);
      var endAngle = startAngle + Math.PI * 0.01;
      context.arc(x, y, radius, startAngle, endAngle, false);
      context.lineWidth = 16;
      context.strokeStyle = "#041946";
      context.stroke();
    }
  }

  function drawRoundChart2(id, rate, rate2, rate3) {
    $("#col_l .chart" + id).show();

    var canvas = document.getElementById("cvs_normal_rate" + id);
    var context = canvas.getContext("2d");
    context.clearRect(0, 0, canvas.width, canvas.height);
    var x = canvas.width / 2;
    var y = canvas.height / 2;

    // draw back
    var radius = 60;
    var startAngle = -Math.PI / 2;
    var endAngle = startAngle + Math.PI * 2;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle, endAngle, counterClockwise);
    context.lineWidth = 7;
    context.strokeStyle = "#3435A7";
    context.stroke();

    // draw overlay 1 ==== 实际
    var radius = 44;
    var startAngle2 = startAngle;
    var endAngle2 = startAngle + (endAngle - startAngle) * rate;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
    context.lineWidth = 7;

    // linear gradient
    if (rate < 0.5) {
      var color = context.createLinearGradient(
        0,
        canvas.height,
        canvas.width * rate,
        0
      );
    } else if (rate < 0.8) {
      var color = context.createLinearGradient(
        0,
        canvas.height,
        canvas.width * rate,
        0
      );
    } else {
      var color = context.createLinearGradient(
        0,
        canvas.height,
        canvas.width * rate,
        canvas.width / 2
      );
    }
    color.addColorStop(0, "#65498B");
    color.addColorStop(1, "#FFE801");

    context.strokeStyle = color;
    context.stroke();

    // draw overlay 2 ==== 计划
    var radius = 52;
    var startAngle2 = startAngle;
    var endAngle2 = startAngle + (endAngle - startAngle) * rate2;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
    context.lineWidth = 7;

    // linear gradient
    if (rate2 < 0.5) {
      var color = context.createLinearGradient(
        0,
        canvas.height,
        canvas.width * rate2,
        0
      );
    } else if (rate2 < 0.8) {
      var color = context.createLinearGradient(
        0,
        canvas.height,
        canvas.width * rate2,
        0
      );
    } else {
      var color = context.createLinearGradient(
        0,
        canvas.height,
        canvas.width * rate2,
        canvas.width / 2
      );
    }
    color.addColorStop(0, "#446295");
    color.addColorStop(1, "#93FF6E");

    context.strokeStyle = color;
    context.stroke();

    // draw overlay 3 ==== 平均
    var radius = 60;
    var startAngle2;
    var endAngle2;
    var counterClockwise = false;

    // linear gradient
    if (rate3 <= 0.5) {
      var color = context.createLinearGradient(
        canvas.width / 2,
        0,
        canvas.width / 2,
        canvas.height * rate3
      );
      startAngle2 = startAngle;
      endAngle2 = startAngle + (endAngle - startAngle) * rate3;
    } else if (rate3 <= 0.75) {
      var color = context.createLinearGradient(
        canvas.width,
        canvas.height / 2,
        0,
        canvas.height / 2
      );
      startAngle2 = startAngle + Math.PI / 2;
      endAngle2 = startAngle + (endAngle - startAngle) * rate3;
    } else {
      var color = context.createLinearGradient(
        canvas.width / 2,
        canvas.height,
        canvas.width / 2,
        0
      );
      startAngle2 = startAngle + Math.PI;
      endAngle2 = startAngle + (endAngle - startAngle) * rate3;
    }

    color.addColorStop(0.2, "#3435A7");
    color.addColorStop(1, "#FFFFFF");

    context.beginPath();
    context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
    context.lineWidth = 7;
    context.strokeStyle = color;
    context.stroke();

    // pointer
    var angle = startAngle + (endAngle - startAngle) * rate3;
    $("#cvs_normal_rate" + id + "_pointer").css(
      "transform",
      "rotate(" + (angle / Math.PI) * 180 + "deg)"
    );
  }

  function drawRoundChart3(id, rate) {
    $("#col_l .chart" + id).show();

    var canvas = document.getElementById("cvs_normal_rate" + id);
    var context = canvas.getContext("2d");
    context.clearRect(0, 0, canvas.width, canvas.height);
    var x = canvas.width / 2;
    var y = canvas.height / 2;

    // draw back
    var radius = 66;
    var startAngle = Math.PI - (Math.PI * 2 * rate) / 2;
    var endAngle = Math.PI + (Math.PI * 2 * rate) / 2;

    context.beginPath();
    context.arc(x, y, radius, 0, 2 * Math.PI, false);
    context.lineWidth = 7;
    context.strokeStyle = "#3435A7";
    context.stroke();

    var radius = 58;
    context.beginPath();
    context.arc(x, y, radius, 0, 2 * Math.PI, false);
    context.lineWidth = 7;
    context.strokeStyle = "#FFBC00";
    context.stroke();
    //
    context.beginPath();
    context.arc(x, y, radius, startAngle, endAngle, false);
    context.lineWidth = 7;
    context.strokeStyle = "#8EFF60";
    context.stroke();

    // draw lines
    var numslice = 12;
    for (var i = 0; i < numslice; i++) {
      context.beginPath();
      var startAngle = i * ((Math.PI * 2) / numslice);
      var endAngle = startAngle + Math.PI * 0.01;
      context.arc(x, y, radius, startAngle, endAngle, false);
      context.lineWidth = 11;
      context.strokeStyle = "#041946";
      context.stroke();
    }
  }

  // ------------------------------------------------------------------------
  // 延误原因 公司／非公司
  // ------------------------------------------------------------------------
  var param = {
    SOLR_CODE: "FAC_COMP_DELAY_CAUSE_RATE_KPI",
    COMP_CODE: comp_code,
    KPI_CODE: "DELAY_NO",
    VALUE_TYPE: "kpi_value_d",
    DATE_TYPE: "M",
    LIMIT: 12,
    OPTIMIZE: 1,
  };

  $.ajax({
    type: "post",
    url: "/bi/query/getkpi",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var data_dly = response.data[comp_code]["DELAY_NO"]["M"];

      var comp_delay_num = 0;
      var none_delay_num = 0;

      var date = new Date();
      var year = date.getFullYear() + "";

      // 公司原因 总数
      for (var time in data_dly) {
        var d = data_dly[time];
        var len2 = comp_cause.length;
        for (var j = 0; j < len2; j++) {
          var causeName = comp_cause[j];
          if (!isNaN(d[causeName])) {
            var val = Number(d[causeName]);
            if (time.indexOf(year) >= 0) {
              if (val > 0) {
                comp_delay_num += Number(val);
              }
            }
          }
        }
      }

      // 非公司原因 总数
      for (var time in data_dly) {
        var d = data_dly[time];
        var len2 = none_cause.length;
        for (var j = 0; j < len2; j++) {
          var causeName = none_cause[j];
          if (!isNaN(d[causeName])) {
            var val = Number(d[causeName]);
            if (causeName == "民航局航班时刻安排") {
              causeName = "时刻安排";
            }
            if (time.indexOf(year) >= 0) {
              if (val > 0) {
                none_delay_num += Number(val);
              }
            }
          }
        }
      }

      var total = comp_delay_num + none_delay_num;
      var rate = total > 0 ? comp_delay_num / total : 0;
      var rate1 = Math.round(rate * 100);
      var rate2 = 100 - rate1;
      $("#comp_delay_ratio").text(rate1 + "%");
      $("#none_delay_ratio").text(rate2 + "%");
      drawRoundChart3(7, rate);
    },
    error: function () {},
  });

  // ------------------------------------------------------------------------
  // 年度 总利用率
  // ------------------------------------------------------------------------

  var ac_kpi = [
    "FLY_TIME", //飞行时间
    "AC_NUM", //飞机架数
  ];
  var param = {
    SOLR_CODE: "FAC_COMP_ACTYPE_KPI",
    COMP_CODE: comp_code,
    KPI_CODE: ac_kpi.join(","),
    VALUE_TYPE: "kpi_value_d", //本期
    ACTYPE: actypelist.join(","),
    DATE_TYPE: "Y",
    LIMIT: 1,
    OPTIMIZE: 1,
  };

  $.ajax({
    type: "post",
    url: "/bi/query/getkpi",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      if (response.data != undefined) {
        var acnum = response.data[comp_code]["AC_NUM"]["Y"];
        var ftime = response.data[comp_code]["FLY_TIME"]["Y"];
        var total_acnum = 0;
        var total_ftime = 0;

        for (var ac in acnum) {
          var lst = acnum[ac];
          if (lst) {
            for (var t in lst) {
              var val = lst[t];
              if (val > 0) {
                total_acnum += Number(val);
              }
            }
          }
        }

        for (var ac in ftime) {
          var lst = ftime[ac];
          if (lst) {
            for (var t in lst) {
              var val = lst[t];
              if (val > 0) {
                total_ftime += Number(val);
              }
            }
          }
        }

        var utime = 0;
        if (total_acnum > 0) {
          utime = total_ftime / total_acnum;
        }

        $("#year_avg_val_use_time").text(Math.round(utime * 10) / 10);

        drawRoundChart2(9, -1, -1, utime / 24);
      }
    },
    error: function () {},
  });

  // ------------------------------------------------------------------------
  // 机队规模
  // ------------------------------------------------------------------------
  var param = {
    SOLR_CODE: "FAC_COMP_ACTYPE_KPI",
    COMP_CODE: comp_code,
    KPI_CODE: "AC_NUM",
    VALUE_TYPE: "kpi_value_d", //本期
    ACTYPE: actypelist.join(","),
    DATE_TYPE: "D",
    LIMIT: 1,
    OPTIMIZE: 1,
  };

  $.ajax({
    type: "post",
    url: "/bi/query/getkpi",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      if (response.data != undefined) {
        var acnum = response.data[comp_code]["AC_NUM"]["D"];
        var acdata = {};

        for (var ac in acnum) {
          acdata[ac] = 0;
          var lst = acnum[ac];
          if (lst) {
            for (var t in lst) {
              var val = lst[t];
              if (val > 0) {
                acdata[ac] += Number(val);
              }
            }
          }
        }

        for (var i = 0; i < actypelist.length; i++) {
          var ac = actypelist[i];
          $("#ac_num_" + ac).text(acdata[ac]);
        }
      }
    },
    error: function () {},
  });

  // ------------------------------------------------------------------------
  // 年吞吐量
  // ------------------------------------------------------------------------
  var param = {
    SOLR_CODE: "FAC_COMP_PSR_KPI",
    COMP_CODE: comp_code,
    KPI_CODE: "NON_FOREIGN_PRS_FLT_TOTAL,FOREIGN_PRS_FLT_TOTAL",
    VALUE_TYPE: "kpi_value_d", //本期
    DATE_TYPE: "Y",
    LIMIT: 1,
    OPTIMIZE: 1,
  };

  $.ajax({
    type: "post",
    url: "/bi/query/getkpi",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      if (response.data != undefined) {
        //   var date = new Date();
        //   var year = date.getFullYear();
        //   var total_trvnum_i = Number(response.data[comp_code]['FOREIGN_PRS_FLT_TOTAL']['Y'][year])
        //   var total_trvnum_l = Number(response.data[comp_code]['NON_FOREIGN_PRS_FLT_TOTAL']['Y'][year])
        var total_trvnum_i = 0;
        var total_trvnum_l = 0;
        var data1 = response.data[comp_code]["FOREIGN_PRS_FLT_TOTAL"]["Y"];
        for (var date in data1) {
          if (data1[date]) {
            total_trvnum_i = Number(data1[date]);
            break;
          }
        }
        var data2 = response.data[comp_code]["NON_FOREIGN_PRS_FLT_TOTAL"]["Y"];
        for (var date in data2) {
          if (data2[date]) {
            total_trvnum_l = Number(data2[date]);
            break;
          }
        }
        $("#year_val_trv_num_l").text(total_trvnum_l);
        $("#year_val_trv_num_i").text(total_trvnum_i);
        $("#year_val_trv_num").text(total_trvnum_i + total_trvnum_l);
      }
    },
    error: function () {},
  });

  // ------------------------------------------------------------------------
  // 正常率走势
  // ------------------------------------------------------------------------

  var normal_rate_date_type = "M";
  var normal_rate_rank_type = "ALL";

  $("#tab_normal_rate_month").on("off");
  $("#tab_normal_rate_month").on("click", function (evt) {
    currentTabIndex = 0;

    normal_rate_date_type = "M";
    $("#tab_normal_rate_month").addClass("selected");
    $("#tab_normal_rate_year").removeClass("selected");
    $("#chart_normal_trend_month").show();
    $("#chart_normal_trend_year").hide();

    clearTimeout(itv_autoSwitchTab);
    itv_autoSwitchTab = setTimeout(autoSwitchTab, time_autoSwitchTab);
  });

  $("#tab_normal_rate_year").on("off");
  $("#tab_normal_rate_year").on("click", function (evt) {
    currentTabIndex = 1;

    normal_rate_date_type = "Y";
    $("#tab_normal_rate_month").removeClass("selected");
    $("#tab_normal_rate_year").addClass("selected");
    $("#chart_normal_trend_month").hide();
    $("#chart_normal_trend_year").show();

    clearTimeout(itv_autoSwitchTab);
    itv_autoSwitchTab = setTimeout(autoSwitchTab, time_autoSwitchTab);
  });

  // 从接口获取月度正常率走势数据
  function setNormalRateTrendMonth() {
    getNormalRateTrendFromApi("D");
  }

  function drawNormalRateTrendmMonth(xAxisData, chartData) {
    var chart = echarts.init(
      document.getElementById("chart_normal_trend_month")
    );
    var option = {};
    option = {
      tooltip: {
        show: true,
      },
      legend: {
        show: false,
        data: [""],
      },
      grid: {
        top: 10,
        left: 40,
        right: 10,
        bottom: 30,
      },
      xAxis: [
        {
          type: "category",
          //data: ['1月','2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月'],
          data: xAxisData,
          nameTextStyle: {
            color: "#516BBA",
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0)", // 轴线颜色
            },
          },
          axisLabel: {
            textStyle: {
              color: "#516BBA", // 轴标签颜色大小
              fontSize: 11,
            },
          },
        },
      ],
      yAxis: [
        {
          type: "value",
          name: "",
          min: 0,
          max: 100,
          interval: 25,
          nameTextStyle: {
            color: "#516BBA",
            fontSize: 10,
          },
          axisLabel: {
            formatter: "{value}%",
            textStyle: {
              color: "#516BBA",
              fontSize: 10,
            },
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0)",
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ["#243474"], // 分割线颜色
            },
          },
        },
      ],
      series: [
        {
          name: "",
          type: "bar",
          barWidth: 6,
          data: chartData,
          itemStyle: {
            normal: {
              color: "#2693FF",
            },
          },
        },
      ],
    };

    chart.setOption(option);
  }

  // function setNormalRateTrendMonth_bak(datalist) {
  //   if (today_normal_rate_from_api == undefined) {
  //     setTimeout(setNormalRateTrendMonth, 10, datalist);
  //     return;
  //   }

  //   var responsedata;
  //   if (datalist) {
  //     responsedata = datalist;
  //     normal_rate_trend_month_data = responsedata;
  //   } else if (normal_rate_trend_month_data) {
  //     responsedata = normal_rate_trend_month_data;
  //   } else {
  //     return;
  //   }

  //   var mdat_nor = responsedata[comp_code]["NORMAL_NO_T"]["D"];
  //   var mdat_excuted = responsedata[comp_code]["EXCUTED_NO"]["D"];
  //   var mdat_cancel = responsedata[comp_code]["CANCEL_NO"]["D"];

  //   // 正常率趋势 每日
  //   var date = new Date();
  //   var mm = date.getMonth() + 1;
  //   var d = date.getDate();
  //   var yy = date.getFullYear();

  //   if (mm < 10) {
  //     mm = "0" + mm;
  //   }
  //   if (d < 10) {
  //     d = "0" + d;
  //   }

  //   var normal_rate_list = [];

  //   // today
  //   var today = yy + "" + mm + d;

  //   var rate_from_db = -1;

  //   for (var i = 0; i < d; i++) {
  //     var dd = i + 1;
  //     if (dd < 10) {
  //       dd = "0" + dd;
  //     }
  //     var month = yy + "" + mm + dd;
  //     var val = getNumber(mdat_nor[month]);
  //     var val2 = getNumber(mdat_excuted[month]);
  //     var val3 = getNumber(mdat_cancel[month]);
  //     var rate = 0;
  //     if (val > 0 && val2 > 0) {
  //       rate = Math.round((val / (val2 + val3)) * 1000) / 10;
  //     }

  //     if (month.toString() == today.toString()) {
  //       normal_rate_list.push(today_normal_rate_from_api * 100);
  //     } else {
  //       normal_rate_list.push(rate);
  //     }
  //   }

  //   var list = normal_rate_list;

  //   var chart = echarts.init(
  //     document.getElementById("chart_normal_trend_month")
  //   );

  //   var colorrange = ["#FF0000", "#FF8000", "#FFFF26", "#00B22D"];
  //   var d = new Date();
  //   var numofday = new Date(d.getFullYear(), d.getMonth() + 1, 0).getDate();

  //   var xAxisData = function () {
  //     var arr = [];
  //     for (var i = 0; i < numofday; i++) {
  //       arr.push(i + 1);
  //     }
  //     return arr;
  //   };
  //   var chartData = function () {
  //     var arr = [];
  //     for (var i = 0; i < numofday; i++) {
  //       var val = list[i];
  //       val = isNaN(val) ? 0 : val;
  //       var color;
  //       if (val < 85) {
  //         color = "#FF8000";
  //       } else {
  //         color = "#69DB00";
  //       }

  //       var dat = {
  //         value: val,
  //         itemStyle: {
  //           normal: {
  //             color: color,
  //           },
  //         },
  //       };
  //       arr.push(dat);
  //     }
  //     return arr;
  //   };

  //   option = {
  //     tooltip: {
  //       show: true,
  //     },
  //     legend: {
  //       show: false,
  //       data: [""],
  //     },
  //     grid: {
  //       top: 10,
  //       left: 40,
  //       right: 10,
  //       bottom: 30,
  //     },
  //     xAxis: [
  //       {
  //         type: "category",
  //         //data: ['1月','2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月'],
  //         data: xAxisData(),
  //         nameTextStyle: {
  //           color: "#516BBA",
  //         },
  //         axisLine: {
  //           lineStyle: {
  //             color: "rgba(255,255,255,0)", // 轴线颜色
  //           },
  //         },
  //         axisLabel: {
  //           textStyle: {
  //             color: "#516BBA", // 轴标签颜色大小
  //             fontSize: 11,
  //           },
  //         },
  //       },
  //     ],
  //     yAxis: [
  //       {
  //         type: "value",
  //         name: "",
  //         min: 0,
  //         max: 100,
  //         interval: 25,
  //         nameTextStyle: {
  //           color: "#516BBA",
  //           fontSize: 10,
  //         },
  //         axisLabel: {
  //           formatter: "{value}%",
  //           textStyle: {
  //             color: "#516BBA",
  //             fontSize: 10,
  //           },
  //         },
  //         axisLine: {
  //           lineStyle: {
  //             color: "rgba(255,255,255,0)",
  //           },
  //         },
  //         splitLine: {
  //           show: true,
  //           lineStyle: {
  //             color: ["#243474"], // 分割线颜色
  //           },
  //         },
  //       },
  //     ],
  //     series: [
  //       {
  //         name: "",
  //         type: "bar",
  //         barWidth: 6,
  //         data: chartData(),
  //         itemStyle: {
  //           normal: {
  //             color: "#2693FF",
  //           },
  //         },
  //       },
  //     ],
  //   };

  //   chart.setOption(option);
  // }
  rootObj.setNormalRateTrendMonth = setNormalRateTrendMonth;

  // 从接口获取年度正常率走势数据
  function setNormalRateTrendYear() {
    getNormalRateTrendFromApi("M");
  }

  function drawNormalRateTrendmYear(chartData) {
    var chart = echarts.init(
      document.getElementById("chart_normal_trend_year")
    );
    var option = {};
    option = {
      tooltip: {
        show: true,
      },
      legend: {
        show: false,
        data: [""],
      },
      grid: {
        top: 10,
        left: 40,
        right: 10,
        bottom: 30,
      },
      xAxis: [
        {
          type: "category",
          data: [
            "1月",
            "2月",
            "3月",
            "4月",
            "5月",
            "6月",
            "7月",
            "8月",
            "9月",
            "10月",
            "11月",
            "12月",
          ],
          //data: xAxisData(),
          nameTextStyle: {
            color: "#516BBA",
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0)", // 轴线颜色
            },
          },
          axisLabel: {
            textStyle: {
              color: "#516BBA", // 轴标签颜色大小
              fontSize: 11,
            },
          },
        },
      ],
      yAxis: [
        {
          type: "value",
          name: "",
          min: 0,
          max: 100,
          interval: 25,
          nameTextStyle: {
            color: "#516BBA",
            fontSize: 10,
          },
          axisLabel: {
            formatter: "{value}%",
            textStyle: {
              color: "#516BBA",
              fontSize: 10,
            },
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0)",
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ["#243474"], // 分割线颜色
            },
          },
        },
      ],
      series: [
        {
          name: "",
          type: "bar",
          barWidth: 12,
          data: chartData,
          itemStyle: {
            normal: {
              color: "#2693FF",
            },
          },
        },
      ],
    };

    chart.setOption(option);
  }

  // 年
  // var normal_rate_trend_year_data;
  // function setNormalRateTrendYear_bak(datalist) {
  //   var list;
  //   if (datalist) {
  //     list = datalist;
  //     normal_rate_trend_year_data = list;
  //   } else if (normal_rate_trend_year_data) {
  //     list = normal_rate_trend_year_data;
  //   } else {
  //     return;
  //   }
  //   var chart = echarts.init(
  //     document.getElementById("chart_normal_trend_year")
  //   );

  //   var colorrange = ["#FF0000", "#FF8000", "#FFFF26", "#00B22D"];

  //   var numofday = 12;

  //   var xAxisData = function () {
  //     var arr = [];
  //     for (var i = 0; i < numofday; i++) {
  //       arr.push(i + 1);
  //     }
  //     return arr;
  //   };
  //   var chartData = function () {
  //     var arr = [];
  //     for (var i = 0; i < numofday; i++) {
  //       var val = list[i];
  //       val = isNaN(val) ? 0 : val;
  //       var color;
  //       if (val < 85) {
  //         color = "#FF8000";
  //       } else {
  //         color = "#69DB00";
  //       }

  //       var dat = {
  //         value: val,
  //         itemStyle: {
  //           normal: {
  //             color: color,
  //           },
  //         },
  //       };
  //       arr.push(dat);
  //     }
  //     return arr;
  //   };

  //   option = {
  //     tooltip: {
  //       show: true,
  //     },
  //     legend: {
  //       show: false,
  //       data: [""],
  //     },
  //     grid: {
  //       top: 10,
  //       left: 40,
  //       right: 10,
  //       bottom: 30,
  //     },
  //     xAxis: [
  //       {
  //         type: "category",
  //         data: [
  //           "1月",
  //           "2月",
  //           "3月",
  //           "4月",
  //           "5月",
  //           "6月",
  //           "7月",
  //           "8月",
  //           "9月",
  //           "10月",
  //           "11月",
  //           "12月",
  //         ],
  //         //data: xAxisData(),
  //         nameTextStyle: {
  //           color: "#516BBA",
  //         },
  //         axisLine: {
  //           lineStyle: {
  //             color: "rgba(255,255,255,0)", // 轴线颜色
  //           },
  //         },
  //         axisLabel: {
  //           textStyle: {
  //             color: "#516BBA", // 轴标签颜色大小
  //             fontSize: 11,
  //           },
  //         },
  //       },
  //     ],
  //     yAxis: [
  //       {
  //         type: "value",
  //         name: "",
  //         min: 0,
  //         max: 100,
  //         interval: 25,
  //         nameTextStyle: {
  //           color: "#516BBA",
  //           fontSize: 10,
  //         },
  //         axisLabel: {
  //           formatter: "{value}%",
  //           textStyle: {
  //             color: "#516BBA",
  //             fontSize: 10,
  //           },
  //         },
  //         axisLine: {
  //           lineStyle: {
  //             color: "rgba(255,255,255,0)",
  //           },
  //         },
  //         splitLine: {
  //           show: true,
  //           lineStyle: {
  //             color: ["#243474"], // 分割线颜色
  //           },
  //         },
  //       },
  //     ],
  //     series: [
  //       {
  //         name: "",
  //         type: "bar",
  //         barWidth: 12,
  //         data: chartData(),
  //         itemStyle: {
  //           normal: {
  //             color: "#2693FF",
  //           },
  //         },
  //       },
  //     ],
  //   };

  //   chart.setOption(option);
  // }

  rootObj.setNormalRateTrendYear = setNormalRateTrendYear;

  // ------------------------------------------------------------------------
  // 获取日实际正常率 数据
  // ------------------------------------------------------------------------
  function getNormalRateToday() {
    const today = new Date();
    // 获取昨天的日期
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1); // 设置为昨天
    yesterday.setUTCHours(16, 0, 0, 0); // 设置为昨天16:00:00 UTC
    // 获取今天的日期
    today.setUTCHours(15, 59, 59, 999); // 设置为今天15:59:59 UTC
    // 格式化为字符串
    const stdStartUtcTime = yesterday
      .toISOString()
      .slice(0, 19)
      .replace("T", " ");
    const stdEndUtcTime = today.toISOString().slice(0, 19).replace("T", " ");

    return new Promise((resolve, reject) => {
      var params = {
        companyCodes: comp_code,
        isLongRegsNotIn: false,
        stdEndUtcTime: stdEndUtcTime,
        stdStartUtcTime: stdStartUtcTime,
      };
      $.ajax({
        type: "post",
        url: "/bi/web/flightAmountStaticV2?normal_rate",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(params),
        success: function (res) {
          var kpiValue = 0;
          if (res && res.pfdappPercent) {
            kpiValue = Number(res.pfdappPercent);
          }
          resolve(Number(kpiValue) / 100);
        },
        error: function () {
          resolve(0); // 发生错误时解析为 0
        },
      });
    });
  }

  // 从接口获取月、年正常率走势
  function getNormalRateTrendFromApi(dateType) {
    var limit;
    var maxDateId = "";

    // 获取当前日期
    var currentDate = new Date(); // 自动获取当前日期
    var year = currentDate.getFullYear();
    var month = currentDate.getMonth() + 1; // 月份从0开始，所以需要加1
    var day = currentDate.getDate();

    var xAxisData = [];
    for (var i = 0; i < day; i++) {
      xAxisData.push(i + 1);
    }

    if (dateType === "D") {
      maxDateId =
        year.toString() +
        (month < 10 ? "0" + month : month) +
        (day < 10 ? "0" + (day - 1) : day - 1);
      limit = day - 1;
    } else if (dateType === "M") {
      maxDateId = year.toString() + (month < 10 ? "0" + month : month);
      limit = month; // 当前月份
    } else {
      return;
    }

    var param = {
      compCode: comp_code,
      kpiCode: "NORMAL_RATE_ZT",
      dateType: dateType,
      limit: limit,
      maxDateId: maxDateId,
    };

    $.ajax({
      type: "post",
      url: "/bi/spring/facCompKpi/queryKpiTrend.json",
      contentType: "application/json",
      dataType: "json",
      async: true,
      data: JSON.stringify(param),
      success: function (response) {
        var chartData = [];
        for (var i = 0; i < response.data.length; i++) {
          let kpiValue = response.data[i].kpiValue;
          kpiValue = isNaN(kpiValue) ? 0 : kpiValue * 100;
          let color = "";
          if (kpiValue < 85) {
            color = "#FF8000";
          } else {
            color = "#69DB00";
          }
          chartData.push({
            value: kpiValue.toFixed(1),
            itemStyle: {
              normal: {
                color: color,
              },
            },
          });
        }
        if (dateType === "D") {
          getNormalRateToday().then((kpiValue) => {
            let todayVal = (kpiValue * 100).toFixed(1);
            let todayColor = Number(todayVal) < 85 ? "#FF8000" : "#69DB00";
            chartData.push({
              value: todayVal,
              itemStyle: {
                normal: {
                  color: todayColor,
                },
              },
            });
            drawNormalRateTrendmMonth(xAxisData, chartData);
          });
        } else if (dateType === "M") {
          drawNormalRateTrendmYear(chartData);
        }
      },
      error: function () {
        // 处理错误情况
      },
    });
  }

  // 安全风险走势
  var param = {
    mode: "query",
    CODE: "safty_risk",
  };

  $.ajax({
    type: "post",
    url: "/bi/web/west_monthly_data",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      if (response.data != undefined) {
        setSaftyRiskTrend(response.data);
      }
    },
    error: function (jqXHR, txtStatus, errorThrown) {
      console.log("----error");
    },
  });

  var safty_risk_data;
  function setSaftyRiskTrend(datalist) {
    if (settings == undefined) {
      setTimeout(setSaftyRiskTrend, 10, datalist);
      return;
    }

    var list;
    if (datalist) {
      list = datalist;
      safty_risk_data = list;
    } else {
      list = safty_risk_data;
    }
    var chart = echarts.init(document.getElementById("chart_safty_risk_trend"));

    var colors = ["#FFC926", "#9673FF", "#59B200"];

    var numofitm = 12;

    var xAxisData = [
      "1月",
      "2月",
      "3月",
      "4月",
      "5月",
      "6月",
      "7月",
      "8月",
      "9月",
      "10月",
      "11月",
      "12月",
    ];

    var date = new Date();
    var year1 = Number(date.getFullYear());
    var year2 = Number(year1) - 1;
    var year3 = Number(year1) - 2;

    var datakv = {};
    datakv[year1] = {};
    datakv[year2] = {};
    datakv[year3] = {};

    if (list.length > 0) {
      var len = list.length;
      for (var i = 0; i < len; i++) {
        var d = list[i];
        if (Number(d.YEAR) == year1) {
          datakv[year1][d.MONTH] = d.VALUE;
        } else if (Number(d.YEAR) == year2) {
          datakv[year2][d.MONTH] = d.VALUE;
        } else if (Number(d.YEAR) == year3) {
          datakv[year3][d.MONTH] = d.VALUE;
        }
      }
    }

    var chartData = function () {
      var arr = [];
      for (var i = 0; i < numofitm; i++) {
        var val = datakv[year1][i + 1];
        val = isNaN(val) ? "" : val;
        var dat = {
          value: val,
        };
        arr.push(dat);
      }
      return arr;
    };

    var chartData2 = function () {
      var arr = [];
      for (var i = 0; i < numofitm; i++) {
        var val = datakv[year2][i + 1];
        val = isNaN(val) ? "" : val;
        var dat = {
          value: val,
        };
        arr.push(dat);
      }
      return arr;
    };

    var chartData3 = function () {
      var arr = [];
      for (var i = 0; i < numofitm; i++) {
        var val = datakv[year3][i + 1];
        val = isNaN(val) ? "" : val;
        var dat = {
          value: val,
        };
        arr.push(dat);
      }
      return arr;
    };

    option = {
      tooltip: {
        show: true,
      },
      legend: {
        show: true,
        top: 10,
        left: 60,
        data: [
          {
            name: year1 + "",
            //icon: 'circle',
            textStyle: {
              color: colors[0],
            },
          },
          {
            name: year2 + "",
            //icon: 'circle',
            textStyle: {
              color: colors[1],
            },
          },
          {
            name: year3 + "",
            //icon: 'circle',
            textStyle: {
              color: colors[2],
            },
          },
        ],
      },
      grid: {
        top: 46,
        left: 25,
        right: 55,
        bottom: 30,
      },
      xAxis: [
        {
          type: "category",
          data: xAxisData,
          nameTextStyle: {
            color: "#516BBA",
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0)", // 轴线颜色
            },
          },
          axisLabel: {
            textStyle: {
              color: "#516BBA", // 轴标签颜色大小
              fontSize: 11,
            },
          },
        },
      ],
      yAxis: [
        {
          type: "value",
          name: "",
          position: "right",
          //min: 0,
          //max: 100,
          //interval: 25,
          nameTextStyle: {
            color: "#516BBA",
            fontSize: 10,
          },
          axisLabel: {
            formatter: "{value}",
            textStyle: {
              color: "#516BBA",
              fontSize: 10,
            },
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0)",
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ["#243474"], // 分割线颜色
            },
          },
        },
      ],
      series: [
        {
          name: year1 + "",
          type: "line",
          data: chartData(),
          itemStyle: {
            normal: {
              color: colors[0],
            },
          },
        },
        {
          name: year2 + "",
          type: "line",
          data: chartData2(),
          itemStyle: {
            normal: {
              color: colors[1],
            },
          },
        },
        {
          name: year3 + "",
          type: "line",
          data: chartData3(),
          itemStyle: {
            normal: {
              color: colors[2],
            },
          },

          markLine: {
            silent: false,
            data: [
              {
                name: "",
                yAxis: settings.SAFTY_RISK_WARNING,
                lineStyle: {
                  normal: {
                    color: "#CC99FF",
                    type: "dashed",
                    width: 1,
                  },
                },
              },
              {
                name: "",
                yAxis: settings.SAFTY_RISK_DANGER,
                lineStyle: {
                  normal: {
                    color: "#FF0000",
                    type: "dashed",
                    width: 1,
                  },
                },
              },
              {
                name: "",
                yAxis: settings.SAFTY_RISK_MONITOR,
                lineStyle: {
                  normal: {
                    color: "#FFFF4D",
                    type: "dashed",
                    width: 1,
                  },
                },
              },
              {
                name: "",
                yAxis: settings.SAFTY_RISK_GOAL,
                lineStyle: {
                  normal: {
                    color: "#73DCFF",
                    type: "dashed",
                    width: 1,
                  },
                },
              },
            ],
            symbol: "",
            symbolSize: 0,
            label: {
              normal: {
                position: "end",
                formatter: "{c}",
              },
            },
          },
        },
      ],
    };

    chart.setOption(option);
  }
  rootObj.setSaftyRiskTrend = setSaftyRiskTrend;

  // 投诉率
  var param = {
    mode: "query",
    CODE: "complaint_rate",
  };

  $.ajax({
    type: "post",
    url: "/bi/web/west_monthly_data",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      if (response.data != undefined) {
        setComplaintRate(response.data);
      }
    },
    error: function (jqXHR, txtStatus, errorThrown) {
      console.log("----error");
    },
  });

  var complaint_rate_data;
  function setComplaintRate(datalist) {
    var list;
    if (datalist) {
      list = datalist;
      complaint_rate_data = list;
    } else {
      list = complaint_rate_data;
    }
    var chart = echarts.init(document.getElementById("chart_complaint_rate"));

    var colors = ["#FF8000", "#9673FF", "#59B200"];

    var xAxisData = [];

    var date = new Date();
    var year1 = Number(date.getFullYear());

    var datakv = [];
    //datakv[year1] = {};

    if (list.length > 0) {
      var len = list.length;
      for (var i = 0; i < len; i++) {
        var d = list[i];
        if (Number(d.MONTH) < 10 && d.MONTH.length == 1) {
          d.MONTH = "0" + d.MONTH;
        }
        //if(Number(d.YEAR) == year1){
        datakv.push({
          date: d.YEAR + "" + d.MONTH,
          month: d.MONTH,
          val: d.VALUE,
        });
        //}
      }
    }

    datakv.sort(function (a, b) {
      return Number(a.date) - Number(b.date);
    });

    var sdata = [];
    for (var i = 0; i < datakv.length; i++) {
      var dd = datakv[i];
      var val = dd.val;
      val = isNaN(val) ? "" : val;
      var dat = {
        value: val,
      };
      sdata.push(dat);
      xAxisData.push(dd.month);
    }

    option = {
      tooltip: {
        show: true,
      },
      legend: {
        show: false,
        top: 10,
        left: 60,
        data: [],
      },
      grid: {
        top: 20,
        left: 55,
        right: 25,
        bottom: 35,
      },
      xAxis: [
        {
          type: "category",
          data: xAxisData,
          nameTextStyle: {
            color: "#516BBA",
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0)", // 轴线颜色
            },
          },
          axisLabel: {
            textStyle: {
              color: "#516BBA", // 轴标签颜色大小
              fontSize: 11,
            },
          },
        },
      ],
      yAxis: [
        {
          type: "value",
          name: "",
          position: "left",
          //min: 0,
          //max: 100,
          //interval: 25,
          nameTextStyle: {
            color: "#516BBA",
            fontSize: 10,
          },
          axisLabel: {
            formatter: "{value}",
            textStyle: {
              color: "#516BBA",
              fontSize: 10,
            },
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0)",
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ["#243474"], // 分割线颜色
            },
          },
        },
      ],
      series: [
        {
          name: "",
          type: "line",
          barWidth: 12,
          data: sdata,
          itemStyle: {
            normal: {
              color: colors[0],
            },
          },
          label: {
            normal: {
              show: true,
              position: "top",
              formatter: "{c}",
              textStyle: {
                fontSize: 11,
                color: "#BDCCFF",
              },
            },
          },
        },
      ],
    };

    chart.setOption(option);
  }
  rootObj.setComplaintRate = setComplaintRate;

  // 投诉排名
  var param = {
    mode: "query",
    CODE: "complaint_rank",
  };

  $.ajax({
    type: "post",
    url: "/bi/web/west_monthly_data",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      if (response.data != undefined) {
        setComplaintRank(response.data);
      }
    },
    error: function (jqXHR, txtStatus, errorThrown) {
      console.log("----error");
    },
  });

  var complaint_rank_data;
  function setComplaintRank(datalist) {
    var list;
    if (datalist) {
      list = datalist;
      complaint_rank_data = list;
    } else {
      list = complaint_rank_data;
    }
    var chart = echarts.init(document.getElementById("chart_complaint_rank"));

    var colors = ["#FF8000", "#9673FF", "#59B200"];

    var numofitm = 12;

    var xAxisData = [];

    var date = new Date();
    var year1 = Number(date.getFullYear());

    var datakv = [];
    //datakv[year1] = {};

    if (list.length > 0) {
      var len = list.length;
      for (var i = 0; i < len; i++) {
        var d = list[i];
        if (Number(d.MONTH) < 10 && d.MONTH.length == 1) {
          d.MONTH = "0" + d.MONTH;
        }
        //if(Number(d.YEAR) == year1){
        datakv.push({
          date: d.YEAR + "" + d.MONTH,
          month: d.MONTH,
          val: d.VALUE,
        });
        //}
      }
    }

    datakv.sort(function (a, b) {
      return Number(a.date) - Number(b.date);
    });

    var sdata = [];
    for (var i = 0; i < datakv.length; i++) {
      var dd = datakv[i];
      var val = dd.val;
      val = isNaN(val) ? "" : val;
      var dat = {
        value: val,
      };
      sdata.push(dat);
      xAxisData.push(dd.month);
    }

    var chartData = function () {
      var arr = [];
      for (var i = 0; i < datakv.length; i++) {
        var dd = datakv[i];
        var val = dd.val;
        val = isNaN(val) ? "" : val;
        var dat = {
          value: val,
        };
        arr.push(dat);
      }
      return arr;
    };

    option = {
      tooltip: {
        show: true,
      },
      legend: {
        show: false,
        top: 10,
        left: 60,
        data: [],
      },
      grid: {
        top: 20,
        left: 55,
        right: 25,
        bottom: 35,
      },
      xAxis: [
        {
          type: "category",
          data: xAxisData,
          nameTextStyle: {
            color: "#516BBA",
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0)", // 轴线颜色
            },
          },
          axisLabel: {
            textStyle: {
              color: "#516BBA", // 轴标签颜色大小
              fontSize: 11,
            },
          },
        },
      ],
      yAxis: [
        {
          type: "value",
          name: "",
          position: "left",
          nameTextStyle: {
            color: "#516BBA",
            fontSize: 10,
          },
          axisLabel: {
            formatter: "{value}",
            textStyle: {
              color: "#516BBA",
              fontSize: 10,
            },
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0)",
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ["#243474"], // 分割线颜色
            },
          },
        },
      ],
      series: [
        {
          name: "",
          type: "bar",
          barWidth: 12,
          data: sdata,
          itemStyle: {
            normal: {
              color: colors[0],
            },
          },
          label: {
            normal: {
              show: true,
              position: "top",
              formatter: "{c}",
              textStyle: {
                fontSize: 11,
                color: "#BDCCFF",
              },
            },
          },
        },
      ],
    };

    chart.setOption(option);
  }
  rootObj.setComplaintRank = setComplaintRank;

  function getRandNumber() {
    var newnum = parseInt(Math.random() * 100);
    if (newnum >= 0 && newnum <= 100) {
      return newnum;
    } else {
      return getRandNumber();
    }
  }

  // ------------------------------------------------------------------------
  // 地球 航线
  // ------------------------------------------------------------------------
  function setAirlines() {
    if (arp_detail_list == undefined) {
      setTimeout(setAirlines, 10);
      return;
    }

    var seriesData = [];

    for (var fltno in flightInfoList) {
      flt = flightInfoList[fltno];

      var arp1 = arp_detail_list[flt.depStn];
      var arp2 = arp_detail_list[flt.arrStn];

      if (
        arp1 &&
        arp2 &&
        arp1.longitude &&
        arp1.latitude &&
        arp2.longitude &&
        arp2.latitude
      ) {
        var color;

        var statusMap = {
          ARR: "落地",
          NDR: "落地",
          ATD: "推出",
          ATA: "到达",
          CNL: "取消",
          DEL: "延误",
          DEP: "起飞",
          RTR: "返航",
          SCH: "计划",
        };

        if (flt.status == "DEL" || (flt.delay1 != "" && flt.dur1 > 0)) {
          color = "#fff663"; // 黄色
        } else if (
          flt.status == "ARR" ||
          flt.status == "NDR" ||
          flt.status == "ATD" ||
          flt.status == "ATA" ||
          flt.status == "DEP"
        ) {
          color = "#0cff00"; // 绿色
        } else if (flt.status == "CNL" || flt.status == "RTR") {
          color = "#fff663"; // 黄色 // '#FF0000'; // 红色
        } else {
          //SCH
          color = "#00c6ff"; // 蓝色
        }

        seriesData.push({
          fltno: fltno,
          arrStn: flt.arrStn,
          depStn: flt.depStn,
          coords: [
            [arp1.longitude, arp1.latitude],
            [arp2.longitude, arp2.latitude],
          ],
          value: fltno,
          lineStyle: {
            width: 1,
            color: color,
            opacity: 1,
          },
        });
      }
    }

    // 航线的 tooltip
    var tooltip = {
      trigger: "item",
      show: true,
      formatter: function (params, ticket, callback) {
        console.log("params", params);

        var data = params.data;
        var arp1 = arp_detail_list[data.arrStn];
        var arp2 = arp_detail_list[data.depStn];

        var fltno = data.fltno;
        var flt = flightInfoList[fltno];

        var city_name1 = arp1.city_name;
        var arp_name1 = arp1.chn_name;

        var city_name2 = arp2.city_name;
        var arp_name2 = arp2.chn_name;

        var name1 =
          arp_name1.indexOf(city_name1) > -1
            ? arp_name1
            : city_name1 + arp_name1;
        var name2 =
          arp_name2.indexOf(city_name2) > -1
            ? arp_name2
            : city_name2 + arp_name2;

        var html = "";

        //航班号、起飞城市-到达城市、航班状态（实际/预计/计划起飞时间-实际/预计/计划到达时间）、机号（机型）、客座率（布局）、实时位置、实施油量
        html += "航班号: " + fltno + "<br>";
        html += city_name1 + " - " + city_name2 + "<br>";
        if (
          flt.status == "DEP" ||
          flt.status == "ARR" ||
          flt.status == "NDR" ||
          flt.status == "ATA"
        ) {
          // 起飞,落地,到达
          // 实际起飞时间 atdChn
          html += "实际起飞时间: " + trimTime(flt.atdChn) + "<br>";
        } else {
          // 预计出发
          html += "预计出发时间: " + trimTime(flt.etdChn) + "<br>";
        }

        if (flt.status == "ATA") {
          // 到达
          // 实际起飞时间 atdChn
          html += "实际到达时间: " + trimTime(flt.ataChn) + "<br>";
        } else {
          // 预计到达
          html += "预计到达时间: " + trimTime(flt.etaChn) + "<br>";
        }

        if (flt.delay1 != "" && flt.dur1 > 0) {
          html += "延误原因: " + flt.delay1Name + "<br>";
          html +=
            "延误时间: " +
            (Number(flt.dur1) +
              Number(flt.dur2) +
              Number(flt.dur3) +
              Number(flt.dur4)) +
            "分钟<br>";
        }

        html += "机号: " + data.acno + "(" + flt.acType + ")" + "<br>";
        html +=
          "实时位置: " +
          Math.round(data.value[0] * 10000) / 10000 +
          ", " +
          Math.round(data.value[1] * 10000) / 10000 +
          "<br>";

        return html;
      },

      backgroundColor: "#021e55",
    };

    // 基地
    var seriesBaseData = [];
    for (var arp in ARP_COORD_LIST) {
      var name = ARP_NAME_LIST[arp];
      var coord = ARP_COORD_LIST[arp];
      coord = coord.concat(0);
      seriesBaseData.push({
        name: name,
        value: coord,
        itemStyle: {
          color: "#009d0b",
          opacity: 1,
        },
        label: {
          show: false,
          distance: 2,
          position: "bottom",
          textStyle: {
            color: "#FFFFFF",
            backgroundColor: "rgba(0,0,0,0)",
            borderWidth: 1,
            borderColor: "rgba(0,0,0,0.5)",
            fontSize: 11,
            formatter: "{b}",
          },
        },
      });
    }

    var series = [];
    series.push({
      type: "lines3D",
      coordinateSystem: "globe",
      //blendMode: 'lighter',
      effect: {
        show: true,
        period: 6,
        trailLength: 0.3,
        trailColor: "#fff",
        trailWidth: 1,
      },

      silent: false,

      tooltip: tooltip,

      data: seriesData,
    });

    series.push({
      type: "scatter3D",
      coordinateSystem: "globe",
      //blendMode: 'lighter',
      symbol: "circle",
      symbolSize: 5,
      itemStyle: {
        color: "#009d0b",
        opacity: 1,
      },
      silent: true,
      data: seriesBaseData,
    });

    var option = {
      series: series,
    };

    chart_earch.setOption(option);
  }

  // 多媒体播放文件
  var param = {
    mode: "query",
  };

  $.ajax({
    type: "post",
    url: "/bi/web/west_files",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var lst = response.data;
      mediaList = [];
      for (var i = lst.length - 1; i >= 0; i--) {
        var itm = lst[i];
        if (Number(itm.HIDDEN) == 0) {
          mediaList.push(itm);
        }
      }

      mediaList.sort(function (a, b) {
        return Number(a.SORT) - Number(b.SORT);
      });

      if (currentMediaId > mediaList.length - 1) {
        currentMediaId = 0;
      }

      if (mediaList.length > 0) {
        playMedia();
      }
    },
    error: function (jqXHR, txtStatus, errorThrown) {
      console.log("----error");
    },
  });

  ///////////// test

  // load file
  /*
  var param = {
    "mode": "getfile",
    "filename": "20171108745b0e50-2558-4aa0-98cb-4bb4bbaf1c96",
  }

  $.ajax({
      type: 'post',
      url:"/bi/web/file",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function(response) {

          var base64str = response.fileinfo;
          var html = '<span style="height:100%;display:inline-block;vertical-align:middle"></span><img style="display:inline-block; max-width:230px;max-height:240px;" src="data:image/jpeg;base64,'+base64str+'" />';
          $('#mediaWrap').html(html);
          
      },
      error:function(jqXHR, txtStatus, errorThrown) {
        console.log('----error');
      }
  });
  */
} // end loadAll

// 多媒体播放文件
var mediaList = [];
var currentMediaId = 0;
var tmo_media;

function playMedia() {
  var media = mediaList[currentMediaId];

  var type = media.TYPE;
  var val = media.VALUE;

  if (val.indexOf("http") == -1) {
    if (isNaN(val.substr(0, 8))) {
      if (mediaList.length > 1) {
        if (currentMediaId < mediaList.length - 1) {
          currentMediaId++;
        } else {
          currentMediaId = 0;
        }
        playMedia();
      }
      return;
    }

    // load file
    var param = {
      mode: "getfile",
      filename: val,
    };

    $.ajax({
      type: "post",
      url: "/bi/web/file",
      contentType: "application/json",
      dataType: "json",
      async: true,
      data: JSON.stringify(param),
      success: function (response) {
        var base64str = response.fileinfo;

        if (type == "img") {
          var html =
            '<span style="height:100%;display:inline-block;vertical-align:middle"></span><img style="display:inline-block; max-width:230px;max-height:240px;" src="data:image/jpeg;base64,' +
            base64str +
            '" />';
          $("#mediaWrap").html(html);
        }
      },
      error: function (jqXHR, txtStatus, errorThrown) {
        console.log("----error");
      },
    });
  } else {
    // url
    if (type == "img") {
      var html =
        '<span style="height:100%;display:inline-block;vertical-align:middle"></span><img style="display:inline-block; max-width:230px;max-height:240px;" src="' +
        val +
        '" />';
      $("#mediaWrap").html(html);
    } else if (type == "video") {
      var html = "";
      html +=
        '<video src="' +
        val +
        '" controls="controls" autoplay="true" width="230px" height="240px">';
      html += "您的浏览器不支持 video 标签";
      html += "</video>";
      $("#mediaWrap").html(html);
    }
  }

  var interval = Number(media.INTERVAL);
  if (isNaN(interval)) {
    interval = 10;
  }

  clearTimeout(tmo_media);

  tmo_media = setTimeout(function () {
    if (currentMediaId < mediaList.length - 1) {
      currentMediaId++;
    } else {
      currentMediaId = 0;
    }
    playMedia();
  }, interval * 1000);
}

function findFltInfo(fltno) {
  for (var i = all_flight_list.length - 1; i >= 0; i--) {
    var flt = all_flight_list[i];
    if (flt.flightNo == fltno) {
      return flt;
    }
  }
  return undefined;
}

// 获取机场列表
var arp_detail_list;
function getAirportList() {
  var param = {
    //"AIRPORT_CODE": arpcodes, // 可选，传入机场CODE
  };
  $.ajax({
    type: "post",
    url: "/bi/web/airportdetail",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      arp_detail_list = {};
      var list = response.airport;
      for (var i = list.length - 1; i >= 0; i--) {
        var arp = list[i];
        arp_detail_list[arp.code] = arp;
      }
    },
    error: function () {},
  });
}
getAirportList();

// 自动循环切换两个TAB
var itv_autoSwitchTab;
var time_autoSwitchTab = 5 * 60 * 1000; //5分钟切换
var currentTabIndex = 0;

function autoSwitchTab() {
  clearTimeout(itv_autoSwitchTab);

  if (currentTabIndex == 0) {
    $("#tab_normal_rate_year").click();
  } else {
    $("#tab_normal_rate_month").click();
  }

  itv_autoSwitchTab = setTimeout(autoSwitchTab, time_autoSwitchTab);
}

clearTimeout(itv_autoSwitchTab);
itv_autoSwitchTab = setTimeout(autoSwitchTab, time_autoSwitchTab);

// 加载数据
loadAll();
setInterval(loadAll, 5 * 60 * 1000); // 5分钟刷新一次

// ------------------------------------------------------------------------
// 时钟
// ------------------------------------------------------------------------
function setTime() {
  var date = new Date();
  var timestamp = date.getTime();
  var timezoneOffset = date.getTimezoneOffset();
  var utc_timestamp = timestamp + timezoneOffset * 60 * 1000;
  var utc_date = new Date();
  utc_date.setTime(utc_timestamp);

  var sydney_date = new Date();
  sydney_date.setTime(utc_timestamp + 11 * 3600 * 1000);

  var newyork_date = new Date();
  newyork_date.setTime(utc_timestamp - 5 * 3600 * 1000);

  $("#time_beijing").text(
    formatNum(date.getHours()) + ":" + formatNum(date.getMinutes())
  );
  $("#time_london").text(
    formatNum(utc_date.getHours()) + ":" + formatNum(utc_date.getMinutes())
  );
  $("#time_sydney").text(
    formatNum(sydney_date.getHours()) +
      ":" +
      formatNum(sydney_date.getMinutes())
  );
  $("#time_newyork").text(
    formatNum(newyork_date.getHours()) +
      ":" +
      formatNum(newyork_date.getMinutes())
  );

  $("#date_beijing").text(date.getDate() + " " + getEngMonth(date.getMonth()));
  $("#date_london").text(
    utc_date.getDate() + " " + getEngMonth(utc_date.getMonth())
  );
  $("#date_sydney").text(
    sydney_date.getDate() + " " + getEngMonth(sydney_date.getMonth())
  );
  $("#date_newyork").text(
    newyork_date.getDate() + " " + getEngMonth(newyork_date.getMonth())
  );
}
function formatNum(n) {
  if (n < 10) {
    return "0" + n;
  } else {
    return n;
  }
}
function getEngMonth(month) {
  var mlist = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Spt",
    "Oct",
    "Nov",
    "Dec",
  ];
  return mlist[month].toUpperCase();
}

setInterval(setTime, 1000);
setTime();

// ------------------------------------------------------
// 3D 地球
// ------------------------------------------------------
var chart_earch;
var earch_option;
function crate3DEarth() {
  chart_earch = echarts.init(document.getElementById("earth3d"));

  earch_option = {
    tooltip: {
      show: false,
    },
    backgroundColor: "rgba(0,0,0,0)",
    globe: {
      baseTexture: "../asset/earth.jpg",
      heightTexture: "../asset/height.jpg",

      displacementScale: 0.02,

      shading: "lambert",
      //shading: 'realistic',

      viewControl: {
        autoRotate: false,
        zoomSensitivity: false,
        targetCoord: [106, 32],
      },

      light: {
        ambient: {
          intensity: 0.1,
        },
        main: {
          intensity: 1.5,
        },
      },

      layers: [
        {
          type: "blend",
          blendTo: "emission",
          texture: "../asset/night.jpg",
        },
      ],
    },
    series: [
      {
        type: "lines3D",
        coordinateSystem: "globe",

        //blendMode: 'lighter',

        effect: {
          show: true,
          period: 6,
          constantSpeed: 10,
          trailLength: 0.3,
          trailColor: "#fff",
          trailWidth: 1.5,
        },

        silent: true,

        data: [],
      },
    ],
  };

  chart_earch.setOption(earch_option);
}

crate3DEarth();

function updateAlleCharts() {
  rootObj.setSaftyRiskTrend();
  rootObj.setComplaintRate();
  rootObj.setComplaintRank();
  rootObj.setNormalRateTrendMonth();
  rootObj.setNormalRateTrendYear();
}

if (getQueryString("scale") == 1) {
  $("#link_2base").attr("href", encodeURI("base.html?scale=1"));
} else if (getQueryString("scale") == "auto") {
  $("#link_2base").attr("href", encodeURI("base.html?scale=auto"));
} else {
  $("#link_2base").attr("href", encodeURI("base.html"));
}
