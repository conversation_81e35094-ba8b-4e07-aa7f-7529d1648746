// min ≤ r ≤ max
function randomNumRange(<PERSON>,<PERSON>){
      var Range = Max - Min;
      var Rand = Math.random();
      var num = Min + Math.round(Rand * Range); //四舍五入
      return num;
}

function countFromZero(element, num, numOfStep, format){
  if(numOfStep == 0){
    numOfStep = 100;
  }
  var step = Math.ceil(num/numOfStep);
  
  var n = Math.ceil(num/step);

  var start = 0;
  var i=0;

  function setNumStr(){

    if(i < n){
        var nnn = start + step * i;
        if(nnn > num){
          nnn = num;
        }
        if(format){
          numStr = formatCurrency(nnn, 0);
        }else{
          numStr = nnn;
        }
        element.text(numStr);

        setTimeout(setNumStr, 10);

        i++;
    }
    
  }

  setNumStr();

}



// 截取小数位数
function trimDecimal(num, len) {
  var nnn = 1;
  for(var i=0; i<len; i++){
    nnn = nnn * 10;
  }
  return Math.round(num*nnn)/nnn;
}


/** 
 * 将数值四舍五入(保留 decimalLen 位小数)后格式化成金额形式 
 * 
 * @param num 数值(Number或者String) 
 * @return 金额格式的字符串,如'1,234,567.45' 
 * @type String 
 */  
function formatCurrency(num, decimalLen) {
    if(isNaN(num)||num==null)  
        num = "0";
    num = num.toString().replace(/\$|\,/g,'');  
    
    sign = (num == (num = Math.abs(num)));
    var cents = '0';
    if(decimalLen == 2){

        num = Math.floor(num*100+0.50000000001);  
        cents = num%100;  
        num = Math.floor(num/100).toString();
        if(cents<10)  
        cents = "0" + cents; 

    }else if(decimalLen == 1){

        num = Math.floor(num*10+0.50000000001);  
        cents = num%10;  
        num = Math.floor(num/10).toString();  

    }else{

        num = Math.round(num).toString();

    }
     
    for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++){
      num = num.substring(0,num.length-(4*i+3))+','+num.substring(num.length-(4*i+3));
    }

    if(decimalLen > 0){
      return ((sign?'':'-') + num + '.' + cents);
    }else{
      return ((sign?'':'-') + num);
    }
    

}  


function isMac(){
  return navigator.appVersion.indexOf("Mac") != -1;
}
function isIE(){
  return navigator.appVersion.indexOf("MSIE") != -1;
}

function fontSizeDiff(){
  if(isMac()){
    return 0;
  }else{
    return -2;
  }
}


function chartDateFormatter(value, index) {
    if(value.length == 8 || value.length == 6){
      // 20170316
      // 201703
      var d = value.substr(-4, 4);
      var d1 = d.substr(0, 2);
      var d2 = d.substr(-2, 2);
      return d1 + '/' + d2;
    }
    return value;
}


// 判断 object 是否为空
function objectIsEmpty(obj){
  for(var k in obj){
    return false;
  }
  return true;
}


// 2015-03-12 12:00:00 转换成标准时间
function parserDate(date) {
  return new Date(Number(date.substr(0,4)), Number(date.substr(5,2))-1, Number(date.substr(8,2)), Number(date.substr(11,2)), Number(date.substr(14,2)), Number(date.substr(17,2)));
};


function formatLonLat(dat){
  if(dat.indexOf('E') > -1){
      dat = dat.substr(1);
      dat = Number(dat)/1000000;
  }else if(dat.indexOf('W') > -1){
      dat = '-'+dat.substr(1);
      dat = Number(dat)/1000000;
  }else if(dat.indexOf('N') > -1){
      dat = dat.substr(1);
      dat = Number(dat)/1000000;
  }else if(dat.indexOf('S') > -1){
      dat = '-'+dat.substr(1);
      dat = Number(dat)/1000000;
  }
  return dat;
}

// 通过2个经纬度算 正北夹角
function getGeoAngle(lat2, lng2, lat1, lng1) {
  /*
    var x1 = lng1;
    var y1 = lat1;
    var x2 = lng2;
    var y2 = lat2;
    var pi = Math.PI;
    var w1 = y1 / 180 * pi;
    var j1 = x1 / 180 * pi;
    var w2 = y2 / 180 * pi;
    var j2 = x2 / 180 * pi;
    var ret;
    if (j1 == j2) {
        if (w1 > w2) { return 180; } //北半球的情况，南半球忽略
        else if (w1 < w2) { return 0; }
        else { return -1; } //位置完全相同
    }
    ret = 4 * Math.pow(Math.sin((w1 - w2) / 2), 2) - Math.pow(Math.sin((j1 - j2) / 2) * (Math.cos(w1) - Math.cos(w2)), 2);
    ret = Math.sqrt(ret);
    var temp = (Math.sin(Math.abs(j1 - j2) / 2) * (Math.cos(w1) + Math.cos(w2)));
    ret = ret / temp;
    ret = Math.atan(ret) / pi * 180;
    if (j1 > j2) // 1为参考点坐标
    {
        if (w1 > w2) { ret += 180; }
        else { ret = 180 - ret; }
    }
    else if (w1 > w2) { ret = 360 - ret; }

    return ret-90;
    */

    return bearing(lat1,lng1,lat2,lng2);


}


/**
 * Calculate the bearing between two positions as a value from 0-360
 *
 * @param lat1 - The latitude of the first position
 * @param lng1 - The longitude of the first position
 * @param lat2 - The latitude of the second position
 * @param lng2 - The longitude of the second position
 *
 * @return int - The bearing between 0 and 360
 */
function bearing (lat1,lng1,lat2,lng2) {
    var dLon = (lng2-lng1);
    var y = Math.sin(dLon) * Math.cos(lat2);
    var x = Math.cos(lat1)*Math.sin(lat2) - Math.sin(lat1)*Math.cos(lat2)*Math.cos(dLon);
    var brng = this._toDeg(Math.atan2(y, x));
    return 360 - ((brng + 360) % 360);
}

/**
 * Since not all browsers implement this we have our own utility that will
 * convert from degrees into radians
 *
 * @param deg - The degrees to be converted into radians
 * @return radians
 */
function _toRad(deg) {
     return deg * Math.PI / 180;
}

/**
 * Since not all browsers implement this we have our own utility that will
 * convert from radians into degrees
 *
 * @param rad - The radians to be converted into degrees
 * @return degrees
 */
function _toDeg(rad) {
    return rad * 180 / Math.PI;
}

