function createComboBox(id, list, width, maxheight, callback, selectedIndex){
  var html = '';
  html += '<span class="combobox_label"></span>';

  var len = list.length;
  var listhtml = '';
  for(var i=0; i<len; i++){
    var obj = list[i];
    listhtml += "<span class='item' data-index="+ i+" data='"+obj.data+"'>"+obj.label+"</span>";
  }
  html += '<span class="combobox_list">'+listhtml+'</span>';
  $('#'+id).html(html);
  $('#'+id).addClass('combobox');
  $('#'+id).css('width', width+'px');
  $('#'+id + ' .combobox_list').css('max-height', maxheight+'px');
  $('#'+id + ' .combobox_list').hide();

  $('#'+id + ' .combobox_label').on('click', function(){
    if($('#'+id + ' .combobox_list').is(':visible')){
      $('#'+id + ' .combobox_list').hide();
    }else{
      $('#'+id + ' .combobox_list').show();
    }
  });

  $('#'+id + ' .combobox_list .item').on('click', function(){
    $('#'+id + ' .combobox_list').hide();
    $('#'+id + ' .combobox_label').text($(this).text());
    $('#'+id).attr('data', $(this).attr('data'));
    if(callback!=undefined) callback($(this).attr('data'), $(this).text(),$(this).attr("data-index"));
  });


  var active = false;
  var tmo;

  $('#'+id).on('mouseover', function(){
    clearTimeout(tmo);
    active = true;
  });
  $('#'+id).on('mouseout', function(){
    active = false;
    tmo = setTimeout(checkActive, 300);
  });

  function checkActive(){
    if(!active){
      $('#'+id + ' .combobox_list').hide();
    }
  }

  
  if(!selectedIndex){
    // select first item
    if(list.length > 0){
      $('#'+id + ' .combobox_label').text(list[0].label);
      $('#'+id).attr('data', list[0].data);
    }
  }else{
    $('#'+id + ' .combobox_label').text(list[selectedIndex].label);
    $('#'+id).attr('data', list[selectedIndex].data);
  }
  


}