var current_company_code;

var midTabId = 1;
// 可显示的历史 数量
var query_limit = 20;
// 日期类型
var date_type = 'L';

var legendColorList1 = ['#e95551', '#f7a449', '#fff353', '#21b3ce', '#176ebf', '#0a3a89', '#8619db', '#aa44e1', '#a5a27d', '#925c86'];
legendColorList1 = legendColorList1.concat(legendColorList1);
var legendColorList2 = ['#aecffd', '#f98a01', '#d11f1e', '#f3f1d5'];

var legendColorList3 = ['#e95551', '#f7a449', '#fff353', '#21b3ce', '#176ebf', '#0a3a89', '#8619db', '#aa44e1', '#a5a27d', '#925c86'];
legendColorList3 = legendColorList3.concat(legendColorList3);
var legendColorList4 = ['#aecffd', '#f98a01', '#d11f1e', '#f3f1d5', '#f98a01'];

var ac_type_list = ['787', '767', '330', '737', '320', '321', '319', '350', '145', '190'];



// -------------------------------------------------------

//                          KPI

// -------------------------------------------------------



var monthList = [];
var quarterList = [];

var selected_month;
var selected_quarter;

var kpiDataReady = false;
var fetchingKpiData = false;

var all_company_data = {};
var latestDate;
var flightInfoList = {};

var eventDict = {};

var startBjTime;
var startBjTime2;
var endBjTime;
var endBjTime2;

var all_delay_cause_data;

var weekMaps = {};


// 获取例会周对应的日期范围
var weekDateRangeList;

function getAllCompanyKpiData1() {
    if ($('#loading_msk').length == 0) {
        // showLoading();
    }

    if (companylist.length == 0) {
        setTimeout(getAllCompanyKpiData1, 0);
        return;
    }

    if (fetchingKpiData) return;

    fetchingKpiData = true;

    var loadingInProgress = 0;



    // check url hash
    var hash = window.location.hash.substr(1);
    if (hash && companyCode2Name[hash] != undefined) {
        current_company_code = hash;
    } else {
        current_company_code = 'HU';
    }


    var comp_code = current_company_code;

    var len = companylist.length;
    var all_comp_codelist = [];
    var codelist_no_parent = [];

    for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        if (dat.code != parent_company && (current_company_code == parent_company || current_company_code == dat.code)) {
            codelist_no_parent.push(dat.code);
        }
        //if(current_company_code == parent_company || current_company_code == dat.code){
        all_comp_codelist.push(dat.code);
        //}
    }


    var kpi_list = [
        //'EXCUTED_NO', //执行班次
        //'TRV_NUM', //旅客量
        //'CKI_NUM', //已执行运输旅客量
        'NORMAL_RATE_ZT', //中台正常率
        'NORMAL_NO_T', //正常班次
        'SCH_NO', //计划班次
        'EST_INC_FUEL', //预估收入(含油)

        'CANCEL_NO', //取消班次
        'DIV_NO', //备降班次
        'TURNBACK_NO', //返航班次
        'DELAY_NO_240', //延误4小时
    ];


    // 本期
    if (all_company_data['kpi_value_d'] == undefined) {
        loadingInProgress++;
        var param = {
            'SOLR_CODE': 'FAC_COMP_KPI',
            'COMP_CODE': all_comp_codelist.join(','),
            'KPI_CODE': kpi_list.join(','),
            'VALUE_TYPE': 'kpi_value_d', //本期
            'DATE_TYPE': 'D,L,M,Y',
            "OPTIMIZE": 1,
            'LIMIT': query_limit
        }

        $.ajax({
            type: 'post',
            url: "/bi/query/getkpi",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {

                if (response.data != undefined) {
                    all_company_data['kpi_value_d'] = response.data;

                    ///////
                    var ddd = response.data[current_company_code]['SCH_NO']['L'];
                    var week_list = [];
                    for (var date in ddd) {
                        week_list.push(date);
                    }

                    weekDateRangeList = [];

                    var param = {
                        "DATE_ID": week_list.join(','),
                        "FIELD": "DATE_DESC" // 对应数据表字段 DATE_DESC_XS
                    }

                    $.ajax({
                        type: 'post',
                        url: "/bi/web/datetype",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(param),
                        success: function (response) {
                            for (var k in response) {
                                var v = response[k];
                                if (!isNaN(k)) {
                                    var arr = v.split('-');
                                    var weeknum = Number(k.substr(4, 3));

                                    var d1 = arr[0].substr(0, 4) + '-' + arr[0].substr(4, 2) + '-' + arr[0].substr(6, 2);
                                    var d2 = arr[1].substr(0, 4) + '-' + arr[1].substr(4, 2) + '-' + arr[1].substr(6, 2);

                                    weekDateRangeList.push({
                                        date: k,
                                        range: [d1, d2],
                                        weeknum: weeknum
                                    });
                                }
                            }

                            // 从大->小
                            weekDateRangeList.sort(function (a, b) {
                                return b.date - a.date
                            });

                            loadingInProgress--;
                            checkDataReady();

                        },
                        error: function () { }
                    });

                    //////

                    ////// 日期下拉菜单 D
                    var now = moment();
                    var cblist = [];
                    // var len = 6;
                    // var len = 30;
                    var len = 20;
                    for (var i = 0; i < len; i++) {

                        var date = now.format('YYYY-MM-DD')
                        var label = now.format('M') + '月' + now.format('D') + '日';
                        cblist.push({
                            'label': label,
                            'data': date
                        });

                        now.subtract(1, 'day'); // 今日无数据，从昨日开始
                    }
                    var selectIndex = 0;
                    // if(cblist != undefined && cblist.length >=2){
                    //     selectIndex = 2;
                    // }
                    createComboBox('main_cb_D', cblist, 98, 240, getAllCompanyKpiData1, selectIndex);


                    ////// 日期下拉菜单 L

                    var cblist = [];
                    var len = week_list.length;
                    var last_date;
                    for (var i = 0; i < len; i++) {
                        var date = week_list[i];
                        // Week: *********
                        //var label = date.substr(0,4) + '年第' + Number(date.substr(4,3)) + '周';
                        //20170419 双双要求：显示的周数+1
                        var weeknum = Number(date.substr(4, 3));
                        var label = '第' + (weeknum) + '周 ';
                        cblist.unshift({
                            'label': label,
                            'data': date
                        });
                        var year = Number(date.substr(0, 4));
                        var lastNum = weeknum - 1;
                        if (lastNum == 0) {
                            var date2 = (year - 1) + '05300';
                            weekMaps[weeknum] = date2;
                        }
                        weekMaps[weeknum + 1] = date;
                        last_date = date;
                    }
                    // cblist = cblist.slice(0, 6);
                    createComboBox('main_cb_L', cblist, 98, 240, getAllCompanyKpiData1, 1);

                    // 显示 week 日期范围
                    $('#main_cb_L .combobox_label').on('mouseover', function (event) {
                        event.preventDefault();
                        if (weekDateRangeList) {
                            var date = $('#main_cb_L').attr('data');

                            var len = weekDateRangeList.length;
                            for (var i = 0; i < len; i++) {
                                var wdat = weekDateRangeList[i];
                                if (wdat.date == date) {
                                    var str = wdat.range[0] + '~' + wdat.range[1];
                                    str = str.replace(/-/g, '');
                                    $('#week_date_range').text(str);
                                    $('#week_date_range').fadeIn();
                                    break;
                                }
                            }
                        }
                    });

                    // 隐藏 week 日期范围
                    $('#main_cb_L .combobox_label').on('mouseout', function (event) {
                        event.preventDefault();
                        if (weekDateRangeList) {
                            $('#week_date_range').fadeOut();
                        }
                    });
                    $('#main_cb_L .combobox_label').on('click', function (event) {
                        event.preventDefault();
                        if (weekDateRangeList) {
                            $('#week_date_range').fadeOut();
                        }
                    });


                    ////// 日期下拉菜单 M
                    var cblist = [];
                    // var len = 6;    
                    // var len = 12;
                    var len = 20;
                    var now = moment();
                    for (var i = 0; i < len; i++) {
                        if (i > 0) {
                            now.subtract(1, 'months');
                        }
                        var date = now.format('YYYY-MM')
                        var label = now.format('YYYY') + '年' + now.format('M') + '月';
                        cblist.push({
                            'label': label,
                            'data': date
                        });
                    }
                    createComboBox('main_cb_M', cblist, 98, 240, getAllCompanyKpiData1, 1);

                    // 显示 month 日期范围
                    $('#main_cb_M .combobox_label').on('mouseover', function (event) {
                        event.preventDefault();
                        var month = $('#main_cb_M').attr('data');
                        var curmonth = moment().format("YYYY-MM");
                        var numofdays = moment(month, "YYYY-MM").daysInMonth(); // 获取一个月有几天
                        var days = numofdays;
                        if (days < 10) {
                            days = '0' + days;
                        }
                        if (curmonth == month) {
                            days = moment().format("DD");
                        }
                        var str = month + '-01' + '~' + month + '-' + days;
                        str = str.replace(/-/g, '');
                        $('#month_date_range').text(str);
                        $('#month_date_range').fadeIn();
                    });

                    // 隐藏 month 日期范围
                    $('#main_cb_M .combobox_label').on('mouseout', function (event) {
                        event.preventDefault();
                        $('#month_date_range').fadeOut();
                    });
                    $('#main_cb_M .combobox_label').on('click', function (event) {
                        event.preventDefault();
                        $('#month_date_range').fadeOut();
                    });



                    ////// 日期下拉菜单 Y
                    var cblist = [];
                    var len = 3;
                    var now = new Date();
                    var year = now.getFullYear();
                    for (var i = 0; i < len; i++) {
                        var date = year - i;
                        var label = date + '年';
                        cblist.push({
                            'label': label,
                            'data': date
                        });
                    }

                    createComboBox('main_cb_Y', cblist, 98, 240, getAllCompanyKpiData1, 0);


                    //////



                }

            },
            error: function () {

            }
        });
    }


    // 上期
    if (all_company_data['kpi_value_sq_d'] == undefined) {
        loadingInProgress++;
        var param = {
            'SOLR_CODE': 'FAC_COMP_KPI',
            'COMP_CODE': all_comp_codelist.join(','),
            'KPI_CODE': kpi_list.join(','),
            'VALUE_TYPE': 'kpi_value_sq_d', //上期
            'DATE_TYPE': 'D,L,M,Y',
            "OPTIMIZE": 1,
            'LIMIT': query_limit
        }

        $.ajax({
            type: 'post',
            url: "/bi/query/getkpi",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {

                if (response.data != undefined) {
                    all_company_data['kpi_value_sq_d'] = response.data;
                    loadingInProgress--;
                    checkDataReady();
                }

            },
            error: function () {

            }
        });
    }


    //环比 
    if (all_company_data['kpi_ratio_sq_d'] == undefined) {
        loadingInProgress++;
        var param = {
            'SOLR_CODE': 'FAC_COMP_KPI',
            'COMP_CODE': all_comp_codelist.join(','),
            'KPI_CODE': kpi_list.join(','),
            'VALUE_TYPE': 'kpi_ratio_sq_d', //环比 
            'DATE_TYPE': 'D,L,M,Y',
            "OPTIMIZE": 1,
            'LIMIT': query_limit
        }

        $.ajax({
            type: 'post',
            url: "/bi/query/getkpi",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {

                if (response.data != undefined) {
                    all_company_data['kpi_ratio_sq_d'] = response.data;
                    loadingInProgress--;
                    checkDataReady();
                }

            },
            error: function () {

            }
        });
    }


    function checkDataReady() {
        if (loadingInProgress == 0) {
            getAllCompanyKpiData();
        }
    }

    checkDataReady();


}


function getCurrentDate() {
    return $('#main_cb_' + date_type).attr('data');
}

function getCurrentDateLabel() {
    var date = '';
    if (date_type == 'D') {
        date = $('#main_cb_D .combobox_label').text();
    } else if (date_type == 'L') {
        date = $('#main_cb_L .combobox_label').text();
    } else if (date_type == 'M') {
        date = $('#main_cb_M .combobox_label').text();
    } else if (date_type == 'Y') {
        date = $('#main_cb_Y .combobox_label').text();
    }
    return date;
}


function getAllCompanyKpiData() {

    kpiDataReady = false;

    setTitleDate();


    // reset
    eventListTabType = 'runtime';
    resetFilters()

    $('.block_r .tab').removeClass('selected');
    $('.block_r .tab1').addClass('selected');;
    $('.block_r .tabc').hide();
    $('.block_r .tabc1').show();





    // check url hash
    var hash = window.location.hash.substr(1);
    if (hash && companyCode2Name[hash] != undefined) {
        current_company_code = hash;
    } else {
        current_company_code = 'HU';
    }


    var comp_code = current_company_code;

    var len = companylist.length;
    var all_comp_codelist = [];
    var all_yhs_codelist = [];
    var codelist_no_parent = [];

    var yhscode; // 公司三字码... 港行没有 yhscode，nodeId
    var nodeId;
    for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        if (dat.code != parent_company && (current_company_code == parent_company || current_company_code == dat.code)) {
            codelist_no_parent.push(dat.code);
        }
        //if(current_company_code == parent_company || current_company_code == dat.code){
        all_comp_codelist.push(dat.code);
        //}

        if (current_company_code == dat.code) {
            if (current_company_code == parent_company) {
                yhscode = 'ALL';
                nodeId = 1;
            } else {
                yhscode = dat.yhscode;
                nodeId = dat.node;
                if (nodeId == '') {
                    nodeId = companyCode2NodeId[dat.code];
                }
            }
        }


        var ycode;
        if (dat.code == parent_company) {
            ycode = 'ALL';
        } else {
            ycode = dat.yhscode;
        }
        all_yhs_codelist.push(ycode);
    }



    // 接口用到的当日 开始 结束 时间
    var curDateStr = getCurrentDate();

    if (date_type == 'D') {
        var date = moment(curDateStr);
        startBjTime = endBjTime = date.format('YYYY-MM-DD');

        // 同期 前一天
        date.subtract(1, 'day');
        startBjTime2 = endBjTime2 = date.format('YYYY-MM-DD');

        // --- 时间end

    } else if (date_type == 'L') {
        var week1;
        var week2;

        var len = weekDateRangeList.length;
        for (var i = 0; i < len; i++) {
            var wdat = weekDateRangeList[i];
            if (wdat.date == curDateStr) {
                week1 = wdat;
                week2 = weekDateRangeList[i + 1];
                break;
            }
        }

        startBjTime = week1.range[0];
        endBjTime = week1.range[1];

        if (moment(endBjTime).isAfter(moment())) {
            endBjTime = moment().format('YYYY-MM-DD');
        }

        startBjTime2 = week2.range[0];
        endBjTime2 = week2.range[1];

        // --- 时间end

    } else if (date_type == 'M') {
        var day1 = moment(curDateStr + '-01');
        var days = day1.daysInMonth();
        var day2 = moment(curDateStr + '-' + days);

        startBjTime = day1.format('YYYY-MM-DD');
        endBjTime = day2.format('YYYY-MM-DD');

        if (curDateStr == moment().format('YYYY-MM')) {
            endBjTime = moment().format('YYYY-MM-DD');
        }

        day1 = day1.subtract(1, 'months');
        var days = day1.daysInMonth();
        day2 = moment(day1.format('YYYY-MM') + '-' + days);
        startBjTime2 = day1.format('YYYY-MM-DD');
        endBjTime2 = day2.format('YYYY-MM-DD');

        // --- 时间end

    } else if (date_type == 'Y') {
        var day1 = moment(curDateStr + '-01-01');
        var day2 = moment(curDateStr + '-12-31');

        startBjTime = day1.format('YYYY-MM-DD');
        endBjTime = day2.format('YYYY-MM-DD');

        if (curDateStr == moment().format('YYYY')) {
            endBjTime = moment().format('YYYY-MM-DD');
        }

        day1 = day1.subtract(1, 'years');
        day2 = moment(day1.format('YYYY') + '-12-31');
        startBjTime2 = day1.format('YYYY-MM-DD');
        endBjTime2 = day2.format('YYYY-MM-DD');

        // --- 时间end
    }



    var loadingInProgress = 0;

    // ------------------------------------------------------------------------
    // 各种航班统计信息。。。。
    // ------------------------------------------------------------------------



    var today = moment().format('YYYY-MM-DD');

    if (date_type == 'D') {

        var curDateStr = getCurrentDate();

        var date = moment(curDateStr);

        var stdEndUtcTime = date.format('YYYY-MM-DD') + ' 15:59:59';

        // 同期 前一天
        date.subtract(1, 'day');
        var stdStartUtcTime = date.format('YYYY-MM-DD') + ' 16:00:00';


        // 昨天
        var stdEndUtcTime2 = date.format('YYYY-MM-DD') + ' 15:59:59';

        date.subtract(1, 'day');
        var stdStartUtcTime2 = date.format('YYYY-MM-DD') + ' 16:00:00';
        // --- 时间end

        /*
        flightAmountStatic 只能取当日数据
        getFocDailySummaryInfo 只能取历史数据，不包含延误1-2，2-4的数据
        历史日的数据用getFocDailySummaryInfo接口，不显示延误1-2，2-4的
        */

        /*
        qftc 取消航班总数
        qftc0 昨日取消航班总数
        qftc1 今日取消航班总数
        qftc2 次日取消航班总数
        */

        if (today == getCurrentDate()) {
            loadingInProgress++;
            var param = {
                "stdStartUtcTime": stdStartUtcTime,
                "stdEndUtcTime": stdEndUtcTime,
                "companyCodes": codelist_no_parent.join(','), //
                "AcTypeList": "",
                "depstns": "",
                "arrstns": ""
            }

            $.ajax({
                type: 'post',
                url: "/bi/redis/7x2_flt_sts",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {

                    response.pfPercent = Number(response.pfdappPercent) / 100;
                    all_company_data['flt_sts'] = response;

                    loadingInProgress--;
                    checkDataReady();

                },
                error: function () { }
            });

        } else {

            loadingInProgress++;
            var param = {
                "companyCodes": codelist_no_parent.join(','),
                "fltDateStart": startBjTime,
                "fltDateEnd": endBjTime,
                "deleteds": 0,
                "groupByCompany": 'false',
            }

            $.ajax({
                type: 'post',
                url: "/bi/web/getFocDailySummaryInfo",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {

                    response.data[0].qftc1 = response.data[0].qftc;
                    response.data[0].dftc = response.data[0].dbftc; // 返航备降是加在一起的，这里只能这样分开了，返航设为0
                    response.data[0].bftc = 0;
                    all_company_data['flt_sts'] = response.data[0];

                    loadingInProgress--;
                    checkDataReady();
                },
                error: function () { }
            });
        }

        // 上期
        loadingInProgress++;
        var param = {
            "companyCodes": codelist_no_parent.join(','),
            "fltDateStart": startBjTime2,
            "fltDateEnd": endBjTime2,
            "deleteds": 0,
            "groupByCompany": 'false',
        }

        $.ajax({
            type: 'post',
            url: "/bi/web/getFocDailySummaryInfo",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {

                all_company_data['flt_sts_sq'] = response.data[0];

                loadingInProgress--;
                checkDataReady();
            },
            error: function () { }
        });

    } else if (date_type == 'M') {
        loadingInProgress++;
        var param = {
            "companyCodes": codelist_no_parent.join(','),
            "fltDateStart": startBjTime,
            "fltDateEnd": endBjTime,
            "deleteds": 0,
            "groupByCompany": 'false',
        }

        $.ajax({
            type: 'post',
            url: "/bi/web/getFocDailySummaryInfo",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {

                all_company_data['flt_sts'] = response.data[0];

                loadingInProgress--;
                checkDataReady();
            },
            error: function () { }
        });


        // 上期
        loadingInProgress++;
        var param = {
            "companyCodes": codelist_no_parent.join(','),
            "fltDateStart": startBjTime2,
            "fltDateEnd": endBjTime2,
            "deleteds": 0,
            "groupByCompany": 'false',
        }

        $.ajax({
            type: 'post',
            url: "/bi/web/getFocDailySummaryInfo",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {

                all_company_data['flt_sts_sq'] = response.data[0];

                loadingInProgress--;
                checkDataReady();
            },
            error: function () { }
        });


    } else if (date_type == 'Y') {


        loadingInProgress++;
        var param = {
            "companyCodes": codelist_no_parent.join(','),
            "fltDateStart": startBjTime,
            "fltDateEnd": endBjTime,
            "deleteds": 0,
            "groupByCompany": 'false',
        }

        $.ajax({
            type: 'post',
            url: "/bi/web/getFocDailySummaryInfo",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {

                all_company_data['flt_sts'] = response.data[0];

                loadingInProgress--;
                checkDataReady();
            },
            error: function () { }
        });


        // 上期
        loadingInProgress++;
        var param = {
            "companyCodes": codelist_no_parent.join(','),
            "fltDateStart": startBjTime2,
            "fltDateEnd": endBjTime2,
            "deleteds": 0,
            "groupByCompany": 'false',
        }

        $.ajax({
            type: 'post',
            url: "/bi/web/getFocDailySummaryInfo",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {

                all_company_data['flt_sts_sq'] = response.data[0];

                loadingInProgress--;
                checkDataReady();
            },
            error: function () { }
        });


    }



    // ------------------------------------------------------------------------
    // 计划旅客+已完成
    // ------------------------------------------------------------------------
    loadingInProgress++
    var param = {
        "fltAlcdtwList": codelist_no_parent.join(','),
        "fltDateStart": startBjTime,
        "fltDateEnd": endBjTime,
    }

    $.ajax({
        type: 'post',
        url: "/bi/web/getPassengerNum",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            if (response.data && response.data[0]) {
                all_company_data['getPassengerNum'] = Number(response.data[0].total); //旅客订票人数
            } else {
                all_company_data['getPassengerNum'] = 0;
            }

            loadingInProgress--;
            checkDataReady();
        },
        error: function () {
            all_company_data['getPassengerNum'] = 0;

            console.log('getPassengerNum 接口报错');

            loadingInProgress--;
            checkDataReady();
        }
    });


    // 上期
    loadingInProgress++
    var param = {
        "fltAlcdtwList": codelist_no_parent.join(','),
        "fltDateStart": startBjTime2,
        "fltDateEnd": endBjTime2,
    }

    $.ajax({
        type: 'post',
        url: "/bi/web/getPassengerNum",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            if (response.data && response.data[0]) {
                all_company_data['getPassengerNum_sq'] = Number(response.data[0].total); //旅客订票人数
            } else {
                all_company_data['getPassengerNum_sq'] = 0;
            }

            loadingInProgress--;
            checkDataReady();
        },
        error: function () {
            all_company_data['getPassengerNum_sq'] = 0;

            console.log('getPassengerNum 接口报错');

            loadingInProgress--;
            checkDataReady();
        }
    });



    // ------------------------------------------------------------------------
    // 运行不正常事件统计
    // ------------------------------------------------------------------------
    //if(date_type == 'D'){

    all_company_data['runTimeEventStatic'] = {};
    all_company_data['runTimeEventStatic_sq'] = {};

    loadingInProgress++
    var param = {
        "companyCode": 'ALL', //返回所有子公司的列表，自己求和，不包含ALL
        "startTime": startBjTime,
        "endTime": endBjTime,
    }

    $.ajax({
        type: 'post',
        url: "/bi/web/runTimeEventCompanyStatic",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            var lst = response.result;
            var len = lst ? lst.length : 0;
            var total = 0;
            for (var i = 0; i < len; i++) {
                var d = lst[i];
                var ysh = d.companyCode;
                var code = companyYshcode2Code[ysh];
                total += Number(d.count);
                all_company_data['runTimeEventStatic'][code] = d.count;
            }
            all_company_data['runTimeEventStatic']['HNAHK'] = total;

            loadingInProgress--;
            checkDataReady();
        },
        error: function () { }
    });


    loadingInProgress++
    var param = {
        "companyCode": 'ALL', //返回所有子公司的列表，自己求和，不包含ALL
        "startTime": startBjTime2,
        "endTime": endBjTime2,
    }

    $.ajax({
        type: 'post',
        url: "/bi/web/runTimeEventCompanyStatic",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            var lst = response.result;
            var len = lst.length;
            var total = 0;
            for (var i = 0; i < len; i++) {
                var d = lst[i];
                var ysh = d.companyCode;
                var code = companyYshcode2Code[ysh];
                total += Number(d.count);
                all_company_data['runTimeEventStatic_sq'][code] = d.count;
            }
            all_company_data['runTimeEventStatic_sq']['HNAHK'] = total;

            loadingInProgress--;
            checkDataReady();
        },
        error: function () { }
    });


    //}


    // ------------------------------------------------------------------------
    // 运行非正常统计列表
    // ------------------------------------------------------------------------
    //if(date_type == 'D'){

    loadingInProgress++
    var param = {
        "companyCode": yhscode,
        "startTime": startBjTime,
        "endTime": endBjTime,
    }

    $.ajax({
        type: 'post',
        url: "/bi/web/runTimeEventList",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            all_company_data['runTimeEventList'] = response.records;

            loadingInProgress--;
            checkDataReady();
        },
        error: function () { }
    });

    //}


    if (date_type == 'L') {

        loadingInProgress++
        var param = {
            "companyCode": yhscode,
            "startTime": startBjTime,
            "endTime": endBjTime,
        }

        $.ajax({
            type: 'post',
            url: "/bi/web/runTimeEventTypeStatic",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {
                if (response.result && response.result.staticList) {
                    all_company_data['runTimeEventTypeStatic'] = response.result.staticList;
                } else {
                    console.log('runTimeEventTypeStatic 接口错误');
                }

                loadingInProgress--;
                checkDataReady();
            },
            error: function () { }
        });

    }


    if (date_type == 'Y') {

        // 安全指标
        loadingInProgress++
        var param = {
            "companyCode": yhscode, //航空公司三字码
            "year": getCurrentDate(),
            "staticType": 2, //是否可为空:Y(备注:统计类型:1:万时率; 2:千时率， 填默认 是 2 千次率)
        }

        $.ajax({
            type: 'post',
            url: "/bi/web/coreRiskSafeStatic",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {
                all_company_data['coreRiskSafeStatic'] = response.result;

                loadingInProgress--;
                checkDataReady();
            },
            error: function () { }
        });
    }


    if (date_type == 'M' || date_type == 'Y') {

        var year = moment(startBjTime).format('YYYY');
        var month = moment(startBjTime).format('M');
        if (date_type == 'Y') {
            month = '';
        }

        // 核心风险统计
        loadingInProgress++
        var param = {
            "companyCode": yhscode, //航空公司三字码
            "year": year + '',
            "month": month + '', // 空统计全年
            "staticType": 2, //是否可为空:Y(备注:统计类型:1:万时率; 2:千时率， 不填默认是 2 千次率)
            "riskType": 2, //是否可为空:(备注: 险状态类型: 1:安全指标; 2:核心风险 , 不填默认是 2:核心风险)
            "riskCodeList": '1,2,3,4,5,6,7,8,9,10,11,12,13,14', //是否可为空:N(备注:核  险编码 1 事故征候 2  为原因事故征 候 3 一类事件 4   违反SOP 险 5 擦机尾/机翼 险 6   稳定进近 险 7 机械原因中断起 、复 、返航、备降 险 8 航空 刮蹭 险 9 冲偏出跑道 险 10 重着陆 险 11 配平错误  险 12 跑道侵  险 13 近地告警 险 14 偏航 险)
        }

        $.ajax({
            type: 'post',
            url: "/bi/web/coreRiskStaticList",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {
                all_company_data['coreRiskStaticList'] = response.result;

                loadingInProgress--;
                checkDataReady();
            },
            error: function () { }
        });

    }


    // ------------------------------------------------------------------------
    // 安全不正常事件统计
    // ------------------------------------------------------------------------
    //if(date_type == 'D'){

    all_company_data['findEventStatic'] = {};


    var ycode = 'ALL';
    for (var yc in companyYshcode2Code) {
        if (companyYshcode2Code[yc] == current_company_code) {
            ycode = yc;
        }
    }

    loadingInProgress++
    var param = {
        "companyCode": ycode,
        "startTime": startBjTime + ' 00:00:00',
        "endTime": endBjTime + ' 23:59:59',
    }

    $.ajax({
        type: 'post',
        url: "/bi/web/findEventStatic",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            all_company_data['findEventStatic'][current_company_code] = response.result;

            loadingInProgress--;
            checkDataReady();
        },
        error: function () { }
    });

    //}


    // ------------------------------------------------------------------------
    // 安全非正常统计列表
    // ------------------------------------------------------------------------
    //if(date_type == 'D'){
    loadingInProgress++
    var param = {
        "companyCode": yhscode,
        "startTime": startBjTime,
        "endTime": endBjTime,
    }

    $.ajax({
        type: 'post',
        url: "/bi/web/findEventList",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            all_company_data['findEventList'] = response.result.eventList;

            loadingInProgress--;
            checkDataReady();
        },
        error: function () { }
    });

    // 本期
    all_company_data['findEventStaticNum'] = {};
    loadingInProgress++
    var param = {
        "companyCode": "ALL",
        "startTime": startBjTime,
        "endTime": endBjTime,
    }

    $.ajax({
        type: 'post',
        url: "/bi/web/findEventCount",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            var data = response.data;
            if (data) {
                for (code in data) {
                    all_company_data['findEventStaticNum'][companyYshcode2Code[code]] = data[code];
                }
            }
            loadingInProgress--;
            checkDataReady();
        },
        error: function () { }
    });

    // 上期
    all_company_data['findEventStaticNum_sq'] = {};
    loadingInProgress++
    var param = {
        "companyCode": "ALL",
        "startTime": startBjTime2,
        "endTime": endBjTime2,
    }

    $.ajax({
        type: 'post',
        url: "/bi/web/findEventCount",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            var data = response.data;
            if (data) {
                for (code in data) {
                    all_company_data['findEventStaticNum_sq'][companyYshcode2Code[code]] = data[code];
                }
            }
            loadingInProgress--;
            checkDataReady();
        },
        error: function () { }
    });


    //}


    // ------------------------------------------------------------------------
    // 今日延误原因分析
    // ------------------------------------------------------------------------
    if (date_type == 'D') {

        loadingInProgress++

        var param = {
            'SOLR_CODE': 'FAC_COMP_DELAY_CAUSE_RATE_KPI',
            'COMP_CODE': all_comp_codelist.join(','),
            'KPI_CODE': 'DELAY_NO',
            'VALUE_TYPE': 'kpi_value_d',
            'DATE_TYPE': 'D',
            'LIMIT': 1,
            'OPTIMIZE': 1
        }

        $.ajax({
            type: 'post',
            url: "/bi/query/getkpi",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {

                if (response.data != undefined) {

                    if (all_delay_cause_data == undefined) {
                        all_delay_cause_data = {};
                    }
                    all_delay_cause_data['kpi_value_d'] = response.data;

                    loadingInProgress--;
                    checkDataReady();
                }

            },
            error: function () { }
        });



        loadingInProgress++

        var param = {
            'SOLR_CODE': 'FAC_COMP_DELAY_CAUSE_RATE_KPI',
            'COMP_CODE': all_comp_codelist.join(','),
            'KPI_CODE': 'DELAY_NO',
            'VALUE_TYPE': 'kpi_value_sq_d',
            'DATE_TYPE': 'D',
            'LIMIT': 1,
            'OPTIMIZE': 1
        }

        $.ajax({
            type: 'post',
            url: "/bi/query/getkpi",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {

                if (response.data != undefined) {

                    if (all_delay_cause_data == undefined) {
                        all_delay_cause_data = {};
                    }
                    all_delay_cause_data['kpi_value_sq_d'] = response.data;

                    loadingInProgress--;
                    checkDataReady();
                }

            },
            error: function () { }
        });

    }

    // 我关注的事件 1
    /*
    loadingInProgress++
    var param = {
        "companyCode": yhscode, //航空公司三字码
        "startTime": '1990-01-01',//startBjTime,
        "endTime": '2999-01-01',//endBjTime,
        "isAll": 1,//0 my
        //"userAd": userinfo.id,
        "type": 1, // 1:安全，2:运行
        "limit": 20,
        "start": 0,
        "isHistory": 0,
    }

    $.ajax({           
        type: 'post',
        url:"/bi/web/careEventStatic",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
            all_company_data['careEventStatic_aq'] = response.result;
            
            loadingInProgress--;
            checkDataReady();
        },
        error:function() {
        }
    });


    loadingInProgress++
    var param = {
        "companyCode": yhscode, //航空公司三字码
        "startTime": '1990-01-01',//startBjTime,
        "endTime": '2999-01-01',//endBjTime,
        "isAll": '1',//0 my
        //"userAd": userinfo.id,
        "type": '2', // 1:安全，2:运行
        "limit": '20',
        "start": '0',
        "isHistory": '0',
    }

    $.ajax({           
        type: 'post',
        url:"/bi/web/careEventStatic",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
            all_company_data['careEventStatic_yx'] = response.result;
            
            loadingInProgress--;
            checkDataReady();
        },
        error:function() {
        }
    });

    */



    /*
    var param = {
    }
    $.ajax({           
        type: 'post',
        url:"/bi/web/findAllCompanyLookup",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
            

        },
        error:function() {
        }
    });
    */


    /*
    var param = {
        companyId: 9
    }
    $.ajax({           
        type: 'post',
        url:"/bi/web/getMFltDelayCodeInfo",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
            

        },
        error:function() {
        }
    });
    */


    // 运行非正常事件属性情况统计接口
    //if(date_type == 'L'){
    loadingInProgress++
    var param = {
        "companyCode": yhscode,
        "startTime": startBjTime,
        "endTime": endBjTime,
    }
    $.ajax({
        type: 'post',
        url: "/bi/web/runTimePropertiesStatic",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            var r = response.result;
            all_company_data['runTimePropertiesStatic'] = r ? r.staticList : [];

            loadingInProgress--;
            checkDataReady();
        },
        error: function () { }
    });
    //}



    // 安全不正常事件惩处统计接口
    /*
    var param = {
        "companyCode": 'ALL',
        "startTime": startBjTime,
        "endTime": endBjTime,

    }
    $.ajax({           
        type: 'post',
        url:"/bi/web/eventPunishStatic",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
            response.result // Array ... companyCode,companyName,count

        },
        error:function() {
        }
    });
    */



    function checkDataReady() {
        if (loadingInProgress == 0 && !kpiDataReady) {
            kpiDataReady = true;
            fetchingKpiData = false;

            updateAllKpi();

            dfd.done(function () {
                if (hasAllCompanyPermission()) {
                    midTabId = 1;
                    $('.block_mid .tab').removeClass('selected');
                    $('.block_mid .tab1').addClass('selected');;
                    $('.block_mid .tab1').removeClass('hide');
                    $('.block_mid .tabc').addClass('hide');
                    $('.block_mid .tabc1').removeClass('hide');
                    switchTab(midTabId)
                } else {

                    midTabId = 2;
                    $('.block_mid .tab1').remove();
                    $('.block_mid .tab').removeClass('selected');
                    $('.block_mid .tab2').removeClass('hide');
                    $('.block_mid .tab2').addClass('selected');;
                    $('.block_mid .tabc').addClass('hide');
                    $('.block_mid .tabc2').removeClass('hide');
                    switchTab(midTabId)
                }

            })

            hideLoading();
        }
    }


    checkDataReady();



}



function updateAllKpi() {

    // 左上角

    if (date_type == 'D') {

        // 预估收入 ------------------------------------------------------
        var ddd = all_company_data['kpi_value_d'][current_company_code]['EST_INC_FUEL']['D'];
        var ddd_hb = all_company_data['kpi_ratio_sq_d'][current_company_code]['EST_INC_FUEL']['D'];
        latestDate = 0;
        for (var date in ddd) {
            if (Number(date) > Number(latestDate)) {
                latestDate = date;
            }
        }
        var val = Math.round(Number(ddd[latestDate]));
        var hb = Math.round(Number(ddd_hb[latestDate]) * 10000) * 0.01;
        $('#d_EST_INC_FUEL').text(val);
        $('#d_EST_INC_FUEL_hb .v').text(trimDecimal(hb, 2) + '%');

        $('#d_EST_INC_FUEL_hb').removeClass('up');
        $('#d_EST_INC_FUEL_hb').removeClass('down');
        if (hb > 0) {
            $('#d_EST_INC_FUEL_hb').addClass('up');
        } else if (hb < 0) {
            $('#d_EST_INC_FUEL_hb').addClass('down');
        }

        audioValueData.EST_INC = val;


        // 计划航班 ------------------------------------------------------
        var sch_total = getKpiVal('kpi_value_d', 'SCH_NO'); //Number(all_company_data['flt_sts'].pftc);//计划航班总数
        var sch_sq = getKpiVal('kpi_value_sq_d', 'SCH_NO'); //Number(all_company_data['flt_sts_sq'].pftc);




        audioValueData.FLT_TOTAL = sch_total;


        // 环比=(本期-上期)/上期×100%
        var hb = sch_sq > 0 ? Math.round((sch_total - sch_sq) / sch_sq * 10000) / 100 : 0;
        $('#d_SCH_NO').text(sch_total);
        $('#d_SCH_NO_hb .v').text(trimDecimal(hb, 2) + '%');

        $('#d_SCH_NO_hb').removeClass('up');
        $('#d_SCH_NO_hb').removeClass('down');
        if (hb > 0) {
            $('#d_SCH_NO_hb').addClass('up');
        } else if (hb < 0) {
            $('#d_SCH_NO_hb').addClass('down');
        }

        
        
        // 正常率 ------------------------------------------------------
        var normal_rate = getNormalRate('kpi_value_d');
        // sq
        var normal_rate_sq = getNormalRate('kpi_value_sq_d')

        if (date_type == 'D') {
            //当天的用ESB的数据
            if (moment().format('YYYY-MM-DD') == getCurrentDate()) {
                normal_rate = (Number(all_company_data['flt_sts'].pfPercent) * 100).toFixed(1);
                normal_rate_sq = (Number(all_company_data['flt_sts_sq'].pfPercent) * 100).toFixed(1);
            }
           
        }



        audioValueData.NORMAL_RATE = normal_rate

        // 环比=(本期-上期)/上期×100%
        var hb = normal_rate_sq >0 ? (normal_rate - normal_rate_sq) : 0
        $('#d_NORMAL_RATE').text(trimDecimal(normal_rate, 2) + '%');
        $('#d_NORMAL_RATE_hb .v').text(normal_rate_sq >0 ? (trimDecimal(hb, 2) + '%') : '-');

        $('#d_NORMAL_RATE_hb').removeClass('up');
        $('#d_NORMAL_RATE_hb').removeClass('down');
        if (hb > 0) {
            $('#d_NORMAL_RATE_hb').addClass('up');
        } else if (hb < 0) {
            $('#d_NORMAL_RATE_hb').addClass('down');
        }


        // 旅客人数 ------------------------------------------------------
        var psr_total = Number(all_company_data['getPassengerNum']);
        var psr_sq = Number(all_company_data['getPassengerNum_sq']);

        // 环比=(本期-上期)/上期×100%
        var hb = psr_sq > 0 ? Math.round((psr_total - psr_sq) / psr_sq * 10000) / 100 : 0;
        var psr_num = Math.round(psr_total / 1000) / 10;
        $('#d_CKI_NUM').text(psr_num);
        $('#d_CKI_NUM_hb .v').text(trimDecimal(hb, 2) + '%');

        audioValueData.TRV_NUM = psr_num;

        $('#d_CKI_NUM_hb').removeClass('up');
        $('#d_CKI_NUM_hb').removeClass('down');
        if (hb > 0) {
            $('#d_CKI_NUM_hb').addClass('up');
        } else if (hb < 0) {
            $('#d_CKI_NUM_hb').addClass('down');
        }


        // 运行不正常 ------------------------------------------------------
        // 取消 //////////
        var stsdat = all_company_data['flt_sts'];
        var stsdat_sq = all_company_data['flt_sts_sq'];

        var cancel = getKpiVal('kpi_value_d', 'CANCEL_NO'); //Number(stsdat.qftc);//取消
        var cancel_sq = getKpiVal('kpi_value_sq_d', 'CANCEL_NO'); //Number(stsdat_sq.qftc);//取消

        // 环比=(本期-上期)/上期×100%
        var hb = cancel_sq > 0 ? Math.round((cancel - cancel_sq) / cancel_sq * 1000) / 10 : 0;
        $('#d_CANCEL').text(cancel);
        var per = sch_total > 0 ? Math.round(cancel / sch_total * 1000) / 10 : 0
        $('#d_CANCEL_per').text(per + '%');
        $('#d_CANCEL_hb .v').text(trimDecimal(hb, 2) + '%');

        $('#d_CANCEL_hb').removeClass('up_red');
        $('#d_CANCEL_hb').removeClass('down_green');
        if (hb > 0) {
            $('#d_CANCEL_hb').addClass('up_red');
        } else if (hb < 0) {
            $('#d_CANCEL_hb').addClass('down_green');
        }

        audioValueData.FLT_CANCEL = cancel;


        // 返航备降 //////////
        var stsdat = all_company_data['flt_sts'];
        var stsdat_sq = all_company_data['flt_sts_sq'];

        //var fhbj = Number(stsdat.dbftc);//备降+返航
        //var fhbj_sq = Number(stsdat_sq.dbftc);//备降+返航
        var fhbj = getKpiVal('kpi_value_d', 'DIV_NO') + getKpiVal('kpi_value_d', 'TURNBACK_NO');
        var fhbj_sq = getKpiVal('kpi_value_sq_d', 'DIV_NO') + getKpiVal('kpi_value_sq_d', 'TURNBACK_NO');

        audioValueData.FLT_FH = fhbj;

        // 环比=(本期-上期)/上期×100%
        var hb = fhbj_sq > 0 ? Math.round((fhbj - fhbj_sq) / fhbj_sq * 1000) / 10 : 0;
        $('#d_RETURN').text(fhbj);
        var per = sch_total > 0 ? Math.round(fhbj / sch_total * 1000) / 10 : 0
        $('#d_RETURN_per').text(per + '%');
        $('#d_RETURN_hb .v').text(trimDecimal(hb, 2) + '%');

        $('#d_RETURN_hb').removeClass('up_red');
        $('#d_RETURN_hb').removeClass('down_green');
        if (hb > 0) {
            $('#d_RETURN_hb').addClass('up_red');
        } else if (hb < 0) {
            $('#d_RETURN_hb').addClass('down_green');
        }

        // 延误>4 //////////
        var stsdat = all_company_data['flt_sts'];
        var stsdat_sq = all_company_data['flt_sts_sq'];

        //var delay4 = Number(stsdat.pfdtc4);//延误>4
        //var delay4_sq = Number(stsdat_sq.pfdtc4);//延误>4

        var delay4 = getKpiVal('kpi_value_d', 'DELAY_NO_240');
        var delay4_sq = getKpiVal('kpi_value_sq_d', 'DELAY_NO_240');

        audioValueData.FLT_4H = delay4;

        // 环比=(本期-上期)/上期×100%
        var hb = delay4_sq > 0 ? Math.round((delay4 - delay4_sq) / delay4_sq * 1000) / 10 : 0;
        $('#d_DELAY4').text(delay4);
        var per = sch_total > 0 ? Math.round(delay4 / sch_total * 1000) / 10 : 0
        $('#d_DELAY4_per').text(per + '%');
        $('#d_DELAY4_hb .v').text(hb + '%');

        $('#d_DELAY4_hb').removeClass('up_red');
        $('#d_DELAY4_hb').removeClass('down_green');
        if (hb > 0) {
            $('#d_DELAY4_hb').addClass('up_red');
        } else if (hb < 0) {
            $('#d_DELAY4_hb').addClass('down_green');
        }


        var eventCount = all_company_data['runTimeEventStatic'][current_company_code];
        var eventCount_sq = all_company_data['runTimeEventStatic_sq'][current_company_code];

        if (isNaN(eventCount)) {
            eventCount = 0;
        }
        if (isNaN(eventCount_sq)) {
            eventCount_sq = 0;
        }

        // 环比=(本期-上期)/上期×100%
        var hb = eventCount_sq > 0 ? Math.round((eventCount - eventCount_sq) / eventCount_sq * 1000) / 10 : 0;
        $('#d_ABNORMAL_EVT').text(eventCount);
        var per = sch_total > 0 ? Math.round(eventCount / sch_total * 1000) / 10 : 0;
        $('#d_ABNORMAL_EVT_per').text(per + '%'); // 非正常/已执行航班数量
        $('#d_ABNORMAL_EVT_hb .v').text(trimDecimal(hb, 2) + '%');

        $('#d_ABNORMAL_EVT_hb').removeClass('up_red');
        $('#d_ABNORMAL_EVT_hb').removeClass('down_green');
        if (hb > 0) {
            $('#d_ABNORMAL_EVT_hb').addClass('up_red');
        } else if (hb < 0) {
            $('#d_ABNORMAL_EVT_hb').addClass('down_green');
        }

        audioValueData.FLT_ABN = eventCount;


        // 安全不正常 ------------------------------------------------------
        /*
        安全事件总数 eventCount
        意外事件总数 accidentCount
        意外事件占 accidentRate
        机械事件总数 machineCount
        机械事件占 machineRate
        为事件总数 humanEventCount
        为事件占 humanEventRate
        其他事件总数 otherEventCount
        其他事件占 otherEventRate
        事故征候总数 signCount
        一类事件总数 seriousCount
        航空公司三字码 companyCode
        */
        var evtstsdat = all_company_data['findEventStatic'][current_company_code];
        if (evtstsdat) {
            var eventCount = Number(evtstsdat.eventCount);
            var eventCount_sq = Number(evtstsdat.eventCountPre);
        } else {
            var eventCount = 0;
            var eventCount_sq = 0;
        }


        // 环比=(本期-上期)/上期×100%
        var hb = eventCount_sq > 0 ? Math.round((eventCount - eventCount_sq) / eventCount_sq * 1000) / 10 : 0;
        $('#d_ABNORMAL_EVT2 .vv .v').text(eventCount);
        //$('#d_ABNORMAL_EVT2 .hb .v').text(trimDecimal(hb,2)+'%');
        var os = eventCount - eventCount_sq;
        var ost = os > 0 ? ('+' + os) : os
        $('#d_ABNORMAL_EVT2 .hb .v').text(ost);

        $('#d_ABNORMAL_EVT2 .hb').removeClass('up_red');
        $('#d_ABNORMAL_EVT2 .hb').removeClass('down_green');
        if (os > 0) {
            $('#d_ABNORMAL_EVT2 .hb').addClass('up_red');
        } else if (os < 0) {
            $('#d_ABNORMAL_EVT2 .hb').addClass('down_green');
        }

        audioValueData.EVT_TOTAL = eventCount;
        audioValueData.EVT_TOTAL_HB = os;


        // 人为
        var val = evtstsdat ? Number(evtstsdat.humanEventCount) : 0;
        var val2 = evtstsdat ? Number(evtstsdat.humanEventRate) : 0;
        $('#d_ABNORMAL_EVT_RW .val').text(val);
        $('#d_ABNORMAL_EVT_RW .per .v').html(val2);

        // 意外
        var val = evtstsdat ? Number(evtstsdat.accidentCount) : 0;
        var val2 = evtstsdat ? Number(evtstsdat.accidentRate) : 0;
        $('#d_ABNORMAL_EVT_YW .val').text(val);
        $('#d_ABNORMAL_EVT_YW .per .v').html(val2);

        // 机械
        var val = evtstsdat ? Number(evtstsdat.machineCount) : 0;
        var val2 = evtstsdat ? Number(evtstsdat.machineRate) : 0;
        $('#d_ABNORMAL_EVT_JX .val').text(val);
        $('#d_ABNORMAL_EVT_JX .per .v').html(val2);

        // 其它
        var val = evtstsdat ? Number(evtstsdat.otherEventCount) : 0;
        var val2 = evtstsdat ? Number(evtstsdat.otherEventRate) : 0;
        $('#d_ABNORMAL_EVT_QT .val').text(val);
        $('#d_ABNORMAL_EVT_QT .per .v').html(val2);

        // 事故征候
        var val = evtstsdat ? Number(evtstsdat.signCount) : 0;
        var val_sq = 0; //evtstsdat_sq ? Number(evtstsdat_sq.signCount) : 0;
        var change = val - val_sq;
        if (change > 0) {
            change = '+' + change;
        }
        $('#d_ABNORMAL_EVT_SIGN').text(val);
        $('#d_ABNORMAL_EVT_SIGN_hb').text(change);

        // // 一类事件
        // var val = evtstsdat ? Number(evtstsdat.seriousCount) : 0;
        // 一类事件等于一类事件数+岗位红线数
        var val = evtstsdat ? Number(evtstsdat.seriousCount) : 0;
        if (evtstsdat) {
            val += Number(evtstsdat.securityLineCount);
        }
        var val_sq = 0; //evtstsdat_sq ? Number(evtstsdat_sq.seriousCount) : 0;
        var change = val - val_sq;
        if (change > 0) {
            change = '+' + change;
        }
        $('#d_ABNORMAL_EVT_SERI').text(val);
        $('#d_ABNORMAL_EVT_SERI_hb').text(change);

        if (evtstsdat) {
            audioValueData.EVT_T1 = evtstsdat.accidentCount
            audioValueData.EVT_T2 = evtstsdat.humanEventCount
            audioValueData.EVT_T3 = evtstsdat.machineCount
            audioValueData.EVT_T4 = evtstsdat.otherEventCount
        }




        // 运行不正常事件 环形 图标
        setChartRunTimeCircle();

        // ==================  Middle ===================



        // setChartCompsFltAndNormalrate();
        // setChartCompsAbnormalEvents();
        // setChartCompsSecurityAbnormalEvents();
        // setChartCompsEstInc();


        // 
        if (eventListTabType == 'runtime') {
            showRunTimeEventList();
        } else {
            showSecurityEventList();
        }



    } // D end
    else if (date_type == 'L') {

        // 预估收入 ------------------------------------------------------
        var ddd = all_company_data['kpi_value_d'][current_company_code]['EST_INC_FUEL']['L'];
        var ddd_hb = all_company_data['kpi_ratio_sq_d'][current_company_code]['EST_INC_FUEL']['L'];
        latestDate = 0;
        for (var date in ddd) {
            if (Number(date) > Number(latestDate)) {
                latestDate = date;
            }
        }
        var val = Math.round(Number(ddd[latestDate]));
        var hb = Math.round(Number(ddd_hb[latestDate]) * 10000) * 0.01;
        $('#l_EST_INC_FUEL').text(val);
        $('#l_EST_INC_FUEL_hb .v').text(trimDecimal(hb, 2) + '%');

        $('#l_EST_INC_FUEL_hb').removeClass('up');
        $('#l_EST_INC_FUEL_hb').removeClass('down');
        if (hb > 0) {
            $('#l_EST_INC_FUEL_hb').addClass('up');
        } else if (hb < 0) {
            $('#l_EST_INC_FUEL_hb').addClass('down');
        }

        audioValueData.EST_INC = val;


        // 计划航班 ------------------------------------------------------
        var sch = getKpiVal('kpi_value_d', 'SCH_NO'); //Number(all_company_data['flt_sts'].pftc);//计划航班总数
        var sch_sq = getKpiVal('kpi_value_sq_d', 'SCH_NO'); //Number(all_company_data['flt_sts_sq'].pftc);


        var sch_total = sch;

        audioValueData.FLT_TOTAL = sch_total;

        // 环比=(本期-上期)/上期×100%
        var hb = sch_sq > 0 ? Math.round((sch - sch_sq) / sch_sq * 10000) / 100 : 0;
        $('#l_SCH_NO').text(sch);
        $('#l_SCH_NO_hb .v').text(trimDecimal(hb, 2) + '%');

        $('#l_SCH_NO_hb').removeClass('up');
        $('#l_SCH_NO_hb').removeClass('down');
        if (hb > 0) {
            $('#l_SCH_NO_hb').addClass('up');
        } else if (hb < 0) {
            $('#l_SCH_NO_hb').addClass('down');
        }

        // 正常率 ------------------------------------------------------
        var normal_rate = getNormalRate('kpi_value_d');
        // sq
        var normal_rate_sq = getNormalRate('kpi_value_sq_d')
        // 正常率 ------------------------------------------------------
        audioValueData.NORMAL_RATE = normal_rate
        // sq
        // 环比=(本期-上期)/上期×100%
        var hb = normal_rate_sq >0 ? normal_rate - normal_rate_sq : '';
        $('#l_NORMAL_RATE').text(trimDecimal(normal_rate, 2) + '%');
        $('#l_NORMAL_RATE_hb .v').text(normal_rate_sq >0 ? (trimDecimal(hb, 2) + '%'):'');

        $('#l_NORMAL_RATE_hb').removeClass('up');
        $('#l_NORMAL_RATE_hb').removeClass('down');
        if (hb > 0) {
            $('#l_NORMAL_RATE_hb').addClass('up');
        } else if (hb < 0) {
            $('#l_NORMAL_RATE_hb').addClass('down');
        }


        // 旅客人数 ------------------------------------------------------
        var psr_total = Number(all_company_data['getPassengerNum']);
        var psr_sq = Number(all_company_data['getPassengerNum_sq']);

        // 环比=(本期-上期)/上期×100%
        var hb = psr_sq > 0 ? Math.round((psr_total - psr_sq) / psr_sq * 10000) / 100 : 0;
        var psr_num = Math.round(psr_total / 1000) / 10
        $('#l_CKI_NUM').text(psr_num);
        $('#l_CKI_NUM_hb .v').text(trimDecimal(hb, 2) + '%');

        audioValueData.TRV_NUM = psr_num;

        $('#l_CKI_NUM_hb').removeClass('up');
        $('#l_CKI_NUM_hb').removeClass('down');
        if (hb > 0) {
            $('#l_CKI_NUM_hb').addClass('up');
        } else if (hb < 0) {
            $('#l_CKI_NUM_hb').addClass('down');
        }


        // 运行不正常 ------------------------------------------------------

        // 取消 //////////
        var stsdat = all_company_data['flt_sts'];
        var stsdat_sq = all_company_data['flt_sts_sq'];

        //var cancel = Number(stsdat.qftc);//取消
        //var cancel_sq = Number(stsdat_sq.qftc);//取消
        var cancel = getKpiVal('kpi_value_d', 'CANCEL_NO'); //Number(stsdat.qftc);//取消
        var cancel_sq = getKpiVal('kpi_value_sq_d', 'CANCEL_NO'); //Number(stsdat_sq.qftc);//取消

        // 环比=(本期-上期)/上期×100%
        var hb = cancel_sq > 0 ? Math.round((cancel - cancel_sq) / cancel_sq * 1000) / 10 : 0;
        $('#l_CANCEL').text(cancel);
        var per = sch_total > 0 ? Math.round(cancel / sch_total * 1000) / 10 : 0
        $('#l_CANCEL_per').text(per + '%');
        $('#l_CANCEL_hb .v').text(trimDecimal(hb, 2) + '%');

        $('#l_CANCEL_hb').removeClass('up_red');
        $('#l_CANCEL_hb').removeClass('down_green');
        if (hb > 0) {
            $('#l_CANCEL_hb').addClass('up_red');
        } else if (hb < 0) {
            $('#l_CANCEL_hb').addClass('down_green');
        }

        audioValueData.FLT_CANCEL = cancel;


        // 返航备降 //////////
        var stsdat = all_company_data['flt_sts'];
        var stsdat_sq = all_company_data['flt_sts_sq'];

        //var fhbj = Number(stsdat.dbftc);//备降+返航
        //var fhbj_sq = Number(stsdat_sq.dbftc);//备降+返航
        var fhbj = getKpiVal('kpi_value_d', 'DIV_NO') + getKpiVal('kpi_value_d', 'TURNBACK_NO');
        var fhbj_sq = getKpiVal('kpi_value_sq_d', 'DIV_NO') + getKpiVal('kpi_value_sq_d', 'TURNBACK_NO');

        audioValueData.FLT_FH = fhbj;

        // 环比=(本期-上期)/上期×100%
        var hb = fhbj_sq > 0 ? Math.round((fhbj - fhbj_sq) / fhbj_sq * 1000) / 10 : 0;
        $('#l_RETURN').text(fhbj);
        var per = sch_total > 0 ? Math.round(fhbj / sch_total * 1000) / 10 : 0
        $('#l_RETURN_per').text(per + '%');
        $('#l_RETURN_hb .v').text(trimDecimal(hb, 2) + '%');

        $('#l_RETURN_hb').removeClass('up_red');
        $('#l_RETURN_hb').removeClass('down_green');
        if (hb > 0) {
            $('#l_RETURN_hb').addClass('up_red');
        } else if (hb < 0) {
            $('#l_RETURN_hb').addClass('down_green');
        }

        // 延误>4 //////////
        var stsdat = all_company_data['flt_sts'];
        var stsdat_sq = all_company_data['flt_sts_sq'];

        //var delay4 = Number(stsdat.pfdtc4);//延误>4
        //var delay4_sq = Number(stsdat_sq.pfdtc4);//延误>4
        var delay4 = getKpiVal('kpi_value_d', 'DELAY_NO_240');
        var delay4_sq = getKpiVal('kpi_value_sq_d', 'DELAY_NO_240');

        audioValueData.FLT_4H = delay4;

        // 环比=(本期-上期)/上期×100%
        var hb = delay4_sq > 0 ? Math.round((delay4 - delay4_sq) / delay4_sq * 1000) / 10 : 0;
        $('#l_DELAY4').text(delay4);
        var per = sch_total > 0 ? Math.round(delay4 / sch_total * 1000) / 10 : 0
        $('#l_DELAY4_per').text(per + '%');
        $('#l_DELAY4_hb .v').text(hb + '%');

        $('#l_DELAY4_hb').removeClass('up_red');
        $('#l_DELAY4_hb').removeClass('down_green');
        if (hb > 0) {
            $('#l_DELAY4_hb').addClass('up_red');
        } else if (hb < 0) {
            $('#l_DELAY4_hb').addClass('down_green');
        }


        var eventCount = all_company_data['runTimeEventStatic'][current_company_code];
        var eventCount_sq = all_company_data['runTimeEventStatic_sq'][current_company_code];

        if (isNaN(eventCount)) {
            eventCount = 0;
        }
        if (isNaN(eventCount_sq)) {
            eventCount_sq = 0;
        }

        // 环比=(本期-上期)/上期×100%
        var hb = eventCount_sq > 0 ? Math.round((eventCount - eventCount_sq) / eventCount_sq * 1000) / 10 : 0;
        $('#l_ABNORMAL_EVT').text(eventCount);
        var per = sch_total > 0 ? Math.round(eventCount / sch_total * 1000) / 10 : 0;
        $('#l_ABNORMAL_EVT_per').text(per + '%'); // 非正常/已执行航班数量
        $('#l_ABNORMAL_EVT_hb .v').text(trimDecimal(hb, 2) + '%');

        $('#l_ABNORMAL_EVT_hb').removeClass('up_red');
        $('#l_ABNORMAL_EVT_hb').removeClass('down_green');
        if (hb > 0) {
            $('#l_ABNORMAL_EVT_hb').addClass('up_red');
        } else if (hb < 0) {
            $('#l_ABNORMAL_EVT_hb').addClass('down_green');
        }

        audioValueData.FLT_ABN = eventCount;



        // 安全不正常 ------------------------------------------------------
        /*
        安全事件总数 eventCount
        意外事件总数 accidentCount
        意外事件占 accidentRate
        机械事件总数 machineCount
        机械事件占 machineRate
        为事件总数 humanEventCount
        为事件占 humanEventRate
        其他事件总数 otherEventCount
        其他事件占 otherEventRate
        事故征候总数 signCount
        一类事件总数 seriousCount
        航空公司三字码 companyCode
        */
        var evtstsdat = all_company_data['findEventStatic'][current_company_code];
        if (evtstsdat) {
            var eventCount = Number(evtstsdat.eventCount);
            var eventCount_sq = Number(evtstsdat.eventCountPre);
        } else {
            var eventCount = 0;
            var eventCount_sq = 0;
        }


        // 环比=(本期-上期)/上期×100%
        var hb = eventCount_sq > 0 ? Math.round((eventCount - eventCount_sq) / eventCount_sq * 1000) / 10 : 0;
        $('#l_ABNORMAL_EVT2 .vv .v').text(eventCount);
        //$('#l_ABNORMAL_EVT2 .hb .v').text(trimDecimal(hb,2)+'%');
        var os = eventCount - eventCount_sq;
        var ost = os > 0 ? ('+' + os) : os
        $('#l_ABNORMAL_EVT2 .hb .v').text(ost);

        $('#l_ABNORMAL_EVT2 .hb').removeClass('up_red');
        $('#l_ABNORMAL_EVT2 .hb').removeClass('down_green');
        if (os > 0) {
            $('#l_ABNORMAL_EVT2 .hb').addClass('up_red');
        } else if (os < 0) {
            $('#l_ABNORMAL_EVT2 .hb').addClass('down_green');
        }

        audioValueData.EVT_TOTAL = eventCount;
        audioValueData.EVT_TOTAL_HB = os;


        // 人为
        var val = evtstsdat ? Number(evtstsdat.humanEventCount) : 0;
        var val2 = evtstsdat ? Number(evtstsdat.humanEventRate) : 0;
        $('#l_ABNORMAL_EVT_RW .val').text(val);
        $('#l_ABNORMAL_EVT_RW .per .v').html(val2);

        // 意外
        var val = evtstsdat ? Number(evtstsdat.accidentCount) : 0;
        var val2 = evtstsdat ? Number(evtstsdat.accidentRate) : 0;
        $('#l_ABNORMAL_EVT_YW .val').text(val);
        $('#l_ABNORMAL_EVT_YW .per .v').html(val2);

        // 机械
        var val = evtstsdat ? Number(evtstsdat.machineCount) : 0;
        var val2 = evtstsdat ? Number(evtstsdat.machineRate) : 0;
        $('#l_ABNORMAL_EVT_JX .val').text(val);
        $('#l_ABNORMAL_EVT_JX .per .v').html(val2);

        // 其它
        var val = evtstsdat ? Number(evtstsdat.otherEventCount) : 0;
        var val2 = evtstsdat ? Number(evtstsdat.otherEventRate) : 0;
        $('#l_ABNORMAL_EVT_QT .val').text(val);
        $('#l_ABNORMAL_EVT_QT .per .v').html(val2);

        // 事故征候
        var val = evtstsdat ? Number(evtstsdat.signCount) : 0;
        var val_sq = 0; //evtstsdat_sq ? Number(evtstsdat_sq.signCount) : 0;
        var change = val - val_sq;
        if (change > 0) {
            change = '+' + change;
        }
        $('#l_ABNORMAL_EVT_SIGN').text(val);
        $('#l_ABNORMAL_EVT_SIGN_hb').text(change);


        // // 一类事件
        // var val = evtstsdat ? Number(evtstsdat.seriousCount) : 0;
        // 一类事件等于一类事件数+岗位红线数
        var val = evtstsdat ? Number(evtstsdat.seriousCount) : 0;
        if (evtstsdat) {
            val += Number(evtstsdat.securityLineCount);
        }
        var val_sq = 0; //evtstsdat_sq ? Number(evtstsdat_sq.seriousCount) : 0;
        var change = val - val_sq;
        if (change > 0) {
            change = '+' + change;
        }
        $('#l_ABNORMAL_EVT_SERI').text(val);
        $('#l_ABNORMAL_EVT_SERI_hb').text(change);

        if (evtstsdat) {
            audioValueData.EVT_T1 = evtstsdat.accidentCount
            audioValueData.EVT_T2 = evtstsdat.humanEventCount
            audioValueData.EVT_T3 = evtstsdat.machineCount
            audioValueData.EVT_T4 = evtstsdat.otherEventCount
        } else {
            audioValueData.EVT_T1 = 0
            audioValueData.EVT_T2 = 0
            audioValueData.EVT_T3 = 0
            audioValueData.EVT_T4 = 0
        }



        // ==================  Middle ===================

        setChartRunTimeCircle()


        //
        setChartCompsFltAndNormalrate();
        setChartCompsAbnormalEvents();
        setChartCompsSecurityAbnormalEvents();
        setChartCompsEstInc();


        // 
        if (eventListTabType == 'runtime') {
            showRunTimeEventList();
        } else {
            showSecurityEventList();
        }



    } // L end
    else if (date_type == 'M') {


        // 预估收入 ------------------------------------------------------
        var ddd = all_company_data['kpi_value_d'][current_company_code]['EST_INC_FUEL']['M'];
        var ddd_hb = all_company_data['kpi_ratio_sq_d'][current_company_code]['EST_INC_FUEL']['M'];
        latestDate = 0;
        for (var date in ddd) {
            if (Number(date) > Number(latestDate)) {
                latestDate = date;
            }
        }
        var val = Math.round(Number(ddd[latestDate]));
        var hb = Math.round(Number(ddd_hb[latestDate]) * 10000) * 0.01;
        $('#m_EST_INC_FUEL').text(val);
        $('#m_EST_INC_FUEL_hb .v').text(trimDecimal(hb, 2) + '%');

        $('#m_EST_INC_FUEL_hb').removeClass('up');
        $('#m_EST_INC_FUEL_hb').removeClass('down');
        if (hb > 0) {
            $('#m_EST_INC_FUEL_hb').addClass('up');
        } else if (hb < 0) {
            $('#m_EST_INC_FUEL_hb').addClass('down');
        }

        audioValueData.EST_INC = val;


        // 计划航班 ------------------------------------------------------
        var sch = getKpiVal('kpi_value_d', 'SCH_NO');
        var sch_sq = getKpiVal('kpi_value_sq_d', 'SCH_NO');



        // 环比=(本期-上期)/上期×100%
        var hb = sch_sq > 0 ? Math.round((sch - sch_sq) / sch_sq * 10000) / 100 : 0;
        $('#m_SCH_NO').text(sch);
        $('#m_SCH_NO_hb .v').text(trimDecimal(hb, 2) + '%');

        $('#m_SCH_NO_hb').removeClass('up');
        $('#m_SCH_NO_hb').removeClass('down');
        if (hb > 0) {
            $('#m_SCH_NO_hb').addClass('up');
        } else if (hb < 0) {
            $('#m_SCH_NO_hb').addClass('down');
        }

        var sch_total = sch;

        audioValueData.FLT_TOTAL = sch_total;


        // 正常率 ------------------------------------------------------
        var normal_rate = getNormalRate('kpi_value_d');
        // sq
        var normal_rate_sq = getNormalRate('kpi_value_sq_d')
        audioValueData.NORMAL_RATE = normal_rate
        // sq
        var hb = normal_rate - normal_rate_sq;
        $('#m_NORMAL_RATE').text(trimDecimal(normal_rate, 2) + '%');
        $('#m_NORMAL_RATE_hb .v').text(normal_rate_sq>0 ? (trimDecimal(hb, 2) + '%'):'');

        $('#m_NORMAL_RATE_hb').removeClass('up');
        $('#m_NORMAL_RATE_hb').removeClass('down');
        if (hb > 0) {
            $('#m_NORMAL_RATE_hb').addClass('up');
        } else if (hb < 0) {
            $('#m_NORMAL_RATE_hb').addClass('down');
        }


        // 旅客人数 ------------------------------------------------------
        var psr_total = Number(all_company_data['getPassengerNum']);
        var psr_sq = Number(all_company_data['getPassengerNum_sq']);

        // 环比=(本期-上期)/上期×100%
        var hb = psr_sq > 0 ? Math.round((psr_total - psr_sq) / psr_sq * 10000) / 100 : 0;
        var psr_num = Math.round(psr_total / 1000) / 10
        $('#m_CKI_NUM').text(psr_num);
        $('#m_CKI_NUM_hb .v').text(trimDecimal(hb, 2) + '%');

        audioValueData.TRV_NUM = psr_num;

        $('#m_CKI_NUM_hb').removeClass('up');
        $('#m_CKI_NUM_hb').removeClass('down');
        if (hb > 0) {
            $('#m_CKI_NUM_hb').addClass('up');
        } else if (hb < 0) {
            $('#m_CKI_NUM_hb').addClass('down');
        }


        // 运行不正常 ------------------------------------------------------

        // 取消 //////////
        var stsdat = all_company_data['flt_sts'];
        var stsdat_sq = all_company_data['flt_sts_sq'];

        var cancel = getKpiVal('kpi_value_d', 'CANCEL_NO'); //Number(stsdat.qftc);//取消
        var cancel_sq = getKpiVal('kpi_value_sq_d', 'CANCEL_NO'); //Number(stsdat_sq.qftc);//取消

        // 环比=(本期-上期)/上期×100%
        var hb = cancel_sq > 0 ? Math.round((cancel - cancel_sq) / cancel_sq * 1000) / 10 : 0;
        $('#m_CANCEL').text(cancel);
        var per = sch_total > 0 ? Math.round(cancel / sch_total * 1000) / 10 : 0
        $('#m_CANCEL_per').text(per + '%');
        $('#m_CANCEL_hb .v').text(trimDecimal(hb, 2) + '%');

        $('#m_CANCEL_hb').removeClass('up_red');
        $('#m_CANCEL_hb').removeClass('down_green');
        if (hb > 0) {
            $('#m_CANCEL_hb').addClass('up_red');
        } else if (hb < 0) {
            $('#m_CANCEL_hb').addClass('down_green');
        }

        audioValueData.FLT_CANCEL = cancel;


        // 返航备降 //////////
        var stsdat = all_company_data['flt_sts'];
        var stsdat_sq = all_company_data['flt_sts_sq'];

        //var fhbj = Number(stsdat.dbftc);//备降+返航
        //var fhbj_sq = Number(stsdat_sq.dbftc);//备降+返航
        var fhbj = getKpiVal('kpi_value_d', 'DIV_NO') + getKpiVal('kpi_value_d', 'TURNBACK_NO');
        var fhbj_sq = getKpiVal('kpi_value_sq_d', 'DIV_NO') + getKpiVal('kpi_value_sq_d', 'TURNBACK_NO');

        audioValueData.FLT_FH = fhbj;

        // 环比=(本期-上期)/上期×100%
        var hb = fhbj_sq > 0 ? Math.round((fhbj - fhbj_sq) / fhbj_sq * 1000) / 10 : 0;
        $('#m_RETURN').text(fhbj);
        var per = sch_total > 0 ? Math.round(fhbj / sch_total * 1000) / 10 : 0
        $('#m_RETURN_per').text(per + '%');
        $('#m_RETURN_hb .v').text(trimDecimal(hb, 2) + '%');

        $('#m_RETURN_hb').removeClass('up_red');
        $('#m_RETURN_hb').removeClass('down_green');
        if (hb > 0) {
            $('#m_RETURN_hb').addClass('up_red');
        } else if (hb < 0) {
            $('#m_RETURN_hb').addClass('down_green');
        }

        // 延误>4 //////////
        var stsdat = all_company_data['flt_sts'];
        var stsdat_sq = all_company_data['flt_sts_sq'];

        //var delay4 = Number(stsdat.pfdtc4);//延误>4
        //var delay4_sq = Number(stsdat_sq.pfdtc4);//延误>4
        var delay4 = getKpiVal('kpi_value_d', 'DELAY_NO_240');
        var delay4_sq = getKpiVal('kpi_value_sq_d', 'DELAY_NO_240');

        audioValueData.FLT_4H = delay4;

        // 环比=(本期-上期)/上期×100%
        var hb = delay4_sq > 0 ? Math.round((delay4 - delay4_sq) / delay4_sq * 1000) / 10 : 0;
        $('#m_DELAY4').text(delay4);
        var per = sch_total > 0 ? Math.round(delay4 / sch_total * 1000) / 10 : 0
        $('#m_DELAY4_per').text(per + '%');
        $('#m_DELAY4_hb .v').text(hb + '%');

        $('#m_DELAY4_hb').removeClass('up_red');
        $('#m_DELAY4_hb').removeClass('down_green');
        if (hb > 0) {
            $('#m_DELAY4_hb').addClass('up_red');
        } else if (hb < 0) {
            $('#m_DELAY4_hb').addClass('down_green');
        }


        var eventCount = all_company_data['runTimeEventStatic'][current_company_code];
        var eventCount_sq = all_company_data['runTimeEventStatic_sq'][current_company_code];

        if (isNaN(eventCount)) {
            eventCount = 0;
        }
        if (isNaN(eventCount_sq)) {
            eventCount_sq = 0;
        }

        // 环比=(本期-上期)/上期×100%
        var hb = eventCount_sq > 0 ? Math.round((eventCount - eventCount_sq) / eventCount_sq * 1000) / 10 : 0;
        $('#m_ABNORMAL_EVT').text(eventCount);
        var per = sch_total > 0 ? Math.round(eventCount / sch_total * 1000) / 10 : 0;
        $('#m_ABNORMAL_EVT_per').text(per + '%'); // 非正常/已执行航班数量
        $('#m_ABNORMAL_EVT_hb .v').text(trimDecimal(hb, 2) + '%');

        $('#m_ABNORMAL_EVT_hb').removeClass('up_red');
        $('#m_ABNORMAL_EVT_hb').removeClass('down_green');
        if (hb > 0) {
            $('#m_ABNORMAL_EVT_hb').addClass('up_red');
        } else if (hb < 0) {
            $('#m_ABNORMAL_EVT_hb').addClass('down_green');
        }

        audioValueData.FLT_ABN = eventCount;



        // 安全不正常 ------------------------------------------------------
        /*
        安全事件总数 eventCount
        意外事件总数 accidentCount
        意外事件占 accidentRate
        机械事件总数 machineCount
        机械事件占 machineRate
        为事件总数 humanEventCount
        为事件占 humanEventRate
        其他事件总数 otherEventCount
        其他事件占 otherEventRate
        事故征候总数 signCount
        一类事件总数 seriousCount
        航空公司三字码 companyCode
        */
        var evtstsdat = all_company_data['findEventStatic'][current_company_code];
        if (evtstsdat) {
            var eventCount = Number(evtstsdat.eventCount);
            var eventCount_sq = Number(evtstsdat.eventCountPre);
        } else {
            var eventCount = 0;
            var eventCount_sq = 0;
        }


        // 环比=(本期-上期)/上期×100%
        var hb = eventCount_sq > 0 ? Math.round((eventCount - eventCount_sq) / eventCount_sq * 1000) / 10 : 0;
        $('#m_ABNORMAL_EVT2').text(eventCount);
        //$('#m_ABNORMAL_EVT2_hb').text(trimDecimal(hb,2)+'%');
        var os = eventCount - eventCount_sq;
        var ost = os > 0 ? ('+' + os) : os
        $('#m_ABNORMAL_EVT2_hb').text(ost);

        $('#m_ABNORMAL_EVT2_hb').removeClass('up_red');
        $('#m_ABNORMAL_EVT2_hb').removeClass('down_green');
        if (os > 0) {
            $('#m_ABNORMAL_EVT2_hb').addClass('up_red');
        } else if (os < 0) {
            $('#m_ABNORMAL_EVT2_hb').addClass('down_green');
        }

        audioValueData.EVT_TOTAL = eventCount;
        audioValueData.EVT_TOTAL_HB = os;

        if (evtstsdat) {
            if (current_company_code == parent_company) {
                var eventCount = Number(evtstsdat.eventCount);
                var eventCount_sq = Number(evtstsdat.eventCountPre);
            } else {
                var eventCount = Number(evtstsdat.totalRw);
                var eventCount_sq = Number(evtstsdat.totalRwPre);
            }

        } else {
            var eventCount = 0;
            var eventCount_sq = 0;
        }
        // 千次率
        var sch_total_esb = Number(all_company_data['flt_sts'].cftc);
        var sch_total_sq_esb = Number(all_company_data['flt_sts_sq'].cftc);
        // var perq = sch_total > 0 ? Math.round(eventCount / sch_total * 100000) / 100 : 0;
        // var perq_sq = sch_total_sq > 0 ? Math.round(eventCount_sq / sch_total_sq * 100000) / 100 : 0;
        var perq = sch_total_esb > 0 ? Math.round(eventCount / sch_total_esb * 100000) / 100 : 0;
        var perq_sq = sch_total_sq_esb > 0 ? Math.round(eventCount_sq / sch_total_sq_esb * 100000) / 100 : 0;
        var hb = perq_sq > 0 ? Math.round((perq - perq_sq) / perq_sq * 10000) / 100 : 0;

        var os = perq - perq_sq;
        os = trimDecimal(os, 2);
        var ost = os > 0 ? ('+' + os) : os;
        $('#m_ABNORMAL_EVT2_Q').text(trimDecimal(perq, 2));
        $('#m_ABNORMAL_EVT2_Q_hb').text(ost);

        $('#m_ABNORMAL_EVT2_Q_hb').removeClass('up_red');
        $('#m_ABNORMAL_EVT2_Q_hb').removeClass('down_green');
        if (hb > 0) {
            $('#m_ABNORMAL_EVT2_Q_hb').addClass('up_red');
        } else if (hb < 0) {
            $('#m_ABNORMAL_EVT2_Q_hb').addClass('down_green');
        }


        // 事故征候
        var val = evtstsdat ? Number(evtstsdat.signCount) : 0;
        var val_sq = 0; //evtstsdat_sq ? Number(evtstsdat_sq.signCount) : 0;
        var change = val - val_sq;
        if (change > 0) {
            change = '+' + change;
        }
        $('#m_ABNORMAL_EVT_SIGN').text(val);
        $('#m_ABNORMAL_EVT_SIGN_hb').text(change);

        // // 一类事件
        // var val = evtstsdat ? Number(evtstsdat.seriousCount) : 0;
        // 一类事件总数等于一类事件加上岗位红线数
        var val = evtstsdat ? Number(evtstsdat.seriousCount) : 0;
        if (evtstsdat) {
            val += Number(evtstsdat.securityLineCount);
        }
        var val_sq = 0; //evtstsdat_sq ? Number(evtstsdat_sq.seriousCount) : 0;
        var change = val - val_sq;
        if (change > 0) {
            change = '+' + change;
        }
        $('#m_ABNORMAL_EVT_SERI').text(val);
        $('#m_ABNORMAL_EVT_SERI_hb').text(change);



        // 核心风险
        var html = '';
        var lst = all_company_data['coreRiskStaticList'];
        if (lst) {
            var len = lst.length;
            for (var i = 0; i < len; i++) {
                var d = lst[i]
                var real = 0;
                if (!isNaN(d.real)) {
                    real = d.real;
                }
                var barper = Number(real) * 100;
                barper = Math.min(barper, 100);
                html += "<div class='rowline con_flex_row'>"
                html += "<div class='name flex_none'>" + d.riskName + "</div>"
                html += "<div class='barbg flex_none'>"
                html += "<div class='barwrap'>"
                html += "<div class='bar' style='width:" + barper + "%'></div>"
                html += "<div class='barend'></div>"
                html += "</div>"
                html += "</div>"
                html += "<div class='vals flex_1'>"
                html += "<span class='v1'>" + real + "</span> / <span class='v2'>" + d.standard + "</span>"
                html += "</div>"
                html += "</div>"
            }
        }

        $('.core_risk_scrollpane .scrollcontent').html(html);
        // scrollbar
        $(".core_risk_scrollpane").niceScroll({
            cursorcolor: "rgba(1,75,153,0.8)",
            cursorborder: "rgba(0,0,0,0)"
        });
        $(".core_risk_scrollpane").getNiceScroll(0).resize();


        // ==================  Middle ===================

        //
        setChartCompsFltAndNormalrate();
        setChartCompsAbnormalEvents();
        setChartCompsSecurityAbnormalEvents();
        setChartCompsEstInc();

        // 
        if (eventListTabType == 'runtime') {
            showRunTimeEventList();
        } else {
            showSecurityEventList();
        }


    } // M end
    else if (date_type == 'Y') {


        // 预估收入 ------------------------------------------------------
        var ddd = all_company_data['kpi_value_d'][current_company_code]['EST_INC_FUEL']['Y'];
        var ddd_hb = all_company_data['kpi_ratio_sq_d'][current_company_code]['EST_INC_FUEL']['Y'];
        latestDate = 0;
        for (var date in ddd) {
            if (Number(date) > Number(latestDate)) {
                latestDate = date;
            }
        }
        var val = Math.round(Number(ddd[latestDate]));
        var hb = Math.round(Number(ddd_hb[latestDate]) * 10000) * 0.01;
        $('#y_EST_INC_FUEL').text(val);
        $('#y_EST_INC_FUEL_hb .v').text(trimDecimal(hb, 2) + '%');

        $('#y_EST_INC_FUEL_hb').removeClass('up');
        $('#y_EST_INC_FUEL_hb').removeClass('down');
        if (hb > 0) {
            $('#y_EST_INC_FUEL_hb').addClass('up');
        } else if (hb < 0) {
            $('#y_EST_INC_FUEL_hb').addClass('down');
        }

        audioValueData.EST_INC = val;


        // 计划航班 ------------------------------------------------------
        var sch = Number(all_company_data['flt_sts'].pftc); //计划航班总数
        var sch_sq = Number(all_company_data['flt_sts_sq'].pftc);

        //var nor_total = getKpiVal('kpi_value_d', 'NORMAL_NO_T');
        //var nor_sq = getKpiVal('kpi_value_sq_d', 'NORMAL_NO_T');

        // 环比=(本期-上期)/上期×100%
        var hb = sch_sq > 0 ? Math.round((sch - sch_sq) / sch_sq * 10000) / 100 : 0;
        $('#y_SCH_NO').text(sch);
        $('#y_SCH_NO_hb .v').text(trimDecimal(hb, 2) + '%');

        $('#y_SCH_NO_hb').removeClass('up');
        $('#y_SCH_NO_hb').removeClass('down');
        if (hb > 0) {
            $('#y_SCH_NO_hb').addClass('up');
        } else if (hb < 0) {
            $('#y_SCH_NO_hb').addClass('down');
        }

        var sch_total = sch;
        var sch_total_sq = sch_sq;

        audioValueData.FLT_TOTAL = sch_total;


        // 正常率 ------------------------------------------------------
        var normal_rate = getNormalRate('kpi_value_d');
        // sq
        var normal_rate_sq = getNormalRate('kpi_value_sq_d')
        audioValueData.NORMAL_RATE = normal_rate
        var hb = normal_rate_sq >0  ? (normal_rate - normal_rate_sq) : '-';
        $('#y_NORMAL_RATE').text(trimDecimal(normal_rate, 2) + '%');
        $('#y_NORMAL_RATE_hb .v').text(normal_rate_sq>0 ? (trimDecimal(hb, 2) + '%') : '-');

        $('#y_NORMAL_RATE_hb').removeClass('up');
        $('#y_NORMAL_RATE_hb').removeClass('down');
        if (hb > 0) {
            $('#y_NORMAL_RATE_hb').addClass('up');
        } else if (hb < 0) {
            $('#y_NORMAL_RATE_hb').addClass('down');
        }


        // 旅客人数 ------------------------------------------------------
        var psr_total = Number(all_company_data['getPassengerNum']);
        var psr_sq = Number(all_company_data['getPassengerNum_sq']);

        // 环比=(本期-上期)/上期×100%
        var hb = psr_sq > 0 ? Math.round((psr_total - psr_sq) / psr_sq * 10000) / 100 : 0;
        var psr_num = Math.round(psr_total / 1000) / 10
        $('#y_CKI_NUM').text(psr_num);
        $('#y_CKI_NUM_hb .v').text(trimDecimal(hb, 2) + '%');

        audioValueData.TRV_NUM = psr_num;

        $('#y_CKI_NUM_hb').removeClass('up');
        $('#y_CKI_NUM_hb').removeClass('down');
        if (hb > 0) {
            $('#y_CKI_NUM_hb').addClass('up');
        } else if (hb < 0) {
            $('#y_CKI_NUM_hb').addClass('down');
        }



        var eventCount = all_company_data['runTimeEventStatic'][current_company_code];
        var eventCount_sq = all_company_data['runTimeEventStatic_sq'][current_company_code];

        if (isNaN(eventCount)) {
            eventCount = 0;
        }
        if (isNaN(eventCount_sq)) {
            eventCount_sq = 0;
        }

        // 环比=(本期-上期)/上期×100%
        var hb = eventCount_sq > 0 ? Math.round((eventCount - eventCount_sq) / eventCount_sq * 1000) / 10 : 0;
        $('#y_ABNORMAL_EVT').text(eventCount);
        var per = sch_total > 0 ? Math.round(eventCount / sch_total * 1000) / 10 : 0;
        $('#y_ABNORMAL_EVT_per').text(per + '%'); // 非正常/已执行航班数量
        $('#y_ABNORMAL_EVT_hb .v').text(trimDecimal(hb, 2) + '%');

        $('#y_ABNORMAL_EVT_hb').removeClass('up');
        $('#y_ABNORMAL_EVT_hb').removeClass('down');
        if (hb > 0) {
            $('#y_ABNORMAL_EVT_hb').addClass('up');
        } else if (hb < 0) {
            $('#y_ABNORMAL_EVT_hb').addClass('down');
        }

        audioValueData.FLT_ABN = eventCount;



        // 安全不正常 ------------------------------------------------------
        /*
份 year  
事件征候万时率 accidentRate  
其中 为原因 humanReasonRate  
事件征候万时率标准 accidentStandard
为原因标准 humanReasonStandard 
一类事件次数 seriousCount  
事件征候蓝 峰值 accidentBlue  
事件征候红 峰值 accidentRed
事件征候  峰值 accidentYellow  
为原因蓝 峰值 humanReasonBlue
为原因红 峰值 humanReasonRed
为原因  峰值 humanReasonYellow
        */
        var evtstsdat = all_company_data['coreRiskSafeStatic'];

        var accidentRate = evtstsdat && !isNaN(evtstsdat.accidentRate) ? Number(evtstsdat.accidentRate) : 0;
        var accidentStandard = evtstsdat && !isNaN(evtstsdat.accidentStandard) ? Number(evtstsdat.accidentStandard) : 0;
        var seriousRate = evtstsdat && !isNaN(evtstsdat.seriousRate) ? Number(evtstsdat.seriousRate) : 0;
        var seriousStandard = evtstsdat && !isNaN(evtstsdat.seriousStandard) ? Number(evtstsdat.seriousStandard) : 0;
        var humanReasonRate = evtstsdat && !isNaN(evtstsdat.humanReasonRate) ? Number(evtstsdat.humanReasonRate) : 0;
        var humanReasonStandard = evtstsdat && !isNaN(evtstsdat.humanReasonStandard) ? Number(evtstsdat.humanReasonStandard) : 0;

        // 事故征候
        $('#y_accidentRate').text(accidentRate);
        $('#y_accidentStandard').text(accidentStandard);

        // 一类事件
        $('#y_seriousRate').text(seriousRate);
        $('#y_seriousStandard').text(seriousStandard);

        // 人为事故征候
        $('#y_humanReasonRate').text(humanReasonRate);
        $('#y_humanReasonStandard').text(humanReasonStandard);


        drawHalfCircle('cvs_beegrid1', accidentRate * 100, accidentStandard * 100);
        drawHalfCircle('cvs_beegrid2', seriousRate * 100, seriousStandard * 100);
        drawHalfCircle('cvs_beegrid3', humanReasonRate * 100, humanReasonStandard * 100);


        // 安全不正常事件惩处
        var val = 0;
        var lst = all_company_data['findEventList'];
        for (var i = lst.length - 1; i >= 0; i--) {
            var dd = lst[i];
            var isPunish = dd.isPunish;
            if (isPunish != 'false') {
                val++;
            }
        }
        $('#y_PUNISH_NUM').text(val);


        // 核心风险
        var html = '';
        var lst = all_company_data['coreRiskStaticList'];
        if (lst) {
            var len = lst.length;
            for (var i = 0; i < len; i++) {
                var d = lst[i]
                var real = 0;
                if (!isNaN(d.real)) {
                    real = d.real;
                }
                var barper = Number(real) * 100;
                barper = Math.min(barper, 100);
                html += "<div class='rowline con_flex_row'>"
                html += "<div class='name flex_none'>" + d.riskName + "</div>"
                html += "<div class='barbg flex_none'>"
                html += "<div class='barwrap'>"
                html += "<div class='bar' style='width:" + barper + "%'></div>"
                html += "<div class='barend'></div>"
                html += "</div>"
                html += "</div>"
                html += "<div class='vals flex_1'>"
                html += "<span class='v1'>" + real + "</span> / <span class='v2'>" + d.standard + "</span>"
                html += "</div>"
                html += "</div>"
            }
        }

        $('.core_risk_scrollpane .scrollcontent').html(html);
        // scrollbar
        $(".core_risk_scrollpane").niceScroll({
            cursorcolor: "rgba(1,75,153,0.8)",
            cursorborder: "rgba(0,0,0,0)"
        });
        $(".core_risk_scrollpane").getNiceScroll(0).resize();


        // ==================  Middle ===================

        //
        setChartCompsFltAndNormalrate();
        setChartCompsAbnormalEvents();
        setChartCompsSecurityAbnormalEvents();
        setChartCompsEstInc();

        // 
        if (eventListTabType == 'runtime') {
            showRunTimeEventList();
        } else {
            showSecurityEventList();
        }


    } // Y end

    setDelayCause()
    getplane()

    startLoadAudio();

}


function setDelayCause() {
    if (!all_delay_cause_data || !all_delay_cause_data['kpi_value_d'] || !all_delay_cause_data['kpi_value_sq_d']) {
        return;
    }

    var comp_total = 0;
    var comp_total_sq = 0;

    var date_type = 'D';

    var len = companylist.length;
    for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        var compcode = dat.code;
        if ((compcode != parent_company && current_company_code == parent_company || current_company_code == compcode) && all_delay_cause_data['kpi_value_d'][compcode]) {

            var data_dly = all_delay_cause_data['kpi_value_d'][compcode]['DELAY_NO'][date_type];

            // 公司原因 总数
            for (var time in data_dly) {
                var d = data_dly[time];
                var len2 = comp_cause.length;

                for (var j = 0; j < len2; j++) {
                    var causeName = comp_cause[j];
                    if (!isNaN(d[causeName])) {
                        var val = Number(d[causeName]);
                        comp_total += val;

                    }
                }

            }

        }

    }


    for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        var compcode = dat.code;
        if ((compcode != parent_company && current_company_code == parent_company || current_company_code == compcode) && all_delay_cause_data['kpi_value_sq_d'][compcode]) {

            var data_dly = all_delay_cause_data['kpi_value_sq_d'][compcode]['DELAY_NO'][date_type];

            // 公司原因 总数
            for (var time in data_dly) {
                var d = data_dly[time];
                var len2 = comp_cause.length;

                for (var j = 0; j < len2; j++) {
                    var causeName = comp_cause[j];
                    if (!isNaN(d[causeName])) {
                        var val = Number(d[causeName]);
                        comp_total_sq += val;

                    }
                }

            }

        }

    }


    // 环比=(本期-上期)/上期×100%
    var hb = comp_total_sq > 0 ? Math.round((comp_total - comp_total_sq) / comp_total_sq * 10000) / 100 : 0;

    $('#d_COMP_DELAY').text(comp_total);
    $('#d_COMP_DELAY_hb .v').text(trimDecimal(hb, 2) + '%');

    $('#d_COMP_DELAY_hb').removeClass('up_red');
    $('#d_COMP_DELAY_hb').removeClass('down_green');
    if (hb > 0) {
        $('#d_COMP_DELAY_hb').addClass('up_red');
    } else if (hb < 0) {
        $('#d_COMP_DELAY_hb').addClass('down_green');
    }
    console.log('comp_total', comp_total);
    console.log('comp_total_sq', comp_total_sq);

}


function getplane() {
    // if(date_type != 'D' && date_type != 'L'){
    //     return;
    // }
    // ------------------------------------------------------------------------
    // 运力分布 - 可用运力
    // ------------------------------------------------------------------------
    /*
    日运力相关逻辑和处理要点如下 
     
    一：日运力总数调用接口
     
            1.1 请求处理
            String acTypeFilter = "D00,D10";//需要过滤掉的机型
            ApiRequest req = new ApiRequest();
            req.setOption("acOwners", StringUtils.join(airCodeSet));
            req.setOption("acTypeNotIn", acTypeFilter);
            PageParam pageParam = new PageParam();
            pageParam.setPageIndex(1);
            pageParam.setPageSize(MyUtil.pageLimit);//2000
            req.setPageParam(pageParam);
            ApiResponse res = acTypeApi.getPdcAcReg(req);
     
       1.2 结果集处理
     
          金鹏结果集要过滤掉货舱F0的数据
           if ("Y8".equals(airCode) && "F0".equals(cabin)) {
                    continue;
                }
          获取所有的短机号：acreg
     
       有多少个短机号，就有多少架总运力；通过“acOwner”来进行航司分组，可以获取不同航司的总运力。
     
     
    二：日可用运力
       2.1 请求处理
         shortNos不依据不同的航司的机号列表作为参数（这个参数来自计算总运力的接口返回进行加工的）
     
            ApiRequest req = new ApiRequest();
            req.setOption("mntUtcStart", startUtcTime);//北京时间某日 00:00:00转换类UTC时间
            req.setOption("mntUtcEnd", endUtcTime); //北京时间某日 23:59:59转换类UTC时间
            req.setOption("acregs", shortNos);//短机号列表List<String>
            boolean isToday = this.isToday(fltDate);
            if (isToday) {
                String nowStr = DateUtil.getDefaultNowStr();
                req.setOption("nowInMntUtcRange", MyUtil.convertBJ2OhterTime(nowStr, "0000")); // 日区分为当日和非当日，当日要传这个参数，当前时间转为UTC时间
            }
            PageParam param = new PageParam();
            param.setPageIndex(1);
            param.setPageSize(MyUtil.pageLimit);//2000
            req.setPageParam(param);
            ApiResponse res = focMaintApi.getFocMaintInfoByListByPage(req);
     
    2.2    停场计算规则
     
    2.2.1当日：按实时情况查询的逻辑（实时查询当日情况：以实时为准，查询的时间点若有飞机处于计划或非计划停场，则统计计划或非计划停场架次为1；否则，为0；举例：某飞机0700-1200处于非计划停场，1000查询该飞机则统计为非计划停场架次1，1300查询则统计为非计划停场架次0。）
    2.2. 2 非当日的天：当蓝色逻辑（每日0600-2400期间，计划或非计划停场时间小于9小时，计划或非计划停场架次统计为0；9（含）-12小时统计为0.5；大于等于12小时，统计为1。可用运力=总运力-停场。）
    移动取的还是北就时间00：00：00至23：59：59，然后转为utc时间
    2.2.3  接口输出：
    2.2.3.1运力要去重：一架飞机有多个停场计划,取最晚依据结束时间(tEnd)
     
    2.2.3.2 停场类型判断：String type = (String) dataMap.get("distinctcol");//1计划 2非计划
     
    2.3 可用运力=总运力-停场运力

    */

    // ------------------------------------------------------------------------
    var len = companylist.length;
    var codelist = [];
    for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        if (dat.code != parent_company) {
            codelist.push(dat.code);
        }
    }

    var comp_codes = current_company_code == parent_company ? codelist.join(',') : current_company_code;

    // 接口用到的当日 开始 结束 时间
    var date = new Date();
    var mm = date.getMonth() + 1;
    var dd = date.getDate();
    if (mm < 10) {
        mm = '0' + mm;
    }
    if (dd < 10) {
        dd = '0' + dd;
    }

    var stdEndUtcTime = date.getFullYear() + '-' + mm + '-' + dd + ' 15:59:59';
    var yesterday_ts = date.getTime() - 86400000;
    var twodayago_ts = date.getTime() - 86400000 * 2;
    date.setTime(yesterday_ts);
    mm = date.getMonth() + 1;
    dd = date.getDate();
    if (mm < 10) {
        mm = '0' + mm;
    }
    if (dd < 10) {
        dd = '0' + dd;
    }
    var stdStartUtcTime = date.getFullYear() + '-' + mm + '-' + dd + ' 16:00:00';


    // UTC NOW
    var date = new Date();
    var mm = date.getUTCMonth() + 1;
    var dd = date.getUTCDate();
    var h = date.getUTCHours();
    var m = date.getUTCMinutes();
    var s = date.getUTCSeconds();
    if (mm < 10) {
        mm = '0' + mm;
    }
    if (dd < 10) {
        dd = '0' + dd;
    }
    if (h < 10) {
        h = '0' + h;
    }
    if (m < 10) {
        m = '0' + m;
    }
    if (s < 10) {
        s = '0' + s;
    }
    var utcTimeNow = date.getFullYear() + '-' + mm + '-' + dd + ' ' + h + ':' + m + ':' + s;
    var queryCompany = current_company_code == parent_company ? '' : current_company_code
    var url = `/bi/spring/aircraft/getAircrafCnt?company=${queryCompany}`;
    $.ajax({
        type: 'get',
        url: url,
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        success: function (response) {
            var data = response.data;
            $('#' + date_type + '_PLANE2').text(data);

        },
        error: function () { }
    });
    // 总运力
    var param = {
        "acOwners": comp_codes,
        "acTypeNotIn": 'D00,D10', //需要过滤掉的机型
    }

    $.ajax({
        type: 'post',
        url: "/bi/web/getPdcAcReg",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            var list = response.data;

            var list2 = [{
                acOwner: "HU", acType: "73B", acreg: "D81", config: "C8Y162", longReg: "BXXXX", unit: "034", version: "73BV02"
            },
            {
                acOwner: "HU", acType: "73B", acreg: "D81", config: "C8Y162", longReg: "BXXXX", unit: "034", version: "73BV02"
            },
            {
                acOwner: "HU", acType: "73B", acreg: "D81", config: "C8Y162", longReg: "BXXXX", unit: "034", version: "73BV02"
            },
            {
                acOwner: "HU", acType: "73B", acreg: "D81", config: "C8Y162", longReg: "BXXXX", unit: "034", version: "73BV02"
            }, {
                acOwner: "HU", acType: "73B", acreg: "D81", config: "C8Y162", longReg: "BXXXX", unit: "034", version: "73BV02"
            },
            {
                acOwner: "HU", acType: "73B", acreg: "D81", config: "C8Y162", longReg: "BXXXX", unit: "034", version: "73BV02"
            },
            {
                acOwner: "HU", acType: "73B", acreg: "D81", config: "C8Y162", longReg: "BXXXX", unit: "034", version: "73BV02"
            },
            {
                acOwner: "HU", acType: "73B", acreg: "D81", config: "C8Y162", longReg: "BXXXX", unit: "034", version: "73BV02"
            },
            {
                acOwner: "JD", acType: "73B", acreg: "D81", config: "C8Y162", longReg: "BXXXX", unit: "034", version: "73BV02"
            },
            {
                acOwner: "JD", acType: "73B", acreg: "D81", config: "C8Y162", longReg: "BXXXX", unit: "034", version: "73BV02"
            },
            {
                acOwner: "JD", acType: "73B", acreg: "D81", config: "C8Y162", longReg: "BXXXX", unit: "034", version: "73BV02"
            },
            {
                acOwner: "JD", acType: "73B", acreg: "D81", config: "C8Y162", longReg: "BXXXX", unit: "034", version: "73BV02"
            }, {
                acOwner: "JD", acType: "73B", acreg: "D81", config: "C8Y162", longReg: "BXXXX", unit: "034", version: "73BV02"
            },
            {
                acOwner: "JD", acType: "73B", acreg: "D81", config: "C8Y162", longReg: "BXXXX", unit: "034", version: "73BV02"
            },
            {
                acOwner: "JD", acType: "73B", acreg: "D81", config: "C8Y162", longReg: "BXXXX", unit: "034", version: "73BV02"
            },
            {
                acOwner: "GS", acType: "73B", acreg: "D81", config: "C8Y162", longReg: "BXXXX", unit: "034", version: "73BV02"
            },
            {
                acOwner: "GS", acType: "73B", acreg: "D81", config: "C8Y162", longReg: "BXXXX", unit: "034", version: "73BV02"
            },
            {
                acOwner: "GS", acType: "73B", acreg: "D81", config: "C8Y162", longReg: "BXXXX", unit: "034", version: "73BV02"
            },
            {
                acOwner: "GS", acType: "73B", acreg: "D81", config: "C8Y162", longReg: "BXXXX", unit: "034", version: "73BV02"
            }, {
                acOwner: "GS", acType: "73B", acreg: "D81", config: "C8Y162", longReg: "BXXXX", unit: "034", version: "73BV02"
            },
            {
                acOwner: "GS", acType: "73B", acreg: "D81", config: "C8Y162", longReg: "BXXXX", unit: "034", version: "73BV02"
            },
            {
                acOwner: "GS", acType: "73B", acreg: "D81", config: "C8Y162", longReg: "BXXXX", unit: "034", version: "73BV02"
            }
            ];

            if (current_company_code == parent_company) {
                list = list.concat(list2);
            } else {
                var newArr = list2.filter(item => item.acOwner == current_company_code);
                list = list.concat(newArr);
            }
            var acregs = [];
            var acTypes = [];
            var reg2actyp = {};
            for (var i = list.length - 1; i >= 0; i--) {
                var obj = list[i];
                // if (obj.acOwner == 'Y8' && ['B5057', 'B2826', 'B2820', 'B1339'].indexOf(obj.longReg) > -1) { //金鹏结果集暂不过滤掉货舱F0的数据，只去除特定的机号
                //     continue;
                // }

                acregs.push(obj.acreg);
                if (acTypes.indexOf(obj.acType) == -1) {
                    acTypes.push(obj.acType);
                }
                reg2actyp[obj.acreg] = obj.acType;
            }

            if (date_type != 'D') {
                var param = {
                    "startDate": startBjTime,
                    "endDate": endBjTime,
                    "company": comp_codes
                }

                $.ajax({
                    type: 'post',
                    url: "/bi/web/getCapacityDay",
                    contentType: 'application/json',
                    dataType: 'json',
                    async: true,
                    data: JSON.stringify(param),
                    success: function (response) {
                        var canUseCap = response.canUseCap;
                        var end = canUseCap - parseInt(canUseCap);
                        if (end < 0.5) {
                            canUseCap = Math.round(canUseCap);
                        } else {
                            canUseCap = Math.round(canUseCap) - 0.5;
                        }
                        $('#' + date_type + '_PLANE1').text(canUseCap);
                    },
                    error: function () { }
                });

            } else {
                // 获取机型转换表
                var param = {
                    "actypecode": acTypes.join(','),
                }

                $.ajax({
                    type: 'post',
                    url: "/bi/web/actypquery",
                    contentType: 'application/json',
                    dataType: 'json',
                    async: true,
                    data: JSON.stringify(param),
                    success: function (response) {
                        actypeMapList = response;

                        ac_data_list = {};
                        for (ac in actypeMapList) {
                            var accode = actypeMapList[ac];
                            if (ac_type_list.indexOf(accode) > -1) {
                                if (ac_data_list[accode] == undefined) {
                                    ac_data_list[accode] = 0;
                                }
                            }
                        }

                        for (var reg in reg2actyp) {
                            var ac = reg2actyp[reg];
                            var accode = actypeMapList[ac];
                            if (accode) {
                                ac_data_list[accode]++;
                            } else {
                                console.log(ac, '没有大小机型对照数据');
                            }
                        }

                        // 停场运力
                        var param = {
                            "mntUtcStart": stdStartUtcTime,
                            "mntUtcEnd": stdEndUtcTime,
                            "nowInMntUtcRange": utcTimeNow,
                            "acregs": acregs.join(','),
                        }

                        $.ajax({
                            type: 'post',
                            url: "/bi/web/getFocMaintInfoByListByPage",
                            contentType: 'application/json',
                            dataType: 'json',
                            async: true,
                            data: JSON.stringify(param),
                            success: function (response) {
                                var list2 = response.data;
                                var acregs2 = [];
                                var aog_planes = [];
                                for (var i = list2.length - 1; i >= 0; i--) {
                                    var obj = list2[i];

                                    var time1 = parserDate(obj.tStart).getTime() + 8 * 3600 * 1000;
                                    var time2 = parserDate(obj.tEnd).getTime() + 8 * 3600 * 1000;
                                    var date = new Date();
                                    var timenow = date.getTime();

                                    if (acregs2.indexOf(obj.acreg) == -1 && timenow > time1 && timenow < time2 && (obj.distinctcol == 1 || obj.distinctcol == 2)) {
                                        acregs2.push(obj.acreg);
                                        // 对应大机型数量减去维护中的数量
                                        var actyp = reg2actyp[obj.acreg];
                                        var accode = actypeMapList[actyp];
                                        ac_data_list[accode]--;
                                    }

                                    //
                                    //distinctcol=2 为非计划停场
                                    //今日非计划停场飞机
                                    //飞机号
                                    //机型
                                    //停场结束时间tEnd=预计恢复时间
                                    //剩余=预计恢复时间-目前时间
                                    if (obj.distinctcol == 2 && timenow > time1 && timenow < time2) {
                                        aog_planes.push(obj);
                                    }
                                }

                                ac_data_list_ready = true;
                                // acregs2
                                // 可用运力=总运力-停场运力                                
                                var available_ac = acregs.length - acregs2.length;
                                $('#' + date_type + '_PLANE1').text(available_ac);

                                /*
                                function setOnGround(){
                                  if(exe_total_plane == -1){
                                    setTimeout(setOnGround, 0);
                                    return;
                                  }
                                  $('#plane_on_ground').text(available_ac-exe_total_plane);
                                  var per = (available_ac-exe_total_plane)/available_ac * 100;
                                  $('#bar_plane_on_ground').css('width', per+'%');
                                }
    
                                setOnGround();
                                */

                                // AOG 飞机列表


                                // aog end

                            },
                            error: function () { }
                        });
                        // end

                    },
                    error: function () { }
                });
            }
            //end
        },
        error: function () { }
    });
}

var audioValueData = {};

function startLoadAudio() {

    stopAudio();

    // 语音播报

    /* 
    A3.6-general
    
    年度累计修正吨公里耗油 {Y_MOD_FUELT}，
    年度累计吨公里耗油 {Y_FUELT}，

    */

    var tpl = '';
    tpl += '{COMP} {DATE} 航班总量{FLT_TOTAL}班次，正常率{NORMAL_RATE}，旅客人数{TRV_NUM}万人，预估收入{EST_INC}元。';
    if (date_type == 'D' || date_type == 'L') {
        tpl += '取消{FLT_CANCEL}班次，返航备降{FLT_FH}班次，延误大于4小时{FLT_4H}班次，不正常事件量{FLT_ABN}班次。';
        tpl += '安全不正常事件{EVT_TOTAL}起，环比{EVT_TOTAL_HB}起，人为事件{EVT_T2}起，意外事件{EVT_T1}起，机械事件{EVT_T3}起，其它事件{EVT_T4}起。'; //['意外', '人为', '机械', '其他']
    } else if (date_type == 'M') {
        tpl += '取消{FLT_CANCEL}班次，返航备降{FLT_FH}班次，延误大于4小时{FLT_4H}班次，不正常事件量{FLT_ABN}班次。';
        tpl += '安全不正常事件{EVT_TOTAL}起，环比{EVT_TOTAL_HB}起';
    } else if (date_type == 'Y') {

    }

    //--
    var compname = companyCode2Name[current_company_code];
    tpl = tpl.replace(/{COMP}/g, compname);

    //--
    var datelabel = getCurrentDateLabel();
    tpl = tpl.replace(/{DATE}/g, datelabel);

    //--
    tpl = tpl.replace(/{FLT_TOTAL}/g, formatCurrency(audioValueData.FLT_TOTAL, 0));
    tpl = tpl.replace(/{NORMAL_RATE}/g, audioValueData.NORMAL_RATE + '%');
    tpl = tpl.replace(/{TRV_NUM}/g, audioValueData.TRV_NUM);
    tpl = tpl.replace(/{EST_INC}/g, formatCurrency(audioValueData.EST_INC * 10000, 0));
    tpl = tpl.replace(/{FLT_CANCEL}/g, audioValueData.FLT_CANCEL);
    tpl = tpl.replace(/{FLT_FH}/g, audioValueData.FLT_FH);
    tpl = tpl.replace(/{FLT_4H}/g, audioValueData.FLT_4H);
    tpl = tpl.replace(/{FLT_ABN}/g, audioValueData.FLT_ABN);
    tpl = tpl.replace(/{EVT_TOTAL}/g, audioValueData.EVT_TOTAL);
    tpl = tpl.replace(/{EVT_TOTAL_HB}/g, audioValueData.EVT_TOTAL_HB);
    tpl = tpl.replace(/{EVT_T1}/g, audioValueData.EVT_T1);
    tpl = tpl.replace(/{EVT_T2}/g, audioValueData.EVT_T2);
    tpl = tpl.replace(/{EVT_T3}/g, audioValueData.EVT_T3);
    tpl = tpl.replace(/{EVT_T4}/g, audioValueData.EVT_T4);


    text2audio(tpl, true);


}



function showRunTimeEventList() {
    var list = all_company_data['runTimeEventList'];


    var fltListByComp = {};
    var len = companylist.length;
    for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        if (dat.code != parent_company) {
            fltListByComp[dat.yhscode] = {
                list: [],
                code: dat.code,
                name: dat.name,
                sort: dat.sort
            };
        }
    }

    var legendNameList = [];

    var prolist = all_company_data['runTimePropertiesStatic'];
    prolist.sort(function (a, b) {
        return a.code - b.code
    });

    for (var i in prolist) {
        var d = prolist[i];
        legendNameList.push(d.name);
    }

    var eventKVList = {};
    if (list && list.length > 0) {
        for (var i = list.length - 1; i >= 0; i--) {
            var dd = list[i];
            eventKVList[dd.id] = dd;
            if (fltListByComp[dd.companyCode]) {

                if (dd.eventAttrName.indexOf(runTimeEventListNameFilter) == -1 && runTimeEventListNameFilter != '') {
                    continue;
                }

                fltListByComp[dd.companyCode].list.push(dd);
            }
        }
    }


    var eventList = [];
    for (var yshcode in fltListByComp) {
        var dd = fltListByComp[yshcode];
        eventList.push(dd);
    }

    eventList.sort(function (a, b) {
        return a.sort - b.sort
    });


    /*
    acType: "A320"
    airlineCn: "济南-三亚"
    airlineEn: "TNA-SYX"
    arrstn: "SYX"
    arrstnName: "三亚"
    comments: "null"
    companyCode: "CHB"
    companyName: "西部航空"
    createdBy: "zh_bai"
    createdByName: "白周浩"
    createdTime: "1516872201000"
    deleted: "false"
    depstn: "TNA"
    depstnName: "济南"
    deptId: ",4,"
    deptName: "维修工程部"
    description: "1月24日，PN6364航班，B6765飞机，执行济南-三亚，计划起飞时间0925，前段航班0806到位，过站机组检查飞机时发现3号主轮有扎上，明显看到有钉子扎入，通知海技重庆MCC，评估需要换轮，0917海技通报暂找不到航材和维修人员，通知机组退场休息，航班延误时间待定，0926西部航材管理询问到东航济南航材库中有A320的轮胎，1200确定轮子可以从东航借，但是确定不了如何领取及搬运，1240轮胎运到飞机下面，开始更换，1342飞机放行，航班1419关舱。最终起飞延误4小时48分。"
    entryTime: "1516872000000"
    eventAttr: ",4,"
    eventAttrName: "工程机务"
    eventType: "2"
    eventTypeName: "公司程序"
    flightDate: "1516723200000"
    flightDateStr: "2018-01-24"
    flightNo: "PN6364"
    id: "21"
    isPunish: "false"
    isRead: "null"
    lastAppUpdatedTime: "null"
    longReg: "B6765"
    noticeTimeStr: "2018-01-25 17:20:00"
    processResult: "1 将类似事件一一记录，向公司反映，评估是否需要取消无过站放行，或者取消保障能力较差的航站的无过站放行政策；↵   2 工程部已经制定领导走访方案，重点针对南京、济南保障问题进行现场调研。↵   3 建议集团统一协调，提高临时需要维修机务人员支援的工作实效。"
    punishAds: "null"
    punishInfo: "null"
    punishNames: "null"
    punishNos: "null"
    signerName: "汪建劳"
    signerNo: "1000102886"
    sta: "1748"
    status: "3"
    std: "1417"
    surveyResult: "1 公司运行无过站放行，在济南机务只有一般勤务协议，飞机出现故障需要维修或需要机务放行时，均需要临时协调当地机务单位，协调耗时较长，还要看外单位是否支持。↵   2 在公司没有取消无过站放行政策以前暂改变不了现状。"
    title: "1月24日PN6364(济南-三亚）航班延误事件"
    updatedBy: "zh_bai"
    updatedByName: "白周浩"
    updatedTime: "1516872201000"
    */



    var html = '';
    var len = eventList.length
    for (var i = 0; i < len; i++) {
        var dd = eventList[i];
        var len2 = dd.list.length;
        if (len2 > 0) {
            html += "<div class='comprow' id='comprow_" + i + "' >";
            // HEAD
            html += "<div class='head'>";
            html += "<div class='tt' style='background-image: url(img/logo_" + dd.code + ".png)'><span class='lb'>" + dd.name + "</span><span class='num'>(" + len2 + "班次)</span></div>";
            if (len2 > 5) {
                html += "<div class='btns'><span class='btn btn_prev disabled' data-row='" + i + "' data-len='" + len2 + "' data-page='0' ></span><span class='btn btn_next' data-row='" + i + "' data-len='" + len2 + "' data-page='0' ></span></div>";
            }
            html += "</div><!-- /head -->";
            // LIST
            html += "<div class='itmlst'>";
            for (var j = 0; j < len2; j++) {
                var evt = dd.list[j];

                var date = evt.flightDateStr;
                var darr = date.split('-');
                date = darr[1] + '/' + darr[2];
                var color = legendColorList1[legendNameList.indexOf(evt.eventAttrName)];

                html += "<div class='blk blk_" + j + "' id='event_id_" + evt.id + "' data-id='" + evt.id + "' data-fltno='" + evt.flightNo + "' data-code='" + dd.code + "' data-date='" + evt.flightDateStr + "' data-depstn='" + evt.depstn + "' >";
                html += "<div class='time'>";
                html += "<span class='l'>" + date + "</span>";
                html += "<span class='r hour'></span>";
                html += "</div>";
                html += "<div class='fltno'>" + evt.flightNo + "</div>";
                var style = '';
                evt.airlineCn = evt.airlineCn.replace(/\//g, '');
                if (evt.airlineCn.length > 7) {
                    style = 'position: absolute; line-height:13px; bottom:23px;';
                }
                html += "<div class='city' style='" + style + "' >" + evt.airlineCn + "</div>";
                html += "<div class='bot'><span class='dot' style='color:" + color + "; '>●</span><span class='lb'>" + (evt.eventAttrName == null ? '-' : evt.eventAttrName) + "</span></div>";
                html += "</div><!-- /blk -->";

                if (j < 5) {
                    fetchFltInfo(dd.code, evt.flightDateStr + ' 00:00:00', evt.flightDateStr + ' 23:59:59', [evt.flightNo], evt.id, evt.depstn);
                }

            }
            html += "</div><!-- /itmlst -->";

            // END
            html += "</div><!-- /comprow -->";
        }
    }

    $('.block_r .scrollcontent').html('');
    $('.block_r .tabc1 .scrollcontent').html(html);

    var html = '';
    var len = legendNameList.length;
    for (var i = 0; i < len; i++) {
        var name = legendNameList[i];
        var color;
        var opacity;
        if (runTimeEventListNameFilter == name || runTimeEventListNameFilter == '') {
            color = legendColorList1[i];
            opacity = 1;
        } else {
            color = '#5d9ae3';
            opacity = 0.3;
        }

        html += "<span class='itm' data-name='" + name + "' style='opacity:" + opacity + "' ><span class='dot' style='color:" + color + "; '>●</span><span class='lb'>" + (name == null ? '-' : name) + "</span></span>";
    }
    $('.block_r .tabc1 .legend .lst').html(html);

    $('.block_r .tabc1 .legend .lst .itm').off('click');
    $('.block_r .tabc1 .legend .lst .itm').on('click', function (evt) {
        if (runTimeEventListNameFilter == '') {
            runTimeEventListNameFilter = $(this).attr('data-name');
        } else {
            runTimeEventListNameFilter = ''
        }

        showRunTimeEventList()
    })


    // scrollbar
    $(".block_r .tabc1 .scrollpane").niceScroll({
        cursorcolor: "rgba(1,75,153,0.8)",
        cursorborder: "rgba(0,0,0,0)"
    });
    $(".block_r .tabc1 .scrollpane").getNiceScroll(0).resize();

    //
    $('.block_r .tabc1 .itmlst .blk').off('click');
    $('.block_r .tabc1 .itmlst .blk').on('click', function (evt) {
        evt.stopPropagation();
        var id = $(this).attr('data-id');
        var code = $(this).attr('data-code');

        showPop($(this), id, code);
    });

    //


    $('.block_r .btn_prev').off('click');
    $('.block_r .btn_prev').on('click', function (evt) {
        evt.stopPropagation();
        if ($(this).hasClass('disabled')) {
            return;
        }
        var id = $(this).attr('data-row');
        var len = $(this).attr('data-len');
        var page = $(this).attr('data-page');
        var perpage = 5;

        page = Number(page) - 1;
        $('#comprow_' + id + ' .btn_next').attr('data-page', page);
        $('#comprow_' + id + ' .btn_prev').attr('data-page', page);

        $('#comprow_' + id + ' .btn_next').removeClass('disabled');
        if (page == 0) {
            $('#comprow_' + id + ' .btn_prev').addClass('disabled');
        }

        for (var i = page * perpage; i < (page + 1) * perpage; i++) {
            var blk = $('#comprow_' + id + ' .blk_' + i);
            var loaded = blk.attr('data-flt-loaded');
            if (isNaN(loaded)) {
                var data_code = blk.attr('data-code');
                var data_id = blk.attr('data-id');
                var data_date = blk.attr('data-date');
                var data_fltno = blk.attr('data-fltno');
                var data_depstn = blk.attr('data-depstn');
                fetchFltInfo(data_code, data_date + ' 00:00:00', data_date + ' 23:59:59', [data_fltno], data_id, data_depstn);
            }
        }

        var width = 405;

        $('#comprow_' + id + ' .itmlst').animate({
            left: -(width * page)
        }, {
            duration: 300
        });
    });


    $('.block_r .btn_next').off('click');
    $('.block_r .btn_next').on('click', function (evt) {
        evt.stopPropagation();
        if ($(this).hasClass('disabled')) {
            return;
        }
        var id = $(this).attr('data-row');
        var len = $(this).attr('data-len');
        var page = $(this).attr('data-page');
        var perpage = 5;

        page = Number(page) + 1;
        $('#comprow_' + id + ' .btn_next').attr('data-page', page);
        $('#comprow_' + id + ' .btn_prev').attr('data-page', page);

        $('#comprow_' + id + ' .btn_prev').removeClass('disabled');
        if (len <= (page + 1) * perpage) {
            $('#comprow_' + id + ' .btn_next').addClass('disabled');
        }

        for (var i = page * perpage; i < (page + 1) * perpage; i++) {
            var blk = $('#comprow_' + id + ' .blk_' + i);
            var loaded = blk.attr('data-flt-loaded');
            if (isNaN(loaded)) {
                var data_code = blk.attr('data-code');
                var data_id = blk.attr('data-id');
                var data_date = blk.attr('data-date');
                var data_fltno = blk.attr('data-fltno');
                var data_depstn = blk.attr('data-depstn');
                fetchFltInfo(data_code, data_date + ' 00:00:00', data_date + ' 23:59:59', [data_fltno], data_id, data_depstn);
            }
        }

        var width = 405;

        $('#comprow_' + id + ' .itmlst').animate({
            left: -(width * page)
        }, {
            duration: 300
        });
    });



    // 所有关注/我的关注
    /*
    var html = '';
    var evtlst = all_company_data['careEventStatic_yx'].runtimeEventList;
    if(evtlst){
        var len = evtlst.length;
        for(var i=0; i<len; i++){
            var evt = evtlst[i];
            var color = legendColorList1[legendNameList.indexOf(evt.eventAttrName)];
            html += "<span class='itm '><span class='dot' style='color:"+color+"'>●</span><span class='lb'>"+evt.flightNo+"</span></span>"
        }
    }
    $('.allfavlst .cont').html(html);
    $('.allfavlst .cont').css('left', 0);

    var evtlst_page = 0;
    var evtlst_total_page = Math.ceil(evtlst.length/3);

    if(evtlst_total_page > 1){
        $('#btn_allfav_L_t1').css('opacity', 0.3);
        $('#btn_allfav_R_t1').css('opacity', 1);

        $('#btn_allfav_L_t1').show();
        $('#btn_allfav_R_t1').show();

    }else{
        $('#btn_allfav_L_t1').hide();
        $('#btn_allfav_R_t1').hide();
    }

    $('#btn_allfav_L_t1').off('click');
    $('#btn_allfav_L_t1').on('click', function(evt){
        evt.stopPropagation();

        if(evtlst_page > 0){
            evtlst_page--;
        }

        $('#btn_allfav_R_t1').css('opacity', 1);
        if(evtlst_page == 0){
            $('#btn_allfav_L_t1').css('opacity', 0.3);
        }
        
        var width = 220;
        $('.allfavlst .cont').animate({
            left:-(width*evtlst_page)
        }, {
            duration:300
        });
    });

    $('#btn_allfav_R_t1').off('click');
    $('#btn_allfav_R_t1').on('click', function(evt){
        evt.stopPropagation();

        if(evtlst_page < evtlst_total_page-1){
            evtlst_page++;
        }

        $('#btn_allfav_L_t1').css('opacity', 1);
        if(evtlst_page == evtlst_total_page-1){
            $('#btn_allfav_R_t1').css('opacity', 0.3);
        }
        
        var width = 220;
        $('.allfavlst .cont').animate({
            left:-(width*evtlst_page)
        }, {
            duration:300
        });
        
    });
    */


    $('#pop_tab1 .btnx').off('click');
    $('#pop_tab1 .btnx').on('click', function (evt) {
        evt.stopPropagation();
        $('#pop_tab1').hide();
    });


    function showPop(elem, id, code) {
        $('#pop_tab2').hide();

        var evt = eventKVList[id];

        var fltinfo = flightInfoList[id];

        var name = evt.eventAttrName;
        var color = legendColorList1[legendNameList.indexOf(evt.eventAttrName)];
        var date = evt.flightDateStr;
        var darr = date.split('-');
        date = darr[1] + '/' + darr[2];


        $('#pop_tab1 .head .fltno').html(evt.companyName + ' ' + evt.flightNo);
        $('#pop_tab1 .head .fltno').css('background-image', 'url(img/logo_' + code + '.png)');
        //

        $('#pop_tab1 .row1 .date').html(date);
        $('#pop_tab1 .row1 .ac').html(evt.acType);
        $('#pop_tab1 .row1 .cabin').html('');


        $('#pop_tab1 .city1 .nm').html(evt.depstnName);
        $('#pop_tab1 .city2 .nm').html(evt.arrstnName);

        if (fltinfo) {
            // 时间
            $('#pop_tab1 .city1 .tm').html(getHMtime(fltinfo.stdChn)); //计划出发
            $('#pop_tab1 .city2 .tm').html(getHMtime(fltinfo.staChn)); //计划到达

            $('#pop_tab1 .city1 .tm2').html(getHMtime(fltinfo.atdChn)); //实际出发
            $('#pop_tab1 .city2 .tm2').html(getHMtime(fltinfo.ataChn)); //实际到达
        }


        var noticeTime = evt.noticeTimeStr;
        var narr = noticeTime.split('-');
        noticeTime = narr[1] + '/' + narr[2];
        noticeTime = noticeTime.substring(0, noticeTime.length - 3);

        // $('#pop_tab1 .row3 .reason').html(evt.eventAttrName);
        $('#pop_tab1 .row3 .bzcsx').html(evt.eventAttrName);
        $('#pop_tab1 .row3 .sjlx').html(evt.eventTypeName);

        $('#pop_tab1 .adjust .fltno').html(evt.flightNo);
        $('#pop_tab1 .adjust .fromto').html(evt.airlineCn);
        $('#pop_tab1 .adjust .date').html(date);
        // $('#pop_tab1 .adjust .tag').html(name);
        // $('#pop_tab1 .adjust .tag').css('background-color', color);

        $('#pop_tab1 .reason_tz').html(evt.description);
        $('#pop_tab1 .time_fb').html(noticeTime);
        $('#pop_tab1 .people_fb').html(evt.createdByName);

        $('#pop_tab1 .reason_yw').html(evt.eventAttrName);

        $('#pop_tab1 .process .result').html(evt.processResult);
        $('#pop_tab1 .process .line2').html(noticeTime + '  发布人: ' + evt.createdByName);

        $('#pop_tab1 .surveyResult').html(evt.surveyResult);

        $('#pop_tab1 .punishInfo').html(evt.isPunish != 'false' ? evt.punishInfo : '无');

        $('#pop_tab1 .comments').html(evt.comments != 'null' ? evt.comments : '无');


        getCabin(evt.longReg, '#pop_tab1 .row1 .cabin');


        var param = {
            "id": id,
        }
        $.ajax({
            type: 'post',
            url: "/bi/web/runTimeEventDetail",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {
                var comments = response.result.comments;
                var lst = [];
                for (var i = 0; i < comments.length; i++) {
                    var cmt = comments[i];
                    if (cmt.content != '' && cmt.commentByName != '') {
                        lst.push(cmt);
                    }
                }
                var statusMap = {
                    "1": "调查分析中",
                    "2": "落实措施中",
                    "3": "已关闭",
                    "4": "待定"
                };
                var text = "";
                if (statusMap[response.result.status]) {
                    text = statusMap[response.result.status];
                }
                $('#pop_tab1 .statustext').text(text);
                var html = '';
                if (lst.length > 0) {
                    for (var i = 0; i < lst.length; i++) {
                        var cmt = lst[i];

                        html += "<div class='blktit'>批示意见</div>";
                        html += "<div class='blkcon'>";
                        html += "<div class='blkrow'>";
                        html += "<span class='comments'>" + cmt.content + "</span><br>";
                        html += "<span class='title'>批示人及批示时间</span><br>";
                        html += "<span class=''>" + cmt.commentByName + ' ' + cmt.commentTimeStr + "</span>";
                        html += "</div>";
                        html += "</div>";

                        /*
                        if(cmt.replys.length > 0){
                            for(var j=0; j<cmt.replys.length; j++){
                                var rpy = cmt.replys[j];
                                html += "<div class='blktit'>回复意见</div>";
                                html += "<div class='blkcon'>";
                                html += "<div class='blkrow'>";
                                html += "<span class=''>"+rpy.content+"</span><br>";
                                html += "<span class='title'>回复人及回复时间</span><br>";
                                html += "<span class=''>"+rpy.replyByName+' '+rpy.replyTimeStr+"</span>";
                                html += "</div>";
                                html += "</div>";
                            }
                        }else{
                            html += "<div class='blktit'>回复意见</div>";
                            html += "<div class='blkcon'>";
                            html += "<div class='blkrow'>";
                            html += "<span class=''>无</span><br>";
                            html += "<span class='title'>回复人及回复时间</span><br>";
                            html += "<span class=''>无</span>";
                            html += "</div>";
                            html += "</div>";
                        }
                        */
                    }



                } else {
                    html += "<div class='blktit'>批示意见</div>";
                    html += "<div class='blkcon'>";
                    html += "<div class='blkrow'>";
                    html += "<span class='comments'>无</span><br>";
                    html += "<span class='title'>批示人及批示时间</span><br>";
                    html += "<span class=''>无</span>";
                    html += "</div>";
                    html += "</div>";

                    /*
                    html += "<div class='blktit'>回复意见</div>";
                    html += "<div class='blkcon'>";
                    html += "<div class='blkrow'>";
                    html += "<span class=''>无</span><br>";
                    html += "<span class='title'>回复人及回复时间</span><br>";
                    html += "<span class=''>无</span>";
                    html += "</div>";
                    html += "</div>";
                    */
                }

                $('#pop_tab1 .commentlist').html(html);

                $("#pop_tab1 .scrollpane").getNiceScroll(0).resize();
            },
            error: function () { }
        });



        /*
        var param = {
            "eventId": id,
        }

        $.ajax({           
            type: 'post',
            url:"/bi/web/runTimeEventComment",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function(response) {
                

            },
            error:function() {
            }
        });
        */


        setXY();

        function setXY() {
            var bx = elem.offset().left;
            var bt = elem.offset().top;

            var left = bx - $('#pop_tab1').width() - 10;
            var arrtop = bt - ($('.page-wrapper').height() - $('#pop_tab1').height() - 20) + elem.height() / 2;

            $('#pop_tab1').css('left', left);
            $('#pop_tab1').css('bottom', 20);
            $('#pop_tab1 .arr').css('top', arrtop);

            $('#pop_tab1').show();


            // scrollbar
            $("#pop_tab1 .scrollpane").niceScroll({
                cursorcolor: "rgba(4,18,62,0.3)",
                cursorborder: "rgba(0,0,0,0)"
            });
            $("#pop_tab1 .scrollpane").getNiceScroll(0).resize();
            $("#pop_tab1 .scrollpane").getNiceScroll(0).doScrollTop(0, 0);
        }

    }

} //showRunTimeEventList

function showSecurityEventList() {
    var list = all_company_data['findEventList'];

    var fltListByComp = {};
    var len = companylist.length;
    for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        if (dat.code != parent_company) {
            fltListByComp[dat.yhscode] = {
                list: [],
                code: dat.code,
                name: dat.name,
                sort: dat.sort
            };
        }
    }


    var eventKVList = {};
    for (var i = list.length - 1; i >= 0; i--) {
        var dd = list[i];
        eventKVList[dd.id] = dd;
        if (dd.eventReason != eventListReasonFilter && eventListReasonFilter != '') {
            continue;
        }

        var level = dd.eventLevel;
        if (level == 5) { // 岗位红线归类到严重错误上
            level = 2;
        }
        if (level != eventListLevelFilter && eventListLevelFilter != '') {
            continue;
        }

        if (isPunishFilter == 1 && dd.isPunish == 'false') {
            continue;
        }

        if (fltListByComp[dd.companyCode]) {
            fltListByComp[dd.companyCode].list.push(dd);
        }
    }

    var eventList = [];
    for (var yshcode in fltListByComp) {
        var dd = fltListByComp[yshcode];
        eventList.push(dd);
    }

    eventList.sort(function (a, b) {
        return a.sort - b.sort
    });
  console.log('eventList----------',fltListByComp,eventList)


    /*
    acType: "E190"
    airlineCn: "临沂-长沙"
    airlineEn: "LYI-HHA"
    arrstn: "HHA"
    arrstnName: "长沙"
    comments: "null"
    companyCode: "CBG"
    companyName: "北部湾航空"
    createdBy: "xt.zhu"
    createdByName: "朱晓彤"
    createdTime: "1515933709000"
    deal: "QAR分析情况：EGPWS警告发生前，飞机状态好，着陆形态建立，速度设置与SOP线相符合，航向道和下滑道无任何偏差；EGPWS警告发生总共21秒，直到接地前7英尺消失，期间下降率正常；警告发生后，飞机始终高于下滑道，未有发生低于下滑道的情况。↵工程分析情况：经检查排故，无机械故障情况；经与厂家咨询，可能是外部干扰导致GPS信号丢失。"
    deleted: "false"
    depstn: "LYI"
    depstnName: "临沂"
    description: "2018年1月14日，北部湾航空E190/B3179飞机执行GX8976（临沂-长沙）航班，长沙进近过程中由于GPS信号丢失触发地形警告，经了解，大致从200尺（决断高度）开始响起terrain警告，期间断断续续响了5、6声，机组报告跑道完全能见，预判为假警告，故继续进近，最终于16:58正常落地长沙。"
    eventLevel: "1" // ['一般事件', '一类事件', '事故征候', '待定'];
    eventReason: "4" //['意外', '人为', '机械', '其他'];
    eventType: "可控飞行撞地/障碍物"
    flightDate: "1515859200000"
    flightDateStr: "2018-01-14"
    flightNo: "GX8976"
    id: "1117"
    isPunish: "false"
    isRead: "null"
    lastAppUpdatedTime: "null"
    levelDetail: "一般事件"
    longReg: "B3179"
    noticeTime: "1515933420000"
    noticeTimeStr: "2018-01-14 20:37:00"
    precaution: "无。"
    punishAds: "null"
    punishInfo: "null"
    punishNames: "null"
    sta: "1658"
    status: "3" //状态跟踪 1:调查分析中、2:落实措施中、3:已 关闭;)
    std: "1517"
    title: "2018年1月14日北部湾航空E190/B3179飞机执行GX8976（临沂-长沙）航班长沙近进触发地形假警告"
    updatedBy: "xt.zhu"
    updatedByName: "朱晓彤"
    updatedTime: "1516064520000"
    */
    // 事件原因: 1:意外; 2:人为; 3:机械; 4: 其他
    var legendNameList = ['意外', '人为', '机械', '其他'];
    var legendNameList2 = ['安全报告事件', '一类事件', '事故征候', '岗位红线', '待定'];
    var legendName2Key = {
        '安全报告事件': 8,
        '一类事件': 2,
        '事故征候': 3,
        '岗位红线': 5,
        '待定': 3,
    };
    var legendKey2Name = {
        8: '安全报告事件',
        2: '一类事件',
        3: '事故征候',
        5: '岗位红线',
        3: '待定',
    };

    var statusList = ['调查分析中', '落实措施中', '已关闭'];

    var html = '';
    var len = eventList.length
    for (var i = 0; i < len; i++) {
        var dd = eventList[i];
        var len2 = dd.list.length;
        if (len2 > 0) {
            html += "<div class='comprow' id='comprow_" + i + "' >";
            // HEAD
            html += "<div class='head'>";
            html += "<div class='tt' style='background-image: url(img/logo_" + dd.code + ".png)'><span class='lb'>" + dd.name + "</span><span class='num'>(" + len2 + "班次)</span></div>";
            if (len2 > 5) {
                html += "<div class='btns'><span class='btn btn_prev disabled' data-row='" + i + "' data-len='" + len2 + "' data-page='0' ></span><span class='btn btn_next' data-row='" + i + "' data-len='" + len2 + "' data-page='0' ></span></div>";
            }
            html += "</div><!-- /head -->";
            // LIST
            html += "<div class='itmlst'>";
            for (var j = 0; j < len2; j++) {
                var evt = dd.list[j];

                var date = evt.flightDateStr;
                var darr = date.split('-');
                date = darr[1] + '/' + darr[2];
                // var color = legendColorList1[Number(evt.eventReason) - 1];
                // var color2 = legendColorList2[Number(evt.eventLevel) - 1];

                var color = legendColorList3[Number(evt.eventReason) - 1];
                var color2 = legendColorList4[Number(evt.eventLevel) - 1];

                html += "<div class='blk blk_" + j + "' id='event_id_" + evt.id + "' data-id='" + evt.id + "' data-fltno='" + evt.flightNo + "' data-code='" + dd.code + "' data-date='" + evt.flightDateStr + "' data-depstn='" + evt.depstn + "' >";
                html += "<div class='time'>";
                html += "<span class='l'>" + date + "</span>";
                html += "<span class='r hour'></span>";
                html += "</div>";
                html += "<div class='fltno' style='color:" + color2 + "'>" + evt.flightNo + "</div>";
                var style = '';
                evt.airlineCn = evt.airlineCn.replace(/\//g, '');
                if (evt.airlineCn.length > 7) {
                    style = 'position: absolute; line-height:13px; bottom:23px;';
                }
                html += "<div class='city' style='" + style + "' >" + evt.airlineCn + "</div>";
                html += "<div class='bot'><span class='dot' style='color:" + color + "; '>●</span><span class='lb'>" + (evt.eventType == null ? '-' : evt.eventType) + "</span></div>";
                html += "</div><!-- /blk -->";

                //
                if (j < 5) {
                    fetchFltInfo(dd.code, evt.flightDateStr + ' 00:00:00', evt.flightDateStr + ' 23:59:59', [evt.flightNo], evt.id, evt.depstn);
                }

            }
            html += "</div><!-- /itmlst -->";

            // END
            html += "</div><!-- /comprow -->";
        }
    }

    $('.block_r .scrollcontent').html('');
    $('.block_r .tabc2 .scrollcontent').html(html);

    var html = '';
    var len = legendNameList.length;
    for (var i = 0; i < len; i++) {
        var name = legendNameList[i];
        var color;
        var opacity;
        if (eventListReasonFilter == i + 1 || (eventListReasonFilter == '' && eventListLevelFilter == '')) {
            color = legendColorList1[i];
            opacity = 1;
        } else {
            color = '#5d9ae3';
            opacity = 0.3;
        }
        html += "<span class='itm' data-reason='" + (i + 1) + "' style='opacity:" + opacity + "' ><span class='dot' style='color:" + color + "; '>●</span><span class='lb'>" + (name == null ? '-' : name) + "</span></span>";
    }
    $('.block_r .tabc2 .legend .lst').html(html);

    var html = '';
    var len = legendNameList2.length;
    for (var i = 0; i < len; i++) {
        var name = legendNameList2[i];
        var lev = legendName2Key[name];
        if (lev == "5") { // 如果有岗位红线统一归类到严重错误2上
            continue
        }
        var color;
        var opacity;
        if (eventListLevelFilter == i + 1 || (eventListReasonFilter == '' && eventListLevelFilter == '')) {
            color = legendColorList2[i];
            opacity = 1;
        } else {
            color = '#5d9ae3';
            opacity = 0.3;
        }
        html += "<span class='itm' data-level='" + lev + "' style='opacity:" + opacity + "' ><span class='dot' style='color:" + color + "; '>●</span><span class='lb'>" + (name == null ? '-' : name) + "</span></span>";
    }
    $('.block_r .tabc2 .legend .lst2').html(html);


    $('.block_r .tabc2 .legend .lst .itm').off('click');
    $('.block_r .tabc2 .legend .lst .itm').on('click', function (evt) {
        if (eventListReasonFilter == '') {
            eventListReasonFilter = $(this).attr('data-reason');
        } else {
            eventListReasonFilter = ''
        }

        showSecurityEventList()

    })

    $('.block_r .tabc2 .legend .lst2 .itm').off('click');
    $('.block_r .tabc2 .legend .lst2 .itm').on('click', function (evt) {
        if (eventListLevelFilter == '') {
            eventListLevelFilter = $(this).attr('data-level');
        } else {
            eventListLevelFilter = ''
        }

        showSecurityEventList()
    })


    // scrollbar
    $(".block_r .tabc2 .scrollpane").niceScroll({
        cursorcolor: "rgba(1,75,153,0.8)",
        cursorborder: "rgba(0,0,0,0)"
    });
    $(".block_r .tabc2 .scrollpane").getNiceScroll(0).resize();

    //
    $('.block_r .tabc2 .itmlst .blk').off('click');
    $('.block_r .tabc2 .itmlst .blk').on('click', function (evt) {
        evt.stopPropagation();
        var id = $(this).attr('data-id');
        var code = $(this).attr('data-code');
        showPop($(this), id, code);
    });

    //

    $('.block_r .btn_prev').off('click');
    $('.block_r .btn_prev').on('click', function (evt) {
        evt.stopPropagation();
        if ($(this).hasClass('disabled')) {
            return;
        }
        var id = $(this).attr('data-row');
        var len = $(this).attr('data-len');
        var page = $(this).attr('data-page');
        var perpage = 5;

        page = Number(page) - 1;
        $('#comprow_' + id + ' .btn_next').attr('data-page', page);
        $('#comprow_' + id + ' .btn_prev').attr('data-page', page);

        $('#comprow_' + id + ' .btn_next').removeClass('disabled');
        if (page == 0) {
            $('#comprow_' + id + ' .btn_prev').addClass('disabled');
        }

        for (var i = page * perpage; i < (page + 1) * perpage; i++) {
            var blk = $('#comprow_' + id + ' .blk_' + i);
            var loaded = blk.attr('data-flt-loaded');
            if (isNaN(loaded)) {
                var data_code = blk.attr('data-code');
                var data_id = blk.attr('data-id');
                var data_date = blk.attr('data-date');
                var data_fltno = blk.attr('data-fltno');
                var data_depstn = blk.attr('data-depstn');
                fetchFltInfo(data_code, data_date + ' 00:00:00', data_date + ' 23:59:59', [data_fltno], data_id, data_depstn);
            }
        }

        var width = 405;

        $('#comprow_' + id + ' .itmlst').animate({
            left: -(width * page)
        }, {
            duration: 300
        });
    });


    $('.block_r .btn_next').off('click');
    $('.block_r .btn_next').on('click', function (evt) {
        evt.stopPropagation();
        if ($(this).hasClass('disabled')) {
            return;
        }
        var id = $(this).attr('data-row');
        var len = $(this).attr('data-len');
        var page = $(this).attr('data-page');
        var perpage = 5;

        page = Number(page) + 1;
        $('#comprow_' + id + ' .btn_next').attr('data-page', page);
        $('#comprow_' + id + ' .btn_prev').attr('data-page', page);

        $('#comprow_' + id + ' .btn_prev').removeClass('disabled');
        if (len <= (page + 1) * perpage) {
            $('#comprow_' + id + ' .btn_next').addClass('disabled');
        }

        for (var i = page * perpage; i < (page + 1) * perpage; i++) {
            var blk = $('#comprow_' + id + ' .blk_' + i);
            var loaded = blk.attr('data-flt-loaded');
            if (isNaN(loaded)) {
                var data_code = blk.attr('data-code');
                var data_id = blk.attr('data-id');
                var data_date = blk.attr('data-date');
                var data_fltno = blk.attr('data-fltno');
                var data_depstn = blk.attr('data-depstn');
                fetchFltInfo(data_code, data_date + ' 00:00:00', data_date + ' 23:59:59', [data_fltno], data_id, data_depstn);
            }
        }

        var width = 405;

        $('#comprow_' + id + ' .itmlst').animate({
            left: -(width * page)
        }, {
            duration: 300
        });
    });



    // 所有关注/我的关注
    /*
    var html = '';
    var evtlst = all_company_data['careEventStatic_aq'].safeEventList;
    if(evtlst){
        var len = evtlst.length;
        for(var i=0; i<len; i++){
            var evt = evtlst[i];
            var color = legendColorList1[Number(evt.eventReason)-1];
            html += "<span class='itm con_flex_row'><span class='dot flex_none' style='color:"+color+"'>●</span><span class='lb flex_none'>"+evt.flightNo+"</span></span>"
        }
    }
    $('.allfavlst .cont').html(html);
    $('.allfavlst .cont').css('left', 0);


    var evtlst_page = 0;
    var evtlst_total_page = Math.ceil(evtlst.length/3);

    if(evtlst_total_page > 1){
        $('#btn_allfav_L_t2').css('opacity', 0.3);
        $('#btn_allfav_R_t2').css('opacity', 1);

        $('#btn_allfav_L_t2').show();
        $('#btn_allfav_R_t2').show();
    }else{
        $('#btn_allfav_L_t2').hide();
        $('#btn_allfav_R_t2').hide();
    }

    $('#btn_allfav_L_t2').off('click');
    $('#btn_allfav_L_t2').on('click', function(evt){
        evt.stopPropagation();

        if(evtlst_page > 0){
            evtlst_page--;
        }

        $('#btn_allfav_R_t2').css('opacity', 1);
        if(evtlst_page == 0){
            $('#btn_allfav_L_t2').css('opacity', 0.3);
        }
        
        var width = 220;
        $('.allfavlst .cont').animate({
            left:-(width*evtlst_page)
        }, {
            duration:300
        });
    });

    $('#btn_allfav_R_t2').off('click');
    $('#btn_allfav_R_t2').on('click', function(evt){
        evt.stopPropagation();

        if(evtlst_page < evtlst_total_page-1){
            evtlst_page++;
        }

        $('#btn_allfav_L_t2').css('opacity', 1);
        if(evtlst_page == evtlst_total_page-1){
            $('#btn_allfav_R_t2').css('opacity', 0.3);
        }
        
        var width = 220;
        $('.allfavlst .cont').animate({
            left:-(width*evtlst_page)
        }, {
            duration:300
        });
        
    });
    */


    $('#pop_tab2 .btnx').off('click');
    $('#pop_tab2 .btnx').on('click', function (evt) {
        evt.stopPropagation();
        $('#pop_tab2').hide();
    });


    function showPop(elem, id, code) {
        $('#pop_tab1').hide();

        var evt = eventKVList[id];

        var fltinfo = flightInfoList[id];

        var color = legendColorList1[legendNameList.indexOf(evt.eventAttrName)];
        var date = evt.flightDateStr;
        var darr = date.split('-');
        date = darr[1] + '/' + darr[2];

        $('#pop_tab2 .head .fltno').html(evt.companyName + ' ' + evt.flightNo);
        $('#pop_tab2 .head .fltno').css('background-image', 'url(img/logo_' + code + '.png)');
        //

        $('#pop_tab2 .row1 .date').html(date);
        $('#pop_tab2 .row1 .ac').html(evt.acType);


        $('#pop_tab2 .city1 .nm').html(evt.depstnName);
        $('#pop_tab2 .city2 .nm').html(evt.arrstnName);

        if (fltinfo) {
            // 时间
            $('#pop_tab2 .city1 .tm').html(getHMtime(fltinfo.stdChn)); //计划出发
            $('#pop_tab2 .city2 .tm').html(getHMtime(fltinfo.staChn)); //计划到达

            $('#pop_tab2 .city1 .tm2').html(getHMtime(fltinfo.atdChn)); //实际出发
            $('#pop_tab2 .city2 .tm2').html(getHMtime(fltinfo.ataChn)); //实际到达
        }


        var noticeTime = evt.noticeTimeStr;
        var narr = noticeTime.split('-');
        noticeTime = narr[1] + '/' + narr[2];
        noticeTime = noticeTime.substring(0, noticeTime.length - 3);

        $('#pop_tab2 .event_type').html(evt.levelDetail);
        $('#pop_tab2 .time_sb').html(noticeTime);

        $('#pop_tab2 .eventType').html(evt.eventType);
        $('#pop_tab2 .title').html(evt.title);
        $('#pop_tab2 .description').html(evt.description);
        $('#pop_tab2 .deal').html(evt.deal);
        $('#pop_tab2 .precaution').html(evt.precaution);
        $('#pop_tab2 .status').html(statusList[Number(evt.status) - 1]); // 状态跟踪 1:调查分析中、2:落实措施中、3:已 关闭;)
        $('#pop_tab2 .punishInfo').html(evt.isPunish != 'false' ? evt.punishInfo : '无');
        //$('#pop_tab2 .comments').html(evt.comments != 'null' ? evt.comments : '无');



        var param = {
            "id": id,
        }
        $.ajax({
            type: 'post',
            url: "/bi/web/findEventDetail",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {
                var comments = response.result.comments;
                var lst = [];
                for (var i = 0; i < comments.length; i++) {
                    var cmt = comments[i];
                    if (cmt.content != '' && cmt.commentByName != '') {
                        lst.push(cmt);
                    }
                }
                var html = ''
                if (lst.length > 0) {
                    for (var i = 0; i < lst.length; i++) {
                        var cmt = lst[i];

                        html += "<div class='row'>";
                        html += "<div class='label'>批示意见</div><br>";
                        html += "<span class=''>" + cmt.content + "</span><br>";
                        html += "<span class='label'>批示人及批示时间</span><br>";
                        html += "<span class=''>" + cmt.commentByName + ' ' + cmt.commentTimeStr + "</span>";
                        html += "</div>";

                        /*
                        if(cmt.replys && cmt.replys.length > 0){
                            for(var j=0; j<cmt.replys.length; j++){
                                var rpy = cmt.replys[j];
                                html += "<div class='row'>";
                                html += "<div class='label'>回复意见</div><br>";
                                html += "<span class=''>"+rpy.content+"</span><br>";
                                html += "<span class='label'>回复人及回复时间</span><br>";
                                html += "<span class=''>"+rpy.replyByName+' '+rpy.replyTimeStr+"</span>";
                                html += "</div>";
                            }
                        }else{
                            html += "<div class='row'>";
                            html += "<div class='label'>回复意见</div><br>";
                            html += "<span class=''>无</span><br>";
                            html += "<span class='label'>回复人及回复时间</span><br>";
                            html += "<span class=''>无</span>";
                            html += "</div>";
                        }
                        */
                    }



                } else {

                    html += "<div class='row'>";
                    html += "<div class='label'>批示意见</div><br>";
                    html += "<span class=''>无</span><br>";
                    html += "<span class='label'>批示人及批示时间</span><br>";
                    html += "<span class=''>无</span>";
                    html += "</div>";

                    /*
                    html += "<div class='row'>";
                    html += "<div class='label'>回复意见</div><br>";
                    html += "<span class=''>无</span><br>";
                    html += "<span class='label'>回复人及回复时间</span><br>";
                    html += "<span class=''>无</span>";
                    html += "</div>";
                    */
                }

                $('#pop_tab2 .commentlist').html(html);

                $("#pop_tab2 .scrollpane").getNiceScroll(0).resize();

            },
            error: function () { }
        });


        /*
        var param = {
            "eventId": id,
        }

        $.ajax({           
            type: 'post',
            url:"/bi/web/commentEvent",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function(response) {
                

            },
            error:function() {
            }
        });
        */

        setXY();

        function setXY() {
            var bx = elem.offset().left;
            var bt = elem.offset().top;

            var left = bx - $('#pop_tab2').width() - 10;
            var arrtop = bt - ($('.page-wrapper').height() - $('#pop_tab2').height() - 20) + elem.height() / 2;

            $('#pop_tab2').css('left', left);
            $('#pop_tab2').css('bottom', 20);
            $('#pop_tab2 .arr').css('top', arrtop);

            $('#pop_tab2').show();


            // scrollbar
            $("#pop_tab2 .scrollpane").niceScroll({
                cursorcolor: "rgba(4,18,62,0.3)",
                cursorborder: "rgba(0,0,0,0)"
            });
            $("#pop_tab2 .scrollpane").getNiceScroll(0).resize();
            $("#pop_tab2 .scrollpane").getNiceScroll(0).doScrollTop(0, 0);
        }

    }

} //showSecurityEventList



var runTimeEventListNameFilter = ''; //
var eventListReasonFilter = ''; //['意外', '人为', '机械', '其他'];
var eventListLevelFilter = ''; //['一般事件', '一类事件', '事故征候', '待定'];
var isPunishFilter = 0; //0, 1
var eventListTabType = 'runtime'; //runtime, security

$('.block_r .tab').on('click', function (evt) {
    evt.stopPropagation();

    resetFilters();

    var id = $(this).attr('data-id');
    selectEventTab(id);
})


function selectEventTab(id) {
    $('.block_r .tab').removeClass('selected');
    $('.block_r .tab' + id).addClass('selected');

    eventListTabType = $('.block_r .tab' + id).attr('data-type');

    $('.block_r .tabc').hide();
    $('.block_r .tabc' + id).show();

    if (eventListTabType == 'runtime') {
        showRunTimeEventList();
    } else {
        showSecurityEventList();
    }
}

dfd.done(function () {
    if (!hasAllCompanyPermission()) {
        $("#content_Y .block_mid").remove();
    }
})


$('#date_select .tab').on('click', function (evt) {
    evt.stopPropagation();
    $('#date_select .tab').removeClass('selected');
    $(this).addClass('selected');
    date_type = $(this).attr('data-type');
    $('.tabcontent').hide();
    $('#content_' + date_type).show();
    getAllCompanyKpiData1();
})

$('#date_select .tab_' + date_type).addClass('selected');
$('#content_' + date_type).show();



// 运行不正常 点击左边按钮 筛选 右边列表

$('.block_llt .itm1').on('click', function (evt) {
    evt.stopPropagation();

})
$('.block_llt .itm2').on('click', function (evt) {
    evt.stopPropagation();

})
$('.block_llt .itm3').on('click', function (evt) {
    evt.stopPropagation();

})
$('.block_llt .itm4').on('click', function (evt) {
    evt.stopPropagation();
    $('.block_r .tab1').click();
})


// 安全不正常 点击左边按钮 筛选 右边列表

function resetFilters() {
    runTimeEventListNameFilter = '';
    eventListReasonFilter = '';
    eventListLevelFilter = '';
    isPunishFilter = 0;
}

$('.block_llb .itm1').on('click', function (evt) {
    evt.stopPropagation();

    resetFilters()

    eventListReasonFilter = '2'; //['意外', '人为', '机械', '其他'];
    selectEventTab(2);

})
$('.block_llb .itm2').on('click', function (evt) {
    evt.stopPropagation();

    resetFilters()

    eventListReasonFilter = '1';
    selectEventTab(2);
})
$('.block_llb .itm3').on('click', function (evt) {
    evt.stopPropagation();

    resetFilters()

    eventListReasonFilter = '3';
    selectEventTab(2);
})
$('.block_llb .itm4').on('click', function (evt) {
    evt.stopPropagation();

    resetFilters()

    eventListReasonFilter = '4';
    selectEventTab(2);
})

$('.block_llb .btnlev3').on('click', function (evt) {
    evt.stopPropagation();

    resetFilters()

    eventListLevelFilter = '3'; //['一般事件', '一类事件', '事故征候', '待定'];
    selectEventTab(2);

})
$('.block_llb .btnlev2').on('click', function (evt) {
    evt.stopPropagation();

    resetFilters()

    eventListLevelFilter = '2'; //['一般事件', '一类事件', '事故征候', '待定'];
    selectEventTab(2);
})


$('.block_llt .punish').on('click', function (evt) {
    evt.stopPropagation();

    resetFilters()

    isPunishFilter = 1;
    selectEventTab(2);
})

$('.btn_event').on('click', function (evt) {
    evt.stopPropagation();

    resetFilters()

    selectEventTab(2);
})

function chooseRuntime(name) {
    resetFilters();
    runTimeEventListNameFilter = name;
    selectEventTab(1);
}

// ---------------------- 

function onCompanyChanged(comp_code) {
    current_company_code = comp_code;
    checkCompanyReay();
}

function checkCompanyReay() {
    if (companyCode2Name[current_company_code] == undefined) {
        setTimeout(checkCompanyReay, 0);
    } else {
        getAllCompanyKpiData1();
    }
}



function setTitleDate() {
    var date = '';
    if (date_type == 'D') {
        date = $('#main_cb_D .combobox_label').text();
    } else if (date_type == 'L') {
        date = $('#main_cb_L .combobox_label').text();
    } else if (date_type == 'M') {
        date = $('#main_cb_M .combobox_label').text();
    } else if (date_type == 'Y') {
        date = $('#main_cb_Y .combobox_label').text();
    }
    $('.pagetitle .maintitle .date').html(date); // 页面标题-时间
}

function getNormalRate(data_type) {
    var date = getCurrentDate();
    date = date.replace(/\-/g, '');
    var compcode = current_company_code
    var ddd = all_company_data[data_type][compcode]['NORMAL_RATE_ZT'][date_type];
    var val = Number(ddd[date]);

    if (!isNaN(val)) {
       return  (val*100).toFixed(1)
    }
    return '';
}

function getKpiVal(data_type, kpi_code) {
    var today = moment().format('YYYY-MM-DD');

    if (date_type == 'D' && kpi_code == 'SCH_NO') {
        if (data_type == 'kpi_value_d') {
            return Number(all_company_data['flt_sts'].pftc);
        } else if (data_type == 'kpi_value_sq_d') {
            return Number(all_company_data['flt_sts_sq'].pftc);
        }
        return 0;
    }

    if (date_type == 'D' && kpi_code == 'EXCUTED_NO') {
        if (data_type == 'kpi_value_d') {
            return Number(all_company_data['flt_sts'].cftc); //已执行航班总数
        } else if (data_type == 'kpi_value_sq_d') {
            return Number(all_company_data['flt_sts_sq'].cftc); //已执行航班总数
        }
        return 0;
    }

    if (date_type == 'D' && kpi_code == 'NORMAL_NO_T') {
        if (data_type == 'kpi_value_d') {
            return Number(all_company_data['flt_sts'].pfrtc);
        } else if (data_type == 'kpi_value_sq_d') {
            return Number(all_company_data['flt_sts_sq'].pfrtc);
        }
        return 0;
    }

    if (date_type == 'D' && kpi_code == 'CANCEL_NO') {
        if (data_type == 'kpi_value_d') {
            return Number(all_company_data['flt_sts'].qftc1);
        } else if (data_type == 'kpi_value_sq_d') {
            return Number(all_company_data['flt_sts_sq'].qftc1);
        }
        return 0;
    }

    if (date_type == 'D' && kpi_code == 'DIV_NO') {
        if (data_type == 'kpi_value_d') {
            return Number(all_company_data['flt_sts'].dftc);
        } else if (data_type == 'kpi_value_sq_d') {
            return Number(all_company_data['flt_sts_sq'].dftc);
        }
        return 0;
    }

    if (date_type == 'D' && kpi_code == 'TURNBACK_NO') {
        if (data_type == 'kpi_value_d') {
            return Number(all_company_data['flt_sts'].bftc);
        } else if (data_type == 'kpi_value_sq_d') {
            return Number(all_company_data['flt_sts_sq'].bftc);
        }
        return 0;
    }

    if (date_type == 'D' && kpi_code == 'DELAY_NO_240') {
        if (data_type == 'kpi_value_d') {
            return Number(all_company_data['flt_sts'].pfdtc4);
        } else if (data_type == 'kpi_value_sq_d') {
            return Number(all_company_data['flt_sts_sq'].pfdtc4);
        }
        return 0;
    }


    var date = getCurrentDate();
    date = date.replace(/\-/g, '');


    var sum = 0;
    var compcode = current_company_code
    if (compcode == parent_company) {

        var len = companylist.length;
        for (var i = 0; i < len; i++) {

            var dat = companylist[i];
            if (dat.code != parent_company) {
                var ddd = all_company_data[data_type][dat.code][kpi_code][date_type];
                var val = Number(ddd[date]);
                if (!isNaN(val)) {
                    sum += Number(val);
                }
            }
        }

    } else {
        var ddd = all_company_data[data_type][compcode][kpi_code][date_type];
        var val = Number(ddd[date]);
        if (!isNaN(val)) {
            sum = Number(val);
        }
    }

    return sum;
}



getAllCompanyKpiData1();



// 求和所有子公司的指标
function sumKpi(data_type, compcode, kpi_code, datetype) {

    var sum = 0;

    if (compcode == parent_company) {

        var len = companylist.length;
        for (var i = 0; i < len; i++) {

            var dat = companylist[i];
            if (dat.code != parent_company) {
                var ddd = all_company_data[data_type][dat.code][kpi_code][datetype];
                var val = 0;
                for (var date in ddd) {
                    if (!isNaN(ddd[date])) {
                        val += Number(ddd[date]);
                    }
                }
                if (!isNaN(val)) {
                    sum += Number(val);
                }
            }
        }

    } else {
        var ddd = all_company_data[data_type][compcode][kpi_code][datetype];
        var val = 0;
        for (var date in ddd) {
            if (!isNaN(ddd[date])) {
                val += Number(ddd[date]);
            }
        }
        if (!isNaN(val)) {
            sum += Number(val);
        }
    }

    return sum;

}



// ------------------------------------------------------------------------
// 获取 航班信息
// ------------------------------------------------------------------------
function fetchFltInfo(companyCode, stdStart, stdEnd, fltNoList, elemid, depstn) {

    if (flightInfoList[elemid] == undefined) {
        setTimeout(function () {

            var param = {
                "stdStart": stdStart,
                "stdEnd": stdEnd,
                "acOwner": companyCode,
                "statusList": '',
                "fltNoList": fltNoList.join(','),
            }
            $.ajax({
                type: 'post',
                url: "/bi/web/getStandardFocFlightInfo",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    var list = response.data;
                    if (list) {
                        for (var i = list.length - 1; i >= 0; i--) {
                            var obj = list[i];
                            if (obj.depStn == depstn) {
                                var hourmin = getHMtime(obj.stdChn); // 计划出发
                                $('#event_id_' + elemid + ' .hour').text(hourmin);
                                $('#event_id_' + elemid).attr('data-flt-loaded', '1');
                                flightInfoList[elemid] = obj;
                                break;
                            }
                        }
                    }
                },
                error: function () { }
            });

        }, 10);

    } else {
        var obj = flightInfoList[elemid];
        var hourmin = getHMtime(obj.stdChn); // 计划出发
        $('#event_id_' + elemid + ' .hour').text(hourmin);
        $('#event_id_' + elemid).attr('data-flt-loaded', '1');
    }


}


// 格式化时间 hh:mm
function getHMtime(fulltime) {
    var arr = fulltime.split(' ');
    var arr2 = arr[1].split(':');
    var hourmin = arr2[0] + ':' + arr2[1];
    return hourmin;
}

// ------------------------------------------------------------------------
// 获取 座舱布局
// ------------------------------------------------------------------------
var acCabinList = {};

function getCabin(acno, elem) {
    if (acCabinList[acno] == undefined) {
        var param = {
            "acNo": acno,
        }
        $.ajax({
            type: 'post',
            url: "/bi/web/getAcAircraftList",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {
                var ac = response.data[0].data;
                var cabin = ac.cabin; // 座舱布局
                $(elem).text(cabin);
                acCabinList[acno] = cabin
            },
            error: function () { }
        });

    } else {
        var cabin = acCabinList[acno];
        $(elem).text(cabin);
    }

}



function drawHalfCircle(id, ratestr, ratestr2) {

    var ratestr = Math.round(ratestr * 10) / 10;

    var rate = ratestr / 100;
    var rate2 = ratestr2 / 100;

    var canvas = document.getElementById(id);
    var context = canvas.getContext('2d');
    context.clearRect(0, 0, canvas.width, canvas.height);
    var x = canvas.width / 2;
    var y = canvas.height;

    var radius = 36;
    var lineWidth = 10;

    // draw back
    var startAngle = Math.PI;
    var endAngle = startAngle + Math.PI;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle, endAngle, counterClockwise);
    context.lineWidth = lineWidth;
    context.strokeStyle = '#0952cb';
    context.stroke();

    // draw overlay
    var startAngle2 = startAngle;
    var endAngle2 = startAngle + (endAngle - startAngle) * rate;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
    context.lineWidth = lineWidth;

    // linear gradient
    if (rate < 0.5) {
        var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
    } else if (rate < 0.8) {
        var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
    } else {
        var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, canvas.width / 2);
    }
    color.addColorStop(0, '#0b72ec');
    color.addColorStop(1, '#54d0fd');

    context.strokeStyle = color;
    context.stroke();


    // pointer
    var angle = startAngle + (endAngle - startAngle) * (rate2);
    $('#' + id + '_pointer').css('transform', 'rotate(' + (angle / Math.PI * 180) + 'deg)');
}



/////////////////////////  CHARTS ////////////////////////



function setChartRunTimeCircle() {


    // ---------------------------
    var chart_id = 'chart_l_runTimeEvent';

    var colors = ['#e95551', '#f7a449', '#fff353', '#21b3ce', '#176ebf', '#0a3a89', '#8619db', '#aa44e1'];
    // ---------------------------



    var data_s1 = [];
    var data_legend = [];

    //var lst = all_company_data['runTimeEventTypeStatic']
    var lst = all_company_data['runTimePropertiesStatic']
    if (lst) {
        var len = lst.length;
        for (var i = 0; i < len; i++) {
            var d = lst[i];
            data_s1.push({
                value: d.count,
                name: d.name,
                rate: d.rate,
                code: d.code
            });
        }
    }



    // ---------------------------------------------------------
    var perpage = 5;
    var totalpage = Math.ceil(data_s1.length / perpage);
    var page = 0;

    function setTableDat() {
        var total = 0;
        for (var i = 0; i < data_s1.length; i++) {
            var d = data_s1[i];
            total += Number(d.value);
        }

        var html = '';
        for (var i = page * perpage; i < Math.min(data_s1.length, (page + 1) * perpage); i++) {
            var d = data_s1[i];
            var c = colors[i];
            var per = Math.round(Number(d.value) / total * 1000) / 10;
            if (isNaN(per)) {
                per = 0;
            }
            html += "<div class='legend_row'>";
            html += "<span class='color' style='color:" + c + "'>◼︎</span>";
            html += "<span class='name' onclick=chooseRuntime('" + d.name + "')>" + d.name + "</span>";
            html += "<span class='num white'>" + d.value + "</span>";
            html += "<span class='fs11'>起</span>";
            html += "<span class='per'>" + per + "%</span>";
            html += "</div>";
        }
        $('#l_runTimeEventChartLegend').html(html);
    }
    setTableDat();

    $('.block_llt .btn_prev').on('click', function (event) {
        event.preventDefault();
        if (page == 0) {
            return;
        }

        page--;

        $('.block_llt .btn_next').removeClass('disabled');
        if (page == 0) {
            $('.block_llt .btn_prev').addClass('disabled');
        }

        setTableDat()

    });

    $('.block_llt .btn_next').on('click', function (event) {
        event.preventDefault();
        if (page == totalpage - 1) {
            return;
        }

        page++

        $('.block_llt .btn_prev').removeClass('disabled');
        if (page == totalpage - 1) {
            $('.block_llt .btn_next').addClass('disabled');
        }

        setTableDat()

    });


    // ---------------------------------------------------------
    // 主KPI图表
    var chart = echarts.init(document.getElementById(chart_id));

    var option = {

        tooltip: {
            trigger: 'item',
            /*
            formatter: function (params, ticket, callback) {
                var html = '';
                html += '<div class="chart_tip">';
                html += '<div class="tit">'+params.name+'</div>';
                html += '<div class="r1"><span class="c1">占比</span><span class="c2">'+params.percent+'%</span></div>';
                html += '<div class="r1"><span class="c1">兑换量</span><span class="c2">'+formatCurrency(Math.round(params.value/10000),0)+'万分</span></div>';
                if(params.data.tb > 0){
                    //html += '<div class="r1"><span class="c1">同比</span><span class="c2">'+params.data.tb+'%</span><span class="green">↑</span></div>';
                }else if(params.data.tb < 0){
                    //html += '<div class="r1"><span class="c1">同比</span><span class="c2">'+params.data.tb+'%</span><span class="red ">↓</span></div>';
                }else{
                    //html += '<div class="r1"><span class="c1">同比</span><span class="c2">-</span></div>';
                }
                if(params.data.hb > 0){
                    html += '<div class="r1"><span class="c1">环比</span><span class="c2">'+params.data.hb+'%</span><span class="green">↑</span></div>';
                }else if(params.data.hb < 0){
                    html += '<div class="r1"><span class="c1">环比</span><span class="c2">'+params.data.hb+'%</span><span class="red ">↓</span></div>';
                }else{
                    html += '<div class="r1"><span class="c1">环比</span><span class="c2">-</span></div>';
                }
                html += '</div>';

                return html;
             },
             backgroundColor: '',
            */
        },
        legend: {
            show: false,
            orient: 'vertical',
            y: 19,
            left: 'left',
            itemWidth: 8,
            itemHeight: 8,
            textStyle: {
                color: '#99ccff',
            },
            data: data_legend
        },
        color: colors,
        series: [{
            name: '',
            type: 'pie',
            radius: ['45%', '80%'],
            center: ['50%', '50%'],
            data: data_s1,
            //roseType: 'angle',
            clockwise: false,
            label: {
                normal: {
                    show: false,
                    position: 'inside',
                    formatter: '{d}%',
                    textStyle: {
                        fontSize: 10 + fontSizeDiff()
                    }
                }
            },
            labelLine: {
                normal: {
                    show: false,
                    smooth: 0.2,
                    length: 0,
                    length2: 0
                }
            },


            animationType: 'scale',
            animationEasing: 'elasticOut',
            animationDelay: function (idx) {
                return Math.random() * 200;
            }
        }]
    };

    chart.setOption(option);
    chart.on("click", function (params) {
        chooseRuntime(params.data.name);
    })

}



// 航司对比
// 各公司 航班量 正常率
function setChartCompsFltAndNormalrate() {

    if (all_company_data['flt_sts'] == undefined) {
        //setTimeout(setChartCompsFltAndNormalrate, 10);
        //return;
    }

    // ---------------------------
    var chart_id1 = 'chart_' + date_type.toLowerCase() + '_m_t1_1';
    if ($('#' + chart_id1).length == 0) {
        return;
    }
    var colors = [
        ['#FFF', '#0a6bc0'], //柱状图渐变颜色
        ['#ffe55a', '#ffe55a'], //柱状图渐变颜色
    ];
    // ---------------------------

    var xAxisData = [];
    var data_s1 = [];
    var data_s2 = [];

    var currentData = getCurrentDate();
    currentData = currentData.replace(/\-/g, '');

    var len = companylist.length;
    for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        var compcode = dat.code;
        if (compcode != parent_company) {
            if (all_company_data['kpi_value_d'][compcode]) {

                if (date_type != 'Y') {
                    var val2 = all_company_data['kpi_value_d'][compcode]['SCH_NO'][date_type][currentData];
                } else {
                    var val2 = sumKpi('kpi_value_d', compcode, 'SCH_NO', 'M');
                }
                var val1 = all_company_data['kpi_value_d'][compcode]['NORMAL_RATE_ZT'][date_type][currentData];
                var rate = (Number(val1) * 100).toFixed(1);
            } else {
                var val2 = 0;
                var rate = 0;
            }


            xAxisData.push(dat.code);
            data_s1.push({
                value: trimDecimal(val2, 0)
            });
            data_s2.push({
                value: rate
            });
        }
    }


    // ---------------------------------------------------------
    // 主KPI图表
    var chart = echarts.init(document.getElementById(chart_id1));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                return params.name + ': ' + params.value;
            },

        },
        legend: {
            show: true,
            orient: 'horizontal',
            top: 10,
            left: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '航班量',
                icon: 'rect',
            }, {
                name: '正常率',
                icon: 'circle',
            }]
        },
        grid: {
            top: 50,
            left: 55,
            right: 55,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 10 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 10 + fontSizeDiff(),
                },
                formatter: '{value}'
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }, {
            type: 'value',
            name: '',
            nameLocation: 'end',
            position: 'right',
            //min: 0,
            //max: 100,
            //interval: 10,
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 10 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 10 + fontSizeDiff(),
                    align: 'right',
                },
                margin: 30,
                formatter: '{value}%',
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ['rgba(69,165,246,0.1)'] // 分割线颜色
                }
            }
        }],
        series: [{
            yAxisIndex: 0,
            name: '航班量',
            type: 'bar',
            barWidth: 8,
            data: data_s1,
            label: {
                normal: {
                    show: false,
                    position: 'top'
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[0][0]
                        }, {
                            offset: 1,
                            color: colors[0][1]
                        }]),
                }
            },

        }, {
            yAxisIndex: 1,
            name: '正常率',
            type: 'line',
            symbol: 'circle',
            symbolSize: 3,
            width: 0.5,
            smooth: false,
            data: data_s2,
            label: {
                normal: {
                    show: false,
                }
            },
            lineStyle: {
                normal: {
                    color: colors[1][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[1][0],
                    opacity: 1,
                }
            },

        }]
    };

    chart.setOption(option);

}



// 本司趋势
// 航班量 正常率
function setChartTrendFltAndNormalrate() {

    // ---------------------------
    var chart_id1 = 'chart_' + date_type.toLowerCase() + '_m_t2_1';
    if ($('#' + chart_id1).length == 0) {
        return;
    }
    var colors = [
        ['#FFF', '#0a6bc0'], //柱状图渐变颜色
        ['#ffe55a', '#ffe55a'], //柱状图渐变颜色
    ];
    // ---------------------------

    var xAxisData = [];
    var data_s1 = [];
    var data_s2 = [];


    var date_list = [];
    var lst = all_company_data['kpi_value_d'][current_company_code]['NORMAL_RATE_ZT'][date_type];
    for (var d in lst) {
        date_list.push(d);
    }

    if (date_list.length > 8) {
        date_list = date_list.splice(date_list.length - 8, 8);
    }

    /////


    var len = companylist.length;
    for (var j = 0; j < date_list.length; j++) {
        var date = date_list[j];

        if (date_type == 'D') {
            xAxisData.push(date);
        } else if (date_type == 'L') {
            // Week: *********
            //var label = date.substr(0,4) + '年第' + Number(date.substr(4,3)) + '周';
            //20170419 双双要求：显示的周数+1
            var label = '第' + (Number(date.substr(4, 3))) + '周 '; //+ response[date];
            xAxisData.push(label);
        } else if (date_type == 'M') {
            var label = date.substr(2, 2) + '/' + date.substr(4, 2);
            xAxisData.push(label);
        }


        var val1 = all_company_data['kpi_value_d'][current_company_code]['NORMAL_RATE_ZT'][date_type][date];
        var val2 = all_company_data['kpi_value_d'][current_company_code]['SCH_NO'][date_type][date];
        var rate = (Number(val1) * 100).toFixed(1)

        data_s1.push({
            value: val2
        });
        data_s2.push({
            value: rate
        });

    }


    // ---------------------------------------------------------
    // 主KPI图表
    var chart = echarts.init(document.getElementById(chart_id1));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                return params.name + ': ' + params.value;
            },

        },
        legend: {
            show: true,
            orient: 'horizontal',
            top: 10,
            left: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '航班量',
                icon: 'rect',
            }, {
                name: '正常率',
                icon: 'circle',
            }]
        },
        grid: {
            top: 50,
            left: 55,
            right: 55,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
                formatter: function (value, index) {
                    return chartDateFormatter(value, index);
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 10 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 10 + fontSizeDiff(),
                },
                formatter: '{value}'
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }, {
            type: 'value',
            name: '',
            nameLocation: 'end',
            position: 'right',
            //min: 0,
            //max: 100,
            //interval: 10,
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 10 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 10 + fontSizeDiff(),
                    align: 'right',
                },
                margin: 30,
                formatter: '{value}%',
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ['rgba(69,165,246,0.1)'] // 分割线颜色
                }
            }
        }],
        series: [{
            yAxisIndex: 0,
            name: '航班量',
            type: 'bar',
            barWidth: 8,
            data: data_s1,
            label: {
                normal: {
                    show: false,
                    position: 'top'
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[0][0]
                        }, {
                            offset: 1,
                            color: colors[0][1]
                        }]),
                }
            },

        }, {
            yAxisIndex: 1,
            name: '正常率',
            type: 'line',
            symbol: 'circle',
            symbolSize: 3,
            width: 0.5,
            smooth: false,
            data: data_s2,
            label: {
                normal: {
                    show: false,
                }
            },
            lineStyle: {
                normal: {
                    color: colors[1][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[1][0],
                    opacity: 1,
                }
            },

        }]
    };

    chart.setOption(option);

}



// 航司对比
// 各公司 运行不正常事件 环比
function setChartCompsAbnormalEvents() {

    // ---------------------------
    var chart_id1 = 'chart_' + date_type.toLowerCase() + '_m_t1_2';
    if ($('#' + chart_id1).length == 0) {
        return;
    }
    var colors = [
        ['#00ffff', '#00ffff'], //柱状图渐变颜色
        ['#5aff5c', '#5aff5c'], //柱状图渐变颜色
    ];
    // ---------------------------

    var xAxisData = [];
    var data_s1 = [];
    var data_s2 = [];


    var len = companylist.length;
    for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        var compcode = dat.code;
        if (compcode != parent_company) {

            var abn = all_company_data['runTimeEventStatic'][compcode];
            var abn_sq = all_company_data['runTimeEventStatic_sq'][compcode];

            if (isNaN(abn)) {
                abn = 0;
            }
            if (isNaN(abn_sq)) {
                abn_sq = 0;
            }

            // 环比=(本期-上期)/上期×100%
            var hb = abn_sq > 0 ? Math.round((abn - abn_sq) / abn_sq * 10000) / 100 : 0;

            xAxisData.push(dat.code);
            data_s1.push({
                value: abn
            });
            data_s2.push({
                value: hb
            });
        }
    }


    // ---------------------------------------------------------
    // 主KPI图表
    var chart = echarts.init(document.getElementById(chart_id1));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                return params.name + ': ' + params.value;
            },

        },
        legend: {
            show: true,
            orient: 'horizontal',
            top: 10,
            left: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            selected: {
                '环比': false
            },
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '运行不正常事件量',
                icon: 'circle',
            }, {
                name: '环比',
                icon: 'circle',
            }]
        },
        grid: {
            top: 50,
            left: 55,
            right: 55,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 10 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 10 + fontSizeDiff(),
                },
                formatter: '{value}'
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }, {
            type: 'value',
            name: '',
            nameLocation: 'end',
            position: 'right',
            //min: 0,
            //max: 100,
            //interval: 10,
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 10 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 10 + fontSizeDiff(),
                    align: 'right',
                },
                margin: 30,
                formatter: '{value}%',
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ['rgba(69,165,246,0.1)'] // 分割线颜色
                }
            }
        }],
        series: [{
            yAxisIndex: 0,
            name: '运行不正常事件量',
            type: 'line',
            symbol: 'circle',
            symbolSize: 3,
            width: 0.3,
            smooth: true,
            data: data_s1,
            label: {
                normal: {
                    show: false,
                }
            },
            lineStyle: {
                normal: {
                    color: colors[0][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[0][0],
                    opacity: 1,
                }
            },
            areaStyle: {
                normal: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0,
                            color: 'rgba(10,76,191,0.3)' // 0% 处的颜色
                        }, {
                            offset: 1,
                            color: 'rgba(10,76,191,0)' // 100% 处的颜色
                        }],
                        globalCoord: false // 缺省为 false
                    },
                    opacity: 1,
                }
            },

        }, {
            yAxisIndex: 1,
            name: '环比',
            type: 'line',
            symbol: 'circle',
            symbolSize: 3,
            width: 1,
            smooth: false,
            data: data_s2,
            label: {
                normal: {
                    show: false,
                }
            },
            lineStyle: {
                normal: {
                    color: colors[1][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[1][0],
                    opacity: 1,
                }
            },

        }]
    };

    chart.setOption(option);

}


// 本司趋势
// 运行不正常事件 环比
var trendAbnormalEvents = [];

function setChartTrendAbnormalEvents() {

    trendAbnormalEvents = [];


    var ycode = 'ALL';
    for (var yc in companyYshcode2Code) {
        if (companyYshcode2Code[yc] == current_company_code) {
            ycode = yc;
        }
    }

    var loadingInProgress = 0;


    if (date_type == 'D') {
        for (var i = 0; i < 8; i++) {

            var date = new Date();
            var startTime_ts = date.getTime() - 86400000 * i;
            date.setTime(startTime_ts);

            var mm = date.getMonth() + 1;
            var dd = date.getDate();
            if (mm < 10) {
                mm = '0' + mm;
            }
            if (dd < 10) {
                dd = '0' + dd;
            }

            var datestr = date.getFullYear() + '' + mm + '' + dd;
            var startTime = date.getFullYear() + '-' + mm + '-' + dd + ' 00:00:00';
            var endTime = date.getFullYear() + '-' + mm + '-' + dd + ' 23:59:59';

            loadDat(datestr, startTime, endTime);

        }
    } else if (date_type == 'L') {
        var len = weekDateRangeList.length;
        len = Math.min(len, 8);
        for (var i = 0; i < len; i++) {
            var wdat = weekDateRangeList[i];

            var datestr = wdat.date //'第'+wdat.weeknum+'周';
            var startTime = wdat.range[0] + ' 00:00:00';
            var endTime = wdat.range[1] + ' 23:59:59';

            loadDat(datestr, startTime, endTime);

        }
    } else if (date_type == 'M') {
        var len = 8;
        for (var i = 0; i < len; i++) {

            var date = new Date();
            var day1 = moment([date.getFullYear(), date.getMonth(), 1]);
            var day2 = moment();

            if (i > 0) {
                day1 = day1.subtract(i, 'months');
                var days = day1.daysInMonth();
                day2 = moment([day1.format('YYYY'), Number(day1.format('M')) - 1, days]);
            }

            var datestr = day1.format('YYYYMM')
            var startTime = day1.format('YYYY-MM-DD') + ' 00:00:00';
            var endTime = day2.format('YYYY-MM-DD') + ' 23:59:59';

            loadDat(datestr, startTime, endTime);
        }
    }



    function loadDat(date, startTime, endTime) {
        loadingInProgress++
        var param = {
            "companyCode": ycode,
            "startTime": startTime,
            "endTime": endTime,
        }

        $.ajax({
            type: 'post',
            url: "/bi/web/runTimeEventStatic",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {
                loadingInProgress--
                var r = response.result;
                trendAbnormalEvents.push({
                    date: date,
                    count: r ? r.eventCount : 0
                });

                checkLoaded()
            },
            error: function () { }
        });
    }

    function checkLoaded() {
        if (loadingInProgress == 0) {
            setChartTrendAbnormalEvents2();
        }
    }

}

function setChartTrendAbnormalEvents2() {

    // ---------------------------
    var chart_id1 = 'chart_' + date_type.toLowerCase() + '_m_t2_2';
    if ($('#' + chart_id1).length == 0) {
        return;
    }
    var colors = [
        ['#00ffff', '#00ffff'], //柱状图渐变颜色
        ['#5aff5c', '#5aff5c'], //柱状图渐变颜色
    ];
    // ---------------------------

    var xAxisData = [];
    var data_s1 = [];

    trendAbnormalEvents.sort(function (a, b) {
        return a.date - b.date
    });

    var len = trendAbnormalEvents.length;
    for (var i = 0; i < len; i++) {
        var val = trendAbnormalEvents[i];
        if (date_type == 'D') {
            xAxisData.push(val.date);
        } else if (date_type == 'L') {
            var week = Number(val.date.substr(4, 3));
            xAxisData.push('第' + week + '周');
        } else if (date_type == 'M') {
            var date = val.date
            xAxisData.push(date);
        }
        data_s1.push({
            value: val.count
        });
    }


    // ---------------------------------------------------------
    // 主KPI图表
    var chart = echarts.init(document.getElementById(chart_id1));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                return params.name + ': ' + params.value;
            },

        },
        legend: {
            show: true,
            orient: 'horizontal',
            top: 10,
            left: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            selected: {
                '环比': false
            },
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '运行不正常事件量',
                icon: 'circle',
            }, {
                name: '环比',
                icon: 'circle',
            }
            ]
        },
        grid: {
            top: 50,
            left: 55,
            right: 25,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
                formatter: function (value, index) {
                    return chartDateFormatter(value, index);
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 10 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 10 + fontSizeDiff(),
                },
                formatter: '{value}'
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        },],
        series: [{
            yAxisIndex: 0,
            name: '运行不正常事件量',
            type: 'line',
            symbol: 'circle',
            symbolSize: 3,
            width: 0.3,
            smooth: false,
            data: data_s1,
            label: {
                normal: {
                    show: false,
                }
            },
            lineStyle: {
                normal: {
                    color: colors[0][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[0][0],
                    opacity: 1,
                }
            },
            areaStyle: {
                normal: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0,
                            color: 'rgba(10,76,191,0.3)' // 0% 处的颜色
                        }, {
                            offset: 1,
                            color: 'rgba(10,76,191,0)' // 100% 处的颜色
                        }],
                        globalCoord: false // 缺省为 false
                    },
                    opacity: 1,
                }
            }

        }]
    };

    chart.setOption(option);

}



// 航司对比
// 各公司 安全不正常事件 环比
function setChartCompsSecurityAbnormalEvents() {

    // ---------------------------
    var chart_id1 = 'chart_' + date_type.toLowerCase() + '_m_t1_3';
    if ($('#' + chart_id1).length == 0) {
        return;
    }
    var colors = [
        ['#ffff00', '#ffff00'], //柱状图渐变颜色
        ['#5aff5c', '#5aff5c'], //柱状图渐变颜色
    ];
    // ---------------------------

    var xAxisData = [];
    var data_s1 = [];
    var data_s2 = [];

    /*
    安全事件总数 eventCount
    意外事件总数 accidentCount
    意外事件占 accidentRate
    机械事件总数 machineCount
    机械事件占 machineRate
    为事件总数 humanEventCount
    为事件占 humanEventRate
    其他事件总数 otherEventCount
    其他事件占 otherEventRate
    事故征候总数 signCount
    一类事件总数 seriousCount
    航空公司三字码 companyCode
    */

    var len = companylist.length;
    for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        var compcode = dat.code;
        if (compcode != parent_company) {
            var sts = all_company_data['findEventStaticNum'][compcode];
            var sts_sq = all_company_data['findEventStaticNum_sq'][compcode];
            var abn = 0;
            var abn_sq = 0;

            if (sts) {
                abn = sts;
            }
            if (sts_sq) {
                abn_sq = sts_sq;
            }
            var hb = abn_sq > 0 ? Math.round((abn - abn_sq) / abn_sq * 10000) / 100 : 0;
            // var sts = all_company_data['findEventStatic'][compcode];

            // if (sts) {
            //     var abn = sts.eventCount;
            //     var abn_sq = sts.eventCountPre;
            //     // 环比=(本期-上期)/上期×100%
            //     var hb = abn_sq > 0 ? Math.round((abn - abn_sq) / abn_sq * 10000) / 100 : 0;

            // } else {
            //     var abn = 0;
            //     var hb = 0;
            // }
            xAxisData.push(dat.code);
            data_s1.push({
                value: abn
            });
            data_s2.push({
                value: hb
            });

        }
    }


    // ---------------------------------------------------------
    // 主KPI图表
    var chart = echarts.init(document.getElementById(chart_id1));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                return params.name + ': ' + params.value;
            },

        },
        legend: {
            show: true,
            orient: 'horizontal',
            top: 10,
            left: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            selected: {
                '环比': false
            },
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '安全不正常事件量',
                icon: 'circle',
            }, {
                name: '环比',
                icon: 'circle',
            }]
        },
        grid: {
            top: 50,
            left: 55,
            right: 55,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 10 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 10 + fontSizeDiff(),
                },
                formatter: '{value}'
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }, {
            type: 'value',
            name: '',
            nameLocation: 'end',
            position: 'right',
            //min: 0,
            //max: 100,
            //interval: 10,
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 10 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 10 + fontSizeDiff(),
                    align: 'right',
                },
                margin: 30,
                formatter: '{value}%',
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ['rgba(69,165,246,0.1)'] // 分割线颜色
                }
            }
        }],
        series: [{
            yAxisIndex: 0,
            name: '安全不正常事件量',
            type: 'line',
            symbol: 'circle',
            symbolSize: 3,
            width: 0.3,
            smooth: true,
            data: data_s1,
            label: {
                normal: {
                    show: false,
                }
            },
            lineStyle: {
                normal: {
                    color: colors[0][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[0][0],
                    opacity: 1,
                }
            },
            areaStyle: {
                normal: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0,
                            color: 'rgba(10,76,191,0.3)' // 0% 处的颜色
                        }, {
                            offset: 1,
                            color: 'rgba(10,76,191,0)' // 100% 处的颜色
                        }],
                        globalCoord: false // 缺省为 false
                    },
                    opacity: 1,
                }
            },

        }, {
            yAxisIndex: 1,
            name: '环比',
            type: 'line',
            symbol: 'circle',
            symbolSize: 3,
            width: 1,
            smooth: false,
            data: data_s2,
            label: {
                normal: {
                    show: false,
                }
            },
            lineStyle: {
                normal: {
                    color: colors[1][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[1][0],
                    opacity: 1,
                }
            },

        }]
    };

    chart.setOption(option);

}


// 
// 本公司 安全不正常事件 环比
var trendSecurityAbnormalEvents = [];

function setChartTrendSecurityAbnormalEvents() {

    trendSecurityAbnormalEvents = [];


    var ycode = 'ALL';
    for (var yc in companyYshcode2Code) {
        if (companyYshcode2Code[yc] == current_company_code) {
            ycode = yc;
        }
    }

    var loadingInProgress = 0;



    if (date_type == 'D') {
        for (var i = 0; i < 8; i++) {

            var date = new Date();
            var startTime_ts = date.getTime() - 86400000 * i;
            date.setTime(startTime_ts);

            var mm = date.getMonth() + 1;
            var dd = date.getDate();
            if (mm < 10) {
                mm = '0' + mm;
            }
            if (dd < 10) {
                dd = '0' + dd;
            }

            var datestr = date.getFullYear() + '' + mm + '' + dd;
            var startTime = date.getFullYear() + '-' + mm + '-' + dd + ' 00:00:00';
            var endTime = date.getFullYear() + '-' + mm + '-' + dd + ' 23:59:59';

            loadDat(datestr, startTime, endTime);

        }
    } else if (date_type == 'L') {
        var len = weekDateRangeList.length;
        len = Math.min(len, 8);
        for (var i = 0; i < len; i++) {
            var wdat = weekDateRangeList[i];

            var datestr = wdat.date //'第'+wdat.weeknum+'周'; 
            var startTime = wdat.range[0] + ' 00:00:00';
            var endTime = wdat.range[1] + ' 23:59:59';

            loadDat(datestr, startTime, endTime);

        }
    } else if (date_type == 'M') {
        var len = 8;
        for (var i = 0; i < len; i++) {

            var date = new Date();
            var day1 = moment([date.getFullYear(), date.getMonth(), 1]);
            var day2 = moment();

            if (i > 0) {
                day1 = day1.subtract(i, 'months');
                var days = day1.daysInMonth();
                day2 = moment([day1.format('YYYY'), Number(day1.format('M')) - 1, days]);
            }

            var datestr = day1.format('YYYYMM')
            var startTime = day1.format('YYYY-MM-DD') + ' 00:00:00';
            var endTime = day2.format('YYYY-MM-DD') + ' 23:59:59';

            loadDat(datestr, startTime, endTime);
        }
    }


    function loadDat(date, startTime, endTime) {
        loadingInProgress++
        var param = {
            "companyCode": ycode,
            "startTime": startTime,
            "endTime": endTime,
        }

        $.ajax({
            type: 'post',
            url: "/bi/web/findEventStatic",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {
                loadingInProgress--
                var r = response.result;
                trendSecurityAbnormalEvents.push({
                    date: date,
                    count: r ? r.eventCount : 0
                });

                checkLoaded()
            },
            error: function () { }
        });
    }

    function checkLoaded() {
        if (loadingInProgress == 0) {
            setChartTrendSecurityAbnormalEvents2();
        }
    }

}

function setChartTrendSecurityAbnormalEvents2() {

    // ---------------------------
    var chart_id1 = 'chart_' + date_type.toLowerCase() + '_m_t2_3';
    if ($('#' + chart_id1).length == 0) {
        return;
    }
    var colors = [
        ['#ffff00', '#ffff00'], //柱状图渐变颜色
        ['#5aff5c', '#5aff5c'], //柱状图渐变颜色
    ];
    // ---------------------------

    var xAxisData = [];
    var data_s1 = [];

    trendSecurityAbnormalEvents.sort(function (a, b) {
        return a.date - b.date
    });

    var len = trendSecurityAbnormalEvents.length;
    for (var i = 0; i < len; i++) {
        var val = trendSecurityAbnormalEvents[i];
        if (date_type == 'D') {
            xAxisData.push(val.date);
        } else if (date_type == 'L') {
            var week = Number(val.date.substr(4, 3));
            xAxisData.push('第' + week + '周');
        } else if (date_type == 'M') {
            var date = val.date
            xAxisData.push(date);
        }
        data_s1.push({
            value: val.count
        });
    }


    // ---------------------------------------------------------
    // 主KPI图表
    var chart = echarts.init(document.getElementById(chart_id1));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                return params.name + ': ' + params.value;
            },

        },
        legend: {
            show: true,
            orient: 'horizontal',
            top: 10,
            left: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '安全不正常事件量',
                icon: 'circle',
            },]
        },
        grid: {
            top: 50,
            left: 55,
            right: 25,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
                formatter: function (value, index) {
                    return chartDateFormatter(value, index);
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 10 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 10 + fontSizeDiff(),
                },
                formatter: '{value}'
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        },],
        series: [{
            yAxisIndex: 0,
            name: '安全不正常事件量',
            type: 'line',
            symbol: 'circle',
            symbolSize: 3,
            width: 0.3,
            smooth: false,
            data: data_s1,
            label: {
                normal: {
                    show: false,
                }
            },
            lineStyle: {
                normal: {
                    color: colors[0][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[0][0],
                    opacity: 1,
                }
            },
            areaStyle: {
                normal: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0,
                            color: 'rgba(10,76,191,0.3)' // 0% 处的颜色
                        }, {
                            offset: 1,
                            color: 'rgba(10,76,191,0)' // 100% 处的颜色
                        }],
                        globalCoord: false // 缺省为 false
                    },
                    opacity: 1,
                }
            },

        }]
    };

    chart.setOption(option);

}



// 各公司 预估收入 环比
function setChartCompsEstInc() {

    // ---------------------------
    var chart_id1 = 'chart_' + date_type.toLowerCase() + '_m_t1_4';
    if ($('#' + chart_id1).length == 0) {
        return;
    }
    var colors = [
        ['#fb91ff', '#fb91ff'], //柱状图渐变颜色
        ['#50ff50', '#50ff50'], //柱状图渐变颜色
    ];
    // ---------------------------

    var xAxisData = [];
    var data_s1 = [];
    var data_s2 = [];

    var date = getCurrentDate();
    let currentDate = new Date(date);
    let now = new Date();
    if ('D' == date_type || 'M' == date_type) {
        date = date.replace(/\-/g, "");
    } else if ('L' == date_type) {
        var weekNum = Number(date.substr(4, 3));
        date = weekMaps[weekNum];
    }

    var len = companylist.length;
    for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        var compcode = dat.code;
        if (compcode != parent_company) {
            var val1 = 0;
            var hb = 0;

            if (all_company_data['kpi_value_d'][compcode]) {
                // var val1 = all_company_data['kpi_value_d'][compcode]['EST_INC_FUEL'][date_type][latestDate];
                // var hb = all_company_data['kpi_ratio_sq_d'][compcode]['EST_INC_FUEL'][date_type][latestDate];
                if (date_type == "D") {

                    if (all_company_data['kpi_value_d'][compcode]['EST_INC_FUEL'][date_type][date] == undefined) {
                        val1 = all_company_data['kpi_value_d'][compcode]['EST_INC_FUEL'][date_type][Number(date) - 2 + ""];
                        hb = all_company_data['kpi_ratio_sq_d'][compcode]['EST_INC_FUEL'][date_type][Number(date) - 2 + ""];
                    } else {
                        val1 = all_company_data['kpi_value_d'][compcode]['EST_INC_FUEL'][date_type][date];
                        hb = all_company_data['kpi_ratio_sq_d'][compcode]['EST_INC_FUEL'][date_type][date];
                    }

                } else {
                    val1 = all_company_data['kpi_value_d'][compcode]['EST_INC_FUEL'][date_type][date];
                    hb = all_company_data['kpi_ratio_sq_d'][compcode]['EST_INC_FUEL'][date_type][date];
                }

                if (val1 == undefined) {
                    val1 = 0
                }
                if (hb == undefined) {
                    hb = 0;

                }
            } else {
                val1 = 0;
                hb = 0;
            }
            xAxisData.push(dat.code);
            data_s1.push({
                value: Math.round(val1)
            });
            data_s2.push({
                value: trimDecimal(hb * 100, 2)
            });
        }
    }


    // ---------------------------------------------------------
    // 主KPI图表
    var chart = echarts.init(document.getElementById(chart_id1));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                return params.name + ': ' + params.value;
            },

        },
        legend: {
            show: true,
            orient: 'horizontal',
            top: 10,
            left: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            selected: {
                '环比': false
            },
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '预估收入（万元）',
                icon: 'circle',
            }, {
                name: '环比',
                icon: 'circle',
            }]
        },
        grid: {
            top: 50,
            left: 55,
            right: 55,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 10 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 10 + fontSizeDiff(),
                },
                formatter: '{value}'
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }, {
            type: 'value',
            name: '',
            nameLocation: 'end',
            position: 'right',
            //min: 0,
            //max: 100,
            //interval: 10,
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 10 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 10 + fontSizeDiff(),
                    align: 'right',
                },
                margin: 30,
                formatter: '{value}%',
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ['rgba(69,165,246,0.1)'] // 分割线颜色
                }
            }
        }],
        series: [{
            yAxisIndex: 0,
            name: '预估收入（万元）',
            type: 'line',
            symbol: 'circle',
            symbolSize: 3,
            width: 0.3,
            smooth: true,
            data: data_s1,
            label: {
                normal: {
                    show: false,
                }
            },
            lineStyle: {
                normal: {
                    color: colors[0][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[0][0],
                    opacity: 1,
                }
            },
            areaStyle: {
                normal: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0,
                            color: 'rgba(10,76,191,0.3)' // 0% 处的颜色
                        }, {
                            offset: 1,
                            color: 'rgba(10,76,191,0)' // 100% 处的颜色
                        }],
                        globalCoord: false // 缺省为 false
                    },
                    opacity: 1,
                }
            },

        }, {
            yAxisIndex: 1,
            name: '环比',
            type: 'line',
            symbol: 'circle',
            symbolSize: 3,
            width: 1,
            smooth: false,
            data: data_s2,
            label: {
                normal: {
                    show: false,
                }
            },
            lineStyle: {
                normal: {
                    color: colors[1][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[1][0],
                    opacity: 1,
                }
            },

        }]
    };

    currentDate.toDateString() == now.toDateString() && 'D' == date_type ? chart.dispose() : chart.setOption(option);

}



// 本航司 预估收入 环比
function setChartTrendEstInc() {

    // ---------------------------
    var chart_id1 = 'chart_' + date_type.toLowerCase() + '_m_t2_4';
    if ($('#' + chart_id1).length == 0) {
        return;
    }
    var colors = [
        ['#fb91ff', '#fb91ff'], //柱状图渐变颜色
        ['#50ff50', '#50ff50'], //柱状图渐变颜色
    ];
    // ---------------------------

    var xAxisData = [];
    var data_s1 = [];


    var date_list = [];
    var lst = all_company_data['kpi_value_d'][current_company_code]['EST_INC_FUEL'][date_type];
    var cnt = 0;
    var tempLst = [];
    for (var d in lst) {
        tempLst.push(d);
    }

    for (var i = tempLst.length - 1; i > -1; i--) {
        date_list.push(tempLst[i]);
        cnt++;
        if (cnt >= 8) {
            break;
        }
    }

    /////


    var len = companylist.length;
    for (var j = date_list.length - 1; j > -1; j--) {
        var date = date_list[j];

        if (date_type == 'D') {
            xAxisData.push(date);
        } else if (date_type == 'L') {
            // Week: *********
            //var label = date.substr(0,4) + '年第' + Number(date.substr(4,3)) + '周';
            //20170419 双双要求：显示的周数+1
            var label = '第' + (Number(date.substr(4, 3))) + '周 '; //+ response[date];
            xAxisData.push(label);
        } else if (date_type == 'M') {
            var label = date.substr(2, 2) + '/' + date.substr(4, 2);
            xAxisData.push(label);
        }

        var val1 = all_company_data['kpi_value_d'][current_company_code]['EST_INC_FUEL'][date_type][date];

        data_s1.push({
            value: val1
        });

    }


    // ---------------------------------------------------------
    // 主KPI图表
    var chart = echarts.init(document.getElementById(chart_id1));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                return params.name + ': ' + params.value;
            },

        },
        legend: {
            show: true,
            orient: 'horizontal',
            top: 10,
            left: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '预估收入（万元）',
                icon: 'circle',
            }, {
                name: '环比',
                icon: 'circle',
            }]
        },
        grid: {
            top: 50,
            left: 55,
            right: 25,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
                formatter: function (value, index) {
                    return chartDateFormatter(value, index);
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 10 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 10 + fontSizeDiff(),
                },
                formatter: '{value}'
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        },

        ],
        series: [{
            yAxisIndex: 0,
            name: '预估收入（万元）',
            type: 'line',
            symbol: 'circle',
            symbolSize: 3,
            width: 0.3,
            smooth: false,
            data: data_s1,
            label: {
                normal: {
                    show: false,
                }
            },
            lineStyle: {
                normal: {
                    color: colors[0][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[0][0],
                    opacity: 1,
                }
            },
            areaStyle: {
                normal: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0,
                            color: 'rgba(10,76,191,0.3)' // 0% 处的颜色
                        }, {
                            offset: 1,
                            color: 'rgba(10,76,191,0)' // 100% 处的颜色
                        }],
                        globalCoord: false // 缺省为 false
                    },
                    opacity: 1,
                }
            },

        },

        ]
    };

    chart.setOption(option);

}


function switchTab(id) {
    $('.block_mid .tabc').addClass("hide");
    $('.block_mid .tabc' + id).removeClass("hide");

    if (id == 1) {
        setChartCompsFltAndNormalrate();
        setChartCompsAbnormalEvents();
        setChartCompsSecurityAbnormalEvents();
        setChartCompsEstInc();
    } else {
        setChartTrendFltAndNormalrate();
        setChartTrendAbnormalEvents();
        setChartTrendSecurityAbnormalEvents();
        setChartTrendEstInc();
    }
}
$('.block_mid .tab').on('click', function (evt) {
    evt.stopPropagation();
    $('.block_mid .tab').removeClass('selected');
    $(this).addClass('selected');
    var id = $(this).attr('data-id');
    switchTab(id)
})

