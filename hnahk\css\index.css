.page-wrapper {
  position: absolute;
  top: 0px;
  width: 2688px;
  height: 790px;
  overflow-x: hidden;
  overflow-y: hidden;
}


#page-bgimg {
  position: absolute;
  top: 0px;
  width: 100%;
  height: 100%;
  background: url(../img/indexbg.png?2) no-repeat 0 0;
  overflow-x: hidden;
  overflow-y: hidden;
}

.con_flex_column{ display: -webkit-flex; -webkit-flex-flow: column;}
.con_flex_row{ display: -webkit-flex; -webkit-flex-flow: row;}
.flex_none{-webkit-flex:none;}
.flex_1{-webkit-flex:1;}


.pagetitle {
  position: absolute; 
  top: 12px; 
  left: 1048px; 
  width: 600px;
  height: 60px;
  line-height: 60px;
  font-size: 34px;
  font-weight: 500;
  text-align: center;
}
.pagetitle .tt {
  display: inline-block;
  height: 100%;
  padding-left: 150px;
  background: url(../img/logo.png?0) no-repeat left center ;
}

.bigtitle {
  text-align: center;
  font-size: 24px;
  width: 300px;
  height: 26px;
  font-weight: 500;
}
#col_left .bigtitle {
  position: absolute;
  top: -104px;
  left: 260px; 
}
#col_right .bigtitle {
  position: absolute; 
  top: -63px; 
  left: 260px; 
}

.con{
  position: absolute; 
  top: 0px; 
  left: 0px; 
  display: block; 
  width: 100%; 
  height: 100%;
}

.sectitle {
  font-size: 18px;
  color: white;
  font-weight: bold;
}

#top_l {
  position: absolute;
  top: 63px;
  left: 225px;
  width: 500px;
  height: 70px;
  text-align: center;

  background: rgba(255,0,0,0);
}


/* ------------- col_left ----------- */

#col_left  {
  position: absolute;
  top: 163px;
  left: 65px;
  width: 819px;
  height: 570px;

  background: rgba(255,0,0,0);
}

#col_left .blk {
  width: 100px;
  height: 50px;
  margin: 0 10px;
  background: rgba(255,0,0,0);
}
#col_left .blk .tm {
  height: 16px;
  line-height: 16px;
  margin: 0;
  display: inline-block;
  float: left;
}
#col_left .blk .da {
  display: inline-block;
  float: left;
  margin-top: 4px;
}

#col_left .yt{
  position: absolute;
  left: 83px;
  top: -2px;
}

#col_left .oz{
  position: absolute;
  left: 83px;
  top:104px;
}

#col_left .az{
  position: absolute;
  left: 83px;
  top:211px;
}

#col_left .bm{
  position: absolute;
  left: 83px;
  top:315px;
}

#col_left .rus{
  position: absolute;
  left: 83px;
  top:420px;
}
#col_left .content{
  text-align: center;
  cursor: pointer;
}
#col_left .content .content_up{
  position: absolute;
  width: 113px;
  height: 45px;
  border-bottom: 1px solid #04569c;
  top: 53px;
  left: 30px;
}
#col_left .content .content_down{
  position: absolute;
  width: 113px;
  height: 45px;
  top: 103px;
  left: 30px;
}
#col_left .content .pic_content{
  position: absolute;
  top: 121px;
  left: -27px;
}

#col_left .gjth {
  position: absolute;
  left: 458px;
  top: -21px;
}

#col_left .gnth {
  position: absolute;
  left: 291px;
  top: 76px;
}

#col_left .hb {
  position: absolute;
  left: 291px;
  top: 269px;
}
#col_left .hx {
  position: absolute;
  left: 458px;
  top: 366px;
}
#col_left .ztzcl {
  position: absolute;
  left: 623px;
  top: 269px;
}
#col_left .plane {
  position: absolute;
  left: 623px;
  top: 76px;
}

#col_left .pic_gjthcs{
  width: 68px;
  height: 68px;
  position: absolute;
  top: 57px;
  left: -39px;
  background: url(../img/pic_gjthcs.png?0) no-repeat left center ;
}
#col_left .pic_gnthcs{
  width: 68px;
  height: 68px;
  position: absolute;
  top: 57px;
  left: -39px;
  background: url(../img/pic_gnthcs.png?0) no-repeat left center ;
}
#col_left .pic_hb{
  width: 68px;
  height: 68px;
  position: absolute;
  top: 45px;
  left: -39px;
  background: url(../img/pic_hb.png?0) no-repeat left center ;
}
#col_left .pic_hx{
  width: 68px;
  height: 68px;
  position: absolute;
  top: 50px;
  left: -39px;
  background: url(../img/pic_hx.png?0) no-repeat left center ;
}
#col_left .pic_ztzcl{
  width: 68px;
  height: 68px;
  position: absolute;
  top: 50px;
  left: -39px;
  background: url(../img/pic_ztzcl.png?0) no-repeat left center ;
}
#col_left .pic_plane{
  width: 68px;
  height: 68px;
  position: absolute;
  top: 58px;
  left: -30px;
  background: url(../img/pic_plane.png?0) no-repeat left center ;
}

#col_left .row1 {
  width: 100%;
  height: 180px;

  background: rgba(255,0,0,0);
}

#col_left .col {
  position: relative;
  display: inline-block;
}

#col_left .row1 .col1 {
  width: 270px;
  height: 100%;

  background: rgba(255,0,0,0);
}

#col_left .row1 .col1 .chart {
  position: absolute;
  left: 42px;
}

#col_left .row1 .col2 {
  width: 270px;
  height: 100%;

  background: rgba(255,0,0,0);
}
#col_left .row1 .col2 .sectitle {
  position: absolute;
  left: 77px;
  top: 37px;
}
#col_left .row1 .col2 .lb {
  position: absolute;
  left: 108px;
  top: 86px;
}

#col_left .row1 .col3 {
  height: 100%;

}
#col_left .row1 .col3 .chart {
  position: absolute;
  width: 150px;
  height: 150px;
  top: 9px;
  left: 60px;
  background: rgba(255,0,0,0);
}


#col_left .row2 {
  width: 100%;
  height: 392px;

  background: rgba(255,0,0,0);
}

#col_left .row2 .col1 {
  width: 412px;
  height: 100%;

  background: rgba(255,0,0,0);
}
#col_left .row2 .col2 {
  height: 100%;

}




/* ------------- col_mid ----------- */

#col_mid  {
  position: absolute;
  left: 900px;
  top: 110px;
  width: 900px;
  height: 780px;

}
#col_mid .tabs {
  position: absolute;
  left: 209px;
  width: 600px;
  height: 40px;
}
#col_mid .tabs .tab {
  position: relative;
  display: inline-block;
  height: 40px;
  line-height: 40px;
  font-size: 18px;
  font-weight: bold;
  padding: 3px 40px 0 20px;
  cursor: pointer;
  color: #178df5;
  pointer-events: auto;
}
#col_mid .tabs .tab.selected {
  color: #FFF;
  background: url(../img/tabbg.png) no-repeat 0 0;
}

#col_mid .tabc1 {
  position: absolute;
  left: 0px;
  top: 50px;
  width: 100%;
  height: 100%;
  background: url(../img/col_mid_tabc1.png) no-repeat 12px 34px;
}
#col_mid .tabc2 {
  position: absolute;
  left: 0px;
  top: 50px;
  width: 100%;
  height: 100%;
  background: url(../img/col_mid_tabc2.png) no-repeat 12px 34px;
}
#col_mid .tabc3 {
  position: absolute;
  left: 0px;
  top: 50px;
  width: 100%;
  height: 100%;
  background: url(../img/col_mid_tabc3.png) no-repeat 12px 34px;
}

#col_mid .legend .itm {
  color: #2583c7;
}
#col_mid .legend .itm span {
  display: inline-block;
  vertical-align: middle;
  margin-right: 3px;
}

#col_mid .tabc1 .legend .dot{
  width: 8px;
  height: 8px;
  border-radius: 4px;
}

#col_mid .tabc1 .legend .itm1 .dot{
  background: #1781c2;
background: -moz-radial-gradient(center, ellipse cover,  #ffffff 0%, #1781c2 100%);
background: -webkit-radial-gradient(center, ellipse cover,  #ffffff 0%,#1781c2 100%);
background: radial-gradient(ellipse at center,  #ffffff 0%,#1781c2 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#1781c2',GradientType=1 );
}

#col_mid .tabc1 .legend .itm2 .dot {
  background: #efb835;
background: -moz-radial-gradient(center, ellipse cover,  #ffffff 0%, #efb835 100%);
background: -webkit-radial-gradient(center, ellipse cover,  #ffffff 0%,#efb835 100%);
background: radial-gradient(ellipse at center,  #ffffff 0%,#efb835 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#efb835',GradientType=1 );
}

#col_mid .tabc2 .legend .dot{
  width: 12px;
  height: 2px;
}

#col_mid .tabc2 .legend .itm1 .dot{
  background: #c4fc48;
}

#col_mid .tabc2 .legend .itm2 .dot {
  background: #ffba00;
}

#col_mid .tabc3 .complst .itm {
  margin-bottom: 8px;
}

#col_mid .tabc3 .legend .itm {
  display: inline-block;
  width: 60px;
  height: 50px;
  text-align: center;
}

#col_mid .tabc3 .legend .dot{
  width: 12px;
  height: 12px;
  border-radius: 6px;
  display: inline-block;

}

#col_mid .tabc3 .legend .itm1 .dot{
  background: #68f13e;
background: -moz-radial-gradient(center, ellipse cover,  #ffffff 0%, #68f13e 100%);
background: -webkit-radial-gradient(center, ellipse cover,  #ffffff 0%,#68f13e 100%);
background: radial-gradient(ellipse at center,  #ffffff 0%,#68f13e 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#68f13e',GradientType=1 );
}

#col_mid .tabc3 .legend .itm2 .dot {
  background: #f9e897;
background: -moz-radial-gradient(center, ellipse cover,  #ffffff 0%, #f9e897 100%);
background: -webkit-radial-gradient(center, ellipse cover,  #ffffff 0%,#f9e897 100%);
background: radial-gradient(ellipse at center,  #ffffff 0%,#f9e897 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#f9e897',GradientType=1 );
}

#col_mid .tabc3 .legend .itm3 .dot{
  background: #fa7e16;
background: -moz-radial-gradient(center, ellipse cover,  #ffffff 0%, #fa7e16 100%);
background: -webkit-radial-gradient(center, ellipse cover,  #ffffff 0%,#fa7e16 100%);
background: radial-gradient(ellipse at center,  #ffffff 0%,#fa7e16 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#fa7e16',GradientType=1 );
}

#col_mid .tabc3 .legend .itm4 .dot{
  background: #ff5d55;
background: -moz-radial-gradient(center, ellipse cover,  #ffffff 0%, #ff5d55 100%);
background: -webkit-radial-gradient(center, ellipse cover,  #ffffff 0%,#ff5d55 100%);
background: radial-gradient(ellipse at center,  #ffffff 0%,#ff5d55 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#ff5d55',GradientType=1 );
}

#col_mid .tabc3 .compmap{
  position: absolute;
  top: 24px;
  left: 172px;
  width: 544px;
  height: 544px;
  pointer-events: auto;
}

#col_mid .tabc3 .compmap .itm{
  position: absolute;
  width: 32px;
  height: 32px;
  background-size: 32px;
  background-repeat: no-repeat;
}

#col_mid .tabc3 .complst{
  height: 340px;
  overflow: hidden;
}

#col_mid .bar {
  position: relative;
  display: inline-block;
  height: 8px;
  vertical-align: middle;
}
#col_mid .innerbar {
  position: absolute;
  display: block;
  height: 8px;
}
#col_mid .greenbar {
  background: #27b0fe;
}
#col_mid .bluebar {
  background: #022857;
}
#col_mid .darkbar {
  background: #022857;
  width: 50px;
  margin: 0 2px;
  overflow: hidden;
}
#col_mid .baritmrow {
  margin-bottom: 3px;
}
#col_mid .baritmrow .acno {
  color: #fff;
  padding-right: 3px;
  font-size: 12px;
}
#col_mid .baritmrow .val {
  display: inline-block;
  font-size: 12px;
  width: 20px;
}
#col_mid .baritmrow .val_l {
  text-align: right;
}



/* ------------- col_right ----------- */

#col_right  {
  position: absolute;
  top: 123px;
  left: 1809px;
  width: 826px;
  height: 618px;

  background: rgba(255,0,0,0);
}

#col_right .col1 {
  position: absolute;
  width: 426px;
  height: 100%;
  z-index: 10;
}

#comp_table .table{
  position: absolute;
  width: 100%;
  height: 100%;
}
#comp_table .c{
  display: inline-block;
  height: 42px;
  line-height: 42px;
  overflow: hidden;
}
#comp_table .head{
  width: 100%;
  font-size: 14px;
  font-weight: bold;
}
#comp_table .head .c{
  background-color: rgba(36,85,142,0.5);
  height: 32px;
  line-height: 32px;
}
#comp_table .rw{
  width: 100%;
  cursor: pointer;
}
#comp_table .rw.alt .c{
  background-color: rgba(29,60,109,0.5);
}
#comp_table .rw:hover .c{
  background-color: rgba(29,60,109,0.3);
}
#comp_table .selected{
  color: #013c9e;
  font-weight: bold;
  font-size: 16px;
}
#comp_table .selected.alt .c{
  background-color: rgba(0,0,0,0) !important;
}
#comp_table .selected .blue1{
  color: #013c9e !important;
}

#comp_table .selected_bg{
  position: absolute;
  width: 437px;
  height: 66px;
  background: url(../img/table_row_selected.png?0) no-repeat left center;
  z-index: 0;
  top: 63px;
  transition: all 0.2s ease-out;
}

#comp_table .c1{
  width: 100px;
  padding-left: 20px;
}
#comp_table .c2{
  width: 110px;
}
#comp_table .c3{
  width: 100px;
}
#comp_table .c4{
  width: 92px;
}

#col_right .col2 {
  position: absolute;
  left: 402px;
  width: 423px;
  height: 100%;
  background: rgba(255,0,0,0);
  z-index: 9;
}

#col_right .col2 .row1 {
  margin-top: 23px;
  width: 100%;
  height: 55px;
  text-align: center;
}
#col_right .col2 .row1 .complogo {
  display: inline-block;
  max-height: 55px;
  width: auto;
  vertical-align: middle;
}
#col_right .col2 .row1 .compname {
  display: inline-block;
  line-height: 60px;
  font-weight: bold;
  font-size: 18px;
  vertical-align: middle;
  margin-left: 3px;
}

#col_right .col2 .row2 {
  position: relative;
  top: 18px;
  width: 98%;
  height: 78%;
}
#col_right .col2 .row2 ul{
  padding-left: 31px;
  overflow-y: auto;
  height: 100%;
}
#col_right .col2 .row2 .plan_scroll::-webkit-scrollbar{
  background-color: #00112b;
  border: rgba(0, 0, 0, 0);
  border-radius: 5px;
  width: 5px;
  height: 80%;
}
#col_right .col2 .row2 .plan_scroll::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background: #00a9ff;
}
#col_right .col2 .row2 .plan_scroll .plane_pic{
  width: 281px;
  height: 74px;
  position: relative;
  left: 40px;
  background: url(../img/plane_pic.png?0) no-repeat left center;
}
#col_right .col2 .row2 .plan_scroll .plane_modle{
  position: relative;
  right: 12px;
  top: 10px;
  text-align: right;
}
#col_right .col2 .row2 .plan_scroll .plane_num{
  position: relative;
  width: 30%;
  top: 41px;
  left: 36px;
}
#col_right .col2 .row2 .plan_scroll .plane_rate{
  position: relative;
  top: 10px;
  right: 59px;
  text-align: right;
}
#col_right .col2 .row2 ul>li {
  list-style-type:none;
  margin-bottom: 10px;
}

#col_right .col2 .row2 .plane{
  width: 362px;
  height: 181px;
  background: url(../img/planebg.png?0) no-repeat left center;
}
#col_right .col2 .row2 .chart {
  position: absolute;
  left: 35px;
  top: 19px;
}
#col_right .col2 .row2 table {
  position: absolute;
  width: 216px;
  height: 100%;
  right: 0;
  font-size: 12px;
  text-align: center;
}
#col_right .col2 .row2 table th{
  font-weight: bold;
  border-bottom: 1px solid #04569c;
  text-align: center;
  height: 36px;
  padding: 0 3px;
}
#col_right .col2 .row2 table td{

}
#col_right .col2 .row2 table .clr{
  background-color: #013067;
}
#col_right .col2 .row2 table .hc{
  background-color: rgba(22,61,108,0.3);
}
#col_right .col2 .row2 table .rb{
  border-right: 1px solid #04569c;
}


#col_right .col2 .row3 {
  position: relative;
  width: 100%;
}
#col_right .col2 .row3 .bar {
  position: absolute;
  width: 384px;
  height: 25px;
  top: 60px;
  left: 18px;
  background-color: #054bad;
  font-weight: bold;
}
#col_right .col2 .row3 .barin {
  position: absolute;
  height: 100%;
  background-color: #95cefd;
}
#col_right .col2 .row3 .bar .lb1 {
  position: absolute;
  line-height: 25px;
  left: 10px;
  color: #080f29;
}
#col_right .col2 .row3 .bar .lb2 {
  position: absolute;
  line-height: 25px;
  right: 10px;
  text-align: right;
}
#col_right .col2 .row3 .rate1 {
  position: absolute;
  color: #95cefd;
  top: 90px;
  left: 20px;
}
#col_right .col2 .row3 .rate2 {
  position: absolute;
  text-align: right;
  color: #054bad;
  top: 90px;
  right: 26px;
}
#col_right .col2 .row3 .chart {
  position: absolute;
  left: 0px;
  top: 130px;
  width: 100%;
  height: 226px;
  background: rgba(255,0,0,0);
}





