(function () {
    $(".unsafeEvent .reason .detail").on("click", function () {
        $("#pop_unsafe_detail").removeClass("hide");
        $(".windowMask").removeClass("hide");

        var dateType = getDateType();
        var dateId = getDateId(getCurrentDayObj(), dateType);

        if (window.unsafeWin == null) {
            initunsafeDetailWin(dateType, dateId);
        } else {
            window.unsafeWin.refreshView(dateType, dateId);
        }
    });


    function initunsafeDetailWin(dateType, dateId) {
        var page = new Vue({
            el: '.unsafe_detail-win-body',
            template: $("#unsafe_detail_template").html(),
            data: function () {
                return {
                    "dateType": dateType,
                    "weeks": weeks,
                    showWeekList: false,
                    selectedWeek: null,
                    weekCmpActive: false,
                    companyEventList: [],
                    eventType: '1',
                    allEventList: [],
                    activeEvent: {},
                    colors: ["#ca0000", "#ff0000", "#ff9f00", "#ffff00", " #ea68a2", "#8957a1"],
                    status: { "1": "调查分析中", "2": "落实措施中", "3": "已关闭" },
                    activeEventReason: null,
                    types: [
                        { name: "人为", color: '#ca0000' },
                        { name: "机械", color: '#ff0000' },
                        { name: "天气", color: '#ff9f00' },
                        { name: "意外", color: '#ffff00' },
                        { name: "代理", color: '#ea68a2' },
                        { name: "其他", color: '#8957a1' }
                    ]
                }
            },
            mounted: function () {
                //日
                var me = this;

                if (this.weeks.length > 1) {
                    this.selectedWeek = weeks[1]
                }


                $(me.$refs["datetimepicker_D"]).datetimepicker({
                    defaultDate: new Date(),
                    format: "YYYY/MM/DD",
                    sideBySide: true,
                    maxDate: new Date(),
                    widgetPositioning: {
                        horizontal: 'right'
                    },
                    locale: 'zh-cn'
                });
                //月
                $(me.$refs['datetimepicker_M']).datetimepicker({
                    defaultDate: new Date(),
                    format: "YYYY/MM",
                    viewMode: 'months',
                    sideBySide: true,
                    maxDate: new Date(),
                    widgetPositioning: {
                        horizontal: 'right'
                    },
                    locale: 'zh-cn'
                });
                //年
                $(me.$refs['datetimepicker_Y']).datetimepicker({
                    defaultDate: new Date(),
                    format: "YYYY",
                    viewMode: 'years',
                    sideBySide: true,
                    maxDate: new Date(),
                    widgetPositioning: {
                        horizontal: 'right'
                    },
                    locale: 'zh-cn'
                });


                me.setDatePickerValue(dateType, dateId);
                //先查数据,再做下面事件监听,不然可能会触发dp.change事件,多做一次请求

                $(me.$refs["companyList"]).niceScroll({
                    cursorcolor: "#1b5092",//#CC0071 光标颜色
                    cursoropacitymax: 1, //改变不透明度非常光标处于活动状态（scrollabar“可见”状态），范围从1到0
                    touchbehavior: false, //使光标拖动滚动像在台式电脑触摸设备
                    cursorwidth: "8px", //像素光标的宽度
                    cursorborder: "0px solid #fff",
                    cursorborderradius: "3px",//以像素为光标边界半径
                    autohidemode: false, //是否隐藏滚动条
                    background: 'rgba(27, 80, 146, 0.3)'
                });

                me.queryData(me.getDate());

                $(me.$refs["datetimepicker_D"]).on("dp.change", function (e) {
                    me.queryData(e.date._d);

                });
                $(me.$refs['datetimepicker_M']).on("dp.change", function (e) {
                    me.queryData(e.date._d);
                });
                $(me.$refs['datetimepicker_Y']).on("dp.change", function (e) {
                    me.queryData(e.date._d);
                });
                me.mounted = true;


            },
            methods: {
                setDatePickerValue(dateType, dateId) {
                    var me = this;
                    if (dateType != 'L') {
                        $(me.$refs[`datetimepicker_${dateType}`]).data("DateTimePicker").date(me.getDateStr(dateType, dateId));
                    } else {
                        var selectedWeek = me.weeks.find(v => { return v.DATE_ID == dateId });
                        if (me.selectedWeek != selectedWeek) {
                            me.selectedWeek = selectedWeek;
                        }
                    }
                    if (me.mounted) {
                        me.queryData();
                    }
                },
                refreshView(dateType, dateId) {
                    var me = this;
                    me.dateType = dateType;
                    me.setDatePickerValue(dateType, dateId);
                },
                getDateStr(dateType, dateId) {
                    if (dateType == 'D') {
                        return moment(dateId, 'YYYYMMDD').format('YYYY/MM/DD');
                    } else if (dateType == 'M') {
                        return moment(dateId, 'YYYYMM').format('YYYY/MM');;
                    } else if (dateType == 'Y') {
                        return dateId;
                    }
                },
                getWeekDesc() {
                    return this.selectedWeek ? this.selectedWeek.DATE_DESC_XS : ''
                },
                onWeekMouseOut() {
                    this.weekCmpActive = false;
                    setTimeout(this.validActive, 300)
                },
                onWeekMouseOver() {
                    this.weekCmpActive = true;
                },
                validActive() {
                    if (!this.weekCmpActive) {
                        this.showWeekList = false;
                    }
                },
                dropWeekList() {
                    this.showWeekList = true;
                },
                onWeekChange(week) {
                    this.selectedWeek = week;
                    this.showWeekList = false;
                    this.queryData(week);

                },
                getWeekLabel: function (item) {
                    if (item) {
                        var week = item.DATE_ID.substring(5, 7);
                        var year = item.DATE_ID.substring(0, 4);
                        return `${year}年第${week}周`;
                    }
                },
                closeWin: function () {
                    // window.unsafeWin = null;
                    $("#pop_unsafe_detail").addClass("hide");
                    // $("#pop_unsafe_detail .unsafe_detail-win-body").html("");
                    $(".windowMask").addClass("hide");
                },
                switchDateType(dateType) {
                    this.dateType = dateType;
                    var date = this.getDate();
                    this.queryData(date);
                },
                getDate() {
                    var me = this;
                    var dateType = this.dateType;
                    if (dateType == 'D') {
                        return $(me.$refs['datetimepicker_D']).data("DateTimePicker").date().startOf("day")._d;
                    } else if (dateType == 'L') {
                        return this.selectedWeek;
                    } else if (dateType == 'M') {
                        return $(me.$refs['datetimepicker_M']).data("DateTimePicker").date().startOf("day")._d
                    } else if (dateType == 'Y') {
                        return $(me.$refs['datetimepicker_Y']).data("DateTimePicker").date().startOf("day")._d
                    }
                },
                getCompanyYHCodes() {
                    if (isAllCompany()) {
                        let companys = [];
                        for (var p in companyYshcode2Code) {
                            if (p != 'ALL' && p != '') {
                                companys.push(p)
                            }
                        }
                        return companys;
                    } else {
                        return [getYhCode()]
                    }
                },
                getStyle(companyCodeYh) {
                    var companyCode = companyYshcode2Code[companyCodeYh];
                    return `background-image: url(./img/logo_${companyCode}.svg)`
                },
                formatTimeStr(toff) {
                    if (toff) {
                        var hour = toff.substring(0, 2);
                        var mm = toff.substring(2, 4);
                        return `${hour}:${mm}`
                    } else {
                        return '-';
                    }

                },
                formatTime(t) {
                    if (t) {

                        return moment(t).format("HH:mm")
                    } else {
                        return '-';
                    }

                },
                getTypeColor(event) {
                    for (var i = 0; i < this.types.length; i++) {
                        if (this.types[i].name == event.REASON_NAME) {
                            return this.types[i].color;
                        }
                    }
                },
                getStatusText() {
                    var me = this;
                    return me.status[me.activeEvent.STATUS]
                },
                checkIsActive(item) {
                    var me = this;
                    if (me.activeEventReason == null || me.activeEventReason == item.name) {
                        return true;
                    }
                    return false;
                },
                filterEvent(item) {
                    var me = this;
                    if (me.activeEventReason == item) {
                        //已经选中而取消过滤
                        me.processData(me.allEventList);
                        me.activeEventReason = null;
                    } else {
                        me.activeEventReason = item;
                        var companyEvents = {};
                        for (var p in me.allEventList) {
                            var eventList = me.allEventList[p].filter(i => {
                                return i.REASON_NAME == me.activeEventReason;
                            });
                            if (eventList.length > 0) {
                                companyEvents[p] = eventList;
                            }
                        }
                        me.processData(companyEvents);
                    }

                },

                hasNext(item) {
                    return item.events.length > item.idx + 4
                },
                hasPre(item) {
                    return item.idx > 0
                },
                prePage(item) {
                    if (!this.hasPre(item)) {
                        return;
                    }
                    let idx = item.idx;
                    let newIdx = idx - 4;
                    let end = idx;
                    item.sliceEvents = item.events.slice(newIdx, end);
                    item.idx = newIdx;
                },
                nextPage(item) {
                    if (!this.hasNext(item)) {
                        return;
                    }
                    let idx = item.idx;
                    let newIdx = idx + 4;
                    let end = idx + 8;
                    item.sliceEvents = item.events.slice(newIdx, end);
                    item.idx = newIdx;
                },
                getEventList(companyEvents, companyCode) {
                    var list = companyEvents[companyCode];
                    if (list) {
                        return list;
                    } else {
                        companyEvents[companyCode] = [];
                        return companyEvents[companyCode];
                    }
                },

                processData(companyEvents) {
                    var me = this;
                    var companyEventList = [];


                    for (var p in companyEvents) {
                        companyEventList.push({
                            companyCode: p,
                            companyName: companyCode2Name[companyYshcode2Code[p]],
                            emptyEvent: false,
                            events: companyEvents[p],
                            sliceEvents: companyEvents[p].slice(0, 4),
                            idx: 0
                        })
                    }

                    companyEventList.sort(function (a, b) {
                        return getCompanyIndex(companyYshcode2Code[a.companyCode]) - getCompanyIndex(companyYshcode2Code[b.companyCode])
                    });

                    if (isAllCompany()) {
                        companylist.forEach(company => {
                            var yhcompanyCode = company.yhscode;
                            if (yhcompanyCode != "" && yhcompanyCode != 'HKA') {
                                //公司为空时,也要显示,所以要把其它没有数据的公司加进来(除了香港)
                                if (!companyEvents.hasOwnProperty(yhcompanyCode)) {
                                    companyEventList.push({
                                        companyCode: yhcompanyCode,
                                        companyName: companyCode2Name[companyYshcode2Code[yhcompanyCode]],
                                        emptyEvent: true,
                                        events: [],
                                        sliceEvents: [],
                                        idx: 0
                                    })
                                }
                            }

                        });
                    } else {
                        let yhCode = getYhCode();
                        if (!companyEvents.hasOwnProperty(yhCode)) {
                            companyEventList.push({
                                companyCode: yhCode,
                                companyName: companyCode2Name[companyYshcode2Code[yhCode]],
                                emptyEvent: true,
                                events: [],
                                sliceEvents: [],
                                idx: 0
                            })
                        }
                    }

                    me.companyEventList = companyEventList;
                    me.activeEvent = {};
                    if (me.companyEventList.length > 0) {
                        if (!me.companyEventList[0].emptyEvent) {
                            me.showDetail(me.companyEventList[0].events[0]);
                        }
                    } else {
                        $(me.$refs["eventDetail"]).getNiceScroll().remove();
                    }

                    setTimeout(function () {
                        $(me.$refs["companyList"]).getNiceScroll(0).resize();
                    }, 300)

                },
                showDetail(event) {
                    var me = this;

                    var dtd = me.queryCommonet(event);
                    dtd.done(function () {

                        eking.ui.loading.show();
                        me.activeEvent = event;
                        eking.ui.loading.hide();
                        setTimeout(function () {
                            if (!$(me.$refs["eventDetail"]).getNiceScroll(0)) {
                                $(me.$refs["eventDetail"]).niceScroll({
                                    cursorcolor: "#1b5092",//#CC0071 光标颜色
                                    cursoropacitymax: 1, //改变不透明度非常光标处于活动状态（scrollabar“可见”状态），范围从1到0
                                    touchbehavior: false, //使光标拖动滚动像在台式电脑触摸设备
                                    cursorwidth: "8px", //像素光标的宽度
                                    cursorborderradius: "3px",//以像素为光标边界半径
                                    cursorborder: "0px solid #fff",
                                    autohidemode: false, //是否隐藏滚动条
                                    background: 'rgba(27, 80, 146, 0.3)'
                                });
                            }
                            else {
                                $(me.$refs["eventDetail"]).getNiceScroll(0).resize();
                                $(me.$refs["eventDetail"]).getNiceScroll(0).doScrollPos(0, 0);
                            }
                        }, 300)

                    })

                },
                hasComment(evt) {
                    if (evt && evt.comments) {
                        return evt.comments.length > 0
                    }
                    return false;
                },
                queryCommonet(evt) {
                    var params = {
                        "eventId": evt.ID
                    };

                    return $.ajax({
                        type: 'post',
                        url: "/bi/query/getsmseventcomment",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(params),
                        success: function (response) {
                            var data = response.data;// 批示数组
                            evt.comments = data;

                        }
                    });
                },

                queryData() {
                    var me = this;
                    eking.ui.loading.show();
                    var dateType = this.dateType;
                    var selectedDate = me.getDate();

                    var startTime = formatDate(getStartDate(selectedDate, dateType));
                    var endTime = formatDate(getEndDate(selectedDate, dateType))+" 23:59:59";

                    var params = {
                        "compCode": me.getCompanyYHCodes().join(","),
                        "startDate": startTime,
                        "endDate": endTime,
                        "dataType": 0
                    };
                    var d1 = $.ajax({
                        type: 'post',
                        url: "/bi/query/getsmseventdetail?unsafe-eventlist",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(params),
                        success: function (response) {
                            me.allEventList = response.data;

                            if (me.activeEventReason != null) {
                                me.filterEvent(me.activeEventReason);
                            } else {
                                me.processData(me.allEventList);
                            }


                            eking.ui.loading.hide();

                            setTimeout(function () {
                                $(me.$refs["companyList"]).getNiceScroll(0).resize();
                            }, 300)

                        }
                    });
                }

            }
        });

        window.unsafeWin = page;

    }





})()