
/**/

div {
  pointer-events: auto;
}

.page-wrapper {
  background: #06112f url(../img/a4.1_bg.jpg) no-repeat top center;
}


.pagetitle{
  position: absolute;
  width: 1306px;
  height: 59px;
  top: 65px;
  left: 30px;
  text-align: center;
  pointer-events: none;
}
.maintitle{
  color: #FFF;
  width: 100%;
  font-size: 20px;
  padding: 13px 0 0 0;
  margin: 0 auto;
  font-weight: bold;
}
.submaintitle{
  color: #FFF;
  width: 100%;
  font-size: 22px;
  padding: 0 20px;
  margin: 0 auto;
}

#main_cb_month {
  position: absolute;
  top: 13px;
  left: 1098px;
  height: 34px;
  width: 85px;
  z-index: 1000;
}
#main_cb_quarter {
  position: absolute;
  top: 13px;
  left: 1202px;
  height: 34px;
  width: 104px;
  z-index: 1001;
}

#main_cb_month .combobox_label
{
  padding: 6px 6px;
  background-image: url(../img/combobox_arr.png);
  background-repeat: no-repeat;
  background-position: 83px center;
  border-radius: 4px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}
#main_cb_quarter .combobox_label
{
  padding: 6px 6px;
  background-image: url(../img/combobox_arr.png);
  background-repeat: no-repeat;
  background-position: 92px center;
  border-radius: 4px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: none;
}

.combotab_selected .combobox_label {
  background-color: #2a81d2;
}
.combotab .combobox_label {
  background-color: #0950a2;
  opacity: 0.5;
  color: #8abfff;
}

#week_date_range {
  position: absolute;
  top: 16px;
  left: 931px;
  width: 160px;
  font-size: 14px;
  text-align: center;
  border: 1px solid #1f5fa8;
  border-radius: 2px;
  padding: 3px 0 5px 0px;
  background-color: #021d39;
  box-shadow: 0 1px 8px #021121;

  display: none;
}


/* ------ */

#maintab1 {
  position: absolute;
  top: 80px;
  left: 30px;
  width: 77px;
  height: 36px;

  border: 1px solid rgba(41,140,232,0.6);
  border-radius: 4px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;

  color: #237fd5;
  background-color: #022860;
  line-height: 34px;
  font-size: 16px;

  text-align: center;
  cursor: pointer;
  pointer-events: auto;
}

#maintab2 {
  position: absolute;
  top: 80px;
  left: 107px;
  width: 77px;
  height: 36px;
  
  border: 1px solid rgba(41,140,232,0.6);
  border-radius: 4px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: none;

  color: #237fd5;
  background-color: #022860;
  line-height: 34px;
  font-size: 16px;

  text-align: center;
  cursor: pointer;
  pointer-events: auto;
}

#maintab1.selected,
#maintab2.selected {
  color: #fff;
  background-color: #2a81d2;
}


/* --- */
#subtab1 {
  position: absolute;
  top: 66px;
  left: 256px;
  width: 66px;
  height: 66px;

  border-radius: 4px;

  color: #0557c6;
  background: #002363 url(../img/a4.1_people0s.png) no-repeat center 7px;
  font-size: 12px;
  padding-top: 45px;

  text-align: center;
  cursor: pointer;
  pointer-events: auto;

}

#subtab2 {
  position: absolute;
  top: 66px;
  left: 327px;
  width: 66px;
  height: 66px;
  
  border-radius: 4px;

  color: #0557c6;
  background: #002363 url(../img/a4.1_people1s.png) no-repeat center 7px;
  font-size: 12px;
  padding-top: 45px;

  text-align: center;
  cursor: pointer;
  pointer-events: auto;

}

#subtab1.selected {
  color: #86c4fe;
  background: #032e81 url(../img/a4.1_people0.png) no-repeat center 7px;

}
#subtab2.selected {
  color: #86c4fe;
  background: #032e81 url(../img/a4.1_people1.png) no-repeat center 7px;

}


.pagetitlebg {
  position: absolute;
  top: 46px;
  left: 0;
  width: 100%;
  height: 133px;
  background: url(../img/a4.1_titlebg.png) no-repeat center 0;
  pointer-events: none;
}

/* ------ */

.block_o {
  position: absolute;
  top: 80px;
  left: 350px;
  width: 666px;
  height: 666px;

}

#centerballbg {
  position: absolute;
  top: 130px;
  left: 129px;
  width: 407px;
  height: 409px;
}

#centerballbg.tab1{
  background-image: url(../img/a4.1_ball_1.png);
}
#centerballbg.tab2{
  background-image: url(../img/a4.1_ball_2.png);
}

#centerball {
  position: absolute;
  top: 240px;
  left: 260px;
  width: 160px;
  height: 200px;
  text-align: center;
}
#centerball .itm{
  width: 140px;
  height: 46px;
  font-size: 28px;
  font-weight: bold;
  line-height: 46px;
  pointer-events: auto;
  transform-origin: 50% 50%;
  cursor: pointer;
}
#centerball .itm.selected{
  background: -moz-linear-gradient(left,  rgba(0,20,116,0) 0%, rgba(0,20,116,1) 25%, rgba(0,20,116,1) 75%, rgba(0,20,116,0) 100%);
  background: -webkit-linear-gradient(left,  rgba(0,20,116,0) 0%,rgba(0,20,116,1) 25%,rgba(0,20,116,1) 75%,rgba(0,20,116,0) 100%);
  background: linear-gradient(to right,  rgba(0,20,116,0) 0%,rgba(0,20,116,1) 25%,rgba(0,20,116,1) 75%,rgba(0,20,116,0) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00001474', endColorstr='#00001474',GradientType=1 );
}
#centerball .itm .lb{
  width: 100%;
  height: 100%;
  padding-left: 30px;
  background: url(../img/a4.1_plane_b.png) no-repeat 10px center;
}
#centerball .itm.selected .lb{
  background: url(../img/a4.1_plane_w.png) no-repeat 10px center;
}

#dartboard {
  position: absolute;
}

#imgring {
  position: absolute;
  top: 0;
  left: 0;
  width: 666px;
  height: 666px;
  pointer-events: none;
}

#imgring .img {
  position: absolute;
  width: 150px;
  height: 150px;
  transform-origin: 50% 50%;
  background-repeat: no-repeat;
  background-position: 0 -12px;
  pointer-events: none;
}
#imgring .img .lb {
  position: absolute;
  width: 100%;
  height: 20px;
  bottom: 30px;
  pointer-events: none;
  text-align: center;
  font-size: 15px;
  font-weight: bold;
  text-shadow: 0px 0px 5px rgba(0,0,0,0.6);
}



/* ---- */

.smallpop {
  position: absolute;
  display: inline-block;
  min-height: 70px;
  pointer-events: auto;
  cursor: pointer;
}
.pop1 {
  min-width: 210px;
}
.pop2 {
  max-width: 280px;
}
.smallpop .bg{
  position: absolute;
  left: 4px;
  top: 2px;
  width: calc(100% - 8px);
  height: calc(100% - 4px);

  border-radius: 5px;
  border: 1px solid #0078f8;

  background: #0746ad;
  background: -moz-linear-gradient(top,  #0746ad 0%, #03316f 100%);
  background: -webkit-linear-gradient(top,  #0746ad 0%,#03316f 100%);
  background: linear-gradient(to bottom,  #0746ad 0%,#03316f 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#0746ad', endColorstr='#03316f',GradientType=0 );

}
.smallpop.selected .bg,
.smallpop:hover .bg{
  border: 1px solid #267efb;

  background: #004de0;
  background: -moz-linear-gradient(top,  #602f4a 0%, #0042aa 100%);
  background: -webkit-linear-gradient(top,  #602f4a 0%,#0042aa 100%);
  background: linear-gradient(to bottom,  #602f4a 0%,#0042aa 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#602f4a', endColorstr='#0042aa',GradientType=0 );

  box-shadow: 0 2px 12px #021121;

}
.smallpop .c0{
  position: absolute;
  left: 0px;
  top: 0px;
  width: 46px;
  height: 33px;
  background: url(../img/a4.1_pop_c0.png) no-repeat;
}
.smallpop .c1{
  position: absolute;
  right: 0px;
  top: 0px;
  width: 17px;
  height: 36px;
  background: url(../img/a4.1_pop_c1.png) no-repeat;
}
.smallpop .c2{
  position: absolute;
  right: 0px;
  bottom: 0px;
  width: 46px;
  height: 23px;
  background: url(../img/a4.1_pop_c2.png) no-repeat;
}
.smallpop .c3{
  position: absolute;
  left: 0px;
  bottom: 0px;
  width: 18px;
  height: 23px;
  background: url(../img/a4.1_pop_c3.png) no-repeat;
}
.smallpop table{
  position: relative;
  margin: 8px 11px;

}
.smallpop td{
  text-align: center;
  min-width: 40px;
}
.smallpop td img{
  width: 10px;
  height: 10px;
}
.smallpop .head{
  font-size: 12px;
  color: #FFF;
  padding-bottom: 6px;
  font-weight: bold;
}
.smallpop .bd{
  font-size: 12px;
  color: #3b9dfa;
}
.smallpop .tt{
  text-align: right;
  max-width: 120px;
  padding: 1px 3px 1px 8px;
}

.smallpop span{
  padding: 1px 8px 1px 0px;
}
.smallpop .color0,
.bigpop .color0{
  color: #2d72c6;
}
.smallpop .color1,
.bigpop .color1{
  color: #87bdff;
}
.smallpop .color2,
.bigpop .color2{
  color: #fffc00;
}
.smallpop .color3,
.bigpop .color3{
  color: #ee0b19;
}
/* ---- */

.bigpop {
  position: absolute;
  display: inline-block;
  min-height: 70px;
  min-width: 330px;
  max-height: 400px;
  max-width: 400px;
}
.bigpop .bg{
  position: absolute;
  left: 4px;
  top: 2px;
  width: calc(100% - 8px);
  height: calc(100% - 4px);

  border-radius: 5px;
  border: 1px solid #0078f8;

  overflow: hidden;

  background: #022e71;

  box-shadow: 0 2px 12px #021121;

}
.bigpop .c0{
  position: absolute;
  left: 0px;
  top: 0px;
  width: 77px;
  height: 59px;
  background: url(../img/a4.1_pop_bc0.png) no-repeat;
}
.bigpop .c1{
  position: absolute;
  right: 0px;
  top: 0px;
  width: 31px;
  height: 66px;
  background: url(../img/a4.1_pop_bc1.png) no-repeat;
}
.bigpop .c2{
  position: absolute;
  right: 0px;
  bottom: 0px;
  width: 72px;
  height: 58px;
  background: url(../img/a4.1_pop_bc2.png) no-repeat;
}
.bigpop .c3{
  position: absolute;
  left: 0px;
  bottom: 0px;
  width: 30px;
  height: 65px;
  background: url(../img/a4.1_pop_bc3.png) no-repeat;
}
.bigpop .titlebg{
  position: absolute;
  width: 100%;
  height: 40px;
  background: #022456;
  background: -moz-linear-gradient(left,  #022456 0%, #1995c9 17%, #1993c8 32%, #1065af 55%, #022153 100%);
  background: -webkit-linear-gradient(left,  #022456 0%,#1995c9 17%,#1993c8 32%,#1065af 55%,#022153 100%);
  background: linear-gradient(to right,  #022456 0%,#1995c9 17%,#1993c8 32%,#1065af 55%,#022153 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#022456', endColorstr='#022153',GradientType=1 );
}
.bigpop .title{
  position: absolute;
  padding-left: 46px;
  width: 100%;
  height: 40px;
  line-height: 40px;
  font-size: 15px;
  font-weight: bold;
  color: #fff;
  white-space:nowrap;
  background-image: url(../img/a4.1_arrow.png);
  background-position: 20px center;
  background-repeat: no-repeat; 

}
.bigpop .close{
  position: absolute;
  width: 14px;
  height: 14px;
  top: 16px; 
  right: 15px;
  background: url(../img/pop_x_w.png) no-repeat;
  cursor: pointer;
}
.bigpop .tablebg{
  position: absolute;
  width: 100%;
  height: 27px;
  top: 40px;
  background: #042d68;
}
.bigpop table{
  position: relative;
  margin: 40px 15px 15px 15px;

}
.bigpop td{
  text-align: center;
  min-width: 50px;
}
.bigpop .head{
  font-size: 16px;
  color: #FFF;
  height: 35px;
  font-weight: bold;
}
.bigpop .bd{
  font-size: 14px;
  color: #9bc8ff;
  line-height: 22px;
}
.bigpop .tt{
  text-align: right;
  padding: 1px 3px 1px 8px;
  min-width: 80px;
}

.bigpop .scrollpane {
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  margin: 46px 10px 15px 10px;
  max-height: 345px;
}

.bigpop .scrollpane .block{
  position: relative;
  display: block;
  border-bottom: 1px solid #022e71;
}
.bigpop .scrollpane .block .tit{
  height: 30px;
  line-height: 30px;
  padding-left: 17px;
  padding-right: 17px;
  color: #87bdff;
  font-weight: bold;
  background: #022359;
}
.bigpop .scrollpane .block .tit.yellow{
  color: #e9dc0b;
}
.bigpop .scrollpane .block .tit.red{
  color: #e90b18;
}

.bigpop .scrollpane .block .arr_yellow{
  display: inline-block;
  width: 20px;
  height: 10px;
  background: url(../img/a4.1_arrow_yellow.png) no-repeat center center;
}
.bigpop .scrollpane .block .arr_red{
  display: inline-block;
  width: 20px;
  height: 10px;
  background: url(../img/a4.1_arrow_red.png) no-repeat center center;
}

.bigpop .scrollpane .block .fleet{
  line-height: 20px;
  padding: 5px 0 5px 17px;
  font-size: 12px;
}
.bigpop .scrollpane .block .fleet .itm{
  display: inline-block;
  width: 111px;
  height: 16px;
}


/* ---- */


#popwrap {
  position: absolute;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  pointer-events: none;
}


.legend {
  position: absolute;
  right: 30px;
  bottom: 20px;
  width: 136px;
  height: 100px;
  color: #7fb7ff;
  font-size: 12px;
}
.legend .lb{
  color: #3d8aed;
  height: 20px;
}
.legend img{
  width: 10px;
  height: 10px;
}
.legend table{
  background: rgba(0,58,123,1);
  border: 8px solid rgba(0,58,123,1);
}
.legend td{
  height: 24px;
  padding: 0 3px; 
}


/* --- */
.ext_link {
  display: inline-block;
  width: 23px;
  height: 23px;
  cursor: pointer;
  pointer-events: auto;
  background: url(../img/ext_link1.png) no-repeat center center;
}

#ext_link1 {
  position: absolute;
  left: 198px;
  top: 89px;
}
