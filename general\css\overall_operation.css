.p0{
    padding:0;
}
.p10{
    padding:10px;
}
.container-fluid {
    width:100%;
    padding:0;
}
.w10{
    width:10%;
}
.w20{
    width:20%;
}
.w30{
    width:30%;
}
.w40{
    width:40%;
}
.w50{
    width:50%;
}
.w60{
    width:60%;
}
.w70{
    width:70%;
}
.w80{
    width:80%;
}
.w85{
    width:85%;
}
.w90{
    width:90%;
}
.left2{
    left: 2%;
}
.left5{
    left: 5%;
}
.left10{
    left: 10%;
}
.left20{
    left: 20%;
}
.left25{
    left: 25%;
}
.left30{
    left: 30%;
}
.left40{
    left: 40%;
}
.left50{
    left: 50%;
}
.left60{
    left: 60%;
}
.left70{
    left: 70%;
}
.left80{
    left: 80%;
}
.left90{
    left: 90%;
}

.btn_go {
    padding: 6px;
    border-radius: 4px;
    border: 1px solid #2874c1;
    cursor: pointer;
    pointer-events: auto;
    background: url(../img/arr.png) no-repeat 130px center;
    width: 150px;
}
.logo img{
    position: absolute;
    left:90px;
}
/**下拉框**/
#companycombo {
    position: absolute;
    top: 12px;
    left: 0px;
    width: 70px;
    height: 32px;
    font-size: 10px;
}

#companycombo .box {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 70px;
    height: 32px;
    border: 1px solid #2a81d2;
    border-radius: 3px;
    padding: 7px 0 0 9px;
    cursor: pointer;
    background: url(../img/combo_arr.png) no-repeat 57px 13px;
    background-color: #073b77;
    pointer-events: auto;
}

#companycombo .box:hover {
    border: 1px solid #2f8be1;
    background-color: #073b77;
}

#companylist {
    position: absolute;
    top: 31px;
    left: 0px;
    width: 70px;
    border: 1px solid #1f5fa8;
    border-radius: 2px;
    padding: 5px 0 5px 0px;
    background-color: #021d39;
    display: none;
    z-index: 1;
    pointer-events: auto;
}
#companylist  .itm {
    width: 100%;
    height: 32px;
    padding: 6px 0 0 10px;
    cursor: pointer;
    position:relative;
}

#companylist  .itm:hover {
    background-color: #1f5fa8;
}
.combobox_list{
    position: absolute;
    top: 31px;
    left: 0px;
    width: 133px;
    overflow-y: scroll;
    border: 1px solid #1f5fa8;
    border-radius: 2px;
    padding: 5px 0 5px 0px;
    background-color: #021d39;
    display: none;
    z-index: 1;
    pointer-events: auto;
}

.combobox_list .item {
    display: block;
    padding: 3px;
    position: relative;
    cursor: pointer;
    pointer-events: auto;
    letter-spacing: -0.5px;
}

.combobox_list .item:hover {
    background-color: #1f5fa8;
}
#date_range {
    position: absolute;
    right: 120px;
    width: 180px;
    font-size: 14px;
    text-align: center;
    border: 1px solid #1f5fa8;
    border-radius: 2px;
    padding: 3px 0 5px 0px;
    background-color: #021d39;
    box-shadow: 0 1px 8px #021121;
    z-index: 999;
    display: none;
}

.up_arr{
    display: inline-block;
    height: 10px;
    width: 10px;
    background: url(../img/arr_up2.png) no-repeat center;
}
.down_arr{
    display: inline-block;
    height: 10px;
    width: 10px;
    background: url(../img/arr_down2.png) no-repeat center;
}
.label-title{
    border-left:5px solid #2a81d2;
    border-radius: 0;
    font-size: 15px;
    padding-left: 7px;
}
.label-title .line_1{
    padding-left: 10px;
    color:#2a81d2;
    font-weight: 600;
    font-size: 1px;
}
.label-title .line_2_21{
    width: 112px;
    bottom: 4px;
    left: 96%;
    border-bottom:1px solid #2a81d2;
}
.label-title .line_2_46{
    width: 156px;
    bottom: 4px;
    left: 95%;
    border-bottom:1px solid #2a81d2;
}
.label-title .line_2_54{
    width: 176px;
    bottom: 4px;
    left: 95%;
    border-bottom:1px solid #2a81d2;
}
.label-title .line_2_72{
    width: 456px;
    bottom: 4px;
    left: 95%;
    border-bottom:1px solid #2a81d2;
}
.label-content{
    width:100%;
    top:40px;
}
.third-row .label-content .air_line_1{
    background: url(../img/air_line_1.png?1) no-repeat top center;
    height: 106px;
}
.third-row .label-content .air_line_1 span,.label-content .air_line_2 span{
    bottom: 12px;
    position: absolute;
    width: 100%;
    text-align: center;
}
.third-row .label-content .air_line_2{
    background: url(../img/air_line_2.png?1) no-repeat top center;
    height: 106px;
}
.third-row .label-content .air_line_content{
    top: 12px;
}
.third-row .label-content .air_line_content .container-fluid:last-child{
    top: 45px;
}
.third-row .label-content .air_line_content div.row:first-child,.third-row .label-content .air_line_content div.row:last-child{
    margin-top:10px;
}
.third-row .label-content .air_line_content .air_line_o,.third-row .label-content .air_line_content .air_line_i{
    top: -2px;
    left: 43px;
}
.third-row .label-content .air_line_content .air_line_o_content,.third-row .label-content .air_line_content .air_line_i_content{
    top: 17px;
    left: 43px;
    font-size:10px;
    color: #0297e7;
}

.four-row .label-content .air_plan_bg{
    background: url(../img/air_plan.png?1) no-repeat top center;
    height: 106px;
}
.four-row .label-content .air_plan_content_1,.five-row .label-content .passenger_content_1{
    top:24px;
}
.four-row .label-content .air_plan_content_1_top strong,.five-row .label-content .passenger_content_1_top strong{
    font-size:22px;
}
.four-row .label-content .air_plan_content_1_bottom,.five-row .label-content .passenger_content_1_bottom{
    font-size:13px;
    color: #0297e7;
    top: 34px;
}

.five-row .label-content .passenger_bg{
    background: url(../img/passenger.png?1) no-repeat top center;
    height: 106px;
}
.six-row .label-content .plf_y2y,.six-row .label-content .date_ur_y2y,.seven-row .label-content .fleet_y2y{
    top: 10px;
    left: 0px;
}
.six-row .label-content .plf_m2m,.six-row .label-content .date_ur_m2m,.seven-row .label-content .fleet_m2m{
    top: 50px;
    left: 0px;
}
.label_content{
    text-align: center;
    width: 100%;
    top: 2px;
}
.label_footer{
    bottom: 1px;
    left: 22px;
}
.seven-row .label-content .oil_bg{
    background: url(../img/oil.png?1) no-repeat top center;
    height: 77px;
    width:67px;
    left: 40px;
}
.seven-row .label-content .oil_content{
    text-align: center;
    left: 41px;
    top: 75px;
    font-size: 17px;
}

.air_border{
    background: url(../img/air_border.png?1) no-repeat top center;
    height: 80px;
    top: 10px;
    left: -15px;
}
.air_border .air_border_content_left{
    width: 50%;
    float: left;
    padding: 5%;
    position: absolute;
    left: 15%;
    bottom: 45px;
}
.air_border .air_border_content_left_1{
    width: 40%;
    float: left;
    padding: 5%;
    position: absolute;
    left: 15%;
    bottom: 45px;
}
.air_border .air_border_content_right{
    width: 39%;
    float: left;
    padding: 5%;
    position: absolute;
    right: 9%;
    bottom: 45px;
}
.air_border .air_border_content_right_1{
    width: 38%;
    float: left;
    padding: 5%;
    position: absolute;
    right: 10%;
    bottom: 45px;
}
.air_border .air_border_content_left .air_border_content_left_bottom,.air_border .air_border_content_left_1 .air_border_content_left_bottom,
.air_border .air_border_content_right .air_border_content_right_bottom,.air_border .air_border_content_right_1 .air_border_content_right_bottom {
    font-size:13px;
    color: #0297e7;
    top: 48px;
}
.air_border strong{
    font-size:19px;
}

.footer_label{
    background: url(../img/label_bg.png?1) no-repeat top center;
    height: 40px;
    width: 73px;
    font-size: 13px;
    text-align: center;
}

.title{
    text-align: center;
    color: #0297e7;
    left: 101.6%;
    top: 10px;
}
.hidden{
    display: none !important;
}

/** 背景 **/
.page-bgimg {
    position: absolute;
    top: 0px;
    width: 1920px;
    height: 1080px;
    background: url(../img/overall_operation.bg.png?2) no-repeat top center;
    background-size: 100%;
    overflow-x: hidden;
    overflow-y: hidden;
    pointer-events: none;
}

.page-wrapper {
    position: absolute;
    top: 0px;
    width: 1920px;
    height: 1080px;
    overflow-x: hidden;
    overflow-y: hidden;
    pointer-events: none;
}

.map-wrapper{
    position: absolute;
    top:120px;
    width:640px;
    height:600px;
    left: 102%;
    overflow-x: hidden;
    overflow-y: hidden;
    pointer-events: all;
}
.airmap-wrapper{
    position: absolute;
    top: -15px;
    left: -15px;
    width: 300px;
    height: 230px;
    overflow-x: hidden;
    overflow-y: hidden;
    pointer-events: all;
}
.indicator{
    text-align: center;
}
.indicator canvas{
    position: relative;
}
.chart{
    left:5px;
    text-align: center;
}
.chart .pointer {
    width: 122px;
    height: 122px;
}
.chart .mk {
    position: absolute;
    font-size: 9px;
    color:#44a3f4
}
.chart .lb {
    position: absolute;
    width: 100%;
    height: 50px;
    top: 78px;
}

.col-l{
    padding: 0px 0 0 33px;
    width: 32%;
}
.col-m{
    padding: 0;
    width: 33%;
}
.col-r{
    padding:0 25px 0 0;
    width: 33%;
}

.col-l .first-row h5{
    color: #0297e7;
    font-weight: 600;
    margin-top: 12px;
    font-size: 18px;
    text-align: center;
}
.col-l .first-row{
    top:25px
}
.col-l .second-row {
    top: 90px;
}
.col-l .third-row {
    top: 135px;
}
.col-l .four-row {
    top: 300px;
}
.col-l .five-row {
    top: 465px;
}
.col-l .six-row {
    top: 630px;
}
.col-l .seven-row {
    top: 795px;
}
.col-l .date_type_select {
    /*width: 160px;*/
    height: 30px;
    border: 1px solid #1a6cbc;
    border-radius: 3px;
    display: inline-block;
    vertical-align: middle;
    box-sizing: border-box;
    float: left;
}

.col-l .date_type_select .tab {
    display: inline-block;
    vertical-align: middle;
    width: 39px;
    height: 29px;
    float: left;
    box-sizing: border-box;
    text-align: center;
    line-height: 28px;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    font-size: 14px;
    color: #44a3f4;
    border-right: 1px solid #2a81d2;
    pointer-events: auto;
    position: relative;
}
.col-l .date_type_select .tab:last-child{
    border-right: 0;
}
.col-l .date_type_select .tab.hover {
    background-color: #2a81d2;
    color: white;
}
.col-l .date_type_select .tab:hover {
    background-color: #2a81d2;
    color: white;
}
.col-l .date_type_select .tab:nth-child(1) {
    position: relative;
    /*left: -1px;*/
    border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
}
.col-l .date_type_select .tab:nth-child(4) {
    position: relative;
    /*right: -2px;*/
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
}
.col-l .limcomb {
    height: 30px;
    width: 135px !important;
    border-radius: 4px;
    border: 1px solid rgba(41, 140, 232, 0.6);
    background: #0b3379;
    float: left;
    margin-left: 10px;
}
.col-l .limcomb .combobox_label{
    font-size: 14px;
    text-align: center;
    line-height: 28px;
    padding: 5px;
    cursor: pointer;
    pointer-events: auto;
}

.col-l .limcomb .combobox_label {
    display: inline-block;
    padding: 0;
    height: 30px;
    line-height: 30px;
    text-align: left;
    background-image: url(../img/combobox_arr.png);
    background-repeat: no-repeat;
    background-position: 115px center;
    border-radius: 4px;
    border-right: none;
    border: 0px !important;
    box-sizing: border-box;
    padding-left: 5px;
    width: 100%;
}

.col-m .legend_position{
    position:absolute;
    top: 770px;
    left: 102%;
}
.col-m .legend_normal_rate .legend_green{
    top:33px;
}
.col-m .legend_normal_rate .legend_yellow{
    top:33px;
}
.col-m .legend_normal_rate .legend_orange{
    top:66px;
}
.col-m .legend_normal_rate .legend_red{
    top:66px;
}
.col-m .legend_normal_rate .green_pin_bg{
    background: url(../img/green_pin.png?1) no-repeat top center;
    height: 21px;
    width: 21px;
}
.col-m .legend_normal_rate .yellow_pin_bg{
    background: url(../img/yellow_pin.png?1) no-repeat top center;
    height: 21px;
    width: 21px;
}
.col-m .legend_normal_rate .orange_pin_bg{
    background: url(../img/orange_pin.png?1) no-repeat top center;
    height: 21px;
    width: 21px;
}
.col-m .legend_normal_rate .red_pin_bg{
    background: url(../img/red_pin.png?1) no-repeat top center;
    height: 21px;
    width: 21px;
}
.col-m .legend_normal_rate .green_pin_rate,
.col-m .legend_normal_rate .yellow_pin_rate,
.col-m .legend_normal_rate .orange_pin_rate,
.col-m .legend_normal_rate .red_pin_rate{
    color:#44a3f4;
    padding-left: 25px;
}
.col-m .legend_io {
    top: 33px;
}
.col-m .legend_io .earth_pin{
    background: url(../img/earth_pin.png?1) no-repeat top center;
    height: 64px;
    width: 64px;
}
.col-m .legend_io .io_place{
    font-size: 15px;
    font-weight: bold;
}
.col-m .legend_io .io_place_content{
    line-height: 25px;
}
.col-m .legend_io .io_rate{
    color:#44a3f4;
    top:27px;
}
.col-m .legend_io .io_rate .io_shifts,.col-m .legend_io .io_rate .io_normal_rate{
    color:#fff;
}
.col-m .rank{
    left: 102%;
    top: 890px;
}
.col-m .rank .tab{
    border-top: 1px solid #1a6cbc;
    border-left: 1px solid #1a6cbc;
    border-right: 1px solid #1a6cbc;
    text-align: center;
    font-size: 16px;
}
.col-m .rank .tab .tab_left,.col-m .rank .tab .tab_right{
    cursor:pointer;
}
.col-m .rank .tab .unselected{
    border-bottom: 1px solid #1a6cbc;
    border-right: 1px solid #1a6cbc;
    background-color:#023979;
    pointer-events: auto;
}
.col-m .rank .tab .unselected{
    border-bottom: 1px solid #1a6cbc;
    border-left: 1px solid #1a6cbc;
    background-color:#023979;
    pointer-events: auto;
}
.col-m .rank .result{
    top: 43px;
    text-align: center;
    padding: 16px;
    border-bottom: 1px solid #1a6cbc;
    border-left: 1px solid #1a6cbc;
    border-right: 1px solid #1a6cbc;
}
.col-m .rank .result td{
    width: 10%;
}
.col-m .rank .result td>span{
    color:#0296e5;
    font-size: 15px;
    font-weight: bold;
}

.col-r .time{
    left: 1320px;
    top: 25px;
}
.col-r .time td{
    width: 20%;
    color:#0296e5;
    font-weight: bold;
}
.col-r .media{
    left: 1320px;
    top: 80px;
    height: 342px;
}

.col-r .media .media_bg{
    background: url(../img/media_bg.png?1) no-repeat top center;
    height: 303px;
    width: 540px;
}

.col-r .normal_rate{
    left: 1320px;
    top: 455px;
    overflow: hidden;
    height: 400px;
}
.col-r .normal_rate .title_1{
    color:#0296e5;
    text-align: center;
    font-size: 15px;
    font-weight: bold;
}
.col-r .normal_rate .rate_1 .y_normal_rate,
.col-r .normal_rate .rate_1 .m_normal_rate,
.col-r .normal_rate .rate_1 .w_normal_rate{
    top: 42px;
    left: 24px;
}
.col-r .normal_rate .rate_2{
    top: 155px;
    left: 18px;
}

.col-r .normal_rate .title_2{
    top:214px;
    color:#0296e5;
    text-align: center;
    font-size: 15px;
    font-weight: bold;
}
.col-r .normal_rate .rate_3{
    top: 247px;
    left: 38px;
}

.col-r .delay{
    left: 1320px;
    top: 870px;
    overflow: hidden;
    height: 300px;
}
.col-r .delay canvas{
    left:211px; background:rgba(255,0,0,0);
}
.col-r .delay .center{
    width: 160px;
    height: 20px;
    left: 193px;
    top: 32px;
}
.col-r .delay .delay_left{
    background: url(../img/delay_left.png?1) no-repeat top center;
    height: 72px;
    width: 198px;
    top: 25px;
    left: 20px;
    padding: 16px 16px 16px 25px;
}
.col-r .delay .delay_left .delay_rate_comp_color{
    color:#da9a1a;
}
.col-r .delay .delay_right{
    background: url(../img/delay_right.png?1) no-repeat top center;
    height: 72px;
    width: 198px;
    top: 25px;
    right: 17px;
    padding: 16px 16px 16px 33px;
}
.col-r .delay .delay_right .delay_rate_none_color{
    color:#02B0F9;
}

#popover_map {
    position: absolute;
    z-index: 888;
    width: 378px;
    height: 200px;
    top: 84px;
    left: 771px;
    background: url(../img/pop.bg.png) no-repeat 0 0;
    -moz-transform-origin: 50% 50%;
    -wekkit-transform-origin: 50% 50%;
    -ms-transform-origin: 50% 50%;
    transform-origin: 50% 50%;
    opacity: 0;
}


#popover_map .title{
    position: absolute;
    left: 1px;
    width: 100%;
    height: 50px;
    top: 23px;
    text-align: center;
    color:#fff;
}
#popover_map .close{
    position: absolute;
    width: 15px;
    height: 15px;
    top: 26px;
    right: 25px;
    background: url(../img/x.png) no-repeat 0 0;
    pointer-events: auto;
    cursor: pointer;
    opacity: 0.8;
}
#popover_map .popover_left{
    left: 12px;
}
#popover_map .blue{
    color: #0297e7;
}
#popover_map .content{
    top: 80px;
    left: 20px;
}
.content .line_2{
    top: 21px;
}
.content .line_3{
    top: 47px;
}
.content .line_4{
    top: 68px;
}



.mr_t5{
    margin-top: 5px;
}
.mr_t10{
    margin-top: 10px;
}
.mr_t15{
    margin-top: 15px;
}
.mr_t20{
    margin-top: 20px;
}
.mr_t25{
    margin-top: 25px;
}
.mr_t30{
    margin-top: 30px;
}