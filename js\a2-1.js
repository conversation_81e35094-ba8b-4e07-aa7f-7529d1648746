showLoading();


// ------------------------------------------------------------

$('#mainframe').attr('src', 'map/map-a2-1.html');

// ------------------------------------------------------------



var current_company_code;


// 可显示的历史 数量
var query_limit = 20;
// 日期类型
var date_type = 'L';

var actypelist = '787,767,330,333,737,320,321,319,145,190,350';


// -------------------------------------------------------

//                          KPI

// -------------------------------------------------------


var weekList = [];
var monthList = [];
var yearList = [];
var dateList = [];


var selected_day;
var selected_month;
var selected_year;
var selected_week_last_day;

var all_company_data; // 存储各个公司基本指标
var all_company_data_ac; // 飞机日利用率 计算指标
var all_company_data_acNew // 飞机日利用率 计算指标-新的算法
var all_company_data_task; // 存储预算任务
var all_company_data_task_year; // 存储年预算任务
var year_inc_to_date; // 累加到指定日 年度累计收入
var year_inc_to_date_sq; // 累加到指定日 年度累计收入上期

var weekDateRangeList; //例会周的日期对应表


var fetchingKpiData = false;


// 机型id／code对应表
var actypeId2Code;

// 日期类型选择
function chooseDateType() {
    $(".date_type_select .tab").on('click', function () {

        $(".date_type_select .tab").removeClass('hover');
        $(this).addClass('hover');
        date_type = $(this).data('type');
        $(".limcomb").addClass('hidden');
        if (date_type == "L") {

            $("#main_cb_week").removeClass('hidden');
        } else if (date_type == "M") {
            $("#main_cb_month").removeClass('hidden');
        } else if (date_type == "Y") {
            $("#main_cb_year").removeClass('hidden');
        } else {
            $("#main_cb_day").removeClass('hidden');
        }
        updateAllKpi();
    });
}
chooseDateType();

// 后台JOB图标显示
function changeDataRefresh() {
    $('.pagetitle .data_refresh').hide();
    var param = {
        'CODE': 'J_HVP_FAC_COMP_KPI'
    }
    $.ajax({
        type: 'post',
        url: "/bi/sys/isrunning",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            if (response.isrun == 1) {
                $('.pagetitle .data_refresh').show();
                regTooltip('#btnrefresh', '后台数据更新中');
            }
        },
        error: function () {

        }
    });
}
changeDataRefresh();
setInterval(changeDataRefresh, 1 * 60 * 60 * 1000);

function getAllCompanyKpiData() {
    // companylist = ['HNAHK', 'HU', 'JD', 'GS', 'HX', '8L', 'PN', 'FU', 'GX', 'UQ', 'Y8', '9H', 'GT'];
    if (companylist.length == 0) {
        setTimeout(getAllCompanyKpiData, 0);
        return;
    }

    // check url hash
    var hash = window.location.hash.substr(1);
    if (hash && companyCode2Name[hash] != undefined) {
        current_company_code = hash;
    } else {
        current_company_code = parent_company;
    }



    if (fetchingKpiData) return;

    fetchingKpiData = true;

    all_company_data = {};
    all_company_data['L'] = {};

    var comp_code = current_company_code;

    var len = companylist.length;
    console.log("###", companylist)
    var codelist = [];
    for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        codelist.push(dat.code);
    }


    var loadingInProgress = 0;

    var kpi_list = [
        'EST_INC_FUEL', //总收入（含燃油）
        'TRV_NUM', //旅客量
        'TRV_RATE', //客座率
        'INL_TRV_RATE', //国内客座率
        'INT_TRV_RATE', //国际客座率
        'HOUR_INC_FUEL', //小时收(含燃油)
        'CAP_KILO_INC_FUEL', //座收(含燃油)
        //'INC_TASK', //收入任务(含油). 用来计算收入任务完成率
        //'FLY_TIME_TASK', //飞行时间任务. 用来计算收入任务完成率
        //'AC_NUM_TASK', //飞机架次任务. 用来计算收入任务完成率

        // 下面4个用来计算 国际／国内 座收：收入／座公里
        'INL_CAP_KILO', //国内座公里
        'INT_CAP_KILO', //国际座公里
        'INL_INC', //国内收入
        'INT_INC', //国际收入

        'AVG_TKT_PRICE', //平均票价
        'INL_AVG_TKT_PRICE', //国内平均票价
        'INT_AVG_TKT_PRICE', //国际平均票价

        'SHIFTS', //班次
        'FLY_TIME', //飞行小时

        'INL_HOUR_INC_FUEL', // 国内小时收入
        'INT_HOUR_INC_FUEL', // 国际小时收入
        'TRV_KILO', //客公里
        'CAP_KILO' //座公里

    ];

    // -------------------------------------------------------
    // 周
    // -------------------------------------------------------


    // 本期

    loadingInProgress++;
    var param = {
        'SOLR_CODE': 'FAC_COMP_KPI',
        'COMP_CODE': codelist.join(','),
        'KPI_CODE': kpi_list.join(','),
        'VALUE_TYPE': 'kpi_value_d', //本期
        'DATE_TYPE': 'L,M,D,Y',
        "OPTIMIZE": 1,
        'LIMIT': query_limit
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getkpi?EST_INC_FUEL_getWeekDateRange",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            console.log("*******")
            console.log(response);
            if (response.data != undefined) {

                all_company_data['kpi_value_d'] = response.data;


                // L
                var datlist = response.data[comp_code]['EST_INC_FUEL']['L'];

                weekList = [];
                for (var date in datlist) {
                    weekList.push(date);
                }
                getWeekDateRange(weekList);
                var cblist = [];
                var len = weekList.length;
                var last_date;
                for (var i = 0; i < len; i++) {
                    var date = weekList[i];
                    // Week: *********
                    //var label = date.substr(0,4) + '年第' + Number(date.substr(4,3)) + '周';
                    //20170419 双双要求：显示的周数+1
                    var weeknum = Number(date.substr(4, 3));
                    // 20180111, 因为周数+1，新的一年缺少第1周，所以增加第1周，数据显示上年最后一周的
                    if (weeknum == 1) {
                        var label = '第' + 1 + '周 ';
                        cblist.unshift({
                            'label': label,
                            'data': last_date
                        });
                    }
                    var label = '第' + (weeknum + 1) + '周 ';
                    cblist.unshift({
                        'label': label,
                        'data': date
                    });

                    last_date = date;
                }
                createComboBox('main_cb_week', cblist, 84, 240, updateAllKpi, 1);


                // $('#main_cb_week .combobox_label').on('click', function(event) {
                //     event.preventDefault();

                //     if (date_type == 'L') {
                //         return;
                //     }

                //     $('#main_cb_week').addClass('combotab_selected');
                //     $('#main_cb_week').removeClass('combotab');
                //     $('#main_cb_month').addClass('combotab');
                //     $('#main_cb_month').removeClass('combotab_selected');

                //     date_type = 'L';
                //     updateAllKpi();
                // });

                // 显示 week 日期范围
                $('#main_cb_week .combobox_label').on('mouseover', function (event) {
                    event.preventDefault();
                    if (weekDateRangeList && date_type == 'L') {
                        var date = getCurrentDate();
                        $('#week_date_range').text(weekDateRangeList[date]);
                        $('#week_date_range').fadeIn();
                    }
                });

                // 隐藏 week 日期范围
                $('#main_cb_week .combobox_label').on('mouseout', function (event) {
                    event.preventDefault();
                    if (weekDateRangeList) {
                        $('#week_date_range').fadeOut();
                    }
                });



                // M
                var date = new Date();
                var month = date.getMonth() + 1;
                if (month < 10) {
                    month = '0' + month;
                }
                var nowmonth = date.getFullYear() + '' + month;

                var datlist = response.data[comp_code]['EST_INC_FUEL']['M'];

                var cblist = [];
                for (var date in datlist) {
                    // 201703
                    if (date <= nowmonth) {
                        var label = date.substr(0, 4) + '年' + Number(date.substr(4, 2)) + '月';
                        cblist.unshift({
                            'label': label,
                            'data': date
                        });

                        monthList.push(date);
                    }
                }
                createComboBox('main_cb_month', cblist, 104, 240, updateAllKpi, 1);

                // Y 显示年范围-201904提的新需求
                var date = new Date();
                var year = date.getFullYear();
                var datlist = response.data[comp_code]['EST_INC_FUEL']['Y'];

                var cblist = [];
                for (var date in datlist) {
                    // 2017转2017年
                    if (date >= 2017) {
                        if (date <= year) {
                            var label = date + '年';
                            cblist.unshift({
                                'label': label,
                                'data': date
                            });

                            yearList.push(date);
                        }
                    } else {
                        continue;
                    }

                }
                createComboBox('main_cb_year', cblist, 104, 240, updateAllKpi, 0);

                // 显示 month 日期范围
                $('#main_cb_month .combobox_label').on('mouseover', function (event) {
                    event.preventDefault();
                    var month = $('#main_cb_month').attr('data');
                    var curmonth = moment().format("YYYYMM");
                    var numofdays = moment(month, "YYYYMM").daysInMonth(); // 获取一个月有几天
                    var days = numofdays;
                    if (days < 10) {
                        days = '0' + days;
                    }
                    if (curmonth == month) {
                        days = moment().format("DD");
                    }
                    $('#week_date_range').text(month + '01' + '~' + month + days);
                    $('#week_date_range').fadeIn();
                });

                // 隐藏 month 日期范围
                $('#main_cb_month .combobox_label').on('mouseout', function (event) {
                    event.preventDefault();
                    if (weekDateRangeList) {
                        $('#week_date_range').fadeOut();
                    }
                });
                $('#main_cb_month .combobox_label').on('click', function (event) {
                    event.preventDefault();
                    if (weekDateRangeList) {
                        $('#week_date_range').fadeOut();
                    }
                });

                // D
                var date2 = new Date();
                var month2 = date2.getMonth() + 1;

                var day = date2.getDate();
                if (month2 < 10) {
                    month2 = '0' + month2;
                }
                if (day < 10) {
                    day = '0' + day;
                }
                var nowdate = date2.getFullYear() + '' + month2 + '' + day;
                let t_2day = date2.getDate() - 2;
                if (t_2day < 10) {
                    t_2day = '0' + t_2day;
                }
                let t_2 = date2.getFullYear() + '' + month2 + '' + t_2day;
                let hasValue = false;

                var datlist2 = response.data[comp_code]['EST_INC_FUEL']['D'];
                var cblist2 = [];

                for (var date in datlist2) {
                    // 20170304
                    if (date <= nowdate) {
                        var label = date.substr(0, 4) + '年' + date.substr(4, 2) + '月' + date.substr(6, 2) + '日';
                        cblist2.unshift({
                            'label': label,
                            'data': date
                        });

                        dateList.push(date);
                    }
                    if (date == t_2) {
                        hasValue = true;
                    }
                }
                let index = 0;
                if (hasValue) {
                    cblist2.some((v, i) => {
                        if (v.data == t_2) {
                            index = i;
                            return true;
                        }
                    })
                }
                createComboBox('main_cb_day', cblist2, 104, 240, updateAllKpi, index);

                // $('#main_cb_day .combobox_label').on('click', function(event) {
                //     event.preventDefault();

                //     if (date_type == 'M') {
                //         return;
                //     }

                //     $('#main_cb_week').addClass('combotab');
                //     $('#main_cb_week').removeClass('combotab_selected');
                //     $('#main_cb_month').addClass('combotab_selected');
                //     $('#main_cb_month').removeClass('combotab');

                //     date_type = 'M';
                //     updateAllKpi();
                // });


                // 显示 day 日期范围
                // $('#main_cb_day .combobox_label').on('mouseover', function(event) {
                //     event.preventDefault();
                //     var month = $('#main_cb_day').attr('data');
                //     var curmonth = moment().format("YYYYMMDD");
                //     var numofdays = moment(month, "YYYYMMDD").daysInMonth(); // 获取一个月有几天
                //     var days = numofdays;
                //     if (days < 10) {
                //         days = '0' + days;
                //     }
                //     if (curmonth == month) {
                //         days = moment().format("DD");
                //     }
                //     $('#week_date_range').text(month + '01' + '~' + month + days);
                //     $('#week_date_range').fadeIn();
                // });

                // // 隐藏 month 日期范围
                // $('#main_cb_day .combobox_label').on('mouseout', function(event) {
                //     event.preventDefault();
                //     if (weekDateRangeList) {
                //         $('#week_date_range').fadeOut();
                //     }
                // });
                // $('#main_cb_day .combobox_label').on('click', function(event) {
                //     event.preventDefault();
                //     if (weekDateRangeList) {
                //         $('#week_date_range').fadeOut();
                //     }
                // });

                loadingInProgress--;
                checkDataReady();



            }

        },
        error: function () {

        }
    });

    //同期 
    loadingInProgress++;
    var param = {
        'SOLR_CODE': 'FAC_COMP_KPI',
        'COMP_CODE': codelist.join(','),
        'KPI_CODE': kpi_list.join(','),
        'VALUE_TYPE': 'kpi_value_tq_d', //同期 
        'DATE_TYPE': 'L,M,D,Y',
        "OPTIMIZE": 1,
        'LIMIT': query_limit
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getkpi?1",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {

            if (response.data != undefined) {
                all_company_data['kpi_value_tq_d'] = response.data;
                loadingInProgress--;
                checkDataReady();
            }

        },
        error: function () {

        }
    });

    //同比 
    loadingInProgress++;
    var param = {
        'SOLR_CODE': 'FAC_COMP_KPI',
        'COMP_CODE': codelist.join(','),
        'KPI_CODE': kpi_list.join(','),
        'VALUE_TYPE': 'kpi_ratio_tq_d', //同比 
        'DATE_TYPE': 'L,M,D,Y',
        "OPTIMIZE": 1,
        'LIMIT': query_limit
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {

            if (response.data != undefined) {
                all_company_data['kpi_ratio_tq_d'] = response.data;
                loadingInProgress--;
                checkDataReady();
            }

        },
        error: function () {

        }
    });

    //上期 
    loadingInProgress++;
    var param = {
        'SOLR_CODE': 'FAC_COMP_KPI',
        'COMP_CODE': codelist.join(','),
        'KPI_CODE': kpi_list.join(','),
        'VALUE_TYPE': 'kpi_value_sq_d', //上期 
        'DATE_TYPE': 'L,M,D,Y',
        "OPTIMIZE": 1,
        'LIMIT': query_limit
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getkpi?1",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {

            if (response.data != undefined) {
                all_company_data['kpi_value_sq_d'] = response.data;
                loadingInProgress--;
                checkDataReady();
            }

        },
        error: function () {

        }
    });

    //环比
    loadingInProgress++;
    var param = {
        'SOLR_CODE': 'FAC_COMP_KPI',
        'COMP_CODE': codelist.join(','),
        'KPI_CODE': kpi_list.join(','),
        'VALUE_TYPE': 'kpi_ratio_sq_d', //环比
        'DATE_TYPE': 'L,M,D,Y',
        "OPTIMIZE": 1,
        'LIMIT': query_limit
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {

            if (response.data != undefined) {
                all_company_data['kpi_ratio_sq_d'] = response.data;
                loadingInProgress--;
                checkDataReady();
            }

        },
        error: function () {

        }
    });

    var a = setTimeout(function () {

        if (all_company_data['kpi_ratio_sq_d'] != undefined && all_company_data['kpi_ratio_tq_d'] != undefined) {

            kpiDataReady = true;
            updateAllKpi();
            hideLoading();
            // clearInterval(a);
        }
    }, 9500);

    //获取周的预算任务
    loadingInProgress++;
    var param = {
        'SOLR_CODE': 'FAC_COMP_KPI',
        'COMP_CODE': codelist.join(','),
        'KPI_CODE': 'INC_TASK,FLY_TIME_TASK,AC_NUM_TASK',
        'VALUE_TYPE': 'kpi_value_d',
        'DATE_TYPE': 'L,M,D,Y',
        "OPTIMIZE": 1,
        'LIMIT': 60
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {

            if (response.data != undefined) {
                all_company_data_task = response.data;
                loadingInProgress--;
                checkDataReady();
            }

        },
        error: function () {

        }
    });


    //获取年预算任务
    loadingInProgress++;
    var param = {
        'SOLR_CODE': 'FAC_COMP_KPI',
        'COMP_CODE': codelist.join(','),
        'KPI_CODE': 'INC_TASK_OIL', //'INC_TASK', // 20180307 叶衍双 让改成 INC_TASK_OIL
        'VALUE_TYPE': 'kpi_value_d',
        'DATE_TYPE': 'Y',
        "OPTIMIZE": 1,
        'LIMIT': 1
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {

            if (response.data != undefined) {
                all_company_data_task_year = response.data;
                loadingInProgress--;
                checkDataReady();
            }

        },
        error: function () {

        }
    });
    var ac_list_except_other_type = [];
    dfd.done(function () {
        if (hasAllCompanyPermission()) {
            // -------------------------------------------------------
            // 获取所有机型 / 在统计日利用率时排除“其它”类型的飞机
            // -------------------------------------------------------
          
            var param = {}

            $.ajax({
                type: 'post',
                url: "/bi/web/actypeall",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {

                    if (response.actype) {
                        var list = response.actype;

                        actypeId2Code = {};

                        var len = list.length;
                        for (var i = 0; i < len; i++) {
                            var obj = list[i];
                            actypeId2Code[obj.id] = obj.code;

                            if (obj.actyptyp == 'Wide' || obj.actyptyp == 'Narrow' || obj.actyptyp == 'Regional') {
                                // 宽体机,窄体机,支线机
                                ac_list_except_other_type.push(obj.code);
                            }
                        }

                        // 获取日利用率相关指标
                        getAcKpiData();
                    }

                },
                error: function () {

                }
            });
        }
    })





    // -------------------------------------------------------
    // 日利用率
    // -------------------------------------------------------

    function getAcKpiData() {

        var param = {
            'SOLR_CODE': 'FAC_COMP_ACTYPE_KPI',
            'COMP_CODE': codelist.join(','),
            'KPI_CODE': 'FLY_TIME,AC_NUM', // 飞行时间、飞机架次 == 用来计算飞机日利用率
            'VALUE_TYPE': 'kpi_value_d', //本期
            'DATE_TYPE': 'L,M,D,Y',
            'ACTYPE': actypelist,
            "OPTIMIZE": 1,
            'LIMIT': query_limit
        }

        $.ajax({
            type: 'post',
            url: "/bi/query/getackpi",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {

                if (response.data != undefined) {

                    // 统计所有机型总和的 FLY_TIME AC_NUM 指标
                    var dtypes = ['L', 'M', 'D', 'Y'];

                    for (var kk = 0; kk < dtypes.length; kk++) {
                        var d_type = dtypes[kk];
                        var formatedData = {};
                        for (var compcode in response.data) {
                            for (var kpicode in response.data[compcode]) {

                                var aclist = response.data[compcode][kpicode][d_type]['data3'];

                                if (formatedData[compcode] == undefined) {
                                    formatedData[compcode] = {};
                                }
                                if (formatedData[compcode][kpicode] == undefined) {
                                    formatedData[compcode][kpicode] = {};
                                }
                                if (formatedData[compcode][kpicode][d_type] == undefined) {
                                    formatedData[compcode][kpicode][d_type] = {};
                                }

                                var len = aclist.length;
                                for (var i = 0; i < len; i++) {
                                    var acdat = aclist[i];
                                    var acid = acdat.actype;
                                    var ac = actypeId2Code[acid];
                                    // 只统计 宽体机,窄体机,支线机 类型的飞机，其它类型不统计
                                    if (ac_list_except_other_type.indexOf(ac) > -1) {
                                        var ddd = acdat.date;
                                        var len2 = ddd.length;
                                        for (var j = 0; j < len2; j++) {
                                            var obj = ddd[j];
                                            var date = obj.date;
                                            var val = obj.value;
                                            if (formatedData[compcode][kpicode][d_type][date] == undefined) {
                                                formatedData[compcode][kpicode][d_type][date] = 0;
                                            }
                                            if (!isNaN(val)) {
                                                formatedData[compcode][kpicode][d_type][date] += Number(val);
                                            }
                                        }
                                    }
                                }

                            }

                        }

                        if (all_company_data_ac == undefined) {
                            all_company_data_ac = {};
                        }
                        if (all_company_data_ac[d_type] == undefined) {
                            all_company_data_ac[d_type] = {};
                        }
                        all_company_data_ac[d_type]['kpi_value_d'] = formatedData;
                    }


                    setBlockR2Kpi();
                    setBlockR1T2Kpi();

                }

            },
            error: function () {

            }
        });



        var param = {
            'SOLR_CODE': 'FAC_COMP_ACTYPE_KPI',
            'COMP_CODE': codelist.join(','),
            'KPI_CODE': 'FLY_TIME,AC_NUM', // 飞行时间、飞机架次 == 涌来计算飞机日利用率
            'VALUE_TYPE': 'kpi_value_tq_d', //同期
            'DATE_TYPE': 'L,M,D,Y',
            'ACTYPE': actypelist,
            "OPTIMIZE": 1,
            'LIMIT': query_limit
        }

        $.ajax({
            type: 'post',
            url: "/bi/query/getackpi?2",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {

                if (response.data != undefined) {

                    // 统计所有机型总和的 FLY_TIME AC_NUM 指标
                    var dtypes = ['L', 'M', 'D', 'Y'];

                    for (var kk = 0; kk < dtypes.length; kk++) {
                        var d_type = dtypes[kk];
                        var formatedData = {};

                        for (var compcode in response.data) {
                            for (var kpicode in response.data[compcode]) {
                                var aclist = response.data[compcode][kpicode][d_type]['data3'];

                                if (formatedData[compcode] == undefined) {
                                    formatedData[compcode] = {};
                                }
                                if (formatedData[compcode][kpicode] == undefined) {
                                    formatedData[compcode][kpicode] = {};
                                }
                                if (formatedData[compcode][kpicode][d_type] == undefined) {
                                    formatedData[compcode][kpicode][d_type] = {};
                                }

                                var len = aclist.length;
                                for (var i = 0; i < len; i++) {
                                    var acdat = aclist[i];
                                    var acid = acdat.actype;
                                    var ac = actypeId2Code[acid];
                                    // 只统计 宽体机,窄体机,支线机 类型的飞机，其它类型不统计
                                    if (ac_list_except_other_type.indexOf(ac) > -1) {
                                        var ddd = acdat.date;
                                        var len2 = ddd.length;
                                        for (var j = 0; j < len2; j++) {
                                            var obj = ddd[j];
                                            var date = obj.date;
                                            var val = obj.value;
                                            if (formatedData[compcode][kpicode][d_type][date] == undefined) {
                                                formatedData[compcode][kpicode][d_type][date] = 0;
                                            }
                                            if (!isNaN(val)) {
                                                formatedData[compcode][kpicode][d_type][date] += Number(val);
                                            }
                                        }
                                    }
                                }


                            }

                        }

                        if (all_company_data_ac == undefined) {
                            all_company_data_ac = {};
                        }
                        if (all_company_data_ac[d_type] == undefined) {
                            all_company_data_ac[d_type] = {};
                        }
                        all_company_data_ac[d_type]['kpi_value_tq_d'] = formatedData;
                    }

                    setBlockR1T2Kpi();
                }

            },
            error: function () {

            }
        });


    }



    // 获取例会周对应的日期范围
    function getWeekDateRange(week_list) {
        var param = {
            "DATE_ID": week_list.join(','),
            "FIELD": "DATE_TYPE" // 对应数据表字段 DATE_DESC_XS
        }

        $.ajax({
            type: 'post',
            url: "/bi/web/datetype",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {

                weekDateRangeList = response;

                // 获取最新的有数据的日期，用来确定当前例会周截止的日期是哪天
                var param = {
                    'SOLR_CODE': 'FAC_COMP_KPI',
                    'COMP_CODE': parent_company,
                    'KPI_CODE': 'TRV_RATE',
                    'VALUE_TYPE': 'kpi_value_d',
                    'DATE_TYPE': 'D',
                    "OPTIMIZE": 1,
                    'LIMIT': 1
                }

                $.ajax({
                    type: 'post',
                    url: "/bi/query/getkpi",
                    contentType: 'application/json',
                    dataType: 'json',
                    async: true,
                    data: JSON.stringify(param),
                    success: function (response) {

                        if (response.data != undefined) {
                            var ddd = response.data[parent_company]['TRV_RATE']['D'];
                            var latest_date;
                            for (var da in ddd) {
                                latest_date = da;
                                break;
                            }

                            //把最新周的结束日期换成上面查到的最新日期
                            if (latest_date) {
                                var latest_week = 0;
                                for (var week in weekDateRangeList) {
                                    if (Number(week) > Number(latest_week)) {
                                        latest_week = week;
                                    }
                                }
                                if (latest_week != 0) {
                                    var date_range = weekDateRangeList[latest_week];
                                    var arr = date_range.split('-');
                                    if (Number(arr[1]) > Number(latest_date) && Number(arr[0]) <= Number(latest_date)) {
                                        //weekDateRangeList[latest_week] = arr[0]+'-'+latest_date;
                                    }
                                }
                            }
                        }

                        loadingInProgress--;
                        checkDataReady();

                    },
                    error: function () { }
                });


            },
            error: function () { }
        });
    }



    function checkDataReady() {
        if (loadingInProgress == 0) {
            kpiDataReady = true;
            updateAllKpi();
            hideLoading();

        }
    }



}



// 获取年度累计收入
function getYearIncToNow() {
    var date = '';
    if ('M' == date_type) {
        var month = getCurrentDate();
        var curmonth = moment().format("YYYYMM");
        var numofdays = moment(month, "YYYYMM").daysInMonth(); // 获取一个月有几天
        var days = numofdays;
        if (days < 10) {
            days = '0' + days;
        }
        if (curmonth == month) {
            days = moment().format("DD");
        }
        date = month + days;

    } else if ('D' == date_type) {
        var day = getCurrentDate();
        // var curday = moment().format("YYYYMMDD");
        // var numofdays = moment(month, "YYYYMMDD").daysInMonth(); // 获取一个月有几天
        // var days = numofdays;
        // if (days < 10) {
        //     days = '0' + days;
        // }
        // if (curmonth == month) {
        //     days = moment().format("DD");
        // }
        date = day;

    } else if ('Y' == date_type) {
        var year = getCurrentDate();
        date = year;
    } else {
        if (!weekDateRangeList) {
            setTimeout(getYearIncToNow, 0);
            return;
        }
        var week = $('#main_cb_week').attr('data');
        var daterange = weekDateRangeList[week];
        var arr = daterange.split('-');
        date = arr[1];
        // 当前周日期大于当前时间取当天，避免跨年问题
        var curday = moment().format("YYYYMMDD");
        if (date > curday) {
            date = curday;
        }
        selected_week_last_day = date;
    }

    year_inc_to_date = undefined;
    year_inc_to_date_sq = undefined;



    var loadingInProgress = 0;

    var comp_id;

    var len = companylist.length;
    for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        if (current_company_code == dat.code) {
            comp_id = dat.id;
            break;
        }
    }

    // 当期
    loadingInProgress++;
    var param = {
        //'SOLR_CODE': 'FAC_COMP_KPI',
        'COMP_ID': comp_id,
        'KPI_ID': '10010', //'EST_INC_FUEL',
        'VALUE_TYPE': 'kpi_value_d', // 当期
        //'DATE_TYPE': 'D',
        'DATE_ID': date,
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getyearkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {

            year_inc_to_date = Number(response.total);
            loadingInProgress--;
            checkYearIncDataReady();

        },
        error: function () {

        }
    });



    // 上期
    loadingInProgress++;
    var param = {
        //'SOLR_CODE': 'FAC_COMP_KPI',
        'COMP_ID': comp_id,
        'KPI_ID': '10010', //'EST_INC_FUEL',
        'VALUE_TYPE': 'kpi_value_tq_d', // kpi_value_sq_d 改成kpi_value_tq_d 获取去年同期每天的值，累计后就是去年这个日期的累计值
        //'DATE_TYPE': 'D',
        'DATE_ID': date,
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getyearkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {

            year_inc_to_date_sq = Number(response.total);
            loadingInProgress--;
            checkYearIncDataReady();

        },
        error: function () {

        }
    });



    function checkYearIncDataReady() {
        //console.log('year_inc_to_date', year_inc_to_date);
        //console.log('year_inc_to_date_sq', year_inc_to_date_sq);
        if (loadingInProgress == 0) {
            setMapKpi();
            setBlockL1Kpi();
        }
    }

}



// 中间地图各个公司kpi
function setMapKpi() {

    if (year_inc_to_date == undefined || year_inc_to_date_sq == undefined) {
        setTimeout(setMapKpi, 10);
        return;
    }

    if ($('#mainframe')[0].contentWindow.setRateMap != undefined && usersCompayCodeList.length > 0) {

        var compdata = all_company_data['kpi_value_d'];
        var selected_week = getCurrentDate();

        $('#mainframe')[0].contentWindow.setRateMap(usersCompayCodeList, compdata, all_company_data_task, selected_week);

    } else {

        setTimeout(setMapKpi, 10);

    }
}

function setBlockL1Kpi() {

    if (year_inc_to_date == undefined || year_inc_to_date_sq == undefined) {
        setTimeout(setBlockL1Kpi, 10);
        return;
    }

    $('.block_l1 .tit').text(companyCode2Name[current_company_code]);

    // 收入 week
    var date = getCurrentDate();
    // console.log("$$$$$$$$$$$$$")
    // console.log(JSON.stringify(all_company_data), all_company_data['kpi_ratio_tq_d'], all_company_data['kpi_ratio_sq_d'], current_company_code)
    var val = all_company_data['kpi_value_d'][current_company_code]['EST_INC_FUEL'][date_type][date];
    var tb = all_company_data['kpi_ratio_tq_d'][current_company_code]['EST_INC_FUEL'][date_type][date];
    var hb = all_company_data['kpi_ratio_sq_d'][current_company_code]['EST_INC_FUEL'][date_type][date];

    var inc_bq = Math.round(val);
    var inc_bq_tb = Math.round(tb * 10000) / 100;
    var inc_bq_hb = Math.round(hb * 10000) / 100;

    //年度累计收入-同比-选择年
    if (date.length == 4) {
        $('#kpi_' + 'Y' + '_' + 'kpi_value_d' + '_' + 'EST_INC_FUEL').text(formatCurrency(Math.round(val), 0));
    }
    //年度累计收入-选择年
    if (date.length == 4) {
        $('#kpi_' + 'Y' + '_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .val').text(trimDecimal(hb * 100, 2));
    }

    $('#kpi_' + 'kpi_value_d' + '_' + 'EST_INC_FUEL').text(formatCurrency(Math.round(val), 0));

    if (date.length == 4 && (tb == null || tb == undefined)) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'EST_INC_FUEL .val').text('1');
    } else {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'EST_INC_FUEL .val').text(trimDecimal(tb * 100, 2));
    }
    if (tb > 0) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'EST_INC_FUEL .green').removeClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'EST_INC_FUEL .red').addClass('hide');
    } else if (tb < 0) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'EST_INC_FUEL .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'EST_INC_FUEL .red').removeClass('hide');
    } else {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'EST_INC_FUEL .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'EST_INC_FUEL .red').addClass('hide');
    }
    $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .val').text(trimDecimal(hb * 100, 2));
    if (hb > 0) {
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .green').removeClass('hide');
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .red').addClass('hide');
    } else if (hb < 0) {
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .red').removeClass('hide');
    } else {
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .red').addClass('hide');
    }


    // 收入 年度累计, 累积到当前选中的week

    var date_now = new Date();
    var year_now = date_now.getFullYear();

    var inc_total = year_inc_to_date

    var inc_task = 0; //所有公司相加
    var len = companylist.length;
    for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        var compcode = dat.code;
        if ((current_company_code == compcode && compcode != parent_company) || current_company_code == parent_company) {
            var val = all_company_data_task_year[compcode]['INC_TASK_OIL']['Y'][year_now];
            if (val > 0) {
                inc_task += Number(val);
            }
        }
    }

    // 环比=(本期-上期)/上期×100%
    var hb = year_inc_to_date_sq > 0 ? Math.round((year_inc_to_date - year_inc_to_date_sq) / year_inc_to_date_sq * 10000) / 100 : 0;

    var inc_year = Math.round(inc_total);
    var inc_task = Math.round(inc_task);
    var inc_year_hb = hb;

    // 兼容处理，不显示海航航空HNAHK年度收入任务指标
    if (current_company_code == "HNAHK") {
        inc_task = -1;
    }
    //$('#kpi_' + 'Y' + '_' + 'kpi_value_d' + '_' + 'EST_INC_FUEL').text(formatCurrency(Math.round(inc_total), 0));
    //年度累计收入-选择年
    if (date.length > 4) {
        $('#kpi_' + 'Y' + '_' + 'kpi_value_d' + '_' + 'EST_INC_FUEL').text(formatCurrency(Math.round(inc_total), 0));
    }

    $('#kpi_' + 'Y' + '_' + 'kpi_value_d' + '_' + 'INC_TASK .val').text(inc_task > 0 ? formatCurrency(Math.round(inc_task), 0) : '-');
    if (date.length > 4) {
        $('#kpi_' + 'Y' + '_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .val').text(hb);
    }

    if (hb > 0) {
        $('#kpi_' + 'Y' + '_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .green').removeClass('hide');
        $('#kpi_' + 'Y' + '_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .red').addClass('hide');
    } else if (hb < 0) {
        $('#kpi_' + 'Y' + '_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .green').addClass('hide');
        $('#kpi_' + 'Y' + '_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .red').removeClass('hide');
    } else {
        $('#kpi_' + 'Y' + '_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .green').addClass('hide');
        $('#kpi_' + 'Y' + '_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .red').addClass('hide');
    }

    // 任务完成进度
    var progress = inc_task > 0 ? Math.round(inc_total) / Math.round(inc_task) : 0;
    progress = Math.round(progress * 10000) / 100;
    if (progress == 0) {
        progress = '-'
    }
    $('#kpi_Y_progress_EST_INC_FUEL .val').text(progress);

    // 旅客量
    var val = all_company_data['kpi_value_d'][current_company_code]['TRV_NUM'][date_type][date];
    var hb = all_company_data['kpi_ratio_sq_d'][current_company_code]['TRV_NUM'][date_type][date];
    var tb = all_company_data['kpi_ratio_tq_d'][current_company_code]['TRV_NUM'][date_type][date];

    var trv_num = Math.round(val / 1000) * 1000;
    var trv_num_hb = Math.round(hb * 10000) / 100;

    $('#kpi_' + 'kpi_value_d' + '_' + 'TRV_NUM').text(formatCurrency(Math.round(val / 1000) / 10, 1));
    $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'TRV_NUM .val').text(trimDecimal(hb * 100, 2));

    //选择年
    if (date.length == 4 && (tb == null || tb == undefined)) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'TRV_NUM .val').text(trimDecimal(hb * 100, 2));
    } else {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'TRV_NUM .val').text(trimDecimal(tb * 100, 2));
    }

    if (hb > 0) {
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'TRV_NUM .green').removeClass('hide');
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'TRV_NUM .red').addClass('hide');
    } else if (hb < 0) {
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'TRV_NUM .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'TRV_NUM .red').removeClass('hide');
    } else {
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'TRV_NUM .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'TRV_NUM .red').addClass('hide');
    }

    if (tb > 0) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'TRV_NUM .green').removeClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'TRV_NUM .red').addClass('hide');
    } else if (tb < 0) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'TRV_NUM .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'TRV_NUM .red').removeClass('hide');
    } else {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'TRV_NUM .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'TRV_NUM .red').addClass('hide');
    }


    // 客座率
    var val = all_company_data['kpi_value_d'][current_company_code]['TRV_RATE'][date_type][date];
    var hb = all_company_data['kpi_ratio_sq_d'][current_company_code]['TRV_RATE'][date_type][date];
    var tb = all_company_data['kpi_ratio_tq_d'][current_company_code]['TRV_RATE'][date_type][date];

    var trv_rate = Math.round(val * 1000) / 10;
    var trv_rate_hb = Math.round(hb * 10000) / 100;

    $('#kpi_' + 'kpi_value_d' + '_' + 'TRV_RATE').text(trimDecimal(val * 100, 1));
    $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'TRV_RATE .val').text(trimDecimal(hb * 100, 2));

    if (date.length == 4 && (tb == null || tb == undefined)) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'TRV_RATE .val').text(trimDecimal(hb * 100, 2));
    } else {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'TRV_RATE .val').text(trimDecimal(tb * 100, 2));
    }

    if (hb > 0) {
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'TRV_RATE .green').removeClass('hide');
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'TRV_RATE .red').addClass('hide');
    } else if (hb < 0) {
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'TRV_RATE .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'TRV_RATE .red').removeClass('hide');
    } else {
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'TRV_RATE .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'TRV_RATE .red').addClass('hide');
    }
    if (tb > 0) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'TRV_RATE .green').removeClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'TRV_RATE .red').addClass('hide');
    } else if (tb < 0) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'TRV_RATE .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'TRV_RATE .red').removeClass('hide');
    } else {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'TRV_RATE .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'TRV_RATE .red').addClass('hide');
    }

    // 小时收入
    var val = all_company_data['kpi_value_d'][current_company_code]['HOUR_INC_FUEL'][date_type][date];
    var hb = all_company_data['kpi_ratio_sq_d'][current_company_code]['HOUR_INC_FUEL'][date_type][date];
    var tb = all_company_data['kpi_ratio_tq_d'][current_company_code]['HOUR_INC_FUEL'][date_type][date];
    var hour_inc = val;
    var hour_inc_hb = Math.round(hb * 10000) / 100;

    $('#kpi_' + 'kpi_value_d' + '_' + 'HOUR_INC_FUEL').text(trimDecimal(val, 2));
    $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'HOUR_INC_FUEL .val').text(trimDecimal(hb * 100, 2));

    if (date.length == 4 && (tb == null || tb == undefined)) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'HOUR_INC_FUEL .val').text(trimDecimal(hb * 100, 2));
    } else {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'HOUR_INC_FUEL .val').text(trimDecimal(tb * 100, 2));
    }

    if (hb > 0) {
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'HOUR_INC_FUEL .green').removeClass('hide');
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'HOUR_INC_FUEL .red').addClass('hide');
    } else if (hb < 0) {
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'HOUR_INC_FUEL .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'HOUR_INC_FUEL .red').removeClass('hide');
    } else {
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'HOUR_INC_FUEL .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'HOUR_INC_FUEL .red').addClass('hide');
    }
    if (tb > 0) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'HOUR_INC_FUEL .green').removeClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'HOUR_INC_FUEL .red').addClass('hide');
    } else if (tb < 0) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'HOUR_INC_FUEL .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'HOUR_INC_FUEL .red').removeClass('hide');
    } else {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'HOUR_INC_FUEL .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'HOUR_INC_FUEL .red').addClass('hide');
    }


    // 客公里RPK
    var val_CAP_KILO_INC_FUEL = all_company_data['kpi_value_d'][current_company_code]['CAP_KILO_INC_FUEL'][date_type][date];
    var hb_CAP_KILO_INC_FUEL = all_company_data['kpi_ratio_sq_d'][current_company_code]['CAP_KILO_INC_FUEL'][date_type][date];
    var val = all_company_data['kpi_value_d'][current_company_code]['TRV_KILO'][date_type][date];
    var hb = all_company_data['kpi_ratio_sq_d'][current_company_code]['TRV_KILO'][date_type][date];
    var tb = all_company_data['kpi_ratio_tq_d'][current_company_code]['TRV_KILO'][date_type][date];
    var inc_kilo = val_CAP_KILO_INC_FUEL;
    var inc_kilo_hb = Math.round(hb_CAP_KILO_INC_FUEL * 10000) / 100;

    $('#kpi_' + 'kpi_value_d' + '_' + 'TRV_KILO').text(formatCurrency(trimDecimal(val / 10000, 0)));
    $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'TRV_KILO .val').text(trimDecimal(hb * 100, 2));

    if (date.length == 4 && (tb == null || tb == undefined)) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'TRV_KILO .val').text(trimDecimal(hb * 100, 2));
    } else {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'TRV_KILO .val').text(trimDecimal(tb * 100, 2));
    }

    if (hb > 0) {
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'TRV_KILO .green').removeClass('hide');
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'TRV_KILO .red').addClass('hide');
    } else if (hb < 0) {
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'TRV_KILO .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'TRV_KILO .red').removeClass('hide');
    } else {
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'TRV_KILO .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'TRV_KILO .red').addClass('hide');
    }
    if (tb > 0) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'TRV_KILO .green').removeClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'TRV_KILO .red').addClass('hide');
    } else if (tb < 0) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'TRV_KILO .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'TRV_KILO .red').removeClass('hide');
    } else {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'TRV_KILO .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'TRV_KILO .red').addClass('hide');
    }


    // 飞行班次
    var val = all_company_data['kpi_value_d'][current_company_code]['SHIFTS'][date_type][date];
    var hb = all_company_data['kpi_ratio_sq_d'][current_company_code]['SHIFTS'][date_type][date];
    var tb = all_company_data['kpi_ratio_tq_d'][current_company_code]['SHIFTS'][date_type][date];
    var shifts = val;
    var shifts_hb = Math.round(hb * 10000) / 100;

    $('#kpi_' + 'kpi_value_d' + '_' + 'SHIFTS').text(formatCurrency(trimDecimal(val, 0)));
    $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'SHIFTS .val').text(trimDecimal(hb * 100, 2));
    if (date.length == 4 && (tb == null || tb == undefined)) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'SHIFTS .val').text(trimDecimal(hb * 100, 2));
    } else {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'SHIFTS .val').text(trimDecimal(tb * 100, 2));
    }

    if (hb > 0) {
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'SHIFTS .green').removeClass('hide');
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'SHIFTS .red').addClass('hide');
    } else if (hb < 0) {
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'SHIFTS .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'SHIFTS .red').removeClass('hide');
    } else {
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'SHIFTS .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'SHIFTS .red').addClass('hide');
    }
    if (tb > 0) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'SHIFTS .green').removeClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'SHIFTS .red').addClass('hide');
    } else if (tb < 0) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'SHIFTS .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'SHIFTS .red').removeClass('hide');
    } else {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'SHIFTS .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'SHIFTS .red').addClass('hide');
    }


    // 飞行时间
    var val = all_company_data['kpi_value_d'][current_company_code]['FLY_TIME'][date_type][date];
    var hb = all_company_data['kpi_ratio_sq_d'][current_company_code]['FLY_TIME'][date_type][date];
    var tb = all_company_data['kpi_ratio_tq_d'][current_company_code]['FLY_TIME'][date_type][date];
    var fly_time = val;
    var fly_time_hb = Math.round(hb * 10000) / 100;

    $('#kpi_' + 'kpi_value_d' + '_' + 'FLY_TIME').text(formatCurrency(trimDecimal(val, 0)));
    $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'FLY_TIME .val').text(trimDecimal(hb * 100, 2));

    if (date.length == 4 && (tb == null || tb == undefined)) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'FLY_TIME .val').text(trimDecimal(hb * 100, 2));
    } else {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'FLY_TIME .val').text(trimDecimal(tb * 100, 2));
    }

    if (hb > 0) {
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'FLY_TIME .green').removeClass('hide');
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'FLY_TIME .red').addClass('hide');
    } else if (hb < 0) {
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'FLY_TIME .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'FLY_TIME .red').removeClass('hide');
    } else {
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'FLY_TIME .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'FLY_TIME .red').addClass('hide');
    }
    if (tb > 0) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'FLY_TIME .green').removeClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'FLY_TIME .red').addClass('hide');
    } else if (tb < 0) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'FLY_TIME .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'FLY_TIME .red').removeClass('hide');
    } else {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'FLY_TIME .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'FLY_TIME .red').addClass('hide');
    }


    // 平均票价
    var val = all_company_data['kpi_value_d'][current_company_code]['AVG_TKT_PRICE'][date_type][date];
    var avg_tkt_price = trimDecimal(val, 0);
    $('#kpi_' + 'kpi_value_d' + '_' + 'AVG_TKT_PRICE').text(formatCurrency(trimDecimal(val, 0)));

    // 国际平均票价
    var val = all_company_data['kpi_value_d'][current_company_code]['INT_AVG_TKT_PRICE'][date_type][date];
    var hb = all_company_data['kpi_ratio_sq_d'][current_company_code]['INT_AVG_TKT_PRICE'][date_type][date];
    var tb = all_company_data['kpi_ratio_tq_d'][current_company_code]['INT_AVG_TKT_PRICE'][date_type][date];
    var int_avg_tkt_price = trimDecimal(val, 0);
    var int_avg_tkt_price_hb = Math.round(hb * 10000) / 100;

    $('#kpi_' + 'kpi_value_d' + '_' + 'INT_AVG_TKT_PRICE').text(formatCurrency(trimDecimal(val, 0)));

    if (date.length == 4 && (tb == null || tb == undefined)) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'INT_AVG_TKT_PRICE .val').text(trimDecimal(hb * 100, 2));
    } else {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'INT_AVG_TKT_PRICE .val').text(trimDecimal(tb * 100, 2));
    }

    if (tb > 0) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'INT_AVG_TKT_PRICE .green').removeClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'INT_AVG_TKT_PRICE .red').addClass('hide');
    } else if (tb < 0) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'INT_AVG_TKT_PRICE .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'INT_AVG_TKT_PRICE .red').removeClass('hide');
    } else {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'INT_AVG_TKT_PRICE .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'INT_AVG_TKT_PRICE .red').addClass('hide');
    }
    // $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'INT_AVG_TKT_PRICE .val').text(trimDecimal(hb * 100, 2));
    // if (hb > 0) {
    //     $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'INT_AVG_TKT_PRICE .green').removeClass('hide');
    //     $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'INT_AVG_TKT_PRICE .red').addClass('hide');
    // } else if (hb < 0) {
    //     $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'INT_AVG_TKT_PRICE .green').addClass('hide');
    //     $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'INT_AVG_TKT_PRICE .red').removeClass('hide');
    // } else {
    //     $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'INT_AVG_TKT_PRICE .green').addClass('hide');
    //     $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'INT_AVG_TKT_PRICE .red').addClass('hide');
    // }

    // 国内平均票价
    var val = all_company_data['kpi_value_d'][current_company_code]['INL_AVG_TKT_PRICE'][date_type][date];
    var hb = all_company_data['kpi_ratio_sq_d'][current_company_code]['INL_AVG_TKT_PRICE'][date_type][date];
    var tb = all_company_data['kpi_ratio_tq_d'][current_company_code]['INL_AVG_TKT_PRICE'][date_type][date];
    var inl_avg_tkt_price = trimDecimal(val, 0);
    var inl_avg_tkt_price_hb = Math.round(hb * 10000) / 100;

    $('#kpi_' + 'kpi_value_d' + '_' + 'INL_AVG_TKT_PRICE').text(formatCurrency(trimDecimal(val, 0)));

    if (date.length == 4 && (tb == null || tb == undefined)) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'INL_AVG_TKT_PRICE .val').text(trimDecimal(hb * 100, 2));
    } else {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'INL_AVG_TKT_PRICE .val').text(trimDecimal(tb * 100, 2));
    }

    if (tb > 0) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'INL_AVG_TKT_PRICE .green').removeClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'INL_AVG_TKT_PRICE .red').addClass('hide');
    } else if (tb < 0) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'INL_AVG_TKT_PRICE .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'INL_AVG_TKT_PRICE .red').removeClass('hide');
    } else {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'INL_AVG_TKT_PRICE .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'INL_AVG_TKT_PRICE .red').addClass('hide');
    }
    // $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'INL_AVG_TKT_PRICE .val').text(trimDecimal(hb * 100, 2));
    // if (hb > 0) {
    //     $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'INL_AVG_TKT_PRICE .green').removeClass('hide');
    //     $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'INL_AVG_TKT_PRICE .red').addClass('hide');
    // } else if (hb < 0) {
    //     $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'INL_AVG_TKT_PRICE .green').addClass('hide');
    //     $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'INL_AVG_TKT_PRICE .red').removeClass('hide');
    // } else {
    //     $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'INL_AVG_TKT_PRICE .green').addClass('hide');
    //     $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'INL_AVG_TKT_PRICE .red').addClass('hide');
    // }


    // 可供客公里ASK
    var val_INT_INC = all_company_data['kpi_value_d'][current_company_code]['INT_INC'][date_type][date];
    var hb_INT_INC = all_company_data['kpi_ratio_sq_d'][current_company_code]['INT_INC'][date_type][date];
    var val = all_company_data['kpi_value_d'][current_company_code]['CAP_KILO'][date_type][date];
    var hb = all_company_data['kpi_ratio_sq_d'][current_company_code]['CAP_KILO'][date_type][date];
    var tb = all_company_data['kpi_ratio_tq_d'][current_company_code]['CAP_KILO'][date_type][date];
    var int_inc = trimDecimal(val_INT_INC, 0);
    var int_inc_hb = Math.round(hb_INT_INC * 10000) / 100;

    $('#kpi_' + 'kpi_value_d' + '_' + 'CAP_KILO').text(formatCurrency(trimDecimal(val / 10000, 0)));
    $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'CAP_KILO .val').text(trimDecimal(hb * 100, 2));

    if (date.length == 4 && (tb == null || tb == undefined)) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'CAP_KILO .val').text(trimDecimal(hb * 100, 2));
    } else {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'CAP_KILO .val').text(trimDecimal(tb * 100, 2));
    }

    if (hb > 0) {
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'CAP_KILO .green').removeClass('hide');
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'CAP_KILO .red').addClass('hide');
    } else if (hb < 0) {
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'CAP_KILO .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'CAP_KILO .red').removeClass('hide');
    } else {
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'CAP_KILO .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'CAP_KILO .red').addClass('hide');
    }
    if (tb > 0) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'CAP_KILO .green').removeClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'CAP_KILO .red').addClass('hide');
    } else if (tb < 0) {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'CAP_KILO .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'CAP_KILO .red').removeClass('hide');
    } else {
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'CAP_KILO .green').addClass('hide');
        $('#kpi_' + 'kpi_ratio_tq_d' + '_' + 'CAP_KILO .red').addClass('hide');
    }
    // $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'INT_INC .val').text(trimDecimal(hb * 100, 2));
    // if (hb > 0) {
    //     $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'INT_INC .green').removeClass('hide');
    //     $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'INT_INC .red').addClass('hide');
    // } else if (hb < 0) {
    //     $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'INT_INC .green').addClass('hide');
    //     $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'INT_INC .red').removeClass('hide');
    // } else {
    //     $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'INT_INC .green').addClass('hide');
    //     $('#kpi_' + 'kpi_ratio_sq_d' + '_' + 'INT_INC .red').addClass('hide');
    // }

    // 占整体收入的占比
    // var val_all = all_company_data['kpi_value_d'][current_company_code]['EST_INC_FUEL'][date_type][date];
    // var per = val_all > 0 ? (val / val_all) : 0;
    // var int_inc_per = trimDecimal(per * 100, 2);
    // $('#kpi_INT_INC_per .val').text(trimDecimal(per * 100, 2));



    // 语音播报
    function onAudioTplLoad(tplobj) {

        // {COMP} {DATE} 收入{INC}元，同比{TB1}，环比{HB1}。 
        // 年度累计收入{INC_YEAR}元，环比{HB2}。年度收入任务指标{INC_TASK}元，完成进度{PROGRESS}。
        // 旅客运输量{TRV_NUM}人，环比{HB3}。 
        // 客座率{TRV_RATE}，环比{HB4}。 
        // 小时收入{INC_HOUR}元，环比{HB5}。 
        // 座公里收入{INC_KILO}元，环比{HB6}。


        var tpl = tplobj.txt;

        //--
        var compname = companyCode2Name[current_company_code];
        tpl = tpl.replace(/{COMP}/g, compname);

        //--
        var date = getCurrentDate();
        if (date_type == 'L') {
            var datelabel = date.substr(0, 4) + '年' + '第' + (Number(date.substr(4, 3)) + 1) + '周';
        } else if (date_type == 'M') {
            var datelabel = date.substr(0, 4) + '年' + date.substr(4, 2) + '月';
        } else if (date_type == 'D') {
            var datelabel = date.substr(0, 4) + '年' + date.substr(4, 2) + '月' + date.substr(6, 2) + '日';
        }

        tpl = tpl.replace(/{DATE}/g, datelabel);

        //--
        tpl = tpl.replace(/{INC}/g, formatCurrency(inc_bq * 10000, 2));
        tpl = tpl.replace(/{PROGRESS}/g, progress + '%');

        //--
        tpl = tpl.replace(/{TB1}/g, ratioTemplete(inc_bq_tb));
        //--
        tpl = tpl.replace(/{HB1}/g, ratioTemplete(inc_bq_hb));

        //--
        tpl = tpl.replace(/{INC_YEAR}/g, formatCurrency(inc_year * 10000, 2));
        tpl = tpl.replace(/{INC_TASK}/g, formatCurrency(inc_task * 10000, 2));
        //--
        tpl = tpl.replace(/{HB2}/g, ratioTemplete(inc_year_hb));
        //--
        tpl = tpl.replace(/{TRV_NUM}/g, formatCurrency(trv_num, 2));
        //--
        tpl = tpl.replace(/{HB3}/g, ratioTemplete(trv_num_hb));

        //--
        tpl = tpl.replace(/{TRV_RATE}/g, trv_rate + '%');
        //--
        tpl = tpl.replace(/{HB4}/g, ratioTemplete(trv_rate_hb));

        //--
        tpl = tpl.replace(/{INC_HOUR}/g, formatCurrency(hour_inc * 10000, 2));
        //--
        tpl = tpl.replace(/{HB5}/g, ratioTemplete(hour_inc_hb));

        //--
        tpl = tpl.replace(/{INC_KILO}/g, formatCurrency(inc_kilo, 2));
        //--
        tpl = tpl.replace(/{HB6}/g, ratioTemplete(inc_kilo_hb));


        selectPage(1);
        text2audio(tpl, true, playPart2);

    }

    function playPart2() {
        selectPage(2);
        getAudioTemplate('A2.1-general2', onAudioTplLoad2);
    }

    // 语音播报
    function onAudioTplLoad2(tplobj) {

        // 飞行班次{SHIFTS}班次，环比{HB7}。
        // 飞行时间{FLY_TIME}小时，环比{HB8}。
        // 平均票价{AVT_TKT}元。国际{AVT_TKT}元，环比{HB9}。国内{INL_AVT_TKT}元，环比{HB10}。
        // 国际收入{INT_INC}万元，环比{HB11}，占整体收入的占比百分之{INT_INC_PER}。

        var tpl = tplobj.txt;

        //--
        tpl = tpl.replace(/{SHIFTS}/g, formatCurrency(shifts, 0));
        //--
        tpl = tpl.replace(/{HB7}/g, ratioTemplete(shifts_hb));

        //--
        tpl = tpl.replace(/{FLY_TIME}/g, formatCurrency(fly_time, 0));
        //--
        tpl = tpl.replace(/{HB8}/g, ratioTemplete(fly_time_hb));

        //--
        tpl = tpl.replace(/{AVT_TKT}/g, formatCurrency(avg_tkt_price, 0));
        tpl = tpl.replace(/{INT_AVT_TKT}/g, formatCurrency(int_avg_tkt_price, 0));
        tpl = tpl.replace(/{INL_AVT_TKT}/g, formatCurrency(inl_avg_tkt_price, 0));
        //--
        tpl = tpl.replace(/{HB9}/g, ratioTemplete(int_avg_tkt_price_hb));
        tpl = tpl.replace(/{HB10}/g, ratioTemplete(inl_avg_tkt_price_hb));

        //--
        tpl = tpl.replace(/{INT_INC}/g, formatCurrency(int_inc, 0));
        //--
        tpl = tpl.replace(/{HB11}/g, ratioTemplete(int_inc_hb));
        tpl = tpl.replace(/{INT_INC_PER}/g, int_inc_per);


        text2audio(tpl, true);

    }

    stopAudio();

    getAudioTemplate('A2.1-general', onAudioTplLoad);



}

// 年收入鼠标提示框事件
function yearTipsEvent() {
    $("#totalYear").on("mousemove", function () {
        $(this).find(".float-tips").show();
    });

    $("#totalYear").on("mouseout", function () {
        $(".float-tips").hide();
    });

}
yearTipsEvent();

// 右边tab内容页初始化进度条
function initScroll() {

    $(".scroll_cover").niceScroll({
        cursorcolor: "rgba(1,75,153,0.8)",
        cursorborder: "rgba(0,0,0,0)",
        autohidemode: false
    });
    // $(".scroll_cover").getNiceScroll(0).resize();


}
initScroll();

function setBlockR1Kpi() {

    //'EST_INC_FUEL', //总收入（含燃油）
    //'INC_TASK', //收入任务. 用来计算收入任务完成率

    var date = getCurrentDate();

    var html1 = '';
    var html2 = '';
    var list1 = [];
    var list2 = [];

    var len = companylist.length;

    for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        var compcode = dat.code;
        console.log(all_company_data_task)
        if (all_company_data['kpi_value_d'][compcode] != undefined && all_company_data_task != undefined && compcode != parent_company) {
            var val1 = all_company_data['kpi_value_d'][compcode]['EST_INC_FUEL'][date_type][date];
            var val2 = all_company_data_task[compcode]['INC_TASK'][date_type][date];
            var rate = val2 > 0 ? Math.round((val1 / val2) * 1000) / 10 : 0;

            if (rate > 100) {
                list1.push({
                    "name": dat.nameabbr,
                    "rate": rate
                });
            } else if (rate < 95 && rate > 0) {
                list2.push({
                    "name": dat.nameabbr,
                    "rate": rate
                });
            }

            if (rate == 0) {
                //console.log(compcode, date, 'EST_INC_FUEL='+val1, 'INC_TASK='+val2);
            }
        }
    }

    list1.sort(function (a, b) {
        return b.rate - a.rate
    });
    len = list1.length;
    for (var i = 0; i < len; i++) {
        var dat = list1[i];
        html1 += '<span class="white">' + dat.name + '</span><span class="green val">' + dat.rate + '%</span>';
    }
    if (list1.length == 0) {
        html1 += '<span class="white">无</span>';
    }

    list2.sort(function (a, b) {
        return a.rate - b.rate
    });
    len = list2.length;
    for (var i = 0; i < len; i++) {
        var dat = list2[i];
        html2 += '<span class="white">' + dat.name + '</span><span class="red val">' + dat.rate + '%</span>';
    }
    if (list2.length == 0) {
        html2 += '<span class="white">无</span>';
    }

    $('.block_r1 .list1').html(html1);
    $('.block_r1 .list2').html(html2);

}


// 飞机日利用率
// 日利用率 FLY_TIME / AC_NUM，FAC_COMP_ACTYPE_KPI 表
function setBlockR2Kpi() {
    //'FLY_TIME', //飞行时长
    //'AC_NUM', //飞机架次
    //
    if (!all_company_data_ac || !all_company_data_ac[date_type]['kpi_value_d']) {
        return;
    }

    var date = getCurrentDate();

    var html1 = '';
    var html2 = '';
    var list1 = [];
    var list2 = [];

    var len = companylist.length;

    for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        var compcode = dat.code;
        if (all_company_data_ac[date_type]['kpi_value_d'][compcode] != undefined && all_company_data_task != undefined && compcode != parent_company) {
            var val1 = all_company_data_ac[date_type]['kpi_value_d'][compcode]['FLY_TIME'][date_type][date];
            var val2 = all_company_data_ac[date_type]['kpi_value_d'][compcode]['AC_NUM'][date_type][date];

            // 飞机日利用率
            var rate1 = val2 > 0 ? (val1 / val2) : 0;

            // 预算 飞机日利用率
            var val1 = all_company_data_task[compcode]['FLY_TIME_TASK'][date_type][date];
            var val2 = all_company_data_task[compcode]['AC_NUM_TASK'][date_type][date];
            var rate2 = val2 > 0 ? (val1 / val2) : 0;

            // 预算完成率
            var rate = rate2 > 0 ? Math.round((rate1 / rate2) * 1000) / 10 : 0;

            if (rate > 100) {
                list1.push({
                    "name": dat.nameabbr,
                    "rate": rate
                });
            } else if (rate < 95 && rate > 0) {
                list2.push({
                    "name": dat.nameabbr,
                    "rate": rate
                });
            }

            if (rate == 0) {
                //console.log(compcode, date, 'FLY_TIME_TASK='+val1, 'AC_NUM_TASK='+val2);
            }
        }


    }

    list1.sort(function (a, b) {
        return b.rate - a.rate
    });
    len = list1.length;
    for (var i = 0; i < len; i++) {
        var dat = list1[i];
        html1 += '<span class="white">' + dat.name + '</span><span class="green val">' + dat.rate + '%</span>';
    }
    if (list1.length == 0) {
        html1 += '<span class="white">无</span>';
    }

    list2.sort(function (a, b) {
        return a.rate - b.rate
    });
    len = list2.length;
    for (var i = 0; i < len; i++) {
        var dat = list2[i];
        html2 += '<span class="white">' + dat.name + '</span><span class="red val">' + dat.rate + '%</span>';
    }
    if (list2.length == 0) {
        html2 += '<span class="white">无</span>';
    }

    $('.block_r2 .list1').html(html1);
    $('.block_r2 .list2').html(html2);
}


function setBlockR3Kpi() {
    //'HOUR_INC_FUEL', //小时收(含燃油)
    //

    var date = getCurrentDate();

    var html1 = '';
    var html2 = '';
    var list1 = [];
    var list2 = [];

    var len = companylist.length;
    for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        var compcode = dat.code;
        if (all_company_data['kpi_value_d'][compcode] != undefined && all_company_data_task != undefined && compcode != parent_company) {
            var val1 = all_company_data['kpi_value_d'][compcode]['HOUR_INC_FUEL'][date_type][date];

            var val2 = all_company_data_task[compcode]['INC_TASK'][date_type][date];
            var val3 = all_company_data_task[compcode]['FLY_TIME_TASK'][date_type][date];
            // 任务小时收入=任务总收入／任务飞行时间
            var task = val3 > 0 ? (val2 / val3) : 0;

            // 预算完成率
            var rate = task > 0 ? Math.round(val1 / task * 1000) / 10 : 0;

            if (rate > 100) {
                list1.push({
                    "name": dat.nameabbr,
                    "rate": rate
                });
            } else if (rate < 95 && rate > 0) {
                list2.push({
                    "name": dat.nameabbr,
                    "rate": rate
                });
            }

            if (rate == 0) {
                //console.log(compcode, date, 'HOUR_INC_FUEL='+val1, 'INC_TASK='+val2, 'FLY_TIME_TASK='+val3);
            }
        }
    }

    list1.sort(function (a, b) {
        return b.rate - a.rate
    });
    len = list1.length;
    for (var i = 0; i < len; i++) {
        var dat = list1[i];
        html1 += '<span class="white">' + dat.name + '</span><span class="green val">' + dat.rate + '%</span>';
    }
    if (list1.length == 0) {
        html1 += '<span class="white">无</span>';
    }

    list2.sort(function (a, b) {
        return a.rate - b.rate
    });
    len = list2.length;
    for (var i = 0; i < len; i++) {
        var dat = list2[i];
        html2 += '<span class="white">' + dat.name + '</span><span class="red val">' + dat.rate + '%</span>';
    }
    if (list2.length == 0) {
        html2 += '<span class="white">无</span>';
    }

    $('.block_r3 .list1').html(html1);
    $('.block_r3 .list2').html(html2);
}


// 核心经营指标 -> 日利用率经营情况
function setBlockR1T2Kpi() {
    if (all_company_data_ac == undefined || all_company_data_ac[date_type]['kpi_value_d'] == undefined || all_company_data_ac[date_type]['kpi_value_tq_d'] == undefined) {
        return;
    }

    // 集团总体
    var grp_obj = calAcRate(parent_company);

    $('#val_grp_ac_rate .val').text(Math.round(grp_obj.rate * 10) / 10);
    $('#val_grp_ac_rate_tb .val').text(grp_obj.tb);

    $('#val_grp_ac_rate_tb .green').hide();
    $('#val_grp_ac_rate_tb .red').hide();
    $('#val_grp_ac_rate_tb .yellow').hide();

    $('#val_grp_ac_rate_tb .sub').show();

    if (grp_obj.tb > 0) {
        $('#val_grp_ac_rate_tb .green').show();
    } else if (grp_obj.tb < 0) {
        $('#val_grp_ac_rate_tb .red').show();
    } else {
        $('#val_grp_ac_rate_tb .yellow').show();
        $('#val_grp_ac_rate_tb .sub').hide();
        $('#val_grp_ac_rate_tb .val').text('-');
    }

    var comp_arr = [];

    // 各个分公司
    var len = companylist.length;
    for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        var compcode = dat.code;
        if (compcode != parent_company) {

            var obj = calAcRate(compcode);
            var com = {};
            com.name = dat.nameabbr;
            com.tb = obj.tb;
            comp_arr.push(com);
        }
    }

    comp_arr.sort(function (a, b) {
        return b.tb - a.tb
    });

    // 增长
    var html = '';
    var len = comp_arr.length;
    var k = 0;
    for (var i = 0; i < len; i++) {
        var com = comp_arr[i];
        var tb = com.tb;
        if (Number(tb) > 0 && k < 4) {
            html += '<div class="itm">';
            html += '<div class="blue_ll">' + com.name + '</div>';
            html += '<div>';
            html += '<span class="val">' + tb + '</span><span class="fs10">%</span><span class="green">↑</span>';
            html += '</div>';
            html += '</div>';
            k++
        }
    }
    if (html == '') {
        html += '<div class="itm">';
        html += '<div class="blue_ll">无</div>';
        html += '</div>';
    }
    $('.block_r1t2 .DESC').html(html);

    // 下滑
    comp_arr.reverse();
    var html = '';
    var k = 0;
    for (var i = 0; i < len; i++) {
        var com = comp_arr[i];
        var tb = com.tb;
        if (Number(tb) < 0 && k < 4) {
            html += '<div class="itm">';
            html += '<div class="blue_ll">' + com.name + '</div>';
            html += '<div>';
            html += '<span class="val">' + tb + '</span><span class="fs10">%</span><span class="red">↓</span>';
            html += '</div>';
            html += '</div>';
            k++
        }
    }
    if (html == '') {
        html += '<div class="itm">';
        html += '<div class="blue_ll">无</div>';
        html += '</div>';
    }
    $('.block_r1t2 .ASC').html(html);
}

// 核心经营指标 -> 客座率
function setBlockR2T2Kpi() {
    // 国内 ---------
    var grp_obj = getTrvRate(parent_company, 'INL_TRV_RATE');

    $('#val_grp_l_trv_rate .val').text(grp_obj.value);
    $('#val_grp_l_trv_rate_tb .val').text(grp_obj.tb);

    $('#val_grp_l_trv_rate_tb .green').hide();
    $('#val_grp_l_trv_rate_tb .red').hide();
    $('#val_grp_l_trv_rate_tb .yellow').hide();

    $('#val_grp_l_trv_rate_tb .sub').show();

    if (grp_obj.tb > 0) {
        $('#val_grp_l_trv_rate_tb .green').show();
    } else if (grp_obj.tb < 0) {
        $('#val_grp_l_trv_rate_tb .red').show();
    } else {
        $('#val_grp_l_trv_rate_tb .yellow').show();
        $('#val_grp_l_trv_rate_tb .sub').hide();
        $('#val_grp_l_trv_rate_tb .val').text('-');
    }

    var comp_arr = [];

    // 各个分公司
    var len = companylist.length;
    for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        var compcode = dat.code;
        if (compcode != parent_company) {

            var obj = getTrvRate(compcode, 'INL_TRV_RATE');
            var com = {};
            com.name = dat.nameabbr;
            com.tb = obj.tb;
            comp_arr.push(com);
        }
    }

    comp_arr.sort(function (a, b) {
        return b.tb - a.tb
    });

    // 增长
    var html = '';
    var len = comp_arr.length;
    var k = 0;
    for (var i = 0; i < len; i++) {
        var com = comp_arr[i];
        var tb = com.tb;
        if (Number(tb) > 0 && k < 4) {
            html += '<div class="itm">';
            html += '<div class="blue_ll">' + com.name + '</div>';
            html += '<div>';
            html += '<span class="val">' + tb + '</span><span class="fs10">%</span><span class="green">↑</span>';
            html += '</div>';
            html += '</div>';
            k++
        }
    }
    if (html == '') {
        html += '<div class="itm">';
        html += '<div class="blue_ll">无</div>';
        html += '</div>';
    }
    $('#tabc_l_TRV_RATE .DESC').html(html);

    // 下滑
    comp_arr.reverse();
    var html = '';
    var k = 0;
    for (var i = 0; i < len; i++) {
        var com = comp_arr[i];
        var tb = com.tb;
        if (Number(tb) < 0 && k < 4) {
            html += '<div class="itm">';
            html += '<div class="blue_ll">' + com.name + '</div>';
            html += '<div>';
            html += '<span class="val">' + tb + '</span><span class="fs10">%</span><span class="red">↓</span>';
            html += '</div>';
            html += '</div>';
            k++
        }
    }
    if (html == '') {
        html += '<div class="itm">';
        html += '<div class="blue_ll">无</div>';
        html += '</div>';
    }
    $('#tabc_l_TRV_RATE .ASC').html(html);



    // 国际 ---------
    var grp_obj = getTrvRate(parent_company, 'INT_TRV_RATE');

    $('#val_grp_i_trv_rate .val').text(grp_obj.value);
    $('#val_grp_i_trv_rate_tb .val').text(grp_obj.tb);

    $('#val_grp_i_trv_rate_tb .green').hide();
    $('#val_grp_i_trv_rate_tb .red').hide();
    $('#val_grp_i_trv_rate_tb .yellow').hide();

    $('#val_grp_i_trv_rate_tb .sub').show();

    if (grp_obj.tb > 0) {
        $('#val_grp_i_trv_rate_tb .green').show();
    } else if (grp_obj.tb < 0) {
        $('#val_grp_i_trv_rate_tb .red').show();
    } else {
        $('#val_grp_i_trv_rate_tb .yellow').show();
        $('#val_grp_i_trv_rate_tb .sub').hide();
        $('#val_grp_i_trv_rate_tb .val').text('-');
    }

    var comp_arr = [];

    // 各个分公司
    var len = companylist.length;
    for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        var compcode = dat.code;
        if (compcode != parent_company) {

            var obj = getTrvRate(compcode, 'INT_TRV_RATE');
            var com = {};
            com.name = dat.nameabbr;
            com.tb = obj.tb;
            comp_arr.push(com);
        }
    }

    comp_arr.sort(function (a, b) {
        return b.tb - a.tb
    });

    // 增长
    var html = '';
    var len = comp_arr.length;
    var k = 0;
    for (var i = 0; i < len; i++) {
        var com = comp_arr[i];
        var tb = com.tb;
        if (Number(tb) > 0 && k < 4) {
            html += '<div class="itm">';
            html += '<div class="blue_ll">' + com.name + '</div>';
            html += '<div>';
            html += '<span class="val">' + tb + '</span><span class="fs10">%</span><span class="green">↑</span>';
            html += '</div>';
            html += '</div>';
            k++
        }
    }
    if (html == '') {
        html += '<div class="itm">';
        html += '<div class="blue_ll">无</div>';
        html += '</div>';
    }
    $('#tabc_i_TRV_RATE .DESC').html(html);

    // 下滑
    comp_arr.reverse();
    var html = '';
    var k = 0;
    for (var i = 0; i < len; i++) {
        var com = comp_arr[i];
        var tb = com.tb;
        if (Number(tb) < 0 && k < 4) {
            html += '<div class="itm">';
            html += '<div class="blue_ll">' + com.name + '</div>';
            html += '<div>';
            html += '<span class="val">' + tb + '</span><span class="fs10">%</span><span class="red">↓</span>';
            html += '</div>';
            html += '</div>';
            k++
        }
    }
    if (html == '') {
        html += '<div class="itm">';
        html += '<div class="blue_ll">无</div>';
        html += '</div>';
    }
    $('#tabc_i_TRV_RATE .ASC').html(html);



    // 集团总体 ---------
    var grp_obj = getTrvRate(parent_company, 'TRV_RATE');

    $('#val_grp_a_trv_rate .val').text(grp_obj.value);
    $('#val_grp_a_trv_rate_tb .val').text(grp_obj.tb);

    $('#val_grp_a_trv_rate_tb .green').hide();
    $('#val_grp_a_trv_rate_tb .red').hide();
    $('#val_grp_a_trv_rate_tb .yellow').hide();

    $('#val_grp_a_trv_rate_tb .sub').show();

    if (grp_obj.tb > 0) {
        $('#val_grp_a_trv_rate_tb .green').show();
    } else if (grp_obj.tb < 0) {
        $('#val_grp_a_trv_rate_tb .red').show();
    } else {
        $('#val_grp_a_trv_rate_tb .yellow').show();
        $('#val_grp_a_trv_rate_tb .sub').hide();
        $('#val_grp_a_trv_rate_tb .val').text('-');
    }

    var comp_arr = [];

    // 各个分公司
    var len = companylist.length;
    for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        var compcode = dat.code;
        if (compcode != parent_company) {

            var obj = getTrvRate(compcode, 'TRV_RATE');
            var com = {};
            com.name = dat.nameabbr;
            com.tb = obj.tb;
            comp_arr.push(com);
        }
    }

    comp_arr.sort(function (a, b) {
        return b.tb - a.tb
    });

    // 增长
    var html = '';
    var len = comp_arr.length;
    var k = 0;
    for (var i = 0; i < len; i++) {
        var com = comp_arr[i];
        var tb = com.tb;
        if (Number(tb) > 0 && k < 4) {
            html += '<div class="itm">';
            html += '<div class="blue_ll">' + com.name + '</div>';
            html += '<div>';
            html += '<span class="val">' + tb + '</span><span class="fs10">%</span><span class="green">↑</span>';
            html += '</div>';
            html += '</div>';
            k++
        }
    }
    if (html == '') {
        html += '<div class="itm">';
        html += '<div class="blue_ll">无</div>';
        html += '</div>';
    }
    $('#tabc_a_TRV_RATE .DESC').html(html);

    // 下滑
    comp_arr.reverse();
    var html = '';
    var k = 0;
    for (var i = 0; i < len; i++) {
        var com = comp_arr[i];
        var tb = com.tb;
        if (Number(tb) < 0 && k < 4) {
            html += '<div class="itm">';
            html += '<div class="blue_ll">' + com.name + '</div>';
            html += '<div>';
            html += '<span class="val">' + tb + '</span><span class="fs10">%</span><span class="red">↓</span>';
            html += '</div>';
            html += '</div>';
            k++
        }
    }
    if (html == '') {
        html += '<div class="itm">';
        html += '<div class="blue_ll">无</div>';
        html += '</div>';
    }
    $('#tabc_a_TRV_RATE .ASC').html(html);
}

// 核心经营指标 -> 小时收入
function setBlockR4T2Kpi() {
    var _inner = function () {
        // 国内 ---------
        var grp_obj = getHour(parent_company, 'INL_HOUR_INC_FUEL');

        $('#val_hour_bq .val').text(grp_obj.value);
        $('#val_hour_tb .val').text(grp_obj.tb);

        $('#val_hour_tb .green').hide();
        $('#val_hour_tb .red').hide();
        $('#val_hour_tb .yellow').hide();

        $('#val_hour_tb .sub').show();

        if (grp_obj.tb > 0) {
            $('#val_hour_tb .green').show();
        } else if (grp_obj.tb < 0) {
            $('#val_hour_tb .red').show();
        } else {
            $('#val_hour_tb .yellow').show();
            $('#val_hour_tb .sub').hide();
            $('#val_hour_tb .val').text('-');
        }

        var comp_arr = [];

        // 各个分公司
        var len = companylist.length;
        for (var i = 0; i < len; i++) {

            var dat = companylist[i];
            var compcode = dat.code;
            if (compcode != parent_company) {

                var obj = getHour(compcode, 'INL_HOUR_INC_FUEL');
                var com = {};
                com.name = dat.nameabbr;
                com.value = obj.value;
                comp_arr.push(com);
            }
        }

        comp_arr.sort(function (a, b) {
            return b.value - a.value
        });

        // 增长
        var html = '';
        var len = comp_arr.length;
        var k = 0;
        for (var i = 0; i < len; i++) {
            var com = comp_arr[i];
            var value = com.value;
            if (value > 0 && k < 4) {
                html += '<div class="itm">';
                html += '<div class="blue_ll">' + com.name + '</div>';
                html += '<div class="green">';
                html += '<span class="val">' + value + '</span><span class="fs10">万元</span>';
                html += '</div>';
                html += '</div>';
                k++
            }
        }
        if (html == '') {
            html += '<div class="itm">';
            html += '<div class="blue_ll">无</div>';
            html += '</div>';
        }
        $('#tabc_hour_INC_FUEL .DESC').html(html);

        // 下滑
        comp_arr.reverse();
        var html = '';
        var k = 0;
        for (var i = 0; i < len; i++) {
            var com = comp_arr[i];
            var value = com.value;
            if (value > 0 && k < 4) {
                html += '<div class="itm">';
                html += '<div class="blue_ll">' + com.name + '</div>';
                html += '<div class="red">';
                html += '<span class="val">' + value + '</span><span class="fs10">万元</span>';
                html += '</div>';
                html += '</div>';
                k++
            }
        }
        if (html == '') {
            html += '<div class="itm">';
            html += '<div class="blue_ll">无</div>';
            html += '</div>';
        }
        $('#tabc_hour_INC_FUEL .ASC').html(html);



        // 国际 ---------
        var grp_obj = getHour(parent_company, 'INT_HOUR_INC_FUEL');

        $('#val_hour_int_bq .val').text(grp_obj.value);
        $('#val_hour_int_tb .val').text(grp_obj.tb);

        $('#val_hour_int_tb .green').hide();
        $('#val_hour_int_tb .red').hide();
        $('#val_hour_int_tb .yellow').hide();

        $('#val_hour_int_tb .sub').show();

        if (grp_obj.tb > 0) {
            $('#val_hour_int_tb .green').show();
        } else if (grp_obj.tb < 0) {
            $('#val_hour_int_tb .red').show();
        } else {
            $('#val_hour_int_tb .yellow').show();
            $('#val_hour_int_tb .sub').hide();
            $('#val_hour_int_tb .val').text('-');
        }

        var comp_arr = [];

        // 各个分公司
        var len = companylist.length;
        for (var i = 0; i < len; i++) {

            var dat = companylist[i];
            var compcode = dat.code;
            if (compcode != parent_company) {

                var obj = getHour(compcode, 'INT_HOUR_INC_FUEL');
                var com = {};
                com.name = dat.nameabbr;
                com.value = obj.value;
                comp_arr.push(com);
            }
        }

        comp_arr.sort(function (a, b) {
            return b.value - a.value
        });

        // 增长
        var html = '';
        var len = comp_arr.length;
        var k = 0;
        for (var i = 0; i < len; i++) {
            var com = comp_arr[i];
            var value = com.value;
            if (value > 0 && k < 4) {
                html += '<div class="itm">';
                html += '<div class="blue_ll">' + com.name + '</div>';
                html += '<div class="green">';
                html += '<span class="val">' + value + '</span><span class="fs10">万元</span>';
                html += '</div>';
                html += '</div>';
                k++
            }
        }
        if (html == '') {
            html += '<div class="itm">';
            html += '<div class="blue_ll">无</div>';
            html += '</div>';
        }
        $('#tabc_hour_INT_FUEL .DESC').html(html);

        // 下滑
        comp_arr.reverse();
        var html = '';
        var k = 0;
        for (var i = 0; i < len; i++) {
            var com = comp_arr[i];
            var value = com.value;
            if (value > 0 && k < 4) {
                html += '<div class="itm">';
                html += '<div class="blue_ll">' + com.name + '</div>';
                html += '<div class="red">';
                html += '<span class="val">' + value + '</span><span class="fs10">万元</span>';
                html += '</div>';
                html += '</div>';
                k++
            }
        }
        if (html == '') {
            html += '<div class="itm">';
            html += '<div class="blue_ll">无</div>';
            html += '</div>';
        }
        $('#tabc_hour_INT_FUEL .ASC').html(html);



        // 集团总体 ---------
        var grp_obj = getHour(parent_company, 'HOUR_INC_FUEL');

        $('#val_hour_group_bq .val').text(grp_obj.value);
        $('#val_hour_group_tb .val').text(grp_obj.tb);

        $('#val_hour_group_tb .green').hide();
        $('#val_hour_group_tb .red').hide();
        $('#val_hour_group_tb .yellow').hide();

        $('#val_hour_group_tb .sub').show();

        if (grp_obj.tb > 0) {
            $('#val_hour_group_tb .green').show();
        } else if (grp_obj.tb < 0) {
            $('#val_hour_group_tb .red').show();
        } else {
            $('#val_hour_group_tb .yellow').show();
            $('#val_hour_group_tb .sub').hide();
            $('#val_hour_group_tb .val').text('-');
        }

        var comp_arr = [];

        // 各个分公司
        var len = companylist.length;
        for (var i = 0; i < len; i++) {

            var dat = companylist[i];
            var compcode = dat.code;
            if (compcode != parent_company) {

                var obj = getHour(compcode, 'HOUR_INC_FUEL');
                var com = {};
                com.name = dat.nameabbr;
                com.value = obj.value;
                comp_arr.push(com);
            }
        }

        comp_arr.sort(function (a, b) {
            return b.value - a.value
        });

        // 增长
        var html = '';
        var len = comp_arr.length;
        var k = 0;
        for (var i = 0; i < len; i++) {
            var com = comp_arr[i];
            var value = com.value;
            if (value > 0 && k < 4) {
                html += '<div class="itm">';
                html += '<div class="blue_ll">' + com.name + '</div>';
                html += '<div class="green">';
                html += '<span class="val">' + value + '</span><span class="fs10">万元</span>';
                html += '</div>';
                html += '</div>';
                k++
            }
        }
        if (html == '') {
            html += '<div class="itm">';
            html += '<div class="blue_ll">无</div>';
            html += '</div>';
        }
        $('#tabc_hour_group_FUEL .DESC').html(html);

        // 下滑
        comp_arr.reverse();
        var html = '';
        var k = 0;
        for (var i = 0; i < len; i++) {
            var com = comp_arr[i];
            var value = com.value;
            if (value > 0 && k < 4) {
                html += '<div class="itm">';
                html += '<div class="blue_ll">' + com.name + '</div>';
                html += '<div class="red">';
                html += '<span class="val">' + value + '</span><span class="fs10">万元</span>';
                html += '</div>';
                html += '</div>';
                k++
            }
        }
        if (html == '') {
            html += '<div class="itm">';
            html += '<div class="blue_ll">无</div>';
            html += '</div>';
        }
        $('#tabc_hour_group_FUEL .ASC').html(html);
    }
    var b = setInterval(function () {
        if (all_company_data['kpi_ratio_sq_d'] != undefined && all_company_data['kpi_ratio_tq_d'] != undefined) {

            _inner();
            clearInterval(b);
        }
    }, 100)
}

// 核心经营指标 -> 座公里收入
function setBlockR3T2Kpi() {

    var _inner = function () {
        var date = getCurrentDate();

        // 国内 ---------

        // 集团总体
        var grp_obj = getKiloInc(parent_company, 'INL_INC', 'INL_CAP_KILO');

        // console.log(parent_company, grp_obj);

        $('#val_grp_l_kilo_inc .val').text(grp_obj.value);
        $('#val_grp_l_kilo_inc_tb .val').text(grp_obj.tb);

        $('#val_grp_l_kilo_inc_tb .green').hide();
        $('#val_grp_l_kilo_inc_tb .red').hide();
        $('#val_grp_l_kilo_inc_tb .yellow').hide();

        $('#val_grp_l_kilo_inc_tb .sub').show();

        if (grp_obj.tb > 0) {
            $('#val_grp_l_kilo_inc_tb .green').show();
        } else if (grp_obj.tb < 0) {
            $('#val_grp_l_kilo_inc_tb .red').show();
        } else {
            $('#val_grp_l_kilo_inc_tb .yellow').show();
            $('#val_grp_l_kilo_inc_tb .sub').hide();
            $('#val_grp_l_kilo_inc_tb .val').text('-');
        }

        var comp_arr = [];

        // 各个分公司
        var len = companylist.length;
        for (var i = 0; i < len; i++) {

            var dat = companylist[i];
            var compcode = dat.code;
            if (compcode != parent_company) {

                var obj = getKiloInc(compcode, 'INL_INC', 'INL_CAP_KILO');
                var com = {};
                com.name = dat.nameabbr;
                com.value = obj.value;
                comp_arr.push(com);
            }
        }

        comp_arr.sort(function (a, b) {
            return b.value - a.value
        });

        // 增长
        var html = '';
        var len = comp_arr.length;
        var k = 0;
        for (var i = 0; i < len; i++) {
            var com = comp_arr[i];
            var value = com.value;
            if (value > 0 && k < 4) {
                html += '<div class="itm">';
                html += '<div class="blue_ll">' + com.name + '</div>';
                html += '<div class="green">';
                html += '<span class="val">' + value + '</span><span class="fs10">元</span>';
                html += '</div>';
                html += '</div>';
                k++
            }
        }
        if (html == '') {
            html += '<div class="itm">';
            html += '<div class="blue_ll">无</div>';
            html += '</div>';
        }
        $('#tabc_l_CAP_KILO_INC_FUEL .DESC').html(html);

        // 下滑
        comp_arr.reverse();
        var html = '';
        var k = 0;
        for (var i = 0; i < len; i++) {
            var com = comp_arr[i];
            var value = com.value;
            if (value > 0 && k < 4) {
                html += '<div class="itm">';
                html += '<div class="blue_ll">' + com.name + '</div>';
                html += '<div class="red">';
                html += '<span class="val">' + value + '</span><span class="fs10">元</span>';
                html += '</div>';
                html += '</div>';
                k++
            }
        }
        if (html == '') {
            html += '<div class="itm">';
            html += '<div class="blue_ll">无</div>';
            html += '</div>';
        }
        $('#tabc_l_CAP_KILO_INC_FUEL .ASC').html(html);



        // 国际---------

        // 集团总体
        var grp_obj = getKiloInc(parent_company, 'INT_INC', 'INT_CAP_KILO');

        $('#val_grp_i_kilo_inc .val').text(grp_obj.value);
        $('#val_grp_i_kilo_inc_tb .val').text(grp_obj.tb);

        $('#val_grp_i_kilo_inc_tb .green').hide();
        $('#val_grp_i_kilo_inc_tb .red').hide();
        $('#val_grp_i_kilo_inc_tb .yellow').hide();

        $('#val_grp_i_kilo_inc_tb .sub').show();

        if (grp_obj.tb > 0) {
            $('#val_grp_i_kilo_inc_tb .green').show();
        } else if (grp_obj.tb < 0) {
            $('#val_grp_i_kilo_inc_tb .red').show();
        } else {
            $('#val_grp_i_kilo_inc_tb .yellow').show();
            $('#val_grp_i_kilo_inc_tb .sub').hide();
            $('#val_grp_i_kilo_inc_tb .val').text('-');
        }

        var comp_arr = [];

        // 各个分公司
        var len = companylist.length;
        for (var i = 0; i < len; i++) {

            var dat = companylist[i];
            var compcode = dat.code;
            if (compcode != parent_company) {

                var obj = getKiloInc(compcode, 'INT_INC', 'INT_CAP_KILO');
                var com = {};
                com.name = dat.nameabbr;
                com.value = obj.value;
                comp_arr.push(com);
            }
        }

        comp_arr.sort(function (a, b) {
            return b.value > a.value ? 1 : -1
        });


        // 增长
        var html = '';
        var len = comp_arr.length;
        var k = 0;
        for (var i = 0; i < len; i++) {
            var com = comp_arr[i];
            var value = com.value;
            if (value > 0 && k < 4) {
                html += '<div class="itm">';
                html += '<div class="blue_ll">' + com.name + '</div>';
                html += '<div class="green">';
                html += '<span class="val">' + value + '</span><span class="fs10">元</span>';
                html += '</div>';
                html += '</div>';
                k++;
            }

        }
        if (html == '') {
            html += '<div class="itm">';
            html += '<div class="blue_ll">无</div>';
            html += '</div>';
        }
        $('#tabc_i_CAP_KILO_INC_FUEL .DESC').html(html);

        // 下滑
        comp_arr.reverse();
        var html = '';
        var k = 0;
        for (var i = 0; i < len; i++) {
            var com = comp_arr[i];
            var value = com.value;
            if (value > 0 && k < 4) {
                html += '<div class="itm">';
                html += '<div class="blue_ll">' + com.name + '</div>';
                html += '<div class="red">';
                html += '<span class="val">' + value + '</span><span class="fs10">元</span>';
                html += '</div>';
                html += '</div>';
                k++;
            }
        }
        if (html == '') {
            html += '<div class="itm">';
            html += '<div class="blue_ll">无</div>';
            html += '</div>';
        }
        $('#tabc_i_CAP_KILO_INC_FUEL .ASC').html(html);



        // 整体---------
        // 集团总体
        var val = all_company_data['kpi_value_d'][parent_company]['CAP_KILO_INC_FUEL'][date_type][date];
        var tb = all_company_data['kpi_ratio_tq_d'][parent_company]['CAP_KILO_INC_FUEL'][date_type][date];
        var inc_kilo = trimDecimal(val, 2);
        var inc_kilo_tb = Math.round(tb * 10000) / 100;
        inc_kilo_tb = trimDecimal(inc_kilo_tb, 2)

        $('#val_grp_a_kilo_inc .val').text(inc_kilo);
        $('#val_grp_a_kilo_inc_tb .val').text(inc_kilo_tb);

        $('#val_grp_a_kilo_inc_tb .green').hide();
        $('#val_grp_a_kilo_inc_tb .red').hide();
        $('#val_grp_a_kilo_inc_tb .yellow').hide();

        $('#val_grp_a_kilo_inc_tb .sub').show();

        if (inc_kilo_tb > 0) {
            $('#val_grp_a_kilo_inc_tb .green').show();
        } else if (inc_kilo_tb < 0) {
            $('#val_grp_a_kilo_inc_tb .red').show();
        } else {
            $('#val_grp_a_kilo_inc_tb .yellow').show();
            $('#val_grp_a_kilo_inc_tb .sub').hide();
            $('#val_grp_a_kilo_inc_tb .val').text('-');
        }

        var comp_arr = [];

        // 各个分公司
        var len = companylist.length;
        for (var i = 0; i < len; i++) {

            var dat = companylist[i];
            var compcode = dat.code;
            if (compcode != parent_company) {
                var val = all_company_data['kpi_value_d'][compcode]['CAP_KILO_INC_FUEL'][date_type][date];
                var com = {};
                com.name = dat.nameabbr;
                com.value = trimDecimal(val, 2);
                comp_arr.push(com);
            }
        }

        comp_arr.sort(function (a, b) {
            return b.value > a.value ? 1 : -1
        });


        // 增长
        var html = '';
        var len = comp_arr.length;
        var k = 0;
        for (var i = 0; i < len; i++) {
            var com = comp_arr[i];
            var value = com.value;
            if (value > 0 && k < 4) {
                html += '<div class="itm">';
                html += '<div class="blue_ll">' + com.name + '</div>';
                html += '<div class="green">';
                html += '<span class="val">' + value + '</span><span class="fs10">元</span>';
                html += '</div>';
                html += '</div>';
                k++;
            }

        }
        if (html == '') {
            html += '<div class="itm">';
            html += '<div class="blue_ll">无</div>';
            html += '</div>';
        }
        $('#tabc_a_CAP_KILO_INC_FUEL .DESC').html(html);

        // 下滑
        comp_arr.reverse();
        var html = '';
        var k = 0;
        for (var i = 0; i < len; i++) {
            var com = comp_arr[i];
            var value = com.value;
            if (value > 0 && k < 4) {
                html += '<div class="itm">';
                html += '<div class="blue_ll">' + com.name + '</div>';
                html += '<div class="red">';
                html += '<span class="val">' + value + '</span><span class="fs10">元</span>';
                html += '</div>';
                html += '</div>';
                k++;
            }
        }
        if (html == '') {
            html += '<div class="itm">';
            html += '<div class="blue_ll">无</div>';
            html += '</div>';
        }
        $('#tabc_a_CAP_KILO_INC_FUEL .ASC').html(html);
    }

    var b = setInterval(function () {
        if (all_company_data['kpi_ratio_sq_d'] != undefined && all_company_data['kpi_ratio_tq_d'] != undefined) {

            _inner();
            clearInterval(b);
        }
    }, 100)
}


// 计算飞机日利用率
function calAcRate(compcode) {
    var date = getCurrentDate();

    var val1 = all_company_data_ac[date_type]['kpi_value_d'][compcode]['FLY_TIME'][date_type][date];
    var val2 = all_company_data_ac[date_type]['kpi_value_d'][compcode]['AC_NUM'][date_type][date];

    // 飞机日利用率
    var rate1 = val2 > 0 ? (val1 / val2) : 0;

    // 同比
    var val1 = all_company_data_ac[date_type]['kpi_value_tq_d'][compcode]['FLY_TIME'][date_type][date];
    var val2 = all_company_data_ac[date_type]['kpi_value_tq_d'][compcode]['AC_NUM'][date_type][date];

    // 飞机日利用率
    var rate2 = val2 > 0 ? (val1 / val2) : 0;

    // 同比=(本期-同期)÷同期×100%
    var tb = rate2 > 0 ? Math.round((rate1 - rate2) / rate2 * 1000) / 10 : 0

    var obj = {};
    obj.rate = rate1;
    obj.tb = tb;
    return obj;
}


// 获取客座率
function getTrvRate(compcode, kpicode) {
    var date = getCurrentDate();

    var val = all_company_data['kpi_value_d'][compcode][kpicode][date_type][date];
    if (isNaN(val)) {
        val = 0;
    } else {
        val = trimDecimal(val * 100, 1);
    }

    var tb = all_company_data['kpi_ratio_tq_d'][compcode][kpicode][date_type][date];
    if (isNaN(tb)) {
        tb = 0;
    } else {
        tb = trimDecimal(tb * 100, 1);
    }

    var obj = {};
    obj.value = val;
    obj.tb = tb;
    return obj;
}


// 获取座公里收入
function getKiloInc(compcode, kpicode, kpicode2) {
    var date = getCurrentDate();

    // 本期--------

    // 收入
    var inc = all_company_data['kpi_value_d'][compcode][kpicode][date_type][date];
    if (isNaN(inc)) {
        inc = 0;
    }
    // console.log('inc', inc);

    // 座公里
    var kilo = all_company_data['kpi_value_d'][compcode][kpicode2][date_type][date];
    if (isNaN(kilo)) {
        kilo = 0;
    }
    // console.log('kilo', kilo);

    // 座公里收入
    var value = Math.round(inc / kilo * 10000 * 100) / 100; // 元
    var value2 = inc / kilo;
    // console.log('座公里收入', value);
    if (isNaN(value) || kilo == 0) {
        value = 0;
    }


    // 同期--------

    // 收入
    // console.log("座公里")
    // console.log(JSON.stringify(all_company_data), all_company_data['kpi_value_tq_d'], compcode)
    var inc_tq = all_company_data['kpi_value_tq_d'][compcode][kpicode][date_type][date];
    if (isNaN(inc_tq)) {
        inc_tq = 0;
    }
    // console.log('inc_tq', inc_tq);

    // 座公里
    var kilo_tq = all_company_data['kpi_value_tq_d'][compcode][kpicode2][date_type][date];
    if (isNaN(kilo_tq)) {
        kilo_tq = 0;
    }
    // console.log('kilo_tq', kilo_tq);

    // 座公里收入
    var value_tq = Math.round(inc_tq / kilo_tq * 10000 * 100) / 100; // 元
    var value_tq2 = inc_tq / kilo_tq;
    // console.log('座公里收入 TG', value_tq);
    if (isNaN(value_tq) || kilo_tq == 0) {
        value_tq = 0;
    }

    // 同比
    // 同比=(本期-同期)÷同期×100%
    var tb = Math.round((value2 - value_tq2) / value_tq2 * 1000) / 10;
    if (isNaN(tb) || value_tq2 == 0) {
        tb = 0;
    }

    // 上期--------

    // 收入
    // console.log("座公里")
    var inc_sq = all_company_data['kpi_value_sq_d'][compcode][kpicode][date_type][date];
    if (isNaN(inc_sq)) {
        inc_sq = 0;
    }

    // 座公里
    var kilo_sq = all_company_data['kpi_value_sq_d'][compcode][kpicode2][date_type][date];
    if (isNaN(kilo_sq)) {
        kilo_sq = 0;
    }

    // 座公里收入
    var value_sq = Math.round(inc_sq / kilo_sq * 10000 * 100) / 100; // 元
    // console.log('座公里收入 TG', value_tq);
    if (isNaN(value_sq) || kilo_sq == 0) {
        value_sq = 0;
    }

    // 环比
    // 环比=(本期-上期)÷同期×100%
    var hb = Math.round((value - value_sq) / value_sq * 1000) / 10;
    if (isNaN(hb) || value_sq == 0) {
        hb = 0;
    }

    // console.log('------------');

    var obj = {};
    obj.value = isNaN(value) ? 0 : value;
    obj.value_tq = isNaN(value_tq) ? 0 : value_tq;
    obj.tb = isNaN(tb) ? 0 : tb;
    obj.value_sq = isNaN(value_sq) ? 0 : value_sq;
    obj.hb = isNaN(hb) ? 0 : hb;
    return obj;
}

// 获取小时收入
function getHour(compcode, kpicode) {
    var date = getCurrentDate();

    // 本期--------

    // 小时收入
    var value = all_company_data['kpi_value_d'][compcode][kpicode][date_type][date];
    if (isNaN(value)) {
        value = 0;
    }
    // console.log('小时收入本期', value);


    // 同期--------

    // 收入
    var value_tq = all_company_data['kpi_value_tq_d'][compcode][kpicode][date_type][date];
    if (isNaN(value_tq)) {
        value_tq = 0;
    }
    // console.log('小时收入同期', value_tq);

    // 同比
    // 同比=(本期-同期)÷同期×100%
    var tb = Math.round((value - value_tq) / value_tq * 1000) / 10;
    if (isNaN(tb) || value_tq == 0) {
        tb = 0;
    }

    // console.log('------------');

    var obj = {};
    obj.value = isNaN(value) ? 0 : Number(value).toFixed(2);
    obj.value_tq = isNaN(value_tq) ? 0 : value_tq;
    obj.tb = isNaN(tb) ? 0 : tb;
    return obj;
}

function getCurrentDate() {
    var date = '';
    if (date_type == 'L') {
        date = $('#main_cb_week').attr('data');
    } else if (date_type == 'M') {
        date = $('#main_cb_month').attr('data');
    } else if (date_type == 'D') {
        date = $('#main_cb_day').attr('data');
    } else if (date_type == 'Y') {
        date = $('#main_cb_year').attr('data');
    }
    return date;
}


function updateAllKpi(data, label) {
    if (!kpiDataReady) {
        return;
    }

    if (date_type == 'L') {
        $('#lb_current_inc').text('本周收入');
    } else if (date_type == 'M') {
        $('#lb_current_inc').text('本月收入');
    } else if (date_type == 'D') {
        $('#lb_current_inc').text('本日收入');
    } else if (date_type == 'Y') {
        $('#lb_current_inc').text('本年收入');
    }

    getYearIncToNow();

    //setMapKpi();
    //setBlockL1Kpi();

    dfd.done(function () {
        if (!hasAllCompanyPermission()) {
            $(".block_cover").remove();
            $(".tab_block").remove();
            return;
        } else {
            setBlockR1Kpi();
            setBlockR2Kpi();
            setBlockR3Kpi();

            setBlockR1T2Kpi();
            setBlockR2T2Kpi();
            setBlockR3T2Kpi();
            setBlockR4T2Kpi();
        }
    })



    if ($('#popover_trend_chart').is(':visible')) {
        showTrendChartPopover(selected_trend_blockid);
    }

    if (current_company_code != parent_company) {
        $('#selected_mapmarkpoint').show();
    } else {
        $('#selected_mapmarkpoint').hide();
    }

    setExtLink();

    setTitleDate();


}



// -------------------------------------------------------

//                    区域弹出图层信息

// -------------------------------------------------------
var previous_mapmarkpoint;

function showCompanyPopover(obj, trans) {
    var compcode = obj.code;


    var mx = obj.mouseX - 18;
    var my = obj.mouseY - 18;

    all_company_data

    var pw = 480; //$('#popover_map').width();
    var ph = 308; //$('#popover_map').height();

    var osw = document.body.offsetWidth;
    var osh = osw / 1366 * 768;


    if (previous_mapmarkpoint) {

        var duration;
        if (trans) {
            duration = 300;
        } else {
            duration = 0;
        }

        $('#popover_map').animate({
            opacity: 0,
        }, {
            queue: false,
            duration: duration,
            step: function (now, fx) {
                if (fx.prop == 'opacity') {
                    var ss = Math.min((now + 0.3), 1);
                    $(this).css('transform', 'scale(' + ss + ')');
                }
            },
            complete: function () {
                moveMarkpoint();
                setData();
                resetPop();
                setTimeout(showPop, 0);
            },
        });


    } else {
        moveMarkpoint();
        setData();
        resetPop();
        setTimeout(showPop, 0);
    }


    function moveMarkpoint() {

        $('#selected_mapmarkpoint').css('left', mx + 'px');
        $('#selected_mapmarkpoint').css('top', my + 'px');

    }

    function resetPop() {

        $('#popover_map').css('transform', 'scale(0.001)');
        $('#popover_map').css('opacity', 0);
        $('#popover_map').css('left', (mx - pw / 2) + 'px');
        $('#popover_map').css('top', (my - ph / 2) + 'px');

    }

    function showPop() {

        $('#popover_map').css('pointer-events', 'auto');

        $('#popover_map').animate({
            left: (osw - pw) / 2 + 'px',
            top: (osh - ph) / 2.5 + 'px',
            opacity: 1,
        }, {
            queue: false,
            duration: 500,
            step: function (now, fx) {
                if (fx.prop == 'opacity') {
                    $(this).css('transform', 'scale(' + now + ')');
                }
            },
        });
    }



    function setData() {
        // 收入 week
        var date = getCurrentDate();

        var compname = companyCode2Name[compcode];
        $('#popover_map .title').text(compname);

        var val = all_company_data['kpi_value_d'][compcode]['EST_INC_FUEL'][date_type][date];
        var task = all_company_data_task[compcode]['INC_TASK'][date_type][date];
        var hb = all_company_data['kpi_ratio_sq_d'][compcode]['EST_INC_FUEL'][date_type][date];
        var tb = all_company_data['kpi_ratio_tq_d'][compcode]['EST_INC_FUEL'][date_type][date];
        //popmap_kpi_value_d_EST_INC_FUEL
        $('#popmap_' + 'kpi_value_d' + '_' + 'EST_INC_FUEL').text(formatCurrency(Math.round(val), 0));

        $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'EST_INC_FUEL .val').text(trimDecimal(tb * 100, 2));
        //选择年使用
        if (date.length == 4) {
            $('#popmap_' + 'Y' + '_' + 'kpi_value_d' + '_' + 'EST_INC_FUEL').text(formatCurrency(Math.round(val), 0));
            $('#popmap_' + 'Y' + '_' + 'kpi_ratio_tq_d' + '_' + 'EST_INC_FUEL .val').text(trimDecimal(tb * 100, 2));
        }
        if (tb > 0) {
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'EST_INC_FUEL .green').removeClass('hide');
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'EST_INC_FUEL .red').addClass('hide');
        } else if (tb < 0) {
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'EST_INC_FUEL .green').addClass('hide');
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'EST_INC_FUEL .red').removeClass('hide');
        } else {
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'EST_INC_FUEL .green').addClass('hide');
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'EST_INC_FUEL .red').addClass('hide');
        }

        // $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .val').text(trimDecimal(hb * 100, 2));
        // if (hb > 0) {
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .green').removeClass('hide');
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .red').addClass('hide');
        // } else if (hb < 0) {
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .green').addClass('hide');
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .red').removeClass('hide');
        // } else {
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .green').addClass('hide');
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .red').addClass('hide');
        // }

        // console.log('popmap_rate', val, task);
        if (task > 0) {
            $('#popmap_rate').text(Math.round(val / task * 1000) / 10 + '%');
        } else {
            $('#popmap_rate').text('-');
        }



        // 收入 年度累计, 累积到当前选中的week

        var date_now = new Date();
        var year_now = date_now.getFullYear();

        var inc_total = year_inc_to_date;
        var inc_total_sq = year_inc_to_date_sq;

        var inc_task = all_company_data_task_year[compcode]['INC_TASK_OIL']['Y'][year_now];

        var hb = year_inc_to_date_sq > 0 ? Math.round((year_inc_to_date - year_inc_to_date_sq) / year_inc_to_date_sq * 10000) / 100 : 0;

        //选择日 周 月使用
        if (date.length > 4) {
            $('#popmap_' + 'Y' + '_' + 'kpi_value_d' + '_' + 'EST_INC_FUEL').text(formatCurrency(Math.round(inc_total), 0));
            $('#popmap_' + 'Y' + '_' + 'kpi_ratio_tq_d' + '_' + 'EST_INC_FUEL .val').text(trimDecimal(hb, 2));
        }

        if (hb > 0) {
            $('#popmap_' + 'Y' + '_' + 'kpi_ratio_tq_d' + '_' + 'EST_INC_FUEL .green').removeClass('hide');
            $('#popmap_' + 'Y' + '_' + 'kpi_ratio_tq_d' + '_' + 'EST_INC_FUEL .red').addClass('hide');
        } else if (hb < 0) {
            $('#popmap_' + 'Y' + '_' + 'kpi_ratio_tq_d' + '_' + 'EST_INC_FUEL .green').addClass('hide');
            $('#popmap_' + 'Y' + '_' + 'kpi_ratio_tq_d' + '_' + 'EST_INC_FUEL .red').removeClass('hide');
        } else {
            $('#popmap_' + 'Y' + '_' + 'kpi_ratio_tq_d' + '_' + 'EST_INC_FUEL .green').addClass('hide');
            $('#popmap_' + 'Y' + '_' + 'kpi_ratio_tq_d' + '_' + 'EST_INC_FUEL .red').addClass('hide');
        }
        // $('#popmap_' + 'Y' + '_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .val').text(trimDecimal(hb, 2));
        // if (hb > 0) {
        //     $('#popmap_' + 'Y' + '_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .green').removeClass('hide');
        //     $('#popmap_' + 'Y' + '_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .red').addClass('hide');
        // } else if (hb < 0) {
        //     $('#popmap_' + 'Y' + '_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .green').addClass('hide');
        //     $('#popmap_' + 'Y' + '_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .red').removeClass('hide');
        // } else {
        //     $('#popmap_' + 'Y' + '_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .green').addClass('hide');
        //     $('#popmap_' + 'Y' + '_' + 'kpi_ratio_sq_d' + '_' + 'EST_INC_FUEL .red').addClass('hide');
        // }
        if (inc_task > 0) {
            $('#popmap_rate_Y').text(Math.round(inc_total / inc_task * 1000) / 10 + '%');
        } else {
            $('#popmap_rate_Y').text('-');
        }


        // 旅客运输量 week
        var tb = all_company_data['kpi_ratio_tq_d'][compcode]['TRV_NUM'][date_type][date];
        if (isNaN(tb)) {
            tb = '0';
        } else {
            tb = trimDecimal(tb * 100, 2);
        }
        $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'TRV_NUM .val').text(tb);
        if (tb > 0) {
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'TRV_NUM .green').removeClass('hide');
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'TRV_NUM .red').addClass('hide');
        } else if (tb < 0) {
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'TRV_NUM .green').addClass('hide');
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'TRV_NUM .red').removeClass('hide');
        } else {
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'TRV_NUM .green').addClass('hide');
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'TRV_NUM .red').addClass('hide');
        }
        // var hb = all_company_data['kpi_ratio_sq_d'][compcode]['TRV_NUM'][date_type][date];
        // if (isNaN(hb)) {
        //     hb = '0';
        // } else {
        //     hb = trimDecimal(hb * 100, 2);
        // }
        // $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'TRV_NUM .val').text(hb);
        // if (hb > 0) {
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'TRV_NUM .green').removeClass('hide');
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'TRV_NUM .red').addClass('hide');
        // } else if (hb < 0) {
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'TRV_NUM .green').addClass('hide');
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'TRV_NUM .red').removeClass('hide');
        // } else {
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'TRV_NUM .green').addClass('hide');
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'TRV_NUM .red').addClass('hide');
        // }


        // 客座率 week
        var tb = all_company_data['kpi_ratio_tq_d'][compcode]['TRV_RATE'][date_type][date];
        if (isNaN(tb)) {
            tb = '0';
        } else {
            tb = trimDecimal(tb * 100, 2);
        }
        $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'TRV_RATE .val').text(tb);
        if (tb > 0) {
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'TRV_RATE .green').removeClass('hide');
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'TRV_RATE .red').addClass('hide');
        } else if (tb < 0) {
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'TRV_RATE .green').addClass('hide');
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'TRV_RATE .red').removeClass('hide');
        } else {
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'TRV_RATE .green').addClass('hide');
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'TRV_RATE .red').addClass('hide');
        }
        // var hb = all_company_data['kpi_ratio_sq_d'][compcode]['TRV_RATE'][date_type][date];
        // if (isNaN(hb)) {
        //     hb = '0';
        // } else {
        //     hb = trimDecimal(hb * 100, 2);
        // }
        // $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'TRV_RATE .val').text(hb);
        // if (hb > 0) {
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'TRV_RATE .green').removeClass('hide');
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'TRV_RATE .red').addClass('hide');
        // } else if (hb < 0) {
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'TRV_RATE .green').addClass('hide');
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'TRV_RATE .red').removeClass('hide');
        // } else {
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'TRV_RATE .green').addClass('hide');
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'TRV_RATE .red').addClass('hide');
        // }

        // 小时收 week
        var tb = all_company_data['kpi_ratio_tq_d'][compcode]['HOUR_INC_FUEL'][date_type][date];
        if (isNaN(tb)) {
            tb = '0';
        } else {
            tb = trimDecimal(tb * 100, 2);
        }
        $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'HOUR_INC_FUEL .val').text(tb);
        if (tb > 0) {
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'HOUR_INC_FUEL .green').removeClass('hide');
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'HOUR_INC_FUEL .red').addClass('hide');
        } else if (tb < 0) {
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'HOUR_INC_FUEL .green').addClass('hide');
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'HOUR_INC_FUEL .red').removeClass('hide');
        } else {
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'HOUR_INC_FUEL .green').addClass('hide');
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'HOUR_INC_FUEL .red').addClass('hide');
        }
        // var hb = all_company_data['kpi_ratio_sq_d'][compcode]['HOUR_INC_FUEL'][date_type][date];
        // if (isNaN(hb)) {
        //     hb = '0';
        // } else {
        //     hb = trimDecimal(hb * 100, 2);
        // }
        // $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'HOUR_INC_FUEL .val').text(hb);
        // if (hb > 0) {
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'HOUR_INC_FUEL .green').removeClass('hide');
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'HOUR_INC_FUEL .red').addClass('hide');
        // } else if (hb < 0) {
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'HOUR_INC_FUEL .green').addClass('hide');
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'HOUR_INC_FUEL .red').removeClass('hide');
        // } else {
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'HOUR_INC_FUEL .green').addClass('hide');
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'HOUR_INC_FUEL .red').addClass('hide');
        // }

        // 座收 week
        var tb = all_company_data['kpi_ratio_tq_d'][compcode]['CAP_KILO_INC_FUEL'][date_type][date];
        if (isNaN(tb)) {
            tb = '0';
        } else {
            tb = trimDecimal(tb * 100, 2);
        }
        $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'CAP_KILO_INC_FUEL .val').text(tb);
        if (tb > 0) {
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'CAP_KILO_INC_FUEL .green').removeClass('hide');
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'CAP_KILO_INC_FUEL .red').addClass('hide');
        } else if (tb < 0) {
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'CAP_KILO_INC_FUEL .green').addClass('hide');
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'CAP_KILO_INC_FUEL .red').removeClass('hide');
        } else {
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'CAP_KILO_INC_FUEL .green').addClass('hide');
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'CAP_KILO_INC_FUEL .red').addClass('hide');
        }
        // var hb = all_company_data['kpi_ratio_sq_d'][compcode]['CAP_KILO_INC_FUEL'][date_type][date];
        // if (isNaN(hb)) {
        //     hb = '0';
        // } else {
        //     hb = trimDecimal(hb * 100, 2);
        // }
        // $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'CAP_KILO_INC_FUEL .val').text(hb);
        // if (hb > 0) {
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'CAP_KILO_INC_FUEL .green').removeClass('hide');
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'CAP_KILO_INC_FUEL .red').addClass('hide');
        // } else if (hb < 0) {
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'CAP_KILO_INC_FUEL .green').addClass('hide');
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'CAP_KILO_INC_FUEL .red').removeClass('hide');
        // } else {
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'CAP_KILO_INC_FUEL .green').addClass('hide');
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'CAP_KILO_INC_FUEL .red').addClass('hide');
        // }

        // 座收国际
        var grp_obj = getKiloInc(compcode, 'INT_INC', 'INT_CAP_KILO');
        var tb = grp_obj.tb;
        if (isNaN(tb)) {
            tb = '0';
        } else {
            tb = trimDecimal(tb, 2);
        }
        $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'INT_CAP_KILO .val').text(tb);
        if (tb > 0) {
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'INT_CAP_KILO .green').removeClass('hide');
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'INT_CAP_KILO .red').addClass('hide');
        } else if (tb < 0) {
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'INT_CAP_KILO .green').addClass('hide');
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'INT_CAP_KILO .red').removeClass('hide');
        } else {
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'INT_CAP_KILO .green').addClass('hide');
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'INT_CAP_KILO .red').addClass('hide');
        }
        // var grp_obj = getKiloInc(compcode, 'INT_INC', 'INT_CAP_KILO');
        // var hb = grp_obj.hb;
        // if (isNaN(hb)) {
        //     hb = '0';
        // } else {
        //     hb = trimDecimal(hb, 2);
        // }
        // $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'INT_CAP_KILO .val').text(hb);
        // if (hb > 0) {
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'INT_CAP_KILO .green').removeClass('hide');
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'INT_CAP_KILO .red').addClass('hide');
        // } else if (hb < 0) {
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'INT_CAP_KILO .green').addClass('hide');
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'INT_CAP_KILO .red').removeClass('hide');
        // } else {
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'INT_CAP_KILO .green').addClass('hide');
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'INT_CAP_KILO .red').addClass('hide');
        // }

        // 座收国内
        var grp_obj = getKiloInc(compcode, 'INL_INC', 'INL_CAP_KILO');
        var tb = grp_obj.tb;
        if (isNaN(tb)) {
            tb = '0';
        } else {
            tb = trimDecimal(tb, 2);
        }
        $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'INL_CAP_KILO .val').text(tb);
        if (tb > 0) {
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'INL_CAP_KILO .green').removeClass('hide');
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'INL_CAP_KILO .red').addClass('hide');
        } else if (tb < 0) {
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'INL_CAP_KILO .green').addClass('hide');
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'INL_CAP_KILO .red').removeClass('hide');
        } else {
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'INL_CAP_KILO .green').addClass('hide');
            $('#popmap_' + 'kpi_ratio_tq_d' + '_' + 'INL_CAP_KILO .red').addClass('hide');
        }

        // var grp_obj = getKiloInc(compcode, 'INL_INC', 'INL_CAP_KILO');
        // var hb = grp_obj.hb;
        // if (isNaN(hb)) {
        //     hb = '0';
        // } else {
        //     hb = trimDecimal(hb, 2);
        // }
        // $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'INL_CAP_KILO .val').text(hb);
        // if (hb > 0) {
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'INL_CAP_KILO .green').removeClass('hide');
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'INL_CAP_KILO .red').addClass('hide');
        // } else if (hb < 0) {
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'INL_CAP_KILO .green').addClass('hide');
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'INL_CAP_KILO .red').removeClass('hide');
        // } else {
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'INL_CAP_KILO .green').addClass('hide');
        //     $('#popmap_' + 'kpi_ratio_sq_d' + '_' + 'INL_CAP_KILO .red').addClass('hide');
        // }


    }


    previous_mapmarkpoint = {};
    previous_mapmarkpoint.x = mx;
    previous_mapmarkpoint.y = my;

}

function hideCompanyPopover() {
    $('#popover_map').css('opacity', 0);
    $('#popover_map').css('pointer-events', 'none');
    previous_mapmarkpoint = undefined;

}

$('#popover_map .close').on('click', function (evt) {
    hideCompanyPopover();
})



function showChartPopover(type) {
    $("#income_week").hide();
    $('#popover_trend_chart').fadeOut();
    if (!all_company_data) {
        return;
    }
    var _type = $(".date_type_select .hover").data("type");

    var typeObj = {
        "L": "#main_cb_week",
        "D": "#main_cb_day",
        "M": "#main_cb_month",
        "Y": "#main_cb_year"
    }
    var timeTitle = $(typeObj[_type] + " .combobox_label").text();
    if (chart_pop1 && chart_pop1.clear) {
        chart_pop1.clear();
    }
    if (chart_pop2 && chart_pop2.clear) {
        chart_pop2.clear();
    }

    if (type == 1) {
        //预算完成情况 总收入
        $('#popover_chart .tit').text(timeTitle + '收入预算完成情况');
        $('#popover_chart .legend1').text('收入（万元）');
        $('#popover_chart .legend1b').hide();
        $('#popover_chart .legend2').text('预算完成率');
        $('#popover_chart .note').text('备注：收入预算完成率=当月收入/月度预算收入，数据来源为航空股份report系统。');

        setChartPop1();

    } else if (type == 2) {
        //预算完成情况 日利用率
        if (!all_company_data_ac) {
            return;
        }
        $('#popover_chart .tit').text(timeTitle + '飞机日利用率预算完成情况');
        $('#popover_chart .legend1').text('日利用率（小时）');
        $('#popover_chart .legend1b').hide();
        $('#popover_chart .legend2').text('预算完成率');
        $('#popover_chart .note').text('备注：飞机利用率预算完成率=当月飞机利用率/月度预算飞机利用率，数据来源为航空股份report系统。');

        setChartPop2();

    } else if (type == 3) {
        //预算完成情况 小时收入
        $('#popover_chart .tit').text(timeTitle + '小时收入预算完成情况');
        $('#popover_chart .legend1').text('小时收入（万元）');
        $('#popover_chart .legend1b').hide();
        $('#popover_chart .legend2').text('预算完成率');
        $('#popover_chart .note').text('备注：小时收入预算完成率=当月小时收入/月度预算小时收入，数据来源为航空股份report系统。');

        setChartPop3();

    } else if (type == 4) {
        // 核心经营指标 日利用率 
        $('#popover_chart .tit').text(timeTitle + '飞机日利用率同比情况');
        $('#popover_chart .legend1').text('日利用率 本期（小时）');
        $('#popover_chart .legend1b').text('同期');
        $('#popover_chart .legend1b').show();
        $('#popover_chart .legend2').text('同比');
        $('#popover_chart .note').text('备注：数据来源为航空股份report系统。');

        setChartPop4();

    } else if (type == 5) {
        // 核心经营指标 客座率
        $('#popover_chart .tit').text(timeTitle + '国内客座率同比情况');
        $('#popover_chart .legend1').text('客座率');
        $('#popover_chart .legend1b').text('同期');
        $('#popover_chart .legend1b').show();
        $('#popover_chart .legend2').text('同比');
        $('#popover_chart .note').text('备注：客座率同比=本期客座率-同期客座率，数据来源为航空股份report系统');

        setChartPop5();

    } else if (type == 6) {
        //核心经营指标  国内座公里收入
        $('#popover_chart .tit').text(timeTitle + '国内座公里收入同比情况');
        $('#popover_chart .legend1').text('座公里收入（元）');
        $('#popover_chart .legend1b').text('同期');
        $('#popover_chart .legend1b').show();
        $('#popover_chart .legend2').text('同比');
        $('#popover_chart .note').text('备注：数据来源为航空股份report系统。');

        setChartPop6();

    } else if (type == 7) {
        // 核心经营指标 国际座公里收入
        $('#popover_chart .tit').text(timeTitle + '国际座公里收入同比情况');
        $('#popover_chart .legend1').text('座公里收入（元）');
        $('#popover_chart .legend1b').text('同期');
        $('#popover_chart .legend1b').show();
        $('#popover_chart .legend2').text('同比');
        $('#popover_chart .note').text('备注：数据来源为航空股份report系统。');

        setChartPop7();

    } else if (type == 8) {
        // 核心经营指标 座公里收入
        $('#popover_chart .tit').text(timeTitle + '整体座公里收入同比情况');
        $('#popover_chart .legend1').text('座公里收入（元）');
        $('#popover_chart .legend1b').text('同期');
        $('#popover_chart .legend1b').show();
        $('#popover_chart .legend2').text('同比');
        $('#popover_chart .note').text('备注：数据来源为航空股份report系统。');

        setChartPop8();

    } else if (type == 9) {
        // 核心经营指标 国际客座率
        $('#popover_chart .tit').text(timeTitle + '国际客座率同比情况');
        $('#popover_chart .legend1').text('客座率');
        $('#popover_chart .legend1b').text('同期');
        $('#popover_chart .legend1b').show();
        $('#popover_chart .legend2').text('同比');
        $('#popover_chart .note').text('备注：客座率同比=(本期客座率-同期客座率)/同期客座率，数据来源为航空股份report系统');

        setChartPop9();

    } else if (type == 10) {
        // 核心经营指标 整体客座率
        $('#popover_chart .tit').text(timeTitle + '整体客座率同比情况');
        $('#popover_chart .legend1').text('客座率');
        $('#popover_chart .legend1b').text('同期');
        $('#popover_chart .legend1b').show();
        $('#popover_chart .legend2').text('同比');
        $('#popover_chart .note').text('备注：客座率同比=(本期客座率-同期客座率)/同期客座率，数据来源为航空股份report系统');

        setChartPop10();

    } else if (type == 11) {
        // 核心经营指标 整体小时收入
        $('#popover_chart .tit').text(timeTitle + '整体小时收入同比情况');
        $('#popover_chart .legend1').text('小时收入');
        $('#popover_chart .legend1b').text('同期');
        $('#popover_chart .legend1b').show();
        $('#popover_chart .legend2').text('同比');
        $('#popover_chart .note').text('备注：数据来源为航空股份report系统');

        setChartPop11();

    } else if (type == 12) {
        // 核心经营指标 国内小时收入
        $('#popover_chart .tit').text(timeTitle + '国内小时收入同比情况');
        $('#popover_chart .legend1').text('小时收入');
        $('#popover_chart .legend1b').text('同期');
        $('#popover_chart .legend1b').show();
        $('#popover_chart .legend2').text('同比');
        $('#popover_chart .note').text('备注：数据来源为航空股份report系统');

        setChartPop12();

    } else if (type == 13) {
        // 核心经营指标 国际小时收入
        $('#popover_chart .tit').text(timeTitle + '国际小时收入同比情况');
        $('#popover_chart .legend1').text('小时收入');
        $('#popover_chart .legend1b').text('同期');
        $('#popover_chart .legend1b').show();
        $('#popover_chart .legend2').text('同比');
        $('#popover_chart .note').text('备注：数据来源为航空股份report系统');

        setChartPop13();

    }

    $('#popover_chart').fadeIn();
}


var chart_pop1;
var chart_pop2;

// 总收入
function setChartPop1() {
    // ---------------------------
    var chart_id1 = 'chart_pop1';
    var chart_id2 = 'chart_pop2';
    var kpi_code = 'EST_INC_FUEL';
    var colors = [
        ['#00c311', '#007892'], //柱状图渐变颜色
        ['#fbc900', ''], //折线颜色
    ];
    // ---------------------------

    var dataArray = [];

    var date = getCurrentDate();

    var kpidata = all_company_data['kpi_value_d'];
    for (var compcode in kpidata) {
        if (compcode != parent_company) {
            var obj = kpidata[compcode];

            // 预算完成率
            var val1 = obj[kpi_code][date_type][date];
            var val2 = all_company_data_task[compcode]['INC_TASK'][date_type][date];
            var rate = val2 > 0 ? Math.round((val1 / val2) * 1000) / 10 : 0;

            dataArray.push({
                'sort': companyCode2Sort[compcode],
                'nameabbr': companyCode2Nameabbr[compcode],
                'value': val1,
                'rate': rate
            });
        }
    }

    dataArray.sort(function (a, b) {
        return a.sort - b.sort
    });

    var xAxisData = [];
    var data_s1 = []; //主KPI
    var data_s2 = []; //预算完成率


    var len = dataArray.length;
    for (var i = 0; i < len; i++) {
        var obj = dataArray[i];
        xAxisData.push(obj.nameabbr);
        data_s1.push(Math.round(obj.value));
        data_s2.push(Math.round(obj.rate));
    }



    // ---------------------------------------------------------
    // 主KPI图表
    chart_pop1 = echarts.init(document.getElementById(chart_id1));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                return params.name + ': ' + params.value;
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 66,
            right: 20,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
                formatter: '{value}'
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'bar',
            barWidth: 12,
            data: data_s1,
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    color: colors[0][0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[0][0]
                        }, {
                            offset: 1,
                            color: colors[0][1]
                        }]),
                }
            },
        }]
    };

    chart_pop1.setOption(option);



    // ---------------------------------------------------------
    // 预算完成率 图表
    chart_pop2 = echarts.init(document.getElementById(chart_id2));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value) + '%';
                return params.name + ': ' + params.value + '%';
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 66,
            right: 20,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                show: false,
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                show: false,
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            //max: 130,
            //min: 70,
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                show: false,
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'line',
            symbol: 'rect',
            symbolSize: 6,
            smooth: false,
            data: data_s2,
            label: {
                normal: {
                    show: true,
                    //position: 'top'
                }
            },
            lineStyle: {
                normal: {
                    color: colors[1][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[1][0],
                    opacity: 1,
                }
            },
            markLine: {
                silent: true,
                data: [{
                    name: '',
                    yAxis: 95,
                    lineStyle: {
                        normal: {
                            color: '#f82c20',
                            type: 'dashed',
                        }
                    },
                }, {
                    name: '',
                    yAxis: 100,
                    lineStyle: {
                        normal: {
                            color: '#00b91b',
                            type: 'dashed',
                        }
                    },
                }],
                symbol: '',
                symbolSize: 0,
                label: {
                    normal: {
                        position: 'start',
                        formatter: '{c}%',
                    }
                },
            },
        },]
    };

    chart_pop2.setOption(option);

}


// 日利用率

function setChartPop2() {
    //'FLY_TIME', //飞行时长
    //'AC_NUM', //飞机架次
    // ---------------------------
    var chart_id1 = 'chart_pop1';
    var chart_id2 = 'chart_pop2';

    var colors = [
        ['#00c311', '#007892'], //柱状图渐变颜色
        ['#fbc900', ''], //折线颜色
    ];
    // ---------------------------

    var dataArray = [];

    var date = getCurrentDate();
    console.log("****", all_company_data_ac)

    var kpidata = all_company_data_ac[date_type]['kpi_value_d'];
    for (var compcode in kpidata) {
        if (compcode != parent_company) {
            var obj = kpidata[compcode];

            var val1 = obj['FLY_TIME'][date_type][date];
            var val2 = obj['AC_NUM'][date_type][date];
            var rate1 = val2 > 0 ? Math.round((val1 / val2) * 10) / 10 : 0;

            // 预算 飞机日利用率
            var val1 = all_company_data_task[compcode]['FLY_TIME_TASK'][date_type][date];
            var val2 = all_company_data_task[compcode]['AC_NUM_TASK'][date_type][date];
            var rate2 = val2 > 0 ? (val1 / val2) : 0;

            // 预算完成率
            var rate = rate2 > 0 ? Math.round((rate1 / rate2) * 1000) / 10 : 0;

            dataArray.push({
                'sort': companyCode2Sort[compcode],
                'nameabbr': companyCode2Nameabbr[compcode],
                'value': rate1,
                'rate': rate,
            });

        }
    }

    dataArray.sort(function (a, b) {
        return a.sort - b.sort
    });

    var xAxisData = [];
    var data_s1 = []; //主KPI
    var data_s2 = []; //预算完成率


    var len = dataArray.length;
    for (var i = 0; i < len; i++) {
        var obj = dataArray[i];
        xAxisData.push(obj.nameabbr);
        data_s1.push(Math.round(obj.value * 10) / 10);
        data_s2.push(obj.rate);
    }



    // ---------------------------------------------------------
    // 主KPI图表
    chart_pop1 = echarts.init(document.getElementById(chart_id1));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                return params.name + ': ' + params.value;
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 66,
            right: 20,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
                formatter: '{value}'
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'bar',
            barWidth: 12,
            data: data_s1,
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    color: colors[0][0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[0][0]
                        }, {
                            offset: 1,
                            color: colors[0][1]
                        }]),
                }
            },
        }]
    };

    chart_pop1.setOption(option);



    // ---------------------------------------------------------
    // 预算完成率 图表
    chart_pop2 = echarts.init(document.getElementById(chart_id2));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value) + '%';
                return params.name + ': ' + params.value + '%';
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 66,
            right: 20,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                show: false,
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                show: false,
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            //max: 130,
            //min: 70,
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                show: false,
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'line',
            symbol: 'rect',
            symbolSize: 6,
            smooth: false,
            data: data_s2,
            label: {
                normal: {
                    show: true,
                    //position: 'top'
                }
            },
            lineStyle: {
                normal: {
                    color: colors[1][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[1][0],
                    opacity: 1,
                }
            },
            markLine: {
                silent: true,
                data: [{
                    name: '',
                    yAxis: 95,
                    lineStyle: {
                        normal: {
                            color: '#f82c20',
                            type: 'dashed',
                        }
                    },
                }, {
                    name: '',
                    yAxis: 100,
                    lineStyle: {
                        normal: {
                            color: '#00b91b',
                            type: 'dashed',
                        }
                    },
                }],
                symbol: '',
                symbolSize: 0,
                label: {
                    normal: {
                        position: 'start',
                        formatter: '{c}%',
                    }
                },
            },
        },]
    };

    chart_pop2.setOption(option);

}


// 小时收入
function setChartPop3() {
    // ---------------------------
    var chart_id1 = 'chart_pop1';
    var chart_id2 = 'chart_pop2';
    var kpi_code = 'HOUR_INC_FUEL';
    var colors = [
        ['#00c311', '#007892'], //柱状图渐变颜色
        ['#fbc900', ''], //折线颜色
    ];
    // ---------------------------

    var dataArray = [];

    var date = getCurrentDate();

    var kpidata = all_company_data['kpi_value_d'];
    for (var compcode in kpidata) {
        if (compcode != parent_company) {
            var obj = kpidata[compcode];

            var val1 = obj[kpi_code][date_type][date];

            var val2 = all_company_data_task[compcode]['INC_TASK'][date_type][date];
            var val3 = all_company_data_task[compcode]['FLY_TIME_TASK'][date_type][date];
            // 任务小时收入=任务总收入／任务飞行时间
            var task = val3 > 0 ? (val2 / val3) : 0;

            // 预算完成率
            var rate = task > 0 ? Math.round(val1 / task * 1000) / 10 : 0;

            dataArray.push({
                'sort': companyCode2Sort[compcode],
                'nameabbr': companyCode2Nameabbr[compcode],
                'value': val1,
                'rate': rate
            });
        }
    }

    dataArray.sort(function (a, b) {
        return a.sort - b.sort
    });

    var xAxisData = [];
    var data_s1 = []; //主KPI
    var data_s2 = []; //预算完成率


    var len = dataArray.length;
    for (var i = 0; i < len; i++) {
        var obj = dataArray[i];
        xAxisData.push(obj.nameabbr);
        data_s1.push(Math.round(obj.value * 100) / 100);
        data_s2.push(obj.rate);
    }



    // ---------------------------------------------------------
    // 主KPI图表
    chart_pop1 = echarts.init(document.getElementById(chart_id1));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                return params.name + ': ' + params.value;
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 66,
            right: 20,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
                formatter: '{value}'
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'bar',
            barWidth: 12,
            data: data_s1,
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    color: colors[0][0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[0][0]
                        }, {
                            offset: 1,
                            color: colors[0][1]
                        }]),
                }
            },
        }]
    };

    chart_pop1.setOption(option);



    // ---------------------------------------------------------
    // 预算完成率 图表
    chart_pop2 = echarts.init(document.getElementById(chart_id2));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value) + '%';
                return params.name + ': ' + params.value + '%';
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 66,
            right: 20,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                show: false,
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                show: false,
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            //max: 130,
            //min: 70,
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                show: false,
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'line',
            symbol: 'rect',
            symbolSize: 6,
            smooth: false,
            data: data_s2,
            label: {
                normal: {
                    show: true,
                    //position: 'top'
                }
            },
            lineStyle: {
                normal: {
                    color: colors[1][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[1][0],
                    opacity: 1,
                }
            },
            markLine: {
                silent: true,
                data: [{
                    name: '',
                    yAxis: 95,
                    lineStyle: {
                        normal: {
                            color: '#f82c20',
                            type: 'dashed',
                        }
                    },
                }, {
                    name: '',
                    yAxis: 100,
                    lineStyle: {
                        normal: {
                            color: '#00b91b',
                            type: 'dashed',
                        }
                    },
                }],
                symbol: '',
                symbolSize: 0,
                label: {
                    normal: {
                        position: 'start',
                        formatter: '{c}%',
                    }
                },
            },
        },]
    };

    chart_pop2.setOption(option);

}

// 本周收入趋势情况
function setChartWeekTrend(compcode) {
    $("#income_week").show();
    // ---------------------------
    var chart_id1 = 'bar_week';
    var chart_id2 = "line_week"
    var colors = [
        ['#00c311', '#007892'], //柱状图渐变颜色
        ['#6ec7f6', '#03a5ed'], //柱状图渐变颜色
        ['#fbc900', ''], //折线颜色
    ];
    // ---------------------------

    var xAxisData = [];
    var data_s1 = []; //本期
    var data_s2 = []; //同期
    var data_s3 = []; //同比


    // 本期
    var dataArray = [];
    console.log("week_trend")
    console.log(all_company_data)
    var date = getCurrentDate();
    var kpidata = all_company_data['kpi_value_d'][compcode]['EST_INC_FUEL'][date_type];
    var dateArr = [];
    for (var p in kpidata) {
        dateArr.push(p);
    }
    var sixDateArr = [];
    for (var i = 0; i < 6; i++) {
        var _index = dateArr.indexOf(date) - i;

        if (_index > -1) {
            var _d = dateArr[_index];
            // 把最近的数据送入数组
            data_s1.push(kpidata[dateArr[_index]]);
            if (date_type == "D") {
                var _value = [_d.substr(0, 4) + "年", _d.substr(4, 2) + "月" + _d.substr(6, 2) + "日"].join("\n");
                xAxisData.push(_value)
            } else if (date_type == "L") {
                xAxisData.push("第" + (Number(_d.substr(4, 3)) + 1) + "周");
            } else if (date_type == "M") {
                var _value = [_d.substr(0, 4) + "年", Number(_d.substr(4, 2)) + "月"].join("\n");
                xAxisData.push(_value);
            } else if (date_type == "Y") {
                var _value = [_d + "年"];
                xAxisData.push(_value);
            }
        }
    }


    // 同期
    var kpidata2 = all_company_data['kpi_value_tq_d'][compcode]['EST_INC_FUEL'][date_type];

    for (var i = 0; i < 6; i++) {
        var _index = dateArr.indexOf(date) - i;
        if (_index > -1) {

            // 把最近的数据送入数组
            if (kpidata2[dateArr[_index]] == undefined) {
                /* data_s2.push(null); */
                data_s2.push('0');
            } else {
                data_s2.push(kpidata2[dateArr[_index]]);
            }

        }
    }


    // 同比


    // s3
    var len = data_s1.length;
    for (var i = 0; i < len; i++) {
        var val = data_s1[i];
        var val_tq = data_s2[i];
        // 同比=(本期-同期)÷同期×100%
        if (val_tq != '0' || val_tq != null || val_tq != undefined) {
            var tb = Math.round((val - val_tq) / val_tq * 10000) / 100;
        } else {
            var tb = '0';
        }

        if (isNaN(tb) || val_tq == 0) {
            tb = 0;
        }
        data_s3.push(tb);
    }

    // ---------------------------------------------------------
    // 主KPI图表
    chart_pop1 = echarts.init(document.getElementById(chart_id1));

    var option1 = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                return params.name + ': ' + params.value;
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 12,
            itemHeight: 12,
            padding: 10,
            // itemGap: 10,
            textStyle: {
                color: '#41a8ff',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: "收入"
            }, {
                name: "同比"
            }]
        },
        grid: {
            top: "15%",
            left: "15%",
            right: "15%",
            bottom: "15%"
        },
        xAxis: [{
            type: 'category',
            data: xAxisData.reverse(),
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            position: "left",
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
                formatter: '{value}'
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '收入',
            type: 'bar',
            barWidth: 15,
            data: data_s1.reverse(),
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    formatter: function (param) {
                        return Number(param.value).toFixed(0);
                    },
                    color: colors[0][0],
                    offset: [7, 0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[0][0]
                        }, {
                            offset: 1,
                            color: colors[0][1]
                        }]),
                }
            },
        }]
    };

    chart_pop1.setOption(option1);

    chart_pop2 = echarts.init(document.getElementById(chart_id2));

    var option2 = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value) + '%';
                return params.name + ': ' + params.value + '%';
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: "30%",
            left: "15%",
            right: "15%",
            bottom: "15%"
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                show: false,
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                show: false,
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            //max: 100,
            //min: -100,
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                show: false,
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'line',
            symbol: 'rect',
            symbolSize: 6,
            smooth: false,
            data: data_s3.reverse(),
            label: {
                normal: {
                    show: true,
                    //position: 'top'
                }
            },
            lineStyle: {
                normal: {
                    color: colors[2][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[2][0],
                    opacity: 1,
                }
            },
            markLine: {
                silent: true,
                data: [{
                    name: '',
                    yAxis: 0,
                    lineStyle: {
                        normal: {
                            color: '#f82c20',
                            type: 'dashed',
                        }
                    },
                }],
                symbol: '',
                symbolSize: 0,
                label: {
                    normal: {
                        position: 'start',
                        formatter: '{c}%',
                    }
                },
            },
        },]
    };

    chart_pop2.setOption(option2);


}
// setTimeout(function() {
//     setChartWeekTrend("HNAHK");
// }, 3000)
// 核心经营指标 -> 飞机日利用率同比
function setChartPop4() {
    //'FLY_TIME', //飞行时长
    //'AC_NUM', //飞机架次
    // ---------------------------
    var chart_id1 = 'chart_pop1';
    var chart_id2 = 'chart_pop2';

    var colors = [
        ['#00c311', '#007892'], //柱状图渐变颜色
        ['#6ec7f6', '#03a5ed'], //柱状图渐变颜色
        ['#fbc900', ''], //折线颜色
    ];
    // ---------------------------

    var xAxisData = [];
    var data_s1 = []; //主KPI
    var data_s2 = []; //主KPI 同期
    var data_s3 = []; //同比
    var dataEchar_1 = [];
    var dataEchar_2 = [];


    // 当期
    var dataArray = [];

    var date = getCurrentDate();
    console.log("****", all_company_data_ac)
    var kpidata = all_company_data_ac[date_type]['kpi_value_d'];
    console.log(kpidata, parent_company)
    for (var compcode in kpidata) {
        if (compcode != parent_company) {
            var obj = kpidata[compcode];
            var val1 = obj['FLY_TIME'][date_type][date];
            var val2 = obj['AC_NUM'][date_type][date];
            var rate = val2 > 0 ? val1 / val2 : 0;
            dataArray.push({
                'sort': companyCode2Sort[compcode],
                'nameabbr': companyCode2Nameabbr[compcode],
                'value': rate,
            });
        }
    }

    dataArray.sort(function (a, b) {
        return a.sort - b.sort
    });

    // s1
    var len = dataArray.length;
    for (var i = 0; i < len; i++) {
        var obj = dataArray[i];
        xAxisData.push(obj.nameabbr);
        /* data_s1.push(Math.round(obj.value * 10) / 10); */
        data_s1.push(obj.value);
        dataEchar_1.push(obj.value.toFixed(1));
    }



    // 同期
    var dataArray = [];

    var date = getCurrentDate();

    var kpidata = all_company_data_ac[date_type]['kpi_value_tq_d'];
    for (var compcode in kpidata) {
        if (compcode != parent_company) {
            var obj = kpidata[compcode];
            var val1 = obj['FLY_TIME'][date_type][date];
            var val2 = obj['AC_NUM'][date_type][date];
            var rate = val2 > 0 ? val1 / val2 : 0;
            dataArray.push({
                'sort': companyCode2Sort[compcode],
                'nameabbr': companyCode2Nameabbr[compcode],
                'value': rate,
            });
        }
    }

    dataArray.sort(function (a, b) {
        return a.sort - b.sort
    });

    // s2
    var len = dataArray.length;

    for (var i = 0; i < len; i++) {
        var obj = dataArray[i];
        /* data_s2.push(Math.round(obj.value * 10) / 10); */
        data_s2.push(obj.value);
        dataEchar_2.push(obj.value.toFixed(1));
    }



    // 同比


    // s3
    var len = data_s1.length;
    for (var i = 0; i < len; i++) {
        var val = data_s1[i];
        var val_tq = data_s2[i];
        // 同比=本期-同期
        /* var tb = (val - val_tq).toFixed(1); */
        // 同比=(本期-同期)÷同期×100%
        var tb = Math.round((val - val_tq) / val_tq * 1000) / 10;
        if (isNaN(tb) || val_tq == 0) {
            tb = 0;
        }
        data_s3.push(tb);
    }

    // ---------------------------------------------------------
    // 主KPI图表
    chart_pop1 = echarts.init(document.getElementById(chart_id1));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                return params.name + ': ' + params.value;
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 36,
            right: 20,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
                formatter: '{value}'
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'bar',
            barWidth: 20,
            data: dataEchar_1,
            barGap: '0%', //柱间距离 多个series柱子的空袭
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    color: colors[0][0],
                    offset: [-7, 0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[0][0]
                        }, {
                            offset: 1,
                            color: colors[0][1]
                        }]),
                }
            },
        }, {
            name: '',
            type: 'bar',
            barWidth: 20,
            data: dataEchar_2,
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    color: colors[1][0],
                    offset: [7, 0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[1][0]
                        }, {
                            offset: 1,
                            color: colors[1][1]
                        }]),
                }
            },
        }]
    };

    chart_pop1.setOption(option);



    // ---------------------------------------------------------
    // 同比 图表
    chart_pop2 = echarts.init(document.getElementById(chart_id2));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value) + '%';
                return params.name + ': ' + params.value + '%';
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 36,
            right: 20,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                show: false,
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                show: false,
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            //max: 100,
            //min: -100,
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                show: false,
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'line',
            symbol: 'rect',
            symbolSize: 6,
            smooth: false,
            data: data_s3,
            label: {
                normal: {
                    show: true,
                    //position: 'top'
                }
            },
            lineStyle: {
                normal: {
                    color: colors[2][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[2][0],
                    opacity: 1,
                }
            },
            markLine: {
                silent: true,
                data: [{
                    name: '',
                    yAxis: 0,
                    lineStyle: {
                        normal: {
                            color: '#f82c20',
                            type: 'dashed',
                        }
                    },
                }],
                symbol: '',
                symbolSize: 0,
                label: {
                    normal: {
                        position: 'start',
                        formatter: '{c}%',
                    }
                },
            },
        },]
    };

    chart_pop2.setOption(option);

}


// 核心经营指标 -> 国内 客座率同比
function setChartPop5() {
    // ---------------------------
    var chart_id1 = 'chart_pop1';
    var chart_id2 = 'chart_pop2';

    var colors = [
        ['#00c311', '#007892'], //柱状图渐变颜色
        ['#6ec7f6', '#03a5ed'], //柱状图渐变颜色
        ['#fbc900', ''], //折线颜色
    ];
    // ---------------------------

    var xAxisData = [];
    var data_s1 = []; //主KPI
    var data_s2 = []; //主KPI 同期
    var data_s3 = []; //同比


    // 当期
    var dataArray = [];

    var date = getCurrentDate();

    var kpidata = all_company_data['kpi_value_d'];
    for (var compcode in kpidata) {
        if (compcode != parent_company) {
            var obj = kpidata[compcode];
            var rate = obj['INL_TRV_RATE'][date_type][date];
            if (rate > 0) {
                dataArray.push({
                    'sort': companyCode2Sort[compcode],
                    'nameabbr': companyCode2Nameabbr[compcode],
                    'value': rate,
                });
            }
        }
    }

    dataArray.sort(function (a, b) {
        return a.sort - b.sort
    });

    // s1
    var len = dataArray.length;
    for (var i = 0; i < len; i++) {
        var obj = dataArray[i];
        xAxisData.push(obj.nameabbr);
        data_s1.push(Math.round(obj.value * 1000) / 10);
    }



    // 同期
    var dataArray = [];

    var date = getCurrentDate();

    var kpidata = all_company_data['kpi_value_tq_d'];
    for (var compcode in kpidata) {
        if (compcode != parent_company) {
            var obj = kpidata[compcode];
            var rate = obj['INL_TRV_RATE'][date_type][date];
            if (rate > 0) {
                dataArray.push({
                    'sort': companyCode2Sort[compcode],
                    'nameabbr': companyCode2Nameabbr[compcode],
                    'value': rate,
                });
            }
        }
    }

    dataArray.sort(function (a, b) {
        return a.sort - b.sort
    });

    // s2
    var len = dataArray.length;
    for (var i = 0; i < len; i++) {
        var obj = dataArray[i];
        data_s2.push(Math.round(obj.value * 1000) / 10);
    }



    // 同比


    // s3
    var len = data_s1.length;
    for (var i = 0; i < len; i++) {
        var val = data_s1[i];
        var val_tq = data_s2[i];
        //本期-同期
        var tb = (val - val_tq).toFixed(1);

        if (isNaN(tb) || val_tq == 0) {
            tb = 0;
        }
        data_s3.push(tb);
    }



    // ---------------------------------------------------------
    // 主KPI图表
    chart_pop1 = echarts.init(document.getElementById(chart_id1));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                return params.name + ': ' + params.value;
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 36,
            right: 20,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
                formatter: '{value}%'
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'bar',
            barWidth: 20,
            data: data_s1,
            barGap: '0%', //柱间距离 多个series柱子的空袭
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    color: colors[0][0],
                    offset: [-7, 0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[0][0]
                        }, {
                            offset: 1,
                            color: colors[0][1]
                        }]),
                }
            },
        }, {
            name: '',
            type: 'bar',
            barWidth: 20,
            data: data_s2,
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    color: colors[1][0],
                    offset: [7, 0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[1][0]
                        }, {
                            offset: 1,
                            color: colors[1][1]
                        }]),
                }
            },
        }]
    };

    chart_pop1.setOption(option);



    // ---------------------------------------------------------
    // 同比 图表
    chart_pop2 = echarts.init(document.getElementById(chart_id2));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value) + '%';
                return params.name + ': ' + params.value + '%';
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 36,
            right: 20,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                show: false,
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                show: false,
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            //max: 100,
            //min: -100,
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                show: false,
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'line',
            symbol: 'rect',
            symbolSize: 6,
            smooth: false,
            data: data_s3,
            label: {
                normal: {
                    show: true,
                    //position: 'top'
                }
            },
            lineStyle: {
                normal: {
                    color: colors[2][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[2][0],
                    opacity: 1,
                }
            },
            markLine: {
                silent: true,
                data: [{
                    name: '',
                    yAxis: 0,
                    lineStyle: {
                        normal: {
                            color: '#f82c20',
                            type: 'dashed',
                        }
                    },
                }],
                symbol: '',
                symbolSize: 0,
                label: {
                    normal: {
                        position: 'start',
                        formatter: '{c}%',
                    }
                },
            },
        },]
    };

    chart_pop2.setOption(option);
}

// 核心经营指标 -> 国际 客座率同比
function setChartPop9() {

    // ---------------------------
    var chart_id1 = 'chart_pop1';
    var chart_id2 = 'chart_pop2';

    var colors = [
        ['#00c311', '#007892'], //柱状图渐变颜色
        ['#6ec7f6', '#03a5ed'], //柱状图渐变颜色
        ['#fbc900', ''], //折线颜色
    ];
    // ---------------------------

    var xAxisData = [];
    var data_s1 = []; //主KPI
    var data_s2 = []; //主KPI 同期
    var data_s3 = []; //同比


    // 当期
    var dataArray = [];

    var date = getCurrentDate();

    var kpidata = all_company_data['kpi_value_d'];
    for (var compcode in kpidata) {
        if (compcode != parent_company) {
            var obj = kpidata[compcode];
            var rate = obj['INT_TRV_RATE'][date_type][date];
            if (rate > 0) {
                dataArray.push({
                    'sort': companyCode2Sort[compcode],
                    'nameabbr': companyCode2Nameabbr[compcode],
                    'value': rate,
                });
            }

        }
    }

    dataArray.sort(function (a, b) {
        return a.sort - b.sort
    });

    // s1
    var len = dataArray.length;
    for (var i = 0; i < len; i++) {
        var obj = dataArray[i];
        xAxisData.push(obj.nameabbr);
        data_s1.push(Math.round(obj.value * 1000) / 10);
    }



    // 同期
    var dataArray = [];

    var date = getCurrentDate();

    var kpidata = all_company_data['kpi_value_tq_d'];
    for (var compcode in kpidata) {
        if (compcode != parent_company) {
            var obj = kpidata[compcode];
            var rate = obj['INT_TRV_RATE'][date_type][date];
            if (rate > 0) {
                dataArray.push({
                    'sort': companyCode2Sort[compcode],
                    'nameabbr': companyCode2Nameabbr[compcode],
                    'value': rate,
                });
            }
        }
    }

    dataArray.sort(function (a, b) {
        return a.sort - b.sort
    });

    // s2
    var len = dataArray.length;
    for (var i = 0; i < len; i++) {
        var obj = dataArray[i];
        data_s2.push(Math.round(obj.value * 1000) / 10);
    }



    // 同比


    // s3
    var len = data_s1.length;
    for (var i = 0; i < len; i++) {
        var val = data_s1[i];
        var val_tq = data_s2[i];
        // 同比=本期-同期
        var tb = (val - val_tq).toFixed(1);
        if (isNaN(tb) || val_tq == 0) {
            tb = 0;
        }
        data_s3.push(tb);
    }



    // ---------------------------------------------------------
    // 主KPI图表
    chart_pop1 = echarts.init(document.getElementById(chart_id1));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                return params.name + ': ' + params.value;
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 36,
            right: 20,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
                formatter: '{value}%'
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'bar',
            barWidth: 20,
            data: data_s1,
            barGap: '0%', //柱间距离 多个series柱子的空袭
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    color: colors[0][0],
                    offset: [-7, 0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[0][0]
                        }, {
                            offset: 1,
                            color: colors[0][1]
                        }]),
                }
            },
        }, {
            name: '',
            type: 'bar',
            barWidth: 20,
            data: data_s2,
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    color: colors[1][0],
                    offset: [7, 0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[1][0]
                        }, {
                            offset: 1,
                            color: colors[1][1]
                        }]),
                }
            },
        }]
    };

    chart_pop1.setOption(option);



    // ---------------------------------------------------------
    // 同比 图表
    chart_pop2 = echarts.init(document.getElementById(chart_id2));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value) + '%';
                return params.name + ': ' + params.value + '%';
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 36,
            right: 20,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                show: false,
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                show: false,
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            //max: 100,
            //min: -100,
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                show: false,
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'line',
            symbol: 'rect',
            symbolSize: 6,
            smooth: false,
            data: data_s3,
            label: {
                normal: {
                    show: true,
                    //position: 'top'
                }
            },
            lineStyle: {
                normal: {
                    color: colors[2][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[2][0],
                    opacity: 1,
                }
            },
            markLine: {
                silent: true,
                data: [{
                    name: '',
                    yAxis: 0,
                    lineStyle: {
                        normal: {
                            color: '#f82c20',
                            type: 'dashed',
                        }
                    },
                }],
                symbol: '',
                symbolSize: 0,
                label: {
                    normal: {
                        position: 'start',
                        formatter: '{c}%',
                    }
                },
            },
        },]
    };

    chart_pop2.setOption(option);

}

// 核心经营指标 -> 国内 小时收入同比
function setChartPop12() {

    // ---------------------------
    var chart_id1 = 'chart_pop1';
    var chart_id2 = 'chart_pop2';

    var colors = [
        ['#00c311', '#007892'], //柱状图渐变颜色
        ['#6ec7f6', '#03a5ed'], //柱状图渐变颜色
        ['#fbc900', ''], //折线颜色
    ];
    // ---------------------------

    var xAxisData = [];
    var data_s1 = []; //主KPI
    var data_s2 = []; //主KPI 同期
    var data_s3 = []; //同比
    var data_s4 = []; //同比

    // 当期
    var dataArray = [];

    var date = getCurrentDate();

    var kpidata = all_company_data['kpi_value_d'];
    for (var compcode in kpidata) {
        if (compcode != parent_company) {
            var obj = kpidata[compcode];
            var rate = obj['INL_HOUR_INC_FUEL'][date_type][date];
            if (rate > 0) {
                dataArray.push({
                    'sort': companyCode2Sort[compcode],
                    'nameabbr': companyCode2Nameabbr[compcode],
                    'value': rate,
                });
            }

        }
    }

    dataArray.sort(function (a, b) {
        return a.sort - b.sort
    });

    // s1
    var len = dataArray.length;
    for (var i = 0; i < len; i++) {
        var obj = dataArray[i];
        xAxisData.push(obj.nameabbr);
        data_s1.push(Math.round(obj.value * 10) / 10);
    }



    // 同期
    var dataArray = [];

    var date = getCurrentDate();
    var kpidata = all_company_data['kpi_value_tq_d'];
    for (var compcode in kpidata) {
        if (compcode != parent_company) {
            var obj = kpidata[compcode];
            var rate = obj['INL_HOUR_INC_FUEL'][date_type][date];
            if (rate > 0) {
                dataArray.push({
                    'sort': companyCode2Sort[compcode],
                    'nameabbr': companyCode2Nameabbr[compcode],
                    'value': rate,
                });
            }
        }
    }

    dataArray.sort(function (a, b) {
        return a.sort - b.sort
    });

    // s2
    var len = dataArray.length;
    for (var i = 0; i < len; i++) {
        var obj = dataArray[i];
        data_s2.push(Math.round(obj.value * 10) / 10);
    }



    // 同比


    // s3
    /* var len = data_s1.length;
    for (var i = 0; i < len; i++) {
        var val = data_s1[i];
        var val_tq = data_s2[i]; */
    // 同比=(本期-同期)÷同期×100%
    /*     var tb = Math.round((val - val_tq) / val_tq * 1000) / 10;
         if (isNaN(tb) || val_tq == 0) {
             tb = 0;
         }
         data_s3.push(tb);
     } */
    var dataArray2 = [];
    var kpidata2 = all_company_data['kpi_ratio_tq_d'];
    for (var compcode2 in kpidata2) {
        if (compcode2 != parent_company) {
            var obj2 = kpidata2[compcode2];
            var rate2 = obj2['INL_HOUR_INC_FUEL'][date_type][date];
            if (compcode2 != "HX") {
                dataArray2.push({
                    'sort': companyCode2Sort[compcode2],
                    'nameabbr': companyCode2Nameabbr[compcode2],
                    'value': rate2,
                });
            }
        }
    }

    dataArray2.sort(function (a, b) {
        return a.sort - b.sort
    });

    // s4
    var len2 = dataArray2.length;
    for (var i = 0; i < len2; i++) {
        var obj2 = dataArray2[i];
        data_s4.push(Math.round((obj2.value * 100) * 10) / 10);
    }



    // ---------------------------------------------------------
    // 主KPI图表
    chart_pop1 = echarts.init(document.getElementById(chart_id1));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                return params.name + ': ' + params.value;
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 76,
            right: 20,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
                formatter: '{value}万元'
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'bar',
            barWidth: 20,
            data: data_s1,
            barGap: '0%', //柱间距离 多个series柱子的空袭
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    color: colors[0][0],
                    offset: [-7, 0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[0][0]
                        }, {
                            offset: 1,
                            color: colors[0][1]
                        }]),
                }
            },
        }, {
            name: '',
            type: 'bar',
            barWidth: 20,
            data: data_s2,
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    color: colors[1][0],
                    offset: [7, 0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[1][0]
                        }, {
                            offset: 1,
                            color: colors[1][1]
                        }]),
                }
            },
        }]
    };

    chart_pop1.setOption(option);



    // ---------------------------------------------------------
    // 同比 图表
    chart_pop2 = echarts.init(document.getElementById(chart_id2));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value) + '%';
                return params.name + ': ' + params.value + '%';
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 80,
            right: 20,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                show: false,
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                show: false,
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            //max: 100,
            //min: -100,
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                show: false,
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'line',
            symbol: 'rect',
            symbolSize: 6,
            smooth: false,
            data: data_s4,
            label: {
                normal: {
                    show: true,
                    //position: 'top'
                }
            },
            lineStyle: {
                normal: {
                    color: colors[2][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[2][0],
                    opacity: 1,
                }
            },
            markLine: {
                silent: true,
                data: [{
                    name: '',
                    yAxis: 0,
                    lineStyle: {
                        normal: {
                            color: '#f82c20',
                            type: 'dashed',
                        }
                    },
                }],
                symbol: '',
                symbolSize: 0,
                label: {
                    normal: {
                        position: 'start',
                        formatter: '{c}%',
                    }
                },
            },
        },]
    };

    chart_pop2.setOption(option);

}

// 核心经营指标 -> 国际 小时收入同比
function setChartPop13() {

    // ---------------------------
    var chart_id1 = 'chart_pop1';
    var chart_id2 = 'chart_pop2';

    var colors = [
        ['#00c311', '#007892'], //柱状图渐变颜色
        ['#6ec7f6', '#03a5ed'], //柱状图渐变颜色
        ['#fbc900', ''], //折线颜色
    ];
    // ---------------------------

    var xAxisData = [];
    var data_s1 = []; //主KPI
    var data_s2 = []; //主KPI 同期
    var data_s4 = []; //同比


    // 当期
    var dataArray = [];

    var date = getCurrentDate();

    var kpidata = all_company_data['kpi_value_d'];
    for (var compcode in kpidata) {
        if (compcode != parent_company) {
            var obj = kpidata[compcode];
            var rate = obj['INT_HOUR_INC_FUEL'][date_type][date];
            if (rate > 0) {
                dataArray.push({
                    'sort': companyCode2Sort[compcode],
                    'nameabbr': companyCode2Nameabbr[compcode],
                    'value': rate,
                });
            }

        }
    }

    dataArray.sort(function (a, b) {
        return a.sort - b.sort
    });

    // s1
    var len = dataArray.length;
    for (var i = 0; i < len; i++) {
        var obj = dataArray[i];
        xAxisData.push(obj.nameabbr);
        data_s1.push(Math.round(obj.value * 10) / 10);
    }



    // 同期
    var dataArray = [];

    var date = getCurrentDate();

    var kpidata = all_company_data['kpi_value_tq_d'];
    for (var compcode in kpidata) {
        if (compcode != parent_company) {
            var obj = kpidata[compcode];
            var rate = obj['INT_HOUR_INC_FUEL'][date_type][date];
            if (rate > 0) {
                dataArray.push({
                    'sort': companyCode2Sort[compcode],
                    'nameabbr': companyCode2Nameabbr[compcode],
                    'value': rate,
                });
            }
        }
    }

    dataArray.sort(function (a, b) {
        return a.sort - b.sort
    });

    // s2
    var len = dataArray.length;
    for (var i = 0; i < len; i++) {
        var obj = dataArray[i];
        data_s2.push(Math.round(obj.value * 10) / 10);
    }



    // 同比


    // s3
    /* var len = data_s1.length;
    for (var i = 0; i < len; i++) {
        var val = data_s1[i];
        var val_tq = data_s2[i]; */
    // 同比=(本期-同期)÷同期×100%
    /* var tb = Math.round((val - val_tq) / val_tq * 1000) / 10;
    if (isNaN(tb) || val_tq == 0) {
        tb = 0;
    }
    data_s3.push(tb);
} */
    var dataArray2 = [];
    var kpidata2 = all_company_data['kpi_ratio_tq_d'];
    for (var compcode2 in kpidata2) {
        if (compcode2 != parent_company) {
            var obj2 = kpidata2[compcode2];
            var rate2 = obj2['INT_HOUR_INC_FUEL'][date_type][date];
            //data_s4.push((rate * 100) / 100);
            /* if (rate > 0) { */
            dataArray2.push({
                'sort': companyCode2Sort[compcode2],
                'nameabbr': companyCode2Nameabbr[compcode2],
                'value': rate2,
            });
            /* } */
        }
    }

    dataArray2.sort(function (a, b) {
        return a.sort - b.sort
    });

    // s4
    var len2 = dataArray2.length;
    for (var i = 0; i < len2; i++) {
        var obj2 = dataArray2[i];
        data_s4.push(Math.round((obj2.value * 100) * 10) / 10);
    }


    // ---------------------------------------------------------
    // 主KPI图表
    chart_pop1 = echarts.init(document.getElementById(chart_id1));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                return params.name + ': ' + params.value;
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 76,
            right: 20,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
                formatter: '{value}万元'
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'bar',
            barWidth: 20,
            data: data_s1,
            barGap: '0%', //柱间距离 多个series柱子的空袭
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    color: colors[0][0],
                    offset: [-7, 0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[0][0]
                        }, {
                            offset: 1,
                            color: colors[0][1]
                        }]),
                }
            },
        }, {
            name: '',
            type: 'bar',
            barWidth: 20,
            data: data_s2,
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    color: colors[1][0],
                    offset: [7, 0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[1][0]
                        }, {
                            offset: 1,
                            color: colors[1][1]
                        }]),
                }
            },
        }]
    };

    chart_pop1.setOption(option);



    // ---------------------------------------------------------
    // 同比 图表
    chart_pop2 = echarts.init(document.getElementById(chart_id2));
    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value) + '%';
                return params.name + ': ' + params.value + '%';
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 80,
            right: 20,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                show: false,
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                show: false,
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            //max: 100,
            //min: -100,
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                show: false,
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'line',
            symbol: 'rect',
            symbolSize: 6,
            smooth: false,
            data: data_s4,
            label: {
                normal: {
                    show: true,
                    //position: 'top'
                }
            },
            lineStyle: {
                normal: {
                    color: colors[2][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[2][0],
                    opacity: 1,
                }
            },
            markLine: {
                silent: true,
                data: [{
                    name: '',
                    yAxis: 0,
                    lineStyle: {
                        normal: {
                            color: '#f82c20',
                            type: 'dashed',
                        }
                    },
                }],
                symbol: '',
                symbolSize: 0,
                label: {
                    normal: {
                        position: 'start',
                        formatter: '{c}%',
                    }
                },
            },
        },]
    };

    chart_pop2.setOption(option);

}

// 核心经营指标 -> 整体小时收入同比
function setChartPop11() {

    // ---------------------------
    var chart_id1 = 'chart_pop1';
    var chart_id2 = 'chart_pop2';

    var colors = [
        ['#00c311', '#007892'], //柱状图渐变颜色
        ['#6ec7f6', '#03a5ed'], //柱状图渐变颜色
        ['#fbc900', ''], //折线颜色
    ];
    // ---------------------------

    var xAxisData = [];
    var data_s1 = []; //主KPI
    var data_s2 = []; //主KPI 同期
    var data_s4 = []; //同比


    // 当期
    var dataArray = [];

    var date = getCurrentDate();

    var kpidata = all_company_data['kpi_value_d'];
    for (var compcode in kpidata) {
        if (compcode != parent_company) {
            var obj = kpidata[compcode];
            var rate = obj['HOUR_INC_FUEL'][date_type][date];
            if (rate > 0) {
                dataArray.push({
                    'sort': companyCode2Sort[compcode],
                    'nameabbr': companyCode2Nameabbr[compcode],
                    'value': rate,
                });
            }

        }
    }

    dataArray.sort(function (a, b) {
        return a.sort - b.sort
    });

    // s1
    var len = dataArray.length;
    for (var i = 0; i < len; i++) {
        var obj = dataArray[i];
        xAxisData.push(obj.nameabbr);
        data_s1.push(Math.round(obj.value * 10) / 10);
    }



    // 同期
    var dataArray = [];

    var date = getCurrentDate();

    var kpidata = all_company_data['kpi_value_tq_d'];
    for (var compcode in kpidata) {
        if (compcode != parent_company) {
            var obj = kpidata[compcode];
            var rate = obj['HOUR_INC_FUEL'][date_type][date];
            if (rate > 0) {
                dataArray.push({
                    'sort': companyCode2Sort[compcode],
                    'nameabbr': companyCode2Nameabbr[compcode],
                    'value': rate,
                });
            }
        }
    }

    dataArray.sort(function (a, b) {
        return a.sort - b.sort
    });

    // s2
    var len = dataArray.length;
    for (var i = 0; i < len; i++) {
        var obj = dataArray[i];
        data_s2.push(Math.round(obj.value * 10) / 10);
    }



    // 同比


    // s3
    /* var len = data_s1.length;
    for (var i = 0; i < len; i++) {
        var val = data_s1[i];
        var val_tq = data_s2[i]; */
    // 同比=(本期-同期)÷同期×100%
    /* var tb = Math.round((val - val_tq) / val_tq * 1000) / 10;
    if (isNaN(tb) || val_tq == 0) {
        tb = 0;
    }
    data_s3.push(tb);
} */
    var dataArray2 = [];
    var kpidata2 = all_company_data['kpi_ratio_tq_d'];
    for (var compcode2 in kpidata2) {
        if (compcode2 != parent_company) {
            var obj2 = kpidata2[compcode2];
            var rate2 = obj2['HOUR_INC_FUEL'][date_type][date];
            //data_s4.push((rate * 100) / 100);
            /* if (rate > 0) { */
            dataArray2.push({
                'sort': companyCode2Sort[compcode2],
                'nameabbr': companyCode2Nameabbr[compcode2],
                'value': rate2,
            });
            /* } */
        }
    }

    dataArray2.sort(function (a, b) {
        return a.sort - b.sort
    });

    // s4
    var len2 = dataArray2.length;
    for (var i = 0; i < len2; i++) {
        var obj2 = dataArray2[i];
        if (obj2.value != undefined) {
            data_s4.push(Math.round((obj2.value * 100) * 10) / 10);
        }

    }
    // ---------------------------------------------------------
    // 主KPI图表
    chart_pop1 = echarts.init(document.getElementById(chart_id1));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                return params.name + ': ' + params.value;
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 76,
            right: 20,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
                formatter: '{value}万元'
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'bar',
            barWidth: 20,
            data: data_s1,
            barGap: '0%', //柱间距离 多个series柱子的空袭
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    color: colors[0][0],
                    offset: [-7, 0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[0][0]
                        }, {
                            offset: 1,
                            color: colors[0][1]
                        }]),
                }
            },
        }, {
            name: '',
            type: 'bar',
            barWidth: 20,
            data: data_s2,
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    color: colors[1][0],
                    offset: [7, 0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[1][0]
                        }, {
                            offset: 1,
                            color: colors[1][1]
                        }]),
                }
            },
        }]
    };

    chart_pop1.setOption(option);



    // ---------------------------------------------------------
    // 同比 图表
    chart_pop2 = echarts.init(document.getElementById(chart_id2));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value) + '%';
                return params.name + ': ' + params.value + '%';
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 80,
            right: 20,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                show: false,
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                show: false,
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            //max: 100,
            //min: -100,
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                show: false,
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'line',
            symbol: 'rect',
            symbolSize: 6,
            smooth: false,
            data: data_s4,
            label: {
                normal: {
                    show: true,
                    //position: 'top'
                }
            },
            lineStyle: {
                normal: {
                    color: colors[2][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[2][0],
                    opacity: 1,
                }
            },
            markLine: {
                silent: true,
                data: [{
                    name: '',
                    yAxis: 0,
                    lineStyle: {
                        normal: {
                            color: '#f82c20',
                            type: 'dashed',
                        }
                    },
                }],
                symbol: '',
                symbolSize: 0,
                label: {
                    normal: {
                        position: 'start',
                        formatter: '{c}%',
                    }
                },
            },
        },]
    };


    chart_pop2.setOption(option);

}

// 核心经营指标 -> 整体 客座率同比
function setChartPop10() {

    // ---------------------------
    var chart_id1 = 'chart_pop1';
    var chart_id2 = 'chart_pop2';

    var colors = [
        ['#00c311', '#007892'], //柱状图渐变颜色
        ['#6ec7f6', '#03a5ed'], //柱状图渐变颜色
        ['#fbc900', ''], //折线颜色
    ];
    // ---------------------------

    var xAxisData = [];
    var data_s1 = []; //主KPI
    var data_s2 = []; //主KPI 同期
    var data_s3 = []; //同比


    // 当期
    var dataArray = [];

    var date = getCurrentDate();

    var kpidata = all_company_data['kpi_value_d'];
    for (var compcode in kpidata) {
        if (compcode != parent_company) {
            var obj = kpidata[compcode];
            var rate = obj['TRV_RATE'][date_type][date];
            dataArray.push({
                'sort': companyCode2Sort[compcode],
                'nameabbr': companyCode2Nameabbr[compcode],
                'value': rate,
            });
        }
    }

    dataArray.sort(function (a, b) {
        return a.sort - b.sort
    });

    // s1
    var len = dataArray.length;
    for (var i = 0; i < len; i++) {
        var obj = dataArray[i];
        xAxisData.push(obj.nameabbr);
        data_s1.push(Math.round(obj.value * 1000) / 10);
    }



    // 同期
    var dataArray = [];

    var date = getCurrentDate();

    var kpidata = all_company_data['kpi_value_tq_d'];
    for (var compcode in kpidata) {
        if (compcode != parent_company) {
            var obj = kpidata[compcode];
            var rate = obj['TRV_RATE'][date_type][date];
            dataArray.push({
                'sort': companyCode2Sort[compcode],
                'nameabbr': companyCode2Nameabbr[compcode],
                'value': rate,
            });
        }
    }

    dataArray.sort(function (a, b) {
        return a.sort - b.sort
    });

    // s2
    var len = dataArray.length;
    for (var i = 0; i < len; i++) {
        var obj = dataArray[i];
        data_s2.push(Math.round(obj.value * 1000) / 10);
    }



    // 同比


    // s3
    var len = data_s1.length;
    for (var i = 0; i < len; i++) {
        var val = data_s1[i];
        var val_tq = data_s2[i];
        // 同比=本期-同期
        var tb = (val - val_tq).toFixed(1);
        if (isNaN(tb) || val_tq == 0) {
            tb = 0;
        }
        data_s3.push(tb);
    }



    // ---------------------------------------------------------
    // 主KPI图表
    chart_pop1 = echarts.init(document.getElementById(chart_id1));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                return params.name + ': ' + params.value;
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 36,
            right: 20,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
                formatter: '{value}%'
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'bar',
            barWidth: 20,
            data: data_s1,
            barGap: '0%', //柱间距离 多个series柱子的空袭
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    color: colors[0][0],
                    offset: [-7, 0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[0][0]
                        }, {
                            offset: 1,
                            color: colors[0][1]
                        }]),
                }
            },
        }, {
            name: '',
            type: 'bar',
            barWidth: 20,
            data: data_s2,
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    color: colors[1][0],
                    offset: [7, 0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[1][0]
                        }, {
                            offset: 1,
                            color: colors[1][1]
                        }]),
                }
            },
        }]
    };

    chart_pop1.setOption(option);



    // ---------------------------------------------------------
    // 同比 图表
    chart_pop2 = echarts.init(document.getElementById(chart_id2));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value) + '%';
                return params.name + ': ' + params.value + '%';
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 36,
            right: 20,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                show: false,
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                show: false,
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            //max: 100,
            //min: -100,
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                show: false,
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'line',
            symbol: 'rect',
            symbolSize: 6,
            smooth: false,
            data: data_s3,
            label: {
                normal: {
                    show: true,
                    //position: 'top'
                }
            },
            lineStyle: {
                normal: {
                    color: colors[2][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[2][0],
                    opacity: 1,
                }
            },
            markLine: {
                silent: true,
                data: [{
                    name: '',
                    yAxis: 0,
                    lineStyle: {
                        normal: {
                            color: '#f82c20',
                            type: 'dashed',
                        }
                    },
                }],
                symbol: '',
                symbolSize: 0,
                label: {
                    normal: {
                        position: 'start',
                        formatter: '{c}%',
                    }
                },
            },
        },]
    };

    chart_pop2.setOption(option);
}


// 核心经营指标 -> 国内 座公里收入同比
function setChartPop6() {
    // ---------------------------
    var chart_id1 = 'chart_pop1';
    var chart_id2 = 'chart_pop2';

    var colors = [
        ['#00c311', '#007892'], //柱状图渐变颜色
        ['#6ec7f6', '#03a5ed'], //柱状图渐变颜色
        ['#fbc900', ''], //折线颜色
    ];
    // ---------------------------

    var xAxisData = [];
    var data_s1 = []; //主KPI
    var data_s2 = []; //主KPI 同期
    var data_s3 = []; //同比



    var dataArray = [];

    var len = companylist.length;
    for (var i = 0; i < len; i++) {
        var dat = companylist[i];
        var compcode = dat.code;
        if (compcode != parent_company) {
            var obj = getKiloInc(compcode, 'INL_INC', 'INL_CAP_KILO');
            dataArray.push({
                'sort': companyCode2Sort[compcode],
                'nameabbr': companyCode2Nameabbr[compcode],
                'value': obj.value,
                'value_tq': obj.value_tq,
                'tb': obj.tb,
            });
        }
    }

    dataArray.sort(function (a, b) {
        return a.sort - b.sort
    });


    var len = dataArray.length;
    for (var i = 0; i < len; i++) {
        var obj = dataArray[i];
        // 部分航空公司没有国际航线，所以只显示有数据的
        if (obj.value > 0) {
            xAxisData.push(obj.nameabbr);

            // 当期
            // s1
            data_s1.push(obj.value);

            // 同期
            // s2
            data_s2.push(obj.value_tq);

            // 同比
            // s3
            data_s3.push(obj.tb);
        }
    }



    // ---------------------------------------------------------
    // 主KPI图表
    chart_pop1 = echarts.init(document.getElementById(chart_id1));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                return params.name + ': ' + params.value;
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 36,
            right: 20,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
                formatter: '{value}'
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'bar',
            barWidth: 20,
            data: data_s1,
            barGap: '0%', //柱间距离 多个series柱子的空袭
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    color: colors[0][0],
                    offset: [-7, 0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[0][0]
                        }, {
                            offset: 1,
                            color: colors[0][1]
                        }]),
                }
            },
        }, {
            name: '',
            type: 'bar',
            barWidth: 20,
            data: data_s2,
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    color: colors[1][0],
                    offset: [7, 0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[1][0]
                        }, {
                            offset: 1,
                            color: colors[1][1]
                        }]),
                }
            },
        }]
    };

    chart_pop1.setOption(option);



    // ---------------------------------------------------------
    // 同比 图表
    chart_pop2 = echarts.init(document.getElementById(chart_id2));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value) + '%';
                return params.name + ': ' + params.value + '%';
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 36,
            right: 20,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                show: false,
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                show: false,
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            //max: 100,
            //min: -100,
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                show: false,
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'line',
            symbol: 'rect',
            symbolSize: 6,
            smooth: false,
            data: data_s3,
            label: {
                normal: {
                    show: true,
                    //position: 'top'
                }
            },
            lineStyle: {
                normal: {
                    color: colors[2][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[2][0],
                    opacity: 1,
                }
            },
            markLine: {
                silent: true,
                data: [{
                    name: '',
                    yAxis: 0,
                    lineStyle: {
                        normal: {
                            color: '#f82c20',
                            type: 'dashed',
                        }
                    },
                }],
                symbol: '',
                symbolSize: 0,
                label: {
                    normal: {
                        position: 'start',
                        formatter: '{c}%',
                    }
                },
            },
        },]
    };

    chart_pop2.setOption(option);
}


// 核心经营指标 -> 国际 座公里收入同比
function setChartPop7() {
    // ---------------------------
    var chart_id1 = 'chart_pop1';
    var chart_id2 = 'chart_pop2';

    var colors = [
        ['#00c311', '#007892'], //柱状图渐变颜色
        ['#6ec7f6', '#03a5ed'], //柱状图渐变颜色
        ['#fbc900', ''], //折线颜色
    ];
    // ---------------------------

    var xAxisData = [];
    var data_s1 = []; //主KPI
    var data_s2 = []; //主KPI 同期
    var data_s3 = []; //同比


    var dataArray = [];

    var len = companylist.length;
    for (var i = 0; i < len; i++) {
        var dat = companylist[i];
        var compcode = dat.code;
        if (compcode != parent_company) {
            var obj = getKiloInc(compcode, 'INT_INC', 'INT_CAP_KILO');
            dataArray.push({
                'sort': companyCode2Sort[compcode],
                'nameabbr': companyCode2Nameabbr[compcode],
                'value': obj.value,
                'value_tq': obj.value_tq,
                'tb': obj.tb,
            });
        }
    }

    dataArray.sort(function (a, b) {
        return a.sort - b.sort
    });


    var len = dataArray.length;
    for (var i = 0; i < len; i++) {
        var obj = dataArray[i];

        // 部分航空公司没有国际航线，所以只显示有数据的
        if (obj.value > 0) {
            xAxisData.push(obj.nameabbr);

            // 当期
            // s1
            data_s1.push(obj.value);

            // 同期
            // s2
            data_s2.push(obj.value_tq);

            // 同比
            // s3
            data_s3.push(obj.tb);
        }

    }



    // ---------------------------------------------------------
    // 主KPI图表
    chart_pop1 = echarts.init(document.getElementById(chart_id1));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                return params.name + ': ' + params.value;
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 36,
            right: 20,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
                formatter: '{value}'
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'bar',
            barWidth: 20,
            data: data_s1,
            barGap: '0%', //柱间距离 多个series柱子的空袭
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    color: colors[0][0],
                    offset: [-7, 0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[0][0]
                        }, {
                            offset: 1,
                            color: colors[0][1]
                        }]),
                }
            },
        }, {
            name: '',
            type: 'bar',
            barWidth: 20,
            data: data_s2,
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    color: colors[1][0],
                    offset: [7, 0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[1][0]
                        }, {
                            offset: 1,
                            color: colors[1][1]
                        }]),
                }
            },
        }]
    };

    chart_pop1.setOption(option);



    // ---------------------------------------------------------
    // 同比 图表
    chart_pop2 = echarts.init(document.getElementById(chart_id2));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value) + '%';
                return params.name + ': ' + params.value + '%';
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 36,
            right: 20,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                show: false,
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                show: false,
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            //max: 100,
            //min: -100,
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                show: false,
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'line',
            symbol: 'rect',
            symbolSize: 6,
            smooth: false,
            data: data_s3,
            label: {
                normal: {
                    show: true,
                    //position: 'top'
                }
            },
            lineStyle: {
                normal: {
                    color: colors[2][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[2][0],
                    opacity: 1,
                }
            },
            markLine: {
                silent: true,
                data: [{
                    name: '',
                    yAxis: 0,
                    lineStyle: {
                        normal: {
                            color: '#f82c20',
                            type: 'dashed',
                        }
                    },
                }],
                symbol: '',
                symbolSize: 0,
                label: {
                    normal: {
                        position: 'start',
                        formatter: '{c}%',
                    }
                },
            },
        },]
    };

    chart_pop2.setOption(option);
}



// 核心经营指标 -> 座公里收入同比
function setChartPop8() {
    // ---------------------------
    var chart_id1 = 'chart_pop1';
    var chart_id2 = 'chart_pop2';

    var colors = [
        ['#00c311', '#007892'], //柱状图渐变颜色
        ['#6ec7f6', '#03a5ed'], //柱状图渐变颜色
        ['#fbc900', ''], //折线颜色
    ];
    // ---------------------------

    var xAxisData = [];
    var data_s1 = []; //主KPI
    var data_s2 = []; //主KPI 同期
    var data_s3 = []; //同比


    var dataArray = [];

    var date = getCurrentDate();

    var len = companylist.length;
    for (var i = 0; i < len; i++) {
        var dat = companylist[i];
        var compcode = dat.code;
        if (compcode != parent_company) {

            var val = all_company_data['kpi_value_d'][compcode]['CAP_KILO_INC_FUEL'][date_type][date];
            var val_tq = all_company_data['kpi_value_tq_d'][compcode]['CAP_KILO_INC_FUEL'][date_type][date];
            var tb = all_company_data['kpi_ratio_tq_d'][compcode]['CAP_KILO_INC_FUEL'][date_type][date];
            var inc_kilo = trimDecimal(val, 2);
            var inc_kilo_tq = trimDecimal(val_tq, 2);
            var inc_kilo_tb = Math.round(tb * 1000) / 10;

            dataArray.push({
                'sort': companyCode2Sort[compcode],
                'nameabbr': companyCode2Nameabbr[compcode],
                'value': inc_kilo,
                'value_tq': inc_kilo_tq,
                'tb': inc_kilo_tb,
            });
        }
    }

    dataArray.sort(function (a, b) {
        return a.sort - b.sort
    });


    var len = dataArray.length;
    for (var i = 0; i < len; i++) {
        var obj = dataArray[i];

        // 部分航空公司没有国际航线，所以只显示有数据的
        if (obj.value > 0) {
            xAxisData.push(obj.nameabbr);

            // 当期
            // s1
            data_s1.push(obj.value);

            // 同期
            // s2
            data_s2.push(obj.value_tq);

            // 同比
            // s3
            data_s3.push(obj.tb);
        }

    }

    // ---------------------------------------------------------
    // 主KPI图表
    chart_pop1 = echarts.init(document.getElementById(chart_id1));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                return params.name + ': ' + params.value;
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 36,
            right: 20,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
                formatter: '{value}'
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'bar',
            barWidth: 20,
            data: data_s1,
            barGap: '0%', //柱间距离 多个series柱子的空袭
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    color: colors[0][0],
                    offset: [-7, 0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[0][0]
                        }, {
                            offset: 1,
                            color: colors[0][1]
                        }]),
                }
            },
        }, {
            name: '',
            type: 'bar',
            barWidth: 20,
            data: data_s2,
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    color: colors[1][0],
                    offset: [7, 0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[1][0]
                        }, {
                            offset: 1,
                            color: colors[1][1]
                        }]),
                }
            },
        }]
    };

    chart_pop1.setOption(option);



    // ---------------------------------------------------------
    // 同比 图表
    chart_pop2 = echarts.init(document.getElementById(chart_id2));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value) + '%';
                return params.name + ': ' + params.value + '%';
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 36,
            right: 20,
            bottom: 25,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                show: false,
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                show: false,
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            //max: 100,
            //min: -100,
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                show: false,
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'line',
            symbol: 'rect',
            symbolSize: 6,
            smooth: false,
            data: data_s3,
            label: {
                normal: {
                    show: true,
                    //position: 'top'
                }
            },
            lineStyle: {
                normal: {
                    color: colors[2][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[2][0],
                    opacity: 1,
                }
            },
            markLine: {
                silent: true,
                data: [{
                    name: '',
                    yAxis: 0,
                    lineStyle: {
                        normal: {
                            color: '#f82c20',
                            type: 'dashed',
                        }
                    },
                }],
                symbol: '',
                symbolSize: 0,
                label: {
                    normal: {
                        position: 'start',
                        formatter: '{c}%',
                    }
                },
            },
        },]
    };

    chart_pop2.setOption(option);
}



// 右边栏 ： 预算完成情况
$('#tab_block1').on('click', function (event) {
    event.preventDefault();

    $('#tab_block1').addClass('on');
    $('#tab_block1').removeClass('off');

    $('#tab_block2').addClass('off');
    $('#tab_block2').removeClass('on');

    $('#ext_link1').addClass('on');
    $('#ext_link1').removeClass('off');

    $('#ext_link2').addClass('off');
    $('#ext_link2').removeClass('on');

    $('.tabc1').show();
    $('.tabc2').hide();
});

// 右边栏 ： 核心经营指标
$('#tab_block2').on('click', function (event) {
    event.preventDefault();

    $('#tab_block1').addClass('off');
    $('#tab_block1').removeClass('on');

    $('#tab_block2').addClass('on');
    $('#tab_block2').removeClass('off');

    $('#ext_link1').addClass('off');
    $('#ext_link1').removeClass('on');

    $('#ext_link2').addClass('on');
    $('#ext_link2').removeClass('off');

    $('.tabc1').hide();
    $('.tabc2').show();

});


// 客座率 国内 国际切换 
$('#tab_l_TRV_RATE').on('click', function (event) {
    event.preventDefault();
    // 国内
    $('#tabc_l_TRV_RATE').show();
    $('#tabc_i_TRV_RATE').hide();
    $('#tabc_a_TRV_RATE').hide();

    $('#tab_l_TRV_RATE').addClass('selected');
    $('#tab_i_TRV_RATE').removeClass('selected');
    $('#tab_a_TRV_RATE').removeClass('selected');
});

$('#tab_i_TRV_RATE').on('click', function (event) {
    event.preventDefault();
    // 国际
    $('#tabc_i_TRV_RATE').show();
    $('#tabc_l_TRV_RATE').hide();
    $('#tabc_a_TRV_RATE').hide();

    $('#tab_i_TRV_RATE').addClass('selected');
    $('#tab_l_TRV_RATE').removeClass('selected');
    $('#tab_a_TRV_RATE').removeClass('selected');
});

$('#tab_a_TRV_RATE').on('click', function (event) {
    event.preventDefault();
    // 国际
    $('#tabc_i_TRV_RATE').hide();
    $('#tabc_l_TRV_RATE').hide();
    $('#tabc_a_TRV_RATE').show();

    $('#tab_i_TRV_RATE').removeClass('selected');
    $('#tab_l_TRV_RATE').removeClass('selected');
    $('#tab_a_TRV_RATE').addClass('selected');
});


// 座公里收入 国内 国际切换 
$('#tab_l_CAP_KILO_INC_FUEL').on('click', function (event) {
    event.preventDefault();
    // 国内
    $('#tabc_l_CAP_KILO_INC_FUEL').show();
    $('#tabc_i_CAP_KILO_INC_FUEL').hide();
    $('#tabc_a_CAP_KILO_INC_FUEL').hide();

    $('#tab_l_CAP_KILO_INC_FUEL').addClass('selected');
    $('#tab_i_CAP_KILO_INC_FUEL').removeClass('selected');
    $('#tab_a_CAP_KILO_INC_FUEL').removeClass('selected');
});

$('#tab_i_CAP_KILO_INC_FUEL').on('click', function (event) {
    event.preventDefault();
    // 国际
    $('#tabc_i_CAP_KILO_INC_FUEL').show();
    $('#tabc_l_CAP_KILO_INC_FUEL').hide();
    $('#tabc_a_CAP_KILO_INC_FUEL').hide();

    $('#tab_i_CAP_KILO_INC_FUEL').addClass('selected');
    $('#tab_l_CAP_KILO_INC_FUEL').removeClass('selected');
    $('#tab_a_CAP_KILO_INC_FUEL').removeClass('selected');
});

$('#tab_a_CAP_KILO_INC_FUEL').on('click', function (event) {
    event.preventDefault();
    // 国际
    $('#tabc_i_CAP_KILO_INC_FUEL').hide();
    $('#tabc_l_CAP_KILO_INC_FUEL').hide();
    $('#tabc_a_CAP_KILO_INC_FUEL').show();

    $('#tab_i_CAP_KILO_INC_FUEL').removeClass('selected');
    $('#tab_l_CAP_KILO_INC_FUEL').removeClass('selected');
    $('#tab_a_CAP_KILO_INC_FUEL').addClass('selected');
});

// 小时收入 国内 国际切换 
$('#tab_l_hour_INC_FUEL').on('click', function (event) {
    event.preventDefault();
    // 国内
    $('#tabc_hour_INC_FUEL').show();
    $('#tabc_hour_INT_FUEL').hide();
    $('#tabc_hour_group_FUEL').hide();

    $('#tab_l_hour_INC_FUEL').addClass('selected');
    $('#tab_i_hour_INC_FUEL').removeClass('selected');
    $('#tab_a_hour_INC_FUEL').removeClass('selected');
});

$('#tab_i_hour_INC_FUEL').on('click', function (event) {
    event.preventDefault();
    // 国际
    $('#tabc_hour_INT_FUEL').show();
    $('#tabc_hour_INC_FUEL').hide();
    $('#tabc_hour_group_FUEL').hide();

    $('#tab_i_hour_INC_FUEL').addClass('selected');
    $('#tab_l_hour_INC_FUEL').removeClass('selected');
    $('#tab_a_hour_INC_FUEL').removeClass('selected');
});

$('#tab_a_hour_INC_FUEL').on('click', function (event) {
    event.preventDefault();
    // 国际
    $('#tabc_hour_INC_FUEL').hide();
    $('#tabc_hour_INT_FUEL').hide();
    $('#tabc_hour_group_FUEL').show();

    $('#tab_l_hour_INC_FUEL').removeClass('selected');
    $('#tab_i_hour_INC_FUEL').removeClass('selected');
    $('#tab_a_hour_INC_FUEL').addClass('selected');
});



$('#popover_chart .btnx').on('click', function (event) {
    event.preventDefault();
    $('#popover_chart').fadeOut();
});

// 打开弹窗 预算完成情况 收入
$('#btn_view_chart_r1').on('click', function (event) {
    event.preventDefault();
    showChartPopover(1);
});

// 打开弹窗 预算完成情况 日利用率
$('#btn_view_chart_r2').on('click', function (event) {
    event.preventDefault();
    showChartPopover(2);
});

// 打开弹窗 预算完成情况 小时收入
$('#btn_view_chart_r3').on('click', function (event) {
    event.preventDefault();
    showChartPopover(3);
});

// 打开弹窗 日利用率
$('#btn_view_chart_r1t2').on('click', function (event) {
    event.preventDefault();
    showChartPopover(4);
});


//
// 打开弹窗 国内客座率
$('#btn_view_chart_r2t2l').on('click', function (event) {
    event.preventDefault();
    showChartPopover(5);
});

// 打开弹窗 国际客座率
$('#btn_view_chart_r2t2i').on('click', function (event) {
    event.preventDefault();
    showChartPopover(9);
});

// 打开弹窗 整体客座率
$('#btn_view_chart_r2t2a').on('click', function (event) {
    event.preventDefault();
    showChartPopover(10);
});


//
// 打开弹窗 国内座公里收入
$('#btn_view_chart_r3t2l').on('click', function (event) {
    event.preventDefault();
    showChartPopover(6);
});

// 打开弹窗 国际座公里收入
$('#btn_view_chart_r3t2i').on('click', function (event) {
    event.preventDefault();
    showChartPopover(7);
});

// 打开弹窗 整体座公里收入
$('#btn_view_chart_r3t2a').on('click', function (event) {
    event.preventDefault();
    showChartPopover(8);
});

// 打开弹窗 整体小时收入
$('#btn_view_chart_r4t2a').on('click', function (event) {
    event.preventDefault();
    showChartPopover(11);
});

// 打开弹窗 国内小时收入
$('#btn_view_chart_r4t2l').on('click', function (event) {
    event.preventDefault();
    showChartPopover(12);
});

// 打开弹窗 国际小时收入
$('#btn_view_chart_r4t2i').on('click', function (event) {
    event.preventDefault();
    showChartPopover(13);
});

// 打开弹窗，显示本期收入趋势
$("#week_trend").on('click', function (event) {
    event.preventDefault();
    $('#popover_chart').fadeOut();
    $('#popover_trend_chart').fadeOut();
    var compName = $(".block_l1>.block-header").text();

    $("#income_week .headtitle").text(compName + "收入趋势")
    var compcode = window.location.hash.split("#")[1];
    setChartWeekTrend(compcode);
})
// 打开弹窗，显示本期收入趋势
$("#income_week .btnx").on('click', function (event) {
    event.preventDefault();
    $("#income_week").hide();
})



// 左边栏弹窗
var selected_trend_blockid;

function showTrendChartPopover(blockid) {

    selected_trend_blockid = blockid;

    // ---------------------------
    var chart_id1 = 'chart_trend_pop';
    var kpi_code;

    var colors = [
        ['#00c311', '#007892'], //柱状图渐变颜色
        ['#6ec7f6', '#03a5ed'], //柱状图渐变颜色
        ['#fbc900', ''], //折线颜色
    ];
    // ---------------------------

    var compname = companyCode2Name[current_company_code];

    var unit;
    var suffix;
    var marginleft;

    if (blockid == 1) {
        kpi_code = 'EST_INC_FUEL'
        unit = '万元';
        suffix = '';
        marginleft = 66;
        $('#popover_trend_chart .tit').text(compname + ' 收入趋势');
        $('#popover_trend_chart .legend1').text('收入（万元）');
        $('#popover_trend_chart .legend2').text('同比');
        // $('#popover_trend_chart').css('top', '136px');
        // $('#popover_trend_chart').css('left', '320px');

    } else if (blockid == 2) {
        kpi_code = 'TRV_NUM'
        unit = '万人次';
        suffix = '';
        marginleft = 45;
        $('#popover_trend_chart .tit').text(compname + ' 旅客运输量趋势');
        $('#popover_trend_chart .legend1').text('旅客运输量（万人次）');
        $('#popover_trend_chart .legend2').text('同比');
        // $('#popover_trend_chart').css('top', '418px');
        // $('#popover_trend_chart').css('left', '320px');
    } else if (blockid == 3) {
        kpi_code = 'TRV_RATE'
        unit = '';
        suffix = '%';
        marginleft = 45;
        $('#popover_trend_chart .tit').text(compname + ' 客座率趋势');
        $('#popover_trend_chart .legend1').text('客座率（%）');
        $('#popover_trend_chart .legend2').text('同比');
        // $('#popover_trend_chart').css('top', '418px');
        // $('#popover_trend_chart').css('left', '320px');
    } else if (blockid == 4) {
        kpi_code = 'HOUR_INC_FUEL'
        unit = '万元';
        suffix = '';
        marginleft = 36;
        $('#popover_trend_chart .tit').text(compname + ' 小时收入趋势');
        $('#popover_trend_chart .legend1').text('小时收入（万元）');
        $('#popover_trend_chart .legend2').text('同比');
        // $('#popover_trend_chart').css('top', '476px');
        // $('#popover_trend_chart').css('left', '320px');
    } else if (blockid == 5) {
        kpi_code = 'TRV_KILO'
        unit = '万人公里';
        suffix = '';
        marginleft = 36;
        $('#popover_trend_chart .tit').text(compname + ' 客公里RPK趋势');
        $('#popover_trend_chart .legend1').text('客公里RPK（万人公里）');
        $('#popover_trend_chart .legend2').text('同比');
        // $('#popover_trend_chart').css('top', '476px');
        // $('#popover_trend_chart').css('left', '320px');
    } else if (blockid == 6) {
        kpi_code = 'SHIFTS'
        unit = '班次';
        suffix = '';
        marginleft = 46;
        $('#popover_trend_chart .tit').text(compname + ' 飞行班次趋势');
        $('#popover_trend_chart .legend1').text('飞行班次（班次）');
        $('#popover_trend_chart .legend2').text('同比');
        // $('#popover_trend_chart').css('top', '418px');
        // $('#popover_trend_chart').css('left', '320px');
    } else if (blockid == 7) {
        kpi_code = 'FLY_TIME'
        unit = '小时';
        suffix = '';
        marginleft = 56;
        $('#popover_trend_chart .tit').text(compname + ' 飞行时间趋势');
        $('#popover_trend_chart .legend1').text('飞行时间（小时）');
        $('#popover_trend_chart .legend2').text('同比');
        // $('#popover_trend_chart').css('top', '418px');
        // $('#popover_trend_chart').css('left', '320px');
    } else if (blockid == 8) {
        kpi_code = 'AVG_TKT_PRICE'
        unit = '元';
        suffix = '';
        marginleft = 46;
        $('#popover_trend_chart .tit').text(compname + ' 平均票价趋势');
        $('#popover_trend_chart .legend1').text('平均票价（元）');
        $('#popover_trend_chart .legend2').text('同比');
        // $('#popover_trend_chart').css('top', '476px');
        // $('#popover_trend_chart').css('left', '320px');
    } else if (blockid == 9) {
        kpi_code = 'CAP_KILO'
        unit = '万座公里';
        suffix = '';
        marginleft = 56;
        $('#popover_trend_chart .tit').text(compname + ' 可供客公里ASK趋势');
        $('#popover_trend_chart .legend1').text('可供客公里ASK（万座公里）');
        $('#popover_trend_chart .legend2').text('同比');
        // $('#popover_trend_chart').css('top', '476px');
        // $('#popover_trend_chart').css('left', '320px');
    }

    $('#popover_chart').fadeOut();
    $('#income_week').fadeOut();
    $('#popover_trend_chart').fadeIn();

    var xAxisData = [];
    var data_s1 = []; //主KPI
    var data_s2 = []; //同比
    var dates = [];
    if (date_type == 'L') {
        dates = weekList;
    } else if (date_type == 'M') {
        dates = monthList;
    } else if (date_type == 'D') {
        dates = dateList;
    } else if (date_type == 'Y') {
        dates = yearList;
    }
    var curDate = getCurrentDate();
    for (var index in dates) {
        var date = dates[index];
        if (Number(curDate) >= Number(date)) {
            var val = all_company_data['kpi_value_d'][current_company_code][kpi_code][date_type][date];
            var tb = all_company_data['kpi_ratio_tq_d'][current_company_code][kpi_code][date_type][date];

            if (blockid == 1) {
                val = Math.round(val);
            } else if (blockid == 2) {
                val = Math.round(val / 1000) / 10;
            } else if (blockid == 3) {
                val = trimDecimal(val * 100, 1);
            } else if (blockid == 4) {
                val = trimDecimal(val, 2);
            } else if (blockid == 5) {
                val = trimDecimal(val / 10000, 0);
            } else if (blockid == 6) {
                val = trimDecimal(val, 2);
            } else if (blockid == 7) {
                val = trimDecimal(val, 0);
            } else if (blockid == 8) {
                val = trimDecimal(val, 0);
            } else if (blockid == 9) {
                val = trimDecimal(val / 10000, 0);
            }


            var label = '';
            if (date_type == 'L') {
                label = '第' + (Number(date.substr(4, 3)) + 1) + '周';
            } else if (date_type == 'M') {
                label = date.substr(0, 4) + '年' + '\n' + date.substr(4, 2) + '月';
            } else if (date_type == 'D') {
                label = date.substr(0, 4) + '年' + '\n' + date.substr(4, 2) + '月' + date.substr(6, 2) + '日';
            } else if (date_type == 'Y') {
                label = date + '年';
            }
            xAxisData.push(label);
            data_s1.push(val);
            if (tb == '0' || tb == null || tb == undefined) {
                data_s2.push(0);
            } else {
                data_s2.push(trimDecimal(tb * 100, 2));
            }

        }
    }

    if (xAxisData.length > 6) {
        xAxisData = xAxisData.slice(-6);
        data_s1 = data_s1.slice(-6);
        data_s2 = data_s2.slice(-6);
    }


    // ---------------------------
    var chart_id1 = 'chart_trend_pop1';
    var chart_id2 = 'chart_trend_pop2';

    var colors = [
        ['#00c311', '#007892'], //柱状图渐变颜色
        ['#6ec7f6', '#03a5ed'], //柱状图渐变颜色
        ['#fbc900', ''], //折线颜色
    ];
    // ---------------------------

    // ---------------------------------------------------------
    // 主KPI图表
    chart_pop1 = echarts.init(document.getElementById(chart_id1));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                return params.name + ': ' + params.value;
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: "15%",
            left: "15%",
            right: "15%",
            bottom: "15%"
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
                formatter: '{value}' + suffix
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'bar',
            barWidth: 16,
            data: data_s1,
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    color: colors[0][0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[0][0]
                        }, {
                            offset: 1,
                            color: colors[0][1]
                        }]),
                }
            },
        }]
    };

    chart_pop1.setOption(option);



    // ---------------------------------------------------------
    // 预算完成率 图表
    chart_pop2 = echarts.init(document.getElementById(chart_id2));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value) + '%';
                return params.name + ': ' + params.value + '%';
            },

        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: "20%",
            left: "15%",
            right: "15%",
            bottom: "15%"
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            boundaryGap: [10, 10],
            nameTextStyle: {
                color: '#41a8ff'
            },
            axisLine: {
                show: false,
                lineStyle: {
                    color: '#84baf0' // 轴线颜色
                }
            },
            axisLabel: {
                show: false,
                interval: 0, // 强制显示所有标签
                rotate: 0,
                textStyle: {
                    color: '#41a8ff', // 轴标签颜色大小
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 12 + fontSizeDiff()
            },
            axisLabel: {
                show: false,
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 12 + fontSizeDiff(),
                },
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'line',
            symbol: 'rect',
            symbolSize: 6,
            smooth: false,
            data: data_s2,
            label: {
                normal: {
                    show: true,
                    //position: 'top'
                }
            },
            lineStyle: {
                normal: {
                    color: colors[2][0],
                }
            },
            // 拐点
            itemStyle: {
                normal: {
                    color: colors[2][0],
                    opacity: 1,
                }
            },
            markLine: {
                silent: true,
                data: [{
                    name: '',
                    yAxis: 0,
                    lineStyle: {
                        normal: {
                            color: '#f82c20',
                            type: 'dashed',
                        }
                    },
                }],
                symbol: '',
                symbolSize: 0,
                label: {
                    normal: {
                        position: 'start',
                        formatter: '{c}%',
                    }
                },
            },
        },]
    };

    chart_pop2.setOption(option);
}

$('#popover_trend_chart .btnx').on('click', function (event) {
    event.preventDefault();
    $('#popover_trend_chart').fadeOut();
});

regTooltip('.btntoop', '查看指标近期趋势');

// 打开弹窗 左边栏 收入
$('.btn_view_chart_l1t1').on('click', function (event) {
    event.preventDefault();
    showTrendChartPopover(1);
});
// 打开弹窗 左边栏 旅客运输量
$('.btn_view_chart_l1t2').on('click', function (event) {
    event.preventDefault();
    showTrendChartPopover(2);
});
// 打开弹窗 左边栏 客座率
$('.btn_view_chart_l1t3').on('click', function (event) {
    event.preventDefault();
    showTrendChartPopover(3);
});
// 打开弹窗 左边栏 小时收入
$('.btn_view_chart_l1t4').on('click', function (event) {
    event.preventDefault();
    showTrendChartPopover(4);
});
// 打开弹窗 左边栏 客公里RPK收入
$('.btn_view_chart_l1t5').on('click', function (event) {
    event.preventDefault();
    showTrendChartPopover(5);
});
// 打开弹窗 左边栏
$('.btn_view_chart_l1t6').on('click', function (event) {
    event.preventDefault();
    showTrendChartPopover(6);
});
// 打开弹窗 左边栏
$('.btn_view_chart_l1t7').on('click', function (event) {
    event.preventDefault();
    showTrendChartPopover(7);
});
// 打开弹窗 左边栏
$('.btn_view_chart_l1t8').on('click', function (event) {
    event.preventDefault();
    showTrendChartPopover(8);
});
// 打开弹窗 左边栏
$('.btn_view_chart_l1t9').on('click', function (event) {
    event.preventDefault();
    showTrendChartPopover(9);
});


// 左边栏 上一页／下一页
var selected_page = 1;
$('.block_l1 .btn_prev').on('click', function (event) {
    event.preventDefault();
    selectPage(1)
});
$('.block_l1 .btn_next').on('click', function (event) {
    event.preventDefault();
    selectPage(2)
});

function selectPage(page) {
    selected_page = page
    if (page == 1) {
        $('.block_l1 .btn_prev').addClass('disabled');
        $('.block_l1 .btn_next').removeClass('disabled');

        $('.block_l1 .page1').css('left', '0');
        $('.block_l1 .page2').css('left', '288px');

        clearTimeout(itv_autoSwitchPage);
        itv_autoSwitchPage = setTimeout(autoSwitchPage, 20000);
    } else {
        $('.block_l1 .btn_prev').removeClass('disabled');
        $('.block_l1 .btn_next').addClass('disabled');

        $('.block_l1 .page1').css('left', '-288px');
        $('.block_l1 .page2').css('left', '0');

        clearTimeout(itv_autoSwitchPage);
        itv_autoSwitchPage = setTimeout(autoSwitchPage, 20000);
    }
}

//自动切换 上一页／下一页
function autoSwitchPage() {

    if (!autoSwitch) {
        itv_autoSwitchPage = setTimeout(autoSwitchPage, 10);
        return;
    }

    if (!audioEnded && !audioPaused) {
        itv_autoSwitchPage = setTimeout(autoSwitchPage, 10);
        return;
    }

    if (selected_page == 1) {
        selectPage(2)
    } else {
        selectPage(1)
    }

}

// 自动循环切换两个TAB
var itv_autoSwitchPage;
clearTimeout(itv_autoSwitchPage);
itv_autoSwitchPage = setTimeout(autoSwitchPage, 20000);



// 切换回 集团公司
$('#hna_button').on('click', function (event) {
    event.preventDefault();
    switchCompany(parent_company);
});



// ---------------------- 

function onCompanyChanged(comp_code) {
    current_company_code = comp_code;
    checkCompanyReay();
}

function checkCompanyReay() {
    if (companyCode2Name[current_company_code] == undefined) {
        setTimeout(checkCompanyReay, 0);
    } else {
        updateAllKpi();

        regTooltip('#hna_button', companyCode2Name[parent_company]);
    }
}


getAllCompanyKpiData();



function setTitleDate() {
    var date = '';
    if (date_type == 'L') {
        date = $('#main_cb_week .combobox_label').text();
    } else if (date_type == 'M') {
        date = $('#main_cb_month .combobox_label').text();
    } else if (date_type == 'Y') {
        date = $('#main_cb_year .combobox_label').text();
    }
    $('.pagetitle .maintitle .date').html(date); // 页面标题-时间
}



// HBI 外部跳转链接
function checkUserInfoLoaded() {
    if (!usersCompayCodeList || usersCompayCodeList.length == 0) {
        setTimeout(checkUserInfoLoaded, 10);
        return;
    }

    if (usersCompayCodeList.indexOf(parent_company) > -1) {
        $('#ext_link1').show();
        $('#ext_link2').show();

    }
}

function setExtLink() {
    if (!weekDateRangeList) {
        setTimeout(setExtLink, 0);
        return;
    }
    var date = getCurrentDate();
    var daterange;
    var datetype;
    if (date_type == 'L') {
        datetype = '例会周';
        daterange = weekDateRangeList[date];

        //$('#ext_link1').attr('href', encodeURI('http://cdp.hnair.net:8080/HBI/rdThird.do?res_id=146277592351729810&paramArr=日期=<' + daterange + '>::报表类型=<' + datetype + '>'));
        //PCR_335. 20171212
        //$('#ext_link0').attr('href', encodeURI('http://cdp.hnair.net:8080/HBI/rdThird.do?res_id=141640922033173711&paramArr=3公司=<'+current_company_code+'>::2日期=<'+daterange+'>::1报表类型=<'+datetype+'>::4收入类型=<增值税>'));
        //$('#ext_link2').attr('href', encodeURI('http://cdp.hnair.net:8080/HBI/rdThird.do?res_id=143435390639569110&paramArr=2日期=<'+daterange+'>::1报表类型=<'+datetype+'>::3收入类型=<增值税>'));
        //$('#ext_link0').attr('href', encodeURI('http://cdp.hnair.net:8080/HBI/rdThird.do?res_id=146277592351729810&paramArr=日期=<' + daterange + '>::报表类型=<' + datetype + '>'));
        //$('#ext_link2').attr('href', encodeURI('http://cdp.hnair.net:8080/HBI/rdThird.do?res_id=146277592351729810&paramArr=日期=<' + daterange + '>::报表类型=<' + datetype + '>'));
        $('#ext_link2').attr('href', 'http://cdp.hnair.net:8080/HBI/rdThird.do?res_id=146277592351729810&paramArr=日期=<' + daterange + '>::报表类型=<' + datetype + '>');
    } else if (date_type == 'M') {
        datetype = '月';
        daterange = date;
        /*
        var m = Number(date.substr(4,2));
        var m1 = [1,3,5,7,8,10,12];
        var m2 = [2,4,6,9,11];
        var end;
        if(m1.indexOf(m) > -1){
            end = 31;
        }else if(m2.indexOf(m) > -1){
            end = 30;
        }else{
            end = 28;
        }
        daterange = date+'01'+'-'+date+''+end;
        */
        // var link = encodeURI('http://cdp.hnair.net:8080/HBI/rdThird.do?res_id=146277595260497218&paramArr=起始日期=<' + date + '>::结束日期=<' + date + '>');
        // $('#ext_link1').attr('href', link);
        // $('#ext_link2').attr('href', link);
        $('#ext_link2').attr('href', 'http://cdp.hnair.net:8080/HBI/rdThird.do?res_id=146277595260497218&paramArr=起始日期=<' + date + '>::结束日期=<' + date + '>');
    } else if (date_type == 'Y') {
        datetype = '年';
        daterange = date;
        $('#ext_link2').attr('href', 'http://cdp.hnair.net:8080/HBI/rdThird.do?res_id=141640922033173711&paramArr=3公司=<' + current_company_code + '>::2日期=<' + date + '>::1报表类型=<' + datetype + '>::4收入类型=<增值税>');
    } else {
        datetype = '日';
        daterange = date;
        $('#ext_link2').attr('href', 'http://cdp.hnair.net:8080/HBI/rdThird.do?res_id=146277592351729810&paramArr=日期=<' + daterange + '>::报表类型=<' + datetype + '>');
    }
    if (date_type == 'Y') {
        $('#ext_link0').attr('href', 'http://cdp.hnair.net:8080/HBI/rdThird.do?res_id=141640922033173711&paramArr=3公司=<' + current_company_code + '>::2日期=<' + date + '>::1报表类型=<' + datetype + '>::4收入类型=<增值税>');
    } else {
        $('#ext_link0').attr('href', encodeURI('http://cdp.hnair.net:8080/HBI/rdThird.do?res_id=141640922033173711&paramArr=3公司=<' + current_company_code + '>::2日期=<' + daterange + '>::1报表类型=<' + datetype + '>::4收入类型=<增值税>'));
    }
    // 去掉该链接
    // $('#ext_link1').attr('href', 'http://cdp.hnair.net:8080/HBI/rdThird.do?res_id=146277592351729810&paramArr=日期=<' + daterange + '>::报表类型=<' + datetype + '>');
    // $('#ext_link2').attr('href', 'http://cdp.hnair.net:8080/HBI/rdThird.do?res_id=146277592351729810&paramArr=日期=<' + daterange + '>::报表类型=<' + datetype + '>');
}
checkUserInfoLoaded();
regTooltip('.ext_link', '查看关联报表');
regTooltip('#totalYear', '备注：今年1月1日截至本期最后1日的 客运收入累计（含燃油，增值税）');
regTooltip("#week_trend", '查看指标近期趋势');



// 第一次使用的用户引导
function showFirstTimeGuide() {
    if (userinfo && userinfo.id) {
        $('#user_guide').show();
        $('#user_guide').append('<div class="img"></div>');
        showUserCenter();
        var info = $('#pop_usercenter').clone();
        $('#user_guide').append(info);
        hideUserCenter();

        $('#user_guide').on('click', function (event) {
            event.preventDefault();
            $('#user_guide').remove();

            //
            localStorage.setItem('first_time_guide_viewed', 1);

        });

    } else {
        setTimeout(showFirstTimeGuide, 10);
    }
}

if (localStorage.getItem('first_time_guide_viewed') == undefined) {
    showFirstTimeGuide();
}
// 右边栏上下翻按钮事件
function upDownEvent() {
    $(".button_cover .button").on('click', function () {
        var _this = $(this);
        if (_this.hasClass('down')) {
            $(".block_cover .scroll_cover").animate({
                top: -215 + "px"
            }, 500, function () {
                _this.hide();
                $(".button_cover .up").show();
            });
        }
        if (_this.hasClass('up')) {
            $(".block_cover .scroll_cover").animate({
                top: 0 + "px"
            }, 500, function () {
                _this.hide();
                $(".button_cover .down").show();
            });
        }
    })
}
upDownEvent();
