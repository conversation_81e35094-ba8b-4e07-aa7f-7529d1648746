showLoading();


// ------------------------------------------------------------

$('#mainframe').attr('src', 'map/map-a2-3.html');

// ------------------------------------------------------------




var current_company_code = 'HU'; // 只做股份一家公司的   //parent_company;
var current_company_id = 100100; // 只做股份一家公司的
$('#companycombo').hide(); // 不显示成员公司



// 获取24小时售票情况，总量
function getTodayTicketSale(){
    var param = {
        company: "100100"
    }

    $.ajax({
        type: 'post',
        url:"/bi/web/todayticketsale",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            setBlockLKpi(response);
            setBlockRKpi(response);

            hideLoading();
            
        },
        error:function() {
        }
    });
}


// 今日累计销售情况
var currentCounterNumber = 0;
function setBlockLKpi(data){

    var total_sales = data.SAL_AMT.total;
    var total_tickets = data.SAL_VOL.total;
    var ticket_avg = formatCurrency(Math.round(Number(total_sales)/Number(total_tickets)),0);

    $('.block_l .tkt_sal .val').text(formatCurrency(total_sales,0));
    $('.block_l .tkt_avg .val').text(ticket_avg);

    // ------------------------------------------------------
    // 累计客票 计数器
    // ------------------------------------------------------

    var numFrom = currentCounterNumber;
    var numTo = Number(total_tickets);
    currentCounterNumber = numTo;

    function ticketCountTo(){
        $.animateToNumber('counterContainer', numTo, numFrom, null, 'group', {0: '张'});
        numFrom = numTo;
    }

    ticketCountTo(numTo);

    var numberWidth = 24;
    var numberOfGap = Math.ceil(numTo.toString().length/3)-1;
    var gapWidth = 5;
    var splitterWidth = 20;
    var counterWidth = numTo.toString().length*numberWidth + numberOfGap*gapWidth + splitterWidth + 130;
    $('#counterContainer').css('width', counterWidth+'px');

}

// 今日出票趋势
function setBlockRKpi(data){

    var hourdata = data.SAL_VOL.data;

    // ---------------------------
    var chart_id = 'chart_r';
    
    var colors = [
        ['#ceeffe', '#4ee443'], //柱状图渐变颜色
    ];
    // ---------------------------

    


    var xAxisData = [];
    var data_s1 = []; //主KPI


    var len = 24;
    for(var i=0; i<len; i++){
        xAxisData.push(i);
        var val = hourdata[i];
        if(isNaN(val)){
            val = 0;
        }
        data_s1.push(val);
    }


    // ---------------------------------------------------------
    // 主KPI图表
    var chart = echarts.init(document.getElementById(chart_id));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                return params.name + ': ' + params.value;
             },

        },
        legend: {
            show:false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle:{
              color: 'rgba(255,255,255,0.7)',
              fontSize: 11+fontSizeDiff()
            },
            data:[
                {
                    name:'',
                    icon:'circle',
                }
            ]
        },
        grid:{
            top: 20,
            left: 44,
            right: 10,
            bottom: 30,
        },
        xAxis: [
            {
                type: 'category',
                data: xAxisData,
                //boundaryGap: [10,10],
                nameTextStyle:{
                    color: '#fff'
                },
                axisLine:{
                    lineStyle:{
                        color:'#84baf0' // 轴线颜色
                    }
                },
                axisLabel:{
                    interval: 1, // 间隔显示
                    rotate: 0,
                    textStyle:{
                        color:'#fff', // 轴标签颜色大小
                        fontSize: 12+fontSizeDiff(),
                    },
                },
                axisTick: {
                    show: false, // 不显示刻度线
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
                name: '',
                nameTextStyle:{
                    color: '#41a8ff',
                    fontSize: 11+fontSizeDiff()
                },
                axisLabel:{
                    textStyle:{
                        color:'#41a8ff',
                        fontSize: 11+fontSizeDiff(),
                    },
                    formatter: '{value}'
                },
                axisLine:{
                    lineStyle:{
                        color:'rgba(0,0,0,0)'
                    }
                },
                splitLine:{
                    show:true,
                    lineStyle:{
                        color: ['rgba(255,255,255,0.1)'] // 分割线颜色
                    }
                },
            }
        ],
        series: [
            {
                name: '',
                type:'bar',
                barWidth: 6,
                animation: false,
                data: data_s1,
                label: {
                    normal: {
                        show: false,
                        position: 'top'
                    }
                },
                itemStyle: {
                    normal:{
                        color: new echarts.graphic.LinearGradient(
                            0, 0, 0, 1,
                            [
                                {offset: 0, color: colors[0][0]},
                                {offset: 1, color: colors[0][1]}
                            ]),
                    }
                },
            }
        ]
    };

    chart.setOption(option);

    
}







// ---------------------- 

function onCompanyChanged(comp_code){
    current_company_code = comp_code;
    checkCompanyReay();
}

function checkCompanyReay(){
    if(companyCode2Name[current_company_code] == undefined){
        setTimeout(checkCompanyReay,0);
    }else{
        // TODO.....
    }
}




// 获取所有数据
function fetchAllData(){
    getTodayTicketSale();
}




var interval = 300000; //5分钟刷新一次数据
setInterval(fetchAllData, interval);




