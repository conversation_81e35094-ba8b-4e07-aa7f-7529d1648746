(function () {

    dfd.done(function () {
        if (!hasAllCompanyPermission()) {
            $(".CARGO_INC .detail").remove();
            return;
        }
    })

    /**
     * 旅客,航班,货运对比
     */

    $(".CARGO_INC .detail").on("click", function () {
        $("#pop_cargo_detail").removeClass("hide");
        $(".windowMask").removeClass("hide");

        var dateId = $(".zongshouru").attr("dateKey");
        var dateType = getDateType();

        if (window.cargoDetailWin == null) {
            intCargoDetailWin(dateType, dateId);
        } else {
            window.cargoDetailWin.refreshView(dateType, dateId);
        }
    });


    function intCargoDetailWin(dateType, dateId) {
        var page = new Vue({
            el: '.cargo-detail-win-body',
            template: $("#cargo_detail_template").html(),
            data: function () {
                return {
                    "dateType": dateType,
                    "weeks": weeks,
                    showWeekList: false,
                    selectedWeek: null,
                    weekCmpActive: false,
                    quanhuojis: [],
                    fucangs: [],
                    kelahuos: [],
                    cargoNums: [],
                    mounted: false,
                    cargoCompany: cargoCompany
                }
            },
            mounted: function () {
                //日
                var me = this;

                if (this.weeks.length > 1) {
                    this.selectedWeek = weeks[1]
                }



                $(me.$refs["datetimepicker_D"]).datetimepicker({
                    defaultDate: new Date(),
                    format: "YYYY/MM/DD",
                    sideBySide: true,
                    maxDate: new Date(),
                    widgetPositioning: {
                        horizontal: 'right'
                    },
                    locale: 'zh-cn'
                });
                //月
                $(me.$refs['datetimepicker_M']).datetimepicker({
                    defaultDate: new Date(),
                    format: "YYYY/MM",
                    viewMode: 'months',
                    sideBySide: true,
                    maxDate: new Date(),
                    widgetPositioning: {
                        horizontal: 'right'
                    },
                    locale: 'zh-cn'
                });
                //年
                $(me.$refs['datetimepicker_Y']).datetimepicker({
                    defaultDate: new Date(),
                    format: "YYYY",
                    viewMode: 'years',
                    sideBySide: true,
                    maxDate: new Date(),
                    widgetPositioning: {
                        horizontal: 'right'
                    },
                    locale: 'zh-cn'
                });


                me.setDatePickerValue(dateType, dateId);
                //先查数据,再做下面事件监听,不然可能会触发dp.change事件,多做一次请求
                me.queryData(me.getDate());

                $(me.$refs["datetimepicker_D"]).on("dp.change", function (e) {
                    me.queryData(e.date._d);

                });
                $(me.$refs['datetimepicker_M']).on("dp.change", function (e) {
                    me.queryData(e.date._d);
                });
                $(me.$refs['datetimepicker_Y']).on("dp.change", function (e) {
                    me.queryData(e.date._d);
                });
                me.mounted = true;


            },
            methods: {
                setDatePickerValue(dateType, dateId) {
                    var me = this;
                    if (dateType != 'L') {
                        $(me.$refs[`datetimepicker_${dateType}`]).data("DateTimePicker").date(me.getDateStr(dateType, dateId));
                    } else {
                        var selectedWeek = me.weeks.find(v => { return v.DATE_ID == dateId });
                        if (me.selectedWeek != selectedWeek) {
                            me.selectedWeek = selectedWeek;
                            //改变才数据,且在mounted 之后,
                            if (me.mounted) {
                                me.queryData(selectedWeek);
                            }

                        }

                    }
                },
                refreshView(dateType, dateId) {
                    var me = this;
                    if (me.dateType != dateType) {
                        me.switchDateType(dateType);
                    } else {
                        me.setDatePickerValue(dateType, dateId);
                    }

                },
                getDateStr(dateType, dateId) {
                    if (dateType == 'D') {
                        return moment(dateId, 'YYYYMMDD').format('YYYY/MM/DD');
                    } else if (dateType == 'M') {
                        return moment(dateId, 'YYYYMM').format('YYYY/MM');;
                    } else if (dateType == 'Y') {
                        return dateId;
                    }
                },
                getWeekDesc() {
                    return this.selectedWeek ? this.selectedWeek.DATE_DESC_XS : ''
                },
                onWeekMouseOut() {
                    this.weekCmpActive = false;
                    setTimeout(this.validActive, 300)
                },
                onWeekMouseOver() {
                    this.weekCmpActive = true;
                },
                validActive() {
                    if (!this.weekCmpActive) {
                        this.showWeekList = false;
                    }
                },
                dropWeekList() {
                    this.showWeekList = true;
                },
                onWeekChange(week) {
                    this.selectedWeek = week;
                    this.showWeekList = false;
                    this.queryData(week);

                },
                getWeekLabel: function (item) {
                    if (item) {
                        var week = item.DATE_ID.substring(5, 7);
                        var year = item.DATE_ID.substring(0, 4);
                        return `${year}年第${week}周`;
                    }
                },
                closeWin: function () {
                    $("#pop_cargo_detail").addClass("hide");
                    $(".windowMask").addClass("hide");
                },
                switchDateType(dateType) {
                    this.dateType = dateType;
                    var date = this.getDate();
                    this.queryData(date);
                },
                getDate() {
                    var me = this;
                    var dateType = this.dateType;
                    if (dateType == 'D') {
                        return $(me.$refs['datetimepicker_D']).data("DateTimePicker").date().startOf("day")._d;
                    } else if (dateType == 'L') {
                        return this.selectedWeek;
                    } else if (dateType == 'M') {
                        return $(me.$refs['datetimepicker_M']).data("DateTimePicker").date().startOf("day")._d
                    } else if (dateType == 'Y') {
                        return $(me.$refs['datetimepicker_Y']).data("DateTimePicker").date().startOf("day")._d
                    }
                },
                formatData(value, divNum, precision) {
                    if (divNum == 0) {
                        return "-"
                    }
                    return toFixed(value / divNum, precision)
                },
                getKpiData(kpiCode, data) {
                    if (data != null) {
                        var list = data[kpiCode];
                        if (list != null && list.length > 0) {
                            return list[0].KPI_VALUE
                        }
                        return null;
                    }
                    return null;
                },
                getKpiDataTongbi(kpiCode, data) {
                    if (data != null) {
                        var list = data[kpiCode];
                        if (list != null && list.length > 0) {
                            return list[0].KPI_RATIO_TQ;

                        }
                        return null;
                    }
                    return null;
                },
                formatTongBi(tongBiValue) {
                    if (tongBiValue != null && tongBiValue != "") {
                        var tb = toFixed(tongBiValue * 100, 1) + '%';
                        if (tongBiValue < 0) {
                            tb += " ↓"
                        } else if (tongBiValue > 0) {
                            tb += " ↑"
                        } else {
                            tb += "     "
                        }
                        return tb;
                    }
                    return "-";
                },
                getRate(num1, divNum, precision) {
                    if (num1 == 0 || divNum == '' || divNum == 0) {
                        return "0%"
                    }
                    return toFixed(num1 / divNum * 100, precision) + "%";
                },
                getTongBiRate() {

                },
                getCargoCompanyName(cmpId) {
                    for (var i = 0; i < this.cargoCompany.length; i++) {
                        if (cmpId == this.cargoCompany[i].id) {
                            return this.cargoCompany[i].name;
                        }
                    }
                    return "";
                },
                queryData(date) {
                    var me = this;
                    eking.ui.loading.show();
                    //腹舱收入 DETAIL_CARGO_INC_BELLY 10207   腹舱班次 DETAIL_CARGO_SHIFTS_BELLY 10210
                    //全货机收入 DETAIL_CARGO_INC_FULL 10208  全货机班次 DETAIL_CARGO_SHIFTS_FULL 10211
                    //客拉货收入 DETAIL_CARGO_INC_PASS 10209  客拉货班次 DETAIL_CARGO_SHIFTS_PASS 10212

                    var companyIds = [];
                    for (var i = 0; i < this.cargoCompany.length; i++) {
                        companyIds.push(this.cargoCompany[i].id);
                    }

                    var param = {
                        "DATE_ID": getDateId(date, this.dateType),
                        "DATE_TYPE": this.dateType,
                        'KPI_CODE': "DETAIL_CARGO_INC_FULL,DETAIL_CARGO_SHIFTS_FULL",
                        'COMP_ID': companyIds.join(",")
                    }
                    var d1 = $.ajax({
                        type: 'post',
                        url: "/bi/query/getfaccomkpi?cargoDetailWin_quanhuoji",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(param),
                        success: function (response) {
                            var quanhuojis = [];
                            var data1 = response.data;
                            var parentCompanyCode = '100';
                            var hnaData = data1[parentCompanyCode];
                            var HNA_DETAIL_CARGO_INC_FULL = me.getKpiData('DETAIL_CARGO_INC_FULL', hnaData);
                            var HNA_DETAIL_CARGO_SHIFTS_FULL = me.getKpiData('DETAIL_CARGO_SHIFTS_FULL', hnaData);
                            for (var p in data1) {
                                if (p != parentCompanyCode) {
                                    var item = data1[p];
                                    var data = {
                                        inc: me.getKpiData('DETAIL_CARGO_INC_FULL', item),
                                        incTB: me.getKpiDataTongbi('DETAIL_CARGO_INC_FULL', item),
                                        shifts: me.getKpiData('DETAIL_CARGO_SHIFTS_FULL', item),
                                        shiftsTB: me.getKpiDataTongbi('DETAIL_CARGO_SHIFTS_FULL', item),
                                        companyCode: p,
                                        companyName: me.getCargoCompanyName(p)
                                    }
                                    data = $.extend(data, {
                                        incRate: me.getRate(data['inc'], HNA_DETAIL_CARGO_INC_FULL, 1),
                                        shiftsRate: me.getRate(data['shifts'], HNA_DETAIL_CARGO_SHIFTS_FULL, 1)
                                    })
                                    quanhuojis.push(data);

                                }
                            }
                            // quanhuojis.sort(function (a, b) {
                            //     return getCompanyIndex(a.companyCode) - getCompanyIndex(b.companyCode)
                            // });
                            me.quanhuojis = quanhuojis;


                        }
                    });
                    //腹舱收入 DETAIL_CARGO_INC_BELLY 10207   腹舱班次 DETAIL_CARGO_SHIFTS_BELLY 10210
                    var kpi_list2 = [
                        "DETAIL_CARGO_INC_BELLY",
                        "DETAIL_CARGO_SHIFTS_BELLY"
                    ];

                    var param2 = {
                        "DATE_ID": getDateId(date, this.dateType),
                        "DATE_TYPE": this.dateType,
                        'KPI_CODE': kpi_list2.join(','),
                        'COMP_ID': companyIds.join(",")
                    }
                    var d2 = $.ajax({
                        type: 'post',
                        url: "/bi/query/getfaccomkpi?cargoDetailWin_fucang",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(param2),
                        success: function (response) {
                            var fucangs = [];
                            var data1 = response.data;
                            var parentCompanyCode = '100';
                            var hnaData = data1[parentCompanyCode];
                            var HNA_DETAIL_CARGO_SHIFTS_BELLY = me.getKpiData('DETAIL_CARGO_SHIFTS_BELLY', hnaData);
                            var HNA_DETAIL_CARGO_INC_BELLY = me.getKpiData('DETAIL_CARGO_INC_BELLY', hnaData);
                            for (var p in data1) {
                                if (p != parentCompanyCode) {
                                    var item = data1[p];
                                    var data = {
                                        inc: me.getKpiData('DETAIL_CARGO_INC_BELLY', item),
                                        shifts: me.getKpiData('DETAIL_CARGO_SHIFTS_BELLY', item),
                                        incTB: me.getKpiDataTongbi('DETAIL_CARGO_INC_BELLY', item),
                                        shiftsTB: me.getKpiDataTongbi('DETAIL_CARGO_SHIFTS_BELLY', item),
                                        companyCode: p,
                                        companyName: me.getCargoCompanyName(p)
                                    }
                                    data = $.extend(data, {
                                        shiftsRate: me.getRate(data['shifts'], HNA_DETAIL_CARGO_SHIFTS_BELLY, 1),
                                        incRate: me.getRate(data['inc'], HNA_DETAIL_CARGO_INC_BELLY, 1)
                                    })
                                    fucangs.push(data);


                                }
                            }
                            // fucangs.sort(function (a, b) {
                            //     return getCompanyIndex(a.companyCode) - getCompanyIndex(b.companyCode)
                            // });
                            me.fucangs = fucangs;


                        }
                    });



                    //客拉货收入 DETAIL_CARGO_INC_PASS 10209  客拉货班次 DETAIL_CARGO_SHIFTS_PASS 10212


                    var param3 = {
                        "DATE_ID": getDateId(date, this.dateType),
                        "DATE_TYPE": this.dateType,
                        'KPI_CODE': 'DETAIL_CARGO_INC_PASS,DETAIL_CARGO_SHIFTS_PASS',
                        'COMP_CODE': getAllCompany(true).join(",")
                    }

                    var d3 = $.ajax({
                        type: 'post',
                        url: "/bi/query/getfaccomkpi?cargoDetailWin_kelahuo",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(param3),
                        success: function (response) {

                            var kelahuos = [];
                            var data1 = response.data;
                            var parentCompanyCode = 'HNAHK';
                            var hnaData = data1[parentCompanyCode];
                            var HNA_DETAIL_CARGO_INC_PASS = me.getKpiData('DETAIL_CARGO_INC_PASS', hnaData);
                            var HNA_DETAIL_CARGO_SHIFTS_PASS = me.getKpiData('DETAIL_CARGO_SHIFTS_PASS', hnaData);
                            for (var p in data1) {
                                if (p != parentCompanyCode) {
                                    var item = data1[p];
                                    var data = {
                                        inc: me.getKpiData('DETAIL_CARGO_INC_PASS', item),
                                        shifts: me.getKpiData('DETAIL_CARGO_SHIFTS_PASS', item),
                                        incTB: me.getKpiDataTongbi('DETAIL_CARGO_INC_PASS', item),
                                        shiftsTB: me.getKpiDataTongbi('DETAIL_CARGO_SHIFTS_PASS', item),
                                        companyCode: p,
                                        companyName: companyCode2Nameabbr[p]
                                    }
                                    data = $.extend(data, {
                                        incRate: me.getRate(data['inc'], HNA_DETAIL_CARGO_INC_PASS, 1),
                                        shiftsRate: me.getRate(data['shifts'], HNA_DETAIL_CARGO_SHIFTS_PASS, 1)
                                    })
                                    kelahuos.push(data);
                                }
                            }
                            kelahuos.sort(function (a, b) {
                                return getCompanyIndex(a.companyCode) - getCompanyIndex(b.companyCode)
                            });

                            me.kelahuos = kelahuos;
                        }
                    });

                    var dtds = [d1, d2, d3];
                    $.when.apply(this, dtds).then(function () {
                        eking.ui.loading.hide();
                    });

                },
                drawChart(seriesdata) {
                    var me = this;
                    var chart = echarts.init(me.$refs['cargoNumDom']);
                    var option = {
                        color: colors,
                        series: [{
                            type: 'pie',
                            radius: ['40%', '60%'],
                            avoidLabelOverlap: true,
                            label: {
                                show: false,
                                position: 'center'
                            },
                            emphasis: {
                                label: {
                                    show: true,
                                    fontSize: getChartFontSize(16),
                                    fontWeight: 'bold'
                                }
                            },
                            labelLine: {
                                show: false
                            },
                            data: seriesdata
                        }]

                    };
                    chart.setOption(option);
                }

            }
        });

        window.cargoDetailWin = page;

    }





})()