document.oncontextmenu = function () {
  return false;
};

var company_code;
var companylist;
var companyCode2Sort;

var rangeObj = this;

var createBMap = function () {
  var series = [];

  option = {
    color: [],
    tooltip: {
      trigger: "item",
      show: true,
      formatter: function (params, ticket, callback) {
        //console.log(params);
        var data = params.data;

        var html = '<div style="padding: 5px;">';

        if (data.isCity) {
          var response = window.parent.window.weather[data.name];
          html += data.name;
          if (response) {
            html += "<br>";

            var weatherInfoCodes = ["FG", "TS", "TSRA", "RA", "SS", "DS"];
            var visibility =
              isNaN(response.visibility) || response.visibility == 0
                ? 9999
                : Number(response.visibility); //能见度
            var rvr =
              isNaN(response.rvr) || response.rvr == 0
                ? 9999
                : Number(response.rvr); //跑道目视距离
            var cloudInfo =
              isNaN(response.cloudInfo) || response.cloudInfo == 0
                ? 9999
                : Number(response.cloudInfo); //云况
            var windFs = isNaN(response.windFs) ? 0 : Number(response.windFs); //风速
            var weatherInfo = response.weatherInfo; //天气现象
            var weatherInfoTxt = response.weatherInfoTxt.replace(
              /<[^>]+>/g,
              ""
            );
            var temperature = isNaN(response.temperature)
              ? "-"
              : Number(response.temperature) + "℃";

            html += weatherInfoTxt + "<br>";
            html += "气温: " + temperature + "<br>";
            html += "风速: " + windFs + "km/h<br>";
            html += "能见度: " + visibility + "m<br>";
          }
        } else {
          var flt = data.flt;
          var depAirport = window.parent.window.airportList[flt.depStn]; //出发机场信息
          var arrAirport = window.parent.window.airportList[flt.arrStn]; //到达机场信息

          //航班号、起飞城市-到达城市、航班状态（实际/预计/计划起飞时间-实际/预计/计划到达时间）、机号（机型）、客座率（布局）、实时位置、实施油量
          html += "航班号: " + data.name + "<br>";
          html += flt.depCity + " - " + flt.arrCity + "<br>";
          if (
            flt.status == "DEP" ||
            flt.status == "ARR" ||
            flt.status == "NDR" ||
            flt.status == "ATA"
          ) {
            // 起飞,落地,到达
            // 实际起飞时间 atdChn
            html += "实际起飞时间: " + trimTime(flt.atdChn) + "<br>";
          } else {
            // 预计出发
            html += "预计出发时间: " + trimTime(flt.etdChn) + "<br>";
          }

          if (flt.status == "ATA") {
            // 到达
            // 实际起飞时间 atdChn
            html += "实际到达时间: " + trimTime(flt.ataChn) + "<br>";
          } else {
            // 预计到达
            html += "预计到达时间: " + trimTime(flt.etaChn) + "<br>";
          }

          if (flt.delay1 != "" && flt.dur1 > 0) {
            html += "延误原因: " + flt.delay1Name + "<br>";
            html +=
              "延误时间: " +
              (Number(flt.dur1) +
                Number(flt.dur2) +
                Number(flt.dur3) +
                Number(flt.dur4)) +
              "分钟<br>";
          }

          html += "机号: " + data.acno + "(" + flt.acType + ")" + "<br>";
          html +=
            "实时位置: " +
            Math.round(data.value[0] * 10000) / 10000 +
            ", " +
            Math.round(data.value[1] * 10000) / 10000 +
            "<br>";
          // html += '实时油量: ' + Math.round(data.oil) + 'cc';
        }

        html += "</div>";

        return html;
      },
      backgroundColor: "#021e55",
    },
    geo: {
      map: "world",
      roam: true,
      zoom: 1.88,
      center: [60, 25],
      silent: true,
      label: {
        emphasis: {
          show: false,
        },
      },
      itemStyle: {
        normal: {
          areaColor: "#3d7dd0",
          borderColor: "#14224c",
        },
        emphasis: {
          areaColor: "#3d7dd0",
          borderColor: "#14224c",
        },
      },
      regions: [
        {
          name: "ChinaLine",
          itemStyle: {
            normal: {
              borderWidth: 2,
              areaColor: "#3d7dd0",
              borderColor: "#3d7dd0",
            },
          },
        },
      ],
      //regions:countries
    },
    backgroundColor: "#14224c",
    series: series,
  };

  var myChart = echarts.init(document.getElementById("map"));
  myChart.setOption(option);
  window.onresize = myChart.resize;

  rangeObj.myChart = myChart;

  // 点击事件
  myChart.on("click", function (params) {
    params.event.event.stopPropagation();
    params.event.event.preventDefault();

    if (params.data.isAirline == undefined) {
      setAirline(params.data);
      window.parent.window.showFlightDetails(params.data);
    }
  });

  // 经纬度坐标转换成屏幕像素坐标
  function geoCoord2Pixel(geoCoord) {
    var point = new BMap.Point(geoCoord[0], geoCoord[1]);
    var pos = map.pointToOverlayPixel(point);
    return [pos.x, pos.y];
  }
};

// 飞机位置
function setPlaneLocation() {
  if (rangeObj.myChart != undefined) {
    var planes = window.parent.window.planeLocationList;
    var flightList = window.parent.window.flightInfoList;

    var series = [];
    var list_1 = []; // 正常航班
    var list_2 = []; // 延误航班
    var len = planes.length;

    var statusMap = {
      ARR: "落地",
      NDR: "落地",
      ATD: "推出",
      ATA: "到达",
      CNL: "取消",
      DEL: "延误",
      DEP: "起飞",
      RTR: "返航",
      SCH: "计划",
    };

    //var fltlstxxx = [];
    //var fltlstxxx2 = [];

    //for(var fi in flightList){
    //  var flt = flightList[fi];
    //  if(flt.status == 'DEP' && fi.indexOf('GS') > -1){
    //    fltlstxxx2.push(fi);
    //  }
    //}

    for (var i = 0; i < len; i++) {
      var dat = planes[i];

      var flt = flightList[dat.fltno];

      if (flt) {
        if (flt.status == "DEP" && flt.delay1 == "") {
          // 正常在飞

          //fltlstxxx.push(dat.fltno);

          list_1.push({
            name: dat.fltno,
            acno: dat.acno,
            oil: dat.oil,
            vec: dat.vec,
            flt: flt,
            value: [dat.lon, dat.lat],
            symbolRotate: -dat.vec + 90, //航向   正北是0°，顺时针，0到360° // 因为图片机头指向右，所以需要逆时针转90度
          });
        } else if (flt.status == "DEP" && flt.delay1 != "") {
          // 延误航班

          //fltlstxxx.push(dat.fltno);

          list_2.push({
            name: dat.fltno,
            acno: dat.acno,
            oil: dat.oil,
            vec: dat.vec,
            flt: flt,
            value: [dat.lon, dat.lat],
            symbolRotate: -dat.vec + 90, //航向   正北是0°，顺时针，0到360° // 因为图片机头指向右，所以需要逆时针转90度
          });
        }
      }
    }

    //console.log('fltlstxxx', fltlstxxx);
    //console.log('fltlstxxx2', fltlstxxx2);

    series.push({
      name: "normal",
      type: "scatter",
      coordinateSystem: "geo",
      symbol: "image://../img/a3.3.legend_1.png",
      symbolSize: 15,
      label: {
        normal: {
          show: false,
        },
      },
      data: list_1,
    });

    series.push({
      name: "delay",
      type: "scatter",
      coordinateSystem: "geo",
      symbol: "image://../img/a3.3.legend_2.png",
      symbolSize: 15,
      label: {
        normal: {
          show: false,
        },
      },
      data: list_2,
    });

    var options = {
      series: series,
    };

    rangeObj.myChart.setOption(options);
  } else {
    setTimeout(setPlaneLocation, 0);
  }
}

// 设置航线
function setAirline(dat) {
  var series = [];

  var flt = window.parent.window.flightInfoList[dat.name];

  var depAirport = window.parent.window.airportList[flt.depStn]; //出发机场信息
  var arrAirport = window.parent.window.airportList[flt.arrStn]; //到达机场信息

  // 加载 飞行轨迹
  var param = {
    mode: "track",
    fi: dat.name,
  };
  $.ajax({
    type: "post",
    url: "/bi/web/flightMq",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      // 计划出发时间
      var date = new Date();
      var stdChnTM = parserDate(flt.stdChn); // 计划出发时间
      var ts_dep = stdChnTM.getTime() - 8 * 60 * 60 * 1000;
      date.setTime(ts_dep);
      var mm = date.getMonth() + 1;
      var dd = date.getDate();
      var h = date.getHours();
      var m = date.getMinutes();
      var s = date.getSeconds();
      if (mm < 10) {
        mm = "0" + mm;
      }
      if (dd < 10) {
        dd = "0" + dd;
      }
      if (h < 10) {
        h = "0" + h;
      }
      if (m < 10) {
        m = "0" + m;
      }
      if (s < 10) {
        s = "0" + s;
      }
      var utc_dep =
        date.getFullYear() + "" + mm + "" + dd + "" + h + "" + m + "" + s; // 现在时间
      ///////

      var data = [];

      var pointlist = response.data;

      for (var i = pointlist.length - 1; i >= 0; i--) {
        var d = pointlist[i];
        d.UTC = d.UTC.replace(/\D/g, "");
      }

      // 国内航线
      if (flt.fltType != "I") {
        // 删除相同时间的坐标点
        var idx = 0;
        for (var i = 0; i < pointlist.length; i++) {
          pointlist[i].idx = idx;
          idx++;
          var d = pointlist[i];
          for (var j = pointlist.length - 1; j >= 0; j--) {
            var d2 = pointlist[j];
            if (d.UTC == d2.UTC && d.idx != d2.idx) {
              pointlist.splice(j, 1);
            }
          }
        }

        // 国内航线删除坐标带E，W，N，S的坐标，避免两个MQ出现航路偏差
        for (var i = pointlist.length - 1; i >= 0; i--) {
          var d = pointlist[i];
          if (isNaN(d.LAT) || isNaN(d.LON)) {
            pointlist.splice(i, 1);
          }
        }
      }

      var date = new Date();
      var ts = date.getTime() - 86400000;
      var ts_now = date.getTime() - 8 * 60 * 60 * 1000;

      date.setTime(ts);
      var mm = date.getMonth() + 1;
      var dd = date.getDate();
      if (mm < 10) {
        mm = "0" + mm;
      }
      if (dd < 10) {
        dd = "0" + dd;
      }
      var utc_time = date.getFullYear() + "" + mm + "" + dd + "210000"; // UTC 昨天 21点，北京时间今天早上5点

      //
      date.setTime(ts_now);
      var mm = date.getMonth() + 1;
      var dd = date.getDate();
      var h = date.getHours();
      var m = date.getMinutes();
      var s = date.getSeconds();
      if (mm < 10) {
        mm = "0" + mm;
      }
      if (dd < 10) {
        dd = "0" + dd;
      }
      if (h < 10) {
        h = "0" + h;
      }
      if (m < 10) {
        m = "0" + m;
      }
      if (s < 10) {
        s = "0" + s;
      }
      var utc_now =
        date.getFullYear() + "" + mm + "" + dd + "" + h + "" + m + "" + s; // 现在时间

      for (var i = pointlist.length - 1; i >= 0; i--) {
        // 删除昨天的航迹坐标
        var d = pointlist[i];
        if (d.UTC < utc_time || d.UTC > utc_now || d.UTC < utc_dep) {
          pointlist.splice(i, 1);
        }
      }

      pointlist.sort(function (a, b) {
        return Number(b.UTC) - Number(a.UTC);
      });

      console.log("pointlist", pointlist);

      if (pointlist && pointlist.length > 1) {
        var len = pointlist.length;
        for (var i = 1; i < len; i++) {
          var p1 = pointlist[i - 1];
          var p2 = pointlist[i];

          var lon = formatLonLat(p1.LON);
          var lat = formatLonLat(p1.LAT);

          var lon2 = formatLonLat(p2.LON);
          var lat2 = formatLonLat(p2.LAT);

          data.push({
            fromName: "",
            toName: "",
            coords: [
              [lon, lat],
              [lon2, lat2],
            ],
          });
        }

        series.push({
          name: "lines",
          type: "lines",
          coordinateSystem: "geo",
          zlevel: 1,
          effect: {
            show: false,
          },
          lineStyle: {
            normal: {
              color: "#ffffff",
              width: 2,
              curveness: 0,
            },
          },
          label: {
            normal: {
              show: false,
            },
          },
          silent: true,
          data: data,
        });
      } else {
        // 直线距离
        /*
                series.push({
                    name: 'lines',
                    type: 'lines',
                    coordinateSystem: 'geo',
                    zlevel: 1,
                    effect: {
                        show: false,
                    },
                    lineStyle: {
                        normal: {
                            color: '#ffffff',
                            width: 2,
                            curveness: 0
                        }
                    },
                    label: {
                        normal: {
                            show: false,
                        }
                    },
                    silent: true,
                    data: [{
                              fromName: flt.depCity,
                              toName: flt.arrCity,
                              coords: [[depAirport.longitude, depAirport.latitude], [arrAirport.longitude, arrAirport.latitude]]
                          }]
                });
                */
      }

      ////// 设置小飞机图标。 城市位置

      var symbol = "";

      if (flt.status == "DEP" && flt.delay1 == "") {
        symbol = "image://../img/a3.3.legend_1.png";
      } else {
        symbol = "image://../img/a3.3.legend_2.png"; // 延误航班
      }

      if (pointlist.length > 1) {
        var ac_lon = formatLonLat(pointlist[0].LON);
        var ac_lat = formatLonLat(pointlist[0].LAT);
        var ac_lon1 = formatLonLat(pointlist[1].LON);
        var ac_lat1 = formatLonLat(pointlist[1].LAT);
        var vec = getGeoAngle(ac_lat, ac_lon, ac_lat1, ac_lon1);
      } else {
        var ac_lon = dat.value[0];
        var ac_lat = dat.value[1];
        var vec = dat.vec;
      }

      series.push({
        name: "scatter",
        type: "scatter",
        coordinateSystem: "geo",
        zlevel: 2,

        data: [
          {
            name: flt.depCity,
            isAirline: true,
            isCity: true,
            cityType: "dep",
            value: [depAirport.longitude, depAirport.latitude],
            symbol: "circle",
            symbolSize: 7,
            itemStyle: {
              normal: {
                color: "#3d7dd0",
                borderColor: "#ffffff",
                borderWidth: 2,
              },
            },
            label: {
              normal: {
                show: true,
                formatter: "{b}",
                offset: [0, -16],
                textStyle: {
                  fontSize: 12,
                  color: "#FFFFFF",
                },
              },
            },
          },
          {
            name: flt.arrCity,
            isAirline: true,
            isCity: true,
            cityType: "arr",
            value: [arrAirport.longitude, arrAirport.latitude],
            symbol: "circle",
            symbolSize: 7,
            itemStyle: {
              normal: {
                color: "#3d7dd0",
                borderColor: "#ffffff",
                borderWidth: 2,
              },
            },
            label: {
              normal: {
                show: true,
                formatter: "{b}",
                offset: [0, -16],
                textStyle: {
                  fontSize: 12,
                  color: "#FFFFFF",
                },
              },
            },
          },
          {
            name: dat.name,
            acno: dat.acno,
            oil: dat.oil,
            flt: flt,
            isAirline: true,
            value: [ac_lon, ac_lat], //dat.value,
            symbol: symbol,
            symbolSize: 15,
            label: {
              normal: {
                show: false,
              },
            },
            symbolRotate: -vec + 90, //-dat.vec+90, //航向   正北是0°，顺时针，0到360° // 因为图片机头指向右，所以需要逆时针转90度
          },
        ],
      });

      var options = rangeObj.myChart.getOption();
      options.series = series;
      rangeObj.myChart.setOption(options, true);
    },
    error: function (jqXHR, txtStatus, errorThrown) {
      console.log("----error");
    },
  });

  // 加载 飞行轨迹
  /*
    var param = {
        "aircraftNos": dat.acno,
    }
    $.ajax({           
        type: 'post',
        url:"/bi/web/getTrackInfo",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            var pointlist = JSON.parse(response.data);
            var data = [];
            
            if(pointlist && pointlist.length > 1){
                var len = pointlist.length;
                for(var i=1; i<len; i++){
                  var p1 = pointlist[i-1];
                  var p2 = pointlist[i];
                  data.push({
                      fromName: '',
                      toName: '',
                      coords: [[p1.lon, p1.lat], [p2.lon, p2.lat]]
                  });
                }

                series.push({
                    name: 'lines',
                    type: 'lines',
                    coordinateSystem: 'geo',
                    zlevel: 1,
                    effect: {
                        show: false,
                    },
                    lineStyle: {
                        normal: {
                            color: '#ffffff',
                            width: 2,
                            curveness: 0
                        }
                    },
                    label: {
                        normal: {
                            show: false,
                        }
                    },
                    silent: true,
                    data: data
                });
                

            } else {

                series.push({
                    name: 'lines',
                    type: 'lines',
                    coordinateSystem: 'geo',
                    zlevel: 1,
                    effect: {
                        show: false,
                    },
                    lineStyle: {
                        normal: {
                            color: '#ffffff',
                            width: 2,
                            curveness: 0
                        }
                    },
                    label: {
                        normal: {
                            show: false,
                        }
                    },
                    silent: true,
                    data: [{
                              fromName: flt.depCity,
                              toName: flt.arrCity,
                              coords: [[depAirport.longitude, depAirport.latitude], [arrAirport.longitude, arrAirport.latitude]]
                          }]
                });

            }

            var options = rangeObj.myChart.getOption();
            options.series = series;
            rangeObj.myChart.setOption(options, true);
        }

    }); // 飞行轨迹 end
    */
}

// common.js 中调用
function main(complist, code2sort) {
  companylist = complist;
  companyCode2Sort = code2sort;
  createBMap();
}

// 用来从iframe外部调用的函数，切换公司
function onCompCodeChanged(compcode, compid) {
  company_code = compcode;
}

var mouseX, mouseY;
$(document).mousemove(function (e) {
  mouseX = e.pageX;
  mouseY = e.pageY;
});

// 截取小数位数
function trimDecimal(num, len) {
  var nnn = 1;
  for (var i = 0; i < len; i++) {
    nnn = nnn * 10;
  }
  return Math.round(num * nnn) / nnn;
}

// min ≤ r ≤ max
function randomNumRange(Min, Max) {
  var Range = Max - Min;
  var Rand = Math.random();
  var num = Min + Math.round(Rand * Range); //四舍五入
  return num;
}

// 获取参数
function getQueryString(name) {
  var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
  var r = window.location.search.substr(1).match(reg);
  if (r != null) return unescape(r[2]);
  return "";
}

function shuffle(arr) {
  var len = arr.length;
  for (var i = 0; i < len - 1; i++) {
    var idx = Math.floor(Math.random() * (len - i));
    var temp = arr[idx];
    arr[idx] = arr[len - i - 1];
    arr[len - i - 1] = temp;
  }
  return arr;
}

function trimTime(timestr) {
  var arr = timestr.split(" ");
  var arr2 = arr[1].split(":");
  return arr2[0] + ":" + arr2[1];
}

// 两组经纬度的距离 单位为km
function getDistance(lat1, lng1, lat2, lng2) {
  var radLat1 = (lat1 * Math.PI) / 180.0;
  var radLat2 = (lat2 * Math.PI) / 180.0;
  var a = radLat1 - radLat2;
  var b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180.0;
  var s =
    2 *
    Math.asin(
      Math.sqrt(
        Math.pow(Math.sin(a / 2), 2) +
          Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)
      )
    );
  s = s * 6378.137; // EARTH_RADIUS;
  s = Math.round(s * 10000) / 10000;
  return s;
}
