const myChart = echarts.init(document.getElementById('map'));
const comp_code = 'PN';
const comp_id = '100500';
const companyNodeId = 5732;
const comp_code_list = ['HU', '8L', 'HX', 'PN', 'GS', 'JD', 'FU', 'UQ', 'Y8', 'GX', '9H', 'GT'];
const statusMap = {
    'ARR': '落地',
    'NDR': '落地',
    'ATD': '推出',
    'ATA': '到达',
    'CNL': '取消',
    'DEL': '延误',
    'DEP': '起飞',
    'RTR': '返航',
    'SCH': '计划'
};
const weather_map = {
    '晴': 'icon-e600_sunny',
    '沙': 'icon-e617_dust1',
    '雹': 'icon-e620_hail',
    '雾': 'icon-e615_fog',
    '烟': 'icon-e615_fog',
    '阴': 'icon-e604_gloomy',
    '雷': 'icon-e606_rain2',
    '暴': 'icon-e606_rain2',
    '风': 'icon-e612_wind',
    '霾': 'icon-e613_haze',
    '云': 'icon-e602_cloudy',
    '雨': 'icon-e607_rain3',
    '雪': 'icon-e610_snow3'
};
const wxCode2Name = {
    'TS': '干雷',
    'TSRA': '中雷雨',
    '-TSRA': '弱雷雨',
    '+TSRA': '强雷雨',
    'CB': '对流云',
    'TCU': '浓积云',
    'RA': '中雨',
    '+RA': '大雨',
    '+SHRA': '强阵雨',
    'SHRA': '中阵雨',
    'DZ': '毛毛雨',
    'FZRA': '冻雨',
    'GR': '冰雹',
    'GS': '霰',
    'WS': '风切变',
    'FG': '大雾',
    'FU': '烟',
    'HZ': '霾',
    'BR': '轻雾',
    'FZFG': '冻雾',
    'BCFG': '散雾',
    'MIFG': '浅雾',
    'SN': '中雪',
    '+SN': '大雪',
    'SHSN': '阵雪',
    '+SHSN': '强阵雪',
    'BLSN': '高吹雪',
    'DRSA': '低吹雪',
    'SA': '扬沙',
    'SS': '沙暴',
    'BLSA': '高吹沙',
    'DRSA': '低吹沙',
    '+SS': '强沙暴',
    'DU': '浮尘',
};
let _scrollInterval;
let _flightNo = "";
let _cabin;
let _sortFlt;
let _flightInfoList;
let _flightInfoListObj;
let _company_kpi_data;
let _airportList;
let _ac_aircraft_list;
let _depAirport = ""; //出发机场信息
let _middleAirport = "";
let _arrAirport = ""; //到达机场信息
let _planeLocationList; //飞机实时位置
let _pointlist;//飞行轨迹
let _weather = {};
let _runaway_data = []; //跑道数据
let _option = {
    color: [],
    tooltip: {
        trigger: 'item',
        show: false,
    },
    geo: {
        map: 'world',
        roam: true,
        zoom: 2.7,
        center: [97, 28],
        silent: true,
        label: {
            emphasis: {
                show: false
            }
        },
        itemStyle: {
            normal: {
                areaColor: '#3d7dd0',
                borderColor: '#14224c'
            },
            emphasis: {
                areaColor: '#3d7dd0',
                borderColor: '#14224c'
            }
        },
        //regions:countries
    },
    backgroundColor: '#14224c',
    series: []
};
class Flight {
    constructor(){
        this._initMap();
        this._initAcAircraftList();
    }
    loadAll() {
        _flightNo = this._getQueryString('fltno') || this._getQueryString('flightNo') || this._getQueryString('flight');
        $('.fltno').text(_flightNo);
        this.getStandardFocFlightInfo();
        this.getKpi();
        this.getPsr();
        this.getCrew();
        $.when(this.getPlanePos(),this.getPlaneTrack()).done(this.setMapData());
    }
    getStandardFocFlightInfo () {
        // 开始结束时间
        let date = new Date();
        let mm = date.getMonth() + 1;
        let dd = date.getDate();
        if (mm < 10) {
            mm = '0' + mm;
        }
        if (dd < 10) {
            dd = '0' + dd;
        }
        let stdStart = date.getFullYear() + '-' + mm + '-' + dd + ' 00:00:00';
        let stdEnd = date.getFullYear() + '-' + mm + '-' + dd + ' 23:59:59';

        let next_ts = date.getTime() + 86400000 * 7;
        date.setTime(next_ts);
        mm = date.getMonth() + 1;
        dd = date.getDate();
        if (mm < 10) {
            mm = '0' + mm;
        }
        if (dd < 10) {
            dd = '0' + dd;
        }
        let stdEnd7 = date.getFullYear() + '-' + mm + '-' + dd + ' 23:59:59';
        let param = {
            "stdStart": stdStart,
            "stdEnd": stdEnd,
            "acOwner": comp_code,
            "statusList": '',
        };
        let getinfo = $.ajax({
            type: 'post',
            url: "/bi/web/getStandardFocFlightInfo",
            contentType: 'application/json',
            dataType: 'json',
            async: false,
            data: JSON.stringify(param),
            success: function (response) {
                let list = response.data;
                console.log("******");
                console.log(response.data);
                _flightInfoList = {};
                _flightInfoListObj = {};
                for (let i = list.length - 1; i >= 0; i--) {
                    const obj = list[i];
                    _flightInfoList[obj.flightNo] = obj;
                    if (_flightInfoListObj[obj.flightNo] == undefined) {
                        _flightInfoListObj[obj.flightNo] = [];
                        _flightInfoListObj[obj.flightNo].push(obj);
                    } else {
                        _flightInfoListObj[obj.flightNo].push(obj);
                    }

                    if (obj.flightNo == _flightNo) {
                        console.log(obj)
                    }
                }
                const flt = _flightInfoListObj[_flightNo];
                if (flt == undefined) {
                    alert('没有查询到航班信息');
                    return;
                }
                console.log(_flightNo, flt);
                if (flt.length > 1) {
                    const dep1 = flt[0].depCity,
                        arr1 = flt[0].arrCity,
                        dep2 = flt[1].depCity,
                        arr2 = flt[1].arrCity;
                    if (arr1 == dep2) {
                        _sortFlt = flt;
                    } else if (arr2 == dep1) {
                        _sortFlt = flt.reverse();
                    }
                } else {
                    _sortFlt = flt;
                }
            }.bind(this)
        });
        $.when(getinfo).done(()=>{
            this.getCabin(_sortFlt[0].acLongNo);
            $.when(this._initAirportList()).done(function (){
                this.setFltInfo(_sortFlt);
                this.getPlaneInfo(_sortFlt[0].acLongNo,stdStart,stdEnd7);
            }.bind(this));
        })
    }
    // ------------------------------------------------------------------------
    // 获取 座舱布局
    // ------------------------------------------------------------------------
    getKpi(){
        let param = {
            'SOLR_CODE': 'FAC_COMP_KPI',
            'COMP_CODE': comp_code,
            'KPI_CODE': 'TRV_RATE,SCH_NO,NORMAL_NO_T',
            'VALUE_TYPE': 'kpi_value_d',
            "OPTIMIZE": 1,
            'DATE_TYPE': 'M',
            'LIMIT': 1
        };

        $.ajax({
            type: 'post',
            url: "/bi/query/getkpi",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function(response) {
                if (response.data != undefined) {
                    _company_kpi_data = response.data;
                }

            }.bind(this),
            error: function() {

            }
        });
    }
    getCabin(acno) {
        let param = {
            "acNo": acno,
        };
        $.ajax({
            type: 'post',
            url: "/bi/web/getAcAircraftList",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function(response) {
                let ac = response.data[0].data;
                _cabin = ac.cabin; // 座舱布局
                let acType = ac.acType; // 机型
                let mtwKg = ac.mtwKg; //最大起飞重量KG
                let dewKg = ac.dewKg; //空机重量KG
                // 座舱布局图
                _cabin = _cabin.toUpperCase();
                $('.cabin').removeClass('CY');
                $('.cabin').removeClass('Y');
                $('.cabin').removeClass('w_CY');
                $('.cabin').removeClass('W_Y');
                if (acType.indexOf('787') > -1 || acType.indexOf('767') > -1 || acType.indexOf('777') > -1 || acType.indexOf('330') > -1) {
                    if (_cabin.indexOf('C') > -1 && _cabin.indexOf('Y') > -1) {
                        $('.cabin').addClass('W_CY');
                    } else {
                        $('.cabin').addClass('W_Y');
                    }
                } else {
                    if (_cabin.indexOf('C') > -1 && _cabin.indexOf('Y') > -1) {
                        $('.cabin').addClass('CY');
                    } else {
                        $('.cabin').addClass('Y');
                    }
                }
                $('.cabin .lb').text(_cabin);
                $('.actype').text(ac.acCrewType);
                $('.dewKg').text(ac.dewKg);
                $('.mtwKg').text(ac.mtwKg);
                $('.oil_2').text(ac.mfLb);
            }.bind(this),
            error: function() {}
        });
    }
    // ------------------------------------------------------------------------
    // 获取 机组信息
    // ------------------------------------------------------------------------
    getCrew(){
        let param = {
            "flightNo": _flightNo,
        };
        $.ajax({
            type: 'post',
            url: "/bi/web/findFlightReportV2",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function(response) {
                if (response.data && response.data[0]) {
                    let data = response.data[0];
                    // 机长
                    let names = data.captain.replace(/\d+/g, '').split('@');
                    for (let i = names.length - 1; i >= 0; i--) {
                        if (names[i].indexOf('(') > -1) {
                            let aaa = names[i].split('(');
                            if (aaa.length > 1) {
                                names[i] = aaa[0];
                            }
                        }
                    }
                    $('.captain').text(names.join(','));

                    // 副驾
                    if (data.firstVice1) {
                        let names = data.firstVice1.replace(/\d+/g, '').split('@'); // 删除数字
                        for (let i = names.length - 1; i >= 0; i--) {
                            if (names[i].indexOf('(') > -1) {
                                let aaa = names[i].split('(');
                                if (aaa.length > 1) {
                                    names[i] = aaa[0];
                                }
                            }
                        }
                        $('.firstVice1').text(names.join(','));
                    }
                    // 乘务员
                    let crew = data.crwStewardInf;
                    if (crew) {
                        crew = crew.replace(/\d+/g, ''); // 删除数字
                        let arr = crew.split('；');
                        let names = [];
                        for (let i in arr) {
                            let t = arr[i];
                            let n = t.split(':')[0];
                            names.push(n);
                        }
                        let steward = names.splice(0, 6);
                        let steward_all = names;
                        $('.crwStewardInf').text(steward.join(',')); //乘务员
                    }

                    // 安全员
                    if (data.safer1) {
                        let crew = data.safer1.replace(/\d+/g, ''); // 删除数字
                        let safer = crew.split('/');
                        safer = safer.splice(0, 2);
                        $('.safer1').text(safer.join(',')); //安全员
                    }


                }
            },
            error: function() {}
        });
    }
    // ------------------------------------------------------------------------
    // 获取 乘客信息
    // ------------------------------------------------------------------------
    getPsr(){
        let param = {
            "flightNo": _flightNo,
        };
        $.ajax({
            type: 'post',
            url: "/bi/web/findPsrStat",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function(response) {
                let psr = response.data[0];
                if (!psr) {
                    this._initRoundChart2(0);
                    $('#cvs_chart2_lb1').text('--');
                    return;
                }
                let ckiNum = psr.ckiNum; // 值机人数
                let chdNum = !isNaN(psr.chdNum) ? Number(psr.ckiChdNum) : 0;
                let eldNum = !isNaN(psr.eldNum) ? Number(psr.ckiEldNum) : 0;
                let infNum = !isNaN(psr.infNum) ? Number(psr.ckiInfNum) : 0;
                let vipNum = !isNaN(psr.vipNum) ? Number(psr.ckiVipNum) : 0;
                let cipNum = !isNaN(psr.cipNum) ? Number(psr.ckiCipNum) : 0;
                let ckiInNum = !isNaN(psr.ckiInNum) ? Number(psr.ckiInNum) : 0;
                let ckiOutNum = !isNaN(psr.ckiOutNum) ? Number(psr.ckiOutNum) : 0;
                let cNum = !isNaN(psr.cNum) ? Number(psr.ckiCNum) : 0;
                let yNum = !isNaN(psr.yNum) ? Number(psr.ckiYNum) : 0;
                $('.adultNum').text(ckiNum); //总数
                $('.chdNum').text(chdNum); //儿童
                $('.eldNum').text(eldNum); //老人
                $('.infNum').text(infNum); //婴儿
                $('.vipNumCipNum').text(vipNum + cipNum);
                $('.cNum').text(cNum);
                $('.yNum').text(yNum);
                $('.zz_psr').text(ckiInNum + ckiOutNum);
                $('.ckiJpVNum').text(!isNaN(psr.ckiJpVNum) ? Number(psr.ckiJpVNum) : 0);
                $('.ckiJpGoldenNum').text(!isNaN(psr.ckiJpGoldenNum) ? Number(psr.ckiJpGoldenNum) : 0);
                $('.ckiJpSilverNum').text(!isNaN(psr.ckiJpSilverNum) ? Number(psr.ckiJpSilverNum) : 0);
                let setTrvRate = () => {
                    if (_cabin == undefined || _company_kpi_data == undefined) {
                        setTimeout(setTrvRate, 0);
                        return;
                    }

                    // cabin 座舱布局 C6Y170
                    let arr = _cabin.split('Y');
                    let seat1 = !isNaN(arr[1]) ? arr[1] : 0;
                    let arr2 = arr[0].split('C');
                    let seat2 = !isNaN(arr2[1]) ? arr2[1] : 0;
                    let seat = Number(seat1) + Number(seat2);

                    // 客座率
                    $('#cvs_chart2_lb1').removeClass('red');
                    $('#cvs_chart2_lb1').removeClass('green');
                    $('.trv_rate .down_arr').hide();
                    $('.trv_rate .up_arr').hide();
                    let psr_rate = seat > 0 ? Math.round(ckiNum / seat * 1000) / 10 : '-';
                    if (psr_rate > 100) {
                        psr_rate = 100;
                    }
                    if (isNaN(psr_rate)) {
                        psr_rate = 0;
                        this._initRoundChart2(0);
                        $('#cvs_chart2_lb1').text('--');
                    } else {
                        this._initRoundChart2(psr_rate / 100);
                        $('#cvs_chart2_lb1').text(psr_rate + '%');
                    }
                    let avg_rate = 0;
                    let ddd = _company_kpi_data[comp_code]['TRV_RATE']['M']
                    for (let d in ddd) {
                        avg_rate = Number(ddd[d]) * 100;
                    }
                    if (avg_rate > psr_rate) {
                        $('#cvs_chart2_lb1').addClass('red');
                        $('.trv_rate .down_arr').show();
                        $('.trv_rate_sub').text('低于航司平均');
                    } else {
                        $('#cvs_chart2_lb1').addClass('green');
                        $('.trv_rate .up_arr').show();
                        $('.trv_rate_sub').text('高于航司平均');
                    }
                };
                setTrvRate();
            }.bind(this),
            error: function() {}
        });
    }
    getPlaneInfo(acLongNo,stdStart,stdEnd7){
        let param = {
            "flightNo": _flightNo,
            "flightDate": stdStart.split(' ')[0],
        };
        $.ajax({
            type: 'post',
            url: "/bi/web/getFltLoadSheetInfo",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function(response) {
                let ac = response.date;
                if (ac) {
                    $('.oil_1').text(ac.takeOffFuel);
                    $('.oil_3').text(ac.tripFuel);
                    $('.takeOffWeight').text(ac.takeOffWeight);
                }


            },
            error: function() {}
        });
        let maintInfoList;
        let maintInfoIndex = 0;
        param = {
            "startDate": stdStart,
            "endDate": stdEnd7,
            "acLongNo": acLongNo,
        };
        $.ajax({
            type: 'post',
            url: "/bi/web/getFocMaintInfoByList",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function(response) {
                maintInfoList = response.data;
                if (maintInfoList && maintInfoList.length > 0) {
                    setMaintPage();
                }

            }.bind(this),
            error: function() {}
        });
        let setMaintPage = () => {
            $('.col_r .btn_prev').removeClass('disabled');
            $('.col_r .btn_next').removeClass('disabled');
            if (maintInfoIndex == 0) {
                $('.col_r .btn_prev').addClass('disabled');
            }
            if (maintInfoIndex == maintInfoList.length - 1) {
                $('.col_r .btn_next').addClass('disabled');
            }
            let info = maintInfoList[maintInfoIndex];
            let airport = _airportList[info.stn];
            $('.mntstn').text(airport.chn_name);
            $('.seq').text(info.seq);
            $('.mntComment').text(info.mntComment);
            $('.mntComment').attr('title', info.mntComment);
            let tend = info.tEnd;
            tend = tend.slice(0, tend.lastIndexOf(':'));
            $('.mnttEnd').text(tend);
        };
        $('.col_r .btn_prev').on('click', function(evt) {
            if (maintInfoIndex > 0) {
                maintInfoIndex--;
                setMaintPage();
            }
        });
        $('.col_r .btn_next').on('click', function(evt) {
            if (maintInfoIndex < maintInfoList.length - 1) {
                maintInfoIndex++;
                setMaintPage();
            }
        });
    }
    // ------------------------------------------------------------------------
    // 获取飞机实时位置
    // ------------------------------------------------------------------------
    getPlanePos(){
        let param = {
            'mode': 'pos'
        };
        $.ajax({
            type: 'post',
            url: "/bi/web/flightMq",
            contentType: 'application/json',
            dataType: 'json',
            async: false,
            data: JSON.stringify(param),
            success: function(response) {
                _planeLocationList = [];
                let plist = {};
                let processData = (data1) => {
                    let lst = {};
                    let len = data1.length;
                    for (var i = 0; i < len; i++) {
                        var dd = data1[i];
                        var fi = dd.fi;
                        if (lst[fi] == undefined) {
                            lst[fi] = {
                                data: []
                            };
                            lst[fi]['data'].push(dd);
                        } else {
                            lst[fi]['data'].push(dd);
                        }
                    }

                    return lst;
                };
                let list = processData(response.data.data1);
                let fltobj = list[_flightNo];
                console.log(_flightNo, fltobj);
                if (fltobj) {
                    let itmx2 = fltobj.data;
                    let itm;
                    if (itmx2 && itmx2.length > 1) {
                        let itm1 = itmx2[0];
                        let itm2 = itmx2[1];
                        itm1.UTC = itm1.UTC.replace(' ', '');
                        itm2.UTC = itm2.UTC.replace(' ', '');
                        if (itm1.UTC > itm2.UTC) {
                            itm = itm1;
                            itm.LON1 = itm2.LON;
                            itm.LAT1 = itm2.LAT;
                        } else if (itm1.UTC < itm2.UTC) {
                            itm = itm2;
                            itm.LON1 = itm1.LON;
                            itm.LAT1 = itm1.LAT;
                        } else {
                            itm = itm2;
                            itm.LON1 = itm1.LON;
                            itm.LAT1 = itm1.LAT;
                        }
                    } else if (itmx2 && itmx2.length > 0) {
                        itm = itmx2[0];

                    }
                    if (itm) {
                        let alt = itm.ALT;
                        let cas = itm.CAS;
                        let vec;
                        let fltno = itm.fi;
                        let acno = itm.an;
                        acno = acno.replace('-', '');
                        let lon = formatLonLat(itm.LON);
                        let lon1 = formatLonLat(itm.LON1);
                        let lat = formatLonLat(itm.LAT);
                        let lat1 = formatLonLat(itm.LAT1);
                        if (isNaN(itm.LON)) {
                            vec = Number(itm.VEC);
                        }
                        let oil = isNaN(itm.OIL) ? '' : itm.OIL;
                        let pdat = {
                            fltno: fltno,
                            acno: acno,
                            alt: alt,
                            vec: vec,
                            lon: lon,
                            lat: lat,
                            lon1: lon1,
                            lat1: lat1,
                            oil: oil,
                        };
                        let code = acno + '-' + fltno;
                        if (pdat.vec == undefined) {
                            pdat.vec = getGeoAngle(pdat.lat, pdat.lon, pdat.lat1, pdat.lon1);
                        }
                        _planeLocationList.push(pdat);
                    }
                }
                console.log('planeLocationList', _planeLocationList);

            }.bind(this),
            error: function(jqXHR, txtStatus, errorThrown) {

            }
        });

    }

    // ------------------------------------------------------------------------
    // 获取飞机航线
    // ------------------------------------------------------------------------
    getPlaneTrack(){
        let param = {
            'mode': 'track',
            'fi': _flightNo,
        };
        $.ajax({
            type: 'post',
            url: "/bi/web/flightMq",
            contentType: 'application/json',
            dataType: 'json',
            async: false,
            data: JSON.stringify(param),
            success: function(response) {
                _pointlist = response.data;
            }.bind(this),
            error: function(jqXHR, txtStatus, errorThrown) {
                console.log('----error');
            }
        });
    }
    // ------------------------------------------------------------------------
    // 获取天气信息
    // ------------------------------------------------------------------------
    getWeather(flt){
        // 到达机场
        let param = {
            'airport': flt.arrStn // 到达机场三字码
        };

        $.ajax({
            type: 'post',
            url: "/bi/web/7x2_arp_weather",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function(response) {
                if (Number(response.errorcode) == 0) {
                    _weather[flt.arrCity] = response;
                    this.setWeather(flt.arrCity + flt.arrStnCn + '机场', 2, response);
                }

            }.bind(this),
            error: function(jqXHR, txtStatus, errorThrown) {}
        });
        // 出发机场
        param = {
            'airport': flt.depStn // 出发机场三字码
        }
        $.ajax({
            type: 'post',
            url: "/bi/web/7x2_arp_weather",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function(response) {
                if (Number(response.errorcode) == 0) {
                    _weather[flt.depCity] = response;
                    this.setWeather(flt.depCity + flt.depStnCn + '机场', 1, response);
                }
            }.bind(this),
            error: function(jqXHR, txtStatus, errorThrown) {}
        });

        var date = new Date();
        var ts = date.getTime() - 3600 * 8 * 1000;
        date.setTime(ts);
        var mm = date.getMonth() + 1;
        var dd = date.getDate();
        var hour = date.getHours();
        var min = date.getMinutes();
        var sec = date.getSeconds();
        if (mm < 10) {
            mm = '0' + mm;
        }
        if (dd < 10) {
            dd = '0' + dd;
        }
        if (hour < 10) {
            hour = '0' + hour;
        }
        if (min < 10) {
            min = '0' + min;
        }
        if (sec < 10) {
            sec = '0' + sec;
        }
        var awosTimeEnd = date.getFullYear() + '-' + mm + '-' + dd + ' ' + hour + ':' + min + ':' + sec;
        var ts = date.getTime() - 60 * 10 * 1000; //最新10分钟
        date.setTime(ts);
        mm = date.getMonth() + 1;
        dd = date.getDate();
        var hour = date.getHours();
        var min = date.getMinutes();
        var sec = date.getSeconds();
        if (mm < 10) {
            mm = '0' + mm;
        }
        if (dd < 10) {
            dd = '0' + dd;
        }
        if (hour < 10) {
            hour = '0' + hour;
        }
        if (min < 10) {
            min = '0' + min;
        }
        if (sec < 10) {
            sec = '0' + sec;
        }
        var awosTimeBegin = date.getFullYear() + '-' + mm + '-' + dd + ' ' + hour + ':' + min + ':' + sec;
        param = {
            'airport': flt.arrStn, // 到达机场三字码
            'awosTimeBegin': awosTimeBegin, // UTC时间 yyyy-MM-dd HH:mm:ss
            'awosTimeEnd': awosTimeEnd, // UTC时间 yyyy-MM-dd HH:mm:ss
        };
        $.ajax({
            type: 'post',
            url: "/bi/web/runway_weather",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function(response) {
                if (response.data && response.data.length) {
                    _runaway_data = response.data;
                    let i = 0;
                    if (_scrollInterval) clearInterval(_scrollInterval);
                    _scrollInterval = setInterval(()=>{i = this._scrollRunawayWeathar(i)}, 5000);
                }
            }.bind(this),
            error: function(jqXHR, txtStatus, errorThrown) {

            }
        });
    }
    // ------------------------------------------------------------------------
    // 获取特殊报
    // ------------------------------------------------------------------------
    getWfMetrepBiaoZhuns(flt){
        let date = new Date();
        let mm = date.getMonth() + 1;
        let dd = date.getDate();
        if (mm < 10) {
            mm = '0' + mm;
        }
        if (dd < 10) {
            dd = '0' + dd;
        }
        let stdStart = date.getFullYear() + '-' + mm + '-' + dd + ' 00:00:00';
        let stdEnd = date.getFullYear() + '-' + mm + '-' + dd + ' 23:59:59';
        var param = {
            "cccsList": flt.depStnFourCode,
            "updateDateStart": stdStart,
            "updateDateEnd": stdEnd,
        }
        $.ajax({
            type: 'post',
            url: "/bi/web/findWfMetrepBiaoZhuns",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function(response) {
                for (let k in response) {
                    let obj = response[k];
                    if (obj.ivisAlarmValue == 2 || obj.irvrAlarmValue == 2
                        || obj.wxCode == "2" || obj.iyunalarmValue == 2 || obj.ittAlarmValue == 2
                        || obj.iwindSwitchAlarmValue == 2) { //1 表示黄色; 2 表示红色
                        let cont = wxCode2Name[obj.wx] ? wxCode2Name[obj.wx] : obj.wx;
                        $('.weather_city1 .tsb').text(cont);
                    }
                }

            }.bind(this),
            error: function() {}
        });

        param = {
            "cccsList": flt.arrStnFourCode,
            "updateDateStart": stdStart,
            "updateDateEnd": stdEnd,
        }

        $.ajax({
            type: 'post',
            url: "/bi/web/findWfMetrepBiaoZhuns",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function(response) {
                for (let k in response) {
                    let obj = response[k];
                    if (obj.ivisAlarmValue == 2 || obj.irvrAlarmValue == 2
                        || obj.wxCode == "2" || obj.iyunalarmValue == 2 || obj.ittAlarmValue == 2
                        || obj.iwindSwitchAlarmValue == 2) { //1 表示黄色; 2 表示红色
                        let cont = wxCode2Name[obj.wx] ? wxCode2Name[obj.wx] : obj.wx;
                        $('.weather_city2 .tsb').text(cont);
                    }
                }

            }.bind(this),
            error: function() {}
        });
    }

    // ------------------------------------------------------------------------
    // 设置天气信息
    // ------------------------------------------------------------------------
    setWeather(plane_name, id, response){
        console.log(response);
        let weatherInfoTxt = response.weatherInfoTxt? response.weatherInfoTxt.replace(/<[^>]+>/g, "") : "--";
        let weatherInfo = response.weatherInfo ? response.weatherInfo : "--"; //天气现象
        let temperature = response.temperature ? isNaN(response.temperature) ? '-' : Number(response.temperature) + '℃' : "--";
        let visibility = response.visibility ? isNaN(response.visibility) || response.visibility == 0 ? 9999 : Number(response.visibility) : "--"; //能见度
        let windFx = response.windFx ? response.windFx : "--"; //风向
        let windFs = response.windFs ? isNaN(response.windFs) ? 0 : Number(response.windFs) : "--"; //风速
        let cloudInfoTxt = response.cloudInfoTxt? response.cloudInfoTxt.replace(/&nbsp;/g,"") : "--";

        let weather_css = 'icon-e600_sunny';
        for (let wtxt in weather_map) {
            if (weatherInfoTxt.indexOf(wtxt) > -1) {
                weather_css = weather_map[wtxt];
            }
        }

        $('.weather_city' + id + ' .plane').text(plane_name);
        let planewidth = $('.weather_city' + id + ' .plane').width();
        let weatherwidth = $('.weather_city' + id).width();
        $('.weather_city' + id + ' .line').css("width", 98 - ((planewidth/weatherwidth) * 100) + "%");
        // 设置天气状况icon
        $('.weather_city' + id + ' .weather_ico span').attr('class', weather_css);
        $('.weather_city' + id + ' .temperature').text(temperature);
        $('.weather_city' + id + ' .condition').text(weatherInfoTxt);
        if (id == 2) $("#weatherInfo").text(weatherInfoTxt);
        $('.weather_city' + id + ' .visibility').text(visibility + 'm');
        $('.weather_city' + id + ' .windFs').text(windFs + 'km/h');
        $('.weather_city' + id + ' .windFx').text(windFx + "°");
        $('.weather_city' + id + ' .cloudInfo').text(cloudInfoTxt.substring(0, cloudInfoTxt.indexOf("云")+1));
    }
    // ------------------------------------------------------------------------
    // 设置顶部信息
    // ------------------------------------------------------------------------
    setFltInfo(flt){
        if (_airportList == undefined) {
            return;
        }
        if (flt.length > 1) {
            $('.city1').text(flt[0].depCity);
            $('.citym').text(flt[0].arrCity);
            $('.city2').text(flt[1].arrCity);

            _depAirport = _airportList[flt[0].depStn]; //出发机场信息
            _middleAirport = _airportList[flt[0].arrStn] // 中转机场信息
            _arrAirport = _airportList[flt[1].arrStn]; //到达机场信息
            $('.arp1').text(flt[0].depStnCn);
            $('.arpm').text(flt[0].arrStnCn);
            $('.arp2').text(flt[1].arrStnCn);
            $('.fltsts').removeClass('status1');
            $('.fltsts').removeClass('status1');
            // 航段一
            if (flt[0].status == "ATA") {
                $(".leg1").addClass('flightStatus3');
                $('.leg1Status').text(statusMap[flt[0].status]);
                // $('.fltsts').hide();
                this.getWeather(flt[1]);
                this.getWfMetrepBiaoZhuns(flt[1]);
            } else if (flt[0].delay1 != '' && flt[0].dur1 > 0) {
                $('.leg1').addClass('flightStatus2');
                $('.leg1Status').text('晚点');
                // $('.fltsts').hide();
                $('.fltsts').addClass('status2');
                $('.fltsts').text('晚点');
                this.getWeather(flt[0]);
                this.getWfMetrepBiaoZhuns(flt[0]);
            } else if (flt[0].status != "ATA") {
                $('.fltsts').addClass('status1');
                // $('.fltsts').hide();
                flt[0].status == "SCH" ? $('.leg1').addClass('flightStatus3') : $('.leg1').addClass('flightStatus1');
                $('.leg1Status').text(statusMap[flt[0].status]);
                $('.fltsts').text(statusMap[flt[0].status]);
                this.getWeather(flt[0]);
                this.getWfMetrepBiaoZhuns(flt[0]);
            }
            // 航段二
            if (flt[1].status == "ATA") {
                $(".leg2").addClass('flightStatus3');
                $('.leg2Status').text(statusMap[flt[1].status]);
                $('.fltsts').hide();
            } else if (flt[1].delay1 != '' && flt[1].dur1 > 0) {
                $('.leg2').addClass('flightStatus2');
                $('.leg2Status').text('晚点');
                // $('.fltsts').hide();
                if (flt[0].status == "ATA") {
                    $('.fltsts').addClass('status2');
                    $('.fltsts').text('晚点');
                }
            } else if (flt[1].status != "ATA") {
                flt[1].status == "SCH" ? $('.leg2').addClass('flightStatus3') : $('.leg2').addClass('flightStatus1');
                $('.leg2Status').text(statusMap[flt[1].status]);
                // $('.fltsts').hide();
                if (flt[0].status == "ATA") {
                    $('.fltsts').addClass('status1');
                    $('.fltsts').text(statusMap[flt[1].status]);
                }
            }
            let etdChn = flt[0].etdChn; //预计起飞时间（北京时间）
            let atdChn = flt[0].atdChn; //实际起飞时间（北京时间）
            let etaChn = flt[1].etaChn; //预计到达时间（北京时间）
            let ataChn = flt[1].ataChn; //实际到达时间（北京时间）
            let stdChn = flt[0].stdChn; //计划出发
            let staChnm = flt[0].staChn; // 中转计划到达
            let atdChnm = flt[1].atdChn; // 中转实际出发
            let ataChnm = flt[0].ataChn; // 中转实际到达
            let stdChn1 = flt[1].stdChn; // 中转计划起飞
            let staChn = flt[1].staChn; // 中转计划到达
            $('.atdChn').text(this._trimTime(atdChn));
            $('.etdChn').text("--");
            $('.etdChn').closest(".flex_1").find(".t2").text("");
            $('.ataChn').text(this._trimTime(ataChn));
            $('.stdChn').text(this._trimTime(stdChn));
            $('.staChn').text("--");
            $('.staChn').closest(".flex_1").find(".t2").text("");
            $('.ataChnm').text(this._trimTime(ataChnm));
            $('.staChnm').text(this._trimTime(staChnm));
            $('.stdChnm').text(this._trimTime(stdChn1));
            $('.staChnmm').text(this._trimTime(staChn));


            console.log("flightInfoList", _flightInfoList);
            // 查找前序航班
            let prev_flt;
            if (flt[0].status == "ATA") {
                prev_flt = flt[0];
            } else {
                for (let fltno in _flightInfoListObj) {
                    for (let i = 0; i < _flightInfoListObj[fltno].length ; i++) {
                        let ff = _flightInfoListObj[fltno][i]
                        if (ff.acLongNo != '' && ff.acLongNo == flt[0].acLongNo && ff.arrStn == flt[0].depStn && ff.stdChn < flt[0].stdChn) {
                            prev_flt = ff;
                            break;
                        }
                    }
                }
            }
            if (prev_flt) {
                $('.preflight').text(prev_flt.flightNo + ' ' + prev_flt.depCity + ' ' + prev_flt.arrCity);
                let blk2width = $(".col_l .blk2").width();
                let blk2c1width = $(".col_l .blk2 .row1 .c1").width();
                $(".col_l .blk2 .row1 .c2").css("width", 96-((blk2c1width/blk2width)*100) + "%");
                $('.preflight_status').text(statusMap[prev_flt.status]);
                $('.preflight_arrive_time').text(this._dateFormat(prev_flt.ataChn));
            } else {
                $('.preflight').text('--');
            }
            // 航班日期
            $('.flt_time').text(this._dateFormat(flt[0].datopChn));
            // 航线里程
            $('.ariline_dis').text('');
            // 已执行
            $('.ariline_dis2').text('');
            // 飞行时长
            let d_time1 = parserDate(flt[0].etdChn);
            let a_time1 = parserDate(flt[0].etaChn);
            let atdChn1 = parserDate(flt[0].atdChn);//第一段实际起飞时间
            let ataChn1 = parserDate(flt[0].ataChn);//第一段实际到达时间

            let d_time2 = parserDate(flt[1].etdChn);
            let a_time2 = parserDate(flt[1].etaChn);
            let atdChn2 = parserDate(flt[1].atdChn);//第二段实际起飞时间
            let ataChn2 = parserDate(flt[1].ataChn);//第二段实际到达时间

            let msec = (a_time1.getTime() - d_time1.getTime()) + (a_time2.getTime() - d_time2.getTime());
            let min = Math.round(msec / (60 * 1000));
            let tm = Math.floor(min / 60) + '小时';
            if (min % 60 > 0) {
                tm = tm + min % 60 + '分';
            }
            $('.fly_time').text(tm);
            if (flt[0].status == "ATA" || flt[0].status == "ARR" || flt[0].status == "DNR") {
                if (flt[1].status == "DEP" || flt[1].status == "ATD") {
                    $('.ariline_min').text(Math.round(((new Date() > atdChn2 ? new Date() - atdChn2 : 0) + (ataChn1 - atdChn1)) / (60 * 1000)));
                } else if (flt[1].status == "ATA" || flt[1].status == "ARR" || flt[1].status == "DNR") {
                    $('.ariline_min').text(Math.round(((ataChn2 - atdChn2) + (ataChn1 - atdChn1)) / (60 * 1000)));
                } else {
                    $('.ariline_min').text(Math.round((ataChn1 - atdChn1) / (60 * 1000)));
                }
            } else if (flt[0].status == "DEP" || flt[0].status == "ATD") {
                 $('.ariline_min').text(Math.round((new Date() > atdChn1 ? new Date() - atdChn1 : 0) / (60 * 1000)));
            }
            $('.acno').text(flt[0].acLongNo);
        }
        if (flt.length != 2) {
            $('.city1').text(flt[0].depCity);
            $('.citym').hide();
            $('.city2').text(flt[0].arrCity);
            _depAirport = _airportList[flt[0].depStn]; //出发机场信息
            // middleAirport = airportList[flt[0].arrStn] // 中转机场信息
            _arrAirport = _airportList[flt[0].arrStn]; //到达机场信息
            $('.arp1').text(flt[0].depStnCn);
            $('.arpm').hide();
            $('.arp2').text(flt[0].arrStnCn);
            $('.fltsts').removeClass('status1');
            $('.fltsts').removeClass('status1');
            $(".leg1").hide();
            $(".leg2").hide();
            $(".jingting").hide();
            // 航段一
            if (flt[0].delay1 != '' && flt[0].dur1 > 0) {
                $('.fltsts').addClass('status2');
                $('.fltsts').text('晚点');
            } else {
                $('.fltsts').addClass('status1');
                $('.fltsts').text(statusMap[flt[0].status]);
            }
            let etdChn = flt[0].etdChn; //预计起飞时间（北京时间）
            let atdChn = flt[0].atdChn; //实际起飞时间（北京时间）
            let etaChn = flt[0].etaChn; //预计到达时间（北京时间）
            let ataChn = flt[0].ataChn; //实际到达时间（北京时间）
            let stdChn = flt[0].stdChn; //计划出发
            let staChn = flt[0].staChn; //计划到达
            $('.atdChn').text(this._trimTime(atdChn));
            $('.etdChn').text(this._trimTime(etdChn));
            $('.ataChn').text(this._trimTime(ataChn));
            $('.etaChn').text(this._trimTime(etaChn));
            $('.etaChn').closest(".flex_1").find(".t2").text("预计落地");
            $('.stdChn').text(this._trimTime(stdChn));
            $('.staChn').text(this._trimTime(staChn));
            $('.ataChnm').hide();
            $('.staChnm').hide();
            $('.atdChnm').hide();
            // 查找前序航班
            let prev_flt;
            for (let fltno in _flightInfoListObj) {
                for (let i = 0; i < _flightInfoListObj[fltno].length ; i++) {
                    let ff = _flightInfoListObj[fltno][i]
                    if (ff.acLongNo != '' && ff.acLongNo == flt[0].acLongNo && ff.arrStn == flt[0].depStn && ff.stdChn < flt[0].stdChn) {
                        prev_flt = ff;
                        break;
                    }
                }
            }
            if (prev_flt) {
                $('.preflight').text(prev_flt.flightNo + ' ' + prev_flt.depCity + ' ' + prev_flt.arrCity);
                let blk2width = $(".col_l .blk2").width();
                let blk2c1width = $(".col_l .blk2 .row1 .c1").width();
                $(".col_l .blk2 .row1 .c2").css("width", 96-((blk2c1width/blk2width)*100) + "%");
                $('.preflight_status').text(statusMap[prev_flt.status]);
                $('.preflight_arrive_time').text(this._dateFormat(prev_flt.ataChn));
            } else {
                $('.preflight').text('--');
            }
            // 航班日期
            $('.flt_time').text(this._dateFormat(flt[0].datopChn));
            // 航线里程
            $('.ariline_dis').text('');
            // 已执行
            $('.ariline_dis2').text('');
            // 飞行时长
            let d_time = parserDate(etdChn);
            let a_time = parserDate(etaChn);
            let atdChn1 = parserDate(flt[0].atdChn);//第一段实际起飞时间
            let ataChn1 = parserDate(flt[0].ataChn);//第一段实际到达时间
            let msec = a_time.getTime() - d_time.getTime();
            let min = Math.round(msec / (60 * 1000));
            let tm = Math.floor(min / 60) + '小时';
            if (min % 60 > 0) {
                tm = tm + min % 60 + '分';
            }
            $('.fly_time').text(tm);
            if (flt[0].status == "ATA" || flt[0].status == "ARR" || flt[0].status == "DNR") {
                $('.ariline_min').text(Math.round((ataChn1 - atdChn1) / (60 * 1000)));
            } else if (flt[0].status == "DEP" || flt[0].status == "ATD") {
                $('.ariline_min').text(Math.round((new Date() > atdChn1 ? new Date() - atdChn1 : 0) / (60 * 1000)));
            }
            $('.acno').text(flt[0].acLongNo);
            this.getWeather(flt[0]);
            this.getWfMetrepBiaoZhuns(flt[0]);
        }
    }
    setMapData() {
        let series = [];
        // 飞机位置
        let planes = _planeLocationList;
        let flightList = _flightInfoListObj;
        let len = planes.length;
        let acdat;
        for (let i = 0; i < len; i++) {
            var ac = planes[i];
            if (ac.fltno == _flightNo) {
                acdat = ac;
                if ($('.oil_rt') != undefined) {
                    if (!isNaN(ac.oil)) {
                        $('.oil_rt').text(Math.round(ac.oil) + 'cc');
                    } else {
                        $('.oil_rt').text('--');
                    }
                }
            }
        }
        let flt;
        let depAirport = ""; //出发机场信息
        let middleAirport = "";
        let arrAirport = ""; //到达机场信息
        if (flightList[_flightNo].length > 1) {
            if (flightList[_flightNo][0].status == "ATA") {
                flt = flightList[_flightNo][1];
            } else {
                flt = flightList[_flightNo][0];
            }
            depAirport = _airportList[flightList[_flightNo][0].depStn]; //出发机场信息
            middleAirport = _airportList[flightList[_flightNo][1].depStn]
            arrAirport = _airportList[flightList[_flightNo][1].arrStn]; //到达机场信息
        } else {
            flt = flightList[_flightNo][0];
            depAirport = _airportList[flightList[_flightNo][0].depStn]; //出发机场信息
            arrAirport = _airportList[flightList[_flightNo][0].arrStn]; //到达机场信息
        }

        // 飞行轨迹
        let data = [];
        for(var i= _pointlist.length-1; i>=0; i--){
            var d = _pointlist[i];
            d.UTC = d.UTC.replace(/\D/g,'')
        }

        // 国内航线
        if (flt.fltType != 'I') {
            // 删除相同时间的坐标点
            let idx = 0;
            for (let i = 0; i < _pointlist.length; i++) {
                _pointlist[i].idx = idx;
                idx++;
                let d = _pointlist[i];
                for (let j = _pointlist.length - 1; j >= 0; j--) {
                    let d2 = _pointlist[j];
                    if (d.UTC == d2.UTC && d.idx != d2.idx) {
                        _pointlist.splice(j, 1);
                    }
                }
            }

            // 国内航线删除坐标带E，W，N，S的坐标，避免两个MQ出现航路偏差
            for (let i = _pointlist.length - 1; i >= 0; i--) {
                let d = _pointlist[i];
                if (isNaN(d.LAT) || isNaN(d.LON)) {
                    _pointlist.splice(i, 1);
                }
            }
        }

        // 计划出发时间
        let date = new Date();
        let stdChnTM = parserDate(flt.stdChn); // 计划出发时间
        let ts_dep = stdChnTM.getTime() - (8 * 60 * 60 * 1000);
        date.setTime(ts_dep);
        let mm = date.getMonth() + 1;
        let dd = date.getDate();
        let h = date.getHours();
        let m = date.getMinutes();
        let s = date.getSeconds();
        if (mm < 10) {
            mm = '0' + mm;
        }
        if (dd < 10) {
            dd = '0' + dd;
        }
        if (h < 10) {
            h = '0' + h;
        }
        if (m < 10) {
            m = '0' + m;
        }
        if (s < 10) {
            s = '0' + s;
        }
        let utc_dep = date.getFullYear() + '' + mm + '' + dd + '' + h + '' + m + '' + s; // 现在时间


        date = new Date();
        let ts = date.getTime() - 86400000;
        let ts_now = date.getTime() - (8 * 60 * 60 * 1000);
        date.setTime(ts);
        mm = date.getMonth() + 1;
        dd = date.getDate();
        if (mm < 10) {
            mm = '0' + mm;
        }
        if (dd < 10) {
            dd = '0' + dd;
        }
        let utc_time = date.getFullYear() + '' + mm + '' + dd + '210000'; // UTC 昨天 21点，北京时间今天早上5点
        date.setTime(ts_now);
        mm = date.getMonth() + 1;
        dd = date.getDate();
        h = date.getHours();
        m = date.getMinutes();
        s = date.getSeconds();
        if (mm < 10) {
            mm = '0' + mm;
        }
        if (dd < 10) {
            dd = '0' + dd;
        }
        if (h < 10) {
            h = '0' + h;
        }
        if (m < 10) {
            m = '0' + m;
        }
        if (s < 10) {
            s = '0' + s;
        }
        let utc_now = date.getFullYear() + '' + mm + '' + dd + '' + h + '' + m + '' + s; // 现在时间

        for (let i = _pointlist.length - 1; i >= 0; i--) {
            // 删除昨天的航迹坐标
            var d = _pointlist[i];
            if (d.UTC < utc_time) {
                _pointlist.splice(i, 1);
            }
        }

        _pointlist.sort(function(a, b) {
            return Number(b.UTC) - Number(a.UTC);
        });

        if (_pointlist && _pointlist.length > 1) {
            len = _pointlist.length;
            for (let i = 1; i < len; i++) {
                let p1 = _pointlist[i - 1];
                let p2 = _pointlist[i];
                let lon = formatLonLat(p1.LON);
                let lat = formatLonLat(p1.LAT);
                let lon2 = formatLonLat(p2.LON);
                let lat2 = formatLonLat(p2.LAT);

                data.push({
                    fromName: '',
                    toName: '',
                    coords: [
                        [lon, lat],
                        [lon2, lat2]
                    ]
                });
            }
            series.push({
                name: 'lines',
                type: 'lines',
                coordinateSystem: 'geo',
                zlevel: 1,
                effect: {
                    show: false,
                },
                lineStyle: {
                    normal: {
                        color: '#ffffff',
                        width: 2,
                        curveness: 0
                    }
                },
                label: {
                    normal: {
                        show: false,
                    }
                },
                silent: true,
                data: data
            });
        }

        ////// 设置小飞机图标。 城市位置
        let symbol = '';

        if (flt.status == 'DEP' && flt.delay1 == '') {
            symbol = 'image://img/flight.legend_1.png';
        } else {
            symbol = 'image://img/flight.legend_2.png'; // 延误航班
        }

        if (_pointlist.length > 1) {
            var ac_lon = formatLonLat(_pointlist[0].LON);
            var ac_lat = formatLonLat(_pointlist[0].LAT);
            let ac_lon1 = formatLonLat(_pointlist[1].LON);
            let ac_lat1 = formatLonLat(_pointlist[1].LAT);
            var vec = getGeoAngle(ac_lat, ac_lon, ac_lat1, ac_lon1);
        } else {
            var ac_lon = acdat.lon;
            var ac_lat = acdat.lat;
            var vec = acdat.vec;
        }

        if (flightList[_flightNo].length > 1) {
            series.push({
                name: 'scatter',
                type: 'scatter',
                coordinateSystem: 'geo',
                zlevel: 2,

                data: [{
                    name: flightList[_flightNo][0].depCity,
                    isAirline: true,
                    isCity: true,
                    cityType: 'dep',
                    value: [depAirport.longitude, depAirport.latitude],
                    symbol: 'circle',
                    symbolSize: 7,
                    itemStyle: {
                        normal: {
                            color: '#3d7dd0',
                            borderColor: '#ffffff',
                            borderWidth: 2,
                        }
                    },
                    label: {
                        normal: {
                            show: true,
                            formatter: '{b}',
                            offset: [0, -16],
                            textStyle: {
                                fontSize: 12,
                                color: '#FFFFFF',
                            }
                        }
                    }
                }, {
                    name: flightList[_flightNo][0].arrCity,
                    isAirline: true,
                    isCity: true,
                    cityType: 'dep',
                    value: [middleAirport.longitude, middleAirport.latitude],
                    symbol: 'circle',
                    symbolSize: 7,
                    itemStyle: {
                        normal: {
                            color: '#3d7dd0',
                            borderColor: '#ffffff',
                            borderWidth: 2,
                        }
                    },
                    label: {
                        normal: {
                            show: true,
                            formatter: '{b}',
                            offset: [0, -16],
                            textStyle: {
                                fontSize: 12,
                                color: '#FFFFFF',
                            }
                        }
                    }
                }, {
                    name: flightList[_flightNo][1].arrCity,
                    isAirline: true,
                    isCity: true,
                    cityType: 'arr',
                    value: [arrAirport.longitude, arrAirport.latitude],
                    symbol: 'circle',
                    symbolSize: 7,
                    itemStyle: {
                        normal: {
                            color: '#3d7dd0',
                            borderColor: '#ffffff',
                            borderWidth: 2,
                        }
                    },
                    label: {
                        normal: {
                            show: true,
                            formatter: '{b}',
                            offset: [0, -16],
                            textStyle: {
                                fontSize: 12,
                                color: '#FFFFFF',
                            }
                        }
                    }
                }, {
                    name: acdat.fltno,
                    acno: acdat.acno,
                    oil: acdat.oil,
                    flt: flt,
                    isAirline: true,
                    value: [ac_lon, ac_lat],
                    symbol: symbol,
                    symbolSize: 22,
                    label: {
                        normal: {
                            show: false,
                        }
                    },
                    symbolRotate: -vec + 90, //-acdat.vec+90, //航向   正北是0°，顺时针，0到360° // 因为图片机头指向右，所以需要逆时针转90度
                }]
            });
        } else {
            series.push({
                name: 'scatter',
                type: 'scatter',
                coordinateSystem: 'geo',
                zlevel: 2,
                data: [{
                    name: flt.depCity,
                    isAirline: true,
                    isCity: true,
                    cityType: 'dep',
                    value: [depAirport.longitude, depAirport.latitude],
                    symbol: 'circle',
                    symbolSize: 7,
                    itemStyle: {
                        normal: {
                            color: '#3d7dd0',
                            borderColor: '#ffffff',
                            borderWidth: 2,
                        }
                    },
                    label: {
                        normal: {
                            show: true,
                            formatter: '{b}',
                            offset: [0, -16],
                            textStyle: {
                                fontSize: 12,
                                color: '#FFFFFF',
                            }
                        }
                    }
                }, {
                    name: flt.arrCity,
                    isAirline: true,
                    isCity: true,
                    cityType: 'arr',
                    value: [arrAirport.longitude, arrAirport.latitude],
                    symbol: 'circle',
                    symbolSize: 7,
                    itemStyle: {
                        normal: {
                            color: '#3d7dd0',
                            borderColor: '#ffffff',
                            borderWidth: 2,
                        }
                    },
                    label: {
                        normal: {
                            show: true,
                            formatter: '{b}',
                            offset: [0, -16],
                            textStyle: {
                                fontSize: 12,
                                color: '#FFFFFF',
                            }
                        }
                    }
                }, {
                    name: acdat.fltno,
                    acno: acdat.acno,
                    oil: acdat.oil,
                    flt: flt,
                    isAirline: true,
                    value: [ac_lon, ac_lat],
                    symbol: symbol,
                    symbolSize: 22,
                    label: {
                        normal: {
                            show: false,
                        }
                    },
                    symbolRotate: -vec + 90, //-acdat.vec+90, //航向   正北是0°，顺时针，0到360° // 因为图片机头指向右，所以需要逆时针转90度
                }]
            });
        }
        let options = {
            series: series
        }
        myChart.setOption(options);
        // 航线里程
        let dis = this._getDistance(depAirport.latitude, depAirport.longitude, arrAirport.latitude, arrAirport.longitude);
        $('.ariline_dis').text(Math.round(dis));
        let dis2 = this._getDistance(depAirport.latitude, depAirport.longitude, _planeLocationList[0].lat, _planeLocationList[0].lon);
        $('.ariline_dis2').text(Math.round(dis2));
    }
    //内部私有方法
    _initMap(){
        myChart.setOption(_option);
        window.onresize = myChart.resize;
    }
    _initAirportList(){
        let param = {
            //"AIRPORT_CODE": arpcodes, // 可选，传入机场CODE
        }
        $.ajax({
            type: 'post',
            url: "/bi/web/airportdetail",
            contentType: 'application/json',
            dataType: 'json',
            async: false,
            data: JSON.stringify(param),
            success: function(response) {
                _airportList = {};
                var list = response.airport;
                for (var i = list.length - 1; i >= 0; i--) {
                    var arp = list[i];
                    _airportList[arp.code] = arp;
                }
            }.bind(this),
            error: function() {}
        });
    }
    _initAcAircraftList(){
        var param = {
            "companyNodeId": companyNodeId,
        };
        $.ajax({
            type: 'post',
            url: "/bi/web/getAcAircraftList",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function(response) {
                _ac_aircraft_list = response.data;
            }.bind(this),
            error: function() {}
        });
    }
    _initRoundChart2(rate){
        let canvas = document.getElementById('cvs_chart2');
        let context = canvas.getContext('2d');
        context.clearRect(0, 0, canvas.width, canvas.height);
        let x = canvas.width / 2;
        let y = canvas.height / 2;
        let radius = 47;
        let lineWidth = 20;
        // draw back
        let startAngle = Math.PI - Math.PI / 3.6;
        let endAngle = startAngle + Math.PI + Math.PI / 3.6 * 2;
        context.beginPath();
        context.arc(x, y, radius, startAngle, endAngle, false);
        context.lineWidth = lineWidth;
        context.strokeStyle = '#00438B';
        context.stroke();
        context.beginPath();
        context.arc(x, y, 33, startAngle, endAngle, false);
        context.lineWidth = 1;
        context.strokeStyle = '#2683CA';
        context.stroke();
        // draw overlay
        let startAngle2 = startAngle;
        let endAngle2 = startAngle + (endAngle - startAngle) * rate;
        context.beginPath();
        context.arc(x, y, radius, startAngle2, endAngle2, false);
        context.lineWidth = lineWidth;
        // linear gradient
        let color;
        if (rate < 0.5) {
            color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
        } else if (rate < 0.8) {
            color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
        } else {
            color = context.createLinearGradient(0, canvas.height, canvas.width * rate, canvas.width / 2);
        }
        color.addColorStop(0, '#122A61');
        color.addColorStop(1, '#78DAFC');
        context.strokeStyle = color;
        context.stroke();
        // draw head
        let startAngle3 = startAngle + (endAngle - startAngle) * rate - (endAngle - startAngle) * 0.01;
        let endAngle3 = startAngle + (endAngle - startAngle) * rate;
        context.beginPath();
        context.arc(x, y, radius, startAngle3, endAngle3, false);
        context.lineWidth = lineWidth;
        context.strokeStyle = '#FFFFFF';
        context.stroke();
    }
    _scrollRunawayWeathar(i){
        console.log("i = " + i);
        if(_runaway_data.length > 0 && i < _runaway_data.length) {
            let item = _runaway_data[i];
            Object.keys(item).forEach(function (k,index) {
                // console.log("runaway["+i+"]", k, item[k]);
                $("#"+k).html(item[k]);
            })
            i ++;
        } else {
            i = 0;
        }
        return i;
    }
    _getQueryString(name) {
        let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        let r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return '';
    }
    _trimTime(timestr) {
        let arr = timestr.split(' ');
        let arr2 = arr[1].split(':');
        return arr2[0] + ':' + arr2[1];
    }
    _getDistance(lat1, lng1, lat2, lng2) {
        let radLat1 = lat1 * Math.PI / 180.0;
        let radLat2 = lat2 * Math.PI / 180.0;
        let a = radLat1 - radLat2;
        let b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
        let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
            Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
        s = s * 6378.137; // EARTH_RADIUS;
        s = Math.round(s * 10000) / 10000;
        return s;
    }
    _dateFormat(str){
        let [arr1,arr2,arr3] = str.split('-');
        return arr3.includes(" ")? arr1 + '年' + arr2 + '月' + arr3.split(" ")[0] + '日' + ' ' + arr3.split(" ")[1] : arr1 + '年' + arr2 + '月' + arr3 + '日';
    }
}
let fli = new Flight();
fli.loadAll();
setInterval(function(){fli.loadAll()}, 5 * 60 * 1000);