//乘法函数  
function accMul(arg1, arg2) {  
    var m = 0, s1 = arg1.toString(), s2 = arg2.toString();  
    try {  
        m += s1.split(".")[1].length;  
    }  
    catch (e) {  
    }  
    try {  
        m += s2.split(".")[1].length;  
    }  
    catch (e) {  
    }  
    return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m);  
}   
 
//给Number类型增加一个mul方法，使用时直接用 .mul 即可完成计算。   
Number.prototype.mul = function (arg) {  
    return accMul(arg, this);  
};   
 
//除法函数  
function accDiv(arg1, arg2) {  
    var t1 = 0, t2 = 0, r1, r2;  
    try {  
        t1 = arg1.toString().split(".")[1].length;  
    }  
    catch (e) {  
    }  
    try {  
        t2 = arg2.toString().split(".")[1].length;  
    }  
    catch (e) {  
    }  
    with (Math) {  
        r1 = Number(arg1.toString().replace(".", ""));  
        r2 = Number(arg2.toString().replace(".", ""));  
        return (r1 / r2) * pow(10, t2 - t1);  
    }  
}   
//给Number类型增加一个div方法，，使用时直接用 .div 即可完成计算。   
Number.prototype.div = function (arg) {  
    return accDiv(this, arg);  
};
function accAdd(arg1, arg2) {  
    var r1, r2, m;  
    try {  
        r1 = arg1.toString().split(".")[1].length;  
    }  
    catch (e) {  
        r1 = 0;  
    }  
    try {  
        r2 = arg2.toString().split(".")[1].length;  
    }  
    catch (e) {  
        r2 = 0;  
    }  
    m = Math.pow(10, Math.max(r1, r2));  
    return (arg1.mul(m) + arg2.mul(m)).div(m);  
}   
 
//给Number类型增加一个add方法，，使用时直接用 .add 即可完成计算。   
Number.prototype.add = function (arg) {  
    return accAdd(arg, this);  
};  
 
  
//减法函数  
function Subtr(arg1, arg2) {  
    var r1, r2, m, n;  
    try {  
        r1 = arg1.toString().split(".")[1].length;  
    }  
    catch (e) {  
        r1 = 0;  
    }  
    try {  
        r2 = arg2.toString().split(".")[1].length;  
    }  
    catch (e) {  
        r2 = 0;  
    }  
    m = Math.pow(10, Math.max(r1, r2));  
     //last modify by deeka  
     //动态控制精度长度  
    n = (r1 >= r2) ? r1 : r2;  
    return parseFloat(((arg1 * m - arg2 * m) / m).toFixed(n));  
}  
  
//给Number类型增加一个add方法，，使用时直接用 .sub 即可完成计算。   
Number.prototype.sub = function (arg) {  
    return Subtr(this, arg);  
};





/*
@containerId : 容器ID
@numTo (Int) : 滚动的目标数字
@numFrom (Int) : 滚动的开始数字
@length (Int) : 位数
@groupType (String): 分割情况，comma：TODO 逗号分隔， group：方块圈起来
@splitChars (Object): 需要插入的分割符 {0:'张'}
*/

$.animateToNumber = function(containerId, numTo, numFrom, numLength, groupType, splitChars){

  var data = {};
  var targetClass = {};
  var zero = {};
  var html = '';


  function numToObj(number){
    if(number == 0) {
      return data.zero;
    }
    var obj = {};
    var str = number.toString();
    var len = str.length;
    for(var i=0; i<len; i++){
      obj['digit'+i] = parseInt(str.charAt(len-i-1));
    }
    return obj;
  }


  $.scrollToNum = function(obj, num, pos, time){
    var $this = $(obj);
    var target = 'group_0_' + data.numbers[num];

    $this.find(".numbers-view").stop(true, true);
    var numHeight = $('.number-div .number').css('height').split('px')[0];
    var top = Math.ceil(num * numHeight);
    var currentTop = -parseFloat($this.find(".numbers-view").css("top").split("px")[0]);
    
    

    if(top == currentTop) {
      return;
    } else if(currentTop < top) {
      // G0:0123456789 内部滚动
      $this.find(".numbers-view").animate({top: -top}, time, "swing", function(){
        $this.find(".numbers-view").css({top: -top});
      });
    } else {
      // 从group0 滚动到 group1   G0:0123456789 -> G1:0123456789
      var top_group1 = Math.ceil((num+10) * numHeight);
      $this.find(".numbers-view").animate({top: -top_group1}, time, "swing", function(){
        // 回退到group0的数字上
        $this.find(".numbers-view").css({top: -top});
      });
    }


  }


  var length1 = numTo.toString().length;
  if(numLength > 0){
    var length = Math.max(numLength, length1);
  }else{
    var length = length1;
  }

  for(var i=0; i<length; i++){
    var tmp_html = html;
    if(splitChars[i] != undefined){
      tmp_html = '<div class="number-spliter">' + splitChars[i] + '</div>' + html;
    }

    html = '<div class="digit' + i + ' number"></div>';

    if(groupType=="group"){
      numberWidth = 24
      if(i==0){
        html = html+'</div>';
      }

      if(i==length-1){
        var width = numberWidth;
        width = width * ((length-1)%3+1);
        if(width == 0){
          width = numberWidth;
        }
        width = width + 2 + 8;//2px border width, 5 padding-left
        html = '<div class="number-group" style="width:'+width+'px">'+html;
      }else if((i+1)%3==0){
        var width = numberWidth;
        width = width * 3 + 2 + 8;//2px border width, 5 padding-left
        html = '</div><div class="number-group" style="width:'+width+'px">'+html;
      }
    }

    html = html + tmp_html;


    if(groupType=="comma" && (i+1)%3==0 && length>i+1){

      html = '<div class="comma">,</div>' + html;

    }

  }

  html = '<div class="number-div">' + html + '</div>';
  $('#'+containerId).html(html);

  for(var i=0; i<length; i++){
    targetClass['digit'+i] = $('#'+containerId+' .digit'+i);
    zero['digit'+i] = 0;
  }


  data = {
    numbers: ["zero", "one", "two", "three", "four", "five", "six", "seven", "eight", "nine"],
    targetClass: targetClass,
    zero: zero,
    numbersTmp: ""
  };


  function numDiv(num, group){
    var height = $('.number-div .number').css('height').split('px')[0];
    var top = (height * num + group * height * 10) + 'px';
    return "<div class='group_" + group + "_" + data.numbers[num] + "' style='top:"+top+"'>" + num + "</div>";
  }
  
  for(var i = 0; i < 20; i ++) {
    data.numbersTmp += numDiv(i%10, Math.floor(i/10));
  }
  
  $("#"+containerId+" .number-div .number").append("<div class='numbers-view'>" + data.numbersTmp + "</div>");


  var objFrom = numToObj(numFrom);
  var objTo = numToObj(numTo);
  
  $.each(objFrom, function(key, value){
    $.scrollToNum(data.targetClass[key], value, key, 0);
  });

  $.each(objTo, function(key, value){
    $.scrollToNum(data.targetClass[key], value, key, 1100);
  });

}