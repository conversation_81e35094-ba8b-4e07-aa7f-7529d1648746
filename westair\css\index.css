
.page-bgimg {
  position: absolute;
  top: 0px;
  width: 2132px;
  height: 768px;
  background: url(../img/index_bg.png?2) no-repeat top center;
  overflow-x: hidden;
  overflow-y: hidden;
}

.page-wrapper {
  position: absolute;
  top: 0px;
  width: 2132px;
  height: 768px;
  overflow-x: hidden;
  overflow-y: hidden;
}

#welcome_msg {
  position: absolute;
  width:730px; 
  height:39px; 
  left: calc((100% - 730px)/2); 
  top:7px; 
  overflow:hidden;
}

#col_tl {
	position: absolute;
	width: 380px;
	height: 44px;
	top: 25px;
	left: 295px;
}

#col_tl .col1 {
	position: absolute;
	width: 120px;
	height: 100%;
	left: 0px;
}
#col_tl .col2 {
	position: absolute;
	width: 120px;
	height: 100%;
	left: 120px;
}
#col_tl .col3 {
	position: absolute;
	width: 120px;
	height: 100%;
	left: 240px;
}
#col_tl .t {
	position: absolute;
	top: 0px;
}
#col_tl .b {
	position: absolute;
	top: 16px;
}


#col_l {
	position: absolute;
	width: 712px;
	height: 664px;
	top: 80px;
	left: 19px;
}

#col_l .row1{
	position: absolute;
	width: 100%;
	height: 179px;
	top: 31px;
}
#col_l .row2{
	position: absolute;
	width: 100%;
	height: 210px;
	top: 243px;
	
}
#col_l .row3{
	position: absolute;
	width: 100%;
	height: 179px;
	top: 485px;
}

#col_l .chart1,
#col_l .chart7
{
	position: absolute;
	top: 15px;
	left: 48px;
}
#col_l .chart4
{
	position: absolute;
	top: 10px;
	left: 43px;
}
#col_l .chart2,
#col_l .chart8
{
	position: absolute;
	top: 15px;
	left: 285px;
}
#col_l .chart3
{
	position: absolute;
	top: 15px;
	left: 522px;
}
#col_l .chart5
{
	position: absolute;
	top: 25px;
	left: 285px;
}
#col_l .chart6
{
	position: absolute;
	top: 10px;
	left: 517px;
}
#col_l .chart9
{
	position: absolute;
	top: 10px;
	left: 517px;
}

#col_l .chart{
	width: 140px;
	height: 140px;
	background: url('../img/chart_bg.png') no-repeat center center;
}
#col_l .chart4,
#col_l .chart6,
#col_l .chart9
{
	width: 150px;
	height: 150px;
	background: url('../img/time_ring.svg') no-repeat center center;
}
#col_l .chart7
{
	width: 140px;
	height: 140px;
	background: none;
}
#col_l .chart8
{
	width: 140px;
	height: 140px;
	background: url('../img/chart_bg2.png') no-repeat center center;
}
#col_l .chart .pointer{
	position: absolute;
	top: 0px;
	left: 0px;
}


#col_l .row1 .chart .lb_rate1{
	position: absolute;
	top: 114px;
	left: 0;
	font-size: 12px;
	line-height: 20px;
	color: #79FF44 !important;
}
#col_l .row1 .chart .lb_rate2{
	position: absolute;
	top: 114px;
	left: 80px;
	font-size: 12px;
	line-height: 20px;
	color: #FFBC00 !important;
}
#col_l .row1 .chart .lb_rate1 span{
	color: #79FF44 !important;
}
#col_l .row1 .chart .lb_rate2 span{
	color: #FFBC00 !important;
}


#col_l .row2 .chart .lb_rate0{
	position: absolute;
	width: 100px;
	font-size: 12px;
	line-height: 20px;
	color: #FFF !important;
}
#col_l .row2 .chart .lb_rate1{
	position: absolute;
	width: 100px;
	font-size: 12px;
	line-height: 20px;
	color: #79FF44 !important;
}
#col_l .row2 .chart .lb_rate2{
	position: absolute;
	width: 100px;
	font-size: 12px;
	line-height: 20px;
	color: #FFBC00 !important;
}
#col_l .row2 .chart .lb_rate0 span{
	color: #FFF !important;
}
#col_l .row2 .chart .lb_rate1 span{
	color: #79FF44 !important;
}
#col_l .row2 .chart .lb_rate2 span{
	color: #FFBC00 !important;
}


#col_l .row3 .chart .lb_rate1{
	position: absolute;
	top: 114px;
	left: 0;
	font-size: 12px;
	line-height: 20px;
	color: #79FF44 !important;
	text-align: left;
}
#col_l .row3 .chart .lb_rate2{
	position: absolute;
	top: 114px;
	left: 80px;
	font-size: 12px;
	line-height: 20px;
	color: #FFBC00 !important;
	text-align: right;
}
#col_l .row3 .chart .lb_rate1 span{
	color: #79FF44 !important;
}
#col_l .row3 .chart .lb_rate2 span{
	color: #FFBC00 !important;
}







#col_m {
	position: absolute;
	width: 666px;
	height: 689px;
	top: 58px;
	left: 734px;
}

#col_m .bot {
	position: absolute;
	width: 100%;
	height: 133px;
	bottom: 0px;
}
#col_m .bot .tab_trend {
	position: absolute;
	width: 140px;
	top: 30px;
	left: 25px;
}

#col_m .bot .tab_normal_rate {
	display: inline-block; 
	width: 100%; 
	height: 42px;
	text-align: center;
	cursor: pointer;
	border:1px solid #3939A2;
	pointer-events: auto;
	color: #4358BD;
	line-height: 42px;
}
#col_m .bot .tab_normal_rate.selected {
	background-color: #3939A2;
	color: #FFF;
}
#col_m .bot .chart_trend {
	position: absolute;
	width:480px; 
	height:100px;
	top: 30px;
	left: 170px;
}



#col_tr {
	position: absolute;
	width: 444px;
	height: 44px;
	top: 25px;
	right: 95px;
	line-height: 20px;
}

#col_r {
	position: absolute;
	width: 712px;
	height: 669px;
	top: 78px;
	right: 18px;
	
}

#col_r .row1{
	position: absolute;
	width: 100%;
	height: 245px;
	top: 31px;
}
#col_r .row1 .col1{
	position: absolute;
	width: 238px;
	height: 100%;
}
#col_r .row1 .col1 .ac{
	position: absolute;
	width: 192px;
	height: 92px;
}
#col_r .row1 .col1 .ac1{
	left: 23px;
	top: 12px;
}
#col_r .row1 .col1 .ac2{
	left: 125px;
	top: 12px;
}
#col_r .row1 .col1 .ac .type{
	position: absolute;
	left: 20px;
	top: 17px;
}
#col_r .row1 .col1 .ac .num{
	position: absolute;
	width: 192px;
	left: 	0px;
	top: 41px;
	text-align: center;
}
#col_r .row1 .col1 .people{
	position: absolute;
	width: 100%;
	height: 100px;
	top: 145px;
}
#col_r .row1 .col1 .people .total{
	position: absolute;
	top: 12px;
	left: 20px;
}
#col_r .row1 .col1 .people .inl{
	position: absolute;
	top: 44px;
	left: 20px;
}
#col_r .row1 .col1 .people .inl .lb{
	display: block;
	color: #8AFF54;
}
#col_r .row1 .col1 .people .int{
	position: absolute;
	top: 44px;
	left: 120px;
}
#col_r .row1 .col1 .people .int .lb{
	display: block;
	color: #FF8000;
}
#col_r .row1 .col1 .people .ico{
	position: absolute;
	width: 25px;
	height: 66px;
	top: 17px;
	left: 195px;
	background: url('../img/people2.png') no-repeat 0 0;
}
#col_r .row1 .col1 .people .ico .t{
	position: absolute;
	width: 100%;
	height: 100%;
	background: url('../img/people2.png') no-repeat -25px 0;
}


#col_r .row1 .col2{
	position: absolute;
	width: 238px;
	height: 100%;
	left: 238px;
}
#col_r .row1 .col2 .ac{
	position: absolute;
	width: 92px;
	height: 92px;
}
#col_r .row1 .col2 .ac1{
	left: 23px;
	top: 12px;
}
#col_r .row1 .col2 .ac2{
	left: 125px;
	top: 12px;
}
#col_r .row1 .col2 .ac .num{
	position: absolute;
	width: 60px;
	left: 17px;
	top: 41px;
	text-align: center;
}
#col_r .row1 .col2 .zzlk{
	left: 80px;
	top: 180px;
}

#col_r .row1 .col3{
	position: absolute;
	width: 238px;
	height: 100%;
	left: 476px;
}

#col_r .row2{
	position: absolute;
	width: 100%;
	height: 176px;
	top: 309px;
}

.markline_label {
	position: absolute;
	width: 330px;
	left: 285px;
	top: 12px;
	font-size: 12px;
}
.markline_label .line {
	display: inline-block;
	height: 4px;
	width: 25px;
	border-top: 1px dashed;
	margin-right: 3px;
}
.markline_label .line1 {
	border-color: #CC99FF;
}
.markline_label .line2 {
	border-color: #FF0000;
}
.markline_label .line3 {
	border-color: #FFFF4D;
}
.markline_label .line4 {
	border-color: #73DCFF;
}
.markline_label .lb {
	margin-right: 8px;
}



#col_r .row3{
	position: absolute;
	width: 100%;
	height: 184px;
	top: 485px;
}
#col_r .row3 .col1{
	position: absolute;
	width: 357px;
	height: 150px;
	top: 35px;
}
#col_r .row3 .col2{
	position: absolute;
	width: 357px;
	height: 150px;
	top: 35px;
	left: 357px;
}


#earth3d-wrapper {
	position: absolute;
	width: 666px;
	height: 558px;
	top: 0px;
}


#link_2base {
	position: absolute;
	width: 119px;
	height: 27px;
	top: 20px;
	left: 5px;
	background: url('../img/link_2base.png') no-repeat 0 0;
	cursor: pointer;
	pointer-events: auto;
}


