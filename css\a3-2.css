
/**/

#bgcolor {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgb(9,72,149);
  background: -moz-radial-gradient(center, ellipse cover,  rgba(17,77,166,1) 0%, rgba(2,12,37,1) 100%);
  background: -webkit-radial-gradient(center, ellipse cover,  rgba(17,77,166,1) 0%,rgba(2,12,37,1) 100%);
  background: radial-gradient(ellipse at center,  rgba(17,77,166,1) 0%,rgba(2,12,37,1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#094895', endColorstr='#020c25',GradientType=1 );
}


.pagetitle{
  position: absolute;
  width: 100%;
  height: 54px;
  top: 58px;
  text-align: center;

}
.maintitle{
  position: absolute;
  top: 18px;
  color: #fff;
  width: 100%;
  font-size: 24px;
}

#main_cb_week {
  position: absolute;
  top: 19px;
  right: 126px;
  height: 34px;
  width: 85px;
  z-index: 1000;
}
#main_cb_month {
  position: absolute;
  top: 19px;
  right: 22px;
  height: 34px;
  width: 104px;
  z-index: 1001;
}

#main_cb_week .combobox_label
{
  padding: 6px 6px;
  background-image: url(../img/combobox_arr.png);
  background-repeat: no-repeat;
  background-position: 63px center;
  border-radius: 4px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}
#main_cb_month .combobox_label
{
  padding: 6px 6px;
  background-image: url(../img/combobox_arr.png);
  background-repeat: no-repeat;
  background-position: 83px center;
  border-radius: 4px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: none;
}

.combotab_selected .combobox_label {
  background-color: #2a81d2;
}
.combotab .combobox_label {
  background-color: #0950a2;
  opacity: 0.5;
  color: #8abfff;
}

#week_date_range {
  position: absolute;
  top: 58px;
  right: 50px;
  width: 160px;
  font-size: 14px;
  text-align: center;
  border: 1px solid #1f5fa8;
  border-radius: 2px;
  padding: 3px 0 5px 0px;
  background-color: #021d39;
  box-shadow: 0 1px 8px #021121;

  display: none;
  z-index: 999;
  
}






/*Common*/
.block-frame {
  position: absolute;
  width: 270px;
  pointer-events: auto;
}


.block-header {
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  background: none !important;
  padding: 2px 0 0 7px;
  margin: 0; 
  color: #fff !important;
  border-left: 6px solid #0090ff;
  text-align: left;
  line-height: 18px;
}
.block-frame .cont {
  position: relative;
  border: none !important;
  background-color: rgba(0,0,0,0) !important;

}
.block-header span {
  color: #011f4d;
  font-size: 16px;
  font-weight: bold;
  padding: 0 0px 0 0;
}



/* ------- */
.center_kpi_grids {
  position: absolute;
  top: 139px;
  left: 308px;
  width: 767px;
  height: 540px;
  background-image: url(../img/a3.2.midbg.png?2);
  background-repeat: no-repeat;
  text-align: center;
}
.center_kpi_grids .view .mid {
  position: absolute;
  top: 201px;
  left: 302px;
  width: 150px;
  height: 150px;
  transform-origin: 50% 50%;
  -moz-transform-origin: 50% 50%;
  -ms-transform-origin: 50% 50%;
  -webkit-transform-origin: 50% 50%;
  
  opacity: 0;

}

.center_kpi_grids .view .mid .logo {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 150px;
  height: 70px;
  background-repeat: no-repeat;
  background-position: center center;
}
.center_kpi_grids .view .mid .title {
  position: absolute;
  top: 60px;
  left: 0px;
  width: 150px;
}
.center_kpi_grids .view .mid .lb {
  position: absolute;
  top: 88px;
  left: 0px;
  width: 150px;
}
.center_kpi_grids .view .mid .delay_cause {
  position: absolute;
  top: 103px;
  left: 0px;
  width: 150px;
}

.center_kpi_grids .view .bigtt {
  display: block;
}
.center_kpi_grids .view .RATE {
  position: absolute;
  top: 126px;
  left: 247px;
  width: 130px;
  height: 70px;
  color: #fff;
}
.center_kpi_grids .view .ARR {
  position: absolute;
  top: 126px;
  left: 377px;
  width: 130px;
  height: 70px;
  color: #fff;
}
.center_kpi_grids .view .INT {
  position: absolute;
  top: 239px;
  left: 439px;
  width: 130px;
  height: 70px;
  color: #fff;
}
.center_kpi_grids .view .ORI {
  position: absolute;
  top: 347px;
  left: 377px;
  width: 130px;
  height: 70px;
  color: #fff;
}
.center_kpi_grids .view .DEP {
  position: absolute;
  top: 239px;
  left: 184px;
  width: 130px;
  height: 70px;
  color: #fff;
}
.center_kpi_grids .view .EXE {
  position: absolute;
  top: 347px;
  left: 247px;
  width: 130px;
  height: 70px;
  color: #fff;
}
.center_kpi_grids .view .green {
  color: #22e122 !important;
}



.center_kpi_grids .itm {
  width: 96px;
  height: 106px;
  position: absolute;
  color: #fff;

  cursor: pointer;
  pointer-events: auto;

  background-image: url(../img/a3.2.grid0.png?2);
  background-repeat: no-repeat;

}
.center_kpi_grids .itm .bg {
  width: 96px;
  height: 106px;
  position: absolute;
  background-image: url(../img/a3.2.grid_h.png?2);
  background-repeat: no-repeat;
  display: none;
}
.center_kpi_grids .itm .logo {
  position: absolute;
  top: 15px;
  left: 13px;
  width: 70px;
  height: 70px;
  background-repeat: no-repeat;
  background-position: center center;
}
.center_kpi_grids .itm .name {
  font-size: 12px;
  position: absolute;
  top: 61px;
  left: 2px;
  width: 90px;
  font-weight: bold;
  text-align: center;
  display: none;
}
.center_kpi_grids .itm.leg0 {
}
.center_kpi_grids .itm.leg0.select {
  background-image: url(../img/a3.2.grid1.png?2);
  background-repeat: no-repeat;
}
.center_kpi_grids .itm.leg1 {
  background-image: url(../img/a3.2.grid2.png?2);
  background-repeat: no-repeat;
}
.center_kpi_grids .itm.leg1.select {
  background-image: url(../img/a3.2.grid3.png?2);
  background-repeat: no-repeat;
}
.center_kpi_grids .itm.select {
  color: #0f3f89;
}

.center_kpi_grids .itm0 {
  top: 34px;
  left: 85px;
}
.center_kpi_grids .itm1 {
  top: 116px;
  left: 37px;
}
.center_kpi_grids .itm2 {
  top: 116px;
  left: 131px;
}
.center_kpi_grids .itm3 {
  top: 197px;
  left: -12px;
}
.center_kpi_grids .itm4 {
  top: 197px;
  left: 85px;
}
.center_kpi_grids .itm5 {
  top: 278px;
  left: 37px;
}
.center_kpi_grids .itm6 {
  top: 358px;
  left: 85px;
}
.center_kpi_grids .itm7 {
  top: 34px;
  left: 572px;
}
.center_kpi_grids .itm8 {
  top: 116px;
  left: 525px;
}
.center_kpi_grids .itm9 {
  top: 116px;
  left: 619px;
}
.center_kpi_grids .itm10 {
  top: 197px;
  left: 572px;
}
.center_kpi_grids .itm11 {
  top: 278px;
  left: 619px;
}
.center_kpi_grids .itm12 {
  top: 359px;
  left: 572px;
}
.center_kpi_grids .itm13 {
  top: 197px;
  left: 666px;
}




/* --- */
.block_l1 {
  height: 410px;
  left: 15px;
  top: 100px;
}
.block_l1 .cont{
  padding-top: 10px;
  height: 384px;
}
.block_l1 .normal_rate {
  position: absolute;
  top: 25px;
}
.block_l1 .cont .row1{
  position: absolute;
  top: 49px;
  width: 100%;
  height: 166px;
  pointer-events: none;
}
.block_l1 .cont .row1 .b1{
  position: absolute;
  left: 0;
  top: 33px;
}
.block_l1 .cont .row1 .b1 .kpi{
  color: #a3d800;
  display: block;
  line-height: 22px;
  height: 22px;
}
.block_l1 .cont .row1 .b2{
  position: absolute;
  right: 0;
  top: 33px;
  text-align: right;
}
.block_l1 .cont .row1 .b2 .kpi{
  color: #3ec4fd;
  display: block;
  line-height: 22px;
  height: 22px;
}
.block_l1 .cont .row1 .c{
  position: absolute;
  left: 110px;
  top: 37px;
  text-align: center;
  line-height: 16px;
}
.block_l1 .cont .row1 .c .r1{
  color: #a3d800;
}
.block_l1 .cont .row1 .c .r2{
  color: #3ec4fd;
}
.block_l1 .cont .row1 .c .vs{
  display: block;
}

.block_l1 .cont .row1 canvas{
  position: absolute;
  left:71px; 
  top:15px; 
  background:rgba(255,0,0,0);
}

.block_l1 .cont .row2{
  position: absolute;
  height:216px; 
  width: 100%;
  top:166px; 
  pointer-events: auto;
}

.block_l1 .tab {
  position: absolute;
  width: 50%;
  height: 32px;
  top: 0;
  background-color: #1a4886;
  color: #3d93df;
  text-align: center;
  line-height: 32px;
  font-size: 14px;
  cursor: pointer;
}
.block_l1 .selected {
  color: #fff;
  background-color: #2a81d2;
}
.block_l1 .tab1 {
  left: 0px;
}
.block_l1 .tab2 {
  left: 50%;
}
.block_l1 .tabc{
  position: absolute;
  width: 100%;
  height: 175px;
  top: 32px;
}

.block_l1 .tabc table{
  max-height: 163px;
  margin-top: 10px;
  font-size: 12px;
}
.block_l1 .tabc .c1{
  text-align: right;
  padding-left: 25px;
  color: #47aafd;
  width: 100px;
}
.block_l1 .tabc .c2{
  text-align: right;
  padding-left: 3px;
  color: #a8d5ff;
}
.block_l1 .tabc .c3{
  padding-left: 3px;
}
.block_l1 .tabc .c3 .bar{
  display: inline-block;
  height: 3px;
  margin-bottom: 4px;
  margin-right: 3px;
  background: rgb(1,70,139);
  background: -moz-linear-gradient(left,  rgba(1,70,139,1) 0%, rgba(112,218,252,1) 100%);
  background: -webkit-linear-gradient(left,  rgba(1,70,139,1) 0%,rgba(112,218,252,1) 100%);
  background: linear-gradient(to right,  rgba(1,70,139,1) 0%,rgba(112,218,252,1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#01468b', endColorstr='#6fd9fd',GradientType=1 );
}



/* --- */

.block_l2 {
  height: 218px;
  left: 15px;
  top: 530px;
}
.block_l2 .cont{
  height: 190px;
  padding: 12px 0 12px 0;
}




.cont .blk{
  display: block;
}
.cont .blk span{
  display: inline-block;
  margin-right: 0;
}
.cont .blk .val{
  margin-right: 12px;
}
.cont .blk.con{
  margin-bottom: 10px;
  min-height: 40px;
}
.cont .itm{
  display: inline-block;
  height: 40px;
  padding-right: 4px;
  padding-top: 3px;
}
.cont .itm .nm{
  display: block;
  color: #99ccff;
}
.cont .itm .val{
  font-weight: bold;
}


/* --- */




/* --- */
.block_r1 {
  right: 8px;
  top: 135px;
}
.block_r1 .cont{
  padding: 12px 0 12px 0;
  height: 288px;
}
.block_r1 .row1{
  display: block;
  height: 78px;
}
.block_r1 .blk1{
  position: absolute;
  top: 15px;
  width: 200px;
}
.block_r1 .blk2{
  position: absolute;
  top: 15px;
  left: 150px;
  width: 150px;
}




/* --- */
.block_r2 {
  right: 8px;
  top: 453px;
}
.block_r2 .cont{
  top: 25px;
  height: 288px;
  padding: 12px 0 12px 0;
}
.block_r2 .tab{
  position: absolute;
  text-align: center;
  font-size: 14px;
  font-weight: bold;
  color: #011f4d;
  padding-top: 1px;
  cursor: pointer;
}
.block_r2 .tab{
  width: 138px;
  height: 24px;

  text-align: center;
  font-size: 16px;
  font-weight: bold;
  padding: 2px 0 0 7px;
  color: #3578de;
  border-left: 6px solid #064a8b;
  text-align: left;
  line-height: 18px;
}
.block_r2 .tab.selected{
  border-left: 6px solid #0090ff;
  color: #fff;
}

.block_r2 .tab1{
  left: 0;
}
.block_r2 .tab2{
  left: 111px;
}
.block_r2 .row1{
  display: block;
  height: 78px;
}
.block_r2 .blk1{
  position: absolute;
  top: 15px;
  width: 150px;
}


/* --- */
#map_legend{
  position: absolute;
  left: 952px;
  top: 572px;
  width: 111px;
  height: 129px;
  background: url(../img/a3.2.gridlegend.png?2) no-repeat 0 0;
}
#map_legend .tit{
  position: absolute;
  width: 100%;
  top: 36px;
  text-align: center;
  font-weight: bold;
}
#map_legend .itm{
  position: absolute;
  width: 100px;
  left: 41px;
}
#map_legend .l1{
  top: 62px;
}
#map_legend .l2{
  top: 79px;
}




/* --- */

#popover_map {
  position: absolute;
  z-index: 888;
  width: 298px;
  height: 298px;

  top:  -1000px;

  background: url(../img/a3.2.pop_map.png) no-repeat 0 0;

  -moz-transform-origin: 50% 50%;
  -wekkit-transform-origin: 50% 50%;
  -ms-transform-origin: 50% 50%;
  transform-origin: 50% 50%;

  opacity: 0;
}


#popover_map .title{
  position: absolute;
  width: 100%;
  height: 50px;
  top: 34px;
  text-align: center;
  color: #92ebf6;
}
#popover_map .blk {
  display: block;
  line-height: 18px;
}
#popover_map .bigtt {
  line-height: 14px;
}
#popover_map .col1{
  position: absolute;
  top: 85px;
  left: 30px;
  width: 170px;
  height: 160px;

}
#popover_map .col2{
  position: absolute;
  top: 85px;
  left: 160px;
  width: 160px;
  height: 160px;
}





/* --- */
#popover_chart {
  position: absolute;
  z-index: 999;
  width: 640px;
  height: 320px;
  top: 270px;
  left: 362px;
  border: 1px solid #237fd5;
  background: #083476;
  display: none;
  pointer-events: auto;
}

#popover_chart .tit{
  position: absolute;
  width: 100%;
  height: 55px;
  background: #123e92;
  color: #7cc3ff;
  font-size: 24px;
  line-height: 50px;
  text-align: center;
  font-weight: bold;
}

#popover_chart .btnx{
  position: absolute;
  width: 30px;
  height: 30px;
  top: 0;
  right: 0;
  
  border-left:  1px solid #237fd5;
  border-bottom:  1px solid #237fd5;
  background: #083476 url(../img/pop_x.png) no-repeat center center;
  pointer-events: auto;
  cursor: pointer;
}
#popover_chart .legends{
  position: absolute;
  width: 100%;
  height: 20px;
  bottom: 14px;
  font-size: 14px;
  color: #91c9ff;
  text-align: center;
}
#popover_chart .legend{
  display: inline-block;
  margin:  0 10px;
}
#popover_chart .legend1{
  padding-left: 15px;
  background: #083476 url(../img/a2.1.legend1.png) no-repeat 0 center;
}
#popover_chart .legend2{
  padding-left: 30px;
  background: #083476 url(../img/a3.2.legend2.png) no-repeat 0 center;
}
#popover_chart .chartblock{
  position: absolute;
  top: 80px;
}



/* --- */
#popover_trend_chart {
  position: absolute;
  z-index: 1999;
  width: 380px;
  height: 228px;
  border: 1px solid #237fd5;
  background: #083476;
  display: none;
  pointer-events: auto;
}

#popover_trend_chart .tit{
  position: absolute;
  width: 100%;
  height: 30px;
  background: #123e92;
  color: #7cc3ff;
  font-size: 16px;
  line-height: 30px;
  text-align: center;
  font-weight: bold;
}

#popover_trend_chart .btnx{
  position: absolute;
  width: 30px;
  height: 30px;
  top: 0;
  right: 0;
  
  background: url(../img/pop_x.png) no-repeat center center;
  pointer-events: auto;
  cursor: pointer;
}
#popover_trend_chart .chartblock{
  position: absolute;
  top: 30px;
}


/* --- */


.btn_view_chart {
  position: absolute;
  width: 184px;
  height: 27px;
  cursor: pointer;
  text-align: center;
  line-height: 24px;
  font-size: 12px;
  color: #fff;
  pointer-events: auto;
  
  background: -moz-linear-gradient(top,  #0ddef5 0%, #1b59b3 6%, #134491 91%, #002559 100%); /* FF3.6-15 */
  background: -webkit-linear-gradient(top,  #0ddef5 0%,#1b59b3 6%,#134491 91%,#002559 100%); /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom,  #0ddef5 0%,#1b59b3 6%,#134491 91%,#002559 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#0ddef5', endColorstr='#002559',GradientType=0 ); /* IE6-9 */

  border-radius: 5px;

  -moz-box-shadow:0px 1px 3px #000f1e; -webkit-box-shadow:0px 1px 3px #000f1e; box-shadow:0px 1px 3px #000f1e;

}

.btn_view_chart .inside {
  position: absolute;
  width: 100%;
  height: 100%;
  padding-left: 8px;

  border-radius: 4px;

  border: 1px solid rgba(38,178,247,0.5);

}

.btn_view_chart:hover .inside {
  background: #1954b0;
}
.btn_view_chart:active .inside {
  background: #153970;
}

.btn_view_chart span {
  padding: 0 5px 0 14px;
  background: url(../img/a2.1.ico3.png) no-repeat 0 3px;
}



.ranking {
  position: absolute;
  width: 500px;
  height: 50px;
  bottom: 30px;
  font-size: 12px;
}
.ranking1 {
  left: 320px;
}
.ranking2 {
  left: 705px;
}

.ranking .tit{
  position: absolute;
  font-size: 14px;
  font-weight: bold;
  padding: 2px 0 0 7px;
  color: #fff;
  border-left: 6px solid #0090ff;
  text-align: left;
  line-height: 16px;
}
.ranking .lst{
  position: absolute;
  top: 22px;
}
.ranking .itm{
  display: inline-block;
  color: #59acff;
  line-height: 18px;
  padding-right: 10px;
}
.ranking .itm .no{
  display: block;
  color: #fff;
}
.ranking .itm .nm{
  display: block;
  color: #59acff;
}


#selected_mapmarkpoint{
  position: absolute;
  width: 36px;
  height: 36px;
  background: url(../img/mapmarkpoint_L.svg) no-repeat center center;
  background-size: 36px 36px;
}