(function () {
    dfd.done(function () {
        if (!hasAllCompanyPermission()) {
            $(".keyongyunli .detail").remove();
            return;
        }
    })

    $(".keyongyunli .detail").on("click", function () {
        $("#pop_capacity").removeClass("hide");
        $(".windowMask").removeClass("hide");
        if (window.capacityWin == null) {
            initCapacityWin();
        } else {
            window.capacityWin.onTabChange(true);
        }
    });


    function initCapacityWin() {
        var page = new Vue({
            el: '.capacity-win-body',
            template: $("#capacity_template").html(),
            data: function () {
                return {
                    "dateType": "D",
                    "weeks": weeks,
                    queryDate1: null,
                    queryDate2: null,
                    showWeekList: false,
                    selectedWeek: null,
                    weekCmpActive: false,
                    showFirstTab: true,
                    activeCompanyCode: '',
                    activeActype: '',
                    data: {},
                    data1: [],
                    wideList: [],
                    narrowList: [],
                    rList: []
                }
            },
            mounted: function () {
                var me = this;
                this.queryData(this.getDate());
                me.hackEChartDom();
            },
            computed: {
                companyStyle() {
                    let maxWith = Math.ceil(this.data1.length / 2) * 190;
                    return `max-width:${maxWith}px`;
                },
            },
            methods: {
                getCompanyCodes() {
                    var list = [];
                    for (let index = 0; index < companylist.length; index++) {
                        const element = companylist[index];
                        list.push(element.code)

                    }
                    return list;

                },
                hackEChartDom() {
                    var me = this;
                    var scale = 1 / pageZoomScale;
                    $(me.$el).find(".chartblock").css('zoom', scale);
                    $(me.$el).find(".chartblock").css('-moz-transform', 'scale(' + scale + ',' + scale + ')');
                    $(me.$el).find(".chartblock").css('-moz-transform-origin', 'left top');
                    $(me.$el).find(".chartblock").each(function (index, el) {
                        var width = $(this).width();
                        var height = $(this).height();
                        $(this).css('width', width * pageZoomScale + 'px');
                        $(this).css('height', 400 * pageZoomScale + 'px');
                    });
                },
                getStyle(companyCode) {
                    return `background: url(./img/logo_${companyCode}.svg) no-repeat right center;background-size: 24px;`
                },
                getDate() {
                    return moment().startOf("date")._d;
                    // var me = this;
                    // var dateType = this.dateType;
                    // if (dateType == 'D') {
                    //     return $(me.$refs['datetimepicker_D']).data("DateTimePicker").date().startOf("day")._d;
                    // } else if (dateType == 'L') {
                    //     return this.selectedWeek;
                    // } else if (dateType == 'M') {
                    //     return $(me.$refs['datetimepicker_M']).data("DateTimePicker").date().startOf("day")._d
                    // } else if (dateType == 'Y') {
                    //     return $(me.$refs['datetimepicker_Y']).data("DateTimePicker").date().startOf("day")._d
                    // }
                },
                getWeekDesc() {
                    return this.selectedWeek ? this.selectedWeek.DATE_DESC_XS : ''
                },
                onWeekMouseOut() {
                    this.weekCmpActive = false;
                    setTimeout(this.validActive, 300)
                },
                onWeekMouseOver() {
                    this.weekCmpActive = true;
                },
                validActive() {
                    if (!this.weekCmpActive) {
                        this.showWeekList = false;
                    }
                },
                dropWeekList() {
                    this.showWeekList = true;
                },
                onWeekChange(week) {
                    this.selectedWeek = week;
                    this.showWeekList = false;
                    this.queryData(week);

                },
                getWeekLabel: function (item) {
                    if (item) {
                        var week = item.DATE_ID.substring(5, 7);
                        var year = item.DATE_ID.substring(0, 4);
                        return `${year}年第${week}周`;
                    }
                },
                closeWin: function () {
                    $("#pop_capacity").addClass("hide");
                    $(".windowMask").addClass("hide");
                },
                switchDateType(dateType) {
                    this.dateType = dateType;
                    this.queryData(this.getDate());
                },
                onTabChange(showFirstTab) {
                    this.showFirstTab = showFirstTab;
                    //queryDate2,queryDate1有值,表示已经加载了,如果日期变了,要刷新
                    if (!this.showFirstTab) {
                        //加载了就不重复加载了
                        if (!this.isSameDate(this.queryDate2, this.getDate())) {
                            this.queryDataByAcType(this.getDate());
                        }
                    } else {
                        if (!this.isSameDate(this.queryDate1, this.getDate())) {
                            this.queryData(this.getDate())
                        }

                    }
                },
                isSameDate(d1, d2) {
                    if (!d1) {
                        return false;
                    }
                    return moment(d1).diff(moment(d2)) == 0;
                },
                queryDataByAcType(date) {
                    var me = this;

                    eking.ui.loading.show();
                    var param = {
                        // "companyCodes": me.getCompanyCodes(), //需要过滤掉的机型,
                    }

                    $.ajax({
                        type: 'post',
                        url: "/bi/spring/aircraft/queryYunliStatGroupByAcType",
                        mainGroup: "acType",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(param),
                        success: function (res) {
                            eking.ui.loading.hide();
                            var data = res.data;


                            var wideList = [];
                            var narrowList = [];
                            var rList = [];
                            data.forEach((item, index) => {
                                if (item.acTypeType == '宽体机') {
                                    wideList.push(item);
                                } else if (item.acTypeType == '窄体机') {
                                    narrowList.push(item);
                                } else if (item.acTypeType == '支线机') {
                                    rList.push(item);
                                }
                            })

                            me.wideList = wideList;
                            me.narrowList = narrowList;
                            me.rList = rList;
                            $(me.$refs.ecDom2).width($(me.$refs['tabContainer']).width());
                            me.hackEChartDom();
                            me.queryCompanyStat(me.wideList[0].actype);
                            me.queryDate2 = date;
                        }
                    });
                },

                queryData(date) {
                    var me = this;

                    eking.ui.loading.show();
                    var param = {
                        // "companyCodes": me.getCompanyCodes(), //需要过滤掉的机型,
                    }

                    $.ajax({
                        type: 'post',
                        url: "/bi/spring/aircraft/queryYunliStatGroupByCompany",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(param),
                        success: function (res) {
                            eking.ui.loading.hide();
                            console.log(res);
                            me.data = res.data;
                            var data1 = [];

                            me.data.forEach((item, index) => {
                                data1.push({
                                    companyCode: item.company,
                                    companyName: item.compName,
                                    data: item
                                })
                            })

                            data1.sort(function (a, b) {
                                return getCompanyIndex(a.companyCode) - getCompanyIndex(b.companyCode)
                            });
                            me.data1 = data1;
                            var width = Math.ceil(me.data1.length / 2) * 190;
                            $(me.$refs.ecDom).width(width);
                            me.hackEChartDom();
                            me.queryAcTypeStat(me.data1[0].companyCode);
                            me.queryDate1 = date;
                        }
                    });
                },
                queryAcTypeStat(companyCode) {
                    let me = this;

                    $.ajax({
                        type: 'get',
                        url: `/bi/spring/aircraft/queryCompanyYunliGroupByAcType?company=${companyCode}`,
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        success: function (res) {
                            eking.ui.loading.hide();
                            me.drawBar(res.data, companyCode)

                        }
                    });

                },
                queryCompanyStat(acType) {
                    let me = this;
                    var param = {
                        acType: acType
                        // "companyCodes": me.getCompanyCodes(), //需要过滤掉的机型,
                    }
                    $.ajax({
                        type: 'post',
                        url: `/bi/spring/aircraft/queryActypeYunliGroupByCompany`,
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(param),
                        success: function (res) {
                            eking.ui.loading.hide();


                            me.drawCompanyBar(res.data, acType)

                        }
                    });

                },
                drawBar(listData, companyCode) {
                    this.activeCompanyCode = companyCode;
                    var list = [];
                    list = listData;
                    list.sort(function (a, b) {
                        return a.acType = b.acType;
                    });
                    var colors = [
                        ['#00c311', '#007892'], //柱状图渐变颜色
                        ['#ff2929', '#b23e5c'], //柱状图渐变颜色
                    ];
                    let chart = echarts.init(this.$refs.ecDom, null, { renderer: 'svg' });
                    chart.setOption({
                        dataset: [
                            { source: list }
                        ],
                        legend: {
                            data: ['在册运力', '可用运力'],
                            textStyle: {
                                color: '#44a3f4',
                                fontSize: getChartFontSize(12),
                            },
                            top: getChartFontSize(10)
                        },
                        grid: {
                            left: '5%', // 与容器左侧的距离
                            right: '5%', // 与容器右侧的距离
                            top: '20%', // 与容器顶部的距离
                            bottom: '5%', // 与容器底部的距离,
                            containLabel: true
                        },
                        tooltip: {
                            trigger: "axis",
                            axisPointer: {
                                type: "cross",
                                crossStyle: {
                                    color: "#00a2ff"
                                }
                            }
                        },
                        yAxis: [{
                            type: 'value',
                            name: '',
                            nameTextStyle: {
                                color: '#41a8ff',
                                fontSize: getChartFontSize(16)
                            },
                            axisLabel: {
                                textStyle: {
                                    color: '#41a8ff',
                                    fontSize: getChartFontSize(16),
                                },
                                formatter: '{value}'
                            },
                            axisLine: {
                                lineStyle: {
                                    color: 'rgba(0,0,0,0)'
                                }
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                                }
                            },
                        }],
                        xAxis: [
                            {
                                type: "category",
                                axisLabel: {
                                    color: "#00a2ff",
                                    fontSize: getChartFontSize(16)
                                },
                                color: "#00a2ff",
                                axisLine: {
                                    lineStyle: {
                                        color: "#00a2ff",
                                        width: "2"
                                    }
                                }
                            }
                        ],
                        series: [
                            {
                                type: "bar",
                                barWidth: getChartFontSize(20),
                                label: {
                                    show: true,
                                    color: '#FFFFFF',
                                    position: 'top',
                                    fontSize: getChartFontSize(12)
                                },
                                dimensions: ["actype", "allNum"],
                                name: "在册运力",
                                datasetIndex: 0,

                                itemStyle: {
                                    normal: {
                                        color: new echarts.graphic.LinearGradient(
                                            0, 0, 0, 1,       //4个参数用于配置渐变色的起止位置, 这4个参数依次对应右/下/左/上四个方位. 而0 0 0 1则代表渐变色从正上方开始
                                            [
                                                { offset: 0, color: '#6cbbff' },
                                                { offset: 1, color: '#4448f4' }
                                            ]                //数组, 用于配置颜色的渐变过程. 每一项为一个对象, 包含offset和color两个参数. offset的范围是0 ~ 1, 用于表示位置
                                        )
                                    }
                                }
                            },
                            {
                                type: "bar",
                                barWidth: getChartFontSize(20),
                                label: {
                                    show: true,
                                    color: '#FFFFFF',
                                    fontSize: getChartFontSize(12),
                                    position: 'top'
                                },
                                dimensions: ["actype", "canUseNum"],
                                name: "可用运力",
                                datasetIndex: 0,
                                itemStyle: {
                                    normal: {
                                        color: new echarts.graphic.LinearGradient(
                                            0, 0, 0, 1, [{
                                                offset: 0,
                                                color: '#36d57f'
                                            }, {
                                                offset: 1,
                                                color: '#187b45'
                                            }]),
                                    }
                                }

                            }
                        ]
                    });
                    chart.resize();
                },
                drawCompanyBar(listData, actype) {
                    this.activeActype = actype;
                    var list = listData;
                    list.sort(function (a, b) {
                        return getCompanyIndex(a.company) - getCompanyIndex(b.company)
                    });

                    var colors = [
                        ['#00c311', '#007892'], //柱状图渐变颜色
                        ['#ff2929', '#b23e5c'], //柱状图渐变颜色
                    ];
                    let chart = echarts.init(this.$refs.ecDom2, null, { renderer: 'svg' });
                    chart.setOption({
                        dataset: [
                            { source: list }
                        ],
                        legend: {
                            textStyle: {
                                color: "#00a2ff",
                                fontSize: getChartFontSize(16)
                            },
                            top: 10,
                            data: ['在册运力', '可用运力'],
                        },
                        grid: {
                            left: '5%', // 与容器左侧的距离
                            right: '5%', // 与容器右侧的距离
                            top: '20%', // 与容器顶部的距离
                            bottom: '5%', // 与容器底部的距离
                            containLabel: true
                        },
                        tooltip: {
                            trigger: "axis",
                            axisPointer: {
                                type: "cross",
                                crossStyle: {
                                    color: "#00a2ff"
                                }
                            }
                        },
                        yAxis: [{
                            type: 'value',
                            name: '',
                            nameTextStyle: {
                                color: '#41a8ff',
                                fontSize: getChartFontSize(16)
                            },
                            axisLabel: {
                                textStyle: {
                                    color: '#41a8ff',
                                    fontSize: getChartFontSize(16)
                                },
                                formatter: '{value}'
                            },
                            axisLine: {
                                lineStyle: {
                                    color: 'rgba(0,0,0,0)'
                                }
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                                }
                            },
                        }],
                        xAxis: [
                            {
                                type: "category",
                                axisLabel: {
                                    color: "#00a2ff",
                                    fontSize: getChartFontSize(16)
                                },
                                color: "#00a2ff",
                                axisLine: {
                                    lineStyle: {
                                        color: "#00a2ff",
                                        width: "2"
                                    }
                                }
                            }
                        ],
                        series: [
                            {
                                type: "bar",
                                barWidth: 20,
                                label: {
                                    show: true,
                                    color: '#FFFFFF',
                                    position: 'top'
                                },
                                dimensions: ["compShortName", "allNum"],
                                name: "在册运力",
                                datasetIndex: 0,

                                itemStyle: {
                                    normal: {
                                        color: new echarts.graphic.LinearGradient(
                                            0, 0, 0, 1,       //4个参数用于配置渐变色的起止位置, 这4个参数依次对应右/下/左/上四个方位. 而0 0 0 1则代表渐变色从正上方开始
                                            [
                                                { offset: 0, color: '#6cbbff' },
                                                { offset: 1, color: '#4448f4' }
                                            ]                //数组, 用于配置颜色的渐变过程. 每一项为一个对象, 包含offset和color两个参数. offset的范围是0 ~ 1, 用于表示位置
                                        )
                                    }
                                }
                            },
                            {
                                type: "bar",
                                barWidth: 20,
                                label: {
                                    show: true,
                                    color: '#FFFFFF',
                                    position: 'top'
                                },
                                dimensions: ["compShortName", "canUseNum"],
                                name: "可用运力",
                                datasetIndex: 0,
                                itemStyle: {
                                    normal: {
                                        color: new echarts.graphic.LinearGradient(
                                            0, 0, 0, 1, [{
                                                offset: 0,
                                                color: '#36d57f'
                                            }, {
                                                offset: 1,
                                                color: '#187b45'
                                            }]),
                                    }
                                }

                            }
                        ]
                    });
                    chart.resize();
                }

            }
        });

        window.capacityWin = page;

    }





})()