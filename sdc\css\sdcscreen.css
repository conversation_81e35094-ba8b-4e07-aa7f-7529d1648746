.page-bgimg {
	position: absolute;
	top: 0px;
	width: 2997px;
	height: 1035px;
	background: url(../img/screen_bg.png) no-repeat 0 0;
}

.page-wrapper {
	position: absolute;
	top: 0px;
	width: 2997px;
	height: 1035px;
	overflow-x: hidden;
	overflow-y: hidden;
	pointer-events: none;
}


/* head */
.pagetitle {
	position: absolute;
	left: 1080px;
	top: 50px;
	width: 850px;
	font-size: 48px;
	letter-spacing: 5px;
	font-weight: bold;
	color: #FFFFFF;
}

/* col-left */

#map_left,
#map_mid,
#map_right {
	top: 250px;
	position: absolute;
	width: 750px;
	height: 750px;
	left: 200px;
	top:100px;
	pointer-events: auto;
}

#map_mid {
	left: 910px;
	width: 1139px;
}

#map_right {
	left: 2180px;
}

.mp_left_btm {
	width: 360px;
	position: absolute;
	top: 900px;
	left: 350px;
}

.mp_left_btm .col_1 {
	width: 36px;
	height: 36px;
	background: url(../img/screen_col_1_plan.png) no-repeat 0 0;
}

.mp_left_btm .col_2 {
	position: relative;
	width: 150px;
	left: 45px;
	top: -45px;
}

.mp_left_btm .col_3 .leg_G,
.mp_left_btm .col_3 .leg_Y {
	position: relative;
	top: -90px;
	left: 225PX;
	width: 20px;
	height: 6px;
	background: #2BCA44;
}

.mp_left_btm .col_3 .leg_Y {
	top: -68px;
	background: #FFE400;
}

.mp_left_btm .col_4 .tt1,
.mp_left_btm .col_4 .tt2 {
	position: relative;
	top: -108px;
	left: 258px;
	width: 50px;
}

.mp_left_btm .col_4 .tt2 {
	top: -102px;
}


.mp_mid_btm,
.map_right_btm {
	position: absolute;
	width: 300px;
	height: 20px;
	left: 1400px;
	top: 900px;
}

.mp_mid_btm .tt {
	margin: 0 20px 0 10px;
}

.mp_mid_btm .screen_icon_b,
.mp_mid_btm .screen_icon_g {
	display: inline-block;
	width: 16px;
	height: 20px;
	background: url(../img/screen_icon_b.png) no-repeat 0 0;
}

.mp_mid_btm .screen_icon_g {
	background: url(../img/screen_icon_g.png) no-repeat 0 0;
}


.map_right_btm {
	left: 2450px;
}

.map_right_btm .tt {
	margin: 0 20px 0 10px;
}

.map_right_btm .screen_icon_rg,
.map_right_btm .screen_icon_rb{
	display: inline-block;
	width: 20px;
	height: 20px;
	background: url(../img/screen_icon_rg.png) no-repeat 0 0;
}
.map_right_btm .screen_icon_rb{
	background: url(../img/screen_icon_rb.png) no-repeat 0 0;
}




/* footer */
.pageFooter {
	position: absolute;
	width: 1050px;
	font-size: 28px;
	letter-spacing: 6px;
	top: 985px;
	left: 1111px;
	color: #d70c19;
}
