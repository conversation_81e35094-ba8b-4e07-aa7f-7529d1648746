showLoading();

var comp_code = 'HNAHK';

var area_arps;
var arp_code_list;
var all_flight_list;
var timeout_next_flt;
var fltIntList;
var fltDelay4List;
var flightInfoList;
var acTypeList = {};
var actypeId2Code;
var actypeCodeList = [];

var marquee_itv;
var marquee_itv2;
var marquee_itv3;

// 所有飞机架数
var total_plane = -1;
// 执行中飞机架数
var exe_total_plane = -1;

var ac_type_list = ['787', '767', '330', '737', '320', '321', '319', '350', '145', '190'];

var ac_type_list2 = {
    '787': 27,
    '767': 1,
    '330': 43,
    '737': 247,
    '320': 134,
    //'321':43, 
    '319': 30,
    '145': 11,
    '190': 67
};
var ac_data_list_ready;
var ac_data_list_total; // 每种机型 总运力
var ac_data_list; // 每种机型 可用运力
var exe_ac_data_list;

var actypelist = '787,767,330,333,737,320,321,319,145,190,350';



var kpi_list = [
    'FUELT', //吨公里耗油 
    //'MOD_FUELT', //修正吨公里耗油
];

var kpi_id_list = {
    'FUELT': '10126', //吨公里耗油 
    //'MOD_FUELT':'10127', //修正吨公里耗油
};

var all_company_data;
var all_company_data_ac; // 飞机日利用率 计算指标
var all_company_ac_use; // 日利用率
var all_comp_actype_data;



// 可显示的历史 数量
var query_limit = 1;
// 日期类型
var date_type = 'D';

var actype_list = ['A319', 'A320', 'A321', 'A330', 'B737', 'B767', 'B787', 'E190', 'A350'];



// 页面标题
function getPageTitle() {
    var param = {
        'ID': 'WEB_AOC_COMPNAME',
    }

    $.ajax({
        type: 'post',
        url: "/bi/web/querydatalist",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
            var val = response.data[0].name;
            $('.pagecompname').html(val);
            document.title = val + '运行管理平台';
        },
        error: function(jqXHR, txtStatus, errorThrown) {

        }
    });

}

getPageTitle();


//-------------------------
var stdEndUtcTime
var stdStartUtcTime

function loadAll() {

    if (companylist.length == 0) {
        setTimeout(loadAll, 0);
        return;
    }

    compdelayCause = {};

    var len = companylist.length;
    var codelist = [];
    var codelist_no_parent = [];
    for (var i = 0; i < len; i++) {
        
        var dat = companylist[i];
        codelist.push(dat.code);
        if (dat.code != parent_company) {
            codelist_no_parent.push(dat.code);
        }
    }



    var date = new Date();
    var mm = date.getMonth() + 1;
    var dd = date.getDate();
    if (mm < 10) {
        mm = '0' + mm;
    }
    if (dd < 10) {
        dd = '0' + dd;
    }

    stdEndUtcTime = date.getFullYear() + '-' + mm + '-' + dd + ' 15:59:59';
    var yesterday_ts = date.getTime() - 86400000;
    date.setTime(yesterday_ts);
    mm = date.getMonth() + 1;
    dd = date.getDate();
    if (mm < 10) {
        mm = '0' + mm;
    }
    if (dd < 10) {
        dd = '0' + dd;
    }
    stdStartUtcTime = date.getFullYear() + '-' + mm + '-' + dd + ' 16:00:00';



    // ------------------------------------------------------------------------
    // 航班列表
    // ------------------------------------------------------------------------

    // 所有公司的航班

    var all_fltnos = [];

    // 开始结束时间
    var date = new Date();
    var mm = date.getMonth() + 1;
    var dd = date.getDate();
    if (mm < 10) {
        mm = '0' + mm;
    }
    if (dd < 10) {
        dd = '0' + dd;
    }
    var stdStart = date.getFullYear() + '-' + mm + '-' + dd + ' 00:00:00';
    var stdEnd = date.getFullYear() + '-' + mm + '-' + dd + ' 23:59:59';

    // 延误4小时航班获取
    var param = {
        "stdStartUtcTime": stdStartUtcTime,
        "stdEndUtcTime": stdEndUtcTime,
        "detailType": 'pfdtc4', // 延误4小时航班
        "company": codelist.join(","),
        "fltType": "L" //国内航班

    };
    $.ajax({
        type: 'post',
        url: "/bi/web/getFocLegsStaticDetails",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
            var list = response.data;
            fltDelay4List = [];
            for (var i = list.length - 1; i >= 0; i--) {
                fltDelay4List.push(list[i].data);
            }
        },
        error: function() {}
    });


    var statusMap = {
        'ARR': '落地',
        'NDR': '落地',
        'ATD': '推出',
        'ATA': '到达',
        'CNL': '取消',
        'DEL': '延误',
        'DEP': '起飞',
        'RTR': '返航',
        'SCH': '计划'
    };

    var param = {
        "stdStart": stdStart,
        "stdEnd": stdEnd,
        "acOwner": '',
        "statusList": 'DEP', // 只返回起飞的，表示在空中
    }
    $.ajax({
        type: 'post',
        url: "/bi/web/getStandardFocFlightInfo",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
            var list = response.data;
            all_flight_list = list;

            flightInfoList = {};
            fltIntList = [];
            //                fltDelay4List = [];

            for (var i = list.length - 1; i >= 0; i--) {
                var obj = list[i];
                flightInfoList[obj.flightNo] = obj;

                // 国际航班
                if (obj.fltType == 'I') {
                    fltIntList.push(obj);
                }

                // 延误4小时航班
                //                    var delaytime = Number(obj.dur1)+Number(obj.dur2)+Number(obj.dur3)+Number(obj.dur4);
                //                    if(delaytime/60 > 4){
                //                        fltDelay4List.push(obj);
                //                    }
            }



        },
        error: function() {}
    });



    // ------------------------------------------------------------------------
    // 各种航班统计信息。。。。
    // ------------------------------------------------------------------------
    var plane_over_air = -1;


    var param = {
        "stdStartUtcTime": stdStartUtcTime,
        "stdEndUtcTime": stdEndUtcTime,
        "companyCodes": codelist_no_parent.join(','),
        "AcTypeList": "",
        "depstns": "",
        "arrstns": ""
    }

    $.ajax({
        type: 'post',
        url: "/bi/redis/7x2_flt_sts",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {


            var sch_total = Number(response.pftc); //计划航班总数
            var sch_normal = Number(response.pfrtc); //计划航班中正常航班总数
            var pfPercent = Number(response.pfdappPercent); //计划航班中的正常航班比率
            var cftc = Number(response.cftc); //已执行航班总数

            $('#today_normal_rate').text(Math.round(pfPercent * 10) / 10);
            // todayNormalRate(pfPercent);

            $('#sch_total').text(sch_total);
            $('#cftc').text(Math.round(cftc / sch_total * 10000) / 100);

            // drawHalfCircle('cvs_flt_num_exec', cftc / sch_total * 100, ['#0aa654', '#67c895']);


            var dftc = response.dftc; //备降航班总数
            var bftc = response.bftc; //返航航班总数
            var qftc = response.qftc; //取消航班总数


            var pfdtc = response.pfdtc; //计划航班中延误航班总数

            var pfdtc1 = response.pfdtc1; //计划航班中延误1小时内的航班总数
            var ffdtc1 = response.ffdtc1; //执行航班中延误1小时内的航班总数
            var rfdtc1 = response.pfdtc1; //未执行航班中延误1小时内的航班总数
            var cfdtc1 = response.cfdtc1; //已执行航班中延误1小时内的航班总数

            var pfdtc12 = response.pfdtc12; //计划航班中延误1~2小时的航班总数
            var ffdtc12 = response.ffdtc12; //执行航班中延误1~2小时的航班总数
            var rfdtc12 = response.pfdtc12; //未执行航班中延误1~2小时的航班总数
            var cfdtc12 = response.cfdtc12; //已执行航班中延误1~2小时的航班总数

            var pfdtc24 = response.pfdtc24; //计划航班中延误2-4小时的航班总数
            var ffdtc24 = response.ffdtc24; //执行航班中延误2-4小时的航班总数
            var rfdtc24 = response.pfdtc24; //未执行航班中延误2-4小时的航班总数
            var cfdtc24 = response.cfdtc24; //已执行航班中延误2-4小时的航班总数

            var pfdtc4 = response.pfdtc4; //计划航班中延误>4小时的航班总数
            var ffdtc4 = response.ffdtc4; //执行航班中延误>4小时的航班总数
            var rfdtc4 = response.pfdtc4; //未执行航班中延误>4小时的航班总数
            var cfdtc4 = response.cfdtc4; //已执行航班中延误>4小时的航班总数

            // 国际航班延误数量
            var pfdtci = Number(response.pfdtci); //计划航班中延误国际航班总数
            var ffdtci = Number(response.ffdtci); //执行航班中延误国际航班总数


            //空中飞机架数
            //plane_over_air = Number(response.facnc);

            //setPlaneOverAirOnGround(plane_over_air);
            //

            var pftci = Number(response.pftci); //国际计划航班总数
            var pftcl = Number(response.pftcl); //国内计划航班总数
            var cftci = Number(response.cftci); //国际已执行航班总数
            var cftcl = Number(response.cftcl); //国内已执行航班总数

            // $('#val_flt_total_china_sch').text(pftcl);
            // var exe_rate = cftcl / pftcl;
            // $('#val_flt_exec_rate_china').text(Math.round(exe_rate * 1000) / 10 + '%');
            // setCircleChart('cvs_flt_l_num_exec', exe_rate, '#06a552');
            //
            // $('#val_flt_total_int_sch').text(pftci);
            // var exe_rate = cftci / pftci;
            // $('#val_flt_exec_rate_int').text(Math.round(exe_rate * 1000) / 10 + '%');
            // setCircleChart('cvs_flt_i_num_exec', exe_rate, '#06a552');


        },
        error: function() {}
    });



    // ------------------------------------------------------------------------
    // 燃效，
    // ------------------------------------------------------------------------



    var loadingInProgress = 0;

    all_company_data = {};

    // 本期
    loadingInProgress++;
    var param = {
        'SOLR_CODE': 'FAC_COMP_KPI',
        'COMP_CODE': codelist.join(','),
        'KPI_CODE': kpi_list.join(','),
        'VALUE_TYPE': 'kpi_value_d', //本期
        'DATE_TYPE': 'L',
        "OPTIMIZE": 1,
        'LIMIT': query_limit
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            if (response.data != undefined) {

                all_company_data['kpi_value_d'] = response.data;

                loadingInProgress--;
                checkDataReady();

            }

        },
        error: function() {}
    });


    // 同比
    loadingInProgress++;
    var param = {
        'SOLR_CODE': 'FAC_COMP_KPI',
        'COMP_CODE': codelist.join(','),
        'KPI_CODE': kpi_list.join(','),
        'VALUE_TYPE': 'kpi_ratio_tq_d', //同比
        'DATE_TYPE': 'L',
        "OPTIMIZE": 1,
        'LIMIT': query_limit
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            if (response.data != undefined) {
                all_company_data['kpi_ratio_tq_d'] = response.data;
                loadingInProgress--;
                checkDataReady();
            }

        },
        error: function() {}
    });



    // 分公司 机型
    loadingInProgress++;
    var param = {
        "SOLR_CODE": "FAC_COMP_ACTYPE_CODE_KPI",
        "COMP_CODE": codelist.join(','),
        "KPI_CODE": kpi_list.join(','),
        "ACTYPE": actype_list.join(','),
        "VALUE_TYPE": "kpi_value_d",
        "DATE_TYPE": 'L',
        'OPTIMIZE': 1,
        "LIMIT": query_limit
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getaccodekpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            if (response.data != undefined) {
                all_comp_actype_data = response.data;
                loadingInProgress--;
                checkDataReady();
            }

        },
        error: function() {}
    });



    // ------------------------------------------------------------------------
    // 日利用率
    // ------------------------------------------------------------------------
    loadingInProgress++;
    var param = {
        'SOLR_CODE': 'FAC_COMP_ACTYPE_KPI',
        'COMP_CODE': codelist.join(','),
        'KPI_CODE': 'FLY_TIME,AC_NUM', // 飞行时间、飞机架次 == 用来计算飞机日利用率
        'VALUE_TYPE': 'kpi_value_d', //本期
        'DATE_TYPE': 'D',
        'ACTYPE': actypelist,
        "OPTIMIZE": 1,
        'LIMIT': 1
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getackpi?kpi=FLY_TIME,AC_NUM",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            if (response.data != undefined) {
                // 设置机型数量
                function setAcData() {

                    if (actypeId2Code == undefined) {
                        setTimeout(setAcData, 0);
                        return;
                    }

                    var formatedData = {};
                    all_company_ac_use = {};

                    // 统计所有机型总和的 FLY_TIME AC_NUM 指标
                    for (var compcode in response.data) {
                        for (var kpicode in response.data[compcode]) {
                            var aclist = response.data[compcode][kpicode][date_type]['data3'];

                            if (formatedData[compcode] == undefined) {
                                formatedData[compcode] = {};
                            }
                            if (formatedData[compcode][kpicode] == undefined) {
                                formatedData[compcode][kpicode] = {};
                            }
                            if (formatedData[compcode][kpicode][date_type] == undefined) {
                                formatedData[compcode][kpicode][date_type] = {};
                            }

                            //
                            if (all_company_ac_use[compcode] == undefined) {
                                all_company_ac_use[compcode] = {};
                            }
                            if (all_company_ac_use[compcode][kpicode] == undefined) {
                                all_company_ac_use[compcode][kpicode] = {};
                            }
                            if (all_company_ac_use[compcode][kpicode][date_type] == undefined) {
                                all_company_ac_use[compcode][kpicode][date_type] = {};
                            }

                            var len = aclist.length;
                            for (var i = 0; i < len; i++) {
                                var acdat = aclist[i];
                                var acid = acdat.actype;
                                var ac = actypeId2Code[acid];
                                // 只统计 宽体机,窄体机,支线机 类型的飞机，其它类型不统计
                                if (ac_list_except_other_type.indexOf(ac) > -1) {
                                    var ddd = acdat.date;
                                    var len2 = ddd.length;

                                    if (all_company_ac_use[compcode][kpicode][date_type][ac] == undefined) {
                                        all_company_ac_use[compcode][kpicode][date_type][ac] = 0;
                                    }

                                    for (var j = 0; j < len2; j++) {
                                        var obj = ddd[j];
                                        var date = obj.date;
                                        var val = obj.value;
                                        if (formatedData[compcode][kpicode][date_type][date] == undefined) {
                                            formatedData[compcode][kpicode][date_type][date] = 0;
                                        }
                                        if (!isNaN(val)) {
                                            formatedData[compcode][kpicode][date_type][date] += Number(val);

                                            all_company_ac_use[compcode][kpicode][date_type][ac] = val
                                        }
                                    }
                                }
                            }

                        }

                    }

                    if (all_company_data_ac == undefined) {
                        all_company_data_ac = {};
                    }
                    if (all_company_data_ac[date_type] == undefined) {
                        all_company_data_ac[date_type] = {};
                    }
                    all_company_data_ac[date_type]['kpi_value_d'] = formatedData;

                    loadingInProgress--;
                    checkDataReady();
                }

                setAcData()



            }

        },
        error: function() {

        }
    });



    // ------------------------------------------------------------------------
    // 计划旅客+已完成
    // ------------------------------------------------------------------------
    var psrTotal; //旅客订票人数
    var psrExec; //旅客值机人数

    var psrIntPlan; //国际旅客订票人数
    var psrIntCki; //国际旅客值机人数

    var psrLntPlan; //国内旅客订票人数
    var psrLntCki; //国内旅客值机人数

    var param = {
        "companyCodes": codelist_no_parent.join(','),
        "stdStartUtcTime": stdStartUtcTime,
        "stdEndUtcTime": stdEndUtcTime,
        "detailType": "pftc", //统计航班（总计）
        //"psrType":"all"
    }

    $.ajax({
        type: 'post',
        url: "/bi/web/getPsrSummInfo",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
            psrTotal = response;

            setTrvNum();
        },
        error: function() {}
    });


    var param = {
        "companyCodes": codelist_no_parent.join(','),
        "stdStartUtcTime": stdStartUtcTime,
        "stdEndUtcTime": stdEndUtcTime,
        "detailType": "cftc", //已执行
        //"psrType":"all"
    }

    $.ajax({
        type: 'post',
        url: "/bi/web/getPsrSummInfo",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
            psrExec = response;

            setTrvNum();
        },
        error: function() {}
    });

    // 国内旅客 总计
    var param = {
        "companyCodes": codelist_no_parent.join(','),
        "stdStartUtcTime": stdStartUtcTime,
        "stdEndUtcTime": stdEndUtcTime,
        "detailType": "pftc", //统计航班（总计）
        // "psrStatus": "0", //0:订座,1:值机
        // "psrType": "all", //all:所有,vip:VIP,cip:CIP,m6: M6+,in:中转转入,jdw:W 舱旅客
        "fltType": "L", //L: 国内 Local; I: 国际 International
        //"inGroup":"in", //集团内或者集团外(针对中转旅客). 默认值为 in;in:集团内 out:集团外
    }

    $.ajax({
        type: 'post',
        url: "/bi/web/BI2GetPsrSummInfoByFocServlet", //实际调用这个DOC接口 getPsrSummInfoByFoc
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
            var lst = response.data;
            psrLntPlan = 0;
            for (var i in lst) {
                var d = lst[i];
                if (!isNaN(d.planNum)) {
                    psrLntPlan += Number(d.planNum);
                }
            }

            setTrvNum();
        },
        error: function() {}
    });

    // 国内旅客 已执行
    var param = {
        "companyCodes": codelist_no_parent.join(','),
        "stdStartUtcTime": stdStartUtcTime,
        "stdEndUtcTime": stdEndUtcTime,
        "detailType": "cftc", //已执行
        // "psrStatus": "1", //0:订座,1:值机
        // "psrType": "all", //all:所有,vip:VIP,cip:CIP,m6: M6+,in:中转转入,jdw:W 舱旅客
        "fltType": "L", //L: 国内 Local; I: 国际 International
        //"inGroup":"in", //集团内或者集团外(针对中转旅客). 默认值为 in;in:集团内 out:集团外
    }

    $.ajax({
        type: 'post',
        url: "/bi/web/BI2GetPsrSummInfoByFocServlet", //实际调用这个DOC接口 getPsrSummInfoByFoc
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
            var lst = response.data;
            psrLntCki = 0;

            for (var i in lst) {
                var d = lst[i];
                if (!isNaN(d.ckiNum)) {
                    psrLntCki += Number(d.ckiNum);
                }
            }

            setTrvNum();
        },
        error: function() {}
    });

    // 国际旅客 总计
    var param = {
        "companyCodes": codelist_no_parent.join(','),
        "stdStartUtcTime": stdStartUtcTime,
        "stdEndUtcTime": stdEndUtcTime,
        "detailType": "pftc", //统计航班（总计）
        // "psrStatus": "0", //0:订座,1:值机
        // "psrType": "all", //all:所有,vip:VIP,cip:CIP,m6: M6+,in:中转转入,jdw:W 舱旅客
        "fltType": "I", //L: 国内 Local; I: 国际 International
        //"inGroup":"in", //集团内或者集团外(针对中转旅客). 默认值为 in;in:集团内 out:集团外
    }

    $.ajax({
        type: 'post',
        url: "/bi/web/BI2GetPsrSummInfoByFocServlet", //实际调用这个DOC接口 getPsrSummInfoByFoc
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
            var lst = response.data;
            psrIntPlan = 0;

            for (var i in lst) {
                var d = lst[i];
                if (!isNaN(d.planNum)) {
                    psrIntPlan += Number(d.planNum);
                }
            }

            setTrvNum2();
        },
        error: function() {}
    });

    // 国际旅客 已执行
    var param = {
        "companyCodes": codelist_no_parent.join(','),
        "stdStartUtcTime": stdStartUtcTime,
        "stdEndUtcTime": stdEndUtcTime,
        "detailType": "cftc", //已执行
        // "psrStatus": "1", //0:订座,1:值机
        // "psrType": "all", //all:所有,vip:VIP,cip:CIP,m6: M6+,in:中转转入,jdw:W 舱旅客
        "fltType": "I", //L: 国内 Local; I: 国际 International
        //"inGroup":"in", //集团内或者集团外(针对中转旅客). 默认值为 in;in:集团内 out:集团外
    }

    $.ajax({
        type: 'post',
        url: "/bi/web/BI2GetPsrSummInfoByFocServlet", //实际调用这个DOC接口 getPsrSummInfoByFoc
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
            var lst = response.data;
            psrIntCki = 0;

            for (var i in lst) {
                var d = lst[i];
                if (!isNaN(d.ckiNum)) {
                    psrIntCki += Number(d.ckiNum);
                }
            }

            setTrvNum2();
        },
        error: function() {}
    });

    function setTrvNum() {
        // if (psrTotal && psrExec) {
        //     var planNum = Number(psrTotal.planNum); //旅客订票人数
        //     var ckiNum = Number(psrExec.ckiNum); //旅客值机人数

        //     $('#val_trv_num_plan').text(planNum);
        //     var rate = planNum > 0 ? Math.round(ckiNum / planNum * 10000) / 100 : 0;
        //     $('#val_trv_num_completed_rate').text(rate);

        //     drawHalfCircle('cvs_trv_num_exec', rate, ['#5d9afe', '#e3f1ba']);


        //     var zz_total = Number(psrTotal.inNum) + Number(psrTotal.outNum);
        //     var zz_exec = Number(psrExec.ckiInNum) + Number(psrExec.ckiOutNum);
        //     $('#val_trv_num_plan_zz').text(zz_total);
        //     var rate = zz_total > 0 ? Math.round(zz_exec / zz_total * 1000) / 10 : 0;
        //     $('#val_trv_zz_completed_rate').text(rate + '%');

        //     setCircleChart('cvs_trv_zz_num_exec', rate / 100, '#9bd8f1');

        // }

        if (psrTotal && psrExec) {
            var planNum = Number(psrTotal.planNum); //旅客订票人数
            var ckiNum = Number(psrExec.ckiNum); //旅客值机人数
            $('#val_trv_num_plan').text(planNum);
            var rate = planNum > 0 ? Math.round(ckiNum / planNum * 10000) / 100 : 0;
            $('#val_trv_num_completed_rate').text(rate);
            // drawHalfCircle('cvs_trv_num_exec', rate, ['#5d9afe', '#e3f1ba']);
        }

        // if (psrLntPlan && psrLntCki > 0) {
        //     $('#val_trv_num_plan_lnt').text(psrLntPlan);
        //     var rate = psrLntPlan > 0 ? Math.round(psrLntCki / psrLntPlan * 1000) / 10 : 0;
        //     $('#val_trv_lnt_completed_rate').text(rate + '%');
        //     setCircleChart('cvs_trv_l_num_exec', rate / 100, '#9bd8f1');
        // }
    }


    function setTrvNum2() {
        if (psrIntPlan && psrIntCki) {
            $('#val_trv_num_plan_int').text(psrIntPlan);
            var rate = psrIntPlan > 0 ? Math.round(psrIntCki / psrIntPlan * 1000) / 10 : 0;
            $('#val_trv_int_completed_rate').text(rate + '%');

            // setCircleChart('cvs_trv_i_num_exec', rate / 100, '#9bd8f1');

            if (psrTotal && psrExec) {
                var planNum = Number(psrTotal.planNum); //旅客订票人数
                var ckiNum = Number(psrExec.ckiNum); //旅客值机人数
                var psrLntPlanT = planNum-psrIntPlan;
                var psrLntCkit = ckiNum-psrIntCki;
                $('#val_trv_num_plan_lnt').text(psrLntPlanT);
                var rate = psrLntPlanT > 0 ? Math.round(psrLntCkit / psrLntPlanT * 1000) / 10 : 0;
                $('#val_trv_lnt_completed_rate').text(rate + '%');
                // setCircleChart('cvs_trv_l_num_exec', rate / 100, '#9bd8f1');
            }
        }
    }



    // ------------------------------------------------------------------------
    // 运力分布 - 可用运力
    // ------------------------------------------------------------------------
    /*
    日运力相关逻辑和处理要点如下 
     
    一：日运力总数调用接口
     
            1.1 请求处理
            String acTypeFilter = "D00,D10";//需要过滤掉的机型
            ApiRequest req = new ApiRequest();
            req.setOption("acOwners", StringUtils.join(airCodeSet));
            req.setOption("acTypeNotIn", acTypeFilter);
            PageParam pageParam = new PageParam();
            pageParam.setPageIndex(1);
            pageParam.setPageSize(MyUtil.pageLimit);//2000
            req.setPageParam(pageParam);
            ApiResponse res = acTypeApi.getPdcAcReg(req);
     
       1.2 结果集处理
     
          金鹏结果集要过滤掉货舱F0的数据
           if ("Y8".equals(airCode) && "F0".equals(cabin)) {
                    continue;
                }
          获取所有的短机号：acreg
     
       有多少个短机号，就有多少架总运力；通过“acOwner”来进行航司分组，可以获取不同航司的总运力。
     
     
    二：日可用运力
       2.1 请求处理
         shortNos不依据不同的航司的机号列表作为参数（这个参数来自计算总运力的接口返回进行加工的）
     
            ApiRequest req = new ApiRequest();
            req.setOption("mntUtcStart", startUtcTime);//北京时间某日 00:00:00转换类UTC时间
            req.setOption("mntUtcEnd", endUtcTime); //北京时间某日 23:59:59转换类UTC时间
            req.setOption("acregs", shortNos);//短机号列表List<String>
            boolean isToday = this.isToday(fltDate);
            if (isToday) {
                String nowStr = DateUtil.getDefaultNowStr();
                req.setOption("nowInMntUtcRange", MyUtil.convertBJ2OhterTime(nowStr, "0000")); // 日区分为当日和非当日，当日要传这个参数，当前时间转为UTC时间
            }
            PageParam param = new PageParam();
            param.setPageIndex(1);
            param.setPageSize(MyUtil.pageLimit);//2000
            req.setPageParam(param);
            ApiResponse res = focMaintApi.getFocMaintInfoByListByPage(req);
     
    2.2    停场计算规则
     
    2.2.1当日：按实时情况查询的逻辑（实时查询当日情况：以实时为准，查询的时间点若有飞机处于计划或非计划停场，则统计计划或非计划停场架次为1；否则，为0；举例：某飞机0700-1200处于非计划停场，1000查询该飞机则统计为非计划停场架次1，1300查询则统计为非计划停场架次0。）
    2.2. 2 非当日的天：当蓝色逻辑（每日0600-2400期间，计划或非计划停场时间小于9小时，计划或非计划停场架次统计为0；9（含）-12小时统计为0.5；大于等于12小时，统计为1。可用运力=总运力-停场。）
    移动取的还是北就时间00：00：00至23：59：59，然后转为utc时间
    2.2.3  接口输出：
    2.2.3.1运力要去重：一架飞机有多个停场计划,取最晚依据结束时间(tEnd)
     
    2.2.3.2 停场类型判断：String type = (String) dataMap.get("distinctcol");//1计划 2非计划
     
    2.3 可用运力=总运力-停场运力

    */

    // ------------------------------------------------------------------------
    var len = companylist.length;
    var codelist = [];
    for (var i = 0; i < len; i++) {
        
        var dat = companylist[i];
        if (dat.code != parent_company) {
            codelist.push(dat.code);
        }
    }

    var comp_codes = codelist.join(',');

    var url = `/bi/spring/aircraft/getAircrafCnt?company=`;
    $.ajax({
        type: 'get',
        url: url,
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        success: function (response) {
            var data = response.data;
            $('#total_plane_num').text(data);
            $('#planeNum').text(data);
           
        },
        error: function () { }
    });

    
    var url2 = `/bi/spring/aircraft/getDepCnt?company=`;
    $.ajax({
        type: 'get',
        url: url2,
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        success: function (response) {
            var data = response.data;
            $('#plane_over_air').text(data);
           
        },
        error: function () { }
    });


    var getYunli = function() {
        var param = {
            "stdStartUtcTime": stdStartUtcTime,
            "stdEndUtcTime": stdEndUtcTime,
            "companyCodes": codelist_no_parent.join(','), //
            "AcTypeList": "",
            "depstns": "",
            "arrstns": ""
        }

        $.ajax({
            type: 'post',
            url: "/bi/redis/7x2_flt_sts",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function(response) {


                var fftc = Number(response.fftc); //执行航班总数

                // 执行中航班架数
                $('#exec_plane_num').text(fftc);


            },
            error: function() {

            }
        });
    }
    // ------------------------------------------------------------------------
    // 运力分布 - 空中机型架数
    // ------------------------------------------------------------------------

    var param = {
        'SOLR_CODE': 'FAC_COMP_ACTYPE_KPI',
        'COMP_CODE': comp_codes,
        'KPI_CODE': 'AC_NUM,EXE_AC_NUM', // 飞机架次, 执行中飞机架数
        'VALUE_TYPE': 'kpi_value_d', //本期
        'DATE_TYPE': 'D',
        'ACTYPE': 'ALL',
        "OPTIMIZE": 1,
        'LIMIT': 1
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getackpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            if (response.data != undefined) {
                var data = response.data;

                // 设置机型数量
                function setAcNum() {
                    if (actypeId2Code == undefined) {
                        setTimeout(setAcNum, 0);
                        return;
                    }

                    total_plane = 0;
                    exe_total_plane = 0;
                    //ac_data_list = {};
                    exe_ac_data_list = {};
                    var numofac = 0;

                    var lenk = companylist.length;
                    for (var kk = 0; kk < lenk; kk++) {
                        
                        var dat = companylist[kk];
                        var compcode = dat.code;
                        if (compcode != parent_company && data[compcode] != undefined) {
                            var kpiaclst1 = data[compcode]['AC_NUM']['D']['data3'];
                            var kpiaclst2 = data[compcode]['EXE_AC_NUM']['D']['data3'];

                            // 总飞机架数
                            var len = kpiaclst1.length;
                            for (var i = 0; i < len; i++) {
                                var acdat = kpiaclst1[i];
                                var acid = acdat.actype;
                                var accode = actypeId2Code[acid];
                                if (accode && accode != 'QITA') {
                                    var acdd = acdat.date;
                                    var len2 = acdd.length;
                                    // 每种机型的架数
                                    var acno = 0;
                                    for (var j = 0; j < len2; j++) {
                                        var dd = acdd[j];
                                        var val = isNaN(dd.value) ? 0 : Number(dd.value);
                                        total_plane += val;
                                        acno += val;
                                    }

                                    // 每种机型的架数
                                    if (ac_type_list.indexOf(accode) > -1) {
                                        acno = (isNaN(acno) || acno == 0) ? '-' : Math.round(acno);
                                        //if(ac_data_list[accode] == undefined){
                                        //    ac_data_list[accode] = 0;
                                        //}
                                        //ac_data_list[accode] += acno;

                                        numofac++;
                                    }
                                }
                            }


                            // 执行中飞机架数
                            var len = kpiaclst2.length;
                            for (var i = 0; i < len; i++) {
                                var acdat = kpiaclst2[i];
                                var acid = acdat.actype;
                                var accode = actypeId2Code[acid];
                                if (accode && accode != 'QITA') {
                                    var acdd = acdat.date;
                                    var len2 = acdd.length;
                                    // 每种机型的架数
                                    var acno = 0;
                                    for (var j = 0; j < len2; j++) {
                                        var dd = acdd[j];
                                        var val = isNaN(dd.value) ? 0 : Number(dd.value);
                                        if (ac_type_list.indexOf(accode) > -1) {
                                            exe_total_plane += val;
                                        }
                                        acno += val;
                                    }

                                    // 执行中 每种机型的架数
                                    if (ac_type_list.indexOf(accode) > -1) {
                                        acno = (isNaN(acno) || acno == 0) ? '-' : Math.round(acno);
                                        if (exe_ac_data_list[accode] == undefined) {
                                            exe_ac_data_list[accode] = 0;
                                        }
                                        exe_ac_data_list[accode] += acno;

                                    }
                                }
                            }


                        }

                    }

                    //
                    function setAcAirGround() {
                        if (!ac_data_list_ready) {
                            setTimeout(setAcAirGround, 0);
                            return;
                        }

                        //
                        var html = '';
                        var barwidth = 50;
                        console.log('ac_data_list', ac_data_list)
                        console.log('ac_data_list_total', ac_data_list_total)
                        for (var accode in ac_data_list_total) {
                            var numac = ac_data_list_total[accode];

                            //numac = ac_type_list2[accode] //2018.8.17

                            if (numac > 0) {
                                var numexeac = exe_ac_data_list[accode];
                                numexeac = isNaN(numexeac) ? 0 : numexeac;

                                var ac_ground = numac - numexeac;
                                ac_ground = Math.max(ac_ground, 0);

                                html += '<div class="baritmrow plane_ac_' + accode + '">';
                                html += '<span class="acno">' + accode + '</span>';
                                html += '<span class="val val_l blue1">' + numac + '</span>';
                                html += '<span class="bar darkbar">';
                                html += '<span class="bar_ac_air innerbar bluebar" style="width: ' + (numexeac / numac * barwidth) + 'px;"></span>';
                                html += '<span class="bar_ac_ground innerbar greenbar" style="width: ' + (ac_ground / numac * barwidth) + 'px; right:0px;"></span>';
                                html += '</span>';
                                html += '<span class="val val_r blue1" style="width:20px;">' + numexeac + '</span> ';
                                html += '</div>';
                            }


                        }


                        $('#air_aclist').html(html);

                    }

                    setAcAirGround();



                    total_plane = (isNaN(total_plane) || total_plane == 0) ? '-' : Math.round(total_plane);

                    // 在册飞机架数 总数//所有机型，包括宽体、窄体、支线、其它
                    // $('#total_plane_num').text(total_plane);

                    // 执行中
                    $('.block_tl .row2 .col1 .val').text(exe_total_plane);
                    // $('#plane_over_air').text(exe_total_plane);
                    // $('#exec_plane_num').text(exe_total_plane);
                    // 获取飞机总数
                    getYunli();

                    //$('#plane_on_ground').text(total_plane-exe_total_plane);

                    //setPlaneOverAirOnGround();



                }

                setAcNum();


            }

        },
        error: function() {}
    });



    function checkDataReady() {
        console.log('loadingInProgress', loadingInProgress);
        if (loadingInProgress == 0) {
            kpiDataReady = true;
            updateAllKpi();
            hideLoading();

        }
    }


    // 获取飞机位置
    getPlaneLocation();

    selectTab(currentSelectedTab);

    getCompNormal();
    setCompDataList();


}


// 获取所有机型
var ac_list_except_other_type = [];
var param = {}
$.ajax({
    type: 'post',
    url: "/bi/web/actypeall",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {

        if (response.actype) {
            var list = response.actype;
            actypeId2Code = {};

            list.sort(function(a, b) {
                return a.sort - b.sort
            });

            var len = list.length;
            for (var i = 0; i < len; i++) {
                var obj = list[i];
                actypeId2Code[obj.id] = obj.code;

                if (obj.actyptyp == 'Wide' || obj.actyptyp == 'Narrow' || obj.actyptyp == 'Regional') {
                    // 宽体机,窄体机,支线机
                    ac_list_except_other_type.push(obj.code);
                }
            }
        }

    },
    error: function() {}
});



// 获取机场列表
var arp_detail_list;

function getAirportList() {

    var param = {
        //"AIRPORT_CODE": arpcodes, // 可选，传入机场CODE
    }
    $.ajax({
        type: 'post',
        url: "/bi/web/airportdetail",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
            arp_detail_list = {};
            var list = response.airport;
            for (var i = list.length - 1; i >= 0; i--) {
                var arp = list[i];
                arp_detail_list[arp.code] = arp;
            }
        },
        error: function() {}
    });
}
getAirportList();



function updateAllKpi() {

    // 本周吨公里耗油
    var ddd = all_company_data['kpi_value_d'][parent_company]['FUELT']['L'];
    for (var week in ddd) {
        $('#week_FUELT').text(ddd[week]);
    }

    // 日利用率
    var acuserate = getAcUseRate(parent_company);
    $('#year_avg_val_use_time').text(Math.round(acuserate * 10) / 10);
    // drawRoundChart2(-1, -1, acuserate / 24);

}

function getAcUseRate(compcode) {

    // 日利用率
    var dd1 = all_company_data_ac[date_type]['kpi_value_d'][compcode]['FLY_TIME'][date_type];
    var dd2 = all_company_data_ac[date_type]['kpi_value_d'][compcode]['AC_NUM'][date_type];
    // // if (compcode == "FU") {
    // console.log(compcode)
    // console.log(dd1)
    // console.log(dd2)
    // // }
    for (var date in dd1) {
        var val1 = dd1[date];
        var val2 = dd2[date];

        // 飞机日利用率
        var rate1 = val2 > 0 ? (val1 / val2) : 0;

        return Number(rate1);
    }

    return 0;
}



// ------------------------------------------------------------------------
// 航班正常率
// ------------------------------------------------------------------------
function todayNormalRate(ratestr) {

    var ratestr = Math.round(ratestr * 10) / 10;
    $('#today_normal_rate').text(ratestr);

    var rate = ratestr / 100;

    var canvas = document.getElementById('cvs_normal_rate');
    var context = canvas.getContext('2d');
    context.clearRect(0, 0, canvas.width, canvas.height);
    var x = canvas.width / 2;
    var y = canvas.height / 2;

    var radius = 68;
    var lineWidth = 30;

    // draw back
    var startAngle = Math.PI - Math.PI / 3.6;
    var endAngle = startAngle + Math.PI + Math.PI / 3.6 * 2;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle, endAngle, counterClockwise);
    context.lineWidth = lineWidth;
    context.strokeStyle = '#00438B';
    context.stroke();

    context.beginPath();
    context.arc(x, y, 48, startAngle, endAngle, counterClockwise);
    context.lineWidth = 1.5;
    context.strokeStyle = '#06aaef';
    context.stroke();

    // draw overlay
    var startAngle2 = startAngle;
    var endAngle2 = startAngle + (endAngle - startAngle) * rate;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
    context.lineWidth = lineWidth;

    // linear gradient
    if (rate < 0.5) {
        var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
    } else if (rate < 0.8) {
        var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
    } else {
        var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, canvas.width / 2);
    }
    color.addColorStop(0, '#012962');

    if (rate < 0.7) {
        color.addColorStop(1, '#A1263E');
    } else if (rate < 0.8) {
        color.addColorStop(1, '#c29700');
    } else {
        color.addColorStop(1, '#40FF00');
    }

    context.strokeStyle = color;
    context.stroke();

    // draw head
    var startAngle2 = startAngle + (endAngle - startAngle) * rate - (endAngle - startAngle) * 0.01;
    var endAngle2 = startAngle + (endAngle - startAngle) * rate;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
    context.lineWidth = lineWidth;

    if (rate < 0.7) {
        context.strokeStyle = '#ff0000';
    } else if (rate < 0.8) {
        context.strokeStyle = '#ffc600';
    } else {
        context.strokeStyle = '#CCFF99';
    }

    context.stroke();
}


function drawRoundChart2(rate, rate2, rate3) {

    var canvas = document.getElementById('cvs_util_time');
    var context = canvas.getContext('2d');
    context.clearRect(0, 0, canvas.width, canvas.height);
    var x = canvas.width / 2;
    var y = canvas.height / 2;

    // draw back
    var radius = 60;
    var startAngle = -Math.PI / 2;
    var endAngle = startAngle + Math.PI * 2;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle, endAngle, counterClockwise);
    context.lineWidth = 7;
    context.strokeStyle = '#3694f6';
    context.stroke();



    // draw overlay 1 ==== 实际
    var radius = 44;
    var startAngle2 = startAngle;
    var endAngle2 = startAngle + (endAngle - startAngle) * rate;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
    context.lineWidth = 7;

    // linear gradient
    if (rate < 0.5) {
        var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
    } else if (rate < 0.8) {
        var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
    } else {
        var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, canvas.width / 2);
    }
    color.addColorStop(0, '#65498B');
    color.addColorStop(1, '#FFE801');

    context.strokeStyle = color;
    context.stroke();



    // draw overlay 2 ==== 计划
    var radius = 52;
    var startAngle2 = startAngle;
    var endAngle2 = startAngle + (endAngle - startAngle) * rate2;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
    context.lineWidth = 7;

    // linear gradient
    if (rate2 < 0.5) {
        var color = context.createLinearGradient(0, canvas.height, canvas.width * rate2, 0);
    } else if (rate2 < 0.8) {
        var color = context.createLinearGradient(0, canvas.height, canvas.width * rate2, 0);
    } else {
        var color = context.createLinearGradient(0, canvas.height, canvas.width * rate2, canvas.width / 2);
    }
    color.addColorStop(0, '#446295');
    color.addColorStop(1, '#93FF6E');

    context.strokeStyle = color;
    context.stroke();


    // draw overlay 3 ==== 平均
    var radius = 60;
    var startAngle2;
    var endAngle2;
    var counterClockwise = false;

    // linear gradient
    if (rate3 <= 0.5) {
        var color = context.createLinearGradient(canvas.width / 2, 0, canvas.width / 2, canvas.height * rate3);
        startAngle2 = startAngle;
        endAngle2 = startAngle + (endAngle - startAngle) * rate3;
    } else if (rate3 <= 0.75) {
        var color = context.createLinearGradient(canvas.width, canvas.height / 2, 0, canvas.height / 2);
        startAngle2 = startAngle + Math.PI / 2;
        endAngle2 = startAngle + (endAngle - startAngle) * rate3;
    } else {
        var color = context.createLinearGradient(canvas.width / 2, canvas.height, canvas.width / 2, 0);
        startAngle2 = startAngle + Math.PI;
        endAngle2 = startAngle + (endAngle - startAngle) * rate3;
    }

    color.addColorStop(0.2, '#3694f6');
    color.addColorStop(1, '#FFFFFF');

    context.beginPath();
    context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
    context.lineWidth = 7;
    context.strokeStyle = color;
    context.stroke();

    // pointer
    var angle = startAngle + (endAngle - startAngle) * (rate3);
    $('#cvs_util_time_pointer').css('transform', 'rotate(' + (angle / Math.PI * 180) + 'deg)');

}


function drawHalfCircle(id, ratestr, colors) {

    var ratestr = Math.round(ratestr * 10) / 10;

    var rate = ratestr / 100;

    var canvas = document.getElementById(id);
    var context = canvas.getContext('2d');
    context.clearRect(0, 0, canvas.width, canvas.height);
    var x = canvas.width / 2;
    var y = canvas.height;

    var radius = 90;
    var lineWidth = 36;

    // draw back
    var startAngle = Math.PI;
    var endAngle = startAngle + Math.PI;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle, endAngle, counterClockwise);
    context.lineWidth = lineWidth;
    context.strokeStyle = '#00438B';
    context.stroke();

    // draw overlay
    var startAngle2 = startAngle;
    var endAngle2 = startAngle + (endAngle - startAngle) * rate;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
    context.lineWidth = lineWidth;

    // linear gradient
    if (rate < 0.5) {
        var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
    } else if (rate < 0.8) {
        var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
    } else {
        var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, canvas.width / 2);
    }
    color.addColorStop(0, colors[0]);
    color.addColorStop(1, colors[1]);

    context.strokeStyle = color;
    context.stroke();


    // overlay
    context.beginPath();
    context.arc(x, y, 76, startAngle, endAngle, counterClockwise);
    context.lineWidth = 12;
    context.strokeStyle = 'rgba(0,0,0,0.18)';
    context.stroke();
}


function setCircleChart(id, rate, color) {

    var canvas = document.getElementById(id);
    var context = canvas.getContext('2d');
    context.clearRect(0, 0, canvas.width, canvas.height);
    var x = canvas.width / 2;
    var y = canvas.height / 2;

    var radius = 33;

    // draw back
    context.beginPath();
    context.arc(x, y, radius, 0, Math.PI * 2, true);
    context.lineWidth = 9;
    context.strokeStyle = '#2B4596';
    context.stroke();


    // top
    var startAngle = -Math.PI / 2;
    var endAngle = startAngle + Math.PI * 2 * rate;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle, endAngle, counterClockwise);
    context.lineWidth = 9;
    context.strokeStyle = color;
    context.stroke();

}


function todayNormalRate2(ratestr) {

    var ratestr = Math.round(ratestr * 10) / 10;
    $('#today_normal_rate2').text(ratestr);

    var rate = ratestr / 100;

    var canvas = document.getElementById('cvs_normal_rate2');
    var context = canvas.getContext('2d');
    context.clearRect(0, 0, canvas.width, canvas.height);
    var x = canvas.width / 2;
    var y = canvas.height / 2;

    var radius = 58;
    var lineWidth = 26;

    // draw back
    var startAngle = Math.PI - Math.PI / 3.6;
    var endAngle = startAngle + Math.PI + Math.PI / 3.6 * 2;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle, endAngle, counterClockwise);
    context.lineWidth = lineWidth;
    context.strokeStyle = '#00438B';
    context.stroke();

    context.beginPath();
    context.arc(x, y, 40, startAngle, endAngle, counterClockwise);
    context.lineWidth = 1.5;
    context.strokeStyle = '#06aaef';
    context.stroke();

    // draw overlay
    var startAngle2 = startAngle;
    var endAngle2 = startAngle + (endAngle - startAngle) * rate;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
    context.lineWidth = lineWidth;

    // linear gradient
    if (rate < 0.5) {
        var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
    } else if (rate < 0.8) {
        var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
    } else {
        var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, canvas.width / 2);
    }
    color.addColorStop(0, '#012962');

    if (rate < 0.7) {
        color.addColorStop(1, '#A1263E');
    } else if (rate < 0.8) {
        color.addColorStop(1, '#c29700');
    } else {
        color.addColorStop(1, '#40FF00');
    }

    context.strokeStyle = color;
    context.stroke();

    // draw head
    var startAngle2 = startAngle + (endAngle - startAngle) * rate - (endAngle - startAngle) * 0.01;
    var endAngle2 = startAngle + (endAngle - startAngle) * rate;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
    context.lineWidth = lineWidth;

    if (rate < 0.7) {
        context.strokeStyle = '#ff0000';
    } else if (rate < 0.8) {
        context.strokeStyle = '#ffc600';
    } else {
        context.strokeStyle = '#CCFF99';
    }

    context.stroke();
}


// ------------------------------------------------------------------------
// 延误原因 公司／非公司
// ------------------------------------------------------------------------

var compdelayCause;

function getCompDelayCause(compcode) {
    if (compdelayCause && compdelayCause[compcode]) {
        setCompDelayCause(compcode)
        return;
    }

    var date_type = "D";
    var param = {
        'SOLR_CODE': 'FAC_COMP_DELAY_CAUSE_RATE_KPI',
        'COMP_CODE': compcode,
        'KPI_CODE': 'DELAY_NO',
        'VALUE_TYPE': 'kpi_value_d',
        'DATE_TYPE': 'D',
        'LIMIT': '1',
        'OPTIMIZE': 1
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            checkLogin(response);

            if (response.data != undefined) {
                var data_dly = response.data[compcode]['DELAY_NO']['D'];

                compdelayCause[compcode] = data_dly;

                setCompDelayCause(compcode)

            }

        },
        error: function() {}
    });
}


function setCompDelayCause(compcode) {
    var data_dly = compdelayCause[compcode]

    var comp_total = 0;
    var none_total = 0;

    var causeKeyV = {};

    for (var time in data_dly) {
        var d = data_dly[time];
        causeKeyV = d;
    }

    // 公司原因 总数
    var cnt = 0;
    for (var time in data_dly) {
        var d = data_dly[time];
        var len = comp_cause.length;
        for (var i = 0; i < len; i++) {
            var causeName = comp_cause[i];
            //if(!isNaN(d[causeName])){
            var val = Number(d[causeName]);
            val = isNaN(val) ? 0 : val;
            comp_total += val;
            //}
        }
        break;
    }

    // 非公司原因 总数
    cnt = 0;
    for (var time in data_dly) {
        var d = data_dly[time];
        var len = none_cause.length;
        for (var i = 0; i < len; i++) {
            var causeName = none_cause[i];
            //if(!isNaN(d[causeName])){
            var val = Number(d[causeName]);
            if (causeName == "民航局航班时刻安排") {
                causeName = "时刻安排"
            }
            val = isNaN(val) ? 0 : val;
            none_total += val;
            //}
        }
        break;
    }



    // percent
    $('#per_delay_cause_comp').text(Math.round(comp_total / (comp_total + none_total) * 100) + '%');
    $('#per_delay_cause_none').text(Math.round(none_total / (comp_total + none_total) * 100) + '%');

    // chart
    var rate = comp_total / (comp_total + none_total);
    rate = Math.round(rate * 1000) / 10;
    var rate2 = 100 - rate;

    $('#comp_cause_rate').html(rate);
    $('#none_cause_rate').html(Math.round(rate2 * 100) / 100);
    $('#comp_cause_bar').css('width', rate + '%');

    setDelayCauseChart(causeKeyV);
}


function setDelayCauseChart(causeKeyV) {
    // ---------------------------
    var chart_id = 'chart_delay_cause';

    var colors = ['#e95551', '#f7a449', '#fff353', '#21b3ce', '#176ebf', '#0a3a89', '#8619db', '#aa44e1'];
    // ---------------------------


    var data_s1 = [];
    var data_legend = [];

    for (var key in causeKeyV) {
        var val = causeKeyV[key];
        if (val > 0) {
            data_s1.push({
                name: key,
                value: val
            });
            data_legend.push({
                name: key,
                icon: 'circle'
            });
        }


    }


    // ---------------------------------------------------------
    // 主KPI图表
    var chart = echarts.init(document.getElementById(chart_id));

    var option = {

        tooltip: {
            trigger: 'item',
            formatter: function(params, ticket, callback) {
                var html = '';
                html += '<div class="chart_tip">';
                html += '<div class="tit">' + params.name + '</div>';
                html += '<div class="r1"><span class="c1">占比</span><span class="c2">' + params.percent + '%</span></div>';
                html += '</div>';

                return html;
            },

        },
        legend: {
            orient: 'vertical',
            x: 18,
            y: 5,
            itemWidth: 8,
            itemHeight: 8,
            textStyle: {
                color: '#99ccff',
            },
            data: data_legend
        },
        color: colors,
        series: [{
            name: '',
            type: 'pie',
            radius: ['36%', '70%'],
            center: ['60%', '45%'],
            data: data_s1,
            //roseType: 'angle',
            clockwise: false,
            label: {
                normal: {
                    //formatter: '{b} {d}%',
                    formatter: '{d}%',
                    textStyle: {
                        fontSize: 11
                    }
                }
            },
            labelLine: {
                normal: {
                    smooth: 0.2,
                    length: 12,
                    length2: 8
                }
            },


            animationType: 'scale',
            animationEasing: 'elasticOut',
            animationDelay: function(idx) {
                return Math.random() * 200;
            }
        }]
    };
    chart.clear();
    chart.setOption(option);
}



function findFltInfo(fltno) {
    for (var i = all_flight_list.length - 1; i >= 0; i--) {
        var flt = all_flight_list[i];
        if (flt.flightNo == fltno) {
            return flt;
        }
    }
    return undefined;
}


var chart_earch;

function crate3DEarth() {
    chart_earch = echarts.init(document.getElementById('earth3d'));

    var option = {
        tooltip: {
            show: false
        },
        backgroundColor: 'rgba(0,0,0,0)',
        globe: {
            baseTexture: 'asset/earth.jpg',
            //heightTexture: '/asset/get/s/data-1491889019097-rJQYikcpl.jpg',

            displacementScale: 0.1,

            shading: 'lambert',

            //environment: 'rgba(0,0,0,0)',
            shading: 'realistic',
            light: {
                main: {
                    intensity: 0.3
                },
                ambient: {
                    intensity: 1.0
                },
            },

            viewControl: {
                autoRotate: false,
                zoomSensitivity: false,
                targetCoord: [115, 32]
            },

            layers: []
        },
        series: []
    }

    chart_earch.setOption(option);
}



// ------------------------------------------------------------------------
// 全球飞机位置
// ------------------------------------------------------------------------

var planeLocationList = []; //空中


function getPlaneLocation() {
    var param = {
        'mode': 'pos'
    }
    $.ajax({
        type: 'post',
        url: "/bi/web/flightMq",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            planeLocationList = [];
            var plist = {};

            //var list = response.data.data1;
            function processData(data1) {
                var lst = {};
                var len = data1.length;
                for (var i = 0; i < len; i++) {
                    var dd = data1[i];
                    var fi = dd.fi;
                    if (lst[fi] == undefined) {
                        lst[fi] = {
                            data: []
                        };
                        lst[fi]['data'].push(dd);
                    } else {
                        lst[fi]['data'].push(dd);
                    }
                }

                return lst;
            }

            var list = processData(response.data.data1);

            for (var fltno in list) {

                var fltobj = list[fltno];
                var itmx2 = fltobj.data;

                var itm;

                if (itmx2 && itmx2.length > 1) {
                    var itm1 = itmx2[0];
                    var itm2 = itmx2[1];


                    itm1.UTC = itm1.UTC.replace(' ', '');
                    itm2.UTC = itm2.UTC.replace(' ', '');

                    if (itm1.UTC > itm2.UTC) {
                        itm = itm1
                        itm.LON1 = itm2.LON;
                        itm.LAT1 = itm2.LAT;
                    } else if (itm1.UTC < itm2.UTC) {
                        itm = itm2
                        itm.LON1 = itm1.LON;
                        itm.LAT1 = itm1.LAT;
                    } else {

                        itm = itm2
                        itm.LON1 = itm1.LON;
                        itm.LAT1 = itm1.LAT;

                        console.log(fltno, '两组经纬度UTC相同');
                    }
                } else if (itmx2 && itmx2.length > 0) {
                    itm = itmx2[0];

                }


                if (itm) {

                    var alt = itm.ALT;
                    var cas = itm.CAS;
                    var vec;

                    var fltno = itm.fi;

                    if (comp_code == parent_company || fltno.indexOf(comp_code) == 0) {

                        var acno = itm.an;
                        acno = acno.replace('-', '');

                        var lon = formatLonLat(itm.LON);
                        var lon1 = formatLonLat(itm.LON1);
                        var lat = formatLonLat(itm.LAT);
                        var lat1 = formatLonLat(itm.LAT1);

                        if (isNaN(itm.LON)) {
                            vec = Number(itm.VEC);
                        }

                        var oil = isNaN(itm.OIL) ? '' : itm.OIL;

                        var pdat = {
                            fltno: fltno,
                            acno: acno,
                            alt: alt,
                            vec: vec,
                            lon: lon,
                            lat: lat,
                            lon1: lon1,
                            lat1: lat1,
                            oil: oil,
                        };

                        var code = acno + '-' + fltno;

                        /*
                        if(plist[code] == undefined){
                            plist[code] = pdat;
                        }else if(plist[code].lon1 == undefined){
                            plist[code].lon1 = pdat.lon;
                            plist[code].lat1 = pdat.lat;
                            if(oil > 0){
                                plist[code].oil = oil;
                            }
                        }else if(oil > 0){
                            plist[code].oil = oil;
                        }
                        */

                        if (pdat.vec == undefined) {
                            pdat.vec = getGeoAngle(pdat.lat, pdat.lon, pdat.lat1, pdat.lon1);
                        }
                        planeLocationList.push(pdat);
                    }
                }
            }

            /*
            for(var code in plist){
                var pdat = plist[code];
                //if(pdat.vec || pdat.lon1 != undefined){
                    if(pdat.vec == undefined){
                        pdat.vec = getGeoAngle(pdat.lat, pdat.lon, pdat.lat1, pdat.lon1);
                    }
                    planeLocationList.push(pdat);
                //}
            }
            */
            console.log('planeLocationList', planeLocationList);

            if (currentSelectedTab == 1) {
                setPlaneLocation()
            } else if (currentSelectedTab == 2) {
                setAirlines()
            }

        },
        error: function(jqXHR, txtStatus, errorThrown) {
            console.log('----error');
        }
    });
}

function setPlaneLocation() {

    if (all_flight_list == undefined || planeLocationList == undefined) {
        setTimeout(setPlaneLocation, 10);
        return;
    }

    var seriesData = [];


    for (var i = planeLocationList.length - 1; i >= 0; i--) {
        var itm = planeLocationList[i];
        var acno = itm.acno;
        var fltno = itm.fltno;

        var vec = itm.vec;
        var alt = itm.alt;

        var lon = itm.lon;
        var lat = itm.lat;

        var flt = findFltInfo(fltno);

        /*
	    黄色：延误 DEL
	    紫色：机务工作 ARR NDR ATA CNL
	    绿色：飞行中 DEP RTR
	    蓝色：未执行 SCH ATD

	    'ARR':'落地',
	    'NDR':'落地',
	    'ATD':'推出',
	    'ATA':'到达',
	    'CNL':'取消',
	    'DEL':'延误',
	    'DEP':'起飞',
	    'RTR':'返航',
	    'SCH':'计划'

	    */
        // 在飞的，空中的飞机
        if (flt && (alt > 0 || flt.status == 'DEP')) {
            var img = '';
            var delay_min = Number(flt.dur1) + Number(flt.dur2) + Number(flt.dur3) + Number(flt.dur4); //延误时间 分钟
            var color;
            var border;
            var svg = 'M90.7,4.8c-1.3-1.5-2.8-2.7-4.5-3.5C84.3,0.4,82.4,0,80.3,0l-0.4,0.1L79.5,0c-2,0-4,0.4-5.8,1.4c-1.8,0.8-3.3,2-4.6,3.5c-2.2,2.3-3.2,4.9-3.2,8l0,47.5L0.1,111.7L0,127.4l65.3-21.2l-0.1,38L39,167.2l0,7.9l3-0.8v1.6l37.9-10.9l37.9,10.9v-1.6l2.9,0.8l-0.1-7.9l-26.1-22.9l-0.1-38l65.4,21.2l-0.1-15.8L94,60.3l-0.1-47.5C93.9,9.8,92.9,7.1,90.7,4.8z';
            if (delay_min == 0) {
                color = "#69d0ff";
                border = "#000000";
            } else {
                color = "#fff60e";
                border = "#000000";
            }
            seriesData.push({
                name: fltno,
                acno: acno,
                value: [lon, lat, 0],
                //symbolRotate: -vec, //航向   正北是0°，顺时针，0到360°
                //symbol:'path://'+svg,
                itemStyle: {
                    color: color,
                    borderColor: border,
                    borderWidth: 1,
                }
            })
        }

    }

    var series = [];
    series.push({
        type: 'scatter3D',
        coordinateSystem: 'globe',
        symbolSize: 6,
        //blendMode: 'lighter',
        slient: true,
        label: {
            normal: {
                show: false
            },
            emphasis: {
                show: false
            }
        },
        data: seriesData
    });

    var option = {
        series: series
    }
    chart_earch.setOption(option);
}


// ------------------------------------------------------------------------
// 地球 航线
// ------------------------------------------------------------------------
function setAirlines() {

    if (arp_detail_list == undefined) {
        setTimeout(setAirlines, 10);
        return;
    }

    var seriesData = [];

    for (var fltno in flightInfoList) {
        flt = flightInfoList[fltno];

        var arp1 = arp_detail_list[flt.depStn];
        var arp2 = arp_detail_list[flt.arrStn];

        if (arp1 && arp2 && arp1.longitude && arp1.latitude && arp2.longitude && arp2.latitude) {
            var color;

            var statusMap = {
                'ARR': '落地',
                'NDR': '落地',
                'ATD': '推出',
                'ATA': '到达',
                'CNL': '取消',
                'DEL': '延误',
                'DEP': '起飞',
                'RTR': '返航',
                'SCH': '计划'
            };

            if (flt.status == 'DEL' || (flt.delay1 != '' && flt.dur1 > 0)) {
                color = '#fff663';
            } else if (flt.status == 'ARR' || flt.status == 'NDR' || flt.status == 'ATD' || flt.status == 'ATA' || flt.status == 'DEP') {
                color = '#0cff00';
            } else if (flt.status == 'CNL' || flt.status == 'RTR') {
                color = '#FF0000';
            } else { //SCH
                color = '#00c6ff';
            }

            seriesData.push({
                fltno: fltno,
                arrStn: flt.arrStn,
                depStn: flt.depStn,
                coords: [
                    [arp1.longitude, arp1.latitude],
                    [arp2.longitude, arp2.latitude]
                ],
                value: fltno,
                lineStyle: {
                    width: 1,
                    color: color,
                    opacity: 1
                },
            });
        }
    }


    // 航线的 tooltip
    var tooltip = {
        trigger: 'item',
        show: true,
        formatter: function(params, ticket, callback) {

            console.log('params', params);

            var data = params.data;
            var arp1 = arp_detail_list[data.arrStn];
            var arp2 = arp_detail_list[data.depStn];

            var fltno = data.fltno;
            var flt = flightInfoList[fltno];

            var city_name1 = arp1.city_name;
            var arp_name1 = arp1.chn_name;

            var city_name2 = arp2.city_name;
            var arp_name2 = arp2.chn_name;

            var name1 = arp_name1.indexOf(city_name1) > -1 ? arp_name1 : (city_name1 + arp_name1)
            var name2 = arp_name2.indexOf(city_name2) > -1 ? arp_name2 : (city_name2 + arp_name2)

            var html = '';

            //航班号、起飞城市-到达城市、航班状态（实际/预计/计划起飞时间-实际/预计/计划到达时间）、机号（机型）、客座率（布局）、实时位置、实施油量
            html += '航班号: ' + fltno + '<br>';
            html += city_name1 + ' - ' + city_name2 + '<br>';
            if (flt.status == 'DEP' || flt.status == 'ARR' || flt.status == 'NDR' || flt.status == 'ATA') {
                // 起飞,落地,到达
                // 实际起飞时间 atdChn
                html += '实际起飞时间: ' + trimTime(flt.atdChn) + '<br>';
            } else {
                // 预计出发
                html += '预计出发时间: ' + trimTime(flt.etdChn) + '<br>';
            }

            if (flt.status == 'ATA') {
                // 到达
                // 实际起飞时间 atdChn
                html += '实际到达时间: ' + trimTime(flt.ataChn) + '<br>';
            } else {
                // 预计到达
                html += '预计到达时间: ' + trimTime(flt.etaChn) + '<br>';
            }

            if (flt.delay1 != '' && flt.dur1 > 0) {
                html += '延误原因: ' + flt.delay1Name + '<br>';
                html += '延误时间: ' + (Number(flt.dur1) + Number(flt.dur2) + Number(flt.dur3) + Number(flt.dur4)) + '分钟<br>';
            }

            html += '机号: ' + data.acno + '(' + flt.acType + ')' + '<br>';
            html += '实时位置: ' + Math.round(data.value[0] * 10000) / 10000 + ', ' + Math.round(data.value[1] * 10000) / 10000 + '<br>';

            return html;
        },

        backgroundColor: '#021e55',
    };


    var series = [];
    series.push({
        type: 'lines3D',
        coordinateSystem: 'globe',

        //blendMode: 'lighter',

        effect: {
            show: true,
            period: 6,
            trailLength: 0.3,
            trailColor: '#fff',
            trailWidth: 1
        },

        silent: false,

        tooltip: tooltip,

        data: seriesData
    });

    var option = {
        series: series
    }

    chart_earch.setOption(option);

}


var comp_normal_rate_list;
var sts_loaded = 0;


function getCompNormal() {
    var len = companylist.length;
    sts_loaded = 0;
    for (var i = 0; i < len; i++) {
        
        var dat = companylist[i];
        if (dat.code != parent_company && dat.code != 'HNAIN') {
            sts_loaded++;
            getCompSts(dat.code)
        }
    }
}

function getCompSts(compcode) {
    var param = {
        "stdStartUtcTime": stdStartUtcTime,
        "stdEndUtcTime": stdEndUtcTime,
        "companyCodes": compcode,
        "AcTypeList": "",
        "depstns": "",
        "arrstns": ""
    }

    $.ajax({
        type: 'post',
        url: "/bi/redis/7x2_flt_sts",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            if (comp_normal_rate_list == undefined) {
                comp_normal_rate_list = {};
            }

            var pfdappPercent = Number(response.pfdappPercent); //计划航班中的正常航班比率
            comp_normal_rate_list[compcode] = pfdappPercent

            sts_loaded--;


        },
        error: function() {}
    });
}



function setNormalRateMap() {

    if (sts_loaded != 0) {
        setTimeout(setNormalRateMap, 10);
        return;
    }

    var html1 = '';
    var html2 = '';

    var len1 = 0;
    var len2 = 0;

    for (var compcode in comp_normal_rate_list) {
        var rate = comp_normal_rate_list[compcode];
        var color, lv;

        rate = Math.round(rate * 100) / 100;

        if (rate >= 80) {
            color = '#8ac507';
            lv = 9
        } else if (rate >= 70) {
            color = '#ffec18';
            lv = 1
        } else if (rate >= 60) {
            color = '#fb9503';
            lv = 3
        } else {
            color = '#f62525';
            lv = 2
        }

        if (rate >= 80) {

            html1 += '<div class="itm">'
            html1 += '<span class="fs12 blue1">' + companyCode2Name[compcode] + '</span><br>'
            html1 += '<span class="fs16" style="color:' + color + '">' + rate + '%</span>'
            html1 += '</div>'

            len1++;
        } else {
            html2 += '<div class="itm">'
            html2 += '<span class="fs12 blue1">' + companyCode2Name[compcode] + '</span><br>'
            html2 += '<span class="fs16" style="color:' + color + '">' + rate + '%</span>'
            html2 += '</div>'

            len2++;
        }


        $('#map_logo_' + compcode).css('background-image', 'url(../img/logo_normal_rate' + lv + '_' + compcode + '.png)');

    }

    html1 += '<div style="height:250px;"><div>'
    html2 += '<div style="height:250px;"><div>'

    $('#normal_rate_lst1a').html(html1);
    $('#normal_rate_lst2a').html(html2);

    //大于N条自动滚动
    if (len1 > 7) {
        var speed = 60;
        var sec = document.getElementById("normal_rate_lst1");
        var sec2 = document.getElementById("normal_rate_lst1b");
        var sec1 = document.getElementById("normal_rate_lst1a");
        sec2.innerHTML = sec1.innerHTML;

        function Marquee1() {
            if (sec2.offsetTop - sec.scrollTop <= 0)
                sec.scrollTop -= sec1.offsetHeight
            else {
                sec.scrollTop++
            }
        }
        clearInterval(marquee_itv2);
        marquee_itv2 = setInterval(Marquee1, speed);
        sec.onmouseover = function() {
            clearInterval(marquee_itv2)
        }
        sec.onmouseout = function() {
            marquee_itv2 = setInterval(Marquee, speed)
        }
    }

    //大于N条自动滚动
    if (len2 > 7) {
        var speed = 60;
        var sec = document.getElementById("normal_rate_lst2");
        var sec2 = document.getElementById("normal_rate_lst2b");
        var sec1 = document.getElementById("normal_rate_lst2a");
        sec2.innerHTML = sec1.innerHTML;

        function Marquee2() {
            if (sec2.offsetTop - sec.scrollTop <= 0)
                sec.scrollTop -= sec1.offsetHeight
            else {
                sec.scrollTop++
            }
        }
        clearInterval(marquee_itv3);
        marquee_itv3 = setInterval(Marquee2, speed);
        sec.onmouseover = function() {
            clearInterval(marquee_itv3)
        }
        sec.onmouseout = function() {
            marquee_itv3 = setInterval(Marquee, speed)
        }
    }

}



var currentSelectedTab = 1;
$('#col_mid .tab').on('click', function() {
    var id = $(this).attr('data-tab');
    currentSelectedTab = id;
    selectTab(id);
})
$('#col_left .content').on('click', function() {
    var id = $(this).attr('data-tab');
    currentSelectedTab = id;
    selectTab(id);
})


function selectTab(id) {
    $('#col_mid .tab').removeClass('selected');
    $('#col_mid .tab' + id).addClass('selected');

    $('#col_mid .tabc').hide();
    $('#col_mid .tabc' + id).show();

    if (id == 1) {
        $('#earth3d').css('pointer-events', 'auto');
        setPlaneLocation()
    } else if (id == 2) {
        $('#earth3d').css('pointer-events', 'auto');
        setAirlines()

        var html = '';
        for (var i in fltDelay4List) {
            var flt = fltDelay4List[i];
            html += flt.flightNo + '<br>'
        }
        $('#delay_flt_list').html(html);

        var html = '';
        for (var i in fltIntList) {
            var flt = fltIntList[i];
            html += flt.flightNo + '<br>'
        }
        html += '<div style="height:250px;"><div>'
        $('#int_flt_list1').html(html);

        //大于N条自动滚动
        if (fltIntList.length > 26) {
            var speed = 60;
            var sec = document.getElementById("int_flt_list");
            var sec2 = document.getElementById("int_flt_list2");
            var sec1 = document.getElementById("int_flt_list1");
            sec2.innerHTML = sec1.innerHTML;

            function Marquee() {
                if (sec2.offsetTop - sec.scrollTop <= 0)
                    sec.scrollTop -= sec1.offsetHeight
                else {
                    sec.scrollTop++
                }
            }
            clearInterval(marquee_itv);
            marquee_itv = setInterval(Marquee, speed);
            sec.onmouseover = function() {
                clearInterval(marquee_itv)
            }
            sec.onmouseout = function() {
                marquee_itv = setInterval(Marquee, speed)
            }
        }


    } else if (id == 3) {
        $('#earth3d').css('pointer-events', 'none');

        var series = [];
        series.push({
            type: 'scatter3D',
            coordinateSystem: 'globe',
            symbolSize: 6,
            //blendMode: 'lighter',
            slient: true,
            label: {
                show: false,
            },
            data: []
        });
        var option = {
            series: series
        }
        chart_earch.setOption(option);


        setNormalRateMap();

    }
}



var compDataListLength;

function setCompDataList() {
    if (sts_loaded != 0 || all_comp_actype_data == undefined 
        || all_company_ac_use == undefined) {
        setTimeout(setCompDataList, 0);
        return;
    }

    var html = '';

    var len = companylist.length;
    var cnt = 0;
    var hxLen = 0;
    var hxFuel = 0;
    for (var i = 0; i < len; i++) {
        
        var dat = companylist[i];
        var compcode = dat.code;
        if (compcode != parent_company && compcode!= 'HNAIN' ) {

            // 本周吨公里耗油
            // var ddd = all_company_data['kpi_ratio_tq_d'][compcode]['FUELT']['L'];
            // var fuel;
            // var fuel_arr;
            // if (ddd != undefined && "" != ddd) {
            //     for (var week in ddd) {
            //         fuel = ddd[week];
            //         if ("" == fuel) {
            //             fuel = 0;
            //         }
            //         break;
            //     }
            // } else {
            //     fuel = 0;
            // }
            // 大新华燃效，直接用控股的737燃效即可
            var fuel;
            var fuel_arr;
            if (compcode == "CN") {
                // fuel = getFuelt(["HU"]);
                fuel = getAcUseFueltOt(100100,'B737');
            } else if (compcode == "HX") {
                fuel = getFuelt(["HU", "JD", "GS", "8L", "PN", "FU", "GX", "UQ", "Y8", "9H"]);
                if (fuel != 0) {
                    fuel = (fuel / 11).toFixed(4);
                }
            } else {
                fuel = getFuelt([compcode]);
            }

            if (fuel < 0) {
                fuel_arr = "<span class='green'>↓</span>"
            } else if (fuel > 0) {
                fuel_arr = "<span class='red'>↑</span>"
            } else {
                fuel_arr = "";
            }

            // 日利用率
            var acuserate = 10;
            if (compcode == "CN") {
                // 大新华利用率，直接用控股的737利用率即可
                console.log("大新华利用率，直接用控股的737利用率即可")
                // acuserate = getAcUseRate("HU");
                // acuserate = Math.round(acuserate * 10) / 10;
                acuserate = getAcUseRateOt('HU','737');
            } else {
                acuserate = getAcUseRate(compcode);
                acuserate = Math.round(acuserate * 10) / 10;
            }

            var rate = comp_normal_rate_list[compcode];
            rate = Math.round(rate * 100) / 100;

            var alt = i % 2 == 1 ? 'alt' : '';

            html += "<div class='rw con_flex_row " + alt + "' id='complist_id" + cnt + "' data-code='" + compcode + "' data-id='" + cnt + "'  data-rate='" + rate + "' >";
            html += "<span class='c flex_none c1 blue1'>" + companyCode2Name[compcode] + "</span>";
            html += "<span class='c flex_none c2 center'>" + acuserate + "</span>";
            html += "<span class='c flex_none c3 center blue1'>" + fuel + fuel_arr + "</span>";
            html += "<span class='c flex_none c4 center'>" + rate + "%</span>";
            html += "</div>";

            cnt++;


        }
    }

    compDataListLength = cnt;


    $('#comp_table .list').html(html);

    $('#comp_table .list .rw').off('click');
    $('#comp_table .list .rw').on('click', function(evt) {
        var id = $(this).attr('data-id');
        clearTimeout(itv_select_comp);
        selectComp(Number(id));
    });

    selectComp(0);

    // clearTimeout(itv_select_comp);
    // itv_select_comp = setTimeout(selectComp, 5000, nextCompId);
}

function getAcUseRateOt(compcode, actype) {
    var ftm = all_company_ac_use[compcode]['FLY_TIME'][date_type][actype];
    var acnum = all_company_ac_use[compcode]['AC_NUM'][date_type][actype];
    if (isNaN(ftm)) {
        ftm = 0;
    }
    if (isNaN(acnum)) {
        acnum = 0;
    }
    var useh = acnum > 0 ? Math.round(ftm / acnum * 10) / 10 : 0;
    return useh;
}

function getAcUseFueltOt(compid, actype) {
    var fuelacs = all_comp_actype_data[100100][kpi_id_list['FUELT']]['L'];
    var fuel = 0;
    var lll = fuelacs[actype].date;
    for (var k = lll.length - 1; k >= 0; k--) {
        var o = lll[k];
        fuel = o.value;
    }
    return fuel;
}

function getFuelt(compcodes) {
    var fuel = 0;
    var len = compcodes.length;
    for (var i = 0; i < len; i++) {
        var compcode = compcodes[i];
        var ddd = all_company_data['kpi_ratio_tq_d'][compcode]['FUELT']['L'];
        var count;
        if (ddd != undefined && "" != ddd) {
            for (var week in ddd) {
                var count = ddd[week];
                if ("" == count) {
                    count = 0;
                }
                break;
            }
        } else {
            count = 0;
        }
        fuel += Number(count);
    }
    return fuel;
}

var itv_select_comp;

$('.compname').on("click", function(){
    if($(this).hasClass("href")) {
        window.location.href = '/largescreen/7x2/index.html?lang=cn&scale=auto';
    }
});

function selectComp(id) {
    clearTimeout(itv_select_comp);
    $('#comp_table .list .rw').removeClass('selected');
    $('#complist_id' + id).addClass('selected');
    $('#comp_table .selected_bg').css('top', (21 + id * 42) + 'px');
    $('#comp_table .selected_bg').show();

    var compcode = $('#complist_id' + id).attr('data-code');
    var rate = $('#complist_id' + id).attr('data-rate');

    $('.complogo').attr('src', '../img/logo_' + compcode + '.png');
    $('.compname').text(companyCode2Name[compcode]);
    if (compcode == "HU"){
        $('.compname').closest(".row1").css("cursor", "pointer");
        $('.compname').addClass("href");
    } else {
        $('.compname').closest(".row1").removeAttr("style");
        $('.compname').removeClass("href");
    }

    var compid;
    var len = companylist.length;
    for (var i = 0; i < len; i++) {
        
        var dat = companylist[i];
        if (compcode == dat.code) {
            compid = dat.id;
        }
    }

    // todayNormalRate2(rate);

    // getCompDelayCause(compcode);


    var html = '';

    // html += "<tr style='height:36px;'>";
    // html += "<th class='clr'>机型</th>";
    // html += "<th class='hc rb'>日利用率</th>";
    // html += "<th class='hc rb'>燃效</th>";
    // html += "<th class='hc' style='width:55px;'>架数</th>";
    // html += "</tr>";
    var dddd1;
    var dddd2;
    var fuelacs;
    // 大新华 日利用率、燃效用股份的
    if ("CN" == compcode) {
        dddd1 = all_company_ac_use["HU"]['FLY_TIME'][date_type];
        dddd2 = all_company_ac_use["HU"]['AC_NUM'][date_type];
        fuelacs = all_comp_actype_data[100100][kpi_id_list['FUELT']]['L'];
    } else {
        dddd1 = all_company_ac_use[compcode]['FLY_TIME'][date_type];
        dddd2 = all_company_ac_use[compcode]['AC_NUM'][date_type];
        fuelacs = all_comp_actype_data[compid][kpi_id_list['FUELT']]['L'];
    }
    // var dddd1 = all_company_ac_use[compcode]['FLY_TIME'][date_type];
    // var dddd2 = all_company_ac_use[compcode]['AC_NUM'][date_type];
    // var fuelacs = all_comp_actype_data[compid][kpi_id_list['FUELT']]['L'];

    for (var accode in dddd1) {
        var ftm = dddd1[accode];
        var acnum = dddd2[accode];
        var useh = acnum > 0 ? Math.round(ftm / acnum * 10) / 10 : '-';
        // 大新华只取股份737的数据
        if ("CN" == compcode) {
            if ("737" != accode) {
                continue;
            }
            acnum = 3; // 大新华737机型架数固定3
        }

        var fuel = '-';
        for (var ac in fuelacs) {
            if (ac.indexOf(accode) > -1) {
                var lll = fuelacs[ac].date;
                for (var k = lll.length - 1; k >= 0; k--) {
                    var o = lll[k];
                    fuel = o.value;
                }
                break;
            }
        }
        let pic = ['737','787','330','350'].indexOf(accode) > -1 ? accode+'.png' : 'plane_pic.png';
        // html += `<li style="width: 362px;height: 181px;  background: url('img/planebg.png?0') no-repeat left center;">
        //             <div class="ffnum fs18" style="position: relative; right: 12px; top: 10px;text-align: right;">${accode}</div>
        //             <div style="width: 281px;height: 74px;position: relative;left: 40px;background: url(img/${pic}?0) no-repeat left center;"></div>
        //             <div class="ffnum fs22" style="position: relative; width: 30%; top: 41px; left: 36px;">${Math.round(acnum)}<span class="fs14">架</span></div>
        //             <div class="ffnum fs22" style="position: relative; top: 10px; right: 48px; text-align: right;">${useh}<span class="fs14">小时</span></div>
        //         </li>`;
        html += '<li style="width: 362px;height: 181px;  background: url("img/planebg.png?0") no-repeat left center;">';
        html += '<div class="ffnum fs18" style="position: relative; right: 12px; top: 10px;text-align: right;">'+accode+'</div>';
        html += '<div style="width: 281px;height: 74px;position: relative;left: 40px;background: url(img/'+pic+'?0) no-repeat left center;"></div>';
        html += '<div class="ffnum fs22" style="position: relative; width: 30%; top: 41px; left: 36px;">'+Math.round(acnum)+'<span class="fs14">架</span></div>';
        html += '<div class="ffnum fs22" style="position: relative; top: 10px; right: 48px; text-align: right;">'+useh+'<span class="fs14">小时</span></div>';
        html += '</li>';
		

    }
    $('.scroll').find(".str_move_clone").remove();
    $('#comp_ac_table').html(html);
    $('.scroll').liMarquee({
        direction: 'up',
        runshort: false
    });
    if (id < compDataListLength - 1) {
        nextCompId = id + 1;
    } else {
        nextCompId = 0;
    }

    // itv_select_comp = setTimeout(selectComp, 10000, nextCompId);
}


// ------------------------------------------------------------------------
// 时钟
// ------------------------------------------------------------------------
function setTime() {
    var date = new Date();
    var timestamp = date.getTime();
    var timezoneOffset = date.getTimezoneOffset();
    var utc_timestamp = timestamp + timezoneOffset * 60 * 1000;
    let japan_date = new Date();
    japan_date.setTime(timestamp + 1 * 3600 * 1000)

    var utc_date = new Date();
    utc_date.setTime(utc_timestamp + 2 * 3600 * 1000);

    var sydney_date = new Date();
    sydney_date.setTime(utc_timestamp + 10 * 3600 * 1000);

    var newyork_date = new Date();
    newyork_date.setTime(utc_timestamp - 4 * 3600 * 1000);

    let russia_date = new Date();
    russia_date.setTime(timestamp - 5 * 3600 * 1000);

    $('#time_japan').text(formatNum(japan_date.getHours()) + ':' + formatNum(japan_date.getMinutes()));
    $('#time_london').text(formatNum(utc_date.getHours()) + ':' + formatNum(utc_date.getMinutes()));
    $('#time_sydney').text(formatNum(sydney_date.getHours()) + ':' + formatNum(sydney_date.getMinutes()));
    $('#time_newyork').text(formatNum(newyork_date.getHours()) + ':' + formatNum(newyork_date.getMinutes()));
    $('#time_russia').text(formatNum(russia_date.getHours()) + ':' + formatNum(russia_date.getMinutes()));

    $('#date_beijing').text(date.getDate() + ' ' + getEngMonth(date.getMonth()));
    $('#date_london').text(utc_date.getDate() + ' ' + getEngMonth(utc_date.getMonth()));
    $('#date_sydney').text(sydney_date.getDate() + ' ' + getEngMonth(sydney_date.getMonth()));
    $('#date_newyork').text(newyork_date.getDate() + ' ' + getEngMonth(newyork_date.getMonth()));
    $('#date_russia').text(russia_date.getDate() + ' ' + getEngMonth(russia_date.getMonth()));

}

function formatNum(n) {
    if (n < 10) {
        return ('0' + n);
    } else {
        return n;
    }
}

function getEngMonth(month) {
    var mlist = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Spt", "Oct", "Nov", "Dec"];
    return mlist[month].toUpperCase();
}

setInterval(setTime, 1000);
setTime();



crate3DEarth();
loadAll();


setInterval(loadAll, 5 * 60 * 1000);



///////// test