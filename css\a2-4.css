
/**/

.page-wrapper {
  background: rgb(9,72,149);
  background: -moz-radial-gradient(center, ellipse cover,  rgba(9,72,149,1) 0%, rgba(2,12,37,1) 100%);
  background: -webkit-radial-gradient(center, ellipse cover,  rgba(9,72,149,1) 0%,rgba(2,12,37,1) 100%);
  background: radial-gradient(ellipse at center,  rgba(9,72,149,1) 0%,rgba(2,12,37,1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#094895', endColorstr='#020c25',GradientType=1 );
}

.limcomb{
  position: absolute;
  top: 13px;
  right: 193px;
  height: 30px;
  width: 135px !important;
  z-index: 1001;
  border-radius: 4px;
  border: 1px solid rgba(41, 140, 232, 0.6);
  background: #0b3379;
}
.limcomb .combobox_label{
  display: inline-block;
  padding: 0;
  height: 30px;
  line-height: 30px;
  text-align: left;
  background-image: url(../img/combobox_arr.png);
  background-repeat: no-repeat;
  background-position: 115px center;
  border-radius: 4px;
  border-right: none;
  border: 0px !important;
  box-sizing: border-box;
  padding-left: 5px;
  width: 100%;

}
.pagetitle{
  position: absolute;
  width: 1306px;
  height: 59px;
  top: 58px;
  left: 30px;
  text-align: center;
  border-bottom: 2px solid #1765a0;
}
.pagetitle .date_type_select {
  /*width: 160px;*/
  height: 30px;
  border: 1px solid #1a6cbc;
  border-radius: 3px;
  display: inline-block;
  vertical-align: middle;
  position: absolute;
  right: 65px;
  top: 13px;
  z-index: 1001;
  box-sizing: border-box;
}
.hidden{
  display: none !important;
}
.pagetitle .date_type_select .tab {
  display: inline-block;
  vertical-align: middle;
  width: 39px;
  height: 29px;
  float: left;
  box-sizing: border-box;
  text-align: center;
  line-height: 28px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  font-size: 14px;
  color: #44a3f4;
  border-right: 1px solid #2a81d2;
  z-index: 1001;
  pointer-events: auto;
}
.pagetitle .date_type_select .tab:last-child{
  border-right: 0;
}
.pagetitle .date_type_select .tab.hover {
  background-color: #2a81d2;
  color: white;
}
.pagetitle .date_type_select .tab:hover {
  background-color: #2a81d2;
  color: white;
}
.pagetitle .date_type_select .tab:nth-child(1) {
  position: relative;
  /*left: -1px;*/
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}
.pagetitle .date_type_select .tab:nth-child(4) {
  position: relative;
  /*right: -2px;*/
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}
.maintitle{
  color: #7fc4ff;
  width: 100%;
  font-size: 24px;
  padding: 11px 0 0 0;
  margin: 0 auto;
  font-weight: bold;
}
.submaintitle{
  color: #7fc4ff;
  width: 100%;
  font-size: 22px;
  padding: 0 20px;
  margin: 0 auto;
}

.pagetitle .data_refresh {
  display: inline-block;
  position: absolute;
  height: 24px;
  width: 24px;
  top: 15px;
  right: 35px;
  z-index: 1001;
  background-image: url(../img/data_refresh.png);
  pointer-events: auto;
  cursor: pointer;
}

/*#main_cb_week {
  position: absolute;
  top: 15px;
  left: 1118px;
  height: 34px;
  width: 85px;
  z-index: 1000;
}
#main_cb_month {
  position: absolute;
  top: 15px;
  left: 1202px;
  height: 34px;
  width: 104px;
  z-index: 1001;
}*/

/*#main_cb_week .combobox_label
{
  padding: 6px 6px;
  background-image: url(../img/combobox_arr.png);
  background-repeat: no-repeat;
  background-position: 63px center;
  border-radius: 4px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}
#main_cb_month .combobox_label
{
  padding: 6px 6px;
  background-image: url(../img/combobox_arr.png);
  background-repeat: no-repeat;
  background-position: 83px center;
  border-radius: 4px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: none;
}
*/
.combotab_selected .combobox_label {
  background-color: #2a81d2;
}
.combotab .combobox_label {
  /*background-color: #0a3278;*/
  /*opacity: 0.5;*/
  color: #fff;
}

#week_date_range {
  position: absolute;
  top: 13px;
  right: 333px;
  width: 160px;
  font-size: 14px;
  text-align: center;
  border: 1px solid #1f5fa8;
  border-radius: 2px;
  padding: 3px 0 5px 0px;
  background-color: #021d39;
  box-shadow: 0 1px 8px #021121;

  display: none;
}

/* ----- */
.block_l1 {
  position: absolute;
  top: 135px;
  left: 32px;
  width: 708px;
  height: 134px;

}

.block_l1 .col1 {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 450px;
  height: 134px;
  border-radius: 7px;
  background-color: #194f92;
  overflow: hidden;

}

.block_l1 .col1 .l {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 172px;
  height: 134px;
  background-color: #2576c9;
  background-image: url(../img/a2.4.buy.png);
  background-repeat: no-repeat;
  background-position: right center;
}
.block_l1 .col1 .r {
  position: absolute;
  top: 0px;
  left: 172px;
  width: 278px;
  height: 134px;
}
.block_l1 .col1 .tit {
  margin-top: 10px;
}
#per_SAL_VOL {
  border-bottom: 2px solid #07347b; 
  line-height: 70px;
  height: 70px;
}
.block_l1 .col1 .r .b1 {
  position: absolute;
  top: 108px;
  left: 35px;
  height: 15px;
  line-height: 15px;
}
.block_l1 .col1 .r .b2 {
  position: absolute;
  top: 108px;
  left: 155px;
  height: 15px;
  line-height: 15px;
}
.block_l1 .col1 .r .b1 span,
.block_l1 .col1 .r .b2 span{
  vertical-align:middle;
}

.block_l1 .col2 {
  position: absolute;
  top: 0px;
  left: 462px;
  width: 270px;
  height: 134px;
  border-radius: 7px;
  background-color: #3555bf;
  overflow: hidden;

}

.block_l1 .col2 .ico1 {
  position: absolute;
  top: 27px;
  left: 23px;
  width: 32px;
  height: 22px;
  background-image: url(../img/ico_ticket.png);
  background-repeat: no-repeat;
}
.block_l1 .col2 .ico2 {
  position: absolute;
  top: 82px;
  left: 25px;
  width: 28px;
  height: 30px;
  background-image: url(../img/ico_inc.png);
  background-repeat: no-repeat;
}

.block_l1 .col2 .r1 {
  position: absolute;
  top: 15px;
  left: 68px;
}
.block_l1 .col2 .r2 {
  position: absolute;
  top: 73px;
  left: 68px;
}

.block_l1 .col2 .tit {
  color: #9eaffc;
}


/* ------ */

.block_l2 {
  position: absolute;
  top: 300px;
  left: 32px;
  width: 710px;
  height: 470px;
  pointer-events: auto;

}

.block_l2 .tit {
  position: absolute;
  width: 100%;
  height: 37px;
  border-bottom: 3px solid #0364ab;
  line-height: 24px;
  text-align: center;
}
.block_l2 .tit span {
  display: inline-block;
  padding-left: 32px;
  background-image: url(../img/a2.4.icoper.png);
  background-repeat: no-repeat;
}

.block_l2 .chart {
  position: absolute;
  top: 40px;
  width: 710px;
}

#chart_l2 {
  width: 710px;
}

.block_l2 .mline1{
  position: absolute;
  color: #2fab4f;
  line-height: 15px;
  top: 58px;
  right: 2px;
}
.block_l2 .mline2{
  position: absolute;
  color: #389ff7;
  line-height: 15px;
  top: 170px;
  right: 2px;
}
.block_l2 .mline3{
  position: absolute;
  color: #ba4d3b;
  line-height: 15px;
  top: 285px;
  right: 2px;
}
.block_l2 .xname{
  position: absolute;
  color: #45a5f6;
  bottom: 1px;
  left: 0px;
}

/*  ----  */
.block_r1 {
  position: absolute;
  top: 120px;
  left: 782px;
  width: 553px;
  height: 270px;

}

.block_r1 .chartimg {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 290px;
  height: 286px;
  background-image: url(../img/a2.4.chart.png);
  background-repeat: no-repeat;
}

.block_r1 .tit {
  position: absolute;
  width: 100%;
  height: 37px;
  top: 15px;
  line-height: 21px;
  text-align: right;
}
.block_r1 .tit span {
  display: inline-block;
  padding-left: 29px;
  background-image: url(../img/a2.4.icoper2.png);
  background-repeat: no-repeat;
}

.block_r1 .itm {
  position: absolute;
  width: 522px;
  height: 28px;
  right: 0;
  text-align: right;

  background: -moz-linear-gradient(left,  rgba(3,19,56,0) 20%, rgba(3,19,56,0.7) 100%);
  background: -webkit-linear-gradient(left,  rgba(3,19,56,0) 20%,rgba(3,19,56,0.7) 100%);
  background: linear-gradient(to right,  rgba(3,19,56,0) 20%,rgba(3,19,56,0.7) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00031338', endColorstr='#031338',GradientType=1 );

  cursor: pointer;
  pointer-events: auto;
}
.block_r1 .itm span{
  position: absolute;
  display: block;
  width: 407px;
  height: 28px;
  right: 0px;
  padding-right: 6px;
  line-height: 28px;

  
}

.block_r1 .itm .line{
  position: absolute;
  width: 407px;
  height: 2px;
  top: 28px;
  right: 0px;
  display: none;
}

.block_r1 .chartimg .val{
  position: absolute;
  display: block;
  font-weight: bold;
  width: 100%;
  text-align: center;
}
.block_r1 .float-dialog{
  background: #0378c3;
  opacity: 1;
  display: none;
  position: absolute;
  top: 25px;
  right: 50px;
  width: 280px;
  box-sizing: border-box;
  padding: 10px;
  z-index: 500;
}
.block_r1 .float-dialog>span{
  display: inline-block;
  width: 100%;
  text-align: left;
  margin-bottom: 10px;
  font-size: 12px;
  color: #fff;
  position: initial !important;
}
.block_r1 .float-dialog>span span{
  display: inline !important;
  font-size: 12px;
  color: #fff;
  position: initial !important;
}
.block_r1 .float-dialog>span span .up{
  display: inline-block;
  /*color: #2ad805 !important;*/
  background: url("../img/limarrowup.png") no-repeat center center / 100%;
  width: 12px;
  height: 16px;
  position: relative;
  bottom: -2px;
}
.block_r1 .float-dialog>span span .down{
  display: inline-block;
  background: url("../img/limarrowdown.png") no-repeat center center / 100%;
  width: 12px;
  height: 16px;
  position: relative;
  bottom: -2px;
}
.block_r1 .itm0 {
  top: 46px;
  color: #4ea12e;
}
.block_r1 .itm1 {
  top: 92px;
  color: #215fa1;
}
.block_r1 .itm2 {
  top: 138px;
  color: #d1981e;
}
.block_r1 .itm3 {
  top: 184px;
  color: #c84ce4;
}
.block_r1 .itm4 {
  top: 230px;
  color: #2492a9;
}


.block_r1 .itm0 span{
  /*background: url(../img/ico_chal0.png) no-repeat 383px center;*/
}
.block_r1 .itm1 span{
  /*background: url(../img/ico_chal1.png) no-repeat 383px center;*/
}
.block_r1 .itm2 span{
  /*background: url(../img/ico_chal2.png) no-repeat 383px center;*/
}
.block_r1 .itm3 span{
  /*background: url(../img/ico_chal3.png) no-repeat 383px center;*/
}
.block_r1 .itm4 span{
  /*background: url(../img/ico_chal4.png) no-repeat 383px center;*/
}

.block_r1 .itm0 .line{
background: -moz-linear-gradient(left,  rgba(78,161,46,0) 0%, rgba(78,161,46,1) 100%);
background: -webkit-linear-gradient(left,  rgba(78,161,46,0) 0%,rgba(78,161,46,1) 100%);
background: linear-gradient(to right,  rgba(78,161,46,0) 0%,rgba(78,161,46,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#004ea12e', endColorstr='#4ea12e',GradientType=1 );

}
.block_r1 .itm1 .line{
background: -moz-linear-gradient(left,  rgba(33,95,161,0) 0%, rgba(33,95,161,1) 100%);
background: -webkit-linear-gradient(left,  rgba(33,95,161,0) 0%,rgba(33,95,161,1) 100%);
background: linear-gradient(to right,  rgba(33,95,161,0) 0%,rgba(33,95,161,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00215fa1', endColorstr='#215fa1',GradientType=1 );

}
.block_r1 .itm2 .line{
background: -moz-linear-gradient(left,  rgba(209,152,30,0) 0%, rgba(209,152,30,1) 100%);
background: -webkit-linear-gradient(left,  rgba(209,152,30,0) 0%,rgba(209,152,30,1) 100%);
background: linear-gradient(to right,  rgba(209,152,30,0) 0%,rgba(209,152,30,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00d1981e', endColorstr='#d1981e',GradientType=1 );

}
.block_r1 .itm3 .line{
background: -moz-linear-gradient(left,  rgba(200,76,228,0) 0%, rgba(200,76,228,1) 100%);
background: -webkit-linear-gradient(left,  rgba(200,76,228,0) 0%,rgba(200,76,228,1) 100%);
background: linear-gradient(to right,  rgba(200,76,228,0) 0%,rgba(200,76,228,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00c84ce4', endColorstr='#c84ce4',GradientType=1 );

}
.block_r1 .itm4 .line{
background: -moz-linear-gradient(left,  rgba(36,146,169,0) 0%, rgba(36,146,169,1) 100%);
background: -webkit-linear-gradient(left,  rgba(36,146,169,0) 0%,rgba(36,146,169,1) 100%);
background: linear-gradient(to right,  rgba(36,146,169,0) 0%,rgba(36,146,169,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#002492a9', endColorstr='#2492a9',GradientType=1 );

}

.block_r1 .val0 {
  top: 55px;
}
.block_r1 .val1 {
  top: 105px;
}
.block_r1 .val2 {
  top: 155px;
}
.block_r1 .val3 {
  top: 199px;
}
.block_r1 .val4 {
  top: 237px;
}
.block_r1 .val0 .sub {
  font-size: 16px;
}
.block_r1 .val1 .sub {
  font-size: 14px;
}
.block_r1 .val2 .sub {
  font-size: 12px;
}
.block_r1 .val3 .sub {
  font-size: 10px;
}
.block_r1 .val4 .sub {
  font-size: 9px;
}


/*  -----  */
.block_r2 {
  position: absolute;
  top: 400px;
  left: 790px;
  width: 545px;
  height: 375px;
  pointer-events: auto;
}

.block_r2 .tit {
  position: absolute;
  width: 100%;
  height: 37px;
  border-bottom: 3px solid #0364ab;
  line-height: 24px;
  text-align: center;
}
.block_r2 .tit span {
  display: inline-block;
  /*padding-left: 28px;*/
  /*background-image: url(../img/a2.4.icochal0.png);*/
  background-repeat: no-repeat;
  background-position: 0 1px;
}
.block_r2 .tab {
  position: absolute;
  width: 180px;
  height: 34px;
  top: 47px;
  line-height: 31px;
  font-weight: bold;
  color: #4aa3ee;
  border: 1px solid #0378c3;
  text-align: center;
  padding-left: 28px;
  opacity: 0.7;
  cursor: pointer;
}
.block_r2 .tab:hover {
  opacity: 1;
}
.block_r2 .selected {
  color: #FFFFFF;
  background-color: #0378c3;
  border: 1px solid #0378c3;
  text-align: center;
  opacity: 1;
}
.block_r2 .tab1 {
  left: 0px;
  background-image: url(../img/a2.4.icotb1.png);
  background-repeat: no-repeat;
  background-position: 23px 5px;
}
.block_r2 .tab2 {
  left: 180px;
  background-image: url(../img/a2.4.icotb2.png);
  background-repeat: no-repeat;
  background-position: 14px 5px;

}
.block_r2 .tab3 {
  left: 360px;
  padding: 0;
  /*background-image: url(../img/a2.4.icotb2.png);
  background-repeat: no-repeat;
  background-position: 23px 5px;*/

}

.block_r2 .chart {
  position: absolute;
  width: 545px;
  height: 270px;
  top: 90px;
}



/* --- */
.ext_link {
  display: inline-block;
  width: 23px;
  height: 23px;
  cursor: pointer;
  pointer-events: auto;
  background: url(../img/ext_link1.png) no-repeat center center;
}

#ext_link1 {
  position: absolute;
  right: 10px;
  top: 10px;
}
#ext_link2 {
  position: absolute;
  right: 207px;
  top: 1px;
  display: none;
}

.remark{
  position: absolute;
  text-align: left;
  height: 30px;
  line-height: 30px;
  background: url("../img/reamrkback.png") no-repeat center;
  background-size: 100% 100%; 
  color: #47aafd;
  font-size: 12px;
  width: 350px;
  left: 1%;
  bottom: -30px;
}