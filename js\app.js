var appVersion = 37

function loadjs(url) {

    var script = document.createElement('script');

    script.type = 'text/javascript';

    if (url.indexOf("?") > -1) {
        url = url + '&ver=' + appVersion;
    } else {
        url = url + '?ver=' + appVersion;
    }
    script.src = url;
    document.getElementsByTagName('head')[0].appendChild(script);

}

function loadjs4Babel(url) {
    var script = document.createElement('script');

    script.type = 'text/javascript';

    if (url.indexOf("?") > -1) {
        url = url + '&ver=' + appVersion;
    } else {
        url = url + '?ver=' + appVersion;
    }
    script.src = url;
    document.getElementsByTagName('head')[0].appendChild(script);

}

function loadjs4BabelDefer(url) {
    var script = document.createElement('script');

    script.type = 'text/javascript';

    if (url.indexOf("?") > -1) {
        url = url + '&ver=' + appVersion;
    } else {
        url = url + '?ver=' + appVersion;
    }
    script.src = url;
    script.defer = true;
    document.getElementsByTagName('head')[0].appendChild(script);

}

function loadCss(url, timestamp) {
    var cssTag = document.getElementById('loadCss');
    var head = document.getElementsByTagName('head').item(0);
    if (cssTag) head.removeChild(cssTag);
    css = document.createElement('link');

    if (timestamp == undefined || timestamp !== false) {
        if (url.indexOf("?") > -1) {
            url = url + '&ver=' + appVersion;
        } else {
            url = url + '?ver=' + appVersion;
        }
    }
    css.href = url;
    css.rel = 'stylesheet';
    css.type = 'text/css';
    head.appendChild(css);
}

function loadCsss(options) {
    for (var i = 0; i < options; i++) {
        var url = options[i].url;
        var timestamp = options[i].timestamp;

    }
    var cssTag = document.getElementById('loadCss');
    var head = document.getElementsByTagName('head').item(0);
    if (cssTag) head.removeChild(cssTag);
    css = document.createElement('link');

    if (timestamp == undefined || timestamp !== false) {
        if (url.indexOf("?") > -1) {
            url = url + '&ver=' + appVersion;
        } else {
            url = url + '?ver=' + appVersion;
        }
    }
    css.href = url;
    css.rel = 'stylesheet';
    css.type = 'text/css';
    head.appendChild(css);
}


