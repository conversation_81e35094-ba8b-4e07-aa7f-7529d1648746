(function() {

  /**
   * String的原型扩展
   * @module String
   */

  /**
   * 批量替换字符串
   * @for String
   * @method String.prototype.replaceAll
   * @param src {string} 要替换的子字符串
   * @param des {String} 要替换成的子字符串
   * @return {String}
   * @example 
       var a = 'hello,can you help me ?';
       a.replaceAll('you','me')
       输出hello, can me help me ?
   */
  String.prototype.replaceAll = function(src, des) {
    return this.split(src).join(des);
  };

  /**
   * 格式化字符串
   * @for String
   * @method String.prototype.format
   * @param src {data} 要格式化的json数据 
   * @return {String}
   * @example 
       var a = 'hello,can <%=helper%> help <%=name%> ?';
       a.format(a,{helper:'you',name:'me'})
       输出hello, can you help me ?
   */
  String.prototype.format = function(data) {
    var _f = function() {
      var _format = /<%=([\d\w\.]+)%>/g;
      _format.lastIndex = 0;
      var args = Array.prototype.slice.call(arguments),
        v;
      str = args.shift() + '';
      if (args.length === 1 && typeof(args[0]) === 'object') {
        args = args[0];
      }
      _format.lastIndex = 0;

      function route(obj, path) {
        obj = obj || {};
        path = path.toString();
        var r = /([\d\w_]+)/g,
          m;
        r.lastIndex = 0;
        while ((m = r.exec(path)) !== null) {
          obj = obj[m[0]];
          if (obj === undefined || obj === null) {
            break;
          }
        }
        return obj;
      }
      return str.replace(_format,
        function(m, n) {
          v = route(args, n);
          return v === undefined ? m : v;
        });
    };

    var _this = this;

    return _f(_this, data);
  };

  /**
   * 用做过滤HTML标签里面的东东 比如这个例子里的<input value="XXX"> XXX就是要过滤的
   * @for String
   * @method String.prototype.escHtmlEp
   * @return {String}
   */
  String.prototype.escHtmlEp = function() {
    return this.replace(/[&'"<>\/\\\-\x00-\x1f\x80-\xff]/g, function(r) {
      return "&#" + r.charCodeAt(0) + ";";
    });
  };

  /**
   * 用做过滤直接放到innerHtml里的,防止xss
   * @for String
   * @method String.prototype.escHtml
   * @return {String}
   */
  String.prototype.escHtml = function() {
    return this.replace(/[&'"<>\/\\\-\x00-\x09\x0b-\x0c\x1f\x80-\xff]/g, function(r) {
      return "&#" + r.charCodeAt(0) + ";";
    }).replace(/ /g, "&nbsp;").replace(/\r\n/g, "<br />").replace(/\n/g, "<br />").replace(/\r/g,
      "<br />"
    );
  };

  /**
   * 用做过滤直接放到HTML里js中的
   * @for String
   * @method String.prototype.escScript
   * @return {String}
   */
  String.prototype.escScript = function() {
    return this.replace(/[\\"']/g, function(r) {
      return "\\" + r;
    }).replace(/%/g, "\\x25").replace(/\n/g, "\\n").replace(/\r/g, "\\r").replace(/\x01/g, "\\x01");
  };

  /**
   * 用做过滤直接URL参数里的 比如 http://a.b.com/abc_cgi?a=XXX XXX就是要过滤的
   * @for String
   * @method String.prototype.escUrl
   * @return {String}
   */
  String.prototype.escUrl = function() {
    return escape(this).replace(/\+/g, "%2B");
  };

  /**
   * @for String
   * @method String.prototype.escMiniUrl
   * @return {String}
   */
  String.prototype.escMiniUrl = function() {
    return this.replace(/%/g, "%25");
  };

  /**
   * 用做过滤直接放到&lt;a href="javascript:alert('XXX')"&gt;&lt;/a&gt;中的XXX
   * @for String
   * @method String.prototype.escHrefScript
   * @return {String}
   */
  String.prototype.escHrefScript = function() {
    return this.escScript().escMiniUrl().escHtmlEp();
  };

  /**
   * 用做过滤直接放到正则表达式中的
   * @for String
   * @method String.prototype.escRegexp
   * @return {String}
   */
  String.prototype.escRegexp = function() {
    return this.replace(/[\\\^\$\*\+\?\{\}\.\(\)\[\]]/g, function(a, b) {
      return "\\" + a;
    });
  };

  /**
   * 过滤开头和末尾的空格
   * @for String
   * @method String.prototype.trim
   * @return {String}
   */
  String.prototype.trim = function() {
    return this.replace(/(^\s*)|(\s*$)/g, "");
  };

  /**
   * 过滤开头的空格
   * @for String
   * @method String.prototype.trimLeft
   * @return {String}
   */
  String.prototype.trimLeft = function() {
    return this.replace(/(^\s*)/g, "");
  };

  /**
   * 过滤末尾的空格
   * @for String
   * @method String.prototype.trimRight
   * @return {String}
   */
  String.prototype.trimRight = function() {
    return this.replace(/(\s*$)/g, "");
  };

  /**
   * 得到字符串的长度
   * @for String
   * @method String.prototype.getRealLen
   * @param isUTF8 {Boolean} 是否按照UTF8编码计算
   * @return {String}
   */
  String.prototype.getRealLen = function(isUTF8) {
    var s = this;
    if (!isUTF8) {
      return s.replace(/[^\x00-\xFF]/g, "**").length;
    } else {
      var cc = s.replace(/[\x00-\xFF]/g, "");
      return (s.length - cc.length) + (encodeURI(cc).length / 3);
    }
  };

  /**
   * 截取字符串
   * @for String
   * @method String.prototype.cut
   * @param bitLen {Number} 截取的长度
   * @param tails {Number} 截取字符串后末尾添加的比如...
   * @return {String}
   */
  String.prototype.cut = function(bitLen, tails) {
    var _this = this;
    var str = _this.toString();
    bitLen -= 0;
    tails = tails || '';
    if (isNaN(bitLen)) {
      return str;
    }
    var len = str.length,
      i = Math.min(Math.floor(bitLen / 2), len),
      cnt = str.slice(0, i).getRealLen();
    for (; i < len && cnt < bitLen; i++) {
      cnt += 1 + !/[\x00-\xFF]/.test(str.charAt(i));
    }
    return str.slice(0, cnt > bitLen ? i - 1 : i) + (i < len ? tails : '');
  };

  /**
   * 取得第一次满足条件的两段子字符串之间的字符串
   * @for String
   * @method String.prototype.between
   * @param sBegin {String} 起始子字符串
   * @param sEnd {String} 结束子字符串
   * @return {String}
   */
  String.prototype.between = function(sBegin, sEnd) {
    var bp = this.indexOf(sBegin);
    if (bp === -1) {
      return ("");
    }
    bp += sBegin.length;
    var ep = this.indexOf(sEnd, bp);
    if (ep === -1) {
      return ("");
    }
    return this.substr(bp, ep - bp);
  };

})()