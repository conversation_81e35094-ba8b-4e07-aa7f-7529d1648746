<!DOCTYPE html>
<html lang="zh">
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
		<link rel="stylesheet" type="text/css" href="css/common.css" />
		<link rel="stylesheet" type="text/css" href="css/flight.css" />
		
		<script src="../js/babel.min.js"></script>
		<script src="../js/polyfill.min.js"></script>
		<script src="../js/jquery-1.11.1.js"></script>
		<script src="../js/echarts.min.4.2.1.js?ver=20211218"></script>
		<script src="js/world.js" type="text/javascript" charset="utf-8"></script>
		<script src="../js/util.js"></script>
		<title>航司大屏航班详情</title>
		<style type="text/css">

			#loading_msk .spinner {
				margin-top:340px;
			}
		</style>

	</head>
	<body>
		<iframe id="login" scrolling="no" width="0" height="0" style="display:none;"></iframe>
		<div class="close-btn scale_item"></div>
		<div id="mapBox"  class="page-wrapper"></div>
		<div class="page-bgimg" style=" -moz-transform-origin:left top; pointer-events: none;"></div>
		<div class="page-wrapper" style=" -moz-transform-origin:left top;">

	
			<!-- head -->
			<div class="pagetitle">
				<!-- /.top -->
				<div class='col_top con_flex_row'>

					<div class="co1 flex_none" style='width:28%'>
						<div class='fltno' style=""></div>
						<div class="flightTimes fs14 blue1"></div>
					</div>

					<div class="mid flex_1 con_flex_row" style="width: 49%;">
						<div class="c1 flex_1">
							<div class="t1 city1"></div>
							<div class="t2">出发机场</div>
						</div>
						<div class="c4 flex_1 leg1">
							<div class="fs12"><span class="leg1Status"></span><br />——————></div>
						</div>
						<div class="c2 flex_1 jingting">
							<div class="t1 citym"></div>
							<div class="blue fs12">经停机场</div>
						</div>
						<div class="c4 flex_1 leg2">
							<div class="fs12"><span class="leg2Status"></span><br />——————></div>
						</div>
						<div class="c3 flex_1">
							<div class="t1 city2"></div>
							<div class="t2">目的机场</div>
						</div>
					</div>

					<div class="co3 flex_none" style='width:22%'>
						<div class='fltsts sts'></div>
					</div>

				</div><!-- /.top -->

			</div>
			<div class="leftLogo">
				<div class="compLogo"></div>
				<div class="compName fs12">海南航空</div>
			</div>
			<!-- col-left -->
			<div class="col_left">
				<ul class="col_left_title">
					<li style="width: 90px;">
						<div class="title_up fs14 blue1 bold">出发机场</div>
						<div class="title_down fs20 bold city1"></div>
					</li>
					<li style="width: 120px;">
						<div class="title_up fs14 blue1 bold">实际离港</div>
						<div class="title_down fs20 bold atd"></div>
					</li>
					<li style="width: 110px;">
						<div class="title_up blue2 bold">计划离港</div>
						<div class="title_down fs20 blue2 bold std"></div>
					</li>
					<li style="width: 110px;height: 60px;">
						<div class="weather weather_city1">
							<div class="weather_ico"><span></span>
								<div class="temperature"></div>
							</div>
							<div class="weatherNum blue1 bold condition"></div>
						</div>
						<div class="title_down fs12" style="margin-left: 30px;"></div>
					</li>
				</ul>
				<div class="col_left_risk">
					<div class="risk_title fs12">风险</div>
					<div class="risk_con fs12">
						<span id="depNode" direction="up" height="50" scrollamount="2">

						</span>
					</div>
				</div>
				<div class="con_left_pie">
					<!-- 圆环 -->
					<div class="pie-wrapper">
						<div id="leftPie"></div>
						<div class="pieTitle fs14 bold">
							<div>起飞horcs</div>
							<div class="pieNum fs14" id="node200"></div>
						</div>
					</div>
					<!-- 机组 -->
					<div class="crew-wrapper">
						<div class="crewCell0 fs10">
							<div class="crewTitle fs14">机组</div>
							<div class="crewNum fs16 bold" id="node201"></div>
						</div>

						<div class="crewCell1 fs10">
							<div class="crewTitle">机组</div>
							<div class="crewTitle">经验能力</div>
							<div class="crewNum" id="node205"></div>
						</div>

						<div class="crewCell2 fs10">
							<div class="crewTitle">机组间</div>
							<div class="crewTitle">配合程度</div>
							<div class="crewNum" id="node204"></div>
						</div>

						<div class="crewCell3 fs10">
							<div class="crewTitle">机组</div>
							<div class="crewTitle">压力程度</div>
							<div class="crewNum" id="node206"></div>
						</div>

					</div>
					<!-- 飞机 -->
					<div class="plane-wrapper">
						<div class="planeCell0 fs10">
							<div class="planeTitle fs14">飞机</div>
							<div class="planeNum fs16 bold" id="node202_1"></div>
						</div>

						<div class="planeCell1 fs10">
							<div class="planeTitle">MEL</div>
							<div class="planeTitle">项关联要素</div>
							<div class="planeNum fs16" id="node202_2"></div>
						</div>

					</div>
					<!-- 环境 -->
					<div class="ambient-wrapper">
						<div class="ambientCell0 fs10">
							<div class="ambientTitle fs14">环境</div>
							<div class="ambientNum fs16 bold" id="node203"></div>
						</div>

						<div class="ambientCell1 fs10">
							<div class="ambientTitle">机场</div>
							<div class="ambientTitle">条件状况</div>
							<div class="ambientNum"  id="node207"></div>
						</div>

						<div class="ambientCell2 fs10">
							<div class="ambientTitle">起飞</div>
							<div class="ambientTitle">重量限制</div>
							<div class="ambientNum" id="node324"></div>
						</div>

						<div class="ambientCell3 fs10">
							<div class="ambientTitle">气象条件</div>
							<div class="ambientNum" id="node208"></div>
						</div>

					</div>
				</div>
				<div class="col_left_miti">
					<div class="miti_title fs12">缓解措施</div>
					<div class="miti_con fs12">
						<span id="depRiskMark"></span>
					</div>
				</div>
			</div>
			<!-- col-mid -->
			<div class="col_mid">
				<!-- col_mid_left -->
				<div class="col_mid_left">
					<div class="topTitle fs14 blue1">航油监控</div>
					<div class="row1">
						<div class="fl fs12 blue1">起飞油量</div>
						<div class="fr fs14 bold" id="oil_1"></div>
					</div>
					<div class="row2">
						<div class="fl fs12 blue1">最大载油量</div>
						<div class="fr fs14 bold" id="oil_2"></div>
					</div>
					<div class="row3">
						<div class="fl fs12 blue1">计划航线耗油</div>
						<div class="fr fs14 bold" id="oil_3"></div>
					</div>
					<!-- 油量标识1 -->
					<div class="col_mid_left_line water1">
						<div class="lin_1 fs12">
							<span class="fs18 bold yellow">--</span>
						</div>
						<div class="lin_2 fs12">
							<span class="fs18 bold blue1">--</span>
							<span class="lineTitle blue1">实际剩余</span>
							<span class="lineNum blue1" id="oil_rt1"></span>
						</div>
						<div class="lin_3 fs12">
							<span class="fs18 bold red">--</span>
						</div>
						<div class="lineLegend fs12" style="position: absolute; top:120px;">
							<div class="line_4">
								<span class="lineTitle yellow">黄色预警</span>
								<span class="lineNum yellow" id="medFuel"></span>
							</div>
							<div class="line_5">
								<span class="lineTitle red">红色预警</span>
								<span class="lineNum red" id="lowFuel"></span>
							</div>
						</div>
					</div>
					<!-- 油量1 -->
					<div class="wave-wrapper_1">
						<div class="wave-animation">
							<div id="water1" class="water">
								<svg viewBox="0 0 560 20" class="water-wave water-wave--back">
									<path d="M420,20c21.5-0.4,38.8-2.5,51.1-4.5c13.4-2.2,26.5-5.2,27.3-5.4C514,6.5,518,4.7,528.5,2.7c7.1-1.3,17.9-2.8,31.5-2.7c0,0,0,0,0,0v20H420z">
									</path>
									<path d="M420,20c-21.5-0.4-38.8-2.5-51.1-4.5c-13.4-2.2-26.5-5.2-27.3-5.4C326,6.5,322,4.7,311.5,2.7C304.3,1.4,293.6-0.1,280,0c0,0,0,0,0,0v20H420z">
									</path>
									<path d="M140,20c21.5-0.4,38.8-2.5,51.1-4.5c13.4-2.2,26.5-5.2,27.3-5.4C234,6.5,238,4.7,248.5,2.7c7.1-1.3,17.9-2.8,31.5-2.7c0,0,0,0,0,0v20H140z">
									</path>
									<path d="M140,20c-21.5-0.4-38.8-2.5-51.1-4.5c-13.4-2.2-26.5-5.2-27.3-5.4C46,6.5,42,4.7,31.5,2.7C24.3,1.4,13.6-0.1,0,0c0,0,0,0,0,0l0,20H140z">
									</path>
								</svg>
								<svg viewBox="0 0 560 20" class="water-wave water-wave--front">
									<path d="M420,20c21.5-0.4,38.8-2.5,51.1-4.5c13.4-2.2,26.5-5.2,27.3-5.4C514,6.5,518,4.7,528.5,2.7c7.1-1.3,17.9-2.8,31.5-2.7c0,0,0,0,0,0v20H420z">
									</path>
									<path d="M420,20c-21.5-0.4-38.8-2.5-51.1-4.5c-13.4-2.2-26.5-5.2-27.3-5.4C326,6.5,322,4.7,311.5,2.7C304.3,1.4,293.6-0.1,280,0c0,0,0,0,0,0v20H420z">
									</path>
									<path d="M140,20c21.5-0.4,38.8-2.5,51.1-4.5c13.4-2.2,26.5-5.2,27.3-5.4C234,6.5,238,4.7,248.5,2.7c7.1-1.3,17.9-2.8,31.5-2.7c0,0,0,0,0,0v20H140z">
									</path>
									<path d="M140,20c-21.5-0.4-38.8-2.5-51.1-4.5c-13.4-2.2-26.5-5.2-27.3-5.4C46,6.5,42,4.7,31.5,2.7C24.3,1.4,13.6-0.1,0,0c0,0,0,0,0,0l0,20H140z">
									</path>
								</svg>
							</div>
						</div>
					</div>
					<div class="row4">
						<div class="fl fs12 blue1">最远备降</div>
					</div>

					<!-- 油量标识2 -->
					<div class="col_mid_left_line2 water2">
						<div class="lin_1 fs12">
							<span class="fs18 bold yellow">--</span>
						</div>
						<div class="lin_2 fs12">
							<span class="fs18 bold blue1">--</span>
							<span class="lineTitle blue1">实际剩余</span>
							<span class="lineNum blue1" id="oil_rt2"></span>
						</div>
						<div class="lin_3 fs12">
							<span class="fs18 bold red">--</span>
						</div>
						<div class="lineLegend fs12" style="position: absolute; top: 110px;">
							<div class="line_4 yellow">
								<span class="lineTitle">黄色预警</span>
								<span class="lineNum yellow" id="nearestaltYellowFuel"></span>
							</div>
							<div class="line_5 red">
								<span class="lineTitle  ">红色预警</span>
								<span class="lineNum red" id="nearestaltRedFuel"></span>
							</div>
						</div>
					</div>
					<!-- 油量2 -->
					<div class="wave-wrapper_2">
						<div class="wave-animation">
							<div id="water2" class="water">
								<svg viewBox="0 0 560 20" class="water-wave water-wave--back">
									<path d="M420,20c21.5-0.4,38.8-2.5,51.1-4.5c13.4-2.2,26.5-5.2,27.3-5.4C514,6.5,518,4.7,528.5,2.7c7.1-1.3,17.9-2.8,31.5-2.7c0,0,0,0,0,0v20H420z">
									</path>
									<path d="M420,20c-21.5-0.4-38.8-2.5-51.1-4.5c-13.4-2.2-26.5-5.2-27.3-5.4C326,6.5,322,4.7,311.5,2.7C304.3,1.4,293.6-0.1,280,0c0,0,0,0,0,0v20H420z">
									</path>
									<path d="M140,20c21.5-0.4,38.8-2.5,51.1-4.5c13.4-2.2,26.5-5.2,27.3-5.4C234,6.5,238,4.7,248.5,2.7c7.1-1.3,17.9-2.8,31.5-2.7c0,0,0,0,0,0v20H140z">
									</path>
									<path d="M140,20c-21.5-0.4-38.8-2.5-51.1-4.5c-13.4-2.2-26.5-5.2-27.3-5.4C46,6.5,42,4.7,31.5,2.7C24.3,1.4,13.6-0.1,0,0c0,0,0,0,0,0l0,20H140z">
									</path>
								</svg>
								<svg viewBox="0 0 560 20" class="water-wave water-wave--front">
									<path d="M420,20c21.5-0.4,38.8-2.5,51.1-4.5c13.4-2.2,26.5-5.2,27.3-5.4C514,6.5,518,4.7,528.5,2.7c7.1-1.3,17.9-2.8,31.5-2.7c0,0,0,0,0,0v20H420z">
									</path>
									<path d="M420,20c-21.5-0.4-38.8-2.5-51.1-4.5c-13.4-2.2-26.5-5.2-27.3-5.4C326,6.5,322,4.7,311.5,2.7C304.3,1.4,293.6-0.1,280,0c0,0,0,0,0,0v20H420z">
									</path>
									<path d="M140,20c21.5-0.4,38.8-2.5,51.1-4.5c13.4-2.2,26.5-5.2,27.3-5.4C234,6.5,238,4.7,248.5,2.7c7.1-1.3,17.9-2.8,31.5-2.7c0,0,0,0,0,0v20H140z">
									</path>
									<path d="M140,20c-21.5-0.4-38.8-2.5-51.1-4.5c-13.4-2.2-26.5-5.2-27.3-5.4C46,6.5,42,4.7,31.5,2.7C24.3,1.4,13.6-0.1,0,0c0,0,0,0,0,0l0,20H140z">
									</path>
								</svg>
							</div>
						</div>
					</div>
					<div class="row5">
						<div class="fl fs12 blue1">最近备降</div>
					</div>


				</div>

				<!-- col_mid_foot -->
				<div class="col_mid_foot">
					<div class="col_1">
						<div class="fprGauge">
							<canvas id="cvs_chart1" width="140" height="140" style="background:rgba(255,0,0,0.0);"></canvas>
							<img id="cvs_chart1_pointer" class="pointer" src="img/pointer2.svg" />
							<img id="cvs_chart1b_pointer" class="pointer" src="img/pointer1.svg" />
						</div>
						<div class="fprInfo">
							<div class="fs12 blue1">本航线本月正常率</div>
							<div class="fs16 green bold"><span id="cvs_chart1_lb1" class="fs22"></span></div>
							<div class="fs12 blue1">本司本月正常率</div>
							<div class="fs16 bold"><span id="cvs_chart1_lb2" class="fs16 white"></span></div>
						</div>


					</div>
					<div class="col_2">
						<div class="silosTitle fs16 bold">座舱布局</div>
						<div class="silosImg">
							<div class='actype blue'></div>
							<div class='lb blue'></div>
						</div>

					</div>
					<div class="col_3">
						<div class="colCrewTitle fs16 bold">机组信息</div>
						<div class="colCrewBody">
							<div class="colCrewInfo fs12">
								<span class="colCrewLeft blue1">责任机长</span>
								<span class="colCrewRight">杨文王</span>
							</div>
							<div class="colCrewInfo fs12">
								<span class="colCrewLeft blue1">其他驾驶</span>
								<span class="colCrewRight">孙立阳,王佩超</span>
							</div>
							<div class="colCrewInfo fs12">
								<span class="colCrewLeft blue1">乘务员</span>
								<span class="colCrewRight">田雨婷，齐艺馨，朱晓琳，徐佩琳，丰晓娜，潘沽</span>
							</div>
							<div class="colCrewInfo fs12">
								<span class="colCrewLeft blue1">安全员</span>
								<span class="colCrewRight">朱志国</span>
							</div>
						</div>
					</div>
					<div class="col_4">
						<div class="plfGauge">
							<canvas id="cvs_chart2" width="116" height="116" style="background:rgba(255,0,0,0.0);"></canvas>
							<div class="fs9" style="position: absolute; width:22px; height:22px; left:19px; top:80px; opacity:0.9; ">0
							</div>
							<div class="fs9" style="position: absolute; width:22px; height:22px; left:10px; top:33px; opacity:0.9; ">25
							</div>
							<div class="fs9" style="position: absolute; width:22px; height:22px; left:53px; top:6px; opacity:0.9; ">50
							</div>
							<div class="fs9" style="position: absolute; width:22px; height:22px; left:95px; top:33px; opacity:0.9; ">75
							</div>
							<div class="fs9" style="position: absolute; width:32px; height:22px; left:87px; top:80px; opacity:0.9; ">100
							</div>
						</div>
						<div class="plfInfo trv_rate">
							<div class="fs12 blue1">客座率</div>
							<div>
								<span class="fs16 green bold" id="cvs_chart2_lb1"></span>
								<span class="up_arr" style='display: none;'></span>
								<span class="down_arr" style='display: none;'></span>
							</div>
							<div class="fs12 blue1">高于航司平均</div>
						</div>
					</div>
				</div>
				<!-- col_mid_right -->
				<div class="col_mid_right">
					<!-- 乘客 -->
					<div class="passenger">
						<div class="fs14 blue1 bold">乘客</div>
						<ul class="row1">
							<li>
								<div class="li_up fs14"><span class="passNum  bold fNum">--</span><span>人</span></div>
								<div class="li_down fs12 blue1">F舱</div>
							</li>
							<li>
								<div class="li_up fs14"><span class="passNum  bold cNum">--</span><span>人</span></div>
								<div class="li_down fs12 blue1">C舱</div>
							</li>
							<li>
								<div class="li_up fs14"><span class="passNum  bold wNum">--</span><span>人</span></div>
								<div class="li_down fs12 blue1">W舱</div>
							</li>
							<li style="margin-right: 0;">
								<div class="li_up fs14"><span class="passNum  bold yNum">--</span><span>人</span></div>
								<div class="li_down fs12 blue1">Y舱</div>
							</li>
						</ul>
						<ul class="row2">
							<li>
								<div class="li_up fs14"><span class="passNum  bold chdNum">--</span><span>人</span></div>
								<div class="li_down fs12 blue1">儿童</div>
							</li>
							<li>
								<div class="li_up fs14"><span class="passNum  bold eldNum">--</span><span>人</span></div>
								<div class="li_down fs12 blue1">老人</div>
							</li>
							<li>
								<div class="li_up fs14"><span class="passNum  bold svipNum">--</span><span>人</span></div>
								<div class="li_down fs12 blue1">要客</div>
							</li>
							<li>
								<div class="li_up fs14"><span class="passNum  bold ckiInOutNum">--</span><span>人</span></div>
								<div class="li_down fs12 blue1">中转</div>
							</li>
							<li style="margin-right: 0;">
								<div class="li_up fs14"><span class="passNum  bold spNum">--</span><span>人</span></div>
								<div class="li_down fs12 blue1">特服</div>
							</li>
						</ul>
					</div>
					<div class="transfer">
						<div class="fs14 blue1 bold" style="position: absolute;top: -5px;left: 5px;">中转旅客人数</div>
						<div class="tranBox1">
							<span class="blue1 fs12">转入</span>
							<span class="fs14 bold ckiInNum">--</span>
							<span>人</span>
						</div>
						<div class="tranBox2">
							<span class="blue1 fs12">转出</span>
							<span class="fs14 bold ckiOutNum">--</span>
							<span>人</span>
						</div>
					</div>
					<!-- 转出航班详情 -->
					<div class="transInfo">
						<div class="transInfoTitle fs14 blue1 bold">转出航班详情</div>
						<div class="tablebox">

							<div class="tbl-header blue1">
								<table border="0" cellspacing="0" cellpadding="0">
									<thead>
									<tr>
										<th>人数</th>
										<th>航班号</th>
										<th>过站时间</th>
									</tr>
									</thead>
									<tbody style="opacity:0;"></tbody>
								</table>
							</div>

							<div class="tbl-body">
								<table border="0" cellspacing="0" cellpadding="0">
									<thead>
									<tr>
										<th>人数</th>
										<th>航班号</th>
										<th>过站时间</th>
									</tr>
									</thead>
									<tbody></tbody>
								</table>
							</div>
						</div>
					</div>

				</div>
			</div>
			<!-- col-right -->
			<div class="col_right">
				<ul class="col_right_title">
					<li style="width: 90px;">
						<div class="title_up fs14 blue1 bold">落地机场</div>
						<div class="title_down fs20 bold city2"></div>
					</li>
					<li style="width: 120px;">
						<div class="title_up fs14 blue1 bold">实际到港</div>
						<div class="title_down fs20 bold ata"></div>
					</li>
					<li style="width: 110px;">
						<div class="title_up blue2 bold">计划到港</div>
						<div class="title_down blue2 fs20 bold sta"></div>
					</li>
					<li style="width: 110px;height: 60px;">
						<div class="weather weather_city2">
							<div class="weather_ico">
                                <span></span>
								<div class="temperature"></div>
							</div>
							<div class="weatherNum blue1 bold condition"></div>
						</div>
						<div class="title_down fs12" style="margin-left: 30px;"></div>
					</li>
				</ul>
				<div class="col_right_risk">
					<div class="risk_title fs12">风险</div>
					<div class="risk_con fs12">
						<span id="arrNode" direction="up" height="50" scrollamount="2">

						</span>
					</div>
				</div>
				<div class="con_right_pie">
					<!-- 圆环 -->
					<div class="pie-wrapper">
						<div id="rightPie"></div>
						<div class="pieTitle fs14 bold">
							<div>落地horcs</div>
							<div class="pieNum fs14" id="node1"></div>
						</div>
					</div>
					<!-- 机组 -->
					<div class="crew-wrapper">
						<div class="crewCell0 fs10">
							<div class="crewTitle fs14">机组</div>
							<div class="crewNum fs16 bold" id="node2"></div>
						</div>

						<div class="crewCell1 fs10">
							<div class="crewTitle">机组</div>
							<div class="crewTitle">经验能力</div>
							<div class="crewNum" id="node6"></div>
						</div>

						<div class="crewCell2 fs10">
							<div class="crewTitle">机组间</div>
							<div class="crewTitle">配合程度</div>
							<div class="crewNum" id="node5"></div>
						</div>

						<div class="crewCell3 fs10">
							<div class="crewTitle">机组</div>
							<div class="crewTitle">压力程度</div>
							<div class="crewNum" id="node7"></div>
						</div>

					</div>
					<!-- 飞机 -->
					<div class="plane-wrapper">
						<div class="planeCell0 fs10">
							<div class="planeTitle fs14">飞机</div>
							<div class="planeNum fs16 bold" id="node3_1"></div>
						</div>

						<div class="planeCell1 fs10">
							<div class="planeTitle">MEL</div>
							<div class="planeTitle">项关联要素</div>
							<div class="planeNum fs16" id="node3_2"></div>
						</div>

					</div>
					<!-- 环境 -->
					<div class="ambient-wrapper">
						<div class="ambientCell0 fs10">
							<div class="ambientTitle fs14">环境</div>
							<div class="ambientNum fs16 bold" id="node4"></div>
						</div>

						<div class="ambientCell1 fs10">
							<div class="ambientTitle">机场</div>
							<div class="ambientTitle">条件状况</div>
							<div class="ambientNum" id="node8"></div>
						</div>

						<div class="ambientCell2 fs10">
							<div class="ambientTitle"></div>
							<div class="ambientTitle"></div>
							<div class="ambientNum" ></div>
						</div>

						<div class="ambientCell3 fs10">
							<div class="ambientTitle">气象条件</div>
							<div class="ambientNum" id="node9"></div>
						</div>

					</div>
				</div>
				<div class="col_right_miti">
					<div class="miti_title fs12">缓解措施</div>
					<div class="miti_con fs12">
						<span id="arrRiskMark"></span>
					</div>
				</div>
			</div>
		</div>


		<script src="js/config.js?ver=1"></script>
		<script src="js/common_flight.js?ver=2"></script>
		<script type="text/babel" src="flight.js" charset="utf-8"></script>
	</body>
</html>
