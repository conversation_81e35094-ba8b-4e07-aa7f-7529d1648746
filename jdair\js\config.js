
var APP_ID = "hnahk-dvp2";

var userinfo;


function SSO_URL () {
  if (window.location.href.indexOf("vis.hnair.net") > -1 || window.location.href.indexOf("bim.hnair.net") > -1 || window.location.href.indexOf("cdp-mobile.hnair.net") > -1) {
    return "https://sso.hnair.net";
  } else {
    return "https://ssotest.hnair.net/opcnet-sso";
  }
}

function SSO_SERVICE () {
  if (window.location.href.includes("vis.hnair.net") || window.location.href.includes("bim.hnair.net") || window.location.href.includes("cdp-mobile.hnair.net")) {
    if (window.location.href.includes("largescreen/7x2")) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen7x2";
    } else if (window.location.href.includes("largescreen/west")) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen_west";
    } else if (window.location.href.includes("largescreen/jdair")) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen_jd";
    } else if (window.location.href.includes("largescreen/hnahk")) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen_hnahk";
    } else if (window.location.href.includes("largescreen/general")) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen_general";
    } else if (window.location.href.includes("largescreen/sdc")) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen_sdc";
    } else if (window.location.href.includes("largescreen")) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen";
    } else {
      return "https://vis.hnair.net/bi/largescreen/redirect_admin";
    }
  } else if (window.location.href.includes("172.16.113.58")) {
    if (window.location.href.includes("largescreen/7x2")) {
      return "http://172.16.113.58:8080/bi/largescreen/redirect_largescreen7x2";
    } else if (window.location.href.includes("largescreen/west")) {
      return "http://172.16.113.58:8080/bi/largescreen/redirect_largescreen_west";
    } else if (window.location.href.includes("largescreen/jdair")) {
      return "http://172.16.113.58:8080/bi/largescreen/redirect_largescreen_jd";
    } else if (window.location.href.includes("largescreen/hnahk")) {
      return "http://172.16.113.58:8080/bi/largescreen/redirect_largescreen_hnahk";
    } else if (window.location.href.includes("largescreen/general")) {
      return "http://172.16.113.58:8080/bi/largescreen/redirect_largescreen_general";
    } else if (window.location.href.includes("largescreen/sdc")) {
      return "http://172.16.113.58:8080/bi/largescreen/redirect_largescreen_sdc";
    } else if (window.location.href.includes("largescreen")) {
      return "http://172.16.113.58:8080/bi/largescreen/redirect_largescreen";
    } else {
      return "http://172.16.113.58:8080/bi/largescreen/redirect_admin";
    }
  } else {
    if (window.location.href.includes("largescreen/7x2")) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen7x2";
    } else if (window.location.href.includes("largescreen/west")) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen_west";
    } else if (window.location.href.includes("largescreen/jdair")) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen_jd";
    } else if (window.location.href.includes("largescreen/hnahk")) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen_hnahk";
    } else if (window.location.href.includes("largescreen/general")) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen_general";
    } else if (window.location.href.includes("largescreen/sdc")) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen_sdc";
    } else if (window.location.href.includes("largescreen")) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen";
    } else {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_admin";
    }
  }
}


// 检测是不是登录了
var needlogin = false;
function checkLogin (response) {
  if (response.errorcode == '9999' && !needlogin) {
    needlogin = true;
    alert("请重新登录");
    redirectToSSO();
  }
}

function redirectToSSO () {
  // 没有登录
  // if(window.location.href.includes("hnair.net")){
  window.location.href = SSO_URL() + "/login?appid=" + APP_ID + "&service=" + SSO_SERVICE();
  // }

}

function logout () {
  // 没有登录
  window.location.href = SSO_URL() + "/logout?appid=" + APP_ID + "&service=" + SSO_SERVICE();
}



function getQueryString (name) {
  var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
  var r = window.location.search.substr(1).match(reg);
  if (r != null) return unescape(r[2]); return '';
}


