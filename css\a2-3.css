
/**/

.page-wrapper {
  
}


.pagetitle{
  position: absolute;
  width: 1306px;
  height: 59px;
  top: 56px;
  left: 30px;
  text-align: center;
}
.maintitle{
  color: #fff;
  width: 100%;
  font-size: 26px;
  padding: 11px 0 0 0;
  margin: 0 auto;
  font-weight: bold;
}
.submaintitle{
  color: #8ccafe;
  width: 350px;
  font-size: 16px;
  padding: 0 20px;
  margin: 0 auto;
}

#back2prevpage {
  position: absolute;
  width: 300px;
  height: 30px;
  top: 96px;
  left: 33px;
  font-size: 16px;
  color: #8ac7fb;
  padding-left: 20px;
  background: url(../img/a2.3_arr_back.png) no-repeat 0 4px;
  cursor: pointer;
  pointer-events: auto;
}

.legend {
  position: absolute;
  left: 30px;
  bottom: 255px;
  color: #c5defe;
}

.legend .itm {
  display: inline-block;
  padding-left: 16px;
  margin-left: 20px;
  background-repeat: no-repeat;
  background-position: left center;
  pointer-events: auto;
  cursor: pointer;
}
.legend .itm:hover {
  color: #fff;
}
.legend .itm1 {
  background-image: url(../img/a2.3.legend_1.png);
}
.legend .itm2 {
  background-image: url(../img/a2.3.legend_2.png);
}
.legend .itm3 {
  background-image: url(../img/a2.3.legend_3.png);
}
.legend .itm4 {
  padding-left: 18px;
  margin-left: 35px;
  background-image: url(../img/a2.3.legend_4.png);
}
.legend .itm5 {
  padding-left: 18px;
  margin-left: 35px;
  background-image: url(../img/a2.3.legend_5.png);

  pointer-events: none;
  cursor: default;
}


/* ---- block ---- */
.block_l {
  position: absolute;
  width: 316px;
  height: 216px;
  bottom: 25px;
  left: 20px;
}

.block_l .tkt_cnt {
  position: absolute;
  top: 52px;
  left: 14px;
}
.block_l .tkt_cnt_vs {
  position: absolute;
  top: 130px;
  left: 14px;
}
.block_l .tkt_sal {
  position: absolute;
  top: 179px;
  left: 14px;
}
.block_l .tkt_avg {
  position: absolute;
  top: 155px;
  left: 14px;
}

#counterContainer {
  position: absolute;
  top: 76px;
  left: 0;
  padding-left: 10px;
}


/* ---- block ---- */
.block_m {
  position: absolute;
  width: 666px;
  height: 216px;
  bottom: 25px;
  left: 345px;
  pointer-events: auto;
}

.block_m table {
  position: absolute;
  top: 46px;
  left: 12px;
  width: 642px;
  height: 153px;
  border-top: 2px solid #1a4887;
  border-bottom: 2px solid #1a4887;
  font-size: 12px;
}

.block_m td {
  border-left: 1px solid #1a4887;
  padding-left: 5px;
}
.block_m .name {
  color: #2e7bd0;
  text-align: right;
  padding-right: 5px;
  border-left: none;
}
.block_m .rank,
.block_m .per {
  color: #fff;
}
.block_m .chal,
.block_m .sal,
.block_m .cnt
 {
  color: #70b4fe;
}


/* ---- block ---- */
.block_r {
  position: absolute;
  width: 326px;
  height: 216px;
  bottom: 25px;
  right: 20px;

  pointer-events: auto;
}


/* --- */
.ext_link {
  display: inline-block;
  width: 23px;
  height: 23px;
  cursor: pointer;
  pointer-events: auto;
  background: url(../img/ext_link1.png) no-repeat center center;
}

#ext_link1 {
  position: absolute;
  top: 0px;
  right: 28px;
}



.block-frame4corner .header .tab {
  display: inline-block;
  padding: 0 0 5px;
  margin: 0 15px 0 0;
  color: #70b4fe;
  font-size: 14px;
  cursor: pointer;
}
.block-frame4corner .header .tab.selected {
  color: #FFF;
  border-bottom: 1px solid #FFF;
}



#legend_cb {
  position: absolute;
  top: 78px;
  right: 18px;
  height: 34px;
  width: 85px;
  z-index: 1000;
}
#legend_cb .combobox_label
{
  padding: 6px 6px;
  background-image: url(../img/combobox_arr.png);
  background-repeat: no-repeat;
  background-position: 121px center;
  border-radius: 4px;
  background-color: #113063; 
}


#switch2earth {
  position: absolute;
  color: #c5defe;
  right: 10px;
  bottom: 255px;
  width: 120px;
  height: 110px;
  padding-top: 95px;
  text-align: center;
  cursor: pointer;
  pointer-events: auto;
  z-index: 21;
  background-image: url(../img/map2d3d_big.png?2);
  background-repeat: no-repeat;
}

#switch2earth.bg1 {
  background-position: 0 0;
}
#switch2earth.bg2 {
  background-position: -120px 0;
}
