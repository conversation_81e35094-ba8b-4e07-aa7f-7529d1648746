
/*营销概览*/

body {
  background: url(../img/a2.1.bg.png) no-repeat top center;
}


#map_tooltip {
  z-index: 9999; 
  top: 0px; 
  left: 0px; 
  position:absolute; 
  width: 200px; 
  height:130px; 
  border: 1px solid #0098FB; 
  border-radius:3px; 
  background:#005CC2;
  display: none;
  pointer-events: auto;
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
  overflow: hidden;
}
#map_tooltip div{
  border-radius:2px; 
}
#map_tooltip table{
  margin: 5px 0;
  width: 100%;
}
#map_tooltip .cl1{
  padding:0 8px 0 8px; 
  text-align: right;
  color: #99CCFF;
  width: 82px;
}
#map_tooltip .cl2{
  text-align: left;
}


.pagetitle{
  position: absolute;
  width: 100%;
  height: 36px;
  top: 82px;
  text-align: center;
  background: url(../img/title-bot-line.png) no-repeat center 25px;
}
.maintitle{
  color: #99B2CB;
  width: 350px;
  font-size: 27px;
  padding: 5px 20px;
  margin: 0 auto;
  text-shadow: 0 0 10px #021121,0 0 10px #021121,0 0 20px #062b52,0 0 20px #062b52;
}
.submaintitle{
  color: #99B2CB;
  width: 350px;
  font-size: 22px;
  padding: 0 20px;
  margin: 0 auto;
  text-shadow: 0 0 10px #021121,0 0 10px #021121,0 0 20px #062b52,0 0 20px #062b52;
}

.legend{
  position: absolute;
  width: 200px;
  height: 200px;
  top: 600px;
  left: 340px;
}

.legend .row{
  height: 25px;
  position: relative;
}
.legend .dot{
  position: absolute;
  height: 30px;
  font-size: 30px;
}
.legend .per{
  font-size: 12px;
  position: absolute;
  height: 30px;
  left: 25px;
  top: 15px;

}
.legend .lg0{
  color: #25C4F9 !important;
}
.legend .lg1{
  color: #00B259 !important;
}
.legend .lg2{
  color: #E0A100 !important;
}
.legend .lg3{
  color: #BF00FF !important;
}
.legend .lg4{
  color: #B00000 !important;
}


#bigmap {
  position: absolute;
  width: 80%;
  height: 80%;
  top: 15%;
  left: 10%;
  background: url(../img/chinamap.png) no-repeat center center;
  background-size: contain;
}

#bigmap .pin {
  position: absolute;
  width: 100px;
  height: 83px;
  text-align: center;
}
#bigmap .pin0 {
  background: url(../img/pin-0.png) no-repeat top center;
}
#bigmap .pin1 {
  background: url(../img/pin-1.png) no-repeat top center;
}
#bigmap .pin2 {
  background: url(../img/pin-2.png) no-repeat top center;
}
#bigmap .pin3 {
  background: url(../img/pin-3.png) no-repeat top center;
}
#bigmap .pin4 {
  background: url(../img/pin-4.png) no-repeat top center;
}
#bigmap .per {
  position: absolute;
  top: 13px;
  left: 5%;
  width: 95%;
  font-size: 22px;
  font-weight: bold;
}
#bigmap sup {
  font-size: 12px;
  font-weight: normal;
}

#bigmap .lb {
  position: absolute;
  bottom: 0px;
  width: 100%;
  font-size: 11px;
}


.select_lb {
  position: absolute;
  left: 310px;
  top: 169px;
  width: 80px;
  pointer-events: auto;
  font-size: 16px;
}
.select_month {
  position: absolute;
  left: 380px;
  top: 166px;
  width: 100px;
  pointer-events: auto;
  font-size: 18px;
}





.panel_l{
  position: absolute;
  width: 280px;
  height: 130px;
  left: 10px;
  top: 165px;
  pointer-events: auto;
}
.panel_l .cont{
  padding: 15px 0 15px 0;
}
.panel_l .row{
  padding-bottom: 10px;
}
.panel_l .row .l{
  display: inline-block;
  text-align: right;
  height: 20px;
  width: 80px;
}
.panel_l .row .r{
  display: inline-block;
  text-align: left;
  height: 20px;
  width: 150px;
  padding-left: 10px;
}



.panel_l2{
  position: absolute;
  width: 280px;
  height: 156px;
  left: 10px;
  top: 305px;
  pointer-events: auto;
}
.panel_l2 .cont{
  padding: 15px 0 15px 0;
}
.panel_l2 .row{
  padding-bottom: 6px;
}
.panel_l2 .row .l{
  display: inline-block;
  text-align: right;
  width: 110px;
}
.panel_l2 .row .r{
  display: inline-block;
  text-align: left;
  width: 150px;
  padding-left: 10px;
  font-size: 18px;
}


.panel_l3{
  position: absolute;
  width: 280px;
  height: 276px;
  left: 10px;
  top: 471px;
  pointer-events: auto;
}
.panel_l3 table{
  width: 100%;
  height: 100%;
}
.panel_l3 .t-body td {
  text-align: left;
  padding: 3px;
  font-weight: normal;
  font-size: 13px;
  height: 26px;
}
.panel_l3 tr {
  border-bottom: 1px solid #01395f;
}
.panel_l3 td.col1 {
  text-align: right;
}
.panel_l3 td.bar {
  width:100px;
}
.panel_l3 .t-body tr:last-child{
  border-bottom: none;
}
.panel_l3 .bar div{
  height: 7px;
  border-radius: 2px;
}




.panel_r1{
  position: absolute;
  width: 280px;
  height: 205px;
  right: 10px;
  top: 110px;
  pointer-events: auto;
  
}

.panel_r2{
  position: absolute;
  width: 280px;
  height: 235px;
  right: 10px;
  top: 325px;
  pointer-events: auto;
}

.panel_r3{
  position: absolute;
  width: 280px;
  height: 178px;
  right: 10px;
  top: 570px;
  pointer-events: auto;
}
















.p2_panel_l{
  position: absolute;
  width: 280px;
  height: 130px;
  left: 10px;
  top: 135px;
  pointer-events: auto;
}
.p2_panel_l .cont{
  padding: 15px 0 15px 0;
}
.p2_panel_l .row{
  padding-bottom: 10px;
}
.p2_panel_l .row .l{
  display: inline-block;
  text-align: right;
  height: 20px;
  width: 80px;
}
.p2_panel_l .row .r{
  display: inline-block;
  text-align: left;
  height: 20px;
  width: 150px;
  padding-left: 10px;
}


.p2_panel_l1{
  position: absolute;
  width: 280px;
  height: 156px;
  left: 10px;
  top: 274px;
  pointer-events: auto;
}



.p2_panel_l2{
  position: absolute;
  width: 280px;
  height: 156px;
  left: 10px;
  top: 438px;
  pointer-events: auto;
}
.p2_panel_l2 .cont{
  padding: 15px 0 15px 0;
}
.p2_panel_l2 .row{
  padding-bottom: 6px;
}
.p2_panel_l2 .row .l{
  display: inline-block;
  text-align: right;
  width: 110px;
}
.p2_panel_l2 .row .r{
  display: inline-block;
  text-align: left;
  width: 150px;
  padding-left: 10px;
  font-size: 18px;
}


.p2_panel_l3{
  position: absolute;
  width: 280px;
  height: 146px;
  left: 10px;
  top: 603px;
  pointer-events: auto;
}





.p2_panel_r1{
  position: absolute;
  width: 280px;
  height: 205px;
  right: 10px;
  top: 110px;
  pointer-events: auto;
  
}

.p2_panel_r2{
  position: absolute;
  width: 280px;
  height: 235px;
  right: 10px;
  top: 325px;
  pointer-events: auto;
}

.p2_panel_r3{
  position: absolute;
  width: 280px;
  height: 178px;
  right: 10px;
  top: 570px;
  pointer-events: auto;
}

