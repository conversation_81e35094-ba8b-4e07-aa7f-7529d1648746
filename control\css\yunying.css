body{
	margin: 0;
	padding: 0;
}

div{
	position: relative;
}
span{
	vertical-align: middle;
	display: inline-block;
}
.page-wrapper {
	position: absolute;
	top: 0px;
	width: 1920px;
	height: 1080px;
	overflow-x: hidden;
	overflow-y: hidden;
	pointer-events: all;
}
.fl{
	float: left;
}
.mask{
	display: none;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: black;
	z-index: 1001;
	-moz-opacity: 0.5;
	opacity: .50;
	filter: alpha(opacity=50);
}



.btnActive{
	color: red;
	font-weight: bold;
}

.ipad{
	width: 768px;
	height: 1024px;
	background: #041337;
	padding: 10px;
	margin: 0 auto;
}


.companylist,
.baselist{
    display: none;
    position: absolute;
    top: 20px;
    left: calc( 50% - 380px );
    padding: 10px 0;
    width: 760px;
    margin-left: 0 auto;
    z-index: 1002;
    background: #041337;
    border: 3px solid #012869;
    border-radius: 20px;
}

.companylist span,
.baselist span{
	padding:5px 10px;
	margin:10px 20px;
	font-size: 48px;
	background: #012869;
	border-radius: 10px;
	cursor: pointer;
	color: #ffffff;
}

.baselist{
	height: auto;
}

.closeBtn{
	font-size: 36px;
	text-align: right;
	padding: 0 30px;
	cursor: pointer;
}

.ipad{
	margin: 0 auto;
}
.ipad .row {
	margin-top: 20px;
	cursor: pointer;
}

.ipad .row button{
	vertical-align: middle;
	width: 750px;
	min-height: 80px;
	font-size: 36px;
}


.aocRow .aoc_col_1{
	width: 70px;
	margin-left: 20px;
	font-size: 28px;
	letter-spacing: 20px;
	text-align: center;
	writing-mode: vertical-lr;
	writing-mode: tb-lr;
}

.aocRow .aoc_col_1,
.aocRow .aoc_col_2{
	height: 500px;
}

.aocRow .aoc_col_2 .aoc_btn_1{
	width: 530px;
}

.aocRow .aoc_col_2 .aoc_btn_2{
	width: 125px;
}

.aocRow .aoc_col_2 .aoc_btn_3,
.aocRow .aoc_col_2 .aoc_btn_4,
.aocRow .aoc_col_2 .aoc_btn_5,
.aocRow .aoc_col_2 .aoc_btn_6{
	width: 660px;
}

.ipad .row .row_btn_3_1,
.ipad .row .row_btn_4_1,
.ipad .row .row_btn_5_1{
	width: 350px;
}

.ipad .row .row_btn_4_2{
width:400px;
}


.ipad .row .row_btn_3_3,
.ipad .row .row_btn_4_3{
	width: 200px;
}

.ipad .row .row_btn_5_2{
	width: 150px;
}

.ipad .row .row_inp_5_1{
    width: 238px;
    min-height: 74px;
    font-size: 28px;
    vertical-align: middle;
    text-align: center;
    overflow: hidden;
}


.datetype-container{
	display: flex;
	height: 40px;
	-webkit-box-align: center;
	-webkit-align-items: center;
	align-items: center;
	flex-direction: row;
  }
  .datetype-container .dateType {
	font-size: 24px;
	height: 40px;
	width: 40px;
	margin-left: 20px;
	border-radius: 20px  ;
	color: #44a3f4;
	line-height: 40px;
	background-color: #094a91;
	cursor: pointer;
	text-align: center;
  }
  
  .datetype-container .selected {
	color: #FFFFFF;
	background-color: #00a0e9;
  }

  .title{
	color:#ffffff;
	padding:10px;
	width: 90%;
	min-height: 60px;
	font-size: 30px;
  }

  .btns{
	  vertical-align: middle;
	  width: 90%;
	  min-height: 60px;
	  font-size: 30px;
	  line-height: 60px;
	  color: #000000;
  }

  .con_flex_column{display:flex;  display: -webkit-flex; -webkit-flex-flow: column;}
.con_flex_row{display:flex;  display: -webkit-flex; -webkit-flex-flow: row;}
.flex_none{-webkit-flex:none; flex: none;}
.flex_1{-webkit-flex:1; flex: none;}


#date_select {
  width: 400px;
  height: 72px;
  font-size: 28px;
  text-align: center;
  overflow: hidden;
  color: #44a3f4;
  border-radius: 10px;
	border:  solid 1px rgba(0, 183, 238, 0.2);
}
#date_select div {
  line-height: 72px;
  width: 100px;
  cursor: pointer;
  user-select: none;

}
#date_select .selected {
  background-color: #265aa3;
  color: #ffffff;
}

#main_cb_week{
	height: 36px;
}
.combobox {
    text-align: left;
    position: relative;
    display: inline-block;
    width: 100%;
    height: 20px;
    vertical-align: middle;
}
.combobox_label{
	padding :0;
	background: url(../../img/combobox_arr.png) no-repeat 125px center;
	border:none;
	line-height: 36px;
	color: #44a3f4;
	height: 36px;
	width: 100%;
	font-size: 14px;
	border-radius: 5px;
	padding-left: 10px;
	margin-top: -1px;
	margin-left: -1px;
	border:1px solid transparent;
	border: solid 1px rgba(0, 183, 238, 0.2);
  }
  
  .combobox_list .item{
	line-height: 36px;
	color: #44a3f4;
	height: 36px;
	font-size: 14px;
	padding-left: 10px;
	width: 100%;
  }
  

.combobox_list {
    position: absolute;
    display: block;
    width: 100%;
    border: 1px solid rgba(41, 140, 232, 0.6);
    border-radius: 2px;
    background-color: #113063;
    overflow-y: scroll;
    max-height: 135px !important;
    pointer-events: auto;
	transform: rotate(0deg);
	max-height: 500px !important;
}

.datetimepicker{
	zoom: 2;
	-moz-transform-origin : 'left top';
	-moz-transform : scale(2,2);
}

.date-block{
	height: 300px;
	z-index: 1000;
}
