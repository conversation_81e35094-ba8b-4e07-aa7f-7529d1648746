
/**/

.page-wrapper {
  
}


.pagetitle{
  position: absolute;
  width: 100%;
  height: 54px;
  top: 58px;
  text-align: center;

}
.maintitle{
  position: absolute;
  top: 18px;
  left: 390px;
  color: #fff;
  width: 380px;
  font-size: 24px;
}

#main_cb_week {
  position: absolute;
  top: 19px;
  left: 738px;
  height: 34px;
  width: 85px;
  z-index: 1000;
}
#main_cb_month {
  position: absolute;
  top: 19px;
  left: 822px;
  height: 34px;
  width: 104px;
  z-index: 1001;
}

#main_cb_week .combobox_label
{
  padding: 6px 6px;
  background-image: url(../img/combobox_arr.png);
  background-repeat: no-repeat;
  background-position: 63px center;
  border-radius: 4px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}
#main_cb_month .combobox_label
{
  padding: 6px 6px;
  background-image: url(../img/combobox_arr.png);
  background-repeat: no-repeat;
  background-position: 83px center;
  border-radius: 4px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: none;
}

.combotab_selected .combobox_label {
  background-color: #2a81d2;
}
.combotab .combobox_label {
  background-color: #0950a2;
  opacity: 0.5;
  color: #8abfff;
}

#week_date_range {
  position: absolute;
  top: 58px;
  left: 738px;
  width: 160px;
  font-size: 14px;
  text-align: center;
  border: 1px solid #1f5fa8;
  border-radius: 2px;
  padding: 3px 0 5px 0px;
  background-color: #021d39;
  box-shadow: 0 1px 8px #021121;

  display: none;
  
}


/* --- */
.block_l1 {
  height: 410px;
  left: 15px;
  top: 100px;
}
.block_l1 .cont{
  height: 384px;
}
.block_l1 .cont .row1{
  position: absolute;
  width: 100%;
  height: 166px;
  pointer-events: none;
}
.block_l1 .cont .row1 .b1{
  position: absolute;
  left: 15px;
  top: 50px;
}
.block_l1 .cont .row1 .b1 .kpi{
  color: #a3d800;
  display: block;
  line-height: 22px;
  height: 22px;
}
.block_l1 .cont .row1 .b2{
  position: absolute;
  right: 15px;
  top: 50px;
  text-align: right;
}
.block_l1 .cont .row1 .b2 .kpi{
  color: #3ec4fd;
  display: block;
  line-height: 22px;
  height: 22px;
}
.block_l1 .cont .row1 .c{
  position: absolute;
  left: 128px;
  top: 50px;
  text-align: center;
}
.block_l1 .cont .row1 .c .r1{
  color: #a3d800;
}
.block_l1 .cont .row1 .c .r2{
  color: #3ec4fd;
}
.block_l1 .cont .row1 .c .vs{
  display: block;
  line-height: 22px;
  height: 20px;
}

.block_l1 .cont .row1 canvas{
  position: absolute;
  left:87px; 
  top:15px; 
  background:rgba(255,0,0,0);
}

.block_l1 .cont .row2{
  position: absolute;
  height:216px; 
  width: 100%;
  top:166px; 
  pointer-events: auto;
}

.block_l1 .tab {
  position: absolute;
  width: 50%;
  height: 32px;
  top: 0;
  background-color: #1a4886;
  color: #3d93df;
  text-align: center;
  line-height: 32px;
  font-size: 14px;
  cursor: pointer;
}
.block_l1 .selected {
  color: #fff;
  background-color: #2a81d2;
}
.block_l1 .tab1 {
  left: 0px;
}
.block_l1 .tab2 {
  left: 50%;
}
.block_l1 .tabc{
  position: absolute;
  width: 100%;
  height: 183px;
  top: 32px;
}

.block_l1 .tabc2 table{
  height: 163px;
  margin-top: 10px;
  font-size: 12px;
}
.block_l1 .tabc2 .c1{
  text-align: right;
  padding-left: 25px;
  color: #47aafd;
}
.block_l1 .tabc2 .c2{
  text-align: right;
  padding-left: 3px;
  color: #a8d5ff;
}
.block_l1 .tabc2 .c3{
  padding-left: 3px;
}
.block_l1 .tabc2 .c3 .bar{
  display: inline-block;
  height: 3px;
  margin-bottom: 4px;
  margin-right: 3px;
  background: rgb(1,70,139);
  background: -moz-linear-gradient(left,  rgba(1,70,139,1) 0%, rgba(112,218,252,1) 100%);
  background: -webkit-linear-gradient(left,  rgba(1,70,139,1) 0%,rgba(112,218,252,1) 100%);
  background: linear-gradient(to right,  rgba(1,70,139,1) 0%,rgba(112,218,252,1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#01468b', endColorstr='#6fd9fd',GradientType=1 );
}



/* --- */

.block_l2 {
  height: 218px;
  left: 15px;
  top: 530px;
}
.block_l2 .cont{
  height: 190px;
  padding: 12px 0 12px 12px;
}




.cont .blk{
  display: block;
}
.cont .blk span{
  display: inline-block;
  margin-right: 5px;
}
.cont .blk .val{
  margin-right: 12px;
}
.cont .blk.con{
  margin-bottom: 10px;
}
.cont .itm{
  display: inline-block;
  height: 40px;
  padding-right: 4px;
  padding-top: 3px;
}
.cont .itm .nm{
  display: block;
  color: #99ccff;
}
.cont .itm .val{
  font-weight: bold;
}


/* --- */




/* --- */
.block_r1 {
  right: 15px;
  top: 100px;
}
.block_r1 .cont{
  padding: 12px 0 12px 12px;
  height: 292px;
}
.block_r1 .row1{
  display: block;
  height: 88px;
}
.block_r1 .blk1{
  position: absolute;
  top: 15px;
  left: 15px;
  width: 150px;
}
.block_r1 .blk2{
  position: absolute;
  top: 15px;
  left: 165px;
  width: 150px;
}




/* --- */
.block_r2 {
  right: 15px;
  top: 437px;
}
.block_r2 .cont{
  top: 25px;
  height: 283px;
  padding: 12px 0 12px 12px;
}
.block_r2 .tab{
  position: absolute;
  text-align: center;
  font-size: 14px;
  font-weight: bold;
  color: #011f4d;
  padding-top: 1px;
  cursor: pointer;
}
.block_r2 .tab{
  width: 138px;
  height: 24px;
  background: url(../img/a3.2_tab_off.png) no-repeat;
}
.block_r2 .tab.selected{
  background: url(../img/a3.2_tab_on.png) no-repeat;
}

.block_r2 .tab1{
  left: 18px;
}
.block_r2 .tab2{
  right: 18px;
}
.block_r2 .row1{
  display: block;
  height: 78px;
}
.block_r2 .blk1{
  position: absolute;
  top: 15px;
  left: 15px;
  width: 150px;
}


/* --- */
#map_legend{
  position: absolute;
  left: 890px;
  top: 650px;
  width: 121px;
  height: 81px;
  background: url(../img/a3.1.legend.png) no-repeat 0 0;
}
#map_legend .tit{
  position: absolute;
  width: 100%;
  top: 2px;
  text-align: center;
}
#map_legend .itm{
  position: absolute;
  width: 100px;
  left: 24px;
}
#map_legend .l1{
  top: 26px;
}
#map_legend .l2{
  top: 44px;
}
#map_legend .l3{
  top: 62px;
}




/* --- */

#popover_map {
  position: absolute;
  z-index: 888;
  width: 298px;
  height: 298px;

  top:  -1000px;

  background: url(../img/a3.2.pop_map.png) no-repeat 0 0;

  -moz-transform-origin: 50% 50%;
  -wekkit-transform-origin: 50% 50%;
  -ms-transform-origin: 50% 50%;
  transform-origin: 50% 50%;

  opacity: 0;
}


#popover_map .title{
  position: absolute;
  width: 100%;
  height: 50px;
  top: 34px;
  text-align: center;
  color: #92ebf6;
}
#popover_map .blk {
  display: block;
  line-height: 18px;
}
#popover_map .bigtt {
  line-height: 14px;
}
#popover_map .col1{
  position: absolute;
  top: 85px;
  left: 30px;
  width: 170px;
  height: 160px;

}
#popover_map .col2{
  position: absolute;
  top: 85px;
  left: 160px;
  width: 160px;
  height: 160px;
}





/* --- */
#popover_chart {
  position: absolute;
  z-index: 999;
  width: 640px;
  height: 320px;
  top: 270px;
  left: 362px;
  border: 1px solid #237fd5;
  background: #083476;
  display: none;
  pointer-events: auto;
}

#popover_chart .tit{
  position: absolute;
  width: 100%;
  height: 55px;
  background: #123e92;
  color: #7cc3ff;
  font-size: 24px;
  line-height: 50px;
  text-align: center;
  font-weight: bold;
}

#popover_chart .btnx{
  position: absolute;
  width: 30px;
  height: 30px;
  top: 0;
  right: 0;
  
  border-left:  1px solid #237fd5;
  border-bottom:  1px solid #237fd5;
  background: #083476 url(../img/pop_x.png) no-repeat center center;
  pointer-events: auto;
  cursor: pointer;
}
#popover_chart .legends{
  position: absolute;
  width: 100%;
  height: 20px;
  bottom: 14px;
  font-size: 14px;
  color: #91c9ff;
  text-align: center;
}
#popover_chart .legend{
  display: inline-block;
  margin:  0 10px;
}
#popover_chart .legend1{
  padding-left: 15px;
  background: #083476 url(../img/a2.1.legend1.png) no-repeat 0 center;
}
#popover_chart .legend2{
  padding-left: 30px;
  background: #083476 url(../img/a3.2.legend2.png) no-repeat 0 center;
}
#popover_chart .chartblock{
  position: absolute;
  top: 80px;
}


/* --- */



.btn_view_chart {
  position: absolute;
  width: 184px;
  height: 24px;
  cursor: pointer;
  text-align: center;
  line-height: 24px;
  font-size: 12px;
  color: #fff;
  pointer-events: auto;
  
  background: #2a81d2;
  
  left: 8px;
  bottom: 15px;

  clip-path:
    polygon(
        0 0, calc(100% - 10px) 0, 100% 10px, 
        100% 100%, 10px 100%, 0 calc(100% - 10px), 0 0
    );
}

.btn_view_chart .inside {
  position: absolute;
  width: 100%;
  height: 100%;
  padding-left: 10px;
  
  background: rgb(28,92,184);
  background: -moz-linear-gradient(top,  rgba(28,92,184,1) 0%, rgba(17,64,138,1) 100%);
  background: -webkit-linear-gradient(top,  rgba(28,92,184,1) 0%,rgba(17,64,138,1) 100%);
  background: linear-gradient(to bottom,  rgba(28,92,184,1) 0%,rgba(17,64,138,1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1c5cb8', endColorstr='#11408a',GradientType=0 );

  clip-path:
    polygon(
        1px 1px, calc(100% - 10px) 1px, calc(100% - 1px) 10px, 
        calc(100% - 1px) calc(100% - 1px), 10px calc(100% - 1px), 1px calc(100% - 10px), 1px 1px
    );
}

.btn_view_chart:hover .inside {
  background: #1954b0;
}
.btn_view_chart:active .inside {
  background: #153970;
}

.btn_view_chart span {
  padding: 0 5px 0 14px;
  background: url(../img/a2.1.ico3.png) no-repeat 0 3px;

  
}



.ranking {
  position: absolute;
  width: 180px;
  height: 200px;
  left: 350px;
  bottom: 30px;
  font-size: 12px;
}
.ranking .itm{
  display: block;
  color: #59acff;
  line-height: 18px;
}
.ranking .itm .no{
  display: inline-block;
  text-align: right;
  width: 20px;
  padding-right: 3px;
}
.ranking .itm .nm{
  display: inline-block;
}
.ranking .tit{
  display: block;
  padding-top: 10px;
}

#selected_mapmarkpoint{
  position: absolute;
  width: 36px;
  height: 36px;
  background: url(../img/mapmarkpoint_L.svg) no-repeat center center;
  background-size: 36px 36px;
}