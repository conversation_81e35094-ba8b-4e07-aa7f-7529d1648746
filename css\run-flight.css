
/*机场延误*/


#map_tooltip {
  z-index: 9999; 
  top: 0px; 
  left: 0px; 
  position:absolute; 
  width: 230px; 
  height:100px; 
  border: 1px solid #0098FB; 
  border-radius:3px; 
  background:#005CC2;
  display: none;
  pointer-events: auto;
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
  overflow: hidden;
}
#map_tooltip div{
  border-radius:2px; 
}


.normalrate {
  position: absolute;
  width: 60px;
  height: 60px;
  top: 201px;
  left: 215px;
  padding-top: 15px;
  text-align: center;
  pointer-events: auto;
  border-radius: 40px;
  background: #062b52;
}

.normalrate .lb{
  font-size: 12px;
  line-height: 12px;
}
.normalrate .val{
  font-size: 18px;
  line-height: 22px;
}

.legend{
  position: absolute;
  width: 300px;
  height: 177px;
  bottom: 0px;
  left: 320px;
  pointer-events: none;
}
.legend .tit{
  height: 18px;
  position: relative;
}
.legend .itmrow{
  height: 25px;
  position: relative;
}
.legend .dot{
  position: absolute;
  height: 30px;
  font-size: 30px;
}
.legend .per{
  font-size: 12px;
  position: absolute;
  height: 30px;
  left: 25px;
  top: 15px;

}
.lg0{
  color: #FF0000 !important;
}
.lg1{
  color: #FF7F00 !important;
}
.lg2{
  color: #f9ff5f !important;
}
.lg3{
  color: #0bc80b !important;
}

.updatetime {
  position: absolute;
  text-align: right;
  width: 300px;
  height: 30px;
  bottom: 10px;
  right: 320px;
  font-size: 12px;
  opacity: 0.6;
}


.pagetitle{
  position: absolute;
  width: 100%;
  height: 36px;
  top: 82px;
  text-align: center;
  background: url(../img/title-bot-line.png) no-repeat center 25px;
}
.maintitle{
  color: #99B2CB;
  width: 350px;
  font-size: 27px;
  padding: 5px 20px;
  margin: 0 auto;
  text-shadow: 0 0 10px #021121,0 0 10px #021121,0 0 20px #062b52,0 0 20px #062b52;
}
.submaintitle{
  color: #99B2CB;
  width: 350px;
  font-size: 22px;
  padding: 0 20px;
  margin: 0 auto;
  text-shadow: 0 0 10px #021121,0 0 10px #021121,0 0 20px #062b52,0 0 20px #062b52;
}


.panel_l{
  position: absolute;
  width: 276px;
  height: 282px;
  left: 10px;
  top: 152px;
  pointer-events: auto;
}
.panel_l .itm{
  padding: 10px;
  height: 80px;
  border-bottom: 1px solid #01395f;
  vertical-align: middle;
  padding: 15px 0 0 15px;
}
.panel_l .itm:last-child{
  border-bottom: none;
}
.panel_l .itm .l{
  display: inline-block;
  vertical-align: middle;
  height: 50px;
  width: 50px;
  margin-right: 10px;
  
}
.panel_l .itm .r{
  display: inline-block;
  vertical-align: middle;
  height: 50px;
  width: 150px;
  
}
.panel_l .itm .r .tit{
  font-weight: bold;
}
.panel_l .itm .r .per{
  font-size: 24px;
  font-weight: bold;
}


.panel_l2{
  position: absolute;
  width: 276px;
  height: 301px;
  left: 10px;
  top: 449px;
  pointer-events: auto;
}

.panel_l2 .t-body{
}
.panel_l2 table{
  width: 100%;
}
.panel_l2 .t-body td,
.panel_l2 .t-body th {
  text-align: center;
  padding: 4px;
  border-bottom: 1px solid #01395f;
  font-weight: normal;
  font-size: 12px;
}
.panel_l2 .t-body td {
  height: 36px;
}
.panel_l2 .t-body th {
  padding-top: 8px;
  padding-bottom: 16px;
}
.panel_l2 .t-body th.sortable {
  cursor: pointer;
}
.panel_l2 .t-body .sortdown {
  background: url(../img/combo_arr.png) no-repeat center 28px;
}
.panel_l2 tr {
  text-align: center;
  border-bottom: 1px solid #01395f;
}
.panel_l2 .t-body tr:last-child{
  border-bottom: none;
}
.panel_l2 .col1 {
  word-break: keep-all;
  text-align: right;
  width: 66px;
}
.panel_l2 .col2 {
  width: 48px;
}
.panel_l2 .col3 {
  width: 48px;
}
.panel_l2 .subtit {
  padding: 15px 0 10px 15px;
  font-weight: bold;
}
#airport_sec {
  height:220px;
  overflow: hidden;
  margin:0;
  padding: 0;
}
#airport_sec1,
#airport_sec2{
height:auto;
}


.panel_r{
  position: absolute;
  width: 290px;
  height: 246px;
  right: 10px;
  top: 91px;
  pointer-events: auto;
}

.panel_r2{
  position: absolute;
  width: 290px;
  height: 226px;
  right: 10px;
  top: 351px;
  pointer-events: auto;
}

.panel_r3{
  position: absolute;
  width: 290px;
  height: 160px;
  right: 10px;
  top:591px;
  pointer-events: auto;
}


