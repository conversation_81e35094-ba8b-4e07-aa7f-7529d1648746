showLoading();


var current_company_code;


// 可显示的历史 数量
var limit = 20;
// 日期类型
var date_type = 'L';


var audio_txt_obj = {};

// -------------------------------------------------------

//                          KPI

// -------------------------------------------------------



var weekList;
var monthList;


var all_delay_cause_data; // 存储各个公司延误原因
var all_company_data; // 存储各个公司基本指标
var all_company_data_ac; // 飞机日利用率 计算指标
var all_company_data_task; // 存储预算任务
var all_company_data_all_week; // 存储过去52周的指标

var all_company_noraml_rate;
var all_company_noraml_rate_ori;
var all_company_noraml_rate_arr;
var all_company_noraml_rate_dep;

var all_company_avg_noraml_rate;
var all_company_avg_noraml_rate_ori;
var all_company_avg_noraml_rate_arr;

var weekDateRangeList; //例会周的日期对应表


var fetchingKpiData = false;

var currentSwitchIndex;
var prevSwitchIndex;


function getAllCompanyKpiData() {

	if (companylist.length == 0) {
		setTimeout(getAllCompanyKpiData, 0);
		return;
	}

	if (fetchingKpiData) return;


	// check url hash
	var hash = window.location.hash.substr(1);
	if (hash && companyCode2Name[hash] != undefined) {
		current_company_code = hash;
	} else {
		current_company_code = parent_company;
	}


	fetchingKpiData = true;

	all_company_data = {};

	var comp_code = current_company_code;

	var len = companylist.length;
	var codelist = [];
	for (var i = 0; i < len; i++) {

		var dat = companylist[i];
		codelist.push(dat.code);
	}

	var loadingInProgress = 0;

	var kpi_list = [
		'NORMAL_RATE_ZT', //正常率-数据来源中台
		'NORMAL_NO_T', //正常班次
		'SCH_NO', //计划班次
		'EXCUTED_NO', //执行班次

		'ORI_NORMAL_NO', //始发正常班次
		'ORI_NO_SCH', //始发计划班次
		'DEP_NORMAL_NO', //出港正常班次
		'DEP_NO', //出港班次
		'ARR_NORMAL_NO', //到港正常班次
		'ARR_NO', //到港班次
		'DISPAT_NORMAL_NO', //放行正常班次
		'DISPAT_NO', //放行班次


		'SCH_NO_INT', //国际计划班次
		'NORMAL_NO_T_INT', //国际正常班次

	];

	// -------------------------------------------------------
	// 周
	// -------------------------------------------------------


	// 本期

	loadingInProgress++;
	var param = {
		'SOLR_CODE': 'FAC_COMP_KPI',
		'COMP_CODE': codelist.join(','),
		'KPI_CODE': kpi_list.join(','),
		'VALUE_TYPE': 'kpi_value_d', //本期
		'DATE_TYPE': 'L,M',
		"OPTIMIZE": 1,
		'LIMIT': limit
	}

	$.ajax({
		type: 'post',
		url: "/bi/query/getkpi",
		contentType: 'application/json',
		dataType: 'json',
		async: true,
		data: JSON.stringify(param),
		success: function(response) {

			if (response.data != undefined) {

				all_company_data['kpi_value_d'] = response.data;

				var ddd = response.data['HU']['SCH_NO']['L'];

				weekList = [];
				for (var date in ddd) {
					weekList.push(date);
				}

				getWeekDateRange(weekList);


				var cblist = [];
				var len = weekList.length;
				for (var i = 0; i < len; i++) {
					var date = weekList[i];
					// Week: *********
					//var label = date.substr(0,4) + '年第' + Number(date.substr(4,3)) + '周';
					//20170419 双双要求：显示的周数+1
					//20170614 双双要求：A3.2 运行页面 显示的周数不用+1
					var label = '第' + (Number(date.substr(4, 3))) + '周 '; //+ response[date];
					cblist.unshift({
						'label': label,
						'data': date
					});
				}
				createComboBox('main_cb_week', cblist, 84, 240, updateAllKpi, 1);


				$('#main_cb_week .combobox_label').on('click', function(event) {
					event.preventDefault();

					if (date_type == 'L') {
						return;
					}

					$('#main_cb_week').addClass('combotab_selected');
					$('#main_cb_week').removeClass('combotab');
					$('#main_cb_month').addClass('combotab');
					$('#main_cb_month').removeClass('combotab_selected');

					date_type = 'L';
					updateAllKpi();

				});

				// 显示 week 日期范围
				$('#main_cb_week .combobox_label').on('mouseover', function(event) {
					event.preventDefault();
					if (weekDateRangeList) {
						var date = $('#main_cb_week').attr('data');
						$('#week_date_range').text(weekDateRangeList[date]);
						$('#week_date_range').fadeIn();
					}
				});

				// 隐藏 week 日期范围
				$('#main_cb_week .combobox_label').on('mouseout', function(event) {
					event.preventDefault();
					if (weekDateRangeList) {
						$('#week_date_range').fadeOut();
					}
				});



				// M
				var date = new Date();
				var month = date.getMonth() + 1;
				var day = date.getDate();
				if (month < 10) {
					month = '0' + month;
				}
				var nowmonth = date.getFullYear() + '' + month;

				var datlist = response.data['HU']['SCH_NO']['M'];
				var cblist = [];
				monthList = [];
				for (var date in datlist) {
					if (date <= nowmonth) {
						monthList.push(date);
						var label = date.substr(0, 4) + '年' + Number(date.substr(4, 2)) + '月';
						cblist.unshift({
							'label': label,
							'data': date
						});
					}
				}
				createComboBox('main_cb_month', cblist, 104, 240, updateAllKpi, 1);

				$('#main_cb_month .combobox_label').on('click', function(event) {
					event.preventDefault();

					if (date_type == 'M') {
						return;
					}

					$('#main_cb_week').addClass('combotab');
					$('#main_cb_week').removeClass('combotab_selected');
					$('#main_cb_month').addClass('combotab_selected');
					$('#main_cb_month').removeClass('combotab');


					date_type = 'M';
					updateAllKpi();
				});


				// 显示 month 日期范围
				$('#main_cb_month .combobox_label').on('mouseover', function(event) {
					event.preventDefault();
					var month = $('#main_cb_month').attr('data');
					var curmonth = moment().format("YYYYMM");
					var numofdays = moment(month, "YYYYMM").daysInMonth(); // 获取一个月有几天
					var days = numofdays;
					if (days < 10) {
						days = '0' + days;
					}
					if (curmonth == month) {
						days = moment().format("DD");
					}
					$('#week_date_range').text(month + '01' + '~' + month + days);
					$('#week_date_range').fadeIn();
				});

				// 隐藏 month 日期范围
				$('#main_cb_month .combobox_label').on('mouseout', function(event) {
					event.preventDefault();
					$('#week_date_range').fadeOut();
				});
				$('#main_cb_month .combobox_label').on('click', function(event) {
					event.preventDefault();
					$('#week_date_range').fadeOut();
				});



				loadingInProgress--;
				checkDataReady();



			}

		},
		error: function() {}
	});


	//上期
	loadingInProgress++;
	var param = {
		'SOLR_CODE': 'FAC_COMP_KPI',
		'COMP_CODE': codelist.join(','),
		'KPI_CODE': kpi_list.join(','),
		'VALUE_TYPE': 'kpi_value_sq_d', //上期
		'DATE_TYPE': 'L,M',
		"OPTIMIZE": 1,
		'LIMIT': limit
	}

	$.ajax({
		type: 'post',
		url: "/bi/query/getkpi",
		contentType: 'application/json',
		dataType: 'json',
		async: true,
		data: JSON.stringify(param),
		success: function(response) {

			if (response.data != undefined) {
				all_company_data['kpi_value_sq_d'] = response.data;
				loadingInProgress--;
				checkDataReady();
			}

		},
		error: function() {}
	});



	// ------------------------------------------------------------------------
	// 延误原因 公司／非公司
	// ------------------------------------------------------------------------
	loadingInProgress++;
	var param = {
		'SOLR_CODE': 'FAC_COMP_DELAY_CAUSE_RATE_KPI',
		'COMP_CODE': codelist.join(','),
		'KPI_CODE': 'DELAY_NO',
		'VALUE_TYPE': 'kpi_value_d',
		'DATE_TYPE': 'L,M',
		'LIMIT': limit,
		'OPTIMIZE': 1
	}

	$.ajax({
		type: 'post',
		url: "/bi/query/getkpi",
		contentType: 'application/json',
		dataType: 'json',
		async: true,
		data: JSON.stringify(param),
		success: function(response) {

			checkLogin(response);

			if (response.data != undefined) {

				if (all_delay_cause_data == undefined) {
					all_delay_cause_data = {};
				}
				all_delay_cause_data['kpi_value_d'] = response.data;
				loadingInProgress--;
				checkDataReady();

			}

		},
		error: function() {}
	});


	loadingInProgress++;
	var param = {
		'SOLR_CODE': 'FAC_COMP_DELAY_CAUSE_RATE_KPI',
		'COMP_CODE': codelist.join(','),
		'KPI_CODE': 'DELAY_NO',
		'VALUE_TYPE': 'kpi_value_sq_d',
		'DATE_TYPE': 'L,M',
		'LIMIT': limit,
		'OPTIMIZE': 1
	}

	$.ajax({
		type: 'post',
		url: "/bi/query/getkpi",
		contentType: 'application/json',
		dataType: 'json',
		async: true,
		data: JSON.stringify(param),
		success: function(response) {

			checkLogin(response);

			if (response.data != undefined) {

				if (all_delay_cause_data == undefined) {
					all_delay_cause_data = {};
				}
				all_delay_cause_data['kpi_value_sq_d'] = response.data;
				loadingInProgress--;
				checkDataReady();

			}

		},
		error: function() {}
	});



	// 获取例会周对应的日期范围
	function getWeekDateRange(week_list) {
		var param = {
			"DATE_ID": week_list.join(','),
			"FIELD": "DATE_DESC" // 对应数据表字段 DATE_DESC
		}

		$.ajax({
			type: 'post',
			url: "/bi/web/datetype",
			contentType: 'application/json',
			dataType: 'json',
			async: true,
			data: JSON.stringify(param),
			success: function(response) {

				weekDateRangeList = response;

				// 获取最新的有数据的日期，用来确定当前例会周截止的日期是哪天
				var param = {
					'SOLR_CODE': 'FAC_COMP_KPI',
					'COMP_CODE': parent_company,
					'KPI_CODE': 'TRV_RATE',
					'VALUE_TYPE': 'kpi_value_d',
					'DATE_TYPE': 'D',
					"OPTIMIZE": 1,
					'LIMIT': 1
				}

				$.ajax({
					type: 'post',
					url: "/bi/query/getkpi",
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: JSON.stringify(param),
					success: function(response) {

						if (response.data != undefined) {
							var ddd = response.data[parent_company]['TRV_RATE']['D'];
							var latest_date;
							for (var da in ddd) {
								latest_date = da;
								break;
							}

							//把最新周的结束日期换成上面查到的最新日期
							if (latest_date) {
								var latest_week = 0;
								for (var week in weekDateRangeList) {
									if (Number(week) > Number(latest_week)) {
										latest_week = week;
									}
								}
								if (latest_week != 0) {
									var date_range = weekDateRangeList[latest_week];
									var arr = date_range.split('-');
									if (Number(arr[1]) > Number(latest_date) && Number(arr[
										0]) <= Number(latest_date)) {
										//weekDateRangeList[latest_week] = arr[0]+'-'+latest_date;
									}
								}
							}
						}

					},
					error: function() {}
				});


			},
			error: function() {}
		});
	}



	function checkDataReady() {
		if (loadingInProgress == 0) {
			kpiDataReady = true;
			updateAllKpi();
			hideLoading();

		}
	}



}



function getCurrentDate() {
	var date = '';
	if (date_type == 'L') {
		date = $('#main_cb_week').attr('data');
	} else if (date_type == 'M') {
		date = $('#main_cb_month').attr('data');
	}
	return date;
}



// 中间地图各个公司kpi
var itv_autoSwitchCompGrid;

function setMapKpi() {

	var idx = 0;
	var datlist = all_company_data['kpi_value_d'];
	var date = getCurrentDate();
	var len = companylist.length;
	for (var i = 0; i < len; i++) {

		var dat = companylist[i];
		var compcode = dat.code;
		if (usersCompayCodeList.indexOf(compcode) > -1) {
			var compdat = datlist[compcode];
			
			var rateStr = compdat['NORMAL_RATE_ZT'][date_type][date];
			var rate = (Number(rateStr)*100).toFixed(1);
			
			// RTD 正常率背景色
			sysCompanyKpiList.map(el=>{
				if(el.companyCode === compcode){
					rate = el.normalRate
				}
			})

			$('.center_kpi_grids .itm' + idx).attr('id', 'grid_' + compcode);
			$('.center_kpi_grids .itm' + idx).attr('compcode', compcode);

			$('.center_kpi_grids .itm' + idx).removeClass('leg0');
			$('.center_kpi_grids .itm' + idx).removeClass('leg1');
			if (rate > groupNormalRateGoal) {
				$('.center_kpi_grids .itm' + idx).addClass('leg0');
			} else {
				$('.center_kpi_grids .itm' + idx).addClass('leg1');
			}

			$('.center_kpi_grids .itm' + idx + ' .name').text(companyCode2Name[compcode]);
			$('.center_kpi_grids .itm' + idx + ' .logo').css('background-image', 'url(img/logo_' + compcode + '.png)');
			idx++;
		}
	}

	$('.center_kpi_grids .itm').off('click');
	$('.center_kpi_grids .itm').on('click', function(evt) {

		// common.js
		stopAutoSwitchCompany();

		var compcode = $(this).attr('compcode');

		var len = companylist.length;
		for (var i = 0; i < len; i++) {

			var dat = companylist[i];
			if (dat.code == compcode) {
				currentSwitchIndex = i
				break;
			}
		}

		switchCompany(compcode);

	});


	if (currentSwitchIndex == undefined) {
		clearTimeout(itv_autoSwitchCompGrid);
		itv_autoSwitchCompGrid = setTimeout(switchNextCompGrid, 20000);

	}
	var len = companylist.length;
	for (var i = 0; i < len; i++) {

		var dat = companylist[i];
		if (dat.code == current_company_code) {
			currentSwitchIndex = i
			break;
		}
	}

}

var itv_checkAudioEnd;

function switchNextCompGrid() {

	// 20170821 禁止航司自动轮播
	return;

	// common.js
	if (!autoSwitch) {
		itv_autoSwitchCompGrid = setTimeout(switchNextCompGrid, 10);
		return;
	}

	// common.js
	if (stopAllAutoSwitchCompanyInterval) {
		return;
	}

	if (audioEnded || audioPaused) {

		//if(currentSwitchIndex == prevSwitchIndex){
		//    return;
		//}

		clearTimeout(itv_checkAudioEnd);

		var len = companylist.length;

		if (currentSwitchIndex >= len - 1) {
			currentSwitchIndex = 0;
		} else {
			currentSwitchIndex++;
		}

		for (var i = 0; i < len; i++) {

			var dat = companylist[i];
			var compcode = dat.code;

			if (currentSwitchIndex == i) {
				prevSwitchIndex = currentSwitchIndex;
				switchCompany(compcode);
				break;
			}
		}

		clearTimeout(itv_autoSwitchCompGrid);
		itv_autoSwitchCompGrid = setTimeout(switchNextCompGrid, 20000);

	} else {
		itv_checkAudioEnd = setTimeout(switchNextCompGrid, 10);
	}
}



// 延误原因

var comp_cause_list; // 公司原因
var none_cause_list; // 非公司原因
var trendChartData = {};

function setBlockL1Kpi() {

	if (all_delay_cause_data == undefined || all_delay_cause_data['kpi_value_d'] == undefined || all_delay_cause_data[
			'kpi_value_sq_d'] == undefined) {
		setTimeout(setBlockL1Kpi, 10);
		return;
	}

	var date = getCurrentDate();

	if (current_company_code == parent_company) {
		//$('.block_l1 .tit').text('本期整体正常率');
	} else {
		//$('.block_l1 .tit').text(companyCode2Name[current_company_code]);
	}
	$('.block_l1 .tit').text(companyCode2Name[current_company_code]);



	comp_cause_list = {}; // 公司原因
	none_cause_list = {}; // 非公司原因

	var comp_total = 0;
	var none_total = 0;

	var comp_total_sq = 0;
	var none_total_sq = 0;


	var data_date_val = {}; // 存储每周／月的 公司／非公司 延误班次
	var data_comp_val = {}; // 存储每个公司的 公司／非公司 延误班次



	var len = companylist.length;
	for (var i = 0; i < len; i++) {

		var dat = companylist[i];
		var compcode = dat.code;

		if (compcode != parent_company) {
			comp_cause_list[compcode] = []; // 公司原因
			none_cause_list[compcode] = []; // 非公司原因

			var data_dly = all_delay_cause_data['kpi_value_d'][compcode]['DELAY_NO'][date_type];
			var data_dly_sq = all_delay_cause_data['kpi_value_sq_d'][compcode]['DELAY_NO'][date_type];

			// 公司原因 总数
			for (var time in data_dly) {
				var d = data_dly[time];
				var len2 = comp_cause.length;
				for (var j = 0; j < len2; j++) {
					var causeName = comp_cause[j];
					if (!isNaN(d[causeName])) {
						var val = Number(d[causeName]);
						if (time == date) {
							if (val > 0) {
								comp_cause_list[compcode].push({
									"name": causeName,
									"val": val
								});
							}
						}
					}
				}
			}

			// 非公司原因 总数
			for (var time in data_dly) {
				var d = data_dly[time];
				var len2 = none_cause.length;
				for (var j = 0; j < len2; j++) {
					var causeName = none_cause[j];
					if (!isNaN(d[causeName])) {
						var val = Number(d[causeName]);
						if (causeName == "民航局航班时刻安排") {
							causeName = "时刻安排"
						}
						if (time == date) {
							if (val > 0) {
								none_cause_list[compcode].push({
									"name": causeName,
									"val": val
								});
							}
						}
					}
				}
			}

		}


		if (compcode != parent_company) {

			// 公司原因 总数
			for (var time in data_dly) {
				var d = data_dly[time];
				var len2 = comp_cause.length;
				// 上期后台数据有误前台统计上期
				var time2 = '';
				if (date_type == 'L') {
					if (weekList.indexOf(time) > 0) {
						time2 = weekList[weekList.indexOf(time) - 1];
					}
				} else if (date_type = 'M') {
					if (monthList.indexOf(time) > 0) {
						time2 = weekList[monthList.indexOf(time) - 1];
					}
				}

				// 上期后台数据有误前台统计上期
				var d2 = data_dly[time2];

				if (data_date_val[time] == undefined) {
					data_date_val[time] = {};

					data_date_val[time]['comp'] = 0;
					data_date_val[time]['none'] = 0;
					data_date_val[time]['sch'] = 0;

					data_date_val[time]['comp_sq'] = 0;
					data_date_val[time]['none_sq'] = 0;
					data_date_val[time]['sch_sq'] = 0;

					data_date_val[time]['comp_sq2'] = 0;
					data_date_val[time]['none_sq2'] = 0;
				}

				if (data_comp_val[compcode] == undefined) {
					data_comp_val[compcode] = {};
					data_comp_val[compcode]['comp'] = 0;
					data_comp_val[compcode]['none'] = 0;
				}


				for (var j = 0; j < len2; j++) {
					var causeName = comp_cause[j];
					if (!isNaN(d[causeName])) {
						var val = Number(d[causeName]);
						if (time == date) {
							if (current_company_code == parent_company || current_company_code == compcode)
								comp_total += val;
							if (val > 0) {
								data_comp_val[compcode]['comp'] += val;
							}
						}

						var schno = all_company_data['kpi_value_d'][compcode]['SCH_NO'][date_type][time];

						if ((current_company_code == compcode || current_company_code == parent_company)) {
							data_date_val[time]['comp'] += val;
							data_date_val[time]['sch'] = isNaN(schno) ? 0 : Number(schno);
							// if (current_company_code == parent_company) {
							//     // data_date_val[time]['sch'] += isNaN(schno) ? 0 : Number(schno);
							//     console.log(797,comp_cause,schno,data_date_val[time]['sch'])
							// }
						}
					}

					// 上期后台数据有误前台统计上期
					if (d2 != undefined) {
						if (!isNaN(d2[causeName])) {
							var val1 = Number(d2[causeName]);
							if ((current_company_code == compcode || current_company_code == parent_company)) {
								data_date_val[time]['comp_sq2'] += val1;
							}
						}
					}



				}

				// 计算总和
				if (current_company_code == parent_company) {

					var dat_sch = 0;
					var len = companylist.length;
					for (var j = 0; j < len; j++) {

						var dat2 = companylist[j];
						var ccode = dat2.code;
						if (ccode != parent_company && ccode != 'HX') {
							var v = all_company_data['kpi_value_d'][ccode]['SCH_NO'][date_type][time];
							if (!isNaN(v)) {
								dat_sch += Number(v);
							}
						}
					}

					data_date_val[time]['sch'] = dat_sch;
				}
				// console.log(804,data_date_val)

			}

			// 上期
			for (var time in data_dly_sq) {
				var d = data_dly_sq[time];
				var len2 = comp_cause.length;

				for (var j = 0; j < len; j++) {
					var causeName = comp_cause[j];
					if (!isNaN(d[causeName])) {
						var val = Number(d[causeName]);
						if (current_company_code == parent_company || current_company_code == compcode) comp_total_sq +=
							val;

						var schno = all_company_data['kpi_value_sq_d'][compcode]['SCH_NO'][date_type][time];

						if ((current_company_code == compcode || current_company_code == parent_company)) {
							data_date_val[time]['comp_sq'] += val;
							data_date_val[time]['sch_sq'] = isNaN(schno) ? 0 : Number(schno);
						}
					}
				}

				// 计算总和
				if (current_company_code == parent_company) {
					var dat_sch = 0;
					var len = companylist.length;
					for (var j = 0; j < len; j++) {

						var dat2 = companylist[j];
						var ccode = dat2.code;
						if (ccode != parent_company && ccode != 'HX') {
							var v = all_company_data['kpi_value_sq_d'][ccode]['SCH_NO'][date_type][time];
							if (!isNaN(v)) {
								dat_sch += Number(v);
							}
						}
					}

					data_date_val[time]['sch_sq'] = dat_sch;
				}

			}

			// 非公司原因 总数
			for (var time in data_dly) {
				var d = data_dly[time];
				// 上期后台数据有误前台统计上期
				var d2 = data_dly[time2];
				var len2 = none_cause.length;
				for (var j = 0; j < len2; j++) {
					var causeName = none_cause[j];
					if (!isNaN(d[causeName])) {
						var val = Number(d[causeName]);
						if (causeName == "民航局航班时刻安排") {
							causeName = "时刻安排"
						}
						if (time == date) {
							if (current_company_code == parent_company || current_company_code == compcode)
								none_total += val;
							if (val > 0) {
								data_comp_val[compcode]['none'] += val;
							}
						}

						if ((current_company_code == compcode || current_company_code == parent_company)) {
							data_date_val[time]['none'] += val;
						}

					}

					// 上期后台数据有误前台统计上期
					if (d2 != undefined) {
						if (!isNaN(d2[causeName])) {
							var val1 = Number(d2[causeName]);
							if ((current_company_code == compcode || current_company_code == parent_company)) {
								data_date_val[time]['none_sq2'] += val1;
							}
						}
					}

				}
			}
			for (var time in data_dly_sq) {
				var d = data_dly_sq[time];
				var len2 = none_cause.length;
				for (var j = 0; j < len2; j++) {
					var causeName = none_cause[j];
					if (!isNaN(d[causeName])) {
						var val = Number(d[causeName]);
						if (current_company_code == parent_company || current_company_code == compcode) none_total_sq +=
							val;

						if ((current_company_code == compcode || current_company_code == parent_company)) {
							data_date_val[time]['none_sq'] += val;
						}

					}
				}
			}
		}



	}


	// 公司原因 趋势 chart 数据
	var xAxisData = [];
	var data_s1 = [];
	var data_s2 = [];

	var chart_data = [];
	for (var time in data_date_val) {
		var obj = data_date_val[time];
		var oo = {
			time: time,
			comp: obj['comp'],
			sch: obj['sch'],
			comp_sq: obj['comp_sq'],
			sch_sq: obj['sch_sq'],
			comp_sq2: obj['comp_sq2']
		};
		chart_data.push(oo);

	}

	chart_data.sort(function(a, b) {
		return a.time - b.time
	});

	var curDate = getCurrentDate();
	var len = chart_data.length;
	for (var i = 0; i < len; i++) {
		var oo = chart_data[i];
		var time = oo.time;
		// 公司原因延误的航班
		var val = oo.comp;
		// 计划航班
		var sch = oo.sch;
		var per = sch > 0 ? Math.round(val / sch * 1000) / 10 : 0;

		//上期
		// 公司原因延误的航班
		// var val_sq = oo.comp_sq;

		// 上期后台数据有误前台统计上期
		var val_sq = oo.comp_sq2;
		if (oo.comp_sq2 == undefined) {
			val_sq = oo.comp_sq;
		}

		// 计划航班
		var sch_sq = oo.sch_sq;
		var per_sq = Math.round(val_sq / sch_sq * 1000) / 10;

		// 环比=(本期-上期)/上期×100%
		var hb = per_sq > 0 ? Math.round((per - per_sq) / per_sq * 1000) / 10 : 0;

		if (Number(curDate) >= Number(time)) {
			if (date_type == 'L') {
				// Week: *********
				//var label = date.substr(0,4) + '年第' + Number(date.substr(4,3)) + '周';
				//20170419 双双要求：显示的周数+1
				//20170614 双双要求：A3.2 运行页面 显示的周数不用+1
				var label = '第' + (Number(time.substr(4, 3))) + '周 '; //+ response[date];
				if (xAxisData.indexOf(label) == -1 && weekList.indexOf(time) > -1) {
					xAxisData.push(label);
					data_s1.push(per);
					data_s2.push(hb);
				}
			} else {
				// Week: *********
				var label = Number(time.substr(4, 2)) + '月 \n ' + time.substr(0, 4);
				if (xAxisData.indexOf(label) == -1 && monthList.indexOf(time) > -1) {
					xAxisData.push(label);
					data_s1.push(per);
					data_s2.push(hb);
				}
			}
		}
	}

	trendChartData.xAxisData = xAxisData;
	trendChartData.data_s1 = data_s1;
	trendChartData.data_s2 = data_s2;

	// 非公司原因 趋势 chart 数据
	var xAxisData = [];
	var data_s1t2 = [];
	var data_s2t2 = [];

	var chart_data = [];
	for (var time in data_date_val) {
		var obj = data_date_val[time];
		var oo = {
			time: time,
			none: obj['none'],
			sch: obj['sch'],
			none_sq: obj['none_sq'],
			sch_sq: obj['sch_sq']
		};
		chart_data.push(oo);
	}

	chart_data.sort(function(a, b) {
		return a.time - b.time
	});

	var len = chart_data.length;
	for (var i = 0; i < len; i++) {
		var oo = chart_data[i];
		var time = oo.time;
		// 公司原因延误的航班
		var val = oo.none;
		// 计划航班
		var sch = oo.sch;
		var per = sch > 0 ? Math.round(val / sch * 1000) / 10 : 0;

		//上期
		// 公司原因延误的航班
		// var val_sq = oo.none_sq;

		// 上期后台数据有误前台统计上期
		var val_sq = oo.none_sq2;
		if (oo.none_sq2 == undefined) {
			val_sq = oo.none_sq;
		}

		// 计划航班
		var sch_sq = oo.sch_sq;
		var per_sq = Math.round(val_sq / sch_sq * 1000) / 10;

		// 环比=(本期-上期)/上期×100%
		var hb = per_sq > 0 ? Math.round((per - per_sq) / per_sq * 1000) / 10 : 0;

		if (Number(curDate) >= Number(time)) {
			if (date_type == 'L') {
				// Week: *********
				//var label = date.substr(0,4) + '年第' + Number(date.substr(4,3)) + '周';
				//20170419 双双要求：显示的周数+1
				//20170614 双双要求：A3.2 运行页面 显示的周数不用+1
				var label = '第' + (Number(time.substr(4, 3))) + '周 '; //+ response[date];
				if (xAxisData.indexOf(label) == -1 && weekList.indexOf(time) > -1) {
					xAxisData.push(label);
					data_s1t2.push(per);
					data_s2t2.push(hb);
				}
			} else {
				// Week: *********
				var label = Number(time.substr(4, 2)) + '月 \n ' + time.substr(0, 4);
				if (xAxisData.indexOf(label) == -1 && monthList.indexOf(time) > -1) {
					xAxisData.push(label);
					data_s1t2.push(per);
					data_s2t2.push(hb);
				}
			}
		}
	}



	trendChartData.data_s1t2 = data_s1t2;
	trendChartData.data_s2t2 = data_s2t2;



	// 公司原因
	html = '';
	var cause_list = {};
	var cause_arr_list = [];
	for (var compcode in comp_cause_list) {

		if (current_company_code != parent_company && current_company_code != compcode) {
			continue;
		}

		var list = comp_cause_list[compcode];
		var len = list.length;
		for (var i = 0; i < len; i++) {
			var d = list[i];
			if (cause_list[d.name] == undefined) {
				cause_list[d.name] = 0;
			}
			cause_list[d.name] += Number(d.val);
		}
	}
	for (var name in cause_list) {
		var val = cause_list[name];
		cause_arr_list.push({
			"name": name,
			"val": val
		});
	}

	cause_arr_list.sort(function(a, b) {
		return b.val - a.val;
	});
	
	
	

	var len = cause_arr_list.length;
	var fontsize = len > 10 ? 10 : 12;
	for (var i = 0; i < len; i++) {
		var d = cause_arr_list[i];
		var per = (comp_total + none_total) > 0 ? Number(d.val) / (comp_total + none_total) : 0;
		var perstr = Math.round(per * 1000) / 10;
		if (perstr > 0) {
			var barlen = 170 * per;
			html += '<tr style="font-size:' + fontsize + 'px">';
			html += '<td class="c1">' + d.name + '</td>';
			html += '<td class="c2">' + perstr + '%</td>';
			html += '<td class="c3"><span class="bar" style="width:' + barlen + 'px;"></span></td>';
			html += '</tr>';
		}
	}
	
	// RTD 公司原因占比
	html = ''
	var len = reasonByCompany.companyReasonList.length;
	var fontsize = len > 10 ? 10 : 12;
	var rtd_per = (comp_total + none_total) > 0 ? Number(d.val) / (comp_total + none_total) : 0;
	var rtd_perstr = Math.round(per * 1000) / 10;
	reasonByCompany.companyReasonList.map(el=>{
		html += '<tr style="font-size:' + fontsize + 'px">';
		html += '<td class="c1">' + el.reasonName + '</td>';
		html += '<td class="c2">' + el.rate + '%</td>';
		html += '<td class="c3"><span class="bar" style="width:' + `${170 * per}` + 'px;"></span></td>';
		html += '</tr>';
	})

	$('.block_l1 .row2 .tabc1 table').html(html);


	// 非公司原因
	html = '';
	var cause_list = {};
	var cause_arr_list = [];
	for (var compcode in none_cause_list) {

		if (current_company_code != parent_company && current_company_code != compcode) {
			continue;
		}

		var list = none_cause_list[compcode];
		var len = list.length;
		for (var i = 0; i < len; i++) {
			var d = list[i];
			if (cause_list[d.name] == undefined) {
				cause_list[d.name] = 0;
			}
			cause_list[d.name] += Number(d.val);
		}
	}
	for (var name in cause_list) {
		var val = cause_list[name];
		cause_arr_list.push({
			"name": name,
			"val": val
		});
	}

	cause_arr_list.sort(function(a, b) {
		return b.val - a.val;
	});

	var len = cause_arr_list.length;
	var fontsize = len > 10 ? 10 : 12;
	for (var i = 0; i < len; i++) {
		var d = cause_arr_list[i];
		var per = (comp_total + none_total) > 0 ? Number(d.val) / (comp_total + none_total) : 0;
		var perstr = Math.round(per * 1000) / 10;
		if (perstr > 0) {
			var barlen = 170 * per;
			html += '<tr style="font-size:' + fontsize + 'px">';
			html += '<td class="c1">' + d.name + '</td>';
			html += '<td class="c2">' + perstr + '%</td>';
			html += '<td class="c3"><span class="bar" style="width:' + barlen + 'px;"></span></td>';
			html += '</tr>';
		}
	}

	$('.block_l1 .row2 .tabc2 table').html(html);



	// 公司原因占比
	var rate_comp = (comp_total + none_total) > 0 ? comp_total / (comp_total + none_total) : 0;
	var rate_comp_sq = (comp_total_sq + none_total_sq) > 0 ? comp_total_sq / (comp_total_sq + none_total_sq) : 0;
	// 环比=(本期-上期)/上期×100%
	//var hb_comp = comp_total_sq > 0 ? Math.round((comp_total-comp_total_sq)/comp_total_sq * 1000)/10 : 0;
	var hb_comp = rate_comp_sq > 0 ? Math.round((rate_comp - rate_comp_sq) / rate_comp_sq * 1000) / 10 : 0;
	// 非公司占比
	var rate_none = (comp_total + none_total) > 0 ? none_total / (comp_total + none_total) : 0;
	var rate_none_sq = (comp_total_sq + none_total_sq) > 0 ? none_total_sq / (comp_total_sq + none_total_sq) : 0;
	// 环比=(本期-上期)/上期×100%
	//var hb_none = none_total_sq > 0 ? Math.round((none_total-none_total_sq)/none_total_sq * 1000)/10 : 0;
	var hb_none = rate_none_sq > 0 ? Math.round((rate_none - rate_none_sq) / rate_none_sq * 1000) / 10 : 0;

	all_company_avg_delay_rate = rate_comp;

	// percent
	$('.block_l1 .b1 .kpi .val').text(Math.round(rate_comp * 100));
	$('.block_l1 .b2 .kpi .val').text(Math.round(rate_none * 100));
	$('.block_l1 .b1 .hb .val').text(hb_comp + '%');
	$('.block_l1 .b2 .hb .val').text(hb_none + '%');
	
	
	// RTD 公司原因
	$('.block_l1 .b1 .kpi .val').text(reasonByCompany.all.companyResaonRate);
	$('.block_l1 .b2 .kpi .val').text(reasonByCompany.all.externalResaonRate);
	rate_comp = reasonByCompany.all.companyResaonRate /100

	if (hb_comp > 0) {
		$('.block_l1 .b1 .hb .green').removeClass('hide');
		$('.block_l1 .b1 .hb .red').addClass('hide');
	} else if (hb_comp < 0) {
		$('.block_l1 .b1 .hb .green').addClass('hide');
		$('.block_l1 .b1 .hb .red').removeClass('hide');
	} else {
		$('.block_l1 .b1 .hb .green').addClass('hide');
		$('.block_l1 .b1 .hb .red').addClass('hide');
	}

	if (hb_none > 0) {
		$('.block_l1 .b2 .hb .green').removeClass('hide');
		$('.block_l1 .b2 .hb .red').addClass('hide');
	} else if (hb_none < 0) {
		$('.block_l1 .b2 .hb .green').addClass('hide');
		$('.block_l1 .b2 .hb .red').removeClass('hide');
	} else {
		$('.block_l1 .b2 .hb .green').addClass('hide');
		$('.block_l1 .b2 .hb .red').addClass('hide');
	}
	
	// chart 
	var rate = rate_comp;
	var canvas = document.getElementById('cvs_delay_cause');
	var context = canvas.getContext('2d');
	var x = canvas.width / 2;
	var y = canvas.height / 2;

	// draw blue circle
	var radius = 40;
	context.beginPath();
	context.arc(x, y, radius, 0, 2 * Math.PI, false);
	context.lineWidth = 7;
	context.strokeStyle = '#02b0f9';
	context.stroke();

	// draw green arc
	var startAngle = Math.PI - (Math.PI * 2 * rate / 2);
	var endAngle = Math.PI + (Math.PI * 2 * rate / 2);
	context.beginPath();
	context.arc(x, y, radius, startAngle, endAngle, false);
	context.lineWidth = 7;
	context.strokeStyle = '#a3d800';
	context.stroke();

	// draw lines
	var numslice = 12;
	for (var i = 0; i < numslice; i++) {
		context.beginPath();
		var startAngle = i * (Math.PI * 2 / numslice);
		var endAngle = startAngle + Math.PI * 0.01;
		context.arc(x, y, radius, startAngle, endAngle, false);
		context.lineWidth = 7;
		context.strokeStyle = '#0A176E';
		context.stroke();
	}



	// --------------------------------------------------
	// 各个子公司 原因分析
	// --------------------------------------------------
	company_delay_rate_list = {};
	for (var compcode in data_comp_val) {
		var obj = data_comp_val[compcode];
		var comp_val = obj['comp'];
		var none_val = obj['none'];

		//公司原因延误的航班数／计划航班数
		var sch = all_company_data['kpi_value_d'][compcode]['SCH_NO'][date_type][date];
		var rate = sch > 0 ? Math.round(comp_val / sch * 1000) / 10 : 0;

		company_delay_rate_list[compcode] = rate;
	}

	setBlockL2Kpi();



	
}


// 存储每个公司的公司原因延误占比
var company_delay_rate_list;

// 公司原因预警
function setBlockL2Kpi() {

	var html1 = '';
	var html2 = '';
	var list1 = [];
	var list2 = [];

	var len = companylist.length;

	for (var i = 0; i < len; i++) {

		var dat = companylist[i];
		var compcode = dat.code;
		if (compcode != parent_company) {
			var rate = company_delay_rate_list[compcode];
			if (compcode != 'HX') { // HNA_BUG_234 香港没有使用运行品质系统统计航班延误原因. 公司原因预警：这里把香港航空过滤掉，避免“公司原因占计划航班<1.5%的航司”永远显示这家航司
				if (rate < 1.5) {
					list1.push({
						"name": dat.nameabbr,
						"rate": rate
					});
				} else if (rate > 2.5) {
					list2.push({
						"name": dat.nameabbr,
						"rate": rate
					});
				}
			}
		}
	}

	list1.sort(function(a, b) {
		return b.rate - a.rate
	});
	len = Math.min(list1.length, 5);
	for (var i = 0; i < len; i++) {
		var dat = list1[i];
		html1 += '<span class="itm">';
		html1 += '<span class="nm">' + dat.name + '</span>';
		html1 += '<span class="val green">' + dat.rate + '%</span>';
		html1 += '</span>';
	}
	if (list1.length == 0) {
		html1 += '<span class="itm">';
		html1 += '<span class="nm">无</span>';
		html1 += '</span>';
	}

	list2.sort(function(a, b) {
		return b.rate - a.rate
	});
	len = Math.min(list2.length, 5);
	for (var i = 0; i < len; i++) {
		var dat = list2[i];
		html2 += '<span class="itm">';
		html2 += '<span class="nm">' + dat.name + '</span>';
		html2 += '<span class="val red">' + dat.rate + '%</span>';
		html2 += '</span>';
	}
	if (list2.length == 0) {
		html2 += '<span class="itm">';
		html2 += '<span class="nm">无</span>';
		html2 += '</span>';
	}

	$('.block_l2 .list1').html(html1);
	$('.block_l2 .list2').html(html2);
}


// 右上角 整体 正常率 
function setBlockR1Kpi() {
	setAllKpiData()

	if (groupNormalRateGoal == undefined) {
		setTimeout(setBlockR1Kpi, 10);
		return;
	}

	var date = getCurrentDate();

	// 正常率目标
	$('.block_r1 .row1 .blk1 .lb').text(companyCode2Name[parent_company] + '整体正常率');
	/* $('.block_r1 .row1 .blk1 .val').text(groupNormalRateGoal + '%'); */


	// 本期整体正常率
	var val1 = sumKpi('kpi_value_d', 'NORMAL_NO_T');
	var val2 = sumKpi('kpi_value_d', 'SCH_NO');

	// var rate1 = val2 > 0 ? Math.round((Number(val1) / Number(val2)) * 1000) / 10 : 0;
	// all_company_avg_noraml_rate = rate1;
	
	// RTD 本期整体正常率
	var rate1 = curCompanyData.normalRate
	

	$('.block_l1 .normal_rate .val').text(rate1 + '%');
	//$('.block_r1 .row1 .blk2 .val').text(rate1 + '%');
	$('.block_r1 .row1 .blk1 .val').text(rate1 + '%'); //海航航空整体正常率目标
	$('.block_l1 .normal_rate .val').removeClass('red')
	$('.block_l1 .normal_rate .val').removeClass('green')
	if (rate1 >= groupNormalRateGoal) {
		$('.block_l1 .normal_rate .val').addClass('green')
	} else {
		$('.block_l1 .normal_rate .val').addClass('red')
	}


	// 子公司情况

	var html1 = '';
	var html2 = '';
	var list1 = [];
	var list2 = [];

	var len = companylist.length;
	for (var i = 0; i < len; i++) {

		var dat = companylist[i];
		var compcode = dat.code;
		if (compcode != parent_company) {

			var val1 = all_company_data['kpi_value_d'][compcode]['NORMAL_NO_T'][date_type][date];
			var val2 = all_company_data['kpi_value_d'][compcode]['SCH_NO'][date_type][date];
			var rate = val2 > 0 ? Math.round((Number(val1) / Number(val2)) * 1000) / 10 : 0;
			list1.push({
				"name": dat.nameabbr,
				"rate": rate
			});

			/* if (rate > groupNormalRateGoal) {
			    list1.push({
			        "name": dat.nameabbr,
			        "rate": rate
			    });
			} else if (rate < groupNormalRateGoal && rate > 0) {
			    list2.push({
			        "name": dat.nameabbr,
			        "rate": rate
			    });
			} */

			if (all_company_noraml_rate == undefined) {
				all_company_noraml_rate = {};
			}
			all_company_noraml_rate[compcode] = rate;
		}
	}
	list1.sort(function(a, b) {
		return b.rate - a.rate
	});
	len = list1.length;
	len = Math.min(len, 5);

	audio_txt_obj.normal_rate_good = [];

	for (var i = 0; i < len; i++) {
		var dat = list1[i];
		html1 += '<span class="itm">';
		html1 += '<span class="nm">' + dat.name + '</span>';
		html1 += '<span class="val green">' + dat.rate + '%</span>';
		html1 += '</span>';

		audio_txt_obj.normal_rate_good.push(dat.name);
	}
	if (list1.length == 0) {
		html1 += '<span class="itm">';
		html1 += '<span class="nm">无</span>';
		html1 += '</span>';
	}
	list1.sort(function(a, b) {
		return a.rate - b.rate
	});
	len = list1.length;
	len = Math.min(len, 5);
	for (var i = 0; i < len; i++) {
		var dat = list1[i];
		html2 += '<span class="itm">';
		html2 += '<span class="nm">' + dat.name + '</span>';
		html2 += '<span class="val red">' + dat.rate + '%</span>';
		html2 += '</span>';
	}
	if (list1.length == 0) {
		html2 += '<span class="itm">';
		html2 += '<span class="nm">无</span>';
		html2 += '</span>';
	}

	$('.block_r1 .list1').html(html1);
	$('.block_r1 .list2').html(html2);
	
	// RTD 正常率排名
	html1 = ''
	html2 = ''
	all_company_noraml_rate={}
	var listAll = []
	listAll = sysCompanyKpiList.filter(el=>el.companyName !=='航空集团' && el.companyName !='境内航司')
	listAll.sort(function(a,b){ return b.normalRate - a.normalRate});
	listAll.map(el=>{
		all_company_noraml_rate[el.companyCode] = el.normalRate.toFixed(1)*100/100
	})
	len = listAll.length;
	var listData1 = listAll.slice(0,5)
	if (listData1.length == 0) {
		html1 += '<span class="itm">';
		html1 += '<span class="nm">无</span>';
		html1 += '</span>';
	}else{
		listData1.map(el=>{
			html1 += '<span class="itm">';
			html1 += '<span class="nm">' + el.companyName + '</span>';
			html1 += '<span class="val green">' + el.normalRate.toFixed(1)*100/100 + '%</span>';
			html1 += '</span>';
		})
	}
	listAll.sort(function(a,b){ return a.normalRate - b.normalRate});
	var listData2 = listAll.slice(0,5)
	if (listData2.length == 0) {
		html2 += '<span class="itm">';
		html2 += '<span class="nm">无</span>';
		html2 += '</span>';
	}else{
		listData2.map(el=>{
			html2 += '<span class="itm">';
			html2 += '<span class="nm">' + el.companyName + '</span>';
			html2 += '<span class="val red">' + el.normalRate.toFixed(1)*100/100 + '%</span>';
			html2 += '</span>';
		})
	}
	
	$('.block_r1 .list1').html(html1);
	$('.block_r1 .list2').html(html2);


}

// 右下角 始发／到港 正常率 
function setBlockR2Kpi() {

	var date = getCurrentDate();


	// ------------------------------------------------------
	// 始发正常率 
	// ------------------------------------------------------

	var val1 = sumKpi('kpi_value_d', 'ORI_NORMAL_NO');
	var val2 = sumKpi('kpi_value_d', 'ORI_NO_SCH');

	var rate1 = val2 > 0 ? Math.round((Number(val1) / Number(val2)) * 1000) / 10 : 0;
	
	// RTD 始发正常率
	rate1 = curCompanyData.depNormalRate
	
	all_company_avg_noraml_rate_ori = rate1;
	$('.block_r2 .tabc1 .row1 .blk1 .val').text(rate1 + '%');
	
	$('.block_r2 .tabc1 .row1 .blk1 .val').text(curCompanyData.depNormalRate +'%');
	

	// 子公司情况

	var html1 = '';
	var html2 = '';
	var list1 = [];
	var list2 = [];

	var len = companylist.length;

	for (var i = 0; i < len; i++) {

		var dat = companylist[i];
		var compcode = dat.code;
		if (compcode != parent_company) {

			var val1 = all_company_data['kpi_value_d'][compcode]['ORI_NORMAL_NO'][date_type][date];
			var val2 = all_company_data['kpi_value_d'][compcode]['ORI_NO_SCH'][date_type][date];
			var rate = val2 > 0 ? Math.round((Number(val1) / Number(val2)) * 1000) / 10 : 0;

			if (rate > all_company_avg_noraml_rate_ori) {
				list1.push({
					"name": dat.nameabbr,
					"rate": rate
				});
			} else if (rate < all_company_avg_noraml_rate_ori && rate > 0) {
				list2.push({
					"name": dat.nameabbr,
					"rate": rate
				});
			}

			if (all_company_noraml_rate_ori == undefined) {
				all_company_noraml_rate_ori = {};
			}
			all_company_noraml_rate_ori[compcode] = rate;
		}
	}

	list1.sort(function(a, b) {
		return b.rate - a.rate
	});
	len = list1.length;
	len = Math.min(len, 5);
	for (var i = 0; i < len; i++) {
		var dat = list1[i];
		html1 += '<span class="itm">';
		html1 += '<span class="nm">' + dat.name + '</span>';
		html1 += '<span class="val green">' + dat.rate + '%</span>';
		html1 += '</span>';
	}
	if (list1.length == 0) {
		html1 += '<span class="itm">';
		html1 += '<span class="nm">无</span>';
		html1 += '</span>';
	}

	list2.sort(function(a, b) {
		return a.rate - b.rate
	});
	len = list2.length;
	len = Math.min(len, 5);
	for (var i = 0; i < len; i++) {
		var dat = list2[i];
		html2 += '<span class="itm">';
		html2 += '<span class="nm">' + dat.name + '</span>';
		html2 += '<span class="val red">' + dat.rate + '%</span>';
		html2 += '</span>';
	}
	if (list2.length == 0) {
		html2 += '<span class="itm">';
		html2 += '<span class="nm">无</span>';
		html2 += '</span>';
	}

	// RTD 始发正常率子公司排行
	html1 = ''
	html2 = ''
	var listAll = []
	listAll = sysCompanyKpiList.filter(el=>el.companyName !=='航空集团' && el.companyName !='境内航司')
	var listData1 =[]
	var listData2 =[]
	listAll.map(el=>{
		if(el.depNormalRate > curCompanyData.depNormalRate){
			listData1.push({
				"name": el.companyName,
				"rate": el.depNormalRate.toFixed(1)*100/100
			})
		}else if(el.depNormalRate < curCompanyData.depNormalRate && el.depNormalRate >0){
			listData2.push({
				"name": el.companyName,
				"rate": el.depNormalRate.toFixed(1)*100/100
			})
		}
	})
	
	listData1.sort(function(a,b){return b.rate - a.rate});
	listData2.sort(function(a,b){return a.rate - b.rate});
	listData1 = listData1.slice(0,5)
	listData2 = listData2.slice(0,5)
	
	if (listData1.length == 0) {
		html1 += '<span class="itm">';
		html1 += '<span class="nm">无</span>';
		html1 += '</span>';
	}else{
		listData1.map(el=>{
			html1 += '<span class="itm">';
			html1 += '<span class="nm">' + el.name + '</span>';
			html1 += '<span class="val green">' + el.rate + '%</span>';
			html1 += '</span>';
		})
	}
	
	if (listData2.length == 0) {
		html2 += '<span class="itm">';
		html2 += '<span class="nm">无</span>';
		html2 += '</span>';
	}else{
		listData2.map(el=>{
			html2 += '<span class="itm">';
			html2 += '<span class="nm">' + el.name + '</span>';
			html2 += '<span class="val red">' + el.rate+ '%</span>';
			html2 += '</span>';
		})
	}
	
	$('.block_r2 .tabc1 .list1').html(html1);
	$('.block_r2 .tabc1 .list2').html(html2);



	// ------------------------------------------------------
	// 到港正常率 
	// ------------------------------------------------------

	var val1 = sumKpi('kpi_value_d', 'ARR_NORMAL_NO');
	var val2 = sumKpi('kpi_value_d', 'ARR_NO');

	var rate1 = val2 > 0 ? Math.round((Number(val1) / Number(val2)) * 1000) / 10 : 0;
	
	// RTD 到港正常率
	rate1 = curCompanyData.arrNormalRate
	
	all_company_avg_noraml_rate_arr = rate1;
	
	$('.block_r2 .tabc2 .row1 .blk1 .val').text(rate1 + '%');
	

	// 子公司情况

	var html1 = '';
	var html2 = '';
	var list1 = [];
	var list2 = [];

	var len = companylist.length;

	for (var i = 0; i < len; i++) {

		var dat = companylist[i];
		var compcode = dat.code;
		if (compcode != parent_company) {

			// 到港正常率
			var val1 = all_company_data['kpi_value_d'][compcode]['ARR_NORMAL_NO'][date_type][date];
			var val2 = all_company_data['kpi_value_d'][compcode]['ARR_NO'][date_type][date];
			var rate = val2 > 0 ? Math.round((Number(val1) / Number(val2)) * 1000) / 10 : 0;

			if (rate > all_company_avg_noraml_rate_arr) {
				list1.push({
					"name": dat.nameabbr,
					"rate": rate
				});
			} else if (rate < all_company_avg_noraml_rate_arr) {
				list2.push({
					"name": dat.nameabbr,
					"rate": rate
				});
			}

			if (all_company_noraml_rate_arr == undefined) {
				all_company_noraml_rate_arr = {};
			}
			all_company_noraml_rate_arr[compcode] = rate;


			// 出港正常率
			var val1 = all_company_data['kpi_value_d'][compcode]['DEP_NORMAL_NO'][date_type][date];
			var val2 = all_company_data['kpi_value_d'][compcode]['DEP_NO'][date_type][date];
			var rate = val2 > 0 ? Math.round((Number(val1) / Number(val2)) * 1000) / 10 : 0;

			if (all_company_noraml_rate_dep == undefined) {
				all_company_noraml_rate_dep = {};
			}
			all_company_noraml_rate_dep[compcode] = rate;
		}
	}

	list1.sort(function(a, b) {
		return b.rate - a.rate
	});
	len = list1.length;
	len = Math.min(len, 5);
	for (var i = 0; i < len; i++) {
		var dat = list1[i];
		html1 += '<span class="itm">';
		html1 += '<span class="nm">' + dat.name + '</span>';
		html1 += '<span class="val green">' + dat.rate + '%</span>';
		html1 += '</span>';
	}
	if (list1.length == 0) {
		html1 += '<span class="itm">';
		html1 += '<span class="nm">无</span>';
		html1 += '</span>';
	}

	list2.sort(function(a, b) {
		return a.rate - b.rate
	});
	len = list2.length;
	len = Math.min(len, 5);
	for (var i = 0; i < len; i++) {
		var dat = list2[i];
		html2 += '<span class="itm">';
		html2 += '<span class="nm">' + dat.name + '</span>';
		html2 += '<span class="val red">' + dat.rate + '%</span>';
		html2 += '</span>';
	}
	if (list2.length == 0) {
		html2 += '<span class="itm">';
		html2 += '<span class="nm">无</span>';
		html2 += '</span>';
	}
	
	// RTD 到港正常率子公司排行
	html1 = ''
	html2 = ''
	var listAll = []
	listAll = sysCompanyKpiList.filter(el=>el.companyName !=='航空集团' && el.companyName !='境内航司')
	var listData1 =[]
	var listData2 =[]
	listAll.map(el=>{
		if(el.arrNormalRate > curCompanyData.arrNormalRate){
			listData1.push({
				"name": el.companyName,
				"rate": el.arrNormalRate.toFixed(1)*100/100
			})
		}else if(el.arrNormalRate < curCompanyData.arrNormalRate && el.arrNormalRate >0){
			listData2.push({
				"name": el.companyName,
				"rate": el.arrNormalRate.toFixed(1)*100/100
			})
		}
	})
	listData1 =listData1.sort(function(a,b){return b.arrNormalRate - a.arrNormalRate});
	listData2 =listData2.sort(function(a,b){return a.arrNormalRate - b.arrNormalRate});
	listData1 = listData1.slice(0,5)
	listData2 = listData2.slice(0,5)
	
	if (listData1.length == 0) {
		html1 += '<span class="itm">';
		html1 += '<span class="nm">无</span>';
		html1 += '</span>';
	}else{
		listData1.map(el=>{
			html1 += '<span class="itm">';
			html1 += '<span class="nm">' + el.name + '</span>';
			html1 += '<span class="val green">' + el.rate + '%</span>';
			html1 += '</span>';
		})
	}
	
	if (listData2.length == 0) {
		html2 += '<span class="itm">';
		html2 += '<span class="nm">无</span>';
		html2 += '</span>';
	}else{
		listData2.map(el=>{
			html2 += '<span class="itm">';
			html2 += '<span class="nm">' + el.name + '</span>';
			html2 += '<span class="val red">' + el.rate+ '%</span>';
			html2 += '</span>';
		})
	}

	$('.block_r2 .tabc2 .list1').html(html1);
	$('.block_r2 .tabc2 .list2').html(html2);


}


// 求和所有子公司的指标
function sumKpi(data_type, kpi_code, date) {

	if (date == undefined) {
		date = getCurrentDate();
	}

	var sum = 0;

	if (current_company_code == parent_company) {

		var len = companylist.length;
		for (var i = 0; i < len; i++) {

			var dat = companylist[i];
			var val = all_company_data[data_type][dat.code][kpi_code][date_type][date];
			if (!isNaN(val)) {
				sum += Number(val);
			}
		}

	} else {
		var val = all_company_data[data_type][current_company_code][kpi_code][date_type][date];
		if (!isNaN(val)) {
			sum += Number(val);
		}
	}

	return sum;

}



function updateAllKpi(data, label) {
	if (!kpiDataReady) {
		return;
	}
	setMapKpi();

	setBlockL1Kpi();
	setBlockR1Kpi();
	setBlockR2Kpi();

	if ($('#popover_trend_chart').is(':visible')) {
		showTrendChartPopover(selected_trend_blockid);
	}

	setComKpiData(current_company_code);

	setTitleDate();
	
	setAllKpiData()

}



// 全民航正常率排名
function getTop10() {
	// ------------------------------------------------------------------------
	// 十航月度排名
	// ------------------------------------------------------------------------


	var param = {
		'mode': 'query',
		'type': '1',
	}

	$.ajax({
		type: 'post',
		url: "/bi/web/hna_comp_rank",
		contentType: 'application/json',
		dataType: 'json',
		async: true,
		data: JSON.stringify(param),
		success: function(response) {

			checkLogin(response);

			if (response.comp != undefined) {
				var len = response.comp.length;
				var html = '';
				var list = response.comp;
				list.sort(function(a, b) {
					return a.rank - b.rank
				});
				var rank = 1;
				html += '<span class="lst">';
				for (var i = 0; i < len; i++) {
					var obj = list[i];
					html += '<span class="itm">';
					html += '<span class="no">' + obj.rank + '</span>';
					html += '<span class="nm">' + obj.name + '</span>';
					html += '</span>';
				}
				html += '</span>';
				html += '<span class="tit">';
				html += obj.date + '国内主要客运航司正常率排名';
				html += '</span>';

				$('.ranking1').html(html);
			}

		},
		error: function(jqXHR, txtStatus, errorThrown) {

		}
	});


	var param = {
		'mode': 'query',
		'type': '2',
	}

	$.ajax({
		type: 'post',
		url: "/bi/web/hna_comp_rank",
		contentType: 'application/json',
		dataType: 'json',
		async: true,
		data: JSON.stringify(param),
		success: function(response) {

			checkLogin(response);

			if (response.comp != undefined) {
				var len = response.comp.length;
				var html = '';
				var list = response.comp;
				list.sort(function(a, b) {
					return a.rank - b.rank
				});
				var rank = 1;
				html += '<span class="lst">';
				for (var i = 0; i < len; i++) {
					var obj = list[i];
					html += '<span class="itm">';
					html += '<span class="no">' + obj.rank + '</span>';
					html += '<span class="nm">' + obj.name + '</span>';
					html += '</span>';
				}
				html += '</span>';
				html += '<span class="tit">';
				html += obj.date + '航空集团其他航司正常率排名';
				html += '</span>';

				$('.ranking2').html(html);
			}

		},
		error: function(jqXHR, txtStatus, errorThrown) {

		}
	});
}



// 航空投资集团 整体正常率目标
var groupNormalRateGoal = 80;

function getGroupNormalRateGoal() {
	var param = {
		'ID': 'WEB_HNA_NORMAL_RATE',
	}

	$.ajax({
		type: 'post',
		url: "/bi/web/querydatalist",
		contentType: 'application/json',
		dataType: 'json',
		async: true,
		data: JSON.stringify(param),
		success: function(response) {

			groupNormalRateGoal = response.data[0].name;

			$('#map_legend .l1').text(groupNormalRateGoal + '%以上');
			$('#map_legend .l2').text(groupNormalRateGoal + '%以下');

		},
		error: function(jqXHR, txtStatus, errorThrown) {

		}
	});
}



var prev_comp_code;
var prev_comp_name;
var prev_comp_delay_cause;
var mid_fadein_id = 1;
var mid_fadeout_id = 2;

function setComKpiData(compcode) {


	$('.center_kpi_grids .itm').removeClass('select');
	$('#grid_' + compcode).addClass('select');



	var date = getCurrentDate();

	var compname = companyCode2Name[compcode];

	// 正常率
	var element = '.comp_kpi_details .RATE ';

	var val1 = 0;
	var val2 = 0;
	var val_total_sq1 = 0;
	var val_total_sq2 = 0;
	if (compcode != parent_company) {
		val1 = all_company_data['kpi_value_d'][compcode]['NORMAL_NO_T'][date_type][date];
		val2 = all_company_data['kpi_value_d'][compcode]['SCH_NO'][date_type][date];
		val_total_sq1 = all_company_data['kpi_value_sq_d'][compcode]['NORMAL_NO_T'][date_type][date];
		val_total_sq2 = all_company_data['kpi_value_sq_d'][compcode]['SCH_NO'][date_type][date];
	} else {
		// HNAHK 没数据，用子公司的求和
		var len = companylist.length;
		for (var i = 0; i < len; i++) {

			var dat = companylist[i];
			var ccode = dat.code;
			if (ccode != parent_company) {
				var v = all_company_data['kpi_value_d'][ccode]['NORMAL_NO_T'][date_type][date];
				if (!isNaN(v)) {
					val1 += Number(v);
				}
				var v = all_company_data['kpi_value_d'][ccode]['SCH_NO'][date_type][date];
				if (!isNaN(v)) {
					val2 += Number(v);
				}
				var v = all_company_data['kpi_value_sq_d'][ccode]['NORMAL_NO_T'][date_type][date];
				if (!isNaN(v)) {
					val_total_sq1 += Number(v);
				}
				var v = all_company_data['kpi_value_sq_d'][ccode]['SCH_NO'][date_type][date];
				if (!isNaN(v)) {
					val_total_sq2 += Number(v);
				}
			}
		}
	}

	var rate1 = val2 > 0 ? Math.round((Number(val1) / Number(val2)) * 1000) / 10 : 0;
	var rate2 = val_total_sq2 > 0 ? Math.round((Number(val_total_sq1) / Number(val_total_sq2)) * 1000) / 10 : 0;

	// 环比=(本期-上期)/上期×100%
	var hb = rate2 > 0 ? Math.round((rate1 - rate2) / rate2 * 1000) / 10 : 0;
	
	// RTD 正常率\正常率环比
	rate1 = curCompanyData.normalRate
	hb = curCompanyData.normalRateRatio


	$(element + '.kpi .val').text(rate1);
	$(element + '.kpi .val').removeClass('green');
	$(element + '.kpi .val').removeClass('red');
	if (rate1 >= groupNormalRateGoal) {
		$(element + '.kpi .val').addClass('green');
	} else {
		$(element + '.kpi .val').addClass('red');
	}

	$(element + '.hb .val').text(hb);
	if (hb > 0) {
		$(element + '.hb .green').removeClass('hide');
		$(element + '.hb .red').addClass('hide');
		$(element + '.hb .sub').show();
	} else if (hb < 0) {
		$(element + '.hb .green').addClass('hide');
		$(element + '.hb .red').removeClass('hide');
		$(element + '.hb .sub').show();
	} else {
		$(element + '.hb .green').addClass('hide');
		$(element + '.hb .red').addClass('hide');
		$(element + '.hb .sub').hide();
	}

	// 到港正常率
	var element = '.comp_kpi_details .ARR ';

	var val1 = 0;
	var val2 = 0;
	var val_total_sq1 = 0;
	var val_total_sq2 = 0;
	if (compcode != parent_company) {
		val1 = all_company_data['kpi_value_d'][compcode]['ARR_NORMAL_NO'][date_type][date];
		val2 = all_company_data['kpi_value_d'][compcode]['ARR_NO'][date_type][date];
		val_total_sq1 = all_company_data['kpi_value_sq_d'][compcode]['ARR_NORMAL_NO'][date_type][date];
		val_total_sq2 = all_company_data['kpi_value_sq_d'][compcode]['ARR_NO'][date_type][date];
	} else {
		// HNAHK 没数据，用子公司的求和
		var len = companylist.length;
		for (var i = 0; i < len; i++) {

			var dat = companylist[i];
			var ccode = dat.code;
			if (ccode != parent_company) {
				var v = all_company_data['kpi_value_d'][ccode]['ARR_NORMAL_NO'][date_type][date];
				if (!isNaN(v)) {
					val1 += Number(v);
				}
				var v = all_company_data['kpi_value_d'][ccode]['ARR_NO'][date_type][date];
				if (!isNaN(v)) {
					val2 += Number(v);
				}
				var v = all_company_data['kpi_value_sq_d'][ccode]['ARR_NORMAL_NO'][date_type][date];
				if (!isNaN(v)) {
					val_total_sq1 += Number(v);
				}
				var v = all_company_data['kpi_value_sq_d'][ccode]['ARR_NO'][date_type][date];
				if (!isNaN(v)) {
					val_total_sq2 += Number(v);
				}
			}
		}
	}

	var rate1 = val2 > 0 ? Math.round((Number(val1) / Number(val2)) * 1000) / 10 : 0;
	var rate2 = val_total_sq2 > 0 ? Math.round((Number(val_total_sq1) / Number(val_total_sq2)) * 1000) / 10 : 0;

	// 环比=(本期-上期)/上期×100%
	var hb = rate2 > 0 ? Math.round((rate1 - rate2) / rate2 * 1000) / 10 : 0;
	
	// RTD 到港正常率/到港正常率环比
	rate1 = curCompanyData.arrNormalRate
	hb = curCompanyData.arrNormalRateRatio

	$(element + '.kpi .val').text(rate1);
	$(element + '.kpi .val').removeClass('green');
	$(element + '.kpi .val').removeClass('red');
	if (rate1 >= groupNormalRateGoal) {
		$(element + '.kpi .val').addClass('green');
	} else {
		$(element + '.kpi .val').addClass('red');
	}

	$(element + '.hb .val').text(hb);
	if (hb > 0) {
		$(element + '.hb .green').removeClass('hide');
		$(element + '.hb .red').addClass('hide');
		$(element + '.hb .sub').show();
	} else if (hb < 0) {
		$(element + '.hb .green').addClass('hide');
		$(element + '.hb .red').removeClass('hide');
		$(element + '.hb .sub').show();
	} else {
		$(element + '.hb .green').addClass('hide');
		$(element + '.hb .red').addClass('hide');
		$(element + '.hb .sub').hide();
	}



	// 国际航班正常率
	var element = '.comp_kpi_details .INT ';

	var val1 = 0;
	var val2 = 0;
	var val_total_sq1 = 0;
	var val_total_sq2 = 0;
	if (compcode != parent_company) {
		val1 = all_company_data['kpi_value_d'][compcode]['NORMAL_NO_T_INT'][date_type][date];
		val2 = all_company_data['kpi_value_d'][compcode]['SCH_NO_INT'][date_type][date];
		val_total_sq1 = all_company_data['kpi_value_sq_d'][compcode]['NORMAL_NO_T_INT'][date_type][date];
		val_total_sq2 = all_company_data['kpi_value_sq_d'][compcode]['SCH_NO_INT'][date_type][date];
	} else {
		// HNAHK 没数据，用子公司的求和
		var len = companylist.length;
		for (var i = 0; i < len; i++) {

			var dat = companylist[i];
			var ccode = dat.code;
			if (ccode != parent_company) {
				var v = all_company_data['kpi_value_d'][ccode]['NORMAL_NO_T_INT'][date_type][date];
				if (!isNaN(v)) {
					val1 += Number(v);
				}
				var v = all_company_data['kpi_value_d'][ccode]['SCH_NO_INT'][date_type][date];
				if (!isNaN(v)) {
					val2 += Number(v);
				}
				var v = all_company_data['kpi_value_sq_d'][ccode]['NORMAL_NO_T_INT'][date_type][date];
				if (!isNaN(v)) {
					val_total_sq1 += Number(v);
				}
				var v = all_company_data['kpi_value_sq_d'][ccode]['SCH_NO_INT'][date_type][date];
				if (!isNaN(v)) {
					val_total_sq2 += Number(v);
				}
			}
		}
	}

	var rate1 = val2 > 0 ? Math.round((Number(val1) / Number(val2)) * 1000) / 10 : 0;
	var rate2 = val_total_sq2 > 0 ? Math.round((Number(val_total_sq1) / Number(val_total_sq2)) * 1000) / 10 : 0;

	// 环比=(本期-上期)/上期×100%
	var hb = rate2 > 0 ? Math.round((rate1 - rate2) / rate2 * 1000) / 10 : 0;
	
	// RTD 到港正常率/到港正常率环比
	rate1 = curCompanyData.intNormalRate
	hb = curCompanyData.intNormalRateRatio
	
	
	$(element + '.kpi .val').text(rate1);
	$(element + '.kpi .val').removeClass('green');
	$(element + '.kpi .val').removeClass('red');
	if (rate1 >= groupNormalRateGoal) {
		$(element + '.kpi .val').addClass('green');
	} else {
		$(element + '.kpi .val').addClass('red');
	}

	$(element + '.hb .val').text(hb);
	if (hb > 0) {
		$(element + '.hb .green').removeClass('hide');
		$(element + '.hb .red').addClass('hide');
		$(element + '.hb .sub').show();
	} else if (hb < 0) {
		$(element + '.hb .green').addClass('hide');
		$(element + '.hb .red').removeClass('hide');
		$(element + '.hb .sub').show();
	} else {
		$(element + '.hb .green').addClass('hide');
		$(element + '.hb .red').addClass('hide');
		$(element + '.hb .sub').hide();
	}


	// 始发正常率
	var element = '.comp_kpi_details .ORI ';

	var val1 = 0;
	var val2 = 0;
	var val_total_sq1 = 0;
	var val_total_sq2 = 0;
	if (compcode != parent_company) {
		val1 = all_company_data['kpi_value_d'][compcode]['ORI_NORMAL_NO'][date_type][date];
		val2 = all_company_data['kpi_value_d'][compcode]['ORI_NO_SCH'][date_type][date];
		val_total_sq1 = all_company_data['kpi_value_sq_d'][compcode]['ORI_NORMAL_NO'][date_type][date];
		val_total_sq2 = all_company_data['kpi_value_sq_d'][compcode]['ORI_NO_SCH'][date_type][date];
	} else {
		// HNAHK 没数据，用子公司的求和
		var len = companylist.length;
		for (var i = 0; i < len; i++) {

			var dat = companylist[i];
			var ccode = dat.code;
			if (ccode != parent_company) {
				var v = all_company_data['kpi_value_d'][ccode]['ORI_NORMAL_NO'][date_type][date];
				if (!isNaN(v)) {
					val1 += Number(v);
				}
				var v = all_company_data['kpi_value_d'][ccode]['ORI_NO_SCH'][date_type][date];
				if (!isNaN(v)) {
					val2 += Number(v);
				}
				var v = all_company_data['kpi_value_sq_d'][ccode]['ORI_NORMAL_NO'][date_type][date];
				if (!isNaN(v)) {
					val_total_sq1 += Number(v);
				}
				var v = all_company_data['kpi_value_sq_d'][ccode]['ORI_NO_SCH'][date_type][date];
				if (!isNaN(v)) {
					val_total_sq2 += Number(v);
				}
			}
		}
	}

	var rate1 = val2 > 0 ? Math.round((Number(val1) / Number(val2)) * 1000) / 10 : 0;
	var rate2 = val_total_sq2 > 0 ? Math.round((Number(val_total_sq1) / Number(val_total_sq2)) * 1000) / 10 : 0;

	// 环比=(本期-上期)/上期×100%
	var hb = rate2 > 0 ? Math.round((rate1 - rate2) / rate2 * 1000) / 10 : 0;
	
	// RTD 始发正常率/始发正常率环比
	rate1 = curCompanyData.depNormalRate
	hb = curCompanyData.depNormalRateRatio
	

	$(element + '.kpi .val').text(rate1);
	$(element + '.kpi .val').removeClass('green');
	$(element + '.kpi .val').removeClass('red');
	if (rate1 >= groupNormalRateGoal) {
		$(element + '.kpi .val').addClass('green');
	} else {
		$(element + '.kpi .val').addClass('red');
	}

	$(element + '.hb .val').text(hb);
	if (hb > 0) {
		$(element + '.hb .green').removeClass('hide');
		$(element + '.hb .red').addClass('hide');
		$(element + '.hb .sub').show();
	} else if (hb < 0) {
		$(element + '.hb .green').addClass('hide');
		$(element + '.hb .red').removeClass('hide');
		$(element + '.hb .sub').show();
	} else {
		$(element + '.hb .green').addClass('hide');
		$(element + '.hb .red').addClass('hide');
		$(element + '.hb .sub').hide();
	}



	// 放行正常率
	var element = '.comp_kpi_details .DEP ';

	var val1 = 0;
	var val2 = 0;
	var val_total_sq1 = 0;
	var val_total_sq2 = 0;
	if (compcode != parent_company) {
		val1 = all_company_data['kpi_value_d'][compcode]['DISPAT_NORMAL_NO'][date_type][date];
		val2 = all_company_data['kpi_value_d'][compcode]['DISPAT_NO'][date_type][date];
		val_total_sq1 = all_company_data['kpi_value_sq_d'][compcode]['DISPAT_NORMAL_NO'][date_type][date];
		val_total_sq2 = all_company_data['kpi_value_sq_d'][compcode]['DISPAT_NO'][date_type][date];
	} else {
		// HNAHK 没数据，用子公司的求和
		var len = companylist.length;
		for (var i = 0; i < len; i++) {

			var dat = companylist[i];
			var ccode = dat.code;
			if (ccode != parent_company) {
				var v = all_company_data['kpi_value_d'][ccode]['DISPAT_NORMAL_NO'][date_type][date];
				if (!isNaN(v)) {
					val1 += Number(v);
				}
				var v = all_company_data['kpi_value_d'][ccode]['DISPAT_NO'][date_type][date];
				if (!isNaN(v)) {
					val2 += Number(v);
				}
				var v = all_company_data['kpi_value_sq_d'][ccode]['DISPAT_NORMAL_NO'][date_type][date];
				if (!isNaN(v)) {
					val_total_sq1 += Number(v);
				}
				var v = all_company_data['kpi_value_sq_d'][ccode]['DISPAT_NO'][date_type][date];
				if (!isNaN(v)) {
					val_total_sq2 += Number(v);
				}
			}
		}
	}


	var rate1 = val2 > 0 ? Math.round((Number(val1) / Number(val2)) * 1000) / 10 : 0;
	var rate2 = val_total_sq2 > 0 ? Math.round((Number(val_total_sq1) / Number(val_total_sq2)) * 1000) / 10 : 0;

	// 环比=(本期-上期)/上期×100%
	var hb = rate2 > 0 ? Math.round((rate1 - rate2) / rate2 * 1000) / 10 : 0;
	
	// RTD 放行正常率/放行正常率环比
	rate1 = curCompanyData.dispatNormalRate
	hb = curCompanyData.dispatNormalRateRatio


	$(element + '.kpi .val').text(rate1);
	$(element + '.kpi .val').removeClass('green');
	$(element + '.kpi .val').removeClass('red');
	if (rate1 >= groupNormalRateGoal) {
		$(element + '.kpi .val').addClass('green');
	} else {
		$(element + '.kpi .val').addClass('red');
	}

	$(element + '.hb .val').text(hb);
	if (hb > 0) {
		$(element + '.hb .green').removeClass('hide');
		$(element + '.hb .red').addClass('hide');
		$(element + '.hb .sub').show();
	} else if (hb < 0) {
		$(element + '.hb .green').addClass('hide');
		$(element + '.hb .red').removeClass('hide');
		$(element + '.hb .sub').show();
	} else {
		$(element + '.hb .green').addClass('hide');
		$(element + '.hb .red').addClass('hide');
		$(element + '.hb .sub').hide();
	}


	// 航班执行率
	var element = '.comp_kpi_details .EXE ';

	var val1 = 0;
	var val2 = 0;
	var val_total_sq1 = 0;
	var val_total_sq2 = 0;
	if (compcode != parent_company) {
		val1 = all_company_data['kpi_value_d'][compcode]['EXCUTED_NO'][date_type][date];
		val2 = all_company_data['kpi_value_d'][compcode]['SCH_NO'][date_type][date];
		val_total_sq1 = all_company_data['kpi_value_sq_d'][compcode]['EXCUTED_NO'][date_type][date];
		val_total_sq2 = all_company_data['kpi_value_sq_d'][compcode]['SCH_NO'][date_type][date];
	} else {
		// HNAHK 没数据，用子公司的求和
		var len = companylist.length;
		for (var i = 0; i < len; i++) {

			var dat = companylist[i];
			var ccode = dat.code;
			if (ccode != parent_company) {
				var v = all_company_data['kpi_value_d'][ccode]['EXCUTED_NO'][date_type][date];
				if (!isNaN(v)) {
					val1 += Number(v);
				}
				var v = all_company_data['kpi_value_d'][ccode]['SCH_NO'][date_type][date];
				if (!isNaN(v)) {
					val2 += Number(v);
				}
				var v = all_company_data['kpi_value_sq_d'][ccode]['EXCUTED_NO'][date_type][date];
				if (!isNaN(v)) {
					val_total_sq1 += Number(v);
				}
				var v = all_company_data['kpi_value_sq_d'][ccode]['SCH_NO'][date_type][date];
				if (!isNaN(v)) {
					val_total_sq2 += Number(v);
				}
			}
		}
	}


	var rate1 = val2 > 0 ? Math.round((Number(val1) / Number(val2)) * 1000) / 10 : 0;
	var rate2 = val_total_sq2 > 0 ? Math.round((Number(val_total_sq1) / Number(val_total_sq2)) * 1000) / 10 : 0;

	// 环比=(本期-上期)/上期×100%
	var hb = rate2 > 0 ? Math.round((rate1 - rate2) / rate2 * 1000) / 10 : 0;
	
	// RTD 航班执行率/航班执行率环比
	rate1 = curCompanyData.schNoExecuteRatio
	hb = curCompanyData.inlSchNoRatio

	$(element + '.kpi .val').text(rate1);

	$(element + '.hb .val').text(hb);
	if (hb > 0) {
		$(element + '.hb .green').removeClass('hide');
		$(element + '.hb .red').addClass('hide');
		$(element + '.hb .sub').show();
	} else if (hb < 0) {
		$(element + '.hb .green').addClass('hide');
		$(element + '.hb .red').removeClass('hide');
		$(element + '.hb .sub').show();
	} else {
		$(element + '.hb .green').addClass('hide');
		$(element + '.hb .red').addClass('hide');
		$(element + '.hb .sub').hide();
	}


	$('.comp_kpi_details .view .mid' + mid_fadein_id + ' .logo').css('background-image', 'url(img/logo_' + compcode +
		'.png)');
	$('.comp_kpi_details .view .mid' + mid_fadein_id + ' .title').text(compname);

	if (prev_comp_code) {
		$('.comp_kpi_details .view .mid' + mid_fadeout_id + ' .logo').css('background-image', 'url(img/logo_' +
			prev_comp_code + '.png)');
		$('.comp_kpi_details .view .mid' + mid_fadeout_id + ' .title').text(prev_comp_name);
	}


	// 主要延误原因
	$('.comp_kpi_details .view .mid' + mid_fadein_id + ' .delay_cause').text('');
	$('.comp_kpi_details .view .mid' + mid_fadeout_id + ' .delay_cause').text('');

	if (comp_cause_list && none_cause_list) {
		var cause_list1 = [];
		var cause_list2 = [];
		var cause_list = [];
		var cause_listobj = {};

		if (compcode != parent_company) {
			cause_list1 = comp_cause_list[compcode];
			cause_list2 = none_cause_list[compcode];
			cause_list = cause_list1.concat(cause_list2);

		} else {
			// HNAHK 没数据，用子公司的求和
			var len = companylist.length;
			for (var i = 0; i < len; i++) {

				var dat = companylist[i];
				var ccode = dat.code;
				if (ccode != parent_company) {
					var lst = comp_cause_list[ccode];
					for (var j = 0; j < lst.length; j++) {
						var o = lst[j];
						if (cause_listobj[o.name] == undefined) {
							cause_listobj[o.name] = 0;
						}
						cause_listobj[o.name] += Number(o.val);
					}
					var lst = none_cause_list[ccode];
					for (var j = 0; j < lst.length; j++) {
						var o = lst[j];
						if (cause_listobj[o.name] == undefined) {
							cause_listobj[o.name] = 0;
						}
						cause_listobj[o.name] += Number(o.val);
					}
				}
			}

			for (var name in cause_listobj) {
				var o = {
					'name': name,
					'val': cause_listobj[name]
				};
				cause_list.push(o);
			}
		}

		if (cause_list.length > 0) {
			cause_list.sort(function(a, b) {
				return b.val - a.val
			});
			var causename = cause_list[0].name;

			$('.comp_kpi_details .view .mid' + mid_fadein_id + ' .delay_cause').text(causename);
			if (prev_comp_code) {
				$('.comp_kpi_details .view .mid' + mid_fadeout_id + ' .delay_cause').text(prev_comp_delay_cause);
			}
		}


	}

	$('.comp_kpi_details .view .mid' + mid_fadein_id).attr('style', 'opacity: 0; ');
	$('.comp_kpi_details .view .mid' + mid_fadeout_id).attr('style', 'opacity: 1; ');


	$('.comp_kpi_details .view .mid' + mid_fadein_id).animate({
		opacity: 1,
	}, {
		queue: false,
		duration: 500,
		step: function(now, fx) {
			if (fx.prop == 'opacity') {
				var scale = 3 - now * 2;
				$(this).css('transform', 'scale(' + scale + ')');
			}
		},
	});

	$('.comp_kpi_details .view .mid' + mid_fadeout_id).animate({
		opacity: 0,
	}, {
		queue: false,
		duration: 300,
		step: function(now, fx) {
			if (fx.prop == 'opacity') {
				var ss = Math.min((now + 0.3), 1);
				$(this).css('transform', 'scale(' + ss + ')');
			}
		},
	});

	$('#grid_' + compcode + ' .bg').show();
	$('#grid_' + compcode + ' .bg').fadeOut();

	prev_comp_code = compcode;
	prev_comp_name = compname;
	prev_comp_delay_cause = causename;

	if (mid_fadein_id == 1) {
		mid_fadein_id = 2;
		mid_fadeout_id = 1;
	} else {
		mid_fadein_id = 1;
		mid_fadeout_id = 2;
	}

}



// 各公司 延误原因分析
var all_company_avg_delay_rate;

function setChartPop1() {

	if (company_delay_rate_list == undefined) {
		return;
	}

	// ---------------------------
	var chart_id1 = 'chart_pop1';
	var kpi_code = '';
	var colors = [
		['#00c311', '#007892'], //柱状图渐变颜色
		['#ff2929', '#b23e5c'], //柱状图渐变颜色
	];
	// ---------------------------

	var markLineRate = 0;
	var xAxisData = [];
	var data_s1 = []; //主KPI


	var len = companylist.length;

	var rate_sum = 0;
	var num = 0;
	for (var i = 0; i < len; i++) {

		var dat = companylist[i];
		var compcode = dat.code;
		if (compcode != parent_company) {
			var rate = company_delay_rate_list[compcode];
			if (rate > 0) { // HNA_BUG_234, 避免点击按钮后柱状图显示这三家为空
				rate_sum += rate;
				num++
				xAxisData.push(dat.nameabbr);
			}
		}
	}

	markLineRate = Math.round(rate_sum / num * 10) / 10;

	for (var i = 0; i < len; i++) {

		var dat = companylist[i];
		var compcode = dat.code;
		if (compcode != parent_company) {
			var rate = company_delay_rate_list[compcode];

			if (rate > 0) { // HNA_BUG_234, 避免点击按钮后柱状图显示这三家为空
				if (rate <= markLineRate) {
					color = new echarts.graphic.LinearGradient(
						0, 0, 0, 1, [{
							offset: 0,
							color: colors[0][0]
						}, {
							offset: 1,
							color: colors[0][1]
						}]);
					color2 = colors[0][0];

				} else {
					color = new echarts.graphic.LinearGradient(
						0, 0, 0, 1, [{
							offset: 0,
							color: colors[1][0]
						}, {
							offset: 1,
							color: colors[1][1]
						}]);
					color2 = colors[1][0];
				}
				var dat = {
					value: rate,
					itemStyle: {
						normal: {
							color: color
						}
					},
					label: {
						normal: {
							color: color2
						}
					},
				}
				data_s1.push(dat);
			}


		}
	}


	// ---------------------------------------------------------
	// 主KPI图表
	var chart = echarts.init(document.getElementById(chart_id1));

	var option = {
		tooltip: {
			show: true,
			formatter: function(params, ticket, callback) {
				//return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
				return params.name + ': ' + params.value;
			},

		},
		legend: {
			show: false,
			orient: 'horizontal',
			x: 'center',
			itemWidth: 10,
			itemHeight: 10,
			padding: 10,
			itemGap: 10,
			textStyle: {
				color: 'rgba(255,255,255,0.7)',
				fontSize: 11 + fontSizeDiff()
			},
			data: [{
				name: '',
				icon: 'circle',
			}]
		},
		grid: {
			top: 20,
			left: 55,
			right: 40,
			bottom: 25,
		},
		xAxis: [{
			type: 'category',
			data: xAxisData,
			boundaryGap: [10, 10],
			nameTextStyle: {
				color: '#41a8ff'
			},
			axisLine: {
				lineStyle: {
					color: '#84baf0' // 轴线颜色
				}
			},
			axisLabel: {
				interval: 0, // 强制显示所有标签
				rotate: 0,
				textStyle: {
					color: '#41a8ff', // 轴标签颜色大小
					fontSize: 12 + fontSizeDiff(),
				},
			},
			axisTick: {
				show: false, // 不显示刻度线
			}
		}],
		yAxis: [{
			type: 'value',
			name: '',
			nameTextStyle: {
				color: '#41a8ff',
				fontSize: 12 + fontSizeDiff()
			},
			axisLabel: {
				textStyle: {
					color: '#41a8ff',
					fontSize: 12 + fontSizeDiff(),
				},
				formatter: '{value}%'
			},
			axisLine: {
				lineStyle: {
					color: 'rgba(0,0,0,0)'
				}
			},
			splitLine: {
				show: true,
				lineStyle: {
					color: ['rgba(255,255,255,0.05)'] // 分割线颜色
				}
			},
		}],
		series: [{
			name: '',
			type: 'bar',
			barWidth: 12,
			data: data_s1,
			label: {
				normal: {
					show: true,
					position: 'top'
				}
			},
			itemStyle: {
				normal: {
					color: new echarts.graphic.LinearGradient(
						0, 0, 0, 1, [{
							offset: 0,
							color: colors[0][0]
						}, {
							offset: 1,
							color: colors[0][1]
						}]),
				}
			},
			markLine: {
				silent: true,
				data: [{
					name: '',
					yAxis: markLineRate,
					lineStyle: {
						normal: {
							color: '#1699e4',
							type: 'solid',
						}
					},
				}],
				symbol: '',
				symbolSize: 0,
				label: {
					normal: {
						position: 'end',
						formatter: '{c}%',
					}
				},
			},
		}]
	};

	chart.setOption(option);


}


// 各公司 正常率

function setChartPop2() {
	
	
	if (all_company_noraml_rate == undefined) {
		return;
	}

	// ---------------------------
	var chart_id1 = 'chart_pop1';
	var kpi_code = '';
	var colors = [
		['#00c311', '#007892'], //柱状图渐变颜色
		['#ff2929', '#b23e5c'], //柱状图渐变颜色
	];
	// ---------------------------

	var markLineRate = all_company_avg_noraml_rate;
	var xAxisData = [];
	var data_s1 = []; //主KPI

	var len = companylist.length;

	for (var i = 0; i < len; i++) {

		var dat = companylist[i];
		var compcode = dat.code;
		if (compcode != parent_company) {
			var rate = all_company_noraml_rate[compcode];
			xAxisData.push(dat.nameabbr);

			if (rate >= markLineRate) {
				color = new echarts.graphic.LinearGradient(
					0, 0, 0, 1, [{
						offset: 0,
						color: colors[0][0]
					}, {
						offset: 1,
						color: colors[0][1]
					}]);
				color2 = colors[0][0];
			} else {
				color = new echarts.graphic.LinearGradient(
					0, 0, 0, 1, [{
						offset: 0,
						color: colors[1][0]
					}, {
						offset: 1,
						color: colors[1][1]
					}]);
				color2 = colors[1][0];
			}
			var dat = {
				value: rate,
				itemStyle: {
					normal: {
						color: color
					}
				},
				label: {
					normal: {
						color: color2
					}
				},
			}
			data_s1.push(dat);
		}
	}
	
	// RTD 成员公司正常率情况
	len = sysCompanyKpiList.length;
	markLineRate = curCompanyData.normalRate
	var listAll = []
	data_s1 =[]
	xAxisData =[]
	listAll = sysCompanyKpiList.filter(el=>el.companyName !=='航空集团' && el.companyName !='境内航司')
	// listAll.sort(function(a,b){ return b.normalRate - a.normalRate});
	listAll.map(el=>{
		var rate = el.normalRate.toFixed(1)*100/100
		xAxisData.push(el.companyName);
		if(rate > markLineRate){
			color = new echarts.graphic.LinearGradient(
				0, 0, 0, 1, [{
					offset: 0,
					color: colors[0][0]
				}, {
					offset: 1,
					color: colors[0][1]
				}]);
			color2 = colors[0][0];
		}else{
			color = new echarts.graphic.LinearGradient(
				0, 0, 0, 1, [{
					offset: 0,
					color: colors[1][0]
				}, {
					offset: 1,
					color: colors[1][1]
				}]);
			color2 = colors[1][0];
		}
		var dat = {
			value: rate,
			itemStyle: {
				normal: {
					color: color
				}
			},
			label: {
				normal: {
					color: color2
				}
			},
		}
		data_s1.push(dat);
	})
	



	// ---------------------------------------------------------
	// 主KPI图表
	var chart = echarts.init(document.getElementById(chart_id1));

	var option = {
		tooltip: {
			show: true,
			formatter: function(params, ticket, callback) {
				//return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
				return params.name + ': ' + params.value;
			},

		},
		legend: {
			show: false,
			orient: 'horizontal',
			x: 'center',
			itemWidth: 10,
			itemHeight: 10,
			padding: 10,
			itemGap: 10,
			textStyle: {
				color: 'rgba(255,255,255,0.7)',
				fontSize: 11 + fontSizeDiff()
			},
			data: [{
				name: '',
				icon: 'circle',
			}]
		},
		grid: {
			top: 20,
			left: 55,
			right: 40,
			bottom: 25,
		},
		xAxis: [{
			type: 'category',
			data: xAxisData,
			boundaryGap: [10, 10],
			nameTextStyle: {
				color: '#41a8ff'
			},
			axisLine: {
				lineStyle: {
					color: '#84baf0' // 轴线颜色
				}
			},
			axisLabel: {
				interval: 0, // 强制显示所有标签
				rotate: 0,
				textStyle: {
					color: '#41a8ff', // 轴标签颜色大小
					fontSize: 12 + fontSizeDiff(),
				},
			},
			axisTick: {
				show: false, // 不显示刻度线
			}
		}],
		yAxis: [{
			type: 'value',
			name: '',
			nameTextStyle: {
				color: '#41a8ff',
				fontSize: 12 + fontSizeDiff()
			},
			axisLabel: {
				textStyle: {
					color: '#41a8ff',
					fontSize: 12 + fontSizeDiff(),
				},
				formatter: '{value}'
			},
			axisLine: {
				lineStyle: {
					color: 'rgba(0,0,0,0)'
				}
			},
			splitLine: {
				show: true,
				lineStyle: {
					color: ['rgba(255,255,255,0.05)'] // 分割线颜色
				}
			},
		}],
		series: [{
			name: '',
			type: 'bar',
			barWidth: 12,
			data: data_s1,
			label: {
				normal: {
					show: true,
					position: 'top'
				}
			},
			itemStyle: {
				normal: {
					color: new echarts.graphic.LinearGradient(
						0, 0, 0, 1, [{
							offset: 0,
							color: colors[0][0]
						}, {
							offset: 1,
							color: colors[0][1]
						}]),
				}
			},
			markLine: {
				silent: true,
				data: [{
					name: '',
					yAxis: markLineRate,
					lineStyle: {
						normal: {
							color: '#1699e4',
							type: 'solid',
						}
					},
				}],
				symbol: '',
				symbolSize: 0,
				label: {
					normal: {
						position: 'end',
						formatter: '{c}%',
					}
				},
			},
		}]
	};

	chart.setOption(option);


}



// 各公司 始发正常率

function setChartPop3() {

	if (all_company_noraml_rate_ori == undefined) {
		return;
	}

	// ---------------------------
	var chart_id1 = 'chart_pop1';
	var kpi_code = '';
	var colors = [
		['#00c311', '#007892'], //柱状图渐变颜色
		['#ff2929', '#b23e5c'], //柱状图渐变颜色
	];
	// ---------------------------

	var markLineRate = all_company_avg_noraml_rate_ori;
	var xAxisData = [];
	var data_s1 = []; //主KPI


	var len = companylist.length;
	
	for (var i = 0; i < len; i++) {

		var dat = companylist[i];
		dat = sysCompanyKpiList[i]
		var compcode = dat.code;
		if (compcode != parent_company) {
			var rate = all_company_noraml_rate_ori[compcode];
			xAxisData.push(dat.nameabbr);

			if (rate >= markLineRate) {
				color = new echarts.graphic.LinearGradient(
					0, 0, 0, 1, [{
						offset: 0,
						color: colors[0][0]
					}, {
						offset: 1,
						color: colors[0][1]
					}]);
				color2 = colors[0][0];
			} else {
				color = new echarts.graphic.LinearGradient(
					0, 0, 0, 1, [{
						offset: 0,
						color: colors[1][0]
					}, {
						offset: 1,
						color: colors[1][1]
					}]);
				color2 = colors[1][0];
			}
			var dat = {
				value: rate,
				itemStyle: {
					normal: {
						color: color
					}
				},
				label: {
					normal: {
						color: color2
					}
				},
			}
			data_s1.push(dat);
		}
	}
	
	// RTD 各公司始发正常率
	len = sysCompanyKpiList.length;
	markLineRate = curCompanyData.depNormalRate
	var listAll = []
	data_s1 =[]
	xAxisData =[]
	listAll = sysCompanyKpiList.filter(el=>el.companyName !=='航空集团' && el.companyName !='境内航司')
	listAll.sort(function(a,b){ return b.normalRate - a.normalRate});
	listAll.map(el=>{
		var rate = el.depNormalRate.toFixed(1)*100/100
		xAxisData.push(el.companyName);
		if(rate > markLineRate){
			color = new echarts.graphic.LinearGradient(
				0, 0, 0, 1, [{
					offset: 0,
					color: colors[0][0]
				}, {
					offset: 1,
					color: colors[0][1]
				}]);
			color2 = colors[0][0];
		}else{
			color = new echarts.graphic.LinearGradient(
				0, 0, 0, 1, [{
					offset: 0,
					color: colors[1][0]
				}, {
					offset: 1,
					color: colors[1][1]
				}]);
			color2 = colors[1][0];
		}
		var dat = {
			value: rate,
			itemStyle: {
				normal: {
					color: color
				}
			},
			label: {
				normal: {
					color: color2
				}
			},
		}
		data_s1.push(dat);
	})
	
	// ---------------------------------------------------------
	// 主KPI图表
	var chart = echarts.init(document.getElementById(chart_id1));

	var option = {
		tooltip: {
			show: true,
			formatter: function(params, ticket, callback) {
				//return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
				return params.name + ': ' + params.value;
			},

		},
		legend: {
			show: false,
			orient: 'horizontal',
			x: 'center',
			itemWidth: 10,
			itemHeight: 10,
			padding: 10,
			itemGap: 10,
			textStyle: {
				color: 'rgba(255,255,255,0.7)',
				fontSize: 11 + fontSizeDiff()
			},
			data: [{
				name: '',
				icon: 'circle',
			}]
		},
		grid: {
			top: 20,
			left: 55,
			right: 40,
			bottom: 25,
		},
		xAxis: [{
			type: 'category',
			data: xAxisData,
			boundaryGap: [10, 10],
			nameTextStyle: {
				color: '#41a8ff'
			},
			axisLine: {
				lineStyle: {
					color: '#84baf0' // 轴线颜色
				}
			},
			axisLabel: {
				interval: 0, // 强制显示所有标签
				rotate: 0,
				textStyle: {
					color: '#41a8ff', // 轴标签颜色大小
					fontSize: 12 + fontSizeDiff(),
				},
			},
			axisTick: {
				show: false, // 不显示刻度线
			}
		}],
		yAxis: [{
			type: 'value',
			name: '',
			nameTextStyle: {
				color: '#41a8ff',
				fontSize: 12 + fontSizeDiff()
			},
			axisLabel: {
				textStyle: {
					color: '#41a8ff',
					fontSize: 12 + fontSizeDiff(),
				},
				formatter: '{value}'
			},
			axisLine: {
				lineStyle: {
					color: 'rgba(0,0,0,0)'
				}
			},
			splitLine: {
				show: true,
				lineStyle: {
					color: ['rgba(255,255,255,0.05)'] // 分割线颜色
				}
			},
		}],
		series: [{
			name: '',
			type: 'bar',
			barWidth: 12,
			data: data_s1,
			label: {
				normal: {
					show: true,
					position: 'top'
				}
			},
			itemStyle: {
				normal: {
					color: new echarts.graphic.LinearGradient(
						0, 0, 0, 1, [{
							offset: 0,
							color: colors[0][0]
						}, {
							offset: 1,
							color: colors[0][1]
						}]),
				}
			},
			markLine: {
				silent: true,
				data: [{
					name: '',
					yAxis: markLineRate,
					lineStyle: {
						normal: {
							color: '#1699e4',
							type: 'solid',
						}
					},
				}],
				symbol: '',
				symbolSize: 0,
				label: {
					normal: {
						position: 'end',
						formatter: '{c}%',
					}
				},
			},
		}]
	};

	chart.setOption(option);


}


// 各公司 到港正常率

function setChartPop4() {

	if (all_company_noraml_rate_arr == undefined) {
		return;
	}

	// ---------------------------
	var chart_id1 = 'chart_pop1';
	var kpi_code = '';
	var colors = [
		['#00c311', '#007892'], //柱状图渐变颜色
		['#ff2929', '#b23e5c'], //柱状图渐变颜色
	];
	// ---------------------------

	var markLineRate = all_company_avg_noraml_rate_arr;
	var xAxisData = [];
	var data_s1 = []; //主KPI


	var len = companylist.length;

	for (var i = 0; i < len; i++) {

		var dat = companylist[i];
		var compcode = dat.code;
		if (compcode != parent_company) {
			var rate = all_company_noraml_rate_arr[compcode];
			xAxisData.push(dat.nameabbr);

			if (rate >= markLineRate) {
				color = new echarts.graphic.LinearGradient(
					0, 0, 0, 1, [{
						offset: 0,
						color: colors[0][0]
					}, {
						offset: 1,
						color: colors[0][1]
					}]);
				color2 = colors[0][0];
			} else {
				color = new echarts.graphic.LinearGradient(
					0, 0, 0, 1, [{
						offset: 0,
						color: colors[1][0]
					}, {
						offset: 1,
						color: colors[1][1]
					}]);
				color2 = colors[1][0];
			}
			var dat = {
				value: rate,
				itemStyle: {
					normal: {
						color: color
					}
				},
				label: {
					normal: {
						color: color2
					}
				},
			}
			data_s1.push(dat);
		}
	}

	// RTD 各公司到港正常率正常率
	len = sysCompanyKpiList.length;
	markLineRate = curCompanyData.arrNormalRate
	var listAll = []
	data_s1 =[]
	xAxisData =[]
	listAll = sysCompanyKpiList.filter(el=>el.companyName !=='航空集团' && el.companyName !='境内航司')
	listAll.sort(function(a,b){ return b.normalRate - a.normalRate});
	listAll.map(el=>{
		var rate = el.arrNormalRate.toFixed(1)*100/100
		xAxisData.push(el.companyName);
		if(rate > markLineRate){
			color = new echarts.graphic.LinearGradient(
				0, 0, 0, 1, [{
					offset: 0,
					color: colors[0][0]
				}, {
					offset: 1,
					color: colors[0][1]
				}]);
			color2 = colors[0][0];
		}else{
			color = new echarts.graphic.LinearGradient(
				0, 0, 0, 1, [{
					offset: 0,
					color: colors[1][0]
				}, {
					offset: 1,
					color: colors[1][1]
				}]);
			color2 = colors[1][0];
		}
		var dat = {
			value: rate,
			itemStyle: {
				normal: {
					color: color
				}
			},
			label: {
				normal: {
					color: color2
				}
			},
		}
		data_s1.push(dat);
	})

	// ---------------------------------------------------------
	// 主KPI图表
	var chart = echarts.init(document.getElementById(chart_id1));

	var option = {
		tooltip: {
			show: true,
			formatter: function(params, ticket, callback) {
				//return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
				return params.name + ': ' + params.value;
			},

		},
		legend: {
			show: false,
			orient: 'horizontal',
			x: 'center',
			itemWidth: 10,
			itemHeight: 10,
			padding: 10,
			itemGap: 10,
			textStyle: {
				color: 'rgba(255,255,255,0.7)',
				fontSize: 11 + fontSizeDiff()
			},
			data: [{
				name: '',
				icon: 'circle',
			}]
		},
		grid: {
			top: 20,
			left: 55,
			right: 40,
			bottom: 25,
		},
		xAxis: [{
			type: 'category',
			data: xAxisData,
			boundaryGap: [10, 10],
			nameTextStyle: {
				color: '#41a8ff'
			},
			axisLine: {
				lineStyle: {
					color: '#84baf0' // 轴线颜色
				}
			},
			axisLabel: {
				interval: 0, // 强制显示所有标签
				rotate: 0,
				textStyle: {
					color: '#41a8ff', // 轴标签颜色大小
					fontSize: 12 + fontSizeDiff(),
				},
			},
			axisTick: {
				show: false, // 不显示刻度线
			}
		}],
		yAxis: [{
			type: 'value',
			name: '',
			nameTextStyle: {
				color: '#41a8ff',
				fontSize: 12 + fontSizeDiff()
			},
			axisLabel: {
				textStyle: {
					color: '#41a8ff',
					fontSize: 12 + fontSizeDiff(),
				},
				formatter: '{value}'
			},
			axisLine: {
				lineStyle: {
					color: 'rgba(0,0,0,0)'
				}
			},
			splitLine: {
				show: true,
				lineStyle: {
					color: ['rgba(255,255,255,0.05)'] // 分割线颜色
				}
			},
		}],
		series: [{
			name: '',
			type: 'bar',
			barWidth: 12,
			data: data_s1,
			label: {
				normal: {
					show: true,
					position: 'top'
				}
			},
			itemStyle: {
				normal: {
					color: new echarts.graphic.LinearGradient(
						0, 0, 0, 1, [{
							offset: 0,
							color: colors[0][0]
						}, {
							offset: 1,
							color: colors[0][1]
						}]),
				}
			},
			markLine: {
				silent: true,
				data: [{
					name: '',
					yAxis: markLineRate,
					lineStyle: {
						normal: {
							color: '#1699e4',
							type: 'solid',
						}
					},
				}],
				symbol: '',
				symbolSize: 0,
				label: {
					normal: {
						position: 'end',
						formatter: '{c}%',
					}
				},
			},
		}]
	};

	chart.setOption(option);


}



//切换tab
$('.block_l1 .tab').on('click', function(evt) {
	var id = $(this).attr('tab-id');
	$('.block_l1 .tab').removeClass('selected');
	$('.block_l1 .tab' + id).addClass('selected');
	$('.block_l1 .tabc').hide();
	$('.block_l1 .tabc' + id).show();


})

//切换tab
$('.block_r2 .tab').on('click', function(evt) {
	var id = $(this).attr('tab-id');
	$('.block_r2 .tab').removeClass('selected');
	$('.block_r2 .tab' + id).addClass('selected');
	$('.block_r2 .tabc').hide();
	$('.block_r2 .tabc' + id).show();

})



function showChartPopover(type) {
	if (!all_company_data) {
		//return;
	}

	var timeTitle = $(".combotab_selected .combobox_label").text();
	if (type == 1) {
		$('#popover_chart .tit').text(timeTitle + '公司原因占比情况');
		$('#popover_chart .legend1').text('成员公司占比情况');
		$('#popover_chart .legend2').text('整体占比情况');

		setChartPop1();

	} else if (type == 2) {
		$('#popover_chart .tit').text(timeTitle + '成员公司正常率情况');
		$('#popover_chart .legend1').text('成员公司正常率情况');
		$('#popover_chart .legend2').text('本公司整体正常率');

		setChartPop2();

	} else if (type == 3) {
		$('#popover_chart .tit').text(timeTitle + '成员公司始发正常率情况');
		$('#popover_chart .legend1').text('成员公司始发正常率');
		$('#popover_chart .legend2').text('本公司整体始发正常率');

		setChartPop3();

	} else if (type == 4) {
		$('#popover_chart .tit').text(timeTitle + '成员公司到港正常率情况');
		$('#popover_chart .legend1').text('成员公司到港正常率');
		$('#popover_chart .legend2').text('本公司整体到港正常率');

		setChartPop4();

	}

	$('#popover_chart').fadeIn();
}

$('#popover_chart .btnx').on('click', function(event) {
	event.preventDefault();
	$('#popover_chart').fadeOut();
});


// 打开弹窗 延误原因分析
$('#btn_view_chart_l2').on('click', function(event) {
	event.preventDefault();
	showChartPopover(1);
});

// 打开弹窗 正常率
$('#btn_view_chart_r1').on('click', function(event) {
	event.preventDefault();
	showChartPopover(2);
});

// 打开弹窗 始发正常率
$('#btn_view_chart_r2t1').on('click', function(event) {
	event.preventDefault();
	showChartPopover(3);
});

// 打开弹窗 到港正常率
$('#btn_view_chart_r2t2').on('click', function(event) {
	event.preventDefault();
	showChartPopover(4);
});



// 左边栏弹窗
var selected_trend_blockid;

function showTrendChartPopover(blockid) {

	selected_trend_blockid = blockid;

	// ---------------------------
	var chart_id = 'chart_trend_pop';
	var kpi_code;
	var colors = [
		['#319ffd', '#319ffd'], //柱状图渐变颜色
		['#00b179', ''], //折线颜色
	];
	// ---------------------------

	var compname = companyCode2Name[current_company_code];


	if (blockid == 1) {
		$('#popover_trend_chart .tit').text(compname + ' 公司原因趋势');
	} else if (blockid == 2) {
		$('#popover_trend_chart .tit').text(compname + ' 非公司原因趋势');
	}

	$('#popover_trend_chart').css('top', '273px');
	$('#popover_trend_chart').css('left', '286px');
	$('#popover_trend_chart').fadeIn();

	var xAxisData = trendChartData.xAxisData;

	var data_s1 = [];
	var data_s2 = [];

	if (blockid == 1) {
		data_s1 = trendChartData.data_s1;
		data_s2 = trendChartData.data_s2;
	} else if (blockid == 2) {
		data_s1 = trendChartData.data_s1t2;
		data_s2 = trendChartData.data_s2t2;
	}

	// 只是显示最近六组数据
	if (xAxisData.length >= 6) {
		xAxisData = xAxisData.slice(xAxisData.length - 6)
		data_s1 = data_s1.slice(data_s1.length - 6)
		data_s2 = data_s2.slice(data_s2.length - 6)
	}


	// ---------------------------------------------------------

	var chart = echarts.init(document.getElementById(chart_id));

	var option = {
		tooltip: {
			show: true,
			formatter: function(params, ticket, callback) {
				if (params.seriesName == '同比') {
					return params.seriesName + '<br>' + params.name + ': ' + params.value + '%';
				} else {
					return params.seriesName + '<br>' + params.name + ': ' + params.value + '';
				}
			},
			extraCssText: "color:#021e55; padding:5px 10px 6px 10px; box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);background: rgb(178,225,254);background: -moz-linear-gradient(top,  rgba(178,225,254,1) 0%, rgba(100,192,250,1) 100%);background: -webkit-linear-gradient(top,  rgba(178,225,254,1) 0%,rgba(100,192,250,1) 100%);background: linear-gradient(to bottom,  rgba(178,225,254,1) 0%,rgba(100,192,250,1) 100%);filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#b2e1fe', endColorstr='#64c0fa',GradientType=0 );",
			backgroundColor: '',

		},
		title: {
			show: true,
			text: '占总计划航班比例',
			left: 145,
			top: 5,
			textStyle: {
				color: '#45a5f6', // 轴标签颜色大小
				fontSize: 12 + fontSizeDiff(),
			},
		},
		legend: {
			show: true,
			orient: 'horizontal',
			x: 'center',
			bottom: 5,
			itemWidth: 10,
			itemHeight: 10,
			padding: 10,
			itemGap: 10,
			textStyle: {
				color: '#45a5f6',
				fontSize: 12 + fontSizeDiff()
			},
			data: [{
				name: '占比',
			}, {
				name: '环比',
			}]
		},
		grid: {
			top: 54,
			left: 40,
			right: 44,
			bottom: 58,
		},
		xAxis: [{
			type: 'category',
			data: xAxisData,
			boundaryGap: true, // 去掉x轴两边的留白, 空着不显示line难看
			nameTextStyle: {
				color: '#45a5f6'
			},
			axisLine: {
				lineStyle: {
					color: '#45a5f6' // 轴线颜色
				}
			},
			axisLabel: {
				interval: 0, // 强制显示所有标签
				rotate: 0,
				textStyle: {
					color: '#45a5f6', // 轴标签颜色大小
					fontSize: 10 + fontSizeDiff(),
				},
				formatter: function(value, index) {
					return chartDateFormatter(value, index);
				},
			},
			splitLine: {
				show: false, // 不显示刻度线
			},
			axisTick: {
				show: false, // 不显示刻度线
			}

		}],
		yAxis: [{
			type: 'value',
			name: '占比', //公司原因不正常航班占计划航班的比例
			nameLocation: 'end',
			//min: 0,
			//max: 100,
			//interval: 10,
			nameTextStyle: {
				color: '#45a5f6',
				fontSize: 10 + fontSizeDiff(),
			},
			axisLabel: {
				textStyle: {
					color: '#45a5f6',
					fontSize: 10 + fontSizeDiff(),
					align: 'right',
				},
				margin: 5,
				formatter: '{value}%',
			},
			axisLine: {
				lineStyle: {
					color: 'rgba(0,0,0,0)'
				}
			},
			splitLine: {
				show: true,
				lineStyle: {
					color: ['rgba(69,165,246,0.1)'] // 分割线颜色
				}
			}
		}, {
			type: 'value',
			name: '环比',
			nameLocation: 'end',
			position: 'right',
			//min: 0,
			//max: 100,
			//interval: 10,
			nameTextStyle: {
				color: '#45a5f6',
				fontSize: 10 + fontSizeDiff(),
			},
			axisLabel: {
				textStyle: {
					color: '#45a5f6',
					fontSize: 10 + fontSizeDiff(),
					align: 'left',
				},
				margin: 5,
				formatter: '{value}%',
			},
			axisLine: {
				lineStyle: {
					color: 'rgba(0,0,0,0)'
				}
			},
			splitLine: {
				show: false,
				lineStyle: {
					color: ['rgba(69,165,246,0.1)'] // 分割线颜色
				}
			}
		}],
		series: [{
			name: '占比',
			type: 'bar',
			barWidth: 12,
			yAxisIndex: 0,
			data: data_s1,
			label: {
				normal: {
					show: true,
					position: 'top',
					color: colors[0][0],
					formatter: '{c}%',
				}
			},
			itemStyle: {
				normal: {
					color: new echarts.graphic.LinearGradient(
						0, 0, 0, 1, [{
							offset: 0,
							color: colors[0][0]
						}, {
							offset: 1,
							color: colors[0][1]
						}]),
				}
			},
		}, {
			name: '环比',
			type: 'line',
			smooth: false,
			yAxisIndex: 1,
			data: data_s2,
			symbol: 'circle',
			symbolSize: 6,
			lineStyle: {
				normal: {
					color: '#00b179',
				}
			},
			// 拐点
			itemStyle: {
				normal: {
					color: '#00b179',
					opacity: 1, //折线拐点

				}
			},

		}]
	};

	chart.setOption(option);

}

$('#popover_trend_chart .btnx').on('click', function(event) {
	event.preventDefault();
	$('#popover_trend_chart').fadeOut();
});

// 打开弹窗 左边栏 公司原因趋势
$('#btn_view_chart_l1t1').on('click', function(event) {
	event.preventDefault();
	showTrendChartPopover(1);
});
// 打开弹窗 左边栏 非公司原因趋势
$('#btn_view_chart_l1t2').on('click', function(event) {
	event.preventDefault();
	showTrendChartPopover(2);
});

// ---------------------- 

function onCompanyChanged(comp_code) {
	current_company_code = comp_code;
	checkCompanyReay();

}

function checkCompanyReay() {
	if (companyCode2Name[current_company_code] == undefined) {
		setTimeout(checkCompanyReay, 0);
	} else {
		updateAllKpi();
	}
}


// RTD 以下开始获取配置数据
var sysCompanyKpiList = []
function getSysCompanyKpiList() {
	$.ajax({
		type: 'get',
		url: "/bi/spring/sys-company-kpi-value/week/list.json",
		contentType: 'application/json',
		dataType: 'json',
		async: true,
		success: function(response) {
			sysCompanyKpiList = response.data
			setAllKpiData()
		},
		error: function() {}
	});
}


var curCompanyData={}
function setAllKpiData(){
	var hash = window.location.hash.substr(1);
	sysCompanyKpiList.map(item=>{
		if(item.companyCode===hash){
			curCompanyData = item
		}
	})
	getReason(hash)
}

var reasonByCompany = {}
function getReason(code){
	$.ajax({
		type: 'get',
		url: `/bi/spring/sys-company-delay-reason/week/getDelayReasonByCompanyCode.json?companyCode=${code}`,
		contentType: 'application/json',
		dataType: 'json',
		async: true,
		success: function(response) {
			reasonByCompany = response.data
			setCompayTabData()
		},
		error: function() {}
	});
}

function setCompayTabData(){
	$('.pagetitle .maintitle .tit').html('客运正常率分析')
	// RTD 公司原因
	$('.block_l1 .b1 .kpi .val').text(reasonByCompany.all.companyResaonRate);
	// RTD 非公司原因
	$('.block_l1 .b2 .kpi .val').text(reasonByCompany.all.externalResaonRate);
	// RTD 公司原因占比
	var comp_total = 0 
	var html = ''
	var len = reasonByCompany.companyReasonList.length;
	var fontsize = len > 10 ? 10 : 12;
	reasonByCompany.companyReasonList.map(el=>{
		html += '<tr style="font-size:' + fontsize + 'px">';
		html += '<td class="c1">' + el.reasonName + '</td>';
		html += '<td class="c2">' + el.rate + '%</td>';
		html += '<td class="c3"><span class="bar" style="width:' + `${170 * el.rate /100}` + 'px;"></span></td>';
		html += '</tr>';
	})

	$('.block_l1 .row2 .tabc1 table').html(html);

	comp_total = 0
	html = ''
	var len = reasonByCompany.externalReasonList.length;
	var fontsize = len > 10 ? 10 : 12;
	reasonByCompany.externalReasonList.map(el=>{
		html += '<tr style="font-size:' + fontsize + 'px">';
		html += '<td class="c1">' + el.reasonName + '</td>';
		html += '<td class="c2">' + el.rate + '%</td>';
		html += '<td class="c3"><span class="bar" style="width:' + `${170 * el.rate /100}` + 'px;"></span></td>';
		html += '</tr>';
	})

	$('.block_l1 .row2 .tabc2 table').html(html);
}


// RTD
getSysCompanyKpiList();

getAllCompanyKpiData()
getGroupNormalRateGoal();
getTop10();

function setTitleDate() {
	var date = '';
	if (date_type == 'L') {
		date = $('#main_cb_week .combobox_label').text();
	} else if (date_type == 'M') {
		date = $('#main_cb_month .combobox_label').text();
	}
	$('.pagetitle .maintitle .date').html(date); // 页面标题-时间
}


dfd.done(function() {
	if (!hasAllCompanyPermission()) {
		$(".block_r1").addClass("hide");
		$(".block_r2").addClass("hide");
		$(".ranking").addClass("hide");
		$(".block_l2").addClass("hide");
	}
})
