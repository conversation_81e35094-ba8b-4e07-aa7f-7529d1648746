document.oncontextmenu = function () {
  return false;
};

var COMP_CODE_PARENT = "HNAHK";

var company_code;
var companylist;
var companyCode2Sort;

var companyCoordScreenXY = [];
var all_company_data;
var selected_datetype;
var selected_date;
var usersCompayCodeList;

// ------- end -------

var rangeObj = this;

// 省份
var color = "#81d4f5";
var provinces = [
  {
    name: "China",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "北京",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "上海",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "天津",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "重庆",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "香港",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "澳门",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "台湾",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "安徽",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "福建",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "甘肃",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "广东",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "广西",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "贵州",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "海南",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "河北",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "河南",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "黑龙江",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "湖北",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "湖南",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "江苏",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "江西",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "吉林",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "辽宁",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "内蒙古",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "宁夏",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "青海",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "山东",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "山西",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "陕西",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "四川",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "西藏",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "新疆",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "云南",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "浙江",
    itemStyle: {
      normal: {
        areaColor: color,
      },
    },
  },
  {
    name: "藏南",
    itemStyle: {
      normal: {
        areaColor: color,
        borderWidth: 3,
        borderColor: color,
      },
    },
  },
  {
    name: "九段线",
    itemStyle: {
      normal: {
        areaColor: color,
        borderWidth: 3,
        borderColor: color,
      },
    },
  },
  {
    name: "钓鱼岛",
    itemStyle: {
      normal: {
        areaColor: color,
        borderWidth: 5,
        borderColor: color,
      },
    },
  },
];

var createBMap = function () {
  var series = [];

  option = {
    color: [],
    tooltip: {
      trigger: "item",
    },
    geo: {
      map: "world",
      roam: false,
      zoom: 3.5,
      center: [103.443948, 35.609289],
      silent: true,
      label: {
        emphasis: {
          show: false,
        },
      },
      itemStyle: {
        normal: {
          areaColor: "#1f6fd6",
          borderColor: "#022872",
        },
        emphasis: {
          areaColor: "#1f6fd6",
          borderColor: "#022872",
        },
      },
      regions: provinces,
    },
    backgroundColor: "#022872",
    series: series,
  };

  var myChart = echarts.init(document.getElementById("map"));
  myChart.setOption(option);
  window.onresize = myChart.resize;

  rangeObj.myChart = myChart;

  // 点击事件
  myChart.on("click", function (params) {
    params.event.event.stopPropagation();
    params.event.event.preventDefault();

    if (params.data.clickable) {
      var tipobj = {
        mouseX: mouseX,
        mouseY: mouseY,
        code: params.data.compcode,
      };

      // common.js
      window.parent.window.switchCompany(params.data.compcode);
      window.parent.window.stopAutoSwitchCompany();
    }
  });
};

// 设置地图上的数据
function setMapData(arpcode, arpmat, arpvol) {
  if (rangeObj.myChart != undefined) {
    if (all_company_data) {
      var seriesData = [];
      companyCoordScreenXY = {};

      for (var comp_code in all_company_data) {
        var dat = all_company_data[comp_code];

        if (
          comp_code != COMP_CODE_PARENT &&
          usersCompayCodeList.indexOf(comp_code) > -1
        ) {
          // var dat_nor = all_company_data[comp_code]['NORMAL_NO_T'][selected_datetype][selected_date];
          // var dat_sch = all_company_data[comp_code]['SCH_NO'][selected_datetype][selected_date];

          // var rate = Math.round((Number(dat_nor)/Number(dat_sch))*1000)/10
          var rate = (
            Number(
              all_company_data[comp_code]["NORMAL_RATE_ZT"][selected_datetype][
                selected_date
              ]
            ) * 100
          ).toFixed(1);

          var pinImgId = -1;
          if (rate > groupNormalRateGoal) {
            pinImgId = 0;
          } else {
            pinImgId = 2;
          }

          //if(pinImgId > -1){

          companyCoordScreenXY[comp_code] = geoCoord2Pixel(
            companyCoordinate[comp_code]
          );

          seriesData.push({
            name: comp_code,
            compcode: comp_code,
            rate: rate,
            value: companyCoordinate[comp_code],
            //symbol:'image://../img/mapmarkpoint'+pinImgId+'.png',
            symbol:
              "image://../img/logo_normal_rate" +
              pinImgId +
              "_" +
              comp_code +
              ".png",
            symbolSize: 46,
            clickable: true,
          });
          //}
        }
      }
    }

    var series = [];
    series.push({
      name: "",
      type: "scatter",
      coordinateSystem: "geo",
      zlevel: 3,
      label: {
        normal: {
          show: false,
        },
      },
      tooltip: {
        trigger: "item",
        show: false,
      },
      data: seriesData,
    });

    var options = rangeObj.myChart.getOption();
    options.series = series;

    rangeObj.myChart.setOption(options, true);
  } else {
    setTimeout(setMapData, 0);
  }
}

// 经纬度坐标转换成屏幕像素坐标
function geoCoord2Pixel(geoCoord) {
  var pos = rangeObj.myChart.convertToPixel("geo", geoCoord);
  return pos;
}

// common.js 中调用
function main(complist, code2sort) {
  companylist = complist;
  companyCode2Sort = code2sort;
  createBMap();
}

var groupNormalRateGoal;
function setRateMap(usersCompayList, data, datetype, date, normalRateGoal) {
  clearTimeout(itv_autoSwitchCompPopover);
  window.parent.window.hideCompanyPopover();

  groupNormalRateGoal = normalRateGoal;

  //if(company_code == COMP_CODE_PARENT){
  usersCompayCodeList = usersCompayList;
  all_company_data = data;
  selected_datetype = datetype;
  selected_date = date;
  if (rangeObj.setMapData != undefined) {
    rangeObj.setMapData();

    showCompanyPopover(company_code);
  }
  //}
}

// 用来从iframe外部调用的函数，切换公司
function onCompCodeChanged(compcode) {
  company_code = compcode;
}

var itv_autoSwitchCompPopover;
function showCompanyPopover(compcode) {
  var obj = companyCoordScreenXY[compcode];

  if (obj) {
    var tipobj = {
      mouseX: obj[0],
      mouseY: obj[1],
      code: compcode,
    };

    window.parent.window.showCompanyPopover(tipobj, true);
  }

  clearTimeout(itv_autoSwitchCompPopover);
  itv_autoSwitchCompPopover = setTimeout(
    startAutoSwitchCompanyPopover,
    20000,
    compcode
  );
}

function startAutoSwitchCompanyPopover(compcode) {
  // 20170821 禁止航司自动轮播
  return;

  // common.js
  if (!window.parent.window.autoSwitch) {
    itv_autoSwitchCompPopover = setTimeout(
      startAutoSwitchCompanyPopover,
      10,
      compcode
    );
    return;
  }

  // common.js
  if (window.parent.window.stopAllAutoSwitchCompanyInterval) {
    return;
  }

  if (window.parent.window.audioEnded || window.parent.window.audioPaused) {
    var next_compcde;
    var len = companylist.length;
    for (var i = 0; i < len; i++) {
      var dat = companylist[i];
      if (dat.code == compcode) {
        if (i < len - 1) {
          next_compcde = companylist[i + 1].code;
        } else {
          next_compcde = companylist[0].code;
        }
        break;
      }
    }

    window.parent.window.switchCompany(next_compcde);
  } else {
    itv_autoSwitchCompPopover = setTimeout(
      startAutoSwitchCompanyPopover,
      10,
      compcode
    );
  }
}

var mouseX, mouseY;
$(document).mousemove(function (e) {
  mouseX = e.pageX;
  mouseY = e.pageY;
});

// 截取小数位数
function trimDecimal(num, len) {
  var nnn = 1;
  for (var i = 0; i < len; i++) {
    nnn = nnn * 10;
  }
  return Math.round(num * nnn) / nnn;
}

// min ≤ r ≤ max
function randomNumRange(Min, Max) {
  var Range = Max - Min;
  var Rand = Math.random();
  var num = Min + Math.round(Rand * Range); //四舍五入
  return num;
}

// 获取参数
function getQueryString(name) {
  var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
  var r = window.location.search.substr(1).match(reg);
  if (r != null) return unescape(r[2]);
  return "";
}

function shuffle(arr) {
  var len = arr.length;
  for (var i = 0; i < len - 1; i++) {
    var idx = Math.floor(Math.random() * (len - i));
    var temp = arr[idx];
    arr[idx] = arr[len - i - 1];
    arr[len - i - 1] = temp;
  }
  return arr;
}
