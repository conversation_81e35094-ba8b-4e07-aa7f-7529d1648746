var provinceCoordinate = {
	'北京' : [116.395645, 39.929986],
	'上海' : [121.487899, 31.249162],
	'天津' : [117.210813, 39.14393],
	'重庆' : [106.530635, 29.544606],
	'香港' : [114.186124, 22.293586],
	'澳门' : [113.557519, 22.204118],
	'台湾' : [120.961454, 23.80406],
	'安徽' : [117.216005, 31.859252],
	'福建' : [117.984943, 26.050118],
	'甘肃' : [102.457625, 38.103267],
	'广东' : [113.394818, 23.408004],
	'广西' : [108.924274, 23.552255],
	'贵州' : [106.734996, 26.902826],
	'海南' : [109.733755, 19.180501],
	'河北' : [115.661434, 38.61384],
	'河南' : [113.486804, 34.157184],
	'黑龙江' : [128.047414, 47.356592],
	'湖北' : [112.410562, 31.209316],
	'湖南' : [111.720664, 27.695864],
	'江苏' : [119.368489, 33.013797],
	'江西' : [115.676082, 27.757258],
	'吉林' : [126.262876, 43.678846],
	'辽宁' : [122.753592, 41.6216],
	'内蒙古' : [114.415868, 43.468238],
	'宁夏' : [106.155481, 37.321323],
	'青海' : [96.202544, 35.499761],
	'山东' : [118.527663, 36.09929],
	'山西' : [112.515496, 37.866566],
	'陕西' : [109.503789, 35.860026],
	'四川' : [102.89916, 30.367481],
	'西藏' : [89.137982, 31.367315],
	'新疆' : [85.614899, 42.127001],
	'云南' : [101.592952, 24.864213],
	'浙江' : [119.957202, 29.159494],
}

// 地图行政区域边界划分
var provinceNames = [
	'北京',
	'上海市',
	'天津',
	'重庆',
	'香港',
	'澳门',
	'台湾',
	'安徽',
	'福建',
	'甘肃',
	'广东',
	'广西壮族自治区',
	'贵州',
	'海南',
	'河北',
	'河南',
	'黑龙江',
	'湖北',
	'湖南',
	'江苏',
	'江西',
	'吉林省',
	'辽宁',
	'内蒙古',
	'宁夏',
	'青海',
	'山东',
	'山西',
	'陕西',
	'四川',
	'西藏',
	'新疆',
	'云南',
	'浙江省',
]


var companyCoordinate = {
	'HU' : [110,22.083237],
	'8L' : [103.3,32.212365],
	'HX' : [114,24.73618],
	'PN' : [108.6,36.520178],
	'GS' : [117.3,40.469924],
	'JD' : [115,41.958308],
	'FU' : [119.1,28.171125],
	'UQ' : [87.4,46.018901],
	'GX' : [112,30.145845],
	
	'Y8' : [120.893285,34.03943],
	'9H' : [105.660323,38.737318],
	'GT' : [107.132107,25.246432],
	'UO' : [116.257166,26.246166],

}

var companyCodeToName = {
	'HU' : '航空股份',
	'8L' : '祥鹏航空',
	'HX' : '香港航空',
	'PN' : '西部航空',
	'GS' : '天津航空',
	'JD' : '首都航空',
	'FU' : '福州航空',
	'UQ' : '乌鲁木齐航空',
	'GX' : '北部湾航空',
	'Y8' : '扬子江快运',
	'9H' : '长安航空',
	'GT' : '桂林航空',
	'UO' : '香港快运',

}