
/**/

.page-wrapper {
  
}


.pagetitle{
  position: absolute;
  width: 100%;
  height: 54px;
  top: 70px;
  text-align: center;
}
.pagetitle .date_type_select {
  /*width: 160px;*/
  height: 30px;
  border: 1px solid #1a6cbc;
  border-radius: 3px;
  display: inline-block;
  vertical-align: middle;
  position: absolute;
  left: 870px;
  top: 10px;
  z-index: 1001;
  box-sizing: border-box;
}
.hidden{
  display: none !important;
}
.pagetitle .date_type_select .tab {
  display: inline-block;
  vertical-align: middle;
  width: 39px;
  height: 29px;
  float: left;
  box-sizing: border-box;
  text-align: center;
  line-height: 28px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  font-size: 14px;
  color: #44a3f4;
  border-right: 1px solid #2a81d2;
  z-index: 1001;
  pointer-events: auto;
}
.pagetitle .date_type_select .tab:last-child{
  border-right: 0;
}
.pagetitle .date_type_select .tab.hover {
  background-color: #2a81d2;
  color: white;
}
.pagetitle .date_type_select .tab:hover {
  background-color: #2a81d2;
  color: white;
}
.pagetitle .date_type_select .tab:nth-child(1) {
  position: relative;
  /*left: -1px;*/
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}
.pagetitle .date_type_select .tab:nth-child(4) {
  position: relative;
  /*right: -2px;*/
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}
.maintitle{
  color: #7fc4ff;
  width: 100%;
  font-size: 24px;
  padding: 8px 280px 0 0;
  margin: 0 auto;
  text-shadow: 0 0 10px #0d2452,0 0 10px #0d2452,0 0 20px #0c2d68,0 0 20px #0c2d68;
}
.submaintitle{
  color: #7fc4ff;
  width: 100%;
  font-size: 22px;
  padding: 0 20px;
  margin: 0 auto;
}

.pagetitle .data_refresh {
  display: inline-block;
  position: absolute;
  height: 24px;
  width: 24px;
  left: 1030px;
  top: 12px;
  z-index: 1001;
  background-image: url(../img/data_refresh.png);
  pointer-events: auto;
  cursor: pointer;
}

/*#main_cb_week {
  position: absolute;
  top: 8px;
  left: 768px;
  height: 30px;
  width: 85px;
  z-index: 1000;
  border-radius: 4px;
  border: 1px solid rgba(41, 140, 232, 0.6);
  background: #0b3379;
}
#main_cb_month {
  position: absolute;
  top: 8px;
  left: 768px;
  height: 30px;
  width: 104px;
  z-index: 1001;
}*/
.limcomb{
  position: absolute;
  top: 10px;
  left: 730px;
  height: 30px;
  width: 135px !important;
  z-index: 1001;
  border-radius: 4px;
  border: 1px solid rgba(41, 140, 232, 0.6);
  background: #0b3379;
}
.limcomb .combobox_label{
  display: inline-block;
  padding: 0;
  height: 30px;
  line-height: 30px;
  text-align: left;
  background-image: url(../img/combobox_arr.png);
  background-repeat: no-repeat;
  background-position: 115px center;
  border-radius: 4px;
  border-right: none;
  border: 0px !important;
  box-sizing: border-box;
  padding-left: 5px;
  width: 100%;

}
/*#main_cb_week .combobox_label
{
  padding: 6px 6px;
  background-image: url(../img/combobox_arr.png);
  background-repeat: no-repeat;
  background-position: 63px center;
  border-radius: 4px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}
#main_cb_month .combobox_label
{
  padding: 6px 6px;
  background-image: url(../img/combobox_arr.png);
  background-repeat: no-repeat;
  background-position: 83px center;
  border-radius: 4px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: none;
}*/

.combotab_selected .combobox_label {
  background-color: #2a81d2;
}
.combotab .combobox_label {
  /*background-color: #0950a2;*/
  /*opacity: 0.5;*/
  color: #8abfff;
}

#week_date_range {
  position: absolute;
  top: 47px;
  left: 700px;
  width: 160px;
  font-size: 14px;
  text-align: center;
  border: 1px solid #1f5fa8;
  border-radius: 2px;
  padding: 3px 0 5px 0px;
  background-color: #021d39;
  box-shadow: 0 1px 8px #021121;
  z-index: 999;
  display: none;
}


#hna_button{
  position: absolute;
  left: 365px;
  top: 139px;
  width: 88px;
  height: 38px;
  background: url(../img/a2.1-hna.png) no-repeat;
  background-size: contain;
  pointer-events: auto;
  cursor: pointer;
}


/* --- */
.block_l1 {
  height: 310px;
  left: 15px;
  top: 100px;
}
.block_l1 .cont{
  height: 617px;
  background-image: url(../img/a2.1.bg_l1.png);
  background-repeat: no-repeat;
  background-position: center 10px;
}
.block_l1 .cont .row1{
  position: absolute;
  width: 100%;
  height: 150px;
  background: url(../img/a2.1.ico1.png) no-repeat 246px 31px;
}
.block_l1 .cont .row1 .b1{
  position: absolute;
  top: 20px;
  left: 25px;
}
.block_l1 .cont .row1 .b2{
  position: absolute;
  top: 95px;
  left: 25px;
}
.block_l1 .cont .row1 .b3{
  position: absolute;
  top: 95px;
  left: 125px;
  text-align: left;
}

.block_l1 .cont .row2{
  position: absolute;
  width: 100%;
  height: 150px;
  top: 144px;
  background: url(../img/money.png) no-repeat 246px 31px;
  background-size: 13%;
}
.block_l1 .cont .row2 .b1{
  position: absolute;
  top: 20px;
  left: 25px;
  z-index: 100;
}
.block_l1 .cont .row2 .b1 .float-tips{
  display: none;
  font-size: 12px;
  position: relative;
  width: 410px;
  top: 0px;
  left: 0;
  border: 1px solid #237fd5;
  background: #083476;
  z-index: 500;
  text-align: left;
  padding: 10px;
  box-sizing: border-box;
}
.block_l1 .cont .row2 .b2{
  position: absolute;
  top: 95px;
  left: 25px;
}
.block_l1 .cont .row2 .b3{
  position: absolute;
  width: 100px;
  top: 20px;
  right: 25px;
  text-align: right;
}
.block_l1 .cont .row2 .b4{
  position: absolute;
  width: 100px;
  top: 95px;
  right: 25px;
  text-align: right;
}
.block_l1 .cont .row3{
  position: absolute;
  width: 288px;
  height: 308px;
  top: 302px;
  left: 10px;
  overflow: hidden;
}
.block_l1 .cont .row3 .slidepage{
  position: absolute;
  width: 288px;
  height: 282px;
  background-image: url(../img/a2.1.bg_l2.png);
  background-repeat: no-repeat;
  background-position: left top;
  transition: all 0.3s ease-out;
  pointer-events: auto;
}
.block_l1 .cont .row3 .page1{
  left: 0px;
}
.block_l1 .cont .row3 .page2{
  left: 288px;
}
.block_l1 .cont .row3 .btn_prev{
  position: absolute;
  width: 80px;
  height: 20px;
  color: #47aafd;
  cursor: pointer;
  font-size: 12px;
  bottom: 2px;
  left: 0px;

}
.block_l1 .cont .row3 .btn_next{
  position: absolute;
  width: 80px;
  height: 20px;
  color: #47aafd;
  cursor: pointer;
  font-size: 12px;
  bottom: 2px;
  right: 0px;
  text-align: right;
}
.block_l1 .cont .row3 .disabled{
  pointer-events: none;
  opacity: 0.3;
}


.block_l1 .blk1 {
  position: absolute;
  height: 48px;
  top: 6px;
  left: 13px;
}
.block_l1 .blk2 {
  position: absolute;
  text-align: right;
  height: 48px;
  top: 6px;
  right: 13px;
}
.block_l1 .blk3 {
  position: absolute;
  height: 48px;
  bottom: 80px;
  left: 13px;
}
.block_l1 .blk4 {
  position: absolute;
  text-align: right;
  height: 48px;
  bottom: 80px;
  right: 13px;
}
.block_l1 .l2 {
  display: block;
  margin-top: 15px;
}


.block_l1 .blk1 .btnx1{
  position: absolute;
  width: 25px;
  height: 25px;
  top: 0;
  left: 90px;
  border-radius:15px;
  background: #083476 url(../img/a2.1.qs.png) no-repeat center center;
  pointer-events: auto;
  cursor: pointer;
}

.block_l1 .blk2 .btnx2{
  position: absolute;
  width: 25px;
  height: 25px;
  top: 0;
  right: 95px;
  border-radius:15px;
  background: #083476 url(../img/a2.1.qs.png) no-repeat center center;
  pointer-events: auto;
  cursor: pointer;
}

.block_l1 .blk3 .btnx3{
  position: absolute;
  width: 25px;
  height: 25px;
  top: 95px;
  left: 90px;
  border-radius:15px;
  background: #083476 url(../img/a2.1.qs.png) no-repeat center center;
  pointer-events: auto;
  cursor: pointer;
}

.block_l1 .blk4 .btnx4{
  position: absolute;
  width: 25px;
  height: 25px;
  top: 95px;
  right: 95px;
  border-radius:15px;
  background: #083476 url(../img/a2.1.qs.png) no-repeat center center;
  pointer-events: auto;
  cursor: pointer;
}

.block_l1 .row3 .page1 .rw2 {
  line-height:12px; margin-bottom:8px;
}
.block_l1 .row3 .page1 .rw3 {
  line-height:12px;
}
.block_l1 .row3 .page1 .rw4 {
  line-height:13px; margin-bottom:5px;
}
.block_l1 .row3 .page1 .rw5 {
  line-height:13px;
}

.block_l1 .row3 .page2 .rw2 {
  line-height:12px; margin-bottom:8px;
}
.block_l1 .row3 .page2 .rw3 {
  line-height:12px;
}
.block_l1 .row3 .page2 .rw4 {
  line-height:13px; margin-bottom:5px;
}
.block_l1 .row3 .page2 .rw5 {
  line-height:13px;
}


/* --- */
.tab_block {
  position: absolute;
  width: 167px;
  height: 35px;
  cursor: pointer;
  text-align: center;
  line-height: 27px;
  font-size: 15px;
  padding-left: 10px;
  font-weight: bold;
  pointer-events: auto;
}
.tab_block.on {
  background: url(../img/a2.1_r_tab2.png) no-repeat center center;
  color: #011f4d;
}
.tab_block.off {
  background: url(../img/a2.1_r_tab1.png) no-repeat center center;
  color: #2692ef;
}

#tab_block1{
  top: 80px;
  right: 12px;
}
#tab_block2{
  top: 80px;
  right: 166px;
}

/* --- */
.ext_link {
  position: absolute;
  width: 23px;
  height: 23px;
  cursor: pointer;
  pointer-events: auto;
}
.ext_link.on {
  background: url(../img/ext_link1.png) no-repeat center center;
}
.ext_link.off {
  background: url(../img/ext_link1b.png) no-repeat center center;
}
#ext_link0{
  margin-bottom: -3px;
}
#ext_link1{
  top: 109px;
  right: 75px;
  display: none;
}
#ext_link2{
  top: 109px;
  right: 229px;
  display: none;
}


.btn_pos {
  left: 8px;
  bottom: 5px;
}

.btn_view_chart {
  position: absolute;
  width: 184px;
  height: 27px;
  cursor: pointer;
  text-align: center;
  line-height: 24px;
  font-size: 12px;
  color: #fff;
  pointer-events: auto;
  
  background: -moz-linear-gradient(top,  #0ddef5 0%, #0593c2 6%, #134491 91%, #002559 100%); /* FF3.6-15 */
  background: -webkit-linear-gradient(top,  #0ddef5 0%,#0593c2 6%,#134491 91%,#002559 100%); /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom,  #0ddef5 0%,#0593c2 6%,#134491 91%,#002559 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#0ddef5', endColorstr='#002559',GradientType=0 ); /* IE6-9 */

  border-radius: 5px;

  -moz-box-shadow:0px 1px 4px #000; -webkit-box-shadow:0px 1px 4px #000; box-shadow:0px 1px 4px #000;

}

.btn_view_chart .inside {
  position: absolute;
  width: 100%;
  height: 100%;
  padding-left: 8px;

  border-radius: 4px;

  border: 1px solid rgba(38,178,247,0.5);

}

.btn_view_chart:hover .inside {
  background: #1954b0;
}
.btn_view_chart:active .inside {
  background: #153970;
}

.btn_view_chart span {
  padding: 0 5px 0 14px;
  background: url(../img/a2.1.ico3.png) no-repeat 0 3px;
}


/* -- */
.btn_view_chart2 {
  position: absolute;
  width: 26px;
  height: 26px;
  cursor: pointer;
  text-align: center;
  line-height: 24px;
  font-size: 12px;
  color: #fff;
  pointer-events: auto;
  
  background: rgb(255,61,61); /* Old browsers */
  background: -moz-linear-gradient(top,  rgba(255,61,61,1) 0%, rgba(214,0,0,1) 100%); /* FF3.6-15 */
  background: -webkit-linear-gradient(top,  rgba(255,61,61,1) 0%,rgba(214,0,0,1) 100%); /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom,  rgba(255,61,61,1) 0%,rgba(214,0,0,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ff3d3d', endColorstr='#d60000',GradientType=0 ); /* IE6-9 */

  border-radius: 13px;

  -moz-box-shadow:0px 1px 3px #000f1e; -webkit-box-shadow:0px 1px 3px #000f1e; box-shadow:0px 1px 3px #000f1e;

}

.btn_view_chart2 .inside {
  position: absolute;
  width: 100%;
  height: 100%;

  border-radius: 13px;

  border: 1px solid rgba(38,178,247,0.5);

  background: url(../img/a2.1.ico3.png) no-repeat center center;

}

.btn_view_chart2:hover .inside {
  background-color: rgba(214,0,0,1);
}
.btn_view_chart2:active .inside {
  background-color: rgba(200,0,0,1);
}






.cont .blk{
  display: block;
  margin-right: 10px;
}
.cont .blk span{
  display: inline-block;
  margin-right: 5px;
}
.cont .blk .val{
  margin-right: 12px;
}
.cont .blk.con{
  margin-bottom: 10px;
}



/* --- */

.kpitable {
  width: 100%;
  font-size: 12px;
}

.kpitable td{
  border-bottom: 1px solid #354b72;
  height: 40px;
}
.kpitable .c1{
  padding-left: 8px;
  color: #47aafd;
  width: 78px;
  border-right: 1px solid #354b72;
}
.kpitable .c2{
  padding-left: 5px;
}

.kpitable .itm{
  display: inline-block;
  width: 52px;
}



/* --- */
.block_r1 {
  right: 15px;
  top: 135px;
  z-index: 92;
}
.block_r1 .cont{
  height: 160px;
  padding: 12px;
}


/* --- */
.block_r2 {
  right: 15px;
  top: 335px;
  z-index: 91;
}
.block_r2 .cont{
  height: 160px;
  padding: 12px;
}


/* --- */
.block_r3 {
  right: 15px;
  top: 535px;
  z-index: 90;
}
.block_r3 .cont{
  height: 160px;
  padding: 12px;
}









/* --- */
.block_r1t2 {
  right: 15px;
  top: 135px;
  z-index: 92;
}
.block_r1t2 .cont{
  height: 150px;
}


/* --- */
.block_r2t2 {
  right: 15px;
  top: 538px;
  z-index: 91;
}
.block_r2t2 .cont{
  height: 180px;
}
.block_r2t2 .tabs {
  position: relative;
  height: 30px;
  border-bottom: 1px solid #2a81d2;
  text-align: center;
}
.block_r2t2 .tabs .tab {
  position: absolute;
  bottom: 0px;
  width: 60px;
  height: 22px;
  line-height: 22px;
  font-size: 12px;
  background-color: #073b77;
  color: #2a81d2;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  pointer-events: auto;
  cursor: pointer;
}
.block_r2t2 .tabs .selected {
  background-color: #2a81d2;
  color: #fff;
}


/* --- */
.block_r3t2 {
  right: 15px;
  top: 322px;
  z-index: 90;
}
.block_r3t2 .cont{
  height: 180px;
}
.block_r3t2 .tabs {
  position: relative;
  height: 30px;
  border-bottom: 1px solid #2a81d2;
  text-align: center;
}
.block_r3t2 .tabs .tab {
  position: absolute;
  bottom: 0px;
  width: 60px;
  height: 22px;
  line-height: 22px;
  font-size: 12px;
  background-color: #073b77;
  color: #2a81d2;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  pointer-events: auto;
  cursor: pointer;
}
.block_r3t2 .tabs .selected {
  background-color: #2a81d2;
  color: #fff;
}


.block_r4t2 {
  right: 15px;
  top: 322px;
  z-index: 90;
}
.block_r4t2 .cont{
  height: 180px;
}
.block_r4t2 .tabs {
  position: relative;
  height: 30px;
  border-bottom: 1px solid #2a81d2;
  text-align: center;
}
.block_r4t2 .tabs .tab {
  position: absolute;
  bottom: 0px;
  width: 60px;
  height: 22px;
  line-height: 22px;
  font-size: 12px;
  background-color: #073b77;
  color: #2a81d2;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  pointer-events: auto;
  cursor: pointer;
}
.block_r4t2 .tabs .selected {
  background-color: #2a81d2;
  color: #fff;
}





/* --- */
#map_legend{
  position: absolute;
  left: 890px;
  top: 620px;
  width: 121px;
  height: 81px;
  background: url(../img/a2.1.legend.png) no-repeat 0 0;
}
#map_legend .tit{
  position: absolute;
  width: 100%;
  top: 2px;
  text-align: center;
}
#map_legend .itm{
  position: absolute;
  width: 100px;
  left: 24px;
}
#map_legend .l1{
  top: 26px;
}
#map_legend .l2{
  top: 44px;
}
#map_legend .l3{
  top: 62px;
}




/* --- */

#popover_map {
  position: absolute;
  z-index: 888;
  width: 481px;
  height: 308px;
  top:  -1000px;
  background: url(../img/a2.1.pop_map.png) no-repeat 0 0;
  -moz-transform-origin: 50% 50%;
  -wekkit-transform-origin: 50% 50%;
  -ms-transform-origin: 50% 50%;
  transform-origin: 50% 50%;
  opacity: 0;
}


#popover_map .title{
  position: absolute;
  width: 100%;
  height: 50px;
  top: 23px;
  text-align: center;
  color: #92ebf6;
}
#popover_map .close{
  position: absolute;
  width: 15px;
  height: 15px;
  top: 33px;
  right: 25px;
  background: url(../img/x.png) no-repeat 0 0;
  pointer-events: auto;
  cursor: pointer;
  opacity: 0.8;
}

#popover_map .blk {
  display: block;
}
#popover_map .row1{
  position: absolute;
  top: 82px;
  left: 38px;
}
#popover_map .row1 .c1{
  position: absolute;
  width: 240px;
  top: 0;
  left: 0;
}
#popover_map .row1 .c2{
  position: absolute;
  width: 240px;
  top: 0;
  left: 218px;
}
#popover_map .row2{
  position: absolute;
  top: 155px;
  left: 38px;
}
#popover_map .row2 .c1{
  position: absolute;
  width: 150px;
  top: 0;
  left: 0;
}
#popover_map .row2 .c2{
  position: absolute;
  width: 150px;
  top: 0;
  left: 150px;
}
#popover_map .row2 .c3{
  position: absolute;
  width: 150px;
  top: 0;
  left: 305px;
}
#popover_map .row3{
  position: absolute;
  top: 231px;
  left: 38px;
}
#popover_map .row3 .c1{
  position: absolute;
  width: 150px;
  top: 0;
  left: 0;
}
#popover_map .row3 .c2{
  position: absolute;
  width: 150px;
  top: 0;
  left: 150px;
}
#popover_map .row3 .c3{
  position: absolute;
  width: 150px;
  top: 0;
  left: 305px;
}




/* --- */
#popover_chart {
  position: absolute;
  z-index: 999;
  width: 700px;
  height: 438px;
  top: 170px;
  left: 340px;
  border: 1px solid #237fd5;
  background: #083476;
  display: none;
  pointer-events: auto;
}

#popover_chart .tit{
  position: absolute;
  width: 100%;
  height: 55px;
  background: #123e92;
  color: #7cc3ff;
  font-size: 24px;
  line-height: 50px;
  text-align: center;
  font-weight: bold;
}

#popover_chart .btnx{
  position: absolute;
  width: 30px;
  height: 30px;
  top: 0;
  right: 0;
  
  border-left:  1px solid #237fd5;
  border-bottom:  1px solid #237fd5;
  background: #083476 url(../img/pop_x.png) no-repeat center center;
  pointer-events: auto;
  cursor: pointer;
}
#popover_chart .legends{
  position: absolute;
  width: 100%;
  height: 20px;
  bottom: 44px;
  font-size: 14px;
  color: #91c9ff;
  text-align: center;
}
#popover_chart .legend{
  display: inline-block;
  margin:  0 10px;
}
#popover_chart .legend1{
  padding-left: 15px;
  background: #083476 url(../img/a2.1.legend1.png) no-repeat 0 center;
}
#popover_chart .legend1b{
  padding-left: 15px;
  background: #083476 url(../img/a2.1.legend1b.png) no-repeat 0 center;
}
#popover_chart .legend2{
  padding-left: 37px;
  background: #083476 url(../img/a2.1.legend2.png) no-repeat 0 center;
}

#popover_chart .note{
  position: absolute;
  width: 100%;
  height: 20px;
  bottom: 10px;
  font-size: 12px;
  color: #4f85bf;
  padding-left: 20px;
}

#chart_pop1 {
  position: absolute;
  top: 186px;
  left: 44px;
  width: 700px;
}
#chart_pop2 {
  position: absolute;
  top: 65px;
  left: 44px;
  width: 700px;
}

.short_dialog {
  position: absolute;
  z-index: 999;
  width: 510px;
  height: 410px;
  top: 10px;
  left: 310px;
  border: 1px solid #237fd5;
  background: #083476;
  display: none;
  pointer-events: auto;
}
.short_dialog .legends{
  position: absolute;
  width: 100%;
  height: 20px;
  bottom: 10px;
  font-size: 14px;
  color: #91c9ff;
  text-align: center;
}
.short_dialog .legend{
  display: inline-block;
  margin:  0 10px;
}
.short_dialog .legend1{
  padding-left: 15px;
  background: #083476 url(../img/a2.1.legend1.png) no-repeat 0 center;
}
.short_dialog .legend1b{
  padding-left: 15px;
  background: #083476 url(../img/a2.1.legend1b.png) no-repeat 0 center;
}
.short_dialog .legend2{
  padding-left: 37px;
  background: #083476 url(../img/a2.1.legend2.png) no-repeat 0 center;
}
.short_dialog .headtitle{
  /*position: absolute;*/
  width: 100%;
  height: 45px;
  background: #123e92;
  color: #7cc3ff;
  font-size: 22px;
  line-height: 45px;
  text-align: center;
  font-weight: bold;
}

.short_dialog .btnx{
  position: absolute;
  width: 30px;
  height: 30px;
  top: 0;
  right: 0;
  border-left:  1px solid #237fd5;
  border-bottom:  1px solid #237fd5;
  background: #083476 url(../img/pop_x.png) no-repeat center center;
  pointer-events: auto;
  cursor: pointer;
}
.short_dialog .chart_cover{
  width: 100%;
  height: 225px;
}
/* --- */
#popover_trend_chart {
  position: absolute;
  z-index: 999;
  width: 510px;
  height: 410px;
  top: 220px;
  left: 320px;
  border: 1px solid #237fd5;
  background: #083476;
  display: none;
  pointer-events: auto;
}

#popover_trend_chart .tit{
  width: 100%;
  height: 45px;
  background: #123e92;
  color: #7cc3ff;
  font-size: 22px;
  line-height: 45px;
  text-align: center;
  font-weight: bold;
}

#popover_trend_chart .btnx{
  position: absolute;
  width: 30px;
  height: 30px;
  top: 0;
  right: 0;
  border-left:  1px solid #237fd5;
  border-bottom:  1px solid #237fd5;
  background: #083476 url(../img/pop_x.png) no-repeat center center;
  pointer-events: auto;
  cursor: pointer;
}
#popover_trend_chart .legends{
  position: absolute;
  width: 100%;
  height: 20px;
  bottom: 10px;
  font-size: 14px;
  color: #91c9ff;
  text-align: center;
}
#popover_trend_chart .legend{
  display: inline-block;
  margin:  0 10px;
}
#popover_trend_chart .legend1{
  padding-left: 15px;
  background: #083476 url(../img/a2.1.legend1.png) no-repeat 0 center;
}
#popover_trend_chart .legend2{
  padding-left: 37px;
  background: #083476 url(../img/a2.1.legend2.png) no-repeat 0 center;
}

#chart_trend_pop1 {
  position: absolute;
  top: 126px;
  width: 510px;
}
#chart_trend_pop2 {
  position: absolute;
  width: 510px;
}

#selected_mapmarkpoint{
  position: absolute;
  width: 36px;
  height: 36px;
  background: url(../img/mapmarkpoint_L.svg) no-repeat center center;
  background-size: 36px 36px;
}


#user_guide{
  z-index: 8880;
  pointer-events: auto;
  cursor: pointer;
}

#user_guide .img{
  position: absolute;
  width: 100%;
  height: 100%;
  background: url(../img/user_guide.png) no-repeat center center;
  background-size: contain;
}


.block_cover{
  width: 330px;
  position: absolute;
  right: 15px;
  top: 135px;
  z-index: 90;
}
.block_cover .block_content{
  height: 605px;
  z-index: 90;
  width: 100%;
  overflow: hidden;
}
.block_cover .button_cover{
  width: 100%;
  text-align: center;
  position: relative;
}
.block_cover .button_cover .button{
  display: inline-block;
  width: 20px;
  height: 20px;
  cursor: pointer;
  z-index: 120;
  pointer-events: auto;
  position: absolute;
  top: -25px;
  right: 10px;
}
.block_cover .button_cover .button:hover{
  cursor: pointer;
}
.block_cover .button_cover .up{
  background: url("../img/rotateArrow.png") no-repeat center center;
  transform: rotate(180deg);
  background-size: 100%;
}
.block_cover .button_cover .down{
  background: url("../img/rotateArrow.png") no-repeat center center;
  background-size: 100%;
}
.block_cover .button_cover .stop{
  opacity: 0.3;
}
.block_cover .block_content{
  position: relative;
}
.block_cover div{
  pointer-events: auto !important;
}
.block_cover .scroll_cover{
  width: 330px;
  top: 0;
  position: absolute;
  left: 0px;
  overflow: hidden;
  padding-right: 20px;
  height: 600px;
}
.block_cover .scroll_cover .block-frame{
  display: block;
  /*float: left;*/
  width: 100%;
  position: initial;
  position:static;
  margin-bottom: 10px;
}

.remark{
  position: absolute;
  text-align: center;
  height: 30px;
  line-height: 30px;
  background: url("../img/reamrkback.png") no-repeat center;
  background-size: 100% 100%; 
  color: #47aafd;
  font-size: 12px;
  width: 40%;
  left: 30%;
  bottom: 20px;
}