showLoading();



var current_company_code;

// 可显示的历史 数量
var query_limit = 20;
// 日期类型
var date_type = 'M';



// -------------------------------------------------------

//                          KPI

// -------------------------------------------------------



var monthList = [];
var quarterList = [];

var selected_month;
var selected_quarter;

var kpiDataReady = false;
var fetchingKpiData = false;


var comp_risk_list;
var comp_ac_list = {};
var comp_ac_fleet_list = {};

var ac_month_data;
var ac_quarter_data;

var fleet_month_data;
var fleet_quarter_data;

var sliceId2iconId = {};
var sliceIdDataMap = {};


var riskLabel2iconId = {
    '冲偏出跑道': 0,
    '跑道上飞机受损': 1,
    '客舱关键设备受损/失效、人员伤亡': 2,
    '空中相撞': 3,
    '飞行失去控制': 4,
    '空中飞机受损': 5,
    '可控飞行撞地': 6,
    '跑道侵入': 7,

    '不稳定进近': 2,
};

var currentFleetType = '0';
var fleetTypes = {
    '0': '机长机队',
    '1': '操作员机队'
};

var selectedAc;


function getAllCompanyKpiData() {

    if (companylist.length == 0) {
        setTimeout(getAllCompanyKpiData, 0);
        return;
    }

    if (fetchingKpiData) return;


    // check url hash
    var hash = window.location.hash.substr(1);
    if (hash && companyCode2Name[hash] != undefined) {
        current_company_code = hash;
    } else {
        current_company_code = 'HU';
    }


    fetchingKpiData = true;


    var loadingInProgress = 0;



    loadingInProgress++;
    var param = {}

    $.ajax({
        type: 'post',
        url: "/bi/web/comprisklist",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            comp_risk_list = {};
            comp_ac_list = {};

            if (response.data) {
                for (var i = response.data.length - 1; i >= 0; i--) {
                    var dd = response.data[i];
                    if (dd.COMP_NAME == '海南航空') {
                        dd.COMP_NAME = companyCode2Name['HU'];
                    }
                    var code = companyName2Code[dd.COMP_NAME];
                    if (comp_risk_list[code] == undefined) {
                        comp_risk_list[code] = {};
                    }
                    if (comp_ac_list[code] == undefined) {
                        comp_ac_list[code] = [];
                    }
                    if (comp_risk_list[code][dd.CORE_RISK] == undefined) {
                        comp_risk_list[code][dd.CORE_RISK] = {};
                        comp_risk_list[code][dd.CORE_RISK]['AC_BIG_TYPE'] = [];
                        comp_risk_list[code][dd.CORE_RISK]['MONITOR_PROJECT'] = [];
                    }
                    if (comp_risk_list[code][dd.CORE_RISK]['AC_BIG_TYPE'].indexOf(dd.AC_BIG_TYPE) == -1) {
                        comp_risk_list[code][dd.CORE_RISK]['AC_BIG_TYPE'].push(dd.AC_BIG_TYPE);
                    }
                    if (comp_risk_list[code][dd.CORE_RISK]['MONITOR_PROJECT'].indexOf(dd.MONITOR_PROJECT) == -1) {
                        comp_risk_list[code][dd.CORE_RISK]['MONITOR_PROJECT'].push(dd.MONITOR_PROJECT);
                    }
                    if (comp_ac_list[code].indexOf(dd.AC_BIG_TYPE) == -1) {
                        comp_ac_list[code].push(dd.AC_BIG_TYPE);
                    }
                }
            }

            loadingInProgress--;
            checkDataReady();

        },
        error: function() {}
    });


    loadingInProgress++;
    var param = {
        'SOLR_CODE': 'FAC_COMP_CORE_RISK_KPI',
        'KPI_ID': '10128',
        'VALUE_TYPE': 'kpi_value_d',
        'DATE_TYPE': 'M',
        'OPTIMIZE': 1,
        'LIMIT': query_limit
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getriskkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            ac_month_data = {};
            monthList = [];

            if (response != undefined) {

                for (var i in response) {
                    var lst = response[i].data;
                    if (lst && lst.length > 0) {
                        for (var j = lst.length - 1; j >= 0; j--) {
                            var dd = lst[j];
                            if (ac_month_data[dd.dateid] == undefined) {
                                ac_month_data[dd.dateid] = {};
                            }
                            if (monthList.indexOf(dd.dateid) == -1) {
                                monthList.push(dd.dateid);
                            }
                            if (dd.comp_name_s == '海南航空') {
                                dd.comp_name_s = companyCode2Name['HU'];
                            }
                            var code = companyName2Code[dd.comp_name_s];
                            if (ac_month_data[dd.dateid][code] == undefined) {
                                ac_month_data[dd.dateid][code] = {};
                            }
                            if (ac_month_data[dd.dateid][code][dd.core_risk_s] == undefined) {
                                ac_month_data[dd.dateid][code][dd.core_risk_s] = {};
                            }
                            if (ac_month_data[dd.dateid][code][dd.core_risk_s][dd.monitor_project_s] == undefined) {
                                ac_month_data[dd.dateid][code][dd.core_risk_s][dd.monitor_project_s] = {};
                            }
                            if (ac_month_data[dd.dateid][code][dd.core_risk_s][dd.monitor_project_s][dd.ac_big_type_s] == undefined) {
                                ac_month_data[dd.dateid][code][dd.core_risk_s][dd.monitor_project_s][dd.ac_big_type_s] = dd.date;
                            }
                        }

                    }
                }

                loadingInProgress--;
                checkDataReady();
            }

            console.log('ac_month_data', ac_month_data)

        },
        error: function() {}
    });


    loadingInProgress++;
    var param = {
        'SOLR_CODE': 'FAC_COMP_CORE_RISK_KPI',
        'KPI_ID': '10128',
        'VALUE_TYPE': 'kpi_value_d',
        'DATE_TYPE': 'Q',
        'OPTIMIZE': 1,
        'LIMIT': query_limit
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getriskkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            ac_quarter_data = {};
            quarterList = [];

            if (response != undefined) {

                for (var i in response) {
                    var lst = response[i].data;
                    if (lst && lst.length > 0) {
                        for (var j = lst.length - 1; j >= 0; j--) {
                            var dd = lst[j];
                            if (ac_quarter_data[dd.dateid] == undefined) {
                                ac_quarter_data[dd.dateid] = {};
                            }
                            if (quarterList.indexOf(dd.dateid) == -1) {
                                quarterList.push(dd.dateid);
                            }
                            if (dd.comp_name_s == '海南航空') {
                                dd.comp_name_s = companyCode2Name['HU'];
                            }
                            var code = companyName2Code[dd.comp_name_s];
                            if (ac_quarter_data[dd.dateid][code] == undefined) {
                                ac_quarter_data[dd.dateid][code] = {};
                            }
                            if (ac_quarter_data[dd.dateid][code][dd.core_risk_s] == undefined) {
                                ac_quarter_data[dd.dateid][code][dd.core_risk_s] = {};
                            }
                            if (ac_quarter_data[dd.dateid][code][dd.core_risk_s][dd.monitor_project_s] == undefined) {
                                ac_quarter_data[dd.dateid][code][dd.core_risk_s][dd.monitor_project_s] = {};
                            }
                            if (ac_quarter_data[dd.dateid][code][dd.core_risk_s][dd.monitor_project_s][dd.ac_big_type_s] == undefined) {
                                ac_quarter_data[dd.dateid][code][dd.core_risk_s][dd.monitor_project_s][dd.ac_big_type_s] = dd.date;
                            }
                        }

                    }
                }

                loadingInProgress--;
                checkDataReady();
            }

            console.log('ac_quarter_data', ac_quarter_data)

        },
        error: function() {}
    });



    loadingInProgress++;
    var param = {
        'SOLR_CODE': 'FAC_COMP_FLEET_CORE_RISK_KPI',
        'KPI_ID': '10128',
        'VALUE_TYPE': 'kpi_value_d',
        'DATE_TYPE': 'M',
        'OPTIMIZE': 1,
        'LIMIT': query_limit
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getriskkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {
            // console.log(JSON.stringify(response))

            fleet_month_data = {};

            if (response != undefined) {

                for (var i in response) {
                    var lst = response[i].data;
                    if (lst && lst.length > 0) {
                        for (var j = lst.length - 1; j >= 0; j--) {
                            var dd = lst[j];
                            if (fleet_month_data[dd.dateid] == undefined) {
                                fleet_month_data[dd.dateid] = {};
                            }

                            if (dd.comp_name_s == '海南航空') {
                                dd.comp_name_s = companyCode2Name['HU'];
                            }
                            var code = companyName2Code[dd.comp_name_s];
                            if (fleet_month_data[dd.dateid][code] == undefined) {
                                fleet_month_data[dd.dateid][code] = {};
                            }
                            if (fleet_month_data[dd.dateid][code][dd.dim_type_s] == undefined) {
                                fleet_month_data[dd.dateid][code][dd.dim_type_s] = {};
                            }
                            if (fleet_month_data[dd.dateid][code][dd.dim_type_s][dd.ac_big_type_s] == undefined) {
                                fleet_month_data[dd.dateid][code][dd.dim_type_s][dd.ac_big_type_s] = {};
                            }
                            if (fleet_month_data[dd.dateid][code][dd.dim_type_s][dd.ac_big_type_s][dd.core_risk_s] == undefined) {
                                fleet_month_data[dd.dateid][code][dd.dim_type_s][dd.ac_big_type_s][dd.core_risk_s] = {};
                            }
                            if (fleet_month_data[dd.dateid][code][dd.dim_type_s][dd.ac_big_type_s][dd.core_risk_s][dd.monitor_project_s] == undefined) {
                                fleet_month_data[dd.dateid][code][dd.dim_type_s][dd.ac_big_type_s][dd.core_risk_s][dd.monitor_project_s] = {};
                            }
                            if (fleet_month_data[dd.dateid][code][dd.dim_type_s][dd.ac_big_type_s][dd.core_risk_s][dd.monitor_project_s][dd.dim_content_s] == undefined) {
                                fleet_month_data[dd.dateid][code][dd.dim_type_s][dd.ac_big_type_s][dd.core_risk_s][dd.monitor_project_s][dd.dim_content_s] = dd.date;
                            }
                            //
                            // ac 2 fleets
                            if (comp_ac_fleet_list[code] == undefined) {
                                comp_ac_fleet_list[code] = [];
                            }
                            if (comp_ac_fleet_list[code][dd.ac_big_type_s] == undefined) {
                                comp_ac_fleet_list[code][dd.ac_big_type_s] = [];
                            }
                            if (comp_ac_fleet_list[code][dd.ac_big_type_s].indexOf(dd.dim_content_s) == -1) {
                                comp_ac_fleet_list[code][dd.ac_big_type_s].push(dd.dim_content_s);
                            }
                        }

                    }
                }

                loadingInProgress--;
                checkDataReady();
            }

            console.log('fleet_month_data', fleet_month_data)


        },
        error: function() {}
    });


    loadingInProgress++;
    var param = {
        'SOLR_CODE': 'FAC_COMP_FLEET_CORE_RISK_KPI',
        'KPI_ID': '10128',
        'VALUE_TYPE': 'kpi_value_d',
        'DATE_TYPE': 'Q',
        'OPTIMIZE': 1,
        'LIMIT': query_limit
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getriskkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            fleet_quarter_data = {};

            if (response != undefined) {

                for (var i in response) {
                    var lst = response[i].data;
                    if (lst && lst.length > 0) {
                        for (var j = lst.length - 1; j >= 0; j--) {
                            var dd = lst[j];
                            if (fleet_quarter_data[dd.dateid] == undefined) {
                                fleet_quarter_data[dd.dateid] = {};
                            }

                            if (dd.comp_name_s == '海南航空') {
                                dd.comp_name_s = companyCode2Name['HU'];
                            }
                            var code = companyName2Code[dd.comp_name_s];
                            if (fleet_quarter_data[dd.dateid][code] == undefined) {
                                fleet_quarter_data[dd.dateid][code] = {};
                            }
                            if (fleet_quarter_data[dd.dateid][code][dd.dim_type_s] == undefined) {
                                fleet_quarter_data[dd.dateid][code][dd.dim_type_s] = {};
                            }
                            if (fleet_quarter_data[dd.dateid][code][dd.dim_type_s][dd.ac_big_type_s] == undefined) {
                                fleet_quarter_data[dd.dateid][code][dd.dim_type_s][dd.ac_big_type_s] = {};
                            }
                            if (fleet_quarter_data[dd.dateid][code][dd.dim_type_s][dd.ac_big_type_s][dd.core_risk_s] == undefined) {
                                fleet_quarter_data[dd.dateid][code][dd.dim_type_s][dd.ac_big_type_s][dd.core_risk_s] = {};
                            }
                            if (fleet_quarter_data[dd.dateid][code][dd.dim_type_s][dd.ac_big_type_s][dd.core_risk_s][dd.monitor_project_s] == undefined) {
                                fleet_quarter_data[dd.dateid][code][dd.dim_type_s][dd.ac_big_type_s][dd.core_risk_s][dd.monitor_project_s] = {};
                            }
                            if (fleet_quarter_data[dd.dateid][code][dd.dim_type_s][dd.ac_big_type_s][dd.core_risk_s][dd.monitor_project_s][dd.dim_content_s] == undefined) {
                                fleet_quarter_data[dd.dateid][code][dd.dim_type_s][dd.ac_big_type_s][dd.core_risk_s][dd.monitor_project_s][dd.dim_content_s] = dd.date;
                            }
                            //
                            // ac 2 fleets
                            if (comp_ac_fleet_list[code] == undefined) {
                                comp_ac_fleet_list[code] = [];
                            }
                            if (comp_ac_fleet_list[code][dd.ac_big_type_s] == undefined) {
                                comp_ac_fleet_list[code][dd.ac_big_type_s] = [];
                            }
                            if (comp_ac_fleet_list[code][dd.ac_big_type_s].indexOf(dd.dim_content_s) == -1) {
                                comp_ac_fleet_list[code][dd.ac_big_type_s].push(dd.dim_content_s);
                            }
                        }

                    }
                }

                loadingInProgress--;
                checkDataReady();
            }

            console.log('fleet_quarter_data', fleet_quarter_data)


        },
        error: function() {}
    });


    function checkDataReady() {
        if (loadingInProgress == 0 && !kpiDataReady) {
            kpiDataReady = true;


            ////// M
            monthList.sort();

            var date = new Date();
            var month = date.getMonth() + 1;
            var day = date.getDate();
            if (month < 10) {
                month = '0' + month;
            }
            var nowmonth = date.getFullYear() + '' + month;

            var cblist = [];
            var len = monthList.length;
            for (var i = 0; i < len; i++) {
                var date = monthList[i];
                if (date <= nowmonth) {
                    var label = date.substr(0, 4) + '年' + Number(date.substr(4, 2)) + '月';
                    cblist.unshift({
                        'label': label,
                        'data': date
                    });
                }

            }

            // cblist = cblist.slice(0,6);

            if (cblist.length > 0) {
                createComboBox('main_cb_month', cblist, 104, 240, dateChanged, 1);
            }


            $('#main_cb_month .combobox_label').on('click', function(event) {
                event.preventDefault();

                if (date_type == 'M') {
                    return;
                }

                $('#main_cb_quarter').addClass('combotab');
                $('#main_cb_quarter').removeClass('combotab_selected');
                $('#main_cb_month').addClass('combotab_selected');
                $('#main_cb_month').removeClass('combotab');


                date_type = 'M';
                dateChanged();
            });


            // 显示 month 日期范围
            $('#main_cb_month .combobox_label').on('mouseover', function(event) {
                event.preventDefault();
                var month = $('#main_cb_month').attr('data');
                var curmonth = moment().format("YYYYMM");
                var numofdays = moment(month, "YYYYMM").daysInMonth(); // 获取一个月有几天
                var days = numofdays;
                if (days < 10) {
                    days = '0' + days;
                }
                if (curmonth == month) {
                    days = moment().format("DD");
                }
                $('#week_date_range').text(month + '01' + '~' + month + days);
                $('#week_date_range').fadeIn();
            });

            // 隐藏 month 日期范围
            $('#main_cb_month .combobox_label').on('mouseout', function(event) {
                event.preventDefault();
                $('#week_date_range').fadeOut();
            });
            $('#main_cb_month .combobox_label').on('click', function(event) {
                event.preventDefault();
                $('#week_date_range').fadeOut();
            });



            /////// 季度
            quarterList.sort();

            var cblist = [];
            var len = quarterList.length;
            for (var i = 0; i < len; i++) {
                var date = quarterList[i];
                if (date) {
                    var label = date.substr(0, 4) + '年' + Number(date.substr(4, 1)) + '季度';
                    cblist.unshift({
                        'label': label,
                        'data': date
                    });
                }
            }
            cblist = cblist.slice(0, 6);

            if (cblist.length > 0) {
                createComboBox('main_cb_quarter', cblist, 111, 240, dateChanged, 0);
            }


            $('#main_cb_quarter .combobox_label').on('click', function(event) {
                event.preventDefault();

                if (date_type == 'Q') {
                    return;
                }

                $('#main_cb_quarter').addClass('combotab_selected');
                $('#main_cb_quarter').removeClass('combotab');
                $('#main_cb_month').addClass('combotab');
                $('#main_cb_month').removeClass('combotab_selected');

                date_type = 'Q';
                dateChanged();

            });



            // 删除没有数据的公司
            $('#companylist .itm').each(function() {
                var code = $(this).attr('code');
                if (comp_risk_list[code] == undefined) {
                    $(this).remove();
                }
                if (code == 'HU') {
                    $(this).click();
                }
            });


            //updateAllKpi();

            hideLoading();

        }
    }



}



var currentSliceId = 0;
var selectedSliceId = 0;
var translated = false;
var totalSlices;

function drawDartboard() {
    if (!kpiDataReady) {
        return;
    }

    var slices = 0;
    var corerisks = comp_risk_list[current_company_code];
    for (var riskname in corerisks) {
        slices++;
    }

    totalSlices = slices;
    $('#popwrap').html('');

    var canvas = document.getElementById('dartboard');
    var ctx = canvas.getContext('2d');

    const x = canvas.width / 2;
    const y = canvas.height / 2;


    if (!translated) {
        ctx.translate(x, y);
        translated = true;
    }


    const canvasX = canvas.getBoundingClientRect().left + document.documentElement.scrollLeft + x;
    const canvasY = canvas.getBoundingClientRect().top + document.documentElement.scrollTop + y;

    const PI = Math.PI;
    const counterClockwise = false;

    const maxRadius = 305; //最大半径
    const minRadius = 144;

    const sliceAngel = 2 * PI / totalSlices; //最小角度 

    var offset = 0;
    if (totalSlices <= 8) {
        offset = 1;
    } else {
        offset = 2;
    }

    function drawAll(sliceId) {

        ctx.clearRect(-x, -y, canvas.width, canvas.height);

        // draw back
        var radius = 224;
        ctx.beginPath();
        ctx.arc(0, 0, radius, 0, PI * 2, counterClockwise);
        ctx.lineWidth = 160;
        // add linear gradient
        var color = ctx.createLinearGradient(-x + 200, -y, x - 200, y);
        color.addColorStop(0, '#812e5d');
        color.addColorStop(0.3, '#62374d');
        color.addColorStop(0.6, '#1a65e0');
        color.addColorStop(1, '#00d8ff');
        ctx.strokeStyle = color;
        ctx.stroke();
        ctx.closePath();


        // draw back
        var radius = 220;
        ctx.beginPath();
        ctx.arc(0, 0, radius, 0, PI * 2, counterClockwise);
        ctx.lineWidth = 154;
        ctx.strokeStyle = 'rgba(0,0,0,0.1)';
        ctx.stroke();
        ctx.closePath();


        // draw highlight
        if (sliceId != undefined) {
            let startMouseAngel = (sliceId - 1) * sliceAngel - (sliceAngel - (PI / 2 - sliceAngel));
            let endMouseAngel = sliceId * sliceAngel - (sliceAngel - (PI / 2 - sliceAngel));

            var radius = 255;
            ctx.beginPath();
            ctx.arc(0, 0, radius, startMouseAngel, endMouseAngel, counterClockwise);
            ctx.lineWidth = 220;

            var color = ctx.createRadialGradient(0, 0, 180, 0, 0, 330);
            color.addColorStop(0.0, '#049dfa');
            color.addColorStop(0.4, '#0782f6');
            color.addColorStop(0.9, '#072769');
            color.addColorStop(1.0, 'rgba(7,39,105,0.0)');
            ctx.strokeStyle = color;
            ctx.stroke();
            ctx.closePath();
        }


        // draw inner circle
        var radius = 153;
        ctx.beginPath();
        ctx.arc(0, 0, radius, 0, PI * 2, counterClockwise);
        ctx.lineWidth = 18;
        ctx.strokeStyle = 'rgba(0,0,0,0.1)';
        ctx.stroke();
        ctx.closePath();


        // draw lines
        ctx.rotate(PI / 2);
        for (let i = 0; i < totalSlices; i++) {
            ctx.beginPath();
            ctx.moveTo(minRadius, 0);
            ctx.lineWidth = 4;
            ctx.lineTo(maxRadius, 0);
            ctx.strokeStyle = '#0b47cb';
            ctx.stroke();
            ctx.rotate(2 * PI / totalSlices);
            ctx.closePath();
        }
        ctx.rotate(-PI / 2);

        //ctx.restore();
    }


    //监听事件画图
    canvas.removeEventListener('mousemove', window.onCanvasMousemove);
    canvas.addEventListener('mousemove', onCanvasMousemove);

    canvas.removeEventListener('mousedown', window.onCanvasMousedown);
    canvas.addEventListener('mousedown', onCanvasMousedown);

    function onCanvasMousemove(e) {
        let mouseX = e.pageX / pageZoomScale;
        let mouseY = e.pageY / pageZoomScale;

        let mouseRadius = Math.sqrt(Math.pow(mouseX - canvasX, 2) + Math.pow(mouseY - canvasY, 2));

        if (mouseRadius <= maxRadius && mouseRadius >= minRadius) {
            //角度
            let mouseAngel;
            if (mouseX - canvasX < 0) {
                mouseAngel = Math.atan((mouseY - canvasY) / (mouseX - canvasX)) + PI + (sliceAngel - (PI / 2 - sliceAngel));
            } else {
                mouseAngel = Math.atan((mouseY - canvasY) / (mouseX - canvasX)) + (sliceAngel - (PI / 2 - sliceAngel));
            }
            var sliceid = Math.ceil(mouseAngel / sliceAngel);
            if (sliceid == -1) {
                sliceid = totalSlices - 1;
            } else if (sliceid == -2) {
                sliceid = totalSlices - 2;
            }

            if (currentSliceId != sliceid) {
                currentSliceId = sliceid;
                selectedSliceId = sliceid + offset;
                if (selectedSliceId >= totalSlices) {
                    selectedSliceId = selectedSliceId - totalSlices;
                }
                //console.log('selectedSliceId', selectedSliceId, 'currentSliceId', currentSliceId);

                selectSlice();


            }
        }
    }

    function onCanvasMousedown(e) {
        let mouseX = e.pageX / pageZoomScale;
        let mouseY = e.pageY / pageZoomScale;

        let mouseRadius = Math.sqrt(Math.pow(mouseX - canvasX, 2) + Math.pow(mouseY - canvasY, 2));

        if (mouseRadius <= maxRadius && mouseRadius >= minRadius) {
            //角度
            let mouseAngel;
            if (mouseX - canvasX < 0) {
                mouseAngel = Math.atan((mouseY - canvasY) / (mouseX - canvasX)) + PI + (sliceAngel - (PI / 2 - sliceAngel));
            } else {
                mouseAngel = Math.atan((mouseY - canvasY) / (mouseX - canvasX)) + (sliceAngel - (PI / 2 - sliceAngel));
            }
            var sliceid = Math.ceil(mouseAngel / sliceAngel);
            if (sliceid == -1) {
                sliceid = totalSlices - 1;
            } else if (sliceid == -2) {
                sliceid = totalSlices - 2;
            }

            var ssid = sliceid + offset;
            if (ssid >= totalSlices) {
                ssid = ssid - totalSlices;
            }
            if (selectedSliceId == ssid) {
                showBigPop(selectedSliceId);
                // common.js
                stopAutoSwitchCompany();
            }
        }
    }

    window.onCanvasMousemove = onCanvasMousemove;


    function selectSlice() {

        currentSliceId = selectedSliceId - offset;
        if (selectedSliceId >= totalSlices) {
            selectedSliceId = selectedSliceId - totalSlices;
        }

        drawAll(currentSliceId);
        loadTargetIcons(totalSlices);
        //showBigPop(selectedSliceId);
        //hideBigPop();

        var iconid = sliceId2iconId[selectedSliceId];

        $('#darticon_' + selectedSliceId).css('background-image', 'url(img/a4.1_qar_' + iconid + 's.png)');

        $('.smallpop').removeClass('selected');
        $('#smallpop_' + selectedSliceId).addClass('selected');
    }

    window.selectSlice = selectSlice;


    function loadTargetIcons(slices) {
        var html = '';

        var radius = 230;

        var corerisks = comp_risk_list[current_company_code];

        var fleetTypeName = fleetTypes[currentFleetType];

        var i = 0;
        for (var riskname in corerisks) {

            //console.log('riskname-----', riskname);
            var corerisk_data = corerisks[riskname];
            var aclist = corerisk_data.AC_BIG_TYPE;
            var projects = corerisk_data.MONITOR_PROJECT;

            var sliceId = i - offset;

            let startAngel = (sliceId - 1) * sliceAngel - (sliceAngel - (PI / 2 - sliceAngel));
            let endAngel = sliceId * sliceAngel - (sliceAngel - (PI / 2 - sliceAngel));
            let midAngle = (startAngel + endAngel) / 2;

            let px = Math.cos(midAngle) * radius + x;
            let py = Math.sin(midAngle) * radius + y;

            let left = px - 75;
            let top = py - 75;

            var icoid = riskLabel2iconId[riskname];
            sliceId2iconId[i] = icoid;

            html += '<div class="img" id="darticon_' + i + '" data-icon="' + icoid + '" style="background-image: url(img/a4.1_qar_' + icoid + '.png); left:' + left + 'px; top:' + top + 'px; ">';
            html += '<div class="lb">';
            html += riskname;
            html += '</div>';
            html += '</div>';



            var rd = 330
            let popx = Math.cos(midAngle) * rd + x;
            let popy = Math.sin(midAngle) * rd + y;

            if ($('#smallpop_' + i).length == 0) {

                var pop;


                var date = getCurrentDate();

                if ($('#maintab1').hasClass('selected')) {
                    var list;

                    if (date_type == 'M') {
                        list = ac_month_data;
                    } else {
                        list = ac_quarter_data;
                    }

                    var datalist = [];
                    console.log('date', date);
                    if (date && list[date] && list[date][current_company_code]) {
                        var riskdata = list[date][current_company_code][riskname];
                        for (var p in projects) {
                            var pname = projects[p];
                            var ddd = {};
                            ddd.name = pname;
                            ddd.ac = {};
                            for (var aa in aclist) {
                                if (riskdata && riskdata[pname]) {
                                    var accode = aclist[aa];
                                    var val = Number(riskdata[pname][accode]);
                                    //console.log('val', val, current_company_code, riskname, pname, accode);
                                    if (val >= 0) {
                                        ddd.ac[accode] = val;
                                    } else {
                                        ddd.ac[accode] = 0;
                                    }
                                } else {
                                    ddd.ac[accode] = 0;
                                }
                            }
                            datalist.push(ddd);
                        }

                        sliceIdDataMap[i] = {};
                        sliceIdDataMap[i].name = riskname;
                        sliceIdDataMap[i].ac = aclist;
                        sliceIdDataMap[i].data = datalist;

                        pop = $(createAcSmallPop(i, riskname, aclist, datalist));
                    }


                } else {

                    var list;

                    if (date_type == 'M') {
                        list = fleet_month_data;
                    } else {
                        list = fleet_quarter_data;
                    }

                    var datalist = [];

                    var fleetlist = comp_ac_fleet_list[current_company_code][selectedAc];

                    for (var p in projects) {
                        var pname = projects[p];
                        var ddd = {};
                        ddd.name = pname;
                        ddd.color = 0;
                        ddd.fleet = [];

                        if (typeof fleetlist != "undefined")
                            for (var f = fleetlist.length - 1; f >= 0; f--) {
                                var fleetname = fleetlist[f];
                                var val = 0;
                                if (list[date][current_company_code][fleetTypeName][selectedAc]) {
                                    var riskdata = list[date][current_company_code][fleetTypeName][selectedAc][riskname];
                                    if (riskdata && riskdata[pname] && riskdata[pname][fleetname]) {
                                        val = Number(riskdata[pname][fleetname]);
                                        if (ddd.color < val) {
                                            ddd.color = val;
                                        }
                                    }
                                }
                                ddd.fleet.push({
                                    name: fleetname,
                                    color: val
                                });
                            }
                        datalist.push(ddd);
                    }


                    sliceIdDataMap[i] = {};
                    sliceIdDataMap[i].name = riskname;
                    sliceIdDataMap[i].data = datalist;

                    pop = $(createFleetSmallPop(i, riskname, datalist));

                }

                if (pop) {

                    $('#popwrap').append(pop);

                    var ttt = $('.block_o').position().top + popy - pop.height() / 2;
                    if (ttt < 200) {
                        ttt += 80;
                    } else if (ttt < 400) {
                        ttt += 70;
                    } else if (ttt < 500) {
                        ttt -= 30;
                    } else if (ttt > 500) {
                        ttt -= 50;
                    }
                    if (slices % 2 == 1 && i == 0) {
                        ttt += 20;
                    }
                    pop.css('top', ttt + 'px');

                    var lll;
                    if (i < slices / 2) {
                        lll = $('.block_o').position().left + popx;
                        if (ttt > 500) {
                            lll += 50;
                        } else if (ttt < 200) {
                            lll += 50;
                        }
                    } else {
                        lll = $('.block_o').position().left + popx - pop.width();
                        if (ttt > 500) {
                            lll -= 50;
                        } else if (ttt < 200) {
                            lll -= 50;
                        }
                    }
                    if (slices % 2 == 1 && i == 0) {
                        lll += 220;
                    }

                    pop.css('left', lll + 'px');
                }



            }

            i++;

        }

        $('#imgring').html(html);

        $('#popwrap .smallpop').off('click');
        $('#popwrap .smallpop').on('click', function(evt) {
            showBigPop(selectedSliceId);
            // common.js
            stopAutoSwitchCompany();
        });


    }

    function hideBigPop() {
        $('.smallpop').show();
        $('#bigpop').remove();
    }


    function showBigPop(id) {
        hideBigPop();

        var pop2;

        if ($('#maintab1').hasClass('selected')) {

            var riskname = sliceIdDataMap[id].name;
            var aclist = sliceIdDataMap[id].ac;
            var datalist = sliceIdDataMap[id].data;

            pop2 = $(createAcBigPop(id, riskname, aclist, datalist));

        } else {

            var riskname = sliceIdDataMap[id].name;
            var datalist = sliceIdDataMap[id].data;

            pop2 = $(createFleetBigPop(id, riskname, datalist));

        }


        $('#popwrap').append(pop2);

        $('#bigpop .close').off('click');
        $('#bigpop .close').on('click', function(evt) {
            evt.stopPropagation();
            hideBigPop();
        });

        $("#popwrap .scrollpane").niceScroll({
            cursorcolor: "rgba(255,255,255,0.2)",
            cursorborder: "rgba(0,0,0,0)"
        });

        var tt = $('#smallpop_' + id).position().top - 10;
        var ll = $('#smallpop_' + id).position().left - 10;

        if (ll < 600) {
            ll = 80;
            pop2.css('left', ll + 'px');
        } else if (ll > 600) {
            ll = 80;
            pop2.css('right', ll + 'px');
        }


        if (tt + pop2.height() > $('.page-wrapper').height()) {
            tt = $('.page-wrapper').height() - pop2.height() - 10;
        }
        pop2.css('top', tt + 'px');

        $('#smallpop_' + id).hide();

    }


    drawAll();
    loadTargetIcons(totalSlices);
    selectSlice();


    $('.smallpop').off('mouseover');
    $('.smallpop').on('mouseover', function(evt) {
        evt.stopPropagation();
        selectedSliceId = $(this).attr('data-id');
        currentSliceId = selectedSliceId - offset;
        if (selectedSliceId >= totalSlices) {
            selectedSliceId = selectedSliceId - totalSlices;
        }
        //console.log('selectedSliceId', selectedSliceId, 'currentSliceId', currentSliceId);

        drawAll(currentSliceId);
        loadTargetIcons(totalSlices);
        //showBigPop(selectedSliceId);

        $('#darticon_' + selectedSliceId).css('background-image', 'url(img/a4.1_qar_' + selectedSliceId + 's.png)');

        $('.smallpop').removeClass('selected');

    });

}



// 机型小弹窗
function createAcSmallPop(id, title, aclist, datalist) {
    var html = '';
    html += '<div id="smallpop_' + id + '" class="smallpop pop1" data-id="' + id + '">';
    html += '<div class="bg"></div><div class="c0"></div><div class="c1"></div><div class="c2"></div><div class="c3"></div>';

    html += '<table>';
    html += '<tr class="head">';

    html += '<td class="tt bigtt">';
    html += title;
    html += '</td>';

    for (var i = 0; i < aclist.length; i++) {
        html += '<td>';
        html += aclist[i];
        html += '</td>';
    }

    html += '</tr>';

    for (var k = 0; k < datalist.length; k++) {
        var dd = datalist[k];
        html += '<tr class="bd">';

        html += '<td class="tt">';
        html += dd.name;
        html += '</td>';

        for (var i = 0; i < aclist.length; i++) {
            var ac = aclist[i];
            html += '<td>';
            if (dd.ac[ac]) {
                html += '<img src="img/a4.1_dot' + dd.ac[ac] + '.png">';
            } else {
                html += '<img src="img/a4.1_dot0.png">';
            }
            html += '</td>';
        }

        html += '</tr>';
    }

    html += '</table>';

    html += '</div>';


    return html;
}



// 机型大弹窗
function createAcBigPop(id, title, aclist, datalist) {
    var html = '';
    html += '<div id="bigpop" class="bigpop">';
    html += '<div class="bg">';

    html += '<div class="titlebg"></div>';
    html += '<div class="tablebg"></div>';

    html += '<div class="title">';
    html += title;
    html += '</div>';

    html += '</div>'; // class bg

    html += '<div class="c0"></div><div class="c1"></div><div class="c2"></div><div class="c3"></div>';


    html += '<table>';
    html += '<tr class="head">';

    html += '<td class="tt">';
    html += '</td>';

    for (var i = 0; i < aclist.length; i++) {
        html += '<td>';
        html += aclist[i];
        html += '</td>';
    }

    html += '</tr>';

    for (var k = 0; k < datalist.length; k++) {
        var dd = datalist[k];
        html += '<tr class="bd">';

        html += '<td class="tt">';
        html += dd.name;
        html += '</td>';

        for (var i = 0; i < aclist.length; i++) {
            var ac = aclist[i];
            html += '<td>';
            if (dd.ac[ac]) {
                html += '<img src="img/a4.1_dot' + dd.ac[ac] + '.png">';
            } else {
                html += '<img src="img/a4.1_dot0.png">';
            }
            html += '</td>';
        }

        html += '</tr>';
    }

    html += '</table>';


    html += '<div class="close"></div>';


    html += '</div>';


    return html;
}


// 机队小弹窗
function createFleetSmallPop(id, title, datalist) {
    var html = '';
    html += '<div id="smallpop_' + id + '" class="smallpop pop2" data-id="' + id + '">';
    html += '<div class="bg"></div><div class="c0"></div><div class="c1"></div><div class="c2"></div><div class="c3"></div>';

    html += '<table>';
    html += '<tr class="head">';

    html += '<td class="tt bigtt" style="text-align: left;">';
    html += title;
    html += '</td>';

    html += '</tr>';
    html += '<tr class="bd">';
    html += '<td class="tt" style="text-align: left;">';

    for (var k = 0; k < datalist.length; k++) {
        var dd = datalist[k];
        var color = dd.color;
        html += '<span class="color' + color + '">' + dd.name + '</span>';
    }

    html += '</td>';
    html += '</tr>';

    html += '</table>';

    html += '</div>';


    return html;
}

// 机队大弹窗
function createFleetBigPop(id, title, datalist) {
    var html = '';
    html += '<div id="bigpop" class="bigpop">';
    html += '<div class="bg">';

    html += '<div class="titlebg"></div>';

    html += '<div class="title">';
    html += title;
    html += '</div>';

    html += '</div>'; // class bg

    html += '<div class="c0"></div><div class="c1"></div><div class="c2"></div><div class="c3"></div>';


    html += '<div class="scrollpane">';

    for (var k = 0; k < datalist.length; k++) {
        var dd = datalist[k];
        var color = dd.color;
        var fleet = dd.fleet;

        var cc = '';
        var arrow = '';
        if (color == 2) {
            cc = 'yellow';
            arrow = '<span class="arr_yellow"></span>'
        } else if (color == 3) {
            cc = 'red';
            arrow = '<span class="arr_red"></span>'
        }

        html += '<div class="block">';

        html += '<div class="tit ' + cc + '">';
        html += dd.name;
        html += arrow;
        html += '</div>';

        html += '<div class="fleet">';

        for (var i = 0; i < fleet.length; i++) {
            var ft = fleet[i];
            html += '<div class="itm color' + ft.color + '">';
            html += ft.name;
            html += '</div>';
        }

        html += '</div>'; //fleet
        html += '</div>'; //block
    }

    html += '</div>'; //scrollpane
    html += '<div class="close"></div>';
    html += '</div>'; //bigpop


    return html;

}



function createCenterBallAclist(aclist) {

    var html = '';
    var len = aclist.length;

    var margin = '';
    if (len > 4) {
        margin = 'margin-top:-5px;';
    }

    for (var i = 0; i < len; i++) {
        var ac = aclist[i];

        var ss = 1 - i * 0.07;

        var scale = '';
        scale += 'transform: scale(' + ss + ');';
        scale += '-ms-transform: scale(' + ss + ');';
        scale += '-moz-transform: scale(' + ss + ');';
        scale += '-webkit-transform: scale(' + ss + ');';
        scale += '-o-transform: scale(' + ss + ');';



        if (i == 0) {
            selectedAc = ac;
            html += '<div class="itm selected" data-ac="' + ac + '" style="' + scale + margin + '"><div class="lb">' + ac + '</div></div>';
        } else {
            html += '<div class="itm" data-ac="' + ac + '" style="' + scale + margin + '"><div class="lb">' + ac + '</div></div>';
        }

    }

    $('#centerball').html(html);

    $('#centerball .itm').off('click');
    $('#centerball .itm').on('click', function(evt) {
        var ac = $(this).attr('data-ac');
        selectedAc = ac;
        $('#centerball .itm').removeClass('selected');
        $(this).addClass('selected');

        drawDartboard();

    });
}

$('.subtab').on('click', function(evt) {
    var type = $(this).attr('data-type');
    currentFleetType = type;
    $('.subtab').removeClass('selected');
    $(this).addClass('selected');

    drawDartboard();

});



function getCompCodeById(id) {
    var len = companylist.length;
    for (var i = 0; i < len; i++) {
        
        var dat = companylist[i];
        if (dat.id == id) {
            return dat.code;
        }
    }
    return '';
}

function getCompIdByCode(code) {
    var len = companylist.length;
    for (var i = 0; i < len; i++) {
        
        var dat = companylist[i];
        if (dat.code == code) {
            return dat.id;
        }
    }
    return '';
}


function dateChanged() {
    updateAllKpi();
}


function updateAllKpi(data, label) {
    if (!kpiDataReady) {
        return;
    }

    createCenterBallAclist(comp_ac_list[current_company_code]);

    drawDartboard();

    setTitleDate();
    setExtLink();

    // 自动循环切换
    clearTimeout(itv_autoSwitchDartboard);
    itv_autoSwitchDartboard = setTimeout(autoSwitchDartboard, 10000);


    // 自动循环切换两个TAB
    clearTimeout(itv_autoSwitchTab);
    itv_autoSwitchTab = setTimeout(autoSwitchTab, 80000);


    startLoadAudio();
}


function startLoadAudio() {

    stopAudio();

    // 语音播报

    /* 
    {COMP} {DATE} 机型 核心风险总览。
    */
    var tpl = '';

    //--
    var compname = companyCode2Name[current_company_code];
    tpl += compname + ' ';

    //--
    var date = getCurrentDate();
    if (date) {
        var datelabel = '';
        if (date_type == 'Q') {
            datelabel = date.substr(0, 4) + '年' + Number(date.substr(4, 1)) + '季度';
        } else if (date_type == 'M') {
            datelabel = date.substr(0, 4) + '年' + Number(date.substr(4, 2)) + '月';
        }
        tpl += datelabel + ' ';

        tpl += '机型/机队 核心风险总览';

        tpl += '。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。';
        tpl += '。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。';

        //--

        text2audio(tpl, true, playFirstDartboardAudio);
    }

}


function playFirstDartboardAudio() {

    selectedSliceId = 0;
    window.selectSlice();

    var tpl = getDartboardAudioTpl();
    var tplarr = tpl.split('@@@');

    if ($.trim(tpl) == '') {
        playNextDartboardAudio();
        return;
    }

    for (var i = 0; i < tplarr.length; i++) {
        console.log('tpl', tplarr[i]);
        console.log('tpl.length', tplarr[i].length);
        if (i < tplarr.length - 1) {
            text2audio(tplarr[i], false);
        } else {
            text2audio(tplarr[i], false, playNextDartboardAudio);
        }
    }
}

function playNextDartboardAudio() {

    if (!autoSwitch) {
        return;
    }

    if (selectedSliceId == totalSlices - 1) {

        if (currentTabIndex == 0) {
            switchToTab2();
        } else {
            switchToTab1();
        }

        playFirstDartboardAudio();

        return;
    }

    switchDartboard();

    var tpl = getDartboardAudioTpl();
    var tplarr = tpl.split('@@@');

    if ($.trim(tpl) == '') {
        playNextDartboardAudio();
        return;
    }

    for (var i = 0; i < tplarr.length; i++) {
        if (i < tplarr.length - 1) {
            text2audio(tplarr[i], false);
        } else {
            text2audio(tplarr[i], false, playNextDartboardAudio);
        }
    }

}

function getDartboardAudioTpl() {
    var tpl = '';

    var date = getCurrentDate();

    var corename = sliceIdDataMap[selectedSliceId].name;

    var list;
    if (currentTabIndex == 0) {

        // AC

        if (date_type == 'M') {
            list = ac_month_data;
        } else {
            list = ac_quarter_data;
        }
        list = list[date][current_company_code];
        var audiotpl = '';

        var arrtpl_co = [];

        for (var core in list) {
            if (core == corename) {
                var monitors = list[core];

                var arrtpl_mo = [];

                for (var mo in monitors) {
                    var aclst = monitors[mo];

                    var arrtpl_ac = [];

                    for (var ac in aclst) {
                        var val = aclst[ac];
                        if (Number(val) == 2) {
                            arrtpl_ac.unshift(ac.split('').join(' ') + ' 黄色预警');
                        } else if (Number(val) == 3) {
                            arrtpl_ac.unshift(ac.split('').join(' ') + ' 红色预警');
                        }
                    }

                    if (arrtpl_ac.length > 0) {
                        arrtpl_mo.unshift(mo + '：' + arrtpl_ac.join('，'));
                    }
                }

                if (arrtpl_mo.length > 0) {
                    arrtpl_co.unshift(core + '：' + arrtpl_mo.join('。'))
                }
            }

        }

        if (arrtpl_co.length > 0) {
            tpl = arrtpl_co.join('。')
        }

    } else {

        // FLEET

        var fleetTypeName = fleetTypes[currentFleetType];


        if (date_type == 'M') {
            list = fleet_month_data;
        } else {
            list = fleet_quarter_data;
        }
        list = list[date][current_company_code][fleetTypeName][selectedAc];
        var audiotpl = '';

        var arrtpl_co = [];

        for (var core in list) {
            if (core == corename) {
                var monitors = list[core];

                var arrtpl_mo = [];

                for (var mo in monitors) {
                    var ftlst = monitors[mo];

                    var arrtpl_ft = [];

                    for (var ft in ftlst) {
                        var val = ftlst[ft];
                        if (Number(val) == 2) {
                            arrtpl_ft.unshift(ft + ' 黄色预警');
                        } else if (Number(val) == 3) {
                            arrtpl_ft.unshift(ft + ' 红色预警');
                        }
                    }

                    if (arrtpl_ft.length > 0) {
                        arrtpl_mo.unshift(mo + '：' + arrtpl_ft.join('，'));
                    }
                }

                if (arrtpl_mo.length > 0) {
                    arrtpl_co.unshift(core + '：' + arrtpl_mo.join('。'))
                }
            }
        }

        if (arrtpl_co.length > 0) {
            tpl = arrtpl_co.join('。')
        }


    }

    tpl = tpl.replace(/撞地/g, '撞第'); // SB百度语音有时会把“撞地”读成“撞de”

    if (tpl.length < 50 && tpl.length > 0) {
        tpl += '。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。';
        tpl += '。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。';
    } else if (tpl.length > 180) {
        var tarr = tpl.split('，');
        var seclst = [];
        var sec;
        if (tpl.length > 800) {
            sec = Math.round(tarr.length / 8);
        } else if (tpl.length > 700) {
            sec = Math.round(tarr.length / 7);
        } else if (tpl.length > 600) {
            sec = Math.round(tarr.length / 6);
        } else if (tpl.length > 500) {
            sec = Math.round(tarr.length / 5);
        } else if (tpl.length > 400) {
            sec = Math.round(tarr.length / 4);
        } else if (tpl.length > 300) {
            sec = Math.round(tarr.length / 3);
        } else {
            sec = Math.round(tarr.length / 2);
        }

        while (tarr.length > 0) {
            var aa;
            if (tarr.length > sec) {
                aa = tarr.splice(0, sec);
            } else {
                aa = tarr.splice(0, tarr.length);
            }
            seclst.push(aa.join('，'));
        }

        tpl = seclst.join('@@@');
    }

    console.log('getDartboardAudioTpl .length', tpl.length);

    return tpl;
}


function getCurrentDate() {
    var date = '';
    if (date_type == 'Q') {
        date = $('#main_cb_quarter').attr('data');
    } else if (date_type == 'M') {
        date = $('#main_cb_month').attr('data');
    }
    return date;
}



/////////////////////////////

var currentTabIndex = 0;

function switchToTab1() {
    if (currentTabIndex == 0) {
        return;
    }
    currentTabIndex = 0;

    $('#maintab1').addClass('selected');
    $('#maintab2').removeClass('selected');

    drawDartboard();
    $('.subtab').hide();
    $('.legend1').show();
    $('.legend2').hide();
    $('#centerball').hide();
    $('#centerballbg').addClass('tab1');
    $('#centerballbg').removeClass('tab2');

    setExtLink();


    clearTimeout(itv_autoSwitchTab);
    itv_autoSwitchTab = setTimeout(autoSwitchTab, 80000);
}

function switchToTab2() {
    if (currentTabIndex == 1) {
        return;
    }
    currentTabIndex = 1;

    $('#maintab1').removeClass('selected');
    $('#maintab2').addClass('selected');

    drawDartboard();
    $('.subtab').show();
    $('.legend1').hide();
    $('.legend2').show();
    $('#centerball').show();
    $('#centerballbg').addClass('tab2');
    $('#centerballbg').removeClass('tab1');

    setExtLink();


    clearTimeout(itv_autoSwitchTab);
    itv_autoSwitchTab = setTimeout(autoSwitchTab, 80000);
}

$('#maintab1').on('click', function(e) {

    switchToTab1()

});

$('#maintab2').on('click', function(e) {

    switchToTab2()

});


// 自动循环切换两个TAB
var itv_autoSwitchTab;

function autoSwitchTab() {
    clearTimeout(itv_autoSwitchTab);

    if (!autoSwitch) {
        itv_autoSwitchTab = setTimeout(autoSwitchTab, 10);
        return;
    }

    if (!audioEnded && !audioPaused) {
        itv_autoSwitchTab = setTimeout(autoSwitchTab, 10);
        return;
    }

    if (currentTabIndex == 0) {
        switchToTab2();
    } else {
        switchToTab1();
    }

    itv_autoSwitchTab = setTimeout(autoSwitchTab, 80000);

}


// 自动循环转盘
var itv_autoSwitchDartboard;

function autoSwitchDartboard() {
    clearTimeout(itv_autoSwitchDartboard);

    if (!autoSwitch) {
        itv_autoSwitchDartboard = setTimeout(autoSwitchDartboard, 10);
        return;
    }

    if (!audioEnded && !audioPaused) {
        itv_autoSwitchDartboard = setTimeout(autoSwitchDartboard, 10);
        return;
    }

    switchDartboard();

    itv_autoSwitchDartboard = setTimeout(autoSwitchDartboard, 10000);

}


function switchDartboard() {

    if (selectedSliceId == totalSlices - 1) {
        selectedSliceId = 0;
    } else {
        selectedSliceId++;
    }

    window.selectSlice();

}



// ---------------------- 

function onCompanyChanged(comp_code) {
    current_company_code = comp_code;
    checkCompanyReay();
}

function checkCompanyReay() {
    if (companyCode2Name[current_company_code] == undefined) {
        setTimeout(checkCompanyReay, 0);
    } else {
        updateAllKpi();
    }
}



function setTitleDate() {
    var date = '';
    if (date_type == 'Q') {
        date = $('#main_cb_quarter .combobox_label').text();
    } else if (date_type == 'M') {
        date = $('#main_cb_month .combobox_label').text();
    }
    $('.pagetitle .maintitle .date').html(date); // 页面标题-时间
}


function setExtLink() {

    var date = getCurrentDate();
    var orgID = companyCode2NodeId[current_company_code];
    var roleId = Number(currentFleetType) == 0 ? 'CAPTAIN_FLEET' : 'CONTROL_FLEET';

    if (currentTabIndex == 0) {
        $('#ext_link1').attr('href', encodeURI('http://qar.hnair.net/DAP/rdThird.do?res_id=147797212514915201&paramArr=timeType=<' + date_type + '>::time=<' + date + '>::orgID=<' + orgID + '>::airport=<ALL>::acType=<ALL>::alertType=<A>::flagType=<standard>::kpiType=<QCL>'));
    } else {
        $('#ext_link1').attr('href', encodeURI('http://qar.hnair.net/DAP/rdThird.do?res_id=147798554068444703&paramArr=orgID=<' + orgID + '>::acType=<' + selectedAc + '>::fleetId=<ALL>::timeType=<' + date_type + '>::time=<' + date + '>::alertType=<A>::kpiType=<QCL>::roleId=<' + roleId + '>'));
    }
}

regTooltip('.ext_link', '查看关联报表');


getAllCompanyKpiData();



////////