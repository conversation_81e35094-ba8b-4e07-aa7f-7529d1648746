var mapLeft = echarts.init(document.getElementById('map_left'));
var mapMid = echarts.init(document.getElementById('map_mid'));
var mapRight = echarts.init(document.getElementById('map_right'));
const parent_company = 'HNAHK';
const codelist = [];
const codelist_no_parent = [];
let stdStart;
let stdEnd;
let stdEndUtcTime;
let stdStartUtcTime;
let all_company_flt = new Object();
// 飞机航班信息
var flightList = new Object();
// 机场列表
var airportList = new Object();
let all_comp_city = new Object();
let all_hbi2_base = new Object();
let comp_normal_rate_list = new Object();

let compLoc = {
    'HU': [110.306427, 19.874506, 1],
    '8L': [102.721522, 25.053664, 1],
    'HX': [114.283815, 22.313191, 1],
    'PN': [106.648864, 29.728189, 1],
    'GS': [118.205627, 39.09494, 1],
    'JD': [116.428789, 40.929164, 1],
    'FU': [119.301501, 26.089273, 1],
    'UQ': [87.605779, 43.829476, 1],
    'GX': [108.371914, 22.816108, 1],

    'Y8': [121.47535, 31.231859, 1],
    '9H': [108.949468, 34.356973, 1],
    'GT': [110.303054, 25.281461, 1],
    'UO': [114.190104, 22.391809, 1],

    'CN': [114.39213, 38.886353, 1],
}


var mapLeftOption = {
    globe: {
        globeRadius: 50,
        baseTexture: './img/screen_earth_2d.jpg',
        displacementScale: 0.1,
        shading: 'lambert',
        shading: 'realistic',
        light: {
            main: {
                intensity: 0.3
            },
            ambient: {
                intensity: 1.0
            },
        },

        viewControl: {
            autoRotate: true,
            zoomSensitivity: false,
            targetCoord: [105.5, 32]
        },

        layers: []
    },
    itemStyle: {
        areaColor: '#0F4687',//行政区背景色
        color: '#132D98',
        opacity: 1,
        borderWidth: 0.4,
        borderColor: '#16AFF5'//行政区边界线颜色
    },
    viewControl: {},
    //当鼠标放上去  地区区域是否显示名称
    emphasis: {
        label: {
            show: false,
        }
    },
    light: { //光照阴影
        main: {
            color: '#fff', //光照颜色
            intensity: 1.2, //光照强度
            shadowQuality: 'high', //阴影亮度
            shadow: true, //是否显示阴影
            alpha: 55,
            beta: 10

        },
        ambient: {
            color: '#132D98',
            intensity: 0.8
        }
    },
    series: []
};
var mapMidOption = {
    geo3D: {
        map: 'world',
        silent: true,
        itemStyle: {
            areaColor: '#2344A1',//行政区背景色
            color: '#132D98',
            opacity: 1,
            borderWidth: 0.4,
            borderColor: '#16AFF5'//行政区边界线颜色
        },
        viewControl: {
            minBeta: -360,
            maxBeta: 360,
            alpha: 60,//视角绕 x 轴，即上下旋转的角度。
            center: [0, 0, 0],
            distance: 90,//默认视角距离主体的距离
            minDistance: 5,
            panSensitivity: 0, //平移操作的灵敏度，设置为0后无法平移。
            rotateSensitivity: 0, //旋转操作的灵敏度，设置为0后无法旋转。
            zoomSensitivity: 0, //缩放操作的灵敏度，设置为0后无法缩放。
        },
        //当鼠标放上去  地区区域是否显示名称
        emphasis: {
            label: {
                show: false,
            }
        },
        light: { //光照阴影
            main: {
                color: '#fff', //光照颜色
                intensity: 1.2, //光照强度
                shadowQuality: 'high', //阴影亮度
                shadow: true, //是否显示阴影
                alpha: 55,
                beta: 10

            },
            ambient: {
                color: '#132D98',
                intensity: 0.8
            }
        }
    },
    series: []
};
var mapRightOption = {
    geo3D: {
        map: 'china',
        silent: true,
        itemStyle: {
            areaColor: '#0F4687', //行政区背景色
            color: '#132D98',
            opacity: 1,
            borderWidth: 0.4,
            borderColor: '#16AFF5' //行政区边界线颜色
        },

        viewControl: {
            minBeta: -360,
            maxBeta: 360,
            alpha: 50, //视角绕 x 轴，即上下旋转的角度。
            center: [0, 0, 0],
            distance: 120, //默认视角距离主体的距离
            minDistance: 5,
            panSensitivity: 0, //平移操作的灵敏度，设置为0后无法平移。
            rotateSensitivity: 0, //旋转操作的灵敏度，设置为0后无法旋转。
            zoomSensitivity: 0, //缩放操作的灵敏度，设置为0后无法缩放。
        },
        //当鼠标放上去  地区区域是否显示名称
        emphasis: {
            label: {
                show: false,
            }
        },
        light: { //光照阴影
            main: {
                color: '#fff', //光照颜色
                intensity: 1.2, //光照强度
                shadowQuality: 'high', //阴影亮度
                shadow: true, //是否显示阴影
                alpha: 55,
                beta: 10

            },
            ambient: {
                color: '#132D98',
                intensity: 0.8
            }
        }
    },
    series: []
};

class screen {
    constructor() {
        this._initMap();
        this._initTime();
    }

    loadAll() {
        if ($('#loading_msk').length == 0) {
            showLoading();
        }
        var len = companylist.length;
        for (var i = 0; i < len; i++) {
            var dat = companylist[i];
            codelist.push(dat.code);
            if (dat.code != parent_company) {
                codelist_no_parent.push(dat.code);
            }
        }
        Promise.all([this.loadCompNormalRateList(), this.getAllCompSts(), this.getStandardFocFlightInfo(), this.getAirportList(),
            this.loadCompCity_i(), this.loadHbi2Base_i()
        ]).then((resolve) => {
            this.setHxData();
            this.setThcsMapData();
            this.setHszcl();
        }).catch((reject) => {
            console.log(reject);
            alert('数据获取出错！');
        });
    }

    loadCompNormalRateList() {
        return new Promise((resolve, reject) => {
            codelist_no_parent.forEach((v, i) => {
                this.getCompSts(v).then((resolve) => {
                    return true;
                })
            })
            console.log("comp_normal_rate_list", comp_normal_rate_list);
            resolve("success");
        });
    }

    getCompSts(compcode) {
        return new Promise((resolve, reject) => {
            var param = {
                "stdStartUtcTime": stdStartUtcTime,
                "stdEndUtcTime": stdEndUtcTime,
                "companyCodes": compcode,
                "AcTypeList": "",
                "depstns": "",
                "arrstns": ""
            };
            $.ajax({
                type: 'post',
                url: "/bi/redis/7x2_flt_sts",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    var pfPercent = Number(response.pfdappPercent); //计划航班中的正常航班比率
                    comp_normal_rate_list[compcode] = pfPercent;
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    getAllCompSts() {
        var param = {
            "stdStartUtcTime": stdStartUtcTime,
            "stdEndUtcTime": stdEndUtcTime,
            "companyCodes": codelist_no_parent.join(","),
            "AcTypeList": "",
            "depstns": "",
            "arrstns": ""
        };
        $.ajax({
            type: 'post',
            url: "/bi/redis/7x2_flt_sts",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {
                console.log("all_comp_flt_info", response);
                all_company_flt['fftc'] = Number(response.fftc);
            },
            error: function (response) {
            }
        });
    }

    loadCompCity_i() {
        return new Promise((resolve, reject) => {
            var date = new Date();
            var mm = date.getMonth();
            if (mm < 10) {
                mm = '0' + mm;
            }
            var param = {
                "COMP_ID": "100",
                "DATE_ID": date.getFullYear() + "" + mm,
                "ARP_TYPE": "I", //L: 国内 Local; I: 国际 International
            };
            $.ajax({
                type: 'post',
                url: "/bi/web/compCity",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    console.log("all_comp_city", response.data);
                    if (response.data != undefined) {
                        all_comp_city = response.data;
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            })
        })
    }

    loadHbi2Base_i() {
        return new Promise((resolve, reject) => {
            var param = {
                "COMP_CODE": parent_company,
                "AIRPORT_TYPE": "I", //L: 国内 Local; I: 国际 International
            };
            $.ajax({
                type: 'post',
                url: "/bi/web/hbi2Base",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    console.log("all_hbi2_base", response.data);
                    if (response.data != undefined) {
                        all_hbi2_base = response.data;
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            })
        })
    }

    getStandardFocFlightInfo() {
        return new Promise((resolve, reject) => {
            var param = {
                "stdStart": stdStart,
                "stdEnd": stdEnd,
                "acOwner": '',
                "statusList": 'DEP', // 只返回起飞的，表示在空中
            }
            $.ajax({
                type: 'post',
                url: "/bi/web/getStandardFocFlightInfo",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    var list = response.data;
                    for (var i = list.length - 1; i >= 0; i--) {
                        var obj = list[i];
                        flightList[obj.flightNo] = obj;
                        // // 国际航班
                        // if (obj.fltType == 'I') {
                        //     fltIntList.push(obj);
                        // }
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        })
    }

    getAirportList() {
        return new Promise((resolve, reject) => {
            var param = {
                //"AIRPORT_CODE": '', // 可选，传入机场CODE
            }
            $.ajax({
                type: 'post',
                url: "/bi/web/airportdetail",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    var list = response.airport;
                    for (var i = list.length - 1; i >= 0; i--) {
                        var arp = list[i];
                        airportList[arp.code] = arp;
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    setHszcl() {
        let mapRightSerie = new Array();
        let seriesData = new Array();
        for (var compcode in comp_normal_rate_list) {
            var rate = comp_normal_rate_list[compcode];
            var color;

            rate = Math.round(rate * 100) / 100;

            if (rate >= 80) {
                color = 'green';
            } else {
                color = 'red';
            }

            seriesData.push({
                "name": compcode, "value": compLoc[compcode], "itemStyle": {
                    "color": color
                }
            });

        }
        mapRightSerie.push({
            type: "scatter3D",
            coordinateSystem: 'geo3D',
            symbolSize: 24,
            zlevel: 1,
            tooltip: {
                show: false
            },
            label: {
                show: true,
                formatter: '{b}',
                position: 'top',
            },
            data: seriesData
        })

        let options = mapRight.getOption();
        options.series = mapRightSerie;
        mapRight.setOption(options, true);
        hideLoading();
    }

    setThcsMapData() {
        let cityData = all_comp_city.map((v, i) => {
            return {
                "name": v.CITY_NAME,
                "value": [v.AIRPORT_LONGITUDE, v.AIRPORT_LATITUDE, 1]
            }
        });
        let baseData = all_hbi2_base.map((v, i) => {
            return {
                "name": v.CITY_NAME,
                "value": [v.AIRPORT_LONGITUDE, v.AIRPORT_LATITUDE, 1]
            }
        });
        let thcsSeries = new Array();
        thcsSeries.push({
            "type": "scatter3D",
            "coordinateSystem": "geo3D",
            "symbol": "path://M16.319,3.633 C14.538,1.873 12.121,0.884 9.602,0.884 C4.355,0.884 0.102,5.086 0.102,10.270 C0.102,16.822 7.126,24.870 9.602,24.870 C11.946,24.870 19.101,16.887 19.101,10.270 C19.101,7.780 18.101,5.393 16.319,3.633 ZM11.723,12.259 C11.160,12.822 10.397,13.138 9.602,13.138 C7.945,13.138 6.602,11.795 6.602,10.139 C6.602,8.484 7.945,7.141 9.602,7.141 C11.258,7.141 12.602,8.484 12.602,10.139 C12.602,10.934 12.285,11.697 11.723,12.259 Z",
            "symbolSize": 18,
            "symbolOffset": [0, "-30%"],
            "label": {
                "normal": {
                    "formatter": "{b}",
                    "position": "right",
                    "show": false
                },
                "emphasis": {
                    "show": true
                }
            },
            "itemStyle": {
                "normal": {
                    "color": "#00A9FF",
                    "shadowBlur": 10,
                    "shadowColor": "#333"
                }
            },
            data: cityData,
        });
        thcsSeries.push({
            "type": "scatter3D",
            "coordinateSystem": "geo3D",
            "symbol": "path://M16.319,3.633 C14.538,1.873 12.121,0.884 9.602,0.884 C4.355,0.884 0.102,5.086 0.102,10.270 C0.102,16.822 7.126,24.870 9.602,24.870 C11.946,24.870 19.101,16.887 19.101,10.270 C19.101,7.780 18.101,5.393 16.319,3.633 ZM11.723,12.259 C11.160,12.822 10.397,13.138 9.602,13.138 C7.945,13.138 6.602,11.795 6.602,10.139 C6.602,8.484 7.945,7.141 9.602,7.141 C11.258,7.141 12.602,8.484 12.602,10.139 C12.602,10.934 12.285,11.697 11.723,12.259 Z",
            "symbolSize": 18,
            "symbolOffset": [0, "-30%"],
            "label": {
                "normal": {
                    "formatter": "{b}",
                    "position": "right",
                    "show": false
                },
                "emphasis": {
                    "show": true
                }
            },
            "itemStyle": {
                "normal": {
                    "color": "#22AC38",
                    "shadowBlur": 10,
                    "shadowColor": "#333"
                }
            },
            data: baseData,
        });
        let options = mapMid.getOption();
        options.series = thcsSeries;
        mapMid.setOption(options, true);
    }

    setHxData() {
        $('#exec_plane_num').text(all_company_flt['fftc']);
        var seriesData = [];

        for (var fltno in flightList) {
            let flt = flightList[fltno];

            var arp1 = airportList[flt.depStn];
            var arp2 = airportList[flt.arrStn];

            if (arp1 && arp2 && arp1.longitude && arp1.latitude && arp2.longitude && arp2.latitude) {
                var color;

                if (flt.status == 'DEL' || (flt.delay1 != '' && flt.dur1 > 0)) {
                    color = '#fff663';
                } else if (flt.status == 'ARR' || flt.status == 'NDR' || flt.status == 'ATD' || flt.status == 'ATA' || flt.status == 'DEP') {
                    color = '#0cff00';
                } else if (flt.status == 'CNL' || flt.status == 'RTR') {
                    color = '#FF0000';
                } else { //SCH
                    color = '#00c6ff';
                }

                seriesData.push({
                    fltno: fltno,
                    arrStn: flt.arrStn,
                    depStn: flt.depStn,
                    coords: [
                        [arp1.longitude, arp1.latitude],
                        [arp2.longitude, arp2.latitude]
                    ],
                    value: fltno,
                    lineStyle: {
                        width: 1,
                        color: color,
                        opacity: 1
                    },
                });
            }
        }

        let hxseries = new Array();
        hxseries.push({
            type: 'lines3D',
            coordinateSystem: 'globe',
            effect: {
                show: true,
                period: 6,
                trailLength: 0.3,
                trailColor: '#fff',
                trailWidth: 1
            },
            silent: false,
            tooltip: {
                show: false
            },
            data: seriesData
        });
        let option = mapLeft.getOption();
        option.series = hxseries;
        mapLeft.setOption(option, true);
    }

    _initMap() {
        mapLeft.setOption(mapLeftOption);
        mapMid.setOption(mapMidOption);
        mapRight.setOption(mapRightOption);
    }

    _initTime() {
        var date = new Date();
        var mm = date.getMonth() + 1;
        var dd = date.getDate();
        if (mm < 10) {
            mm = '0' + mm;
        }
        if (dd < 10) {
            dd = '0' + dd;
        }
        stdStart = date.getFullYear() + '-' + mm + '-' + dd + ' 00:00:00';
        stdEnd = date.getFullYear() + '-' + mm + '-' + dd + ' 23:59:59';
        stdEndUtcTime = date.getFullYear() + '-' + mm + '-' + dd + ' 15:59:59';
        var yesterday_ts = date.getTime() - 86400000;
        date.setTime(yesterday_ts);
        mm = date.getMonth() + 1;
        dd = date.getDate();
        if (mm < 10) {
            mm = '0' + mm;
        }
        if (dd < 10) {
            dd = '0' + dd;
        }
        stdStartUtcTime = date.getFullYear() + '-' + mm + '-' + dd + ' 16:00:00';
    }
}

let sc = new screen();
$.when(getCompany()).done(sc.loadAll());
document.onclick = function (e) {
    window.location.href = '/largescreen/sdc/index.html?scale=auto';
}