
.page-bgimg {
  position: absolute;
  top: 0px;
  width: 2132px;
  height: 768px;
  background: url(../img/base_bg2.png?10) no-repeat top center;
  overflow-x: hidden;
  overflow-y: hidden;
}

.page-wrapper {
  position: absolute;
  top: 0px;
  width: 2132px;
  height: 768px;
  overflow-x: hidden;
  overflow-y: hidden;
  pointer-events: none;
}

.mapframe {
  position: absolute; 
  top:44px; 
  left: 695px; 
  width:740px; 
  height:570px;
  border: none;
  padding: 0;
  margin: 0;
  overflow-y: hidden;
  overflow-x: hidden;
  pointer-events: auto;

}






#col_l {
	position: absolute;
	width: 682px;
	height: 735px;
	top: 13px;
	left: 13px;
}

#col_l .row1{
	position: absolute;
	width: 100%;
	height: 179px;
}

#col_l .row1 .tit{
	position: absolute;
	bottom: 44px;
	left: 44px;
	font-size: 16px;
}

#col_l .row1 .chart1{
	position: absolute;
	top: 30px;
	left: 160px;
}
#col_l .row1 .chart2{
	position: absolute;
	top: 30px;
	left: 330px;
}
#col_l .row1 .chart3{
	position: absolute;
	top: 30px;
	left: 500px;
}
#col_l .row1 .chart{
	width: 140px;
	height: 140px;
	background: url('../img/chart_bg.png') no-repeat center center;
	display: none;
}
#col_l .row1 .chart .pointer{
	position: absolute;
	top: 0px;
	left: 0px;
}
#col_l .row1 .chart .lb{
	position: absolute;
	top: 1px;
	left: 1px;
	font-size: 12px;
}
#col_l .row1 .chart .lb_rate1{
	position: absolute;
	top: 98px;
	left: 45px;
	font-size: 11px;
	color: #79FF44 !important;
}
#col_l .row1 .chart .lb_rate2{
	position: absolute;
	top: 112px;
	left: 45px;
	font-size: 11px;
	color: #FFBC00 !important;
}
#col_l .row1 .chart .lb_rate1 span{
	color: #79FF44 !important;
}
#col_l .row1 .chart .lb_rate2 span{
	color: #FFBC00 !important;
}








#col_l .row2 {
	position: absolute;
	width: 100%;
	height: 110px;
	top: 185px;
}
#col_l .row2 .col {
	position: absolute;
	width: 206px;
	height: 100px;
}
#col_l .row2 .col1 {
	left: 23px;
}
#col_l .row2 .col2 {
	left: 247px;
}
#col_l .row2 .col3 {
	left: 473px;
}
#col_l .row2 .tit{
	position: absolute;
	top: 10px;
	left: 0px;
}
#col_l .row2 .r1{
	position: absolute;
	top: 30px;
	left: 0px;
}
#col_l .row2 .r2{
	position: absolute;
	top: 39px;
	right: 14px;
	width: 150px;
	text-align: right;
}
#col_l .row2 .barbg{
	position: absolute;
	width:192px; 
	height:12px; 
	left:0px; 
	top:65px; 
	border-radius: 6px; 
	background-color: #1F328B;
}





#col_l .row3 {
	position: absolute;
	width: 638px;
	height: 238px;
	top: 307px;
	left: 25px;
}
#col_l .row3 .toprate {
	position: absolute;
	top: 5px;
	left: 10px;
}
#col_l .row3 .toprate .k2 {
	margin-left: 20px;
}

#col_l .row3 .left {
  position: absolute;
  top: 2px;
  left: 20px;
  text-align: left;
}
#col_l .row3 .right {
  position: absolute;
  top: 2px;
  right: 20px;
  text-align: right;
}

#col_l .row3 .chart {
  position: absolute;
  top: 75px;
  left: 226px;
  width: 200px;
  height: 152px;
}
#col_l .row3 .chart canvas {
  position: absolute;
  top: 10px;
  left: 35px;
  width: 130px;
  height: 130px;
}
#col_l .row3 .chart .txt {
  position: absolute;
  text-align: center;
  top: 55px;
  width: 100%;
}
#col_l .row3 .tit1 {
  position: absolute;
  top: 41px;
}
#col_l .row3 .tit2 {
  position: absolute;
  top: 41px;
  right: 70px;
}
#col_l .row3 .rate1 {
  position: absolute;
  top: 141px;
  left: 158px;
  width: 90px;
  text-align: right;
}
#col_l .row3 .rate2 {
  position: absolute;
  top: 141px;
  left: 407px;
  width: 90px;
}
#holder_delay_cause_comp {
  position: absolute;
  top: 64px;
  left: 00px;
  width: 180px;
}
#holder_delay_cause_none {
  position: absolute;
  top: 64px;
  right: -10px;
  width: 150px;
}






#col_l .row4 {
	position: absolute;
	width: 675px;
	height: 150px;
	top: 579px;
	left: 5px;
}
#col_l .row4 .col{
	position: absolute;
	width: 225px;
	height: 160px;
}
#col_l .row4 .col2{
	left: 225px;
}
#col_l .row4 .col3{
	left: 450px;
}
#col_l .row4 .col .tt{
	position: absolute;
	top: 12px;
	left: 20px;
	font-size: 14px;
	font-weight: bold;
}
#col_l .row4 .chartblock {
	position: absolute;
	width: 222px;
	height: 110px;
	top: 40px;
	left: 0px;
	pointer-events: auto;
}






#col_m {
	position: absolute;
	width: 742px;
	height: 735px;
	top: 13px;
	left: 695px;
}

#col_m .map_title {
	position: absolute;
	width: 100%;
	height: 35px;
	top: 5px;
	text-align: center;
	font-weight: bold;
	font-size: 16px;
}

#col_m .bot {
	position: absolute;
	width: 100%;
	height: 133px;
	bottom: 0px;
}
#col_m .bot .tab_trend {
	position: absolute;
	width: 30px;
	top: 13px;
	left: 15px;
}

#col_m .bot .tab_normal_rate {
	display: inline-block; 
	width: 36px; 
	height: 56px;
	text-align: center;
	cursor: pointer;
	border:1px solid #3939A2;
	pointer-events: auto;
	color: #4358BD;
	padding: 12px 6px 0 6px;
	line-height: 16px;
}
#col_m .bot .tab_normal_rate.selected {
	background-color: #3939A2;
	color: #FFF;
}
#col_m .bot .block_top5 {
	position: absolute;
	width: 250px;
	height: 118px;
	top: 12px;
	right: 10px;
	border:  1px solid #3A55B3;
	border-radius: 5px;
	background-color: #1D2357;
}
#col_m .bot .tab_top5 {
	position: absolute;
	width:100%; 
	height:27px; 
	left:0px; 
	top:33px; 
	border-bottom:1px solid #1D2158;
	background-color: #2D3D8D;
}
#col_m .bot .block_top5 .tit {
	position: absolute;
	left:10px; 
	top:7px; 
	font-size: 16px;
	font-weight: bold;
}
#col_m .bot .tab_rank {
	display: inline-block; 
	height: 26px;
	text-align: center;
	line-height: 27px;
	cursor: pointer;
	pointer-events: auto;
	padding: 0 14px 0 14px;
	color: #9B99FF;
}
#col_m .bot .tab_rank.selected {
	border-bottom:1px solid #FFF;
	color: #FFF;
}
#col_m .bot .rank_no {
	display: inline-block;
	width: 37px;
	text-align: center;
}
#col_m .bot .block_top5 .no {
	position: absolute;
	width: 100%;
	height: 40px;
	top: 69px;
}
#col_m .bot .block_top5 .name {
	position: absolute;
	width: 100%;
	height: 40px;
	top: 88px;
}
#col_m .bot .tit_trend {
	position: absolute;
	width: 100px;
	height: 20px;
	top: 8px;
	left: 68px;
	font-size: 14px;
}
#col_m .bot .chart_trend {
	position: absolute;
	width:400px; 
	height:120px;
	top: 30px;
	left: 68px;
}






#col_r {
	position: absolute;
	width: 672px;
	height: 735px;
	top: 13px;
	left: 1440px;
}

#backupplane_b0 {
	left: 52px;
	top: 50px;
}
#backupplane_b1 {
	left: 173px;
	top: 50px;
}

#block_backup .tit {
	position: absolute;
	width: 100px;
	height: 20px;
	font-weight: bold;
	font-size: 14px;
	top: 20px;
	left: 20px;
}
#block_backup .lb_plane {
	position: absolute;
	width: 40px;
	height: 20px;
	font-size: 12px;
	top: 50px;
	left: 20px;
}
#block_backup .lb_crew {
	position: absolute;
	width: 40px;
	height: 20px;
	font-size: 12px;
	top: 136px;
	left: 20px;
}
#block_backup .backupplane {
	position: absolute;
	width: 133px;
	height: 77px;
	background: url('../img/backupplane_bg.png') no-repeat 0 0;
}
#block_backup .backupplane .ac {
	position: relative;
	width: 130px;
	height: 22px;
	padding: 2px 0 0 30px;
}
#block_backup .backupplane .block {
	position: relative;
	display: inline-block;
	width: 55px;
	height: 52px;
}
#block_backup .backupplane .block .cityname {
	position: relative;
	width: 100%;
	height: 27px;
	color: #130A4D;
	text-align: center;
	padding-top: 2px;
}
#block_backup .backupplane .block .num {
	position: relative;
	width: 100%;
	height: 28px;
	text-align: center;
}
#block_crew {
	position: absolute;
	width: 132px;
	height: 53px;
	top: 136px;
	left: 52px;
	background: url('../img/crew_bg.png') no-repeat 0 0;
	text-align: center;
}
#block_crew .l1{
	position: absolute;
	width: 58px;
	height: 100%;
	left: 0px;
}
#block_crew .l2{
	position: absolute;
	width: 66px;
	height: 100%;
	left: 66px;
}
#block_crew .lb{
	position: relative;
	width: 100%;
	height: 20px;
	font-size: 12px;
	color: #091030;
	margin: 1px 0 5px 0; 
}


#beijingtime {
	width:220px; 
	height:30px; 
	left:349px;
	top:1px;
}

.weather_block {
	position: absolute;
	width:320px; 
	height:111px; 
	left:341px;
	top:84px; 
	pointer-events: auto;
}
#weather_city {
	width:140px; 
	height:22px; 
	left:370px;
	top:29px;
}
#weather_city_prev{
	width:22px; 
	height:22px; 
	left:344px;
	top:29px; 
	pointer-events: auto; 
	cursor:pointer;
	background: url('../img/arr_l.png') no-repeat center center;
}
#weather_city_next{
	width:22px; 
	height:22px; 
	left:505px;
	top:29px; 
	pointer-events: auto; 
	cursor:pointer;
	background: url('../img/arr_r.png') no-repeat center center;
}
#weather_warning_info {
	width:287px; 
	height:22px; 
	left:383px;
	top:217px;
	color: #BDCCFF;
}
#delay_warning_msg {
	width:289px; 
	height:22px; 
	left:43px;
	top:217px;
	color: #BDCCFF;
}
.weather_itms {
	position: absolute;
	width:180px; 
	height:111px; 
	left:0px;
	top:0px;
}
.weather_itms .info {
	display: block; 
	margin-bottom: 5px;
}
.weather_itms .info .txt {
	margin-right:5px;
}
.weather_atlas {
	width:128px; 
	height:110px; 
	left:180px;
	top:0px; 
	background-size: contain;
	background-repeat: no-repeat;
}
.weather_itm {
	display: inline-block;
	width: 55px;
	height: 40px;
	font-size: 12px;
}
#important_flt {
	position: absolute;
	width:646px; 
	height:294px; 
	left:14px;
	top:292px;
	border: 1px solid #3351A7;
}
#important_flt .tit {
	position: absolute;
	font-size: 14px;
	font-weight: bold;
	width: 100px;
	height: 20px;
	top: -30px;
	left: 0px;
}
#important_flt .row {
	position: relative;
	width: 100%;
	margin: 0 !important;
}
#important_flt .top {
	height: 25px;
	background-color: #1A2A60;
}
#important_flt .mid {
	height: 269px;
	overflow: hidden;
}
#important_flt .mid .c1 .itm {
	top: 4px;
}
#important_flt .mid .itm {
	position: relative;
	height: 22px;
	line-height: 22px;
	font-size: 11px;
}
#important_flt .mid .itm:first-child {
	margin-top: 3px;
}
#important_flt .mid .itm.aog .plane {
	color: #FFFF26;
	border-bottom: 1px solid #FFFF26;
}
#important_flt .mid .itm.aog .ico {
	display: inline-block;
	font-size: 9px;
	line-height: 12px;
	height: 12px;
	padding:0 3px; 
	background-color: #FFFF26;
	border-radius: 2px;
	color: #080F31;
	margin-left: 3px;
}

#important_flt .c1 {
	position: relative;
	display: inline-block;
	width: 80px;
	height: 100%;
	padding-left: 12px;
	border-right: 1px solid #253B7F;
}
#important_flt .c2 {
	top: 0px;
	left: 81px;
	width: 563px;
	overflow: hidden;
}
#important_flt .c2 .grid{
	top: 0px;
	width: 90px;
	border-right: 1px solid #253B7F;
}
#important_flt .top .c2{
	top: -30px;
	height: 55px;
}
#important_flt .top .c2 .grid{
	height: 25px;
}
#important_flt .mid .c2{
	height: 100%;
}
#important_flt .mid .c2 .grid{
	height: 267px;
}
#important_flt .top .c2 .grid2{
	width: 3px;
	height: 5px;
	background-color: #fff;
}
#important_flt .top .c2 .gridtxt{
	font-size: 9px;
	width: 14px;
	height: 25px;
	text-align: center;
	line-height: 28px;
}

#important_flt .mid .con {
	top: 12px;
	left: 80px;
	width: 563px;
	height: 254px;
	overflow: hidden;
}
#important_flt .mid .c1 .con1 {
	top: 0;
	left: 9px;
	width: 80px;
}

#important_flt .mid .con .itmrow {
	position: relative;
	height: 22px;
	font-size: 9px;
	color: #A6ABFF;
}
#important_flt .mid .con .itmrow div {
	height:22px;
	width: 1500px;
}
#important_flt .mid .con .itmrow span {
	display: inline-block;
	margin-right: 3px;
}

#important_flt .current {
	width: 1px;
	height: 267px;
	background-color: #BFDFFF;
}
#important_flt .spcont {
	top: 0px;
	left: 0px;
}

.time_yellow{
	color: #0E1731;
	display: inline-block;
	background-color: #FFE700;
	height: 10px;
	padding-left: 3px;
	line-height: 11px;
	
}
.time_purple{
	color: #0E1731;
	display: inline-block;
	background-color: #9570FF;
	height: 10px;
	padding-left: 3px;
	line-height: 11px;
}
.time_blue{
	color: #0E1731;
	display: inline-block;
	background-color: #3776FF;
	height: 10px;
	padding-left: 3px;
	line-height: 11px;
}
.time_green{
	color: #0E1731;
	display: inline-block;
	background-color: #00B300;
	height: 10px;
	padding-left: 3px;
	line-height: 11px;
}



#block_right_bottom {
	position: absolute;
	width: 100%;
	height: 123px;
	top: 608px;
	left: 0;
}
#block_right_bottom .block1 {
	position: absolute;
	width: 100%;
	height: 100%;
	background: url('../img/r_b_1.png') no-repeat center center;
	top: 6px;
}

#block_right_bottom .block1 .blk {
	position: absolute;
	width: 152px;
	height: 100%;
}
#block_right_bottom .block1 .blk1 {
	left: 19px;
}
#block_right_bottom .block1 .blk2 {
	left: 180px;
}
#block_right_bottom .block1 .blk3 {
	left: 342px;
}
#block_right_bottom .block1 .blk4 {
	left: 506px;
}
#block_right_bottom .block1 .lb {
	font-size: 14px;
	font-weight: bold;
	margin-top: 8px;
	height: 40px;
}
#block_right_bottom .block1 .va {
	margin-top: 46px;
	margin-left: 61px;
}
#block_right_bottom .block1 .va2 {
	margin-top: 41px;
	margin-left: 65px;
}

#block_right_bottom .block2 .tab {
	position: absolute;
	width: 110px;
	height: 30px;
	border: 1px solid #3939A2;
	cursor: pointer;
	pointer-events: auto;
}
#block_right_bottom .block2 .tab span{
	position: absolute;
	display: inline-block;
	width: 100%;
	height: 100%;
	padding-left: 35px;
	font-size: 14px;
	font-weight: bold;
	line-height: 30px;
	opacity: 0.4;
}
#block_right_bottom .block2 .tab.selected {
	border: 1px solid #3939A2;
	background-color: #3939A2;
}
#block_right_bottom .block2 .tab.selected span {
	opacity: 1;
}
#block_right_bottom .block2 .tab1 {
	left: 20px;
	top: 28px;
}
#block_right_bottom .block2 .tab2 {
	left: 20px;
	top: 68px;
}
#block_right_bottom .block2 .tab1 span {
	background: url('../img/tab_icon2.png') no-repeat 10px center;
}
#block_right_bottom .block2 .tab2 span {
	background: url('../img/tab_icon1.png') no-repeat 10px center;
}

#block_right_bottom .block2 .tabc {
	position: absolute;
	width: 550px;
	height: 100px;
	left: 130px;
	top: 12px;
}



#block_right_bottom .vip_block {
	position: relative;
	display: inline-block;
	width: 170px;
	height: 97px;
}
#block_right_bottom .vip_block .l1{
	top: 33px;
	left: 20px;
	width: 70px;
	height: 20px;
}
#block_right_bottom .vip_block .l2{
	top: 46px;
	left: 17px;
	width: 70px;
	height: 20px;
}
#block_right_bottom .vip_block .chr{
	top: 5px;
	left: 75px;
}
#block_right_bottom .vip_block .r1{
	top: 35px;
	left: 95px;
	width: 70px;
	height: 20px;
}
#block_right_bottom .vip_block .r2{
	top: 47px;
	left: 92px;
	width: 70px;
	height: 20px;
	color: white;
}

