#earth3d-wrapper {
  background: url(../img/a0-1_bg.png) no-repeat left center;
}

#header {
  height: 83px;
  width: 1920px;
  position: absolute;
  left: 0px;
  top: 0px;
  pointer-events: auto;
  background: url(../img/17_navbg.png) repeat-x;
  background-size: 1920px 80px;
  border-bottom: 1px solid #1f5fa8;
  z-index: initial;
}

.page-wrapper {
  width: 1920px;
  height: 1440px;
  z-index: initial;
}

div {
  pointer-events: auto;
}

/* .pageBg{
  background: url(../img/a0-1_page_bg.png) no-repeat left center;
  background-size: 1366px 768px;
  width: 1366px;
  height: 768px;
} */

#logo {
  position: absolute;
  left: 0px;
  top: 0px;
  width: 800px;
  height: 76px;
  background: url(../img/logo.svg) no-repeat 10px center;
  background-size: 380px 58px;
  padding: 39px 9px 0 0;
  font-size: 14px;
  text-align: center;
  color: #6d92ba;
  cursor: pointer;
}

#main_page_title {
  position: absolute;
  left: 431px;
  top: 22px;
  height: 22px;
  font-size: 22px;
  color: #fdff9d;
  font-weight: bold;
  letter-spacing: 0px;
}

#main_page_subtitle {
  position: absolute;
  left: 460px;
  top: 35px;
  height: 12px;
  font-size: 12px;
  color: #ee0d1a;
  font-weight: bold;
  display: none;
}

#header .selector {
  position: absolute;
  right: 0;
  height: 100%;
  width: 650px;
}

.pagetitle {
  position: absolute;
  width: 100%;
  height: 76px;
  top: 136px;
  text-align: center;
  color: #9de6fa;
  width: 50%;
  left: 25%;
  font-size: 30px;
  margin: 0 auto;
  text-shadow: 0 0 20px #0c2d68, 0 0 20px #0c2d68;
}


.combobox_label {
  padding: 0;
  background: url(../img/combobox_arr.png) no-repeat 125px center;
  border: none;
  line-height: 36px;
  color: #44a3f4;
  height: 36px;
  font-size: 14px;
  border-radius: 5px;
  padding-left: 10px;
  margin-top: -1px;
  margin-left: -1px;
  border: 1px solid transparent;
}

.combobox_list .item {
  line-height: 36px;
  color: #44a3f4;
  height: 36px;
  font-size: 14px;
  padding-left: 10px;
}

.combobox_list {
  max-height: 400px !important;
  z-index: 2000;
}

#week_date_range {
  position: absolute;
  top: 63px;
  right: 158px;
  height: 36px;
  line-height: 36px;
  width: 150px;
  font-size: 14px;
  text-align: center;
  border: 1px solid #1f5fa8;
  border-radius: 2px;
  padding: 3px 0 5px 0px;
  background-color: #021d39;
  box-shadow: 0 1px 8px #021121;
  display: none;
}


.con_flex_column {
  display: flex;
  display: -webkit-flex;
  -webkit-flex-flow: column;
}

.con_flex_row {
  display: flex;
  display: -webkit-flex;
  -webkit-flex-flow: row;
}

.flex_none {
  -webkit-flex: none;
  flex: none;
}

.flex_1 {
  -webkit-flex: 1;
  flex: none;
}


#date_select {
  position: absolute;
  top: 25px;
  left: 130px;
  width: 200px;
  height: 36px;
  font-size: 14px;
  text-align: center;
  overflow: hidden;
  color: #44a3f4;
  border-radius: 5px;
  border: solid 1px rgba(0, 183, 238, 0.2);
}

#date_select div {
  line-height: 36px;
  width: 50px;
  cursor: pointer;
  user-select: none;

}

#date_select .selected,
.dateType .selected {
  background-color: #265aa3;
  border-radius: 5px;
  color: #ffffff;
}

.leftDiv {
  position: relative;
  top: 107px;
  width: 433px;
  height: 100%;
  left: 20px;
}


/* --左右栏样式-- */
.block_l1 {
  height: 937px;
  overflow: hidden;
}

.block_l2 {
  height: 235px;
  margin-top: 5px;
}


.cotent {
  background-repeat: no-repeat;
  background-position: center 10px;
  font-size: 0;
  transition: 0.5s;
}


.content_left {
  border-radius: 5px;
  overflow: hidden;
  transform: rotate(0deg);
  margin-top: 13.333px;
  height: 861px;
  margin-bottom: 27px;
}


.cotent .row1 {
  position: absolute;
  width: 100%;
}

.cotent .row1 .block {
  background-color: rgba(27, 80, 146, 0.9);
  padding: 10px;
  border-radius: 5px;
}

.cotent .row2 .block {
  background-color: rgba(19, 60, 117, 0.9);
  padding: 10px;
  border-radius: 5px;
}

.cotent .row1 .block div {
  margin-top: 1px;
}

.block_l1 .cotent .row1 .b1 div,
.block_l1 .cotent .row1 .b2 div {
  text-align: center;
  margin-top: 1px;
  height: 20px;
}

.block_l1 .cotent .row1 .b3 div,
.block_l1 .cotent .row1 .b4 div {
  height: 30px;
  line-height: 30px;
  text-align: center;
  margin-top: 1px;
}

.block_l1 .cotent .row1 .block .chart {
  position: relative;
  height: 50px;
  margin: 5px 0;
}

.block_l1 .b1 .mk,
.block_l1 .b2 .mk {
  color: #1990FF;
  position: absolute;
  font-size: 9px;
  width: 10px;
  height: 10px;
  text-align: center;
}

.block_l1 .b1 canvas,
.block_l1 .b2 canvas {
  position: absolute;
  top: 0px;
  left: 0px;
}

.block_l1 .b1 .pointer,
.block_l1 .b2 .pointer {
  position: absolute;
  width: 72px;
  height: 72px;
  top: 0px;
  left: 30px;
}

.cotent .row1 .block div span {
  padding: 0 0 0 2px;
}

.cotent .row1 .block .fs12 {
  padding: 0 0 0 25px;
}

.block_l1 .cotent .row1 .b5 .flex-box {
  display: flex;
  flex-direction: row;
  width: 100%;
  margin-top: 5px;
  height: 40px;
}

.block_l1 .cotent .row1 .b5 .flex-box div {
  flex: 1;
  border-right: 1px dashed #47aafd;
  text-align: center;
}

.block_l1 .cotent .row1 .b5 .flex-box div:last-child {
  border-right: none;
}

.block_l1 .cotent .row1 .b1 .fs12 {
  background: url(../img/a0-1_fb.png) no-repeat left center;
  -webkit-background-size: 15px;
  background-size: 15px;
}

.block_l1 .cotent .row1 .b2 .fs12 {
  background: url(../img/a0-1_passenger.png) no-repeat left center;
  -webkit-background-size: 15px;
  background-size: 15px;
}

.block_l1 .cotent .row1 .b3 .fs12 {
  background: url(../img/a0-1_nomflght.png) no-repeat left center;
  -webkit-background-size: 15px;
  background-size: 15px;
}

.block_l1 .cotent .row1 .up,
.block_l2 .cotent .row1 .up {
  padding-right: 10px;
  background: url(../img/arr_up2.png) no-repeat right center;
  -webkit-background-size: 8px;
  background-size: 8px;
}

.block_l1 .cotent .row1 .down,
.block_l2 .cotent .row1 .down {
  padding-right: 10px;
  background: url(../img/arr_down2.png) no-repeat right center;
  -webkit-background-size: 8px;
  background-size: 8px;
}

.block_l1 .cotent .row1 .b4 .fs12 {
  background: url(../img/a0-1_react.png) no-repeat left center;
  -webkit-background-size: 15px;
  background-size: 15px;
}

.block_l1 .cotent .row1 .b5 .fs12 {
  background: url(../img/a0-1_unnomflight.png) no-repeat left center;
  -webkit-background-size: 15px;
  background-size: 15px;
}

.block_l1 .cotent .row1 .b6 .fs18 {
  background: url(../img/a0-1_unnom.png) no-repeat left center;
  -webkit-background-size: 15px;
  background-size: 15px;
  background-position: 10px center;
  padding-left: 30px;
  line-height: 30px;

}

.block_l2 .cotent .row1 .b1 .fs12 {
  background: url(../img/a0-1_safe.svg) no-repeat left center;
  -webkit-background-size: 15px;
  background-size: 15px;
}

.block_l2 .cotent .row1 .b2 .fs12 {
  background: url(../img/a0-1_warn.png) no-repeat left center;
  -webkit-background-size: 15px;
  background-size: 15px;
}

.block_l1 .cotent .row1 .b1 {
  position: relative;
  float: left;
  width: 212px;
  height: 121px;
  margin: 0px 0px 5px 0px;
}

.block_l1 .cotent .row1 .b2 {
  position: relative;
  float: right;
  width: 212px;
  height: 80px;
  margin: 0px 0px 5px 0px;
}

.block_l1 .cotent .row1 .b3 {
  position: relative;
  float: left;
  width: 49%;
  height: 65px;
  margin: 0px 0px 5px 0px;
}

.block_l1 .cotent .row1 .b4 {
  position: relative;
  float: right;
  width: 49%;
  height: 65px;
  margin: 0px 0px 5px 0px;
}

.block_l1 .cotent .row1 .b5 {
  position: relative;
  float: left;
  width: 100%;
  height: 80px;
  margin: 0px 0px 5px 0px;
}

.block_l1 .cotent .row1 .b6 {
  position: initial;
  width: 100%;
  height: 330px;
  margin: 0px 0px 5px 0px;
  overflow: hidden;
  transition: 0.5s;
  margin-top: 10px;
}


#unsafe_detail {
  display: block;
  height: 15px;
  width: 15px;
  background: url(../img/a0-1_detail.svg) no-repeat;
  -webkit-background-size: 15px;
  background-size: 15px;
  cursor: pointer;
  position: absolute;
  right: 10px;
  top: 0px;
}

#unnormal_detail span {
  padding-right: 40px;
  cursor: pointer;

}

#unnormal_detail .detail {
  height: 16px;
  width: 16px;
  background: url(../img/a0-1_detail.svg) no-repeat;
  -webkit-background-size: 16px;
  background-size: 16px;
  cursor: pointer;
  position: absolute;
  right: 15px;
  top: 14px;
}

.block_l1 .cotent .row1 .b6 .b6-cont {
  position: relative;
  height: 68px;
}

.block_l1 .cotent .row1 .b6 .b6-cont .tit {
  position: absolute;
  left: 2px;
  top: 2px;
  width: 50px;
  height: 12px;
}

#chart_abnormal {
  position: absolute;
  width: 245px;
  height: 62px;
  top: 3px;
  left: 16px;
  background: url(../img/a0-1_runtimeevt.png) no-repeat;
  background-size: contain;
}

.block_l1 .cotent .row1 .b6 .extr {
  height: 135px;
  border-top: 1px dashed #1f5fa8;
  text-align: left;
  padding-top: 10px;
  position: relative;
}

.block_l1 .cotent .row1 .b6 .extr .tabcontent {
  position: absolute;
  margin-top: 10px;
  height: 175px;
  width: 100%;
  top: 0px;
  left: 0px;
  /*background-color: rgba(255, 0, 0, 0);*/
}


.events .btns {
  position: absolute;
  height: 20px;
  width: 50px;
  right: 5px;
  top: 5px;
}

.events .btns .pre-btn {
  position: absolute;
  display: block;
  height: 20px;
  width: 20px;
  left: 0;
  background: url(../img/a0-1_pre.png) no-repeat;
  -webkit-background-size: 20px;
  background-size: 20px;
  cursor: pointer;
}


.events .btns .next-btn {
  position: absolute;
  display: block;
  height: 20px;
  width: 20px;
  right: 0;
  background: url(../img/a0-1_next.svg) no-repeat;
  -webkit-background-size: 20px;
  background-size: 20px;
  cursor: pointer;
}

.block-header {
  text-align: center;
  font-size: 24px;
  font-weight: bold;
  height: 40px;
  background: url(../img/17_blockheader.png) no-repeat center top;
  padding: 0 0 0 0;
  background-size: 480px 40px;
  margin: 0;
  color: #0f214c;
  position: relative;
  line-height: 40px;
}

.block_l2 .cotent {
  height: auto;
}

.block_l2 .cotent .row1 {
  position: absolute;
  width: 100%;
}

.block_l2 .cotent .row1 .b1 {
  float: left;
  width: 100%;
  height: 40px;
  margin: 10px 0px 5px 0px;
}

.block_l2 .cotent .row1 .b2 {
  position: relative;
  overflow: hidden;
  float: left;
  width: 100%;
  height: 120px;
  margin: 0px 0px 5px 0px;
  transition: 0.5s;
}

.block_l2 .cotent .row1 .b2 .b2-cont {
  position: relative;
  height: 110px;
  text-align: left;
  display: flex;
  flex-direction: column;
  padding-bottom: 10px;
}

.block_l2 .cotent .row1 .b2 .b2-cont div {
  text-align: center;
}

.block_l2 .cotent .row1 .b2 .extr {
  height: 240px;
  border-top: 1px dashed #1f5fa8;
  text-align: left;
  padding-top: 10px;
  position: relative;
  display: flex;
  flex-direction: column;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 {
  flex: 3;
  display: flex;
  box-align: center;
  align-items: center;
  align-items: center;
  flex-direction: row;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .c {
  height: 100%;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .c:last-child {
  border-left: 1px dashed #1f5fa8;
  margin-left: 10px;
  padding-left: 5px;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .fs22 {
  line-height: 60px;
  background: url(../img/a0-1_warning.png) center center no-repeat;
  height: 100%;
  vertical-align: middle;
  -webkit-background-size: 60px;
  background-size: 60px;
  padding-top: 5px;
  width: 66px;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .up-items {
  height: 100%;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  flex-direction: column;
  width: 108px;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .up-items .items {
  width: 100%;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  flex-direction: row;
  flex: 1;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .up-items .items .item1 {
  flex: 1;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .up-items .items .item2 {
  flex: 3;
  text-align: right;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .up-items .items .icon {
  width: 20px;
  height: 20px;
  display: block;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .up-items .items .icon1 {
  background: url(../img/a0-1_human.png) center center no-repeat;
  -webkit-background-size: 18px;
  background-size: 18px;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .up-items .items .icon2 {
  background: url(../img/a0-1_mechine.png) center center no-repeat;
  -webkit-background-size: 18px;
  background-size: 18px;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r2 {
  flex: 1;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  flex-direction: row;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r2 .item1 {
  margin-right: 70px;
  padding-top: 4px;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r2 .item2 {
  flex: 2;
  padding-right: 5px;
  text-align: left;
}

.block_l3 .cotent .row1 .b1 {
  position: relative;
  float: left;
  width: 49%;
  height: 78px;
  margin: 0px 0px 5px 0px;
}

.block_l3 .cotent .row1 .b2 {
  position: relative;
  float: right;
  width: 49%;
  height: 78px;
  margin: 0px 0px 5px 0px;
}


/** 新样式 **/

.b-mainKpi {
  width: 233px;
  float: left;
  height: 129px;
  border-radius: 5px;
  margin: 0px 10px 13.333px 0px;
  background-color: rgba(27, 80, 146, 0.9);

}

.b-mainKpi .header {
  height: 50px;
  background: url(../img/a0-1_fb.svg) no-repeat;
  background-position-x: 18px;
  background-position-y: center;
  -webkit-background-size: 24px;
  background-size: 24px;
}

.b-mainKpi .header {
  font-size: 21px;
  padding-left: 50px;
  color: #44a3f4;
  line-height: 44px;
}

.b-mainKpi .header .subheader {
  font-size: 14px;
  padding-left: 4px;
  line-height: 48px;
}


.content_left .b-mainKpi {
  height: 114.667px;
}

.content_left .flightnum,
.content_left .rlyl {
  height: 229.333px;

}

.content_left .flightnum .flexBlock div {
  flex: none;
}

.content_left .flightnum .flgithDeatil {
  height: 110px;
  width: 176px;
  margin: 0 auto;

}

.content_left .flightnum .flgithDeatil .item {
  height: 43px;
  line-height: 43px;
  font-size: 18px;
  text-align: left;
}

.content_left .flightnum .flgithDeatil .head1 {
  color: #44a3f4;
  line-height: 16px;
  font-size: 16px;
  height: 16px;
  margin-top: 19px;
}

.b-mainKpi .item {
  height: 30px;
  line-height: 30px;
  font-size: 14px;
  text-align: left;
}

.b-mainKpi .remark {
  height: 30px;
  line-height: 30px;
  font-size: 14px;
  color: #44a3f4;
}

.b-mainKpi div {
  flex: 1;
  -webkit-box-sizing: content-box;
}

.content_left .flightnum .flgithDeatil .All {
  height: 43px;
  line-height: 43px;
  margin-top: 7px;
}

.content_left .flightnum .flgithDeatil .All .col3 {
  font-size: 18px;
}

.content_left .flightnum .flgithDeatil .All .col2 {
  font-size: 18px;
}

.content_left .flightnum .flgithDeatil .item .col1 {
  height: 20px;
  width: 44px;
  line-height: 20px;
  color: #44a3f4;
  text-align: left;
}

.content_left .flightnum .flgithDeatil .item .col2 {
  height: 20px;
  width: 74px;
  line-height: 20px;

}

.content_left .flightnum .flgithDeatil .item .col3 {
  height: 20px;
  width: 56px;
  line-height: 20px;
}

.content_left .flightnum .flgithDeatil .All {
  border-bottom: 0px dotted #00b7ee;
}

.flexBlock {
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  flex-direction: row;
}

.kpivalue {
  flex: 1;
  color: #FFFFFF;
  line-height: 30px;
  font-size: 18px;
  width: 76px;
  text-align: right;
}

.singlekpivalue {
  flex: 1;
  color: #FFFFFF;
  line-height: 30px;
  font-size: 18px;
  width: 76px;
  text-align: center;
  height: 30px;
}

.unit {
  flex: 1;
  color: #FFFFFF;
  line-height: 25px;
  font-size: 10px;
  text-align: left;
}

.b-mainKpi .tonghuanbi div {
  flex: initial;
}

.tonghuanbi {
  font-size: 14px;
  line-height: 30px;
}

.b-mainKpi .flexBlock .huanbi {
  width: 42px;
  color: #44a3f4;
  text-align: right;
  padding-right: 6px;
}

.b-mainKpi .flexBlock .tongbi {
  width: 44px;
  color: #44a3f4;
  text-align: right;
  padding-right: 6px;
}

.flexBlock .tongbiValue {
  color: #2ad805;
  width: 62px;
}

.flexBlock .huanbiValue {
  text-align: left;
  color: #2ad805;
  width: 54px;
}

.flexBlock .up {
  color: #2ad805;
}

.flexBlock .down {
  color: #ff0000;
}

.textCenter {
  text-align: center !important;
}

/** 小时收入**/

.xssr>div {
  font-size: 14px;
  line-height: 30px;
  color: #FFFFFF;
  box-sizing: content-box;
}

.xssr .col1 {
  width: 56px;
  color: #44a3f4;
  padding-right: 5px;
  padding-left: 10px;
}

.xssr .col2 {
  width: 50px;
  text-align: left;
}

.xssr .col3 {
  width: 56px;
  color: #44a3f4;
  padding-right: 4px;
}

.xssr .col4 {
  width: 50px;
  text-align: left;
}

/** 平均票价 **/


.pjpj .flexBlock .col1 {
  color: #44a3f4;
  padding-right: 4px;
  text-align: right;
  width: 42px;
  flex: initial;
}

.pjpj .flexBlock .col2 {
  width: 54px;
  text-align: left;
  flex: initial;
}

.pjpj .flexBlock .col3 {
  color: #44a3f4;
  padding-right: 4px;
  text-align: right;
  width: 54px;
  flex: initial;
}

.pjpj .flexBlock .col4 {
  width: 42px;
  text-align: left;
  flex: initial;
}

/** 左侧可用运力**/
.content_left .keyongyunli {
  width: 480px;
  height: 230px;
  background-color: rgba(27, 80, 146, 0.9);
  border-radius: 4px;
  position: relative;
  float: left;
  overflow: hidden;
}

.keyongyunli .chart>div {
  flex: initial;
}

.keyongyunli .total {
  width: 212px;
  height: 94px;
  top: -90px;
  text-align: center;
  position: relative;
  font-weight: normal;
  color: #FFFFFF;
  font-size: 18px;
}

.keyongyunli .total .yunliunit {
  font-size: 13px;
}

.keyongyunli .title {
  width: 480px;
  height: 30px;
}

.keyongyunli .title span {
  background: url(../img/a0-1_fb.png) no-repeat left center;
  -webkit-background-size: 15px;
  background-size: 15px;
  padding: 0 0 0 25px;
  width: 480px;
  line-height: 30px;
  font-size: 13px;
  margin-left: 10px;
}

.keyongyunli .chart {
  width: 480px;
  height: 140px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.keyongyunli .chart .yunliChartDom {
  width: 196px;
  height: 140px;
}

.keyongyunli .chart .legend {
  margin-top: 10px;
  height: 140px;
  width: 234px;
  overflow: hidden;
}

.keyongyunli .chart .legend .lg-row {
  position: relative;
  width: 114.667px;
  height: 24px;
  float: left;
}

.keyongyunli .chart .legend .lg-row .col {
  float: left;
  font-size: 14px;
  height: 24px;
  width: 54px;
  line-height: 24px;
  text-align: right;
}

.keyongyunli .chart .legend .lg-row .actype {
  width: 32px;
}

.keyongyunli .chart .legend .lg-row .actype>div {
  margin: 2px 0;
  height: 16px;
  border-radius: 4px 0px 4px 0px;
  background-color: #e95551;
  font-size: 14px;
  line-height: 16px;
  text-align: center;
}

.content_left .abnormal {
  width: 480px;
  height: 128px;
  position: relative;
}

.content_left .abnormal .flex-box {
  display: flex;
  flex-direction: row;
  width: 100%;
  margin-top: 5px;
  height: 44px;
}

.content_left .abnormal .flex-box div {
  flex: 1;
  border-right: 1px dashed #47aafd;
  text-align: center;
}

.content_left .abnormal .flex-box div span {
  padding: 0 0 0 2px;
}

.content_left .abnormal .title span {
  background: url(../img/a0-1_unnomflight.svg) no-repeat left center;
  -webkit-background-size: 24px;
  background-size: 24px;
  padding: 0 0 0 35px;
  width: 480px;
  line-height: 44px;
  font-size: 18px;
  margin-left: 15px;
}

.content_left .abnormal .more {
  width: 480px;
  line-height: 44px;
  font-size: 18px;
  text-align: right;
  top: -96px;
  position: relative;
}

.content_left .abnormal .more .text {
  line-height: 44px;
  font-size: 18px;
  padding-right: 10px;
}

/** 右侧**/
.block_r1 {
  position: absolute;
  right: 20px;
  top: 107px;
  width: 480px;
}

.block_r1 .row1 {
  margin-top: 13.333px;
}

.block_member {
  height: 264px;
  position: relative;
  margin-bottom: 27px;
}

.block_member .jinpeng {
  width: 480px;
  height: 264px;
  position: relative;
}

.block_member .jinpeng .total {
  width: 160px;
  height: 132px;
  top: -35px;
  left: 15px;
  text-align: center;
  position: relative;
  font-weight: normal;
  color: #FFFFFF;
  font-size: 18px;
}


.block_member .jinpeng .title {
  width: 480px;
  height: 44px;
}

.block_member .jinpeng .title span {
  background: url(../img/a0-1_clk.svg) no-repeat left center;
  -webkit-background-size: 15px;
  background-size: 24px;
  padding: 0 0 0 25px;
  width: 480px;
  line-height: 44px;
  font-size: 18px;
  margin-left: 20px;
}

.block_member .jinpeng .chart {
  width: 480px;
  height: 125px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.block_member .jinpeng .chart .jinpengChartDom {
  margin-top: 90px;
  margin-left: 15px;
  width: 160px;
  height: 150px;
}

.block_member .jinpeng .chart .legend {
  margin-top: 20px;
  height: 130px;
  width: 272px;
}

.block_member .jinpeng .chart .legend .lg-row {
  position: relative;
  width: 272px;
  height: 40px;
  line-height: 25px;
}

.block_member .jinpeng .chart .legend .lg-row .col {
  float: left;
  font-size: 14px;
  height: 25px;
  line-height: 25px;
  text-align: left;
}

.block_member .jinpeng .chart .legend .lg-row .card {
  width: 54px;
}

.block_member .jinpeng .chart .legend .lg-row .card>div {
  margin: 2px 0;
  width: 48px;
  height: 16px;
  border-radius: 6px 0px 6px 0px;
  background-color: #e95551;
  font-size: 14px;
  line-height: 16px;
  text-align: center;
}


.block_member .nums {
  width: 104px;
  padding-left: 4px;
  font-size: 14px;
}

.block_member .percentage {
  width: 70px;
  padding-left: 10px;
  font-size: 14px;
  width: 100px;
}

.block_member .percentage span {
  color: #44a3f4
}

.zongshouru {
  width: 480px;
  height: 65px;
}

.zongshouru .header .flexBlock {
  font-size: 14px;
  line-height: 50px;
}

.zongshouru .header .flexBlock>span {
  display: block;
  height: 100%;
}

.zongshouru .header .flexBlock .labelName {
  color: #44a3f4;
  font-size: 18px;
  width: 66px;
  text-align: left;
  line-height: 50px;
}

.zongshouru .header .flexBlock .value {
  width: 107px;
  text-align: left;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  flex-direction: row;
}

.zongshouru .header .flexBlock .value .shouruValue {
  text-align: left;
  font-size: 18px;
  color: #FFFFFF;
  height: 50px;

}

.zongshouru .header .flexBlock .value .srunit {
  height: 50px;
  color: #FFFFFF;
  font-size: 14px;
  line-height: 54px;
}

.zongshouru .header .flexBlock .tongbi {
  color: #44a3f4;
  width: 38px;
}

.zongshouru .header .flexBlock .tongbiValue {
  width: 158px;
  padding-left: 4px;
}

.zongshouru .header .flexBlock .detail {
  width: 17px;
  height: 17px;
  background: url(../img/a0-1_detail.svg) no-repeat;
  background-size: 17px;
  cursor: pointer;
}


.nomagin {
  margin: 0px 0px 13.333px 0px;
}

.all-row {
  width: 100%;
}

.block_r1 .row1 {
  position: relative;
  height: 340px;
}

.block_r1 .row2 {
  position: relative;
  margin-top: 27px;
}

.block_r1 .row2 .companydata-container {
  height: 520px;
  width: 616px;
  position: relative;
  float: left;
}

.block_r1 .row2 .companydata-container .companydata {
  height: 520px;
  width: 606px;
  background-color: rgba(19, 60, 117, 0.9);
  border-radius: 5px;
}

.block_r1 .row2 .companyDetail {
  height: 520px;
  width: 403px;
  position: relative;
  float: left;
  background-color: rgba(19, 60, 117, 0.9);
  border-radius: 5px;
}

/*-------*/

/* --底部-- */
.footer .flight-status {
  width: 200px;
  height: 60px;
  position: absolute;
  top: 1300px;
  left: 509px;
  display: flex;
  flex-direction: row;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  font-size: 10px;
}

.footer .flight-status .item1 {
  height: 100%;
  flex: 1;
  background: url(../img/a0-1_flightstatus.svg) center no-repeat;
  border-radius: 50%;
  background-color: #2a468d;
  -webkit-background-size: 48px;
  background-size: 48px;
}

.footer .flight-status .item2 {
  padding-left: 5px;
  height: 100%;
  flex: 2;
}

.footer .flight-status .item2 div {
  height: 50%;
}

.footer .flight-status .item2 div span {
  display: inline-block;
  height: 100%;
  line-height: 25px;
  padding-left: 25px;
  margin-right: 5px;
  background: url(../img/a0-1_zhengchang.svg) left center no-repeat;
  -webkit-background-size: 18px;
  background-size: 18px;
}

.footer .flight-status .item2 div span:last-child {
  background: url(../img/a0-1_yanwu.svg) left center no-repeat;
  -webkit-background-size: 18px;
  background-size: 18px;
}

.footer .map-btns {
  width: 100px;
  height: 60px;
  position: absolute;
  bottom: 40px;
  right: 370px;
}

.footer .map-btns img {
  display: inline-block;
  height: 60px;
  width: 44px;
}

.footer .map-btns .m3d {
  float: left;
  cursor: pointer;
}

.footer .map-btns .m2d {
  float: right;
  cursor: pointer;
}

.footer .c-tels {
  width: 285px;
  position: absolute;
  bottom: 38px;
  right: 20px;
}

.footer .c-tels .title {
  padding-left: 20px;
  background: url(../img/a0-1_tel.png) left center no-repeat;
  -webkit-background-size: 15px;
  background-size: 15px;
  margin-bottom: 10px;
}


.footer .c-tels div .name {
  display: inline-block;
  margin-right: 10px;
  margin-top: 2px;
  width: 135px;
}

.footer .c-tels div .num {
  display: inline-block;
  width: 100px;
}



.footer .execac {
  position: absolute;
  height: 55px;
  left: 725px;
  top: 1300px;
  padding-left: 66px;
}

.footer .execac .icon {
  position: absolute;
  top: 0;
  left: 0;
  width: 60px;
  height: 60px;
  background: url(../img/a0-1_fbzcl.svg) no-repeat center;
  border-radius: 50%;
  background-color: #2a468d;
}

.footer .execac .blk {
  display: block;
  padding-bottom: 10px;
}

#abnormal_total {
  position: absolute;
  width: 30px;
  height: 10px;
  top: 29px;
  left: 30px;
  text-align: center;
  color: white;
  font-size: 14px;
}

#abnormal_hb {
  position: absolute;
  width: 130px;
  height: 20px;
  top: 49px;
  left: 40px;
  text-align: center;
}

#abnormal_main {
  position: absolute;
  width: 150px;
  height: 40px;
  top: 15px;
  left: 157px;
}

#abnormal_main .ico {
  width: 28px;
  height: 30px;
  background: url(../img/a4.2_ico_evt1.png) left center no-repeat;
}

#abnormal_main_val span {
  display: block;
  margin-right: 6px;
}

#abnormal-detail-block {
  top: 640px;
}


.up_red {
  background: url(../img/arr_up_red.png) no-repeat right 2px;
}

.down_green {
  background: url(../img/arr_down_green.png) no-repeat right 2px;
}


.block_r .tabc {
  position: initial;
  width: 100%;
  height: 340px;
  margin-top: 10px;
}

.block_r .tabc div {
  text-align: left;
}

.block_r .tabc span {
  padding: 0;
}

.block_r .tabc1 .legend {
  font-size: 11px;
  color: #5d9ae3;
}

.block_r .tabc1 .legend .itm {
  margin-right: 3px;
  display: inline-block;
  cursor: pointer;
  user-select: none;
}

.block_r .tabc1 .legend .dot {
  font-size: 20px;
  vertical-align: middle;
}

.block_r .tabc1 .legend .lb {
  color: #5d9ae3;
  vertical-align: middle;
}

.block_r .tabc2 .legend {
  font-size: 11px;
  color: #5d9ae3;
}

.block_r .tabc1 .legend .lbl,
.block_r .tabc2 .legend .lbl {
  text-align: center;
  border-radius: 3px;
  padding: 2px;
}

.block_r .tabc2 .legend .itm {
  margin-right: 6px;
  cursor: pointer;
  user-select: none;
}

.block_r .tabc2 .legend .dot {
  font-size: 20px;
  vertical-align: middle;
}

.block_r .tabc2 .legend .lb {
  color: #5d9ae3;
  vertical-align: middle;
}

.block_r .tabc1 .legend .lst,
.block_r .tabc2 .legend .lst {
  display: block;
  margin-left: -5px;
  line-height: 18px;
}

.block_r .tabc2 .legend .lst2 {
  display: block;
  margin-left: -5px;
  line-height: 16px;
}


#scrollpane1 {
  position: relative;
  width: 94%;
  height: 260px;
  overflow: hidden;
  background-color: rgba(255, 0, 0, 0);
}

#scrollpane2 {
  position: absolute;
  top: 76px;
  left: 0;
  width: 100%;
  height: 150px;
  overflow: hidden;
  background-color: rgba(255, 0, 0, 0);
}

.block_r .scrollcontent {
  width: 100%;
}

.block_r .scrollcontent .comprow {
  width: 290px;
  height: 136px;
  overflow: hidden;
  transform: rotate(0deg);
}

.block_r .scrollcontent .comprow .head {
  width: 100%;
  height: 32px;
}

.block_r .scrollcontent .comprow .head .tt {
  position: absolute;
  left: 0;
  width: 200px;
  height: 24px;
  color: #7dc0ff;
  line-height: 25px;
  background-position: 0 0;
  background-repeat: no-repeat;
  background-size: contain;
}

.block_r .scrollcontent .comprow .head .tt .lb {
  margin-left: 32px;
  font-size: 14px;
  font-weight: bold;
}

.block_r .scrollcontent .comprow .head .tt .num {
  margin-left: 5px;
  font-size: 12px;
}

.block_r .scrollcontent .comprow .head .btns {
  float: right;
  text-align: right;
}

.block_r .scrollcontent .comprow .head .btn {
  display: inline-block;
  margin-right: 4px;
  height: 20px;
  width: 20px;
  padding: 0;
  cursor: pointer;

}



.block_r .scrollcontent .comprow .head .btn_prev {
  background: url(../img/a0-1_pre.png) no-repeat;
  background-size: contain;
}

.block_r .scrollcontent .comprow .head .btn_prev.prev-disabled {
  background: url(../img/a0-1_pre-dis.png) no-repeat;
  background-size: contain;
  cursor: default;
}

.block_r .scrollcontent .comprow .head .btn_next {
  background: url(../img/a0-1_next.png) no-repeat;
  background-size: contain;
}

.block_r .scrollcontent .comprow .head .btn_next.next-disabled {
  background: url(../img/a0-1_next-dis.png) no-repeat;
  background-size: contain;
  cursor: default;
}


.block_r .scrollcontent .comprow .itmlst {
  position: absolute;
  top: 35px;
  left: 0;
  height: 88px;
  width: 200000px;
}

.block_r .scrollcontent .comprow .itmlst .blk {
  position: relative;
  display: inline-block;
  width: 68px;
  height: 83px;
  margin-right: 5px;
  border-radius: 5px;
  overflow: hidden;
  transform: rotate(0);
  cursor: pointer;


  background: #183e7c;


}

.block_r .scrollcontent .comprow .itmlst .blk .time {
  color: #4d9cfd;
  font-size: 9px;
  margin: 6px 5px 3px 5px;
}

.block_r .scrollcontent .comprow .itmlst .blk .time .r {
  float: right;
}

.block_r .scrollcontent .comprow .itmlst .blk .fltno {
  text-align: center;
  font-size: 12px;
  font-weight: bold;
  color: #aecffd;
  margin: 3px;
}

.block_r .scrollcontent .comprow .itmlst .blk .city {
  width: 100%;
  text-align: center;
  font-size: 10px;
  color: #4d9cfd;
  height: 22px;
}

.block_r .scrollcontent .comprow .itmlst .blk .bot {
  position: absolute;
  width: 100%;
  bottom: 4px;
  text-align: center;
  font-size: 9px;
  color: #046cd6;
  text-overflow: ellipsis;
  white-space: nowrap;
}



/* ------------------------- */

.pop {
  position: absolute;
  width: 260px;
  height: 500px;
  box-shadow: 0 0 2px #000, 0 0 10px rgba(0, 0, 0, 0.5);
  border-radius: 5px;
  z-index: 9999;
}

.block-frame {
  position: relative;
  width: 480px;
}

.tabs {
  position: relative;
  height: 30px;
  text-align: center;
  margin-bottom: 13.333px;
}

.tabs .tab {
  position: absolute;
  width: 72px;
  height: 30px;
  line-height: 30px;
  font-size: 16px;
  background-color: #073b77;
  color: #2a81d2;
  border-radius: 15px;
  pointer-events: auto;
  cursor: pointer;
}

.keyongyunli .tabs {
  margin-top: 10px;
  margin-right: 44px;
}

.keyongyunli .tabs .tab {
  width: 88px;
}

.tabs .selected {
  background-color: #00a0ea;
  color: #fff;
}

.tabs .keyong {
  left: 117px;
}

.tabs .zaice {
  left: 18px;
}

.tabs .huoyun {
  left: 80px;
}

.datetype-container {
  display: flex;
  height: 24px;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  flex-direction: row;
  right: 0px;
  bottom: 0px;
  position: absolute;
}

.datetype-container .dateType {
  font-size: 14px;
  height: 24px;
  width: 24px;
  margin-left: 10px;
  border-radius: 12px;
  color: #44a3f4;
  line-height: 24px;
  background-color: #094a91;
  cursor: pointer;
}

.datetype-container .selected {
  color: #FFFFFF;
  background-color: #00a0e9;
}

.block_service {
  position: relative;
}

.block_service .tsl {
  position: relative;
  margin-top: 13.333px;
  width: 100%;
}

.block_service .tsl .b-mainKpi {
  width: 100%;
}
.jftsl{
  height:214.6666px ;
}

.block_service .jftsl .flexBlock {
  height: 83px;
  width: 133px;
  margin-top: 60px;
  margin-left: 80px;
  display: block;
  background: url(../img/a0-1_complain.svg) no-repeat center;
  background-size: 133px 83px;
}

.block_service .jftsl .flexBlock .text {
  width: 160px;
  margin-left: 160px;
  font-size: 20px;
  margin-top: 13.333px;
}


#pop_company_shouru_mask {
  background-color: #000000;
  opacity: 0.6;
  position: fixed;
  width: 1920px;
  height: 1440px;
}

.pop-window {
  position: absolute;
  z-index: 4000;
  width: -moz-fit-content;
  height: -moz-fit-content;
  width: -webkit-fit-content;
  height: -webkit-fit-content;
  width: fit-content;
  height: fit-content;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #151534;
  background-color: rgba(21, 21, 52, 0.8);
  border: solid 1px #00b7ee;
  box-shadow: 0px 0px 50px #00b7ee73 inset;
  -webkit-box-shadow: 0px 0px 50px #00b7ee73 inset;
  border-radius: 10px;
  overflow: hidden;
  min-width: 1024px;
}

#pop_company_shouru {
  position: absolute;
  margin: 100px auto;
  width: 1560px;
  height: 940px;
  left: 180px;
  background-color: #151534;
  background-color: rgba(21, 21, 52, 0.8);
  border: solid 1px #00b7ee;
  box-shadow: 0px 0px 50px #00b7ee73 inset;
  -webkit-box-shadow: 0px 0px 50px #00b7ee73 inset;
  border-radius: 10px;
  overflow: hidden;
  z-index: 4000;
}


#pop_company_shouru .shouruWinBody {
  position: relative;
  height: 100%;
  width: 100%;
}

#pop_company_shouru .shouruWinBody .titleSharpe,
.popWin .titleSharpe {
  margin: 0 auto;
  width: 401px;
  height: 15px;
  background: url(../img/a0-1_title_zs.png) no-repeat;
  background-size: 401px 15px;
}

#pop_company_shouru .shouruWinBody .win-footer,
.popWin .win-footer {
  position: absolute;
  bottom: 0;
  height: 7px;
  width: 100%;
}

#pop_company_shouru .shouruWinBody .bottomSharpe,
.popWin .bottomSharpe {
  margin: 0 auto;
  width: 201px;
  height: 8px;
  background: url(../img/a0-1_bottom_zs.png) no-repeat;
  background-size: 201px 8px;
  bottom: 0;
}

#pop_company_shouru .shouruWinBody .title,
.popWin .title {
  position: relative;
  height: 65px;
  font-size: 20px;
  line-height: 60px;
  text-align: center;
  width: 50%;
  margin: 0 auto;
}

#pop_company_shouru .shouruWinBody .closeBtn,
.popWin .closeBtn {
  background: url(../img/a0-1_close.svg) no-repeat center;
  height: 50px;
  width: 50px;
  background-size: 15px;
  top: 5px;
  right: 5px;
  position: absolute;
  cursor: pointer;
  z-index: 5000;
}

#pop_company_shouru .shouruWinBody .tableDiv {
  width: auto;
  height: auto;
  min-height: 777px;
  background-color: #1b5092;
  background-color: rgba(27, 80, 146, 0.5);
  border-radius: 5px;
  margin: 0 20px;
  margin-bottom: 10px;
}

#pop_company_shouru .shouruWinBody .table-container {
  width: 100%;
  height: auto;
  overflow-x: auto;
}

.tableDiv .line {
  height: 1px;
  background-image: linear-gradient(#26bafd,
      #26bafd),
    linear-gradient(#000000,
      #000000);
  background-blend-mode: normal,
    normal;
  opacity: 0.35;
}

.tableDiv .tbrow {
  height: 40px;
  line-height: 40px;
  display: inline-flex;

  position: relative;
}

.tableDiv .tbheader {
  height: 80px;
  line-height: 1.2;
  position: relative;
}

.tableDiv .hightlightRow {
  background-color: rgba(68, 163, 244, 0.3);
}

.tableDiv .hightlightCol {
  background-color: rgba(68, 163, 244, 1);
}

.tableDiv .hightlightCol2 {
  background-color: rgba(68, 163, 244, 0.1);
}

.tableDiv .tbrow .tbcol {
  width: 180px;
  height: 40px;
  font-size: 14px;
  letter-spacing: 0px;
  color: #ffffff;
  text-align: right;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border-bottom: 1px solid rgba(38, 186, 253, 0.5);
}

.tbcol img {
  width: 10px;
  height: 16px;
  position: absolute;
  right: 20px;
  top: 32px;
}

.tableDiv .tbrow .tbcol2 {
  width: 125px;
}

.tableDiv .tbrow .tbcol3 {
  width: 120px;
}

#tbody .tbrow .tbcol span {
  display: inline-block;
  width: 60px;
  color: #908d8d;
}

.border-right {
  border-right: 1px solid rgba(38, 186, 253, 0.5);
}

.col-border {
  border-right: 1px solid rgba(38, 186, 253, 0.5);
}

.tableDiv .tbheader .tbcol {
  font-size: 16px;
  padding-right: 10px;
  height: 80px;
}

.tableDiv .tbheader .tb2Floor {
  font-size: 16px;
  padding-right: 0px;
  height: 80px;
  line-height: 40px;
  width: 500px;
}

.tableDiv .tbheader .tb2Floor-escaped {
  font-size: 16px;
  padding-right: 0px;
  height: 80px;
  line-height: 1.2;
  width: 125px;
}

.tableDiv .tbheader .tb2Floor .subheader {
  height: 40px;
  line-height: 1.2;
  text-align: center;
  width: 500px;
  font-size: 0px;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.tableDiv .tbheader .tb2Floor-escaped .subheader {
  height: 40px;
  line-height: 1.2;
  text-align: center;
  width: 125px;
  font-size: 0px;
}

.tableDiv .tbheader .tb2Floor .subheader img {
  top: unset;
  position: unset;
}

.tableDiv .tbheader .tb2Floor .subheader .topHeader {
  width: 100%;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.tableDiv .tbheader .tb2Floor .subheader>div {
  font-size: 16px;
  flex: 1;
}

.tableDiv .tbheader .tbcol .subTitle {
  font-size: 14px;
}

.tableDiv .tbrow .tbcol .subTitle {
  font-size: 14px;
}

.tableDiv .tbrow .tbcol:first {
  text-align: center;
}

/** 客运货运**/
.ky .kyitem {
  display: block;
}

.ky .hyitem {
  display: none;
}

.hy .kyitem {
  display: none;
}

.hy .hyitem {
  display: block;
}

.passenger .header {
  background-image: url(../img/a0-1_passenger.svg);
}

.rlyl .header {
  background-image: url(../img/a0-1_rlyl.svg);
}

.nomflght .header {
  background-image: url(../img/a0-1_fbzcl.svg);
}


.kzl .header {
  background-image: url(../img/a0-1_kzl.svg);
}

.zongshouru .header {
  background-image: url(../img/a0-1_zsr.svg);
}

.butie .header {
  background-image: url(../img/a0-1_butie.svg);
}

.fuying .header {
  background-image: url(../img/a0-1_fuying.svg);
}

.keshou .header {
  background-image: url(../img/a0-1_keshou.svg);
}


.huoshou .header {
  background-image: url(../img/a0-1_huoshou.svg);
}

.xssr .header {
  background-image: url(../img/a0-1_xssr.svg);
}

.zglsr .header {
  background-image: url(../img/a0-1_zglsr.svg);
}

.pjpj .header {
  background-image: url(../img/a0-1_pjpj.svg);
}

.kglsr .header {
  background-image: url(../img/a0-1_kglsr.svg);
}

.keyongyunli .header {
  background-image: url(../img/a0-1_kyyl.svg);
}

.quanhuoji .header {
  background-image: url(../img/a0-1_quanhuoji.png);
}

.fucang .header {
  background-image: url(../img/a0-1_fucang.png);
}

.klh .header {
  background-image: url(../img/a0-1_klh.png);
}

.huoyounums .header {
  background-image: url(../img/a0-1_hyysl.svg);
}


/** 不安全**/
.block_unsafe {
  position: relative;

}

.block_unsafe .cotent {
  margin-top: 5px;
  width: 309px;
  height: 160px;
  background-color: #1b5092;
  border-radius: 4px;
}


.block_unsafe .unsafeEvent {
  width: 480px;
  height: 317.333px;
  position: relative;
  margin-top: 10px;
}

.block_unsafe .unsafeEvent .total {
  width: 174px;
  height: 132px;
  top: -91px;
  text-align: center;
  position: relative;
  font-weight: normal;
  color: #FFFFFF;
  font-size: 13px;
}


.block_unsafe .unsafeEvent .title {
  width: 480px;
  height: 44px;
}

.block_unsafe .subtitle {
  color: #44a3f4;
  font-size: 16px;
  height: 68px;
  line-height: 60px;
  padding-left: 14px;
}


.block_unsafe .unsafeEvent .title .titleSpan {
  background: url(../img/a0-1_safe.png) no-repeat left center;
  -webkit-background-size: 24px;
  background-size: 24px;
  padding: 0 0 0 35px;
  line-height: 44px;
  font-size: 22px;
  margin-left: 10px;
  color: #44a3f4;
}

.unsafeEvent .detail {
  width: 17px;
  height: 44px;
  margin-right: 15px;
  background: url(../img/a0-1_detail.svg) no-repeat center;
  background-size: 17px;
  cursor: pointer;
  float: right;
}

.unsafeEvent .reason-detail .markRed {
  color: red;
}

.unsafeNums {
  position: relative;
  left: 154px;
  color: #ffffff;
  text-align: left;
  height: 28px;
  top: -28px;
  width: 200px;

}

.unsafeNums .value {
  font-size: 18px;
  line-height: 20px;

}

.unsafeNums .unit {
  font-size: 14px;
}

.reason-detail>div {
  height: 48px;
  text-align: center;
  border-right: 1px dotted #0068b7;
}

.reason-detail .reasonType {
  color: #44a3f4;
  font-size: 14px;
}

.reason-detail .reasonNums {
  color: #ffffff;
  font-size: 18px;
}

.border-none {
  border: none !important;
}

.dateCmp {
  color: initial;
  width: 150px;
  height: 36px;
  background-color: transparent;
  border-radius: 5px;
  border: solid 1px rgba(0, 183, 238, 0.2);
  margin: 0;
}

#header .dateCmp {
  top: 25px;
  position: absolute;
  left: 338px;
}


.dateCmp .form-control {
  width: 112px;
  height: 36px;
  font-size: 14px;
  line-height: 36px;
  letter-spacing: 0px;
  color: #44a3f4;
  border: none;
  padding: 10px 10px;
  background-color: transparent;
}

.dateCmp .input-group-addon {
  background-color: transparent;
  color: #2a81d2;
  border: none;
  height: 32px;
}

#companycombo .box {
  background: url(../img/combo_arr.png) no-repeat 103px 13px;
  background-color: transparent;
  color: #44a3f4;
  padding: 0px 0 0 10px;
  line-height: 36px;
}

#companycombo {
  left: 0px;
  left: initial;
  height: 36px;
}

#companycombo .box {
  height: 36px;
  border: solid 1px rgba(0, 183, 238, 0.2);
}

#ctrl_user {
  top: -4px;
  right: 7px;
  width: 44px;
  height: 44px;
  cursor: pointer;
  background: url(../img/17_nav_ico_log.png) no-repeat center center, url(../img/17_nav_icobg.png) no-repeat center center;
}

.kpiunit {
  font-size: 14px;
}

.dateDesc {
  font-size: 12px;
  color: #44a3f4;
}

.fs18 {
  font-weight: normal;
}

.rlyl .flexBlock {
  height: 48px;
  line-height: 48px;

}

.rlyl .flexBlock .col1 {
  height: 50px;
  line-height: 50px;
  width: 50%;
  color: #44a3f4;
  padding-left: 30px;
  font-size: 18px;
}

.rlyl .flexBlock .col2 {
  height: 50px;
  line-height: 50px;
  width: 50%;
  font-size: 18px;
}

.fs12 {
  font-size: 12px !important;
}

.b-mainKpi .header .detail {
  width: 17px;
  height: 44px;
  margin-right: 15px;
  background: url(../img/a0-1_detail.svg) no-repeat center;
  background-size: 17px;
  cursor: pointer;
  float: right;
}

#pop_flight_noraml_rate {
  width: 60%;
  height: 540px;
  position: absolute;
  top: 26%;
  left: 20%;
  background-color: #151534;
  opacity: 0.8;
  border: solid 1px #00b7ee;
  box-shadow: 0px 0px 50px #00b7ee73 inset;
  -webkit-box-shadow: 0px 0px 50px #00b7ee73 inset;
  border-radius: 10px;
  overflow: hidden;
  z-index: 4000;
}

.popWin .date-componet {
  position: absolute;
  top: 16px;
  right: 68px;
  z-index: 4001;
}

.popWin .dateType {
  float: left;
  width: 200px;
  height: 36px;
  font-size: 14px;
  text-align: center;
  overflow: hidden;
  color: #44a3f4;
  border-radius: 5px;
  border: solid 1px rgba(0, 183, 238, 0.2);
}

.popWin .dateType>div {
  line-height: 36px;
  width: 50px;
  cursor: pointer;
  user-select: none;
}

#pop_flight_noraml_rate {
  width: 1600px;
  left: 160px;
}

.flightNormalRateContainer .ecdom {
  width: 1600px;
  height: 370px;
}

.popWin .dateCmp {
  float: left;
  margin-left: 10px;
}

.flightNormalRateContainer .legends {
  display: -webkit-box;
  -webkit-box-pack: center;
  width: 100%;
  font-size: 14px;
  color: #44a3f4;
  height: 20px;
}

.flightNormalRateContainer .legends>div {
  padding-left: 20px;
  padding-right: 20px;
}

.flightNormalRateContainer .legend {
  display: -webkit-box;
  -webkit-box-pack: center;
  -webkit-box-align: center;
}

.flightNormalRateContainer .legends .legend .lengend-icon {
  width: 12px;
  height: 12px;
  line-height: 20px;
  background-color: #f39800;
  border-radius: 1px;
  margin-right: 4px;
}

.lengend-color-green {
  background-image: linear-gradient(0deg, #00798f 0%, #00bc54 100%), linear-gradient(#009fec, #009fec);
  background-blend-mode: normal, normal;
}

.lengend-color-blue {
  background-image: linear-gradient(0deg, #00798f 0%, #0765f1 100%), linear-gradient(#009fec, #009fec);
  background-blend-mode: normal, normal;
}

.flightNormalRateContainer .legends .legend3 .lengend-icon {
  background: url(../img/a3.2.legend2.png) no-repeat 0 center;
  width: 20px;
  background-size: 20px 1px;

}

.dateCmp .datetimepicker {
  height: 36px;
}

.popWin .combobox_list {
  width: 150px;
}

.datetimepicker .hide {
  display: none !important;
}

.otherCmpData {
  display: -webkit-box;
  -webkit-box-pack: center;
  -webkit-box-align: center;
  font-size: 14px;
}

.otherCmpData .companyName {
  color: #44a3f4;
  margin-right: 5px;
  margin-left: 10px;
}



/**** Style for popup window with tabs*****/
.color-green {
  color: #00ba53;
}

.color-blue {
  color: #44a3f4;
}

.color-white {
  color: #fff;
}

.tabs-container {
  font-size: 14px;
  margin: 0px 40px 40px 40px;
}

.tabs-h-container {}

.tabs-h-container> :not(:first-child) {
  margin-left: 40px;
}

.tab-h-item-container {
  display: inline-block;
  vertical-align: top;
}

.tab-h-header {
  height: 40px;
  background-repeat: repeat-x;
  position: relative;
}

.tab-h-title {
  text-align: center;
  width: 140px;
  height: 40px;
  margin: auto;
  padding-top: 8px;
  font-size: 16px;
  line-height: 20px;
}

.tab-h-large {
  width: 504px;
}

.tab-h-small {
  width: 240px;
}

.tab-h1 {
  background-image: url(../img/a0-1-tab-h1-bg.png);
}

.tab-h1 .tab-h-title {
  background-image: url(../img/a0-1-tab-h1.png);
  background-repeat: no-repeat;
}

.tab-h2 {
  background-image: url(../img/a0-1-tab-h2-bg.png);
}

.tab-h2 .tab-h-title {
  background-image: url(../img/a0-1-tab-h2.png);
  background-repeat: no-repeat;
}

.tab-h3 {
  background-image: url(../img/a0-1-tab-h3-bg.png);
}

.tab-h3 .tab-h-title {
  background-image: url(../img/a0-1-tab-h3.png);
  background-repeat: no-repeat;
}

.tab-h-unit {
  font-size: 12px;
  color: #44a3f4;
  position: absolute;
  right: 5px;
  bottom: 0px;
}

.tab-h-section-title {
  height: 55px;
  padding-top: 20px;
}

.tab-h-section-title>div {
  display: inline-block;
}

.tab-h-section-title .title-dott {
  height: 13px;
  width: 12px;
  display: inline-block;
}

.tab-h-section-title .title-dott>div {
  height: 12px;
  width: 12px;
  transform: rotate(-90deg);
}

.title-left {
  color: #44a3f4;
  width: 290px;
  padding-left: 55px;
}

.title-right {
  color: #25a15e;
}

.tab-h-section-title .title-name {
  display: inline-block;
  padding-left: 10px;
}

.tab-item-title {
  margin-bottom: 10px;
  font-size: 16px;
}

.tab-data-title {
  color: #44a3f4;
  padding-bottom: 5px;
}

.tab-data-title>div,
.tab-data-row>div {
  display: inline-block;
  height: 25px;
  border: 0px;
}

.company-name {
  color: #44a3f4;
  width: 45px;
  text-align: right;
  margin-right: 10px;
}

.data-bar {
  margin-right: 10px;
  width: 85px;
}

.data-name {
  width: 50px;
}

.data-perc {
  width: 50px;
  text-align: right;
}

.data-splitter {
  width: 25px;
}

.tab-data-row .data-bar {
  height: 10px;
  border-radius: 5px;
  background-color: rgba(68, 163, 244, 0.2);
}

.data-bar .data-bar-progress {
  height: 10px;
  border-radius: 5px;
}

.data-bar-blue {
  background: -moz-linear-gradient(left, rgba(68, 72, 244, 1) 0%, rgba(108, 187, 255, 1) 100%);
  background: -webkit-linear-gradient(left, rgba(68, 72, 244, 1) 0%, rgba(108, 187, 255, 1) 100%);
  background: linear-gradient(to right, rgba(68, 72, 244, 1) 0%, rgba(108, 187, 255, 1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#4448f4', endColorstr='#6cbbff', GradientType=1);
}

.data-bar-green {
  background: -moz-linear-gradient(left, rgba(24, 123, 69, 1) 0%, rgba(54, 213, 127, 1) 100%);
  background: -webkit-linear-gradient(left, rgba(24, 123, 69, 1) 0%, rgba(54, 213, 127, 1) 100%);
  background: linear-gradient(to right, rgba(24, 123, 69, 1) 0%, rgba(54, 213, 127, 1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#36d57f', endColorstr='#187b45', GradientType=1);
}

.tab-v1 {
  background-image: url(../img/a0-1-tab-v1-bg.png);
  background-repeat: repeat-y;
}

.tab-v1 .tab-v-title {
  background-image: url(../img/a0-1-tab-v1.png);
  background-repeat: no-repeat;
}

.tab-v2 {
  background-image: url(../img/a0-1-tab-v2-bg.png);
  background-repeat: repeat-y;
}

.tab-v2 .tab-v-title {
  background-image: url(../img/a0-1-tab-v2.png);
  background-repeat: no-repeat;
}

.tab-v3 {
  background-image: url(../img/a0-1-tab-v3-bg.png);
  background-repeat: repeat-y;
}

.tab-v3 .tab-v-title {
  background-image: url(../img/a0-1-tab-v3.png);
  background-repeat: no-repeat;
}

.tab-v-row {
  position: relative;
}

.tabs-v-container .tab-v-row:not(:first-child) {
  margin-top: 20px;
}

.tab-v-title {
  display: inline-block;
  width: 40px;
  height: 120px;
  padding: 30px 0 0 10px;
  font-size: 16px;
  line-height: 20px;
  position: absolute;
  top: calc(50% - 60px);
}

.tab-v-item-list {
  margin: 0px -10px 0px 40px;
  display: table;
  border-spacing: 10px 0px;
}

.tab-v-item {
  display: table-cell;
  background-color: #1b509266;
  border-radius: 10px;
  padding: 20px 18px 10px 18px;
  vertical-align: top;
}

.tab-v-item .company-name {
  text-align: left;
  width: 58px;
}

.tab-v-item .data-name {
  width: 56px;
}

/* Style for actype-company */
.aircraft-tab .tabs-h-container> :not(:first-child) {
  margin-left: 10px;
}

.diamond-box-list {
  margin-top: 20px;
  max-width: 500px;
}

.diamond-box {
  width: 156px;
  height: 98px;
  margin-right: 10px;
  display: inline-block;
  padding: 15px 15px 0px 15px;
  opacity: 0.5;
  margin-top: 10px;
}

.diamond-box.active {
  opacity: 1;
}

.diamond-box-list .diamond-box:nth-child(1) {
  margin-left: 0px;
}

.diamond-box-gray .diamond-box.activeX {
  background-image: url(../img/a0-1-diamond-gray-active.png);
}

.diamond-box-blue .diamond-box.activeX {
  background-image: url(../img/a0-1-diamond-blue-active.png);
}

.diamond-box-green .diamond-box.activeX {
  background-image: url(../img/a0-1-diamond-green-active.png);
}

.diamond-box-gray .diamond-box {
  background-image: url(../img/a0-1-diamond-gray-active.png);
}

.diamond-box-blue .diamond-box {
  background-image: url(../img/a0-1-diamond-blue-active.png);
}

.diamond-box-green .diamond-box {
  background-image: url(../img/a0-1-diamond-green-active.png);
}

.diamond-box-gray .diamond-box-name {
  color: #706b4e;
}

.diamond-box-blue .diamond-box-name {
  color: #44a3f4;
}

.diamond-box-green .diamond-box-name {
  color: #008b73;
}

.diamond-box-title {
  font-size: 16px;
  height: 30px;
}

.diamond-box-line {
  font-size: 15px;
}

.diamond-box-name {
  font-size: 14px;
}

.diamond-box-line .item-left {
  float: left;
  width: 50%;
}

.diamond-box-line .item-right {
  float: right;
}


.keyongyunli .header .detail {
  margin-top: -44px;
}

#pop_capacity {
  width: fit-content;
  height: fit-content;
  padding: 0 35px;
  min-width: 1200px;
  min-height: 670px;
  background-color: #151534;
  background-color: rgba(21, 21, 52, 0.8);
  border: solid 1px #00b7ee;
  box-shadow: 0px 0px 50px #00b7ee73 inset;
  -webkit-box-shadow: 0px 0px 50px #00b7ee73 inset;
  border-radius: 10px;
  z-index: 4000;
}

/* .popWin{
  position: relative;
} */
#loading_msk {
  top: 0;
}

.clear {
  clear: both
}

.capacity-win-body .tab-content {
  min-width: 1130px;
  min-height: 590px;
  margin: 0 auto;
}

.capacity-win-body .companyList,
.subsidy-win-body .companyList {
  width: 100%;
}

.subsidy-win-body .companyList {
  margin-left: 25px;
}

.capacity-win-body .companyList .name,
.subsidy-win-body .companyList .name {
  font-size: 16px;
  line-height: 46px;
  color: #ffffff;
  left: 50px;
  position: absolute;
}

.capacity-win-body .companyList .company,
.subsidy-win-body .companyList .company {
  background: url(../img/a0-1-capacity-b1.png);
  background-size: 180px 100px;
  width: 180px;
  height: 100px;
  float: left;
  margin-left: 10px;
  margin-bottom: 10px;
  position: relative;
  opacity: 0.5;
}

.subsidy-win-body .companyList .company {
  opacity: 1;
}

.capacity-win-body .companyList .active {
  opacity: 1;
}

.capacity-win-body .ecDom {
  width: 100%;
  height: 300px;
}

.capacity-win-body .ecDom2 {
  width: 100%;
  height: 340px;
}

.capacity-win-body .companyList .company .company-icon,
.subsidy-win-body .companyList .company .company-icon {
  width: 24px;
  height: 24px;
  position: absolute;
  left: 17px;
  top: 12px;
}

.capacity-win-body .companyList .company .labelname,
.subsidy-win-body .companyList .company .labelname {
  position: absolute;
  top: 47px;
  padding: 0 17px;
  display: flex;
  width: 100%;
  flex-direction: row;
  color: #44a3f4;
  font-size: 14px;
}

.capacity-win-body .companyList .company .labelname>div,
.subsidy-win-body .companyList .company .labelname>div {
  width: 50%;
}

.capacity-win-body .companyList .company .labelname>div:last-child,
.subsidy-win-body .companyList .company .labelname>div:last-child {
  width: 50%;
  text-align: right;
}

.capacity-win-body .companyList .company .value,
.subsidy-win-body .companyList .company .value {
  position: absolute;
  top: 69px;
  padding: 0 10px;
  display: flex;
  width: 100%;
  flex-direction: row;
  color: #ffffff;
  font-size: 16px;
}

.capacity-win-body .companyList .company .value .subunit {
  font-size: 14px;
}

.capacity-win-body .companyList .company .value>div,
.subsidy-win-body .companyList .company .value>div {
  width: 55%;
}

.capacity-win-body .companyList .company .value>div:last-child,
.subsidy-win-body .companyList .company .value>div:last-child {
  width: 45%;
  text-align: right;
}

.popWin-tabs {
  position: absolute;
  height: 30px;
  left: 35px;
  text-align: center;
  margin-bottom: 10px;
}


.popWin-tabs .tab {
  position: absolute;
  width: 88px;
  height: 30px;
  line-height: 30px;
  font-size: 16px;
  background-color: #073b77;
  color: #2a81d2;
  border-radius: 15px;
  pointer-events: auto;
  cursor: pointer;
}

.popWin-tabs .selected {
  background-color: #00a0ea;
  color: #fff;
}

.popWin-tabs .ac-company {
  left: 100px;
}

.subsidy-win-body .popWin-tabs {
  right: 35px;
  left: initial;
  width: 200px;
  z-index: 200;
}

.subsidy-win-body .popWin-tabs .tab {
  font-size: 14px;
}

.subsidy-win-body .ecDom {
  width: 1200px;
  height: 300px;
}

/** */
#pop_trv_flight_cargo_abused {
  width: auto;
  height: 540px;
  position: absolute;
  top: 26%;
  left: 14%;
  background-color: #151534;
  opacity: 0.8;
  border: solid 1px #00b7ee;
  box-shadow: 0px 0px 50px #00b7ee73 inset;
  -webkit-box-shadow: 0px 0px 50px #00b7ee73 inset;
  border-radius: 10px;
  overflow: hidden;
  z-index: 4000;
}

.trv-flight-cargo .lengend-row>div {
  display: inline-block;
  line-height: 30px;
}

.trv-flight-cargo .lengend-row .companyName {
  width: 64px;

}

.trv-flight-cargo .lengend-row .companyName>div {
  width: 64px;
  margin: 2px 0;
  height: 16px;
  border-radius: 6px 0px 6px 0px;
  background-color: #e95551;
  font-size: 14px;
  line-height: 16px;
  text-align: center;
}

.trv-flight-cargo .lengend-row .nums {
  width: 78px;
  text-align: right;
  padding-right: 10px;
}

.trv-flight-cargo .lengend-row .percentage {
  width: 88px;
}

.trv-flight-cargo .lengend-row .percentage>span {
  color: #44a3f4;
}

#pop_trv_flight_cargo {
  min-height: 520px;
}

.cargoNumDom {
  height: 230px;
  width: 232px;
}

#pop_utilization_rate {
  min-width: 1473px;
  min-height: 600px;
}

#pop_utilization_rate .tab-v-item .data-name {
  width: 60px;
}

#header #companylist {
  z-index: 3000
}

.popup-content {
  padding: 35px 40px;
}

.popup-content .chart-row:not(:first-child) {
  border-top: dotted 1px #00b7ee;
  padding-top: 30px;
  margin-top: 15px;
}

.chart-title {
  color: #44a3f4;
  font-size: 18px;
  padding-left: 15px;
}

.char-title-space {
  display: inline-block;
  width: 30px;
}

.chart-title-data {
  color: #fff;
}

.chart-title-unit {
  font-size: 14px;
}

.chart-title-comp {
  font-size: 14px;
}

.chart-flight-acc-type .chart-diagram {
  height: 200px;
}

.chart-flight-acc-property .chart-diagram {
  height: 200px;
  width: 200px;
}

.chart-left {
  text-align: left;
  float: left;
}

.chart-left .chart-diagram,
.chart-left .chart-legend {
  float: left;
}

.chart-flight-acc-property .chart-legend {
  padding: 20 0 0 0;
}

.chart-flight-acc-property .chart-item {
  width: 240px;
  padding: 5px 0px;
}

.chart-flight-acc-property .chart-legend div {
  display: inline-block;
  font-size: 14px;
  line-height: 14px;
}

.chart-flight-acc-property .chart-legend {
  padding-left: 20px;
}

.chart-flight-acc-property .chart-legend .chart-item .item-color {
  height: 12px;
  width: 12px;
}

.chart-flight-acc-property .chart-legend .chart-item .item-name {
  min-width: 60px;
  padding-left: 4px;
}

.chart-flight-acc-property .chart-legend .chart-item .item-value {
  min-width: 40px;
  text-align: right;
}

.chart-flight-acc-property .chart-legend .chart-item .item-perc {
  width: 50px;
  text-align: right;
}

.compare-equal {
  color: #fff;
}

.compare-down {
  color: #ff0000;
}

.compare-down::after {
  content: '↓';
}

.compare-up {
  color: #2ad805;
}

.compare-up::after {
  content: '↑';
}

.kpi-detail {
  width: 17px;
  height: 44px;
  background: url(../img/a0-1_detail.svg) no-repeat center;
  background-size: 17px;
  cursor: pointer;
  position: absolute;
  right: 15px;
}

#pop_subsidy {
  min-width: 1200px;
  height: 652px;
}


.value .kpiValue {
  text-align: left;
  color: #2ad805;
  width: 54px;
}

.value .down {
  color: #ff0000;
}

.fs14 {
  font-size: 14px !important;
}

#pop_cargo_detail {
  min-width: 1283px;
  min-height: 544px;
}

#pop_cargo_detail .tab-h-large {
  width: 580px;
}

#pop_cargo_detail .tab-vb2 {
  margin-top: 30px;
}

#pop_cargo_detail .company-name {
  width: 60px;
}

#pop_cargo_detail .tab-data-title .data-perc {
  color: inherit;
  width: 80px;
}

#pop_cargo_detail .data-perc {
  color: #2ad805;
  width: 80px;
}

#pop_cargo_detail .down {
  color: #ff0000;
}

#pop_cargo_detail .title-left {
  padding-left: 74px;
  width: 338px;
}

#pop_aux_inc {
  min-width: 1240px;
  min-height: 800px;
}

#pop_aux_inc .edom {
  width: 140px;
  height: 140px;
}

#pop_aux_inc .inc-composition {
  margin: 0 auto;
  padding: 0 20px;
}

#pop_aux_inc .splitLine {
  margin: 0 auto;
  padding: 10px 0;
  height: 3px;
  border-bottom: dashed 1px #44a3f4;
  opacity: 0.5;
}

#pop_aux_inc .inc-title {
  font-size: 18px;
  line-height: 60px;
  color: #44a3f4;
}


#pop_aux_inc .inc-chart-container>div {
  display: inline-block;
  vertical-align: top;
}

#pop_aux_inc .data-item {
  line-height: 30px;
  display: inline-block;
  margin-left: 40px;
}

#pop_aux_inc .data-item .company>div {
  display: inline-block;
}

#pop_aux_inc .data-item .company .dott {
  width: 12px;
  height: 12px;
  margin-right: 5px;
  background-color: #c75360;
}

#pop_aux_inc .data-item-title .company {
  padding-left: 17px;
}

#pop_aux_inc .data-item>div {
  display: inline-block;
  vertical-align: middle;
}

#pop_aux_inc .data-item-title {
  color: #44a3f4;
  font-size: 14px;
  line-height: 16px;
  margin-bottom: 10px;
}

#pop_aux_inc .data-item .company {
  width: 64px;
}

#pop_aux_inc .data-item .inc {
  width: 88px;
}

#pop_aux_inc .data-item .inc-tb {
  width: 80px;
  color: #2ad805;
}

#pop_aux_inc .data-item-title .inc-tb {
  width: 80px;
  color: inherit;
}

#pop_aux_inc .data-item .down {
  color: #ff0000;
}

#pop_aux_inc .data-item .inc-avg {
  width: 75px;
}

#pop_aux_inc .edom2 {
  height: 400px;
  width: 1275px;
}

#pop_caac_complaint {
  width: 1300px;
  height: 500px;
}

#pop_caac_complaint .company {
  width: 150px;
  height: 36px;
  position: absolute;
  left: 40px;
  top: 16px;
  border: solid 1px rgba(0, 183, 238, 0.2);
}

#pop_caac_complaint .win-content {
  padding: 0 40px;
}

#pop_caac_complaint .chart-diagram {
  width: 1235px;
  height: 370px;
}

#pop_flight_accident .chart-flight-acc-type .chart-diagram {
  height: 300px;
  width: 1200px;
}

.company .hide {
  display: none !important;
}

.pop_unsafe_detail {
  width: 970px;
  height: 800px;
}

.pop_unsafe_detail .win-content {
  padding: 0 40px;
}

.pop_unsafe_detail .types {
  width: 510px;
  height: 40px;
  background-image: linear-gradient(to right, #163262, #162f5d, #152c58, #152750, transparent, transparent, transparent);
}

.pop_unsafe_detail .events {
  padding-top: 20px;
}

#pop_unsafe_detail2 .types {
  width: 640px;
}

.pop_unsafe_detail .types .type {
  display: inline-block;
  min-width: 60px;
  line-height: 40px;
  font-size: 14px;
  cursor: pointer;
  color: #44a3f4;
  opacity: 0.3;
  -webkit-user-select: none;
  user-select: none;
  margin-right: 10px;
}

.pop_unsafe_detail .types .type .dot {
  padding-right: 10px;
  line-height: 40px;
}

.pop_unsafe_detail .types .active {
  opacity: 1;
}

.pop_unsafe_detail .event-detail .evt-row {
  min-height: 34px;
}

.pop_unsafe_detail .types .tuli {
  display: inline-block;
  width: 24px;
  height: 40px;
  padding-left: 2px;
  background-color: rgba(68, 163, 244, 1);
  border-radius: 0px 8px 8px 0px;
  vertical-align: middle;
}

.pop_unsafe_detail .events .event-container {
  display: inline-block;
  width: 540px;
  height: 600px;
  vertical-align: middle;
}

.pop_unsafe_detail .events .event-deatil {
  display: inline-block;
  width: 330px;
  height: 600px;
}

#pop_flight_accident {
  min-width: 1280px;
  height: 900px;
  padding: 0 40px;
}

#pop_flight_accident .win-body {
  width: 100%;
  margin: 0 auto;
}

.h-row>div {
  display: inline-block;
  height: 786px;
  vertical-align: top;
}

#pop_flight_accident .col-title {
  font-size: 18px;
  line-height: 30px;
  color: #44a3f4;
}

#pop_flight_accident .h-row .col1 {
  width: 935px;
  border-right: dashed 1px rgba(68, 163, 244, 0.5);
  border-right-width: 3px;
}

#pop_flight_accident .h-row .col2 {
  width: 270px;
  padding-left: 40px;
}

#pop_flight_accident .chart-title {
  font-size: 16px;
  line-height: 40px;
  padding-left: 0px;
}

#pop_flight_accident .types {
  min-height: 100px;
  margin-bottom: 10px;
}

#pop_flight_accident .types .type {
  width: 290px;
  height: 40px;
  display: inline-block;
  box-shadow: 0px 0px 10px rgba(68, 163, 244, 0.5) inset;
  -webkit-box-shadow: 0px 0px 10px rgba(68, 163, 244, 0.5) 00b7ee73 inset;
  border-radius: 4px;
  margin-right: 10px;
  margin-bottom: 10px;
  line-height: 40px;
}

#pop_flight_accident .types .active {
  box-shadow: 0px 0px 15px rgba(68, 163, 244, 1) inset;
  -webkit-box-shadow: 0px 0px 15px rgba(68, 163, 244, 1) 00b7ee73 inset;
}

#pop_flight_accident .types .type .name {
  font-size: 14px;
  color: #44a3f4;
}

#pop_flight_accident .types .type .dot {
  padding-left: 10px;
  width: 20px;
  text-align: left;
}

#pop_flight_accident .types .type .chart-title-data {
  padding-left: 18px;
  font-size: 18px;
}

#pop_flight_accident .chart-flight-acc-property .chart-legend div {
  line-height: 32px;
}

.events .companylist {
  width: 530px;
  height: 600px;
  overflow: hidden;
  display: inline-block;
  vertical-align: top;
}

.events .event-detail {
  width: 330px;
  height: 600px;
  display: inline-block;
  background: url(../img/a0-1-flight-bg.png) no-repeat center;
  background-size: 330px 600px;
  position: absolute;
  margin-left: 30px;

}

.events .companyItem {
  width: 512px;
  height: 146px;
  margin-bottom: 36px;

}

.events .companyItem-empty-event {
  height: 30px;
  margin-bottom: 14px;

}

.events .flight-type .dot {
  padding-right: 5px;
}

.events .companyItem :last-child {
  margin-bottom: 20px;

}

.events .companyItem .companyName {
  position: relative;
  font-size: 16px;
  line-height: 30px;
  color: #44a3f4;
  height: 30px;
  margin-bottom: 12px;
}

.events .companyItem .companyName>div {
  display: inline-block;
  height: 30px;
  vertical-align: top;
}

.events .companyItem .companyName .company-icon {
  width: 24px;
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 24px 24px;
}

.events .companyItem .companyName .name {
  font-size: 16px;
}

.events .companyItem .companyName .name .count {
  font-size: 14px;
}

.events .companyItem .companyName .btns {
  float: right;
}

.events .companyItem .companyName .btns .disabled {
  opacity: 0.2;
}

.events .companyEvent {
  display: -webkit-box;
  box-pack: justify;
  -webkit-box-pack: left;
}

.events .companyEvent .flight {
  width: 120px;
  height: 108px;
  background-color: rgba(27, 80, 146, 0.5);
  border-radius: 5px;
  font-size: 12px;
  line-height: 26px;
  padding-left: 10px;
  color: #44a3f4;
  margin-right: 10px;
  cursor: pointer;
}

.events .companyEvent .active {
  background-color: rgba(27, 80, 146, 1);
  border: solid 1px #44a3f4;
  color: #ffffff;
  cursor: default;
}

.events .companyEvent .route,
.events .companyEvent .flight-type {
  height: 26px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.events .companyEvent .flight .flightNo {
  font-size: 16px;
  line-height: 30px;
}

.event-detail {
  color: #ffffff;
}

.event-detail .flight-identify {
  font-size: 14px;
  color: #44a3f4;
  line-height: 55px;
  padding-left: 20px;
}

.event-detail .flight-identify>div {
  display: inline-block;
  width: 80px;
  padding-left: 30px;
}

.event-detail .flight-identify .flightDate {
  background: url(../img/a0-1-event-flight-date.svg) left center no-repeat;
  background-size: 18px 20px;
}

.pop_unsafe_detail .event-detail .flight-identify .flightDate {
  width: 112px;
}

.event-detail .flight-identify .acNo {
  background: url(../img/a0-1-event-flight.svg) left center no-repeat;
  background-size: 18px 20px;
}

.event-detail .flight-identify .layout {
  background: url(../img/a0-1-event-layout.svg) left center no-repeat;
  background-size: 18px 20px;
}

.event-detail .leg-info {
  background-color: rgba(27, 80, 146, 0.5);
  ;
  padding: 6px 20px;
}

.event-detail .leg-info .route {
  font-size: 16px;
  line-height: 44px;
}

.event-detail .leg-info .times-name {
  color: #00a9ff;
}

.event-detail .leg-info .times-name,
.event-detail .leg-info .times {
  display: -webkit-box;
  box-pack: start;
  -webkit-box-pack: start;
  line-height: 24px;
  height: 24px;

}

.event-detail .leg-info .times-name>div,
.event-detail .leg-info .times>div {
  -webkit-box-flex: 1;
  text-align: left;
}

.event-detail .event-box {
  padding-left: 11px;
}

.event-detail .event-info {
  background-color: rgba(27, 80, 146, 0.5);
  ;
  padding: 0 20px 20px 20px;
  margin-bottom: 10px;
}

.event-detail .event-info .evt-row {
  padding-left: 0px;
}

.event-detail .evt-row>div {
  display: inline-block;
}

.event-detail .evt-row {
  padding-left: 20px;
  min-height: 44px;
}

.event-detail .evt-row .event-label {
  font-size: 14px;
  color: #00a9ff;
  line-height: 44px;
  width: 90px;
}

.event-detail .evt-sitem-row>div {
  display: inline-block;
}

.event-detail .evt-sitem-row .event-label {
  line-height: 34px;
  font-size: 14px;
  color: #00a9ff;
  min-width: 65px;
}

.event-detail .sub-title {
  font-size: 16px;
  color: #00a9ff;
  padding-top: 5px;
  line-height: 44px;
}

.event-detail .evt-content {
  line-height: 24px;
  font-size: 14px;
}

.event-detail .sub-item-label {
  line-height: 24px;
  font-size: 14px;
  color: #00a9ff;
}

.tbcol .subheader .expandOrNot {
  margin-left: 8px;
  width: 16px;
  height: 16px;
}

.tableDiv .tbrow .tb-expand-col {
  width: 500px;
}