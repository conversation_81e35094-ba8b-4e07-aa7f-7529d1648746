#earth3d-wrapper{
    background: url(../img/a0-1_bg.png) no-repeat left center;
}

.page-wrapper {
    height: 646px;
}

div {
  pointer-events: auto;
}

#header .logo {
    float: left;
    margin-left: 20px;
    padding-left: 95px;
    font-size: 16px;
    font-weight: 500;
    color: #fff;
    line-height: 60px;
    background: url(../img/a0-1_logo.png) no-repeat left center;
    -webkit-background-size: 82px;
    background-size: 82px;
}

#header .selector {
    position: absolute;
    right:0;
    height: 100%;
    width:466px;
}

.pagetitle {
    position: absolute;
    width: 100%;
    height: 54px;
    top: 100px;
    text-align: center;
    color: #9de6fa;
    width: 100%;
    font-size: 20px;
    margin: 0 auto;
    text-shadow: 0 0 20px #0c2d68, 0 0 20px #0c2d68;
}





.main_cb_date {
  position: absolute;
  top: 14px;
  left: 275px;
  height: 32px;
  width: 98px;
  z-index: 1000;
}

.main_cb_date .combobox_label{
  padding: 4px 6px;
  background: url(../img/combobox_arr.png) no-repeat 80px center;
}

#week_date_range,
#month_date_range {
  position: absolute;
  top: 50px;
  right: 93px;
  width: 180px;
  font-size: 14px;
  text-align: center;
  border: 1px solid #1f5fa8;
  border-radius: 2px;
  padding: 3px 0 5px 0px;
  background-color: #021d39;
  box-shadow: 0 1px 8px #021121;
  z-index: 999;
  display: none;
}


.con_flex_column{display:flex;  display: -webkit-flex; -webkit-flex-flow: column;}
.con_flex_row{display:flex;  display: -webkit-flex; -webkit-flex-flow: row;}
.flex_none{-webkit-flex:none; flex: none;}
.flex_1{-webkit-flex:1; flex: none;}


#date_select {
  position: absolute;
  top: 14px;
  right: 200px;
  width: 134px;
  height: 32px;
  font-size: 14px;
  text-align: center;
  border-radius: 4px;
  border: 1px solid #2a81d2;
  background-color: #2a81d2;
  overflow: hidden;
  transform: rotate(0deg);
  padding-left: 1px;
}
#date_select div {
  margin: 0 1px 0 0;
  background-color: #0a4589;
  color: #5d9ae3;
  font-weight: bold;
  line-height: 30px;
  width: 43px;
  cursor: pointer;
  user-select: none;
}
#date_select .selected {
  background: none;
  color: #fff;
}






/* --左右栏样式-- */
.block_l1 {
    height: 310px;
    left: 15px;
    top: 70px;
}

.block_l2 {
    height: 310px;
    right: 15px;
    top: 70px;
}

.cotent {
    height: 617px;
    background-repeat: no-repeat;
    background-position: center 10px;
    font-size: 0;
    transition: 0.5s;
}

.content_left {
    height:408px; 
    border-radius:5px; 
    overflow: hidden; 
    transform: rotate(0deg); 
    margin-top:10px;
}

.cotent .row1 {
    position: absolute;
    width: 100%;
}

.cotent .row1 .block {
    background-color: rgba(19,60,117,0.9);
    padding: 10px;
    border-radius: 5px;
}

.cotent .row1 .block div {
    margin-top: 1px;
}

.block_l1 .cotent .row1 .b1 div, .block_l1 .cotent .row1 .b2 div {
    text-align: center;
    margin-top: 1px;
    height: 20px;
}

.block_l1 .cotent .row1 .b3 div, .block_l1 .cotent .row1 .b4 div {
    height: 30px;
    line-height: 30px;
    text-align: center;
    margin-top: 1px;
}

.block_l1 .cotent .row1 .block .chart {
    position: relative;
    height: 50px;
    margin: 5px 0;
}

.block_l1 .b1 .mk,
.block_l1 .b2 .mk{
    color: #1990FF;
    position: absolute;
    font-size: 9px;
    width: 10px;
    height: 10px;
    text-align: center;
}

.block_l1 .b1 canvas,
.block_l1 .b2 canvas{
  position: absolute;
  top: 0px;
  left: 0px;
}
.block_l1 .b1 .pointer,
.block_l1 .b2 .pointer{
  position: absolute;
  width: 72px;
  height: 72px;
  top: 0px;
  left: 30px;
}

.cotent .row1 .block div span {
    padding: 0 0 0 2px;
}

.cotent .row1 .block .fs12 {
    padding: 0 0 0 25px;
}

.block_l1 .cotent .row1 .b5 .flex-box {
    display: flex;
    flex-direction: row;
    width: 100%;
    margin-top: 5px;
    height: 40px;
}

.block_l1 .cotent .row1 .b5 .flex-box div {
    flex: 1;
    border-right: 1px dashed #47aafd;
    text-align: center;
}

.block_l1 .cotent .row1 .b5 .flex-box div:last-child {
    border-right: none;
}

.block_l1 .cotent .row1 .b1 .fs12 {
    background: url(../img/a0-1_fb.png) no-repeat left center;
    -webkit-background-size: 15px;
    background-size: 15px;
}

.block_l1 .cotent .row1 .b2 .fs12 {
    background: url(../img/a0-1_passenger.png) no-repeat left center;
    -webkit-background-size: 15px;
    background-size: 15px;
}

.block_l1 .cotent .row1 .b3 .fs12 {
    background: url(../img/a0-1_nomflght.png) no-repeat left center;
    -webkit-background-size: 15px;
    background-size: 15px;
}

.block_l1 .cotent .row1 .up, .block_l2 .cotent .row1 .up {
    padding-right: 10px;
    background: url(../img/arr_up2.png) no-repeat right center;
    -webkit-background-size: 8px;
    background-size: 8px;
}

.block_l1 .cotent .row1 .down, .block_l2 .cotent .row1 .down {
    padding-right: 10px;
    background: url(../img/arr_down2.png) no-repeat right center;
    -webkit-background-size: 8px;
    background-size: 8px;
}

.block_l1 .cotent .row1 .b4 .fs12 {
    background: url(../img/a0-1_react.png) no-repeat left center;
    -webkit-background-size: 15px;
    background-size: 15px;
}

.block_l1 .cotent .row1 .b5 .fs12 {
    background: url(../img/a0-1_unnomflight.png) no-repeat left center;
    -webkit-background-size: 15px;
    background-size: 15px;
}

.block_l1 .cotent .row1 .b6 .fs12 {
    background: url(../img/a0-1_unnom.png) no-repeat left center;
    -webkit-background-size: 15px;
    background-size: 15px;
}

.block_l2 .cotent .row1 .b1 .fs12 {
    background: url(../img/a0-1_safe.png) no-repeat left center;
    -webkit-background-size: 15px;
    background-size: 15px;
}

.block_l2 .cotent .row1 .b2 .fs12 {
    background: url(../img/a0-1_warn.png) no-repeat left center;
    -webkit-background-size: 15px;
    background-size: 15px;
}

.block_l1 .cotent .row1 .b1 {
    position: relative;
    float: left;
    width: 49%;
    height: 140px;
    margin: 0px 0px 5px 0px;
}

.block_l1 .cotent .row1 .b2 {
    position: relative;
    float: right;
    width: 49%;
    height: 140px;
    margin: 0px 0px 5px 0px;
}

.block_l1 .cotent .row1 .b3 {
    position: relative;
    float: left;
    width: 49%;
    height: 65px;
    margin: 0px 0px 5px 0px;
}

.block_l1 .cotent .row1 .b4 {
    position: relative;
    float: right;
    width: 49%;
    height: 65px;
    margin: 0px 0px 5px 0px;
}

.block_l1 .cotent .row1 .b5 {
    position: relative;
    float: left;
    width: 100%;
    height: 80px;
    margin: 0px 0px 5px 0px;
}

.block_l1 .cotent .row1 .b6 {
    position: relative;
    float: left;
    width: 100%;
    height: 220px;
    margin: 0px 0px 5px 0px;
    overflow: hidden;
    transition: 0.5s;
}

#unnormal_detail {
    display: block;
    height: 15px;
    width: 15px;
    background: url(../img/a0-1_detail.png) no-repeat;
    -webkit-background-size: 15px;
    background-size: 15px;
    cursor: pointer;
    position: absolute;
    right: 5px;
    top: -20px;
}

#unsafe_detail {
    display: block;
    height: 15px;
    width: 15px;
    background: url(../img/a0-1_detail.png) no-repeat;
    -webkit-background-size: 15px;
    background-size: 15px;
    cursor: pointer;
    position: absolute;
    right: 10px;
    top: 0px;
}

.block_l1 .cotent .row1 .b6 .b6-cont {
    position: relative;
    height: 68px;
}

.block_l1 .cotent .row1 .b6 .b6-cont .tit {
    position: absolute;
    left: 2px;
    top: 2px;
    width: 50px;
    height: 12px;
}

#chart_abnormal{
    position: absolute;
    width: 245px;
    height: 62px;
    top: 3px;
    left: 16px;
   background: url(../img/a0-1_runtimeevt.png) no-repeat; 
   background-size: contain;
}

.block_l1 .cotent .row1 .b6 .extr {
    height: 135px;
    border-top: 1px dashed #1f5fa8;
    text-align: left;
    padding-top: 10px;
    position: relative;
}

.block_l1 .cotent .row1 .b6 .extr .tabcontent {
    position: absolute;
    margin-top: 10px;
    height: 104px;
    width: 100%;
    top: 0px;
    left: 0px;
    /*background-color: rgba(255, 0, 0, 0);*/
}

#content_evt_ays {
    padding-top: 8px;
}

#l_runTimeEventChartLegend {
    float: right;
    height: 65px;
    overflow: hidden;
}

#sliderBox {
    position: absolute;
    top: 0;
}

#content_evt_ays .l-box {
    position: relative;
    top: 0px;
    height: 75px;
    overflow: hidden;
}

#content_evt_ays .legend_row {
    color: #4d9cfd;
    text-align: left;
    cursor: pointer;
}

#content_evt_ays .legend_row span {
    display: inline-block;
    font-size: 11px;
}

#content_evt_ays .legend_row .name {
    width: 68px;
}

#content_evt_ays .legend_row .num {
    width: 16px;
    text-align: right;
}

#content_evt_ays .legend_row .per {
    padding-left: 20px;
    padding-right: 7px;
}

#content_evt_ays .btns {
    position: absolute;
    height: 20px;
    width: 50px;
    right: 5px;
    bottom: 5px;
}

#content_evt_ays .btns .pre-btn {
    position: absolute;
    display: block;
    height: 20px;
    width: 20px;
    left: 0;
    background: url(../img/a0-1_pre.png) no-repeat;
    -webkit-background-size: 20px;
    background-size: 20px;
    cursor: pointer;
}

#content_evt_ays .btns .pre-btn.prev-disabled {
    position: absolute;
    display: block;
    height: 20px;
    width: 20px;
    left: 0;
    background: url(../img/a0-1_pre-dis.png) no-repeat;
    -webkit-background-size: 20px;
    background-size: 20px;
    cursor: pointer;
}

#content_evt_ays .btns .next-btn {
    position: absolute;
    display: block;
    height: 20px;
    width: 20px;
    right: 0;
    background: url(../img/a0-1_next.png) no-repeat;
    -webkit-background-size: 20px;
    background-size: 20px;
    cursor: pointer;
}

#content_evt_ays .btns .next-btn.next-disabled {
    position: absolute;
    display: block;
    height: 20px;
    width: 20px;
    right: 0;
    background: url(../img/a0-1_next-dis.png) no-repeat;
    -webkit-background-size: 20px;
    background-size: 20px;
    cursor: pointer;
}

.block_l2 .cotent .row1 {
    position: absolute;
    width: 100%;
}

.block_l2 .cotent .row1 .b1 {
    float: left;
    width: 100%;
    height: 40px;
    margin: 10px 0px 5px 0px;
}

.block_l2 .cotent .row1 .b2 {
    position: relative;
    overflow: hidden;
    float: left;
    width: 100%;
    height: 120px;
    margin: 0px 0px 5px 0px;
    transition: 0.5s;
}

.block_l2 .cotent .row1 .b2 .b2-cont {
    position: relative;
    height: 110px;
    text-align: left;
    display: flex;
    flex-direction: column;
    padding-bottom: 10px;
}

.block_l2 .cotent .row1 .b2 .b2-cont div {
    text-align: center;
}

.block_l2 .cotent .row1 .b2 .extr {
    height: 240px;
    border-top: 1px dashed #1f5fa8;
    text-align: left;
    padding-top: 10px;
    position: relative;
    display: flex;
    flex-direction: column;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 {
    flex: 3;
    display: flex;
    box-align: center;
    align-items: center;
    align-items: center;
    flex-direction: row;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .c {
    height: 100%;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .c:last-child {
    border-left: 1px dashed #1f5fa8;
    margin-left: 10px;
    padding-left: 5px;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .fs22 {
    line-height: 60px;
    background: url(../img/a0-1_warning.png) center center no-repeat;
    height: 100%;
    vertical-align: middle;
    -webkit-background-size: 60px;
    background-size: 60px;
    padding-top: 5px;
    width: 66px;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .up-items {
    height: 100%;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    flex-direction: column;
    width: 108px;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .up-items .items {
    width: 100%;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    flex-direction: row;
    flex: 1;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .up-items .items .item1 {
    flex: 1;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .up-items .items .item2 {
    flex: 3;
    text-align: right;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .up-items .items .icon {
    width: 20px;
    height: 20px;
    display: block;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .up-items .items .icon1 {
    background: url(../img/a0-1_human.png) center center no-repeat;
    -webkit-background-size: 18px;
    background-size: 18px;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r1 .up-items .items .icon2 {
    background: url(../img/a0-1_mechine.png) center center no-repeat;
    -webkit-background-size: 18px;
    background-size: 18px;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r2 {
    flex: 1;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    flex-direction: row;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r2 .item1 {
    margin-right: 70px;
    padding-top: 4px;
}

.block_l2 .cotent .row1 .b2 .b2-cont .r2 .item2 {
    flex: 2;
    padding-right: 5px;
    text-align: left;
}

/*-------*/

/* --底部-- */
.footer .flight-status {
    width: 180px;
    height: 60px;
    position: absolute;
    bottom: 40px;
    left: 370px;
    display: flex;
    flex-direction: row;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    font-size: 10px;
}

.footer .flight-status .item1 {
    height: 100%;
    flex: 1;
    background: url(../img/a0-1_f1.png) center center no-repeat;
    border-radius: 50%;
    background-color: #2a468d;
    -webkit-background-size: 26px;
    background-size: 26px;
}

.footer .flight-status .item2 {
    padding-left: 5px;
    height: 100%;
    flex: 2;
}

.footer .flight-status .item2 div {
    height: 50%;
}

.footer .flight-status .item2 div span {
    display: inline-block;
    height: 100%;
    line-height: 25px;
    padding-left: 25px;
    margin-right: 5px;
    background: url(../img/a0-1_f3.png) left center no-repeat;
    -webkit-background-size: 18px;
    background-size: 18px;
}

.footer .flight-status .item2 div span:last-child {
    background: url(../img/a0-1_f2.png) left center no-repeat;
    -webkit-background-size: 18px;
    background-size: 18px;
}

.footer .map-btns {
    width: 100px;
    height: 60px;
    position: absolute;
    bottom: 40px;
    right: 370px;
}

.footer .map-btns img {
    display: inline-block;
    height: 60px;
    width: 44px;
}

.footer .map-btns .m3d {
    float: left;
    cursor: pointer;
}

.footer .map-btns .m2d {
    float: right;
    cursor: pointer;
}

.footer .c-tels {
    width: 285px;
    position: absolute;
    bottom: 38px;
    right: 20px;
}

.footer .c-tels .title {
    padding-left: 20px;
    background: url(../img/a0-1_tel.png) left center no-repeat;
    -webkit-background-size: 15px;
    background-size: 15px;
    margin-bottom: 10px;
}

.footer .c-tels div .name {
    display: inline-block;
    margin-right: 10px;
    margin-top: 2px;
    width: 135px;
}

.footer .c-tels div .num {
    display: inline-block;
    width: 100px;
}



.footer .execac{
  position: absolute;
  height: 55px;
  left: 560px;
  top: 546px;
  padding-left: 66px;
}
.footer .execac .icon{
  position: absolute;
  top: 0;
  left: 0;
  width:60px;
  height: 60px;
  background: url(../img/a3.3_icon2.png) no-repeat center;
  border-radius: 50%;
  background-color: #2a468d;
}
.footer .execac .blk{
    display: block;
    padding-bottom: 10px;
}

#abnormal_total {
    position: absolute;
    width: 30px;
    height: 10px;
    top: 29px;
    left: 30px;
    text-align: center;
    color: white;
    font-size: 14px;
}

#abnormal_hb {
    position: absolute;
    width: 130px;
    height: 20px;
    top: 49px;
    left: 40px;
    text-align: center;
}

#abnormal_main {
    position: absolute;
    width: 150px;
    height: 40px;
    top: 15px;
    left: 157px;
}

#abnormal_main .ico{
    width: 28px;
    height: 30px;
    background: url(../img/a4.2_ico_evt1.png) left center no-repeat;
}
#abnormal_main_val span {
    display: block;
    margin-right: 6px;
}


.up_red{
  background: url(../img/arr_up_red.png) no-repeat right 2px;
}
.down_green{
  background: url(../img/arr_down_green.png) no-repeat right 2px;
}












.block_r .tabc {
  position: absolute;
  width: 100%;
  height: 340px;
}

.block_r .tabc div{
  text-align: left;
}
.block_r .tabc span{
  padding: 0;
}

.block_r .tabc1 {
  position:relative; 
  float:left;
}

.block_r .tabc1 .legend {
  font-size: 11px;
  color: #5d9ae3;
}
.block_r .tabc1 .legend .itm {
  margin-right: 3px;
  display: inline-block;
  cursor: pointer;
  user-select: none;
}
.block_r .tabc1 .legend .dot {
  font-size: 20px;
  vertical-align: middle;
}
.block_r .tabc1 .legend .lb {
  color: #5d9ae3;
  vertical-align: middle;
}

.block_r .tabc2 .legend {
  font-size: 11px;
  color: #5d9ae3;
}

.block_r .tabc1 .legend .lbl,
.block_r .tabc2 .legend .lbl {
  text-align: center;
  border-radius: 3px;
  padding: 2px;
}
.block_r .tabc2 .legend .itm {
  margin-right: 6px;
  cursor: pointer;
  user-select: none;
}
.block_r .tabc2 .legend .dot {
  font-size: 20px;
  vertical-align: middle;
}
.block_r .tabc2 .legend .lb {
  color: #5d9ae3;
  vertical-align: middle;
}
.block_r .tabc1 .legend .lst,
.block_r .tabc2 .legend .lst {
  display: block;
  margin-left: -5px;
  line-height: 18px;
}
.block_r .tabc2 .legend .lst2{
  display: block;
  margin-left: -5px;
  line-height: 16px;
}


#scrollpane1 {
  position: absolute;
  top: 8px;
  left: 10px;
  width: 94%;
  height: 260px;
  overflow: hidden;
  background-color: rgba(255,0,0,0);
}
#scrollpane2 {
  position: absolute;
  top: 76px;
  left: 0;
  width: 100%;
  height: 150px;
  overflow: hidden;
  background-color: rgba(255,0,0,0);
}

.block_r .scrollcontent {
  width: 100%;
}
.block_r .scrollcontent .comprow{
  width: 290px;
  height: 136px;
  overflow: hidden;
  transform: rotate(0deg);
}
.block_r .scrollcontent .comprow .head{
  width: 100%;
  height: 32px;
}
.block_r .scrollcontent .comprow .head .tt{
  position: absolute;
  left: 0;
  width: 200px;
  height: 24px;
  color: #7dc0ff;
  line-height: 25px;
  background-position: 0 0;
  background-repeat: no-repeat;
  background-size: contain;
}
.block_r .scrollcontent .comprow .head .tt .lb{
  margin-left: 32px;
  font-size: 14px;
  font-weight: bold;
}
.block_r .scrollcontent .comprow .head .tt .num{
  margin-left: 5px;
  font-size: 12px;
}

.block_r .scrollcontent .comprow .head .btns{
  float: right;
  text-align: right;
}
.block_r .scrollcontent .comprow .head .btn{
  display: inline-block;
  margin-right:4px;
  height: 20px;
  width: 20px;
  padding: 0;
  cursor: pointer;
  
}



.block_r .scrollcontent .comprow .head .btn_prev {
    background: url(../img/a0-1_pre.png) no-repeat;
    background-size: contain;
}

.block_r .scrollcontent .comprow .head .btn_prev.prev-disabled {
    background: url(../img/a0-1_pre-dis.png) no-repeat;
    background-size: contain;
    cursor: default;
}

.block_r .scrollcontent .comprow .head .btn_next {
    background: url(../img/a0-1_next.png) no-repeat;
    background-size: contain;
}

.block_r .scrollcontent .comprow .head .btn_next.next-disabled {
    background: url(../img/a0-1_next-dis.png) no-repeat;
    background-size: contain;
    cursor: default;
}


.block_r .scrollcontent .comprow .itmlst{
  position: absolute;
  top: 35px;
  left: 0;
  height: 88px;
  width: 200000px;
}

.block_r .scrollcontent .comprow .itmlst .blk{
  position: relative;
  display: inline-block;
  width: 68px;
  height: 83px;
  margin-right: 5px;
  border-radius: 5px;
  overflow: hidden;
  transform: rotate(0);
  cursor: pointer;


background: #183e7c;


}
.block_r .scrollcontent .comprow .itmlst .blk .time{
  color: #4d9cfd;
  font-size: 9px;
  margin: 6px 5px 3px 5px;
}
.block_r .scrollcontent .comprow .itmlst .blk .time .r{
  float: right;
}
.block_r .scrollcontent .comprow .itmlst .blk .fltno{
  text-align: center;
  font-size: 12px;
  font-weight: bold;
  color: #aecffd;
  margin: 3px ;
}
.block_r .scrollcontent .comprow .itmlst .blk .city{
  width: 100%;
  text-align: center;
  font-size: 10px;
  color: #4d9cfd;
  height: 22px;
}
.block_r .scrollcontent .comprow .itmlst .blk .bot{
  position: absolute;
  width: 100%;
  bottom: 4px;
  text-align: center;
  font-size: 9px;
  color: #046cd6;
  text-overflow:ellipsis;
  white-space: nowrap;
}



/* ------------------------- */

.pop{
  position: absolute;
  width: 260px;
  height: 500px;
  box-shadow: 0 0 2px #000, 0 0 10px rgba(0,0,0,0.5);
  border-radius: 5px;
}
.pop .scrollpane{
  position: absolute;
  width: 100%;
  height: 470px;
  background-color: #bce0fe;
  border-bottom-right-radius: 5px;
  border-bottom-left-radius: 5px;
  transform: rotate(0);
  overflow: hidden;
}
.pop .cont{
  position: relative;
  width: 100%;
}

#pop_tab1 .arr{
  position: absolute;
  height: 16px;
  width: 16px;
  top: 100px;
  left: -6px;
  background-color: #bce0fe;
  transform: rotate(45deg);
}
#pop_tab2 .arr{
  position: absolute;
  height: 16px;
  width: 16px;
  top: 100px;
  right: -6px;
  background-color: #bce0fe;
  transform: rotate(45deg);
}

.pop .head{
  position: relative;
  height: 30px;
  background-color: #90cffe;
  border-bottom: 1px solid #5aa3f1;
  line-height: 30px;
  color: #031c57;
  border-top-right-radius: 5px;
  border-top-left-radius: 5px;
  transform: rotate(0);
  overflow: hidden;
}

.pop .head .fltno{
  display: inline-block;
  font-size: 14px;
  font-weight: bold;
  margin-left: 12px;
  padding-left: 22px;
  line-height: 17px;
  height: 16px;
  background-position: 0 0;
  background-repeat: no-repeat;
  background-size: contain;
}
.pop .head .tag{
  display: inline-block;
  border-radius: 9px;
  height: 16px;
  line-height: 18px;
  padding: 0 6px;
  font-size: 12px;
  font-weight: bold;
  margin-left: 6px;
}
.pop .head .btnx{
  position: absolute;
  display: inline-block;
  width: 18px;
  height: 18px;
  top: 6px;
  right: 7px;
  cursor: pointer;
  background: url(../img/a4.2_pop_x.png) no-repeat center;
}

.pop .row{
  position: relative;
  padding: 7px 12px;
  margin: 0;
  border-bottom: 1px solid #98c7f7;
  min-height: 30px;
  color: #115096;
  font-size: 12px;
  background-color: #bce0fe;
}
.pop .row_w{
  background-color: #fff;
}
.pop .botrow{
  border-bottom-right-radius: 5px;
  border-bottom-left-radius: 5px;
  transform: rotate(0);
  overflow: hidden;
  border: none;
}

.pop .row1 .date{
  display: inline-block;
  padding-left: 20px;
  margin-right: 10px;
  line-height: 20px;
  background: url(../img/a4.2_ico_cal.png) no-repeat left center;
}
.pop .row1 .ac{
  display: inline-block;
  padding-left: 22px;
  margin-right: 10px;
  line-height: 20px;
  background: url(../img/a4.2_ico_plane.png) no-repeat left center;
}
.pop .row1 .cabin{
  display: inline-block;
  padding-left: 16px;
  line-height: 20px;
  background: url(../img/a4.2_ico_seat.png) no-repeat left center;
}
.pop .label{
  font-size: 12px;
  font-weight: normal;
  color: #508cc9;
  margin: 0;
  padding: 0;
}

.pop .cities{
  text-align: center;
  height: 72px;
}
.pop .cities .lbl{
  position: relative;
  width: 22px;
}
.pop .cities .lbl .lb1{
  position: absolute;
  width: 34px;
  top: 19px;
  left: 0;
  display: inline-block;
  border: 1px solid #73ae03;
  color: #73ae03;
  background-color: #FFF;
  border-radius: 3px;
}
#pop_tab1 .cities .lbl .lb2{
  position: absolute;
  width: 34px;
  top: 40px;
  left: 0;
  display: inline-block;
  border: 1px solid #ab6a00;
  color: #ab6a00;
  background-color: #FFF;
  border-radius: 3px;
}
#pop_tab2 .cities .lbl .lb2{
  position: absolute;
  width: 34px;
  top: 40px;
  left: 0;
  display: inline-block;
  border: 1px solid #0058f8;
  color: #0058f8;
  background-color: #FFF;
  border-radius: 3px;
}
.pop .cities .nm{
  position: relative;
  display: inline-block;
  background: #bce0fe;
  padding: 0 5px;
}
.pop .cities .tm{
  display: block;
  font-weight: bold;
  margin-top: 6px;
}
.pop .cities .tm2{
  color:#ab6a00;
  display: block;
  font-weight: bold;
  margin-top: 3px;
}
.pop .cities .stop{
  position: relative;
}
.pop .cities .line{
  position: absolute;
  display: block;
  width: 100%;
  left: 0px;
  top: 7px;
  border-top: 1px solid #3d98e1;
  z-index: 0;
}
.pop .cities .line .dot{
  position: absolute;
  display: block;
  width: 5px;
  height: 5px;
  background: #3d98e1;
  border-radius: 50%;
}
.pop .cities .line .dot1{
  top: -3px;
  left: -2px;
}
.pop .cities .line .dot2{
  top: -3px;
  right: -2px;
}

.pop .blktit {
  margin: 12px 0 8px 8px;
  border-left: 4px solid #185a9e;
  color: #185a9e;
  height: 14px;
  line-height: 14px;
  padding-left: 8px;
  background-color: #bce0fe;
}
.pop .blkcon {
  margin-left: 8px;
  margin-right: 8px;
  border: 1px solid #98c7f7;
  border-bottom: none;
  margin-bottom: 12px;
  color: #115096;
  font-size: 12px;
  background-color: #dcefff;
}
.pop .blkcon .blkrow{
  width: 100%;
  border-bottom: 1px solid #98c7f7;
  padding: 7px 8px;
}
.pop .blkcon .label {
  display: block;
  text-align: left;
  margin-bottom: 4px;
}
.pop .blkcon .tag{
  text-align: center;
  display: inline-block;
  border-radius: 9px;
  height: 16px;
  line-height: 18px;
  padding: 0 6px;
  font-size: 12px;
  color: #000;
}
.pop .line2{
  display: block;
  font-size: 10px;
  margin-top: 5px;
}
