/**
 * @des 用于dealer项目的简单slider
 * <AUTHOR> 2017.9.29
 *
 * @params
 * dom/string slides 滑动的单元
 * dom/string container 所有结构的包含元素
 * num/string itemHeight
 *
 * @methods
 * function toPrePage 到上一页
 * function toNextPage 到下一页
 *
 * @example
 * self.slider = new Slider({
            itemWidth:880,
            itemMargin:32,
            container:'#ticketContainer',
            slides:'.widget-ui-ticket li'
        });
 */

var Slider = function (opt) {
    this.defaultOpt = {
        itemWidth: 400,
        itemHeight: 400,
        itemMargin: 0,
        slideTime: 500,
        sliderLeft: 0,
        container: '',
        totalPage: '',//总页数
        slides: '',
        preTab: '',
        nextTab: '',
        direction: 'horizental',
        btnDisabledClass:'scroller-disabled',
        prevDisabledClass:'prev-disabled',
        nextDisabledClass:'next-disabled',
        beforeSlide: function () {
        },
        afterSlide: function () {

        }
    };

    var self = this,
        opt = this.opt = $.extend(this.defaultOpt, opt),
        slideList,
        container = opt.container;
    if (!$(container).length) {
        return;
    }
    self.slides = slides = $(opt.slides, opt.container);//滑动块
    self.slideList = slideList = slides.parent();//滑动块列表
    self.totalPage = opt.totalPage || slides.length;//滑动总页数(如果传入了totalPage参数就用的totalPage,没有传入就用传入的slides的个数)
    self.currentPage = 0;//滑动当前页数
    this.slidesContainer = $(container);//整个滑动的容器

    this.init();

    return this;
};

Slider.prototype = {
    init: function () {
        var self = this;

        self.render();
        self.bind();
    },
    render: function () {
        var self = this,
            opt = self.opt,
            container = $(opt.container),
            direction = opt.direction,
            slides = self.slides,
            slideLen = self.totalPage,
            totalWidth = slideLen * (opt.itemWidth + opt.itemMargin),
            slideList = self.slideList,
            height = slideList.outerHeight(),
            preTab = $(opt.preTab),
            nextTab = $(opt.nextTab),
            btnDisabledClass = opt.btnDisabledClass,
            prevDisabledClass = opt.prevDisabledClass,
            nextDisabledClass = opt.nextDisabledClass;

        if (preTab && typeof preTab == 'object' && preTab.length) {
            preTab.addClass(btnDisabledClass + ' ' + prevDisabledClass);
        }

        if (slideLen && slideLen > 1) {
            nextTab.removeClass(btnDisabledClass + ' '+ nextDisabledClass);
        }

        if(slideLen == 1){
            // 总共只有一页 不能翻页
            preTab.addClass(btnDisabledClass + ' ' + prevDisabledClass);
            nextTab.addClass(btnDisabledClass + ' '+ nextDisabledClass);
        }

        if (direction == 'vertical') {
            //竖直方向的slider
            slideList && typeof slideList == 'object' && slideList.length && slideList.css({
                position: 'absolute',
                left: 0,
                top: 0
            });

            container && typeof container == 'object' && container.length && container.css({
                overflow: 'hidden',
                position: 'relative'
            });
        } else {
            //默认水平方向
            slides.css({
                width: opt.width,
                marginRight: opt.itemMargin,
                float: 'left',
                display: 'block'
            });
            slideList.length && slideList.addClass('clearfix').css({
                width: totalWidth,
                position: 'absolute',
                left: opt.sliderLeft,
                top: 0
            });

            container.length && container.css({
                overflow: 'hidden',
                position: 'relative',
                height: height
            });
        }
    },
    bind: function () {
        var self = this,
            opt = self.opt,
            preTab = $(opt.preTab),
            nextTab = $(opt.nextTab);

        preTab.length && preTab.on('click', function () {
            self.clickPre();
        });

        nextTab.length && nextTab.on('click', function () {
            self.clickNext();
        });
    },
    toPrePage: function () {
        var self = this,
            totalPage = self.totalPage,
            currentPage = self.currentPage,
            newPage;

        newPage = '';
    },
    toNextPage: function () {

    },
    clickPre: function () {
        var self = this,
            opt = self.opt,
            nextTab = $(opt.nextTab),
            preTab = $(opt.preTab),
            totalPage = self.totalPage,
            currentPage = self.currentPage,
            btnDisabledClass = opt.btnDisabledClass,
            prevDisabledClass = opt.prevDisabledClass,
            nextDisabledClass = opt.nextDisabledClass,
            newPage;

        //已经滚动到尽头 或者 正在滚动
        if (preTab.hasClass(btnDisabledClass) || preTab.hasClass('animating')) {
            return;
        }
        //已经是第一页
        if (currentPage == 0) {
            return;
        }
        //点击这一次之后就不能再点击上一页了
        if (currentPage == 1) {
            preTab.addClass(btnDisabledClass + ' '+prevDisabledClass);
        }

        if (nextTab.hasClass(btnDisabledClass)) {
            nextTab.removeClass(btnDisabledClass + ' ' + nextDisabledClass);
        }

        newPage = currentPage - 1;
        self.currentPage = newPage;

        self.slideTo(newPage, 'pre');
    },
    clickNext: function () {
        var self = this,
            opt = self.opt,
            nextTab = $(opt.nextTab),
            preTab = $(opt.preTab),
            totalPage = self.totalPage,
            currentPage = self.currentPage,
            btnDisabledClass = opt.btnDisabledClass,
            prevDisabledClass = opt.prevDisabledClass,
            nextDisabledClass = opt.nextDisabledClass,
            newPage;

        //已经滚动到尽头 或者 正在滚动
        if (nextTab.hasClass(btnDisabledClass) || nextTab.hasClass('animating')) {
            return;
        }
        //已经是最后一页
        if (currentPage == totalPage - 1) {
            return;
        }
        //点击这一次之后就不能再点击下一页了
        if (currentPage == totalPage - 2) {
            nextTab.addClass(btnDisabledClass + ' ' + nextDisabledClass);
        }

        if (preTab.hasClass(btnDisabledClass)) {
            preTab.removeClass(btnDisabledClass + ' ' + prevDisabledClass);
        }

        newPage = currentPage + 1;
        self.currentPage = newPage;

        self.slideTo(newPage, 'next');
    },
    //滑动到第NUM个slide
    slideTo: function (num,dir) {
        var self = this,
            opt = self.opt,
            width = opt.itemWidth,
            height = opt.itemHeight,
            margin = opt.itemMargin,
            direction = opt.direction,
            slideTime = opt.slideTime,
            sliderLeft=opt.sliderLeft,
            slides = $(opt.slides) || [],
            slide = slides.eq(num),
            slidesContainer = self.slidesContainer,
            len = slides.length,
            slideList = self.slideList,
            easing = opt.easing,
            nextTab = $(opt.nextTab),
            preTab = $(opt.preTab),
            totalPage = self.totalPage,
            maxNum = totalPage -1,
            currentPage = self.currentPage,
            beforeSlide = opt.beforeSlide,
            btnDisabledClass = opt.btnDisabledClass,
            prevDisabledClass = opt.prevDisabledClass,
            nextDisabledClass = opt.nextDisabledClass;

        num = Math.min(num, maxNum);

        self.beforeSlide(num, slidesContainer);

        // 页码范围
        if(currentPage == num){
           // return;
        }
        if(num > maxNum){
            num =  maxNum;
        }

        if(num < 0){
            num =  0;
        }

        // 修改按钮可点击状态
        // 已经到最大页码
        if (num >= maxNum) {
            nextTab.addClass(btnDisabledClass + ' ' + nextDisabledClass);
        }
        // 已经到最小页码
        if (num <= 0) {
            preTab.addClass(btnDisabledClass + ' ' + prevDisabledClass);
        }
        // 上翻可点
        if (num > 0) {
            preTab.removeClass(btnDisabledClass + ' ' + prevDisabledClass);
        }
        // 下翻可点
        if (num < maxNum) {
            nextTab.removeClass(btnDisabledClass + ' ' + nextDisabledClass);
        }

        if(dir == 'next'){
            nextTab.addClass('animating');
        } else {
            preTab.addClass('animating');
        }
        if (direction == 'vertical') {
            //竖直方向滚动
            slideList && slideList.animate({
                top: -(height + margin) * num
            }, slideTime, easing, function () {
                self.afterSlide(num, slidesContainer);
                if(dir == 'next'){
                    nextTab.removeClass('animating');
                } else {
                    preTab.removeClass('animating');
                }
            });
        } else {
            //水平方向滚动
            nextTab.addClass('animating');
            preTab.addClass('animating');
            slideList && slideList.animate({
                left: -(width + margin) * num+sliderLeft
            }, slideTime, easing, function () {
                self.afterSlide(num, slidesContainer);
                if(dir == 'next'){
                    nextTab.removeClass('animating');
                } else {
                    preTab.removeClass('animating');
                }
            });
        }

        // 更新当前页码数
        self.currentPage = num;
    },
    beforeSlide: function (num, slidesContainer) {
        var self = this,
            beforeSlide = self.opt.beforeSlide;
        beforeSlide && typeof beforeSlide == 'function' && beforeSlide(num, slidesContainer);
    },
    afterSlide: function (num, slidesContainer) {
        var self = this,
            afterSlide = self.opt.afterSlide;

        self.currentPage = num;//更新现在页码
        afterSlide && typeof afterSlide == 'function' && afterSlide(num, slidesContainer);
    }
};

// module.exports = Slider;