<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">

  <title>HNA数字航空</title>

  <meta http-equiv="refresh" content="86400">
  <script>
    var script = "<script src='js/app.js?ts=" + new Date().getTime() + "' ><\/script>";
    document.write(script);
  </script>

  <link href="css/nprogress.css" rel="stylesheet">
  <script src="js/nprogress.js"></script>
  <script src="js/jquery-1.11.1.js"></script>
  <script src="js/babel.min.js"></script>
  <script src="js/polyfill.min.js"></script>

  <script src="js/bootstrap.min.js"></script>
  <script src="js/echarts.min.4.6.js"></script>
  <script src="js/echarts-gl.min.js"></script>

  <script src="js/jquery.powertip.min.js"></script>
  <script src="js/jquery.nicescroll.min.js"></script>

  <script src="js/moment-with-locales.min.js"></script>
  <script src="js/bootstrap-datetimepicker.min.js"></script>
  <script>
    loadjs('js/config.js')
    loadjs('js/tingyun.js')
    loadCss('css/bootstrap.css')
    loadCss('css/bootstrap-theme.css')
    loadCss('css/main.css')
    loadCss('css/a0-1.css')
    loadCss('css/bootstrap-datetimepicker.min.css')
    loadjs('js/json.js')

    loadjs('js/ui.js')
    loadjs('js/util.js')
    loadjs('js/lib/eking.js')
    loadjs('js/slider.js')
    loadjs('js/lib/vue.min.js')
    loadjs('js/common.js')
  </script>

</head>

<body style="opacity:0; overflow: hidden;background-color: #0d256b;">
  <iframe id="login" scrolling="no" width="0" height="0" style="display:none;"></iframe>
  <audio id="text2audio"></audio>
  <div id="earth3d-wrapper" class="page-wrapper" style="z-index: 20; display: none; background-color: #000;">
    <div style="position: absolute; width:480px; height:480px; left: 443px; top: 130px; ">
      <div id="earth3d" style="width:480px; height: 480px; pointer-events: auto;"></div>
    </div>
  </div>
  <iframe id="mainframe" class="mainframe scale_item" allowtransparency="true" scrolling="no"></iframe>
  <!-- <div class="pageBg scale_item"></div> -->


  <!--主体-->
  <div class="page-wrapper" id="page-parent-comp">
    <!--头部-->
    <div id="header" class="not-auto-scale-by-commonjs">
      <div id="logo">
        <div id="main_page_title"></div>
        <div id="main_page_subtitle"></div>
      </div>

      <div class="selector">
        <div id="companycombo" code="" style="top:25px;">
          <div class="box"></div>
          <div id="companylist"></div>
        </div>
        <div id='date_select' class='con_flex_row'>
          <div class="tab flex_none tab_D selected" data-type='D'>日</div>
          <div class="tab flex_none tab_L" data-type='L'>周</div>
          <div class="tab flex_none tab_M" data-type='M'>月</div>
          <div class="tab flex_none tab_Y" data-type='Y'>年</div>
        </div>
        <div class="form-group dateCmp">
          <div class="datetimepicker datetimepicker_D">
            <div class='input-group date' id='datetimepicker'>
              <input type='text' class="form-control" />
              <span class="input-group-addon">
                <span class="glyphicon glyphicon-calendar"></span>
              </span>
            </div>
          </div>
          <div class="datetimepicker datetimepicker_L hide">
            <div id="main_cb_week"></div>
          </div>

          <div class="datetimepicker datetimepicker_M hide">
            <div class='input-group date' id='datetimepicker_month'>
              <input type='text' class="form-control" />
              <span class="input-group-addon">
                <span class="glyphicon glyphicon-calendar"></span>
              </span>
            </div>
          </div>
          <div class="datetimepicker datetimepicker_Y hide">
            <div class='input-group date' id='datetimepicker_year'>
              <input type='text' class="form-control" />
              <span class="input-group-addon">
                <span class="glyphicon glyphicon-calendar"></span>
              </span>
            </div>
          </div>
        </div>

      </div>

      <div id="week_date_range"></div>

      <div id="ctrl_user" style="top:22px;right:35px;"></div>

    </div><!-- /#header -->

    <div class="page" style="pointer-events: none;">
      <!--中心标题-->
      <div class="pagetitle">
        <span class="maintitle">
          <span class="comp"></span><span class="tit1">客货运运营情况概览</span><span class="date1"></span>
        </span>
      </div>
      <!--左侧栏-->
      <div class="leftDiv">

        <div class="block_l1 block-frame">

          <div class="block-header tit">
            运行控制
            <!-- <div class="datetype-container yxkz datetype1">
                            <div id="switchDay" class="dateType day selected" dateType="D">日</div>
                            <div id="switchYear" class="dateType year" dateType="Y">年</div>
                        </div> -->
          </div>


          <div class="cotent content_left">

            <div class="row1">
              <div class="b-mainKpi flightnum">
                <div class="header">航班量<span class="subheader">(班次)</span></div>
                <div class="flgithDeatil">
                  <div class="flexBlock item head1">
                    <div class="col1"></div>
                    <div class="col2">计划</div>
                    <div class="col3">已执行</div>
                  </div>
                  <div class="flexBlock item All">
                    <div class="col1">总量</div>
                    <div class="col2" id="schFligntNum"></div>
                    <div class="col3" id="EXCUTED_NO"></div>
                  </div>
                  <div class="flexBlock item INL">
                    <div class="col1">国内</div>
                    <div class="col2" id="SCH_NO_INL"></div>
                    <div class="col3" id="EXCUTED_NO_INL"></div>
                  </div>
                  <div class="flexBlock item INT">
                    <div class="col1">国际</div>
                    <div class="col2" id="SCH_NO_INT"></div>
                    <div class="col3" id="EXCUTED_NO_INT"></div>
                  </div>
                </div>
              </div>

              <div class="b-mainKpi nomagin rlyl">
                <div class="header">
                  日利用率 <div class="detail"></div>
                </div>
                <div class="flexBlock">
                  <span class="col1">宽体机</span>
                  <span class="col2" id='W_AC_UTIL_RATE'>-</span>
                </div>
                <div class="flexBlock">
                  <span class="col1">窄体机</span>
                  <span class="col2" id='N_AC_UTIL_RATE'>-</span>
                </div>
                <div class="flexBlock">
                  <span class="col1">支线机</span>
                  <span class="col2" id='R_AC_UTIL_RATE'>-</span>
                </div>
              </div>

              <div class="b-mainKpi nomflght">
                <div class="header">航班正常率<span class="subheader">(计划)</span>
                  <div class="detail"></div>
                </div>
                <div class="flexBlock item">
                  <div class="col1 textCenter">
                    <span id='val_NORMAL_RATE' class="fs14 white"></span>
                  </div>
                  <div class="col2 textCenter"> <span class="fs14 blue">环比</span>
                    <span id='hb_NORMAL_RATE' class="fs14"></span>
                    <!--or className = down-->
                    <span id='hb_NORMAL_RATE_arr' class="fs14"></span>
                  </div>
                </div>
              </div>
              <div class="b-mainKpi nomagin kzl">
                <div class="header">
                  客座率<span class="subheader">（运行）</span>
                  <div class="detail"></div>
                </div>
                <div class="flexBlock"><span class="singlekpivalue" id='val_PLF_ac'>-</span></div>
              </div>

              <div class="b-mainKpi passenger">
                <div class="header">旅客量<span class="subheader">(万人次)</span></span>
                  <div class="detail"></div>
                </div>
                <div class="flexBlock"><span class="singlekpivalue" id='val_CKI_NUM'>-</span></div>
              </div>


              <div class="b-mainKpi nomagin huoyounums ">
                <div class="header">货邮运输量<div class="detail"></div>
                </div>
                <div class="flexBlock"><span class="singlekpivalue" id='CARGO_VOLUME'>-</span></div>
              </div>

              <div class="b-mainKpi keyongyunli">
                <div class="header">
                  <div class="tabs">
                    <div class="tab yongli keyong" data-type='chartDivKeyongyunli'>可用运力
                    </div>
                    <div class="tab yongli zaice selected" data-type='chartDivZaiceyunli'>总运力</div>
                  </div>
                  <div class="detail"></div>
                </div>

                <div class="yunliChart chartDivKeyongyunli hide">
                  <div class="chart">
                    <div class="yunliChartDom" id="yunliChartDom"></div>
                    <div class="legend" id="yunliLegend">
                    </div>
                  </div>
                  <div class="total">
                    <div id="totalFlightNum"></div>
                    <div class="yunliunit">架</div>
                  </div>
                </div>
                <div class="yunliChart chartDivZaiceyunli">
                  <div class="chart">
                    <div class="yunliChartDom" id="yunliChartDom2"></div>
                    <div class="legend" id="yunliLegend2">
                    </div>
                  </div>
                  <div class="total">
                    <div id="totalFlightNum2"></div>
                    <div class="yunliunit">架</div>
                  </div>
                </div>
              </div>

              <div class="b-mainKpi abnormal">
                <div class="title"><span class="blue_l">不正常航班</span></div>
                <div class="flex-box">
                  <div><span class="blue fs14">返航备降</span><br><span id='val_TURNBACK_NO' class="white fs18"></span>
                  </div>
                  <div><span class="blue fs14">取消航班</span><br><span id='val_CANCEL' class="white fs18"></span></div>
                  <div class='delay12'><span class="blue fs14">延误1-2h</span><br><span id='val_DELAY_NO_1_2'
                      class="white fs18"></span></div>
                  <div class='delay24'><span class="blue fs14">延误2-4h</span><br><span id='val_DELAY_NO_2_4'
                      class="white fs18"></span></div>
                  <div style="border-right:none"><span class="blue fs14">延误>4h</span><br><span id='val_DELAY_NO_240'
                      class="white fs18"></span></div>
                </div>
                <div class="more" id="unnormal_detail"><span class="blue text">运行不正常事件</span>
                  <div class="detail"></div>
                </div>
              </div>
            </div>
          </div>
        </div><!-- /block -->

        <div class="block_unsafe block-frame">
          <div class="block-header tit">
            安全监控
            <!-- <div class="datetype-container aqjk datetype1">
                            <div class="dateType day selected" dateType="D">日</div>
                            <div class="dateType year" dateType="Y">年</div>
                        </div> -->
          </div>
          <div class="b-mainKpi unsafeEvent">
            <div class="title"><span class="titleSpan">不安全事件</span>
            </div>
            <div class="subtitle reason">
              原因<div class="detail"></div>
            </div>
            <div class="flexBlock reason-detail">
              <div>
                <div class="reasonType">人为</div>
                <div class="reasonNums humanEventCount">0</div>
              </div>
              <div>
                <div class="reasonType">机械</div>
                <div class="reasonNums machineCount">0</div>
              </div>
              <div>
                <div class="reasonType">天气</div>
                <div class="reasonNums weatherCount">0</div>
              </div>
              <div>
                <div class="reasonType">意外</div>
                <div class="reasonNums accidentCount">0</div>
              </div>
              <div>
                <div class="reasonType">代理</div>
                <div class="reasonNums agencyCount">0</div>
              </div>
              <div class="border-none">
                <div class="reasonType">其他</div>
                <div class="reasonNums otherEventCount">0</div>
              </div>
            </div>
            <div class="subtitle type">
              性质(年累计)<div class="detail"></div>
            </div>
            <div class="flexBlock reason-detail">
              <div>
                <div class="reasonType">事故</div>
                <div class="reasonNums signCount">0</div>
              </div>
              <div>
                <div class="reasonType">严重征候</div>
                <div class="reasonNums seriousSymCount">0</div>
              </div>
              <div>
                <div class="reasonType">责任征候</div>
                <div class="reasonNums generalSymCount">0</div>
              </div>
              <div>
                <div class="reasonType">岗位红线</div>
                <div class="reasonNums securityLineCount">0</div>
              </div>
              <div>
                <div class="reasonType">一类事件</div>
                <div class="reasonNums seriousCount">0</div>
              </div>
              <div class="border-none">
                <div class="reasonType">二类事件</div>
                <div class="reasonNums generalCount">0</div>
              </div>
            </div>

          </div>
        </div>

      </div>

      <div class="block_r1">
        <div class="block-header tit">
          生产经营
          <!-- <div class="datetype-container scyy">
                        <div class="dateType day selected" dateType="D">日</div>
                        <div class="dateType week" dateType="L">周</div>
                        <div class="dateType month" dateType="M">月</div>
                        <div class="dateType year" dateType="Y">年</div>
                    </div> -->
        </div>
        <div class="cotent content_right">
          <div class="row1">
            <div class="b-mainKpi zongshouru" dateKey="" dateType="">
              <div class="header">
                <div class="flexBlock">
                  <span class="labelName">总收入<span style="font-size: 12px;">(客+货+辅)</span></span>
                  <span class="value"><span class="shouruValue">-</span><span class="srunit">万</span></span>
                  <span class="tongbi">同比</span>
                  <span class="tongbiValue"></span>
                  <span class="detail"></span>
                </div>
              </div>
            </div>
            <div class="b-mainKpi keshou EST_INC_FUEL">
              <div class="header">客运收入<span class="subheader fs12">(含航补)</span></div>
              <div class="flexBlock"><span class="singlekpivalue">-</span></div>
              <div class="flexBlock tonghuanbi">
                <div class="huanbi">环比</div>
                <div class="huanbiValue">-</div>
                <div class="tongbi">同比</div>
                <div class="tongbiValue">-</div>
              </div>
            </div>
            <div class="b-mainKpi CARGO_INC nomagin">
              <div class="header">货运收入<div class="detail"></div>
              </div>
              <div class="flexBlock"><span class="singlekpivalue">-</span></div>
              <div class="flexBlock tonghuanbi">
                <div class="huanbi">环比</div>
                <div class="huanbiValue">-</div>
                <div class="tongbi">同比</div>
                <div class="tongbiValue">-</div>
              </div>
            </div>
            <div class="b-mainKpi fuying AUX_INC">
              <div class="header">辅营收入<div class="detail"></div>
              </div>
              <div class="flexBlock"><span class="singlekpivalue">-</span></div>
              <div class="flexBlock tonghuanbi">
                <div class="huanbi">环比</div>
                <div class="huanbiValue">-</div>
                <div class="tongbi">同比</div>
                <div class="tongbiValue"></div>
              </div>
            </div>
            <div class="b-mainKpi nomagin butie ROUTE_SUBSIDY_INC">
              <div class="header">航线补贴
                <div class="detail"></div>
              </div>
              <div class="flexBlock"><span class="singlekpivalue">-</span></div>
              <div class="flexBlock tonghuanbi">
                <div class="huanbi">环比</div>
                <div class="huanbiValue">-</div>
                <div class="tongbi">同比</div>
                <div class="tongbiValue">-</div>
              </div>
            </div>
          </div>

          <div class="row2">
            <div class="tabs">
              <div class="tab traffic keyun selected" data-type='ky'>客运</div>
              <div class="tab traffic huoyun" data-type='hy'>货运</div>
            </div>
            <div id="tab-item-container" class="ky">
              <div class="b-mainKpi kyitem xssr">
                <div class="header">小时收入</div>
                <div class="flexBlock"><span class="singlekpivalue" id='HOUR_INC_FUEL'>-</span></div>
                <div class="flexBlock item xssr">
                  <div class="col1">宽体机</div>
                  <div class="col2" id="W_HOUR_INC_FUEL">-</div>
                  <div class="col3">窄体机</div>
                  <div class="col4" id="N_HOUR_INC_FUEL">-</div>
                </div>

              </div>
              <div class="b-mainKpi nomagin kyitem zglsr CAP_KILO_INC_FUEL">
                <div class="header"> 座公里收入</div>
                <div class="flexBlock"><span class="singlekpivalue" id='xxx'>-</span></div>
                <div class="flexBlock tonghuanbi">
                  <div class="huanbi">环比</div>
                  <div class="huanbiValue">-</div>
                  <div class="tongbi">同比</div>
                  <div class="tongbiValue">-</div>
                </div>
              </div>
              <div class="b-mainKpi kyitem pjpj">
                <div class="header"> 平均票价</div>
                <div class="flexBlock"><span class="singlekpivalue" id="AVG_TKT_PRICE"></span></div>
                <div class="flexBlock item price">
                  <div class="col col1">国内</div>
                  <div class="col col2" id="INL_AVG_TKT_PRICE">-</div>
                  <div class="col col3">国际</div>
                  <div class="col col4" id="INT_AVG_TKT_PRICE">-</div>
                </div>
              </div>
              <div class="b-mainKpi nomagin kyitem kglsr TRV_KILO_INC_FUEL">
                <div class="header"> 客公里收入</div>
                <div class="flexBlock"><span class="singlekpivalue"></span></div>
                <div class="flexBlock tonghuanbi">
                  <div class="huanbi">环比</div>
                  <div class="huanbiValue">-</div>
                  <div class="tongbi">同比</div>
                  <div class="tongbiValue">-</div>
                </div>
              </div>
              <div class="b-mainKpi quanhuoji hyitem CARGO_INC_FULL">
                <div class="header">全货机</div>
                <div class="flexBlock"><span class="singlekpivalue">-</span></div>
                <div class="flexBlock tonghuanbi">
                  <div class="huanbi">环比</div>
                  <div class="huanbiValue">-</div>
                  <div class="tongbi">同比</div>
                  <div class="tongbiValue">-</div>
                </div>
              </div>
              <div class="b-mainKpi nomagin fucang hyitem CARGO_INC_BELLY">
                <div class="header">腹舱</div>
                <div class="flexBlock"><span class="singlekpivalue">-</span></div>
                <div class="flexBlock tonghuanbi">
                  <div class="huanbi">环比</div>
                  <div class="huanbiValue">-</div>
                  <div class="tongbi">同比</div>
                  <div class="tongbiValue">-</div>
                </div>
              </div>
              <div class="b-mainKpi hyitem klh CARGO_INC_PASS">
                <div class="header">客拉货</div>
                <div class="flexBlock"><span class="singlekpivalue">-</span></div>
                <div class="flexBlock tonghuanbi">
                  <div class="huanbi">环比</div>
                  <div class="huanbiValue">-</div>
                  <div class="tongbi">同比</div>
                  <div class="tongbiValue">-</div>
                </div>
              </div>
              <div style="font: 0px/0px sans-serif;clear: both;display: block"> </div>
            </div>
          </div>

          <div class="block_member block-frame">
            <div class=" b-mainKpi jinpeng clk">
              <div class="title"><span class="blue_l">常旅客（金鹏会员）</span></div>
              <div class="chart">
                <div class="jinpengChartDom" id="jinpengChartDom"></div>
                <div class="legend" id="jinpendLegend"></div>
              </div>
              <div class="total">
                <div id="totalMemberNum"></div>
                <div class="fs14">万人</div>
              </div>
            </div>
          </div>


          <div class="block_service block-frame">
            <div class="block-header tit">
              服务
              <!-- <div class="datetype-container fuwu datetype1">
                                <div class="dateType day selected" dateType="D">日</div>
                                <div class="dateType year" dateType="Y">年</div>
                            </div> -->
            </div>

            <div class="tsl">
              <div class="b-mainKpi jftsl nomagin">
                <div class="kpi-detail"></div>
                <div class="flexBlock">
                  <span class="tsl-icon"></span>
                  <span class="labelName">局方投诉率</span>
                  <span id='val_jf_ts' class="value">-</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>

    <!--底部-->
    <div class="footer">
      <!--飞行状态-->
      <div class="flight-status">
        <div class="item1"></div>
        <div class="item2">
          <div class="fs18 blue_l">飞行状态</div>
          <div>
            <span class="fs16">正常</span>
            <span class="fs16">延误</span>
          </div>
        </div>
      </div>
      <div class="execac">
        <div class='icon'></div>
        <span class="fs18 blk blue_l">执行中航班</span>
        <span class="val ffnum fs22 white" id='plane_exec'></span><span class="sub fs18">架</span>
      </div>
    </div>

    <div id='pop_company_shouru_mask' class='windowMask hide'>
    </div>
    <div id='pop_flight_noraml_rate' class='pop hide popWin'>
      <div class="flightNormalRateContainer"></div>
    </div>
    <div id='pop_capacity' class='hide popWin pop-window'>
      <div class="capacity-win-body"></div>
    </div>
    <div id='pop_trv_flight_cargo' class='pop hide popWin pop-window'>
      <div class="trv-flight-cargo"></div>
    </div>
    <div id='pop_utilization_rate' class='pop hide popWin pop-window'>
      <div class="utilization-rate"></div>
    </div>
    <div id='pop_flight_accident' class='pop hide popWin pop-window'>
      <div class="flight-accident"></div>
    </div>
    <div id='pop_caac_complaint' class='pop hide popWin pop-window'>
      <div class="caac-complaint"></div>
    </div>
    <div id='pop_subsidy' class='pop hide popWin pop-window'>
      <div class="subsidy-win-body"></div>
    </div>
    <div id='pop_cargo_detail' class='pop hide popWin pop-window'>
      <div class="cargo-detail-win-body"></div>
    </div>
    <div id='pop_aux_inc' class='pop hide popWin pop-window'>
      <div class="aux-inc-win-body"></div>
    </div>
    <div id='pop_unsafe_detail' class='pop_unsafe_detail pop hide popWin pop-window'>
      <div class="unsafe_detail-win-body"></div>
    </div>
    <div id='pop_unsafe_detail2' class='pop_unsafe_detail pop hide popWin pop-window'>
      <div class="unsafe_detail2-win-body"></div>
    </div>


    <div id='pop_company_shouru' class='pop popWin hide'>
      <div class="shouruWinBody">
      </div>
    </div>

  </div>

  </div>




  </div><!-- /#wrapper -->


  <!-- Popup Page: 正常率 -->
  <script id="noraml_rate_template" type="text/html">
        <div class="flightNormalRateContainer">
            <div class="titleSharpe"></div>
            <div class="title">各航司正常率对比</div>
            <div class="date-componet">
                <div class='dateType con_flex_row'>
                    <div :class="dateType=='D' ? 'tab flex_none  selected' :'tab flex_none'" @click="switchDateType('D')" >日</div>
                    <div :class="dateType=='L' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('L')" >周</div>
                    <div :class="dateType=='M' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('M')" >月</div>
                    <div :class="dateType=='Y' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('Y')"  >年</div>
                </div>
    
                <div class="dateCmp">
                    <div  class="datetimepicker" :class="dateType=='D'?'':'hide'" >
                        <div class='input-group date datetimepicker' ref="datetimepicker_D">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                    <!-- <div class="datetimepicker dropdown" :class="dateType=='L'?'':'hide'" >
                        <button class="btn btn-default dropdown-toggle" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                            Dropdown
                            <span class="caret"></span>
                        
                    </div> -->

                    <div class="datetimepicker" :class="dateType=='L'?'':'hide'" >
                        <div class="week">
                            <span class="combobox_label" @click="dropWeekList" :title="getWeekDesc()" @mouseover="onWeekMouseOver" @mouseout="onWeekMouseOut" >{{getWeekLabel(selectedWeek)}}</span>
                            <span class="combobox_list" :class="showWeekList?'':'hide'" @mouseover="onWeekMouseOver" @mouseout="onWeekMouseOut"  >
                                <span class='item' @click="onWeekChange(item)" v-for="(item,index) in weeks" :key="index" >{{getWeekLabel(item)}}</span>
                            </span>
                        </div>
                    </div>
    
                    <div class="datetimepicker" :class="dateType=='M'?'':'hide'" >
                        <div class='input-group date datetimepicker_month' ref="datetimepicker_M">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                    <div class="datetimepicker"  :class="dateType=='Y'?'':'hide'">
                        <div class='input-group date datetimepicker_year'  ref="datetimepicker_Y">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="closeBtn" @click="closeWin"></div>
            <div class="legends">
                <div class="legend legend1"><div class="lengend-icon lengend-color-green"></div><div>{{normalRateLengend}}</div></div>
                <div class="legend legend2" v-if="isToday"><div class="lengend-icon lengend-color-blue"></div><div>当前预估正常率</div></div>
                <div class="legend legend4"><div class="lengend-icon"></div><div>公司原因延误率</div></div>
                <div class="legend legend3"><div class="lengend-icon"></div><div>平均值</div></div>
            </div>
            <div class="ecdom chartblock" ref="ecDom">
                 
            </div>

            <!-- <div class="otherCmpData">
                <div><span class="companyName">南方航空正常率：</span><span>92.3%</span></div>
                <div><span class="companyName">中国国际航空正常率:</span><span>92.3%</span></div>
                <div><span class="companyName">东方航空正常率:</span><span>92.3%</span></div>
            </div> -->
            <div class="win-footer">
                <div class="bottomSharpe"></div>
            </div>
        </div>
    </script>

  <script id="capacity_template" type="text/html">
        <div class="capacity-win-body">
            <div class="titleSharpe"></div>
            <div class="popWin-tabs">
                <div  :class="showFirstTab ? 'selected' :''"  class="tab" @click="onTabChange(true)">公司-机型
                </div>
                <div :class="showFirstTab? '' :'selected'"  class="tab ac-company"  @click="onTabChange(false)">机型-公司</div>
            </div>
            <div class="title">各航司运力情况对比</div>
            <div class="closeBtn" @click="closeWin"></div>
            <div  class="tab-content" v-bind:class="{hide: !showFirstTab }">
                <div class="companyList" :style="companyStyle" ref="companyList">
                    <div class="company"  @click="queryAcTypeStat(item.companyCode)" v-for="(item,index) in this.data1" :key="index"    v-bind:class="{active:item.companyCode==activeCompanyCode}"   >
                        <div class="company-icon" :style="getStyle(item.companyCode)"></div>
                        <div class="name">{{item.companyName}}</div>
                        <div class="labelname">
                            <div>在册</div>
                            <div>可用</div>
                        </div>
                        <div class="value">
                            <div class="allnum">{{item.data.allNum}} <span class="subunit">架</span></div>
                            <div class="kynum">{{item.data.canUseNum}} <span class="subunit">架</span></div>
                        </div>
                    </div>
                    <div class="clear"></div> 
                </div>
                <div ref="ecDom" class="ecDom chartblock"></div>
            </div>
            <div v-bind:class="{hide:showFirstTab }" class="tab-content aircraft-tab">
                <div class="tabs-container tabs-h-container" ref="tabContainer">
                    <div class="tab-h-item-container">
                        <div class="tab-h-header tab-h1" v-if="this.wideList.length>0">
                            <div class="tab-h-title">
                                宽体机
                            </div>
                        </div>
                        <div class="diamond-box-list diamond-box-gray">
                            <div class="diamond-box"  @click="queryCompanyStat(item.actype)" v-for="(item,index) in this.wideList" :key="index"    v-bind:class="{active:item.actype==activeActype}">
                                <div class="diamond-box-title">{{item.actype}}</div>
                                <div class="diamond-box-line diamond-box-name">
                                    <div class="item-left">在册</div>
                                    <div class="item-right">可用</div>
                                </div>
                                <div class="diamond-box-line">
                                    <div class="item-left">{{item.allNum}}<span class="subunit">架</span></div>
                                    <div class="item-right">{{item.canUseNum}}<span class="subunit">架</span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-h-item-container"  v-if="this.narrowList.length>0">
                        <div class="tab-h-header tab-h2">
                            <div class="tab-h-title">
                                窄体机
                            </div>
                        </div>
                        <div class="diamond-box-list diamond-box-blue">
                            <div class="diamond-box"  @click="queryCompanyStat(item.actype)"   v-for="(item,index) in this.narrowList" :key="index"    v-bind:class="{active:item.actype==activeActype}">
                                <div class="diamond-box-title">{{item.actype}}</div>
                                <div class="diamond-box-line diamond-box-name">
                                    <div class="item-left">在册</div>
                                    <div class="item-right">可用</div>
                                </div>
                                <div class="diamond-box-line">
                                    <div class="item-left">{{item.allNum}}<span class="subunit">架</span></div>
                                    <div class="item-right">{{item.canUseNum}}<span class="subunit">架</span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-h-item-container" v-if="this.rList.length>0">
                        <div class="tab-h-header tab-h3">
                            <div class="tab-h-title">
                                支线机
                            </div>
                        </div>
                        
                        <div class="diamond-box-list diamond-box-blue">
                            <div class="diamond-box"  @click="queryCompanyStat(item.actype)"   v-for="(item,index) in this.rList" :key="index"    v-bind:class="{active:item.actype==activeActype}">
                                <div class="diamond-box-title">{{item.actype}}</div>
                                <div class="diamond-box-line diamond-box-name">
                                    <div class="item-left">在册</div>
                                    <div class="item-right">可用</div>
                                </div>
                                <div class="diamond-box-line">
                                    <div class="item-left">{{item.allNum}}<span class="subunit">架</span></div>
                                    <div class="item-right">{{item.canUseNum}}<span class="subunit">架</span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div ref="ecDom2" class="ecDom2 chartblock"></div>
            </div>
            <div class="win-footer">
                <div class="bottomSharpe"></div>
            </div>
        </div>
    </script>

  <!-- Popup Page: 旅客量、航班量、货运量 -->
  <script id="trv_flight_cargo_compare_template" type="text/html">
        <div class="trv-flight-cargo">
            <div class="titleSharpe"></div>
            <div class="title">各航司旅客量 / 航班量 / 货运量对比</div>
            <div class="date-componet">
                <div class='dateType con_flex_row'>
                    <div :class="dateType=='D' ? 'tab flex_none  selected' :'tab flex_none'" @click="switchDateType('D')" >日</div>
                    <div :class="dateType=='L' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('L')" >周</div>
                    <div :class="dateType=='M' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('M')" >月</div>
                    <div :class="dateType=='Y' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('Y')"  >年</div>
                </div>
    
                <div class="dateCmp">
                    <div  class="datetimepicker" :class="dateType=='D'?'':'hide'" >
                        <div class='input-group date datetimepicker' ref="datetimepicker_D">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                    <div class="datetimepicker" :class="dateType=='L'?'':'hide'" >
                        <div class="week">
                            <span class="combobox_label" @click="dropWeekList" :title="getWeekDesc()" @mouseover="onWeekMouseOver" @mouseout="onWeekMouseOut" >{{getWeekLabel(selectedWeek)}}</span>
                            <span class="combobox_list" :class="showWeekList?'':'hide'" @mouseover="onWeekMouseOver" @mouseout="onWeekMouseOut"  >
                                <span class='item' @click="onWeekChange(item)" v-for="(item,index) in weeks" :key="index" >{{getWeekLabel(item)}}</span>
                            </span>
                        </div>
                    </div>
    
                    <div class="datetimepicker" :class="dateType=='M'?'':'hide'" >
                        <div class='input-group date datetimepicker_month' ref="datetimepicker_M">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                    <div class="datetimepicker"  :class="dateType=='Y'?'':'hide'">
                        <div class='input-group date datetimepicker_year'  ref="datetimepicker_Y">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
           
            <div class="closeBtn" @click="closeWin"></div>
        
            <div class="tabs-container tabs-h-container" style="min-width: 1350px;">
                <div class="tab-h-item-container tab-h-large">
                    <div class="tab-h-header tab-h1">
                        <div class="tab-h-title">
                            旅客量
                        </div>
                        <span class="tab-h-unit">单位：万人次</span>
                    </div>
                    <div class="tab-h-section-title">
                        <div class="title-left">
                            <div class="title-dott">
                                <div class="data-bar-blue"></div>
                            </div>
                            <div class="title-name">国内旅客</div>
                        </div>
                        <div class="title-right">
                            <div class="title-dott">
                                <div class="data-bar-green"></div>
                            </div>
                            <div class="title-name">国际旅客</div>
                        </div>
                    </div>
                    <div class="tab-data-title">
                        <div class="company-name">航司</div>
                        <div class="data-bar">&nbsp;</div>
                        <div class="data-name">旅客量</div>
                        <div class="data-perc">{{isToday?'计划':'占比'}}</div>
                        <div class="data-splitter">&nbsp;</div>
                        <div class="data-bar">&nbsp;</div>
                        <div class="data-name">旅客量</div>
                        <div class="data-perc">{{isToday?'计划':'占比'}}</div>
                    </div>
                    <div class="tab-data-row" v-for="(item,index) in this.trvNums" :key="index" >
                        <div class="company-name">{{item.companyName}}</div>
                        <div class="data-bar">
                            <div class="data-bar-progress data-bar-blue" :style="{'width':item.INL_TRV_NUM_RATE}"></div>
                        </div>
                        <div class="data-name">{{formatData(item.INL_TRV_NUM,10000,2)}}</div>
                        <div class="data-perc">{{isToday?formatData(item.INL_TRV_NUM_P,10000,2):item.INL_TRV_NUM_RATE}}</div>
                        <div class="data-splitter">&nbsp;</div>
                        <div class="data-bar">
                            <div class="data-bar-progress data-bar-green" :style="{'width':item.INT_TRV_NUM_RATE}"></div>
                        </div>
                        <div class="data-name">{{formatData(item.INT_TRV_NUM,10000,2)}}</div>
                        <div class="data-perc">{{isToday?formatData(item.INT_TRV_NUM_P,10000,2):item.INT_TRV_NUM_RATE}}</div>
                    </div>
                    
                    
                </div>

                <div class="tab-h-item-container tab-h-large">
                    <div class="tab-h-header tab-h2">
                        <div class="tab-h-title">
                            航班量
                        </div>
                        <span class="tab-h-unit">单位：班次</span>
                    </div>
                    <div class="tab-h-section-title">
                        <div class="title-left">
                            <div class="title-dott">
                                <div class="data-bar-blue"></div>
                            </div>
                            <div class="title-name">国内航班</div>
                        </div>
                        <div class="title-right">
                            <div class="title-dott">
                                <div class="data-bar-green"></div>
                            </div>
                            <div class="title-name">国际航班</div>
                        </div>
                    </div>
                    <div class="tab-data-title">
                        <div class="company-name">航司</div>
                        <div class="data-bar">&nbsp;</div>
                        <div class="data-name">航班量</div>
                        <div class="data-perc">{{isToday?'计划':'占比'}}</div>
                        <div class="data-splitter">&nbsp;</div>
                        <div class="data-bar">&nbsp;</div>
                        <div class="data-name">航班量</div>
                        <div class="data-perc">{{isToday?'计划':'占比'}}</div>
                    </div>
                    <div class="tab-data-row" v-for="(item,index) in this.flightNums" :key="index" >
                        <div class="company-name">{{item.companyName}}</div>
                        <div class="data-bar">
                            <div class="data-bar-progress data-bar-blue" :style="{'width':item.EXCUTED_NO_INL_RATE}"></div>
                        </div>
                        <div class="data-name">{{formatData(item.EXCUTED_NO_INL,1,0)}}</div>
                        <div class="data-perc">{{isToday?item.EXCUTED_NO_INL_P:item.EXCUTED_NO_INL_RATE}}</div>
                        <div class="data-splitter">&nbsp;</div>
                        <div class="data-bar">
                            <div class="data-bar-progress data-bar-green" :style="{'width':item.EXCUTED_NO_INT_RATE}"></div>
                        </div>
                        <div class="data-name">{{formatData(item.EXCUTED_NO_INT,1,0)}}</div>
                        <div class="data-perc">{{isToday?item.EXCUTED_NO_INT_P:item.EXCUTED_NO_INT_RATE}}</div>
                    </div>
                </div>

                
                <div class="tab-h-item-container tab-h-small">
                    <div class="tab-h-header tab-h3">
                        <div class="tab-h-title">
                            货运量
                        </div>
                        <span class="tab-h-unit">单位：t</span>
                    </div>
                    <div>
                        <div class="chart">
                            <div class="cargoNumDom chartblock" ref="cargoNumDom"></div>
                            <div class="legend">
                                <div class="lengend-row" v-for="(item,index) in this.cargoNums" :key="index" >
                                    <div class="companyName">
                                        <div :style="{'background-color':item.color}">{{item.name}}</div>
                                    </div>
                                    <div class="nums">{{item.value}}</div>
                                    <div class="percentage">
                                        <span>占比 </span> {{item.rate}}
                                    </div>
                                </div>    
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="win-footer">
                <div class="bottomSharpe"></div>
            </div>
        </div>
    </script>

  <!-- 日利用率 -->
  <script id="utilization_rate_template" type="text/html">
        <div class="utilization-rate">
            <div class="titleSharpe"></div>
            <div class="title">各机型日利用率 / 客座率情况对比</div>
            <div class="date-componet">
                <div class='dateType con_flex_row'>
                    <div :class="dateType=='D' ? 'tab flex_none  selected' :'tab flex_none'" @click="switchDateType('D')" >日</div>
                    <div :class="dateType=='L' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('L')" >周</div>
                    <div :class="dateType=='M' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('M')" >月</div>
                    <div :class="dateType=='Y' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('Y')"  >年</div>
                </div>
    
                <div class="dateCmp">
                    <div  class="datetimepicker" :class="dateType=='D'?'':'hide'" >
                        <div class='input-group date datetimepicker' ref="datetimepicker_D">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                    <!-- <div class="datetimepicker dropdown" :class="dateType=='L'?'':'hide'" >
                        <button class="btn btn-default dropdown-toggle" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                            Dropdown
                            <span class="caret"></span>
                        
                    </div> -->

                    <div class="datetimepicker" :class="dateType=='L'?'':'hide'" >
                        <div class="week">
                            <span class="combobox_label" @click="dropWeekList" :title="getWeekDesc()" @mouseover="onWeekMouseOver" @mouseout="onWeekMouseOut" >{{getWeekLabel(selectedWeek)}}</span>
                            <span class="combobox_list" :class="showWeekList?'':'hide'" @mouseover="onWeekMouseOver" @mouseout="onWeekMouseOut"  >
                                <span class='item' @click="onWeekChange(item)" v-for="(item,index) in weeks" :key="index" >{{getWeekLabel(item)}}</span>
                            </span>
                        </div>
                    </div>
    
                    <div class="datetimepicker" :class="dateType=='M'?'':'hide'" >
                        <div class='input-group date datetimepicker_month' ref="datetimepicker_M">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                    <div class="datetimepicker"  :class="dateType=='Y'?'':'hide'">
                        <div class='input-group date datetimepicker_year'  ref="datetimepicker_Y">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
           
            <div class="closeBtn" @click="closeWin"></div>
        
            <div class="tabs-container tabs-v-container">
                <div class="tab-v-row tab-v1" v-if="this.wideList.length>0">
                    <div class="tab-v-title">
                        <span>宽<br/>体<br/>机</span>
                    </div>
                    <div class="tab-v-item-list">
                        <div class="tab-v-item" v-for="(item,idx) in wideList" :key="idx">
                            <div class="tab-item-title">{{item.actype}}</div>
                            <div class="tab-data-title">
                                <div class="company-name">航司</div>
                                <div class="data-bar">&nbsp;</div>
                                <div class="data-name">日利用率</div>
                                <div class="data-perc color-green">客座率</div>
                            </div>
                            <div class="tab-data-row" v-for="(companyItem,index) in item.companyList" :key="index" >
                                <div class="company-name">{{companyItem.companyName}}</div>
                                <div class="data-bar">
                                    <div class="data-bar-progress data-bar-blue" :style="getStyleWidth(companyItem)"></div>
                                </div>
                                <div class="data-name">{{getAcUtilRate(companyItem)}}</div>
                                <div class="data-perc">{{getTrvRate(companyItem)}}</div>
                            </div>
                        </div>
                        
                    </div>
                </div>
                <div class="tab-v-row tab-v2" v-if="this.narrowList.length>0">
                    <div class="tab-v-title">
                        <span>窄<br/>体<br/>机</span>
                    </div>
                    <div class="tab-v-item-list">
                        <div class="tab-v-item" v-for="(item,idx) in narrowList" :key="idx">
                            <div class="tab-item-title">{{item.actype}}</div>
                            <div class="tab-data-title">
                                <div class="company-name">航司</div>
                                <div class="data-bar">&nbsp;</div>
                                <div class="data-name">日利用率</div>
                                <div class="data-perc color-green">客座率</div>
                            </div>
                            <div class="tab-data-row" v-for="(companyItem,index) in item.companyList" :key="index" >
                                <div class="company-name">{{companyItem.companyName}}</div>
                                <div class="data-bar">
                                    <div class="data-bar-progress data-bar-blue" :style="getStyleWidth(companyItem)"></div>
                                </div>
                                <div class="data-name">{{getAcUtilRate(companyItem)}}</div>
                                <div class="data-perc">{{getTrvRate(companyItem)}}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tab-v-row tab-v3" v-if="this.rList.length>0" >
                    <div class="tab-v-title">
                        <span>支<br/>线<br/>机</span>
                    </div>
                    <div class="tab-v-item-list">
                        <div class="tab-v-item" v-for="(item,idx) in rList" :key="idx">
                            <div class="tab-item-title">{{item.actype}}</div>
                            <div class="tab-data-title">
                                <div class="company-name">航司</div>
                                <div class="data-bar">&nbsp;</div>
                                <div class="data-name">日利用率</div>
                                <div class="data-perc color-green">客座率</div>
                            </div>
                            <div class="tab-data-row" v-for="(companyItem,index) in item.companyList" :key="index" >
                                <div class="company-name">{{companyItem.companyName}}</div>
                                <div class="data-bar">
                                    <div class="data-bar-progress data-bar-blue" :style="getStyleWidth(companyItem)"></div>
                                </div>
                                <div class="data-name">{{getAcUtilRate(companyItem)}}</div>
                                <div class="data-perc">{{getTrvRate(companyItem)}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="win-footer">
                <div class="bottomSharpe"></div>
            </div>
        </div>
    </script>

  <!-- 运行不正常事件 -->
  <script id="flight_accident_template" type="text/html">
        <div class="flight-accident">
            <div class="titleSharpe"></div>
            <div class="title">运行不正常事件</div>
            <div class="date-componet">
                <div class='dateType con_flex_row'>
                    <div :class="dateType=='D' ? 'tab flex_none  selected' :'tab flex_none'" @click="switchDateType('D')" >日</div>
                    <div :class="dateType=='L' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('L')" >周</div>
                    <div :class="dateType=='M' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('M')" >月</div>
                    <div :class="dateType=='Y' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('Y')"  >年</div>
                </div>
    
                <div class="dateCmp">
                    <div  class="datetimepicker" :class="dateType=='D'?'':'hide'" >
                        <div class='input-group date datetimepicker' ref="datetimepicker_D">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                    <!-- <div class="datetimepicker dropdown" :class="dateType=='L'?'':'hide'" >
                        <button class="btn btn-default dropdown-toggle" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                            Dropdown
                            <span class="caret"></span>
                        
                    </div> -->

                    <div class="datetimepicker" :class="dateType=='L'?'':'hide'" >
                        <div class="week">
                            <span class="combobox_label" @click="dropWeekList" :title="getWeekDesc()" @mouseover="onWeekMouseOver" @mouseout="onWeekMouseOut" >{{getWeekLabel(selectedWeek)}}</span>
                            <span class="combobox_list" :class="showWeekList?'':'hide'" @mouseover="onWeekMouseOver" @mouseout="onWeekMouseOut"  >
                                <span class='item' @click="onWeekChange(item)" v-for="(item,index) in weeks" :key="index" >{{getWeekLabel(item)}}</span>
                            </span>
                        </div>
                    </div>
    
                    <div class="datetimepicker" :class="dateType=='M'?'':'hide'" >
                        <div class='input-group date datetimepicker_month' ref="datetimepicker_M">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                    <div class="datetimepicker"  :class="dateType=='Y'?'':'hide'">
                        <div class='input-group date datetimepicker_year'  ref="datetimepicker_Y">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
           
            <div class="closeBtn" @click="closeWin"></div>

            <div class="win-body">
                <div class="h-row">
                    <div class="col1">
                        <div class="chart-row chart-flight-acc-type">
                            <div class="col-title">事件总计</div>
                            <div class="chart-title">
                                总计
                                <span class="chart-title-data">
                                    {{flightAccidentCount}}<span class="chart-title-unit">起</span>
                                </span>
                                <span class="char-title-space"></span>
                                <span class="chart-title-comp">
                                    环比
                                    <span class="compare-equal" v-if="flightAccidentComp >= 9999">-</span>
                                    <span class="compare-up" v-else-if="flightAccidentComp >= 0">{{flightAccidentComp}}%</span>
                                    <span class="compare-down" v-else-if="flightAccidentComp < 0">{{flightAccidentComp}}%</span>
                                    <span class="compare-equal" v-else>{{flightAccidentComp}}%</span>
                                </span>
                            </div>

                            <div class="types">
                                <div class="type" v-bind:class="{active:item.code == activeItem.code}" v-for="(item,index) in flightAccidentData1" :key="index" @click="filterEvent(item)">
                                    <span class="dot" v-bind:style="{color:item.color}">●</span>
                                    <span class="name">{{item.name}}</span>
                                    <span class="chart-title-data">
                                        {{item.count}}<span class="chart-title-unit">起</span>
                                    </span>
                                    <span class="char-title-space"></span>
                                    <span class="chart-title-comp">
                                        环比
                                        <span class="compare-equal" v-if="item.comp >= 9999">-</span>
                                        <span class="compare-up" v-else-if="item.comp >= 0">{{item.comp}}%</span>
                                        <span class="compare-down" v-else-if="item.comp < 0">{{item.comp}}%</span>
                                        <span class="compare-equal" v-else>{{item.comp}}%</span>
                                    </span>
                                </div>
                            </div>

                            <div class="event-detail-container events" v-bind:class="{hide:companyEventList.length==0}">
                                <div class="companylist" ref="companyList">
                                    <div class="company-container">
                                        <div class="companyItem" v-for="(item,index) in companyEventList" :key="index" v-bind:class="{'companyItem-empty-event':item.emptyEvent==true}">
                                            <div class="companyName">
                                                <div class="company-icon" :style='getStyle(item.companyCode)'></div>
                                                <div class="name"><span class="company">{{item.companyName}}</span> <span class="count">({{item.events.length}}班次)</span></div>
                                                <div class="btns" v-if="item.events.length>4">
                                                    <span class="pre-btn" v-bind:class="{disabled:!hasPre(item)}" @click="prePage(item)"></span>
                                                    <span class="next-btn" v-bind:class="{disabled:!hasNext(item)}"  @click="nextPage(item)"></span>
                                                </div>
                                            </div>
                                            <div class="companyEvent">
                                                <div class="flight" v-bind:class="{active:event.id == activeEvent.id}" @click="showDetail(event)" v-for="(event,index) in item.sliceEvents" :key="index" >
                                                    <div class="flightDate">{{moment(event.flightDateStr).format('MM/DD')}} {{formatToff(event.std)}}</div>
                                                    <div class="flightNo">{{event.flightNo}}</div>
                                                    <div class="route">{{event.airlineCn}}</div>
                                                    <div class="flight-type"><span class="dot" v-bind:style="{color:activeItem.color} ">●</span>{{getAtrrName(event)}}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="event-detail" ref="eventDetail" v-if="activeEvent.id!=null">
                                    <div class="event-box">
                                        <div class="flight-identify">
                                            <div class="flightDate">{{moment(activeEvent.flightDateStr).format('MM/DD')}}</div>
                                            <div class="acNo">{{activeEvent.longReg}}</div>
                                            <!-- <div class="layout">{{activeEvent.cabin}}</div> -->
                                        </div>
                                        <div class="leg-info">
                                            <div class="route">{{activeEvent.airlineCn}}</div>
                                            <div class="times-name">
                                                <div>计划起飞</div>
                                                <div>计划到达</div>
                                                <div>调整起飞</div>
                                                <div>调整到达</div>
                                            </div>
                                            <div class="times">
                                                <div>{{activeEvent.std_chn}}</div>
                                                <div>{{activeEvent.sta_chn}}</div>
                                                <div>{{activeEvent.t_off_chn}}</div>
                                                <div>{{activeEvent.t_dwn_chn}}</div>
                                            </div>
                                        </div>
                                        <div class="evt-row">
                                            <div class="event-label">不正常属性：</div><div>{{activeEvent.eventAttrName}}</div>
                                        </div>
                                        <div class="evt-row">
                                            <div class="event-label">事件类型：</div><div>{{activeEvent.eventTypeName}}</div>
                                        </div>

                                        <div class="event-info">
                                            <div class="sub-title">调整情况</div>
                                            <div class="flight-info evt-content">
                                                {{activeEvent.title}}
                                            </div>
                                            <div class="sub-item-label">事件描述</div>
                                            <div class="evt-content">
                                                    {{activeEvent.description}}
                                            </div>

                                            <div class="evt-sitem-row">
                                                <div class="event-label">发布时间：</div><div>{{moment(activeEvent.noticeTimeStr).format('MM/DD HH:mm')}}</div>
                                            </div>
                                            <div class="evt-sitem-row">
                                                <div class="event-label">发布人：</div><div>{{activeEvent.createdByName}}</div>
                                            </div>
                                        </div>
                                        <div class="event-info">
                                            <div class="sub-title">调查结果</div>
                                            <div class="evt-content">
                                                {{activeEvent.surveyResult}}
                                            </div>
                                        </div>
                                        <div class="event-info">
                                            <div class="sub-title">整改措施/处理结果</div>
                                            <div class="evt-content">
                                                {{activeEvent.processResult}}
                                            </div>
                                        </div>
                                        <div class="event-info">
                                            <div class="sub-title">惩处情况</div>
                                            <div class="evt-content">
                                              {{ activeEvent.isPunish != 'false' ? activeEvent.punishInfo : '无'}}
                                            </div>
                                        </div>
                                        <div class="event-info" v-if="!hasComment(activeEvent)" >
                                            <div class="sub-title">批示意见</div>
                                            <div class="evt-content">
                                               无
                                            </div>
                                        </div>
                                        <div class="event-info" v-else >
                                            <div  v-for="(item,index) in activeEvent.comments">
                                                <div class="sub-title">批示意见</div>
                                                <div class="evt-content">
                                                    {{item.content}}
                                                </div>
                                                <div class="sub-title">批示人及批示时间</div>
                                                <div class="evt-content">
                                                    {{item.commentByName +  ' ' + item.commentTimeStr}} 
                                                </div>

                                            </div>
                                           
                                        </div>
                                        <div class="event-info">
                                            <div class="sub-title">状态跟踪</div>
                                            <div class="evt-content">
                                              {{getStatusText()}}
                                            </div>
                                        </div>
                                        <div >
                                            <br/>
                                        </div>
                                      
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="col2">
                        <div class="chart-row chart-flight-acc-property">
                            <div class="chart-title">事件属性分布</div>
                            <div class="chart-container chart-left">
                                <div class="chart-diagram chartblock" ref="flightAccByProperty"  prop-width="950" prop-height="200" ></div>
                                <div class="chart-legend">
                                    <div class="chart-item" v-for="(item,index) in this.flightAccidentData3" :key="index" >
                                        <div class="item-color" :style="{'background-color':item.color}"></div>
                                        <div class="item-name color-blue">{{item.name}}</div>
                                        <div class="item-value color-white">{{item.value}}</div>起
                                        <div class="item-perc color-white">{{item.perc}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
 

            <div class="win-footer">
                <div class="bottomSharpe"></div>
            </div>
        </div>
    </script>

  <script id="subsidy_template" type="text/html">
        <div class="subsidy-win-body">
            <div class="titleSharpe"></div>
            <div class="title">各航司航线补贴情况对比</div>

            <div class="date-componet">
                <div class='dateType con_flex_row'>
                    <div :class="dateType=='D' ? 'tab flex_none  selected' :'tab flex_none'" @click="switchDateType('D')" >日</div>
                    <div :class="dateType=='L' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('L')" >周</div>
                    <div :class="dateType=='M' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('M')" >月</div>
                    <div :class="dateType=='Y' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('Y')"  >年</div>
                </div>
    
                <div class="dateCmp">
                    <div  class="datetimepicker" :class="dateType=='D'?'':'hide'" >
                        <div class='input-group date datetimepicker' ref="datetimepicker_D">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                    <div class="datetimepicker" :class="dateType=='L'?'':'hide'" >
                        <div class="week">
                            <span class="combobox_label" @click="dropWeekList" :title="getWeekDesc()" @mouseover="onWeekMouseOver" @mouseout="onWeekMouseOut" >{{getWeekLabel(selectedWeek)}}</span>
                            <span class="combobox_list" :class="showWeekList?'':'hide'" @mouseover="onWeekMouseOver" @mouseout="onWeekMouseOut"  >
                                <span class='item' @click="onWeekChange(item)" v-for="(item,index) in weeks" :key="index" >{{getWeekLabel(item)}}</span>
                            </span>
                        </div>
                    </div>
    
                    <div class="datetimepicker" :class="dateType=='M'?'':'hide'" >
                        <div class='input-group date datetimepicker_month' ref="datetimepicker_M">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                    <div class="datetimepicker"  :class="dateType=='Y'?'':'hide'">
                        <div class='input-group date datetimepicker_year'  ref="datetimepicker_Y">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="closeBtn" @click="closeWin"></div>

            <div class="companyList" :style="companyStyle">
                <div class="company active"  v-for="(item,index) in this.data1" :key="index">
                    <div class="company-icon" :style="getStyle(item.companyCode)"></div>
                    <div class="name">{{item.companyName}}</div>
                    <div class="labelname">
                        <div>本期</div>
                        <div>同比</div>
                    </div>
                    <div class="value">
                        <div>{{getKpiValue(item)}}<span class="fs14">万</span></div>
                        <div class="kpiValue" v-bind:class="{down:item.data.ROUTE_SUBSIDY_INC[0].KPI_RATIO_TQ<0}">{{getTqRadio(item)}}</div>
                    </div>
                </div>
                <div class="clear"></div> 
            </div>
            <!-- <div class="popWin-tabs">
                <div  :class="showFirstTab ? 'selected' :''"  class="tab" @click="onTabChange(true)">航线补贴
                </div>
                <div :class="showFirstTab? '' :'selected'"  class="tab ac-company"  @click="onTabChange(false)">政策补贴</div>
            </div> -->
            <div  class="tab-content">
                <div ref="ecDom" class="ecDom chartblock"></div>
            </div>
        </div>
    </script>

  <script id="cargo_detail_template" type="text/html">
        <div class="cargo-detail-win-body">
            <div class="titleSharpe"></div>
            <div class="title">各航司货运情况</div>
            <div class="date-componet">
                <div class='dateType con_flex_row'>
                    <div :class="dateType=='D' ? 'tab flex_none  selected' :'tab flex_none'" @click="switchDateType('D')" >日</div>
                    <div :class="dateType=='L' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('L')" >周</div>
                    <div :class="dateType=='M' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('M')" >月</div>
                    <div :class="dateType=='Y' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('Y')"  >年</div>
                </div>
    
                <div class="dateCmp">
                    <div  class="datetimepicker" :class="dateType=='D'?'':'hide'" >
                        <div class='input-group date datetimepicker' ref="datetimepicker_D">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                    <div class="datetimepicker" :class="dateType=='L'?'':'hide'" >
                        <div class="week">
                            <span class="combobox_label" @click="dropWeekList" :title="getWeekDesc()" @mouseover="onWeekMouseOver" @mouseout="onWeekMouseOut" >{{getWeekLabel(selectedWeek)}}</span>
                            <span class="combobox_list" :class="showWeekList?'':'hide'" @mouseover="onWeekMouseOver" @mouseout="onWeekMouseOut"  >
                                <span class='item' @click="onWeekChange(item)" v-for="(item,index) in weeks" :key="index" >{{getWeekLabel(item)}}</span>
                            </span>
                        </div>
                    </div>
    
                    <div class="datetimepicker" :class="dateType=='M'?'':'hide'" >
                        <div class='input-group date datetimepicker_month' ref="datetimepicker_M">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                    <div class="datetimepicker"  :class="dateType=='Y'?'':'hide'">
                        <div class='input-group date datetimepicker_year'  ref="datetimepicker_Y">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
           
            <div class="closeBtn" @click="closeWin"></div>
        
            <div class="tabs-container tabs-h-container">
                <div class="tab-h-item-container tab-h-large">
                    <div class="tab-vb1">
                        <div class="tab-h-header tab-h1">
                            <div class="tab-h-title">
                                全货机
                            </div>
                        </div>
                        <div class="tab-h-section-title">
                            <div class="title-left">
                                <div class="title-dott">
                                    <div class="data-bar-blue"></div>
                                </div>
                                <div class="title-name">班次</div>
                            </div>
                            <div class="title-right">
                                <div class="title-dott">
                                    <div class="data-bar-green"></div>
                                </div>
                                <div class="title-name">收入(万元)</div>
                            </div>
                        </div>
                        <div class="tab-data-title">
                            <div class="company-name">航司</div>
                            <div class="data-bar">&nbsp;</div>
                            <div class="data-name">本期</div>
                            <div class="data-perc">同比</div>
                            <div class="data-splitter">&nbsp;</div>
                            <div class="data-bar">&nbsp;</div>
                            <div class="data-name">本期</div>
                            <div class="data-perc">同比</div>
                        </div>
                        <div class="tab-data-row" v-for="(item,index) in this.quanhuojis" :key="index" >
                            <div class="company-name">{{item.companyName}}</div>
                            <div class="data-bar">
                                <div class="data-bar-progress data-bar-blue" :style="{'width':item.shiftsRate}"></div>
                            </div>
                            <div class="data-name">{{formatData(item.shifts,1,0)}}</div>
                            <div class="data-perc" v-bind:class="{down:item.shiftsTB<0}">{{formatTongBi(item.shiftsTB)}}</div>
                            <div class="data-splitter">&nbsp;</div>
                            <div class="data-bar">
                                <div class="data-bar-progress data-bar-green" :style="{'width':item.incRate}"></div>
                            </div>
                            <div class="data-name">{{formatData(item.inc,1,1)}}</div>
                            <div class="data-perc" v-bind:class="{down:item.incTB<0}">{{formatTongBi(item.incTB)}}</div>
                        </div>
                    </div>
                    <div class="tab-vb2">
                        <div class="tab-h-header tab-h2">
                            <div class="tab-h-title">
                                腹舱
                            </div>
                        </div>
                        <div class="tab-h-section-title">
                            <div class="title-left">
                                <div class="title-dott">
                                    <div class="data-bar-blue"></div>
                                </div>
                                <div class="title-name">班次</div>
                            </div>
                            <div class="title-right">
                                <div class="title-dott">
                                    <div class="data-bar-green"></div>
                                </div>
                                <div class="title-name">收入(万元)</div>
                            </div>
                        </div>
                        <div class="tab-data-title">
                            <div class="company-name">航司</div>
                            <div class="data-bar">&nbsp;</div>
                            <div class="data-name">本期</div>
                            <div class="data-perc">同比</div>
                            <div class="data-splitter">&nbsp;</div>
                            <div class="data-bar">&nbsp;</div>
                            <div class="data-name">本期</div>
                            <div class="data-perc">同比</div>
                        </div>
                        <div class="tab-data-row" v-for="(item,index) in this.fucangs" :key="index" >
                            <div class="company-name">{{item.companyName}}</div>
                            <div class="data-bar">
                                <div class="data-bar-progress data-bar-blue" :style="{'width':item.shiftsRate}"></div>
                            </div>
                            <div class="data-name">{{formatData(item.shifts,1,0)}}</div>
                            <div class="data-perc" v-bind:class="{down:item.shiftsTB<0}">{{formatTongBi(item.shiftsTB)}}</div>
                            <div class="data-splitter">&nbsp;</div>
                            <div class="data-bar">
                                <div class="data-bar-progress data-bar-green" :style="{'width':item.incRate}"></div>
                            </div>
                            <div class="data-name">{{formatData(item.inc,1,1)}}</div>
                            <div class="data-perc" v-bind:class="{down:item.incTB<0}">{{formatTongBi(item.incTB)}}</div>
                        </div>
                    </div>
                </div>

                <div class="tab-h-item-container tab-h-large kelahuo">
                    <div class="tab-h-header tab-h3">
                        <div class="tab-h-title">
                            客拉货
                        </div>
                    </div>
                    <div class="tab-h-section-title">
                        <div class="title-left">
                            <div class="title-dott">
                                <div class="data-bar-blue"></div>
                            </div>
                            <div class="title-name">班次</div>
                        </div>
                        <div class="title-right">
                            <div class="title-dott">
                                <div class="data-bar-green"></div>
                            </div>
                            <div class="title-name">收入(万元)</div>
                        </div>
                    </div>
                    <div class="tab-data-title">
                        <div class="company-name">航司</div>
                        <div class="data-bar">&nbsp;</div>
                        <div class="data-name">本期</div>
                        <div class="data-perc">同比</div>
                        <div class="data-splitter">&nbsp;</div>
                        <div class="data-bar">&nbsp;</div>
                        <div class="data-name">本期</div>
                        <div class="data-perc">同比</div>
                    </div>
                    <div class="tab-data-row" v-for="(item,index) in this.kelahuos" :key="index" >
                        <div class="company-name">{{item.companyName}}</div>
                        <div class="data-bar">
                            <div class="data-bar-progress data-bar-blue" :style="{'width':item.shiftsRate}"></div>
                        </div>
                        <div class="data-name">{{formatData(item.shifts,1,0)}}</div>
                        <div class="data-perc" v-bind:class="{down:item.shiftsTB<0}">{{formatTongBi(item.shiftsTB)}}</div>
                        <div class="data-splitter">&nbsp;</div>
                        <div class="data-bar">
                            <div class="data-bar-progress data-bar-green" :style="{'width':item.incRate}"></div>
                        </div>
                        <div class="data-name">{{formatData(item.inc,1,1)}}</div>
                        <div class="data-perc" v-bind:class="{down:item.incTB<0}">{{formatTongBi(item.incTB)}}</div>
                    </div>
                </div>             
                
            </div>
            <div class="win-footer">
                <div class="bottomSharpe"></div>
            </div>
        </div>
    </script>
  <script id="auxInc_template" type="text/html">
        <div class="aux-inc-win-body">
            <div class="titleSharpe"></div>
            <div class="title">各航司辅营收入情况</div>
            <div class="date-componet">
                <div class='dateType con_flex_row'>
                    <div :class="dateType=='D' ? 'tab flex_none  selected' :'tab flex_none'" @click="switchDateType('D')" >日</div>
                    <div :class="dateType=='L' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('L')" >周</div>
                    <div :class="dateType=='M' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('M')" >月</div>
                    <div :class="dateType=='Y' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('Y')"  >年</div>
                </div>
    
                <div class="dateCmp">
                    <div  class="datetimepicker" :class="dateType=='D'?'':'hide'" >
                        <div class='input-group date datetimepicker' ref="datetimepicker_D">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                    <div class="datetimepicker" :class="dateType=='L'?'':'hide'" >
                        <div class="week">
                            <span class="combobox_label" @click="dropWeekList" :title="getWeekDesc()" @mouseover="onWeekMouseOver" @mouseout="onWeekMouseOut" >{{getWeekLabel(selectedWeek)}}</span>
                            <span class="combobox_list" :class="showWeekList?'':'hide'" @mouseover="onWeekMouseOver" @mouseout="onWeekMouseOut"  >
                                <span class='item' @click="onWeekChange(item)" v-for="(item,index) in weeks" :key="index" >{{getWeekLabel(item)}}</span>
                            </span>
                        </div>
                    </div>
    
                    <div class="datetimepicker" :class="dateType=='M'?'':'hide'" >
                        <div class='input-group date datetimepicker_month' ref="datetimepicker_M">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                    <div class="datetimepicker"  :class="dateType=='Y'?'':'hide'">
                        <div class='input-group date datetimepicker_year'  ref="datetimepicker_Y">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
           
            <div class="closeBtn" @click="closeWin"></div>
            <div class="win-content">
                <div class="inc-composition">
                    <div class="inc-title">收入组成</div>
                    <div class="inc-chart-container">
                        <div>
                          <div class="edom chartblock" ref="edom"></div>
                        </div>
                        <div style="width:1090px">
                            <div class="data-item data-item-title" v-for="(item,index) in [1,2,3]" :key="index">
                                <div class="company">航司</div>
                                <div class="inc"><div>总收入</div><div>(万元)</div></div>
                                <div class="inc-tb">总收入<div style="padding-left:7px">同比</div></div>
                                <div class="inc-avg">人均收入<div style="padding-left:21px">(元)</div></div>
                            </div>
                            <div class="data-item"  v-for="(item,index) in this.companyDataList" :key="index" >
                                <div class="company"><div class="dott" v-bind:style="{'background-color':item.color}"></div><div>{{item.companyName}}</div></div>
                                <div class="inc">{{item.inc}}</div>
                                <div class="inc-tb" v-bind:class="{down:item.incTB<0}">{{getTqRadio(item.incTB)}}</div>
                                <div class="inc-avg">{{item.incAvg}}</div>
                            </div> 
                        </div>
                    </div>
                    <div class="splitLine"></div>
                </div>
                <div class="edom2 chartblock" ref="edom2"></div>
            </div>
            <div class="win-footer">
                <div class="bottomSharpe"></div>
            </div>
        </div>
    </script>

  <!-- 局方投诉率二级页面 -->
  <script id="caac_complaint_template" type="text/html">
        <div class="flight-accident">
            <div class="company">
                <span class="combobox_label" @click="dropCompanyList"   @mouseover="onCompanyMouseOver" @mouseout="onCompanyMouseOut" >{{selectedCompany.name}}</span>
                <span class="combobox_list" v-bind:class="{hide:!showCompanyList}" @mouseover="onCompanyMouseOver" @mouseout="onCompanyMouseOut"  >
                    <span class='item' @click="onCompanyChange(item)" v-for="(item,index) in companylist" :key="index" >{{item.name}}</span>
                </span>
            </div>
            <div class="titleSharpe"></div>
            <div class="title">局方投诉率</div>
            <div class="date-componet">
                <div class='dateType con_flex_row'>
                    <div :class="dateType=='D' ? 'tab flex_none  selected' :'tab flex_none'" @click="switchDateType('D')" >日</div>
                    <div :class="dateType=='L' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('L')" >周</div>
                    <div :class="dateType=='M' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('M')" >月</div>
                    <div :class="dateType=='Y' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('Y')"  >年</div>
                </div>
            </div>
           
            <div class="closeBtn" @click="closeWin"></div>

            <div class="win-content">
                <div class="chart-row chart-caac-complaint">
                    <div class="chart-container chart-center">
                        <div class="chart-diagram chartblock" ref="caacComplaint"></div>
                    </div>
                </div>
            </div>

            <div class="win-footer">
                <div class="bottomSharpe"></div>
            </div>
        </div>
    </script>
  <!-- 不安全事件 -->
  <script id="unsafe_detail_template" type="text/html">
        <div class="unsafe_detail-win-body">
            <div class="titleSharpe"></div>
            <div class="title">安全不正常事件</div>
            <div class="date-componet">
                <div class='dateType con_flex_row'>
                    <div :class="dateType=='D' ? 'tab flex_none  selected' :'tab flex_none'" @click="switchDateType('D')" >日</div>
                    <div :class="dateType=='L' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('L')" >周</div>
                    <div :class="dateType=='M' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('M')" >月</div>
                    <div :class="dateType=='Y' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('Y')"  >年</div>
                </div>
    
                <div class="dateCmp">
                    <div  class="datetimepicker" :class="dateType=='D'?'':'hide'" >
                        <div class='input-group date datetimepicker' ref="datetimepicker_D">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                    <div class="datetimepicker" :class="dateType=='L'?'':'hide'" >
                        <div class="week">
                            <span class="combobox_label" @click="dropWeekList" :title="getWeekDesc()" @mouseover="onWeekMouseOver" @mouseout="onWeekMouseOut" >{{getWeekLabel(selectedWeek)}}</span>
                            <span class="combobox_list" :class="showWeekList?'':'hide'" @mouseover="onWeekMouseOver" @mouseout="onWeekMouseOut"  >
                                <span class='item' @click="onWeekChange(item)" v-for="(item,index) in weeks" :key="index" >{{getWeekLabel(item)}}</span>
                            </span>
                        </div>
                    </div>
    
                    <div class="datetimepicker" :class="dateType=='M'?'':'hide'" >
                        <div class='input-group date datetimepicker_month' ref="datetimepicker_M">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                    <div class="datetimepicker"  :class="dateType=='Y'?'':'hide'">
                        <div class='input-group date datetimepicker_year'  ref="datetimepicker_Y">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
           
            <div class="closeBtn" @click="closeWin"></div>

            <div class="win-content">
                <div class="types">
                    <div class="tuli">图例</div>
                    <div class="type" v-bind:class="{active:checkIsActive(item)}" @click="filterEvent(item.name)" v-for="(item,index) in types" :key="index">
                        <span class="dot" v-bind:style="{color:item.color} ">●</span>{{item.name}}
                    </div>
                </div>
                <div class="events">
                    <div class="chart-row chart-flight-acc-type">
                        <div class="event-detail-container events" v-bind:class="{hide:companyEventList.length==0}">
                            <div class="companylist" ref="companyList">
                                <div class="company-container">
                                    <div class="companyItem" v-for="(item,index) in companyEventList" :key="index"  v-bind:class="{'companyItem-empty-event':item.emptyEvent==true}">
                                        <div class="companyName">
                                            <div class="company-icon" :style='getStyle(item.companyCode)'></div>
                                            <div class="name"><span class="company">{{item.companyName}}</span> <span class="count">({{item.events.length}}班次)</span></div>
                                            <div class="btns" v-if="item.events.length>4">
                                                <span class="pre-btn" v-bind:class="{disabled:!hasPre(item)}" @click="prePage(item)"></span>
                                                <span class="next-btn" v-bind:class="{disabled:!hasNext(item)}"  @click="nextPage(item)"></span>
                                            </div>
                                        </div>
                                        <div class="companyEvent">
                                            <div class="flight" v-bind:class="{active:event.ID == activeEvent.ID}" @click="showDetail(event)" v-for="(event,index) in item.sliceEvents" :key="index" >
                                                <div class="flightDate">{{moment(event.FLIGHT_DATE).format('MM/DD')}} {{event.LOCAL_ATD==''?'-':moment(event.LOCAL_ATD).format('HH:mm')}}</div>
                                                <div class="flightNo">{{event.FLIGHT_NO}}</div>
                                                <div class="route">{{event.DEPSTN_NAME}} - {{event.ARRSTN_NAME}}</div>
                                                <div class="flight-type"><span class="dot" v-bind:style="{color:getTypeColor(event)} ">●</span>{{event.EVENT_TYPE==''?'-':event.EVENT_TYPE}}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="event-detail" ref="eventDetail" v-if="activeEvent.ID!=null">
                                <div class="event-box">
                                    <div class="flight-identify">
                                        <div class="flightDate">{{moment(activeEvent.FLIGHT_DATE).format('YYYY/MM/DD')}}</div>
                                        <div class="acNo">{{activeEvent.LONG_REG}}</div>
                                    </div>
                                    <div class="leg-info">
                                        <div class="route">{{activeEvent.DEPSTN_NAME}} - {{activeEvent.ARRSTN_NAME}}</div>
                                        <div class="times-name">
                                            <div>计划起飞</div>
                                            <div>计划到达</div>
                                            <div>调整起飞</div>
                                            <div>调整到达</div>
                                        </div>
                                        <div class="times">
                                            <div>{{formatTimeStr(activeEvent.STD)}}</div>
                                            <div>{{formatTimeStr(activeEvent.STA)}}</div>
                                            <div>{{formatTime(activeEvent.LOCAL_ATD)}}</div>
                                            <div>{{formatTime(activeEvent.LOCAL_ATA)}}</div>
                                        </div>
                                    </div>
                                  
                                    <div class="evt-row">
                                        <div class="event-label">事件定性：</div><div>{{activeEvent.SMS_EVENT_LEVEL}}</div>
                                    </div>
                                    <div class="evt-row">
                                        <div class="event-label">上报时间:</div><div>{{moment(activeEvent.NOTICE_TIME).format('YYYY/MM/DD HH:mm')}}</div>
                                    </div>
                                    <div class="event-info">
                                        <div class="evt-row">
                                            <div class="event-label">不正常情况:</div><div>{{activeEvent.EVENT_TYPE}}</div>
                                        </div>
                                        <div class="flight-info evt-content">
                                            {{activeEvent.TITLE}}
                                        </div>
                                    </div>
                                    <div class="event-info">
                                        <div class="sub-title">事件描述</div>
                                        <div class="evt-content">
                                            {{activeEvent.DESCRIPTION}}
                                        </div>
                                    </div>
                                    <div class="event-info">
                                        <div class="sub-title">处理情况</div>
                                        <div class="evt-content">
                                            {{activeEvent.DEAL}}
                                        </div>
                                    </div>
                                    <div class="event-info">
                                        <div class="sub-title">安全措施</div>
                                        <div class="evt-content">
                                            {{activeEvent.PRECAUTION}}
                                        </div>
                                    </div>
                                    <div class="event-info">
                                        <div class="sub-title">状态跟踪</div>
                                        <div class="evt-content">
                                            {{getStatusText()}}
                                        </div>
                                    </div>
                                    <div class="event-info">
                                        <div class="sub-title">惩处情况</div>
                                        <div v-if="activeEvent.IS_PUNISH =='false'" class="evt-content">
                                            无
                                        </div>
                                        <div v-else class="evt-content">
                                            {{activeEvent.PUNISH_INFO}}
                                        </div>
                                    </div>
                                    <div class="event-info" v-if="!hasComment(activeEvent)" >
                                        <div class="sub-title">批示意见</div>
                                        <div class="evt-content">
                                           无
                                        </div>
                                    </div>
                                    <div class="event-info" v-else >
                                        <div  v-for="(item,index) in activeEvent.comments">
                                            <div class="sub-title">批示意见</div>
                                            <div class="evt-content">
                                                {{item.CONTENT}}
                                            </div>
                                            <div class="sub-title">批示人及批示时间</div>
                                            <div class="evt-content">
                                                {{item.COMMENT_BY_NAME +  ' ' + moment(item.COMMENT_TIME).format('YYYY-MM-DD HH:mm:ss')}} 
                                            </div>

                                        </div>
                                       
                                    </div>
                                    <div >
                                        <br/>
                                    </div> 
                                  
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <div class="win-footer">
                <div class="bottomSharpe"></div>
            </div>
        </div>
    </script>

  <!-- 经营情况对比 -->
  <script id="business_compare_template" type="text/html">
        <div class="shouruWinBody">
            <div class="titleSharpe"></div>
            <div class="closeBtn" @click="closeWin"></div>
            <div class="title">各航司经营情况对比</div>
            <div class="date-componet">
                <div class='dateType con_flex_row'>
                    <div :class="dateType=='D' ? 'tab flex_none  selected' :'tab flex_none'" @click="switchDateType('D')" >日</div>
                    <div :class="dateType=='L' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('L')" >周</div>
                    <div :class="dateType=='M' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('M')" >月</div>
                    <div :class="dateType=='Y' ? 'tab flex_none selected' :'tab flex_none'" @click="switchDateType('Y')"  >年</div>
                </div>
    
                <div class="dateCmp">
                    <div  class="datetimepicker" :class="dateType=='D'?'':'hide'" >
                        <div class='input-group date datetimepicker' ref="datetimepicker_D">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                    <div class="datetimepicker" :class="dateType=='L'?'':'hide'" >
                        <div class="week">
                            <span class="combobox_label" @click="dropWeekList" :title="getWeekDesc()" @mouseover="onWeekMouseOver" @mouseout="onWeekMouseOut" >{{getWeekLabel(selectedWeek)}}</span>
                            <span class="combobox_list" :class="showWeekList?'':'hide'" @mouseover="onWeekMouseOver" @mouseout="onWeekMouseOut"  >
                                <span class='item' @click="onWeekChange(item)" v-for="(item,index) in weeks" :key="index" >{{getWeekLabel(item)}}</span>
                            </span>
                        </div>
                    </div>
    
                    <div class="datetimepicker" :class="dateType=='M'?'':'hide'" >
                        <div class='input-group date datetimepicker_month' ref="datetimepicker_M">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                    <div class="datetimepicker"  :class="dateType=='Y'?'':'hide'">
                        <div class='input-group date datetimepicker_year'  ref="datetimepicker_Y">
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="table-container" >
                <div class="tableDiv" ref="table">
                    <div class="tbrow tbheader hightlightRow">
                        <div class="tbcol col-border tbcol2 textCenter">航司</div>
                        <div @click="sort('PASS_CARGO_SHIFTS','CARGO_SHIFTS')" class="tbcol col-border" >航班<span class="subTitle">（班次）</span> <img v-if="kpi=='PASS_CARGO_SHIFTS'" :src="'img/order-'+orderType+'.svg'" /><img v-else src="img/order-default.svg" /></div>
                        <div @click="sort('EST_INC_FUEL')" class="tbcol hightlightCol col-border">客运收入<span class="subTitle">（万元）</span><img v-if="kpi=='EST_INC_FUEL'" :src="'img/order-'+orderType+'.svg'" /><img v-else src="img/order-default.svg" /></div>
                        <div @click="sort('PASS_CARGO_INC','DETAIL_CARGO_INC')" class="tbcol hightlightCol col-border">货运收入<span class="subTitle">（万元）</span> <img v-if="kpi=='PASS_CARGO_INC'" :src="'img/order-'+orderType+'.svg'" /><img v-else src="img/order-default.svg" /></div>
                        <div @click="sort('AUX_INC','DETAIL_CARGO_INC')" class="tbcol hightlightCol col-border">辅营<span class="subTitle">（万元）</span> <img v-if="kpi=='AUX_INC'" :src="'img/order-'+orderType+'.svg'" /><img v-else src="img/order-default.svg" /></div>
                        <div @click="sort('ROUTE_SUBSIDY_INC')" class="tbcol hightlightCol col-border">航线补贴<span class="subTitle">（万元）</span> <img v-if="kpi=='ROUTE_SUBSIDY_INC'" :src="'img/order-'+orderType+'.svg'" /><img v-else src="img/order-default.svg" /></div>
                        <div @click="sort('AC_NO_QM','CARGO_AC_NUM')" class="tbcol tbcol3 col-border"><div> 可用运力<br><span class="subTitle">（架）</span> <img v-if="kpi=='AC_NO_QM'" :src="'img/order-'+orderType+'.svg'" /><img v-else src="img/order-default.svg" /></div></div>
                        <div class="tbcol tb2Floor col-border"  v-bind:class="{'tb2Floor-escaped':!lylExpand}">
                            <div>
                                <div class="subheader"  @click="sort('PASS_CARGO_AC_UTIL_RATE','CARGO_AC_UTIL_RATE')">
                                    <div class="topHeader">
                                        <span>利用率</span>
                                        <img v-if="kpi=='PASS_CARGO_AC_UTIL_RATE'" :src="'img/order-'+orderType+'.svg'" /><img v-else src="img/order-default.svg" />
                                    </div>
                                </div>
                                <div class="subheader">
                                    <div @click="expand(lylExpand=!lylExpand)">整体
                                        <img v-if="!lylExpand" class="expandOrNot" src="img/a0-1_expand.svg" />
                                        <img v-else class="expandOrNot" src="img/a0-1_collapse.svg" />
                                    </div>
                                    <div v-if="lylExpand">宽体机</div>
                                    <div v-if="lylExpand">窄体机</div>
                                    <div v-if="lylExpand">支线机</div>
                                </div>
                            </div>
                        </div>
                        <div class="tbcol tb2Floor col-border"  v-bind:class="{'tb2Floor-escaped':!zglsrExpand}">
                            <div>
                                <div class="subheader" @click="sort('CAP_KILO_INC_FUEL')">
                                    <div class="topHeader">
                                        <div>
                                            <span>座公里收入</span>
                                            <span class="subTitle">(元)</span>
                                        </div>
                                        <img v-if="kpi=='CAP_KILO_INC_FUEL'" :src="'img/order-'+orderType+'.svg'" /><img v-else src="img/order-default.svg" />
                                    </div>
                                </div>
                                <div class="subheader">
                                    <div @click="expand(zglsrExpand=!zglsrExpand)">整体
                                        <img v-if="!zglsrExpand" class="expandOrNot" src="img/a0-1_expand.svg" />
                                        <img v-else class="expandOrNot" src="img/a0-1_collapse.svg" />
                                    </div>
                                    <div v-if="zglsrExpand">宽体机</div>
                                    <div v-if="zglsrExpand">窄体机</div>
                                    <div v-if="zglsrExpand">支线机</div>
                                </div>
                            </div>
                        </div>
                        <div class="tbcol tb2Floor col-border"  v-bind:class="{'tb2Floor-escaped':!xssrExpand}">
                            <div>
                                <div class="subheader" @click="sort('PASS_CARGO_HOUR_INC','CARGO_HOUR_INC')">
                                    <div class="topHeader">
                                        <div>
                                            <span>小时收入</span>
                                            <span class="subTitle">(万元)</span>
                                        </div>
                                        <img v-if="kpi=='PASS_CARGO_HOUR_INC'" :src="'img/order-'+orderType+'.svg'" /><img v-else src="img/order-default.svg" />
                                    </div>
                                </div>
                                <div class="subheader">
                                    <div @click="expand(xssrExpand=!xssrExpand)">整体
                                        <img v-if="!xssrExpand" class="expandOrNot" src="img/a0-1_expand.svg" />
                                        <img v-else class="expandOrNot" src="img/a0-1_collapse.svg" />
                                    </div>
                                    <div v-if="xssrExpand">宽体机</div>
                                    <div v-if="xssrExpand">窄体机</div>
                                    <div v-if="xssrExpand">支线机</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="tbody" style="min-height: 100px;">
                        <div class="tbrow"  v-bind:class="{hightlightRow:item.isHightList}"  v-for="(item,index) in keyunList" :key="index">
                            <div class="tbcol col-border tbcol2 textCenter">{{item.isHightList ? '客运合计' : item.companyCode}}</div>
                            <div class="tbcol col-border"><span>{{getValue(item, "PASS_CARGO_SHIFTS")}}</span><span>{{getPercision(item, allItem, 'PASS_CARGO_SHIFTS', !item.isHightList)}}</span></div>
                            <div class="tbcol col-border hightlightCol2"><span>{{getValue(item, "EST_INC_FUEL", 2, 1)}}</span><span>{{getPercision( item, allItem, 'EST_INC_FUEL', !item.isHightList)}}</span></div>
                            <div class="tbcol col-border hightlightCol2"><span>{{getValue( item, "PASS_CARGO_INC", 2, 1)}}</span><span>{{getPercision( item, allItem, 'PASS_CARGO_INC', !item.isHightList)}}</span></div>
                            <div class="tbcol col-border hightlightCol2"><span>{{getValue( item, "AUX_INC", 2, 1)}}</span><span>{{getPercision( item, allItem, 'AUX_INC', !item.isHightList)}}</span></div>
                            <div class="tbcol col-border hightlightCol2"><span>{{getValue( item, "ROUTE_SUBSIDY_INC", 2, 1)}}</span><span>{{getPercision( item, allItem, 'ROUTE_SUBSIDY_INC', !item.isHightList)}}</span></div>
                            <div class="tbcol col-border tbcol3">{{getValue( item, "AC_NO_QM")}}</div>
                            
                            <div class="tbcol tbcol2 " v-bind:class="{'border-right':!lylExpand}">{{getValue( item, "PASS_CARGO_AC_UTIL_RATE", 1)}}</div>
                            <div class="tbcol tbcol2" v-if="lylExpand">{{getValue( item, "W_AC_UTIL_RATE", 1)}}</div>
                            <div class="tbcol tbcol2" v-if="lylExpand">{{getValue( item, "N_AC_UTIL_RATE", 1)}}</div>
                            <div class="tbcol tbcol2 border-right" v-if="lylExpand">{{getValue( item, "B_AC_UTIL_RATE", 1)}}</div>
                            
                            
                            <div class="tbcol tbcol2"  v-bind:class="{'border-right':!zglsrExpand}">{{getValue( item, "CAP_KILO_INC_FUEL", 3)}}</div>
                            <div class="tbcol tbcol2"  v-if="zglsrExpand">{{getValue( item, "W_CAP_KILO_INC_FUEL", 3)}}</div>
                            <div class="tbcol tbcol2"  v-if="zglsrExpand">{{getValue( item, "N_CAP_KILO_INC_FUEL", 3)}}</div>
                            <div class="tbcol tbcol2 border-right" v-if="zglsrExpand">{{getValue( item, "B_CAP_KILO_INC_FUEL", 3)}}</div>
                            
                            <div class="tbcol tbcol2" v-bind:class="{'border-right':!xssrExpand}">{{getValue( item, "PASS_CARGO_HOUR_INC", 1)}}</div>
                            <div class="tbcol tbcol2" v-if="xssrExpand">{{getValue( item, "W_HOUR_INC_FUEL", 1)}}</div>
                            <div class="tbcol tbcol2" v-if="xssrExpand">{{getValue( item, "N_HOUR_INC_FUEL", 1)}}</div>
                            <div class="tbcol tbcol2 border-right" v-if="xssrExpand">{{getValue( item, "B_HOUR_INC_FUEL", 1)}}</div>
                        </div>

                        <div class="tbrow" v-bind:class="{hightlightRow:item.isHightList}"  v-for="(item,index) in huoyunList" :key="index">
                            <div class="tbcol col-border tbcol2 textCenter">{{item.isHightList ? '货运合计' : getCargoCompanyName(item.companyCode)}}</div>
                            <div class="tbcol col-border"><span>{{getValue( item, "CARGO_SHIFTS")}}</span><span>{{getPercision( item, allCargoItem, 'CARGO_SHIFTS', !item.isHightList)}}</span></div>
                            <div class="tbcol col-border hightlightCol2">-</div>
                            <div class="tbcol col-border hightlightCol2"><span>{{getValue( item, "DETAIL_CARGO_INC", 2, 1)}}</span><span>{{getPercision( item, allCargoItem, 'DETAIL_CARGO_INC', !item.isHightList)}}</span></div>
                            <div class="tbcol col-border hightlightCol2">-</div>
                            <div class="tbcol col-border hightlightCol2">-</div>
                            <div class="tbcol col-border tbcol3 border-right">{{getValue( item, "CARGO_AC_NUM")}}</div>
                            
                            <div class="tbcol tbcol2" v-bind:class="{'border-right':!lylExpand}">{{getValue( item, "CARGO_AC_UTIL_RATE", 1)}}</div>
                            <div class="tbcol tbcol2"  v-if="lylExpand">-</div>
                            <div class="tbcol tbcol2"  v-if="lylExpand">-</div>
                            <div class="tbcol tbcol2 border-right"  v-if="lylExpand">-</div>
                            
                            <div class="tbcol tbcol2" v-bind:class="{'border-right':!zglsrExpand}">-</div>
                            <div class="tbcol tbcol2"  v-if="zglsrExpand">-</div>
                            <div class="tbcol tbcol2"  v-if="zglsrExpand">-</div>
                            <div class="tbcol tbcol2 border-right"  v-if="zglsrExpand">-</div>
                            
                            <div class="tbcol tbcol2" v-bind:class="{'border-right':!xssrExpand}">{{getValue( item, "CARGO_HOUR_INC", 1)}}</div>
                            <div class="tbcol tbcol2"  v-if="xssrExpand">-</div>
                            <div class="tbcol tbcol2"  v-if="xssrExpand">-</div>
                            <div class="tbcol tbcol2 border-right"  v-if="xssrExpand">-</div>
                        </div>
                        <div style="margin-bottom:30px"></div>
                    </div>
                </div>
            </div>
            <div class="win-footer">
                <div class="bottomSharpe"></div>
            </div>
     </div>
    </script>

</body>

</html>
<script>
  loadjs4BabelDefer('js/a0-1.js')
  loadjs4BabelDefer('js/a0-1/capacity.js')
  loadjs4BabelDefer('js/a0-1/flight_normal_rate.js')
  loadjs4BabelDefer('js/a0-1/trv_flight_cargo_compare.js')
  loadjs4BabelDefer('js/a0-1/utilization_rate_template.js')
  loadjs4BabelDefer('js/a0-1/flight_accident.js')
  loadjs4BabelDefer('js/a0-1/subsidy.js')
  loadjs4BabelDefer('js/a0-1/cargo_detail.js')
  loadjs4BabelDefer('js/a0-1/aux_inc.js')
  loadjs4BabelDefer('js/a0-1/caac_complaint.js')
  loadjs4BabelDefer('js/a0-1/unsafe_detail.js')
  loadjs4BabelDefer('js/a0-1/unsafe_detail2.js')
  loadjs4BabelDefer('js/a0-1/business_compare.js')
</script>