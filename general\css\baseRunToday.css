.w10 {
    width: 10%;
}

.w20 {
    width: 20%;
}

.w30 {
    width: 30%;
}

.w40 {
    width: 40%;
}

.w50 {
    width: 50%;
}

.w60 {
    width: 60%;
}

.w70 {
    width: 70%;
}

.w80 {
    width: 80%;
}

.w85 {
    width: 85%;
}

.w90 {
    width: 90%;
}

.w100 {
    width: 100%;
}

.left2 {
    left: 2%;
}

.left5 {
    left: 5%;
}

.left10 {
    left: 10%;
}

.left20 {
    left: 20%;
}

.left25 {
    left: 25%;
}

.left30 {
    left: 30%;
}

.left35 {
    left: 35%;
}

.left40 {
    left: 40%;
}

.left50 {
    left: 50%;
}

.left60 {
    left: 60%;
}

.left70 {
    left: 70%;
}

.left80 {
    left: 80%;
}

.left90 {
    left: 90%;
}

/** 背景 **/
.page-bgimg {
    position: absolute;
    top: 0px;
    width: 1920px;
    height: 1080px;
    background: url(../img/DP_baseRunToday_bg.png?1) no-repeat top center;
    background-size: 100%;
    overflow-x: hidden;
    overflow-y: hidden;
    pointer-events: none;
}

.page-wrapper {
    position: absolute;
    top: 0px;
    width: 1920px;
    height: 1080px;
    overflow-x: hidden;
    overflow-y: hidden;
    pointer-events: none;
}

/** 背景 **/

/**下拉框**/
#companycombo {
    position: absolute;
    top: 14px;
    left: 0px;
    width: 70px;
    height: 32px;
    font-size: 10px;
}

#companycombo .box {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 63px;
    height: 23px;
    border: 1px solid #2a81d2;
    border-radius: 3px;
    padding: 7px 0 0 9px;
    cursor: pointer;
    background: url(../img/combo_arr.png) no-repeat 57px 13px;
    background-color: #073b77;
    pointer-events: auto;
}

#companycombo .box:hover {
    border: 1px solid #2f8be1;
    background-color: #073b77;
}

#companylist {
    position: absolute;
    top: 31px;
    left: 0px;
    width: 70px;
    border: 1px solid #1f5fa8;
    border-radius: 2px;
    padding: 5px 0 5px 0px;
    background-color: #021d39;
    display: none;
    z-index: 1;
    pointer-events: auto;
}

#companylist .itm {
    width: 100%;
    height: 23px;
    width: 60px;
    padding: 6px 0 0 10px;
    cursor: pointer;
    position: relative;
}

#companylist .itm:hover {
    background-color: #1f5fa8;
}

.combobox_list {
    position: absolute;
    top: 31px;
    left: 0px;
    width: 64px;
    overflow-y: scroll;
    border: 1px solid #1f5fa8;
    border-radius: 2px;
    padding: 5px 0 5px 0px;
    background-color: #021d39;
    display: none;
    z-index: 1;
    pointer-events: auto;
}

.combobox_list .item {
    display: block;
    padding: 3px;
    position: relative;
    cursor: pointer;
    pointer-events: auto;
    letter-spacing: -0.5px;
}

.combobox_list .item:hover {
    background-color: #1f5fa8;
}

.limcomb {
    height: 30px;
    width: 65px !important;
    border-radius: 4px;
    border: 1px solid rgba(41, 140, 232, 0.6);
    background: #073b77;
    float: left;
}

.limcomb .combobox_label {
    font-size: 14px;
    text-align: center;
    line-height: 28px;
    padding: 5px;
    cursor: pointer;
    pointer-events: auto;
}

.limcomb .combobox_label {
    display: inline-block;
    padding: 0;
    height: 30px;
    line-height: 30px;
    text-align: left;
    background-image: url(../img/combobox_arr.png);
    background-repeat: no-repeat;
    background-position: 49px center;
    border-radius: 4px;
    border-right: none;
    border: 0px !important;
    box-sizing: border-box;
    padding-left: 5px;
    width: 100%;
}

.btn_go {
    padding: 6px;
    border-radius: 4px;
    border: 1px solid #2874c1;
    cursor: pointer;
    pointer-events: auto;
    background: url(../img/arr.png) no-repeat 151px center;
    width: 150px;
}

.label-title {
    border-left: 5px solid #2a81d2;
    border-radius: 0;
    font-size: 15px;
    padding-left: 7px;
}

.label-title .line_1 {
    padding-left: 10px;
    color: #2a81d2;
    font-weight: 600;
    font-size: 1px;
}

.label-title .line_2_80 {
    width: 80px;
    bottom: 4px;
    left: 96%;
    border-bottom: 1px solid #2a81d2;
}

.label-title .line_2_50 {
    width: 50px;
    bottom: 4px;
    left: 96%;
    border-bottom: 1px solid #2a81d2;
}

.label-title .line_2_95 {
    width: 95px;
    bottom: 4px;
    left: 96%;
    border-bottom: 1px solid #2a81d2;
}

.label-title .line_2_125 {
    width: 125px;
    bottom: 4px;
    left: 95%;
    border-bottom: 1px solid #2a81d2;
}

.label-title .line_2_156 {
    width: 156px;
    bottom: 4px;
    left: 95%;
    border-bottom: 1px solid #2a81d2;
}

.label-title .line_2_176 {
    width: 176px;
    bottom: 4px;
    left: 95%;
    border-bottom: 1px solid #2a81d2;
}

.label-title .line_2_210 {
    width: 210px;
    bottom: 4px;
    left: 95%;
    border-bottom: 1px solid #2a81d2;
}

.label-title .line_2_365 {
    width: 365px;
    bottom: 4px;
    left: 95%;
    border-bottom: 1px solid #2a81d2;
}

.label-title .line_2_456 {
    width: 456px;
    bottom: 4px;
    left: 95%;
    border-bottom: 1px solid #2a81d2;
}

.label-title .line_2_400 {
    width: 400px;
    bottom: 4px;
    left: 95%;
    border-bottom: 1px solid #2a81d2;
}

.label-content {
    width: 100%;
    height: 100%;
    top: 28px;
}

.chart {
    left: 28px;
    top: 20px;
    text-align: center;
}

.chart .pointer {
    width: 122px;
    height: 122px;
}

.chart .mk {
    position: absolute;
    font-size: 9px;
    color: #44a3f4
}

.chart .lb {
    position: absolute;
    width: 100%;
    height: 50px;
    top: 78px;
}

/* 头部左边样式 begin */
.baseBodyBg {
    width: 94.5%;
    height: 95%;
    margin-left: 32px;
}

.baseTitle {
    width: 100%;
    height: 6.5%;
}

.baseTitleL {
    width: 32.5%;
    top: 10px;
}

.baseTitleL1 {
    width: 70px;
    height: 45px;
    top: 8px;
}

#baseTitleL1letter {
    width: 13px;
    height: 25px;
    top: 20px;
}

.baseTitleL1img {
    width: 115px;
    height: 40px;
    left: 90px;
    top: 8px;
}

.baseTitleL2 {
    width: 125px;
    height: 20px;
    font-size: 15px;
    font-weight: bold;
    color: #00a9ff;
    left: 210px;
    top: 27px;
}

.baseTitleL3 {
    width: 50px;
    height: 20px;
    left: 65%;
    top: 22px;
    cursor: pointer;
}

.baseTitleL4 {
    width: 150px;
    height: 20px;
    font-size: 8px;
    color: #00a9ff;
    left: 78%;
    top: 20px;
    cursor: pointer;
}

/* 头部左边样式 end */

/* 头部中间标题样式 begin */
.baseTitleC {
    width: 12%;
    height: 100%;
    font-size: 20px;
    font-weight: bold;
    color: #00a9ff;
    left: 45.5%;
    top: 10px;
    font-size: 26px;
}

/* 头部中间标题样式 end */

/* 头部右边样式 begin */
.baseTitleR {
    width: 440px;
    height: 45px;
    right: 0px;
}

.baseTitleRType {
    top: 36px;
    right: 420px;
}

.baseTitleRType select {
    width: 95px;
    height: 26px;
    pointer-events: auto;
    border-radius: 4px;
    border: 1px solid rgba(41, 140, 232, 0.6);
    background: #073b77;
    color: #029cee;
}

.baseTitleRName {
    top: 39px;
    left: 9%;
    font-size: 12px;
}

.baseTitleRPhon {
    left: 25%;
    top: 39px;
}

.baseTitleRPhoNum {
    left: 33%;
    top: 39px;
    font-size: 12px;
}

.baseTitleRMobi {
    left: 57%;
    top: 39px;
}

.baseTitleRMobiNum {
    left: 63%;
    top: 39px;
    font-size: 12px;
}

/* 头部右边样式 end */

/* 主要区域 begin*/
.baseMain {
    top: 120px;
    width: 100%;
    height: 87.2%;
}

/* 主要区域-left begin*/
.baseMainL {
    width: 32%;
    height: 100%;
    left: 17px;
}

.baseMainLTi {
    width: 100%;
    height: 20%;
}

.normalRate {
    width: 33%;
    height: 100%;
}

.arrivalAtPort {
    width: 33%;
    height: 100%;
    left: 33%;
}

.normalRateNum {
    width: 100%;
    height: 82%;
    top: 25%;
    left: 23px;
}

.clearAPort {
    width: 34%;
    height: 100%;
    right: 0px;
}

.baseMainLCe {
    width: 100%;
    height: 29%;
    top: 20%;
}

.baseMainLCeL {
    width: 60%;
    height: 100%;
}

.todayTitle {
    width: 100%;
    height: 25px;
    line-height: 25px;
    vertical-align: middle;
}

.baseMainLCeR {
    width: 38%;
    height: 100%;
    right: 0px;
}

.todayJG {
    width: 100%;
    height: 25%;
}

.todayJGTi {
    padding-top: 2%;
    width: 7%;
    height: 78%;
    font-size: 14px;
    background-color: #044898;
    text-align: center;
    margin-top: 10px;
}

.todayJGMain {
    width: 86%;
    height: 100%;
    right: 15px;
    top: 15%;
}

.todayJGMainTop {
    width: 100%;
    top: 6px;
    height: 20px;
}

.todayJGMainTop2 {
    left: 68%;
    top: 4px;
}

.todayJGMainTop3 {
    right: 0px;
    top: 4px;
}

.todayJGMainBottom {
    top: 66%;
    width: 100%;
    height: 5%;
    background-color: #008C00;
}

/* 当日航班量-进港和出港进度条使用 begin */
.thinbluebarBa {
    border-radius: 2px;
    background-color: #00AFFC;
}

.thinbluebarBa .insidebarBa {
    border-radius: 2px;
    height: 100%;
    background: -moz-linear-gradient(to right, #00AFFC 0%, #FFF 100%);
    background: -webkit-linear-gradient(to right, #00AFFC 0%, #FFF 100%);
    background: linear-gradient(to right, #00AFFC 0%, #FFF 100%);
    -moz-box-shadow: 0px 0px 5px #00AFFC;
    -webkit-box-shadow: 0px 0px 5px #00AFFC;
    box-shadow: 0px 0px 5px #00AFFC;
}

.thinbluebarBa .dotBa {
    top: -1px;
    width: 7px;
    height: 7px;
    border-radius: 4px;
    background: #fff;
    -moz-box-shadow: 0px 0px 8px #122348;
    -webkit-box-shadow: 0px 0px 8px #122348;
    box-shadow: 0px 0px 8px #122348;
}

/* 当日航班量-进港和出港进度条使用 end */
.todayCG {
    width: 100%;
    height: 50%;
    top: 35%;
}

.todayCGTi {
    width: 7%;
    height: 69%;
    font-size: 14px;
    background-color: #044898;
    text-align: center;
    padding-top: 11%;
}

.todayCGMain {
    width: 86%;
    height: 100%;
    right: 15px;
}

.todayCGMainBottom {
    top: 33%;
    width: 100%;
    height: 5%;
    background-color: #008C00;
}

.CGMaimBott {
    width: 86%;
    height: 55%;
    right: 13px;
    bottom: 0px;
}

.CGOriginat {
    font-size: 3px;
    width: 31%;
    height: 100%;
}

.CGOriginat ul {
    list-style-type: none;
    padding-left: 0px;
    margin-top: 0px;
    line-height: 22px;

}

.CGInland {
    font-size: 3px;
    width: 35%;
    height: 100%;
    left: 35%;
}

.CGInland ul {
    list-style-type: none;
    padding-left: 0px;
    margin-top: 0px;
    line-height: 22px;

}

.CGInte {
    font-size: 3px;
    width: 35%;
    height: 100%;
    right: -12px;
}

.CGInte ul {
    list-style-type: none;
    padding-left: 0px;
    margin-top: 0px;
    line-height: 22px;

}

.cgli {
    font-size: 1px !important;
}

.baseMainLCeR {
    width: 40%;
    height: 100%;
    right: 0px;
}

.baseMainLCeRTI {
    width: 100%;
    height: 25px;
    line-height: 25px;
    vertical-align: middle;
}

.baseMainLCeRTop {
    width: 100%;
    height: 41%;
    top: 12%;
}

.baseMainLCeRTop1, .baseMainLCeRBott1 {
    width: 50%;
    height: 100%;
}

.baseMainLCeRTop2, .baseMainLCeRBott2 {
    width: 50%;
    height: 100%;
    right: 1%;
    top: 7px;
}

.baseMainLCeRBott {
    width: 100%;
    height: 41%;
    bottom: 0px;
}

.baseMainLCeRTop2 ul, .baseMainLCeRBott2 ul {
    list-style-type: none;
    padding-left: 5px;
    margin-top: 0px;
    font-size: 12px;
    line-height: 21px;
}

.baseMainLBo {
    width: 100%;
    height: 49%;
    bottom: 0px;
}

.baseMainLBoL {
    width: 54%;
    height: 100%;
}

.baseMainLBoLTop {
    width: 100%;
    height: 43%;
}

.baseMainLBoLTTitl {
    width: 100%;
    height: 18px;
    line-height: 18px;
    vertical-align: middle;
}

.baseMainLBoLTTitl1 {
    width: 4px;
    height: 100%;
    background-color: #00a9ff;
}

.baseMainLBoLTTitl2 {
    width: 96%;
    height: 100%;
    right: 0px;
    font-size: 10px;
}

.unnormal_plan {
    background: url(../img/unnormal_plan.png) no-repeat center;
    height: 68px;
    width: 95px;
    text-align: center;
    top: 10px;
}

.arp_flight {
    bottom: 315px;
}

.arp_flight .actually_arrow {
    border-width: 7px 12px 7px 12px;
    border-style: solid;
    border-color: transparent #00a9ff transparent transparent;
    top: 80px;
    left: 40px;
}

.arp_flight .plan_arrow {
    border-width: 7px 12px 7px 12px;
    border-style: solid;
    border-color: transparent #024394 transparent transparent;
    top: 80px;
    left: 212px;
    transform: rotate(180deg); /*顺时针旋转90°*/
    -ms-transform: rotate(180deg); /* IE 9 */
    -moz-transform: rotate(180deg); /* Firefox */
    -webkit-transform: rotate(180deg); /* Safari 和 Chrome */
    -o-transform: rotate(180deg);
}

.arp_flight .arp_flight_rate_bg {
    background: url(../img/flight1.png) no-repeat center;
    height: 45px;
    width: 45px;
    top: 60px;
    left: 117px;
}

.arp_flight .actually {
    width: 50px;
    top: 65px;
    text-align: center;
}

.arp_flight .actually .actually_label {
    width: 50px;
}

.arp_flight .actually .actually_content {
    top: 25px;
}

.arp_flight .plan {
    width: 50px;
    top: 65px;
    right: 20px;
    text-align: center;
}

.arp_flight .plan .plan_label {
    width: 50px;
}

.arp_flight .plan .plan_content {
    top: 25px;
    width: 50px;
}

.arp_flight .arp_flight_plan1 {
    top: 180px;
    left: 3px;
    display: none;
}

.arp_flight .arp_flight_plan2 {
    top: 210px;
    left: 3px;
    display: none;
}

.arp_flight .arp_flight_plan3 {
    top: 240px;
    left: 3px;
    display: none;
}

.arp_flight .arp_flight_plan4 {
    top: 270px;
    left: 3px;
    display: none;
}

.arp_flight .arp_flight_plan1 .arp_flight_plan1_lebel,
.arp_flight .arp_flight_plan2 .arp_flight_plan2_lebel,
.arp_flight .arp_flight_plan3 .arp_flight_plan3_lebel,
.arp_flight .arp_flight_plan4 .arp_flight_plan4_lebel {
    top: -8px;
}

.arp_flight .arp_flight_plan1 .arp_flight_plan1_content,
.arp_flight .arp_flight_plan2 .arp_flight_plan2_content,
.arp_flight .arp_flight_plan3 .arp_flight_plan3_content,
.arp_flight .arp_flight_plan4 .arp_flight_plan4_content {
    left: 220px;
    top: -7px;
}

.arp_flight .arp_flight_plan1 .scheduleX,
.arp_flight .arp_flight_plan2 .scheduleX,
.arp_flight .arp_flight_plan3 .scheduleX,
.arp_flight .arp_flight_plan4 .scheduleX {
    left: 50px;
}

.scheduleX {
    width: 300px;
    height: 25px;
    background-color: #7d8e91
}

.scheduleX .xList {
    background-color: #2bd74c;
    width: 0%;
    height: 100%;
    position: relative
}

.scheduleX .xList .xNum {
    font-size: 10px;
    position: absolute;
    top: 50%;
    right: 5%;
    color: #fff;
    -webkit-transform: translate(0%, -50%);
    -moz-transform: translate(0%, -50%);
    -ms-transform: translate(0%, -50%);
    -o-transform: translate(0%, -50%);
    transform: translate(0%, -50%);
    z-index: 10
}

.delay_case {
    bottom: 315px;
    left: 50%;
}

.delay_case .delay_case_rate {
    top: 10px;
}

.delay_case .delay_case_rate .vs {
    background: url(../img/vs.png) no-repeat center;
    height: 45px;
    width: 45px;
    top: 28px;
    left: 107px;
}

.delay_case .delay_case_rate .comp_case {
    top: 30px;
    left: 4px;
    width: 100px;
}

.delay_case .delay_case_rate .comp_case .comp_case_label {
    color: #00B384
}

.delay_case .delay_case_rate .comp_case .comp_case_content {
    top: 22px;
    text-align: left;
}

.delay_case .delay_case_rate .uncomp_case {
    top: 30px;
    left: 180px;
    width: 100px;
}

.delay_case .delay_case_rate .uncomp_case .uncomp_case_content {
    top: 22px;
    text-align: right;
    width: 80px;
}

.baseMainLBoLBottom {
    width: 100%;
    height: 55%;
    /* background-color: wheat; */
    bottom: 0px;
}

.baseMainLBoLBottom1 {
    width: 100%;
    height: 18px;
    line-height: 18px;
    vertical-align: middle;
}

.baseMainLBoLBottom2 {
    width: 100%;
    height: 30%;
    top: 15%;
}

.baseMainLBoLBottom2L {
    width: 47%;
    height: 100%;
}

.baseMainLBoLBottom2L1 {
    width: 44%;
    height: 94%;
    padding-top: 6%;
}

.baseMainLBoLBottom2L2 {
    width: 53%;
    height: 100%;
    right: 0px;
}

.baseMainLBoLBottom2L2 ul {
    width: 100%;
    list-style-type: none;
    padding-left: 0px;
    padding-top: -10px;
}

.baseMainLBoLBottom2L2 li {
    line-height: 16px;
    top: -10px;
}

.baseMainLBoLBottom2R {
    width: 47%;
    height: 100%;
    right: 1%;
}

.baseMainLBoLBottom3 {
    width: 100%;
    height: 50%;
    bottom: 0px;
}

.JDGYFJMain1 {
    width: 90%;
    height: 16px;
}

.baseMainLBoR {
    width: 44%;
    height: 100%;
    right: 0px;
}

.baseMainLBoRTop {
    width: 100%;
    height: 32%;
}

.baseMainLBoRTop1 {
    width: 100%;
    height: 18px;
    line-height: 18px;
    vertical-align: middle;
}

.JRYWYYTitle {
    width: 4px;
    height: 100%;
    background-color: #00a9ff;
}

.JRYWYY {
    width: 93%;
    height: 100%;
    font-size: 12px;
    right: 2%;
}

.baseMainLBoRTop2 {
    top: 20px;
    width: 100%;
    height: 65%;
}

.comReson {
    width: 32%;
    height: 46%;
    font-size: 12px;
    top: 27%;
}

.noComReson {
    width: 34%;
    height: 46%;
    font-size: 10px;
    top: 27%;
    right: 0px;
}

.baseMainLBoRBott {
    width: 90%;
    height: 66%;
    top: 130px;
}

.buttReso {
    width: 100%;
    height: 40px;
}

.buttReso .buttResoSelenon {
    width: 50%;
    height: 100%;
    text-align: center;
    line-height: 34px;
    cursor: pointer;
    pointer-events: auto;
}

.buttReso .buttResoSelenon2 {
    width: 50%;
    right: 0px;
    height: 100%;
    text-align: center;
    line-height: 34px;
    cursor: pointer;
    pointer-events: auto;
}

.buttReso .bg1 {
    background-color: #044898;
}

.buttReso .bg2 {
    background-color: #5FC5FF;
}

.buttResoSele {
    width: 262px;
    height: 134px;
    overflow-y: auto;
    pointer-events: auto;
    top: 39px;
    background: -webkit-linear-gradient(#044898, #091028); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(#044898, #091028); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(#044898, #091028); /* Firefox 3.6 - 15 */
    background: linear-gradient(#044898, #091028); /* 标准的语法（必须放在最后） */
}

.buttResoSele .baritmrow {
    padding: 12px 20px 0 30px;
}

.buttResoSele1 {
    width: 100%;
    height: 16px;
}


/* 主要区域-left end*/

/* 主要区域-Center begin*/
.baseMainC {
    width: 35%;
    height: 105%;
    left: 35%;
    top: 20px;
}

.baseMainCTo {
    width: 100%;
    height: 102px;
}

.baseMainCToL {
    width: 130px;
    height: 100%;
}

.baseMainCToLTi {
    width: 110px;
    height: 16px;
    font-size: 14px;
}

.baseMainCToLTi1 {
    width: 5px;
    height: 100%;
    background-color: #00a9ff;
}

.baseMainCToLTi2 {
    top: -2px;
    width: 100px;
    height: 100%;
    right: 0px;
}

.baseMainCToLTi3 {
    width: 50%;
    height: 8px;
    left: 100px;
    background: url(../img/titleR1.png) no-repeat;
    top: 7px;
}

#baseMainCToLInfo {
    width: 100%;
    height: 70%;
    top: 18%;
}

.baseMainCToLInfos {
    width: 100%;
    top: 10px;
}

.baseMainCToLInfos1 {
    width: 22px;
    height: 24px;
    background: url(../img/zc.png) no-repeat;
}

.baseMainCToLInfos2 {
    width: 28px;
    height: 24px;
    left: 30px;
    color: #00a9ff;
    font-size: 14px;
    line-height: 24px;
    text-align: center;
    vertical-align: middle;
}

.baseMainCToLInfos3 {
    width: 22px;
    height: 24px;
    left: 74px;
    background: url(../img/yw.png) no-repeat;
}

.baseMainCToLInfos4 {
    width: 28px;
    height: 24px;
    left: 105px;
    color: #00a9ff;
    font-size: 14px;
    line-height: 24px;
    text-align: center;
    vertical-align: middle;
}

/* 天气 */
.baseMainCToR {
    width: 210px;
    right: 0px;
}

.weatherTitle {
    width: 210px;
    height: 20px;
    font-size: 12px;
}

.weatherTitle1 {
    width: 55px;
    height: 20px;
    line-height: 20px;
    font-size: 16px;
}

.weatherTitle2 {
    width: 40px;
    height: 20px;
    left: 70px;
    line-height: 20px;
    font-size: 16px;
    text-align: center;
}

.weatherTitle3 {
    width: 50px;
    height: 20px;
    right: 60px;
    text-align: center;
    color: #00a9ff;
}

.weatherTitle4 {
    width: 65px;
    height: 20px;
    right: 0px;
    text-align: center;
    color: #00a9ff;
}

.weatherMain {
    top: 25px;
    width: 210px;
    height: 90px;
    line-height: 22px;
}

.weatherMain1 {
    width: 70px;
    height: 90px;
}

.weatherMain1 ul, .weatherMain2 ul, .weatherMain3 ul {
    list-style-type: none;
    padding: 0px;
    font-size: 12px;
}

.weatherMain2 {
    width: 70px;
    height: 90px;
    left: 70px;
}

.weatherMain3 {
    width: 70px;
    height: 90px;
    right: 0px;
}

/* 天气 */

.baseMainCCe {
    width: 100%;
    top: 61px;
    right: 47px;
}

/* 3D地球使用 */
.earth3Ds {
    position: absolute;
}

.baseMainCBott {
    width: 100%;
    height: 23%;
    bottom: 30px;
}

.baseMainCBottL {
    width: 42%;
    height: 100%;
}

.baseMainCBottL2 {
    width: 100%;
    height: 83%;
    bottom: 20px;
}
.searchform .error {
    font-size: 12px;
    color: #60b7ff;
    margin-top: 5px;
    display: none;
    top: 120px;
}
.searchform .dropdown_arrow {
    position: absolute;
    top: 0px;
    right: 8px;
    height: 24px;
    width: 24px;
    pointer-events: auto;
    cursor: pointer;
    background: url(../img/combo_arr.png) no-repeat center;
}
#flt_dropdown_list {
    position: absolute;
    top: 90px;
    left: 50px;
    width: 97px;
    max-height: 98px;
    overflow: hidden;
    overflow-y: scroll;
    border-radius: 3px;
    background-color: #004a91;
    pointer-events: auto;
    display: none;
}
#flt_dropdown_list .itm {
    pointer-events: auto;
    cursor: pointer;
    width: 100%;
    height: 24px;
    display: block;
    line-height: 24px;
    padding-left: 3px;
    font-size: 12px;
}
.baseMainCBottL2Img {
    height: 78px;
    width: 80px;
    background: url(../img/erthBottomL.png) no-repeat center;
    cursor: pointer;
    pointer-events: auto;
}

.baseMainCBottL2Butt {
    width: 58%;
    height: 100%;
    right: 12px;
}


.placOfDepa {
    width: 100%;
    height: 20px;
}

/* 查看具体航班详情的输入框使用 */
.placOfDepaCf {
    width: 93px;
    height: 25px;
    pointer-events: auto;
    margin-top: -5px;
    left: 10px;
    border-radius: 4px;
    border: 1px solid rgba(41, 140, 232, 0.6);
    background: #073b77;
    color: #fff;
}

/* 查看具体航班详情的下拉框使用 */
.placOfDepaCf2 {
    width: 95px;
    height: 26px;
    pointer-events: auto;
    left: 10px;
    border-radius: 4px;
    border: 1px solid rgba(41, 140, 232, 0.6);
    background: #073b77;
    padding: 0px;
}

#buttTj {
    border-radius: 4px;
    border: 1px solid rgba(41, 140, 232, 0.6);
    background: #044898;
    pointer-events: auto;
    cursor: pointer;
    width: 95px;
    height: 28px;
    font-size: 12px;
    padding: 0px;
    color: white;
}

.baseMainCBottR {
    width: 140px;
    height: 140px;
    right: 20px;
}

.baseMainCBottRJG {
    width: 140px;
    height: 40px;
    top: 9px;
}

.baseMainCBottRJG1 {
    width: 14px;
    height: 42px;
    padding: 0 4px;
    background-color: #044898;
}

.baseMainCBottRJG2 {
    width: 32px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    vertical-align: middle;
    background: url(../img/BC.png) no-repeat center;
    left: 30px;
}

.baseMainCBottRJG3 {
    width: 32px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    vertical-align: middle;
    background: url(../img/BC.png) no-repeat center;
    left: 70px;
}

.baseMainCBottRJG4 {
    width: 24px;
    height: 40px;
    left: 110px;
    font-size: 12px;
}

.baseMainCBottRCG {
    width: 140px;
    height: 40px;
    top: 65px;
}

/* 主要区域-Center end*/

/* 主要区域-Right begin*/
.baseMainR {
    width: 30%;
    height: 100%;
    right: -28px;
}

.baseMainR1 {
    width: 100%;
    height: 38%;
}

.baseMainR2 {
    width: 100%;
    height: 38%;
    top: 38%;
}

.baseMainR3 {
    width: 100%;
    height: 20%;
    bottom: 0px;
}

.baseMainR1Titl {
    width: 100%;
    height: 16px;
    font-size: 14px;
}

.baseMainR1Main {
    width: 100%;
    height: 90%;
    bottom: 25px;
}

.basepdbhW {
    width: 25%;
    height: 100%;
    border-right: 0.5px solid #004a86;
    font-size: 12px;
}

.basepdbh {
    width: 100%;
    height: 73%;
    line-height: 14px;
    text-align: center;
    top: 27%;
    color: #01a2f6;
}

.base18LW {
    width: 50%;
    height: 100%;
    /* line-height: 14px;
    text-align: center;
    top: 27%;
    font-weight: bold;
    color: #01a2f6; */
    border-right: 0.5px solid #004a86;
    font-size: 12px;
}

.base18L, .base18R, .base19 {
    width: 100%;
    height: 73%;
    line-height: 35px;
    text-align: center;
    color: #01a2f6;
}

.base18RW {
    width: 50%;
    height: 100%;
    /* line-height: 14px;
    text-align: center;
    top: 27%;
    font-weight: bold;
    color: #01a2f6; */
    border-right: 0.5px solid #004a86;
    font-size: 12px;
}

.base36R, .base36L, .base01 {
    width: 50%;
    height: 73%;
    left: 50%;
    line-height: 35px;
    text-align: center;
    color: #01a2f6;
}

.base18Land36RW {
    width: 25%;
    height: 100%;
    left: 25%;
    color: #01a2f6;
    border-right: 0.5px solid #004a86;
}

.base18Land36R {
    width: 100%;
    height: 100%;
    /* left: 25%;
    font-weight: bold;
    color: #01a2f6; */
}

.base18Rand36LW {
    width: 25%;
    height: 100%;
    left: 50%;
    color: #01a2f6;
    border-right: 0.5px solid #004a86;
}

.base18Rand36L {
    width: 100%;
    height: 100%;
    /* left: 50%;
    font-weight: bold;
    color: #01a2f6; */
}

.base19and01 {
    width: 25%;
    height: 100%;
    left: 75%;
    color: #01a2f6;
}

.baseMainR1Main1 {
    width: 100%;
    height: 12%;
    background-color: #0d396a;
    font-size: 12px;
}

.baseMainR1Main2 {
    width: 100%;
    height: 12%;
    background-color: #0d2550;
    top: 13%;
}

.baseMainR1Main3 {
    width: 100%;
    height: 12%;
    background-color: #0d396a;
    top: 26%;
}

.baseMainR1Main4 {
    width: 100%;
    height: 12%;
    background-color: #0d2550;
    top: 39%;
}

.baseMainR1Main5 {
    width: 100%;
    height: 12%;
    background-color: #0d396a;
    top: 52%;
}

.baseMainR1Main6 {
    width: 100%;
    height: 12%;
    background-color: #0d2550;
    top: 65%;
}

.baseMainR1Main7 {
    width: 100%;
    height: 12%;
    background-color: #0d396a;
    top: 78%;
}

.baseMainR2Butt {
    width: 100%;
    height: 40px;
    top: 10px;
    border-top: 0.5px solid #1a6cbc;
    border-left: 0.5px solid #1a6cbc;
    border-right: 0.5px solid #1a6cbc;
}

.baseMainR2Butt1 {
    width: 50%;
    height: 40px;
    cursor: pointer;
    pointer-events: auto;
}

.baseMainR2Butt1all {
    width: 100px;
    left: 31%;
    top: 5px;
}

.baseMainR2Butt1Img {
    top: 3px;
    width: 24px;
    height: 20px;
    background: url(../img/flightTi2.png) no-repeat center;
}

.baseMainR2Butt1R {
    width: 66px;
    height: 40px;
    line-height: 30px;
    vertical-align: middle;
    right: 0px;
}

.seleCJG {
    background-color: #044898;
}

.baseMainR2Butt2 {
    width: 50%;
    height: 40px;
    right: 0px;
    cursor: pointer;
    pointer-events: auto;
}

.baseMainR2Butt2Img {
    top: 3px;
    width: 24px;
    height: 20px;
    background: url(../img/flightTi.png) no-repeat center;
}

.baseMainR2Main {
    width: 100%;
    height: 78%;
    bottom: 20px;
}

.baseMainR2Main1 {
    width: 100%;
    height: 17%;
    font-size: 12px;
}

.baseMainR2MainAll1 {
    width: 12.5%;
    height: 100%;
    text-align: center;
    padding-top: 2%;
    color: #00a9ff;
}

.baseMainR2MainAll2 {
    width: 12.5%;
    height: 100%;
    left: 12.5%;
    top: 5px;
    text-align: center;
    color: #00a9ff;
}

.baseMainR2MainAll3 {
    width: 12.5%;
    height: 100%;
    left: 25%;
    padding-top: 2%;
    text-align: center;
    color: #00a9ff;
}

.baseMainR2MainAll4 {
    width: 12.5%;
    height: 100%;
    left: 37.5%;
    padding-top: 2%;
    text-align: center;
    color: #00a9ff;
}

.baseMainR2MainAll5 {
    width: 12.5%;
    height: 100%;
    right: 37.5%;
    padding-top: 2%;
    text-align: center;
    color: #00a9ff;
}

.baseMainR2MainAll6 {
    width: 12.5%;
    height: 100%;
    right: 25%;
    padding-top: 2%;
    text-align: center;
    color: #00a9ff;
}

.baseMainR2MainAll7 {
    width: 12.5%;
    height: 100%;
    right: 12.5%;
    padding-top: 2%;
    text-align: center;
    color: #00a9ff;
}

.baseMainR2MainAll8 {
    width: 12.5%;
    height: 100%;
    right: 0px;
    padding-top: 2%;
    text-align: center;
    color: #00a9ff;
}

#baseMainR2MainAll {
    width: 100%;
    height: 78%;
    bottom: 0px;
}

.baseMainR2Main2 {
    width: 100%;
    height: 20%;
    background-color: #043568;
    font-size: 8px !important;
}

.baseMainR2Main3 {
    width: 100%;
    height: 20%;
    background-color: #072144;
    top: 21%;
    font-size: 8px !important;
}

.baseMainR2Main4 {
    width: 100%;
    height: 20%;
    background-color: #043568;
    top: 42%;
    font-size: 8px !important;
}

.baseMainR2Main5 {
    width: 100%;
    height: 20%;
    background-color: #072144;
    top: 63%;
    font-size: 8px !important;
}

.baseMainR2Main6 {
    width: 100%;
    height: 20%;
    background-color: #043568;
    top: 84%;
    font-size: 8px !important;
}

.baseMainR3Titl {
    width: 100%;
    height: 30px;
    line-height: 30px;
    font-size: 12px;
    color: #019beb;
    top: 4%;
}

.baseMainR3Titl1 {
    width: 14.2%;
    height: 30px;
    line-height: 30px;
    text-align: center;
    vertical-align: middle;
}

.baseMainR3Titl2 {
    width: 14.2%;
    height: 30px;
    line-height: 30px;
    left: 14.2%;
    text-align: center;
    vertical-align: middle;
}

.baseMainR3Titl3 {
    width: 8%;
    height: 30px;
    line-height: 30px;
    left: 28.4%;
    text-align: center;
    vertical-align: middle;
}

.baseMainR3Titl4 {
    width: 14.2%;
    height: 30px;
    line-height: 30px;
    left: 36.4%;
    text-align: center;
    vertical-align: middle;
}

.baseMainR3Titl5 {
    width: 14.2%;
    height: 30px;
    line-height: 30px;
    left: 50.6%;
    text-align: center;
    vertical-align: middle;
}

.baseMainR3Titl6 {
    width: 17.4%;
    height: 30px;
    line-height: 30px;
    right: 17.3%;
    text-align: center;
    vertical-align: middle;
}

.baseMainR3Titl7 {
    width: 17.3%;
    height: 30px;
    line-height: 30px;
    right: 0px;
    text-align: center;
    vertical-align: middle;
}

.baseMainR3Main {
    width: 100%;
    height: 60%;
    bottom: 20px;
}

.baseMainR3Main1 {
    width: 100%;
    height: 30px;
    line-height: 30px;
    font-size: 12px;
    color: #019beb;
    background-color: #06356a;
    top: 5px;
}

.baseMainR3Main2 {
    width: 100%;
    height: 30px;
    line-height: 30px;
    font-size: 12px;
    color: #019beb;
    top: 50%;
    background-color: #071e44;
}

.base18Land36R, .base18Rand36L,.base19and01 {
    font-size: 12px;
    color: white;
    text-align: center;
    line-height: 35px;
}

/* 主要区域-Right end*/

/* 主要区域 end*/
