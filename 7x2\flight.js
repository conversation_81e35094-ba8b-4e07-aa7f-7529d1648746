localStorage.setItem('7x2_return_url', window.location.href);
var now = new Date();
localStorage.setItem('7x2_return_time', now.getTime());

var flightNo = getQueryString('fltno') || getQueryString('flightNo') || getQueryString('flight');
var url_scale = getQueryString('scale');
var url_lang = getQueryString('lang');
if (url_scale) {
  url_scale = '&scale=' + url_scale;
} else {
  url_scale = '';
}
if (url_lang) {
  url_lang = '&lang=' + url_lang;
} else {
  url_lang = '';
}
if (!flightNo) {
  localStorage.removeItem('7x2_return_url');
  window.location.href = "index.html?" + url_scale + url_lang;
}


var comp_code = 'HU';
var comp_id = '100100';
var companyNodeId = 9;


var comp_code_list = ['HU', '8L', 'HX', 'PN', 'GS', 'JD', 'FU', 'UQ', 'Y8', 'GX', '9H', 'GT'];


var statusMap = {
  'ARR': '落地',
  'NDR': '落地',
  'ATD': '推出',
  'ATA': '到达',
  'CNL': '取消',
  'DEL': '延误',
  'DEP': '起飞',
  'RTR': '返航',
  'SCH': '计划'
};

var flightInfoList;
var flightInfoListObj;
var company_kpi_data;
var flt_kpi_data;

function loadAll() {

  $('.fltno').text(flightNo);
  $('.fltno').css('background-image', 'url(img/logo_' + comp_code + '.png)');


  // ------------------------------------------------------------------------
  // 接口时间
  // ------------------------------------------------------------------------
  var today_str;
  var date = new Date();
  var mm = date.getMonth() + 1;
  var dd = date.getDate();
  if (mm < 10) {
    mm = '0' + mm;
  }
  if (dd < 10) {
    dd = '0' + dd;
  }
  var today = date.getFullYear() + '-' + mm + '-' + dd;
  var stdEndUtcTime = date.getFullYear() + '-' + mm + '-' + dd + ' 15:59:59';
  var yesterday_ts = date.getTime() - 86400000;
  date.setTime(yesterday_ts);
  mm = date.getMonth() + 1;
  dd = date.getDate();
  if (mm < 10) {
    mm = '0' + mm;
  }
  if (dd < 10) {
    dd = '0' + dd;
  }
  var stdStartUtcTime = date.getFullYear() + '-' + mm + '-' + dd + ' 16:00:00';



  // 开始结束时间
  var date = new Date();
  var mm = date.getMonth() + 1;
  var dd = date.getDate();
  if (mm < 10) {
    mm = '0' + mm;
  }
  if (dd < 10) {
    dd = '0' + dd;
  }
  var stdStart = date.getFullYear() + '-' + mm + '-' + dd + ' 00:00:00';
  var stdEnd = date.getFullYear() + '-' + mm + '-' + dd + ' 23:59:59';

  var next_ts = date.getTime() + 86400000 * 7;
  date.setTime(next_ts);
  mm = date.getMonth() + 1;
  dd = date.getDate();
  if (mm < 10) {
    mm = '0' + mm;
  }
  if (dd < 10) {
    dd = '0' + dd;
  }
  var stdEnd7 = date.getFullYear() + '-' + mm + '-' + dd + ' 23:59:59';



  var param = {
    "stdStart": stdStart,
    "stdEnd": stdEnd,
    "acOwner": comp_code,
    "statusList": '',
  }
  $.ajax({
    type: 'post',
    url: "/bi/web/getStandardFocFlightInfo",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {
      var list = response.data;
      console.log("******")
      console.log(response.data);
      flightInfoList = {};
      flightInfoListObj = {};
      for (var i = list.length - 1; i >= 0; i--) {
        var obj = list[i];
        flightInfoList[obj.flightNo] = obj;
        if (flightInfoListObj[obj.flightNo] == undefined) {
          flightInfoListObj[obj.flightNo] = [];
          flightInfoListObj[obj.flightNo].push(obj);
        } else {
          flightInfoListObj[obj.flightNo].push(obj);
        }

        if (obj.flightNo == flightNo) {
          console.log(obj)
        }
      }

      var flt = flightInfoListObj[flightNo];

      if (flt == undefined) {
        $('body').hide();
        alert('没有查询到航班信息');

        return;
      }

      console.log(flightNo, flt);

      var sortFlt = [];
      if (flt.length > 1) {
        var dep1 = flt[0].depCity,
          arr1 = flt[0].arrCity,
          dep2 = flt[1].depCity,
          arr2 = flt[1].arrCity;
        if (arr1 == dep2) {
          sortFlt = flt;
        } else if (arr2 == dep1) {
          sortFlt = flt.reverse();
        }
      } else {
        sortFlt = flt;
      }


      setFltInfo(sortFlt);

      getCrew();
      getCabin(sortFlt[0].acLongNo);
      getPsr();
      getPlanePos();
      getPlaneInfo(sortFlt[0].acLongNo);



    },
    error: function() {}
  });


  function getKpi() {
    var param = {
      'SOLR_CODE': 'FAC_COMP_KPI',
      'COMP_CODE': comp_code,
      'KPI_CODE': 'TRV_RATE,NORMAL_RATE_ZT_DENOMINATOR,NORMAL_RATE_ZT_MOLECULE',
      'VALUE_TYPE': 'kpi_value_d',
      "OPTIMIZE": 1,
      'DATE_TYPE': 'M',
      'LIMIT': 1
    }

    $.ajax({
      type: 'post',
      url: "/bi/query/getkpi",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function(response) {

        if (response.data != undefined) {
          company_kpi_data = response.data;
          setNormalRate();
        }

      },
      error: function() {

      }
    });



    var param = {
      'SOLR_CODE': 'FAC_COMP_FLT_ROUT_RATE_KPI',
      'COMP_CODE': comp_id,
      'KPI_CODE': 'NORMAL_RATE_ZT_DENOMINATOR,NORMAL_RATE_ZT_MOLECULE',
      'VALUE_TYPE': 'kpi_value_d',
      'FLT_NO': flightNo,
      'DATE_TYPE': 'M',
      'LIMIT': 1,
      "OPTIMIZE": 1
    }

    $.ajax({
      type: 'post',
      url: "/bi/web/getfltkpi",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function(response) {

        if (response != undefined) {
          flt_kpi_data = response;
          setNormalRate();
        }

      },
      error: function() {

      }
    });



  }

  getKpi()

  function setNormalRate() {
    if (flt_kpi_data && company_kpi_data) {
      var dd = flt_kpi_data[comp_id]['NORMAL_RATE_ZT_DENOMINATOR']['M'][flightNo]['date'];
      var flt_sch = 0;
      if (dd) {
        flt_sch = dd[Object.keys(dd)[0]];
      }

      var dd = flt_kpi_data[comp_id]['NORMAL_RATE_ZT_MOLECULE']['M'][flightNo]['date'];
      var flt_nor = 0;
      if (dd) {
        flt_nor = dd[Object.keys(dd)[0]];
      }


      var dd = company_kpi_data[comp_code]['NORMAL_RATE_ZT_DENOMINATOR']['M'];
      var company_sch = 0;
      if (dd) {
        for (var d in dd) {
          var company_sch = dd[d];
        }
      }

      var dd = company_kpi_data[comp_code]['NORMAL_RATE_ZT_MOLECULE']['M'];
      var company_nor = 0;
      if (dd) {
        for (var d in dd) {
          var company_nor = dd[d];
        }
      }

      var flt_rate = flt_sch > 0 ? (flt_nor / flt_sch) : 0
      var company_rate = company_sch > 0 ? (company_nor / company_sch) : 0

      $('#cvs_chart1_lb1').text(Math.round(flt_rate * 1000) / 10 + '%');
      $('#cvs_chart1_lb2').text(Math.round(company_rate * 1000) / 10 + '%');

      if (flt_rate > company_rate) {
        $('#cvs_chart1_lb1').addClass('green')
      } else {
        $('#cvs_chart1_lb1').addClass('red')
      }

      drawRoundChart(1, company_rate, flt_rate);


    }
  }


  function setFltInfo(flt) {
    if (airportList == undefined) {
      return;
    }

    var statusMap = {
      'ARR': '落地',
      'NDR': '落地',
      'ATD': '推出',
      'ATA': '到达',
      'CNL': '取消',
      'DEL': '延误',
      'DEP': '起飞',
      'RTR': '返航',
      'SCH': '计划'
    };

    if (flt.length > 1) {

      $('.city1').text(flt[0].depCity);
      $('.citym').text(flt[0].arrCity);
      $('.city2').text(flt[1].arrCity);

      depAirport = airportList[flt[0].depStn]; //出发机场信息
      middleAirport = airportList[flt[0].arrStn] // 中转机场信息
      arrAirport = airportList[flt[1].arrStn]; //到达机场信息
      $('.arp1').text(flt[0].depStnCn);
      $('.arpm').text(flt[0].arrStnCn);
      $('.arp2').text(flt[1].arrStnCn);


      $('.fltsts').removeClass('status1');
      $('.fltsts').removeClass('status1');
      // 航段一
      if (flt[0].status == "ATA") {
        $(".leg1").addClass('flightStatus3');
        $('.leg1Status').text(statusMap[flt[0].status]);
        // $('.fltsts').hide();
      } else if (flt[0].delay1 != '' && flt[0].dur1 > 0) {
        $('.leg1').addClass('flightStatus2');
        $('.leg1Status').text('晚点');
        // $('.fltsts').hide();
        $('.fltsts').addClass('status2');
        $('.fltsts').text('晚点');
      } else if (flt[0].status != "ATA") {
        $('.fltsts').addClass('status1');
        // $('.fltsts').hide();
        flt[0].status == "SCH" ? $('.leg1').addClass('flightStatus3') : $('.leg1').addClass('flightStatus1');
        $('.leg1Status').text(statusMap[flt[0].status]);
        $('.fltsts').text(statusMap[flt[0].status]);
      }
      // 航段二
      if (flt[1].status == "ATA") {
        $(".leg2").addClass('flightStatus3');
        $('.leg2Status').text(statusMap[flt[1].status]);
        $('.fltsts').hide();
      } else if (flt[1].delay1 != '' && flt[1].dur1 > 0) {
        $('.leg2').addClass('flightStatus2');
        $('.leg2Status').text('晚点');
        // $('.fltsts').hide();
        if (flt[0].status == "ATA") {
          $('.fltsts').addClass('status2');
          $('.fltsts').text('晚点');
        }
      } else if (flt[1].status != "ATA") {
        flt[1].status == "SCH" ? $('.leg2').addClass('flightStatus3') : $('.leg2').addClass('flightStatus1');
        $('.leg2Status').text(statusMap[flt[1].status]);
        // $('.fltsts').hide();
        if (flt[0].status == "ATA") {
          $('.fltsts').addClass('status1');
          $('.fltsts').text(statusMap[flt[1].status]);
        }
      }

      let etdChn = flt[0].etdChn; //预计起飞时间（北京时间）
      let atdChn = flt[0].atdChn; //实际起飞时间（北京时间）
      let etaChn = flt[1].etaChn; //预计到达时间（北京时间）
      let ataChn = flt[1].ataChn; //实际到达时间（北京时间）
      let stdChn = flt[0].stdChn; //计划出发
      let staChnm = flt[0].staChn; // 中转计划到达
      let atdChnm = flt[1].atdChn; // 中转实际出发
      let ataChnm = flt[0].ataChn; // 中转实际到达
      let stdChn1 = flt[1].stdChn; // 中转计划起飞
      let staChn = flt[1].staChn; // 中转计划到达
      $('.atdChn').text(trimTime(atdChn));
      $('.etdChn').text("--");
      $('.etdChn').closest(".flex_1").find(".t2").text("");
      $('.ataChn').text(trimTime(ataChn));
      $('.stdChn').text(trimTime(stdChn));
      $('.staChn').text("--");
      $('.staChn').closest(".flex_1").find(".t2").text("");
      $('.ataChnm').text(trimTime(ataChnm));
      $('.staChnm').text(trimTime(staChnm));
      $('.stdChnm').text(trimTime(stdChn1));
      $('.staChnmm').text(trimTime(staChn));

      // 查找前序航班
      var prev_flt;
      console.log("flightInfoList", flightInfoList)
      if (flt[0].status == "ATA") {
        prev_flt = flt[0];
      } else {
        for (var fltno in flightInfoListObj) {
          //for (let i = 0; i < _flightInfoListObj[fltno].length ; i++) {
		  for (let i = 0; i < flightInfoListObj[fltno].length ; i++) {
          //  let ff = _flightInfoListObj[fltno][i]
		    let ff = flightInfoListObj[fltno][i]
            if (ff.acLongNo != '' && ff.acLongNo == flt[0].acLongNo && ff.arrStn == flt[0].depStn && ff.stdChn < flt[0].stdChn) {
              prev_flt = ff;
              break;
            }
          }
        }
      }

      if (prev_flt) {
        $('.preflight').text(prev_flt.flightNo + ' ' + prev_flt.depCity + ' ' + prev_flt.arrCity);
      } else {
        $('.preflight').text('--');
      }



      // 航班日期
      var arr = flt[0].datopChn.split('-');
      $('.flt_time').text(arr[0] + '年' + arr[1] + '月' + arr[2] + '日');

      // 航线里程
      $('.ariline_dis').text('');
      // 已执行
      $('.ariline_dis2').text('');

      // 飞行时长
      var d_time1 = parserDate(flt[0].etdChn);
      var a_time1 = parserDate(flt[0].etaChn);
      let atdChn1 = parserDate(flt[0].atdChn);//第一段实际起飞时间
      let ataChn1 = parserDate(flt[0].ataChn);//第一段实际到达时间

      var d_time2 = parserDate(flt[1].etdChn);
      var a_time2 = parserDate(flt[1].etaChn);
      let atdChn2 = parserDate(flt[1].atdChn);//第二段实际起飞时间
      let ataChn2 = parserDate(flt[1].ataChn);//第二段实际到达时间

      var msec = (a_time1.getTime() - d_time1.getTime()) + (a_time2.getTime() - d_time2.getTime());
      var min = Math.round(msec / (60 * 1000))
      var tm = Math.floor(min / 60) + '小时';
      if (min % 60 > 0) {
        tm = tm + min % 60 + '分';
      }

      $('.fly_time').text(tm);
      if (flt[0].status == "ATA" || flt[0].status == "ARR" || flt[0].status == "DNR") {
        if (flt[1].status == "DEP" || flt[1].status == "ATD") {
          $('.ariline_min').text(Math.round(((new Date() > atdChn2 ? new Date() - atdChn2 : 0) + (ataChn1 - atdChn1)) / (60 * 1000)));
        } else if (flt[1].status == "ATA" || flt[1].status == "ARR" || flt[1].status == "DNR") {
          $('.ariline_min').text(Math.round(((ataChn2 - atdChn2) + (ataChn1 - atdChn1)) / (60 * 1000)));
        } else {
          $('.ariline_min').text(Math.round((ataChn1 - atdChn1) / (60 * 1000)));
        }
      } else if (flt[0].status == "DEP" || flt[0].status == "ATD") {
        $('.ariline_min').text(Math.round((new Date() > atdChn1 ? new Date() - atdChn1 : 0) / (60 * 1000)));
      }
      // 
      $('.acno').text(flt[0].acLongNo);

    }

    if (flt.length != 2) {

      $('.city1').text(flt[0].depCity);
      $('.citym').hide();
      $('.city2').text(flt[0].arrCity);

      depAirport = airportList[flt[0].depStn]; //出发机场信息
      // middleAirport = airportList[flt[0].arrStn] // 中转机场信息
      arrAirport = airportList[flt[0].arrStn]; //到达机场信息
      $('.arp1').text(flt[0].depStnCn);
      $('.arpm').hide();
      $('.arp2').text(flt[0].arrStnCn);


      $('.fltsts').removeClass('status1');
      $('.fltsts').removeClass('status1');
      $(".leg1").hide();
      $(".leg2").hide();
      $(".jingting").hide();
      // 航段一
      if (flt[0].delay1 != '' && flt[0].dur1 > 0) {
        $('.fltsts').addClass('status2');
        $('.fltsts').text('晚点');
      } else {
        $('.fltsts').addClass('status1');
        $('.fltsts').text(statusMap[flt[0].status]);
      }


      var etdChn = flt[0].etdChn; //预计起飞时间（北京时间）
      var atdChn = flt[0].atdChn; //实际起飞时间（北京时间）
      var etaChn = flt[0].etaChn; //预计到达时间（北京时间）
      var ataChn = flt[0].ataChn; //实际到达时间（北京时间）
      var stdChn = flt[0].stdChn; //计划出发
      var staChn = flt[0].staChn; //计划到达

      $('.atdChn').text(trimTime(atdChn));
      $('.etdChn').text(trimTime(etdChn));
      $('.ataChn').text(trimTime(ataChn));
      $('.etaChn').text(trimTime(etaChn));
      $('.etaChn').closest(".flex_1").find(".t2").text("预计落地");
      $('.stdChn').text(trimTime(stdChn));
      $('.staChn').text(trimTime(staChn));
      $('.ataChnm').hide();
      $('.staChnm').hide();
      $('.atdChnm').hide();

      // 查找前序航班
      var prev_flt;
      for (var fltno in flightInfoListObj) {
        for (let i = 0; i < flightInfoListObj[fltno].length ; i++) {
          let ff = flightInfoListObj[fltno][i]
          if (ff.acLongNo != '' && ff.acLongNo == flt[0].acLongNo && ff.arrStn == flt[0].depStn && ff.stdChn < flt[0].stdChn) {
            prev_flt = ff;
            break;
          }
        }
      }
      if (prev_flt) {
        $('.preflight').text(prev_flt.flightNo + ' ' + prev_flt.depCity + ' ' + prev_flt.arrCity);
      } else {
        $('.preflight').text('--');
      }



      // 航班日期
      var arr = flt[0].datopChn.split('-');
      $('.flt_time').text(arr[0] + '年' + arr[1] + '月' + arr[2] + '日');

      // 航线里程
      $('.ariline_dis').text('');
      // 已执行
      $('.ariline_dis2').text('');

      // 飞行时长
      var d_time = parserDate(etdChn);
      var a_time = parserDate(etaChn);
      let atdChn1 = parserDate(flt[0].atdChn);//第一段实际起飞时间
      let ataChn1 = parserDate(flt[0].ataChn);//第一段实际到达时间
      var msec = a_time.getTime() - d_time.getTime();
      var min = Math.round(msec / (60 * 1000))
      var tm = Math.floor(min / 60) + '小时';
      if (min % 60 > 0) {
        tm = tm + min % 60 + '分';
      }

      $('.fly_time').text(tm);
      if (flt[0].status == "ATA" || flt[0].status == "ARR" || flt[0].status == "DNR") {
        $('.ariline_min').text(Math.round((ataChn1 - atdChn1) / (60 * 1000)));
      } else if (flt[0].status == "DEP" || flt[0].status == "ATD") {
        $('.ariline_min').text(Math.round((new Date() > atdChn1 ? new Date() - atdChn1 : 0) / (60 * 1000)));
      }

      $('.acno').text(flt[0].acLongNo);

    }
    getWeather(flt[0]);



  } //setFltInfo



  function getPlaneInfo(acLongNo) {

    var arr = stdStart.split(' ');
    var param = {
      "flightNo": flightNo,
      "flightDate": arr[0],
    }
    $.ajax({
      type: 'post',
      url: "/bi/web/getFltLoadSheetInfo",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function(response) {
        /*
        acNo: "B5636"
        actualPayload: "10573"
        basicWeight: "43187"
        cargoWeight: "1111"
        errorcode: ""
        errordesc: ""
        flightNo: "HU7823"
        maxPayload: "14250"
        maxTakeOffWeight: "71884"
        maxWeightStandard: "LandingWeight"
        maxZeroFuelWeight: "61688"
        otherBagWeight: "1111"
        otherCargoWeight: "0"
        passengerAdults: "129"
        passengerBaby: "3"
        passengerChild: "4"
        passengerCount: "136"
        passengerWeight: "9462"
        takeOffFuel: "14447"
        takeOffWeight: "68207"
        tripFuel: "10094"
        version: "C8Y156"
        */
        var ac = response.date;

        if (ac) {
          $('.oil_1').text(ac.takeOffFuel);
          $('.oil_3').text(ac.tripFuel);
          $('.otherBagWeight').text(ac.otherBagWeight);
          $('.cargoWeight').text(ac.cargoWeight);
          $('.takeOffWeight').text(ac.takeOffWeight);
        }


      },
      error: function() {}
    });


    var maintInfoList;
    var maintInfoIndex = 0;
    var param = {
      "startDate": stdStart,
      "endDate": stdEnd7,
      "acLongNo": acLongNo,
    }
    $.ajax({
      type: 'post',
      url: "/bi/web/getFocMaintInfoByList",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function(response) {

        maintInfoList = response.data;

        function setMaintInfo() {
          if (airportList == undefined) {
            setTimeout(setMaintInfo, 10);
            return;
          }
          if (maintInfoList && maintInfoList.length > 0) {
            setMaintPage();
          }
        }
        setMaintInfo()

      },
      error: function() {}
    });


    function setMaintPage() {

      $('.col_r .btn_prev').removeClass('disabled');
      $('.col_r .btn_next').removeClass('disabled');

      if (maintInfoIndex == 0) {
        $('.col_r .btn_prev').addClass('disabled');
      }
      if (maintInfoIndex == maintInfoList.length - 1) {
        $('.col_r .btn_next').addClass('disabled');
      }

      var info = maintInfoList[maintInfoIndex];

      var airport = airportList[info.stn];
      $('.mntstn').text(airport.chn_name);

      $('.seq').text(info.seq);

      $('.mntComment').text(info.mntComment);
      $('.mntComment').attr('title', info.mntComment);

      var tend = info.tEnd;
      tend = tend.slice(0, tend.lastIndexOf(':'));
      $('.mnttEnd').text(tend);

    }
    $('.col_r .btn_prev').on('click', function(evt) {
      if (maintInfoIndex > 0) {
        maintInfoIndex--
        setMaintPage()
      }
    })
    $('.col_r .btn_next').on('click', function(evt) {
      if (maintInfoIndex < maintInfoList.length - 1) {
        maintInfoIndex++
        setMaintPage()
      }
    })



    var param = {
      "flightNos": flightNo,
      "stdTimeStart": stdStart,
      "stdTimeEnd": stdEnd,
    }
    $.ajax({
      type: 'post',
      url: "/bi/web/getDspReleaseInfoV2",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function(response) {



      },
      error: function() {}
    });


  }



  // 获取飞机实时位置
  var planeLocationList;

  function getPlanePos() {
    var param = {
      'mode': 'pos'
    }
    $.ajax({
      type: 'post',
      url: "/bi/web/flightMq",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function(response) {

        planeLocationList = []

        var plist = {};

        //var list = response.data.data1;
        function processData(data1) {
          var lst = {};
          var len = data1.length;
          for (var i = 0; i < len; i++) {
            var dd = data1[i];
            var fi = dd.fi;
            if (lst[fi] == undefined) {
              lst[fi] = {
                data: []
              };
              lst[fi]['data'].push(dd);
            } else {
              lst[fi]['data'].push(dd);
            }
          }

          return lst;
        }

        var list = processData(response.data.data1);


        var fltobj = list[flightNo];
        console.log(flightNo, fltobj);

        if (fltobj) {

          var itmx2 = fltobj.data;

          var itm;

          if (itmx2 && itmx2.length > 1) {
            var itm1 = itmx2[0];
            var itm2 = itmx2[1];


            itm1.UTC = itm1.UTC.replace(' ', '');
            itm2.UTC = itm2.UTC.replace(' ', '');

            if (itm1.UTC > itm2.UTC) {
              itm = itm1
              itm.LON1 = itm2.LON;
              itm.LAT1 = itm2.LAT;
            } else if (itm1.UTC < itm2.UTC) {
              itm = itm2
              itm.LON1 = itm1.LON;
              itm.LAT1 = itm1.LAT;
            } else {

              itm = itm2
              itm.LON1 = itm1.LON;
              itm.LAT1 = itm1.LAT;

              console.log(fltno, '两组经纬度UTC相同');
            }
          } else if (itmx2 && itmx2.length > 0) {
            itm = itmx2[0];

          }


          if (itm) {

            var alt = itm.ALT;
            var cas = itm.CAS;
            var vec;

            var fltno = itm.fi;

            var acno = itm.an;
            acno = acno.replace('-', '');

            var lon = formatLonLat(itm.LON);
            var lon1 = formatLonLat(itm.LON1);
            var lat = formatLonLat(itm.LAT);
            var lat1 = formatLonLat(itm.LAT1);

            if (isNaN(itm.LON)) {
              vec = Number(itm.VEC);
            }

            var oil = isNaN(itm.OIL) ? '' : itm.OIL;

            var pdat = {
              fltno: fltno,
              acno: acno,
              alt: alt,
              vec: vec,
              lon: lon,
              lat: lat,
              lon1: lon1,
              lat1: lat1,
              oil: oil,
            };

            var code = acno + '-' + fltno;

            /*
            if(plist[code] == undefined){
                plist[code] = pdat;
            }else if(plist[code].lon1 == undefined){
                plist[code].lon1 = pdat.lon;
                plist[code].lat1 = pdat.lat;
                if(oil > 0){
                    plist[code].oil = oil;
                }
            }else if(oil > 0){
                plist[code].oil = oil;
            }
            */

            if (pdat.vec == undefined) {
              pdat.vec = getGeoAngle(pdat.lat, pdat.lon, pdat.lat1, pdat.lon1);
            }
            planeLocationList.push(pdat);


          }
        }

        /*
        for(var code in plist){
            var pdat = plist[code];
            //if(pdat.vec || pdat.lon1 != undefined){
                if(pdat.vec == undefined){
                    pdat.vec = getGeoAngle(pdat.lat, pdat.lon, pdat.lat1, pdat.lon1);
                }
                planeLocationList.push(pdat);
            //}
        }
        */

        console.log('planeLocationList', planeLocationList);

      },
      error: function(jqXHR, txtStatus, errorThrown) {

      }
    });
  }



  // 加载 飞行轨迹
  var pointlist
  var param = {
    'mode': 'track',
    'fi': flightNo,
  }
  $.ajax({
    type: 'post',
    url: "/bi/web/flightMq",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {

      pointlist = response.data;



    },
    error: function(jqXHR, txtStatus, errorThrown) {
      console.log('----error');
    }
  });

  var tmo_map;

  function setMapData() {

    if (pointlist == undefined || planeLocationList == undefined || flightInfoList == undefined) {
      tmo_map = setTimeout(setMapData, 10);
      return;
    }

    var series = [];


    // 飞机位置
    var planes = planeLocationList;
    var flightList = flightInfoListObj;
    var len = planes.length;
    var acdat;

    for (var i = 0; i < len; i++) {
      var ac = planes[i];
      if (ac.fltno == flightNo) {
        acdat = ac;
        if ($('.oil_rt') != undefined) {
          if (!isNaN(ac.oil)) {
            $('.oil_rt').text(Math.round(ac.oil) + 'cc');
          } else {
            $('.oil_rt').text('--');
          }
        }
      }
    }


    /////////////////////////////////////////////////////
    var flt;
    var depAirport = ""; //出发机场信息
    var middleAirport = ""
    var arrAirport = ""; //到达机场信息
    if (flightList[flightNo].length > 1) {
      if (flightList[flightNo][0].status == "ATA") {
        flt = flightList[flightNo][1];
      } else {
        flt = flightList[flightNo][0];
      }
      depAirport = window.parent.window.airportList[flightList[flightNo][0].depStn]; //出发机场信息
      middleAirport = window.parent.window.airportList[flightList[flightNo][1].depStn]
      arrAirport = window.parent.window.airportList[flightList[flightNo][1].arrStn]; //到达机场信息
    } else {
      flt = flightList[flightNo][0];
      depAirport = window.parent.window.airportList[flightList[flightNo][0].depStn]; //出发机场信息
      arrAirport = window.parent.window.airportList[flightList[flightNo][0].arrStn]; //到达机场信息
    }



    /////////////////////////////////////////////////////

    // 飞行轨迹
    var data = [];

    for(var i= pointlist.length-1; i>=0; i--){
      var d = pointlist[i];
      d.UTC = d.UTC.replace(/\D/g,'')
    }

    // 国内航线
    if (flt.fltType != 'I') {
      // 删除相同时间的坐标点
      var idx = 0;
      for (var i = 0; i < pointlist.length; i++) {
        pointlist[i].idx = idx;
        idx++;
        var d = pointlist[i];
        for (var j = pointlist.length - 1; j >= 0; j--) {
          var d2 = pointlist[j];
          if (d.UTC == d2.UTC && d.idx != d2.idx) {
            pointlist.splice(j, 1);
          }
        }
      }

      // 国内航线删除坐标带E，W，N，S的坐标，避免两个MQ出现航路偏差
      for (var i = pointlist.length - 1; i >= 0; i--) {
        var d = pointlist[i];
        if (isNaN(d.LAT) || isNaN(d.LON)) {
          pointlist.splice(i, 1);
        }
      }
    }

    // 计划出发时间
    var date = new Date();
    var stdChnTM = parserDate(flt.stdChn); // 计划出发时间
    var ts_dep = stdChnTM.getTime() - (8 * 60 * 60 * 1000);
    date.setTime(ts_dep);
    var mm = date.getMonth() + 1;
    var dd = date.getDate();
    var h = date.getHours();
    var m = date.getMinutes();
    var s = date.getSeconds();
    if (mm < 10) {
      mm = '0' + mm;
    }
    if (dd < 10) {
      dd = '0' + dd;
    }
    if (h < 10) {
      h = '0' + h;
    }
    if (m < 10) {
      m = '0' + m;
    }
    if (s < 10) {
      s = '0' + s;
    }
    var utc_dep = date.getFullYear() + '' + mm + '' + dd + '' + h + '' + m + '' + s; // 现在时间
    ///////


    var date = new Date();
    var ts = date.getTime() - 86400000;
    var ts_now = date.getTime() - (8 * 60 * 60 * 1000);

    date.setTime(ts);
    var mm = date.getMonth() + 1;
    var dd = date.getDate();
    if (mm < 10) {
      mm = '0' + mm;
    }
    if (dd < 10) {
      dd = '0' + dd;
    }
    var utc_time = date.getFullYear() + '' + mm + '' + dd + '210000'; // UTC 昨天 21点，北京时间今天早上5点

    //
    date.setTime(ts_now);
    var mm = date.getMonth() + 1;
    var dd = date.getDate();
    var h = date.getHours();
    var m = date.getMinutes();
    var s = date.getSeconds();
    if (mm < 10) {
      mm = '0' + mm;
    }
    if (dd < 10) {
      dd = '0' + dd;
    }
    if (h < 10) {
      h = '0' + h;
    }
    if (m < 10) {
      m = '0' + m;
    }
    if (s < 10) {
      s = '0' + s;
    }
    var utc_now = date.getFullYear() + '' + mm + '' + dd + '' + h + '' + m + '' + s; // 现在时间

    for (var i = pointlist.length - 1; i >= 0; i--) {
      // 删除昨天的航迹坐标
      var d = pointlist[i];
      //if(d.UTC < utc_time || d.UTC > utc_now){
      if (d.UTC < utc_time) {
        pointlist.splice(i, 1);
      }
    }

    pointlist.sort(function(a, b) {
      return Number(b.UTC) - Number(a.UTC);
    });

    console.log('pointlist', pointlist);


    if (pointlist && pointlist.length > 1) {
      var len = pointlist.length;
      for (var i = 1; i < len; i++) {
        var p1 = pointlist[i - 1];
        var p2 = pointlist[i];

        var lon = formatLonLat(p1.LON);
        var lat = formatLonLat(p1.LAT);

        var lon2 = formatLonLat(p2.LON);
        var lat2 = formatLonLat(p2.LAT);

        data.push({
          fromName: '',
          toName: '',
          coords: [
            [lon, lat],
            [lon2, lat2]
          ]
        });
      }

      series.push({
        name: 'lines',
        type: 'lines',
        coordinateSystem: 'geo',
        zlevel: 1,
        effect: {
          show: false,
        },
        lineStyle: {
          normal: {
            color: '#ffffff',
            width: 2,
            curveness: 0
          }
        },
        label: {
          normal: {
            show: false,
          }
        },
        silent: true,
        data: data
      });


    } else {

      // 直线距离
      /*
      series.push({
          name: 'lines',
          type: 'lines',
          coordinateSystem: 'geo',
          zlevel: 1,
          effect: {
              show: false,
          },
          lineStyle: {
              normal: {
                  color: '#ffffff',
                  width: 2,
                  curveness: 0
              }
          },
          label: {
              normal: {
                  show: false,
              }
          },
          silent: true,
          data: [{
                    fromName: depAirport.city_name,
                    toName: arrAirport.city_name,
                    coords: [[depAirport.longitude, depAirport.latitude], [arrAirport.longitude, arrAirport.latitude]]
                }]
      });
      */

    }

    ////// 设置小飞机图标。 城市位置
    var symbol = '';

    if (flt.status == 'DEP' && flt.delay1 == '') {
      symbol = 'image://img/flight.legend_1.png';
    } else {
      symbol = 'image://img/flight.legend_2.png'; // 延误航班
    }

    if (pointlist.length > 1) {
      var ac_lon = formatLonLat(pointlist[0].LON);
      var ac_lat = formatLonLat(pointlist[0].LAT);
      var ac_lon1 = formatLonLat(pointlist[1].LON);
      var ac_lat1 = formatLonLat(pointlist[1].LAT);
      var vec = getGeoAngle(ac_lat, ac_lon, ac_lat1, ac_lon1);
    } else {
      var ac_lon = acdat.lon;
      var ac_lat = acdat.lat;
      var vec = acdat.vec;
    }

    if (flightList[flightNo].length > 1) {
      series.push({
        name: 'scatter',
        type: 'scatter',
        coordinateSystem: 'geo',
        zlevel: 2,

        data: [{
          name: flightList[flightNo][0].depCity,
          isAirline: true,
          isCity: true,
          cityType: 'dep',
          value: [depAirport.longitude, depAirport.latitude],
          symbol: 'circle',
          symbolSize: 7,
          itemStyle: {
            normal: {
              color: '#3d7dd0',
              borderColor: '#ffffff',
              borderWidth: 2,
            }
          },
          label: {
            normal: {
              show: true,
              formatter: '{b}',
              offset: [0, -16],
              textStyle: {
                fontSize: 12,
                color: '#FFFFFF',
              }
            }
          }
        }, {
          name: flightList[flightNo][0].arrCity,
          isAirline: true,
          isCity: true,
          cityType: 'dep',
          value: [middleAirport.longitude, middleAirport.latitude],
          symbol: 'circle',
          symbolSize: 7,
          itemStyle: {
            normal: {
              color: '#3d7dd0',
              borderColor: '#ffffff',
              borderWidth: 2,
            }
          },
          label: {
            normal: {
              show: true,
              formatter: '{b}',
              offset: [0, -16],
              textStyle: {
                fontSize: 12,
                color: '#FFFFFF',
              }
            }
          }
        }, {
          name: flightList[flightNo][1].arrCity,
          isAirline: true,
          isCity: true,
          cityType: 'arr',
          value: [arrAirport.longitude, arrAirport.latitude],
          symbol: 'circle',
          symbolSize: 7,
          itemStyle: {
            normal: {
              color: '#3d7dd0',
              borderColor: '#ffffff',
              borderWidth: 2,
            }
          },
          label: {
            normal: {
              show: true,
              formatter: '{b}',
              offset: [0, -16],
              textStyle: {
                fontSize: 12,
                color: '#FFFFFF',
              }
            }
          }
        }, {
          name: acdat.fltno,
          acno: acdat.acno,
          oil: acdat.oil,
          flt: flt,
          isAirline: true,
          value: [ac_lon, ac_lat],
          symbol: symbol,
          symbolSize: 22,
          label: {
            normal: {
              show: false,
            }
          },
          symbolRotate: -vec + 90, //-acdat.vec+90, //航向   正北是0°，顺时针，0到360° // 因为图片机头指向右，所以需要逆时针转90度
        }]
      });
    } else {
      series.push({
        name: 'scatter',
        type: 'scatter',
        coordinateSystem: 'geo',
        zlevel: 2,

        data: [{
          name: flt.depCity,
          isAirline: true,
          isCity: true,
          cityType: 'dep',
          value: [depAirport.longitude, depAirport.latitude],
          symbol: 'circle',
          symbolSize: 7,
          itemStyle: {
            normal: {
              color: '#3d7dd0',
              borderColor: '#ffffff',
              borderWidth: 2,
            }
          },
          label: {
            normal: {
              show: true,
              formatter: '{b}',
              offset: [0, -16],
              textStyle: {
                fontSize: 12,
                color: '#FFFFFF',
              }
            }
          }
        }, {
          name: flt.arrCity,
          isAirline: true,
          isCity: true,
          cityType: 'arr',
          value: [arrAirport.longitude, arrAirport.latitude],
          symbol: 'circle',
          symbolSize: 7,
          itemStyle: {
            normal: {
              color: '#3d7dd0',
              borderColor: '#ffffff',
              borderWidth: 2,
            }
          },
          label: {
            normal: {
              show: true,
              formatter: '{b}',
              offset: [0, -16],
              textStyle: {
                fontSize: 12,
                color: '#FFFFFF',
              }
            }
          }
        }, {
          name: acdat.fltno,
          acno: acdat.acno,
          oil: acdat.oil,
          flt: flt,
          isAirline: true,
          value: [ac_lon, ac_lat],
          symbol: symbol,
          symbolSize: 22,
          label: {
            normal: {
              show: false,
            }
          },
          symbolRotate: -vec + 90, //-acdat.vec+90, //航向   正北是0°，顺时针，0到360° // 因为图片机头指向右，所以需要逆时针转90度
        }]
      });
    }



    //////////////////////////////////     


    var options = {
      series: series
    }

    rangeObj.myChart.setOption(options);



    // 航线里程
    var dis = getDistance(depAirport.latitude, depAirport.longitude, arrAirport.latitude, arrAirport.longitude);
    $('.ariline_dis').text(Math.round(dis));

    console.log('pointlist[0]', pointlist[0]);

    var dis2 = getDistance(depAirport.latitude, depAirport.longitude, planeLocationList[0].lat, planeLocationList[0].lon);
    $('.ariline_dis2').text(Math.round(dis2));

  }

  setMapData();



  // ------------------------------------------------------------------------
  // 获取 座舱布局
  // ------------------------------------------------------------------------
  var cabin;

  function getCabin(acno) {

    var param = {
      "acNo": acno,
    }
    $.ajax({
      type: 'post',
      url: "/bi/web/getAcAircraftList",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function(response) {
        var ac = response.data[0].data;
        cabin = ac.cabin; // 座舱布局
        var acType = ac.acType; // 机型
        var mtwKg = ac.mtwKg; //最大起飞重量KG
        var dewKg = ac.dewKg; //空机重量KG


        // 座舱布局图
        cabin = cabin.toUpperCase();

        $('.cabin').removeClass('CY');
        $('.cabin').removeClass('Y');
        $('.cabin').removeClass('w_CY');
        $('.cabin').removeClass('W_Y');

        if (acType.indexOf('787') > -1 || acType.indexOf('767') > -1 || acType.indexOf('777') > -1 || acType.indexOf('330') > -1) {
          if (cabin.indexOf('C') > -1 && cabin.indexOf('Y') > -1) {
            $('.cabin').addClass('W_CY');
          } else {
            $('.cabin').addClass('W_Y');
          }
        } else {
          if (cabin.indexOf('C') > -1 && cabin.indexOf('Y') > -1) {
            $('.cabin').addClass('CY');
          } else {
            $('.cabin').addClass('Y');
          }
        }

        $('.cabin .lb').text(cabin);

        $('.actype').text(ac.acCrewType);
        $('.dewKg').text(ac.dewKg);
        $('.mtwKg').text(ac.mtwKg);
        $('.oil_2').text(ac.mfLb);

        setCompName(ac.operator);

      },
      error: function() {}
    });

  }


  function setCompName(operator) {
    if (companylist.length == 0) {
      setTimeout(setCompName, 10, operator);
      return;
    }
    // 承运方
    for (var i = 0; i < companylist.length; i++) {
      var comp = companylist[i];
      if (comp.yhscode == operator) {
        $('.compname').text(comp.name);
        break;
      }
    }

  }


  // ------------------------------------------------------------------------
  // 获取 乘客信息
  // ------------------------------------------------------------------------

  function getPsr() {

    var param = {
      "flightNo": flightNo,
    }
    $.ajax({
      type: 'post',
      url: "/bi/web/findPsrStat",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function(response) {
        var psr = response.data[0];

        if (!psr) {
          drawRoundChart2(0);
          $('#cvs_chart2_lb1').text('--');
          return;
        }

        var ckiNum = psr.ckiNum; // 值机人数

        var chdNum = !isNaN(psr.chdNum) ? Number(psr.ckiChdNum) : 0;
        var eldNum = !isNaN(psr.eldNum) ? Number(psr.ckiEldNum) : 0;
        var infNum = !isNaN(psr.infNum) ? Number(psr.ckiInfNum) : 0;
        var vipNum = !isNaN(psr.vipNum) ? Number(psr.ckiVipNum) : 0;
        var cipNum = !isNaN(psr.cipNum) ? Number(psr.ckiCipNum) : 0;
        var ckiInNum = !isNaN(psr.ckiInNum) ? Number(psr.ckiInNum) : 0;
        var ckiOutNum = !isNaN(psr.ckiOutNum) ? Number(psr.ckiOutNum) : 0;

        var cNum = !isNaN(psr.cNum) ? Number(psr.ckiCNum) : 0;
        var yNum = !isNaN(psr.yNum) ? Number(psr.ckiYNum) : 0;

        var adultNum = ckiNum - chdNum - infNum;

        $('.adultNum').text(adultNum); //成人
        $('.chdNum').text(chdNum); //儿童
        $('.eldNum').text(eldNum); //老人
        $('.infNum').text(infNum); //婴儿
        $('.vipNumCipNum').text(vipNum + cipNum);
        $('.cNum').text(cNum);
        $('.yNum').text(yNum);
        $('.zz_psr').text(ckiInNum + ckiOutNum);


        function setTrvRate() {
          if (cabin == undefined || company_kpi_data == undefined) {
            setTimeout(setTrvRate, 0);
            return;
          }

          // cabin 座舱布局 C6Y170
          var arr = cabin.split('Y');
          var seat1 = !isNaN(arr[1]) ? arr[1] : 0;
          var arr2 = arr[0].split('C');
          var seat2 = !isNaN(arr2[1]) ? arr2[1] : 0;
          var seat = Number(seat1) + Number(seat2);

          // 客座率
          $('#cvs_chart2_lb1').removeClass('red');
          $('#cvs_chart2_lb1').removeClass('green');
          $('.trv_rate .down_arr').hide();
          $('.trv_rate .up_arr').hide();
          var psr_rate = seat > 0 ? Math.round(ckiNum / seat * 1000) / 10 : '-';
          if (psr_rate > 100) {
            psr_rate = 100;
          }
          if (isNaN(psr_rate)) {
            psr_rate = 0;
            drawRoundChart2(0);
            $('#cvs_chart2_lb1').text('--');
          } else {
            drawRoundChart2(psr_rate / 100);
            $('#cvs_chart2_lb1').text(psr_rate + '%');
          }


          var avg_rate = 0;
          var ddd = company_kpi_data[comp_code]['TRV_RATE']['M']
          for (var d in ddd) {
            avg_rate = Number(ddd[d]) * 100;
          }

          if (avg_rate > psr_rate) {
            $('#cvs_chart2_lb1').addClass('red');
            $('.trv_rate .down_arr').show();
            $('.trv_rate_sub').text('低于航司平均');
          } else {
            $('#cvs_chart2_lb1').addClass('green');
            $('.trv_rate .up_arr').show();
            $('.trv_rate_sub').text('高于航司平均');
          }

        }

        setTrvRate();


      },
      error: function() {}
    });
  }



  // ------------------------------------------------------------------------
  // 天气情况
  // ------------------------------------------------------------------------
  var weather = {};

  function getWeather(flt) {

    // 到达机场
    var param = {
      'airport': flt.arrStn // 到达机场三字码
    }

    $.ajax({
      type: 'post',
      url: "/bi/web/7x2_arp_weather",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function(response) {

        if (Number(response.errorcode) == 0) {

          weather[flt.arrCity] = response;
          setWeather(flt.arrCity, 2, response);


        } else {

        }



      },
      error: function(jqXHR, txtStatus, errorThrown) {}
    });


    // 出发机场
    var param = {
      'airport': flt.depStn // 出发机场三字码
    }
    $.ajax({
      type: 'post',
      url: "/bi/web/7x2_arp_weather",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function(response) {

        if (Number(response.errorcode) == 0) {

          weather[depAirport.city_name] = response;
          setWeather(depAirport.city_name, 1, response);
        } else {

        }



      },
      error: function(jqXHR, txtStatus, errorThrown) {}
    });

  }


  function setWeather(city_name, id, response) {

    var weather_map = {
      '晴': 'icon-e600_sunny',
      '沙': 'icon-e617_dust1',
      '雹': 'icon-e620_hail',
      '雾': 'icon-e615_fog',
      '烟': 'icon-e615_fog',
      '阴': 'icon-e604_gloomy',
      '雷': 'icon-e606_rain2',
      '暴': 'icon-e606_rain2',
      '风': 'icon-e612_wind',
      '霾': 'icon-e613_haze',
      '云': 'icon-e602_cloudy',
      '雨': 'icon-e607_rain3',
      '雪': 'icon-e610_snow3',
    };

    /*
    airport
    airportCode
    cloudInfo 云况
    metUtcTime
    rvr 跑道目视距离
    temperature
    visibility 能见度
    weatherInfo 天气现象
    weatherInfoTxt 翻译后的天气
    windFs 风速

    注：FG代表大雾
      TS或TSRA或+RA 代表雷暴
       SS或DS沙尘暴
    */
    var weatherInfoCodes = ['FG', 'TS', 'TSRA', 'RA', 'SS', 'DS'];
    var visibility = isNaN(response.visibility) || response.visibility == 0 ? 9999 : Number(response.visibility); //能见度
    var rvr = isNaN(response.rvr) || response.rvr == 0 ? 9999 : Number(response.rvr); //跑道目视距离
    var cloudInfo = isNaN(response.cloudInfo) || response.cloudInfo == 0 ? 9999 : Number(response.cloudInfo); //云况
    var windFs = isNaN(response.windFs) ? 0 : Number(response.windFs); //风速
    var weatherInfo = response.weatherInfo; //天气现象
    var weatherInfoTxt = response.weatherInfoTxt.replace(/<[^>]+>/g, "");
    var temperature = isNaN(response.temperature) ? '-' : Number(response.temperature) + '℃';

    var weather_css = 'icon-e600_sunny';
    for (var wtxt in weather_map) {
      if (weatherInfoTxt.indexOf(wtxt) > -1) {
        weather_css = weather_map[wtxt];
      }
    }

    $('.weather_city' + id + ' .tt').text(city_name);
    // 设置天气状况icon
    $('.weather_city' + id + ' .weather_ico span').attr('class', weather_css);
    $('.weather_city' + id + ' .temperature').text(temperature);
    $('.weather_city' + id + ' .condition').text(weatherInfoTxt);
    $('.weather_city' + id + ' .visibility').text(visibility + 'm');
    $('.weather_city' + id + ' .windFs').text(windFs + 'km/h');
  }

  $('.col_wea .blk').off('click')
  $('.col_wea .blk').on('click', function(evt) {
    $('.col_wea .blk').removeClass('selected')
    $(this).addClass('selected')

  })


  // ------------------------------------------------------------------------
  // 获取 机组信息
  // ------------------------------------------------------------------------
  function getCrew() {

    var param = {
      "flightNo": flightNo,
    }
    $.ajax({
      type: 'post',
      url: "/bi/web/findFlightReportV2",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function(response) {

        if (response.data && response.data[0]) {

          var data = response.data[0];

          // 机长
          var names = data.captain.replace(/\d+/g, '').split('@');
          for (var i = names.length - 1; i >= 0; i--) {
            if (names[i].indexOf('(') > -1) {
              var aaa = names[i].split('(');
              if (aaa.length > 1) {
                names[i] = aaa[0];
              }
            }
          }
          $('.captain').text(names.join(','));

          // 副驾
          if (data.firstVice1) {
            var names = data.firstVice1.replace(/\d+/g, '').split('@'); // 删除数字
            for (var i = names.length - 1; i >= 0; i--) {
              if (names[i].indexOf('(') > -1) {
                var aaa = names[i].split('(');
                if (aaa.length > 1) {
                  names[i] = aaa[0];
                }
              }
            }
            $('.firstVice1').text(names.join(','));
          }


          // 飞行组
          /*
          var crew = data.crwPilotInf;
          crew = crew.replace(/\d+/g,''); // 删除数字
          var arr = crew.split('；');
          var names = [];
          for(var i in arr){
              var t = arr[i];
              var n = t.split(':')[0];
              names.push(n);
          }
          var pilot = names.splice(0,3);
          var captain = pilot.shift();
          */

          // 乘务员
          var crew = data.crwStewardInf;
          if (crew) {
            crew = crew.replace(/\d+/g, ''); // 删除数字
            var arr = crew.split('；');
            var names = [];
            for (var i in arr) {
              var t = arr[i];
              var n = t.split(':')[0];
              names.push(n);
            }
            var steward = names.splice(0, 6);
            var steward_all = names;
            $('.crwStewardInf').text(steward.join(',')); //乘务员
          }

          // 安全员
          if (data.safer1) {
            var crew = data.safer1.replace(/\d+/g, ''); // 删除数字
            var safer = crew.split('/');
            safer = safer.splice(0, 2);
            $('.safer1').text(safer.join(',')); //安全员
          }


        }
      },
      error: function() {}
    });

  }



} // end of loadAll


// 获取机场列表
var airportList;

function getAirportList() {

  var param = {
    //"AIRPORT_CODE": arpcodes, // 可选，传入机场CODE
  }
  $.ajax({
    type: 'post',
    url: "/bi/web/airportdetail",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {
      airportList = {};
      var list = response.airport;
      for (var i = list.length - 1; i >= 0; i--) {
        var arp = list[i];
        airportList[arp.code] = arp;
      }
    },
    error: function() {}
  });
}
getAirportList();



// ------------------------------------------------------------------------
// 获取 飞机列表
// ------------------------------------------------------------------------
var ac_aircraft_list;

function getAcAircraftList() {
  var param = {
    "companyNodeId": companyNodeId,
  }
  $.ajax({
    type: 'post',
    url: "/bi/web/getAcAircraftList",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function(response) {

      ac_aircraft_list = response.data;

    },
    error: function() {}
  });
}
getAcAircraftList();



function getRandNumber() {
  var newnum = parseInt(Math.random() * 10);
  if (newnum > 0 && newnum < 10) {
    return newnum;
  } else {
    return getRandNumber();
  }
}



function drawRoundChart(id, rate, rate2) {

  $('#col_l .chart' + id).show();

  var canvas = document.getElementById('cvs_chart' + id);
  var context = canvas.getContext('2d');
  context.clearRect(0, 0, canvas.width, canvas.height);
  var x = canvas.width / 2;
  var y = canvas.height / 2;

  // draw back
  var radius = 60;
  var startAngle = Math.PI - Math.PI / 5;
  var endAngle = startAngle + Math.PI + Math.PI / 5 * 2;
  var counterClockwise = false;

  context.beginPath();
  context.arc(x, y, radius, startAngle, endAngle, counterClockwise);
  context.lineWidth = 7;
  context.strokeStyle = '#024491';
  context.stroke();

  // draw overlay 1 ==== 实际
  if (rate >= 0) {
    var radius = 44;
    var startAngle2 = startAngle;
    var endAngle2 = startAngle + (endAngle - startAngle) * rate;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
    context.lineWidth = 7;

    // linear gradient
    if (rate < 0.5) {
      var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
    } else if (rate < 0.8) {
      var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
    } else {
      var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, canvas.width / 2);
    }
    color.addColorStop(0, '#193F82');
    color.addColorStop(1, '#FFFFFF');

    context.strokeStyle = color;
    context.stroke();

    // pointer
    var angle = startAngle + (endAngle - startAngle) * (rate);
    $('#cvs_chart' + id + '_pointer').css('transform', 'rotate(' + (angle / Math.PI * 180) + 'deg)');
    $('#cvs_chart' + id + '_pointer').show();
  } else {
    $('#cvs_chart' + id + '_pointer').hide();
  }



  // draw overlay 2 ==== 计划
  if (rate2 >= 0) {
    var radius = 52;
    var startAngle2 = startAngle;
    var endAngle2 = startAngle + (endAngle - startAngle) * rate2;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
    context.lineWidth = 7;

    // linear gradient
    if (rate2 < 0.5) {
      var color = context.createLinearGradient(0, canvas.height, canvas.width * rate2, 0);
    } else if (rate2 < 0.8) {
      var color = context.createLinearGradient(0, canvas.height, canvas.width * rate2, 0);
    } else {
      var color = context.createLinearGradient(0, canvas.height, canvas.width * rate2, canvas.width / 2);
    }
    color.addColorStop(0, '#446295');
    color.addColorStop(1, '#93FF6E');

    context.strokeStyle = color;
    context.stroke();

    // pointer
    var angle = startAngle + (endAngle - startAngle) * (rate2);
    $('#cvs_chart' + id + 'b_pointer').css('transform', 'rotate(' + (angle / Math.PI * 180) + 'deg)');
    $('#cvs_chart' + id + 'b_pointer').show();
  } else {
    $('#cvs_chart' + id + 'b_pointer').hide();
  }


  // draw lines
  var numslice = 10;
  var radius = 48;
  for (var i = 1; i < numslice; i++) {
    context.beginPath();
    var startAngle = Math.PI - Math.PI / 5 + i * ((Math.PI + Math.PI / 5 * 2) / numslice);
    var endAngle = startAngle + Math.PI * 0.01;
    context.arc(x, y, radius, startAngle, endAngle, false);
    context.lineWidth = 16;
    context.strokeStyle = '#041946';
    context.stroke();
  }


}



function drawRoundChart2(rate) {

  var canvas = document.getElementById('cvs_chart2');
  var context = canvas.getContext('2d');
  context.clearRect(0, 0, canvas.width, canvas.height);
  var x = canvas.width / 2;
  var y = canvas.height / 2;

  var radius = 47;
  var lineWidth = 20;

  // draw back
  var startAngle = Math.PI - Math.PI / 3.6;
  var endAngle = startAngle + Math.PI + Math.PI / 3.6 * 2;
  var counterClockwise = false;

  context.beginPath();
  context.arc(x, y, radius, startAngle, endAngle, counterClockwise);
  context.lineWidth = lineWidth;
  context.strokeStyle = '#00438B';
  context.stroke();


  context.beginPath();
  context.arc(x, y, 33, startAngle, endAngle, counterClockwise);
  context.lineWidth = 1;
  context.strokeStyle = '#2683CA';
  context.stroke();


  // draw overlay
  var startAngle2 = startAngle;
  var endAngle2 = startAngle + (endAngle - startAngle) * rate;
  var counterClockwise = false;

  context.beginPath();
  context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
  context.lineWidth = lineWidth;

  // linear gradient
  if (rate < 0.5) {
    var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
  } else if (rate < 0.8) {
    var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
  } else {
    var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, canvas.width / 2);
  }
  color.addColorStop(0, '#122A61');
  color.addColorStop(1, '#78DAFC');

  context.strokeStyle = color;
  context.stroke();

  // draw head
  var startAngle2 = startAngle + (endAngle - startAngle) * rate - (endAngle - startAngle) * 0.01;
  var endAngle2 = startAngle + (endAngle - startAngle) * rate;
  var counterClockwise = false;

  context.beginPath();
  context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
  context.lineWidth = lineWidth;

  context.strokeStyle = '#FFFFFF';

  context.stroke();

}


//////////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////////



var rangeObj = this;


var createBMap = function() {

  var series = [];

  option = {
    color: [],
    tooltip: {
      trigger: 'item',
      show: false,
    },
    geo: {
      map: 'world',
      roam: true,
      zoom: 1.99,
      center: [36, 25],
      silent: true,
      label: {
        emphasis: {
          show: false
        }
      },
      itemStyle: {
        normal: {
          areaColor: '#3d7dd0',
          borderColor: '#14224c'
        },
        emphasis: {
          areaColor: '#3d7dd0',
          borderColor: '#14224c'
        }
      },
      //regions:countries
    },
    backgroundColor: '#14224c',
    series: series
  };



  var myChart = echarts.init(document.getElementById('map'));
  myChart.setOption(option);
  window.onresize = myChart.resize;


  rangeObj.myChart = myChart;



  // 经纬度坐标转换成屏幕像素坐标
  function geoCoord2Pixel(geoCoord) {
    var point = new BMap.Point(geoCoord[0], geoCoord[1]);
    var pos = map.pointToOverlayPixel(point);
    return [pos.x, pos.y];
  };


};



createBMap();



var mouseX, mouseY;
$(document).mousemove(function(e) {
  mouseX = e.pageX;
  mouseY = e.pageY;
});



// 截取小数位数
function trimDecimal(num, len) {
  var nnn = 1;
  for (var i = 0; i < len; i++) {
    nnn = nnn * 10;
  }
  return Math.round(num * nnn) / nnn;
}

// min ≤ r ≤ max
function randomNumRange(Min, Max) {
  var Range = Max - Min;
  var Rand = Math.random();
  var num = Min + Math.round(Rand * Range); //四舍五入
  return num;
}

// 获取参数
function getQueryString(name) {
  var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
  var r = window.location.search.substr(1).match(reg);
  if (r != null) return unescape(r[2]);
  return '';
}


function shuffle(arr) {
  var len = arr.length;
  for (var i = 0; i < len - 1; i++) {
    var idx = Math.floor(Math.random() * (len - i));
    var temp = arr[idx];
    arr[idx] = arr[len - i - 1];
    arr[len - i - 1] = temp;
  }
  return arr;
}


function trimTime(timestr) {
  var arr = timestr.split(' ');
  var arr2 = arr[1].split(':');
  return arr2[0] + ':' + arr2[1];
}


// 两组经纬度的距离 单位为km
function getDistance(lat1, lng1, lat2, lng2) {
  var radLat1 = lat1 * Math.PI / 180.0;
  var radLat2 = lat2 * Math.PI / 180.0;
  var a = radLat1 - radLat2;
  var b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
  var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
    Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
  s = s * 6378.137; // EARTH_RADIUS;
  s = Math.round(s * 10000) / 10000;
  return s;
}
//getDistance(10.0,113.0,12.0,114.0)

//////////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////////



loadAll();
setInterval(loadAll, 5 * 60 * 1000);