<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">

    <title>HNA数字航空</title>

    <meta http-equiv="refresh" content="86400">
    <script>
        var script = "<script src='js/app.js?ts="+ new Date().getTime() +"' ><\/script>";
        document.write(script);
    </script>
    <link href="css/nprogress.css" rel="stylesheet">
    <script src="js/nprogress.js"></script>
    <script src="js/jquery-1.11.1.js"></script>

    <script src="js/bootstrap.min.js"></script>
    <script src="js/echarts.min.js"></script>
    <script src="js/map/china.js"></script>
    <script src="js/jquery.powertip.min.js"></script>
    <script src="js/moment.min.js"></script>

    <script>
        // loadjs('js/config.js')
        loadjs('js/tingyun.js')
        loadCss('css/bootstrap.css')
        loadCss('css/bootstrap-theme.css')
        loadCss('css/main.css')
        loadCss('css/a1-1.css')
        loadCss('css/bootstrap-datetimepicker.min.css')
        loadjs('js/json.js')
 
        loadjs('js/ui.js')
        loadjs('js/util.js')
        loadjs('js/common.js')
     </script>


    <script src="map/passenger_distribution.js"></script>


</head>
<body style="opacity:0;" >

<iframe id="login" scrolling="no" width="0" height="0" style="display:none;" ></iframe>
<script src="js/config.js?ver=20211218"></script>

<audio id="text2audio"></audio>

<div id="bgcolor"></div>

<div id="mainchart" class="mainchart">
</div><!-- /mainchart -->

<div id="header">

    <div id="logo">
        <div id="main_page_title"></div>
        <div id="main_page_subtitle"></div>
    </div><!-- /#logo -->
   

    <div id="nav">
    </div><!-- /#nav -->

    <div id="topright">
        
    </div><!-- /#topright -->

</div><!-- /#header -->


<div class="page-wrapper" id="page-parent-comp">


    <div class="page" style="pointer-events: none;">

        <div class="pagetitle">
            <div class="maintitle">
                <span class="date" style="opacity: 1"></span><span class="comp"></span><span class="tit"></span>
            </div>
        </div><!-- /pagetitle -->


        <div class="top_kpi top_kpi_col1">
            <div class="tit">年旅客运输量</div>
            <div class="kpi">
                <span class="psg ffnum"></span><span class="sub fs12 blue_ll">万人次</span>
                <span class="tb"></span>
            </div>
        </div>

        <div class="top_kpi top_kpi_col2">
            <div class="tit">年旅客人数</div>
            <div class="kpi">
                <span class="psg ffnum"></span><span class="sub fs12 blue_ll">万人</span>
                <span class="tb"></span>
            </div>
        </div>

        <a class="ext_link" id="ext_link1" target="_blank" href="#"></a>


        <div class="year_trend">
            <div class="fs14 blue_ll">近5年旅客人数趋势(万人)</div>
            <div style="position: absolute; top:20px; width:180px; height:90px;">
                <div id="chart_trend" class="chartblock" style="position: absolute; width:180px; height:90px;" prop-width="180" prop-height="90"></div>
            </div>
            
        </div><!--/ year_trend-->


        <div id="year_slider">
            <div class="track"></div>

            <div class="node node0" node-id="0">
                <div class="dotb"></div>
                <div class="dot"></div>
                <div class="label"></div>
            </div>

            <div class="node node1" node-id="1">
                <div class="dotb"></div>
                <div class="dot"></div>
                <div class="label"></div>
            </div>

            <div class="node node2" node-id="2">
                <div class="dotb"></div>
                <div class="dot"></div>
                <div class="label"></div>
            </div>

            <div class="node node3" node-id="3">
                <div class="dotb"></div>
                <div class="dot"></div>
                <div class="label"></div>
            </div>

            <div class="node node4" node-id="4">
                <div class="dotb"></div>
                <div class="dot"></div>
                <div class="label"></div>
            </div>

            <div class="cursor"></div>

        </div><!--/ year_slider-->


        

        <div class="block_bl block-frame4corner">

            <div class="c0"></div>
            <div class="c1"></div>
            <div class="c2"></div>
            <div class="c3"></div>

            <div class="header">
                飞行频次
            </div>
            <div class="cont">
                <div style="position: absolute; top:45px; width:353px; height:170px;">
                    <div id="chart_frequence" class="chartblock" style="position: absolute; width:353px; height:170px;" prop-width="353" prop-height="170"></div>
                </div>
            </div>
            
        </div><!-- /block -->


        <div class="block_bm block-frame4corner">

            <div class="c0"></div>
            <div class="c1"></div>
            <div class="c2"></div>
            <div class="c3"></div>

            <div class="col1">
                <div class="header">
                    国内国际旅客占比
                </div>
                <div class="cont">
                    <div style="position: absolute; left:10px; top:20px; width:300px; height:190px;">
                        <div id="chart_bm1_msk" class="chartblock" style="position: absolute; width:300px; height:190px;" prop-width="310" prop-height="190"></div>
                        <div id="chart_bm1" class="chartblock" style="position: absolute; width:300px; height:190px;" prop-width="310" prop-height="190"></div>
                    </div>

                </div>
            </div>
            
            <div class="col2">
                <div class="header">
                    会员非会员旅客占比
                </div>
                <div class="cont">
                    <div style="position: absolute; right:10px; top:20px; width:300px; height:190px;">
                        <div id="chart_bm2_msk" class="chartblock" style="position: absolute; width:300px; height:190px;" prop-width="310" prop-height="190"></div>
                        <div id="chart_bm2" class="chartblock" style="position: absolute; width:300px; height:190px;" prop-width="310" prop-height="190"></div>
                    </div>

                </div>
            </div>

            
            
        </div><!-- /block -->


        <div class="block_br block-frame4corner">

            <div class="c0"></div>
            <div class="c1"></div>
            <div class="c2"></div>
            <div class="c3"></div>

            <div class="header">
                旅客年龄分布
            </div>
            <div class="cont">
                <div style="position: absolute; top:50px; left:10px; width:310px; height:170px; ">
                    <div id="chart_br" class="chartblock" style="position: absolute; width:310px; height:170px;" prop-width="310" prop-height="170"></div>
                </div>
            </div>
            
        </div><!-- /block -->


        <div class="block_tr block-frame4corner">

            <div class="tab tab1 hide selected" id="allCompanyTab"  tab-id="1" >分航司的旅客统计</div>
            <div class="tab tab2"  id="provinceTab" tab-id="2" >分省份的旅客统计</div>

            <table class="table1 hide">
               <thead>
                    <tr>
                        <th class="center" width="50">序号</th>
                        <th width="90">航司</th>
                        <th>旅客人数(万)</th>
                        <th width="60">同比</th>
                    </tr>
                </thead>

                <tbody>

                </tbody>
            </table>


            <table class="table2">
               <thead>
                    <tr>
                        <th class="center" width="50">序号</th>
                        <th width="60">省份</th>
                        <th>旅客人数(万)</th>
                        <th width="90">排名升降情况</th>
                    </tr>
                </thead>

                <tbody>

                </tbody>
            </table>


            <table class="table2">
            </table>

            <div class="c0"></div>
            <div class="c1"></div>
            <div class="c2"></div>
            <div class="c3"></div>

            
            
        </div><!-- /block -->
        




    </div><!-- /#page -->






</div><!-- /#wrapper -->




</body>
</html>
<script>
    loadjs4BabelDefer('js/a1-1.js')
</script>    




