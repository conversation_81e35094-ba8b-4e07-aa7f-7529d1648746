define(function (require) {

    var ec = require('echarts');
    require('echarts-x');
    require('echarts/chart/map');
    require('echarts/chart/bar');
    require('echarts-x/chart/map3d');


    var chartEarth3d = null;
    var opts;

    var markPointStyle = {
        normal: {
            color: 'rgba(85, 210, 246, 1.0)' // 空中
        }
    }
    var markPointStyle2 = {
        normal: {
            color: 'rgba(240, 140, 5, 1.0)' // 地面
        }
    }

    var planeLocation = [{
        itemStyle: markPointStyle,
        geoCoord: [0, 0]
    }];

    function refresh() {
        if (chartEarth3d) {
            chartEarth3d.dispose();
        }
        chartEarth3d = ec.init(document.getElementById('earth3d'));

        opts = {
            title: {
                show: false
            },
            legend: {
                show: false,
                data: [],
                selected: {},
                x: 'left',
                orient: 'vertical',
                textStyle: {
                    color: 'white'
                }
            },
            color: ['#73DCFF', '#73DCFF', '#FFFC38', '#73DCFF', '#73DCFF','#73DCFF'],
            tooltip: {
                formatter: '{b}'
            },
            series: [{
                type: 'map3d',
                mapType: 'world',
                baseLayer: {
                    backgroundColor: 'rgba(0, 0, 0, 0.0)',
                    backgroundImage: 'asset/earth.jpg',
                },
                hoverable: false,
                clickable: false,
                selectedMode: false,
                roam: {
                    autoRotate: false,//禁止自动旋转
                    zoom: 1,
                    minZoom: 1,
                    maxZoom: 1,
                    focus: 'China', //对准中国
                    preserve: false //每次 setOption 后保留之前的鼠标操作状态
                },
                mapLocation: {
                    x: '10%',
                    y: '10%',
                    width: '80%',
                    height: '80%'
                },
                itemStyle: {
                    normal: {
                        areaStyle: {
                            color: 'rgba(52, 130, 185, 0.0003)'
                        },
                        borderWidth: 1,
                        borderColor: 'rgba(37, 144, 218, 0.0002)'
                    },
                    emphasis: {
                        areaStyle: {
                            color: 'rgba(37, 144, 218, 0.0003)'
                        }
                    }
                },
                markPoint: {
                    effect: {
                        shadowBlur: 0.0
                    },
                    large: true,
                    symbolSize: 6,
                    data: planeLocation
                },
                markLine: [{
                    effect: {
                        show: true,
                        scaleSize: 2
                    },
                    itemStyle: {
                        normal: {
                            lineStyle: {
                                width: 30,
                                opacity: 1
                            }
                        }
                    },
                    distance: 0
                }]
            },


            ]
        };


        chartEarth3d.setOption(opts);

        window.chartEarth3d = chartEarth3d;


    }

    window.setEarthFocus = function(country){

        opts.series[0].roam.focus = country;
        chartEarth3d.setOption(opts);
    }

    window.setPlaneLocation = function(list, list2){

        planeLocation = [];

        markPointStyle.normal.color = 'rgba(85, 210, 246, 1.0)';
        markPointStyle2.normal.color = 'rgba(240, 140, 5, 1.0)';

        for(var i=list.length-1; i>=0; i--){
            var itm = list[i];
            var plane = itm.an;
            var fltno = itm.fi;
            var lon = itm.LON;
            var lat = itm.LAT;
            if(lon.indexOf('E') > -1){
                lon = lon.substr(1);
                lon = Number(lon)/1000000;
            }else if(lon.indexOf('W') > -1){
                lon = '-'+lon.substr(1);
                lon = Number(lon)/1000000;
            }
            if(lat.indexOf('N') > -1){
                lat = lat.substr(1);
                lat = Number(lat)/1000000;
            }else if(lat.indexOf('S') > -1){
                lat = '-'+lat.substr(1);
                lat = Number(lat)/1000000;
            }

            planeLocation.push({
                    //name: 'p'+i,
                    itemStyle: markPointStyle,
                    geoCoord: [lon, lat]
                });
            
        }

        for(var i=list2.length-1; i>=0; i--){
            var itm = list2[i];
            var plane = itm.an;
            var fltno = itm.fi;
            var lon = itm.LON;
            var lat = itm.LAT;
            if(lon.indexOf('E') > -1){
                lon = lon.substr(1);
                lon = Number(lon)/1000000;
            }else if(lon.indexOf('W') > -1){
                lon = '-'+lon.substr(1);
                lon = Number(lon)/1000000;
            }
            if(lat.indexOf('N') > -1){
                lat = lat.substr(1);
                lat = Number(lat)/1000000;
            }else if(lat.indexOf('S') > -1){
                lat = '-'+lat.substr(1);
                lat = Number(lat)/1000000;
            }

            planeLocation.push({
                    //name: 'p'+i,
                    itemStyle: markPointStyle2,
                    geoCoord: [lon, lat]
                });
            
        }

        opts.series[0].markPoint.data = planeLocation;
        chartEarth3d.setOption(opts);

    }

    window.hidePlaneLocation = function(){
        
        var len = planeLocation.length;
        for(i=0;i<len;i++){
            opts.series[0].markPoint.data[i].itemStyle.normal.color = 'rgba(85, 210, 246, 0.0)';
        }                

        //非常重要，否则也不会清除数据
        //opts.series[0].markPoint.data.length = 0;
        chartEarth3d.setOption(opts);
    }

    
    $(window).resize(function () {
        chartEarth3d.resize();
    });

    setTimeout(function () {
        refresh();
    });
});