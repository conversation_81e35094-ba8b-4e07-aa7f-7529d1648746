<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    
    <title>HNA数字航空</title>

    <meta http-equiv="refresh" content="86400">
    <script src="js/jquery-1.11.1.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/echarts.min.js"></script>
    <script src="js/jquery.powertip.min.js"></script>
    <script src="js/jquery.nicescroll.min.js"></script>
    <script src="js/moment.min.js"></script>
    <script>
        var script = "<script src='js/app.js?ts="+ new Date().getTime() +"' ><\/script>";
        document.write(script);
    </script>
    <script>
       loadjs('js/config.js')
       loadjs('js/tingyun.js')
       loadCss('css/bootstrap.css')
       loadCss('css/bootstrap-theme.css')
       loadCss('css/main.css')
       loadCss('css/a2-4.css')
       loadjs('js/json.js')
       loadjs('js/ui.js')
       loadjs('js/util.js')
       loadjs('js/common.js')
    </script>

</head>
<body style="opacity:0;" >

<iframe id="login" scrolling="no" width="0" height="0" style="display:none;" ></iframe>

<audio id="text2audio"></audio>

<div id="header">

    <div id="logo">
        <div id="main_page_title"></div>
        <div id="main_page_subtitle"></div>
    </div><!-- /#logo -->
   

    <div id="nav">
    </div><!-- /#nav -->

    <div id="topright">
        
    </div><!-- /#topright -->

</div><!-- /#header -->


<div class="page-wrapper" id="page-parent-comp">


    
    



    <div class="page" style="pointer-events: none;">

        <div class="pagetitle">
            <div class="maintitle">
                <span class="comp"></span><span class="tit"></span></span>
            </div>
            <div id="main_cb_week" class="combotab limcomb"></div>
            <div id="main_cb_month" class="combotab limcomb hidden"></div>
            <div id="main_cb_day" class="combotab limcomb hidden"></div>
            <div id="week_date_range"></div>
            <div class="date_type_select">
                <div class="tab" data-type="D">日</div>
                <div class="tab hover" data-type="L">周</div>
                <div class="tab" data-type="M">月</div>
            </div>
            <div class="data_refresh" id="btnrefresh" style="display:none"></div>
        </div><!-- /pagetitle -->


        

        <div class="block_l1">
            <div class="col1">
                <div class="l"></div> 
                <div class="r">
                    <div class="blue_l center tit">直销额占比</div>
                    <div id="per_SAL_VOL" class="ffnum fs40 center hide"><span class="val"></span><span class="fs30 sub">%</span></div>
                    <div id="per_tq_SAL_VOL" class="b1 hide"><span class="blue_l">同比</span> <span class="val bold"></span> <span class="ico_up hide"></span><span class="ico_down hide"></span></div>
                    <div id="per_sq_SAL_VOL" class="b2 hide"><span class="blue_l">环比</span> <span class="val bold"></span> <span class="ico_up hide"></span><span class="ico_down hide"></span></div>
                </div>

                <a class="ext_link" id="ext_link1" target="_blank" href="#"></a>

            </div>

            <div class="col2">
                <div class="ico1"></div>
                <div class="ico2"></div>                
                <div class="r1">
                    <span class="tit">直销额</span>
                    <span id="val_SAL_AMT" class="hide">
                    <span class="bold val ffnum fs22"></span><span class="sub fs12">万元</span><br>
                    <span class="hb">
                        <span class="fs12 tit">环比</span> <span class="per fs12"></span><span class="persub sub fs10">%</span><span class="green hide fs12">↑</span><span class="red hide fs10">↓</span>
                    </span>
                    <span class="tb" style="margin-left:5px;">
                        <span class="fs12 tit">同比</span> <span class="per fs12"></span><span class="persub sub fs10">%</span><span class="green hide fs12">↑</span><span class="red hide fs10">↓</span>
                    </span>
                    </span>
                </div>
                <div class="r2">
                    <span class="tit">直销量</span>
                    <span id="val_SAL_VOL" class="hide">
                    <span class="bold val ffnum fs22"></span><span class="sub fs12">张</span><br>
                    <span class="hb">
                        <span class="fs12 tit">环比</span> <span class="per fs12"></span><span class="persub sub fs10">%</span><span class="green hide fs12">↑</span><span class="red hide fs10">↓</span>
                    </span>
                    <span class="tb" style="margin-left:5px;">
                        <span class="fs12 tit">同比</span> <span class="per fs12"></span><span class="persub sub fs10">%</span><span class="green hide fs12">↑</span><span class="red hide fs10">↓</span>
                    </span>
                    </span>
                </div>
            </div>
            <div class="remark">备注：仅统计航空集团11家境内航空公司直销渠道数据</div>
        </div><!-- /#block_l1 -->


        <div class="block_l2 hide">
            <div class="tit fs18">
            <!-- <a class="ext_link" id="ext_link2" target="_blank" href="#" style="display: none;"></a> -->
                <span>各成员公司直销能力对比 </span>
            </div>
            <div class="chart">
                <!--
                <div class="mline1 fs12 ">情<br>况<br>良<br>好</div>
                <div class="mline2 fs12 ">情<br>况<br>一<br>般</div>
                <div class="mline3 fs12 ">情<br>况<br>不<br>良</div>
                -->
                <div id="chart_bg" class="chartblock" style="position: absolute; width:730px; height:396px;" prop-width="730" prop-height="396">
                    <canvas id="chart_cvs"></canvas>
                </div>
                <div class="xname fs12">成员公司</div>
                <div id="chart_l2" class="chartblock" style="width:710px; height:396px;" prop-width="710" prop-height="396"></div>
            </div>
            
            

        </div><!-- /#block_l2 -->


        <div class="block_r1">
                
            <div class="tit fs18">
                <span>渠道直销额占比</span>
            </div>

            <div class="itm itm0" data-id="0">
                <span></span>
                <div class="line"></div>
                
            </div>

            <div class="itm itm1" data-id="1">
                <span></span>
                <div class="line"></div>
            </div>

            <div class="itm itm2" data-id="2">
                <span></span>
                <div class="line"></div>
            </div>

            <div class="itm itm3" data-id="3">
                <span></span>
                <div class="line"></div>
            </div>

            <div class="itm itm4" data-id="4">
                <span></span>
                <div class="line"></div>
            </div>


            <div class="chartimg">
                <div class="val fs20 val0"></div>
                <div class="val fs18 val1"></div>
                <div class="val fs16 val2"></div>
                <div class="val fs14 val3"></div>
                <div class="val fs12 val4"></div>
            </div>

        </div><!-- /#block_r1 -->


        <div class="block_r2">
            <div class="tit fs18">
                <span></span>
            </div>
            <div class="tab tab1 selected">
                直销额占比走势
            </div>
            <div class="tab tab2">
                航司直销额占比对比
            </div>
            <div class="tab tab3">
                各OTA旗舰店分析
            </div>
            <div class="chart">
                <div id="chart_r2a" class="chartblock" style="width:545px; height:270px; " prop-width="545" prop-height="270"></div>
                <div id="chart_r2b" class="chartblock" style="width:545px; height:270px; display: hidden;" prop-width="545" prop-height="270"></div>
                <div id="chart_r2c" class="chartblock" style="width:545px; height:270px; display: hidden;" prop-width="545" prop-height="270"></div>
            </div>
            
        </div><!-- /#block_r2 -->
        




    </div><!-- /#page -->






</div><!-- /#wrapper -->






</body>
</html>

<script>
  
    loadjs4BabelDefer('js/a2-4.js')
</script>






