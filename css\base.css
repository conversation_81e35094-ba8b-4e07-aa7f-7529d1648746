
.page-bgimg {
  position: absolute;
  top: 0px;
  width: 2688px;
  height: 790px;
  background: url(../img/b1.2-bg.png?2) no-repeat top center;
  overflow-x: hidden;
  overflow-y: hidden;
}

.earthlight {
  left:933px; 
  top:50px; 
  width:676px; 
  height:676px; 
  background: url(../img/earth_light.png) no-repeat center center; 
  background-size: 520px 520px;
}

.page-wrapper {
  position: absolute;
  top: 0px;
  width: 2688px;
  height: 790px;
  overflow-x: hidden;
  overflow-y: hidden;
  pointer-events: none;
}



.yibiao_kedu {
	width:93px; 
  	height:77px; 
	background: url(../img/b1.2_yibiao_kedu.png) no-repeat center center; 
}

.thinbluebar {
	border-radius: 2px; 
	background-color:#00AFFC;
}
.thinbluebar .insidebar {
	border-radius: 2px; 
	height: 100%;
	background: -moz-linear-gradient(to right,  #00AFFC 0%, #FFF 100%);
	background: -webkit-linear-gradient(to right,  #00AFFC 0%, #FFF 100%);
	background: linear-gradient(to right,  #00AFFC 0%, #FFF 100%);
	-moz-box-shadow:0px 0px 5px #00AFFC; 
	-webkit-box-shadow:0px 0px 5px #00AFFC; 
	box-shadow:0px 0px 5px #00AFFC;
}
.thinbluebar .dot {
	top: -1px;
	width: 7px;
	height: 7px;
	border-radius: 4px; 
	background: #fff;
	-moz-box-shadow:0px 0px 8px #122348; 
	-webkit-box-shadow:0px 0px 8px #122348; 
	box-shadow:0px 0px 8px #122348;
}

.tablelist {
	position: absolute;
	width: 100%;
}
.tablelist th {
	color:#4CA5FF;
	border-bottom: 1px solid #0091D8;
	padding: 0 0 3px 7px;
	line-height: 13px;
}
.tablelist td {
	color:#99D7FF;
	border-bottom: 1px solid #0091D8;
	padding: 0 0 0 7px;
	line-height: 13px;
	height: 24px;
	font-size: 11px;
}
.tablelist .abg {
	background:#0D397B;
}
.tablelist .cl1 {
	width: 72px;
}
.tablelist .cl3 {
	width: 72px;
}
.tablelist .ico_vip, .tablelist .ico_vvip {
	display: inline-block;
	width: 12px;
	height: 10px;
}
.tablelist .ico_vip {
	background: url(../img/ico_vip.png) no-repeat center center; 
}
.tablelist .ico_vvip {
	background: url(../img/ico_vvip.png) no-repeat center center; 
}


.yunli_block {
	position: relative;
	width: 43px;
	height: 37px;
	margin-right: 5px;
	display: inline-block;
	background: url(../img/ava_plane_bg.png) no-repeat center center; 
}
.yunli_block .t1{
	position: absolute;
	width: 30px;
	height: 20px;
	color: #000;
	left: 4px;
}
.yunli_block .t2{
	position: absolute;
	width: 40px;
	height: 20px;
	top: 17px;
	left: 4px;
}

.table_runway {
	position: absolute;
	width:280px; 
	height:135px; 
	left:1690px; 
	top:122px;
	font-size: 12px;
}
.table_runway .name {
	border: 1px solid #068AC9;
	border-top: none;
	background: #094592;
	padding: 0 3px;
	text-align: center;
}
.table_runway .first {
	border-top: 1px solid #068AC9;
}
.table_runway td, .table_runway th {
	padding-left: 5px;
}

.green_tag {
	background-color: #008C00;
	padding: 0 3px;
}
.blue_tag {
	background-color: #5E9EED;
	padding: 0 3px;
}
.gray_tag {
	background-color: #666666;
	padding: 0 3px;
}
.orange_tag {
	background-color: #B25900;
	padding: 0 3px;
}
.yellow_tag {
	background-color: #B2B200;
	padding: 0 3px;
}


.backup_arp_list {
	pointer-events: auto;
}
.backup_arp_list .itm {
	margin-right: 5px;
	cursor: pointer;
}



#base_name {
	pointer-events: auto;
	width:98px; 
	left:292px;
	top:48px; 
	cursor: pointer;
	z-index: 999;
}
#base_name .title{
	position: absolute;
	font-size: 20px;
	width:98px; 
	height:26px; 
	color:#FB0B00;
	background: url(../img/base_selection_arr.png) no-repeat right 13px;
}
#base_list{
	position: absolute;
	font-size: 14px;
	top: 30px;
	left: -10px;
	width:116px;
	color:#FB0B00;
	background: #041235;
	border: 1px solid #9c233e;
	-moz-box-shadow:0px 0px 5px #000b27; 
	-webkit-box-shadow:0px 0px 5px #000b27; 
	box-shadow:0px 0px 5px #000b27;
	display: none;
}
#base_list .itm{
	position: relative;
	width:100%;
	height: 32px;
	line-height: 32px;
	padding: 0 10px;
}
#base_list .itm:hover{
	background: #08387a;
}


.tablelist .zzcl1 {
	width: 90px;
}
.tablelist .zzcl2 {
	width: 95px;
}
.tablelist .zzcl3 {
	width: 80px;
}
.tablelist .zzcl4 {
	width: 90px;
}
.tablelist .zzcl5 {
	width: 95px;
}
.tablelist .zzcl6 {
	width: 110px;
}


/* search */
.searchform {
  position: absolute;
  width: 218px;
  height: 210px;
  left: 948px;
  top: 155px;
}
.searchform .tt {
	position: relative;
  font-weight: bold;
  margin-bottom: 4px;
}
.searchform .error {
  position: relative;
  font-size: 12px;
  color: #60b7ff;
  margin-top: 5px;
  display: none;
}
.searchform .ipt {
  position: relative;
  width: 112px;
  height: 24px;
  margin: 2px 0;
}
.searchform .input {
  position: relative;
  width: 112px;
  height: 28px;
  line-height: 28px;
  background-color: #004a91;
  border-radius: 3px;
  border: none;
  pointer-events: auto;
  text-indent: 3px;
  outline: none;
  font-size: 12px;
  pointer-events: auto;
}
.searchform .ico_search {
  position: absolute;
  top: 2px;
  right: 2px;
  height: 24px;
  width: 24px;
  pointer-events: auto;
  cursor: pointer;
  background: url(../img/ico_search.png) no-repeat center;
}

#companycombo .box1 {
	border: none;
	padding-left: 10px;
	font-size: 17px;
	height: 30px;
}

#companycombo .box1:hover {
	border: none;
	background-color: transparent;
}

#companylist {
	top: 30px;
}

.logo_txt{
	position: absolute;
	top: 26px;
	left: 74px;
	height: 68px;
	width: 173px;
	background-repeat: no-repeat;
	background-position: center center;
	background-size: 173px 68px;
}
 