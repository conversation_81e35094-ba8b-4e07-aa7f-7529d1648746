showLoading();



var current_company_code;

// 可显示的历史 数量
var query_limit = 5;
// 日期类型
var date_type = 'Y';


//$('#companycombo').hide(); // 不显示成员公司


// -------------------------------------------------------

//                          KPI

// -------------------------------------------------------



var yearList = [];

var selected_year;

var origin_list;
var origin_code2name;
var all_origin_data;
var all_company_data;



// 省份／城市列表
function getAllOrigin() {
    if (companylist.length == 0) {
        setTimeout(getAllOrigin, 0);
        return;
    }

    var param = {}

    $.ajax({
        type: 'post',
        url: "/bi/web/origin",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            //{origin_code: "110000", init_flg: "L", origin_name: "北京"}
            origin_code2name = {};
            origin_list = response.origin;
            for (var i = origin_list.length - 1; i >= 0; i--) {
                var d = origin_list[i];
                if (d.init_flg == 'L') {
                    origin_code2name[d.origin_code] = d.origin_name;
                }
            }
            getOriginKpiData();
        },
        error: function () { }
    });
}



function getAllCompanyKpiData() {
    if (companylist.length == 0) {
        setTimeout(getAllCompanyKpiData, 0);
        return;
    }

    // check url hash
    var hash = window.location.hash.substr(1);
    if (hash && companyCode2Name[hash] != undefined) {
        current_company_code = hash;
    } else {
        current_company_code = parent_company;
    }


    all_company_data = {};

    var comp_code = current_company_code;

    var len = companylist.length;
    var codelist = [];
    for (var i = 0; i < len; i++) {
        
        var dat = companylist[i];
        codelist.push(dat.code);
    }


    var kpi_list = [
        'PSR_TOTAL', //旅客数年累计
        'PSR_FLT_TOTAL', //乘机人次年累计
        'DP_PSR_TOTAL', //低频旅客总量
        'HY_PSR_TOTAL', //活跃旅客总量
        'GP_PSR_TOTAL', //高频旅客总量
        'KZFR_PSR_TOTAL', //空中飞人旅客总量
        'QT_PSR_TOTAL', //其他旅客总量
        'MEMBER_PRS_TOTAL', //会员旅客总量
        'NON_MEMBER_PRS_TOTAL', //非会员旅客总量
        'NON_FOREIGN_PRS_TOTAL', //国内旅客总量
        'FOREIGN_PRS_TOTAL', //国际旅客总量
        'NON_FOREIGN_PRS_FLT_TOTAL', //国内旅客乘机总人次
        'FOREIGN_PRS_FLT_TOTAL', //国际旅客乘机总人次

        '50_M_PRS_TOTAL', //50后男性旅客总量
        '60_M_PRS_TOTAL', //
        '70_M_PRS_TOTAL', //
        '80_M_PRS_TOTAL', //
        '90_M_PRS_TOTAL', //
        '00_M_PRS_TOTAL', //

        '50_F_PRS_TOTAL', //50后女性旅客总量
        '60_F_PRS_TOTAL', //
        '70_F_PRS_TOTAL', //
        '80_F_PRS_TOTAL', //
        '90_F_PRS_TOTAL', //
        '00_F_PRS_TOTAL', //
    ];



    // 本期
    var param = {
        'SOLR_CODE': 'FAC_COMP_PSR_KPI',
        'KPI_CODE': kpi_list.join(','),
        'COMP_CODE': codelist.join(','),
        'VALUE_TYPE': 'kpi_value_d',
        'DATE_TYPE': 'Y',
        'LIMIT': query_limit,
        'OPTIMIZE': 1
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            if (response.data != undefined) {
                all_company_data['kpi_value_d'] = response.data;
                var years = response.data[parent_company]['PSR_TOTAL']['Y'];
                var latest = 0;
                for (var y in years) {
                    if (latest < y) {
                        latest = y;
                    }
                }

                selected_year = latest;

                for (var i = 0; i < query_limit; i++) {
                    var year = latest - i;
                    yearList.push(year);
                    $('#year_slider .node' + i + ' .label').text(year);
                }

                setMainKpi()
                setYearTrend()
                setBlockBLKpi()
                setBlockBM1Kpi()
                setBlockBM2Kpi()
                setBlockBRKpi()
                setBlockR1T1Kpi()

            }
        },
        error: function () { }
    });


    // 同比
    var param = {
        'SOLR_CODE': 'FAC_COMP_PSR_KPI',
        'KPI_CODE': 'PSR_TOTAL,PSR_FLT_TOTAL',
        'COMP_CODE': codelist.join(','),
        'VALUE_TYPE': 'kpi_ratio_tq_d',
        'DATE_TYPE': 'Y',
        'LIMIT': query_limit,
        'OPTIMIZE': 1
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            if (response.data != undefined) {
                all_company_data['kpi_ratio_tq_d'] = response.data;

                setMainKpi();
                setBlockR1T1Kpi();
            }
        },
        error: function () { }
    });



}


// 省份KPI数据
function getOriginKpiData() {

    all_origin_data = {};

    var comp_code = current_company_code;

    var len = companylist.length;
    var idlist = [];
    for (var i = 0; i < len; i++) {
        
        var dat = companylist[i];
        idlist.push(dat.id);
    }


    var len = origin_list.length;
    var origin_id_list = [];
    for (var origin_code in origin_code2name) {
        origin_id_list.push(origin_code);
    }


    // 本期
    var param = {
        'SOLR_CODE': 'FAC_COMP_ORIGIN_PSR_KPI',
        'KPI_CODE': 'PSR_TOTAL',
        'COMP_CODE': idlist.join(','),
        'VALUE_TYPE': 'kpi_value_d',
        'ORIGIN': origin_id_list.join(','),
        'DATE_TYPE': 'Y',
        'LIMIT': (query_limit + 1), // 多加一年算同期
        "OPTIMIZE": 1
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getoriginkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
            all_origin_data['kpi_value_d'] = response;
            showMainChart();
            //setBlockR1T2Kpi();
            hideLoading();
        },
        error: function () { }
    });

}



function updateAllKpi() {
    if (all_company_data == undefined || all_company_data['kpi_value_d'] == undefined || all_company_data['kpi_ratio_tq_d'] == undefined) {
        setTimeout(updateAllKpi, 10);
        return;
    }

    showMainChart()
    setMainKpi()
    setYearTrend()
    setBlockBLKpi()
    setBlockBM1Kpi()
    setBlockBM2Kpi()
    setBlockBRKpi()
    if (hasAllCompanyPermission()) {
        if (currentTabIndex == 1) {
            setBlockR1T1Kpi()
        } else {
            setBlockR1T2Kpi()
        }
    } else {
        currentTabIndex = 2;
        setBlockR1T2Kpi()
    }

    // 自动循环切换年份
    clearTimeout(itv_autoSwitchYear);
    itv_autoSwitchYear = setTimeout(autoSwitchYear, 30000);

    setExtLink();

    setTitleDate();
}


// 左上角2个指标
function setMainKpi() {
    if (all_company_data == undefined || all_company_data['kpi_value_d'] == undefined || all_company_data['kpi_ratio_tq_d'] == undefined) {
        return;
    }

    if (selected_year == undefined) {
        setTimeout(setMainKpi, 10);
        return;
    }

    var psr1 = all_company_data['kpi_value_d'][current_company_code]['PSR_FLT_TOTAL']['Y'][selected_year];
    psr1 = Math.round(psr1 / 1000) / 10; //万
    if (isNaN(psr1)) {
        psr1 = '-';
    }
    var tb1 = all_company_data['kpi_ratio_tq_d'][current_company_code]['PSR_FLT_TOTAL']['Y'][selected_year];
    tb1 = Math.round(tb1 * 100000) / 1000;
    if (isNaN(tb1)) {
        tb1 = '-';
    }

    var psr2 = all_company_data['kpi_value_d'][current_company_code]['PSR_TOTAL']['Y'][selected_year];
    psr2 = Math.round(psr2 / 1000) / 10; //万
    if (isNaN(psr2)) {
        psr2 = '-';
    }
    var tb2 = all_company_data['kpi_ratio_tq_d'][current_company_code]['PSR_TOTAL']['Y'][selected_year];
    tb2 = Math.round(tb2 * 100000) / 1000;
    if (isNaN(tb2)) {
        tb2 = '-';
    }

    $('.top_kpi_col1 .psg').text(psr1);
    $('.top_kpi_col2 .psg').text(psr2);

    $('.top_kpi_col1 .tb').removeClass('green');
    $('.top_kpi_col1 .tb').removeClass('red');

    if (tb1 > 0) {
        $('.top_kpi_col1 .tb').html('<span class="blue_ll">同比</span> <span class="val">' + tb1 + '</span><span class="">%</span> <span class="">↑</span>');
        $('.top_kpi_col1 .tb').addClass('green');
    } else if (tb1 < 0) {
        $('.top_kpi_col1 .tb').html('<span class="blue_ll">同比</span> <span class="val">' + tb1 + '</span><span class="">%</span> <span class="">↓</span>');
        $('.top_kpi_col1 .tb').addClass('red');
    } else {
        $('.top_kpi_col1 .tb').html('<span class="blue_ll">同比</span> -');
    }


    //

    $('.top_kpi_col2 .tb').removeClass('green');
    $('.top_kpi_col2 .tb').removeClass('red');

    if (tb2 > 0) {
        $('.top_kpi_col2 .tb').html('<span class="blue_ll">同比</span> <span class="val">' + tb2 + '</span><span class="">%</span> <span class="">↑</span>');
        $('.top_kpi_col2 .tb').addClass('green');
    } else if (tb2 < 0) {
        $('.top_kpi_col2 .tb').html('<span class="blue_ll">同比</span> <span class="val">' + tb2 + '</span><span class="">%</span> <span class="">↓</span>');
        $('.top_kpi_col2 .tb').addClass('red');
    } else {
        $('.top_kpi_col2 .tb').html('<span class="blue_ll">同比</span> -');
    }



    // 语音播报
    function onAudioTplLoad(tplobj) {

        // {COMP} {DATE} 年旅客运输量{PSR_FLT_TOTAL}人次，同比{TB1}。旅客人数{PSR_TOTAL}人，同比{TB2}。
        var tpl = tplobj.txt;

        //--
        var compname = companyCode2Name[current_company_code];
        tpl = tpl.replace(/{COMP}/g, compname);

        //--
        tpl = tpl.replace(/{DATE}/g, selected_year + '年');

        //--
        tpl = tpl.replace(/{PSR_FLT_TOTAL}/g, formatCurrency(Number(psr1) * 10000, 2));

        //--
        tpl = tpl.replace(/{TB1}/g, ratioTemplete(tb1));

        //--
        tpl = tpl.replace(/{PSR_TOTAL}/g, formatCurrency(Number(psr2) * 10000, 2));

        //--
        tpl = tpl.replace(/{TB2}/g, ratioTemplete(tb2));


        //text2audio(tpl, true, switchToNextYear);
        text2audio(tpl, true);


        // 加载下一个播放条目
        getAudioTemplate('A1.1-bottom', onAudioTplLoad2);

    }


    // 国际国内 ／ 会员非会员
    function onAudioTplLoad2(tplobj) {

        // 国内旅客占比{RATIO_L}，国际旅客占比{RATIO_I}。会员占比{RATIO_M}，非会员占比{RATIO_NM}。
        var tpl = tplobj.txt;

        var kpidata = all_company_data['kpi_value_d'][current_company_code];

        // 国际国内
        var num_l = Number(kpidata['NON_FOREIGN_PRS_TOTAL']['Y'][selected_year]);
        var num_i = Number(kpidata['FOREIGN_PRS_TOTAL']['Y'][selected_year]);
        var num_total = num_l + num_i;

        var rto_l = num_total > 0 ? Math.round(num_l / num_total * 10000) / 100 + '%' : ' 无';
        var rto_i = num_total > 0 ? Math.round(num_i / num_total * 10000) / 100 + '%' : ' 无';

        // 会员非会员
        var num_m = Number(kpidata['MEMBER_PRS_TOTAL']['Y'][selected_year]);
        var num_u = Number(kpidata['NON_MEMBER_PRS_TOTAL']['Y'][selected_year]);
        var num_tt = num_m + num_u;

        var rto_m = num_tt > 0 ? Math.round(num_m / num_tt * 10000) / 100 + '%' : ' 无';
        var rto_u = num_tt > 0 ? Math.round(num_u / num_tt * 10000) / 100 + '%' : ' 无';

        //--
        tpl = tpl.replace(/{RATIO_L}/g, rto_l);

        //--
        tpl = tpl.replace(/{RATIO_I}/g, rto_i);

        //--
        tpl = tpl.replace(/{RATIO_M}/g, rto_m);

        //--
        tpl = tpl.replace(/{RATIO_NM}/g, rto_u);


        //text2audio(tpl, true, switchToNextYear);
        text2audio(tpl, true);

    }

    if (!isNaN(psr1)) {
        stopAudio();
        getAudioTemplate('A1.1-general', onAudioTplLoad);
    } else {
        // 没有数据，直接跳转到下一年
        //switchToNextYear();
    }



}


// 近5年旅客人数趋势
function setYearTrend() {

    // ---------------------------
    var chart_id = 'chart_trend';

    var colors = [
        ['#3698fc', '#034a87'], //柱状图渐变颜色
    ];
    // ---------------------------



    var xAxisData = yearList.slice();
    xAxisData.reverse();
    var data_s1 = []; //主KPI


    var len = xAxisData.length;
    for (var i = 0; i < len; i++) {
        var year = xAxisData[i];
        var yeardata = all_company_data['kpi_value_d'][current_company_code]['PSR_TOTAL']['Y'];
        var psr = yeardata[year];
        psr = Math.round(Number(psr) / 10000)
        if (isNaN(psr)) {
            psr = 0;
        }
        data_s1.push(psr);
    }



    // ---------------------------------------------------------
    // 主KPI图表
    var chart = echarts.init(document.getElementById(chart_id));

    var option = {
        tooltip: {
            show: true,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                return params.name + ': ' + params.value + '万人';
            },
            backgroundColor: 'rgba(14, 62, 123, 0.8)',
        },
        legend: {
            show: false,
            orient: 'horizontal',
            x: 'center',
            itemWidth: 10,
            itemHeight: 10,
            padding: 10,
            itemGap: 10,
            textStyle: {
                color: 'rgba(255,255,255,0.7)',
                fontSize: 11 + fontSizeDiff()
            },
            data: [{
                name: '',
                icon: 'circle',
            }]
        },
        grid: {
            top: 20,
            left: 0,
            right: 0,
            bottom: 40,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            //boundaryGap: [10,10],
            nameTextStyle: {
                color: '#fff'
            },
            axisLine: {
                lineStyle: {
                    color: '#25548c' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 间隔显示
                rotate: 0,
                textStyle: {
                    color: '#75bafd', // 轴标签颜色大小
                    fontSize: 11 + fontSizeDiff(),
                },
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            nameTextStyle: {
                color: '#41a8ff',
                fontSize: 11 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 11 + fontSizeDiff(),
                },
                formatter: '{value}'
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ['rgba(255,255,255,0.1)'] // 分割线颜色
                }
            },
        }],
        series: [{
            name: '',
            type: 'bar',
            barWidth: 26,
            animation: false,
            data: data_s1,
            label: {
                normal: {
                    show: true,
                    position: 'top',
                    color: colors[0][0]
                }
            },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1, [{
                            offset: 0,
                            color: colors[0][0]
                        }, {
                            offset: 1,
                            color: colors[0][1]
                        }]),
                }
            },
        }]
    };

    chart.setOption(option);


}



// 中国地图
function showMainChart() {

    if (!all_origin_data || all_origin_data['kpi_value_d'] == undefined) {
        return;
    }

    if (selected_year == undefined) {
        setTimeout(showMainChart, 10);
        return;
    }

    var chart = echarts.init(document.getElementById('mainchart'));



    var series_s1 = [];
    var series_s2 = [];

    var len = companylist.length;
    var comp_id;
    for (var i = 0; i < len; i++) {
        
        var dat = companylist[i];
        if (dat.code == current_company_code) {
            comp_id = dat.id;
            break;
        }
    }
    var kpidata = all_origin_data['kpi_value_d'][comp_id]['PSR_TOTAL']['Y']['date'];
    var len = kpidata.length;
    for (var i = 0; i < len; i++) {
        var odat = kpidata[i];
        var origin_id = odat.org;
        var datelist = odat.date;
        var len2 = datelist.length;
        for (var j = 0; j < len2; j++) {
            var ddd = datelist[j];
            if (ddd.date == selected_year) {
                var psr = 0;
                if (!isNaN(ddd.value)) {
                    psr = Number(ddd.value);
                    psr = Math.ceil(psr / 5000);
                }
                var province_name = origin_code2name[origin_id];

                var cut1 = Math.round(psr / 3 * 2);
                var cut2 = Math.round(psr / 3);

                var coords1 = big_data_coord1[province_name];
                // 打乱顺序
                coords1.sort(function () {
                    return 0.5 - Math.random()
                })
                series_s1 = series_s1.concat(coords1.slice(0, cut1));

                var coords2 = big_data_coord2[province_name];
                // 打乱顺序
                coords2.sort(function () {
                    return 0.5 - Math.random()
                })
                series_s2 = series_s2.concat(coords2.slice(0, cut2));

                //console.log(province_name, psr);
            }
        }

    }


    var option = option = {
        title: {
            text: '',
        },

        tooltip: {
            show: false,
            formatter: function (params, ticket, callback) {
                //return params.seriesName + '<br>' + params.name + ': ' + Math.round(params.value);
                //var hour = params.name > 0 ? params.name : "0";
                //return hour + '点: ' + formatCurrency(params.value,0);
                console.log(params, ticket);
            },
            backgroundColor: 'rgba(14, 62, 123, 0.8)',
        },

        geo: {
            name: '旅客人数',
            type: 'scatter',
            map: 'china',
            label: {
                normal: {
                    show: false,
                    textStyle: {
                        color: '#FFFFFF'
                    }
                },
                emphasis: {
                    show: true,
                    textStyle: {
                        color: '#FFFFFF'
                    }
                }
            },
            itemStyle: {
                normal: {
                    areaColor: '#011326',
                    borderColor: '#000'
                },
                emphasis: {
                    areaColor: '#021c38',
                    borderColor: '#3191cb'
                }
            }
        },
        series: [{
            name: '旅客人数',
            type: 'scatter',
            coordinateSystem: 'geo',
            symbolSize: 1,
            large: true,
            itemStyle: {
                normal: {
                    shadowBlur: 2,
                    shadowColor: 'rgba(14, 241, 242, 0.8)',
                    color: 'rgba(37, 140, 249, 0.8)'
                }
            },
            data: series_s1
        }, {
            name: '旅客人数',
            type: 'scatter',
            coordinateSystem: 'geo',
            symbolSize: 1,
            large: true,
            itemStyle: {
                normal: {
                    shadowBlur: 10,
                    shadowColor: 'rgba(255, 255, 255, 0.8)',
                    color: 'rgba(255, 255, 255, 0.8)'
                }
            },
            data: series_s2
        }]
    }

    chart.setOption(option);
}



// 飞行频次
function setBlockBLKpi() {
    // ---------------------------
    var chart_id = 'chart_frequence';

    //var colors = ['#44a5db', '#076db0', '#0a7391', '#0066a7', '#003f77'];
    var colors = ['#e95551', '#f7a449', '#fff353', '#21b3ce', '#176ebf', '#0a3a89', '#8619db', '#aa44e1'];

    // ---------------------------

    /*
        'PSR_FLT_TOTAL', //乘机人次年累计
        'DP_PSR_TOTAL', //低频旅客总量
        'HY_PSR_TOTAL', //活跃旅客总量
        'GP_PSR_TOTAL', //高频旅客总量
        'KZFR_PSR_TOTAL', //空中飞人旅客总量
        'QT_PSR_TOTAL', //其他旅客总量
    */

    var kpidata = all_company_data['kpi_value_d'][current_company_code];

    var data_s1 = [{
        value: kpidata['KZFR_PSR_TOTAL']['Y'][selected_year],
        name: '空中飞人',
        times: '8次以上',

    }, {
        value: kpidata['GP_PSR_TOTAL']['Y'][selected_year],
        name: '高频',
        times: '6-8(含)次',

    }, {
        value: kpidata['HY_PSR_TOTAL']['Y'][selected_year],
        name: '活跃',
        times: '3-5次',

    }, {
        value: kpidata['DP_PSR_TOTAL']['Y'][selected_year],
        name: '低频',
        times: '1-2次',

    },
        /*
        {
            value:kpidata['QT_PSR_TOTAL']['Y'][selected_year],
            name:'其它',
            
        }
        */
    ];


    // ---------------------------------------------------------
    // 主KPI图表
    var chart = echarts.init(document.getElementById(chart_id));

    var option = {

        tooltip: {
            trigger: 'item',
            //formatter: "{a} <br/>{b} : {c} ({d}%)"

            formatter: function (params, ticket, callback) {
                var html = '';
                html += '<b>' + params.name + '</b><br>';
                html += '<span style="font-size:22px;">' + params.percent + '%</span><br>';
                html += '<span style="color:#71bbff; font-size:12px;">' + formatCurrency(Math.round(params.value / 1000) / 10, 0) + '万人</span><br>';
                if (params.data.times) {
                    html += '<span style="color:#71bbff; font-size:12px;">每年飞行' + params.data.times + '</span><br>';
                }
                return html;
            },
            backgroundColor: 'rgba(14, 62, 123, 0.8)',
        },
        color: colors,
        series: [{
            name: '',
            type: 'pie',
            radius: '80%',
            center: ['50%', '50%'],
            data: data_s1,
            roseType: 'angle',
            clockwise: false,
            startAngle: 30,
            label: {
                normal: {
                    formatter: '{b} {d}%',
                    textStyle: {
                        //color: '#70b4fe'
                    }
                }
            },
            labelLine: {
                normal: {
                    lineStyle: {
                        //color: 'rgba(117, 186, 253, 0.3)'
                    },
                    smooth: 0.2,
                    length: 8,
                    length2: 8
                }
            },
            itemStyle: {
                normal: {
                    borderWidth: 0,
                    shadowBlur: 200,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            },

            animationType: 'scale',
            animationEasing: 'elasticOut',
            animationDelay: function (idx) {
                return Math.random() * 200;
            }
        }]
    };

    chart.setOption(option);


}



// 国际国内 旅客占比
function setBlockBM1Kpi() {
    // ---------------------------
    var chart_id = 'chart_bm1';

    var colors = ['#0081c8', '#8db90f'];
    // ---------------------------

    var kpidata = all_company_data['kpi_value_d'][current_company_code];

    var data_s1 = [{
        value: Number(kpidata['NON_FOREIGN_PRS_TOTAL']['Y'][selected_year]),
        name: '国内',

    }, {
        value: Number(kpidata['FOREIGN_PRS_TOTAL']['Y'][selected_year]),
        name: '国际',

    }];

    var startAngle = data_s1[1].value / (data_s1[0].value + data_s1[1].value) * 360 / 2;


    // ---------------------------------------------------------
    // 主KPI图表
    var chart = echarts.init(document.getElementById(chart_id));

    var option = {

        tooltip: {
            trigger: 'item',
            //formatter: "{b} : {c} ({d}%)",
            formatter: function (params, ticket, callback) {
                var html = '';
                html += '<b>' + params.name + '</b><br>';
                html += '<span style="font-size:22px;">' + params.percent + '%</span><br>';
                html += '<span style="color:#71bbff; font-size:12px;">' + formatCurrency(Math.round(params.value / 1000) / 10, 0) + '万人</span><br>';
                return html;
            },
            backgroundColor: 'rgba(14, 62, 123, 0.8)',
        },
        legend: {
            x: 'center',
            y: 'bottom',
            itemWidth: 12,
            itemHeight: 12,
            textStyle: {
                color: '#ffffff',
            },
            data: [{
                name: '国内',
                icon: 'roundRect'
            }, {
                name: '国际',
                icon: 'roundRect'
            },]
        },
        color: colors,
        series: [{
            name: '',
            type: 'pie',
            radius: ['40%', '60%'],
            center: ['50%', '50%'],
            startAngle: startAngle,
            data: data_s1,
            clockwise: false,
            label: {
                normal: {
                    formatter: "{d}%",
                    textStyle: {
                        color: '#70b4fe',
                        fontSize: 14 + fontSizeDiff(),
                    }
                },

            },
            labelLine: {
                normal: {
                    show: false,
                    lineStyle: {
                        color: 'rgba(117, 186, 253, 0.3)'
                    },
                    smooth: 0.2,
                    length: 8,
                    length2: 8
                }
            },
            itemStyle: {
                normal: {
                    borderWidth: 0,
                    shadowBlur: 200,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            },

            animationType: 'scale',
            animationEasing: 'elasticOut',
            animationDelay: function (idx) {
                return Math.random() * 200;
            }
        }]
    };

    chart.setOption(option);


}

// 会员非会员 旅客占比
function setBlockBM2Kpi() {
    // ---------------------------
    var chart_id = 'chart_bm2';

    var colors = ['#0f9c5b', '#6d66ff'];
    // ---------------------------

    var kpidata = all_company_data['kpi_value_d'][current_company_code];

    var data_s1 = [{
        value: Number(kpidata['MEMBER_PRS_TOTAL']['Y'][selected_year]),
        name: '会员',

    }, {
        value: Number(kpidata['NON_MEMBER_PRS_TOTAL']['Y'][selected_year]),
        name: '非会员',

    }];

    var startAngle = data_s1[1].value / (data_s1[0].value + data_s1[1].value) * 360 / 2;


    // ---------------------------------------------------------
    // 主KPI图表
    var chart = echarts.init(document.getElementById(chart_id));

    var option = {

        tooltip: {
            trigger: 'item',
            //formatter: "{b} : {c} ({d}%)",
            formatter: function (params, ticket, callback) {
                var html = '';
                html += '<b>' + params.name + '</b><br>';
                html += '<span style="font-size:22px;">' + params.percent + '%</span><br>';
                html += '<span style="color:#71bbff; font-size:12px;">' + formatCurrency(Math.round(params.value / 1000) / 10, 0) + '万人</span><br>';
                return html;
            },
            backgroundColor: 'rgba(14, 62, 123, 0.8)',
        },
        legend: {
            x: 'center',
            y: 'bottom',
            itemWidth: 12,
            itemHeight: 12,
            textStyle: {
                color: '#ffffff',
            },
            data: [{
                name: '会员',
                icon: 'roundRect'
            }, {
                name: '非会员',
                icon: 'roundRect'
            },]
        },
        color: colors,
        series: [{
            name: '',
            type: 'pie',
            radius: ['40%', '60%'],
            center: ['50%', '50%'],
            startAngle: startAngle,
            data: data_s1,
            clockwise: false,
            label: {
                normal: {
                    formatter: "{d}%",
                    textStyle: {
                        color: '#70b4fe',
                        fontSize: 14 + fontSizeDiff(),
                    }
                },

            },
            labelLine: {
                normal: {
                    show: false,
                    lineStyle: {
                        color: 'rgba(117, 186, 253, 0.3)'
                    },
                    smooth: 0.2,
                    length: 8,
                    length2: 8
                }
            },
            itemStyle: {
                normal: {
                    borderWidth: 0,
                    shadowBlur: 200,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            },

            animationType: 'scale',
            animationEasing: 'elasticOut',
            animationDelay: function (idx) {
                return Math.random() * 200;
            }
        }]
    };

    chart.setOption(option);


}



// 分航司的旅客统计
function setBlockR1T1Kpi() {

    if (all_company_data['kpi_value_d'] == undefined || all_company_data['kpi_ratio_tq_d'] == undefined) {
        return;
    }

    if (selected_year == undefined) {
        setTimeout(setBlockR1T1Kpi, 10);
        return;
    }

    var ranklist = [];
    for (var compcode in all_company_data['kpi_value_d']) {
        if (compcode != parent_company) {
            var psr = all_company_data['kpi_value_d'][compcode]['PSR_TOTAL']['Y'][selected_year];
            psr = Math.round(psr / 1000) / 10; //单位 万
            if (psr > 0) {
                var tb = all_company_data['kpi_ratio_tq_d'][compcode]['PSR_TOTAL']['Y'][selected_year];
                tb = Math.round(tb * 100000) / 1000;
                ranklist.push({
                    code: compcode,
                    psr: psr,
                    tb: tb
                });
            }
        }
    }

    ranklist.sort(function (a, b) {
        return b.psr - a.psr
    });

    var html = '';

    if (ranklist.length > 0) {

        var maxpsr = ranklist[0].psr;

        var len = ranklist.length;
        //len = Math.min(len, 10);
        for (var i = 0; i < len; i++) {
            var dat = ranklist[i];
            var code = dat.code;
            var name = companyCode2Name[code];
            var width = 80 * dat.psr / maxpsr;
            html += '';
            html += '<tr>';
            html += '<td class="no center blue_ll">' + (i + 1) + '</td>';
            html += '<td class="name">' + name + '</td>';
            html += '<td class="cnt"><span data-width="' + width + '" class="bar trans_bar' + i + '" ></span><span class="val blue_ll">' + dat.psr + '</span></td>';
            if (dat.tb > 0) {
                html += '<td class="tb green"><span class="val">' + dat.tb + '</span><span class="">%</span> <span class="">↑</span></td>';
            } else if (dat.tb < 0) {
                html += '<td class="tb red"><span class="val">' + dat.tb + '</span><span class="">%</span> <span class="">↓</span></td>';
            } else {
                html += '<td class="tb"><span class="val">-</span></td>';
            }

            html += '</tr>';
        }

    }


    $('.block_tr .table1 tbody').html(html);

    var len = ranklist.length;
    for (var i = 0; i < len; i++) {
        $('.trans_bar' + i).animate({
            width: $('.trans_bar' + i).attr('data-width') + 'px'
        }, {
            queue: false,
            duration: randomNumRange(200, 400)
        });
    }


}

// 分省份的旅客统计
function setBlockR1T2Kpi() {

    if (all_origin_data['kpi_value_d'] == undefined) {
        return;
    }

    if (selected_year == undefined) {
        setTimeout(setBlockR1T2Kpi, 10);
        return;
    }

    var selected_last_year = Number(selected_year) - 1;

    var len = companylist.length;
    var comp_id;
    for (var i = 0; i < len; i++) {
        
        var dat = companylist[i];
        if (dat.code == current_company_code) {
            comp_id = dat.id;
            break;
        }
    }

    var kpidata = all_origin_data['kpi_value_d'][comp_id]['PSR_TOTAL']['Y']['date'];

    //{origin_code: "110000", init_flg: "L", origin_name: "北京"}

    var ranklist = [];
    var ranklist_tq = [];
    var len = kpidata.length;
    var all_data_is_0 = true;
    for (var i = 0; i < len; i++) {
        var datlist = kpidata[i];

        var origin_id = datlist.org;
        if (!isNaN(origin_id)) {
            var origin_name = origin_code2name[origin_id];
            var psr = undefined;
            var psr_tq = undefined;

            var ddd = datlist.date;
            var len2 = ddd.length;
            for (var j = 0; j < len2; j++) {
                var obj = ddd[j];
                var date = obj.date;
                var val = obj.value;
                if (date == selected_year && !isNaN(val)) {
                    psr = Number(val);
                    psr = Math.round(psr / 1000) / 10; //单位 万
                } else if (date == selected_last_year && !isNaN(val)) {
                    psr_tq = Number(val);
                    psr_tq = Math.round(psr_tq / 1000) / 10; //单位 万
                }
            }

            ranklist.push({
                name: origin_name,
                psr: psr
            });
            ranklist_tq.push({
                name: origin_name,
                psr: psr_tq
            });
        }
    }


    ranklist.sort(function (a, b) {
        return b.psr - a.psr
    });
    ranklist_tq.sort(function (a, b) {
        return b.psr - a.psr
    });

    var html = '';

    if (ranklist.length > 0) {

        var maxpsr = ranklist[0].psr;

        var len = ranklist.length;
        len = Math.min(len, 10);
        for (var i = 0; i < len; i++) {
            var dat = ranklist[i];
            var name = dat.name;
            var width = 80 * dat.psr / maxpsr;
            var rank2 = 0;
            for (var j = 0; j < len; j++) {
                var dat2 = ranklist_tq[j];
                if (dat.name == dat2.name && dat2.psr) {
                    rank2 = j;
                    break;
                }
            }
            var rankchange = rank2 > 0 ? rank2 - i : 0;
            html += '';
            html += '<tr>';
            html += '<td class="no center blue_ll">' + (i + 1) + '</td>';
            html += '<td class="name">' + name + '</td>';
            html += '<td class="cnt"><span data-width="' + width + '" class="bar trans_bar' + i + '" ></span><span class="val blue_ll">' + dat.psr + '</span></td>';
            if (rankchange > 0) {
                html += '<td class="tb green"><span class="val">' + rankchange + '</span> <span class="">↑</span></td>';
            } else if (rankchange < 0) {
                html += '<td class="tb red"><span class="val">' + rankchange + '</span> <span class="">↓</span></td>';
            } else {
                html += '<td class="tb"><span class="val">-</span></td>';
            }

            html += '</tr>';

        }

    }


    $('.block_tr .table2 tbody').html(html);

    var len = ranklist.length;
    for (var i = 0; i < len; i++) {
        $('.trans_bar' + i).animate({
            width: $('.trans_bar' + i).attr('data-width') + 'px'
        }, {
            queue: false,
            duration: randomNumRange(200, 400)
        });
    }


}

// 旅客年龄分布
function setBlockBRKpi() {
    // ---------------------------
    var chart_id = 'chart_br';

    var colors = ['#0883ff', '#e6202a'];
    // ---------------------------

    var data_m = []; //男
    var data_f = []; //女


    /*
        '50_M_PRS_TOTAL', //50后男性旅客总量
        '60_M_PRS_TOTAL', //
        '70_M_PRS_TOTAL', //
        '80_M_PRS_TOTAL', //
        '90_M_PRS_TOTAL', //
        '00_M_PRS_TOTAL', //
        
        '50_F_PRS_TOTAL', //50后女性旅客总量
        '60_F_PRS_TOTAL', //
        '70_F_PRS_TOTAL', //
        '80_F_PRS_TOTAL', //
        '90_F_PRS_TOTAL', //
        '00_F_PRS_TOTAL', //
    */
    var kpidata = all_company_data['kpi_value_d'][current_company_code];

    data_m = [-Math.round(Number(kpidata['00_M_PRS_TOTAL']['Y'][selected_year]) / 10000), -Math.round(Number(kpidata['90_M_PRS_TOTAL']['Y'][selected_year]) / 10000), -Math.round(Number(kpidata['80_M_PRS_TOTAL']['Y'][selected_year]) / 10000), -Math.round(Number(kpidata['70_M_PRS_TOTAL']['Y'][selected_year]) / 10000), -Math.round(Number(kpidata['60_M_PRS_TOTAL']['Y'][selected_year]) / 10000), -Math.round(Number(kpidata['50_M_PRS_TOTAL']['Y'][selected_year]) / 10000)];

    data_f = [
        Math.round(Number(kpidata['00_F_PRS_TOTAL']['Y'][selected_year]) / 10000),
        Math.round(Number(kpidata['90_F_PRS_TOTAL']['Y'][selected_year]) / 10000),
        Math.round(Number(kpidata['80_F_PRS_TOTAL']['Y'][selected_year]) / 10000),
        Math.round(Number(kpidata['70_F_PRS_TOTAL']['Y'][selected_year]) / 10000),
        Math.round(Number(kpidata['60_F_PRS_TOTAL']['Y'][selected_year]) / 10000),
        Math.round(Number(kpidata['50_F_PRS_TOTAL']['Y'][selected_year]) / 10000)
    ];


    // ---------------------------------------------------------
    // 主KPI图表
    var chart = echarts.init(document.getElementById(chart_id));

    var option = {
        title: {
            text: '单位：万人',
            right: 0,
            top: 0,
            textStyle: {
                color: '#70b4fe',
                fontSize: 12 + fontSizeDiff(),
            }
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: { // 坐标轴指示器，坐标轴触发有效
                type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
            },
            //formatter: '{b}<br> {a0}: {c0}<br>{a1}: {c1}'
            formatter: function (params, ticket, callback) {
                var psr_total = all_company_data['kpi_value_d'][current_company_code]['PSR_TOTAL']['Y'][selected_year];
                psr_total = Number(psr_total) / 10000;
                var html = '';
                html += '<b>' + params[0].name + '后</b><br>';
                html += '<span style="font-size:12px;">' + params[0].seriesName + ':' + Math.abs(params[0].data) + '万人 </span>';
                html += '<span style="color:#71bbff; font-size:12px;">占比:' + Math.round(Math.abs(params[0].data) / (psr_total) * 1000) / 10 + '%</span><br>';
                html += '<span style="font-size:12px;">' + params[1].seriesName + ':' + Math.abs(params[1].data) + '万人 </span>';
                html += '<span style="color:#71bbff; font-size:12px;">占比:' + Math.round(Math.abs(params[1].data) / (psr_total) * 1000) / 10 + '%</span>';
                return html;
            },
            backgroundColor: 'rgba(14, 62, 123, 0.8)',
        },
        legend: {
            x: 'left',
            y: 'top',
            itemWidth: 12,
            itemHeight: 12,
            textStyle: {
                color: '#70b4fe',
                fontSize: 12 + fontSizeDiff(),
            },
            data: [{
                name: '男性数量',
                icon: 'roundRect'
            }, {
                name: '女性数量',
                icon: 'roundRect'
            },]
        },
        color: colors,
        grid: {
            top: 40,
            left: 10,
            right: 10,
            bottom: 11,
            containLabel: true
        },
        xAxis: [{
            type: 'value',
            nameTextStyle: {
                color: '#fff'
            },
            axisLine: {
                lineStyle: {
                    color: '#25548c' // 轴线颜色
                }
            },
            axisLabel: {
                interval: 0, // 间隔显示
                rotate: 0,
                formatter: function (value, index) {
                    return Math.abs(value)
                },
                textStyle: {
                    color: '#75bafd', // 轴标签颜色大小
                    fontSize: 11 + fontSizeDiff(),
                },
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['#04305a'] // 分割线颜色
                }
            },
            axisTick: {
                show: false, // 不显示刻度线
            }
        }],
        yAxis: [{
            type: 'category',
            name: '',
            position: 'left',
            nameTextStyle: {
                color: '#41a8ff',
                align: 'left',
                fontSize: 11 + fontSizeDiff()
            },
            axisLabel: {
                textStyle: {
                    color: '#41a8ff',
                    fontSize: 11 + fontSizeDiff(),
                },
                formatter: function (value, index) {
                    return Math.abs(value)
                },
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(0,0,0,0)'
                }
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ['rgba(255,255,255,0.1)'] // 分割线颜色
                }
            },
            data: ['00', '90', '80', '70', '60', '50'],
        }],
        series: [{
            name: '男性数量',
            type: 'bar',
            stack: '数量',
            label: {
                normal: {
                    show: true,
                    position: 'inside',
                    formatter: function (params) {
                        return Math.abs(params.data)
                    },
                }

            },
            data: data_m
        }, {
            name: '女性数量',
            type: 'bar',
            stack: '数量',
            label: {
                normal: {
                    show: true
                }
            },
            data: data_f
        }]
    };

    chart.setOption(option);
    $("#" + chart_id).find("#markname").remove();
    $("#" + chart_id).append('<span id="markname" style="position: absolute;z-index: 500;color: #41a8ff;font-size: ' + (11 + fontSizeDiff()) + 'px;left: 5px;top:22px;">出生年代</span>')
}



// 设置页面元素缩放比例
function onSizeChange() {
    var wapperWidth = 1366;
    var wapperHeight = 768;
    var osw = document.body.offsetWidth;

    var scale = pageZoomScale;

    console.log('pageZoomScale', pageZoomScale);

    var navh = ($('#nav').height() - 18) * scale;

    var left = 0;
    var top = navh;
    var width = osw - 430 * scale;
    var height = osw * (wapperHeight / wapperWidth) - 220 * scale;

    $('#mainchart').attr('style', 'left:' + left + 'px; ' + 'top:' + top + 'px; ' + 'width:' + width + 'px; ' + 'height:' + height + 'px');
    $('#bgcolor').css('height', (osw * (wapperHeight / wapperWidth)) + 'px');

    var chart_bgsize = 66 * scale;
    $('#chart_bm1_msk').css('background-size', chart_bgsize + 'px ' + chart_bgsize + 'px');
    $('#chart_bm2_msk').css('background-size', chart_bgsize + 'px ' + chart_bgsize + 'px');
}
onSizeChange();



// 年份选择器
var selected_year_id;

function selectYear(id) {
    selected_year_id = id;
    selected_year = yearList[id];
    var top = 90 * id;
    $('#year_slider .cursor').css('top', top + 'px');
    $('#year_slider .node .label').removeClass('selected');
    $('#year_slider .node' + id + ' .label').addClass('selected');
}

$('#year_slider .node').on('click', function (evt) {
    var id = $(this).attr('node-id');

    var year = yearList[id];
    var psr = all_company_data['kpi_value_d'][current_company_code]['PSR_FLT_TOTAL']['Y'][year];
    if (isNaN(psr)) {
        return;
    }

    selectYear(id);
    updateAllKpi();
})
selectYear(0);

dfd.done(function () {
    if (!hasAllCompanyPermission()) {
        $('.block_tr .tab').removeClass('selected');
        $('.block_tr .tab' + 2).addClass('selected');
        $("#provinceTab").width(330);
        $('.block_tr table').hide();
        $('.block_tr .table' + 2).show();
    } else {
        //切换tab
        $('#allCompanyTab').removeClass("hide");
        $('.block_tr table').hide();
        $('.block_tr .table1').show();
        $('.block_tr .tab').on('click', function (evt) {
            var id = $(this).attr('tab-id');
            currentTabIndex = id;
            $('.block_tr .tab').removeClass('selected');
            $('.block_tr .tab' + id).addClass('selected');
            $('.block_tr table').hide();
            $('.block_tr .table' + id).show();

            if (currentTabIndex == 1) {
                setBlockR1T1Kpi()
            } else {
                setBlockR1T2Kpi()
            }

            clearTimeout(itv_autoSwitchTab);
            itv_autoSwitchTab = setTimeout(autoSwitchTab, 10000);
        })
    }
})




var currentTabIndex = 1;

function autoSwitchTab() {
    clearTimeout(itv_autoSwitchTab);

    if (!autoSwitch) {
        itv_autoSwitchTab = setTimeout(autoSwitchTab, 10);
        return;
    }

    if (currentTabIndex == 1) {
        $('.block_tr .tab2').click();
    } else {
        $('.block_tr .tab1').click();
    }

    itv_autoSwitchTab = setTimeout(autoSwitchTab, 10000);

}

// 自动循环切换两个TAB
var itv_autoSwitchTab;
clearTimeout(itv_autoSwitchTab);
itv_autoSwitchTab = setTimeout(autoSwitchTab, 10000);


// 自动循环切换年份 -----------------

var itv_autoSwitchYear;

function autoSwitchYear() {

    return; //轮播：不要自动轮播年份，不然语音会一直读；



    if (yearList.length == 0) {
        clearTimeout(itv_autoSwitchYear);
        itv_autoSwitchYear = setTimeout(autoSwitchYear, 10)
        return;
    }
    clearTimeout(itv_autoSwitchYear);

    if (audioPaused) {
        switchToNextYear();
    }

}

function switchToNextYear() {
    if (selected_year_id < yearList.length - 1) {
        selected_year_id++;
    } else {
        selected_year_id = 0;
    }

    selectYear(selected_year_id);
    updateAllKpi();
}

itv_autoSwitchYear = setTimeout(autoSwitchYear, 10000);


// ---------------------- 

function onCompanyChanged(comp_code) {
    current_company_code = comp_code;
    checkCompanyReay();
}

function checkCompanyReay() {
    if (companyCode2Name[current_company_code] == undefined) {
        setTimeout(checkCompanyReay, 0);
    } else {
        updateAllKpi();
    }
}



getAllCompanyKpiData();
getAllOrigin();



function setTitleDate() {
    $('.pagetitle .maintitle .date').html(selected_year); // 页面标题-时间
}



// HBI 外部跳转链接

function setExtLink() {

    $('#ext_link1').attr('href', encodeURI('http://cdp.hnair.net:8080/HBI/rdThird.do?res_id=145369283091707057&paramArr=公司=<' + current_company_code + '>::报表类型=<年>::日期=<' + selected_year + '>'));
}
regTooltip('.ext_link', '查看关联报表');


