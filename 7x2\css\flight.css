
.page-bgimg {
  position: absolute;
  top: 0px;
  width: 2688px;
  height: 790px;
  background: url(../img/flight.bg.png?2) no-repeat top center;
  background-size: 100%;
  overflow-x: hidden;
  overflow-y: hidden;
  pointer-events: none;
}


.page-wrapper {
  position: absolute;
  top: 0px;
  width: 2688px;
  height: 790px;
  overflow-x: hidden;
  overflow-y: hidden;
  pointer-events: none;
}

#map{
	pointer-events: auto !important;
}


.con_flex_column{ display: -webkit-flex; -webkit-flex-flow: column;}
.con_flex_row{ display: -webkit-flex; -webkit-flex-flow: row;}
.con_flex_row div{position: initial;}
.con_flex_row .c4{
	line-height: 35px;
}
.con_flex_row .c4 .fs12{
	text-align: center;
	line-height: 20px;
}
.con_flex_row .c2{
	justify-content: center;
}
.con_flex_row .c2 .t2,.con_flex_row .c2 .t1{
	text-align: center;
}
.flex_none{-webkit-flex:none;}
.flex_1{-webkit-flex:1;}


.blue {
	color:#77BAF8;
}
.green {
	color:#9FDD00;
}





.col_l {
	pointer-events: auto;
	position: absolute;
	top: 110px;
	left: 90px;
	width: 292px;
	height: 611px;
	background-color: rgba(255,0,0,0.0);
}

.col_l .tt {
	position: absolute;
	top: 3px;
	left: 108px;
	background-color: rgba(255,0,0,0.0);

	color: #051339;
	font-size: 18px;
}

.col_l .fltno {
	position: absolute;
	top: 38px;
	left: 11px;
	background-color: rgba(255,0,0,0.0);

	color: #FFF;
	font-size: 16px;

	padding-left: 24px;
	background-repeat: no-repeat;
	background-position: 0 center;
	background-size: auto 16px;
}
.col_l .acimg {
	position: absolute;
	top: 66px;
	left: 11px;
	width: 270px;
	height: 159px;

	background-color: rgba(255,255,255,0.05);
	
	background-repeat: no-repeat;
	background-position: center;
	background-size: cover;
}

.col_l .blk1 {
	position: absolute;
	top: 242px;
	left: 20px;
	width: 253px;
	height: 220px;

	background-color: rgba(255,0,0,0.0);
}

.col_l .blk1 .row1 {
	position: relative;
	width: 100%;
}
.col_l .blk1 .row1 .c1{
	position: relative;
}
.col_l .blk1 .row1 .c2{
	position: relative;
	text-align: center;
}
.col_l .blk1 .row1 .c2 .arr{
	width: 100%;
	height: 20px;
	background: url(../img/flight.arr.png?1) no-repeat top center;
}
.col_l .blk1 .row1 .c3{
	position: relative;
	text-align: right;
}
.col_l .blk1 .row1 .t1{
	position: relative;
	font-size: 18px;
}
.col_l .blk1 .row1 .t2{
	position: relative;
	font-size: 14px;
	color: #77BAF8;
	white-space:nowrap;
}



.col_l .blk1 .row2 {
	position: relative;
	width: 100%;
	margin-top: 13px;
}
.col_l .blk1 .row2 .c1{
	position: relative;
}
.col_l .blk1 .row2 .c3{
	position: relative;
	text-align: right;
}
.col_l .blk1 .row2 .t1{
	position: relative;
	font-size: 16px;
}
.col_l .blk1 .row2 .t2{
	position: relative;
	font-size: 14px;
	color: #77BAF8;
}

.col_l .blk2 {
	position: absolute;
	left: 20px;
	width: 253px;
	height: 220px;

	background-color: rgba(255,0,0,0.0);
}
.col_l .blk2 .t1{
	position: relative;
	font-size: 16px;
}
.col_l .blk2 .t2{
	position: relative;
	font-size: 14px;
	color: #77BAF8;
}







.col_l2 {
	pointer-events: none;
	position: absolute;
	top: 121px;
	left: 424px;
	width: 292px;
	height: 600px;
	background-color: rgba(255,0,0,0.0);
}

.col_l2 div{
	position: relative;
}

.col_l2 .ttt{
	font-size: 14px;
	color: #77BAF8;
}

.col_l2 .blk1{
	position: absolute;
	top: 7px;
	left: 57px;
	width: 100%;
	background-color: rgba(255,0,0,0.0);
}

.col_l2 .blk2{
	position: absolute;
	top: 91px;
	left: 57px;
	width: 100%;
	background-color: rgba(255,0,0,0.0);
}

.col_l2 .blk3{
	position: absolute;
	top: 173px;
	left: 57px;
	width: 100%;
	background-color: rgba(255,0,0,0.0);
}

.col_l2 .oiltt{
	position: absolute;
	top: 358px;
	left: 65px;
	font-size: 16px;
	font-weight: bold;
	background-color: rgba(255,0,0,0.0);
}

.col_l2 .blk4{
	position: absolute;
	top: 390px;
	left: 11px;
	width: 100%;
	background-color: rgba(255,0,0,0.0);
}
.col_l2 .blk4 .ttt{
	margin-top: 13px;
}









.col_top {
	pointer-events: auto;
	position: absolute;
	top: 10px;
	left: 904px;
	width: 892px;
	height: 66px;
	background-color: rgba(255,0,0,0.0);
}

.col_top div{
	position: relative;
}

.col_top .co1{
	text-align: center;
}
.col_top .co3{
	text-align: center;
}

.col_top .mid .c1{
	position: relative;
}
.col_top .mid .c2{
	position: relative;
	text-align: center;
	width: 200px;
}
.col_top .mid .c2 .arr{
	width: 100%;
	height: 18px;
	background: url(../img/flight.arr2.png?1) no-repeat top center;
}
.col_top .mid .c3{
	position: relative;
	text-align: right;
}
.col_top .mid .t1{
	position: relative;
	font-size: 16px;
	font-weight: bold;
}
.col_top .mid .t2{
	position: relative;
	font-size: 14px;
	color: #77BAF8;
}

.col_top .fltno{
	display: inline-block;
	height: 40px;
	font-size: 24px;
	font-weight: bold;
	line-height: 40px;
	margin-top: 4px;
	padding-left: 46px;

	background-repeat: no-repeat;
	background-position: 0 center;
	background-size: auto 40px;
}


.col_top .sts{
	display: inline-block;
	height: 40px;
	font-size: 18px;
	font-weight: bold;
	line-height: 40px;
	margin-top: 4px;
	padding-left: 42px;

	background-repeat: no-repeat;
	background-position: 0 center;
}

.col_top .status1{
	color: #76FF00;
	background-image: url(../img/flight.ac_green.png?1);
}
.col_top .status2{
	color: #FFFF00;
	background-image: url(../img/flight.ac_yellow.png?1);
}

.col_top .flightStatus1{
	color: #76FF00;
}
.col_top .flightStatus2{
	color: #FFFF00;
}
.col_top .flightStatus3{
	color: #666666;
}



.col_bot {
	pointer-events: auto;
	position: absolute;
	bottom: 0px;
	left: 692px;
	width: 1300px;
	height: 150px;
	background-color: rgba(255,0,0,0.0);
}

.col_bot div{
	position: relative;
}

.col_bot .chart1{
	position: absolute;
	top: 10px;
	left: 20px;
	width: 140px;
	height: 140px;
	background: url('../img/chart_bg.png') no-repeat center center;
}
.col_bot .chart1 .pointer{
	position: absolute;
	top: 0px;
	left: 0px;
}
.col_bot .lb_rate1{
	position: absolute;
	top: 17px;
	left: 170px;
}
.col_bot .lb_rate2{
	position: absolute;
	top: 73px;
	left: 170px;
}

.col_bot .mid .cabin{
	position: absolute;
	top: 0px;
	left: 62px;
	width: 550px;
	height: 120px;

	background-repeat: no-repeat;
	background-position: 0 0;
}
.col_bot .mid .cabin.CY{
	background-image: url('../../img/a3.3.plane_layout_N_CY.png');
}
.col_bot .mid .cabin.Y{
	background-image: url('../../img/a3.3.plane_layout_N_Y.png');
}
.col_bot .mid .cabin.W_CY{
	background-image: url('../../img/a3.3.plane_layout_W_CY.png');
}
.col_bot .mid .cabin.W_Y{
	background-image: url('../../img/a3.3.plane_layout_W_Y.png');
}
.col_bot .mid .cabin .lb{
	position: absolute;
	top: 60px;
	left: 30px;
	font-size: 16px;
	font-weight: bold;
}
.col_bot .mid .tt{
	position: absolute;
	top: 2px;
	left: 46px;
	font-size: 18px;
	font-weight: bold;
}

.col_bot .co3 .chart{
	position: absolute;
	top: 12px;
	left: 43px;
	width: 110px;
	height: 110px;
}
.col_bot .co3 .lb_rate1{
	position: absolute;
	top: 27px;
	left: 170px;
}
.col_bot .co3 .lb2{
	position: absolute;
	top: 83px;
	left: 170px;
}

.up_arr{
	display: inline-block;
	height: 16px;
	width: 10px;
  	background: url(../img/arr_up2.png) no-repeat center;
}
.down_arr{
	display: inline-block;
	height: 16px;
	width: 10px;
  	background: url(../img/arr_down2.png) no-repeat center;
}




.col_wea {
	pointer-events: auto;
	position: absolute;
	top: 83px;
	left: 2110px;
	width: 160px;
	height: 300px;
	background-color: rgba(255,0,0,0.0);
}

.col_wea div{
	position: relative;
}

.col_wea .lb{
	height: 28px;
}

.col_wea .blk{
	padding: 1px;
	background-color: #0F377C;
	margin-bottom: 1px;
	cursor: pointer;
}
.col_wea .blk:hover{
	background-color: #1759BB;
}
.col_wea .blk:hover .tt{
	color: #FFF;
}

.col_wea .blk .tt{
	height: 24px;
	line-height: 24px;
	color: #4DA6FF;
	padding-left: 10px;
	font-weight: bold;
}
.col_wea .blk .cont{
	background-color: #101C43;
	
	height: 0;
	overflow: hidden;

	-moz-transition: all .3s;
    -webkit-transition: all .3s;
    -o-transition: all .3s;
    -ms-transition: all .3s;
    transition: all .3s;
}
.col_wea .blk .cont .c1{
	height: 60px;
	padding: 15px 10px 5px 19px;
}
.col_wea .blk .cont .c2{
	padding: 15px 0 5px 0;
}

.col_wea .blk.selected{
	background-color: #1759BB;
}
.col_wea .blk.selected .tt{
	color: #fff;
}
.col_wea .blk.selected .cont{
	height: auto;
}
.weather_ico {
	font-size: 40px;
}





.col_r {
	pointer-events: auto;
	position: absolute;
	top: 110px;
	left: 2313px;
	width: 292px;
	height: 602px;
	background-color: rgba(255,0,0,0.0);
}

.col_r div{
	position: relative;
}

.col_r .blk1 {
	top: 0px;
}
.col_r .blk2 {
	top: 150px;
}
.col_r .blk3 {
	top: 330px;
}
.col_r .blk {
	width: 100%;
	position: absolute;
}

.col_r .blk .tt{
	font-size: 16px;
	font-weight: bold;
	margin: 6px;
	text-align: center;

}
.col_r .blk table{
	margin: 10px;
}
.col_r .blk table td{
	padding: 3px 6px 0 0;
}


.col_r .btn{
	position: absolute;
	display: inline-block;
	margin-right: 10px;
	height: 22px;
	width: 22px;
	padding: 0;
	border-radius: 11px;
	border: 1px solid #1a52a5;
	cursor: pointer;

	top: 173px;
}
.col_r .btn_prev{
	left: 229px;
	background: #012869 url(../../img/a4.2_btn_arr1.png) no-repeat center;
}
.col_r .btn_next{
	left: 259px;
	background: #012869 url(../../img/a4.2_btn_arr2.png) no-repeat center;
}
.col_r .disabled{
	opacity: 0.4;
	pointer-events: none;
}


