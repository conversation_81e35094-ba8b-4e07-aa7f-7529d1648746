body {
	font-family: "microsoft yahei";
}

.page-wrapper {
	position: absolute;
	top: 0px;
	width: 3672px;
	height: 1080px;
	overflow-x: hidden;
	overflow-y: hidden;
}

.page-bgimg {
	position: absolute;
	top: 0px;
	width: 3672px;
	height: 1080px;
	background: url(../img/sdcindexbg.png?2) no-repeat 0 0;
	opacity: .3;
	background-size: 100%;
	overflow-x: hidden;
	overflow-y: hidden;
	pointer-events: none;
}

/* ------------- col_left ----------- */
.timesline .tt {
	position: absolute;
	top: 35px;
	left: 87px;
}

.time-wrapper {
	position: absolute;
	height: 70px;
	top: 23px;
	left: 117px;
}

.timecol {
	display: inline-block;
	position: relative;
	width: 210px;
	text-align: center;
}

.pagetitle {
	position: absolute;
	width: 100%;
	top: 25px;
	left: 1650px;
	font-size: 35px;
	font-weight: bold;
	letter-spacing: 3px;
}

.pagetitle .tt {
	display: inline-block;
	height: 100%;
	padding-left: 150px;
	background: url(../img/logo.png?0) no-repeat left center;
}


/* ------------- col_left ----------- */

.col_left .bigtitle {
	position: absolute;
	top: 109px;
	left: 60px;
	margin-bottom: 32px;
}

.col_left .content {
	position: absolute;
	width: 200px;
	height: 270px;
	text-align: center;
	cursor: pointer;
}

.col_left .content .content_up {
	position: absolute;
	top: 75px;
	left: 95px;
	width: 113px;
	height: 45px;

}

.col_left .content .content_up .ffnum {
	left: 12px;
	top: 25px;
	text-align: left;
}

.col_left .content .content_down {
	position: absolute;
	width: 113px;
	height: 45px;
	top: 157px;
	left: 81px;
}

.col_left .content .content_down .ffnum {
	top: 25px;
	left: 25px;
	text-align: left;
}

.col_left .gjth {
	top: 187px;
	left: 430px;
}

.col_left .gnth {
	top: 330px;
	left: 179px;
}

.col_left .plane {
	top: 330px;
	left: 677px;
}

.col_left .hx {
	top: 620px;
	left: 180px;
}

.col_left .hx .pic_content {
	top: 100px;
}

.col_left .plane .ffnum {
	top: 130px;
	left: 40px;
}

.col_left .ztzcl {
	top: 618px;
	left: 679px;
}

.col_left .hx .pic_content {
	position: relative;
	top: 170px;
	left: -60px;
}

.col_left .hx .content_up {
	top: 85px;
	left: 83px;
}

.col_left .hx .content_up .ffnum {
	left: 25px;
}

.col_left .hx .content_down {
	padding: 5px;
}

.col_left .ztzcl .pic_content {
	position: relative;
	top: 160px;
	left: -60px;
}

.col_left .ztzcl .content_up {
	top: 80px;
	left: 93px;
}

.col_left .ztzcl .content_up .ffnum {
	margin-left: 5px;
}

.col_left .hb {
	top: 764px;
	left: 429px;
}

.col_left .hb .pic_content {
	position: relative;
	top: 160px;
	left: -85px;
}

.col_left .hb .content_up,
.col_left .hb .content_down {
	width: 150px;
	text-align: left;
	left: 83px;
}


/* moreBox */
.content_btn {
	width: 50px;
	position: absolute;
	top: 235px;
	left: 72px;
	border: 2px solid #25a4ed;
	padding: 2px;
	border-radius: 5px;
}

.col_left .moreBox {
	display: none;
	position: absolute;
	left: -66px;
	top: -16px;
	width: 335px;
	height: 290px;
	z-index: 2;
	background: url(../img/more_bg.png) no-repeat left center;

}

.moreBox .title {
	position: absolute;
	left: 115px;
	top: 16px;
}

.moreBox .moreClose {
	position: absolute;
	top: 0;
	right: 30px;
	width: 24px;
	height: 24px;
	background: url(../img/more_col.png) no-repeat 0 0;
}

.col_left .subTitle {
	height: 24px;
	line-height: 24px;
	background: url(../img/subtilte_bg.png) no-repeat center;
}


.col_left .tchsMore {
	background: url(../img/thcsMore_bg.png) no-repeat left center;
}

.col_left .hbMore,
.col_left .ztzclMore {
	background: url(../img/hbMore_bg.png) no-repeat left center;
}


/*moreBox gnthcsMore */
.col_left .gnthcsMore .moreList{
	top: 61px;
	left: 70px;
	width: 210px;
	height: 200px;
	text-align: center;
}
.col_left .gnthcsMore .moreList div{
	display: inline-block;
	float: left;
	height: 30px;
	line-height: 30px;
	width: 30%;
}


/*moreBox planMore */

.col_left .planMore .moreList {
	top: 50px;
	height: 230px;
	overflow: hidden;
}

.col_left .planMore .title {
	left: 150px;
}

.col_left .planMore .topRow {
	height: 40px;
	line-height: 40px;
}

.col_left .planMore .subCon {
	height: 40px;
	line-height: 40px;
	width: 100%;
	text-align: center;
}

.col_left .planMore .subCon div {
	margin-right: 10px;
	display: inline-block;
}

.col_left .planMore .subCon .blue1 {
	display: inline-block;
	margin-right: 5px;
}

.col_mid .hbwzLegend .midMoreInfo .leftCol,
.col_mid .hbwzLegend .midMoreInfo .rightCol {
	position: absolute;
	top: -830px;
	left: -330px;
	width: 400px;
	text-align: left;
	z-index: 2;
}


.col_mid .hbwzLegend .midMoreInfo .leftCol {
	padding-left: 50px;
	background: url(../img/mid_plan_left.png) no-repeat top left;
}

.col_mid .hbwzLegend .midMoreInfo .leftCol .leftColTop {
	margin-top: -8px;
}

.col_mid .hbwzLegend .midMoreInfo .rightCol {
	left: 650px;
	width: 250px;
	padding-left: 60px;
	background: url(../img/mid_plan_right.png) no-repeat top left;
}


.col_mid .hbwzLegend .midMoreInfo .rightCol .rightColTop {
	margin-top: -10px;
}


.col_mid .hbwzLegend .midMoreInfo .rightCol .rightColTable {
	margin-top: 20px;
	margin-left: -60px;
}

.col_mid .hbwzLegend .midMoreInfo .rightCol .rightColTable .r2 {
	margin: 10px 0;
}

.col_mid .hbwzLegend .midMoreInfo .rightCol .rightColTable .r2sp1 {
	margin-left: 30px;
}

.col_mid .hbwzLegend .midMoreInfo .rightCol .rightColTable .r2sp2 {
	margin-left: 100px;
}

.col_mid .hbwzLegend .midMoreInfo .rightCol .rightColTable .tabBody {
	margin-top: 10px;
}

.col_mid .hbwzLegend .midMoreInfo .rightCol .rightColTable .tabBody .rw {
	height: 20px;
	line-height: 20px;
}

.col_mid .hbwzLegend .midMoreInfo .rightCol .rightColTable .tabBody .rw span {
	display: inline-block;
	margin-right: 10px;
}

.col_mid .hbwzLegend .midMoreInfo .rightCol .rightColTable .tabBody .rw .c1,
.col_mid .hbwzLegend .midMoreInfo .rightCol .rightColTable .tabBody .rw .c2 {
	width: 30px;
	text-align: left;
}

.col_mid .hbwzLegend .midMoreInfo .rightCol .rightColTable .tabBody .rw .c2 {
	text-align: right;
}

.col_mid .hbwzLegend .midMoreInfo .rightCol .rightColTable .tabBody .rw .c3pro {
	display: inline-flex;
	width: 100px;
	height: 10px;
	border: 1px solid #25a4ed;
	border-radius: 5px;
}

.col_mid .hbwzLegend .midMoreInfo .rightCol .rightColTable .tabBody .rw .c3pro .bar {
	display: inline-block;
	height: 100%;
	background: #00A9FF;
	border-radius: 5px;
}


/*moreBox hbMore */
.col_left .hbMore .moreList {
	top: 65px;
	left: 45px;
	width: 240px;
	height: 160px;
	text-align: left;
}

.col_left .hbMore .title {
	left: 125px;
}

.col_left .hbMore .moreList .rw {
	width: 240px;
	height: 45px;
	line-height: 45px;
	font-size: 14px;
}

.col_left .hbMore .moreList .topRow {
	width: 240px;
	height: 30px;
	line-height: 30px;
	font-size: 12px;
	font-weight: bold;
}

.col_left .hbMore .moreList .rw div {
	display: inline-block;
}


.col_left .hbMore .moreList .rw .c1 {
	width: 45px;
	text-align: center;
	font-weight: bold;
}

.col_left .hbMore .moreList .rw .c2 {
	width: 60px;
}

.col_left .hbMore .moreList .rw .c3 {
	width: 60px;
}



/*moreBox ztzclMore */
.col_left .ztzclMore .moreList .rw .c1,
.col_left .ztzclMore .moreList .rw .c2,
.col_left .ztzclMore .moreList .rw .c3{
	width: 30%;
	text-align: center;
}

/*moreBox thcsMore */
.moreList {
	position: absolute;
	top: 80px;
	left: 37px;
	width: 259px;
	height: 130px;
	overflow: hidden;
}

.asiaBox,
.eurBox,
.oceanBox,
.americanBox {
	position: absolute;
	padding-left: 82px;
	width: 150px;
	height: 60px;
	text-align: left;
}

.eurBox {
	left: 125px;
}

.oceanBox {
	top: 80px
}

.americanBox {
	left: 125px;
	top: 80px;
}



/* ------------- col_mid ----------- */
.col_mid #mapWrapper {
	position: absolute;
	left: 1100px;
	top: 130px;
	height: 870px;
	width: 1465px;
}

.col_mid #mapWrapper .thcsLegend,
.col_mid #mapWrapper .thcsCnLegend,
.col_mid #mapWrapper .hszclBotLegend {
	position: absolute;
	left: 630px;
	bottom: 0px;
}


.col_mid #mapWrapper .thcsLegend div,
.col_mid #mapWrapper .thcsCnLegend div,
.col_mid #mapWrapper .hszclBotLegend div {
	display: inline-block;
	margin-right: 10px;
}

.col_mid #mapWrapper .thcsLegend .thcsIcon1,
.col_mid #mapWrapper .thcsCnLegend .thcsIcon1,
.col_mid #mapWrapper .hszclBotLegend .zclIcon1 {
	display: inline-block;
	width: 20px;
	height: 20px;
	background-size: 100%;
	background-image: url(../img/symbol_b.png);
}

.col_mid #mapWrapper .thcsLegend .thcsIcon2,
.col_mid #mapWrapper .thcsCnLegend .thcsIcon2,
.col_mid #mapWrapper .hszclBotLegend .zclIcon2 {
	display: inline-block;
	width: 20px;
	height: 20px;
	background-size: 100%;
	background-image: url(../img/symbol_g.png);
}

.col_mid #mapWrapper .hbwzLegend,
.col_mid #mapWrapper .botLegend {
	position: absolute;
	left: 580px;
	bottom: 0;
	color: #c5defe;
}

.col_mid #mapWrapper .thcsChange{
	position: absolute;
	width: 90px;
	height: 90px;
	left: 760px;
	top: -40px;
	background-image: url(../img/earth-small.png);
	pointer-events: auto;
	cursor: pointer;
}
.col_mid #mapWrapper .thcsChange .content{
	position: absolute;
	top: -32px;
	left: 10px;
}

.col_mid #mapWrapper .hbwzLegend .hbwzChange{
	position: absolute;
	width: 135px;
	height: 91px;
	left: 780px;
	top: -40px;
	background-image: url(../img/2Dto3D.png);
	pointer-events: auto;
	cursor: pointer;
}

.col_mid #mapWrapper .hbwzLegend .itm,
.col_mid #mapWrapper .hxLegend .botLegend .itm{
	display: inline-block;
	padding-left: 26px;
	margin-left: 20px;
	background-repeat: no-repeat;
	background-position: left center;
}

.col_mid #mapWrapper .hbwzLegend .itm1 {
	background-image: url(../img/a3.3.legend_1.png);
}

.col_mid #mapWrapper .hbwzLegend .itm2 {
	background-image: url(../img/a3.3.legend_2.png);
}


.col_mid #mapWrapper .hxLegend .itm1{
	background-image: url(../img/mid_hx_g.png);
}
.col_mid #mapWrapper .hxLegend .itm2{
	background-image: url(../img/mid_hx_y.png);
}



.col_mid #mapWrapper .hxLegend .leftColTop{
	position: absolute;
	top: 26px;
	left: 208px;
}

.col_mid #mapWrapper .hxLegend .leftColTop .topTitle{
	padding-left: 50px;
	background: url(../img/mid_hx_tl.png) no-repeat left center;
	margin-bottom: 20px;
}

.col_mid #mapWrapper .hxLegend .leftColTop .ywList{
	height: 260px;
	overflow: hidden;
}

.col_mid #mapWrapper .hxLegend .leftColTop .ywList div,
.col_mid #mapWrapper .hxLegend .rightColTop .gjhbList div{
	margin: 10px 0;
}


.col_mid #mapWrapper .hxLegend .rightColTop{
	position: absolute;
	top: 26px;
	right: 0;
}

.col_mid #mapWrapper .hxLegend .rightColTop .topTitle{
	height: 40px;
	line-height: 40px;
	padding-left: 50px;
	background: url(../img/mid_hx_tr.png) no-repeat left center;
}

.col_mid #mapWrapper .hxLegend .rightColTop .gjhbList{
	width: 100px;
	height: 800px;
	margin-left: 50px;
	overflow: hidden;
}


.col_mid #mapWrapper .hbwzLegend,
.col_mid #mapWrapper .hxLegend {
	display: none;
}


.col_mid #thcsMap,
.col_mid #thcsCnMap,
.col_mid #hbwzMap,
.col_mid #hxMap {
	height: 870px;
	width: 1465px;
}
.col_mid #hbwzMap3D {
    width:800px;
    height: 800px;
    left: 345px;
    top: 25px;
    pointer-events: auto;
}

/* 国际通航城市 */

.col_mid .midNav ul {
	list-style: none;
	position: absolute;
	padding: 0;
	width: 140px;
	left: 1110px;
	top: 140px;
	z-index: 1;
}

.col_mid .midNav ul li {
	height: 50px;
	line-height: 60px;
	margin-bottom: 10px;
	cursor: pointer;
	text-align: center;
	font-weight: bold;
	color: #00a9ff;
	background: url(../img/midlibg.png) no-repeat left center;
}

.col_mid .midNav .liselected {
	color: #FFFFFF;
	background: url(../img/midliactivebg.png) no-repeat left center;
	transition: all 0.2s ease-out;
}

/* hszclLegend */
.col_mid #mapWrapper .hszclLegend{
	display: none;
}
.col_mid #mapWrapper .hszclLegend .rightColTop {
	position: absolute;
	top: 26px;
	right: 0;
}
.col_mid #mapWrapper .hszclLegend .rightColTop .topTitle{
	height: 40px;
	line-height: 40px;
	padding-left: 50px;
	margin-bottom: 20px;
	background: url(../img/mid_zcl_tr.png) no-repeat left center;
}

.col_mid #mapWrapper .hszclLegend .colL,
.col_mid #mapWrapper .hszclLegend .colR{
/* 	display: inline-block;
	width: 100px;*/
	height: 30px;
	line-height: 30px;
	text-align: left;
}
.col_mid #mapWrapper .hszclLegend .colR{
	margin-left: 10px;
	/* width: 50px; */
}

.col_mid .hszclLegend .compmap .itm {
	position: absolute;
	width: 32px;
	height: 32px;
	background-size: 32px;
	background-repeat: no-repeat;
}

.col_mid #mapWrapper .hszclBotLegend .zclIcon1 {
	background-image: url(../img/mid_zcl_botIcon1.png);
}

.col_mid #mapWrapper .hszclBotLegend .zclIcon2 {
	background-image: url(../img/mid_zcl_botIcon2.png);
}
/* ------------- col_Right ----------- */
.col_right {
	position: absolute;
	cursor: pointer;
	top: 120px;
	left: 2670px;
	width: 850px;
	height: 618px
}

.col_right .bigtitle {
	position: relative;
	top: -13px;
	left: 15px;
	margin-bottom: 32px;
}

.col_right_left {
	position: absolute;
	left: -5px;
	width: 560px;
}

.col_right_left .head,
.col_right_left .rw {
	display: -webkit-flex;
	display: flex;
	-webkit-flex-flow: row;
	height: 55px;
	line-height: 40px;
	margin-bottom: 5px;
	text-align: center;
	background-color: #02356A;
}

.col_right_left .c {
	display: inline-block;
	height: 40px;
	line-height: 55px;
	overflow: hidden;
	border-right: 1px solid #01447E;
}

.col_right_left .center {
	text-align: center;
}

.col_right_left .c1 {
	width: 120px;
}

.col_right_left .c2 {
	width: 60px;
}

.col_right_left .c3,
.col_right_left .c4 {
	width: 70px;
}

.col_right_left .c5,
.col_right_left .c6 {
	width: 80px;
}


.col_right_left .c7 {
	width: 150px;
	text-align: center;
	border-right: 0;
}


.col_right_left .rw {
	background-color: #05204B;
}

.col_right_left .list .rw:nth-child(even) {
	background-color: #02356B;
}

.col_right_left .list .rw.selected {
	background-color: #00A9FF;
}

.col_right_right {
	position: absolute;
	right: -112px;
	width: 397px;
}

.col_right_right .head {
	width: 100%;
	height: 65px;
	line-height: 65px;
	text-align: center;
}

.col_right_right .head .compname {
	display: inline-block;
	vertical-align: middle;
	margin-left: 3px;
}

.col_right_right .scroll .plan_scroll {
	position: absolute;
	top: 82px;
	padding: 0;
	list-style-type: none;
}

.col_right_right .scroll .plan_scroll li {
	position: relative;
	list-style-type: none;
	margin-bottom: 10px;
	width: 397px;
	height: 180px;
	background: url(../img/planebg.png?1) no-repeat left center;
}

.col_right_right .scroll .plan_scroll .plane_modle {
	position: absolute;
	right: 20px;
	top: 15px;
	text-align: right;
}

.col_right_right .scroll .plan_scroll .plane_pic {
	position: relative;
	top: 20px;
	height: 73px;
	background: url(../img/737.png?0) no-repeat center;
}

.col_right_right .scroll .plan_scroll .plane_text1,
.col_right_right .scroll .plan_scroll .plane_text2 {
	position: absolute;
	top: 110px;
	left: 80px;
	text-align: center;
}

.col_right_right .scroll .plan_scroll .plane_text2 {
	left: 278px;
}

.col_right_right .scroll .plan_scroll .plane_num,
.col_right_right .scroll .plan_scroll .plane_rate {
	position: absolute;
	top: 130px;
	left: 80px;
	text-align: center;
}

.col_right_right .scroll .plan_scroll .plane_rate {
	left: 278px;
}
