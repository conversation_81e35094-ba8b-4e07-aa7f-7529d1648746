localStorage.setItem('7x2_return_url', window.location.href);
var now = new Date();
localStorage.setItem('7x2_return_time', now.getTime());

var lang = 'zh';
var base_code = getQueryString('base');
var url_scale = getQueryString('scale');
var queryScale = getQueryString('scale');
if (url_scale) {
    url_scale = '&scale=' + url_scale;
    queryScale = '?scale=' + queryScale;
} else {
    url_scale = '';
}
if (!base_code) {
    window.location.href = "base_exhibition.html?base=PEK" + url_scale;
}


var comp_code = 'HU';
var comp_id = '100100';
var companyNodeId = 9;

$(function () {
    companyDtd.done(function () {
        var hash = window.location.hash.substr(1);
        if (!hash) {
            hash = getFirstAuthCompanyCode();
        } else {
            if (!hasCompanyAuth(hash)) {
                showNoPermission();
                return;
            }
        }
        if (!hash) {
            showNoPermission();
            return;
        } else {
            comp_code = hash;
            comp_id = companyCode2CompanyId[hash];
            companyNodeId = companyCode2Node[hash];
        }




        $('#companycombo').attr('code', comp_code);
        $('#companycombo .box1').html(companyCode2Name[comp_code]);
        $(".logo_txt").addClass("logo_" + comp_code);

        var comp_code_list = ['HU', '8L', 'HX', 'PN', 'GS', 'JD', 'FU', 'UQ', 'Y8', 'GX', '9H', 'GT'];
        var BASE_CITY_LIST = {
            "PEK": '北京',
            "HAK": '海口',
            "XIY": '西安',
            "CAN": '广州',
            "DLC": '大连',
            "TYN": '太原',
            "SZX": '深圳',
            "HGH": '杭州',
            "SYX": '三亚',
            "CSX": '长沙',
            "URC": '乌鲁木齐',
            "CKG": '重庆'
        };

        const BASE_CODE_CNI_LIST = {
            "PEK": '2',
            "HAK": '1',
            "XIY": '3',
            "CAN": '21',
            "DLC": '26',
            "TYN": '4',
            "SZX": '29',
            "HGH": '62',
            "SYX": '59',
            "CSX": '63',
            "URC": '20',
            "CKG": '52',
        };

        // 区域名称
        var AREA_LIST = {
            '华北': '100001',
            '华东': '100002',
            '东北': '100003',
            '西南': '100004',
            '中南': '100005',
            '西北': '100006',
            '新疆': '100007',
        };

        var comp_cause = ['飞机故障', '运力调配', '工程机务', '航班计划', '航材保障', '航务保障', '机组保障', '飞行机组保障', '乘务组', '乘务组保障', '空警安全员', '地面保障', '货运保障', '公司原因', '其他航空公司原因'];
        var none_cause = ['公共安全', '机场', '军事活动', '空管', '离港系统', '联检', '旅客', '民航局航班时刻安排', '天气原因', '油料'];

        // 航班保障节点
        var fltnodes = ['cncPilotArrTime', 'checkInEnd', 'cncCabinSupplyEndTime', 'cncCleanEndTime', 'cncMCCReleaseTime', 'planeReady', 'cncInformBoardTime', 'cncBoardOverTime', 'cncClosePaxCabinTime', 'cncCloseCargoCabinTime', 'cncPushTime', 'cncACARSTOFF'];


        var marquee_itv;
        var marquee_vvip_itv;
        var marquee_zz_itv;

        var statusMap = {
            'ARR': '落地',
            'NDR': '落地',
            'ATD': '推出',
            'ATA': '到达',
            'CNL': '取消',
            'DEL': '延误',
            'DEP': '起飞',
            'RTR': '返航',
            'SCH': '计划'
        };


        // 中转航班信息
        var mctInfoList = {};


        // 飞机位置信息
        var planeLocationList;

        // 飞机航班信息
        var flightInfoList;

        // 机场列表
        var airportList;


        // ------------------------------------------------------------------------
        // 基地名称
        var basename = BASE_CITY_LIST[base_code];
        if (basename.length == 2) {
            $('#base_name .title').text(basename);
        } else {
            $('#base_name .title').text(basename);
        }

        $('.current_base_name').text(basename);

        var html = '';
        for (var code in BASE_CITY_LIST) {
            var name = BASE_CITY_LIST[code];
            html += '<div class="itm" data-code="' + code + '">' + name + '</div>'
        }
        $('#base_list').html(html);
        $('#base_name .title').on('click', function () {
            if ($('#base_list').is(':visible')) {
                $('#base_list').fadeOut();
            } else {
                $('#base_list').fadeIn();
            }
        })
        $('#base_list .itm').on('click', function () {
            window.location.href = 'base_exhibition.html?base=' + $(this).attr('data-code') + url_scale + "#" + comp_code;
            $('#base_list').fadeOut();
        })

        var active = false;
        var tmo;

        $('#base_name').on('mouseover', function () {
            clearTimeout(tmo);
            active = true;
        });
        $('#base_name').on('mouseout', function () {
            active = false;
            tmo = setTimeout(checkActive, 300);
        });

        function checkActive() {
            if (!active) {
                $('#base_list').hide();
            }
        }

        $('#back2comp').on('click', function () {
            window.location.href = 'index_exhibition.html' + queryScale + "#" + comp_code;
        })


        // 北京时间
        function setTime() {
            var date = new Date();
            var timestamp = date.getTime();
            var timezoneOffset = date.getTimezoneOffset();
            var utc_timestamp = timestamp + timezoneOffset * 60 * 1000;
            var utc_date = new Date();
            utc_date.setTime(utc_timestamp);

            var sydney_date = new Date();
            sydney_date.setTime(utc_timestamp + 11 * 3600 * 1000);

            var newyork_date = new Date();
            newyork_date.setTime(utc_timestamp - 5 * 3600 * 1000);

            $('#bj_time').text(formatNum(date.getHours()) + ':' + formatNum(date.getMinutes()));

        }

        function formatNum(n) {
            if (n < 10) {
                return ('0' + n);
            } else {
                return n;
            }
        }

        setInterval(setTime, 1000);
        setTime();


        function loadAll() {

            // ------------------------------------------------------------------------
            // 接口时间
            // ------------------------------------------------------------------------
            var date = new Date();
            var mm = date.getMonth() + 1;
            var dd = date.getDate();
            if (mm < 10) {
                mm = '0' + mm;
            }
            if (dd < 10) {
                dd = '0' + dd;
            }
            var today = date.getFullYear() + '-' + mm + '-' + dd;
            var stdEndUtcTime = date.getFullYear() + '-' + mm + '-' + dd + ' 15:59:59';
            var yesterday_ts = date.getTime() - 86400000;
            date.setTime(yesterday_ts);
            mm = date.getMonth() + 1;
            dd = date.getDate();
            if (mm < 10) {
                mm = '0' + mm;
            }
            if (dd < 10) {
                dd = '0' + dd;
            }
            var stdStartUtcTime = date.getFullYear() + '-' + mm + '-' + dd + ' 16:00:00';


            // ------------------------------------------------------------------------
            // 欢迎词
            // ------------------------------------------------------------------------

            var param = {
                'mode': 'query'
            }

            $.ajax({
                type: 'post',
                url: "/bi/web/7x2_welcome",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {

                    if (response.title != undefined) {
                        var msg = response.title[0].txt;
                        //
                        if (msg.length > 30) {
                            $('#welcome_msg').html('<marquee direction="left" scrollamount="10">' + msg + '</marquee>');
                        } else {
                            $('#welcome_msg').text(msg);
                        }
                    }

                },
                error: function (jqXHR, txtStatus, errorThrown) {
                    console.log('----error');
                }
            });


            // ------------------------------------------------------------------------
            // 正常率颜色配置
            // ------------------------------------------------------------------------

            var normal_rate_colors = {};
            var param = {
                'mode': 'query'
            }

            $.ajax({
                type: 'post',
                url: "/bi/web/7x2_normal_rate_color",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    normal_rate_colors = response.ratecolor[0];

                },
                error: function () {
                }
            });


            // ------------------------------------------------------------------------
            // 各种航班统计信息。。。。
            // ------------------------------------------------------------------------

            var fltStaticDep = undefined; // 离港
            var fltStaticArr = undefined; // 到港
            var url = "/bi/spring/focStaticApi/flightAmountStaticV2?depstns=" + base_code;
            var param = {
                "stdStartUtcTime": stdStartUtcTime,
                "stdEndUtcTime": stdEndUtcTime,
                "companyCodes": comp_code,
                "depstns": base_code,
                "arrstns": ""
            }

            $.ajax({
                type: 'post',
                url: url,
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {

                    fltStaticDep = response.data;
                    flightAmountStatic();
                },
                error: function () {
                }
            });


            var param = {
                "stdStartUtcTime": stdStartUtcTime,
                "stdEndUtcTime": stdEndUtcTime,
                "companyCodes": comp_code,
                "depstns": "",
                "arrstns": base_code
            }

            $.ajax({
                type: 'post',
                url: "/bi/spring/focStaticApi/flightAmountStaticV2?arrstns=" + base_code,
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {

                    fltStaticArr = response.data;
                    flightAmountStatic();

                },
                error: function () {
                }
            });


            function flightAmountStatic() {

                if (!fltStaticDep || !fltStaticArr) {
                    return;
                }

                var stsDep = fltStaticDep;
                var stsArr = fltStaticArr;

                var sch_total = Number(stsDep.pftc) + Number(stsArr.pftc); //计划航班总数
                var sch_normal = Number(stsDep.pfrtc) + Number(stsArr.pfrtc); //计划航班中正常航班总数

                var dftc = Number(stsDep.dftc) + Number(stsArr.dftc); //备降航班总数
                var bftc = Number(stsDep.bftc) + Number(stsArr.bftc); //返航航班总数
                var qftc = Number(stsDep.qftc) + Number(stsArr.qftc); //取消航班总数

                $('#flt_return_back').text(dftc + bftc);
                $('#flt_cancel').text(qftc);

                var pfdtc = Number(stsDep.pfdtc) + Number(stsArr.pfdtc); //计划航班中延误航班总数

                var pfdtc1 = Number(stsDep.pfdtc1) + Number(stsArr.pfdtc1); //计划航班中延误1小时内的航班总数
                $('#flt_delay_1').text(pfdtc1);

                var pfdtc12 = Number(stsDep.pfdtc12) + Number(stsArr.pfdtc12); //计划航班中延误1~2小时的航班总数
                $('#flt_delay_12').text(pfdtc12);

                var pfdtc24 = Number(stsDep.pfdtc24) + Number(stsArr.pfdtc24); //计划航班中延误2-4小时的航班总数
                $('#flt_delay_24').text(pfdtc24);

                var pfdtc4 = Number(stsDep.pfdtc4) + Number(stsArr.pfdtc4); //计划航班中延误>4小时的航班总数
                $('#flt_delay_4').text(pfdtc4);

                // 国际／国内航班数量
                var pftci = Number(stsDep.pftci) + Number(stsArr.pftci); //国际计划航班总数

                // 国际航班延误数量
                var pfdtci = Number(stsDep.pfdtci) + Number(stsArr.pfdtci); //计划航班中延误国际航班总数

                //

 


                // 当日航班量

                // 进港
                var pftc = Number(stsArr.pftc); //计划航班总数
                var cftc = Number(stsArr.cftc); //已执行航班总数
                $('#flt_sch_arr').text(pftc);
                $('#flt_exc_arr').text(cftc);
                var w = $('#flt_bar_arr').width() * (cftc / pftc);
                $('#flt_bar_arr .insidebar').css('width', w + 'px');
                $('#flt_bar_arr .dot').css('left', w + 'px');

                // 出港
                var pftc = Number(stsDep.pftc); //计划航班总数
                var cftc = Number(stsDep.cftc); //已执行航班总数
                $('#flt_sch_dep').text(pftc);
                $('#flt_exc_dep').text(cftc);
                var w = $('#flt_bar_dep').width() * (cftc / pftc);
                $('#flt_bar_dep .insidebar').css('width', w + 'px');
                $('#flt_bar_dep .dot').css('left', w + 'px');


                // 国内航班
                $('#flt_sch_dep_l').text(stsDep.pftcl||0); //国内计划航班总数
                $('#flt_exc_dep_l').text(stsDep.cftcl||0); //国内已执行航班总数
                // 国际航班
                $('#flt_sch_dep_i').text(stsDep.pftci||0); //国际计划航班总数
                $('#flt_exc_dep_i').text(stsDep.cftci||0); //国际已执行航班总数
            }
            function getArpStsNew() {
                // 获取机场的进港航班／正常率
                var url = `/bi/spring/sys-company-base-kpi-value/getStnKpi.json?companyCode=${comp_code}&stn=${base_code}`;
                $.ajax({
                    type: 'get',
                    url: url,
                    contentType: 'application/json',
                    dataType: 'json',
                    async: true,
                    data: JSON.stringify({}),
                    success: function (res) {
                      
                        var data = res.data;

                        //到港
                        // var n_rate_arr = Math.round(stsArr.cfPercent) / 100;
                        var n_rate_arr = Math.round(data.arrNormalRate) / 100;
                        drawNormalRateChart('cvs_normal_rate1', n_rate_arr);
                        $('.val_normal_rate1').text(Math.round(n_rate_arr * 100));

                        //离港
                        var n_rate_dep = Math.round(data.depNormalRate) / 100;
                        drawNormalRateChart('cvs_normal_rate3', n_rate_dep);
                        $('.val_normal_rate3').text(Math.round(n_rate_dep * 100));
                        
                        //国际
                        var n_rate_dep = Math.round(data.intExecuteRate) / 100;
                        drawNormalRateChart('cvs_normal_rate4', n_rate_dep);
                        $('.val_normal_rate4').text(Math.round(n_rate_dep * 100));


                        // 整体正常率
                        var n_rate_all = Math.round(data.normalRate) / 100; 
                        $('#base_normal_rate').text(Math.round(n_rate_all * 100));
                        $('.val_normal_rate2').text(Math.round(n_rate_all * 100));
                        drawNormalRateChart('cvs_normal_rate2', n_rate_all);

                    },
                    error: function () {
                    }
                });
            }
            getArpStsNew()


            // ------------------------------------------------------------------------
            // 正常率x4
            // ------------------------------------------------------------------------
            function drawNormalRateChart(canvas_id, rate) {

                var canvas = document.getElementById(canvas_id);
                var context = canvas.getContext('2d');
                var x = canvas.width / 2;
                var y = canvas.height / 2;

                // draw back
                var radius = 44;
                var linewidth = 20;

                var startAngle = Math.PI - Math.PI / 3.6;
                var endAngle = startAngle + Math.PI + Math.PI / 3.6 * 2;
                var counterClockwise = false;

                context.beginPath();
                context.arc(x, y, radius, startAngle, endAngle, counterClockwise);
                context.lineWidth = linewidth;
                context.strokeStyle = '#00438B';
                context.stroke();

                // draw overlay
                var startAngle2 = startAngle;
                var endAngle2 = startAngle + (endAngle - startAngle) * rate;
                var counterClockwise = false;

                context.beginPath();
                context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
                context.lineWidth = linewidth;

                // linear gradient
                if (rate < 0.5) {
                    var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
                } else if (rate < 0.8) {
                    var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
                } else {
                    var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, canvas.width / 2);
                }
                color.addColorStop(0, '#122A61');

                if (rate < 0.7) {
                    color.addColorStop(1, '#A1263E');
                } else if (rate < 0.8) {
                    color.addColorStop(1, '#c29700');
                } else {
                    color.addColorStop(1, '#0093d1');
                }

                context.strokeStyle = color;
                context.stroke();

                // draw head
                var startAngle2 = startAngle + (endAngle - startAngle) * rate - (endAngle - startAngle) * 0.01;
                var endAngle2 = startAngle + (endAngle - startAngle) * rate;
                var counterClockwise = false;

                context.beginPath();
                context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
                context.lineWidth = linewidth;
                if (rate < 0.7) {
                    context.strokeStyle = '#ff0000';
                } else if (rate < 0.8) {
                    context.strokeStyle = '#ffc600';
                } else {
                    context.strokeStyle = '#18c6ff';
                }
                context.stroke();
            }


            // ------------------------------------------------------------------------
            // 获取航班信息
            // ------------------------------------------------------------------------

            var fltIntList = [];
            var flt2hourInList = [];
            var flt2hourOutList = [];
            var acNoInList;
            var acNoOutList;

            // 开始结束时间
            var date = new Date();
            var mm = date.getMonth() + 1;
            var dd = date.getDate();
            if (mm < 10) {
                mm = '0' + mm;
            }
            if (dd < 10) {
                dd = '0' + dd;
            }
            var stdStart = date.getFullYear() + '-' + mm + '-' + dd + ' 00:00:00';
            var stdEnd = date.getFullYear() + '-' + mm + '-' + dd + ' 23:59:59';

            var param = {
                "stdStart": stdStart,
                "stdEnd": stdEnd,
                "acOwner": comp_code,
                "statusList": '',
            }
            $.ajax({
                type: 'post',
                url: "/bi/web/getStandardFocFlightInfo",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    var list = response.data;
                    flightInfoList = {};

                    for (var i = list.length - 1; i >= 0; i--) {
                        var obj = list[i];

                        // 基地 进港。出港 航班
                        if (obj.arrStn == base_code || obj.depStn == base_code) {

                            flightInfoList[obj.flightNo] = obj;

                            // 国际航班
                            if (obj.fltType == 'I') {
                                fltIntList.push(obj);
                            }

                            // 近2小时进出港航班
                            var etdChn = obj.etdChn; //预计起飞时间（北京时间）
                            var atdChn = obj.atdChn; //实际起飞时间（北京时间）
                            var etaChn = obj.etaChn; //预计到达时间（北京时间）
                            var ataChn = obj.ataChn; //实际到达时间（北京时间）

                            var d_time = parserDate(etdChn);
                            var d2_time = parserDate(atdChn);
                            var a_time = parserDate(etaChn);
                            var a2_time = parserDate(ataChn);

                            var now_time = new Date();
                            // 2小时内到达的飞机
                            var ost = a_time.getTime() - now_time.getTime();
                            // var ost2 = a2_time.getTime() - now_time.getTime();
                            if (obj.arrStn == base_code && (ost >= 0 && ost <= 120 * 60 * 1000)) {
                                flt2hourInList.push(obj);
                            }
                            // 2小时内出发的飞机
                            var ost = d_time.getTime() - now_time.getTime();
                            // var ost2 = d2_time.getTime() - now_time.getTime();
                            if (obj.depStn == base_code && (ost >= 0 && ost <= 120 * 60 * 1000)) {
                                flt2hourOutList.push(obj);
                            }
                        }


                    }

                    setIntFltStatus();

                    set2hourInFltStatus();
                    set2hourOutFltStatus();

                    setAirlines();

                    getInPsrStat();
                    getOutPsrStat();

                    getPlaneLocationMq();

                },
                error: function () {
                }
            });


            // ------------------------------------------------------------------------
            // 国际航班状态
            // ------------------------------------------------------------------------
            function setIntFltStatus() {
                if (airportList == undefined) {
                    setTimeout(setIntFltStatus, 10);
                    return;
                }

                var html = '';
                var cnt = 0;
                for (var i = fltIntList.length - 1; i >= 0; i--) {
                    var flt = fltIntList[i];
                    var fltno = flt.flightNo;
                    var cls = cnt % 2 == 0 ? 'abg' : '';
                    var clr = (flt.status != 'DEL' && flt.status != 'CNL' && flt.status != 'RTR') ? 'green2' : 'orange';

                    var arp = airportList[flt.arrStn];
                    var chn_name = flt.arrStn;
                    if (arp && arp.chn_name) {
                        chn_name = arp.chn_name;
                    }

                    html += '<tr class="' + cls + '">';
                    html += '<td class="cl1">' + flt.flightNo + '</td>';
                    html += '<td>' + chn_name + '</td>';
                    html += '<td class="cl3">' + statusMap[flt.status] + ' <span class="' + clr + '">●</span></td>';
                    html += '</tr>';

                    cnt++;
                }

                $('#int_flt_table tbody').html(html);

                //大于N条自动滚动
                if (cnt > 6) {
                    var speed = 60;
                    var sec = document.getElementById("int_flt_table");
                    var sec2 = document.getElementById("int_flt_table2");
                    var sec1 = document.getElementById("int_flt_table1");

                    //sec2.innerHTML=sec1.innerHTML;
                    function Marquee() {
                        if (sec2.offsetTop - sec.scrollTop <= 0)
                            sec.scrollTop -= sec1.offsetHeight
                        else {
                            sec.scrollTop++
                        }
                    }

                    clearInterval(marquee_itv);
                    marquee_itv = setInterval(Marquee, speed);
                    sec.onmouseover = function () {
                        clearInterval(marquee_itv)
                    }
                    sec.onmouseout = function () {
                        marquee_itv = setInterval(Marquee, speed)
                    }
                }

            }


            function getShortTime(fulltime) {
                var str = fulltime.replace(/:/g, "");
                var arr = str.split(' ');
                return arr[1].substr(0, 4);
            }


            // ------------------------------------------------------------------------
            // 近2小时进港航班
            // ------------------------------------------------------------------------
            function set2hourInFltStatus() {
                if (airportList == undefined) {
                    setTimeout(set2hourInFltStatus, 10);
                    return;
                }

                flt2hourInList.sort(function (a, b) {
                    return getShortTime(a.etaChn) - getShortTime(b.etaChn)
                });

                var html = '';
                var cnt = 0;
                var len = flt2hourInList.length;
                len = Math.min(9, len);
                var fltNoList = [];
                for (var i = 0; i < len; i++) {
                    var flt = flt2hourInList[i];
                    fltNoList.push(flt.flightNo);
                }
                var fltLegs = getFltmLegsByPageV2(flt2hourInList[0].datop, fltNoList,flt2hourInList[0].arrStn,null);
                for (var i = 0; i < len; i++) {
                    var flt = flt2hourInList[i];

                    var fltno = flt.flightNo;
                    var cls = cnt % 2 == 0 ? 'abg' : '';

                    var actype = flt.acType;
                    if (actype.indexOf('73') > -1) {
                        actype = '737';
                    } else if (actype.indexOf('76') > -1) {
                        actype = '767';
                    } else if (actype.indexOf('78') > -1) {
                        actype = '787';
                    } else if (actype.indexOf('33') > -1) {
                        actype = '330';
                    } else if (actype.indexOf('32') > -1) {
                        actype = '320';
                    }

                    var staChn = getShortTime(flt.staChn); //计划到达时间（北京时间）
                    var etaChn = getShortTime(flt.etaChn); //预计到达时间（北京时间）
                    var tDwnChn = getShortTime(flt.tDwnChn); //实际落地时间（北京时间）
                    var ataChn = getShortTime(flt.ataChn); //实际到达时间（北京时间）

                    var resArr = fltLegs.filter(r => r.cncDEPSTN == flt.depStn && r.cncARRSTN == flt.arrStn && r.cnvcFlightNo == flt.flightNo)
                    var arrParkPlace = '-'
                    if (resArr.length > 0) {
                        arrParkPlace = resArr[0].cnvcInGATE == undefined ? '-': resArr[0].cnvcInGATE; //到达停机位
                    }

                    if (flt.status == 'NDR' || flt.status == 'ATA') {
                        // 已经到达
                    } else {
                        tDwnChn = '-';
                        ataChn = '-';
                    }

                    var bgc1 = '';
                    if ((flt.status == 'NDR' || flt.status == 'ATA') && Number(flt.dur1) == 0) {
                        bgc1 = 'green_tag'
                    } else if ((flt.status == 'NDR' || flt.status == 'ATA') && Number(flt.dur1) > 0) {
                        bgc1 = 'yellow_tag'
                    }
                    var bgc2 = bgc1;

                    html += '<tr class="' + cls + '" height="34">';
                    html += '<td>' + flt.acLongNo + '</td>';
                    html += '<td class="white">' + fltno + '<br>' + flt.depCity + '</td>';
                    html += '<td>' + actype + '</td>';
                    html += '<td class="white">' + staChn + '</td>';
                    html += '<td class="white">' + etaChn + '</td>';
                    html += '<td class="white"><span class="' + bgc1 + '">' + tDwnChn + '</span></td>';
                    html += '<td class="white"><span class="' + bgc2 + '">' + ataChn + '</span></td>';
                    html += '<td>' + arrParkPlace + '</td>';
                    html += '</tr>';

                    cnt++;

                }

                $('#flt2hourInTable tbody').html(html);
            }


            // ------------------------------------------------------------------------
            // 近2小时出港航班
            // ------------------------------------------------------------------------
            function set2hourOutFltStatus() {

                if (airportList == undefined) {
                    setTimeout(set2hourOutFltStatus, 10);
                    return;
                }

                flt2hourOutList.sort(function (a, b) {
                    return getShortTime(a.etdChn) - getShortTime(b.etdChn)
                });

                var date = new Date();
                var hour = date.getHours();
                var min = date.getMinutes();
                if (hour < 10) {
                    hour = '0' + hour;
                }
                if (min < 10) {
                    min = '0' + min;
                }
                var time_now = hour + '' + min;

                var html = '';
                var cnt = 0;

                var len = flt2hourOutList.length;
                len = Math.min(9, len);
                var fltNoList = [];
                for (var i = 0; i < len; i++) {
                    var flt = flt2hourOutList[i];
                    fltNoList.push(flt.flightNo);
                }
                var fltLegs = getFltmLegsByPageV2(flt2hourOutList[0].datop, fltNoList,null,flt2hourOutList[0].depStn);
                for (var i = 0; i < len; i++) {
                    var flt = flt2hourOutList[i];

                    var fltno = flt.flightNo;


                    var cls = cnt % 2 == 0 ? 'abg' : '';

                    var atdChnHtml = '';

                    var etdChn = getShortTime(flt.etdChn); //预计起飞时间（北京时间）
                    var atdChn = getShortTime(flt.atdChn); //实际起飞时间（北京时间）

                    //console.log(fltno, time_now, atdChn, flt);

                    if (Number(time_now) < Number(atdChn)) {
                        atdChnHtml = '-'
                    } else {
                        if (flt.status == 'DEL' || Number(flt.dur1) > 0) {
                            atdChnHtml = '<span class="yellow_tag">' + atdChn + '</span>'
                        } else {
                            atdChnHtml = '<span class="green_tag">' + atdChn + '</span>'
                        }
                    }

                    var resArr = fltLegs.filter(r => r.cncDEPSTN == flt.depStn && r.cncARRSTN == flt.arrStn && r.cnvcFlightNo == flt.flightNo)
                    var depParkPlace = '-'
                    if (resArr.length > 0) {
                        depParkPlace = resArr[0].cnvcOutGate == undefined ? '-': resArr[0].cnvcOutGate; //出发停机位
                    }


                    var reason = flt.delay1Name;

                    /*
              1 天气原因（云朵）
              2 军事活动（军人）
              3 机组保障（扳手）
              4 飞机故障（螺旋桨）
              5 运力调配（节点）
              6 地面保障（雨伞）
              7 航班计划（飞机和时间）
              8 乘务保障（乘务员）
              9 时刻安排（日历）
              10 空管（铃铛）
              11 流量控制（来回箭头）
              12 公共安全（盾牌）
              13 机场（塔台）
              14 其他（点点）
                    */
                    var delayReason = '';
                    if (reason && reason != '') {
                        var delayId = 14;
                        if (reason.indexOf('天气') > -1) {
                            delayId = 1;
                        } else if (reason.indexOf('军事') > -1) {
                            delayId = 2;
                        } else if (reason.indexOf('机组') > -1) {
                            delayId = 3;
                        } else if (reason.indexOf('故障') > -1) {
                            delayId = 4;
                        } else if (reason.indexOf('运力') > -1) {
                            delayId = 5;
                        } else if (reason.indexOf('地面') > -1) {
                            delayId = 6;
                        } else if (reason.indexOf('计划') > -1) {
                            delayId = 7;
                        } else if (reason.indexOf('乘务') > -1) {
                            delayId = 8;
                        } else if (reason.indexOf('时刻') > -1) {
                            delayId = 9;
                        } else if (reason.indexOf('空管') > -1) {
                            delayId = 10;
                        } else if (reason.indexOf('流量') > -1) {
                            delayId = 11;
                        } else if (reason.indexOf('安全') > -1) {
                            delayId = 12;
                        } else if (reason.indexOf('机场') > -1) {
                            delayId = 13;
                        }
                        delayReason = '<img src="img/ico_delay_' + delayId + '.png">';
                    }

                    html += '<tr class="' + cls + '" height="34" id="dep_' + fltno + '">';
                    html += '<td class="">' + fltno + '<br>' + flt.arrCity + '</td>';
                    html += '<td class="blue1"><span class="">' + etdChn + '</span></td>';
                    html += '<td class="white cncClosePaxCabinTime"></td>';
                    html += '<td class="white cncCloseCargoCabinTime"><span class=""></span></td>';
                    html += '<td class="node"></td>';
                    html += '<td class="white"><span class="cncPushTime"></span></td>';
                    html += '<td class="white cncACARSTOFF">' + atdChnHtml + '</td>';
                    html += '<td class="blue1"><span class="">' + depParkPlace + '</span></td>';
                    html += '<td class="">' + delayReason + '</td>';
                    html += '</tr>';

                    getFltmLegs(fltno);

                    cnt++;

                }

                $('#flt2hourOutTable tbody').html(html);
            }

            // 获取航班进出港停机位
            function getFltmLegsByPageV2(datop, fltNoList, arrIataId, depIataId) {
                var res;
                var param = {
                    "flightDateUTCFrom": datop,
                    "flightDateUTCTo": datop,
                    "fltNoList": fltNoList,
                    "arrIataId": arrIataId,
                    "depIataId": depIataId,
                }
                $.ajax({
                    type: 'post',
                    url: "/bi/spring/flight/getFltmLegsByPageV2",
                    contentType: 'application/json',
                    dataType: 'json',
                    async:false,
                    data: JSON.stringify(param),
                    success: function (response) {
                        res = response.data;
                    }
                })
                return res;
            }



            function getFltmLegs(fltno) {
                var param = {
                    "flightNo": fltno
                }
                $.ajax({
                    type: 'post',
                    url: "/bi/web/getFltmLegsByPage",
                    contentType: 'application/json',
                    dataType: 'json',
                    async: true,
                    data: JSON.stringify(param),
                    success: function (response) {

                        /*
                          cncPilotArrTime,//机组到达|飞行
                          cncStewardArrTime,//机组到达|乘务
                          //航班截载 无法获得 checkInEnd
                          cncCabinSupplyEndTime,//客舱供应|结束，机供品配备
                          cncCleanEndTime,//客舱清洁
                      1   cncMCCReleaseTime,//机务放行
                          //飞机准备好 无法获得 planeReady
                      3   cncInformBoardTime,//通知登机
                      4   cncClosePaxCabinTime,//客舱关闭
                      4   cncCloseCargoCabinTime,//货舱关闭
                          cncPushTime,//飞机推出
                          cncACARSTOFF,//飞机起飞
        
                          1.机务放行 用扳手图标
                          2.飞机准备完毕  用餐食图标
                          3.通知上客  用喇叭图标
                          4.客舱/货舱关闭 用门的图标
        
                          */
                        var cncMCCReleaseTime = '-';
                        var cncInformBoardTime = '-';

                        var cncClosePaxCabinTime = '-';
                        var cncCloseCargoCabinTime = '-';
                        var cncPushTime = '-';
                        var cncACARSTOFF = '-';

                        if (!response || !response.getFltmLegsByPage || !response.getFltmLegsByPage.cncClosePaxCabinTime || response.getFltmLegsByPage.cncClosePaxCabinTime == "") {
                            var date = new Date();
                            var mm = date.getMonth() + 1;
                            var dd = date.getDate();
                            if (mm < 10) {
                                mm = '0' + mm;
                            }
                            if (dd < 10) {
                                dd = '0' + dd;
                            }
                            param = {
                                "fltDate": date.getFullYear() + '-' + mm + '-' + dd,
                                "FltId": fltno,
                                "depStn": base_code
                            }
                            // param = {
                            //     "fltDate" : "2019-09-16",
                            //     "FltId": "HU7820",
                            //     "depStn": "HHA"
                            // }
                            $.ajax({
                                type: 'post',
                                url: "/bi/web/acsacarsgetacarsoooiinfo",
                                contentType: 'application/json',
                                dataType: 'json',
                                async: true,
                                data: JSON.stringify(param),
                                success: function (response) {
                                    console.log("acsacarsgetacarsoooiinfo", response.data);
                                    if (response.data != undefined) {
                                        let acs = response.data[0];
                                        if (acs && acs.CloseTime != "") {
                                            let utcDate = new Date(acs.CloseTime);
                                            let date = new Date(utcDate.setTime(utcDate.getTime() + 8 * 3600 * 1000));
                                            cncClosePaxCabinTime = getShortTime(formatDate(date));
                                        }
                                        if (acs && acs.Cls1 != "") {
                                            let utcDate = new Date(acs.Cls1);
                                            let date = new Date(utcDate.setTime(utcDate.getTime() + 8 * 3600 * 1000));
                                            cncCloseCargoCabinTime = getShortTime(formatDate(date));
                                        }
                                        if (acs && acs.OutTime != "") {
                                            let utcDate = new Date(acs.OutTime);
                                            let date = new Date(utcDate.setTime(utcDate.getTime() + 8 * 3600 * 1000));
                                            cncPushTime = getShortTime(formatDate(date));
                                        }
                                        if (acs && acs.OffTime != "") {
                                            let utcDate = new Date(acs.OffTime);
                                            let date = new Date(utcDate.setTime(utcDate.getTime() + 8 * 3600 * 1000));
                                            cncACARSTOFF = getShortTime(formatDate(date));
                                        }
                                        $('#dep_' + fltno + ' .cncClosePaxCabinTime').text(cncClosePaxCabinTime);
                                        $('#dep_' + fltno + ' .cncCloseCargoCabinTime').text(cncCloseCargoCabinTime);
                                        $('#dep_' + fltno + ' .cncPushTime').text(cncPushTime);
                                        $('#dep_' + fltno + ' .cncACARSTOFF').text(cncACARSTOFF);

                                        if (cncClosePaxCabinTime != '-' || cncCloseCargoCabinTime != '-') {
                                            $('#dep_' + fltno + ' .node').html('<img src="img/ico_node_4.png">');
                                        } else if (cncInformBoardTime != '-') {
                                            $('#dep_' + fltno + ' .node').html('<img src="img/ico_node_3.png">');
                                        } else if (cncMCCReleaseTime != '-') {
                                            $('#dep_' + fltno + ' .node').html('<img src="img/ico_node_1.png">');
                                        }
                                    }

                                },
                                error: function () {
                                }
                            })
                        } else {
                            if (response && response.getFltmLegsByPage && response.getFltmLegsByPage.cncClosePaxCabinTime && response.getFltmLegsByPage.cncClosePaxCabinTime != "") {
                                cncClosePaxCabinTime = getShortTime(response.getFltmLegsByPage.cncClosePaxCabinTime);
                            }
                            if (response && response.getFltmLegsByPage && response.getFltmLegsByPage.cncMCCReleaseTime && response.getFltmLegsByPage.cncMCCReleaseTime != "") {
                                cncMCCReleaseTime = getShortTime(response.getFltmLegsByPage.cncMCCReleaseTime);
                            }
                            if (response && response.getFltmLegsByPage && response.getFltmLegsByPage.cncCloseCargoCabinTime && response.getFltmLegsByPage.cncCloseCargoCabinTime != "") {
                                cncCloseCargoCabinTime = getShortTime(response.getFltmLegsByPage.cncCloseCargoCabinTime);
                            }
                            if (response && response.getFltmLegsByPage && response.getFltmLegsByPage.cncPushTime && response.getFltmLegsByPage.cncPushTime != "") {
                                cncPushTime = getShortTime(response.getFltmLegsByPage.cncPushTime);
                            }
                            $('#dep_' + fltno + ' .cncClosePaxCabinTime').text(cncClosePaxCabinTime);
                            $('#dep_' + fltno + ' .cncCloseCargoCabinTime').text(cncCloseCargoCabinTime);
                            $('#dep_' + fltno + ' .cncPushTime').text(cncPushTime);

                            if (cncClosePaxCabinTime != '-' || cncCloseCargoCabinTime != '-') {
                                $('#dep_' + fltno + ' .node').html('<img src="img/ico_node_4.png">');
                            } else if (cncInformBoardTime != '-') {
                                $('#dep_' + fltno + ' .node').html('<img src="img/ico_node_3.png">');
                            } else if (cncMCCReleaseTime != '-') {
                                $('#dep_' + fltno + ' .node').html('<img src="img/ico_node_1.png">');
                            }

                        }

                    },
                    error: function () {
                    }
                });
            }

            function formatDate(date) {
                var y = date.getFullYear();
                var m = date.getMonth() + 1;
                m = m < 10 ? ('0' + m) : m;
                var d = date.getDate();
                d = d < 10 ? ('0' + d) : d;
                var h = date.getHours();
                h = h < 10 ? ('0' + h) : h;
                var minute = date.getMinutes();
                minute = minute < 10 ? ('0' + minute) : minute;
                var second = date.getSeconds();
                second = second < 10 ? ('0' + second) : second;
                return y + '-' + m + '-' + d + ' ' + h + ':' + minute + ':' + second;
            }

            // ------------------------------------------------------------------------
            // 延误原因 公司／非公司
            // ------------------------------------------------------------------------

            var url = `/bi/spring/sys-company-base-delay-reason/getDelayReasonByCompanyCodeStn.json?companyCode=${comp_code}&stn=${base_code}`;
            $.ajax({
              type: 'get',
              url: url,
              contentType: 'application/json',
              dataType: 'json',
              async: true,
              data: null,
              success: function (response) {
      
      
                var data = response.data;
                var companyReasonList = data.companyReasonList;
                var externalReasonList = data.externalReasonList;
                var all = data.all;
                // 公司
                var html = '';
                var len = companyReasonList.length;
                for (var i = 0; i < len; i++) {
                  var d = companyReasonList[i];
                  var perstr = d.rate;
                  var barlen = d.rate;
                  var name = (lang == 'en') ? d.reasonEname : d.reasonName;
                  if (perstr > 0) {
                    html += '<div class="baritmrow"><span class="blue2">' + name + '</span> <span class="bar greenbar" style="width: ' + barlen + 'px; "></span> <span class="ffnum">' + perstr + '%</span> </div>';
                  }
                }
                $('#holder_delay_cause_comp').html(html);
      
      
      
                html = '';
                var len = externalReasonList.length;
                for (var i = 0; i < len; i++) {
                  var d = externalReasonList[i];
                  var perstr = d.rate;
                  var barlen = d.rate;
                  var name =(lang == 'en') ? d.reasonEname : d.reasonName;
                  if (perstr > 0) {
                    html += '<div class="baritmrow"><span class="blue2">' + name + '</span> <span class="bar bluebar" style="width: ' + barlen + 'px; "></span> <span class="ffnum">' + perstr + '%</span> </div>';
                  }
                }
                $('#holder_delay_cause_none').html(html);
                // percent
                $('#per_delay_cause_comp').text(all.companyResaonRate?all.companyResaonRate:'');
                $('#per_delay_cause_none').text(all.externalResaonRate?all.externalResaonRate:'');
      
      
                // chart
      
                var rate_delay_cause_comp = all.companyResaonRate;
                var rate = rate_delay_cause_comp;
      
                var canvas = document.getElementById('cvs_delay_cause');
                var context = canvas.getContext('2d');
                var x = canvas.width / 2;
                var y = canvas.height / 2;
      
                // draw blue circle
                var radius = 54;
                context.beginPath();
                context.arc(x, y, radius, 0, 2 * Math.PI, false);
                context.lineWidth = 7;
                context.strokeStyle = '#02B0F9';
                context.stroke();
      
                // draw green arc
                var startAngle = Math.PI - (Math.PI * 2 * rate / 2);
                var endAngle = Math.PI + (Math.PI * 2 * rate / 2);
                context.beginPath();
                context.arc(x, y, radius, startAngle, endAngle, false);
                context.lineWidth = 7;
                context.strokeStyle = '#A3D900';
                context.stroke();
      
                // draw lines
                var numslice = 12;
                for (var i = 0; i < numslice; i++) {
                  context.beginPath();
                  var startAngle = i * (Math.PI * 2 / numslice);
                  var endAngle = startAngle + Math.PI * 0.01;
                  context.arc(x, y, radius, startAngle, endAngle, false);
                  context.lineWidth = 8;
                  context.strokeStyle = '#041946';
                  context.stroke();
                }
              },
              error: function () { }
            });



            // ------------------------------------------------------------------------
            // 机型可用运力
            // ------------------------------------------------------------------------
            var param = {
                'mode': 'query',
            }

            $.ajax({
                type: 'post',
                url: "/bi/web/7x2_available_base_plane",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {

                    if (response.data != undefined) {
                        var len = response.data.length;
                        var html = '';
                        var list = response.data;
                        for (var i = 0; i < len; i++) {
                            var obj = list[i];
                            if (obj.ARP == base_code) {
                                html += '<div class="yunli_block">';
                                html += '<span class="t1 fs11">' + obj.AC + '</span> <span class="t2 ffnum fs14" style="width: 70px; ">' + obj.NUM + '<span class="sub blue2 fs9">架</span></span>';
                                html += '</div>';
                            }

                        }
                        $('#available_base_plane').html(html);
                    }

                },
                error: function (jqXHR, txtStatus, errorThrown) {

                }
            });


            // ------------------------------------------------------------------------
            // 5率
            // ------------------------------------------------------------------------

            function setFiveRate(canvas_id, label_id, rate) {

                var canvas = document.getElementById(canvas_id);
                var context = canvas.getContext('2d');
                var x = canvas.width / 2;
                var y = canvas.height / 2;

                var radius = 28;

                // draw blue circle
                context.beginPath();
                context.arc(x, y, radius, 0, 2 * Math.PI, false);
                context.lineWidth = 7;
                context.strokeStyle = '#0074C5';
                context.stroke();

                // draw green arc
                var startAngle = -Math.PI / 2;
                var endAngle = startAngle + (Math.PI * 2 * rate / 2);
                context.beginPath();
                context.arc(x, y, radius, startAngle, endAngle, false);
                context.lineWidth = 7;
                if (rate < 0.7) {
                    context.strokeStyle = '#ff0000';
                } else if (rate < 0.8) {
                    context.strokeStyle = '#ffc600';
                } else {
                    context.strokeStyle = '#99FFFF';
                }
                context.stroke();

                $('#' + label_id).text(Math.round(rate * 100));
            }


            var five_rate_map = {
                '机组到位': '1',
                '机务放行': '2',
                '通知上客': '3',
                '客舱关闭': '4',
                '货舱关闭': '5',
            }

            var param = {
                'mode': 'query',
            }

            $.ajax({
                type: 'post',
                url: "/bi/web/7x2_rates",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {

                    if (response.data != undefined) {
                        var len = response.data.length;
                        var html = '';
                        var list = response.data;
                        for (var i = 0; i < len; i++) {
                            var obj = list[i];
                            if (obj.ARP == base_code) {
                                var id = five_rate_map[obj.TYPE];
                                if (id != undefined && obj.NUM > 0) {
                                    setFiveRate('cvs_five_rate' + id, 'val_five_rate' + id, Number(obj.NUM) / 100);
                                }
                            }
                        }
                    }

                },
                error: function (jqXHR, txtStatus, errorThrown) {

                }
            });


            // ------------------------------------------------------------------------
            // 各个基地过夜飞机架数
            // ------------------------------------------------------------------------

            function getArpOvernightPlane() {

                var kpis = ['AC_NO', 'AC_ARR_NO'];
                var param = {
                    "SOLR_CODE": "FAC_COMP_ARP_ACTYPE_FLIGHT_KPI",
                    "COMP_CODE": comp_code,
                    "ACTYPE": "ALL",
                    "AIRPORT_CODE": base_code,
                    "KPI_CODE": kpis.join(','),
                    "VALUE_TYPE": "kpi_value_d",
                    "DATE_TYPE": "D",
                    "DATE_ID": today,
                    "LIMIT": 0,
                    "OPTIMIZE": 1
                }

                $.ajax({

                    type: 'post',
                    url: "/bi/query/getackpi",
                    contentType: 'application/json',
                    dataType: 'json',
                    async: true,
                    data: JSON.stringify(param),
                    success: function (response) {

                        var data = response.data[comp_code];

                        setArpPlane(data);

                    },
                    error: function () {
                    }
                });


                function setArpPlane(data) {

                    if (actypeId2Code == undefined) {
                        setTimeout(setArpPlane, 10, data);
                        return;
                    }


                    // 计算总量
                    var AC_NO_TOTAL = 0; // 所有机场预计过夜飞机架数
                    var AC_ARR_NO_TOTAL = 0; // 所有机场过夜飞机架数

                    var ac_no_lst = data['AC_NO']['D'][base_code];
                    var ac_arr_lst = data['AC_ARR_NO']['D'][base_code];

                    var ac_no = {};
                    var ac_arr_no = {};

                    for (var ac in ac_no_lst) {
                        var accode = actypeId2Code[ac];
                        var dlst = ac_no_lst[ac];
                        var acnum = Number(dlst);
                        if (acnum >= 0) {
                            AC_NO_TOTAL += acnum;
                            ac_no[accode] = acnum;
                        }
                    }

                    for (var ac in ac_arr_lst) {
                        var accode = actypeId2Code[ac];
                        var dlst = ac_arr_lst[ac];
                        var acnum = Number(dlst);
                        if (acnum >= 0) {
                            AC_ARR_NO_TOTAL += acnum;
                            ac_arr_no[accode] = acnum;
                        }
                    }

                    $('#base_over_night_plane_num').text(AC_NO_TOTAL);

                    //console.log('actypeId2Code', actypeId2Code);
                    //console.log('ac_no', ac_no);
                    //console.log('ac_arr_no', ac_arr_no);

                    for (var accode in ac_no) {
                        var acnum1 = ac_no[accode];
                        var acnum2 = ac_arr_no[accode];
                        if (acnum1 >= 0 && acnum2 >= 0) {
                            $('#base_over_night_plane_num_ac' + accode).html(acnum2 + '/' + acnum1);
                            $('#base_over_night_plane_bar_ac' + accode + ' .pertxt').html(Math.round(acnum2 / acnum1 * 100));
                            var w = acnum2 / acnum1 * 192;
                            $('#base_over_night_plane_bar_ac' + accode + ' .perbar').css('width', w + 'px');
                            $('#base_over_night_plane_bar_ac' + accode + ' .plane').css('left', (w - 40) + 'px');

                        }

                    }


                }

            }

            getArpOvernightPlane();


            // ------------------------------------------------------------------------
            // 十航月度排名
            // ------------------------------------------------------------------------

            var logo_map = {
                '春秋': 'rank_logo_CQ.png',
                '东航': 'rank_logo_DH.png',
                '东方': 'rank_logo_DH.png',
                '国航': 'rank_logo_GH.png',
                '国际': 'rank_logo_GH.png',
                '海航': 'rank_logo_HH.png',
                '海南': 'rank_logo_HN.png',
                '南航': 'rank_logo_NH.png',
                '控股': 'rank_logo_HN.png',
                '南方': 'rank_logo_NH.png',
                '川航': 'rank_logo_SC.png',
                '四川': 'rank_logo_SC.png',
                '山航': 'rank_logo_SD.png',
                '山东': 'rank_logo_SD.png',
                '上航': 'rank_logo_SH.png',
                '上海': 'rank_logo_SH.png',
                '深航': 'rank_logo_SZ.png',
                '深圳': 'rank_logo_SZ.png',
                '厦航': 'rank_logo_XM.png',
                '厦门': 'rank_logo_XM.png',
                '天航': 'rank_logo_TH.png',
                '天津': 'rank_logo_TH.png',

                '祥鹏': 'logo_8L.png',
                '香港': 'logo_HX.png',
                '西部': 'logo_PN.png',
                '天津': 'logo_GS.png',
                '首都': 'logo_JD.png',
                '福州': 'logo_FU.png',
                '乌航': 'logo_UQ.png',
                '金鹏': 'logo_Y8.png',
                '北部湾': 'logo_GX.png',
                '长安': 'logo_9H.png',
                '桂林': 'logo_GT.png',
            };

            var param = {
                'mode': 'query',
                'type': 'normal_rate',
            }

            $.ajax({
                type: 'post',
                url: "/bi/web/7x2_comp_rank",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {

                    if (response.comp != undefined) {
                        var len = response.comp.length;
                        var html = '';
                        var list = response.comp;
                        list.sort(function (a, b) {
                            return a.rank - b.rank
                        });
                        var rank = 1;
                        for (var i = 0; i < len; i++) {
                            var obj = list[i];
                            if (obj.name == '海航' || obj.name == '海南' || obj.name == '海南航空' || obj.name == '控股') {
                                rank = obj.rank;
                            }
                            //
                            var img = logo_map[obj.name];
                            if (img != undefined) {
                                html += '<span style="display: inline-block; width:20px; margin-bottom:3px;">' + obj.rank + '</span><img style="max-width:21px;" src=img/' + img + '><br>';
                            } else {
                                html += '<span style="display: inline-block; width:20px; margin-bottom:3px;">' + obj.rank + '</span>' + obj.name + '<br>';
                            }

                        }
                        $('#comp_rank_month').text(rank);
                        $('#comp_rank_list_month').html(html);

                    }

                },
                error: function (jqXHR, txtStatus, errorThrown) {

                }
            });


            // ------------------------------------------------------------------------
            // 正常率排名走势
            // ------------------------------------------------------------------------
            function setNormalRateTrendMonth(datlist) {
                var chart = echarts.init(document.getElementById('chart_normal_trend_month'));
                console.log("bbbb", datlist)
                var colorrange = ['#0F1B44', '#0091D8'];
                var d = new Date();
                var numofday = new Date(d.getFullYear(), d.getMonth() + 1, 0).getDate();

                var xAxisData = function () {
                    var arr = [];
                    for (var i = 0; i < numofday; i++) {
                        arr.push(i + 1);
                    }
                    return arr;
                };
                var chartData = function () {
                    var arr = [];
                    for (var i = 0; i < numofday; i++) {
                        var ddd = datlist[i];
                        var val = ddd.value;
                        val = isNaN(val) ? 0 : val;
                        var dat = {
                            value: val
                        }
                        arr.push(dat);
                    }
                    console.log("aaaa", arr)
                    return arr;
                };


                option = {
                    tooltip: {
                        show: true
                    },
                    legend: {
                        show: false,
                        data: ['']
                    },
                    grid: {
                        top: 5,
                        left: 26,
                        width: 470,
                        height: 50
                    },
                    xAxis: [{
                        type: 'category',
                        data: xAxisData(),
                        nameTextStyle: {
                            color: '#516BBA'
                        },
                        axisLine: {
                            lineStyle: {
                                color: 'rgba(255,255,255,0)' // 轴线颜色
                            }
                        },
                        axisLabel: {
                            textStyle: {
                                color: '#516BBA', // 轴标签颜色大小
                                fontSize: 11,
                            }
                        }
                    }],
                    yAxis: [{
                        type: 'value',
                        name: '',
                        min: 0,
                        max: 10,
                        interval: 2,
                        axisLabel: {
                            formatter: '{value}%'
                        },
                        nameTextStyle: {
                            color: '#516BBA',
                            fontSize: 11
                        },
                        axisLabel: {
                            textStyle: {
                                color: '#516BBA',
                                fontSize: 11,
                            }
                        },
                        axisLine: {
                            lineStyle: {
                                color: 'rgba(255,255,255,0)'
                            }
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: ['#243474'] // 分割线颜色
                            }
                        }
                    }],
                    series: [{
                        name: '',
                        type: 'line',
                        smooth: true,
                        data: chartData(),
                        itemStyle: {
                            normal: {
                                color: '#99E5FF',
                                opacity: 0 //不显示折线拐点
                            }
                        },
                        areaStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(
                                    0, 0, 0, 1, [{
                                        offset: 0,
                                        color: colorrange[1]
                                    }, {
                                        offset: 1,
                                        color: colorrange[0]
                                    }]),
                                opacity: 0.8
                            }
                        },
                    }]
                };

                chart.setOption(option);
            }

            var param = {
                'mode': "query"
            }

            $.ajax({
                type: 'post',
                url: "/bi/web/west_normal_rates_month", // 西部不再使用，改为股份使用，存储正常率排名走势
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {

                    setNormalRateTrendMonth(response.data);

                },
                error: function (jqXHR, txtStatus, errorThrown) {
                    console.log('----error');
                }
            });


            // ------------------------------------------------------------------------
            // 当日计划旅客量
            // ------------------------------------------------------------------------

            function setTrvRate(canvas_id, color, rate) {

                var canvas = document.getElementById(canvas_id);
                var context = canvas.getContext('2d');
                var x = canvas.width / 2;
                var y = canvas.height / 2;

                var radius = 32;
                var lineWidth = 5;

                // draw blue circle
                context.beginPath();
                context.arc(x, y, radius, 0, 2 * Math.PI, false);
                context.lineWidth = lineWidth;
                context.strokeStyle = '#024394';
                context.stroke();

                // draw color arc
                var startAngle = -Math.PI / 2;
                var endAngle = startAngle + (Math.PI * 2 * rate / 2);
                context.beginPath();
                context.arc(x, y, radius, startAngle, endAngle, false);
                context.lineWidth = lineWidth;
                context.strokeStyle = color;
                context.stroke();

                // draw lines
                var numslice = 12;
                for (var i = 0; i < numslice; i++) {
                    context.beginPath();
                    var startAngle = i * (Math.PI * 2 / numslice);
                    var endAngle = startAngle + Math.PI * 0.01;
                    context.arc(x, y, radius, startAngle, endAngle, false);
                    context.lineWidth = 8;
                    context.strokeStyle = '#041946';
                    context.stroke();
                }

            }


            // ------------------------------------------------------------------------
            // VVIP 航班
            // ------------------------------------------------------------------------
            var param = {
                'mode': 'query'
            }

            $.ajax({
                type: 'post',
                url: "/bi/web/7x2_vvip",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {

                    setVVIPFltStatus(response.flt);

                },
                error: function () {
                }
            });


            function setVVIPFltStatus(fltlst) {
                if (airportList == undefined || flightInfoList == undefined) {
                    setTimeout(setVVIPFltStatus, 10, fltlst);
                    return;
                }


                var html = '';
                var cnt = 0;
                for (var i = fltlst.length - 1; i >= 0; i--) {
                    var fltno = fltlst[i].flt_no;
                    var flt = flightInfoList[fltno];

                    if (flt != undefined) {
                        var clr = (flt.status != 'DEL' && flt.status != 'CNL' && flt.status != 'RTR') ? 'green2' : 'orange';
                        html += '<tr>';
                        html += '<td style="width:62px;">' + flt.flightNo + '</td>';
                        html += '<td>' + flt.depCity + '-' + flt.arrCity + '</td>';
                        html += '<td style="width:62px; text-align:right;">' + statusMap[flt.status] + ' <span class="' + clr + '">●</span></td>';
                        html += '</tr>';

                        cnt++;
                    }

                }


                $('#vvip_flt_table tbody').html(html);

                //大于N条自动滚动
                if (cnt > 4) {
                    var speed = 60;
                    var sec = document.getElementById("vvip_flt_table");
                    var sec2 = document.getElementById("vvip_flt_table2");
                    var sec1 = document.getElementById("vvip_flt_table1");

                    //sec2.innerHTML=sec1.innerHTML;
                    function Marquee() {
                        if (sec2.offsetTop - sec.scrollTop <= 0)
                            sec.scrollTop -= sec1.offsetHeight
                        else {
                            sec.scrollTop++
                        }
                    }

                    clearInterval(marquee_vvip_itv);
                    marquee_vvip_itv = setInterval(Marquee, speed);
                    sec.onmouseover = function () {
                        clearInterval(marquee_vvip_itv)
                    }
                    sec.onmouseout = function () {
                        marquee_vvip_itv = setInterval(Marquee, speed)
                    }

                    $('#vvip_flt_table2').show();
                } else {
                    $('#vvip_flt_table2').hide();
                }

            }


            // ------------------------------------------------------------------------
            // 跑道 天气情况
            // ------------------------------------------------------------------------
            var date = new Date();
            var ts = date.getTime() - 3600 * 8 * 1000;
            date.setTime(ts);
            var mm = date.getMonth() + 1;
            var dd = date.getDate();
            var hour = date.getHours();
            var min = date.getMinutes();
            var sec = date.getSeconds();
            if (mm < 10) {
                mm = '0' + mm;
            }
            if (dd < 10) {
                dd = '0' + dd;
            }
            if (hour < 10) {
                hour = '0' + hour;
            }
            if (min < 10) {
                min = '0' + min;
            }
            if (sec < 10) {
                sec = '0' + sec;
            }
            var awosTimeEnd = date.getFullYear() + '-' + mm + '-' + dd + ' ' + hour + ':' + min + ':' + sec;
            var ts = date.getTime() - 60 * 10 * 1000; //最新10分钟
            date.setTime(ts);
            mm = date.getMonth() + 1;
            dd = date.getDate();
            var hour = date.getHours();
            var min = date.getMinutes();
            var sec = date.getSeconds();
            if (mm < 10) {
                mm = '0' + mm;
            }
            if (dd < 10) {
                dd = '0' + dd;
            }
            if (hour < 10) {
                hour = '0' + hour;
            }
            if (min < 10) {
                min = '0' + min;
            }
            if (sec < 10) {
                sec = '0' + sec;
            }
            var awosTimeBegin = date.getFullYear() + '-' + mm + '-' + dd + ' ' + hour + ':' + min + ':' + sec;

            var param = {
                'airport': base_code,
                'awosTimeBegin': awosTimeBegin, // UTC时间 yyyy-MM-dd HH:mm:ss
                'awosTimeEnd': awosTimeEnd, // UTC时间 yyyy-MM-dd HH:mm:ss
            }

            $.ajax({
                type: 'post',
                url: "/bi/web/runway_weather",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    if (response.data && response.data.length) {
                        var list = response.data;
                        var len = list.length;
                        var runway_added = {};
                        var html = '';
                        var cnt = 0;
                        for (var i = 0; i < len; i++) {
                            var dat = list[i];
                            var runwayname = dat.runwayname;
                            if (runway_added[runwayname] == undefined) {
                                runway_added[runwayname] = 1;
                                var rvrmid = dat.rvrmid;
                                var tenwindspeed = dat.tenwindspeed;
                                var tenwindway = dat.tenwindway;
                                var temperature = dat.temperature;
                                var cloudheight = dat.cloudheight;

                                cloudheight = isNaN(cloudheight) ? '-' : cloudheight + 'm';

                                html += '<tr>';
                                html += '<td class="name first">' + runwayname + '</td>';
                                html += '<td>' + rvrmid + 'm</td>';
                                html += '<td>' + tenwindspeed + 'm/s ' + tenwindway + '°</td>';
                                html += '<td>' + temperature + '°</td>';
                                html += '<td>' + cloudheight + '</td>';
                                html += '</tr>';

                                cnt++;
                                if (cnt == 6) {
                                    break;
                                }
                            }

                        }

                        $('.table_runway tbody').html(html);

                    }
                },
                error: function (jqXHR, txtStatus, errorThrown) {

                }
            });


            // ------------------------------------------------------------------------
            // 天气情况
            // ------------------------------------------------------------------------

            var weather_map = {
                '晴': 'icon-e600_sunny',
                '沙': 'icon-e617_dust1',
                '雹': 'icon-e620_hail',
                '雾': 'icon-e615_fog',
                '烟': 'icon-e615_fog',
                '阴': 'icon-e604_gloomy',
                '雷': 'icon-e606_rain2',
                '暴': 'icon-e606_rain2',
                '风': 'icon-e612_wind',
                '霾': 'icon-e613_haze',
                '云': 'icon-e602_cloudy',
                '雨': 'icon-e607_rain3',
                '雪': 'icon-e610_snow3',
            };

            var weatherCache = {};

            function getWeather(arpcode, callback) {

                if (weatherCache[arpcode] != undefined) {
                    callback(weatherCache[arpcode]);
                    return;
                }

                var param = {
                    'airport': arpcode
                }

                $.ajax({
                    type: 'post',
                    url: "/bi/web/7x2_arp_weather",
                    contentType: 'application/json',
                    dataType: 'json',
                    async: true,
                    data: JSON.stringify(param),
                    success: function (response) {

                        if (Number(response.errorcode) == 0) {


                            /*
                            airport
                            airportCode
                            cloudInfo 云况
                            metUtcTime
                            rvr 跑道目视距离
                            temperature
                            visibility 能见度
                            weatherInfo 天气现象
                            weatherInfoTxt 翻译后的天气
                            windFs 风速
        
                            10个基地的标准（除大连外）
                            红色范围
                            “能见度 小于等于800；跑道视程小于等于700   天气现象（大雾、雷暴等） 云况高度（60-90米） 风速15米/秒”
                            黄色范围
                            “能见度 小于等于1600米；跑道视程小于等于1400米   天气现象（大雾、雷暴等） 云况高度小于等于150米  风速大于等于10米/秒”
        
                            大连的标准
                            红色范围
                            “能见度 小于等于1200；跑道视程小于等于1200  天气现象（大雾、雷暴、沙尘暴） 云况高度小于等于90米  风速大于等于15米/秒”
                            黄色范围
                            “能见度  小于等于2000米；跑道视程小于等于1800米  天气现象（大雾、雷暴等） 云况高度小于等于150米  风速大于等于10米/秒”
        
                            注：FG代表大雾
                              TS或TSRA或+RA 代表雷暴
                               SS或DS沙尘暴
        
                            */
                            var info = {};
                            info.code = response.airport;
                            info.visibility = isNaN(response.visibility) || response.visibility == 0 ? 9999 : Number(response.visibility); //能见度
                            info.rvr = isNaN(response.rvr) || response.rvr == 0 ? 9999 : Number(response.rvr); //跑道目视距离
                            info.cloudInfo = isNaN(response.cloudInfo) || response.cloudInfo == 0 ? 9999 : Number(response.cloudInfo); //云况
                            info.windFs = isNaN(response.windFs) ? 0 : Number(response.windFs); //风速
                            info.windFx = isNaN(response.windFx) ? 0 : Number(response.windFx); //风向
                            info.temperature = isNaN(response.temperature) ? 0 : Number(response.temperature);
                            info.weatherInfo = response.weatherInfo; //天气现象
                            var weatherInfoTxt = response.weatherInfoTxt || '';
                            info.weatherInfoTxt = weatherInfoTxt.replace(/<[^>]+>/g, "");

                            info.weather_css = 'icon-e600_sunny';

                            for (var wtxt in weather_map) {
                                if (info.weatherInfoTxt.indexOf(wtxt) > -1) {
                                    info.weather_css = weather_map[wtxt];
                                }
                            }

                            weatherCache[arpcode] = info;
                            callback(info);


                        } else {
                            console.log('7x2_arp_weather Error');
                        }


                    },
                    error: function (jqXHR, txtStatus, errorThrown) {

                    }
                });
            }

            function setBaseWeather(info) {
                // 设置天气状况icon
                $('#base_weather_ico').attr('class', info.weather_css);
                $('.base_weather .windFs').text(info.windFs + 'm/s');
                $('.base_weather .windFx').text(info.windFx + '°');
                $('.base_weather .visibility').text(info.visibility + 'm');
                $('.base_weather .temperature').text(info.temperature + '°');
            }

            getWeather(base_code, setBaseWeather);


            // ------------------------------------------------------------------------
            // 雷达图
            // ------------------------------------------------------------------------
            var radarPicList;

            function getRadarImage(arpcode) {
                var param = {
                    "airport": arpcode,
                }
                $.ajax({
                    type: 'post',
                    url: "/bi/web/7x2_arp_weather_radar",
                    contentType: 'application/json',
                    dataType: 'json',
                    async: true,
                    data: JSON.stringify(param),
                    success: function (response) {

                        var list = response.data;
                        var cndtradartime = '1970-01-01 00:00:00';
                        var cnvcradarfile = '';
                        if (list) {
                            for (var i = list.length - 1; i >= 0; i--) {
                                var d = list[i];

                                var d1 = parserDate(d.cndtradartime);
                                var d2 = parserDate(cndtradartime);

                                if (d1.getTime() > d2.getTime()) {
                                    cndtradartime = d.cndtradartime;
                                    cnvcradarfile = d.cnvcradarfile;
                                }
                            }

                            if (cnvcradarfile && cnvcradarfile.length > 0) {
                                $('.weather_atlas').css('background-image', 'url(/bi/web/getRadarpic?filename=' + cnvcradarfile + ')');
                            }

                        } else {
                            console.log('雷达接口无数据返回');
                        }


                    },
                    error: function () {
                    }
                });
            }

            getRadarImage(base_code);


            // ------------------------------------------------------------------------
            // 备降机场
            // ------------------------------------------------------------------------
            var param = {
                'mode': 'query',
            }

            $.ajax({
                type: 'post',
                url: "/bi/web/7x2_backup_arp",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {

                    if (response.data != undefined) {

                        function setBackupArp(list) {
                            if (airportList == undefined) {
                                setTimeout(setBackupArp, 10, list);
                                return;
                            }

                            var backuparps = [];
                            var len = list.length;
                            for (var i = 0; i < len; i++) {
                                var obj = list[i];
                                if (obj.COMP == comp_code && obj.ARP == base_code) {
                                    backuparps.push(obj.BACKUP);
                                }
                            }


                            var html = '';
                            if (backuparps.length > 0) {

                                var len = backuparps.length;
                                for (var i = 0; i < len; i++) {
                                    var ddd = backuparps[i];
                                    var arr = ddd.split(' ');
                                    var arpcode = arr[0];
                                    var p_w = arr[1]; // 宽体机停机位
                                    var p_n = arr[2]; // 窄体机停机位
                                    p_w = isNaN(p_w) ? 0 : p_w;
                                    p_n = isNaN(p_n) ? 0 : p_n;
                                    if (airportList[arpcode]) {
                                        var arp = airportList[arpcode];
                                        if (i == 0) {
                                            html += '<span class="itm selected" data-code="' + arpcode + '" data-pw="' + p_w + '" data-pn="' + p_n + '">' + arp.city_name + '</span>';
                                            $('.parking_w').text(p_w);
                                            $('.parking_n').text(p_n);
                                            getWeather(arpcode, setBackupArpWeather);
                                        } else {
                                            html += '<span class="itm blue1" data-code="' + arpcode + '" data-pw="' + p_w + '" data-pn="' + p_n + '">' + arp.city_name + '</span>';
                                        }
                                    }
                                }

                            }

                            $('.backup_arp_list').html(html);

                            $('.backup_arp_list .itm').on('click', function (e) {
                                $('.backup_arp_list .itm').addClass('blue1');
                                $('.backup_arp_list .itm').removeClass('selected');


                                $(this).removeClass('blue1');
                                $(this).addClass('selected');

                                var arpcode = $(this).attr('data-code');

                                $('.parking_w').text($(this).attr('data-pw'));
                                $('.parking_n').text($(this).attr('data-pn'));

                                getWeather(arpcode, setBackupArpWeather);

                            });

                        }

                        setBackupArp(response.data);


                    }

                },
                error: function (jqXHR, txtStatus, errorThrown) {

                }
            });

            function setBackupArpWeather(info) {
                // 设置天气状况icon
                $('#backup_arp_weather_ico').attr('class', info.weather_css);
                $('.backup_arp_weather .windFs').text(info.windFs + 'm/s');
                $('.backup_arp_weather .windFx').text(info.windFx + '°');
                $('.backup_arp_weather .visibility').text(info.visibility + 'm');
                $('.backup_arp_weather .temperature').text(info.temperature + '°');
            }


            // ------------------------------------------------------------------------
            // 值班人员
            // ------------------------------------------------------------------------
            var date = new Date();
            var mm = date.getMonth() + 1;
            var dd = date.getDate();
            if (mm < 10) {
                mm = '0' + mm;
            }
            if (dd < 10) {
                dd = '0' + dd;
            }
            var endDate = date.getFullYear() + '-' + mm + '-' + dd; //+' 23:59:59';
            var yesterday_ts = date.getTime() - 86400000;
            date.setTime(yesterday_ts);
            mm = date.getMonth() + 1;
            dd = date.getDate();
            if (mm < 10) {
                mm = '0' + mm;
            }
            if (dd < 10) {
                dd = '0' + dd;
            }
            var startDate = date.getFullYear() + '-' + mm + '-' + dd; //+' 00:00:00';

            var param = {
                'cniTypeId': 1, //
                'cnvcClassContent': '1', //首席
                'cnvcCompanyId': BASE_CODE_CNI_LIST[base_code],
                'cnvcStartDateStart': startDate,
                'cnvcStartDateEnd': endDate,
            }

            $.ajax({
                type: 'post',
                url: "/bi/web/getGetAPDutyInfosByPage",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    if (response.data && response.data.length > 0) {
                        var nm = response.data[0].cnvcDutyname;
                        $('#duty_name2').html(nm.split('、')[0] + "&nbsp;&nbsp;" + response.data[0].cnvcMobile);
                    }

                },
                error: function () {
                }
            });

            // ------------------------------------------------------------------------
            // 地球 航线
            // ------------------------------------------------------------------------
            function setAirlines() {

                if (airportList == undefined) {
                    setTimeout(setAirlines, 10);
                    return;
                }

                var seriesData = [];

                for (var fltno in flightInfoList) {
                    flt = flightInfoList[fltno];

                    var arp1 = airportList[flt.depStn];
                    var arp2 = airportList[flt.arrStn];

                    if (arp1 && arp2 && arp1.longitude && arp1.latitude && arp2.longitude && arp2.latitude) {
                        var color;

                        var statusMap = {
                            'ARR': '落地',
                            'NDR': '落地',
                            'ATD': '推出',
                            'ATA': '到达',
                            'CNL': '取消',
                            'DEL': '延误',
                            'DEP': '起飞',
                            'RTR': '返航',
                            'SCH': '计划'
                        };

                        if (flt.status == 'DEL' || (flt.delay1 != '' && flt.dur1 > 0)) {
                            color = '#fff663';
                        } else if (flt.status == 'ARR' || flt.status == 'NDR' || flt.status == 'ATD' || flt.status == 'ATA' || flt.status == 'DEP') {
                            color = '#0cff00';
                        } else if (flt.status == 'CNL' || flt.status == 'RTR') {
                            color = '#FF0000';
                        } else { //SCH
                            color = '#00c6ff';
                        }

                        seriesData.push({
                            fltno: fltno,
                            arrStn: flt.arrStn,
                            depStn: flt.depStn,
                            coords: [
                                [arp1.longitude, arp1.latitude],
                                [arp2.longitude, arp2.latitude]
                            ],
                            value: fltno,
                            lineStyle: {
                                width: 1,
                                color: color,
                                opacity: 1
                            },
                        });
                    }
                }


                // 航线的 tooltip
                var tooltip = {
                    trigger: 'item',
                    show: true,
                    formatter: function (params, ticket, callback) {

                        console.log('params', params);

                        var data = params.data;
                        var arp1 = airportList[data.arrStn];
                        var arp2 = airportList[data.depStn];

                        var fltno = data.fltno;
                        var flt = flightInfoList[fltno];

                        var city_name1 = arp1.city_name;
                        var arp_name1 = arp1.chn_name;

                        var city_name2 = arp2.city_name;
                        var arp_name2 = arp2.chn_name;

                        var name1 = arp_name1.indexOf(city_name1) > -1 ? arp_name1 : (city_name1 + arp_name1)
                        var name2 = arp_name2.indexOf(city_name2) > -1 ? arp_name2 : (city_name2 + arp_name2)

                        var html = '';

                        //航班号、起飞城市-到达城市、航班状态（实际/预计/计划起飞时间-实际/预计/计划到达时间）、机号（机型）、客座率（布局）、实时位置、实施油量
                        html += '航班号: ' + fltno + '<br>';
                        html += city_name1 + ' - ' + city_name2 + '<br>';
                        if (flt.status == 'DEP' || flt.status == 'ARR' || flt.status == 'NDR' || flt.status == 'ATA') {
                            // 起飞,落地,到达
                            // 实际起飞时间 atdChn
                            html += '实际起飞时间: ' + trimTime(flt.atdChn) + '<br>';
                        } else {
                            // 预计出发
                            html += '预计出发时间: ' + trimTime(flt.etdChn) + '<br>';
                        }

                        if (flt.status == 'ATA') {
                            // 到达
                            // 实际起飞时间 atdChn
                            html += '实际到达时间: ' + trimTime(flt.ataChn) + '<br>';
                        } else {
                            // 预计到达
                            html += '预计到达时间: ' + trimTime(flt.etaChn) + '<br>';
                        }

                        if (flt.delay1 != '' && flt.dur1 > 0) {
                            html += '延误原因: ' + flt.delay1Name + '<br>';
                            html += '延误时间: ' + (Number(flt.dur1) + Number(flt.dur2) + Number(flt.dur3) + Number(flt.dur4)) + '分钟<br>';
                        }

                        html += '机号: ' + data.acno + '(' + flt.acType + ')' + '<br>';
                        html += '实时位置: ' + Math.round(data.value[0] * 10000) / 10000 + ', ' + Math.round(data.value[1] * 10000) / 10000 + '<br>';

                        return html;
                    },

                    backgroundColor: '#021e55',
                };


                var series = [];
                series.push({
                    type: 'lines3D',
                    coordinateSystem: 'globe',

                    //blendMode: 'lighter',

                    effect: {
                        show: true,
                        period: 6,
                        trailLength: 0.3,
                        trailColor: '#fff',
                        trailWidth: 1
                    },

                    silent: false,

                    tooltip: tooltip,

                    data: seriesData
                });

                var option = {
                    series: series
                }

                chart_earch.setOption(option);

            }


            // ------------------------------------------------------------------------
            // 当日计划旅客
            // ------------------------------------------------------------------------

            var acNo2ckiNumIn = {};
            var acNo2ckiNumOut = {};

            var psrFltList = [];

            // 进港
            function getInPsrStat() {
                var param = {
                    //"flightNo": 'HU7902',
                    //"companyCode": comp_code,
                    //"depIataId": base_code,
                    "arrIataId": base_code,
                }
                $.ajax({
                    type: 'post',
                    url: "/bi/web/findPsrStat",
                    contentType: 'application/json',
                    dataType: 'json',
                    async: true,
                    data: JSON.stringify(param),
                    success: function (response) {

                        var bookNum = 0;
                        var ckiNum = 0;

                        acNoInList = [];

                        if (response && response.data) {

                            var fltlst = response.data;
                            psrFltList = psrFltList.concat(fltlst);

                            var len = fltlst.length;
                            for (var i = 0; i < len; i++) {
                                var flt = fltlst[i];
                                if (flt.flightNo.indexOf(comp_code) > -1) {
                                    var fltinfo = flightInfoList[flt.flightNo];

                                    if (!isNaN(flt.bookNum)) {
                                        bookNum += Number(flt.bookNum); // 订票人数
                                    }
                                    if (!isNaN(flt.ckiNum)) {
                                        ckiNum += Number(flt.ckiNum); // 值机人数
                                        if (fltinfo) {
                                            acNo2ckiNumIn[fltinfo.acLongNo] = Number(flt.ckiNum);
                                        }
                                    }

                                    if (fltinfo) {
                                        acNoInList.push(fltinfo.acLongNo);
                                    }
                                }

                            }

                            $('#bookNum_in').text(bookNum);
                            $('#ckiNum_in').text(ckiNum);

                            setTrvRate('cvs_trv1', '#00B384', ckiNum / bookNum);
                            setTrvRateIn();


                        }

                    },
                    error: function () {
                    }
                });
            }


            function setTrvRateIn() {
                // 飞机布局
                if (ac_aircraft_list == undefined || acNoInList == undefined) {
                    setTimeout(setTrvRateIn, 10, ckiNum);
                    return;
                }

                var seats_total = 0;
                var ckiNum = 0;
                var len = ac_aircraft_list.length;
                for (var i = 0; i < len; i++) {
                    var dd = ac_aircraft_list[i];
                    var acNo = dd.data.longNo;
                    var cabin = dd.data.cabin;
                    var seats = getCabinSeats(cabin);
                    if (acNoInList.indexOf(acNo) >= 0 && seats > 0 && acNo2ckiNumIn[acNo] >= 0) {
                        seats_total += Number(seats);
                        ckiNum += acNo2ckiNumIn[acNo];
                    }
                }
                var rate = ckiNum / seats_total;
                if (rate > 0) {
                    setTrvRate('cvs_trv2', '#86D1FF', rate);
                } else {
                    setTrvRate('cvs_trv2', '#86D1FF', 0);
                }

                $('#cki_rate_in').text(Math.round(rate * 1000) / 10);

            }

            // 出港
            function getOutPsrStat() {
                var param = {
                    //"flightNo": 'HU7902',
                    //"companyCode": comp_code,
                    "depIataId": base_code,
                    //"arrIataId": base_code,
                }
                $.ajax({
                    type: 'post',
                    url: "/bi/web/findPsrStat",
                    contentType: 'application/json',
                    dataType: 'json',
                    async: true,
                    data: JSON.stringify(param),
                    success: function (response) {

                        var bookNum = 0;
                        var ckiNum = 0;

                        acNoOutList = [];

                        if (response && response.data) {

                            var fltlst = response.data;
                            psrFltList = psrFltList.concat(fltlst);

                            var len = fltlst.length;
                            for (var i = 0; i < len; i++) {
                                var flt = fltlst[i];
                                if (flt.flightNo.indexOf(comp_code) > -1) {
                                    var fltinfo = flightInfoList[flt.flightNo];

                                    if (!isNaN(flt.bookNum)) {
                                        bookNum += Number(flt.bookNum); // 订票人数
                                    }
                                    if (!isNaN(flt.ckiNum)) {
                                        ckiNum += Number(flt.ckiNum); // 值机人数
                                        if (fltinfo) {
                                            acNo2ckiNumOut[fltinfo.acLongNo] = Number(flt.ckiNum);
                                        }
                                    }

                                    if (fltinfo) {
                                        acNoOutList.push(fltinfo.acLongNo);
                                    }
                                }

                            }

                            $('#bookNum_out').text(bookNum);
                            $('#ckiNum_out').text(ckiNum);

                            setTrvRate('cvs_trv3', '#00B384', ckiNum / bookNum);
                            setTrvRateOut();

                        }

                    },
                    error: function () {
                    }
                });
            }


            function setTrvRateOut() {
                // 飞机布局
                if (ac_aircraft_list == undefined || acNoOutList == undefined) {
                    setTimeout(setTrvRateOut, 10, ckiNum);
                    return;
                }
                var seats_total = 0;
                var ckiNum = 0;
                var len = ac_aircraft_list.length;
                for (var i = 0; i < len; i++) {
                    var dd = ac_aircraft_list[i];
                    var acNo = dd.data.longNo;
                    var cabin = dd.data.cabin;
                    var seats = getCabinSeats(cabin);
                    if (acNoOutList.indexOf(acNo) >= 0 && seats > 0 && acNo2ckiNumOut[acNo] >= 0) {
                        seats_total += Number(seats);
                        ckiNum += acNo2ckiNumOut[acNo];
                    }
                }

                var rate = ckiNum / seats_total;
                if (rate > 0) {
                    setTrvRate('cvs_trv4', '#86D1FF', rate);
                } else {
                    setTrvRate('cvs_trv4', '#86D1FF', 0);
                }

                $('#cki_rate_out').text(Math.round(rate * 1000) / 10);

            }


            function getCabinSeats(cabin) {
                // cabin 座舱布局 C6Y170
                var arr = cabin.split('Y');
                var seat1 = !isNaN(arr[1]) ? arr[1] : 0;
                var arr2 = arr[0].split('C');
                var seat2 = !isNaN(arr2[1]) ? arr2[1] : 0;
                var seat = Number(seat1) + Number(seat2);

                return seat;
            }


            // 中转规则
            if (mctInfoList[base_code] == undefined) {
                var param = {
                    "iataCode": base_code,
                }
                $.ajax({
                    type: 'post',
                    url: "/bi/web/queryMctInfoAttrs",
                    contentType: 'application/json',
                    dataType: 'json',
                    async: true,
                    data: JSON.stringify(param),
                    success: function (response) {
                        if (response.result && response.result.valueList) {
                            var lst = response.result.valueList;
                            var mctlist = [];
                            var len = lst.length;
                            for (var i = 0; i < len; i++) {
                                var dd = lst[i];
                                mctlist.push(dd);
                            }
                            mctInfoList[base_code] = mctlist;
                        } else {
                            mctInfoList[base_code] = [];
                        }

                        setInOutPsr();

                    },
                    error: function () {
                    }
                });
            } else {
                setInOutPsr();
            }

            // 查询旅客 // 进港
            var fltInOutCnt = 0;
            var fltNoInOutList;
            var fltNoOtherComp;
            var fltInOutPsrNum; // 前一班航班号,后一班航班号:乘客人数

            var param = {
                'fltDate': today,
                'arrStnCode': base_code,
                'inOrOutType': 'out',
                'fltAlcdtwList': comp_code,
            }

            $.ajax({
                type: 'post',
                url: "/bi/web/findPassengerByFltV2",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {

                    //获取转出旅客的进港航班号和出港航班号
                    if (fltNoInOutList == undefined) {
                        fltNoInOutList = [];
                    }
                    if (fltNoOtherComp == undefined) {
                        fltNoOtherComp = [];
                    }
                    if (fltInOutPsrNum == undefined) {
                        fltInOutPsrNum = {};
                    }

                    for (var i in response) {
                        var obj = response[i];
                        var fltNo = obj.fltNo;
                        var psrOutFlt = obj.psrOutFlt;
                        if (psrOutFlt) {
                            var psrOutFltCompCode = psrOutFlt.substr(0, 2);
                            var flt2 = fltNo + ',' + psrOutFlt;
                            if (fltNo && fltNo != '' && comp_code_list.indexOf(psrOutFltCompCode) != -1 && fltNoInOutList.indexOf(flt2) == -1) {
                                fltNoInOutList.push(flt2);

                                if (psrOutFltCompCode != comp_code && fltNoOtherComp.indexOf(psrOutFlt) == -1) {
                                    fltNoOtherComp.push(psrOutFlt);
                                }

                            }
                            if (fltInOutPsrNum[flt2] == undefined) {
                                fltInOutPsrNum[flt2] = 0;
                            }
                            fltInOutPsrNum[flt2]++;
                        }

                    }

                    fltInOutCnt++;
                    setInOutPsr();


                },
                error: function (jqXHR, txtStatus, errorThrown) {
                    console.log('----error');
                }
            });


            // 查询旅客 // 出港
            var param = {
                'fltDate': today,
                'depStnCode': base_code,
                'inOrOutType': 'in',
                'fltAlcdtwList': comp_code,
            }

            $.ajax({
                type: 'post',
                url: "/bi/web/findPassengerByFltV2",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {

                    //获取转出旅客的进港航班号和出港航班号
                    if (fltNoInOutList == undefined) {
                        fltNoInOutList = [];
                    }
                    if (fltNoOtherComp == undefined) {
                        fltNoOtherComp = [];
                    }
                    if (fltInOutPsrNum == undefined) {
                        fltInOutPsrNum = {};
                    }

                    for (var i in response) {
                        var obj = response[i];
                        var fltNo = obj.fltNo;
                        var psrInflt = obj.psrInflt;
                        if (psrInflt) {
                            var psrInfltCompCode = psrInflt.substr(0, 2);
                            var flt2 = psrInflt + ',' + fltNo;
                            if (fltNo && fltNo != '' && comp_code_list.indexOf(psrInfltCompCode) != -1 && fltNoInOutList.indexOf(flt2) == -1) {
                                fltNoInOutList.push(flt2);

                                if (psrInfltCompCode != comp_code && fltNoOtherComp.indexOf(psrInflt) == -1) {
                                    fltNoOtherComp.push(psrInflt);
                                }

                            }
                            if (fltInOutPsrNum[flt2] == undefined) {
                                fltInOutPsrNum[flt2] = 0;
                            }
                            fltInOutPsrNum[flt2]++;
                        }


                    }

                    fltInOutCnt++;
                    setInOutPsr();


                },
                error: function (jqXHR, txtStatus, errorThrown) {
                    console.log('----error');
                }
            });


            function setInOutPsr() {

                if (flightInfoList == undefined || mctInfoList == undefined || fltInOutCnt < 2) {
                    return;
                }

                var fltInfolst = {}; // {flightNo : fltInfo}


                if (fltNoOtherComp && fltNoOtherComp.length > 0) {
                    // 获取航班信息
                    var param = {
                        "stdStart": stdStart,
                        "stdEnd": stdEnd,
                        "acOwner": '',
                        "statusList": '',
                        "fltNoList": fltNoOtherComp.join(','),
                    }
                    $.ajax({
                        type: 'post',
                        url: "/bi/web/getStandardFocFlightInfo",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(param),
                        success: function (response) {
                            var fltList = response.data;
                            var len = fltList.length;
                            for (var i = len - 1; i >= 0; i--) {
                                var ff = fltList[i];
                                fltInfolst[ff.flightNo] = ff;
                            }

                            createMctDataList();

                        },
                        error: function () {
                        }
                    });

                } else {

                    createMctDataList();

                }


                function getMctGroundtime(noFrom, noTo, arpType) {
                    var groundTime = 80;
                    var groundTime2;
                    var groundTime3;
                    var mtclist = mctInfoList[base_code];
                    if (mtclist.length > 0) {
                        var len = mtclist.length;
                        for (var i = mtclist.length - 1; i >= 0; i--) {
                            var fltNoFrom = mtclist[i].fromWhere;
                            var fltNoTo = mtclist[i].toWhere;
                            var airportType = mtclist[i].airportType;
                            groundTime = Number(mtclist[i].groundTime);
                            if (fltNoFrom == 'null' && fltNoTo == 'null') {
                                groundTime2 = groundTime;
                                if (airportType == arpType) {
                                    groundTime3 = groundTime;
                                }
                            }

                            if (fltNoFrom == noFrom && fltNoTo == noTo) {
                                return groundTime;
                            }

                        }
                    }

                    if (groundTime3 > 0) {
                        return groundTime3;
                    }
                    if (groundTime2 > 0) {
                        return groundTime2;
                    }
                    return groundTime;
                }

                //
                function createMctDataList() {
                    if (psrFltList.length == 0) {
                        setTimeout(createMctDataList, 10);
                        return;
                    }

                    if (mctInfoList[base_code] == undefined || mctInfoList[base_code].length == 0) {
                        $('#zz_flt_table tbody').html('<tr><td style="height: 180px; text-align: center;">没有当前基地的MCT</td></tr>');
                        return;
                    }


                    var mctFltList = [];
                    var len = fltNoInOutList.length;
                    for (var i = 0; i < len; i++) {
                        var itm = {};

                        var flt2 = fltNoInOutList[i];
                        var fltnoarr = flt2.split(',');
                        var psrnum = fltInOutPsrNum[flt2];

                        var fltNoFrom = fltnoarr[0];
                        var fltNoTo = fltnoarr[1];
                        itm.fltNoFrom = fltNoFrom;
                        itm.fltNoTo = fltNoTo;
                        itm.psrnum = psrnum;

                        if (flightInfoList[fltNoFrom] != undefined) {
                            itm.fromWhereFlt = flightInfoList[fltNoFrom];
                        } else if (fltInfolst[fltNoFrom] != undefined) {
                            itm.fromWhereFlt = fltInfolst[fltNoFrom];
                        }

                        if (flightInfoList[fltNoTo] != undefined) {
                            itm.toWhereFlt = flightInfoList[fltNoTo];
                        } else if (fltInfolst[fltNoTo] != undefined) {
                            itm.toWhereFlt = fltInfolst[fltNoTo];
                        }

                        if (itm.fromWhereFlt && itm.toWhereFlt) {

                            var arpType = 3;
                            if (itm.fromWhereFlt.fltType == 'I' && itm.toWhereFlt.fltType == 'I') {
                                arpType = 2;
                            } else if (itm.fromWhereFlt.fltType == 'I' || itm.toWhereFlt.fltType == 'I') {
                                arpType = 1;
                            } else {
                                arpType = 3;
                            }
                            itm.groundTime = getMctGroundtime(fltNoFrom, fltNoTo, arpType);

                            mctFltList.push(itm);
                        }


                    }


                    mctFltList.sort(function (a, b) {
                        return getShortTime(a.fromWhereFlt.etaChn) - getShortTime(b.fromWhereFlt.etaChn)
                    });

                    var html = '';
                    var cnt = 0;

                    for (var i = mctFltList.length - 1; i >= 0; i--) {
                        var mctitm = mctFltList[i];

                        var groundTime = Number(mctitm.groundTime);

                        var cls = cnt % 2 == 0 ? 'abg' : '';

                        var bgc1 = '';
                        var flt = mctitm.fromWhereFlt;
                        if ((flt.status == 'NDR' || flt.status == 'ATA') && Number(flt.dur1) == 0) {
                            bgc1 = 'green_tag'
                        } else if ((flt.status == 'NDR' || flt.status == 'ATA') && Number(flt.dur1) > 0) {
                            bgc1 = 'yellow_tag'
                        }

                        var bgc2 = '';
                        var flt = mctitm.toWhereFlt;
                        if ((flt.status == 'ATD' || flt.status == 'DEP') && Number(flt.dur1) == 0) {
                            bgc2 = 'green_tag'
                        } else if ((flt.status == 'ATD' || flt.status == 'DEP') && Number(flt.dur1) > 0) {
                            bgc2 = 'yellow_tag'
                        }

                        var etaChn = getShortTime(mctitm.fromWhereFlt.etaChn); //预计到达时间（北京时间）
                        var etdChn = getShortTime(mctitm.toWhereFlt.etdChn); //预计起飞时间（北京时间）

                        var zzTime = 0;
                        var leftTime = 0;

                        var a_time = parserDate(mctitm.fromWhereFlt.etaChn);
                        var d_time = parserDate(mctitm.toWhereFlt.etdChn);
                        var tmspan = d_time.getTime() - a_time.getTime();
                        zzTime = Math.round(tmspan / (60 * 1000));
                        if (zzTime < 0) {
                            //表示后一班航班是第二天的，改成第二天的航班
                            tmspan = (d_time.getTime() + 86400000) - a_time.getTime();
                            zzTime = Math.round(tmspan / (60 * 1000));
                        }


                        // 中转时间少于groundTime表示中转紧张
                        if (zzTime < groundTime) {
                            //console.log('groundTime', groundTime, 'zzTime', zzTime);

                            var now_time = new Date();
                            var ost = d_time.getTime() - now_time.getTime();
                            var min = Math.round(ost / (60 * 1000));

                            if (min > 0) {
                                leftTime = min;
                            } else {
                                leftTime = 0;
                            }

                            leftTime = Math.min(leftTime, zzTime);

                            html += '<tr class="' + cls + '">';
                            html += '<td class="zzcl1">' + mctitm.fltNoFrom + '</td>';
                            html += '<td class="zzcl2 white"><span class="' + bgc1 + '">' + etaChn + '</span></td>';
                            html += '<td class="zzcl3 blue1"><span class="">' + mctitm.psrnum + '</span></td>';
                            html += '<td class="zzcl4 blue1"><span class="">' + mctitm.fltNoTo + '</span></td>';
                            html += '<td class="zzcl5 white"><span class="' + bgc2 + '">' + etdChn + '</span></td>';
                            html += '<td class="zzcl6 blue1"><span class="">' + zzTime + '</span></td>';
                            html += '<td class="zzcl7 blue1"><span class="">' + leftTime + '</span></td>';
                            html += '</tr>';

                            cnt++;
                        }


                    }

                    if (html.length == 0) {
                        html += '<tr><td style="height: 180px; text-align: center;">目前没有中间紧张航班</td></tr>';
                    }
                    $('#zz_flt_table1 tbody').html(html);

                    //大于N条自动滚动
                    if (cnt > 7) {
                        $('#zz_flt_table2 tbody').html(html);

                        var speed = 60;
                        var sec = document.getElementById("zz_flt_table");
                        var sec2 = document.getElementById("zz_flt_table2");
                        var sec1 = document.getElementById("zz_flt_table1");

                        //sec2.innerHTML=sec1.innerHTML;
                        function Marquee() {
                            if (sec2.offsetTop - sec.scrollTop <= 0)
                                sec.scrollTop -= sec1.offsetHeight
                            else {
                                sec.scrollTop++
                            }
                        }

                        clearInterval(marquee_zz_itv);
                        marquee_zz_itv = setInterval(Marquee, speed);
                        sec.onmouseover = function () {
                            clearInterval(marquee_zz_itv)
                        }
                        sec.onmouseout = function () {
                            marquee_zz_itv = setInterval(Marquee, speed)
                        }
                    }
                }


            }


        } // end of loadAll


        // 获取机场列表
        function getAirportList() {

            var param = {
                //"AIRPORT_CODE": arpcodes, // 可选，传入机场CODE
            }
            $.ajax({
                type: 'post',
                url: "/bi/web/airportdetail",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    airportList = {};
                    var list = response.airport;
                    for (var i = list.length - 1; i >= 0; i--) {
                        var arp = list[i];
                        airportList[arp.code] = arp;
                    }

                },
                error: function () {
                }
            });
        }

        getAirportList();


        // ------------------------------------------------------------------------
        // 获取 飞机列表
        // ------------------------------------------------------------------------
        var ac_aircraft_list;

        function getAcAircraftList() {
            var param = {
                "companyNodeId": companyNodeId,
            }
            $.ajax({
                type: 'post',
                url: "/bi/web/getAcAircraftList",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {

                    ac_aircraft_list = response.data;

                },
                error: function () {
                }
            });
        }

        getAcAircraftList();


        // ------------------------------------------------------------------------
        // 获取所有机型
        // ------------------------------------------------------------------------
        var actypeId2Code;
        var param = {}

        $.ajax({
            type: 'post',
            url: "/bi/web/actypeall",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {

                if (response.actype) {
                    var list = response.actype;
                    actypeId2Code = {};

                    list.sort(function (a, b) {
                        return a.sort - b.sort
                    });

                    var len = list.length;
                    for (var i = 0; i < len; i++) {
                        var obj = list[i];

                        actypeId2Code[obj.id] = obj.code;
                    }

                }

            },
            error: function () {

            }
        });


        function getRandNumber() {
            var newnum = parseInt(Math.random() * 10);
            if (newnum > 0 && newnum < 10) {
                return newnum;
            } else {
                return getRandNumber();
            }
        }


        /////////////

        var chart_earch;

        function crate3DEarth() {
            chart_earch = echarts.init(document.getElementById('earth3d'));

            var option = {

                //tooltip: {},

                backgroundColor: 'rgba(0,0,0,0)',

                globe: {
                    baseTexture: 'asset/earth.jpg',

                    globeRadius: 80,
                    globeOuterRadius: 20,

                    displacementScale: 1,

                    shading: 'lambert',

                    shading: 'realistic',
                    light: {
                        main: {
                            intensity: 0.3
                        },
                        ambient: {
                            intensity: 1.0
                        },
                    },

                    viewControl: {
                        autoRotate: false,
                        zoomSensitivity: false,
                        targetCoord: [106, 32]
                    },

                    layers: []
                },
                series: []
            }

            chart_earch.setOption(option);


        }


        //////////////////////////////////////////////////////////////////////////////
        //////////////////////////////////////////////////////////////////////////////


        var rangeObj = this;


        var createBMap = function () {

            var series = [];

            option = {
                color: [],
                tooltip: {
                    trigger: 'item',
                    show: true,
                    formatter: function (params, ticket, callback) {

                        //console.log(params);
                        var data = params.data;

                        var html = '' //'<div style="padding: 5px;">';

                        if (data.isCity) {
                            /*
                            var response = window.parent.window.weather[data.name];
                            html += data.name;
                            if(response){
                                html += '<br>';
        
                                var weatherInfoCodes = ['FG','TS','TSRA','RA','SS','DS'];
                                var visibility = isNaN(response.visibility) || response.visibility==0 ? 9999 : Number(response.visibility); //能见度
                                var rvr = isNaN(response.rvr) || response.rvr==0 ? 9999 : Number(response.rvr); //跑道目视距离
                                var cloudInfo = isNaN(response.cloudInfo) || response.cloudInfo==0 ? 9999 : Number(response.cloudInfo); //云况
                                var windFs = isNaN(response.windFs) ? 0 : Number(response.windFs); //风速
                                var weatherInfo = response.weatherInfo; //天气现象
                                var weatherInfoTxt = response.weatherInfoTxt.replace(/<[^>]+>/g,"");
                                var temperature = isNaN(response.temperature) ? '-' : Number(response.temperature)+'℃';
        
                                html += weatherInfoTxt + '<br>';
                                html += '气温: ' + temperature + '<br>';
                                html += '风速: ' + windFs + 'km/h<br>';
                                html += '能见度: ' + visibility + 'm<br>';
        
                            }
                            */

                        } else {
                            var flt = data.flt;
                            var depAirport = airportList[flt.depStn]; //出发机场信息
                            var arrAirport = airportList[flt.arrStn]; //到达机场信息

                            //航班号、起飞城市-到达城市、航班状态（实际/预计/计划起飞时间-实际/预计/计划到达时间）、机号（机型）、客座率（布局）、实时位置、实施油量
                            html += '航班号: ' + data.name + '<br>';
                            html += depAirport.city_name + ' - ' + arrAirport.city_name + '<br>';
                            if (flt.status == 'DEP' || flt.status == 'ARR' || flt.status == 'NDR' || flt.status == 'ATA') {
                                // 起飞,落地,到达
                                // 实际起飞时间 atdChn
                                html += '实际起飞时间: ' + trimTime(flt.atdChn) + '<br>';
                            } else {
                                // 预计出发
                                html += '预计出发时间: ' + trimTime(flt.etdChn) + '<br>';
                            }

                            if (flt.status == 'ATA') {
                                // 到达
                                // 实际起飞时间 atdChn
                                html += '实际到达时间: ' + trimTime(flt.ataChn) + '<br>';
                            } else {
                                // 预计到达
                                html += '预计到达时间: ' + trimTime(flt.etaChn) + '<br>';
                            }

                            if (flt.delay1 != '' && flt.dur1 > 0) {
                                html += '延误原因: ' + flt.delay1Name + '<br>';
                                html += '延误时间: ' + (Number(flt.dur1) + Number(flt.dur2) + Number(flt.dur3) + Number(flt.dur4)) + '分钟<br>';
                            }

                            html += '机号: ' + data.acno + '(' + flt.acType + ')' + '<br>';
                            html += '实时位置: ' + Math.round(data.value[0] * 10000) / 10000 + ', ' + Math.round(data.value[1] * 10000) / 10000 + '<br>';
                            html += '实时油量: ' + Math.round(data.oil) + 'cc';
                        }

                        //html += '</div>';

                        return html;
                    },
                    backgroundColor: '#021e55',
                },
                geo: {
                    map: 'world',
                    roam: true,
                    zoom: 2.22,
                    center: [56, 25],
                    silent: true,
                    label: {
                        emphasis: {
                            show: false
                        }
                    },
                    itemStyle: {
                        normal: {
                            areaColor: '#3d7dd0',
                            borderColor: '#14224c'
                        },
                        emphasis: {
                            areaColor: '#3d7dd0',
                            borderColor: '#14224c'
                        }
                    },
                    //regions:countries
                },
                backgroundColor: '#14224c',
                series: series
            };


            var myChart = echarts.init(document.getElementById('map'));
            myChart.setOption(option);
            window.onresize = myChart.resize;


            rangeObj.myChart = myChart;


            // 点击事件
            myChart.on('click', function (params) {
                params.event.event.stopPropagation();
                params.event.event.preventDefault();

                if (params.data.isAirline == undefined) {
                    showFlightDetails(params.data.flt.flightNo);
                }

            });


            // 经纬度坐标转换成屏幕像素坐标
            function geoCoord2Pixel(geoCoord) {
                var point = new BMap.Point(geoCoord[0], geoCoord[1]);
                var pos = map.pointToOverlayPixel(point);
                return [pos.x, pos.y];
            };


        };


        // 飞机位置
        function setPlaneLocation() {

            if (rangeObj.myChart != undefined && planeLocationList != undefined && flightInfoList != undefined) {

                var planes = planeLocationList;
                var flightList = flightInfoList;

                var series = [];
                var list_1 = []; // 正常航班
                var list_2 = []; // 延误航班
                var len = planes.length;

                var statusMap = {
                    'ARR': '落地',
                    'NDR': '落地',
                    'ATD': '推出',
                    'ATA': '到达',
                    'CNL': '取消',
                    'DEL': '延误',
                    'DEP': '起飞',
                    'RTR': '返航',
                    'SCH': '计划'
                };


                //var fltlstxxx = [];
                //var fltlstxxx2 = [];

                //for(var fi in flightList){
                //  var flt = flightList[fi];
                //  if(flt.status == 'DEP' && fi.indexOf('GS') > -1){
                //    fltlstxxx2.push(fi);
                //  }
                //}

                for (var i = 0; i < len; i++) {
                    var dat = planes[i];

                    var flt = flightList[dat.fltno];

                    if (flt) {
                        //if(flt.status == 'DEP' && flt.delay1 == ''){ // 正常在飞
                        if (flt.delay1 == '') { // 正常在飞

                            //fltlstxxx.push(dat.fltno);

                            list_1.push({
                                name: dat.fltno,
                                acno: dat.acno,
                                oil: dat.oil,
                                vec: dat.vec,
                                flt: flt,
                                value: [dat.lon, dat.lat],
                                symbolRotate: -dat.vec + 90, //航向   正北是0°，顺时针，0到360° // 因为图片机头指向右，所以需要逆时针转90度
                            });

                            //}else if(flt.status == 'DEP' && flt.delay1 != ''){  // 延误航班
                        } else if (flt.delay1 != '') { // 延误航班

                            //fltlstxxx.push(dat.fltno);

                            list_2.push({
                                name: dat.fltno,
                                acno: dat.acno,
                                oil: dat.oil,
                                vec: dat.vec,
                                flt: flt,
                                value: [dat.lon, dat.lat],
                                symbolRotate: -dat.vec + 90, //航向   正北是0°，顺时针，0到360° // 因为图片机头指向右，所以需要逆时针转90度
                            });

                        }


                    }

                }

                //console.log('fltlstxxx', fltlstxxx);
                //console.log('fltlstxxx2', fltlstxxx2);


                series.push({
                    name: 'normal',
                    type: 'scatter',
                    coordinateSystem: 'geo',
                    symbol: 'image://img/flight.legend_1.png',
                    symbolSize: 15,
                    label: {
                        normal: {
                            show: false,
                        }
                    },
                    data: list_1
                });

                series.push({
                    name: 'delay',
                    type: 'scatter',
                    coordinateSystem: 'geo',
                    symbol: 'image://img/flight.legend_2.png',
                    symbolSize: 15,
                    label: {
                        normal: {
                            show: false,
                        }
                    },
                    data: list_2
                });


                var options = {
                    series: series
                }

                rangeObj.myChart.setOption(options);


            } else {
                setTimeout(setPlaneLocation, 0);
            }
        }


        createBMap();


        var mouseX, mouseY;
        $(document).mousemove(function (e) {
            mouseX = e.pageX;
            mouseY = e.pageY;
        });


        // 截取小数位数
        function trimDecimal(num, len) {
            var nnn = 1;
            for (var i = 0; i < len; i++) {
                nnn = nnn * 10;
            }
            return Math.round(num * nnn) / nnn;
        }

        // min ≤ r ≤ max
        function randomNumRange(Min, Max) {
            var Range = Max - Min;
            var Rand = Math.random();
            var num = Min + Math.round(Rand * Range); //四舍五入
            return num;
        }

        // 获取参数
        function getQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]);
            return '';
        }


        function shuffle(arr) {
            var len = arr.length;
            for (var i = 0; i < len - 1; i++) {
                var idx = Math.floor(Math.random() * (len - i));
                var temp = arr[idx];
                arr[idx] = arr[len - i - 1];
                arr[len - i - 1] = temp;
            }
            return arr;
        }


        function trimTime(timestr) {
            var arr = timestr.split(' ');
            var arr2 = arr[1].split(':');
            return arr2[0] + ':' + arr2[1];
        }


        // ------------------------------------------------------------------------
        // 获取航班实时位置
        // ------------------------------------------------------------------------

        // 获取飞机实时位置
        function getPlaneLocationMq() {

            var param = {
                'mode': 'pos'
            }
            $.ajax({
                type: 'post',
                url: "/bi/web/flightMq",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {

                    planeLocationList = [];
                    var plist = {};

                    //var list = response.data.data1;
                    function processData(data1) {
                        var lst = {};
                        var len = data1.length;
                        for (var i = 0; i < len; i++) {
                            var dd = data1[i];
                            var fi = dd.fi;
                            if (lst[fi] == undefined) {
                                lst[fi] = {
                                    data: []
                                };
                                lst[fi]['data'].push(dd);
                            } else {
                                lst[fi]['data'].push(dd);
                            }
                        }

                        return lst;
                    }

                    var list = processData(response.data.data1);

                    for (var fltno in list) {

                        var fltobj = list[fltno];
                        var itmx2 = fltobj.data;

                        var itm;

                        if (itmx2 && itmx2.length > 1) {
                            var itm1 = itmx2[0];
                            var itm2 = itmx2[1];


                            itm1.UTC = itm1.UTC.replace(' ', '');
                            itm2.UTC = itm2.UTC.replace(' ', '');

                            if (itm1.UTC > itm2.UTC) {
                                itm = itm1
                                itm.LON1 = itm2.LON;
                                itm.LAT1 = itm2.LAT;
                            } else if (itm1.UTC < itm2.UTC) {
                                itm = itm2
                                itm.LON1 = itm1.LON;
                                itm.LAT1 = itm1.LAT;
                            } else {

                                itm = itm2
                                itm.LON1 = itm1.LON;
                                itm.LAT1 = itm1.LAT;

                                console.log(fltno, '两组经纬度UTC相同');
                            }
                        } else if (itmx2 && itmx2.length > 0) {
                            itm = itmx2[0];

                        }


                        if (itm) {

                            var alt = itm.ALT;
                            var cas = itm.CAS;
                            var vec;

                            var fltno = itm.fi;
                            var flt = flightInfoList[fltno];

                            if (fltno.indexOf(comp_code) == 0 && flt && flt.status == 'DEP') {

                                var acno = itm.an;
                                acno = acno.replace('-', '');

                                var lon = formatLonLat(itm.LON);
                                var lon1 = formatLonLat(itm.LON1);
                                var lat = formatLonLat(itm.LAT);
                                var lat1 = formatLonLat(itm.LAT1);

                                if (isNaN(itm.LON)) {
                                    vec = Number(itm.VEC);
                                }

                                var oil = isNaN(itm.OIL) ? '' : itm.OIL;

                                var pdat = {
                                    fltno: fltno,
                                    acno: acno,
                                    alt: alt,
                                    vec: vec,
                                    lon: lon,
                                    lat: lat,
                                    lon1: lon1,
                                    lat1: lat1,
                                    oil: oil,
                                };

                                var code = acno + '-' + fltno;

                                /*
                                if(plist[code] == undefined){
                                    plist[code] = pdat;
                                }else if(plist[code].lon1 == undefined){
                                    plist[code].lon1 = pdat.lon;
                                    plist[code].lat1 = pdat.lat;
                                    if(oil > 0){
                                        plist[code].oil = oil;
                                    }
                                }else if(oil > 0){
                                    plist[code].oil = oil;
                                }
                                */

                                if (pdat.vec == undefined) {
                                    pdat.vec = getGeoAngle(pdat.lat, pdat.lon, pdat.lat1, pdat.lon1);
                                }
                                planeLocationList.push(pdat);
                            }
                        }
                    }

                    /*
                    for(var code in plist){
                        var pdat = plist[code];
                        //if(pdat.vec || pdat.lon1 != undefined){
                            if(pdat.vec == undefined){
                                pdat.vec = getGeoAngle(pdat.lat, pdat.lon, pdat.lat1, pdat.lon1);
                            }
                            planeLocationList.push(pdat);
                        //}
                    }
                    */
                    console.log('planeLocationList', planeLocationList);
                    setPlaneLocation();

                    /*
                    if($('#earth3d-wrapper').is(':visible')){
                        if(!chart_earch){
                            crate3DEarth();
                        }
                        set3Dlines();
        
                    }
                    */

                },
                error: function (jqXHR, txtStatus, errorThrown) {

                }
            });
        }


        // 显示航班信息页面 TODO
        function showFlightDetails(flightNo) {
            let contain = false;
            for (let i = 0; i < planeLocationList.length; i++) {
                if (planeLocationList[i].fltno == flightNo) {
                    contain = true;
                    break;
                }
            }
            // var url = 'flight.html?fltno=' + flightNo;//原版
            // http://172.16.113.58:8080/largescreen/sdc/flight.html?scale=auto&fltno=PN6340&compCode=PN //新版
            if (contain) {
                var url = '/largescreen/sdc/flight.html?fltno=' + flightNo + '&compCode=' + flightNo.substr(0, 2);
                var url_scale = getQueryString('scale');
                if (url_scale) {
                    url = url + '&scale=' + url_scale;
                }
                windowOpen(url, '_blank')
            }
        }


        function windowOpen(url, target) {
            var a = document.createElement("a");
            a.setAttribute("href", url);
            if (target == null) {
                target = '';
            }
            a.setAttribute("target", target);
            document.body.appendChild(a);
            if (a.click) {
                a.click();
            } else {
                try {
                    var evt = document.createEvent('Event');
                    a.initEvent('click', true, true);
                    a.dispatchEvent(evt);
                } catch (e) {
                    window.open(url);
                }
            }
            document.body.removeChild(a);
        }

        //////////////////////////////////////////////////////////////////////////////
        //////////////////////////////////////////////////////////////////////////////


        $('#btn_switch_map').on('click', function (evt) {
            switchmap();
        });

        function switchmap() {
            if ($('#map').is(':visible')) {
                $('#map').hide();
                $('#map').css('pointer-events', 'none');
                $('#earth3d').show();
                $('.earthlight').show();
                $('#comp_rank_list_month').show();
                $('#lb_rank_list_month').show();
                $('#lb_switch_map').text('查看具体航班详情');

                $('.searchform').fadeOut();
            } else {
                $('#map').show();
                $('#map').css('pointer-events', 'auto');
                $('#earth3d').hide();
                $('.earthlight').hide();
                $('#comp_rank_list_month').hide();
                $('#lb_rank_list_month').hide();
                $('#lb_switch_map').text('查看航线');

                $('.searchform').fadeIn();
            }
        }


        $('.ico_search').on('click', function () {
            var fltno = $('#ipt_fltno').val();
            if (flightInfoList && planeLocationList) {
                var flt = flightInfoList[fltno];

                var len = planeLocationList.length;
                var dat;
                var found = false;
                for (var i = 0; i < len; i++) {
                    var dd = planeLocationList[i];
                    if (fltno == dd.fltno) {
                        dat = dd;
                        found = true;
                    }
                }

                if (flt && found) {
                    $('.searchform .error').hide();
                    showFlightDetails(fltno);
                } else {
                    $('.searchform .error').show();
                }

            }


        })


        crate3DEarth();


        loadAll();
        setInterval(loadAll, 5 * 60 * 1000);

        var websocket = null;
        //判断当前浏览器是否支持WebSocket
        initSocket();

        function initSocket() {
            let host = window.location.host.split(":");
            let protocol = window.location.protocol;
            let ws = protocol === 'https:' ? 'wss' : 'ws';

            function initUserinfo() {
                if ('WebSocket' in window) {
                    if (userinfo) {
                        if (host[0] === "vis.hnair.net") {
                            websocket = new WebSocket(`${ws}://${host[0]}:8280/websocket/7x2-base-${userinfo.id}`);
                        } else {
                            websocket = new WebSocket(`${ws}://${host[0]}:8888/websocket/7x2-base-${userinfo.id}`);
                        }
                        //连接发生错误的回调方法
                        websocket.onerror = function () {
                            console.log("WebSocket连接发生错误");
                        };

                        //连接成功建立的回调方法
                        websocket.onopen = function () {
                            console.log("WebSocket连接成功");
                        }

                        //接收到消息的回调方法
                        websocket.onmessage = function (event) {
                            console.log(event.data);
                            eval(event.data);
                        }

                        //连接关闭的回调方法
                        websocket.onclose = function () {
                            console.log("WebSocket连接关闭");
                        }

                        //监听窗口关闭事件，当窗口关闭时，主动去关闭websocket连接，防止连接还没断开就关闭窗口，server端会抛异常。
                        window.onbeforeunload = function () {
                            websocket.close();
                        }
                    } else {
                        setTimeout(initUserinfo, 10);
                    }
                } else {
                    alert('当前浏览器 Not support websocket');
                }
            }

            initUserinfo();
        }

    });

});



