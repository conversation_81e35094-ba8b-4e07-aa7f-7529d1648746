
/**/

.page-wrapper {
  
}


.pagetitle{
  position: absolute;
  width: 100%;
  height: 54px;
  top: 58px;
  text-align: center;

}
.maintitle{
  position: absolute;
  top: 18px;
  color: #fff;
  width: 100%;
  font-size: 24px;
}

#main_cb_week {
  position: absolute;
  top: 19px;
  right: 119px;
  height: 34px;
  width: 85px;
  z-index: 1000;
}
#main_cb_month {
  position: absolute;
  top: 19px;
  right: 15px;
  height: 34px;
  width: 104px;
  z-index: 1001;
}

#main_cb_week .combobox_label
{
  padding: 6px 6px;
  background-image: url(../img/combobox_arr.png);
  background-repeat: no-repeat;
  background-position: 63px center;
  border-radius: 4px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}
#main_cb_month .combobox_label
{
  padding: 6px 6px;
  background-image: url(../img/combobox_arr.png);
  background-repeat: no-repeat;
  background-position: 83px center;
  border-radius: 4px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: none;
}

.combotab_selected .combobox_label {
  background-color: #2a81d2;
}
.combotab .combobox_label {
  background-color: #0950a2;
  opacity: 0.5;
  color: #8abfff;
}

#week_date_range {
  position: absolute;
  top: 58px;
  right: 50px;
  width: 160px;
  font-size: 14px;
  text-align: center;
  border: 1px solid #1f5fa8;
  border-radius: 2px;
  padding: 3px 0 5px 0px;
  background-color: #021d39;
  box-shadow: 0 1px 8px #021121;

  display: none;
  z-index: 999;
  
}


#hna_button{
  position: absolute;
  left: 365px;
  top: 139px;
  width: 88px;
  height: 38px;
  background: url(../img/a2.1-hna.png) no-repeat;
  background-size: contain;
  pointer-events: auto;
  cursor: pointer;
}


/* --- */
.block_l1 {
  height: 410px;
  left: 15px;
  top: 100px;
}
.block_l1 .cont{
  height: 384px;
}
.block_l1 .cont .row1{
  position: absolute;
  margin: 8px;
  width: 291px;
  height: 177px;
  background-image: url(../img/a3.1_icon1.png);
  background-repeat: no-repeat;
  background-position: 9px 15px;
  background-color: #133c75;
  border-radius: 5px;
}

.block_l1 .cont .row1 .r1{
  margin: 20px 0px 5px 5px;
  padding-left: 76px;
  height: 68px;
  border-bottom: 2px solid #061c43;
}
.block_l1 .cont .row1 .r1 .kpi .val{
  color: #7cd804;
}

.block_l1 .cont .row1 .r2{
  position: absolute;
  top: 87px;
}
.block_l1 .cont .row1 .r2 .col{
  position: absolute;
  width: 130px;
  height: 72px;
  top: 10px;
  padding-left: 20px;
}
.block_l1 .cont .row1 .r2 .c2{
  left: 150px;
}

.block_l1 .cont .row2{
  position: absolute;
  margin: 8px;
  top: 186px;
  width: 291px;
  height: 85px;
  background-image: url(../img/a3.1_icon2.png);
  background-repeat: no-repeat;
  background-position: 9px 13px;
  background-color: #133c75;
  border-radius: 5px;
}

.block_l1 .cont .row3{
  position: absolute;
  margin: 8px;
  top: 280px;
  width: 291px;
  height: 85px;
  background-image: url(../img/a3.1_icon3.png);
  background-repeat: no-repeat;
  background-position: 9px 13px;
  background-color: #133c75;
  border-radius: 5px;
}
.block_l1 .cont .row2 .r1,
.block_l1 .cont .row3 .r1{
  margin: 20px 5px 5px 5px;
  padding-left: 70px;
  height: 68px;
}
.block_l1 .cont .row2 .kpi,
.block_l1 .cont .row3 .kpi{
  display: inline-block;
  min-width: 100px;
}
.block_l1 .cont .row3 .r1 .kpi .val{
  color: #ff7a0e;
}



/* --- */

.block_l2 {
  height: 218px;
  left: 15px;
  top: 530px;
}
.block_l2 .cont{
  height: 190px;
}
.block_l2 .tab{
  position: absolute;
  text-align: center;
  font-size: 14px;
  font-weight: bold;
  color: #011f4d;
  padding-top: 1px;
  cursor: pointer;
}
.block_l2 .tab1{
  width: 155px;
  height: 24px;
  background: url(../img/a3.1_tab_off.png) no-repeat;
}
.block_l2 .tab1.selected{
  background: url(../img/a3.1_tab_on.png) no-repeat;
}

.block_l2 .tab2{
  width: 155px;
  height: 24px;
  background: url(../img/a3.1_tab_off.png) no-repeat;
}
.block_l2 .tab2.selected{
  background: url(../img/a3.1_tab_on.png) no-repeat;
}

.block_l2 .tab2{
  left: 155px;
}

.block_l2 .cont .kpi{
  position: absolute;
  top: 10px;
  width: 100%;
  text-align: center;
}




/* --- */




/* --- */
.block_r1 {
  right: 15px;
  top: 125px;
}
.block_r1 .cont{
  height: 358px;
}

.block_r1 table {
  position: absolute;
  top: 1px;
  width: 100%;
  height: 100%;
  font-size: 12px;
  font-weight: normal;
}
.block_r1 table thead {
  background-color: #15396f;
  color: #fff;
}
.block_r1 table th {
  height: 32px;
}
.block_r1 table th span{
  cursor: pointer;
  display: inline-block;
  padding-right: 8px;
  background: url(../img/table_sort0.png) no-repeat right 4px;
}
.block_r1 table th .desc{
  background: url(../img/table_sort1.png) no-repeat right 4px;
}
.block_r1 table th .asc{
  background: url(../img/table_sort2.png) no-repeat right 4px;
}

.block_r1 table td {
  border-bottom: 1px solid #163563;
}
.block_r1 table .name{
  padding-left: 12px;
}
.block_r1 td .bar{
  display: inline-block;
  margin-bottom: -2px;
  height: 15px;
  margin-right: 3px;
}
.block_r1 td .bar.blue{
  background: rgb(1,70,139);
  background: -moz-linear-gradient(left,  rgba(1,70,139,1) 0%, rgba(8,130,252,1) 100%);
  background: -webkit-linear-gradient(left,  rgba(1,70,139,1) 0%,rgba(8,130,252,1) 100%);
  background: linear-gradient(to right,  rgba(1,70,139,1) 0%,rgba(8,130,252,1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#01468b', endColorstr='#0882fc',GradientType=1 );
}
.block_r1 td .bar.orange{
  background: rgb(249,86,5);
  background: -moz-linear-gradient(left,  rgba(249,86,5,1) 0%, rgba(255,161,43,1) 100%);
  background: -webkit-linear-gradient(left,  rgba(249,86,5,1) 0%,rgba(255,161,43,1) 100%);
  background: linear-gradient(to right,  rgba(249,86,5,1) 0%,rgba(255,161,43,1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f95605', endColorstr='#ffa12b',GradientType=1 );
}
.block_r1 td .bar .val{
  padding-left: 5px;
  color: #fff;
}




/* --- */
.block_r2 {
  right: 15px;
  top: 525px;
}
.block_r2 .cont{
  height: 190px;
}

.block_r2 table {
  position: absolute;
  top: 1px;
  width: 100%;
  height: 188px;
  font-size: 12px;
  font-weight: normal;
}
.block_r2 table thead {
  background-color: #15396f;
  color: #fff;
}
.block_r2 table th {
  height: 32px;
}
.block_r2 table td {
  border-bottom: 1px solid #163563;
}
.block_r2 table .name{
  padding-left: 12px;
}
.block_r2 td .bar .val{
  padding-left: 5px;
  color: #fff;
}





/* --- */
#map_legend{
  position: absolute;
  left: 890px;
  top: 650px;
  width: 121px;
  height: 81px;
  background: url(../img/a3.1.legend.png?2) no-repeat 0 0;
}
#map_legend .tit{
  position: absolute;
  width: 100%;
  top: 2px;
  text-align: center;
}
#map_legend .itm{
  position: absolute;
  width: 100px;
  left: 24px;
}
#map_legend .l1{
  top: 26px;
}
#map_legend .l2{
  top: 44px;
}
#map_legend .l3{
  top: 62px;
}




/* --- */

#popover_map {
  position: absolute;
  z-index: 888;
  width: 414px;
  height: 275px;

  top:  -1000px;

  background: url(../img/a3.1.pop_map.png) no-repeat 0 0;

  -moz-transform-origin: 50% 50%;
  -wekkit-transform-origin: 50% 50%;
  -ms-transform-origin: 50% 50%;
  transform-origin: 50% 50%;

  opacity: 0;
}


#popover_map .title{
  position: absolute;
  width: 100%;
  height: 50px;
  top: 29px;
  text-align: center;
  color: #92ebf6;
}
#popover_map .close{
  position: absolute;
  width: 15px;
  height: 15px;
  top: 33px;
  right: 25px;
  background: url(../img/x.png) no-repeat 0 0;
  pointer-events: auto;
  cursor: pointer;
  opacity: 0.8;
}
#popover_map .blk {
  display: block;
  line-height: 18px;
}
#popover_map .bigtt {
  line-height: 30px;
}
#popover_map .bigtxt {
  line-height: 16px;
}
#popover_map .col1{
  position: absolute;
  top: 82px;
  left: 50px;
  width: 170px;
  height: 160px;
  border-right: 1px solid #3b5c93;
}
#popover_map .col2{
  position: absolute;
  top: 82px;
  left: 250px;
  width: 160px;
  height: 160px;
}


#selected_mapmarkpoint{
  position: absolute;
  width: 36px;
  height: 36px;
  background: url(../img/mapmarkpoint_L.svg?2) no-repeat center center;
  background-size: 36px 36px;
}
