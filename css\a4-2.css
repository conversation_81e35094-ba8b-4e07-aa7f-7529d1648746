
/**/

div {
  pointer-events: auto;
}

.page-wrapper {
  background: #06112f url(../img/a4.2_bg.png?2) no-repeat top center;
}


.pagetitle{
  position: absolute;
  width: 1306px;
  height: 59px;
  top: 65px;
  left: 30px;
  text-align: center;
  pointer-events: none;
}
.maintitle{
  color: #FFF;
  width: 100%;
  font-size: 26px;
  padding: 13px 0 0 0;
  margin: 0 auto;
  font-weight: bold;
}
.submaintitle{
  color: #FFF;
  width: 100%;
  font-size: 22px;
  padding: 0 20px;
  margin: 0 auto;
}

.pagetitle .maintitle .date{
  opacity: 1;
  margin: 0;
}


.main_cb_date {
  position: absolute;
  top: -41px;
  right: 230px;
  height: 30px;
  width: 98px;
  z-index: 1000;
}

.main_cb_date .combobox_label{
  padding: 4px 6px;
  background: url(../img/combobox_arr.png) no-repeat 80px center;
}

#week_date_range,
#month_date_range {
  position: absolute;
  top: -40px;
  right: 330px;
  width: 180px;
  font-size: 14px;
  text-align: center;
  border: 1px solid #1f5fa8;
  border-radius: 2px;
  padding: 3px 0 5px 0px;
  background-color: #021d39;
  box-shadow: 0 1px 8px #021121;
  z-index: 999;
  display: none;
}


.con_flex_column{ display:flex; display: -webkit-flex; -webkit-flex-flow: column;}
.con_flex_row{ display:flex; display: -webkit-flex; -webkit-flex-flow: row;}
.flex_none{-webkit-flex:none;}
.flex_1{-webkit-flex:1;}


#date_select {
  position: absolute;
  top: 109px;
  right: 45px;
  width: 178px;
  height: 34px;
  font-size: 14px;
  text-align: center;
  border-radius: 4px;
  border: 1px solid #2a81d2;
  background-color: #2a81d2;
  overflow: hidden;
  transform: rotate(0deg);
  padding-left: 1px;
}
#date_select div {
  margin: 0 1px 0 0;
  background-color: #0a4589;
  color: #5d9ae3;
  font-weight: bold;
  line-height: 32px;
  width: 43px;
  cursor: pointer;
  user-select: none;
}
#date_select .selected {
  background: none;
  color: #fff;
}

.tabcontent {
  position: absolute;
  height: 596px;
  width: 100%;
  top: 153px;

  background-color: rgba(255, 0, 0, 0);
}

#content_D .left_bg,
#content_L .left_bg {
  position: absolute;
  top: -25px;
  left: 23px;
  width: 367px;
  height: 666px;
  background: url(../img/a4.2_bg_d.png) no-repeat right 0;
}
#content_M .left_bg {
  position: absolute;
  top: -25px;
  left: 23px;
  width: 367px;
  height: 666px;
  background: url(../img/a4.2_bg_m.png) no-repeat right 0;
}
#content_Y .left_bg {
  position: absolute;
  top: -43px;
  left: 23px;
  width: 367px;
  height: 666px;
  background: url(../img/a4.2_bg_y.png) no-repeat right 0;
}

.block_lt {
  position: absolute;
  left: 30px;
  top: -26px;
  width: 375px;
  height: 110px;
}

.block_lt .itm {
  position: relative;
  display: inline-block;
  height: 48px;
  width: 160px;
  cursor: default;
  margin-right: 17px;
  margin-bottom: 4px;
  background-color: rgba(255,0,0,0);
}

.block_lt .itm .lb {
  position: absolute;
  height: 12px;
  font-size: 13px;
  width: 100%;
  top: 3px;
  text-align: center;
  padding-left: 5px;
}
.block_lt .itm .l2 {
  position: absolute;
  width: 100%;
  top: 25px;
  height: 32px;
  text-align: right; 
  padding-right:3px;
  color:#7bc9ff;
}

.block_lt .itm .l2 .val{
  line-height: 15px;
}
.block_lt .itm .l2 .hb{
  font-size: 11px;
  line-height: 10px;
  padding-right: 10px;
}

.block_lt .itm .l2 .hb.up{
  background: url(../img/arr_up2.png) no-repeat right 0;
}
.block_lt .itm .l2 .hb.down{
  background: url(../img/arr_down2.png) no-repeat right 0;
}




/* --- */

.block_llt {
  position: absolute;
  top: 89px;
  left: 37px;
  width: 364px;
  height: 260px;

}

#content_D .block_llt {
  background: rgba(255,0,0,0) url(../img/a4.2_llt_bg.png?2) no-repeat 33px 33px;
}

.block_llt .title {
  width: 100%;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  padding-top: 4px;
  text-shadow: 0 0px 5px #041242;
}

.block_llt .itm {
  position: absolute;
  height: 66px;
  width: 100px;
  text-align: center;
  cursor: pointer;
  background-color: rgba(255,0,0,0);
}

.block_llt .itm1 {
  top: 67px;
  left: 45px;
}
.block_llt .itm2 {
  top: 172px;
  left: 43px;
}
.block_llt .itm3 {
  top: 121px;
  left: 136px;
}
.block_llt .itm4 {
  top: 69px;
  left: 225px;
}
.block_llt .itm5 {
  top: 172px;
  left: 225px;
}

.block_llt .itm .lb {
  font-size: 12px;
  margin-top: 2px;
}
.block_llt .itm .l2 {
  line-height: 15px;
  margin-top: 7px;
}
.block_llt .itm .per {
  color: #80c4ff;
  font-size: 11px;
  font-weight: bold;
}
.block_llt .itm .hb {
  font-size: 10px;
  color: #80c4ff;
  padding-right: 8px;
}
.block_llt .itm .hb .v {
  color: #fff;
}
.block_llt .itm .l2 .hb.up{
  background: url(../img/arr_up2.png) no-repeat right 2px;
}
.block_llt .itm .l2 .hb.down{
  background: url(../img/arr_down2.png) no-repeat right 2px;
}

/* --- L --- */
.block_llt .ttt {
  position: absolute;
  top: 34px;
  left: 10px;
  height: 66px;
  width: 344px;
  text-align: center;
  background-color: #022c69;
}

.block_llt .litm {
  width: 25%;
  display: inline-block;
  margin-top: 4px;
  height: 58px;
  text-align: center;
  cursor: pointer;
  background-color: rgba(255,0,0,0);
}

.block_llt .litm .lb {
  font-size: 12px;
  color: #55adff;
}
.block_llt .litm .l2 {
  line-height: 15px;
  margin-top: 7px;
}
.block_llt .litm .per {
  color: #80c4ff;
  font-size: 11px;
  font-weight: bold;
}
.block_llt .litm .hb {
  color: #80c4ff;
  font-size: 11px;
  font-weight: bold;
  padding-right: 8px;
}
.block_llt .litm .hb .v {
  color: #fff;
  font-size: 11px;
  font-weight: bold;
}
.block_llt .litm .hb.up{
  background: url(../img/arr_up2.png) no-repeat right 2px;
}
.block_llt .litm .hb.down{
  background: url(../img/arr_down2.png) no-repeat right 2px;
}

.block_llt .legend_row {
  color: #4d9cfd;
}
.block_llt .legend_row span {
  display: inline-block;
  font-size: 12px;
}
.block_llt .legend_row .name{
  width: 62px;
  cursor: pointer;
}
.block_llt .legend_row .num{
  width: 36px;
  text-align: right;
}
.block_llt .legend_row .per{
  padding-left: 10px;
}

.block_llt .btns{
  position: absolute; 
  right:0px; 
  top:122px; 
  width:72px; 
  height:30px;
}
.block_llt .btn{
  display: inline-block;
  margin-right: 10px;
  height: 22px;
  width: 22px;
  padding: 0;
  border-radius: 11px;
  border: 1px solid #1a52a5;
  cursor: pointer;
}
.block_llt .btn_prev{
  background: #012869 url(../img/a4.2_btn_arr1.png) no-repeat center;
}
.block_llt .btn_next{
  background: #012869 url(../img/a4.2_btn_arr2.png) no-repeat center;
}
.block_llt .disabled{
  opacity: 0.4;
  cursor: default;
}



.block_llt .beegrid {
  position: absolute;
  height: 116px;
  width: 94px;
  text-align: center;
  color: #39a8ff;
}
.block_llt .beegrid1 {
  left: 34px;
  top: 34px;
}
.block_llt .beegrid2 {
  left: 228px;
  top: 34px;
}
.block_llt .beegrid3 {
  left: 129px;
  top: 130px;
}
.block_llt .beegrid .tt {
  font-size: 12px;
  color: white;
}
.block_llt .beegrid .tt2 {
  font-size: 11px;
}
.block_llt .beegrid .bb1 {
  top: 80px;
  left: 8px;
}
.block_llt .beegrid .bb2 {
  top: 80px;
  right: 8px;
}
.block_llt .beegrid .bb {
  position: absolute;
  font-size: 10px;
}
.block_llt .beegrid .bb span {
  display: block;
}
.block_llt .beegrid .bb .val {
  font-weight: bold;
  font-size: 11px;
}
.block_llt .beegrid .bb1 .val {
  color: #fafc44;
}
.block_llt .beegrid .bb2 .val {
  color: #57d2fe;
}
.block_llt .beegrid .chart {
  position: absolute;
  height: 46px;
  width: 82px;
  top: 36px;
  left: 5px;
  background-color: rgba(255,0,0,0);
}
.block_llt .beegrid .chart canvas{
  position: absolute;
  top: 0;
  left: 0;
}

.block_llt .punish {
  position: absolute;
  top:261px;
  left:39px;
  width: 306px;
  height: 20px;
  cursor: pointer;

}
.block_llt .punish .lb {
  font-size: 14px;
  line-height: 20px;
  margin-right: 5px;
}
.block_llt .punish .val {
  font-size: 20px;
  color: #fde802;
  font-weight: bold;
  line-height: 20px;
}

/* --- */

.block_llb {
  position: absolute;
  top: 346px;
  left: 37px;
  width: 364px;
  height: 240px;

}
.block_llb .title {
  width: 100%;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  padding-top: 4px;
}
.block_llb .blks {
  position: absolute;
  left: 126px;
  top: 48px;
  height: 140px;
  width: 220px;
  background-color: rgba(255,0,0,0);
}
.block_llb .itm {
  position: relative;
  display: inline-block;
  height: 64px;
  width: 105px;
  cursor: default;
  margin-right: 0px;
  margin-bottom: 0px;
  background-color: rgba(255,0,0,0);
  cursor: pointer;

  background-repeat: no-repeat;
  background-position: 10px 15px;
}

.block_llb .itm1 {
  background-image: url(../img/a4.2_ico_evt1.png);
}
.block_llb .itm2 {
  background-image: url(../img/a4.2_ico_evt2.png);
}
.block_llb .itm3 {
  background-image: url(../img/a4.2_ico_evt3.png);
}
.block_llb .itm4 {
  background-image: url(../img/a4.2_ico_evt4.png);
}

.block_llb .itm .lb {
  position: absolute;
  height: 12px;
  font-size: 14px;
  font-weight: bold;
  width: 100%;
  top: 36px;
  left: 6px;
  color: #4d9cfd;
}
.block_llb .itm .l2 {
  position: absolute;
  width: 70px;
  top: 15px;
  left: 42px;
  height: 60px;
}

.block_llb .itm .l2 .val{
  font-size: 14px;
  margin-right: 2px;
}
.block_llb .itm .l2 .un{
  color: #379dfc;
}
.block_llb .itm .l2 .per{
  display: block;
  font-size: 14px;
}

.block_llb .ll {
  position: absolute;
  width: 88px;
  top: 56px;
  left: 20px;
  height: 130px;
  text-align: center;
}
.block_llb .ll .vv {
  padding-top: 20px;
  margin-left: 11px;
}
.block_llb .ll .vv .v {
  font-size: 36px;
  font-weight: bold;
}
.block_llb .ll .vv .sub {
  font-size: 12px;
  color: #379dfc;
}

.block_llb .ll .hb {
  font-size: 12px;
  color: #80c4ff;
  padding-right: 8px;
}
.block_llb .ll .hb .v {
  color: #fff;
}
.block_llb .ll .hb.up{
  background: url(../img/arr_up2.png) no-repeat right 2px;
}
.block_llb .ll .hb.down{
  background: url(../img/arr_down2.png) no-repeat right 2px;
}

.block_llb .tt {
  font-size: 14px;
  font-weight: bold;
  margin-top: 10px;
}

.block_llb .bot {
  position: absolute;
  height: 33px;
  width: 342px;
  left: 13px;
  bottom: 6px;
  background-color: rgba(255,0,0,0);
}
.block_llb .bot .vv {
  position: relative;
  display: inline-block;
  height: 100%;
  width: 160px;
  text-align: center;
  cursor: pointer;
}
.block_llb .bot .vv .val{
  font-size: 22px;
  color: #fe7200;
  vertical-align: middle;
}
.block_llb .bot .vv .lb{
  line-height: 23px;
  color: #55adff;
  vertical-align: middle;
}


.block_llb .ball1{
  position: absolute;
  width: 55px;
  height: 66px;
  left: 38px;
  top: 43px;
  text-align: center;
}
.block_llb .ball1 .lb{
  font-size: 11px;
  color: white;
}
.block_llb .ball1 .val{
  font-size: 20px;
  color: #a0d6ff;
}

.block_llb .ball2{
  position: absolute;
  width: 46px;
  height: 66px;
  left: 97px;
  top: 54px;
  text-align: center;
}
.block_llb .ball2 .lb{
  font-size: 11px;
  color: white;
}
.block_llb .ball2 .hb{
  font-size: 12px;
  color: #a0d6ff;
}


.block_llb .ball3{
  position: absolute;
  width: 55px;
  height: 66px;
  left: 206px;
  top: 49px;
  text-align: center;
}
.block_llb .ball3 .lb{
  font-size: 11px;
  color: white;
}
.block_llb .ball3 .val{
  font-size: 20px;
  color: #37ffb6;
}

.block_llb .ball4{
  position: absolute;
  width: 55px;
  height: 66px;
  left: 265px;
  top: 54px;
  text-align: center;
}
.block_llb .ball4 .lb{
  font-size: 11px;
  color: white;
}
.block_llb .ball4 .hb{
  font-size: 12px;
  color: #37ffb6;
}

.block_llb .bottom {
  position: absolute;
  width: 200px;
  height: 20px;
  color: #55adff;
  font-size: 12px;
}
.block_llb .bottom1 {
  top: 118px;
  left: 13px;
}
.block_llb .bottom2 {
  top: 118px;
  left: 191px;
}
.block_llb .bottom .val {
  color: #fe7200;
  font-weight: bold;
}
.block_llb .bottom .hb {
  font-size: 10px;
  padding-right: 9px;
}

.block_llb .hb.up{
  background: url(../img/arr_up2.png) no-repeat right 2px;
}
.block_llb .hb.down{
  background: url(../img/arr_down2.png) no-repeat right 2px;
}

.block_llb .corelb {
  position: absolute;
  top: 155px;
  right: 16px;
  font-size: 10px;
  color: #0963a9;
}
.block_llb .lb1{
  color: #7cfa38;
}
.block_llb .lb2{
  color: #ffe718;
}
.core_risk_scrollpane {
  position: absolute;
  left: 10px;
  width: 341px;
  height: 200px;
  overflow: hidden;
  background-color: rgba(255,0,0,0);
}
.core_risk_scrollpane .scrollcontent {
  width: 100%;
}
.core_risk_scrollpane .scrollcontent .rowline{
  width: 100%;
  height: 16px;
  overflow: hidden;
  margin-bottom: 6px;
}
.core_risk_scrollpane .scrollcontent .rowline .name{
  display: inline-block;
  width: 100px;
  height: 16px;
  text-align: right;
  padding-right: 8px;
  font-size: 12px;
  line-height: 16px;
  color: #55adff;
  overflow : hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}
.core_risk_scrollpane .scrollcontent .rowline .barbg{
  position: relative;
  display: inline-block;
  width: 169px;
  height: 16px;
  background-color: #0c1947;
  box-shadow: 0 0px 5px #006dd2 inset;
}
.core_risk_scrollpane .scrollcontent .rowline .barwrap{
  position: absolute;
  top: 4px;
  left: 4px;
  width: 161px;
  height: 9px;
}
.core_risk_scrollpane .scrollcontent .rowline .barend{
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background-color: #f6c322;
}
.core_risk_scrollpane .scrollcontent .rowline .bar{
  position: absolute;
  top: 0;
  left: 0;
  width: 100px;
  height: 100%;
background: #044ed1;
background: -moz-linear-gradient(left,  #044ed1 0%, #7bf83a 100%);
background: -webkit-linear-gradient(left,  #044ed1 0%,#7bf83a 100%);
background: linear-gradient(to right,  #044ed1 0%,#7bf83a 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#044ed1', endColorstr='#7bf83a',GradientType=1 );

}

.core_risk_scrollpane .scrollcontent .rowline .vals{
  display: inline-block;
  height: 16px;
  text-align: left;
  font-size: 10px;
  line-height: 16px;
  color: #0963a9;
}
.core_risk_scrollpane .scrollcontent .rowline .vals .v1{
  display: inline-block;
  width: 28px;
  text-align: right;
  color: #7cfa38;
}
.core_risk_scrollpane .scrollcontent .rowline .vals .v2{
  display: inline-block;
  width: 28px;
  text-align: left;
  color: #ffe718;
}





/* --- */

.block_mid {
  position: absolute;
  top: -17px;
  left: 407px;
  width: 500px;
  height: 610px;
  background-color: rgba(255,0,0,0);
}
.block_mid .tabs {
  margin: 0 auto;
  margin-top: 6px;
  width: 304px;
  height: 26px;
  text-align: center;
  border-radius: 4px;
  border: 1px solid #2a81d2;
  background-color: #2a81d2;
  overflow: hidden;
  transform: rotate(0deg);
  padding-left: 1px;
}
.block_mid .tabs div {
  margin: 0 1px 0 0;
  background-color: #083781;
  color: #5d9ae3;
  font-weight: bold;
  line-height: 24px;
  width: 100px;
  cursor: pointer;
  user-select: none;
}
.block_mid .tabs .selected {
  background: none;
  color: #fff;
}

.block_mid .tabc {
  position: absolute;
  left: 0px;
  top: 36px;
  width: 100%;
  height: 570px;
  
}


/* --- */

.block_r {
  position: absolute;
  top: 0px;
  right: 29px;
  width: 411px;
  height: 587px;
}
.block_r .title {
  position: absolute;
  left: 58px;
  top: 19px;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
}
.block_r .tabs {
  position: absolute;
  left: 154px;
  top: 17px;
  height: 26px;
  text-align: center;
  border-radius: 4px;
  border: 1px solid #2a81d2;
  background-color: #2a81d2;
  overflow: hidden;
  transform: rotate(0deg);
  padding-left: 1px;
}
.block_r .tabs div {
  margin: 0 1px 0 0;
  background-color: #083781;
  color: #5d9ae3;
  font-weight: bold;
  line-height: 24px;
  width: 60px;
  cursor: pointer;
  user-select: none;
}
.block_r .tabs .selected {
  background: none;
  color: #fff;
}

.block_r .tabc {
  position: absolute;
  left: 0px;
  top: 36px;
  width: 100%;
  height: 570px;
  pointer-events: none;
}

.block_r .tabc1 .legend {
  position: absolute;
  left: 3px;
  top: 24px;
  font-size: 12px;
}
.block_r .tabc1 .legend .itm {
  margin-right: 3px;
  display: inline-block;
  cursor: pointer;
  user-select: none;
}
.block_r .tabc1 .legend .dot {
  font-size: 20px;
  vertical-align: middle;
}
.block_r .tabc1 .legend .lb {
  color: #5d9ae3;
  vertical-align: middle;
}

.block_r .tabc2 .legend {
  position: absolute;
  left: 3px;
  top: 24px;
  font-size: 12px;
}
.block_r .tabc1 .legend .lbl,
.block_r .tabc2 .legend .lbl {
  position: absolute;
  top: 5px;
  left: 0;
  color: #000e34;
  background-color: #75b6ff;
  text-align: center;
  border-radius: 3px;
  padding: 2px;
}
.block_r .tabc2 .legend .itm {
  margin-right: 6px;
  cursor: pointer;
  user-select: none;
}
.block_r .tabc2 .legend .dot {
  font-size: 20px;
  vertical-align: middle;
}
.block_r .tabc2 .legend .lb {
  color: #5d9ae3;
  vertical-align: middle;
}
.block_r .tabc2 .legend .lst {
  display: block;
  margin-left: 24px;
  line-height: 22px;
}
.block_r .tabc2 .legend .lst2{
  display: block;
  margin-left: 24px;
  line-height: 22px;
}


.block_r .myfav {
  position: absolute;
  display: block;
  left: 0px;
  top: 75px;
  width: 396px;
  height: 34px;
  background: #00276d url(../img/a4.2_star.png) no-repeat 6px 10px;
  padding: 4px 0 0 25px;
}
.block_r .allfav {
  top: 110px;
}
.block_r .myfav .tt {
  font-size: 16px;
  font-weight: bold;
  margin-right: 5px;
}
.block_r .myfav .itm {
  display: inline-block;
  font-size: 12px;
  margin-right: 3px;
  width: 70px;
  height: 20px;
  line-height: 16px;
  background-color: #0147ca;
  border: 1px solid #2a81d2;
  border-radius: 3px;
  color: #fff;
  padding: 0 4px 3px 4px;
  overflow: hidden;
}
.block_r .myfav .dot {
  font-size: 16px;
}
.block_r .myfav .lb {
  font-weight: bold;
}
.block_r .myfav .lst {
  position: absolute;
  display: inline-block;
  width: 220px;
  height: 30px;
  top: 3px;
  left: 127px;
  padding-top: 2px;
  overflow: hidden;
}
.block_r .myfav .lst .cont {
  position: absolute;
  display: block;
  width: 9999px;
  height: 30px;
}
.block_r .myfav .btn_fav_L{
  position: absolute;
  top: 8px;
  right: 24px;
  height: 18px;
  width: 18px;
  padding: 0;
  border-radius: 11px;
  border: 1px solid #1a52a5;
  cursor: pointer;
  background: #012869 url(../img/a4.2_btn_arr1.png) no-repeat center;
  display: none;
  
}
.block_r .myfav .btn_fav_R{
  position: absolute;
  top: 8px;
  right: 3px;
  height: 18px;
  width: 18px;
  padding: 0;
  border-radius: 11px;
  border: 1px solid #1a52a5;
  cursor: pointer;
  background: #012869 url(../img/a4.2_btn_arr2.png) no-repeat center;
  display: none;
  
}

.block_r .scrollpane {
  position: absolute;
  top: 120px; /*150px;*/
  left: 0;
  width: 100%;
  height: 430px; /*400px;*/
  overflow: hidden;
  background-color: rgba(255,0,0,0);
}
.block_r .scrollcontent {
  width: 100%;
}
.block_r .scrollcontent .comprow{
  width: 400px;
  height: 136px;
  overflow: hidden;
  transform: rotate(0deg);
}
.block_r .scrollcontent .comprow .head{
  width: 100%;
  height: 32px;
}
.block_r .scrollcontent .comprow .head .tt{
  position: absolute;
  left: 0;
  width: 200px;
  height: 24px;
  color: #7dc0ff;
  line-height: 25px;
  background-position: 0 0;
  background-repeat: no-repeat;
  background-size: contain;
}
.block_r .scrollcontent .comprow .head .tt .lb{
  margin-left: 32px;
  font-size: 14px;
  font-weight: bold;
}
.block_r .scrollcontent .comprow .head .tt .num{
  margin-left: 5px;
  font-size: 12px;
}

.block_r .scrollcontent .comprow .head .btns{
  float: right;
  text-align: right;
  margin-right: 15px;
}
.block_r .scrollcontent .comprow .head .btn{
  display: inline-block;
  margin-right: 10px;
  height: 22px;
  width: 22px;
  padding: 0;
  border-radius: 11px;
  border: 1px solid #1a52a5;
  cursor: pointer;
}
.block_r .scrollcontent .comprow .head .btn_prev{
  background: #012869 url(../img/a4.2_btn_arr1.png) no-repeat center;
}
.block_r .scrollcontent .comprow .head .btn_next{
  background: #012869 url(../img/a4.2_btn_arr2.png) no-repeat center;
}
.block_r .scrollcontent .comprow .head .disabled{
  opacity: 0.4;
  cursor: default;
}
.block_r .scrollcontent .comprow .itmlst{
  position: absolute;
  top: 35px;
  left: 0;
  height: 88px;
  width: 200000px;
}

.block_r .scrollcontent .comprow .itmlst .blk{
  position: relative;
  display: inline-block;
  width: 76px;
  height: 88px;
  margin-right: 5px;
  border-radius: 5px;
  overflow: hidden;
  transform: rotate(0);
  cursor: pointer;

  box-shadow: 0 0px 2px #000616, 0 0px 2px rgba(22,122,217,0.3) inset;

background: #194285;
background: -moz-linear-gradient(top,  #194285 0%, #043262 100%);
background: -webkit-linear-gradient(top,  #194285 0%,#043262 100%);
background: linear-gradient(to bottom,  #194285 0%,#043262 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#194285', endColorstr='#043262',GradientType=0 );

}
.block_r .scrollcontent .comprow .itmlst .blk .time{
  color: #4d9cfd;
  font-size: 11px;
  margin: 6px 5px 3px 5px;
}
.block_r .scrollcontent .comprow .itmlst .blk .time .r{
  float: right;
}
.block_r .scrollcontent .comprow .itmlst .blk .fltno{
  text-align: center;
  font-size: 14px;
  font-weight: bold;
  color: #aecffd;
  margin: 3px ;
}
.block_r .scrollcontent .comprow .itmlst .blk .city{
  width: 100%;
  text-align: center;
  font-size: 11px;
  color: #4d9cfd;
  height: 22px;
}
.block_r .scrollcontent .comprow .itmlst .blk .bot{
  position: absolute;
  width: 100%;
  bottom: 4px;
  text-align: center;
  font-size: 10px;
  color: #046cd6;
  text-overflow:ellipsis;
  white-space: nowrap;
}



/* ------------------------- */

.pop{
  position: absolute;
  width: 260px;
  height: 500px;
  box-shadow: 0 0 2px #000, 0 0 10px rgba(0,0,0,0.5);
  border-radius: 5px;
}
.pop .scrollpane{
  position: absolute;
  width: 100%;
  height: 470px;
  background-color: #bce0fe;
  border-bottom-right-radius: 5px;
  border-bottom-left-radius: 5px;
  transform: rotate(0);
  overflow: hidden;
}
.pop .cont{
  position: relative;
  width: 100%;
}
.pop .arr{
  position: absolute;
  height: 16px;
  width: 16px;
  top: 100px;
  right: -6px;
  background-color: #bce0fe;
  transform: rotate(45deg);
}
.pop .head{
  position: relative;
  height: 30px;
  background-color: #90cffe;
  border-bottom: 1px solid #5aa3f1;
  line-height: 30px;
  color: #031c57;
  border-top-right-radius: 5px;
  border-top-left-radius: 5px;
  transform: rotate(0);
  overflow: hidden;
}

.pop .head .fltno{
  display: inline-block;
  font-size: 14px;
  font-weight: bold;
  margin-left: 12px;
  padding-left: 22px;
  line-height: 17px;
  height: 16px;
  background-position: 0 0;
  background-repeat: no-repeat;
  background-size: contain;
}
.pop .head .tag{
  display: inline-block;
  border-radius: 9px;
  height: 16px;
  line-height: 18px;
  padding: 0 6px;
  font-size: 12px;
  font-weight: bold;
  margin-left: 6px;
}
.pop .head .btnx{
  position: absolute;
  display: inline-block;
  width: 18px;
  height: 18px;
  top: 6px;
  right: 7px;
  cursor: pointer;
  background: url(../img/a4.2_pop_x.png) no-repeat center;
}

.pop .row{
  position: relative;
  padding: 7px 12px;
  margin: 0;
  border-bottom: 1px solid #98c7f7;
  min-height: 30px;
  color: #115096;
  font-size: 12px;
  background-color: #bce0fe;
}
.pop .row_w{
  background-color: #fff;
}
.pop .botrow{
  border-bottom-right-radius: 5px;
  border-bottom-left-radius: 5px;
  transform: rotate(0);
  overflow: hidden;
  border: none;
}

.pop .row1 .date{
  display: inline-block;
  padding-left: 20px;
  margin-right: 10px;
  line-height: 20px;
  background: url(../img/a4.2_ico_cal.png) no-repeat left center;
}
.pop .row1 .ac{
  display: inline-block;
  padding-left: 22px;
  margin-right: 10px;
  line-height: 20px;
  background: url(../img/a4.2_ico_plane.png) no-repeat left center;
}
.pop .row1 .cabin{
  display: inline-block;
  padding-left: 16px;
  line-height: 20px;
  background: url(../img/a4.2_ico_seat.png) no-repeat left center;
}
.pop .label{
  font-size: 12px;
  font-weight: normal;
  color: #508cc9;
  margin: 0;
  padding: 0;
}

.pop .cities{
  text-align: center;
  height: 72px;
}
.pop .cities .lbl{
  position: relative;
  width: 22px;
}
.pop .cities .lbl .lb1{
  position: absolute;
  width: 34px;
  top: 19px;
  left: 0;
  display: inline-block;
  border: 1px solid #73ae03;
  color: #73ae03;
  background-color: #FFF;
  border-radius: 3px;
}
#pop_tab1 .cities .lbl .lb2{
  position: absolute;
  width: 34px;
  top: 40px;
  left: 0;
  display: inline-block;
  border: 1px solid #ab6a00;
  color: #ab6a00;
  background-color: #FFF;
  border-radius: 3px;
}
#pop_tab2 .cities .lbl .lb2{
  position: absolute;
  width: 34px;
  top: 40px;
  left: 0;
  display: inline-block;
  border: 1px solid #0058f8;
  color: #0058f8;
  background-color: #FFF;
  border-radius: 3px;
}
.pop .cities .nm{
  position: relative;
  display: inline-block;
  background: #bce0fe;
  padding: 0 5px;
}
.pop .cities .tm{
  display: block;
  font-weight: bold;
  margin-top: 6px;
}
.pop .cities .tm2{
  color:#ab6a00;
  display: block;
  font-weight: bold;
  margin-top: 3px;
}
.pop .cities .stop{
  position: relative;
}
.pop .cities .line{
  position: absolute;
  display: block;
  width: 100%;
  left: 0px;
  top: 7px;
  border-top: 1px solid #3d98e1;
  z-index: 0;
}
.pop .cities .line .dot{
  position: absolute;
  display: block;
  width: 5px;
  height: 5px;
  background: #3d98e1;
  border-radius: 50%;
}
.pop .cities .line .dot1{
  top: -3px;
  left: -2px;
}
.pop .cities .line .dot2{
  top: -3px;
  right: -2px;
}

.pop .blktit {
  margin: 12px 0 8px 8px;
  border-left: 4px solid #185a9e;
  color: #185a9e;
  height: 14px;
  line-height: 14px;
  padding-left: 8px;
  background-color: #bce0fe;
}
.pop .blkcon {
  margin-left: 8px;
  margin-right: 8px;
  border: 1px solid #98c7f7;
  border-bottom: none;
  margin-bottom: 12px;
  color: #115096;
  font-size: 12px;
  background-color: #dcefff;
}
.pop .blkcon .blkrow{
  width: 100%;
  border-bottom: 1px solid #98c7f7;
  padding: 7px 8px;
}
.pop .blkcon .label {
  display: block;
  text-align: left;
  margin-bottom: 4px;
}
.pop .blkcon .tag{
  text-align: center;
  display: inline-block;
  border-radius: 9px;
  height: 16px;
  line-height: 18px;
  padding: 0 6px;
  font-size: 12px;
  color: #000;
}
.pop .line2{
  display: block;
  font-size: 10px;
  margin-top: 5px;
}


.up_red{
  background: url(../img/arr_up_red.png) no-repeat right 2px;
}
.down_green{
  background: url(../img/arr_down_green.png) no-repeat right 2px;
}

