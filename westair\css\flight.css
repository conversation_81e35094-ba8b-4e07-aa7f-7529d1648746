/**公共**/
.con_flex_column{ display: -webkit-flex; -webkit-flex-flow: column;}
.con_flex_row{ display: -webkit-flex; -webkit-flex-flow: row;}
.con_flex_row div{position: initial;}
.con_flex_row .c4{
	line-height: 35px;
}
.con_flex_row .c4 .fs12{
	text-align: center;
	line-height: 20px;
}
.con_flex_row .c2{
	justify-content: center;
}
.con_flex_row .c2 .t2,.con_flex_row .c2 .t1{
	text-align: center;
}
.flex_none{-webkit-flex:none;}
.flex_1{-webkit-flex:1;}
.blue {
	color:#77BAF8;
}
.green {
	color:#9FDD00;
}
/** 背景 **/
.page-bgimg {
  position: absolute;
  top: 0px;
  width: 2133px;
  height: 768px;
  background: url(../img/flight.bg.png?1) no-repeat top center;
  background-size: 100%;
  overflow-x: hidden;
  overflow-y: hidden;
  pointer-events: none;
}

.page-wrapper {
  position: absolute;
  top: 0px;
  width: 2133px;
  height: 768px;
  overflow-x: hidden;
  overflow-y: hidden;
  pointer-events: none;
}
/** 航班信息 start**/
.col_l {
	pointer-events: auto;
	position: absolute;
	top: 99px;
	left: 80px;
	width: 259px;
	height: 545px;
	background-color: rgba(255,0,0,0.0);
}

.col_l .tt {
	position: absolute;
	top: 1px;
	left: 96px;
	background-color: rgba(255,0,0,0.0);
	color: #fff;
	font-size: 18px;
}

.col_l .fltno {
	position: absolute;
	top: 36px;
	left: 10px;
	background-color: rgba(255,0,0,0.0);
	color: #FFF;
	font-size: 18px;
	padding-left: 24px;
	background-repeat: no-repeat;
	background-position: 0 center;
	background-size: auto 16px;
}
.col_l .acimg {
	position: absolute;
	top: 75px;
	width: 100%;
	height: 150px;
	background-color: rgba(255,255,255,0.05);
	background-repeat: no-repeat;
	background-position: center;
	background-size: cover;
}

.col_l .blk1 {
	position: absolute;
	top: 44%;
	left: 2%;
	width: 96%;
	background-color: rgba(255,0,0,0.0);
}

.col_l .blk1 .row1 {
	position: relative;
	width: 100%;
}
.col_l .blk1 .row1 .c1{
	position: relative;
}
.col_l .blk1 .row1 .c2{
	position: relative;
	text-align: center;
}
.col_l .blk1 .row1 .c2 .arr{
	width: 100%;
	height: 20px;
	background: url(../img/flight.arr.png?1) no-repeat top center;
}
.col_l .blk1 .row1 .c3{
	position: relative;
	text-align: right;
}
.col_l .blk1 .row1 .t1{
	position: relative;
	font-size: 18px;
}
.col_l .blk1 .row1 .t2{
	position: relative;
	font-size: 14px;
	color: #77BAF8;
	white-space:nowrap;
}

.col_l .blk1 .row2 {
	position: relative;
	width: 100%;
	margin-top: 23px;
}
.col_l .blk1 .row2 .c1{
	position: relative;
}
.col_l .blk1 .row2 .c3{
	position: relative;
	text-align: right;
}
.col_l .blk1 .row2 .t1{
	position: relative;
	font-size: 16px;
}
.col_l .blk1 .row2 .t2{
	position: relative;
	font-size: 14px;
	color: #77BAF8;
}

.col_l .blk2 {
	position: absolute;
	left: 2%;
	width: 100%;
	background-color: rgba(255,0,0,0.0);
}
.col_l .blk2 .row1 {
	display: -webkit-box;
	position: relative;
	width: 100%;
}
.col_l .blk2 .row2 {
	position: relative;
	width: 100%;
	margin-top: 19px;
}
.col_l .blk2 .row1 .c1{
	 position: relative;
 }
.col_l .blk2 .row1 .c2{
	position: relative;
	width:42%;
	text-align: right;
}
.col_l .blk2 .t1{
	position: relative;
	font-size: 16px;
}
.col_l .blk2 .t2{
	position: relative;
	font-size: 14px;
	color: #77BAF8;
}
/** 航班信息 end**/

/** 油量 start**/

.col_l2 {
	pointer-events: none;
	position: absolute;
	top: 14.6%;
	left: 18.4%;
	width: 292px;
	height: 600px;
	background-color: rgba(255,0,0,0.0);
}

.col_l2 div{
	position: relative;
}

.col_l2 .ttt{
	font-size: 14px;
	color: #77BAF8;
}

.col_l2 .blk1{
	position: absolute;
	top: 7px;
	left: 51px;
	width: 100%;
	background-color: rgba(255,0,0,0.0);
}

.col_l2 .blk2{
	position: absolute;
	top: 89px;
	left: 51px;
	width: 100%;
	background-color: rgba(255,0,0,0.0);
}

.col_l2 .blk3{
	position: absolute;
	top: 168px;
	left: 51px;
	width: 100%;
	background-color: rgba(255,0,0,0.0);
}

.col_l2 .oiltt{
	position: absolute;
	top: 347px;
	left: 56px;
	font-size: 16px;
	font-weight: bold;
	background-color: rgba(255,0,0,0.0);
}

.col_l2 .blk4{
	position: absolute;
	top: 390px;
	width: 100%;
	background-color: rgba(255,0,0,0.0);
}
.col_l2 .blk4 .ttt{
	margin-top: 13px;
}
/**油量 end **/

/** 顶部 **/
.col_top {
	pointer-events: auto;
	position: absolute;
	top: 10px;
	left: 34%;
	width: 710px;
	height: 66px;
	background-color: rgba(255,0,0,0.0);
}

.col_top div{
	position: relative;
}

.col_top .co1{
	text-align: center;
}
.col_top .co3{
	text-align: center;
}

.col_top .mid .c1{
	position: relative;
}
.col_top .mid .c2{
	position: relative;
	text-align: center;
	width: 200px;
}
.col_top .mid .c2 .arr{
	width: 100%;
	height: 18px;
	background: url(../img/flight.arr2.png?1) no-repeat top center;
}
.col_top .mid .c3{
	position: relative;
	text-align: right;
}
.col_top .mid .t1{
	position: relative;
	font-size: 16px;
	font-weight: bold;
}
.col_top .mid .t2{
	position: relative;
	font-size: 14px;
	color: #77BAF8;
}

.col_top .fltno{
	display: inline-block;
	height: 40px;
	font-size: 18px;
	font-weight: bold;
	line-height: 44px;
	padding-left: 96px;
	background-repeat: no-repeat;
	background-position: 0 center;
	background-size: auto 40px;
}


.col_top .sts{
	display: inline-block;
	height: 40px;
	font-size: 18px;
	font-weight: bold;
	line-height: 40px;
	margin-top: 4px;
	padding-left: 42px;

	background-repeat: no-repeat;
	background-position: 0 center;
}

.col_top .status1{
	color: #76FF00;
	background-image: url(../img/flight.ac_green.png?1);
}
.col_top .status2{
	color: #FFFF00;
	background-image: url(../img/flight.ac_yellow.png?1);
}

.col_top .flightStatus1{
	color: #76FF00;
}
.col_top .flightStatus2{
	color: #FFFF00;
}
.col_top .flightStatus3{
	color: #666666;
}
/** 顶部end **/

/** 底部 **/
.col_bot {
	pointer-events: auto;
	position: absolute;
	bottom: 0px;
	left: 24.4%;
	width: 500px;
	height: 150px;
	background-color: rgba(255,0,0,0.0);
}

.col_bot div{
	position: relative;
}
.col_bot .mid .cabin{
	position: absolute;
	top: 0px;
	left: 62px;
	width: 550px;
	height: 120px;

	background-repeat: no-repeat;
	background-position: 0 0;
}
.col_bot .mid .cabin.CY{
	background-image: url('../../img/a3.3.plane_layout_N_CY.png');
}
.col_bot .mid .cabin.Y{
	background-image: url('../../img/a3.3.plane_layout_N_Y.png');
}
.col_bot .mid .cabin.W_CY{
	background-image: url('../../img/a3.3.plane_layout_W_CY.png');
}
.col_bot .mid .cabin.W_Y{
	background-image: url('../../img/a3.3.plane_layout_W_Y.png');
}
.col_bot .mid .cabin .lb{
	position: absolute;
	top: 60px;
	left: 30px;
	font-size: 16px;
	font-weight: bold;
}
.col_bot .mid .tt{
	position: absolute;
	top: 2px;
	left: 46px;
	font-size: 18px;
	font-weight: bold;
}

.col_bot .co3 .chart{
	position: absolute;
	top: 12px;
	left: 43px;
	width: 110px;
	height: 110px;
}
.col_bot .co3 .lb_rate1{
	position: absolute;
	top: 27px;
	left: 170px;
}
.col_bot .co3 .lb2{
	position: absolute;
	top: 83px;
	left: 170px;
}
.up_arr{
	display: inline-block;
	height: 16px;
	width: 10px;
	background: url(../img/arr_up2.png) no-repeat center;
}
.down_arr{
	display: inline-block;
	height: 16px;
	width: 10px;
	background: url(../img/arr_down2.png) no-repeat center;
}
/** 底部 end**/

/** 天气预报 **/
.col_wea {
	pointer-events: auto;
	position: absolute;
	top: 67px;
	left: 67%;
	width: 380px;
	background-color: rgba(255,0,0,0.0);
}

.col_wea div{
	position: relative;
}

.col_wea .lb{
	height: 28px;
}

.col_wea .blk{
	padding: 1px;
	/*background-color: #0F377C;*/
	margin-bottom: 1px;
	cursor: pointer;
}

.col_wea .blk .tt{
	color: #fff;
	height: 24px;
	line-height: 24px;
	font-weight: bold;
}
.col_wea .blk .weather_city1 .plane,
.col_wea .blk .weather_city2 .plane{
	font-size:16px;
}
.col_wea .weather_city1 .tt .line,
.col_wea .weather_city2 .tt .line {
	width: 72%;
	float: right;
	position: relative;
	top: 50%;
	height: 1px;
	border-top: solid #7182d5 1px;
}

.col_wea .blk .cont{
	margin-top:10px;
	height: auto;
	overflow: hidden;
	-moz-transition: all .3s;
	-webkit-transition: all .3s;
	-o-transition: all .3s;
	-ms-transition: all .3s;
	transition: all .3s;
}
.col_wea .blk .cont .c1{
	height: 60px;
	padding: 15px 10px 5px 19px;
}
.col_wea .blk .cont .c2{
	/*padding: 15px 0 5px 0;*/
}

.col_wea .table_wea{
	width:100%
}
.col_wea .table_wea tr{
	height: 30px;
}
.col_wea .table_wea .title {
	font-size: 15px;
	color: #7182d5;
}
.col_wea .table_wea .wea_content {
	font-size: 17px;
	color: #a5cdfc;
}
.col_wea .weather_city2 {
	margin-top:13px
}
.col_wea .airport_run {
	margin-top:13px
}
.col_wea .airport_run .tt .l_line{
	width: 30%;
	float: left;
	position: relative;
	top: 50%;
	height: 1px;
	border-top: solid #7182d5 1px;
}
.col_wea .airport_run .tt .r_line{
	width: 30%;
	float: right;
	position: relative;
	top: 50%;
	height: 1px;
	border-top: solid #7182d5 1px;
}
.col_wea .airport_run .plane{
	font-size:15px;
	text-align: center;
	display: inline-block;
	width: 40%;
}

.col_wea .airport_run .table_runway{
	width:100%;
	text-align: center;
	border: solid #7182d5 1px;
}
.col_wea .airport_run .table_runway tr{
	height: 36px;
	border-bottom: solid #7182d5 1px;
}
.col_wea .airport_run .table_runway tr:nth-child(odd){
	background: #1d2357;
}
.col_wea .airport_run .table_runway tr:first-child {
	font-size: 15px;
	color: #a5cdfc;
}
.col_wea .airport_run .table_runway td{
	font-size: 13px;
	word-break: break-all;
	border-right: solid #7182d5 1px;
}
.col_wea .airport_run .table_runway th{
	width: 25%;
	text-align: center;
	font-size: 15px;
	color: #a5cdfc;
	border-right: solid #7182d5 1px;
}
.weather_ico {
	font-size: 40px;
}
/** 天气预报end **/

/** 机组信息 **/
.col_r {
	pointer-events: auto;
	position: absolute;
	top: 95px;
	left: 85.5%;
	width: 292px;
	background-color: rgba(255,0,0,0.0);
}

.col_r div{
	position: relative;
}

.col_r .blk1 {
	top: 0px;
}
.col_r .blk2 {
	top: 152px;
}
.col_r .blk3 {
	top: 339px;
}
.col_r .blk {
	width: 100%;
	position: absolute;
}

.col_r .blk .tt{
	font-size: 16px;
	font-weight: bold;
	margin: 6px 6px 6px -23px;
	text-align: center;

}
.col_r .blk table{
	margin: 8px;
}
.col_r .blk table td{
	padding: 3px 6px 0 0;
}


.col_r .btn{
	position: absolute;
	display: inline-block;
	margin-right: 10px;
	height: 22px;
	width: 22px;
	padding: 0;
	border-radius: 11px;
	border: 1px solid #1a52a5;
	cursor: pointer;

	top: 173px;
}
.col_r .btn_prev{
	left: 212px;
	background: #012869 url(../../img/a4.2_btn_arr1.png) no-repeat center;
}
.col_r .btn_next{
	left: 243px;
	background: #012869 url(../../img/a4.2_btn_arr2.png) no-repeat center;
}
.col_r .disabled{
	opacity: 0.4;
	pointer-events: none;
}
.captain {
	white-space: nowrap;
}
/** 机组信息end **/

#map{
	pointer-events: auto !important;
}



