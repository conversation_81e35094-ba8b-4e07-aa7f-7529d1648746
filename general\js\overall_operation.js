const myChart = echarts.init(document.getElementById('map'));
const airplanChart = echarts.init(document.getElementById('airplan_map'));
const parent_company = 'HNAHK';
// 可显示的历史 数量
const query_limit = 60;
const actypelist = '787,767,330,333,737,320,321,319,145,190'; //机型
const comp_cause = ['飞机故障', '运力调配', '工程机务', '航班计划', '航材保障', '航务保障', '机组保障', '飞行机组保障', '乘务组', '乘务组保障', '空警安全员', '地面保障', '货运保障', '公司原因', '其他航空公司原因'];
const none_cause = ['公共安全', '机场', '军事活动', '空管', '离港系统', '联检', '旅客', '民航局航班时刻安排', '天气原因', '油料'];
let date_type = "L"; //默认日期类型 周
// 获取例会周对应的日期范围
let weekDateRangeList = new Array();
let current_company_code; //当前公司代码
let all_comp_codelist = new Array(); //所有公司编码
let codelist_no_parent = new Array(); //
let all_company_data = new Object(); //FAC_COMP_KPI
let all_company_actype_data = new Object(); //FAC_COMP_ACTYPE_KPI
let all_company_arp_flight_base_int_data = new Object();//FAC_COMP_ARP_FLIGHT_KPI
let all_company_arp_flight_data = new Object();//FAC_COMP_ARP_FLIGHT_KPI
let all_company_arp_delay_cause_data = new Object(); //FAC_COMP_ARP_DELAY_CAUSE_KPI
let all_company_delay_cause_data = new Object(); //FAC_COMP_DELAY_CAUSE_KPI
let setting; //后台指标
let mediaList = new Array(); //媒体播放列表
let currentMediaId = 0;
let tmo_media;
let baseintList = new Array();//境外办事处
let arpcodes = new Array();
let arpdetails = new Array();
let scrollInterval;
let scrollIndex = 0; //境外办事处轮播下标/bi/web/getStandardFocFlightInfo
let actypeMapList = new Object(); //机型对应关系
let ac_data_list = new Object();//机型 数量
let lost_ac_map = new Array(); //没有对应机型
let org_arp_base = new Object(); //分公司基地关系
let weekMaps = new Object();
let click_param; //记录点击参数
const colors = ['blue', 'orange', 'green', 'red'];
const planinfos = {
    'blue': '../general/img/blue_air_plan.png',
    'orange': '../general/img/orange_air_plan.png',
    'green': '../general/img/green_air_plan.png',
    'red': '../general/img/red_air_plan.png'
};

const option = {
    tooltip: {
        show: true,
    },
    geo3D: {
        map: 'china',
        roam: true,
        itemStyle: {
            color: '#0050ae',
            opacity: 1,
            borderWidth: 0.4,
            borderColor: '#2ed8e9'
        },
        emphasis: { //当鼠标放上去  地区区域是否显示名称
            label: {
                show: true,
                textStyle: {
                    color: '#fff',
                    fontSize: 3,
                    backgroundColor: 'rgba(0,23,11,0)'
                }
            }
        },
        light: { //光照阴影
            main: {
                color: '#fff', //光照颜色
                intensity: 1.2, //光照强度
                shadowQuality: 'high', //阴影亮度
                shadow: true, //是否显示阴影
                alpha: 55,
                beta: 10

            },
            ambient: {
                color: '#4880ff',
                intensity: 0.8
            }
        }
    }
};
const loop_airplan_option = {
    timeline: {
        label: { show: false },
        bottom: '-20',
        autoPlay: true,
        playInterval: 10000,
        tooltip: { show: false }
    },
    options: []
};
const airplan_option = {
    tooltip: {
        show: false
    },
    xAxis: {
        axisTick: {
            show: false
        },
        axisLine: {
            show: false
        },
        axisLabel: {
            show: true,
            margin: 15,
            textStyle: {
                color: function (value, index) {
                    var colorList = ['#14b1eb', '#F39800', '#2BCA44', '#EB6877'];
                    return colorList[index];
                }
            }
        }
    },
    yAxis: {
        splitLine: {
            show: false
        },
        axisTick: {
            show: false
        },
        axisLine: {
            show: false
        },
        axisLabel: {
            show: false
        }
    }
};

class Overall_operation {
    constructor() {
        this._initMap();
        this._addEvent();
    }

    loadAll() {
        if ($('#loading_msk').length == 0) {
            showLoading();
        }
        current_company_code = window.location.hash.substr(1);
        $('#logo').attr('src', `/largescreen/general/img/logo_${current_company_code}_CA.png`);
        if (current_company_code == parent_company) {
            $('#logo').attr('height', '30px');
            $('#logo').css("top", "14px");
        } else {
            $('#logo').attr('height', '50px');
            $('#logo').css("top", "3px");
        }
        companylist.forEach((v, i) => {
            all_comp_codelist.push(v.code);
            if (v.code != parent_company && (current_company_code == parent_company || current_company_code == v.code)) {
                codelist_no_parent.push(v.code);
            }
        });
        Promise.all([this.loadOrgArpBase(), this.loadBaseInt()]).then((resolve) => {
            console.log(resolve);
            Promise.all([this.loadAcType(), this.loadSetting(), this.loadMedia(), this.loadAllCompanyData_d(), this.loadAllCompanyData_sq_d(), this.loadAllCompanyData_tq_d(),
            this.loadFacCompActypeKpi_d(), this.loadFacCompActypeKpi_tq_d(), this.loadFacCompActypeKpi_sq_d(),
            this.loadFacCompDelayCaseRateKpi_d(), this.loadFacCompArpDelayCaseRateKpi_d(),
            this.loadFacCompArpFlightKpi_d(), this.loadFacCompArpFlightKpi_sq_d(), this.loadFacCompArpFlightKpi_ratio_sq(), this.loadFacCompArpFlightBaseIntKpi_d()]).then((resolve) => {
                console.log(resolve);
                this.setData(this);
            }).catch((reject) => {
                console.log(reject);
                alert('数据获取出错！');
                hideLoading();
            });
        });

    }

    setData(_this) {
        //指标设置
        if (setting) {
            $("#title").text(setting.TITLE);
            $("#air_line_total").text(setting.AIR_LINE_TOTAL + "条");
            $("#air_line_o").text(setting.AIR_LINE_D + "条");
            $("#air_line_i").text(setting.AIR_LINE_I + "条");
            $("#air_point_total").text(setting.AIR_POINT_TOTAL + "个");
            $("#air_point_o").text(setting.AIR_POINT_D + "个");
            $("#air_point_i").text(setting.AIR_POINT_I + "个");
            $("#air_plan_year_target").text(setting.AIR_PLAN_TARGET_Y);
            $("#passenger_year_target").text(setting.TRAVELLER_TARGET_Y);
        } else {
            $("#title").text("");
            $("#air_line_total").text("--条");
            $("#air_line_o").text("--条");
            $("#air_line_i").text("--条");
            $("#air_point_total").text("--个");
            $("#air_point_o").text("--个");
            $("#air_point_i").text("--个");
            $("#air_plan_year_target").text("--");
            $("#passenger_year_target").text("--");
        }
        //媒体播放
        $('#mediaWrap').html("");
        if (mediaList.length > 0) {
            this.playMedia();
        }
        //计划航班量
        let date = _this.getCurrentDate();
        let airPlanContentByDateType = all_company_data['kpi_value_d'][current_company_code]['SCH_NO'][date_type];
        let airPlanContent = parseInt(airPlanContentByDateType[date]);
        $("#air_plan_content").text(Number.isNaN(airPlanContent) ? "0" : airPlanContent); //计划航班量
        if (date_type == "Y") {
            $("#air_plan_year_count").text(Number.isNaN(airPlanContent) ? "0" : airPlanContent); //计划航班量年度统计
        } else if (date_type == "M") {
            let yyyy = date.substring(0, 4);
            let mm = date.substring(date.length - 2, date.length);
            let count = 0;
            for (let i = 1; i <= parseInt(mm); i++) {
                let currentCount = i < 10 ? parseInt(airPlanContentByDateType[yyyy + "0" + i]) : parseInt(airPlanContentByDateType[yyyy + i]);
                Number.isNaN(currentCount) ? count : count += currentCount;
            }
            $("#air_plan_year_count").text(count); //计划航班量年度统计
        } else if (date_type == "L") {
            let yyyy = date.substring(0, 4);
            let ww = date.substring(4, 7);
            let count = 0;
            for (let i = 1; i <= parseInt(ww); i++) {
                let currentCount = i < 10 ? parseInt(airPlanContentByDateType[yyyy + "00" + i + "00"]) : i < 100 ? parseInt(airPlanContentByDateType[yyyy + "0" + i + "00"]) : parseInt(airPlanContentByDateType[yyyy + i + "00"]);
                Number.isNaN(currentCount) ? count : count += currentCount;
            }
            $("#air_plan_year_count").text(count); //计划航班量年度统计
        }
        //运输旅客量
        let bookNumByDateType = all_company_data['kpi_value_d'][current_company_code]['BOOK_NUM'][date_type];
        if (current_company_code == 'Y8P') {
            bookNumByDateType = all_company_data['kpi_value_d']['Y8']['BOOK_NUM'][date_type];
        }
        let bookNum = parseInt(bookNumByDateType[date]);
        if (current_company_code == 'Y8C') {
            bookNum = NaN;
        }
        $("#passenger_content").text(Number.isNaN(bookNum) ? "0" : (bookNum / 10000).toFixed(1));
        if (current_company_code == 'Y8C') {
            $("#passenger_year_count").text(0);
        } else {
            if (date_type == "Y") {
                $("#passenger_year_count").text(Number.isNaN(bookNum) ? "0" : (bookNum / 10000).toFixed(1));
            } else if (date_type == "M") {
                let yyyy = date.substring(0, 4);
                let mm = date.substring(date.length - 2, date.length);
                let count = 0;
                for (let i = 1; i <= parseInt(mm); i++) {
                    let currentCount = i < 10 ? parseInt(bookNumByDateType[yyyy + "0" + i]) : parseInt(bookNumByDateType[yyyy + i]);
                    Number.isNaN(currentCount) ? count : count += currentCount;
                }
                $("#passenger_year_count").text((count / 10000).toFixed(1));
            } else if (date_type == "L") {
                let yyyy = date.substring(0, 4);
                let ww = date.substring(4, 7);
                let count = 0;
                for (let i = 1; i <= parseInt(ww); i++) {
                    let currentCount = i < 10 ? parseInt(bookNumByDateType[yyyy + "00" + i + "00"]) : i < 100 ? parseInt(bookNumByDateType[yyyy + "0" + i + "00"]) : parseInt(bookNumByDateType[yyyy + i + "00"]);
                    Number.isNaN(currentCount) ? count : count += currentCount;
                }
                $("#passenger_year_count").text((count / 10000).toFixed(1));
            }
        }
        //客座率
        let indicatorContainer_plf = all_company_data['kpi_value_d'][current_company_code]['TRV_RATE'][date_type][date]; //本期客座率
        let plfY2yContent = all_company_data['kpi_ratio_tq_d'][current_company_code]['TRV_RATE'][date_type][date];
        let plfM2mContent = all_company_data['kpi_ratio_sq_d'][current_company_code]['TRV_RATE'][date_type][date];
        if (current_company_code == 'Y8P' || current_company_code == 'Y8C') {
            indicatorContainer_plf = all_company_data['kpi_value_d']['Y8']['TRV_RATE'][date_type][date];
            plfY2yContent = all_company_data['kpi_ratio_tq_d']['Y8']['TRV_RATE'][date_type][date];
            plfM2mContent = all_company_data['kpi_ratio_sq_d']['Y8']['TRV_RATE'][date_type][date];
        }
        this._setIndicatorContainer("indicatorContainer_plf", indicatorContainer_plf * 100);
        if (plfY2yContent) {
            let arrspan = $("#plf_y2y_content").closest(".label_content").find("span:last-child");
            arrspan.removeClass();
            plfY2yContent < 0 ? arrspan.addClass("down_arr") : arrspan.addClass("up_arr");
            $("#plf_y2y_content").text((Math.abs(plfY2yContent) * 100).toFixed(2) + "%");
        }
        if (plfM2mContent) {
            let arrspan = $("#plf_m2m_content").closest(".label_content").find("span:last-child");
            arrspan.removeClass();
            plfM2mContent < 0 ? arrspan.addClass("down_arr") : arrspan.addClass("up_arr");
            $("#plf_m2m_content").text((Math.abs(plfM2mContent) * 100).toFixed(2) + "%");
        }

        //日利用率
        let FLY_TIME = all_company_actype_data['kpi_value_d'][current_company_code]['FLY_TIME'][date_type];//本期
        let FLY_TIME_TQ = all_company_actype_data['kpi_value_tq_d'][current_company_code]['FLY_TIME'][date_type];//同期
        let FLY_TIME_SQ = all_company_actype_data['kpi_value_sq_d'][current_company_code]['FLY_TIME'][date_type];//上期
        let AC_NUM = all_company_actype_data['kpi_value_d'][current_company_code]['AC_NUM'][date_type];//本期
        let AC_NUM_TQ = all_company_actype_data['kpi_value_tq_d'][current_company_code]['AC_NUM'][date_type];//同期
        let AC_NUM_SQ = all_company_actype_data['kpi_value_sq_d'][current_company_code]['AC_NUM'][date_type];//上期
        if (current_company_code == 'Y8P' || current_company_code == 'Y8C') {
            FLY_TIME = all_company_actype_data['kpi_value_d']['Y8']['FLY_TIME'][date_type];//本期
            FLY_TIME_TQ = all_company_actype_data['kpi_value_tq_d']['Y8']['FLY_TIME'][date_type];//同期
            FLY_TIME_SQ = all_company_actype_data['kpi_value_sq_d']['Y8']['FLY_TIME'][date_type];//上期
            AC_NUM = all_company_actype_data['kpi_value_d']['Y8']['AC_NUM'][date_type];//本期
            AC_NUM_TQ = all_company_actype_data['kpi_value_tq_d']['Y8']['AC_NUM'][date_type];//同期
            AC_NUM_SQ = all_company_actype_data['kpi_value_sq_d']['Y8']['AC_NUM'][date_type];//上期
        }
        let FLY_TIME_COUNT = 0;
        let FLY_TIME_TQ_COUNT = 0;
        let FLY_TIME_SQ_COUNT = 0;
        let AC_NUM_COUNT = 0;
        let AC_NUM_TQ_COUNT = 0;
        let AC_NUM_SQ_COUNT = 0;
        for (let key in FLY_TIME) {
            FLY_TIME_COUNT += isNaN(FLY_TIME[key][date]) ? 0 : parseFloat(FLY_TIME[key][date]);
        }
        for (let key in FLY_TIME_TQ) {
            FLY_TIME_TQ_COUNT += isNaN(FLY_TIME_TQ[key][date]) ? 0 : parseFloat(FLY_TIME_TQ[key][date]);
        }
        for (let key in FLY_TIME_SQ) {
            FLY_TIME_SQ_COUNT += isNaN(FLY_TIME_SQ[key][date]) ? 0 : parseFloat(FLY_TIME_SQ[key][date]);
        }
        for (let key in AC_NUM) {
            AC_NUM_COUNT += isNaN(AC_NUM[key][date]) ? 0 : parseFloat(AC_NUM[key][date]);
        }
        for (let key in AC_NUM_TQ) {
            AC_NUM_TQ_COUNT += isNaN(AC_NUM_TQ[key][date]) ? 0 : parseFloat(AC_NUM_TQ[key][date]);
        }
        for (let key in AC_NUM_SQ) {
            AC_NUM_SQ_COUNT += isNaN(AC_NUM_SQ[key][date]) ? 0 : parseFloat(AC_NUM_SQ[key][date]);
        }
        let date_ur = isNaN(FLY_TIME_COUNT / AC_NUM_COUNT) ? 0 : FLY_TIME_COUNT / AC_NUM_COUNT;
        let date_ur_tq = isNaN(FLY_TIME_TQ_COUNT / AC_NUM_TQ_COUNT) ? 0 : FLY_TIME_TQ_COUNT / AC_NUM_TQ_COUNT;
        let date_ur_sq = isNaN(FLY_TIME_SQ_COUNT / AC_NUM_SQ_COUNT) ? 0 : FLY_TIME_SQ_COUNT / AC_NUM_SQ_COUNT;
        this._drawGauge('cvs_date_ur', 'cvs_date_ur_pointer', date_ur / 24);
        $("#date_ur_val").text(date_ur.toFixed(1) + "h");
        let arrspan_tb = $("#date_ur_y2y_content").closest(".label_content").find("span:last-child");
        let arrspan_hb = $("#date_ur_m2m_content").closest(".label_content").find("span:last-child");
        arrspan_tb.removeClass();
        arrspan_hb.removeClass();
        date_ur - date_ur_tq < 0 ? arrspan_tb.addClass("down_arr") : arrspan_tb.addClass("up_arr");
        $("#date_ur_y2y_content").text(date_ur == 0 ? "0.0%" : (Math.abs((date_ur - date_ur_tq) / date_ur_tq) * 100).toFixed(2) + "%");
        date_ur - date_ur_sq < 0 ? arrspan_hb.addClass("down_arr") : arrspan_hb.addClass("up_arr");
        $("#date_ur_m2m_content").text(date_ur == 0 ? "0.0%" : (Math.abs((date_ur - date_ur_sq) / date_ur_sq) * 100).toFixed(2) + "%");

        //燃效
        let oilContent = all_company_data['kpi_value_d'][current_company_code]['MOD_FUELT'][date_type][date];
        let fleetY2yContent = all_company_data['kpi_ratio_tq_d'][current_company_code]['MOD_FUELT'][date_type][date];
        let fleetM2mContent = all_company_data['kpi_ratio_sq_d'][current_company_code]['MOD_FUELT'][date_type][date];
        if (current_company_code == 'Y8P' || current_company_code == 'Y8C') {
            oilContent = all_company_data['kpi_value_d']['Y8']['MOD_FUELT'][date_type][date];
            fleetY2yContent = all_company_data['kpi_ratio_tq_d']['Y8']['MOD_FUELT'][date_type][date];
            fleetM2mContent = all_company_data['kpi_ratio_sq_d']['Y8']['MOD_FUELT'][date_type][date];
        }
        $(".oil_content").text(oilContent);
        if (fleetY2yContent) {
            let arrspan = $("#fleet_y2y_content").closest(".label_content").find("span:last-child");
            arrspan.removeClass();
            fleetY2yContent < 0 ? arrspan.addClass("down_arr") : arrspan.addClass("up_arr");
            $("#fleet_y2y_content").text((Math.abs(fleetY2yContent) * 100).toFixed(2) + "%");
        }
        if (fleetM2mContent) {
            let arrspan = $("#fleet_m2m_content").closest(".label_content").find("span:last-child");
            arrspan.removeClass();
            fleetM2mContent < 0 ? arrspan.addClass("down_arr") : arrspan.addClass("up_arr");
            $("#fleet_m2m_content").text((Math.abs(fleetM2mContent) * 100).toFixed(2) + "%");
        }

        //航司正常率top10
        $(".tab_left .date_label").text($("#main_cb_" + date_type).find(".combobox_label").text());
        $(".tab_right .date_label").text($("#main_cb_" + date_type).find(".combobox_label").text());
        let sortArray = new Array();

        function Normal(code, value) {
            this.code = code;
            this.value = value;
        }

        for (let comKey in companyCode2Name) {
            if (comKey == parent_company || comKey == "Y8" || comKey == "HX") {
                continue;
            }
            //NORMAL_RATE_ZT_DENOMINATOR,NORMAL_RATE_ZT_MOLECULE  
            let NORMAL_NO_T = all_company_data['kpi_value_d'][comKey]['NORMAL_RATE_ZT_MOLECULE'][date_type][date];
            let SCH_NO = all_company_data['kpi_value_d'][comKey]['NORMAL_RATE_ZT_DENOMINATOR'][date_type][date];
            NORMAL_NO_T == "" || SCH_NO == "" ? sortArray.push(new Normal(comKey, 0)) : sortArray.push(new Normal(comKey, NORMAL_NO_T / SCH_NO));
        }
        sortArray.sort((a, b) => {
            return b.value - a.value;
        });
        sortArray.forEach((v, i) => {
            $("#rank_top_" + i).text(companyCode2Name[v.code].replace("航空", ""));
        });

        //正常率
        let NORMAL_NO_T = all_company_data['kpi_value_d'][current_company_code]['NORMAL_RATE_ZT_MOLECULE'];
        let SCH_NO = all_company_data['kpi_value_d'][current_company_code]['NORMAL_RATE_ZT_DENOMINATOR'];
        let NORMAL_NO_T_INT = all_company_data['kpi_value_d'][current_company_code]['NORMAL_NO_T_INT'];
        let SCH_NO_INT = all_company_data['kpi_value_d'][current_company_code]['SCH_NO_INT'];
        let NORMAL_NO_T_INL = all_company_data['kpi_value_d'][current_company_code]['NORMAL_NO_T_INL'];
        let SCH_NO_INL = all_company_data['kpi_value_d'][current_company_code]['SCH_NO_INL'];
        let ORI_NORMAL_NO = all_company_data['kpi_value_d'][current_company_code]['ORI_NORMAL_NO'][date_type][date];
        let ORI_NO_SCH = all_company_data['kpi_value_d'][current_company_code]['ORI_NO_SCH'][date_type][date];
        let DEP_NORMAL_NO = all_company_data['kpi_value_d'][current_company_code]['DEP_NORMAL_NO'][date_type][date];
        let DEP_NO = all_company_data['kpi_value_d'][current_company_code]['DEP_NO'][date_type][date];
        let ARR_NORMAL_NO = all_company_data['kpi_value_d'][current_company_code]['ARR_NORMAL_NO'][date_type][date];
        let ARR_NO = all_company_data['kpi_value_d'][current_company_code]['ARR_NO'][date_type][date];
        if (date_type == "Y") {
            //计算年
            let y_rate = NORMAL_NO_T[date_type][date] / SCH_NO[date_type][date];
            y_rate = Number.isNaN(y_rate) ? 0 : y_rate;
            let NORMALL_RATE_TARGET_Y = setting ? Number(setting.NORMALL_RATE_TARGET_Y) : 0;
            let istarget_y = Number.isNaN(NORMAL_NO_T_INT[date_type][date] / SCH_NO_INT[date_type][date]);
            let istarget_y_o = Number.isNaN(NORMAL_NO_T_INL[date_type][date] / SCH_NO_INL[date_type][date]);
            this._drawGauge('y_normal_rate', 'y_normal_rate_pointer', y_rate, istarget_y && y_rate * 100 < NORMALL_RATE_TARGET_Y ? true : false);
            if (istarget_y && y_rate * 100 < NORMALL_RATE_TARGET_Y) { this._setchartWarning('y_normal_rate', 'y_normal_rate_pointer'); } else { this._setchartDefault('y_normal_rate', 'y_normal_rate_pointer') }
            $("#y_normal_rate_val").text((y_rate * 100).toFixed(1) + "%");
            $("#y_o_rate").text(istarget_y_o ? "0.0%" : ((NORMAL_NO_T_INL[date_type][date] / SCH_NO_INL[date_type][date]) * 100).toFixed(1) + "%");
            $("#y_i_rate").text(istarget_y ? NORMALL_RATE_TARGET_Y + "%" : ((NORMAL_NO_T_INT[date_type][date] / SCH_NO_INT[date_type][date]) * 100).toFixed(1) + "%");
            $("#y_i_rate").closest(".footer_label").find(".label_footer").text(istarget_y ? "目标" : "国际");
            //计算月
            let currentY = new Date().getFullYear();
            let currentM = new Date().getMonth(); //取上个月数据
            let m_rate = currentM < 10 ? NORMAL_NO_T["M"][currentY + "0" + currentM] / SCH_NO["M"][currentY + "0" + currentM] : NORMAL_NO_T["M"][currentY + '' + currentM] / SCH_NO["M"][currentY + '' + currentM];
            m_rate = Number.isNaN(m_rate) ? 0 : m_rate;
            let NORMALL_RATE_TARGET_M = setting ? Number(setting.NORMALL_RATE_TARGET_M) : 0;
            let istarget_m = Number.isNaN(NORMAL_NO_T_INT["M"][currentY.toString() + (currentM < 10 ? "0" + currentM : currentM.toString())] / SCH_NO_INT["M"][currentY.toString() + (currentM < 10 ? "0" + currentM : currentM.toString())]);
            let istarget_m_o = Number.isNaN(NORMAL_NO_T_INL["M"][currentY.toString() + (currentM < 10 ? "0" + currentM : currentM.toString())] / SCH_NO_INL["M"][currentY.toString() + (currentM < 10 ? "0" + currentM : currentM.toString())]);
            this._drawGauge('m_normal_rate', 'm_normal_rate_pointer', m_rate, istarget_m && m_rate * 100 < NORMALL_RATE_TARGET_M ? true : false);
            if (istarget_m && m_rate * 100 < NORMALL_RATE_TARGET_M) { this._setchartWarning('m_normal_rate', 'm_normal_rate_pointer'); } else { this._setchartDefault('m_normal_rate', 'm_normal_rate_pointer') }
            $("#m_normal_rate_val").text((m_rate * 100).toFixed(1) + "%");
            $("#m_o_rate").text(istarget_m_o ? "0.0%" : ((NORMAL_NO_T_INL["M"][currentY.toString() + (currentM < 10 ? "0" + currentM : currentM.toString())] / SCH_NO_INL["M"][currentY.toString() + (currentM < 10 ? "0" + currentM : currentM.toString())]) * 100).toFixed(1) + "%");
            $("#m_i_rate").text(istarget_m ? NORMALL_RATE_TARGET_M + "%" : ((NORMAL_NO_T_INT["M"][currentY.toString() + (currentM < 10 ? "0" + currentM : currentM.toString())] / SCH_NO_INT["M"][currentY.toString() + (currentM < 10 ? "0" + currentM : currentM.toString())]) * 100).toFixed(1) + "%");
            $("#m_i_rate").closest(".footer_label").find(".label_footer").text(istarget_m ? "目标" : "国际");
            //计算周
            let current_date = moment().format("YYYY-MM-DD");
            let w_date = "";
            let len = weekDateRangeList.length;
            for (let i = 0; i < len; i++) {
                let wdat = weekDateRangeList[i];
                if (current_date >= wdat.range[0] && current_date <= wdat.range[1]) {
                    w_date = weekDateRangeList[i + 1].date; //取上一周
                    break;
                }
            }
            let w_rate = NORMAL_NO_T["L"][w_date] / SCH_NO["L"][w_date];
            w_rate = Number.isNaN(w_rate) ? 0 : w_rate;
            let NORMALL_RATE_TARGET_L = setting ? Number(setting.NORMALL_RATE_TARGET_L) : 0;
            let istarget_l = Number.isNaN(NORMAL_NO_T_INT["L"][w_date] / SCH_NO_INT["L"][w_date]);
            let istarget_l_o = Number.isNaN(NORMAL_NO_T_INL["L"][w_date] / SCH_NO_INL["L"][w_date]);
            this._drawGauge('w_normal_rate', 'w_normal_rate_pointer', w_rate, istarget_l && w_rate * 100 < NORMALL_RATE_TARGET_L ? true : false);
            if (istarget_l && w_rate * 100 < NORMALL_RATE_TARGET_L) { this._setchartWarning('w_normal_rate', 'w_normal_rate_pointer'); } else { this._setchartDefault('w_normal_rate', 'w_normal_rate_pointer') }
            $("#w_normal_rate_val").text((w_rate * 100).toFixed(1) + "%");
            $("#w_o_rate").text(istarget_l_o ? "0.0%" : ((NORMAL_NO_T_INL["L"][w_date] / SCH_NO_INL["L"][w_date]) * 100).toFixed(1) + "%");
            $("#w_i_rate").text(istarget_l ? NORMALL_RATE_TARGET_L + "%" : ((NORMAL_NO_T_INT["L"][w_date] / SCH_NO_INT["L"][w_date]) * 100).toFixed(1) + "%");
            $("#w_i_rate").closest(".footer_label").find(".label_footer").text(istarget_l ? "目标" : "国际");
        } else if (date_type == "M") {
            //计算年
            let y_rate = NORMAL_NO_T["Y"][new Date().getFullYear()] / SCH_NO["Y"][new Date().getFullYear()];
            y_rate = Number.isNaN(y_rate) ? 0 : y_rate;
            let NORMALL_RATE_TARGET_Y = setting ? Number(setting.NORMALL_RATE_TARGET_Y) : 0;
            let istarget_y = Number.isNaN(NORMAL_NO_T_INT["Y"][new Date().getFullYear()] / SCH_NO_INT["Y"][new Date().getFullYear()]);
            let istarget_y_o = Number.isNaN(NORMAL_NO_T_INL["Y"][new Date().getFullYear()] / SCH_NO_INL["Y"][new Date().getFullYear()]);
            this._drawGauge('y_normal_rate', 'y_normal_rate_pointer', y_rate, istarget_y && y_rate * 100 < NORMALL_RATE_TARGET_Y ? true : false);
            if (istarget_y && y_rate * 100 < NORMALL_RATE_TARGET_Y) { this._setchartWarning('y_normal_rate', 'y_normal_rate_pointer'); } else { this._setchartDefault('y_normal_rate', 'y_normal_rate_pointer') }
            $("#y_normal_rate_val").text((y_rate * 100).toFixed(1) + "%");
            $("#y_o_rate").text(istarget_y_o ? "0.0%" : ((NORMAL_NO_T_INL["Y"][new Date().getFullYear()] / SCH_NO_INL["Y"][new Date().getFullYear()]) * 100).toFixed(1) + "%");
            $("#y_i_rate").text(istarget_y ? NORMALL_RATE_TARGET_Y + "%" : ((NORMAL_NO_T_INT["Y"][new Date().getFullYear()] / SCH_NO_INT["Y"][new Date().getFullYear()]) * 100).toFixed(1) + "%");
            $("#y_i_rate").closest(".footer_label").find(".label_footer").text(istarget_y ? "目标" : "国际");
            //计算月
            let m_rate = NORMAL_NO_T[date_type][date] / SCH_NO[date_type][date];
            m_rate = Number.isNaN(m_rate) ? 0 : m_rate;
            let NORMALL_RATE_TARGET_M = setting ? Number(setting.NORMALL_RATE_TARGET_M) : 0;
            let istarget_m = Number.isNaN(NORMAL_NO_T_INT[date_type][date] / SCH_NO_INT[date_type][date]);
            let istarget_m_o = Number.isNaN(NORMAL_NO_T_INL[date_type][date] / SCH_NO_INL[date_type][date]);
            this._drawGauge('m_normal_rate', 'm_normal_rate_pointer', m_rate, istarget_m && m_rate * 100 < NORMALL_RATE_TARGET_M ? true : false);
            if (istarget_m && m_rate * 100 < NORMALL_RATE_TARGET_M) { this._setchartWarning('m_normal_rate', 'm_normal_rate_pointer'); } else { this._setchartDefault('m_normal_rate', 'm_normal_rate_pointer') }
            $("#m_normal_rate_val").text((m_rate * 100).toFixed(1) + "%");
            $("#m_o_rate").text(istarget_m_o ? "0.0%" : ((NORMAL_NO_T_INL[date_type][date] / SCH_NO_INL[date_type][date]) * 100).toFixed(1) + "%");
            $("#m_i_rate").text(istarget_m ? NORMALL_RATE_TARGET_M + "%" : ((NORMAL_NO_T_INT[date_type][date] / SCH_NO_INT[date_type][date]) * 100).toFixed(1) + "%");
            $("#m_i_rate").closest(".footer_label").find(".label_footer").text(istarget_m ? "目标" : "国际");
            //计算周
            let current_date = moment().format("YYYY-MM-DD");
            let w_date = "";
            let len = weekDateRangeList.length;
            for (let i = 0; i < len; i++) {
                let wdat = weekDateRangeList[i];
                if (current_date >= wdat.range[0] && current_date <= wdat.range[1]) {
                    w_date = weekDateRangeList[i + 1].date; //取上一周
                    break;
                }
            }
            let w_rate = NORMAL_NO_T["L"][w_date] / SCH_NO["L"][w_date];
            w_rate = Number.isNaN(w_rate) ? 0 : w_rate;
            let NORMALL_RATE_TARGET_L = setting ? Number(setting.NORMALL_RATE_TARGET_L) : 0;
            let istarget_l = Number.isNaN(NORMAL_NO_T_INT["L"][w_date] / SCH_NO_INT["L"][w_date]);
            let istarget_l_o = Number.isNaN(NORMAL_NO_T_INL["L"][w_date] / SCH_NO_INL["L"][w_date]);
            this._drawGauge('w_normal_rate', 'w_normal_rate_pointer', w_rate, istarget_l && w_rate * 100 < NORMALL_RATE_TARGET_L ? true : false);
            if (istarget_l && w_rate * 100 < NORMALL_RATE_TARGET_L) { this._setchartWarning('w_normal_rate', 'w_normal_rate_pointer'); } else { this._setchartDefault('w_normal_rate', 'w_normal_rate_pointer') }
            $("#w_normal_rate_val").text((w_rate * 100).toFixed(1) + "%");
            $("#w_o_rate").text(istarget_l_o ? "0.0%" : ((NORMAL_NO_T_INL["L"][w_date] / SCH_NO_INL["L"][w_date]) * 100).toFixed(1) + "%");
            $("#w_i_rate").text(istarget_l ? NORMALL_RATE_TARGET_L + "%" : ((NORMAL_NO_T_INT["L"][w_date] / SCH_NO_INT["L"][w_date]) * 100).toFixed(1) + "%");
            $("#w_i_rate").closest(".footer_label").find(".label_footer").text(istarget_l ? "目标" : "国际");
        } else if (date_type == "L") {
            //计算年
            let y_rate = NORMAL_NO_T["Y"][new Date().getFullYear()] / SCH_NO["Y"][new Date().getFullYear()];
            y_rate = Number.isNaN(y_rate) ? 0 : y_rate;
            let NORMALL_RATE_TARGET_Y = setting ? Number(setting.NORMALL_RATE_TARGET_Y) : 0;
            let istarget = Number.isNaN(NORMAL_NO_T_INT["Y"][new Date().getFullYear()] / SCH_NO_INT["Y"][new Date().getFullYear()]);
            let istarget_o = Number.isNaN(NORMAL_NO_T_INL["Y"][new Date().getFullYear()] / SCH_NO_INL["Y"][new Date().getFullYear()]);
            this._drawGauge('y_normal_rate', 'y_normal_rate_pointer', y_rate, istarget && y_rate * 100 < NORMALL_RATE_TARGET_Y ? true : false);
            if (istarget && y_rate * 100 < NORMALL_RATE_TARGET_Y) { this._setchartWarning('y_normal_rate', 'y_normal_rate_pointer'); } else { this._setchartDefault('y_normal_rate', 'y_normal_rate_pointer') }
            $("#y_normal_rate_val").text((y_rate * 100).toFixed(1) + "%");
            $("#y_o_rate").text(istarget_o ? "0.0" + "%" : ((NORMAL_NO_T_INL["Y"][new Date().getFullYear()] / SCH_NO_INL["Y"][new Date().getFullYear()]) * 100).toFixed(1) + "%");
            $("#y_i_rate").text(istarget ? NORMALL_RATE_TARGET_Y + "%" : ((NORMAL_NO_T_INT["Y"][new Date().getFullYear()] / SCH_NO_INT["Y"][new Date().getFullYear()]) * 100).toFixed(1) + "%");
            $("#y_i_rate").closest(".footer_label").find(".label_footer").text(istarget ? "目标" : "国际");
            //计算月
            let currentY = new Date().getFullYear();
            let currentM = new Date().getMonth(); //取上个月数据
            let m_rate = currentM < 10 ? NORMAL_NO_T["M"][currentY + "0" + currentM] / SCH_NO["M"][currentY + "0" + currentM] : NORMAL_NO_T["M"][currentY + '' + currentM] / SCH_NO["M"][currentY + '' + currentM];
            m_rate = Number.isNaN(m_rate) ? 0 : m_rate;
            let NORMALL_RATE_TARGET_M = setting ? Number(setting.NORMALL_RATE_TARGET_M) : 0;
            let istarget_m = Number.isNaN(NORMAL_NO_T_INT["M"][currentY.toString() + (currentM < 10 ? "0" + currentM : currentM.toString())] / SCH_NO_INT["M"][currentY.toString() + (currentM < 10 ? "0" + currentM : currentM.toString())]);
            let istarget_m_o = Number.isNaN(NORMAL_NO_T_INL["M"][currentY.toString() + (currentM < 10 ? "0" + currentM : currentM.toString())] / SCH_NO_INL["M"][currentY.toString() + (currentM < 10 ? "0" + currentM : currentM.toString())]);
            this._drawGauge('m_normal_rate', 'm_normal_rate_pointer', m_rate, istarget_m && m_rate * 100 < NORMALL_RATE_TARGET_M ? true : false);
            if (istarget_m && m_rate * 100 < NORMALL_RATE_TARGET_M) { this._setchartWarning('m_normal_rate', 'm_normal_rate_pointer'); } else { this._setchartDefault('m_normal_rate', 'm_normal_rate_pointer') }
            $("#m_normal_rate_val").text((m_rate * 100).toFixed(1) + "%");
            $("#m_o_rate").text(istarget_m_o ? "0.0%" : ((NORMAL_NO_T_INL["M"][currentY.toString() + (currentM < 10 ? "0" + currentM : currentM.toString())] / SCH_NO_INL["M"][currentY.toString() + (currentM < 10 ? "0" + currentM : currentM.toString())]) * 100).toFixed(1) + "%");
            $("#m_i_rate").text(istarget_m ? NORMALL_RATE_TARGET_M + "%" : ((NORMAL_NO_T_INT["M"][currentY.toString() + (currentM < 10 ? "0" + currentM : currentM.toString())] / SCH_NO_INT["M"][currentY.toString() + (currentM < 10 ? "0" + currentM : currentM.toString())]) * 100).toFixed(1) + "%");
            $("#m_i_rate").closest(".footer_label").find(".label_footer").text(istarget_m ? "目标" : "国际");
            //计算周
            let w_rate = NORMAL_NO_T[date_type][date] / SCH_NO[date_type][date];
            w_rate = Number.isNaN(w_rate) ? 0 : w_rate;
            let NORMALL_RATE_TARGET_L = setting ? Number(setting.NORMALL_RATE_TARGET_L) : 0;
            let istarget_l = Number.isNaN(NORMAL_NO_T_INT[date_type][date] / SCH_NO_INT[date_type][date]);
            let istarget_l_o = Number.isNaN(NORMAL_NO_T_INL[date_type][date] / SCH_NO_INL[date_type][date]);
            this._drawGauge('w_normal_rate', 'w_normal_rate_pointer', w_rate, istarget_l && w_rate * 100 < NORMALL_RATE_TARGET_L ? true : false);
            if (istarget_l && w_rate * 100 < NORMALL_RATE_TARGET_L) { this._setchartWarning('w_normal_rate', 'w_normal_rate_pointer'); } else { this._setchartDefault('w_normal_rate', 'w_normal_rate_pointer') }
            $("#w_normal_rate_val").text((w_rate * 100).toFixed(1) + "%");
            $("#w_o_rate").text(istarget_l_o ? "0.0%" : ((NORMAL_NO_T_INL[date_type][date] / SCH_NO_INL[date_type][date]) * 100).toFixed(1) + "%");
            $("#w_i_rate").text(istarget_l ? NORMALL_RATE_TARGET_L + "%" : ((NORMAL_NO_T_INT[date_type][date] / SCH_NO_INT[date_type][date]) * 100).toFixed(1) + "%");
            $("#w_i_rate").closest(".footer_label").find(".label_footer").text(istarget_l ? "目标" : "国际");
        }
        this._setIndicatorContainer("start_rate", Number.isNaN(ORI_NORMAL_NO / ORI_NO_SCH) ? 0 : (ORI_NORMAL_NO / ORI_NO_SCH) * 100);
        this._setIndicatorContainer("off_rate", Number.isNaN(DEP_NORMAL_NO / DEP_NO) ? 0 : (DEP_NORMAL_NO / DEP_NO) * 100);
        this._setIndicatorContainer("arr_rate", Number.isNaN(ARR_NORMAL_NO / ARR_NO) ? 0 : (ARR_NORMAL_NO / ARR_NO) * 100);

        //延误
        let DELAY_TIME = all_company_data['kpi_value_d'][current_company_code]['DELAY_TIME'][date_type][date];
        let D_SCH_NO = all_company_data['kpi_value_d'][current_company_code]['SCH_NO'][date_type][date];
        let DELAY_NO = all_company_delay_cause_data['kpi_value_d'][current_company_code]['DELAY_NO'][date_type][date];
        $("#delay_time").text(Number.isNaN(DELAY_TIME / D_SCH_NO) ? 0 + "h" : (DELAY_TIME / D_SCH_NO).toFixed(1) + "h");
        let total_delay_no = 0;
        let comp_delay_rate = 0;
        let no_comp_delay_rate = 0;
        for (let key in DELAY_NO) {
            total_delay_no += isNaN(parseInt(DELAY_NO[key])) ? 0 : parseInt(DELAY_NO[key]);
        }
        for (let key in DELAY_NO) {
            if (comp_cause.indexOf(key) != -1) {
                comp_delay_rate += isNaN(parseInt(DELAY_NO[key])) ? 0 : parseInt(DELAY_NO[key]) / total_delay_no;
            }
            if (none_cause.indexOf(key) != -1) {
                no_comp_delay_rate += isNaN(parseInt(DELAY_NO[key])) ? 0 : parseInt(DELAY_NO[key]) / total_delay_no;
            }
        }
        console.log(comp_delay_rate, no_comp_delay_rate);
        this._drawDelay('delay_rate', comp_delay_rate);
        $("#delay_rate_comp").text((comp_delay_rate * 100).toFixed(1) + "%");
        $("#delay_rate_none").text((no_comp_delay_rate * 100).toFixed(1) + "%");

        //地图数据
        this.setMapData(_this);
        //境外办事处
        clearInterval(scrollInterval);
        if (arpcodes.length > 0) {
            if (arpcodes.length == 1) {
                let code = arpcodes[0];
                arpdetails.some((v, i) => {
                    if (v.code == code) {
                        $("#io_place").text(v.nation_name.substring(0, v.nation_name.indexOf("/")) + " · " + v.city_name).fadeIn();
                        return true;
                    }
                });
                let SCH_NO_CODE = all_company_arp_flight_base_int_data['kpi_value_d'][current_company_code] ? all_company_arp_flight_base_int_data['kpi_value_d'][current_company_code]['SCH_NO'][date_type][code] : undefined;
                let NORMAL_NO_T_CODE = all_company_arp_flight_base_int_data['kpi_value_d'][current_company_code] ? all_company_arp_flight_base_int_data['kpi_value_d'][current_company_code]['NORMAL_NO_T'][date_type][code] : undefined;
                let SCH_NO = SCH_NO_CODE ? SCH_NO_CODE[date] : 0;
                let NORMAL_NO_T = NORMAL_NO_T_CODE ? NORMAL_NO_T_CODE[date] : 0;
                $("#io_shifts").text(isNaN(SCH_NO) ? 0 : parseInt(SCH_NO)).fadeIn();
                $("#io_normal_rate").text(isNaN(NORMAL_NO_T / SCH_NO) ? 0 + "%" : ((NORMAL_NO_T / SCH_NO) * 100).toFixed(1) + "%").fadeIn();
            } else {
                scrollInterval = setInterval(() => {
                    let code;
                    if (scrollIndex == arpcodes.length - 1) {
                        code = arpcodes[scrollIndex];
                        scrollIndex = 0;
                    } else {
                        code = arpcodes[scrollIndex];
                        scrollIndex++;
                    }
                    arpdetails.some((v, i) => {
                        if (v.code == code) {
                            $("#io_place").fadeOut();
                            setTimeout(() => { $("#io_place").text(v.nation_name.substring(0, v.nation_name.indexOf("/")) + " · " + v.city_name).fadeIn(); }, 600);
                            return true;
                        }
                    });
                    let SCH_NO_CODE = all_company_arp_flight_base_int_data['kpi_value_d'][current_company_code] ? all_company_arp_flight_base_int_data['kpi_value_d'][current_company_code]['SCH_NO'][date_type][code] : undefined;
                    let NORMAL_NO_T_CODE = all_company_arp_flight_base_int_data['kpi_value_d'][current_company_code] ? all_company_arp_flight_base_int_data['kpi_value_d'][current_company_code]['NORMAL_NO_T'][date_type][code] : undefined;
                    let SCH_NO = SCH_NO_CODE ? SCH_NO_CODE[date] : 0;
                    let NORMAL_NO_T = NORMAL_NO_T_CODE ? NORMAL_NO_T_CODE[date] : 0;
                    setTimeout(() => {
                        $("#io_shifts").text(isNaN(SCH_NO) ? 0 : parseInt(SCH_NO));
                        $("#io_normal_rate").text(isNaN(NORMAL_NO_T / SCH_NO) ? 0 + "%" : ((NORMAL_NO_T / SCH_NO) * 100).toFixed(1) + "%");
                    }, 600);

                }, 5000);
            }
        } else {
            $("#io_place").text("-- · --");
            $("#io_shifts").text(0);
            $("#io_normal_rate").text(0 + "%");
        }
        //机队规模
        this.setChartData();

        hideLoading();
    }
    setChartData() {
        let ObjectItem = Object.entries(ac_data_list);
        if (ObjectItem.length > 4) {
            let size = Math.ceil(ObjectItem.length / 4);
            let data = new Array();
            for (let i = 1; i <= size; i++) {
                data.push(i);
            }
            loop_airplan_option.timeline.data = data;
            let options = new Array();
            let loopObjectItem = this._spArray(4, ObjectItem);
            loopObjectItem.forEach((loop, index) => {
                let series = new Array();
                const airplan_option = {
                    tooltip: {
                        show: false
                    },
                    xAxis: {
                        axisTick: {
                            show: false
                        },
                        axisLine: {
                            show: false
                        },
                        axisLabel: {
                            show: true,
                            margin: 15,
                            textStyle: {
                                color: function (value, index) {
                                    var colorList = ['#14b1eb', '#F39800', '#2BCA44', '#EB6877', '#6f0de2', '#8619db', '#aa44e1', '#f50f81', '#ff43c3', '#b169ff', '#8329ff', '#7309e0', '#1064e2', '#1a9ff6', '#00a61a'];
                                    return colorList[index];
                                }
                            }
                        }
                    },
                    yAxis: {
                        splitLine: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        },
                        axisLine: {
                            show: false
                        },
                        axisLabel: {
                            show: false
                        }
                    }
                };
                this._fillSeries(airplan_option, series, loop);
                options.push(airplan_option);
            });
            loop_airplan_option.options = options;
            airplanChart.clear();
            airplanChart.setOption(loop_airplan_option);
        } else {
            let series = new Array();
            this._fillSeries(airplan_option, series, ObjectItem);
            airplanChart.clear();
            airplanChart.setOption(airplan_option);
        }
    }

    setMapData(_this) {
        let date = _this.getCurrentDate();
        let series = new Array();
        let seriesData = new Array();
        let NORMAL_NO_T = all_company_arp_flight_data['kpi_value_d'][current_company_code]['NORMAL_NO_T'][date_type];
        let NORMAL_NO_T_SQ = all_company_arp_flight_data['kpi_value_sq_d'][current_company_code]['NORMAL_NO_T'][date_type];
        let SCH_NO = all_company_arp_flight_data['kpi_value_d'][current_company_code]['SCH_NO'][date_type];
        let SCH_NO_SQ = all_company_arp_flight_data['kpi_value_sq_d'][current_company_code]['SCH_NO'][date_type];
        let SCH_NO_SQ_RATIO = all_company_arp_flight_data['kpi_ratio_sq_d'][current_company_code]['SCH_NO'][date_type];
        let CKI_NUM = all_company_arp_flight_data['kpi_value_d'][current_company_code]['CKI_NUM'][date_type];
        let CKI_NUM_SQ_RATIO = all_company_arp_flight_data['kpi_ratio_sq_d'][current_company_code]['CKI_NUM'][date_type];
        let ARP_DELAY_NO = all_company_arp_delay_cause_data['kpi_value_d'][current_company_code]['DELAY_NO'][date_type];
        let comp_arp_code = org_arp_base[current_company_code];
        comp_arp_code ? comp_arp_code.forEach((v, i) => {
            let title = companyCode2Name[current_company_code] + v.cityName + v.arpName + "基地";
            let rate = 0;
            let rate_sq = 0;
            let rate_ratio = 0;
            let SCH_NO_DATA = 0;
            let total_delay_no = 0;
            let comp_delay_rate = 0;
            let no_comp_delay_rate = 0;
            let DELAY_NO = ARP_DELAY_NO[v.arpCode] && ARP_DELAY_NO[v.arpCode][date] ? ARP_DELAY_NO[v.arpCode][date] : new Array();
            let SCH_NO_SQ_RATIO_DATA = SCH_NO_SQ_RATIO[v.arpCode] && SCH_NO_SQ_RATIO[v.arpCode][date] ? (parseFloat(SCH_NO_SQ_RATIO[v.arpCode][date]) * 100).toFixed(1) : 0;
            let CKI_NUM_DATA = CKI_NUM[v.arpCode] && CKI_NUM[v.arpCode][date] ? parseInt(CKI_NUM[v.arpCode][date]) : 0;
            let CKI_NUM_SQ_RATIO_DATA = CKI_NUM_SQ_RATIO[v.arpCode] && CKI_NUM_SQ_RATIO[v.arpCode][date] ? (parseFloat(CKI_NUM_SQ_RATIO[v.arpCode][date]) * 100).toFixed(1) : 0;
            if (NORMAL_NO_T[v.arpCode] && SCH_NO[v.arpCode] && NORMAL_NO_T[v.arpCode][date] && SCH_NO[v.arpCode][date]) {
                SCH_NO_DATA = parseInt(SCH_NO[v.arpCode][date]);
                rate = isNaN(NORMAL_NO_T[v.arpCode][date] / SCH_NO[v.arpCode][date]) ? 0 : (NORMAL_NO_T[v.arpCode][date] / SCH_NO[v.arpCode][date] * 100).toFixed(1);
            }
            if (NORMAL_NO_T_SQ[v.arpCode] && SCH_NO_SQ[v.arpCode] && NORMAL_NO_T_SQ[v.arpCode][date] && SCH_NO_SQ[v.arpCode][date]) {
                rate_sq = isNaN(NORMAL_NO_T_SQ[v.arpCode][date] / SCH_NO_SQ[v.arpCode][date]) ? 0 : (NORMAL_NO_T_SQ[v.arpCode][date] / SCH_NO_SQ[v.arpCode][date] * 100).toFixed(1);
            }
            rate_ratio = rate == 0 || rate_sq == 0 ? rate_ratio : (rate - rate_sq) / rate_sq;
            let yellow = '#FFF100';
            let red = '#EB6100';
            let green = '#22AC38';
            let orange = '#F39800';
            let color = red;
            if (rate > 80) {
                color = green;
            } else if (rate > 70 && rate <= 80) {
                color = yellow;
            } else if (rate > 60 && rate <= 70) {
                color = orange;
            }
            if (DELAY_NO.length > 0) {
                DELAY_NO.forEach((v, i) => {
                    total_delay_no += isNaN(parseInt(v.value)) ? 0 : parseInt(v.value);
                });
                DELAY_NO.forEach((v, i) => {
                    if (v.classify_s == "非公司原因") {
                        no_comp_delay_rate += isNaN(parseInt(v.value)) ? 0 : parseInt(v.value) / total_delay_no;
                    } else if (v.classify_s == "公司原因") {
                        comp_delay_rate += isNaN(parseInt(v.value)) ? 0 : parseInt(v.value) / total_delay_no;
                    }
                });
            }
            seriesData.push({
                name: v.arpName,
                code: v.arpCode,
                value: [parseFloat(v.longitude), parseFloat(v.latitude), 10, title, SCH_NO_DATA, SCH_NO_SQ_RATIO_DATA, (CKI_NUM_DATA / 10000).toFixed(1), CKI_NUM_SQ_RATIO_DATA, rate, rate_ratio.toFixed(2), (comp_delay_rate * 100).toFixed(1), (no_comp_delay_rate * 100).toFixed(1)],
                symbol: 'pin',
                symbolSize: [28, 30],
                itemStyle: {
                    color: color
                }
            });
        }) : null;

        series.push({
            name: 'scatter3D',
            type: "scatter3D",
            coordinateSystem: 'geo3D',
            zlevel: 3,
            tooltip: {
                show: false
            },
            label: {
                show: true,
                formatter: '{b}'
            },
            data: seriesData
        });
        let options = myChart.getOption();
        options.series = series;
        myChart.setOption(options, true);

        // if (click_param) this.resetPop(click_param);
        myChart.on('click', (params) => {
            click_param = params;
            this.resetPop(params);
        });

    }

    setTime() {
        const date = new Date();
        const timestamp = date.getTime();
        const timezoneOffset = date.getTimezoneOffset();
        const utc_timestamp = timestamp + timezoneOffset * 60 * 1000;
        const utc_date = new Date();
        utc_date.setTime(utc_timestamp);

        const sydney_date = new Date();
        sydney_date.setTime(utc_timestamp + 11 * 3600 * 1000);

        const newyork_date = new Date();
        newyork_date.setTime(utc_timestamp - 5 * 3600 * 1000);

        $('#time_beijing').text(this._formatNum(date.getHours()) + ':' + this._formatNum(date.getMinutes()));
        $('#time_london').text(this._formatNum(utc_date.getHours()) + ':' + this._formatNum(utc_date.getMinutes()));
        $('#time_sydney').text(this._formatNum(sydney_date.getHours()) + ':' + this._formatNum(sydney_date.getMinutes()));
        $('#time_newyork').text(this._formatNum(newyork_date.getHours()) + ':' + this._formatNum(newyork_date.getMinutes()));

        $('#date_beijing').text(date.getDate() + ' ' + this._getEngMonth(date.getMonth()));
        $('#date_london').text(utc_date.getDate() + ' ' + this._getEngMonth(utc_date.getMonth()));
        $('#date_sydney').text(sydney_date.getDate() + ' ' + this._getEngMonth(sydney_date.getMonth()));
        $('#date_newyork').text(newyork_date.getDate() + ' ' + this._getEngMonth(newyork_date.getMonth()));
    }
    playMedia() {
        let media = mediaList[currentMediaId];
        let type = media.TYPE;
        let val = media.VALUE;

        if (val.indexOf('http') == -1) {
            // load file
            let param = {
                "mode": "getfile",
                "filename": val
            };
            $.ajax({
                type: 'post',
                url: "/bi/web/file",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    let base64str = response.fileinfo;
                    if (type == 'img') {
                        var html = '<span style="height:100%;display:inline-block;vertical-align:middle"></span><img style="display:inline-block; max-width:530px;max-height:283px;" src="data:image/jpeg;base64,' + base64str + '" />';
                        $('#mediaWrap').html(html);
                    }

                },
                error: function (jqXHR, txtStatus, errorThrown) {
                    console.log('----error');
                }
            });

        } else {
            // url
            if (type == 'img') {
                let html = '<span style="height:100%;display:inline-block;vertical-align:middle"></span><img style="display:inline-block; max-width:530px;max-height:283px;" src="' + val + '" />';
                $('#mediaWrap').html(html);

            } else if (type == 'video') {
                let html = '';
                html += '<video src="' + val + '" controls="controls" autoplay="true" width="530px" height="283px">';
                html += '您的浏览器不支持 video 标签';
                html += '</video>';
                $('#mediaWrap').html(html);
            }
        }
        let interval = Number(media.INTERVAL);
        if (isNaN(interval)) {
            interval = 10;
        }
        clearTimeout(tmo_media);
        tmo_media = setTimeout(() => {
            if (currentMediaId < mediaList.length - 1) {
                currentMediaId++;
            } else {
                currentMediaId = 0;
            }
            this.playMedia();
        }, interval * 1000);
    }

    // 本期 FAC_COMP_KPI
    loadAllCompanyData_d() {
        return new Promise((resolve, reject) => {
            let param = {
                'SOLR_CODE': 'FAC_COMP_KPI',
                'COMP_CODE': all_comp_codelist.join(','),
                'KPI_CODE': 'NORMAL_RATE_ZT_DENOMINATOR,NORMAL_RATE_ZT_MOLECULE,SCH_NO,TRV_RATE,MOD_FUELT,NORMAL_NO_T,NORMAL_NO_T_INL,SCH_NO_INL,NORMAL_NO_T_INT,SCH_NO_INT,ORI_NORMAL_NO,ORI_NO_SCH,DEP_NORMAL_NO,DEP_NO,DELAY_TIME,ARR_NORMAL_NO,ARR_NO,BOOK_NUM',
                'VALUE_TYPE': 'kpi_value_d', //本期
                'DATE_TYPE': 'L,M,Y',
                "OPTIMIZE": 1,
                'LIMIT': query_limit
            };
            $.ajax({
                type: 'post',
                url: "/bi/query/getkpi",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    console.log("FAC_COMP_KPI kpi_value_d", response.data);
                    if (response.data != undefined) {
                        all_company_data['kpi_value_d'] = response.data;
                    }
                    const ddd = response.data['HU']['SCH_NO']['L'];
                    let week_list = new Array();
                    for (const date in ddd) {
                        week_list.push(date);
                    }
                    this.getWeekDateRange(week_list);
                    this._initSelectDate(week_list);
                    resolve(response.errorcode);

                }.bind(this),
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    // 环比 FAC_COMP_KPI
    loadAllCompanyData_sq_d() {
        return new Promise((resolve, reject) => {
            let param = {
                'SOLR_CODE': 'FAC_COMP_KPI',
                'COMP_CODE': all_comp_codelist.join(','),
                'KPI_CODE': 'TRV_RATE,MOD_FUELT',
                'VALUE_TYPE': 'kpi_ratio_sq_d', //环比
                'DATE_TYPE': 'L,M,Y',
                "OPTIMIZE": 1,
                'LIMIT': query_limit
            };
            $.ajax({
                type: 'post',
                url: "/bi/query/getkpi",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    console.log("FAC_COMP_KPI kpi_ratio_sq_d", response.data);
                    if (response.data != undefined) {
                        all_company_data['kpi_ratio_sq_d'] = response.data;
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    // 同比 FAC_COMP_KPI
    loadAllCompanyData_tq_d() {
        return new Promise((resolve, reject) => {
            let param = {
                'SOLR_CODE': 'FAC_COMP_KPI',
                'COMP_CODE': all_comp_codelist.join(','),
                'KPI_CODE': 'TRV_RATE,MOD_FUELT',
                'VALUE_TYPE': 'kpi_ratio_tq_d', //同比
                'DATE_TYPE': 'L,M,Y',
                "OPTIMIZE": 1,
                'LIMIT': query_limit
            };
            $.ajax({
                type: 'post',
                url: "/bi/query/getkpi",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    console.log("FAC_COMP_KPI kpi_ratio_tq_d", response.data);
                    if (response.data != undefined) {
                        all_company_data['kpi_ratio_tq_d'] = response.data;
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });

    }

    //本期
    loadFacCompActypeKpi_d() {
        return new Promise((resolve, reject) => {
            let param = {
                'SOLR_CODE': 'FAC_COMP_ACTYPE_KPI',
                'COMP_CODE': all_comp_codelist.join(','),
                'KPI_CODE': 'FLY_TIME,AC_NUM', // 飞行时间、飞机架次 == 用来计算飞机日利用率
                'VALUE_TYPE': 'kpi_value_d', //本期
                'DATE_TYPE': 'L,M,Y',
                'ACTYPE': actypelist,
                "OPTIMIZE": 1,
                'LIMIT': query_limit
            };
            $.ajax({
                type: 'post',
                url: "/bi/query/getkpi",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    console.log("FAC_COMP_ACTYPE_KPI kpi_value_d", response.data);
                    if (response.data != undefined) {
                        all_company_actype_data['kpi_value_d'] = response.data;
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    //同期
    loadFacCompActypeKpi_tq_d() {
        return new Promise((resolve, reject) => {
            let param = {
                'SOLR_CODE': 'FAC_COMP_ACTYPE_KPI',
                'COMP_CODE': all_comp_codelist.join(','),
                'KPI_CODE': 'FLY_TIME,AC_NUM', // 飞行时间、飞机架次 == 用来计算飞机日利用率
                'VALUE_TYPE': 'kpi_value_tq_d', //同期
                'DATE_TYPE': 'L,M,Y',
                'ACTYPE': actypelist,
                "OPTIMIZE": 1,
                'LIMIT': query_limit
            };
            $.ajax({
                type: 'post',
                url: "/bi/query/getkpi",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    console.log("FAC_COMP_ACTYPE_KPI kpi_value_tq_d", response.data);
                    if (response.data != undefined) {
                        all_company_actype_data['kpi_value_tq_d'] = response.data;
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    //上期
    loadFacCompActypeKpi_sq_d() {
        return new Promise((resolve, reject) => {
            let param = {
                'SOLR_CODE': 'FAC_COMP_ACTYPE_KPI',
                'COMP_CODE': all_comp_codelist.join(','),
                'KPI_CODE': 'FLY_TIME,AC_NUM', // 飞行时间、飞机架次 == 用来计算飞机日利用率
                'VALUE_TYPE': 'kpi_value_sq_d', //上期
                'DATE_TYPE': 'L,M,Y',
                'ACTYPE': actypelist,
                "OPTIMIZE": 1,
                'LIMIT': query_limit
            };
            $.ajax({
                type: 'post',
                url: "/bi/query/getkpi",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    console.log("FAC_COMP_ACTYPE_KPI kpi_value_sq_d", response.data);
                    if (response.data != undefined) {
                        all_company_actype_data['kpi_value_sq_d'] = response.data;
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    loadFacCompDelayCaseRateKpi_d() {
        return new Promise((resolve, reject) => {
            let param = {
                'SOLR_CODE': 'FAC_COMP_DELAY_CAUSE_RATE_KPI',
                'COMP_CODE': current_company_code, //后台取所有公司 此参数无效
                'KPI_CODE': 'DELAY_NO',
                'VALUE_TYPE': 'kpi_value_d',
                'DATE_TYPE': 'L,M,Y',
                'LIMIT': query_limit,
                'OPTIMIZE': 1
            };
            $.ajax({
                type: 'post',
                url: "/bi/query/getkpi",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    console.log("FAC_COMP_DELAY_CASE_RATE_KPI kpi_value_d", response.data);
                    if (response.data != undefined) {
                        all_company_delay_cause_data['kpi_value_d'] = response.data;
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    loadFacCompArpDelayCaseRateKpi_d() {
        let arpCodelist = org_arp_base[current_company_code] ? org_arp_base[current_company_code].map((item) => {
            return item.arpCode;
        }) : new Array();
        return new Promise((resolve, reject) => {
            let param = {
                'SOLR_CODE': 'FAC_COMP_ARP_DELAY_CAUSE_KPI',
                'COMP_CODE': current_company_code,
                "AIRPORT_CODE": arpCodelist.join(","),
                'KPI_CODE': 'DELAY_NO',
                'VALUE_TYPE': 'kpi_value_d',
                'DATE_TYPE': 'L,M,Y',
                'LIMIT': query_limit,
                'OPTIMIZE': 1
            };
            $.ajax({
                type: 'post',
                url: "/bi/query/getkpi",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    console.log("FAC_COMP_ARP_DELAY_CASE_RATE_KPI kpi_value_d", response.data);
                    if (response.data != undefined) {
                        all_company_arp_delay_cause_data['kpi_value_d'] = response.data;
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    loadFacCompArpFlightKpi_d() {
        let arpCodelist = org_arp_base[current_company_code] ? org_arp_base[current_company_code].map((item) => {
            return item.arpCode;
        }) : new Array();
        return new Promise((resolve, reject) => {
            let param = {
                "SOLR_CODE": "FAC_COMP_ARP_FLIGHT_KPI",
                'COMP_CODE': current_company_code,
                "AIRPORT_CODE": arpCodelist.join(","),
                'KPI_CODE': 'SCH_NO,CKI_NUM,NORMAL_NO_T,SCH_NO',
                "VALUE_TYPE": "kpi_value_d",
                'DATE_TYPE': 'L,M,Y',
                "LIMIT": query_limit,
                "OPTIMIZE": 1,
            };
            $.ajax({
                type: 'post',
                url: "/bi/query/getkpi",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    console.log("FAC_COMP_ARP_FLIGHT_KPI kpi_value_d", response.data);
                    if (response.data != undefined) {
                        all_company_arp_flight_data['kpi_value_d'] = response.data;
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    loadFacCompArpFlightKpi_sq_d() {
        let arpCodelist = org_arp_base[current_company_code] ? org_arp_base[current_company_code].map((item) => {
            return item.arpCode;
        }) : new Array();
        return new Promise((resolve, reject) => {
            let param = {
                "SOLR_CODE": "FAC_COMP_ARP_FLIGHT_KPI",
                'COMP_CODE': current_company_code,
                "AIRPORT_CODE": arpCodelist.join(","),
                'KPI_CODE': 'SCH_NO,CKI_NUM,NORMAL_NO_T,SCH_NO',
                "VALUE_TYPE": "kpi_value_sq_d",
                'DATE_TYPE': 'L,M,Y',
                "LIMIT": query_limit,
                "OPTIMIZE": 1,
            };
            $.ajax({
                type: 'post',
                url: "/bi/query/getkpi",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    console.log("FAC_COMP_ARP_FLIGHT_KPI kpi_value_sq_d", response.data);
                    if (response.data != undefined) {
                        all_company_arp_flight_data['kpi_value_sq_d'] = response.data;
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    loadFacCompArpFlightKpi_ratio_sq() {
        let arpCodelist = org_arp_base[current_company_code] ? org_arp_base[current_company_code].map((item) => {
            return item.arpCode;
        }) : new Array();
        return new Promise((resolve, reject) => {
            let param = {
                "SOLR_CODE": "FAC_COMP_ARP_FLIGHT_KPI",
                'COMP_CODE': current_company_code,
                "AIRPORT_CODE": arpCodelist.join(","),
                'KPI_CODE': 'SCH_NO,CKI_NUM,NORMAL_NO_T,SCH_NO',
                "VALUE_TYPE": "kpi_ratio_sq_d",
                'DATE_TYPE': 'L,M,Y',
                "LIMIT": query_limit,
                "OPTIMIZE": 1,
            };
            $.ajax({
                type: 'post',
                url: "/bi/query/getkpi",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    console.log("FAC_COMP_ARP_FLIGHT_KPI kpi_ratio_sq_d", response.data);
                    if (response.data != undefined) {
                        all_company_arp_flight_data['kpi_ratio_sq_d'] = response.data;
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    loadFacCompArpFlightBaseIntKpi_d() {
        let arpCodelist = org_arp_base[current_company_code] ? org_arp_base[current_company_code].map((item) => {
            return item.arpCode;
        }) : new Array();
        return new Promise((resolve, reject) => {
            let param = {
                "SOLR_CODE": "FAC_COMP_ARP_FLIGHT_KPI",
                'COMP_CODE': current_company_code,
                "AIRPORT_CODE": arpcodes.join(","),
                'KPI_CODE': 'NORMAL_NO_T,SCH_NO',
                "VALUE_TYPE": "kpi_value_d",
                'DATE_TYPE': 'L,M,Y',
                "LIMIT": query_limit,
                "OPTIMIZE": 1,
            };
            $.ajax({
                type: 'post',
                url: "/bi/query/getkpi",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    console.log("FAC_COMP_ARP_FLIGHT_BASE_INT_KPI kpi_value_d", response.data);
                    if (response.data != undefined) {
                        all_company_arp_flight_base_int_data['kpi_value_d'] = response.data;
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    loadOrgArpBase() {
        return new Promise((resolve, reject) => {
            $.ajax({
                type: 'post',
                url: "/bi/query/getRelOrgArpBase",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                success: function (response) {
                    console.log("getRelOrgArpBase", response.data);
                    if (response.data != undefined) {
                        org_arp_base = response.data;
                    }
                    resolve(response.errorcode);
                },
                error: function (response) {
                    reject(response.errorcode);
                }
            });
        });
    }

    loadAcType() {
        return new Promise((resolve, reject) => {
            ac_data_list = {};
            var company = current_company_code == parent_company ? '' : current_company_code;
            var url = `/bi/spring/aircraft/getRegisterdAircraftStat?company=${company}`
            $.ajax({
                type: 'get',
                url: url,
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                success: function (response) {
                    console.log(response);

                    var data = response.data;
                    $("#totalFlightNum2").html(data.total);

                    for (var i = 0; i < data.acStat.length; i++) {
                        ac_data_list[data.acStat[i].actype] = data.acStat[i].cnt
                    }

                    resolve(response.errorcode);

                }
            });
        });
    }

    loadSetting() {
        return new Promise((resolve, reject) => {
            let param = {
                'mode': 'query',
                "comp_id": companyCode2Id[current_company_code]
            };
            $.ajax({
                type: 'post',
                url: "/bi/web/overall_operation_setting",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    setting = response.data[0];
                    console.log("setting", setting);
                    resolve(response.errorcode);
                }
            })
        });
    }
    loadMedia() {
        return new Promise((resolve, reject) => {
            let param = {
                "mode": "query",
                "comp_id": companyCode2Id[current_company_code]
            };
            $.ajax({
                type: 'post',
                url: "/bi/web/overall_operation_files",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    let lst = response.data;
                    mediaList = new Array();
                    for (let i = lst.length - 1; i >= 0; i--) {
                        let itm = lst[i];
                        if (Number(itm.HIDDEN) == 0) {
                            mediaList.push(itm);
                        }
                    }
                    mediaList.sort(function (a, b) {
                        return Number(a.SORT) - Number(b.SORT);
                    });

                    if (currentMediaId > mediaList.length - 1) {
                        currentMediaId = 0;
                    }
                    console.log("mediaList", mediaList);
                    resolve(response.errorcode);
                }
            })
        });

    }
    loadBaseInt() {
        return new Promise((resolve, reject) => {
            let param = {
                "mode": "query",
                "COMP_CODE": current_company_code
            };
            $.ajax({
                type: 'post',
                url: "/bi/web/base_int",
                contentType: 'application/json',
                dataType: 'json',
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                    baseintList = response.data;
                    console.log("baseint", response.data);
                    arpcodes = new Array();
                    baseintList.forEach((v, i) => {
                        arpcodes.push(v.ARP_CODE);
                    });
                    if (arpcodes.length > 0) {
                        param = {
                            "AIRPORT_CODE": arpcodes.join(","), // 可选，传入机场CODE
                        };
                        $.ajax({
                            type: 'post',
                            url: "/bi/web/airportdetail",
                            contentType: 'application/json',
                            dataType: 'json',
                            async: false,
                            data: JSON.stringify(param),
                            success: function (response) {
                                arpdetails = response.airport;
                                console.log("airportdetail", arpdetails);
                                resolve(response.errorcode);
                            },
                            error: function () { }
                        });
                    } else {
                        resolve(response.errorcode);
                    }
                }
            })
        });
    }
    getWeekDateRange(week_list) {
        let param = {
            "DATE_ID": week_list.join(','),
            "FIELD": "DATE_DESC" // 对应数据表字段 DATE_DESC_XS
        };
        $.ajax({
            type: 'post',
            url: "/bi/web/datetype",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {
                for (const k in response) {
                    const v = response[k];
                    if (!isNaN(k)) {
                        const arr = v.split('-');
                        const weeknum = Number(k.substr(4, 3));

                        const d1 = arr[0].substr(0, 4) + '-' + arr[0].substr(4, 2) + '-' + arr[0].substr(6, 2);
                        const d2 = arr[1].substr(0, 4) + '-' + arr[1].substr(4, 2) + '-' + arr[1].substr(6, 2);

                        weekDateRangeList.push({
                            date: k,
                            range: [d1, d2],
                            weeknum: weeknum
                        });
                    }
                }
                // 从大->小
                weekDateRangeList.sort(function (a, b) {
                    return b.date - a.date
                });

            }
        });
    }

    resetPop(params) {
        this._hidePop();
        $("#pop_title").text(params.value[3]);
        $("#pop_date").text($("#main_cb_" + date_type).find(".combobox_label").text());
        $("#pop_hbzl").text(params.value[4]);
        $("#pop_hbzl_hb").text(params.value[5]);
        $("#pop_ckxsl").text(params.value[6]);
        $("#pop_ckxsl_hb").text(params.value[7]);
        $("#pop_zcl").text(params.value[8]);
        $("#pop_zcl_hb").text(params.value[9]);
        $("#pop_comp_case").text(params.value[10]);
        $("#pop_none_case").text(params.value[11]);
        this._showPop();
    }

    getCurrentDate() {
        var date = '';
        if (date_type === 'L') {
            date = $('#main_cb_L').attr('data');
        } else if (date_type === 'M') {
            date = $('#main_cb_M').attr('data');
        } else if (date_type === 'Y') {
            date = $('#main_cb_Y').attr('data');
        }
        return date;
    }

    _addEvent() {
        $("#btn_go_run_today").click(function () {
            this._windowOpen(`/largescreen/general/today_operation.html?scale=auto#${current_company_code}`, '_self');
        }.bind(this));
        $(".date_type_select .tab").click((event) => {
            $(".date_type_select .tab").removeClass('hover');
            $(event.currentTarget).addClass('hover');
            date_type = $(event.currentTarget).data('type');
            $(".limcomb").addClass('hidden');
            if (date_type === "L") {
                $("#main_cb_L").removeClass('hidden');
            } else if (date_type === "M") {
                $("#main_cb_M").removeClass('hidden');
            } else if (date_type === "Y") {
                $("#main_cb_Y").removeClass('hidden');
            }
            click_param = undefined;
            this._hidePop();
            this.setData(this);
        });

        $(document).on("click", ".combobox_list .item", (event) => {
            $(event.currentTarget).closest('.combobox_list').hide();
            $(event.currentTarget).closest('.limcomb').find('.combobox_label').text($(event.currentTarget).text());
            $(event.currentTarget).closest('.limcomb').attr('data', $(event.currentTarget).attr('data'));
            click_param = undefined;
            this._hidePop();
            this.setData(this);
        });
        $(document).on("click", "#popover_map .close", (event) => {
            click_param = undefined;
            this._hidePop();
        });


    }

    _initMap() {
        myChart.setOption(option);
    }

    _initSelectDate(week_list) {
        ////// 日期下拉菜单 L
        let cblist = new Array();
        let len = week_list.length;
        let last_date;
        for (let i = 0; i < len; i++) {
            const date = week_list[i];
            const weeknum = Number(date.substr(4, 3));
            const label = '第' + (weeknum) + '周 ';
            cblist.unshift({
                'label': label,
                'data': date
            });
            const year = Number(date.substr(0, 4));
            const lastNum = weeknum - 1;
            if (lastNum == 0) {
                const date2 = (year - 1) + '05300';
                weekMaps[weeknum] = date2;
            }
            weekMaps[weeknum + 1] = date;
            last_date = date;
        }
        this._createComboBox('main_cb_L', cblist, 133, 240, this.setData, 1);

        // 显示 week 日期范围
        $('#main_cb_L .combobox_label').on('mouseover', function (event) {
            event.preventDefault();
            if (weekDateRangeList) {
                let date = $('#main_cb_L').attr('data');

                let len = weekDateRangeList.length;
                for (let i = 0; i < len; i++) {
                    let wdat = weekDateRangeList[i];
                    if (wdat.date == date) {
                        let str = wdat.range[0] + '~' + wdat.range[1];
                        str = str.replace(/-/g, '');
                        $('#date_range').text(str);
                        $('#date_range').fadeIn();
                        break;
                    }
                }
            }
        });

        // 隐藏 week 日期范围
        $('#main_cb_L .combobox_label').on('mouseout', function (event) {
            event.preventDefault();
            if (weekDateRangeList) {
                $('#date_range').fadeOut();
            }
        });
        $('#main_cb_L .combobox_label').on('click', function (event) {
            event.preventDefault();
            if (weekDateRangeList) {
                $('#date_range').fadeOut();
            }
        });
        ////// 日期下拉菜单 M
        cblist = new Array();
        len = 20;
        let now = moment();
        for (var i = 0; i < len; i++) {
            if (i > 0) {
                now.subtract(1, 'months');
            }
            const date = now.format('YYYYMM');
            const label = now.format('YYYY') + '年' + now.format('M') + '月';
            cblist.push({
                'label': label,
                'data': date
            });
        }
        this._createComboBox('main_cb_M', cblist, 133, 240, this.setData, 1);

        // 显示 month 日期范围
        $('#main_cb_M .combobox_label').on('mouseover', function (event) {
            event.preventDefault();
            const month = $('#main_cb_M').attr('data');
            const curmonth = moment().format("YYYY-MM");
            const numofdays = moment(month, "YYYY-MM").daysInMonth(); // 获取一个月有几天
            let days = numofdays;
            if (days < 10) {
                days = '0' + days;
            }
            if (curmonth == month) {
                days = moment().format("DD");
            }
            let str = month + '-01' + '~' + month + '-' + days;
            str = str.replace(/-/g, '');
            $('#date_range').text(str);
            $('#date_range').fadeIn();
        });

        // 隐藏 month 日期范围
        $('#main_cb_M .combobox_label').on('mouseout', function (event) {
            event.preventDefault();
            $('#date_range').fadeOut();
        });
        $('#main_cb_M .combobox_label').on('click', function (event) {
            event.preventDefault();
            $('#date_range').fadeOut();
        });
        ////// 日期下拉菜单 Y
        cblist = new Array();
        len = 3;
        now = new Date();
        const year = now.getFullYear();
        for (let i = 0; i < len; i++) {
            let date = year - i;
            let label = date + '年';
            cblist.push({
                'label': label,
                'data': date
            });
        }

        this._createComboBox('main_cb_Y', cblist, 133, 240, this.setData, 0);
    }

    _setIndicatorContainer(id, value) {
        $('#' + id).html("");
        let indicatorContainer_plf = $('#' + id).radialIndicator({
            barColor: '#00a9ff',
            barWidth: 10,
            radius: 44,
            barBgColor: '#023979',
            percentage: true,
            fontSize: 16,
            fontColor: '#fff',
            precision: 2
        }).data('radialIndicator');
        indicatorContainer_plf.value(value);
    }

    _drawDelay(canvasId, rate) {
        const canvas = document.getElementById(canvasId);
        const context = canvas.getContext('2d');
        const x = canvas.width / 2;
        const y = canvas.height / 2;
        const radius = 48;
        context.beginPath();
        context.arc(x, y, radius, 0, 2 * Math.PI, false);
        context.lineWidth = 12;
        context.strokeStyle = '#02B0F9';
        context.stroke();

        // draw green arc
        let startAngle = Math.PI - (Math.PI * 2 * rate / 2);
        let endAngle = Math.PI + (Math.PI * 2 * rate / 2);
        context.beginPath();
        context.arc(x, y, radius, startAngle, endAngle, false);
        context.lineWidth = 12;
        context.strokeStyle = '#da9a1a';
        context.stroke();

        // draw lines
        const numslice = 12;
        for (let i = 0; i < numslice; i++) {
            context.beginPath();
            startAngle = i * (Math.PI * 2 / numslice);
            endAngle = startAngle + Math.PI * 0.01;
            context.arc(x, y, radius, startAngle, endAngle, false);
            context.lineWidth = 12;
            context.strokeStyle = '#041946';
            context.stroke();
        }
    }

    _drawGauge(canvasId, pointerId, rate, iswarning) {
        let angle;
        let color;
        const canvas = document.getElementById(canvasId);
        const context = canvas.getContext('2d');
        context.clearRect(0, 0, canvas.width, canvas.height);
        const x = canvas.width / 2;
        const y = canvas.height / 2;

        // draw back
        let radius = 48;
        const startAngle = Math.PI - Math.PI / 5;
        const endAngle = startAngle + Math.PI + Math.PI / 5 * 2;
        let counterClockwise = false;

        context.beginPath();
        context.arc(x, y, radius, startAngle, endAngle, counterClockwise);
        context.lineWidth = 12;
        context.strokeStyle = iswarning ? '#916900' : '#004a91';
        context.stroke();

        // draw overlay
        const startAngle2 = startAngle;
        const endAngle2 = startAngle + (endAngle - startAngle) * rate;
        counterClockwise = false;

        context.beginPath();
        context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
        context.lineWidth = 12;

        if (rate <= 1) {
            // linear gradient
            if (rate < 0.5) {
                color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
            } else if (rate < 0.8) {
                color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
            } else {
                color = context.createLinearGradient(0, canvas.height, canvas.width * rate, canvas.width / 2);
            }
            color.addColorStop(0, iswarning ? '#F1F304' : '#5f5fff');
            color.addColorStop(0.5, iswarning ? '#F3D304' : '#4185e4');
            color.addColorStop(1, iswarning ? '#f39800' : '#00d3ff');
            context.strokeStyle = color;
            context.stroke();
            // pointer
            angle = startAngle + (endAngle - startAngle) * rate;
            $('#' + pointerId).css('transform', 'rotate(' + (angle / Math.PI * 180) + 'deg)');

        } else {
            // pointer
            angle = startAngle;
            $('#' + pointerId).css('transform', 'rotate(' + (angle / Math.PI * 180) + 'deg)');
        }


    }
    _setchartWarning(id, pointer) {
        $("#" + id).siblings(".mk").css("color", "#f39800");
        $("#" + pointer).attr("src", "img/gauge_pointer_warning_102x102.svg");
    }
    _setchartDefault(id, pointer) {
        $("#" + id).siblings(".mk").css("color", "#44a3f4");
        $("#" + pointer).attr("src", "img/gauge_pointer_102x102.svg");
    }
    _createComboBox(id, list, width, maxheight, callback, selectedIndex) {
        let html = '';
        html += '<span class="combobox_label"></span>';

        const len = list.length;
        let listhtml = '';
        for (let i = 0; i < len; i++) {
            const obj = list[i];
            listhtml += '<span class="item" data="' + obj.data + '">' + obj.label + '</span>';
        }
        html += '<span class="combobox_list">' + listhtml + '</span>';
        $('#' + id).html(html);
        $('#' + id).addClass('combobox');
        $('#' + id).css('width', width + 'px');
        $('#' + id + ' .combobox_list').css('max-height', maxheight + 'px');
        $('#' + id + ' .combobox_list').hide();

        $('#' + id + ' .combobox_label').on('click', function () {
            if ($('#' + id + ' .combobox_list').is(':visible')) {
                $('#' + id + ' .combobox_list').hide();
            } else {
                $('#' + id + ' .combobox_list').show();
            }
        });

        let active = false;
        let tmo;
        $('#' + id).on('mouseover', function () {
            clearTimeout(tmo);
            active = true;
        });
        $('#' + id).on('mouseout', function () {
            active = false;
            tmo = setTimeout(checkActive, 300);
        });

        function checkActive() {
            if (!active) {
                $('#' + id + ' .combobox_list').hide();
            }
        }

        if (!selectedIndex) {
            // select first item
            $('#' + id + ' .combobox_label').text(list[0].label);
            $('#' + id).attr('data', list[0].data);
        } else {
            $('#' + id + ' .combobox_label').text(list[selectedIndex].label);
            $('#' + id).attr('data', list[selectedIndex].data);
        }

    }

    _formatNum(n) {
        if (n < 10) {
            return ('0' + n);
        } else {
            return n;
        }
    }

    _getEngMonth(month) {
        var mlist = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Spt", "Oct", "Nov", "Dec"];
        return mlist[month].toUpperCase();
    }

    _showPop() {
        $('#popover_map').css('pointer-events', 'auto');

        $('#popover_map').animate({
            left: '771px',
            top: '84px',
            opacity: 1,
        }, {
            queue: false,
            duration: 500,
            step: function (now, fx) {
                if (fx.prop == 'opacity') {
                    $(this).css('transform', 'scale(' + now + ')');
                }
            },
        });
    }

    _hidePop() {
        $('#popover_map').css('transform', 'scale(0.001)');
        $('#popover_map').css('opacity', 0);
        $('#popover_map').css('left', '771px');
        $('#popover_map').css('top', '84px');

    }
    _spArray(N, Q) {
        let R = [], F;
        for (F = 0; F < Q.length;) {
            R.push(Q.slice(F, F += N))
        }
        return R;
    }
    _windowOpen(url, target) {
        var a = document.createElement("a");
        a.setAttribute("href", url);
        if (target == null) {
            target = '';
        }
        a.setAttribute("target", target);
        document.body.appendChild(a);
        if (a.click) {
            a.click();
        } else {
            try {
                var evt = document.createEvent('Event');
                a.initEvent('click', true, true);
                a.dispatchEvent(evt);
            } catch (e) {
                window.open(url);
            }
        }
        document.body.removeChild(a);
    }
    _fillSeries(airplan_option, series, ObjectItem) {
        ObjectItem = ObjectItem.map((v, i) => {
            return [v[0], v[1], colors[i]]
        });
        let label = new Array();
        let pictorialBarData = new Array();
        let pictorialBarData1 = new Array();
        let barData = new Array();
        ObjectItem.forEach((v, i) => {
            label.push(v[0]);
            pictorialBarData1.push(1);
            pictorialBarData.push({
                value: v[1],
                symbolPosition: 'end',
                label: {
                    show: true,
                    position: 'top',
                    formatter: '{a|}',
                    rich: {
                        a: {
                            height: 15,
                            backgroundColor: {
                                image: planinfos[v[2]]
                            }
                        }
                    }
                }
            });
            barData.push({
                value: v[1],
                label:
                {
                    show: true,
                    lineHeight: 70,
                    color: '#fff',
                    formatter: '{c}架',
                    position: 'top'
                }
            });
        });

        let data1 = {
            name: '',
            type: 'pictorialBar',
            symbol: 'diamond',
            symbolSize: [30, 15],
            symbolOffset: [0, -8],
            z: 12,
            itemStyle: {
                normal: {
                    color: function (params) {
                        const colorList = ['#14b1eb', '#F39800', '#2BCA44', '#EB6877'];
                        return colorList[params.dataIndex]
                    }
                }
            },
            data: pictorialBarData
        };

        let data2 = {
            name: '',
            type: 'pictorialBar',
            symbol: 'diamond',
            symbolSize: [30, 15],
            symbolOffset: [0, 8],
            z: 12,
            itemStyle: {
                normal: {
                    color: function (params) {
                        const colorList = ['#14b1eb', '#F39800', '#2BCA44', '#EB6877'];
                        return colorList[params.dataIndex]
                    }
                }
            },
            data: pictorialBarData1
        };

        let data3 = {
            type: 'bar',
            itemStyle: {
                normal: {
                    color: function (params) {
                        const colorList = ['#14b1eb', '#F39800', '#2BCA44', '#EB6877'];
                        return colorList[params.dataIndex]
                    },
                    opacity: .7
                }
            },
            silent: true,
            barWidth: 30,
            data: barData
        }
        series.push(data1, data2, data3);
        airplan_option.xAxis.data = label;
        airplan_option.series = series;
    }
}

var ov = new Overall_operation();
deferGeneral.done(function () {
    initSelectCompany()
    if (!hasAllCompanyPermission()) {
        $(".all-company-rank").remove();
    }
});
setInterval(() => {
    ov.setTime()
}, 1000);
function initSelectCompany() {
    const company = window.location.hash.substr(1);
    if (!company) {
        switchCompany(selected_company_code);
    } else {
        switchCompany(company);
    }
}
function onCompanyChanged(comp_code) {
    current_company_code = comp_code;
    ov ? ov._hidePop() : new Overall_operation()._hidePop();
    ov ? ov.loadAll() : new Overall_operation().loadAll();
}