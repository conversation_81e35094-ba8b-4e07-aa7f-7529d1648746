(function() {

    // new Vue({
    //     el: '#app',
    //     template: '<App/>',
    //     components: {
    //         App
    //     }
    // })

    var page = new Vue({
        el: '#app',
        template: $("#template").html(),
        data: function(){
            return {
                'whatthefuck': '',
                'btntext': '确定',
                'userinfo': '',
                'tokenid':''
                // vue的数据集合
            }
        },
        methods: {
            showWord: function(){

            },
            btntest: function(){
                alert();
            }
        },
        mounted: function(){
            var _this = this;
            var _inner = function(){
                if(typeof userinfo == "undefined"){
                    setTimeout(_inner, 500);
                }else{
                    _this.userinfo = userinfo;
                    _this.tokenid = userinfo.tokenid;
                }
            };
            _inner();
        }
    });

    console.log(page)

})()