showLoading();


// ------------------------------------------------------------

$('#mainframe').attr('src', 'map/map-a3-1.html');

// ------------------------------------------------------------



var current_company_code;


// 可显示的历史 数量
var query_limit = 20;
// 日期类型
var date_type = 'L';

// FAC_COMP_ACTYPE_KPI 最新有数据的日期 D
var latest_date_ac;

var actypelist = '787,767,330,333,737,320,319,145,190';



// -------------------------------------------------------

//                          KPI

// -------------------------------------------------------


var weekList = [];
var monthList = [];


var all_company_data; // 存储各个公司基本指标
var all_company_data_ac; // 飞机日利用率 计算指标
var all_company_data_task; // 存储预算任务
var ac_list_except_other_type; // 存储机型

var weekDateRangeList; //例会周的日期对应表
var latest_week = 0;

var fetchingKpiData = false;



// 机型id／code对应表
var actypeId2Code;

// 获取所有机型
var param = {}

$.ajax({
  type: 'post',
  url: "/bi/web/actypeall",
  contentType: 'application/json',
  dataType: 'json',
  async: true,
  data: JSON.stringify(param),
  success: function (response) {
    console.log("actypeId2Code finished")
    if (response.actype) {
      var list = response.actype;
      actypeId2Code = {};


      var len = list.length;
      for (var i = 0; i < len; i++) {
        var obj = list[i];
        actypeId2Code[obj.id] = obj.code;
      }

    }

  },
  error: function () { }
});



function getAllCompanyKpiData () {

  if (companylist.length == 0) {
    setTimeout(getAllCompanyKpiData, 0);
    return;
  }

  if (fetchingKpiData) return;


  // check url hash
  var hash = window.location.hash.substr(1);
  if (hash && companyCode2Name[hash] != undefined) {
    current_company_code = hash;
  } else {
    current_company_code = parent_company;
  }


  fetchingKpiData = true;

  all_company_data = {};

  var comp_code = current_company_code;

  var len = companylist.length;
  var codelist = [];
  var codelist_no_parent = [];
  for (var i = 0; i < len; i++) {

    var dat = companylist[i];
    codelist.push(dat.code);
    if (dat.code != parent_company) {
      codelist_no_parent.push(dat.code);
    }
  }


  var loadingInProgress = 0;
  // 10247 NORMAL_RATE_ZT 正常率-数据来源中台
  // 10248 NORMAL_RATE_ZT_MOLECULE 正常率分子-数据来源中台
  // 10249 NORMAL_RATE_ZT_DENOMINATOR 正常率分母-数据来源中
  var kpi_list = [
    //'EXCUTED_NO', //执行班次
    //'TRV_NUM', //旅客量
    'CKI_NUM', //已执行运输旅客量
    'NORMAL_NO_T', //正常班次
    'NORMAL_RATE_ZT', //正常率-数据来源中台
    'SCH_NO', //计划班次
    'SCH_NO_INT', //国际计划班次
    'SCH_NO_INL', //国内计划班次
    //'EXCUTED_NO', //执行班次
    //'EXCUTED_NO_INT', //国际执行班次
    //'EXCUTED_NO_INL', //国内执行班次
  ];

  // -------------------------------------------------------
  // 周
  // -------------------------------------------------------


  // 本期

  loadingInProgress++;
  var param = {
    'SOLR_CODE': 'FAC_COMP_KPI',
    'COMP_CODE': codelist.join(','),
    'KPI_CODE': kpi_list.join(','),
    'VALUE_TYPE': 'kpi_value_d', //本期
    'DATE_TYPE': 'L,M',
    "OPTIMIZE": 1,
    'LIMIT': query_limit
  }

  $.ajax({
    type: 'post',
    url: "/bi/query/getkpi",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {

      if (response.data != undefined) {

        all_company_data['kpi_value_d'] = response.data;

        var ddd = response.data['HU']['SCH_NO']['L'];

        weekList = [];
        for (var date in ddd) {
          weekList.push(date);
        }

        //weekList.reverse();
        //weekList = weekList.slice(0, query_limit);
        //weekList.reverse();

        getWeekDateRange(weekList, setWeekCB);

        function setWeekCB (latest_week) {
          var cblist = [];
          var len = weekList.length;
          for (var i = 0; i < len; i++) {
            var date = weekList[i];
            if (Number(latest_week) >= Number(date)) {
              // Week: *********
              //var label = date.substr(0,4) + '年第' + Number(date.substr(4,3)) + '周';
              //20170419 双双要求：显示的周数+1
              var label = '第' + (Number(date.substr(4, 3))) + '周 '; //+ response[date];
              cblist.unshift({
                'label': label,
                'data': date
              });
            }
          }
          // cblist = cblist.slice(0,6);
          createComboBox('main_cb_week', cblist, 84, 240, updateAllKpi, 1);


          $('#main_cb_week .combobox_label').on('click', function (event) {
            event.preventDefault();

            if (date_type == 'L') {
              return;
            }

            $('#main_cb_week').addClass('combotab_selected');
            $('#main_cb_week').removeClass('combotab');
            $('#main_cb_month').addClass('combotab');
            $('#main_cb_month').removeClass('combotab_selected');

            date_type = 'L';
            updateAllKpi();

          });

          // 显示 week 日期范围
          $('#main_cb_week .combobox_label').on('mouseover', function (event) {
            event.preventDefault();
            if (weekDateRangeList) {
              var date = $('#main_cb_week').attr('data');
              $('#week_date_range').text(weekDateRangeList[date]);
              $('#week_date_range').fadeIn();
            }
          });

          // 隐藏 week 日期范围
          $('#main_cb_week .combobox_label').on('mouseout', function (event) {
            event.preventDefault();
            if (weekDateRangeList) {
              $('#week_date_range').fadeOut();
            }
          });


        }



        // M
        var datlist = response.data['HU']['SCH_NO']['M'];
        monthList = [];
        for (var date in datlist) {
          monthList.push(date);
        }

        monthList.reverse();
        monthList = monthList.slice(0, query_limit);
        monthList.reverse();

        var date = new Date();
        var month = date.getMonth() + 1;
        var day = date.getDate();
        if (month < 10) {
          month = '0' + month;
        }
        var nowmonth = date.getFullYear() + '' + month;

        var cblist = [];
        var len = monthList.length;
        for (var i = 0; i < len; i++) {
          var date = monthList[i];
          if (date <= nowmonth) {
            var label = date.substr(0, 4) + '年' + Number(date.substr(4, 2)) + '月';
            cblist.unshift({
              'label': label,
              'data': date
            });
          }

        }
        // cblist = cblist.slice(0,6);
        createComboBox('main_cb_month', cblist, 104, 240, updateAllKpi, 0);

        $('#main_cb_month .combobox_label').on('click', function (event) {
          event.preventDefault();

          if (date_type == 'M') {
            return;
          }

          $('#main_cb_week').addClass('combotab');
          $('#main_cb_week').removeClass('combotab_selected');
          $('#main_cb_month').addClass('combotab_selected');
          $('#main_cb_month').removeClass('combotab');


          date_type = 'M';
          updateAllKpi();
        });


        // 显示 month 日期范围
        $('#main_cb_month .combobox_label').on('mouseover', function (event) {
          event.preventDefault();
          var month = $('#main_cb_month').attr('data');
          var curmonth = moment().format("YYYYMM");
          var numofdays = moment(month, "YYYYMM").daysInMonth(); // 获取一个月有几天
          var days = numofdays;
          if (days < 10) {
            days = '0' + days;
          }
          if (curmonth == month) {
            days = moment().format("DD");
          }
          $('#week_date_range').text(month + '01' + '~' + month + days);
          $('#week_date_range').fadeIn();
        });

        // 隐藏 month 日期范围
        $('#main_cb_month .combobox_label').on('mouseout', function (event) {
          event.preventDefault();
          $('#week_date_range').fadeOut();
        });
        $('#main_cb_month .combobox_label').on('click', function (event) {
          event.preventDefault();
          $('#week_date_range').fadeOut();
        });


        loadingInProgress--;
        checkDataReady();



      }

    },
    error: function () { }
  });


  // 上期 用来计算环比
  loadingInProgress++;
  var param = {
    'SOLR_CODE': 'FAC_COMP_KPI',
    'COMP_CODE': codelist.join(','),
    'KPI_CODE': kpi_list.join(','),
    'VALUE_TYPE': 'kpi_value_sq_d', //上期
    'DATE_TYPE': 'L,M',
    "OPTIMIZE": 1,
    'LIMIT': query_limit
  }

  $.ajax({
    type: 'post',
    url: "/bi/query/getkpi",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {

      if (response.data != undefined) {
        all_company_data['kpi_value_sq_d'] = response.data;
        loadingInProgress--;
        checkDataReady();
      }

    },
    error: function () { }
  });


  // 同期 用来计算同比
  loadingInProgress++;
  var param = {
    'SOLR_CODE': 'FAC_COMP_KPI',
    'COMP_CODE': codelist.join(','),
    'KPI_CODE': kpi_list.join(','),
    'VALUE_TYPE': 'kpi_value_tq_d', //同期
    'DATE_TYPE': 'L,M',
    "OPTIMIZE": 1,
    'LIMIT': query_limit
  }

  $.ajax({
    type: 'post',
    url: "/bi/query/getkpi",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {

      if (response.data != undefined) {
        all_company_data['kpi_value_tq_d'] = response.data;
        loadingInProgress--;
        checkDataReady();
      }

    },
    error: function () { }
  });



  //获取整年所有周的预算任务
  /*
  var param = {
  'SOLR_CODE': 'FAC_COMP_KPI',
  'COMP_CODE': codelist.join(','),
  'KPI_CODE': 'FLY_TIME_TASK,AC_NUM_TASK',
  'VALUE_TYPE': 'kpi_value_d',  
  'DATE_TYPE': 'L,M',
  'LIMIT': 60
  }

  $.ajax({
      type: 'post',
      url:"/bi/query/getkpi",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function(response) {

          all_company_data_task = response.data;

          setBlockR2Kpi();
          
      },
      error:function() {
      }
  });
  */

  // -------------------------------------------------------
  // 获取所有机型 / 在统计日利用率时排除“其它”类型的飞机
  // -------------------------------------------------------
  /*
  var param = {
  }

  $.ajax({
      type: 'post',
      url:"/bi/web/actypeall",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function(response) {

          ac_list_except_other_type = [];

          if(response.actype){
              var list = response.actype;

              var len = list.length;
              for(var i=0; i<len; i++){
                  var obj = list[i];
                  if(obj.actyptyp == 'Wide' || obj.actyptyp == 'Narrow' || obj.actyptyp == 'Regional'){
                      // 宽体机,窄体机,支线机
                      ac_list_except_other_type.push(obj.code);
                  }
              }

              // 获取日利用率相关指标
              getAcKpiData();
          }
          
      },
      error:function() {
      }
  });
  */


  // -------------------------------------------------------
  // 日利用率
  // -------------------------------------------------------
  /*
  function getAcKpiData(){

      var param = {
      'SOLR_CODE': 'FAC_COMP_ACTYPE_KPI',
      'COMP_CODE': codelist_no_parent.join(','),
      'KPI_CODE': 'FLY_TIME,AC_NUM', // 飞行时间、飞机架次 == 涌来计算飞机日利用率
      'VALUE_TYPE': 'kpi_value_d', //本期
      'DATE_TYPE': 'L,M',
      'ACTYPE': actypelist,
      'LIMIT': query_limit
      }

      $.ajax({
          type: 'post',
          url:"/bi/query/getackpi",
          contentType: 'application/json',
          dataType: 'json',
          async: true,
          data: JSON.stringify(param),
          success: function(response) {

              if(response.data != undefined){

                  if(all_company_data_ac == undefined){
                      all_company_data_ac = {};
                  }
                  all_company_data_ac['kpi_value_d'] = response.data;
                  
                  setBlockR2Kpi();

              }
              
          },
          error:function() {
          }
      });

  }
  */



  // 获取 FAC_COMP_ACTYPE_KPI 表最新的有数据的日期
  var param = {
    'SOLR_CODE': 'FAC_COMP_ACTYPE_KPI',
    'COMP_CODE': parent_company,
    'KPI_CODE': 'AC_NUM',
    'VALUE_TYPE': 'kpi_value_d',
    'DATE_TYPE': 'D',
    "OPTIMIZE": 1,
    'ACTYPE': '737', //
    'LIMIT': 1
  }

  $.ajax({
    type: 'post',
    url: "/bi/query/getackpi",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      console.log("latest_date_ac finsined")
      if (response.data != undefined) {
        var ddd = response.data[parent_company]['AC_NUM']['D']['data3'];
        var len = ddd.length;

        for (var i = 0; i < len; i++) {
          var ac = ddd[i];
          var dlist = ac.date
          var len2 = dlist.length;
          for (var j = 0; j < len2; j++) {
            var da = dlist[j];
            latest_date_ac = da.date;
            break;
          }
        }

      }
    },
    error: function () { }
  });



  // 获取例会周对应的日期范围
  function getWeekDateRange (week_list, callback) {
    var param = {
      "DATE_ID": week_list.join(','),
      "FIELD": "DATE_DESC" // 对应数据表字段 DATE_DESC
      //"FIELD":"DATE_TYPE" // 对应数据表字段 DATE_DESC_XS
    }

    $.ajax({
      type: 'post',
      url: "/bi/web/datetype",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function (response) {
        console.log("weekDateRangeList finished")
        weekDateRangeList = response;

        // 今天
        var date = new Date();
        var mm = date.getMonth() + 1;
        var dd = date.getDate();
        if (mm < 10) {
          mm = '0' + mm;
        }
        if (dd < 10) {
          dd = '0' + dd;
        }
        var latest_date = date.getFullYear() + '' + mm + '' + dd;

        // 删除开始日期>今天的
        for (var week in weekDateRangeList) {
          var range = weekDateRangeList[week];
          var arr = range.split('-');
          if (Number(arr[0]) > Number(latest_date)) {
            delete weekDateRangeList[week];
          }
        }

        //把最新周的结束日期换成上面查到的最新日期

        if (latest_date) {
          for (var week in weekDateRangeList) {
            if (Number(week) > Number(latest_week)) {
              latest_week = week;
            }
          }
          if (latest_week != 0) {
            var date_range = weekDateRangeList[latest_week];
            var arr = date_range.split('-');
            if (Number(arr[1]) > Number(latest_date) && Number(arr[0]) <= Number(latest_date)) {
              //weekDateRangeList[latest_week] = arr[0]+'-'+latest_date;
            }

          }
        }

        if (callback) callback(latest_week);

        checkDataReady();


      },
      error: function () { }
    });
  }



  function checkDataReady () {
    if (loadingInProgress == 0 && weekDateRangeList) {
      kpiDataReady = true;
      updateAllKpi();
      hideLoading();

    }
  }



}



function getCurrentDate () {
  var date = '';
  if (date_type == 'L') {
    date = $('#main_cb_week').attr('data');
  } else if (date_type == 'M') {
    date = $('#main_cb_month').attr('data');
  }
  return date;
}



// 中间地图各个公司kpi
function setMapKpi () {

  if ($('#mainframe')[0].contentWindow.setRateMap != undefined && groupNormalRateGoal != undefined && usersCompayCodeList.length > 0) {

    var compdata = all_company_data['kpi_value_d'];
    var date = getCurrentDate();
    var date_normal_rate; // 正常率用的周数要+1

    if (date_type == 'L') {
      // Week: *********
      date_normal_rate = date;
    } else {
      date_normal_rate = date;
    }

    $('#mainframe')[0].contentWindow.setRateMap(usersCompayCodeList, compdata, date_type, date_normal_rate, groupNormalRateGoal);

  } else {

    setTimeout(setMapKpi, 10);

  }
}


// 求和所有子公司的指标
function sumKpi (data_type, kpi_code, date) {

  if (date == undefined) {
    date = getCurrentDate();
  }

  var sum = 0;

  if (current_company_code == parent_company) {

    var len = companylist.length;
    for (var i = 0; i < len; i++) {

      var dat = companylist[i];
      var val = all_company_data[data_type][dat.code][kpi_code][date_type][date];
      if (!isNaN(val)) {
        sum += Number(val);
      }
    }

  } else {
    var val = all_company_data[data_type][current_company_code][kpi_code][date_type][date];
    if (!isNaN(val)) {
      sum += Number(val);
    }
  }

  return sum;

}


// 左上角指标块
function setBlockL1Kpi () {

  //$('.block_l1 .tit').text(companyCode2Name[current_company_code]);

  var date = getCurrentDate();
  var date_normal_rate; // 正常率用的周数要+1

  if (date_type == 'L') {
    // Week: *********
    date_normal_rate = date;
  } else {
    date_normal_rate = date;
  }


  $('.block_l1 .tit').text(companyCode2Name[current_company_code]);

  // 航班总量
  // Monica杜 2018-04-12 16:45:07 刚跟业务确认全部用SCH_NO
  //var val_total = sumKpi('kpi_value_d', 'EXCUTED_NO');
  // var val_total = sumKpi('kpi_value_d', 'SCH_NO');
  //var val_total_sq = sumKpi('kpi_value_sq_d', 'EXCUTED_NO');
  // var val_total = all_company_data['kpi_value_d']['HNAHK']['SCH_NO'][date_type][getCurrentDate()];
  var val_total = all_company_data['kpi_value_d'][current_company_code]['SCH_NO'][date_type][getCurrentDate()];
  // var val_total_sq = sumKpi('kpi_value_sq_d', 'SCH_NO');
  // var val_total_sq = all_company_data['kpi_value_sq_d']['HNAHK']['SCH_NO'][date_type][getCurrentDate()];
  var val_total_sq = all_company_data['kpi_value_sq_d'][current_company_code]['SCH_NO'][date_type][getCurrentDate()];
  var shift = val_total;

  // 环比=(本期-上期)/上期×100%
  var hb = Math.round((val_total - val_total_sq) / val_total_sq * 1000) / 10;
  var shift_hb = hb;

  $('.block_l1 .row1 .r1 .kpi .val').text(formatCurrency(Math.round(val_total), 0));
  $('.block_l1 .row1 .r1 .hb .val').text(hb + '%');
  if (hb > 0) {
    $('.block_l1 .row1 .r1 .hb .green').removeClass('hide');
    $('.block_l1 .row1 .r1 .hb .red').addClass('hide');
  } else if (hb < 0) {
    $('.block_l1 .row1 .r1 .hb .green').addClass('hide');
    $('.block_l1 .row1 .r1 .hb .red').removeClass('hide');
  } else {
    $('.block_l1 .row1 .r1 .hb .green').addClass('hide');
    $('.block_l1 .row1 .r1 .hb .red').addClass('hide');
  }


  // 国内 航班量
  //var val = sumKpi('kpi_value_d', 'EXCUTED_NO_INL');
  // var val = sumKpi('kpi_value_d', 'SCH_NO_INL');
  // var val = all_company_data['kpi_value_d']['HNAHK']['SCH_NO_INL'][date_type][getCurrentDate()];
  var val = all_company_data['kpi_value_d'][current_company_code]['SCH_NO_INL'][date_type][getCurrentDate()];
  //var val_sq = sumKpi('kpi_value_sq_d', 'EXCUTED_NO_INL');
  // var val_sq = sumKpi('kpi_value_sq_d', 'SCH_NO_INL');
  // var val_sq = all_company_data['kpi_value_sq_d']['HNAHK']['SCH_NO_INL'][date_type][getCurrentDate()];
  var val_sq = all_company_data['kpi_value_sq_d'][current_company_code]['SCH_NO_INL'][date_type][getCurrentDate()];
  var shift_l = val;

  // 环比=(本期-上期)/上期×100%
  var hb = val_sq > 0 ? Math.round((val - val_sq) / val_sq * 1000) / 10 : 0;
  var shift_l_hb = hb;

  $('.block_l1 .row1 .r2 .c1 .kpi .val').text(formatCurrency(Math.round(val), 0));
  $('.block_l1 .row1 .r2 .c1 .hb .val').text(hb + '%');
  if (hb > 0) {
    $('.block_l1 .row1 .r2 .c1 .hb .green').removeClass('hide');
    $('.block_l1 .row1 .r2 .c1 .hb .red').addClass('hide');
  } else if (hb < 0) {
    $('.block_l1 .row1 .r2 .c1 .hb .green').addClass('hide');
    $('.block_l1 .row1 .r2 .c1 .hb .red').removeClass('hide');
  } else {
    $('.block_l1 .row1 .r2 .c1 .hb .green').addClass('hide');
    $('.block_l1 .row1 .r2 .c1 .hb .red').addClass('hide');
  }



  // 国际 航班量
  //var val = sumKpi('kpi_value_d', 'EXCUTED_NO_INT');
  // var val = sumKpi('kpi_value_d', 'SCH_NO_INT');
  // var val = all_company_data['kpi_value_d']['HNAHK']['SCH_NO_INT'][date_type][getCurrentDate()];
  var val = all_company_data['kpi_value_d'][current_company_code]['SCH_NO_INT'][date_type][getCurrentDate()];
  //var val_sq = sumKpi('kpi_value_sq_d', 'EXCUTED_NO_INT');
  // var val_sq = sumKpi('kpi_value_sq_d', 'SCH_NO_INT');
  // var val_sq = all_company_data['kpi_value_sq_d']['HNAHK']['SCH_NO_INT'][date_type][getCurrentDate()];
  var val_sq = all_company_data['kpi_value_sq_d'][current_company_code]['SCH_NO_INT'][date_type][getCurrentDate()];
  var shift_i = val;

  // 环比=(本期-上期)/上期×100%
  var hb = val_sq > 0 ? Math.round((val - val_sq) / val_sq * 1000) / 10 : 0;
  var shift_i_hb = hb;

  $('.block_l1 .row1 .r2 .c2 .kpi .val').text(formatCurrency(Math.round(val), 0));
  $('.block_l1 .row1 .r2 .c2 .hb .val').text(hb + '%');
  if (hb > 0) {
    $('.block_l1 .row1 .r2 .c2 .hb .green').removeClass('hide');
    $('.block_l1 .row1 .r2 .c2 .hb .red').addClass('hide');
  } else if (hb < 0) {
    $('.block_l1 .row1 .r2 .c2 .hb .green').addClass('hide');
    $('.block_l1 .row1 .r2 .c2 .hb .red').removeClass('hide');
  } else {
    $('.block_l1 .row1 .r2 .c2 .hb .green').addClass('hide');
    $('.block_l1 .row1 .r2 .c2 .hb .red').addClass('hide');
  }

  var normalRateNew = all_company_data['kpi_value_d'][current_company_code]['NORMAL_RATE_ZT'][date_type][getCurrentDate()];
  var normalRateNewSQ = all_company_data['kpi_value_sq_d'][current_company_code]['NORMAL_RATE_ZT'][date_type][getCurrentDate()];
  var hb = Number(normalRateNew) - Number(normalRateNewSQ);

  $('.block_l1 .row2 .r1 .kpi .val').text((Number(normalRateNew) * 100).toFixed(1));
  $('.block_l1 .row2 .r1 .hb .val').text(Number(hb * 100.0).toFixed(1) + '%');

  if (hb > 0) {
    $('.block_l1 .row2 .r1 .hb .green').removeClass('hide');
    $('.block_l1 .row2 .r1 .hb .red').addClass('hide');
  } else if (hb < 0) {
    $('.block_l1 .row2 .r1 .hb .green').addClass('hide');
    $('.block_l1 .row2 .r1 .hb .red').removeClass('hide');
  } else {
    $('.block_l1 .row2 .r1 .hb .green').addClass('hide');
    $('.block_l1 .row2 .r1 .hb .red').addClass('hide');
  }


  // 已执行运输旅客量
  // var val_total = sumKpi('kpi_value_d', 'CKI_NUM');
  // var val_total = all_company_data['kpi_value_d']['HNAHK']['CKI_NUM'][date_type][getCurrentDate()];
  var val_total = all_company_data['kpi_value_d'][current_company_code]['CKI_NUM'][date_type][getCurrentDate()];
  // var val_total_sq = sumKpi('kpi_value_sq_d', 'CKI_NUM');
  // var val_total_sq = all_company_data['kpi_value_sq_d']['HNAHK']['CKI_NUM'][date_type][getCurrentDate()];
  var val_total_sq = all_company_data['kpi_value_sq_d'][current_company_code]['CKI_NUM'][date_type][getCurrentDate()];
  var trv_num = val_total;

  // 环比=(本期-上期)/上期×100%
  var hb = val_total_sq > 0 ? Math.round((val_total - val_total_sq) / val_total_sq * 1000) / 10 : 0;
  var trv_num_hb = hb;

  $('.block_l1 .row3 .r1 .kpi .val').text(formatCurrency(Math.round(val_total / 1000) / 10, 1));
  $('.block_l1 .row3 .r1 .hb .val').text(hb + '%');
  if (hb > 0) {
    $('.block_l1 .row3 .r1 .hb .green').removeClass('hide');
    $('.block_l1 .row3 .r1 .hb .red').addClass('hide');
  } else if (hb < 0) {
    $('.block_l1 .row3 .r1 .hb .green').addClass('hide');
    $('.block_l1 .row3 .r1 .hb .red').removeClass('hide');
  } else {
    $('.block_l1 .row3 .r1 .hb .green').addClass('hide');
    $('.block_l1 .row3 .r1 .hb .red').addClass('hide');
  }


}



// 旅客趋势
function setBlockL2T1Kpi () {
  // ---------------------------
  var chart_id = 'chart_l2t1';
  var kpi_code = '';
  var colors = [
    ['#92bde9', '#65a2de'],
    ['#00b179'],
  ];

  // ---------------------------

  var cur_date = getCurrentDate();

  var xAxisData = [];
  var data_s1 = [];
  var data_s2 = [];

  if (date_type == 'L') {
    var len = weekList.length;
    for (var i = 0; i < len; i++) {
      var date = weekList[i];
      if (Number(cur_date) >= Number(date)) {
        // Week: *********
        //var label = date.substr(0,4) + '年第' + Number(date.substr(4,3)) + '周';
        //20170419 双双要求：显示的周数+1
        var label = '第' + (Number(date.substr(4, 3))) + '周 '; //+ response[date];
        xAxisData.push(label);

        var val = sumKpi('kpi_value_d', 'CKI_NUM', date); //all_company_data['kpi_value_d'][current_company_code]['CKI_NUM'][date_type][date];
        var val_sq = sumKpi('kpi_value_sq_d', 'CKI_NUM', date); //all_company_data['kpi_value_sq_d'][current_company_code]['CKI_NUM'][date_type][date];

        // 环比=(本期-上期)/上期×100%
        var hb = Math.round((val - val_sq) / val_sq * 1000) / 10;

        data_s1.push(Math.round(val / 100) / 100);
        data_s2.push(hb);
      }

    }

    xAxisData = xAxisData.slice(-6);
    data_s1 = data_s1.slice(-6);
    data_s2 = data_s2.slice(-6);

  } else {
    var len = monthList.length;
    for (var i = 0; i < len; i++) {
      var date = monthList[i];
      if (Number(cur_date) >= Number(date)) {
        // Week: *********
        var label = Number(date.substr(4, 2)) + '月 \n ' + date.substr(0, 4);
        xAxisData.push(label);

        var val = sumKpi('kpi_value_d', 'CKI_NUM', date); //all_company_data['kpi_value_d'][current_company_code]['CKI_NUM'][date_type][date];
        var val_sq = sumKpi('kpi_value_sq_d', 'CKI_NUM', date); //all_company_data['kpi_value_sq_d'][current_company_code]['CKI_NUM'][date_type][date];

        // 环比=(本期-上期)/上期×100%
        var hb = Math.round((val - val_sq) / val_sq * 1000) / 10;

        data_s1.push(Math.round(val / 100) / 100);
        data_s2.push(hb);
      }

    }

    xAxisData = xAxisData.slice(-6);
    data_s1 = data_s1.slice(-6);
    data_s2 = data_s2.slice(-6);
  }



  var chart = echarts.init(document.getElementById(chart_id));

  var option = {
    tooltip: {
      show: true,
      formatter: function (params, ticket, callback) {
        if (params.seriesName == '同比') {
          return params.seriesName + '<br>' + params.name + ': ' + params.value + '%';
        } else {
          return params.seriesName + '<br>' + params.name + ': ' + params.value + '';
        }
      },
      extraCssText: "color:#021e55; padding:5px 10px 6px 10px; box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);background: rgb(178,225,254);background: -moz-linear-gradient(top,  rgba(178,225,254,1) 0%, rgba(100,192,250,1) 100%);background: -webkit-linear-gradient(top,  rgba(178,225,254,1) 0%,rgba(100,192,250,1) 100%);background: linear-gradient(to bottom,  rgba(178,225,254,1) 0%,rgba(100,192,250,1) 100%);filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#b2e1fe', endColorstr='#64c0fa',GradientType=0 );",
      backgroundColor: '',

    },
    legend: {
      show: true,
      orient: 'horizontal',
      x: 'center',
      right: 10,
      itemWidth: 10,
      itemHeight: 10,
      padding: 10,
      itemGap: 10,
      textStyle: {
        color: '#45a5f6',
        fontSize: 12 + fontSizeDiff()
      },
      data: [{
        name: '旅客量',
        icon: 'rect',
      }, {
        name: '同比',
        icon: 'circle',
      }]
    },
    grid: {
      top: 55,
      left: 40,
      right: 44,
      bottom: 35,
    },
    xAxis: [{
      type: 'category',
      data: xAxisData,
      boundaryGap: true, // 去掉x轴两边的留白, 空着不显示line难看
      nameTextStyle: {
        color: '#45a5f6'
      },
      axisLine: {
        lineStyle: {
          color: '#45a5f6' // 轴线颜色
        }
      },
      axisLabel: {
        interval: 0, // 强制显示所有标签
        rotate: 0,
        textStyle: {
          color: '#45a5f6', // 轴标签颜色大小
          fontSize: 10 + fontSizeDiff(),
        },
        formatter: function (value, index) {
          return chartDateFormatter(value, index);
        },
      },
      splitLine: {
        show: false, // 不显示刻度线
      },
      axisTick: {
        show: false, // 不显示刻度线
      }

    }],
    yAxis: [{
      type: 'value',
      name: '旅客量(万人次)', //
      nameLocation: 'end',
      //min: 0,
      //max: 100,
      //interval: 10,
      nameTextStyle: {
        color: '#45a5f6',
        fontSize: 10 + fontSizeDiff(),
      },
      axisLabel: {
        textStyle: {
          color: '#45a5f6',
          fontSize: 10 + fontSizeDiff(),
          align: 'right',
        },
        margin: 5,
        formatter: '{value}',
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(0,0,0,0)'
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: ['rgba(69,165,246,0.1)'] // 分割线颜色
        }
      }
    }, {
      type: 'value',
      name: '同比', //同比
      nameLocation: 'end',
      position: 'right',
      //min: 0,
      //max: 100,
      //interval: 10,
      nameTextStyle: {
        color: '#45a5f6',
        fontSize: 10 + fontSizeDiff(),
      },
      axisLabel: {
        textStyle: {
          color: '#45a5f6',
          fontSize: 10 + fontSizeDiff(),
          align: 'left',
        },
        margin: 5,
        formatter: '{value}%',
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(0,0,0,0)'
        }
      },
      splitLine: {
        show: false,
        lineStyle: {
          color: ['rgba(69,165,246,0.1)'] // 分割线颜色
        }
      }
    }],
    series: [{
      name: '旅客量',
      type: 'bar',
      barWidth: 12,
      yAxisIndex: 0,
      data: data_s1,
      label: {
        normal: {
          show: false,
          position: 'top'
        }
      },
      itemStyle: {
        normal: {
          color: new echarts.graphic.LinearGradient(
            0, 0, 0, 1, [{
              offset: 0,
              color: colors[0][0]
            }, {
              offset: 1,
              color: colors[0][1]
            }]),
        }
      },
    }, {
      name: '同比',
      type: 'line',
      smooth: false,
      yAxisIndex: 1,
      data: data_s2,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        normal: {
          color: colors[1][0],
        }
      },
      // 拐点
      itemStyle: {
        normal: {
          color: colors[1][0],
          opacity: 1, //折线拐点

        }
      },

    }]
  };

  chart.setOption(option);

}


// 正常率趋势
function setBlockL2T2Kpi () {


  // ---------------------------
  var chart_id = 'chart_l2t2';
  var kpi_code = '';
  var colors = [
    ['#92bde9', '#65a2de'],
    ['#00b179'],
  ];

  // ---------------------------

  var cur_date = getCurrentDate();

  var xAxisData = [];
  var data_s1 = [];
  var data_s2 = [];

  if (date_type == 'L') {
    var len = weekList.length;
    for (var i = 0; i < len; i++) {
      var date = weekList[i];
      if (Number(cur_date) >= Number(date)) {
        // Week: *********
        //var label = date.substr(0,4) + '年第' + Number(date.substr(4,3)) + '周';
        //20170419 双双要求：显示的周数+1
        var label = '第' + (Number(date.substr(4, 3))) + '周 '; //+ response[date];
        xAxisData.push(label);

        var date_normal_rate; // 正常率用的周数要+1

        date_normal_rate = date;



        // var val1 = sumKpi('kpi_value_d', 'NORMAL_NO_T', date_normal_rate);
        // var val2 = sumKpi('kpi_value_d', 'SCH_NO', date_normal_rate);
        // var val_total_sq1 = sumKpi('kpi_value_sq_d', 'NORMAL_NO_T', date_normal_rate);
        // var val_total_sq2 = sumKpi('kpi_value_sq_d', 'SCH_NO', date_normal_rate);

        // var rate1 = val2 > 0 ? Math.round((Number(val1) / Number(val2)) * 1000) / 10 : 0;
        // var rate2 = val_total_sq2 > 0 ? Math.round((Number(val_total_sq1) / Number(val_total_sq2)) * 1000) / 10 : 0;

        // 环比=(本期-上期)/上期×100%
        // var hb = rate2 > 0 ? Math.round((rate1 - rate2) / rate2 * 1000) / 10 : 0;

        var normalRateNew = all_company_data['kpi_value_d'][current_company_code]['NORMAL_RATE_ZT'][date_type][date_normal_rate];
        var normalRateNewSQ = all_company_data['kpi_value_sq_d'][current_company_code]['NORMAL_RATE_ZT'][date_type][date_normal_rate];
        var hb = Number(normalRateNew) - Number(normalRateNewSQ);
        data_s1.push((Number(normalRateNew) * 100).toFixed(1));
        data_s2.push((Number(hb) * 100).toFixed(1));
      }

    }

    xAxisData = xAxisData.slice(-6);
    data_s1 = data_s1.slice(-6);
    data_s2 = data_s2.slice(-6);

  } else {
    var len = monthList.length;
    for (var i = 0; i < len; i++) {
      var date = monthList[i];
      if (Number(cur_date) >= Number(date)) {
        // Week: *********
        var label = Number(date.substr(4, 2)) + '月 \n ' + date.substr(0, 4);
        xAxisData.push(label);

        // var val1 = sumKpi('kpi_value_d', 'NORMAL_NO_T', date);
        // var val2 = sumKpi('kpi_value_d', 'SCH_NO', date);
        // var val_total_sq1 = sumKpi('kpi_value_sq_d', 'NORMAL_NO_T', date);
        // var val_total_sq2 = sumKpi('kpi_value_sq_d', 'SCH_NO', date);

        // var rate1 = val2 > 0 ? Math.round((Number(val1) / Number(val2)) * 1000) / 10 : 0;
        // var rate2 = val_total_sq2 > 0 ? Math.round((Number(val_total_sq1) / Number(val_total_sq2)) * 1000) / 10 : 0;

        // // 环比=(本期-上期)/上期×100%
        // var hb = rate2 > 0 ? Math.round((rate1 - rate2) / rate2 * 1000) / 10 : 0;




        var normalRateNew = all_company_data['kpi_value_d'][current_company_code]['NORMAL_RATE_ZT'][date_type][date];
        var normalRateNewSQ = all_company_data['kpi_value_sq_d'][current_company_code]['NORMAL_RATE_ZT'][date_type][date];
        var hb = Number(normalRateNew) - Number(normalRateNewSQ);

        data_s1.push((Number(normalRateNew) * 100).toFixed(1));
        data_s2.push((Number(hb) * 100).toFixed(1));
      }

    }

    xAxisData = xAxisData.slice(-6);
    data_s1 = data_s1.slice(-6);
    data_s2 = data_s2.slice(-6);
  }



  var chart = echarts.init(document.getElementById(chart_id));

  var option = {
    tooltip: {
      show: true,
      formatter: function (params, ticket, callback) {
        if (params.seriesName == '环比') {
          return params.seriesName + '<br>' + params.name + ': ' + params.value + '%';
        } else {
          return params.seriesName + '<br>' + params.name + ': ' + params.value + '';
        }
      },
      extraCssText: "color:#021e55; padding:5px 10px 6px 10px; box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);background: rgb(178,225,254);background: -moz-linear-gradient(top,  rgba(178,225,254,1) 0%, rgba(100,192,250,1) 100%);background: -webkit-linear-gradient(top,  rgba(178,225,254,1) 0%,rgba(100,192,250,1) 100%);background: linear-gradient(to bottom,  rgba(178,225,254,1) 0%,rgba(100,192,250,1) 100%);filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#b2e1fe', endColorstr='#64c0fa',GradientType=0 );",
      backgroundColor: '',

    },
    legend: {
      show: true,
      orient: 'horizontal',
      x: 'center',
      right: 10,
      itemWidth: 10,
      itemHeight: 10,
      padding: 10,
      itemGap: 10,
      textStyle: {
        color: '#45a5f6',
        fontSize: 12 + fontSizeDiff()
      },
      data: [{
        name: '正常率',
        icon: 'rect',
      }, {
        name: '环比',
        icon: 'circle',
      }]
    },
    grid: {
      top: 55,
      left: 40,
      right: 44,
      bottom: 35,
    },
    xAxis: [{
      type: 'category',
      data: xAxisData,
      boundaryGap: true, // 去掉x轴两边的留白, 空着不显示line难看
      nameTextStyle: {
        color: '#45a5f6'
      },
      axisLine: {
        lineStyle: {
          color: '#45a5f6' // 轴线颜色
        }
      },
      axisLabel: {
        interval: 0, // 强制显示所有标签
        rotate: 0,
        textStyle: {
          color: '#45a5f6', // 轴标签颜色大小
          fontSize: 10 + fontSizeDiff(),
        },
        formatter: function (value, index) {
          return chartDateFormatter(value, index);
        },
      },
      splitLine: {
        show: false, // 不显示刻度线
      },
      axisTick: {
        show: false, // 不显示刻度线
      }

    }],
    yAxis: [{
      type: 'value',
      name: '正常率', //利用率
      nameLocation: 'end',
      //min: 0,
      //max: 100,
      //interval: 10,
      nameTextStyle: {
        color: '#45a5f6',
        fontSize: 10 + fontSizeDiff(),
      },
      axisLabel: {
        textStyle: {
          color: '#45a5f6',
          fontSize: 10 + fontSizeDiff(),
          align: 'right',
        },
        margin: 5,
        formatter: '{value}%',
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(0,0,0,0)'
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: ['rgba(69,165,246,0.1)'] // 分割线颜色
        }
      }
    }, {
      type: 'value',
      name: '环比',
      nameLocation: 'end',
      position: 'right',
      //min: 0,
      //max: 100,
      //interval: 10,
      nameTextStyle: {
        color: '#45a5f6',
        fontSize: 10 + fontSizeDiff(),
      },
      axisLabel: {
        textStyle: {
          color: '#45a5f6',
          fontSize: 10 + fontSizeDiff(),
          align: 'left',
        },
        margin: 5,
        formatter: '{value}%',
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(0,0,0,0)'
        }
      },
      splitLine: {
        show: false,
        lineStyle: {
          color: ['rgba(69,165,246,0.1)'] // 分割线颜色
        }
      }
    }],
    series: [{
      name: '正常率',
      type: 'bar',
      barWidth: 12,
      yAxisIndex: 0,
      data: data_s1,
      label: {
        normal: {
          show: false,
          position: 'top'
        }
      },
      itemStyle: {
        normal: {
          color: new echarts.graphic.LinearGradient(
            0, 0, 0, 1, [{
              offset: 0,
              color: colors[0][0]
            }, {
              offset: 1,
              color: colors[0][1]
            }]),
        }
      },
    }, {
      name: '环比',
      type: 'line',
      smooth: false,
      yAxisIndex: 1,
      data: data_s2,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        normal: {
          color: colors[1][0],
        }
      },
      // 拐点
      itemStyle: {
        normal: {
          color: colors[1][0],
          opacity: 1, //折线拐点

        }
      },

    }]
  };

  chart.setOption(option);
}


// 获取在册飞机架数
var companyAcNum = {};

function getAcCnt () {

  var url = `/bi/spring/aircraft/getCompAircraftNum`;

  $.ajax({
    type: 'get',
    url: url,
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    success: function (response) {
      var list = response.data;
      companyAcNum = {};
      for (var i = list.length - 1; i >= 0; i--) {
        var obj = list[i];
        // if (obj.acOwner == 'Y8' && ['B5057', 'B2826', 'B2820', 'B1339'].indexOf(obj.longReg) > -1) { //金鹏结果集暂不过滤掉货舱F0的数据，只去除特定的机号
        //     continue;
        // }
        // 在册飞机架数 总数//所有机型，包括宽体、窄体、支线、其它
        companyAcNum[obj.compCode] = obj.usedAircraftNum
        // var val = companyAcNum[obj.acOwner];
        setBlockR1Kpi();
      }
    },
    error: function () { }
  });

  // var param = {
  // 'SOLR_CODE': 'FAC_COMP_ACTYPE_KPI',
  // 'COMP_CODE': codelist.join(','),
  // 'KPI_CODE': 'AC_NUM', // 飞机架次
  // 'VALUE_TYPE': 'kpi_value_d', //本期
  //    'DATE_TYPE': 'D',
  //    'DATE_ID': date,
  //    'ACTYPE': 'ALL',
  //    'LIMIT': 1
  //    }
  //
  //    $.ajax({
  //        type: 'post',
  //        url:"/bi/query/getackpi",
  //        contentType: 'application/json',
  //        dataType: 'json',
  //        async: true,
  //        data: JSON.stringify(param),
  //        success: function(response) {
  //
  //            if(response.data != undefined){
  //                var data = response.data;
  //
  //                // 显示周的最后一天、月的28号的数据
  //                for(var compcode in data){
  //                    var kpiaclst = data[compcode]['AC_NUM']['D']['data3'];
  //                    var val1 = 0;
  //
  //                    var len = kpiaclst.length;
  //                    for(var i=0; i<len; i++){
  //                        var acdat = kpiaclst[i];
  //                        var acid = acdat.actype;
  //                        var accode = actypeId2Code[acid];
  //                        if(accode && accode != 'QITA'){
  //                            var acdd = acdat.date;
  //                            var len2 = acdd.length;
  //                            // 每种机型的架数
  //                            var acno = 0;
  //                            for(var j=0; j<len2; j++){
  //                                var dd = acdd[j];
  //                                var val = isNaN(dd.value) ? 0 : Number(dd.value);
  //                                val1 += val;
  //                                acno += val;
  //                            }
  //                        }
  //                        
  //                    }
  //                    
  //                    val1 = (isNaN(val1) || val1==0) ? 0 : Math.round(val1);
  //
  //                    // 在册飞机架数 总数//所有机型，包括宽体、窄体、支线、其它
  //                    companyAcNum[compcode] = val1;
  //                    setBlockR1Kpi();
  //                }
  //                
  //
  //            }
  //            
  //        },
  //        error:function() {
  //        }
  //    });
}


// 各航司运行情况
// @sortby 按哪个字段排序
// @order 降序/升序
function setBlockR1Kpi (sortby, order) {
  if (objectIsEmpty(companyAcNum)) {
    setTimeout(setBlockR1Kpi, 0, sortby, order);
    return;
  }


  var table_data = [];

  var date = getCurrentDate();

  var date_normal_rate; // 正常率用的周数要+1

  if (date_type == 'L') {
    // Week: *********
    date_normal_rate = date;

  } else {
    date_normal_rate = date;
  }

  var len = companylist.length;
  var sum = 0;
  for (var i = 0; i < len; i++) {

    var dat = companylist[i];
    var compcode = dat.code;
    if (compcode != parent_company) {

      var name = companyCode2Nameabbr[compcode];
      var SCH_NO = all_company_data['kpi_value_d'][compcode]['SCH_NO'][date_type][date];
      var CKI_NUM = all_company_data['kpi_value_d'][compcode]['CKI_NUM'][date_type][date];



      var normalRateNew = all_company_data['kpi_value_d'][compcode]['NORMAL_RATE_ZT'][date_type][date];
      var normalRateNewSQ = all_company_data['kpi_value_sq_d'][compcode]['NORMAL_RATE_ZT'][date_type][date];
      var hb = ((Number(normalRateNew) - Number(normalRateNewSQ)) * 100).toFixed(1);






      var rate1 = (Number(normalRateNew) * 100).toFixed(1);
      // var rate2 = val_total_tq2 > 0 ? Math.round((Number(val_total_tq1) / Number(val_total_tq2)) * 1000) / 10 : 0;

      // var hb = rate2 > 0 ? Math.round((rate1 - rate2) / rate2 * 1000) / 10 : 0;

      var acnum = companyAcNum[compcode] || "-";
      //境内航司飞机数要排除港行
      // if("HNAIN"==compcode){
      //     acnum =0;
      //     for(p in companyAcNum){
      //         if(p!='HX'){
      //             acnum += companyAcNum[p]||0;
      //         }
      //     }
      // }

      table_data.push({
        'name': name,
        'flt': Math.round(SCH_NO),
        'psr': Math.round(CKI_NUM / 1000) / 10,
        'acnum': acnum,
        'rate': rate1,
        'hb': hb
      });
    }

  }


  if (sortby) {
    table_data.sort(function (a, b) {
      if (order == 'desc') {
        return b[sortby] - a[sortby]
      } else {
        return a[sortby] - b[sortby]
      }
    });
  }

  $('.block_r1 table th span').removeClass('desc')
  $('.block_r1 table th span').removeClass('asc')
  $('.block_r1 table th').attr('data-sort', '')
  $('.block_r1 table .' + sortby + ' span').addClass(order)
  $('.block_r1 table .' + sortby).attr('data-sort', order)

  var html = '';

  var len = table_data.length;
  for (var i = 0; i < len; i++) {
    var dat = table_data[i];

    html += '<tr>';
    html += '<td class="name">' + dat.name + '</td>';
    html += '<td class="blue_ll">' + dat.flt + '</td>';
    html += '<td class="blue_ll">' + dat.psr + '万</td>';
    html += '<td class="blue_ll">' + dat.acnum + '</td>';
    var color;
    if (dat.rate >= 80 || isNaN(dat.rate)) {
      color = 'blue';
    } else {
      color = 'orange';
    }
    html += '<td class=""><span class="bar ' + color + '" style="width:' + dat.rate * 0.5 + 'px;"><span class="val">' + dat.rate + '%</span></span></td>';
    html += '<td class="blue_ll"><span class="val">' + dat.hb + '</span><span class="">%</span>';
    if (dat.hb > 0) {
      html += '<span class="green">↑</span>';
    } else if (dat.hb < 0) {
      html += '<span class="red">↓</span>';
    } else {
      html += '<span class=""></span>';
    }
    html += '</td>';
    html += '</tr>';

  }

  $('.block_r1 table tbody').html(html);

  $('.block_r1 table th span').off('click');
  $('.block_r1 table th span').on('click', function (evt) {
    var sortby = $(this).parent().attr('data-name');
    var order = $(this).parent().attr('data-sort');
    if (order == 'desc') {
      order = 'asc'
    } else {
      order = 'desc'
    }
    setBlockR1Kpi(sortby, order);
  })

}

// 获取前一个理会周
function getPrevWeek (date) {
  if (date_type == 'L') {
    // 取上一周的数据
    var week = Number(date.substr(4, 3));
    var year = Number(date.substr(0, 4));
    if (week > 1) {
      week = week - 1;
    } else {
      year = year - 1;
      week = 53;
    }
    if (week.toString().length == 1) {
      week = '00' + week;
    } else {
      week = '0' + week;
    }
    date = year + '' + week + '00';
  }
  return date;
}

// 日利用率未达标情况
/*
function setBlockR2Kpi(){
    if(actypeId2Code == undefined || all_company_data_task == undefined || all_company_data_ac == undefined || all_company_data_ac['kpi_value_d'] == undefined){
        setTimeout(setBlockR2Kpi, 10);
        return;
    }

    var table_data = [];

    var date = getCurrentDate();

    date = getPrevWeek(date);

    var len = companylist.length;
    for(var i=0; i<len; i++){
        
        var dat = companylist[i];
        var compcode = dat.code;
        if(compcode != parent_company){

            var name = companyCode2Name[compcode];

            // 预算 飞机日利用率
            var val1 = all_company_data_task[compcode]['FLY_TIME_TASK'][date_type][date];
            var val2 = all_company_data_task[compcode]['AC_NUM_TASK'][date_type][date];
            var rate2 = val2 > 0 ? (val1 / val2) : 0;

            var len2 = ac_list_except_other_type.length;
            for(var k=0; k<len2; k++){
                var actype = ac_list_except_other_type[k];
                var obj = calAcRate(compcode, actype);

                // 日利用率不达标
                if(obj.rate > 0 && rate2 > 0 && obj.rate < rate2){
                    table_data.push({'name': name, 'actype': actype, 'rate': Math.round(obj.rate*10)/10, 'diff': Math.round((obj.rate-rate2)*10)/10 });
                }
            }
        }
    }

    table_data.sort(function(a, b){
        return a['diff']-b['diff']
    });

    table_data = table_data.slice(0,5);


    var html = '';

    var len = table_data.length;
    for(var i=0; i<len; i++){
        var dat = table_data[i];

        html += '<tr>';
        html += '<td class="name">'+dat.name+'</td>';
        html += '<td class="blue_ll">'+dat.actype+'</td>';
        html += '<td class="blue_ll bold">'+dat.rate+'h</td>';
        
        html += '<td class="val red bold">'+Math.abs(dat.diff)+'h</td>';
        
        html += '</td>';
        html += '</tr>';

    }

    $('.block_r2 table tbody').html(html);

}
*/


// 计算飞机日利用率
function calAcRate (compcode, actype) {

  var date = getCurrentDate();

  date = getPrevWeek(date);

  var kpiac1 = all_company_data_ac['kpi_value_d'][compcode]['FLY_TIME'][date_type]['data3'];
  var kpiac2 = all_company_data_ac['kpi_value_d'][compcode]['AC_NUM'][date_type]['data3'];

  var val1 = 0;
  var val2 = 0;

  var len = kpiac1.length;
  for (var i = 0; i < len; i++) {
    var acdat = kpiac1[i];
    var acid = acdat.actype;
    var ac = actypeId2Code[acid];
    if (actype == ac) {
      var datelist = acdat.date;
      var len2 = datelist.length;
      for (var j = 0; j < len2; j++) {
        var obj = datelist[j];
        if (obj.date == date) {
          var vv = isNaN(obj.value) ? 0 : Number(obj.value);
          val1 += Number(vv);
          break;
        }
      }
    }
  }


  var len = kpiac1.length;
  for (var i = 0; i < len; i++) {
    var acdat = kpiac2[i];
    var acid = acdat.actype;
    var ac = actypeId2Code[acid];
    if (actype == ac) {
      var datelist = acdat.date;
      var len2 = datelist.length;
      for (var j = 0; j < len2; j++) {
        var obj = datelist[j];
        if (obj.date == date) {
          var vv = isNaN(obj.value) ? 0 : Number(obj.value);
          val2 += Number(vv);
          break;
        }
      }
    }
  }

  // 飞机日利用率
  var rate1 = val2 > 0 ? (val1 / val2) : 0;


  // 同比
  /*
  var kpiac1 = all_company_data_ac['kpi_value_tq_d'][compcode]['FLY_TIME'][date_type]['data3'];
  var kpiac2 = all_company_data_ac['kpi_value_tq_d'][compcode]['AC_NUM'][date_type]['data3'];

  var val1 = 0;
  var val2 = 0;

  var len = kpiac1.length;
  for(var i=0; i<len; i++){
      var acdat = kpiac1[i];
      var acid = acdat.actype;
      var ac = actypeId2Code[acid];
      if(actype == ac){
          var datelist = acdat.date;
          var len2 = datelist.length;
          for(var j=0; j<len2; j++){
              var obj = datelist[j];
              if(obj.date == date){
                  var vv = isNaN(obj.value) ? 0 : Number(obj.value);
                  val1 += Number(vv);
                  break;
              }
          }
      }
  }


  var len = kpiac1.length;
  for(var i=0; i<len; i++){
      var acdat = kpiac2[i];
      var acid = acdat.actype;
      var ac = actypeId2Code[acid];
      if(actype == ac){
          var datelist = acdat.date;
          var len2 = datelist.length;
          for(var j=0; j<len2; j++){
              var obj = datelist[j];
              if(obj.date == date){
                  var vv = isNaN(obj.value) ? 0 : Number(obj.value);
                  val2 += Number(vv);
                  break;
              }
          }
      }
  }

  // 飞机日利用率
  var rate2 = val2 > 0 ? (val1 / val2) : 0;


  // 同比
  var tb = rate2 > 0 ? Math.round((rate1-rate2)/rate2 * 1000)/10 : 0
  
  */

  var obj = {};
  obj.rate = rate1;
  //obj.tb = tb;
  return obj;

  return rate;
}



// 获取座公里收入
function getKiloInc (compcode, kpicode, kpicode2) {
  var date = getCurrentDate();

  // 本期--------

  // 收入
  var inc = all_company_data[date_type]['kpi_value_d'][compcode][kpicode][date_type][date];
  if (isNaN(inc)) {
    inc = 0;
  }

  // 座公里
  var kilo = all_company_data[date_type]['kpi_value_d'][compcode][kpicode2][date_type][date];
  if (isNaN(kilo)) {
    kilo = 0;
  }

  // 座公里收入
  var value = Math.round(inc / kilo * 10000 * 100) / 100; // 元
  if (isNaN(value) || kilo == 0) {
    value = 0;
  }


  // 同期--------

  // 收入
  var inc_tq = all_company_data[date_type]['kpi_value_tq_d'][compcode][kpicode][date_type][date];
  if (isNaN(inc_tq)) {
    inc_tq = 0;
  }

  // 座公里
  var kilo_tq = all_company_data[date_type]['kpi_value_tq_d'][compcode][kpicode2][date_type][date];
  if (isNaN(kilo_tq)) {
    kilo_tq = 0;
  }

  // 座公里收入
  var value_tq = Math.round(inc_tq / kilo_tq * 10000 * 100) / 100; // 元
  if (isNaN(value_tq) || kilo_tq == 0) {
    value_tq = 0;
  }

  // 同比
  // 同比=(本期-同期)÷同期×100%
  var tb = Math.round((value - value_tq) / value_tq * 1000) / 10;
  if (isNaN(tb) || value_tq == 0) {
    tb = 0;
  }

  var obj = {};
  obj.value = isNaN(value) ? 0 : value;
  obj.value_tq = isNaN(value_tq) ? 0 : value_tq;
  obj.tb = isNaN(value) ? 0 : tb;
  return obj;
}



function updateAllKpi (data, label) {
  if (!kpiDataReady) {
    return;
  }
  setMapKpi();

  setBlockL1Kpi();
  setBlockL2T1Kpi();
  setBlockL2T2Kpi()


  getAcCnt();
  setBlockR1Kpi();
  //setBlockR2Kpi();

  if (current_company_code != parent_company) {
    $('#selected_mapmarkpoint').show();
  } else {
    $('#selected_mapmarkpoint').hide();
  }


  setTitleDate();


}



// 航空投资集团 整体正常率目标
var groupNormalRateGoal;

function getGroupNormalRateGoal () {
  var param = {
    'ID': 'WEB_HNA_NORMAL_RATE',
  }

  $.ajax({
    type: 'post',
    url: "/bi/web/querydatalist",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {

      groupNormalRateGoal = response.data[0].name;

      $('#map_legend .l1').text(groupNormalRateGoal + '%以上');
      $('#map_legend .l2').text(groupNormalRateGoal + '%以下');

    },
    error: function (jqXHR, txtStatus, errorThrown) {

    }
  });
}



// -------------------------------------------------------

//                    区域弹出图层信息

// -------------------------------------------------------
var previous_mapmarkpoint;

function showCompanyPopover (obj, trans) {
  var compcode = obj.code;


  var mx = obj.mouseX - 18;
  var my = obj.mouseY - 18;

  all_company_data

  var pw = 480; //$('#popover_map').width();
  var ph = 308; //$('#popover_map').height();

  var osw = document.body.offsetWidth;
  var osh = osw / 1366 * 768;


  if (previous_mapmarkpoint) {

    var duration;
    if (trans) {
      duration = 300;
    } else {
      duration = 0;
    }

    $('#popover_map').animate({
      opacity: 0,
    }, {
      queue: false,
      duration: duration,
      step: function (now, fx) {
        if (fx.prop == 'opacity') {
          var ss = Math.min((now + 0.3), 1);
          $(this).css('transform', 'scale(' + ss + ')');
        }
      },
      complete: function () {
        moveMarkpoint();
        setData();
        resetPop();
        setTimeout(showPop, 0);
      },
    });


  } else {
    moveMarkpoint();
    setData();
    resetPop();
    setTimeout(showPop, 0);
  }


  function moveMarkpoint () {

    $('#selected_mapmarkpoint').css('left', mx + 'px');
    $('#selected_mapmarkpoint').css('top', my + 'px');

  }

  function resetPop () {

    $('#popover_map').css('transform', 'scale(0.001)');
    $('#popover_map').css('opacity', 0);
    $('#popover_map').css('left', (mx - pw / 2) + 'px');
    $('#popover_map').css('top', (my - ph / 2) + 'px');

  }

  function showPop () {

    $('#popover_map').css('pointer-events', 'auto');

    $('#popover_map').animate({
      left: (osw - pw) / 2 + 'px',
      top: (osh - ph) / 2.5 + 'px',
      opacity: 1,
    }, {
      queue: false,
      duration: 500,
      step: function (now, fx) {
        if (fx.prop == 'opacity') {
          $(this).css('transform', 'scale(' + now + ')');
        }
      },
    });
  }



  function setData () {

    var date = getCurrentDate();
    var date_normal_rate; // 正常率用的周数要+1

    if (date_type == 'L') {
      // Week: *********
      date_normal_rate = date;

    } else {
      date_normal_rate = date;
    }


    var compname = companyCode2Name[compcode];
    $('#popover_map .title').text(compname);


    // 航班总量
    var val = all_company_data['kpi_value_d'][compcode]['SCH_NO'][date_type][date];
    var val_sq = all_company_data['kpi_value_sq_d'][compcode]['SCH_NO'][date_type][date];

    // 环比=(本期-上期)/上期×100%
    var hb = Math.round((val - val_sq) / val_sq * 1000) / 10;

    var element = '#popover_map .SHIFTS ';

    $(element + '.kpi .val').text(formatCurrency(Math.round(val), 0));
    $(element + '.hb .val').text(hb);
    if (hb > 0) {
      $(element + '.hb .green').removeClass('hide');
      $(element + '.hb .red').addClass('hide');
    } else if (hb < 0) {
      $(element + '.hb .green').addClass('hide');
      $(element + '.hb .red').removeClass('hide');
    } else {
      $(element + '.hb .green').addClass('hide');
      $(element + '.hb .red').addClass('hide');
    }


    // 国内 航班总量
    var val = all_company_data['kpi_value_d'][compcode]['SCH_NO_INL'][date_type][date];
    var val_sq = all_company_data['kpi_value_sq_d'][compcode]['SCH_NO_INL'][date_type][date];

    // 环比=(本期-上期)/上期×100%
    var hb = val_sq > 0 ? Math.round((val - val_sq) / val_sq * 1000) / 10 : 0;

    var element = '#popover_map .SHIFTS_L ';

    $(element + '.kpi .val').text(formatCurrency(Math.round(val), 0));
    $(element + '.hb .val').text(hb);
    if (hb > 0) {
      $(element + '.hb .green').removeClass('hide');
      $(element + '.hb .red').addClass('hide');
      $(element + '.hb .sub').show();
    } else if (hb < 0) {
      $(element + '.hb .green').addClass('hide');
      $(element + '.hb .red').removeClass('hide');
      $(element + '.hb .sub').show();
    } else {
      $(element + '.hb .green').addClass('hide');
      $(element + '.hb .red').addClass('hide');
      $(element + '.hb .sub').hide();
    }


    // 国际 航班总量
    var val = all_company_data['kpi_value_d'][compcode]['SCH_NO_INT'][date_type][date];
    var val_sq = all_company_data['kpi_value_sq_d'][compcode]['SCH_NO_INT'][date_type][date];

    // 环比=(本期-上期)/上期×100%
    var hb = val_sq > 0 ? Math.round((val - val_sq) / val_sq * 1000) / 10 : 0;

    var element = '#popover_map .SHIFTS_I ';

    $(element + '.kpi .val').text(formatCurrency(Math.round(val), 0));
    $(element + '.hb .val').text(hb);
    if (hb > 0) {
      $(element + '.hb .green').removeClass('hide');
      $(element + '.hb .red').addClass('hide');
      $(element + '.hb .sub').show();
    } else if (hb < 0) {
      $(element + '.hb .green').addClass('hide');
      $(element + '.hb .red').removeClass('hide');
      $(element + '.hb .sub').show();
    } else {
      $(element + '.hb .green').addClass('hide');
      $(element + '.hb .red').addClass('hide');
      $(element + '.hb .sub').hide();
    }










    var normalRateNew = all_company_data['kpi_value_d'][compcode]['NORMAL_RATE_ZT'][date_type][date];
    var normalRateNewSQ = all_company_data['kpi_value_sq_d'][compcode]['NORMAL_RATE_ZT'][date_type][date];
    var hb = ((Number(normalRateNew) - Number(normalRateNewSQ)) * 100).toFixed(1);
    var rate1 = Number(normalRateNew) * 100;


    var element = '#popover_map .NORMAL_RATE ';

    $(element + '.kpi .val').text(formatCurrency(Math.round(rate1 * 10) / 10, 1));
    $(element + '.hb .val').text(hb);
    if (hb > 0) {
      $(element + '.hb .green').removeClass('hide');
      $(element + '.hb .red').addClass('hide');
      $(element + '.hb .sub').show();
    } else if (hb < 0) {
      $(element + '.hb .green').addClass('hide');
      $(element + '.hb .red').removeClass('hide');
      $(element + '.hb .sub').show();
    } else {
      $(element + '.hb .green').addClass('hide');
      $(element + '.hb .red').addClass('hide');
      $(element + '.hb .sub').hide();
    }



    // 旅客运输量
    var val = all_company_data['kpi_value_d'][compcode]['CKI_NUM'][date_type][date];
    var val_sq = all_company_data['kpi_value_sq_d'][compcode]['CKI_NUM'][date_type][date];

    // 环比=(本期-上期)/上期×100%
    var hb = val_sq > 0 ? Math.round((val - val_sq) / val_sq * 1000) / 10 : 0;

    var element = '#popover_map .TRV_NUM ';

    $(element + '.kpi .val').text(formatCurrency(Math.round(val / 1000) / 10, 1));
    $(element + '.hb .val').text(hb);
    if (hb > 0) {
      $(element + '.hb .green').removeClass('hide');
      $(element + '.hb .red').addClass('hide');
      $(element + '.hb .sub').show();
    } else if (hb < 0) {
      $(element + '.hb .green').addClass('hide');
      $(element + '.hb .red').removeClass('hide');
      $(element + '.hb .sub').show();
    } else {
      $(element + '.hb .green').addClass('hide');
      $(element + '.hb .red').addClass('hide');
      $(element + '.hb .sub').hide();
    }



  }


  previous_mapmarkpoint = {};
  previous_mapmarkpoint.x = mx;
  previous_mapmarkpoint.y = my;

}

function hideCompanyPopover () {
  $('#popover_map').css('opacity', 0);
  $('#popover_map').css('pointer-events', 'none');
  previous_mapmarkpoint = undefined;

}


$('#popover_map .close').on('click', function (evt) {
  hideCompanyPopover();
})


/*
//切换tab
$('.block_l2 .tab').on('click', function(evt){
    var id = $(this).attr('tab-id');
    currentTabIndex = id;
    $('.block_l2 .tab').removeClass('selected');
    $('.block_l2 .tab'+id).addClass('selected');
    $('.block_l2 .tabc').hide();
    $('.block_l2 .tabc'+id).show();

    if(id == 1){
        setBlockL2T1Kpi()
    }else if(id == 2){
        setBlockL2T2Kpi()
    }

    clearTimeout(itv_autoSwitchTab);
    itv_autoSwitchTab = setTimeout(autoSwitchTab, 10000);
})


var currentTabIndex = 1;
function autoSwitchTab(){
    clearTimeout(itv_autoSwitchTab);

    if(!autoSwitch){
        itv_autoSwitchTab = setTimeout(autoSwitchTab, 10);
        return;
    }

    if(currentTabIndex == 1){
        $('.block_l2 .tab2').click();
    }else{
        $('.block_l2 .tab1').click();
    }

    itv_autoSwitchTab = setTimeout(autoSwitchTab, 10000);

}

// 自动循环切换两个TAB
var itv_autoSwitchTab;
clearTimeout(itv_autoSwitchTab);
itv_autoSwitchTab = setTimeout(autoSwitchTab, 10000);
*/

// 切换回 集团公司
$('#hna_button').on('click', function (event) {
  event.preventDefault();
  switchCompany(parent_company);
});



// ---------------------- 

function onCompanyChanged (comp_code) {
  current_company_code = comp_code;
  checkCompanyReay();
}

function checkCompanyReay () {
  if (companyCode2Name[current_company_code] == undefined) {
    setTimeout(checkCompanyReay, 0);
  } else {
    updateAllKpi();

    regTooltip('#hna_button', companyCode2Name[parent_company]);
  }
}


getAllCompanyKpiData();
getGroupNormalRateGoal();



function setTitleDate () {
  var date = '';
  if (date_type == 'L') {
    date = $('#main_cb_week .combobox_label').text();
  } else if (date_type == 'M') {
    date = $('#main_cb_month .combobox_label').text();
  }
  $('.pagetitle .maintitle .date').html(date); // 页面标题-时间
}

dfd.done(function () {
  if (!hasAllCompanyPermission()) {
    $("#allCompanyBlock").remove();
    return;
  }
})