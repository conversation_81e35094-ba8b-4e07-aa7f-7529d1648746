
/**/

.hnaUnit{
	margin-left: 110px; 
}

div {
  pointer-events: auto;
}

.page-wrapper {
  background: #06112f url(../img/a3.6.bg.png) no-repeat top center;
}


.pagetitle{
  position: absolute;
  width: 1306px;
  height: 59px;
  top: 66px;
  left: 30px;
  text-align: center;
}
.maintitle{
  color: #FFF;
  width: 100%;
  font-size: 24px;
  padding: 11px 0 0 0;
  margin: 0 auto;
  font-weight: bold;
}
.submaintitle{
  color: #FFF;
  width: 100%;
  font-size: 22px;
  padding: 0 20px;
  margin: 0 auto;
}

#main_cb_week {
  position: absolute;
  top: 28px;
  left: 1118px;
  height: 34px;
  width: 85px;
  z-index: 1000;
}
#main_cb_month {
  position: absolute;
  top: 28px;
  left: 1202px;
  height: 34px;
  width: 104px;
  z-index: 1001;
}

#main_cb_week .combobox_label
{
  padding: 6px 6px;
  background-image: url(../img/combobox_arr.png);
  background-repeat: no-repeat;
  background-position: 63px center;
  border-radius: 4px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}
#main_cb_month .combobox_label
{
  padding: 6px 6px;
  background-image: url(../img/combobox_arr.png);
  background-repeat: no-repeat;
  background-position: 83px center;
  border-radius: 4px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: none;
}

.combotab_selected .combobox_label {
  background-color: #2a81d2;
}
.combotab .combobox_label {
  background-color: #0950a2;
  opacity: 0.5;
  color: #8abfff;
}

#week_date_range {
  position: absolute;
  top: 31px;
  left: 955px;
  width: 160px;
  font-size: 14px;
  text-align: center;
  border: 1px solid #1f5fa8;
  border-radius: 2px;
  padding: 3px 0 5px 0px;
  background-color: #021d39;
  box-shadow: 0 1px 8px #021121;

  display: none;
}

/* ----- */
.block_l1 {
  position: absolute;
  top: 135px;
  left: 35px;
  width: 250px;
  height: 260px;

}
.block_l1 .tt {
  position: absolute;
  top: 0px;
  left: 0px;
  color: #a1d4fc;
  font-size: 14px;
  font-weight: bold;
}
.block_l1 .val {
  position: absolute;
  top: 22px;
  left: 39px;
  /*color: #49e5fc;*/
  color: #a1d4fc;
  font-size: 33px;
  font-weight: bold;
  letter-spacing:-2px;
}
.block_l1 .charttt {
  position: absolute;
  top: 78px;
  left: 0px;
  color: #a1d4fc;
  font-size: 14px;
  font-weight: bold;
}
.block_l1 .chart {
  position: absolute;
  top: 95px;
  left: 0px;
  width: 250px;
  height: 180px;
  pointer-events: auto;

}

/* ------ */

.block_l2 {
  position: absolute;
  top: 458px;
  left: 35px;
  width: 250px;
  height: 260px;
}
.block_l2 .tt {
  position: absolute;
  top: 0px;
  left: 0px;
  color: #a1d4fc;
  font-size: 14px;
  font-weight: bold;
}
.block_l2 .val {
  position: absolute;
  top: 22px;
  left: 39px;
  /*color: #75fc5a;*/
  color: #a1d4fc;
  font-size: 33px;
  font-weight: bold;
  letter-spacing:-2px;
}
.block_l2 .charttt {
  position: absolute;
  top: 78px;
  left: 0px;
  color: #a1d4fc;
  font-size: 14px;
  font-weight: bold;
}
.block_l2 .chart {
  position: absolute;
  top: 95px;
  left: 0px;
  width: 250px;
  height: 180px;
  pointer-events: auto;
  
}

/* ------ */

.block_m {
  position: absolute;
  top: 139px;
  left: 322px;
  width: 722px;
  height: 602px;

  /*background-color: rgba(255,0,0,0.2);*/
}
.block_m .tabs {
  position: absolute;
  top: 19px;
  width: 100%;
  text-align: center;
}
.block_m .tab {
  font-size: 20px;
  font-weight: bold;
  color: #2e73b0;
  cursor: pointer;
  display: inline-block;
  padding: 0 0 5px 0;
  margin: 0 18px;
  line-height: 19px;
}
.block_m .tab.selected {
  color: #cdedff;
  border-bottom: 2px solid #cdedff;
}

.block_m .splitter {
  display: inline-block;
  height: 16px;
  width: 1px;
  border-right: 2px solid #4b719d;
}


.block_m .lg {
  position: absolute;
  top: 58px;
  left: 40px;
  width: 100%;
  font-size: 12px;
}
.block_m .lg1 {
  display: inline-block;
  margin-right: 10px;
}
.block_m .lg2 {
  display: inline-block;
  color: #21b0d9;
}
.block_m .chart {
  position: absolute;
  top: 88px;
  left: 4px;
  width: 701px;
  height: 367px;
}


.block_m .chart .lb1 {
  position: absolute;
  top: -19px;
  right: 4px;
  font-size: 12px;
  text-shadow: 0px 0px 5px #02215d, 0px 0px 5px #02215d;
}
.block_m .chart .lb2 {
  position: absolute;
  top: 368px;
  right: 4px;
  font-size: 12px;
  text-shadow: 0px 0px 5px #02215d, 0px 0px 5px #02215d;
}

.block_m .chart .markline{
  position: absolute;
  top: 183px;
  left: 0px;
  width: 100%;
  height: 0px;
  border-top: 1px solid #2eaafd;
}
#canvas_bg {
  background-color: rgba(3,69,155,0.3);

}
.block_m .chart .left_mask {
  position: absolute;
  top: 1px;
  left: 0px;
  width: 99px;
  height: 368px;
  background: url(../img/a3.6.chart_left_mask.png) no-repeat top center;
}

.block_m .top5lb {
  position: absolute;
  top: 564px;
  left: 30px;
  color: #a1d4fc;
  font-weight: bold;
}
.block_m .top5table {
  position: absolute;
  top: 496px;
  left: 176px;
  width: 524px;
  height: 105px;
  text-align: center;
}
.block_m .top5table .col1 {
  color: #90d0fd;
  font-size: 12px;
  font-weight: bold;
}
.block_m .top5table .rank {
  color: #FFF;
  font-size: 16px;
  font-weight: bold;
  display: block;
}
.block_m .top5table .name {
  color: #FFF;
  display: block;
  color: #90d0fd;
}
.block_m .top5table .row1 {
  height: 56px;
}
.block_m .top5table .row2 {
  color: #64b5f7;
}

#spaceballs {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  overflow: hidden;
  text-align: center;
}
#spaceballs .ball {
  position: absolute;
  left: -60px;
  top: calc((100% - 64px)/2);
  width: 64px;
  height: 64px;
  transform-origin: 50% 50%;
  cursor: pointer;
}
#spaceballs .ball:hover {
  transform: scale(1.05);
}
#spaceballs .ball1 {
  background: url(../img/spaceball_blue.png) no-repeat center center;
}
#spaceballs .ball2 {
  background: url(../img/spaceball_red.png) no-repeat center center;
}
#spaceballs .ball .logo {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-position: center center;
  pointer-events: none;
}
#spaceballs .ball .lb{
  position: absolute;
  width: 100%;
  font-size: 10px;
  display: none;
  text-shadow: 0px 0px 1px #000, 0px 0px 2px #001338, 0px 0px 2px #001338, 0px 0px 3px #02215d, 0px 0px 3px #02215d;
  pointer-events: none;
}
#spaceballs .ball .row1{
  top: -23px;
  color: #fff;
}
#spaceballs .ball .row2{
  top: -12px;
  color: #31caf6;
}


/* ------ */

.block_r {
  position: absolute;
  top: 139px;
  right: 24px;
  width: 266px;
  height: 602px;

}
.block_r .tt {
  position: absolute;
  top: 4px;
  left: 106px;
  color: #90d0fd;
  font-weight: bold;
}
.block_r .tabs {
  position: absolute;
  top: 35px;
  width: 100%;
  text-align: center;
}
.block_r .tab {
  font-size: 18px;
  font-weight: bold;
  color: #2e73b0;
  cursor: pointer;
  display: inline-block;
  line-height: 20px;
  padding-bottom: 5px;
}
.block_r .tab.selected {
  color: #cdedff;
  border-bottom: 2px solid #cdedff;
}

.block_r .tab1 {
  margin: 0 10px 0 0;

}
.block_r .tab2 {
  margin: 0 0 0 10px;
}
.block_r .splitter {
  display: inline-block;
  height: 14px;
  width: 1px;
  border-right: 2px solid #4b719d;
}

.block_r .lg {
  position: absolute;
  top: 67px;
  left: 0px;
  width: 100%;
  height: 24px;
  font-size: 12px;
  background: #092044;
  padding: 3px 0 0 10px;
}
.block_r .lg1 {
  display: inline-block;
  color: #75c2eb;
  margin-right: 10px;
}
.block_r .lg2 {
  display: inline-block;
  color: #008cf8;
  margin-right: 10px;
}
.block_r .lg3 {
  display: inline-block;
  color: #f23f47;
  margin-right: 10px;
}


.block_r .scrollpane {
  position: absolute;
  overflow: auto;
  top: 92px;
  left: 0px;
  width: 100%;
  height: 510px;

}

.block_r table {
  margin: 10px 0 0 0;
}
.block_r table .ac{
  width: 59px;
  background: url(../img/a3.6.ico_plane.png) no-repeat 3px center;
  color: #8fcbff;
  text-align: right;
  padding-right: 5px;
  font-size: 11px;
  font-weight: bold;
}
.block_r table .comp{
  text-align: right;
  padding-right: 5px;
  font-size: 10px;
}
.block_r table .val{
  color: #8fcbff;
  padding-left: 3px;
  font-size: 9px;
}
.block_r table .low .comp{
  color: #f23f47;
}
.block_r table .high .comp{
  color: #008cf8;
}
.block_r table td {
  line-height: 12px;
}
.block_r table .bar {
  position: relative;
  display: inline-block;
  height: 6px;
  vertical-align: middle;
}
.block_r table .avg .bar {
background: rgb(8,59,135);
background: -moz-linear-gradient(left,  rgba(8,59,135,1) 0%, rgba(127,204,244,1) 100%);
background: -webkit-linear-gradient(left,  rgba(8,59,135,1) 0%,rgba(127,204,244,1) 100%);
background: linear-gradient(to right,  rgba(8,59,135,1) 0%,rgba(127,204,244,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#083b87', endColorstr='#7fccf4',GradientType=1 );
}
.block_r table .low .bar {
background: rgb(8,59,135);
background: -moz-linear-gradient(left,  rgba(8,59,135,1) 0%, rgba(239,63,72,1) 100%);
background: -webkit-linear-gradient(left,  rgba(8,59,135,1) 0%,rgba(239,63,72,1) 100%);
background: linear-gradient(to right,  rgba(8,59,135,1) 0%,rgba(239,63,72,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#083b87', endColorstr='#ef3f48',GradientType=1 );
}
.block_r table .high .bar {
background: rgb(8,59,135);
background: -moz-linear-gradient(left,  rgba(8,59,135,1) 0%, rgba(0,140,249,1) 100%);
background: -webkit-linear-gradient(left,  rgba(8,59,135,1) 0%,rgba(0,140,249,1) 100%);
background: linear-gradient(to right,  rgba(8,59,135,1) 0%,rgba(0,140,249,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#083b87', endColorstr='#008cf9',GradientType=1 );
}


.chart_tip {
  border-radius: 4px;
  color:#021e55; 
  padding:5px 10px 10px 10px; 
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
  background: rgb(178,225,254);
  background: -moz-linear-gradient(top,  rgba(178,225,254,1) 0%, rgba(100,192,250,1) 100%);
  background: -webkit-linear-gradient(top,  rgba(178,225,254,1) 0%,rgba(100,192,250,1) 100%);
  background: linear-gradient(to bottom,  rgba(178,225,254,1) 0%,rgba(100,192,250,1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#b2e1fe', endColorstr='#64c0fa',GradientType=0 );
  font-size: 12px;
}



