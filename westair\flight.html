<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>西部运控大屏</title>
    <meta http-equiv="refresh" content="3600">
	<link href="../css/bootstrap.css" rel="stylesheet">
	<link href="../css/bootstrap-theme.css" rel="stylesheet">
	<link href="css/common.css?ver=20211218" rel="stylesheet">
	<link href="css/flight.css?ver=20211218" rel="stylesheet">
</head>
<body>
<iframe id="login" scrolling="no" width="0" height="0" style="display:none;" ></iframe>
<div id="map" class="page-wrapper"></div>

<div class="page-bgimg" style=" -moz-transform-origin:left top; pointer-events: none;">
</div>

<div class="page-wrapper" style=" -moz-transform-origin:left top;">
	<div class="col_l" style="">
		<div class="tt" style="">航班信息</div>
		<div class="fltno" style="" ></div>
		<div class="acimg" style="background-image: url(img/plane_pic.png)" ></div>
		<div class="blk1" >
			<div class="row1 con_flex_row" >
				<div class="c1 flex_1" >
					<div class="t1 city1">南京</div>
					<div class="t2 arp1" >禄口</div>
				</div>
				<div class="c2 flex_1">
					<div class="t1 citym" ></div>
					<div class="t2 arpm" ></div>
				</div>
				<div class="c3 flex_1" >
					<div class="t1 city2" >重庆</div>
					<div class="t2 arp2" >江北</div>
				</div>
			</div>


			<div class="row2 con_flex_row" >
				<div class="c1 flex_1" >
					<div class="t1 stdChn" >--</div>
					<div class="t2" >计划离港</div>
				</div>
				<div class="c2 flex_1 jingting" >
					<div class="t1 staChnm" >--</div>
					<div class="t2" >计划到港</div>
				</div>
				<div class="c3 flex_1" >
					<div class="t1 staChn" >--</div>
					<div class="t2" >计划到港</div>
				</div>
			</div>

			<div class="row2 con_flex_row" >
				<div class="c1 flex_1" >
					<div class="t1 etdChn" >--</div>
					<div class="t2" >预计起飞</div>
				</div>
				<div class="c2 flex_1 jingting" >
					<div class="t1 stdChnm" >--</div>
					<div class="t2" >计划离港</div>
				</div>
				<div class="c3 flex_1" >
					<div class="t1 etaChn staChnmm" >--</div>
					<div class="t2" >计划到港</div>
				</div>
			</div>

			<div class="row2 con_flex_row" >
				<div class="c1 flex_1" >
					<div class="t1 atdChn" >--</div>
					<div class="t2" >实际离港</div>
				</div>
				<div class="c2 flex_1 jingting" >
					<div class="t1 ataChnm" >--</div>
					<div class="t2" >实际到港</div>
				</div>
				<div class="c3 flex_1" >
					<div class="t1 ataChn" >--</div>
					<div class="t2" >实际到港</div>
				</div>
			</div>

		</div>

		<div class="blk2" style="top:93%;" >
			<div class="row1" >
				<div class="c1 flex_1" >
					<div class="t2" >前序航班</div>
					<div class="t1 preflight" >--</div>
				</div>
				<div class="c2 flex_1" >
					<div class="t2" >前序航班</div>
					<div class="t1 preflight_status" >--</div>
				</div>
			</div>
			<div class="row2" >
				<div class="c1 flex_1" >
					<div class="t2" >实际到达时间</div>
					<div class="t1 preflight_arrive_time" >--</div>
				</div>
			</div>
		</div>

	</div><!-- /.col_l -->



	<div class="col_l2" style="">
		<div class="blk1" >
			<div class="ttt" >航班日期</div>
			<div class="fs16 flt_time" >--</div>
		</div>

		<div class="blk2" >
			<div class="ttt" >航线里程</div>
			<div class="fs16" style='line-height: 18px;'>
				<span class='fs16 ariline_dis'>--</span><span class='fs12 blue'>公里</span>
				<span class='fs12 blue'>已执行</span> <span class='fs14 green ariline_dis2'>--</span> <span class='fs12 blue'>公里</span>

			</div>
		</div>

		<div class="blk3" >
			<div class="ttt" >飞行时长</div>
			<div class="fs16" style='line-height: 18px;'>
				<span class='fs16 fly_time'>--</span>
				<span class='fs12 blue'>已执行</span> <span class='fs14 green ariline_min'>--</span> <span class='fs12 blue'>分钟</span>
			</div>
		</div>

		<div class="oiltt">
			油量
		</div>

		<div class="blk4" >
			<div class="ttt" >实时油量</div>
			<div class="fs14 oil_rt" >--</div>

			<div class="ttt" >起飞油量</div>
			<div class="fs14 oil_1" >--</div>

			<div class="ttt" >最大载油量</div>
			<div class="fs14 oil_2" >--</div>

			<div class="ttt" >航线计划耗油</div>
			<div class="fs14 oil_3" >--</div>
		</div>

	</div><!-- /.col_l2 -->


	<div class='col_top con_flex_row'>

		<div class="co1 flex_none" style='width:26%' >
			<div class='fltno' style="" ></div>
		</div>

		<div class="mid flex_1 con_flex_row" style="width: 49%;">
			<div class="c1 flex_1" >
				<div class="t1 city1" ></div>
				<div class="t2" >出发机场</div>
			</div>
			<div class="c4 flex_1 leg1" >
				<div class="fs12"><span class="leg1Status"></span><br/>————></div>
			</div>
			<div class="c2 flex_1 jingting" >
				<div class="t1 citym"></div>
				<div class="blue fs12">经停机场</div>
			</div>
			<div class="c4 flex_1 leg2">
				<div class="fs12"><span class="leg2Status"></span><br/>————></div>
			</div>
			<div class="c3 flex_1" >
				<div class="t1 city2" ></div>
				<div class="t2" >目的机场</div>
			</div>
		</div>

		<div class="co3 flex_none" style='width:22%' >
			<div class='fltsts sts'></div>
		</div>

	</div><!-- /.top -->


	<div class='col_bot con_flex_row'>

		<div class="co3 flex_none" style='width:50%' >
			<div class="chart">
				<canvas id="cvs_chart2" width="116" height="116" style="background:rgba(255,0,0,0.0);"></canvas>

				<div class="fs9" style="position: absolute; width:22px; height:22px; left:19px; top:80px; opacity:0.9; ">0</div>
				<div class="fs9" style="position: absolute; width:22px; height:22px; left:10px; top:33px; opacity:0.9; ">25</div>
				<div class="fs9" style="position: absolute; width:22px; height:22px; left:53px; top:6px; opacity:0.9; ">50</div>
				<div class="fs9" style="position: absolute; width:22px; height:22px; left:95px; top:33px; opacity:0.9; ">75</div>
				<div class="fs9" style="position: absolute; width:32px; height:22px; left:87px; top:80px; opacity:0.9; ">100</div>

			</div>

			<div class="trv_rate lb_rate1 blue">客座率<br><span id="cvs_chart2_lb1" class="fs22 "></span><span class="up_arr" style='display: none;'></span><span class="down_arr" style='display: none;'></span></div>
			<div class="trv_rate_sub lb2 fs12 blue"></div>
		</div>
		<div class="mid flex_1" >
			<div class='tt'>座舱布局</div>
			<div class='cabin'>
				<div class='lb blue'></div>
			</div>
		</div>


	</div><!-- /.col_bot -->


	<div class='col_wea'>

		<div class="fs16 blue lb">天气预报</div>

		<div class="blk weather_city1" >
			<div class='tt'>
				<span class="plane">--</span>
				<span class="line"></span>
			</div>
			<div class='cont con_flex_row'>
				<div class="c1 flex_none">
				</div>
				<div class="c2 flex_1">
					<table  class="table_wea">
						<tbody>
							<tr>
								<td rowspan="2" width="31%">
									<span class="weather_ico"><span></span></span>
								</td>
								<td class="title" width="23%">能见度</td>
								<td class="title" width="23%">风速</td>
								<td class="title" width="23%">风向</td>
							</tr>
							<tr>
								<td class="visibility"></td>
								<td class="windFs"></td>
								<td class="windFx"></td>
							</tr>
							<tr style="margin-top:20px">
								<td class="condition"></td>
								<td class="title">云况</td>
								<td class="title">特殊报</td>
								<td></td>
							</tr>
							<tr>
								<td class="temperature"></td>
								<td class="cloudInfo"></td>
								<td class="tsb"></td>
								<td></td>
							</tr>

						</tbody>
					</table>
				</div>
			</div>
		</div><!-- /.blk -->
		<div class="blk weather_city2">
			<div class='tt'>
				<span class="plane">--</span>
				<span class="line"></span>
			</div>
			<div class='cont con_flex_row'>
				<div class="c1 flex_none">
				</div>
				<div class="c2 flex_1">
					<table  class="table_wea">
						<tbody>
							<tr>
								<td rowspan="2" width="31%">
									<span class="weather_ico"><span></span></span>
								</td>
								<td class="title" width="23%">能见度</td>
								<td class="title" width="23%">风速</td>
								<td class="title" width="23%">风向</td>
							</tr>
							<tr>
								<td class="visibility"></td>
								<td class="windFs"></td>
								<td class="windFx"></td>
							</tr>
							<tr style="margin-top:20px">
								<td class="condition"></td>
								<td class="title">云况</td>
								<td class="title">特殊报</td>
								<td></td>
							</tr>
							<tr>
								<td class="temperature"></td>
								<td class="cloudInfo"></td>
								<td class="tsb"></td>
								<td></td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div><!-- /.blk -->
		<div class="blk airport_run">
			<div class='tt'>
				<span class="l_line"></span>
				<span class="plane">
					<span style="position: relative;left: -7px;color: #7182d5;"><</span>着陆机场跑道情况<span style="position: relative;right: -7px;color: #7182d5;">></span>
				</span>
				<span class="r_line"></span>
			</div>
			<div class='cont con_flex_row'>
				<table  class="table_runway">
					<tbody>
						<tr>
							<th width="40%">跑道编号</th>
							<td width="30%" id="runwaynamea">--</td>
							<td width="30%" id="runwaynameb">--</td>
						</tr>
						<tr>
							<th>天气现象</th>
							<td colspan="2" id="weatherInfo">--</td>
						</tr>
						<tr>
							<th>跑道能见度</th>
							<td><span id="visibilitya">--</span>m</td>
							<td><span id="visibilityb">--</span>m</td>
						</tr>
						<tr>
							<th>跑道视程</th>
							<td colspan="2">
								<table style="width:100%;">
									<tr style="border:none">
										<td width="33.3%">
											<span style="font-size: 10px;color: #b9bfde;">TDA</span><br><span id="rvrtdz">--</span>
										</td>
										<td width="33.3%">
											<span style="font-size: 10px;color: #b9bfde;">MID</span><br><span id="rvrmid">--</span>
										</td>
										<td width="33.3%">
											<span style="font-size: 10px;color: #b9bfde;">END</span><br><span id="rvrend">--</span>
										</td>
									</tr>
								</table>
							</td>
						</tr>
						<tr>
							<th>云底高</th>
							<td><span id="cloudheighta">--</span>m</td>
							<td><span id="cloudheightb">--</span>m</td>
						</tr>
						<tr>
							<th>10分钟风向/风速</th>
							<td><span id="tenwindwaya">--</span>m/s&nbsp;&nbsp;<span id="tenwindspeeda">--</span>°</td>
							<td><span id="tenwindwayb">--</span>m/s&nbsp;&nbsp;<span id="tenwindspeedb">--</span>°</td>
						</tr>
						<tr>
							<th>温度/露点</th>
							<td colspan="2"><span id="temperature">--</span>℃/<span id="dewpoint">--</span>℃</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>


	</div><!-- /.col_wea -->



	<div class='col_r'>

		<div class="blk blk1" >
			<div class='tt'>机组信息</div>
			<table >
				<tbody>
					<tr>
						<td class="blue" style='width:75px;'>责任机长</td>
						<td class="captain" style="line-height: 16px;white-space: normal;"></td>
					</tr>
					<tr>
						<td class="blue">其他驾驶</td>
						<td class="firstVice1" ></td>
					</tr>
					<tr>
						<td class="blue">乘务员</td>
						<td class="crwStewardInf" style='line-height:16px;padding-right: 25px;'></td>
					</tr>
					<tr>
						<td class="blue">安全员</td>
						<td class="safer1" ></td>
					</tr>

				</tbody>
			</table>
		</div>

		<div class="blk blk2" >
			<div class='tt'>旅客舱单</div>
			<table >
				<tbody>
					<tr>
						<td class="blue">总数</td>
						<td class="adultNum" style='width:40px;'>--</td>

						<td class="blue">中转旅客</td>
						<td class="zz_psr" >--</td>

					</tr>
					<tr>
						<td class="blue">老人</td>
						<td class="eldNum" >--</td>

						<td class="blue">白金</td>
						<td class="ckiJpVNum" >--</td>
					</tr>
					<tr>
						<td class="blue">儿童</td>
						<td class="chdNum" >--</td>

						<td class="blue">金卡</td>
						<td class="ckiJpGoldenNum" >--</td>
					</tr>
					<tr>
						<td class="blue">VIP/CIP</td>
						<td class="vipNumCipNum" >--</td>

						<td class="blue">银卡</td>
						<td class="ckiJpSilverNum" >--</td>
					</tr>
					<tr>
						<td class="blue">头等舱旅客</td>
						<td class="cNum" >--</td>

						<td class="blue"></td>
						<td class="" ></td>
					</tr>
					<tr>
						<td class="blue">经济舱旅客</td>
						<td class="yNum" >--</td>

						<td class="blue"></td>
						<td class="" ></td>
					</tr>

				</tbody>
			</table>
		</div>


		<div class="blk blk3" >
			<div class='tt'>飞机信息</div>
			<table >
				<tbody>
					<tr>
						<td class="blue" style='width: 95px;'>机号</td>
						<td class="acno" >--</td>
					</tr>
					<tr>
						<td class="blue">机型</td>
						<td class="actype" >--</td>
					</tr>
					<tr>
						<td class="blue">飞机基重</td>
						<td class="dewKg" >--</td>
					</tr>
					<tr>
						<td class="blue">实际起飞重量</td>
						<td class="takeOffWeight" >--</td>
					</tr>
					<tr>
						<td class="blue">最大起飞重量</td>
						<td class="mtwKg" >--</td>
					</tr>

				</tbody>
			</table>

			<table style="margin-top: 19px;line-height: 23px;">
				<tbody>
					<tr>
						<td class="blue" style='width: 95px; '>维修类型</td>
						<td class="seq" >--</td>
					</tr>
					<tr>
						<td class="blue">维修机场</td>
						<td class="mntstn" >--</td>
					</tr>
					<tr>
						<td class="blue">维修结束时间</td>
						<td class="mnttEnd" >--</td>
					</tr>
					<tr>
						<td class="blue">维修任务描述</td>
						<td >
							<div class="mntComment" style='white-space: normal;word-break:break-all;word-wrap:break-word;width:170px;line-height: 17px;text-overflow:ellipsis;'>--</div>
						</td>
					</tr>
				</tbody>
			</table>

			<span class='btn btn_prev disabled'></span>
			<span class='btn btn_next disabled'></span>

		</div>

	</div><!-- /.col_r -->



</div><!-- /.page-wrapper -->
<script src="../js/jquery-1.11.1.js"></script>
<script src="../js/tingyun.js?ver=20211218"></script>
<script src="../js/bootstrap.min.js"></script>
<script src="../js/echarts.min.js?ver=20211218"></script>
<script src="../js/echarts-gl.min.js?ver=20211218"></script>
<script src="../js/map/world.js"></script>
<script src="../js/json.js"></script>
<script src="../js/util.js"></script>
<script src="common.js?ver=1"></script>
<script src="flight.js?ver=1.0.0"></script>


</body>
</html>