<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    
    <title>HNA数字航空</title>

    <meta http-equiv="refresh" content="86400">
    <script>
        var script = "<script src='js/app.js?ts="+ new Date().getTime() +"' ><\/script>";
        document.write(script);
    </script>
    <link href="css/nprogress.css" rel="stylesheet">
    <script src="js/nprogress.js"></script>

	<script src="js/babel.min.js"></script>
	<script src="js/polyfill.min.js"></script>

    <script src="js/jquery-1.11.1.js"></script>

    <script src="js/bootstrap.min.js"></script>
    <script src="js/echarts.min.js"></script>
    <script src="js/jquery.powertip.min.js"></script>
    <script src="js/moment.min.js"></script>

    <script>
        loadjs('js/tingyun.js')
        loadCss('css/bootstrap.css')
        loadCss('css/bootstrap-theme.css')
        loadCss('css/main.css')
        loadCss('css/a2-5.css')
        loadCss('css/bootstrap-datetimepicker.min.css')
        loadjs('js/json.js')
    
        loadjs('js/ui.js')
        loadjs('js/util.js')
        loadjs('js/lib/eking.js')
        loadjs('js/slider.js')
        loadjs('js/common.js')
     </script>
</head>
<body style="opacity:0;" >

<iframe id="login" scrolling="no" width="0" height="0" style="display:none;" ></iframe>
<script src="js/config.js?ver=20211218"></script>

<audio id="text2audio"></audio>

<div id="header">

    <div id="logo">
        <div id="main_page_title"></div>
        <div id="main_page_subtitle"></div>
    </div><!-- /#logo -->
   

    <div id="nav">
    </div><!-- /#nav -->

    <div id="topright">
        
    </div><!-- /#topright -->

</div><!-- /#header -->


<div class="page-wrapper" id="page-parent-comp">


    <div class="page" style="pointer-events: none;">

        <div class="pagetitle">
            <div class="maintitle">
                <span class="comp"></span><span class="tit"></span>
            </div>
            <div id="main_cb_week" class="combotab limcomb"></div>
            <div id="main_cb_month" class="combotab limcomb hidden"></div>
            <div id="main_cb_day" class="combotab limcomb hidden"></div>
            <div id="week_date_range"></div>
            <div class="date_type_select">
                <div class="tab" data-type="D">日</div>
                <div class="tab hover" data-type="L">周</div>
                <div class="tab" data-type="M">月</div>
            </div>
            <div class="data_refresh" id="btnrefresh" style="display:none"></div>
        </div><!-- /pagetitle -->


        <div id="ac_kpi_cards" class="block_b">
            <div class="tit">机型基础信息分析 <a class="ext_link" id="ext_link2" target="_blank" href="#"></a></div>
            <div class="scrollpane">
                <div class="content">
                    <div class="lst list0"></div>
                    <div class="lst list1"></div>
                    <div class="lst list2"></div>
                    <div class="lst list3"></div>
                </div>
            </div>
            <div class="btn_prev"></div>
            <div class="btn_next"></div>

            <div id="item4clone" class="itm" style="display: none;">
                <div class="border"></div>
                <div class="ico"></div>
                <div class="ac"></div>
                <div class="num canhide"><span class="val"></span><span class="sub">架</span></div>
                <div class="b1">
                    <span class="t">日利用率</span>
                    <span class="b canhide"><span class="v"></span></span>
                </div>
                <div class="b2">
                    <span class="t">同比</span>
                    <span class="b canhide"><span class="v"></span><span class="green hide">↑</span><span class="red hide">↓</span></span>
                </div>
                <div class="b3">
                    <span class="t">客座率</span>
                    <span class="b canhide"><span class="v"></span></span>
                </div>
                <div class="b4">
                    <span class="t">同比</span>
                    <span class="b canhide"><span class="v"></span><span class="green hide">↑</span><span class="red hide">↓</span></span>
                </div>
                
            </div>

            

        </div><!-- /#block_b -->


        <div class="block_t_tt"></div>


        <div class="block_t">

            <div class="itm itm0">
                <div class="border"></div>
                <div class="ico"></div>
                <div class="lb">机型收入(万元)</div>
                <div class="l2">
                    <span id="val_EST_INC_FUEL" class="val fs24"></span>
                    <span id="tb_EST_INC_FUEL" class="tb">同比<br><span class="val"></span></span>
                </div>
            </div>

            <!-- <div class="itm itm1">
                <div class="border"></div>
                <div class="ico"></div>
                <div class="lb">收入任务(万元)</div>
                <div class="l2">
                    <span id="val_INC_TASK_OIL" class="val fs24"></span>
                    <span id="tb_INC_TASK_OIL" class="tb">同比<br><span class="val"></span></span>
                </div>
            </div> -->

            <!-- <div class="itm itm2">
                <div class="border"></div>
                <div class="ico"></div>
                <div class="lb">任务完成率</div>
                <div class="l2">
                    <span id="val_TASK_RATE" class="val fs24"></span>
                    <span id="tb_TASK_RATE" class="tb">同比<br><span class="val"></span></span>
                </div>
            </div> -->

            <div class="itm itm1" style="left: 260.5px;">
                <div class="border"></div>
                <div class="ico"></div>
                <div class="lb">飞行班次</div>
                <div class="l2">
                    <span id="val_SHIFTS" class="val fs24"></span>
                    <span id="tb_SHIFTS" class="tb">同比<br><span class="val"></span></span>
                </div>
            </div>

            <div class="itm itm2" style="left: 521px;">
                <div class="border"></div>
                <div class="ico"></div>
                <div class="lb">小时收入(万元)</div>
                <div class="l2">
                    <span id="val_HOUR_INC_FUEL" class="val fs24"></span>
                    <span id="tb_HOUR_INC_FUEL" class="tb">同比<br><span class="val"></span></span>
                </div>
            </div>

            <div class="itm itm3" style="left: 781.5px;">
                <div class="border"></div>
                <div class="ico"></div>
                <div class="lb">座公里收入(元)</div>
                <div class="l2">
                    <span id="val_CAP_KILO_INC_FUEL" class="val fs24"></span>
                    <span id="tb_CAP_KILO_INC_FUEL" class="tb">同比<br><span class="val"></span></span>
                </div>
            </div>

            <div class="itm itm4" style="left: 1042px;">
                <div class="border"></div>
                <div class="ico"></div>
                <div class="lb">旅客量(人)</div>
                <div class="l2">
                    <span id="val_TRV_NUM" class="val fs24"></span>
                    <span id="tb_TRV_NUM" class="tb">同比<br><span class="val"></span></span>
                </div>
            </div>

        </div><!-- /#block_t -->


        <div class="block_m allCompanyData hide">

            <div class="block_ml">
                <table>
                    <tr>
                        <td rowspan="2" class="fs20 center" style="width:110px; background:#0960a6;">日利用率<br>运营情况</td>
                        <td class="fs16 center " style="background:#6f6a51;">宽体机</td>
                        <td class="fs16 center " style="background:#2e7eae;">窄体机</td>
                        <td class="fs16 center " style="background:#1c7664;">支线机</td>
                    </tr>

                    <tr>
                        <td class="fs16 col2"><img src="img/a2.5.actype1.png"></td>
                        <td class="fs16 col1"><img src="img/a2.5.actype2.png"></td>
                        <td class="fs16 col2"><img src="img/a2.5.actype3.png"></td>
                    </tr>

                    <tr class="tline">
                        <td class="fs14 blue_l tt col1">日利用率最高</td>
                        <td id="ac_rate_1_0" class="fs14 center col2"></td>
                        <td id="ac_rate_1_1" class="fs14 center col1"></td>
                        <td id="ac_rate_1_2" class="fs14 center col2"></td>
                    </tr>

                    <tr class="tline">
                        <td class="fs14 blue_l tt col1">日利用率最低</td>
                        <td id="ac_rate_2_0" class="fs14 center col2"></td>
                        <td id="ac_rate_2_1" class="fs14 center col1"></td>
                        <td id="ac_rate_2_2" class="fs14 center col2"></td>
                    </tr>

                    <tr class="tline">
                        <td class="fs14 blue_l tt col1">同比增长最快</td>
                        <td id="ac_rate_0_0" class="fs14 center col2"></td>
                        <td id="ac_rate_0_1" class="fs14 center col1"></td>
                        <td id="ac_rate_0_2" class="fs14 center col2"></td>
                    </tr>

                </table>
            </div><!-- /#block_ml -->


            <div class="block_mr">

                <div id="chart_bg" class="chartblock" style="width:880px; height:212px;" prop-width="880" prop-height="212">
                    <canvas id="chart_cvs"></canvas>
                </div>
                <div class="remark" style="width:880px; height:30px;" prop-width="880" prop-height="30">备注：数据来源为HBI系统；收入类型为增值税含燃油</div>
                <div id="chart_mr1" class="chartblock" style="width:880px; height:212px;" prop-width="880" prop-height="212"></div>

                <div id="chart_mr2" class="chartblock hide" style="width:880px; height:212px;" prop-width="880" prop-height="212"></div>

                <div class="tab tab1 selected">
                    利用率同比情况
                </div>
                <div class="tab tab2">
                    座公里收入
                </div>

                <a class="ext_link" id="ext_link1" target="_blank" href="#"></a>

            </div><!-- /#block_mr -->



        </div><!-- /#block_m -->
        

        


    </div><!-- /#page -->


</div><!-- /#wrapper -->






</body>
</html>



<script>
   
    loadjs4BabelDefer('js/a2-5.js')
   
</script>
