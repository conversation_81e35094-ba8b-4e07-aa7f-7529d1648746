.page-wrapper {
  position: absolute;
  top: 0px;
  width: 1872px;
  height: 790px;
  overflow-x: hidden;
  overflow-y: hidden;
  pointer-events: none;
}


#page-bgimg {
  position: absolute;
  top: 0px;
  width: 100%;
  height: 100%;
  background: url(../img/indexbg.png?0) no-repeat top center;
  overflow-x: hidden;
  overflow-y: hidden;
}

#top_l {
  position: absolute;
  top: 10px;
  left: 45px;
  width: 210px;
  height: 70px;
  background: url(../img/logo.png?0) no-repeat top center;
}

#welcome_msg {
  position: absolute;
  width:900px; 
  height:39px; 
  left: calc((100% - 900px)/2); 
  top:7px; 
  overflow:hidden;
}

#top_r {
  position: absolute;
  top: 25px;
  right: 45px;
  width: 380px;
  height: 70px;
}
#top_r .timeblk {
  position: relative;
  display: inline-block;
  padding-top: 5px;
  width: 116px;
  height: 100%;
  line-height: 16px;
}
#top_r .duty {
  position: absolute;
  text-align: center;
  padding-top: 3px;
  top: 8px;
  right: 10px;
  width: 116px;
  height: 28px;
  border: 1px solid #0078D1;
  border-radius: 4px;
  background-color: #003873;
}



#col_l {
  position: absolute;
  width: 563px;
  top: 75px;
  left: 46px;
}
#col_l .titlebar {
  text-align: center;
  padding-top: 2px;
  width: 100%;
  height: 28px;
  color: #003873;
  font-size: 18px;
  font-weight: bold;
  background: url(../img/titlebar.png?0) no-repeat top center;
  margin-bottom: 10px;
}

#col_l .blkbar {
  text-align: center;
  padding-top: 3px;
  width: 100%;
  height: 32px;
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  background: url(../img/c0.png?0) no-repeat top left, url(../img/c1.png?0) no-repeat top right;
  background-color: #08387A;
  margin-bottom: 10px;
  border-top: 1px solid #0078D1;
}



#col_l .row1 {
  position: relative;
  width: 100%;
  height: 195px;
  border-bottom: none;
  
}

/* -- */
#col_l .row1 .kpi1 {
  position: absolute;
  top: 15px;
  left: 26px;
}
#col_l .row1 .kpi1 .bar {
  position: relative;
  background-color: #0d357e;
  border-radius: 6px;
  height: 12px;
  width: 252px;
  margin-bottom: 5px;
}
#col_l .row1 .kpi1 .bar .barin {
  transition: all 0.5s ease-out;
  position: absolute;
  height: 12px;
  border-radius: 6px;
  background: -moz-linear-gradient(left,  rgba(6,38,97,1) 0%, rgba(2,131,205,1) 52%, rgba(138,233,247,1) 100%); /* FF3.6-15 */
  background: -webkit-linear-gradient(left,  rgba(6,38,97,1) 0%,rgba(2,131,205,1) 52%,rgba(138,233,247,1) 100%); /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to right,  rgba(6,38,97,1) 0%,rgba(2,131,205,1) 52%,rgba(138,233,247,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#062661', endColorstr='#8ae9f7',GradientType=1 ); /* IE6-9 */
}
#col_l .row1 .kpi1 .bar .plane {
  transition: all 0.5s ease-out;
  position: absolute;
  height: 18px;
  width: 20px;
  top: -3px;
  background: url(../img/plane_white.png?0) no-repeat top left;
}

/* -- */
#col_l .row1 .kpi2 {
  position: absolute;
  top: 115px;
  left: 26px;
}
#col_l .row1 .kpi2 .bar {
  position: relative;
  background-color: #0d357e;
  border-radius: 6px;
  height: 10px;
  width: 136px;
  margin-bottom: 5px;
}
#col_l .row1 .kpi2 .bar .barin {
  transition: all 0.5s ease-out;
  position: absolute;
  height: 10px;
  border-radius: 6px;
  background: -moz-linear-gradient(left,  rgba(6,38,97,1) 0%, rgba(2,131,205,1) 52%, rgba(138,233,247,1) 100%); /* FF3.6-15 */
  background: -webkit-linear-gradient(left,  rgba(6,38,97,1) 0%,rgba(2,131,205,1) 52%,rgba(138,233,247,1) 100%); /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to right,  rgba(6,38,97,1) 0%,rgba(2,131,205,1) 52%,rgba(138,233,247,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#062661', endColorstr='#8ae9f7',GradientType=1 ); /* IE6-9 */
}
#col_l .row1 .kpi2 .bar .plane {
  transition: all 0.5s ease-out;
  position: absolute;
  height: 14px;
  width: 15px;
  top: -2px;
  background: url(../img/plane_white.png?0) no-repeat top left;
  background-size: 15px 14px;
}

/* -- */
#col_l .row1 .kpi3 {
  position: absolute;
  top: 115px;
  left: 180px;
}
#col_l .row1 .kpi3 .bar {
  position: relative;
  background-color: #0d357e;
  border-radius: 6px;
  height: 10px;
  width: 100px;
  margin-bottom: 5px;
}
#col_l .row1 .kpi3 .bar .barin {
  transition: all 0.5s ease-out;
  position: absolute;
  height: 10px;
  border-radius: 6px;
  background: -moz-linear-gradient(left,  rgba(6,38,97,1) 0%, rgba(2,131,205,1) 52%, rgba(138,233,247,1) 100%); /* FF3.6-15 */
  background: -webkit-linear-gradient(left,  rgba(6,38,97,1) 0%,rgba(2,131,205,1) 52%,rgba(138,233,247,1) 100%); /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to right,  rgba(6,38,97,1) 0%,rgba(2,131,205,1) 52%,rgba(138,233,247,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#062661', endColorstr='#8ae9f7',GradientType=1 ); /* IE6-9 */
}
#col_l .row1 .kpi3 .bar .plane {
  transition: all 0.5s ease-out;
  position: absolute;
  height: 14px;
  width: 15px;
  top: -2px;
  background: url(../img/plane_white.png?0) no-repeat top left;
  background-size: 15px 14px;
}

#col_l .row1 .chart {
  position: absolute;
  top: 12px;
  right: 60px;
  width: 177px;
  height: 177px;
  background: url(../img/chartbg.png?0) no-repeat top left;
}




#col_l .row2 {
  position: relative;
  width: 100%;
  height: 235px;
  border-top: none;
  border-bottom: none;
}
#col_l .row2 .left {
  position: absolute;
  top: 2px;
  left: 20px;
  text-align: left;
}
#col_l .row2 .right {
  position: absolute;
  top: 2px;
  right: 20px;
  text-align: right;
}

#col_l .row2 .chart {
  position: absolute;
  top: 57px;
  left: 183px;
  width: 200px;
  height: 152px;
  background: url(../img/delay_chart_bg.png?0) no-repeat top left;
}
#col_l .row2 .chart canvas {
  position: absolute;
  top: 10px;
  left: 35px;
  width: 130px;
  height: 130px;
}
#col_l .row2 .chart .txt {
  position: absolute;
  text-align: center;
  top: 40px;
  width: 100%;
}

#holder_delay_cause_comp {
  position: absolute;
  top: 40px;
  left: 20px;
  width: 180px;
}
#holder_delay_cause_none {
  position: absolute;
  top: 40px;
  right: 0px;
  width: 150px;
}

#col_l .row3 {
  position: relative;
  width: 100%;
  height: 210px;
  border-top: none;
  
}
#col_l .row3 .mid {
  position: absolute;
  top: 57px;
  left: 185px;
  width: 145px;
  height: 132px;
  background: url(../img/ylfb_bg.png?0) no-repeat top center;
  background-size: 100%;
  padding-top: 20px;
}
#col_l .row3 .ll {
  position: absolute;
  top: 47px;
  left: 8px;
  width: 180px;
}
#col_l .row3 .rr {
  position: absolute;
  top: 47px;
  right: 8px;
  width: 240px;
}
#col_l .row3 .ll .b1 {
  position: absolute;
  top: 30px;
  left: 10px;
  width: 170px;
  padding-left: 22px;
  background: url(../img/ico_1.png?0) no-repeat center left;
}
#col_l .row3 .ll .b2 {
  position: absolute;
  top: 30px;
  left: 105px;
  width: 85px;
  padding-left: 22px;
  background: url(../img/ico_2.png?0) no-repeat center left;
}

#col_l .row3 .ll .barlist {
  position: absolute;
  top: 75px;
  left: 12px;
  width: 160px;
  height: 78px;
  font-size: 12px;
}
#col_l .row3 .rr .b1 {
  position: absolute;
  top: 30px;
  left: 25px;
  width: 200px;
  padding-left: 22px;
  background: url(../img/ico_3.png?0) no-repeat center left;
}
#col_l .row3 .rr .b2 {
  position: absolute;
  top: 30px;
  left: 105px;
  width: 200px;
  padding-left: 22px;
  background: url(../img/ico_4.png?0) no-repeat center left;
}
#col_l .row3 .rr .b3{
  position: absolute;
  top: 30px;
  left: 180px;
  width: 85px;
  padding-left: 22px;
  background: url(../img/stopplane.png?0) no-repeat center left;
}
#col_l .row3 .rr .barlist {
  position: absolute;
  top: 75px;
  left: 12px;
  width: 210px;
  height: 78px;
  font-size: 12px;
}
#col_l .row3 .barlist table{
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}
#col_l .row3 .bar{
  height: 8px;
}
#col_l .row3 .barlist table .r{
  text-align: right;
  padding-right: 3px;
}


/* ---------------------------------------- */
#col_m {
  position: absolute;
  width: 650px;
  height: 687px;
  top: 77px;
  left: 608px;
}

#col_m .title_earth{
  position: absolute;
  width: 100%;
  top: 5px;
  text-align: center;
}
#col_m .normal_rate_l{
  position: absolute;
  width: 120px;
  top: 10px;
  left: 12px;
  text-align: left;
}
#col_m .normal_rate_r{
  position: absolute;
  width: 120px;
  top: 10px;
  right: 12px;
  text-align: right;
}

#map_switch_lb {
  position: absolute;
  width: 120px;
  top: 168px;
  right: -16px;
  text-align: center;
}
#map_switch_btn {
  position: absolute;
  width: 80px;
  height: 80px;
  right: 6px;
  top: 83px;
  cursor:pointer; 
  pointer-events: auto;
}

#china_map {
  position: absolute;
}
#china_map div{
  position: absolute;
}
.earthbg {
  position: absolute;
  top: 28px;
  left:28px; 
  width: 602px;
  height: 602px;
  background: url(../img/earth_bg.png?1) no-repeat center center;
}
.earthlight {
  position: absolute;
  left:62px; 
  top:62px; 
  width:534px; 
  height:534px; 
  background: url(../img/earth_light.png) no-repeat center center; 
  background-size: 534px 534px;
}

#comp_rank_list_month {
  position: absolute;
  left:15px; 
  bottom:136px; 
  width:60px; 
}
.comp_rank_month {
  position: absolute;
  left:15px; 
  bottom:110px; 
  width:160px; 
}
#comp_rank_list_year {
  position: absolute;
  right:0px; 
  bottom:136px; 
  width:60px; 

}
.comp_rank_year {
  text-align: right;
  position: absolute;
  right:15px; 
  bottom:110px; 
  width:160px; 
}

#base_cards {
  position: absolute;
  bottom:0px; 
  left: 2px;
  width:100%; 
  height: 100px;
  background: rgba(25,48,101,0.8) url(../img/bottomcardsbg.png) no-repeat top center; 
  overflow-x: hidden;
  overflow-y: hidden;
}

#base_cards .reel {
  position: absolute;
  top: 12px;
  left: 13px;
  width: 100%;
  height: 500px;
}


.card {
  border: 1px solid;
  border-radius: 8px;
  width: 152px;
  height: 80px;
  overflow: hidden;

  -moz-box-shadow:0px 2px 8px #122348; 
  -webkit-box-shadow:0px 2px 8px #122348; 
  box-shadow:0px 2px 8px #122348;

  transform: rotate(0deg);

  display: inline-block;
  margin-right: 3px;
  margin-bottom: 20px;
}

.card div{
  position: relative;
  transform: rotate(0deg);
}
.card .head{
  
  width: 100%;
  text-align: center;
  border-bottom: 1px solid;
  padding: 2px;
  font-weight: bold;
}
.card .head .weather{
  position: absolute;
  top: 3px;
  right: 8px;
  width: 30px;
  height: 20px;
  text-align: right;
}
.card .cont{
  width: 120%;
  margin-left: -10%;
  text-align: center;
}
.card .cont .title{
  text-align: left;
  padding: 1px 0 0 12%;

}
.card .cont .itm{
  position: absolute;
  display: block;
  padding: 5px 2px 0 2px;
}
.card .cont .itm .sb{
  font-size: 9px;
}
.card .cont .itm .num{
  line-height: 20px;
  font-size: 14px;
}
.card .cont .itm1{
  left: 23px;
}
.card .cont .itm2{
  left: 59px;
}
.card .cont .itm3{
  left: 95px;
}
.card .cont .itm4{
  left: 130px;
}


.bluecard {
  border-color: #238DE6;
  background: rgba(25, 96, 185, 0.8);
}
.bluecard .head {
  border-color: #238DE6;
  background: -moz-linear-gradient(top,  rgba(33,103,190,1) 0%, rgba(22,89,178,1) 100%);
  background: -webkit-linear-gradient(top,  rgba(33,103,190,1) 0%,rgba(22,89,178,1) 100%);
  background: linear-gradient(to bottom,  rgba(33,103,190,1) 0%,rgba(22,89,178,1) 100%);
}

.yellowcard {
  border-color: #238DE6;
  background: rgba(25, 96, 185, 0.8);
}
.yellowcard .head {
  border-color: #ecc95a;
  background: -moz-linear-gradient(top,  #c6a51c 0%, #b78c31 100%);
  background: -webkit-linear-gradient(top,  #c6a51c 0%,#b78c31 100%);
  background: linear-gradient(to bottom,  #c6a51c 0%,#b78c31 100%);
}

.redcard {
  border-color: #238DE6;
  background: rgba(25, 96, 185, 0.8);
}
.redcard .head {
  border-color: #b24359;
  background: -moz-linear-gradient(top,  #850f25 0%, #760a19 100%);
  background: -webkit-linear-gradient(top,  #850f25 0%, #760a19 100%);
  background: linear-gradient(to bottom,  #850f25 0%, #760a19 100%);
}



/* ---------------------------------------- */

#col_r {
  position: absolute;
  width: 563px;
  top: 75px;
  right: 46px;
}
#col_r .titlebar {
  text-align: center;
  padding-top: 2px;
  width: 100%;
  height: 28px;
  color: #003873;
  font-size: 18px;
  font-weight: bold;
  background: url(../img/titlebar.png?0) no-repeat top center;
  margin-bottom: 10px;
}

#col_r .blkbar {
  text-align: center;
  padding-top: 3px;
  width: 100%;
  height: 32px;
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  background: url(../img/c0.png?0) no-repeat top left, url(../img/c1.png?0) no-repeat top right;
  background-color: #08387A;
  margin-bottom: 10px;
  border-top: 1px solid #0078D1;
}



#col_r .row1 {
  position: relative;
  width: 100%;
  height: 195px;
  border-bottom: none;
}

#col_r .row1 .tab {
  height: 39px;
  display: inline-block;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  padding-top: 8px;
  color: #599AD2;
  cursor: pointer;
  pointer-events: auto;
}
#col_r .row1 .tab1 {
  width: 186px;
  margin-right: 1px;
  padding-left: 30px;
  background: rgba(29,94,182, 0.3) url(../img/tab_ico1.png?0) no-repeat 60px center;
}
#col_r .row1 .tab1.selected {
  color: #fff;
  background: url(../img/tab_ico1on.png?0) no-repeat 60px center;
}

#col_r .row1 .tab2 {
  width: 186px;
  margin-right: 1px;
  padding-left: 30px;
  background: rgba(29,94,182, 0.3) url(../img/tab_ico2.png?0) no-repeat 60px center;
}
#col_r .row1 .tab2.selected {
  color: #fff;
  background: url(../img/tab_ico2on.png?0) no-repeat 60px center;
}

#col_r .row1 .tab3 {
  position: absolute;
  right: 0px;
  width: 187px;
  padding-left: 30px;
  background: rgba(29,94,182, 0.3) url(../img/tab_ico3.png?0) no-repeat 38px center;
}
#col_r .row1 .tab3.selected {
  color: #fff;
  background: url(../img/tab_ico3on.png?0) no-repeat 38px center;
}
#col_r .row1 .legend {
  position: absolute;
  top: 45px;
  left: 10px;
}
#col_r .row1 .legend .itm1 {
  background: url(../img/flag_yellow.png?0) no-repeat left center;
  padding-left: 12px;
  margin-left: 10px;
}
#col_r .row1 .legend .itm2 {
  background: url(../img/flag_red.png?0) no-repeat left center;
  padding-left: 12px;
  margin-left: 18px;
}

.planeLocationLegend {
  position: absolute;
  bottom: 20px;
  width: 100%;
  text-align: center;
  display: none;
}
.planeLocationLegend .itm1{
  display: inline-block;
  /*background: url(../img/pin_plane1.png?0) no-repeat left center;*/
  /*padding-left: 22px;*/
  margin-right: 10px;
}
.planeLocationLegend .itm2{
  display: inline-block;
  /*background: url(../img/pin_plane2.png?0) no-repeat left center;*/
  /*padding-left: 22px;*/
}

#col_r .row1 .flt_tabs {
  position: absolute;
  top: 68px;
  left: 10px;
  width: 540px;
  height: 26px;
  overflow-x: hidden; 
  pointer-events: auto;
}
#col_r .row1 .flt_tabs .wrap{
  position: absolute;
}
#col_r .row1 .flt_tabs .itm{
  position: absolute;
  width: 86px;
  height: 26px;
  padding-top: 3px;
  text-align: center;
  color: #52B8FF;
  background: url(../img/flt_tab.png?0) no-repeat left top;
  cursor: pointer;
  pointer-events: auto;
}
#col_r .row1 .flt_tabs .itm.selected{
  color: #fff;
  background: url(../img/flt_tab_on.png?0) no-repeat left top;
}
#col_r .row1 .flt_tabs span{
  font-weight: bold;
}
.flag_red{
  padding-right: 13px;
  background: url(../img/flag_red.png?0) no-repeat right center;
}
.flag_yellow{
  padding-right: 13px;
  background: url(../img/flag_yellow.png?0) no-repeat right center;
}
#col_r .row1 .flt_info {
  position: absolute;
  top: 93px;
  left: 10px;
  width: 540px;
  height: 94px;
  border: 1px solid #1C83D8;
}
#col_r .row1 .flt_info .flt{
  position: absolute;
  top: 6px;
  left: 10px;
  padding-left: 35px;
  font-weight: bold;
  font-size: 14px;
  background: url(../img/ico_5.png?0) no-repeat left center;
}
#col_r .row1 .flt_info table{
  position: absolute;
  top: 32px;
  width: 100%;
  height: 60px;
}
#col_r .row1 .flt_info table th{
  font-size: 12px;
  font-weight: normal;
  color: #52B8FF;
  background-color: #063779;
  padding: 2px;
}
#col_r .row1 .flt_info table td{
  font-size: 14px;
  color: #A1DFFF;
  font-weight: bold;
}
#col_r .row1 .flt_info table .arr{
  width: 90px;
  height: 100%;
  background: url(../img/flt_arr.png?0) no-repeat center center;
}

#col_r .row2 {
  position: relative;
  width: 100%;
  height: 235px;
  border-top: none;
  border-bottom: none;
}
#col_r .row2 .box {
  position: absolute;
  width: 526px;
  height: 166px;
  top: 51px;
  left: 16px;
  border: 1px solid rgba(14,128,218,0.6);
  background: url(../img/blkbg.png?0) no-repeat center center;
}
#col_r .row2 .box .ll{
  position: absolute;
  width: 50%;
  height: 100%;
  border-right: 1px solid rgba(14,128,218,0.6);
}
#col_r .row2 .box .ll .l{
  position: absolute;
  width: 60%;
  height: 80%;
  top: 20%;
  left: 0px;
  text-align: center;
  padding-top: 37px;
  background: url(../img/ico_6.png?0) no-repeat center top;
}
#col_r .row2 .box .ll .r{
  position: absolute;
  width: 50%;
  height: 50%;
  top: 60px;
  left: 155px;
  font-size: 12px;
}
#col_r .row2 .box .ll .r .ib1{
  display: inline-block;
  text-align: right;
  line-height: 20px;
}
#col_r .row2 .box .ll .r .ib2{
  display: inline-block;
  line-height: 20px;
}

#col_r .row2 .box .rr{
  position: absolute;
  width: 50%;
  height: 100%;
  left: 50%;
}
#col_r .row2 .box .rr .l{
  position: absolute;
  width: 60%;
  height: 80%;
  top: 20%;
  left: 0px;
  text-align: center;
  padding-top: 37px;
  background: url(../img/ico_7.png?0) no-repeat center top;
}
#col_r .row2 .box .rr .r{
  position: absolute;
  width: 50%;
  height: 50%;
  top: 40px;
  left: 145px;
  font-size: 12px;
}
#col_r .row2 .box .rr .r .ib1{
  display: inline-block;
  text-align: right;
  line-height: 20px;
}
#col_r .row2 .box .rr .r .ib2{
  display: inline-block;
  line-height: 20px;
}



#col_r .row3 {
  position: relative;
  width: 100%;
  height: 210px;
  border-top: none;
}
#col_r .row3 .ll {
  position: absolute;
  width: 222px;
  height: 160px;
  top: 40px;
  left: 19px;
}
#col_r .row3 .ll .total{
  position: absolute;
  top: 16px;
}
#col_r .row3 .ll .chart{
  position: absolute;
  width: 192px;
  height: 60px;
  top: 50px;
}
#col_r .row3 .ll .complete{
  position: absolute;
  width: 222px;
  height: 160px;
  top: 115px;
}

#col_r .row3 .rr {
  position: absolute;
  width: 322px;
  height: 160px;
  top: 40px;
  left: 225px;
}
#col_r .row3 .rr .chart1 {
  position: absolute;
  width: 160px;
  height: 156px;
  top: 0px;
  left: 0px;
  background: url(../img/chartbg1.png?0) no-repeat center top;
}
#col_r .row3 .rr .chart1 .chart {
  position: absolute;
  width: 100px;
  height: 100px;
  top: 22px;
  left: 31px;
}
#col_r .row3 .rr .chart1 .center {
  position: absolute;
  width: 118px;
  height: 60px;
  top: 54px;
  left: 24px;
}
#col_r .row3 .rr .chart1 .l {
  position: absolute;
  width: 100px;
  height: 16px;
  bottom: 0px;
  left: 5px;
}
#col_r .row3 .rr .chart1 .r {
  position: absolute;
  width: 100px;
  height: 16px;
  bottom: 0px;
  right: 5px;
  text-align: right;
}



#col_r .row3 .rr .chart2 {
  position: absolute;
  width: 160px;
  height: 156px;
  top: 0px;
  left: 162px;
  background: url(../img/chartbg1.png?0) no-repeat center top;
}
#col_r .row3 .rr .chart2 .chart {
  position: absolute;
  width: 100px;
  height: 100px;
  top: 22px;
  left: 31px;
}
#col_r .row3 .rr .chart2 .center {
  position: absolute;
  width: 118px;
  height: 60px;
  top: 54px;
  left: 24px;
}
#col_r .row3 .rr .chart2 .l {
  position: absolute;
  width: 100px;
  height: 16px;
  bottom: 0px;
  left: 5px;
}
#col_r .row3 .rr .chart2 .r {
  position: absolute;
  width: 100px;
  height: 16px;
  bottom: 0px;
  right: 5px;
  text-align: right;
}


.area_stroke {
  stroke: #003366;
  stroke-width: 1;
}
.brown{
  color: #f39800;
}
.area_green1 {
  fill: #216A3C;
}
.area_green2 {
  fill: #348361;
}
.area_green3 {
  fill: #54946F;
}
.area_yellow {
  fill: #cfaa5b !important;
}
.area_orange {
  fill: #db814a !important;
}
.area_red {
  fill: #9F373C !important;
}

.pin_green {
  background: url('../img/pin_green.png?2') no-repeat 0 0;
}
.pin_yellow {
  background: url('../img/pin_yellow.png?2') no-repeat 0 0;
}
.pin_red {
  background: url('../img/pin_red.png?2') no-repeat 0 0;
}
