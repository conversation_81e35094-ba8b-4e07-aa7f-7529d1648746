class ipadcontrol {
	constructor() {
		this.addEvent();
	}


	addEvent() {
		function formatDate(date) {
			var month = date.getMonth() + 1;
			var dateN = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
			return date.getFullYear() + "-" + (month < 10 ? "0" + month : month) + "-" + dateN;
		}

		function getDateType() {
			return $("#date_select .selected").attr("data-type");
		}

		$(function () {

			function onWeekChange(data,txt,idx){
				$.ajax({
					type: 'post',
					url: `/ipad/socket/push/yunying-${userinfo.id}`,
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: `changeWeek('${idx}')`,
					success: function (response) {
						console.log(response);
					},
					error: function (response) {
					}
				});
				 
			}

			function getWeekLabel(dateId) {
				var week = dateId.substring(5, 7);
				var year = dateId.substring(0, 4);
				return `${year}年第${week}周`;
			}
			
			function getWeeks() {
			
				var param = { "LIMIT": 53, "DATE_TYPE_CN": "例会周" };
			
				$.ajax({
					type: 'post',
					url: "/bi/web/getdimdates?week",
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: JSON.stringify(param),
					success: function (response) {
						var data = response.data;
						var list = [];
						for (var i = 0; i < data.length; i++) {
							list.push({
								data: JSON.stringify(data[i]),
								label: getWeekLabel(data[i].DATE_ID)
							})
						}
						createComboBox('main_cb_week', list, 150, 600, onWeekChange, 1);
			
						// 显示 week 日期范围
						$('#main_cb_week .combobox_label').on('mouseover', function (e) {
							e.preventDefault();
							var target = $(e.currentTarget).parent();
							var data = JSON.parse(target.attr("data"))
							$('#week_date_range').text(data.DATE_DESC_XS);
							$('#week_date_range').fadeIn();
						});
			
						// 隐藏 week 日期范围
						$('#main_cb_week .combobox_label').on('mouseout', function (event) {
							event.preventDefault();
							$('#week_date_range').fadeOut();
						});
						$('#main_cb_week .combobox_label').on('click', function (event) {
							event.preventDefault();
							$('#week_date_range').hide();
						});
			
					}
				});
			
			}
			getWeeks();
			
			$(".switchCompany").hide();
			$('#datetimepicker').datetimepicker({
				defaultDate: new Date(),
				format: "YYYY/MM/DD",
				sideBySide: true,
				maxDate: new Date(),
				widgetPositioning: {
					horizontal: 'right'
				},
				locale: 'zh-cn'
			});

			$('#datetimepicker_month').datetimepicker({
				defaultDate: new Date(),
				format: "YYYY/MM",
				viewMode: 'months',
				sideBySide: true,
				maxDate: new Date(),
				widgetPositioning: {
					horizontal: 'right'
				},
				locale: 'zh-cn'
			});
			//年
			$('#datetimepicker_year').datetimepicker({
				defaultDate: new Date(),
				format: "YYYY",
				viewMode: 'years',
				sideBySide: true,
				maxDate: new Date(),
				widgetPositioning: {
					horizontal: 'right'
				},
				locale: 'zh-cn'
			});



			$("#date_select .tab").on('click', function (e) {
				var target = $(e.currentTarget);
				$("#date_select .tab").removeClass("selected");
				target.addClass("selected");
				var dateType = target.attr("data-type");
				$(".dateCmp .datetimepicker").hide();
				$(".datetimepicker_" + dateType).show();
				$.ajax({
					type: 'post',
					url: `/ipad/socket/push/yunying-${userinfo.id}`,
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: `switchDateType('${dateType}')`,
					success: function (response) {
						console.log(response);
					},
					error: function (response) {
					}
				});
	
			});

			var changeDate = function(selelctDate,dateType){
				$.ajax({
					type: 'post',
					url: `/ipad/socket/push/yunying-${userinfo.id}`,
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: `changeDate('${selelctDate}','${dateType}')`,
					success: function (response) {
						console.log(response);
					},
					error: function (response) {
					}
				});

			}


			$('#datetimepicker').on("dp.change", function (e) {
				var selelctDate = formatDate(e.date._d);
				var dateType = getDateType();
				changeDate(selelctDate,dateType);

			});

			$('#datetimepicker_month').on("dp.change", function (e) {
				var selelctDate = formatDate(e.date._d);
				var dateType = getDateType();
				changeDate(selelctDate,dateType);
			});
			$('#datetimepicker_year').on("dp.change", function (e) {
				var selelctDate = formatDate(e.date._d);
				var dateType = getDateType();
				changeDate(selelctDate,dateType);
			});


			$(".companydetail").on("click", function (e) {
				$.ajax({
					type: 'post',
					url: `/ipad/socket/push/yunying-${userinfo.id}`,
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: "popJingyingWin()",
					success: function (response) {
						console.log(response);
					},
					error: function (response) {
					}
				});

			})
			$(".closeWin").on("click", function (e) {
				$.ajax({
					type: 'post',
					url: `/ipad/socket/push/yunying-${userinfo.id}`,
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: "closeJingyingWin()",
					success: function (response) {
						console.log(response);
					},
					error: function (response) {
					}
				});

			})


			var showComList = function () {
				$(".mask").show();
				$(".companylist").show();
			}
			var hideComList = function () {
				$(".mask").hide();
				$(".companylist").hide();
			}

			userInfoDtd.done(function () {
				
				//权限相关
				var _find_right = $.grep(userinfo.MENU.menus, function (ele, i) {
					return ("a0-1.html".indexOf(ele.url)|| "a0-13.html".indexOf(ele.url)) > -1 && ele.url != "";
				});

				var authCompany = _find_right[0].companys;
				var authCompanyList = [];
				$.each(authCompany,function(idx,item){
					authCompanyList.push(item.company);
				})

				$.ajax({
					type: 'POST',
					url: '/bi/query/company',
					async: true,
					dataType: 'json',
					data: '',
					success: function (response) {
						if (response.errorcode == '9999') {
							needlogin = true;
							alert("请重新登录");
							redirectToSSO();
							return;
						}
						var htmlStr = "";
						var companyList = response.comp;
						for (var i = 0; i < companyList.length; i++) {
							if(authCompanyList.indexOf(companyList[i].id)>-1){
								htmlStr += `<span code="${companyList[i].code}">${companyList[i].name}</span>`;
							}
						}

						$(".companylist").append(htmlStr);

						$(".switchCompany").show();
						$(".switchCompany").on("click", function () {
							showComList();
						});
						$(".mask").click(function (event) {
							hideComList();
						});

						$(".closeBtn").click(function (event) {
							hideComList();
						});

						$(".companylist span").click(function (event) {
							let companyCode = $(event.currentTarget).attr("code");
							hideComList();

							var dataCommand = `switchCompany('${companyCode}')`
							$.ajax({
								type: 'post',
								url: `/ipad/socket/push/yunying-${userinfo.id}`,
								contentType: 'application/json',
								dataType: 'json',
								async: true,
								data: dataCommand,
								success: function (response) {
									console.log(response);
								},
								error: function (response) {
								}
							});

						});
					}
				});
			});


		});
	}
}
let i = new ipadcontrol();