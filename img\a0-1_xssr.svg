<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24" height="24" viewBox="0 0 24 24">
  <metadata><?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c138 79.159824, 2016/09/14-01:09:01        ">
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
      <rdf:Description rdf:about=""/>
   </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?></metadata>
<defs>
    <style>
      .cls-1 {
        fill: #00a0e9;
        fill-rule: evenodd;
        filter: url(#filter);
      }
    </style>
    <filter id="filter" x="1485" y="504" width="24" height="24" filterUnits="userSpaceOnUse">
      <feImage preserveAspectRatio="none" x="1485" y="504" width="24" height="24" result="image" xlink:href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCI+CiAgPGRlZnM+CiAgICA8c3R5bGU+CiAgICAgIC5jbHMtMSB7CiAgICAgICAgZmlsbDogdXJsKCNsaW5lYXItZ3JhZGllbnQpOwogICAgICB9CiAgICA8L3N0eWxlPgogICAgPGxpbmVhckdyYWRpZW50IGlkPSJsaW5lYXItZ3JhZGllbnQiIHgxPSIxMiIgeTE9IjI0IiB4Mj0iMTIiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KICAgICAgPHN0b3Agb2Zmc2V0PSItMC4yNSIgc3RvcC1jb2xvcj0iIzhlZWJmNyIvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEuMjUiIHN0b3AtY29sb3I9IiMwMGE5ZmYiLz4KICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgPC9kZWZzPgogIDxyZWN0IGNsYXNzPSJjbHMtMSIgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0Ii8+Cjwvc3ZnPgo="/>
      <feComposite result="composite" operator="in" in2="SourceGraphic"/>
      <feBlend result="blend" in2="SourceGraphic"/>
    </filter>
  </defs>
  <path id="小时收入" class="cls-1" d="M1497,504a12,12,0,1,0,12,12A12,12,0,0,0,1497,504Zm0.74,22.48v-1.436h-1.49v1.436a10.5,10.5,0,0,1-9.72-9.693l1.45,0.022,0.02-1.491-1.48-.021a10.509,10.509,0,0,1,9.74-9.777v1.5h1.49v-1.5a10.5,10.5,0,0,1,9.73,9.777l-1.48.021,0.03,1.491,1.45-.022A10.518,10.518,0,0,1,1497.74,526.48ZM1497,516m3.17-5.8-0.48-.575h4.78l-5.45,6.839h3.34l-0.31,2.281h-3.65l-0.63,4.464h-3.64l0.62-4.464h-3.62l0.31-2.281h3.33l-3.21-6.215-0.53-.624h4.1l2.09,4.631Z" transform="translate(-1485 -504)"/>
</svg>
