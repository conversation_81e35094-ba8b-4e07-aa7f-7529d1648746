
var company = 'HU';
var baseCode = "PEK";
var companyNodeId = 9;


function getUrlParam(paraName) {
	let url = document.location.toString();
	let arrObj = url.split("?");

	if (arrObj.length > 1) {
		let arrPara = arrObj[1].split("&");
		let arr;

		for (let i = 0; i < arrPara.length; i++) {
			arr = arrPara[i].split("=");

			if (arr != null && arr[0] == paraName) {
				return arr[1];
			}
		}
		return "";
	}
	else {
		return "";
	}
}
var hash = getUrlParam("company")
var companyCode2NodeId = {
	'HNAHK': 1,
	'HU': 9,
	'JD': 4477,
	'8L': 1990,
	'HX': 8028,
	'PN': 5732,
	'GS': 4248,
	'FU': 102025,
	'UQ': 102024,
	'Y8': 13,
	'GX': 801775,
	'9H': 11,
	'GT': 801547,
	'UO': 101352,
	'AW': 19612,//加纳
	'B5': 5157,//金鹿
	'CN': 6381,//大新华
}

if (!hash || 'HNAHK' == hash) {
	company = "HU";
} else {
	company = hash;
	companyNodeId = companyCode2NodeId[hash];
}

function onCompanyChanged() {
	main(company);
}

var baseCode = 'PEK'


function main(company) {
	var page = new Vue({
		el: '#vue-container',
		// template: $("#body_template").html(),
		components: {
			// 'vue-seamless-scroll': httpVueLoader('./js/vue/vue-seamless-scroll.vue'),
		},
		data: function () {
			return {
				baseCode: 'PEK',
				baseName: '北京',
				ORI_NO_SCH: 0,
				company: company,
				baseList: [],
				companyList: companylist,
				companyNodeId: companyNodeId,
				flightInfo: {
					flightNum: "89"
				},
				delayData: {
					compCauseItemList: [],
					noneCauseItemList: [],
					compCauseNum: 0,
					noneCauseNum: 0,
					total: 0
				},
				totalAcNum: '-', //总运力
				stopAcNum: '-', //停场运力
				airNum: '-', //空中运力
				groundNum: '-',//地面运力
				yunliList: [],



				//rock start
				actypeId2Code: {},
				today: '',
				stdStartUtcTime: '',
				stdEndUtcTime: '',
				fltStaticDep: {},
				fltStaticArr: {},
				fasData: {
					arrPftc: 0,
					arrCftc: 0,
					depPftc: 0,
					depCftc: 0,
					depPftcl: 0,
					depCftcl: 0,
					depPftci: 0,
					depCftci: 0,
				},
				pftc: 0, // 航班总量
				exeRate: 0,// 航班总执行率
				pftcl: 0, // 国内航班总数
				execRateChina: 0, // 国内执行航班率
				pftci: 0, // 国际计划航班总数
				execRateInt: 0,  // 国际执行航班率
				planNum: 0, // 计划运输旅客总量
				ckiNum: 0,// 已运输旅客总量
				normalRate: 0, // 航班正常率
				acNoTotal: 0,// 基地过夜飞机架数
				acListData: [],// 基地过夜飞机列表
				// rock end



				// Sherlock start
				dftc: 0,
				qftc1: 0,
				pfdtc12: 0,
				pfdtc24: 0,
				pfdtc4: 0,
				pfdtci: 0,
				planeCount: 0,
				planeList: [],
				fiveRate: {
					num1: 0,
					num2: 0,
					num3: 0,
					num4: 0,
					num5: 0,
				},
				bookNumIn: 0,
				ckiNumIn: 0,
				cvsTrvIn: 0,
				bookNumOut: 0,
				ckiNumOut: 0,
				cvsTrvOut: 0,
				aircraftList: [],
				psrFltList: [],
				flightInfoList: {},
				acNoInList: [],
				acNoOutList: [],
				weatherImg: 'img/a0-14/weather_sun.svg',
				weatherMap: {
					'晴': 'img/a0-14/weather_sun.svg',
					'沙': 'img/a0-14/weather_dust.svg',
					'雹': 'img/a0-14/weather_pao.svg',
					'雾': 'img/a0-14/weather_fog.svg',
					'烟': 'img/a0-14/weather_fog.svg',
					'阴': 'img/a0-14/weather_gloomy.svg',
					'雷': 'img/a0-14/weather_lei.svg',
					'暴': 'img/a0-14/weather_rain.svg',
					'风': 'img/a0-14/weather_wind.svg',
					'霾': 'img/a0-14/weather_haze.svg',
					'云': 'img/a0-14/weather_clou.svg',
					'雨': 'img/a0-14/weather_rain.svg',
					'雪': 'img/a0-14/weather_snow.svg',
				},
				visibility: 0,
				windFs: 0,
				windFx: 0,
				temperature: 0,
				//Sherlock end



				// slin  start
				abnormal: {
					companyCnt: '-',
					noCompanyCnt: '-',
					companyPercent: '-',
					noCompanyPercent: '-'
				},
				//slin end
			}
		},


		destroyed() {
			let me = this;
			if (me.intervalId != null) {
				clearInterval(me.intervalId);
			}
		},
		mounted() {
			let me = this;
			this.queryBase();
			me.doQueryCmp();
			me.intervalId = setInterval(me.doQuery, 300000);

		},
		methods: {
			doQuery() {
				this.doQueryCmp();
				this.doQueryBaseData();

			},
			queryOriFlightCounts() {
				var me = this;

				var company = me.company;
				if (company == 'CN') {
					company = 'HU';
				}
				var param = {
					"compCode": company,
					"airportCode": me.baseCode,
					"kpiCodes": ['ORI_NO_SCH'],
					"dateType": "D",
					"dateId": moment(new Date()).format('YYYYMMDD')
				}


				$.ajax({
					type: 'post',
					url: "/bi/spring/facCompArpFlightKpi/queryKpi.json",
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: JSON.stringify(param),
					success: function (response) {
						if (response.data && response.data.ORI_NO_SCH) {
							me.ORI_NO_SCH = Number(response.data.ORI_NO_SCH.kpiValue)
						} else {
							me.ORI_NO_SCH = '-'
						}

					},
					error: function () {
					}
				});

				var arrParam = {
					"stdStartUtcTime": me.stdStartUtcTime,
					"stdEndUtcTime": me.stdEndUtcTime,
					"companyCodes": company,
					"depstns": "",
					"showMore": 1,
					"arrstns": me.baseCode
				}

				$.ajax({
					type: 'post',
					url: "/bi/spring/focStaticApi/flightAmountStaticV2?arrstns=" + me.baseCode,
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: JSON.stringify(arrParam),
					success: function (response) {
						me.fltStaticArr = response.data;
						me.setFasData();
					},
					error: function () {
					}
				});
			},
			doQueryCmp() {
				this.getDelayReason();
				this.getAcStatusStat();
				this.flightAmountStatic();
			},
			// 共用方法 START
			setUTCTime() {
				this.yesterday = moment(new Date()).subtract(1, 'day').format('YYYYMMDD');
				this.today = moment(new Date()).format('YYYYMMDD');
				this.stdEndUtcTime = moment(new Date()).format('YYYY-MM-DD 15:59:59');
				this.stdStartUtcTime = moment(new Date()).subtract(1, 'day').format('YYYY-MM-DD 16:00:00');;

			},
			getWeatherFx() {
				if (this.windFx === 888) {
					return '方向不定';
				}
				return `${this.windFx}°`
			},
			// 共用方法 END

			//rock start
			getAllSubCompany() {
				var rs = [];
				var len = this.companyList.length;
				for (var i = 0; i < len; i++) {
					var dat = this.companyList[i];
					if (dat.code != parent_company && dat.code != 'HNAIN') {
						rs.push(dat.code);
					}
				}
				return rs
			},
			// 当日航班量
			queryFlightAmountStatic() {
				var me = this;
				me.setUTCTime()
				var company = me.company;
				if (company == 'CN') {
					company = 'HU';
				}
				var param = {
					"stdStartUtcTime": me.stdStartUtcTime,
					"stdEndUtcTime": me.stdEndUtcTime,
					"companyCodes": company,
					"depstns": me.baseCode,
					"showMore": 1,
					"arrstns": ""
				}


				$.ajax({
					type: 'post',
					url: "/bi/spring/focStaticApi/flightAmountStaticV2?depstns=" + me.baseCode,
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: JSON.stringify(param),
					success: function (response) {
						me.fltStaticDep = response.data;
						me.setFasData();
					},
					error: function () {
					}
				});

				var arrParam = {
					"stdStartUtcTime": me.stdStartUtcTime,
					"stdEndUtcTime": me.stdEndUtcTime,
					"companyCodes": company,
					"depstns": "",
					"showMore": 1,
					"arrstns": me.baseCode
				}

				$.ajax({
					type: 'post',
					url: "/bi/spring/focStaticApi/flightAmountStaticV2?arrstns=" + me.baseCode,
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: JSON.stringify(arrParam),
					success: function (response) {
						me.fltStaticArr = response.data;
						me.setFasData();
					},
					error: function () {
					}
				});
			},
			// 界面回显数据
			setFasData() {
				var me = this;
				var stsDep = me.fltStaticDep;
				var stsArr = me.fltStaticArr;
				if (!stsDep || !stsArr) {
					return;
				}

				me.fasData.arrPftc = stsArr.pftc
				me.fasData.arrCftc = stsArr.cftc
				me.fasData.depPftc = stsDep.pftc
				me.fasData.depCftc = stsDep.cftc
				me.fasData.depPftcl = stsDep.pftcl//国内计划航班总数
				me.fasData.depCftcl = stsDep.cftcl//国内已执行航班总数
				me.fasData.depPftci = stsDep.pftci//国际计划航班总数
				me.fasData.depCftci = stsDep.cftci//国际已执行航班总数

			},


			// 基地过夜飞机架数
			getArpOvernightPlane() {
				var me = this;
				me.setUTCTime();
				var company = me.company;
				if (company == 'CN') {
					company = 'HU';
				}
				me.acNoTotal = 0;


				$.ajax({
					type: 'get',
					url: `/bi/spring/aoc/bj/getOverNightFlight?compCode=${company}&arp=${me.baseCode}&dateId=${me.today}`,
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					success: function (response) {
						var data = response.data;
						me.acNoTotal = data.cnt;
						var colorList = ['#fff', '#2BCC43', '#FAAF33', '#44A3F4'];
						var acList = [];
						var list = data.list;
						if (list && list.length > 0) {
							for (var i = 0; i < list.length; i++) {
								var item = list[i];
								var colorIndex = i % colorList.length;
								var color = colorList[colorIndex];
								acList.push({
									acType: item.acType,
									acNum: item.overNightNum + '/' + item.kpiValue,
									acNumPer: Math.round(item.overNightNum / item.kpiValue * 100),
									barPlan: item.overNightNum / item.kpiValue * 100,
									color: color
								});
							}
						}

						if (acList.length == 0) {
							acList = [
								{ acType: '330', acNum: 0, acNumPer: 0, barPlan: 0, color: '#fff' },
								{ acType: '737', acNum: 0, acNumPer: 0, barPlan: 0, color: '#2BCC43' },
								{ acType: '350', acNum: 0, acNumPer: 0, barPlan: 0, color: '#FAAF33' },
								{ acType: '787', acNum: 0, acNumPer: 0, barPlan: 0, color: '#44A3F4' },
							]
						}

						me.acListData = acList
					},
					error: function () {
					}
				});

			},
			setArpPlane(data) {
				if (!this.actypeId2Code) {
					setTimeout(this.setArpPlane, 10, data);
					return;
				}

				// 计算总量
				var AC_NO_TOTAL = 0; // 所有机场预计过夜飞机架数

				var ac_no_lst = data['AC_NO']['D'][this.baseCode];
				console.log('ac_no_lst', ac_no_lst, this.baseCode)

				var ac_no = {};

				for (var ac in ac_no_lst) {
					var accode = this.actypeId2Code[ac];
					var dlst = ac_no_lst[ac];
					var acnum = Number(dlst);
					if (acnum >= 0) {
						AC_NO_TOTAL += acnum;
						ac_no[accode] = acnum;
					}
				}

				this.acNoTotal = AC_NO_TOTAL;
				var colorList = ['#fff', '#2BCC43', '#FAAF33', '#44A3F4'];
				var acList = [];

				for (var accode in ac_no) {
					var acnum1 = ac_no[accode];
					var acnum2 = ac_arr_no[accode];
					if (acnum1 >= 0 && acnum2 >= 0) {
						var colorIndex = Object.keys(ac_no).indexOf(accode) % colorList.length;
						var color = colorList[colorIndex];
						acList.push({
							acType: accode,
							acNum: acnum2 + '/' + acnum1,
							acNumPer: Math.round(acnum2 / acnum1 * 100),
							barPlan: acnum2 / acnum1 * 100,
							color: color
						});
					}
				}

				if (acList.length == 0) {
					acList = [
						{ acType: '330', acNum: 0, acNumPer: 0, barPlan: 0, color: '#fff' },
						{ acType: '737', acNum: 0, acNumPer: 0, barPlan: 0, color: '#2BCC43' },
						{ acType: '350', acNum: 0, acNumPer: 0, barPlan: 0, color: '#FAAF33' },
						{ acType: '787', acNum: 0, acNumPer: 0, barPlan: 0, color: '#44A3F4' },
					]
				}

				this.acListData = acList
			},
			// 计划运输旅客总量
			getPsrSummInfo() {
				let me = this;
				me.setUTCTime();
				var param = {
					"companyCodes": me.company,
					"stdStartUtcTime": me.stdStartUtcTime,
					"stdEndUtcTime": me.stdEndUtcTime,
					"detailType": "pftc", //统计航班（总计）
					//"psrType":"all"
				}
				$.ajax({
					type: 'post',
					url: "/bi/web/getPsrSummInfo",
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: JSON.stringify(param),
					success: function (response) {
						me.planNum = Number(response.planNum); //旅客订票人数
						// setTrvNum();
					},
					error: function () { }
				});

				var param2 = {
					"companyCodes": me.company,
					"stdStartUtcTime": me.stdStartUtcTime,
					"stdEndUtcTime": me.stdEndUtcTime,
					"detailType": "cftc",  //已执行
					//"psrType":"all"
				}

				$.ajax({
					type: 'post',
					url: "/bi/web/getPsrSummInfo",
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: JSON.stringify(param2),
					success: function (response) {
						me.ckiNum = Number(response.ckiNum); //旅客值机人数
						me.drawTrvNum();
					},
					error: function () { }
				});
			},
			drawTrvNum() {
				this.drawPie(this.ckiNum / this.planNum, this.$refs.echTrvNum, 60, 10)
			},
			// 航班总量
			drawExecRate(rateData) {
				let me = this;
				let option = {
					series: [
						{
							radius: '100%',
							type: 'gauge',
							itemStyle: {
								color: '#44A3F4'
							},
							progress: {
								show: true,
								width: 13
							},
							pointer: {
								show: false
							},
							axisLine: {
								lineStyle: {
									width: 13,
									color: [
										[1, '#0062AB'] // 进度颜色为蓝色
									]
								}
							},
							axisTick: {
								show: false
							},
							splitLine: {
								show: false
							},
							axisLabel: {
								show: false
							},
							anchor: {
								show: false
							},
							title: {
								show: false
							},
							detail: {
								formatter: function (value) {
									return '{a|' + me.pftc + '}\n{b|架次}';
								},
								rich: {
									a: {
										fontWeight: 'bold',
										fontSize: 18,
										color: '#fff',
										lineHeight: 20,
									},
									b: {
										fontSize: 12,
										color: '#fff',
									}
								}
							},
							data: [
								{
									value: rateData
								}
							]
						},
					]
				};
				this.drawGauge('echFltCount', option)
			},
			//航班正常量
			drawNormalRate() {
				let me = this;
				let option = {
					series: [
						{
							radius: '100%',
							type: 'gauge',
							itemStyle: {
								color: '#44A3F4'
							},
							progress: {
								show: true,
								width: 25
							},
							pointer: {
								itemStyle: {
									color: '#fff' // 将指针颜色设置为白色
								}
							},
							axisLine: {
								lineStyle: {
									width: 25,
									color: [
										[1, '#0062AB'] // 进度颜色为蓝色
									]
								}
							},
							axisTick: {
								show: false
							},
							splitLine: {
								show: false
							},
							axisLabel: {
								show: false
							},
							anchor: {
								show: false
							},
							title: {
								show: false
							},
							detail: {
								show: false
							},
							data: [
								{
									value: me.normalRate
								}
							]
						},
					]
				};
				this.drawGauge('echNormalRate', option)
			},
			drawGauge(dom, domOption) {
				let echart = echarts.init(this.$refs[dom]);
				let option = domOption
				echart.setOption(option);
				window.addEventListener("resize", () => {
					echart.resize();
				});
				echart.hideLoading();
			},


			// rock end





			// Sherlock start

			flightAmountStatic() {
				let me = this;
				var stdEndUtcTime = moment(new Date()).format('YYYY-MM-DD 15:59:59');
				var stdStartUtcTime = moment(new Date()).subtract(1, 'day').format('YYYY-MM-DD 16:00:00');
				var company = me.company;
				var param = {
					"stdStartUtcTime": stdStartUtcTime,
					"stdEndUtcTime": stdEndUtcTime,
					"companyCodes": company,
					"returnAbnormalFlightByCompanyReason": "true",
					"showMore": 1,
					"AcTypeList": "",
					"depstns": "",
					"arrstns": ""
				}
				$.ajax({
					type: 'post',
					url: "/bi/spring/focStaticApi/flightAmountStaticV2",
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: JSON.stringify(param),
					success: function (res) {
						var response = res.data;
						me.pftc = response.pftc; // 航班总量
						var exeRate = Number(response.pftc) == 0 ? 0 : Math.round(Number(response.cftc) / Number(response.pftc) * 100)
						me.exeRate = exeRate + '%'; // 航班总执行率
						var pftcl = Number(response.pftcl); //国内计划航班总数
						me.pftcl = pftcl //国内计划航班总数
						var cftcl = Number(response.cftcl); //国内已执行航班总数
						me.execRateChina = Number(pftcl) == 0 ? 0 + '%' : Math.round(cftcl / pftcl * 100) + '%' //国内执行航班率
						var pftci = Number(response.pftci); //国际计划航班总数
						me.pftci = pftci //国际计划航班总数
						var cftci = Number(response.cftci); //国际已执行航班总数
						me.execRateInt = Number(pftci) == 0 ? 0 + '%' : Math.round(Number(cftci) / Number(pftci) * 100) + '%' //国际执行航班率
						var pfdappPercent = Number(response.pfdappPercent); //计划航班中的正常航班比率
						me.normalRate = Math.round(pfdappPercent * 100) / 100;
						me.drawNormalRate()
						me.dftc = response.dftc; //备降航班总数
						me.qftc1 = response.qftc1; //取消航班总数
						me.pfdtc12 = response.pfdtc12; //计划航班中延误1~2小时的航班总数
						me.pfdtc24 = response.pfdtc24; //计划航班中延误2-4小时的航班总数
						me.pfdtc4 = response.pfdtc4; //计划航班中延误>4小时的航班总数
						// 国际航班延误数量
						me.pfdtci = Number(response.pfdtci); //计划航班中延误国际航班总数

						// 航班总量图
						me.drawExecRate(exeRate)

						var pfdtc = Number(response.pfdtc);
						console.log("总数=", pfdtc)
						var abfcr = Number(response.abfcr);
						me.setDelayCause(pfdtc, abfcr);
					},
					error: function () { }
				});

				// 非计划停场
				$.ajax({
					type: 'get',
					url: `/bi/spring/sdmCapacity/queryNotPlanPark?companyCode=${company}`,
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					success: function (response) {
						var aog_planes = response.data;
						// AOG 飞机列表
						var len = aog_planes.length;
						me.planeCount = len;
						me.planeList = aog_planes;
					}
				});
			},

			/**
			 * 五率
			 */
			queryFiveRate() {
				var me = this;
				$.ajax({
					type: 'get',
					url: `/bi/spring/aoc/bj//queryFiveRate?arp=${me.baseCode}`,
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					success: function (res) {
						var data = res.data;
						data.forEach(i => {
							if (i.type == '机组到位') {
								me.fiveRate.num1 = Number(i.num);
							}
							if (i.type == '机务放行') {
								me.fiveRate.num2 = Number(i.num);
							}
							if (i.type == '通知上客') {
								me.fiveRate.num3 = Number(i.num);
							}
							if (i.type == '客舱关闭') {
								me.fiveRate.num4 = Number(i.num);
							}
							if (i.type == '货舱关闭') {
								me.fiveRate.num5 = Number(i.num);
							}
						});
						me.drawFiveRateChart('机组到位', me.$refs.fiveEchart1, me.fiveRate.num1, me.getColor(me.fiveRate.num1));
						me.drawFiveRateChart('机务放行', me.$refs.fiveEchart2, me.fiveRate.num2, me.getColor(me.fiveRate.num2));
						me.drawFiveRateChart('通知上客', me.$refs.fiveEchart3, me.fiveRate.num3, me.getColor(me.fiveRate.num3));
						me.drawFiveRateChart('客舱关闭', me.$refs.fiveEchart4, me.fiveRate.num4, me.getColor(me.fiveRate.num4));
						me.drawFiveRateChart('货舱关闭', me.$refs.fiveEchart5, me.fiveRate.num5, me.getColor(me.fiveRate.num5));
					},
					error: function () { }
				});
			},
			getColor(num) {
				var color;
				if (num < 70) {
					color = '#ff0000';
				} else if (num < 80) {
					color = '#FAAF33';//'#ffc600';
				} else {
					color = '#44A3F4';//'#99FFFF';
				}
				return color;
			},
			drawFiveRateChart(name, ref, num, color) {
				let me = this;
				let echart = echarts.init(ref);
				let option = {
					tooltip: {
						trigger: 'item',
						formatter: "{a} <br/>{b}:({c}%)"
					},
					color: [color, '#0062AB'],//环形图两部分的颜色
					graphic: {
						elements: [{
							type: 'image',
							left: "center",
							top: "center",
							style: {
								width: 36,
								height: 36
							}
						}]
					},
					series: [
						{
							name: name,
							type: 'pie',//设为饼图
							radius: ['85%', '100%'],//设置内半径和外半径，形成环状
							avoidLabelOverlap: false,
							label: {
								normal: {
									show: false,
									position: 'center'
								}
							},
							data: [{ value: me.getDelayRate(num, 100), name: '达成' },
							{ value: me.getDelayRate(100 - num, 100), name: '未达成' }]
						}
					]
				};

				echart.setOption(option);
				window.addEventListener("resize", () => {
					echart.resize();
				});
				echart.hideLoading();
			},
			// 天气
			getWeather() {
				var me = this;
				var param = {
					'airport': me.baseCode
				}
				$.ajax({
					type: 'post',
					url: "/bi/web/7x2_arp_weather",
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: JSON.stringify(param),
					success: function (response) {

						if (Number(response.errorcode) == 0) {
							var info = {};
							info.code = response.airport;
							me.visibility = isNaN(response.visibility) || response.visibility == 0 ? 9999 : Number(response.visibility); //能见度
							me.windFs = isNaN(response.windFs) ? 0 : Number(response.windFs); //风速
							me.windFx = isNaN(response.windFx) ? 0 : Number(response.windFx); //风向
							me.temperature = isNaN(response.temperature) ? 0 : Number(response.temperature); // 温度
							var weatherInfoTxt = response.weatherInfoTxt || '';
							weatherInfoTxt = weatherInfoTxt.replace(/<[^>]+>/g, "");
							var weatherCss = 'img/a0-14/weather_sun.svg';
							for (var wtxt in me.weatherMap) {
								if (weatherInfoTxt.indexOf(wtxt) > -1) {
									weatherCss = me.weatherMap[wtxt];
								}
							}
							me.weatherImg = weatherCss;
						} else {
							console.log('7x2_arp_weather Error');
						}
					},
					error: function () {

					}
				});
				me.getRadarImage();
			},

			// ------------------------------------------------------------------------
			// 雷达图
			// ------------------------------------------------------------------------


			getRadarImage() {
				var me = this
				var param = {
					"airport": me.baseCode,
				}
				$.ajax({
					type: 'post',
					url: "/bi/web/7x2_arp_weather_radar",
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: JSON.stringify(param),
					success: function (response) {
						var list = response.data;
						var cndtradartime = '1970-01-01 00:00:00';
						var cnvcradarfile = '';

						if (list) {
							for (var i = list.length - 1; i >= 0; i--) {
								var d = list[i];
								var d1 = parserDate(d.cndtradartime);
								var d2 = parserDate(cndtradartime);
								if (d1.getTime() > d2.getTime()) {
									cndtradartime = d.cndtradartime;
									cnvcradarfile = d.cnvcradarfile;
								}
							}

							if (cnvcradarfile && cnvcradarfile.length > 0) {
								$('.weather_atlas').css('background-image', 'url(/bi/web/getRadarpic?filename=' + cnvcradarfile + ')');
							}
						} else {
							console.log('雷达接口无数据返回');
						}
					},
					error: function () {
					}
				});
			},

			// 获取航班信息
			getFlighList() {
				var me = this;
				var stdEnd = moment(new Date()).format('YYYY-MM-DD 23:59:59');
				var stdStart = moment(new Date()).format('YYYY-MM-DD 00:00:00');
				var company = me.company;
				if (company == 'CN') {
					company = 'HU';
				}
				var param = {
					"stdStart": stdStart,
					"stdEnd": stdEnd,
					"acOwner": company == 'HNAHK' ? '' : company,
					"depOrArrStn": me.baseCode,
					"statusList": '',
				}
				$.ajax({
					type: 'post',
					url: "/bi/web/getStandardFocFlightInfo",
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: JSON.stringify(param),
					success: function (response) {
						var list = response.data;
						var flightInfoList = {};
						for (var i = list.length - 1; i >= 0; i--) {
							var obj = list[i];
							flightInfoList[obj.flightNo] = obj;
						}
						me.flightInfoList = flightInfoList;
					},
					error: function () {
					}
				});
			},

			// 进港
			getInPsrStat() {
				var me = this;
				var param = {
					"arrIataId": me.baseCode,
				}
				var company = me.company;
				if (company == 'CN') {
					company = 'HU';
				}
				$.ajax({
					type: 'post',
					url: "/bi/web/findPsrStat",
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: JSON.stringify(param),
					success: function (response) {
						var bookNum = 0;
						var ckiNum = 0;

						var acNoInList = [];
						var acNo2ckiNumIn = [];
						if (response && response.data) {

							var fltlst = response.data;
							me.psrFltList = me.psrFltList.concat(fltlst);

							var len = fltlst.length;
							for (var i = 0; i < len; i++) {
								var flt = fltlst[i];
								if (company == 'HNAHK' || flt.flightNo.indexOf(company) > -1) {
									var fltinfo = me.flightInfoList[flt.flightNo];

									if (!isNaN(flt.bookNum)) {
										bookNum += Number(flt.bookNum); // 订票人数
									}
									if (!isNaN(flt.ckiNum)) {
										ckiNum += Number(flt.ckiNum); // 值机人数
										if (fltinfo) {
											acNo2ckiNumIn[fltinfo.acLongNo] = Number(flt.ckiNum);
										}
									}

									if (fltinfo) {
										acNoInList.push(fltinfo.acLongNo);
									}
								}

							}
							me.acNo2ckiNumIn = acNo2ckiNumIn;
							me.acNoInList = acNoInList;
							me.bookNumIn = bookNum;
							me.ckiNumIn = ckiNum;
							me.drawPsrStatChart('进港人数', me.$refs.todayPlanEchart1, me.ckiNumIn, me.bookNumIn - me.ckiNumIn, '#2BCC43', '人')
							me.setTrvRateIn(ckiNum);
						}

					},
					error: function () {
					}
				});
			},

			// 出港
			getOutPsrStat() {
				var me = this;
				var param = {
					"depIataId": me.baseCode,
				}
				var company = me.company;
				if (company == 'CN') {
					company = 'HU';
				}
				$.ajax({
					type: 'post',
					url: "/bi/web/findPsrStat",
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: JSON.stringify(param),
					success: function (response) {
						var bookNum = 0;
						var ckiNum = 0;

						var acNoOutList = [];
						var acNo2ckiNumOut = [];
						if (response && response.data) {

							var fltlst = response.data;
							me.psrFltList = me.psrFltList.concat(fltlst);

							var len = fltlst.length;
							for (var i = 0; i < len; i++) {
								var flt = fltlst[i];
								if (company == 'HNAHK' || flt.flightNo.indexOf(company) > -1) {
									var fltinfo = me.flightInfoList[flt.flightNo];

									if (!isNaN(flt.bookNum)) {
										bookNum += Number(flt.bookNum); // 订票人数
									}
									if (!isNaN(flt.ckiNum)) {
										ckiNum += Number(flt.ckiNum); // 值机人数
										if (fltinfo) {
											acNo2ckiNumOut[fltinfo.acLongNo] = Number(flt.ckiNum);
										}
									}

									if (fltinfo) {
										acNoOutList.push(fltinfo.acLongNo);
									}
								}

							}
							me.acNo2ckiNumOut = acNo2ckiNumOut;
							me.acNoOutList = acNoOutList;
							me.bookNumOut = bookNum;
							me.ckiNumOut = ckiNum;
							me.drawPsrStatChart('出港人数', me.$refs.todayPlanEchart3, me.ckiNumOut, me.bookNumOut - me.ckiNumOut, '#2BCC43', '人')
							me.setTrvRateOut(ckiNum);
						}

					},
					error: function () {
					}
				});
			},

			getAcAircraftList() {
				var me = this;
				var company = me.company;
				var companyNodeId = me.companyNodeId;
				if (company == 'CN') {
					companyNodeId = 9;
				}
				var param = {
					"companyNodeId": companyNodeId,
				}
				$.ajax({
					type: 'post',
					url: "/bi/web/getAcAircraftList",
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					data: JSON.stringify(param),
					success: function (response) {
						me.aircraftList = response.data;
					},
					error: function () {
					}
				});
			},

			setTrvRateIn(ckiNum) {
				var me = this;
				// 飞机布局
				if (me.aircraftList.length == 0 || me.acNoInList.length == 0) {
					setTimeout(me.setTrvRateIn, 10, ckiNum);
					return;
				}

				var seats_total = 0;
				var ckiNum = 0;
				var len = me.aircraftList.length;
				for (var i = 0; i < len; i++) {
					var dd = me.aircraftList[i];
					var acNo = dd.data.longNo;
					var cabin = dd.data.cabin;
					var seats = me.getCabinSeats(cabin);
					if (me.acNoInList.indexOf(acNo) >= 0 && seats > 0 && me.acNo2ckiNumIn[acNo] >= 0) {
						seats_total += Number(seats);
						ckiNum += me.acNo2ckiNumIn[acNo];
					}
				}
				var rate = ckiNum / seats_total;
				me.cvsTrvIn = Math.round(rate * 1000) / 10;
				me.drawPsrStatChart('进港上座率', me.$refs.todayPlanEchart2, me.cvsTrvIn, (100 - me.cvsTrvIn).toFixed(2), '#44A3F4', '%')
			},
			setTrvRateOut(ckiNum) {
				var me = this;
				// 飞机布局
				if (me.aircraftList.length == 0 || me.acNoOutList.length == 0) {
					setTimeout(me.setTrvRateOut, 10, ckiNum);
					return;
				}
				var seats_total = 0;
				var ckiNum = 0;
				var len = me.aircraftList.length;
				for (var i = 0; i < len; i++) {
					var dd = me.aircraftList[i];
					var acNo = dd.data.longNo;
					var cabin = dd.data.cabin;
					var seats = me.getCabinSeats(cabin);
					if (me.acNoOutList.indexOf(acNo) >= 0 && seats > 0 && me.acNo2ckiNumOut[acNo] >= 0) {
						seats_total += Number(seats);
						ckiNum += me.acNo2ckiNumOut[acNo];
					}
				}
				var rate = ckiNum / seats_total;
				me.cvsTrvOut = Math.round(rate * 1000) / 10;
				me.drawPsrStatChart('出港上座率', me.$refs.todayPlanEchart4, me.cvsTrvOut, (100 - me.cvsTrvOut).toFixed(2), '#44A3F4', '%')
			},
			getCabinSeats(cabin) {
				// cabin 座舱布局 C6Y170
				var arr = cabin.split('Y');
				var seat1 = !isNaN(arr[1]) ? arr[1] : 0;
				var arr2 = arr[0].split('C');
				var seat2 = !isNaN(arr2[1]) ? arr2[1] : 0;
				var seat = Number(seat1) + Number(seat2);

				return seat;
			},
			// 进出港饼图
			drawPsrStatChart(name, ref, num1, num2, color, d) {
				console.log(name, num1, num2, d)
				var value = d === '%' ? Number(num1) / 100 : num1 / (num1 + num2)
				console.log(value)
				this.drawPie(value, ref, 80, 10, '#1B488E', color, true)
			},
			// 进出港饼图(弃用)
			drawPsrStatChart1(name, ref, num1, num2, color, d) {
				let echart = echarts.init(ref);
				let option = {
					tooltip: {
						trigger: 'item',
						formatter: "{a} <br/>{b}:{c}" + d
					},
					color: [color, '#0062AB'],//环形图两部分的颜色
					graphic: {
						elements: [{
							type: 'image',
							left: "center",
							top: "center",
							style: {
								width: 36,
								height: 36
							}
						}]
					},
					series: [
						{
							name: name,
							type: 'pie',//设为饼图
							radius: ['85%', '100%'],//设置内半径和外半径，形成环状
							avoidLabelOverlap: false,
							label: {
								normal: {
									show: false,
									position: 'center'
								}
							},
							data: [{ value: num1, name: '完成' },
							{ value: num2, name: '未完成' }]
						}
					]
				};

				echart.setOption(option);
				window.addEventListener("resize", () => {
					echart.resize();
				});
				echart.hideLoading();
			},
			//Sherlock end





			// slin  start

			setDelayCause(total, compCnt) {
				var noCompCnt = total - compCnt;
				this.abnormal.companyPercent = this.getDelayRate(compCnt, total);
				this.abnormal.noCompanyPercent = this.getDelayRate(noCompCnt, total);
				this.abnormal.companyCnt = compCnt;
				this.abnormal.noCompanyCnt = noCompCnt;
				var canvas = this.$refs.reason_canvas;
				this.drawPie(compCnt / total, canvas, 80, 10)
			},

			doQueryBaseData() {
				this.getFlighList();
				this.getAcAircraftList();
				this.getInPsrStat();
				this.getOutPsrStat();
				this.queryFiveRate();
				this.getWeather();

				this.queryFlightAmountStatic();
				// 基地过夜飞机架数
				this.getArpOvernightPlane()
				// 计划运输旅客总量
				this.getPsrSummInfo()
				this.queryBackupArp();
				this.queryOriFlightCounts()

				// this.queryBackupArp();

			},
			drawPie(rate, canvas, radius, lineWidth, bgColor = '#02b0f9', arcColor = '#a3d800', vertice = false) {
				var context = canvas.getContext('2d');
				context.clearRect(0, 0, canvas.width, canvas.height);
				var x = canvas.width / 2;
				var y = canvas.height / 2;

				// draw blue circle
				context.beginPath();
				context.arc(x, y, radius, 0, 2 * Math.PI, false);
				context.lineWidth = lineWidth;
				context.strokeStyle = bgColor;
				context.stroke();

				// draw green arc
				var startAngle = Math.PI - (Math.PI * 2 * rate / 2);
				var endAngle = Math.PI + (Math.PI * 2 * rate / 2);

				if (vertice) {
					startAngle = Math.PI * 1.5;
					endAngle = startAngle + (Math.PI * 2 * rate);
				}

				context.beginPath();
				context.arc(x, y, radius, startAngle, endAngle, false);
				context.lineWidth = lineWidth;
				context.strokeStyle = arcColor;
				context.stroke();

				// draw lines
				var numslice = 12;
				for (var i = 0; i < numslice; i++) {
					context.beginPath();
					var startAngle = i * (Math.PI * 2 / numslice);
					var endAngle = startAngle + Math.PI * 0.01;
					context.arc(x, y, radius, startAngle, endAngle, false);
					context.lineWidth = lineWidth;
					context.strokeStyle = '#0A176E';
					context.stroke();
				}

			},

			drwaTrvRatePie() {

			},
			//备降机场
			queryBackupArp() {
				$.ajax({
					type: 'get',
					url: `/bi/spring/aoc/bj//queryBackupArp?compCode=${this.company}&arp=${this.baseCode}`,
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					success: function (response) {
						console.log(response.data);
						if (response.data.length > 0) {

						}
					},
					error: function () { }
				});
			},


			//slin end

			onChangeCompany(v) {
			},
			onChangeBase(base) {
				let me = this;
				me.baseCode = base.baseCode;
				me.baseName = base.baseName;
				me.doQueryBaseData();
			},


			queryBase() {
				let me = this;
				$.ajax({
					type: 'get',
					url: `/bi/spring/aoc/bj//queryBase?compCode=${me.company}`,
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					success: function (res) {
						var data = res.data;
						if (data && data.length > 0) {
							me.baseList = data;
							me.onChangeBase(data[0]);
						}


					},
					error: function () { }
				});
			},
			getAcStatusStat() {
				var me = this;
				var company = this.company;
				$.ajax({
					type: 'get',
					url: `/bi/spring/aircraft/getAcStatusStat?company=${me.company}`,
					contentType: 'application/json',
					dataType: 'json',
					async: true,
					success: function (res) {
						var data = res.data;
						me.totalAcNum = data.total;
						me.stopAcNum = data.maintCnt;
						me.airNum = data.airCnt;
						me.groundNum = data.groundCnt;
						var detail = data.detail;
						let list = [];

						detail.forEach(i => {
							let item = {
								acType: i.actype,
								airNum: i.airCnt,
								groundNum: i.groundCnt,
								stopNum: i.maintCnt,
								total: i.cnt
							}
							list.push(item);
						});
						me.yunliList = list;
					},
					error: function () { }
				});
			},
			getPercent(num, div) {
				if (div > 0) {
					return Number(num * 100 / div).toFixed(0) + '%';
				}
				return '0%'
			},
			getDelayRate(v, vdiv) {
				if (v === 0) {
					return 0;
				}
				if (vdiv === 0) {
					return 50;
				}
				return Math.round(v / vdiv * 1000) / 10
			},
			queryFlightInfo() {

			},
			/**
			 * 今日延误原因分析
			 */
			getDelayReason() {
				var me = this;
				// $.ajax({
				// 	type: 'get',
				// 	url: "/bi/spring/aoc/bj//getDelayReason?compCode=HU",
				// 	contentType: 'application/json',
				// 	dataType: 'json',
				// 	async: true,
				// 	success: function (res) {
				// 		me.delayData = res.data;
				// 		me.drawKpiPieChart();
				// 	},
				// 	error: function () { }
				// });
			},
			drawKpiPieChart() {
				let me = this;
				let echart = echarts.init(this.$refs.pieKpiEchart);
				let option = {
					tooltip: {
						trigger: 'item',
						formatter: "{a} <br/>{b}:({c}%)"
					},
					color: ["#00a9ff", "#a1dd00"],//环形图两部分的颜色
					graphic: {
						elements: [{
							type: 'image',
							left: "center",
							top: "center",
							style: {
								symbol: 'image://../img/a0-14/VS.svg',
								width: 36,
								height: 36
							}
						}]
					},
					series: [
						{
							name: '延误原因',
							type: 'pie',//设为饼图
							radius: ['85%', '100%'],//设置内半径和外半径，形成环状
							avoidLabelOverlap: false,
							label: {
								normal: {
									show: false,
									position: 'center'
								}
							},
							data: [
								{ value: me.getDelayRate(me.delayData.noneCauseNum, me.delayData.total), name: '非公司原因' },
								{ value: me.getDelayRate(me.delayData.compCauseNum, me.delayData.total), name: '航空公司原因' }]
						}
					]
				};

				echart.setOption(option);
				window.addEventListener("resize", () => {
					echart.resize();
				});
				echart.hideLoading();
			},


		}
	});
	return;
}