document.oncontextmenu = function () {
  return false;
}
var userinfo;
var companylist = [];
var companyCode2Name = {};
var companyCode2Id = {};
var companyCode2Sort = {};
var selected_company_code = 'HNAHK';
var companyColors = ['#1fb3ff', '#7d5aee', '#f06234', '#008e01', '#ffaf3d', '#54c6ac', '#f06eaa', '#5379c0', '#a7c835', '#1fb3aa', '#7d5acc', '#f06287', '#008e91', '#ffaf5c', '#54c6ef', '#f06edd', '#5379e6', '#a7c876'];
var companyCode2Color = {};

var usersCompanyList = [];

function getUserInfo () {

  $.ajax({
    type: 'post',
    url: "/bi/sso/loginforBI2",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify({}),
    success: function (response) {
      userinfo = response;
      // 有权限的公司
      usersCompanyList = [];
      var comlist = userinfo.COMPANY.companys;
      if (comlist) {
        var len = comlist.length;
        for (var i = 0; i < len; i++) {
          var obj = comlist[i];
          var comid = obj['company'];
          usersCompanyList.push(comid);
        }
      }

      if (usersCompanyList.length == 0) {
        alert('您没有访问权限，请联系管理员。');
        return;
      }

      //有权限的菜单
      usersMenuList = userinfo.MENU.menus;

      if (usersMenuList.length == 0) {
        alert('您没有访问权限，请联系管理员。');
      }
      getCompany();

      $('body').show();
      setPageScale();

    },
    error: function (error) {
      console.log(error, "redirect to sso")
      redirectToSSO();
    }
  });
  // if (typeof jQuery != 'undefined' && $('#login').length) {

  //     $('#login').attr('src', '/bi/sso/loginforBI2?ts=' + new Date().getTime());

  //     function checkIFrame() {

  //         if ($('#login')[0].contentWindow != undefined) {
  //             try {
  //                 var doc = $('#login')[0].contentWindow.document.body;

  //                 let result = $(doc).text();
  //                 if (result.indexOf("rejectLogin") > -1) {
  //                     let info = JSON.parse(result);
  //                     alert(info.errordesc)
  //                     return;
  //                 }

  //                 if (result && result.length > 200) {
  //                     userinfo = JSON.parse($(doc).text());
  //                     console.log('userinfo', userinfo);

  //                     // 有权限的公司
  //                     usersCompanyList = [];
  //                     var comlist = userinfo.COMPANY.companys;
  //                     if (comlist) {
  //                         var len = comlist.length;
  //                         for (var i = 0; i < len; i++) {
  //                             var obj = comlist[i];
  //                             var comid = obj['company'];
  //                             usersCompanyList.push(comid);
  //                         }
  //                     }

  //                     if (usersCompanyList.length == 0) {
  //                         alert('您没有访问权限，请联系管理员。');
  //                         return;
  //                     }

  //                     //有权限的菜单
  //                     usersMenuList = userinfo.MENU.menus;

  //                     if (usersMenuList.length == 0) {
  //                         alert('您没有访问权限，请联系管理员。');
  //                     }
  //                     getCompany();

  //                     $('body').show();
  //                     setPageScale();

  //                 } else {
  //                     setTimeout(checkIFrame, 10);
  //                 }
  //             } catch (error) {
  //                 $('#login').remove();
  //                 redirectToSSO();
  //             } finally {
  //             }
  //         } else {
  //             setTimeout(checkIFrame, 10);
  //         }
  //     }

  //     checkIFrame();

  // }

}

function redirectToSSO () {
  window.location.href = SSO_URL() + "/login?appid=" + APP_ID + "&service=" + SSO_SERVICE();

}

function SSO_URL () {
  if (window.location.href.indexOf("vis.hnair.net") > -1 || window.location.href.indexOf("bim.hnair.net") > -1 || window.location.href.indexOf("cdp-mobile.hnair.net") > -1) {
    return "https://sso.hnair.net";
  } else {
    return "https://ssotest.hnair.net/opcnet-sso";
  }
}

function SSO_SERVICE () {
  if (window.location.href.includes("vis.hnair.net") || window.location.href.includes("bim.hnair.net") || window.location.href.includes("cdp-mobile.hnair.net")) {
    if (window.location.href.includes("largescreen/7x2")) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen7x2";
    } else if (window.location.href.includes("largescreen/west")) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen_west";
    } else if (window.location.href.includes("largescreen/jdair")) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen_jd";
    } else if (window.location.href.includes("largescreen/hnahk")) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen_hnahk";
    } else if (window.location.href.includes("largescreen/general")) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen_general";
    } else if (window.location.href.includes("largescreen/sdc")) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen_sdc";
    } else if (window.location.href.includes("largescreen")) {
      return "https://vis.hnair.net/bi/largescreen/redirect_largescreen";
    } else {
      return "https://vis.hnair.net/bi/largescreen/redirect_admin";
    }
  } else if (window.location.href.includes("localhost")) {
    if (window.location.href.includes("largescreen/7x2")) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen7x2";
    } else if (window.location.href.includes("largescreen/west")) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen_west";
    } else if (window.location.href.includes("largescreen/jdair")) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen_jd";
    } else if (window.location.href.includes("largescreen/hnahk")) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen_hnahk";
    } else if (window.location.href.includes("largescreen/general")) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen_general";
    } else if (window.location.href.includes("largescreen/sdc")) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen_sdc";
    } else if (window.location.href.includes("largescreen")) {
      return "http://localhost:8080/bi/largescreen/redirect_largescreen";
    } else {
      return "http://localhost:8080/bi/largescreen/redirect_admin";
    }
  } else if (window.location.href.indexOf("vis-dev.hnair.net") > -1) {
    if (window.location.href.indexOf("largescreen/7x2") > -1) {
      return "https://vis-dev.hnair.net/bi/largescreen/redirect_largescreen7x2";
    } else if (window.location.href.indexOf("largescreen/west") > -1) {
      return "https://vis-dev.hnair.net/bi/largescreen/redirect_largescreen_west";
    } else if (window.location.href.indexOf("largescreen/jdair") > -1) {
      return "https://vis-dev.hnair.net/bi/largescreen/redirect_largescreen_jd";
    } else if (window.location.href.indexOf("largescreen/hnahk") > -1) {
      return "https://vis-dev.hnair.net/bi/largescreen/redirect_largescreen_hnahk";
    } else if (window.location.href.indexOf("largescreen/general") > -1) {
      return "https://vis-dev.hnair.net/bi/largescreen/redirect_largescreen_general";
    } else if (window.location.href.indexOf("largescreen/sdc") > -1) {
      return "https://vis-dev.hnair.net/bi/largescreen/redirect_largescreen_sdc";
    } else if (window.location.href.indexOf("largescreen") > -1) {
      return "https://vis-dev.hnair.net/bi/largescreen/redirect_largescreen";
    } else {
      return "https://vis-dev.hnair.net/bi/largescreen/redirect_admin";
    }
  } else {
    if (window.location.href.includes("largescreen/7x2")) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen7x2";
    } else if (window.location.href.includes("largescreen/west")) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen_west";
    } else if (window.location.href.includes("largescreen/jdair")) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen_jd";
    } else if (window.location.href.includes("largescreen/hnahk")) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen_hnahk";
    } else if (window.location.href.includes("largescreen/general")) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen_general";
    } else if (window.location.href.includes("largescreen/sdc")) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen_sdc";
    } else if (window.location.href.includes("largescreen")) {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_largescreen";
    } else {
      return "https://cdp-test.hnair.net/bi/largescreen/redirect_admin";
    }
  }
}

function createNavMenu (userinfo) {
  var navMenu = [];
  var usersMenuList = userinfo.MENU.menus;
  if (usersMenuList.length == 0) {
    return navMenu;
  }

  var len = usersMenuList.length;

  for (var i = 0; i < len; i++) {
    var obj = usersMenuList[i];
    if (obj.parent_id == 0 || obj.parent_id == '') {
      var id = obj.id;
      var menu = {
        'name': obj.name,
        'page': obj.url,
        'target': obj.target,
        'sub': []
      };

      //查找子菜单
      var len2 = usersMenuList.length;
      for (var j = 0; j < len2; j++) {
        var obj2 = usersMenuList[j];
        if (obj2.parent_id == id) {
          var submenu = {
            'name': obj2.name,
            'page': obj2.url,
            'target': obj2.target
          };
          menu.sub.push(submenu);
        }
      }
      navMenu.push(menu);
    }
  }
  return navMenu;
}

// 页面访问日志
function userlog () {
  if (userinfo && userinfo.id) {
    var pagename;
    var navMenu = createNavMenu(userinfo);
    var len = navMenu.length;
    for (var i = 0; i < len; i++) {
      var obj = navMenu[i];
      var subs = obj.sub;

      for (var j = 0; j < subs.length; j++) {
        var sobj = subs[j];

        if (sobj.page.length > 0 && window.location.href.indexOf(sobj.page) > -1) {
          pagename = sobj.name;

        } else if (sobj.subpage && sobj.subpage.length > 0) {
          for (var k = 0; k < sobj.subpage.length; k++) {
            var pp = sobj.subpage[k];
            if (window.location.href.indexOf(pp) > -1) {
              pagename = sobj.name;
              break;
            }
          }
        }
      }
    }

    if (pagename) {

      var date = new Date();
      var mm = date.getMonth() + 1;
      var dd = date.getDate();
      var h = date.getHours();
      var m = date.getMinutes();
      var s = date.getSeconds();

      if (mm < 10) {
        mm = '0' + mm;
      }
      if (dd < 10) {
        dd = '0' + dd;
      }
      if (h < 10) {
        h = '0' + h;
      }
      if (m < 10) {
        m = '0' + m;
      }
      if (s < 10) {
        s = '0' + s;
      }

      var timeNow = date.getFullYear() + '年' + mm + '月' + dd + '日 ' + h + ':' + m + ':' + s;

      var operation = '访问页面:' + pagename + ', 时间' + timeNow;
      var category = '页面';

      var param = {
        'userid': userinfo.id,
        'oper': operation,
        'category': category,
      }

      $.ajax({
        type: 'post',
        url: "/bi/web/userlog",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {

        },
        error: function () {
        }
      });

    }


  } else {
    setTimeout(userlog, 100);
  }
}


$(window).resize(function () {
  //location.reload();
  setPageScale();
});

var pageZoomScale = 1;
var pageZoomScaleY = 1;

function setPageScale () {
  var screenWidth = 4080;
  var screenHeight = 1200;

  var wapperWidth = 3672;
  var wapperHeight = 1080;

  var osw = document.body.offsetWidth;
  var osh = window.innerHeight;


  if (getQueryString('scale') == 1) {
    $('body').css('overflow-x', 'auto');
    $('body').css('overflow-y', 'auto');
    pageZoomScale = 1;
    pageZoomScaleY = 1;
  } else if (getQueryString('scale') == 'auto') {
    $('body').css('overflow-x', 'hidden');
    $('body').css('overflow-y', 'auto');
    pageZoomScale = osw / wapperWidth;
    pageZoomScaleY = pageZoomScale;
  } else {
    $('body').css('overflow-x', 'hidden');
    $('body').css('overflow-y', 'hidden');
    pageZoomScale = osw / wapperWidth;
    pageZoomScaleY = osh / wapperHeight;

  }


  // for firefox
  $('.page-bgimg').css('transform', 'scale(' + pageZoomScale + ',' + pageZoomScaleY + ')');
  $('.page-bgimg').css('transform-origin', 'left top');

  $('.page-wrapper').css('transform', 'scale(' + pageZoomScale + ',' + pageZoomScaleY + ')');
  $('.page-wrapper').css('transform-origin', 'left top');


  var scaleInvert = 1 / pageZoomScale;
  var scaleInvertY = 1 / pageZoomScaleY;

  // firefox
  //$('.chartblock').css('transform', 'scale('+scaleInvert+','+scaleInvertY+')');
  //$('.chartblock').css('transform-origin', 'left top');

  //$('.chartblock').each(function(index, el) {
  //  $(this).css('width', $(this).attr('prop-width')*pageZoomScale+'px');
  //  $(this).css('height', $(this).attr('prop-height')*pageZoomScaleY+'px');
  //});

  var left = 0;
  var top = 0;
  var width = osw;
  var height = width * (wapperHeight / wapperWidth);
  $('.mainframe').attr('style', 'left:' + left + 'px; ' + 'top:' + top + 'px; ' + 'width:' + width + 'px; ' + 'height:' + height + 'px');

  // update all echarts
  try {
    if (typeof (eval('updateAlleCharts')) == "function") {
      updateAlleCharts();
    }
  } catch (e) {
  }


}


function getQueryString (name) {
  var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
  var r = window.location.search.substr(1).match(reg);
  if (r != null) return unescape(r[2]);
  return '';
}


function showLoading () {
  var html = '<div id="loading_msk"><div class="spinner spinner_animate"></div></div>';
  $('.page-wrapper').append(html);
}

function hideLoading () {
  $('#loading_msk').remove();
}


setPageScale();


function checkBrowser () {
  var support = true;
  var isIE = false;
  var isIE11 = false;
  var isChrome = false;
  var isFirefox = false;
  var userAgent = navigator.userAgent;
  if (userAgent.indexOf("Opera") > -1 || userAgent.indexOf("OPR") > -1) {
    // 'Opera';
  } else if (userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1) {
    // 'IE';
    isIE = true;
  } else if (userAgent.indexOf("Edge") > -1) {
    // 'Edge';
  } else if (userAgent.indexOf("Firefox") > -1) {
    // 'Firefox';
    isFirefox = true;
  } else if (userAgent.indexOf("Safari") > -1 && userAgent.indexOf("Chrome") == -1) {
    // 'Safari';
  } else if (userAgent.indexOf("Chrome") > -1 && userAgent.indexOf("Safari") > -1) {
    // 'Chrome';
    isChrome = true;
  } else if (!!window.ActiveXObject || "ActiveXObject" in window) {
    // 'IE>=11';
  } else {
    // 'Unkonwn';
    support = false;
  }

  if (isIE) {
    var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
    reIE.test(userAgent);
    var fIEVersion = parseFloat(RegExp["$1"]);
    if (fIEVersion == 7) {
      support = false;
    } else if (fIEVersion == 8) {
      support = false;
    } else if (fIEVersion == 9) {
      support = false;
    } else if (fIEVersion == 10) {
      support = false;
    } else if (fIEVersion == 11) {
      // "IE11";
      isIE11 = true;
    } else {
      support = false;
    }
  }

  // 检测遨游浏览器
  try {
    if (window.external.max_version && window.external.max_version != undefined) {
      support = false;
    }
  } catch (ex) {

  }


  if (support) {
    getUserInfo();
  } else {
    //window.location.href = "/largescreen/browser.html";
    alert('您的浏览器版本可能存在兼容问题，建议使用 Google Chrome 或 IE11 浏览器。');
    if (typeof jQuery != 'undefined') {
      $('body').hide();
    }
  }

}

checkBrowser();
userlog();

function getCompany () {
  $.ajax({
    type: 'POST',
    url: '/bi/query/company',
    async: false,
    dataType: 'json',
    data: '',
    success: function (response) {
      checkLogin(response);
      let html = '';
      companylist = response.comp;
      companylist.sort(function (a, b) {
        return a.sort - b.sort
      });

      const len = companylist.length;
      for (let i = 0; i < len; i++) {
        const dat = companylist[i];
        companyCode2Name[dat.code] = dat.name;
        companyCode2Id[dat.code] = dat.id;
        companyCode2Sort[dat.code] = dat.sort;
        if (i != 0) {
          companyCode2Color[dat.code] = companyColors[i - 1];
        } else {
          companyCode2Color[dat.code] = '#e60000';
        }
        if (usersCompanyList.indexOf(dat.id) > -1) {
          html += '<div class="itm" code="' + dat.code + '" comp_id="' + dat.id + '">' + dat.code + '</div>';
        }
        if (dat.code == 'HNAHK') {
          $('#companycombo').attr('code', dat.code);
          $('#companycombo').attr('comp_id', dat.id);
        }
      }

      $('#companylist').html(html);

    },
    error: function (e) {
      console.log('ajax error');
      console.log(e);
    }
  });

};

function switchCompany (code) {
  selected_company_code = code;
  let findCode = false;
  const len = companylist.length;
  for (var i = 0; i < len; i++) {
    var dat = companylist[i];
    if (dat.code == code) {
      $('#companycombo .box').text(dat.code);
      findCode = true;
      break;
    }
  }
  if (findCode) {
    window.location.hash = '#' + code;
    $('#companycombo').attr('code', code);
    onCompanyChanged(code, companyCode2Name[code]);
  } else {
    $('#companycombo .box').text(code);
    window.location.hash = '#' + code;
    $('#companycombo').attr('code', code);
  }
}

(function () {
  // 选择公司
  $('#companycombo .box').on('click', function () {
    if ($('#companylist').is(':visible')) {
      $('#companylist').hide();
    } else {
      $('#companylist').slideDown('200', function () {
      });
    }
  });

  $(document).on("click", "#companylist .itm", function () {
    $('#companylist').hide();
    const code = $(this).attr('code');
    switchCompany(code);

  });

  $('#companycombo').on('mouseleave', function () {
    $('#companylist').hide();
  });
  document.write("<script src='/largescreen/js/lib/slm.js'></script>")

})();

