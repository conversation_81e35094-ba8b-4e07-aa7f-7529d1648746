<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">

    <title>航空运控大屏</title>

    <meta http-equiv="refresh" content="3600">
	<link href="../css/nprogress.css" rel="stylesheet">
	<script src="../js/nprogress.js"></script>
    <script src="../js/tingyun.js?ver=20211218"></script>

    <link href="../css/bootstrap.css" rel="stylesheet">
    <link href="../css/bootstrap-theme.css" rel="stylesheet">
    <link href="css/common.css?ver=20211218" rel="stylesheet">
    <link href="css/flight.css?ver=20211218" rel="stylesheet">

    <script src="../js/jquery-1.11.1.js"></script>
	<script src="../js/bootstrap.min.js"></script>
	<script src="../js/echarts.min.js?ver=20211218"></script>
	<script src="../js/echarts-gl.min.js?ver=20211218"></script>
	<script src="../js/map/world.js"></script>
	<script src="../js/json.js"></script>
	<script src="../js/util.js"></script>
	
</head>
<body>

<iframe id="login" scrolling="no" width="0" height="0" style="display:none;" ></iframe>
<!--script src="js/config.js?ver=20211218"></script-->
<script type="text/javascript">
	$('body').hide();
</script>

<div id="map" class="page-wrapper"></div>

<div class="page-bgimg" style=" -moz-transform-origin:left top; pointer-events: none;">

</div>


<div class="page-wrapper" style=" -moz-transform-origin:left top;">


<div class="col_l" style="">
	<div class="tt" style="">航班信息</div>
	<div class="fltno" style="" ></div>
	<div class="acimg" style="background-image: url(img/plane_pic.png)" ></div>
	<div class="blk1" >
		<div class="row1 con_flex_row" >
			<div class="c1 flex_1" >
				<div class="t1 city1"></div>
				<div class="t2 arp1" ></div>
			</div>
			<div class="c2 flex_1">
				<div class="t1 citym" ></div>
				<div class="t2 arpm" ></div>
			</div>
			<div class="c3 flex_1" >
				<div class="t1 city2" ></div>
				<div class="t2 arp2" ></div>
			</div>
		</div>


		<div class="row2 con_flex_row" >
			<div class="c1 flex_1" >
				<div class="t1 stdChn" >--</div>
				<div class="t2" >计划离港</div>
			</div>
			<div class="c2 flex_1 jingting" >
				<div class="t1 staChnm" >--</div>
				<div class="t2" >计划到港</div>
			</div>
			<div class="c3 flex_1" >
				<div class="t1 staChn" >--</div>
				<div class="t2" >计划到港</div>
			</div>
		</div>

		<div class="row2 con_flex_row" >
			<div class="c1 flex_1" >
				<div class="t1 etdChn" >--</div>
				<div class="t2" >预计起飞</div>
			</div>
			<div class="c2 flex_1 jingting" >
				<div class="t1 stdChnm" >--</div>
				<div class="t2" >计划离港</div>
			</div>
			<div class="c3 flex_1" >
				<div class="t1 etaChn staChnmm" >--</div>
				<div class="t2" >计划到港</div>
			</div>
		</div>

		<div class="row2 con_flex_row" >
			<div class="c1 flex_1" >
				<div class="t1 atdChn" >--</div>
				<div class="t2" >实际离港</div>
			</div>
			<div class="c2 flex_1 jingting" >
				<div class="t1 ataChnm" >--</div>
				<div class="t2" >实际到港</div>
			</div>
			<div class="c3 flex_1" >
				<div class="t1 ataChn" >--</div>
				<div class="t2" >实际到港</div>
			</div>
		</div>


	</div>


	<div class="blk2" style="top:480px;" >
		<div class="t2" >承运方</div>
		<div class="t1 compname" ></div>
	</div>

	<div class="blk2" style="top:550px;">
		<div class="t2" >前序航班</div>
		<div class="t1 preflight" ></div>
	</div>

</div><!-- /.col_l -->



<div class="col_l2" style="">
	<div class="blk1" >
		<div class="ttt" >航班日期</div>
		<div class="fs16 flt_time" ></div>
	</div>

	<div class="blk2" >
		<div class="ttt" >航线里程</div>
		<div class="fs16" style='line-height: 18px;'>
			<span class='fs20 ariline_dis'></span><span class='fs12 blue'>公里</span>
			<span class='fs12 blue'>已执行</span> <span class='fs14 green ariline_dis2'></span> <span class='fs12 blue'>公里</span>
			
		</div>
	</div>

	<div class="blk3" >
		<div class="ttt" >飞行时长</div>
		<div class="fs16" style='line-height: 18px;'>
			<span class='fs16 fly_time'>--</span>
			<span class='fs12 blue'>已执行</span> <span class='fs14 green ariline_min'>--</span> <span class='fs12 blue'>分钟</span>
		</div>
	</div>

	<div class="oiltt">
		油量
	</div>

	<div class="blk4" >
		<div class="ttt" >实时油量</div>
		<div class="fs14 oil_rt" >--</div>

		<div class="ttt" >起飞油量</div>
		<div class="fs14 oil_1" >--</div>

		<div class="ttt" >最大载油量</div>
		<div class="fs14 oil_2" >--</div>

		<div class="ttt" >航线计划耗油</div>
		<div class="fs14 oil_3" >--</div>
	</div>

</div><!-- /.col_l2 -->


<div class='col_top con_flex_row'>

	<div class="co1 flex_none" style='width:28%' >
		<div class='fltno' style="" ></div>
	</div>

	<div class="mid flex_1 con_flex_row" style="width: 49%;">
		<div class="c1 flex_1" >
			<div class="t1 city1" ></div>
			<div class="t2" >出发机场</div>
		</div>
		<div class="c4 flex_1 leg1" >
			<div class="fs12"><span class="leg1Status"></span><br/>——————></div>
		</div>
		<div class="c2 flex_1 jingting" >
			<div class="t1 citym"></div>
			<div class="blue fs12">经停机场</div>
		</div>
		<div class="c4 flex_1 leg2">
			<div class="fs12"><span class="leg2Status"></span><br/>——————></div>
		</div>
		<div class="c3 flex_1" >
			<div class="t1 city2" ></div>
			<div class="t2" >目的机场</div>
		</div>
	</div>

	<div class="co3 flex_none" style='width:22%' >
		<div class='fltsts sts'></div>
	</div>

</div><!-- /.top -->


<div class='col_bot con_flex_row'>

	<div class="co1 flex_none" style='width:24%' >
		<div class="chart1 chart">
			<canvas id="cvs_chart1" width="140" height="140" style="background:rgba(255,0,0,0.0);"></canvas>
			<img id="cvs_chart1_pointer" class="pointer" src="img/pointer2.svg" />
			<img id="cvs_chart1b_pointer" class="pointer" src="img/pointer1.svg" />
		</div>

		<div class="lb_rate1 blue">本航线本月正常率<br><span id="cvs_chart1_lb1" class="fs22"></span></div>
		<div class="lb_rate2 blue">本司本月正常率<br><span id="cvs_chart1_lb2" class="fs16 white"></span></div>
	</div>

	<div class="mid flex_1" >
		<div class='tt'>座舱布局</div>
		<div class='cabin'>
			<div class='lb blue'></div>
		</div>
	</div>

	<div class="co3 flex_none" style='width:24%' >
		<div class="chart">
			<canvas id="cvs_chart2" width="116" height="116" style="background:rgba(255,0,0,0.0);"></canvas>

			<div class="fs9" style="position: absolute; width:22px; height:22px; left:19px; top:80px; opacity:0.9; ">0</div>
			<div class="fs9" style="position: absolute; width:22px; height:22px; left:10px; top:33px; opacity:0.9; ">25</div>
			<div class="fs9" style="position: absolute; width:22px; height:22px; left:53px; top:6px; opacity:0.9; ">50</div>
			<div class="fs9" style="position: absolute; width:22px; height:22px; left:95px; top:33px; opacity:0.9; ">75</div>
			<div class="fs9" style="position: absolute; width:32px; height:22px; left:87px; top:80px; opacity:0.9; ">100</div>

		</div>

		<div class="trv_rate lb_rate1 blue">客座率<br><span id="cvs_chart2_lb1" class="fs22 "></span><span class="up_arr" style='display: none;'></span><span class="down_arr" style='display: none;'></span></div>
		<div class="trv_rate_sub lb2 fs12 blue"></div>
	</div>

</div><!-- /.col_bot -->


<div class='col_wea'>

	<div class="fs16 blue lb">天气预报</div>

	<div class="blk blk1 weather_city1 selected" >
		<div class='tt'></div>
		<div class='cont con_flex_row'>
			
			<div class="c1 flex_none">
				<span class="weather_ico"><span></span></span>
			</div>

			<div class="c2 flex_1">
				<table >
					<tbody>
						<tr>
							<td class="temperature fs28 blue" style='line-height: 28px;'></td>
						</tr>
						<tr>
							<td class="condition" style='padding-bottom: 10px;'>多云</td>
						</tr>
						<tr>
							<td class="blue">能见度</td>
						</tr>
						<tr>
							<td class="visibility" style='padding-bottom: 10px;'></td>
						</tr>
						<tr>
							<td class="blue">风速</td>
						</tr>
						<tr>
							<td class="windFs" style='padding-bottom: 10px;'></td>
						</tr>
						
					</tbody>
				</table>
			</div>
		</div>
	</div><!-- /.blk -->


	<div class="blk blk2 weather_citym hide" >
		<div class='tt'></div>
		<div class='cont con_flex_row'>
			
			<div class="c1 flex_none">
				<span class="weather_ico"><span></span></span>
			</div>

			<div class="c2 flex_1">
				<table >
					<tbody>
						<tr>
							<td class="temperature fs28 blue" style='line-height: 28px;'></td>
						</tr>
						<tr>
							<td class="condition" style='padding-bottom: 10px;'></td>
						</tr>
						<tr>
							<td class="blue">能见度</td>
						</tr>
						<tr>
							<td class="visibility" style='padding-bottom: 10px;'></td>
						</tr>
						<tr>
							<td class="blue">风速</td>
						</tr>
						<tr>
							<td class="windFs" style='padding-bottom: 10px;'></td>
						</tr>
						
					</tbody>
				</table>
			</div>
		</div>
	</div><!-- /.blk -->

	<div class="blk blk3 weather_city2" >
		<div class='tt'></div>
		<div class='cont con_flex_row'>
			
			<div class="c1 flex_none">
				<span class="weather_ico"><span></span></span>
			</div>

			<div class="c2 flex_1">
				<table >
					<tbody>
						<tr>
							<td class="temperature fs28 blue" style='line-height: 28px;'></td>
						</tr>
						<tr>
							<td class="condition" style='padding-bottom: 10px;'></td>
						</tr>
						<tr>
							<td class="blue">能见度</td>
						</tr>
						<tr>
							<td class="visibility" style='padding-bottom: 10px;'></td>
						</tr>
						<tr>
							<td class="blue">风速</td>
						</tr>
						<tr>
							<td class="windFs" style='padding-bottom: 10px;'></td>
						</tr>
						
					</tbody>
				</table>
			</div>
		</div>
	</div><!-- /.blk -->

	

</div><!-- /.col_wea -->



<div class='col_r'>

	<div class="blk blk1" >
		<div class='tt'>机组信息</div>
		<table >
			<tbody>
				<tr>
					<td class="blue" style='width:90px;'>责任机长</td>
					<td class="captain" ></td>
				</tr>
				<tr>
					<td class="blue">其他驾驶</td>
					<td class="firstVice1" ></td>
				</tr>
				<tr>
					<td class="blue">乘务员</td>
					<td class="crwStewardInf" style='line-height:16px;'></td>
				</tr>
				<tr>
					<td class="blue">安全员</td>
					<td class="safer1" ></td>
				</tr>
				
			</tbody>
		</table>
	</div>

	<div class="blk blk2" >
		<div class='tt'>舱单</div>
		<table >
			<tbody>
				<tr>
					<td class="blue">成人</td>
					<td class="adultNum" style='width:40px;'>--</td>

					<td class="blue">中转旅客</td>
					<td class="zz_psr" >--</td>

				</tr>
				<tr>
					<td class="blue">老人</td>
					<td class="eldNum" >--</td>

					<td class="blue">托运行李重量</td>
					<td class="otherBagWeight" >--</td>
				</tr>
				<tr>
					<td class="blue">儿童</td>
					<td class="chdNum" >--</td>

					<td class="blue">货物重量</td>
					<td class="cargoWeight" >--</td>
				</tr>
				<tr>
					<td class="blue">VIP/CIP</td>
					<td class="vipNumCipNum" >--</td>

					<td class="blue"></td>
					<td class="" ></td>
				</tr>
				<tr>
					<td class="blue">头等舱旅客</td>
					<td class="cNum" >--</td>

					<td class="blue"></td>
					<td class="" ></td>
				</tr>
				<tr>
					<td class="blue">经济舱旅客</td>
					<td class="yNum" >--</td>

					<td class="blue"></td>
					<td class="" ></td>
				</tr>
				
			</tbody>
		</table>
	</div>


	<div class="blk blk3" >
		<div class='tt'>飞机信息</div>
		<table >
			<tbody>
				<tr>
					<td class="blue" style='width: 95px;'>机号</td>
					<td class="acno" >--</td>
				</tr>
				<tr>
					<td class="blue">机型</td>
					<td class="actype" >--</td>
				</tr>
				<tr>
					<td class="blue">飞机基重</td>
					<td class="dewKg" >--</td>
				</tr>
				<tr>
					<td class="blue">实际起飞重量</td>
					<td class="takeOffWeight" >--</td>
				</tr>
				<tr>
					<td class="blue">最大起飞重量</td>
					<td class="mtwKg" >--</td>
				</tr>
				
			</tbody>
		</table>

		<table style='border-top:1px solid rgba(255,255,255,0.15)'>
			<tbody>
				<tr>
					<td class="blue" style='width: 95px; padding-top: 10px;'>维修类型</td>
					<td class="seq" style='padding-top: 10px;'>--</td>
				</tr>
				<tr>
					<td class="blue">维修机场</td>
					<td class="mntstn" >--</td>
				</tr>
				<tr>
					<td class="blue">维修结束时间</td>
					<td class="mnttEnd" >--</td>
				</tr>
				<tr>
					<td class="blue">维修任务描述</td>
					<td >
						<div class="mntComment" style='overflow:hidden; width:170px; height:37px; line-height:18px; text-overflow:ellipsis;'>--</div>
					</td>
				</tr>
			</tbody>
		</table>

		<span class='btn btn_prev disabled'></span>
		<span class='btn btn_next disabled'></span>

	</div>

</div><!-- /.col_r -->



</div><!-- /.page-wrapper -->




<script src="common.js?ver=20211218"></script>
<script src="flight.js?ver=20211218"></script>


</body>
</html>