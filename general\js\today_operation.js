const myChart = echarts.init(document.getElementById("map3d"));
const myChart2d = echarts.init(document.getElementById("map2d"));
let current_company_code; //当前公司代码
const parent_company = "HNAHK";
var flt_tab_id = "tab_vip";
var legendColorList1 = [
  "#e95551",
  "#f7a449",
  "#fff353",
  "#21b3ce",
  "#176ebf",
  "#0a3a89",
  "#8619db",
  "#aa44e1",
  "#a5a27d",
  "#925c86",
];
var legendColorList2 = ["#aecffd", "#f98a01", "#d11f1e", "#f3f1d5"];
var legendColorList3 = [
  "#e95551",
  "#f7a449",
  "#fff353",
  "#21b3ce",
  "#176ebf",
  "#0a3a89",
  "#8619db",
  "#aa44e1",
  "#a5a27d",
  "#925c86",
];
var legendColorList4 = ["#aecffd", "#f98a01", "#d11f1e", "#f3f1d5", "#f98a01"];
var legendNameList = ["意外", "人为", "机械", "其他"];
var legendNameList2 = [
  "安全报告事件",
  "一类事件",
  "事故征候",
  "岗位红线",
  "待定",
];
var legendName2Key = {
  安全报告事件: 8,
  一类事件: 2,
  事故征候: 3,
  岗位红线: 5,
  待定: 3,
};
var legendKey2Name = {
  8: "安全报告事件",
  2: "一类事件",
  3: "事故征候",
  5: "岗位红线",
  3: "待定",
};
const BASE_CODE_CNI_LIST = {
  PEK: "2",
  HAK: "1",
  XIY: "3",
  CAN: "21",
  DLC: "26",
  TYN: "4",
  SZX: "29",
  HGH: "62",
  SYX: "59",
  CSX: "63",
  URC: "20",
  CKG: "52",
};
var statusList = ["调查分析中", "落实措施中", "已关闭"];
// 航班保障节点
var fltnodes = [
  "cncPilotArrTime",
  "checkInEnd",
  "cncCabinSupplyEndTime",
  "cncCleanEndTime",
  "cncMCCReleaseTime",
  "planeReady",
  "cncInformBoardTime",
  "cncBoardOverTime",
  "cncClosePaxCabinTime",
  "cncCloseCargoCabinTime",
  "cncPushTime",
  "cncACARSTOFF",
];
var weather_map = {
  晴: "icon-e600_sunny",
  沙: "icon-e617_dust1",
  雹: "icon-e620_hail",
  雾: "icon-e615_fog",
  烟: "icon-e615_fog",
  阴: "icon-e604_gloomy",
  雷: "icon-e606_rain2",
  暴: "icon-e606_rain2",
  风: "icon-e612_wind",
  霾: "icon-e613_haze",
  云: "icon-e602_cloudy",
  雨: "icon-e607_rain3",
  雪: "icon-e610_snow3",
};
// 不正常天气基地
const wxCode2Name = {
  TS: "干雷",
  TSRA: "中雷雨",
  "-TSRA": "弱雷雨",
  "+TSRA": "强雷雨",
  CB: "对流云",
  TCU: "浓积云",
  RA: "中雨",
  "+RA": "大雨",
  "+SHRA": "强阵雨",
  SHRA: "中阵雨",
  DZ: "毛毛雨",
  FZRA: "冻雨",
  GR: "冰雹",
  GS: "霰",
  WS: "风切变",
  FG: "大雾",
  FU: "烟",
  HZ: "霾",
  BR: "轻雾",
  FZFG: "冻雾",
  BCFG: "散雾",
  MIFG: "浅雾",
  SN: "中雪",
  "+SN": "大雪",
  SHSN: "阵雪",
  "+SHSN": "强阵雪",
  BLSN: "高吹雪",
  DRSA: "低吹雪",
  SA: "扬沙",
  SS: "沙暴",
  BLSA: "高吹沙",
  DRSA: "低吹沙",
  "+SS": "强沙暴",
  DU: "浮尘",
};
var BASE_CITY_LIST = {
  PEK: "北京",
  HAK: "海口",

  XIY: "西安",
  CAN: "广州",
  DLC: "大连",
  TYN: "太原",
  SZX: "深圳",

  HGH: "杭州",
  SYX: "三亚",
  CSX: "长沙",
  URC: "乌鲁木齐",
  CKG: "重庆",
};
var comp_cause = [
  "飞机故障",
  "运力调配",
  "工程机务",
  "航班计划",
  "航材保障",
  "航务保障",
  "机组保障",
  "飞行机组保障",
  "乘务组",
  "乘务组保障",
  "安全员保障",
  "空警安全员",
  "地面保障",
  "货运保障",
  "公司原因",
  "其他航空公司原因",
];
var none_cause = [
  "公共安全",
  "机场",
  "军事活动",
  "空管",
  "离港系统",
  "联检",
  "旅客",
  "时刻安排",
  "民航局航班时刻安排",
  "天气原因",
  "油料",
  "流量控制",
];
const query_limit = 20;
var ac_type_list = ["330", "737", "767", "787", "350"];
let all_comp_codelist = new Array(); //所有公司编码
let codelist_no_parent = new Array();
let all_company_data = new Object(); //FAC_COMP_KPI
let delay = new Object(); //FAC_COMP_KPI
let ac_num = new Object();
var acregs = [];
var acTypes = [];
var reg2actyp = {};
var tingchang_ac2num = {};
var acregs2 = [];
var aog_planes = [];
var lost_ac_map = [];
var actypeId2Code = {};
var ac_data_list = {};
var actypeMapList = {};
var all_arp_kpi_value = {};
var arp_has_plane_list = [];
var arp_kpi_value = {};
var normal_rate_colors = {};
var planeLocationList = [];
var all_flight_list = [];
var vip_flt_no = {};
var vip_flt_no_list = [];
var vvip_flt_backup_list = {};
var warning_flt_no_list = [];
var important_flt_no_list = [];
var arp_detail_list = {};
var fltCrwCache = {};
var fltLegCache = {};
//////// 机场气象告警 //////////
var unnormalBase = [];
var arpcodes = [];
var airMap = new Map();
var alertAirport = [];
let org_arp_base = new Object(); //分公司基地关系
let dutyObj = new Object();
var base_code;
var marquee_itv_airPlane;
var available_ac;
var planNum; //旅客订票人数
var ckiNum; //旅客值机人数
let stdEndUtcTime;
let stdStartUtcTime;
let stdStart;
let stdEnd;
let current_date;
let zdgzSwpier;
var yhscode;
let flt_sts = new Object();
var option = {
  backgroundColor: "rgba(0,0,0,0)",
  globe: {
    baseTexture: "asset/earth.jpg",
    globeRadius: 80,
    globeOuterRadius: 20,
    displacementScale: 1,
    shading: "lambert",
    shading: "realistic",
    light: {
      main: {
        intensity: 0.3,
      },
      ambient: {
        intensity: 1.0,
      },
    },
    viewControl: {
      autoRotate: false,
      zoomSensitivity: false,
      targetCoord: [106, 32],
    },
    layers: [],
  },
  series: [],
};
let option2d = {
  color: [],
  geo: {
    map: "world",
    roam: true,
    zoom: 1.88,
    center: [100, 30],
    silent: true,
    label: {
      emphasis: {
        show: false,
      },
    },
    itemStyle: {
      normal: {
        areaColor: "#3d7dd0",
        borderColor: "#14224c",
      },
      emphasis: {
        areaColor: "#3d7dd0",
        borderColor: "#14224c",
      },
    },
    regions: [
      {
        name: "ChinaLine",
        itemStyle: {
          normal: {
            borderWidth: 2,
            areaColor: "#3d7dd0",
            borderColor: "#3d7dd0",
          },
        },
      },
    ],
  },
  series: [],
};

class Today_operation {
  constructor() {
    this._addEvent();
    this._initMap();
    this._initTime();
  }

  loadAll() {
    if ($("#loading_msk").length == 0) {
      showLoading();
    }
    current_company_code = window.location.hash.substr(1);
    $("#logo").attr(
      "src",
      `/largescreen/general/img/logo_${current_company_code}_CA.png`
    );
    if (current_company_code == parent_company) {
      $("#logo").attr("height", "30px");
      $("#logo").css("top", "14px");
    } else {
      $("#logo").attr("height", "50px");
      $("#logo").css("top", "3px");
    }
    companylist.forEach((v, i) => {
      all_comp_codelist.push(v.code);
      if (
        v.code != parent_company &&
        (current_company_code == parent_company ||
          current_company_code == v.code)
      ) {
        codelist_no_parent.push(v.code);
      }
      if (current_company_code == v.code) {
        if (current_company_code == parent_company) {
          yhscode = "ALL";
        } else {
          yhscode = v.yhscode;
        }
      }
    });
    Promise.all([this.loadairportdetail(), this.getRelOrgArpBase()]).then(
      (resolve) => {
        Promise.all([
          this.loadnormal_rate_color(),
          this.load7x2_arp_weather(),
          this.load7x2_flt_sts(),
          this.loadAllCompanyData_d(),
          this.loadplanNum(),
          this.loadckiNum(),
          this.loadCompDelayCase(),
          this.loadPdcAcReg(),
          this.loadactypeall(),
          this.loadAcNum(),
          this.loadArpFlight(),
          this.loadORI_NORMAL_NO(),
          this.loadplaneLocation(),
          this.loadStandardFocFlightInfo(),
          this.loadvip_flt_no_list(),
          this.loadvvip_flt_no_list(),
          this.loadwarning_flt_no_list(),
          this.loadimportant_flt_no_list(),
          this.loadrunTimePropertiesStatic(),
          this.loadrunTimeEventList(),
          this.loadfindEventList(),
          this.loadfindWfMetrepBiaoZhuns(),
          this.loadalertAirport(),
          this.initDuty(),
        ])
          .then((resolve) => {
            this.setLeftData();
            this.setMidData();
            this.setRightData();
            this.initDuty();
            this._hideLoad();
          })
          .catch((reject) => {
            console.log(reject);
            alert("数据获取出错！");
            this._hideLoad();
          });
      }
    );
  }
  initDuty() {
    return new Promise((resolve, reject) => {
      $("#cnvcDutyname").text("");
      $("#cnvcTel").text("");
      $("#cnvcMobile").text("");
      if (base_code) {
        var date = new Date();
        var mm = date.getMonth() + 1;
        var dd = date.getDate();
        if (mm < 10) {
          mm = "0" + mm;
        }
        if (dd < 10) {
          dd = "0" + dd;
        }
        var endDate = date.getFullYear() + "-" + mm + "-" + dd;
        var yesterday_ts = date.getTime() - 86400000;
        date.setTime(yesterday_ts);
        mm = date.getMonth() + 1;
        dd = date.getDate();
        if (mm < 10) {
          mm = "0" + mm;
        }
        if (dd < 10) {
          dd = "0" + dd;
        }
        var startDate = date.getFullYear() + "-" + mm + "-" + dd;
        var param = {
          cniTypeId: 1, //
          cnvcCompanyId: BASE_CODE_CNI_LIST[base_code],
          cnvcStartDateStart: startDate,
          cnvcStartDateEnd: endDate,
        };
        $.ajax({
          type: "post",
          url: "/bi/web/getGetAPDutyInfosByPage",
          contentType: "application/json",
          dataType: "json",
          async: true,
          data: JSON.stringify(param),
          success: function (response) {
            if (response.data && response.data.length > 0) {
              let list = response.data;
              list.forEach((v, i) => {
                if (!dutyObj[v.cnvcClasscontentName]) {
                  dutyObj[v.cnvcClasscontentName] = [
                    v.cnvcDutyname,
                    v.cnvcMobile,
                    v.cnvcTel,
                  ];
                }
              });
              $("#dutyselect").html("");
              Object.entries(dutyObj).forEach((v, i) => {
                if (i == 0) {
                  $("#cnvcDutyname").text(v[1][0]);
                  $("#cnvcTel").text(v[1][2]);
                  $("#cnvcMobile").text(v[1][1]);
                }
                $("#dutyselect").append(
                  `<option value="${v[1]}">${v[0]}</option>`
                );
              });
            }
            resolve(response.errorcode);
          },
          error: function (response) {
            reject(response.errorcode);
          },
        });
      } else {
        resolve("success");
      }
    });
  }
  getRelOrgArpBase() {
    return new Promise((resolve, reject) => {
      $.ajax({
        type: "post",
        url: "/bi/query/getRelOrgArpBase",
        contentType: "application/json",
        dataType: "json",
        async: true,
        success: function (response) {
          console.log("getRelOrgArpBase", response.data);
          if (response.data != undefined) {
            org_arp_base = response.data;
          }
          $(".combobox_list").html("");
          $("#placeselect").html("");
          $("#base_cards .swiper-wrapper").html("");
          let list = org_arp_base[current_company_code];
          if (list) {
            list.forEach((v, i) => {
              this.createSwiper(v.arpCode, v.cityName);
              if (i == 1) {
                //$(".combobox .combobox_label").text(v.cityName);
                base_code = v.arpCode;
                $("#placeselect").append(
                  `<option value="${v.arpCode}" selected>${v.cityName}</option>`
                );
              } else {
                $("#placeselect").append(
                  `<option value="${v.arpCode}">${v.cityName}</option>`
                );
              }
            });
          }
          var swiper = new Swiper("#base_cards", {
            nextButton: ".swiper-button-next",
            prevButton: ".swiper-button-prev",
            effect: "coverflow",
            grabCursor: true,
            centeredSlides: true,
            slidesPerView: "3",
            coverflow: {
              rotate: 50,
              stretch: 0,
              depth: 100,
              modifier: 1,
              slideShadows: true,
            },
            autoplay: {
              stopOnLastSlide: false,
            },
          });

          resolve(response.errorcode);
        }.bind(this),
        error: function (response) {},
      });
    });
  }
  createSwiper(code, name) {
    var html = `<div id="arp_code_${code}" class="card bluecard swiper-slide" style="width: 152px">
		    <div class="head">
		        <div class="city">${name}</div>
		        <div class="weather " style="pointer-events: auto;">
		            <span class="icon-e600_sunny"></span>
		        </div>
		    </div>
		    <div class="cont">
		        <div class="itm itm1">
		            <span class="fs10">始发</span><br>
		            <span class="normal_rate1 num"></span><span class="sb">%</span>
		        </div>
		        <div class="itm itm2">
		            <span class="fs10">进港</span><br>
		            <span class="normal_rate2 num"></span><span class="sb">%</span>
		        </div>
		        <div class="itm itm3">
		            <span class="fs10">出港</span><br>
		            <span class="normal_rate3 num"></span><span class="sb">%</span>
		        </div>
		        <div class="itm itm4">
		            <span class="fs10">整体</span><br>
		            <span class="normal_rate4 num"></span><span class="sb">%</span>
		        </div>
		    </div>
		
		</div>`;
    $("#base_cards .swiper-wrapper").append(html);
  }
  loadalertAirport() {
    return new Promise((resolve, reject) => {
      var param = {
        OPTIMIZE: 1,
      };
      $.ajax({
        type: "post",
        url: "/bi/web/alertAirport",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          var list = response.data;
          if (list) {
            list.sort(function (a, b) {
              return b.KPI_VALUE - a.KPI_VALUE;
            });
            alertAirport = list;
          }
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }
  loadfindWfMetrepBiaoZhuns() {
    return new Promise((resolve, reject) => {
      var param = {
        cccsList: arpcodes.join(","),
        updateDateStart: stdStart,
        updateDateEnd: stdEnd,
      };
      $.ajax({
        type: "post",
        url: "/bi/web/findWfMetrepBiaoZhuns",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          for (var k in response) {
            var obj = response[k];
            if (
              obj.ivisAlarmValue == 2 ||
              obj.irvrAlarmValue == 2 ||
              obj.wxCode == "2" ||
              obj.iyunalarmValue == 2 ||
              obj.ittAlarmValue == 2 ||
              obj.iwindSwitchAlarmValue == 2
            ) {
              //1 表示黄色; 2 表示红色
              unnormalBase.push(obj);
            }
          }
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }
  loadfindEventList() {
    return new Promise((resolve, reject) => {
      var startBjTime = dateFormat("YYYY-mm-dd", new Date());
      var endBjTime = dateFormat("YYYY-mm-dd", new Date());
      var param = {
        companyCode: yhscode,
        startTime: startBjTime,
        endTime: endBjTime,
      };
      $.ajax({
        type: "post",
        url: "/bi/web/findEventList",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          all_company_data["findEventList"] = response.result.eventList;
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }
  loadrunTimePropertiesStatic() {
    return new Promise((resolve, reject) => {
      var startBjTime = dateFormat("YYYY-mm-dd", new Date());
      var endBjTime = dateFormat("YYYY-mm-dd", new Date());
      var param = {
        companyCode: yhscode,
        startTime: startBjTime,
        endTime: endBjTime,
      };
      $.ajax({
        type: "post",
        url: "/bi/web/runTimePropertiesStatic",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          all_company_data["runTimePropertiesStatic"] =
            response.result.staticList;
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }
  loadrunTimeEventList() {
    return new Promise((resolve, reject) => {
      var startBjTime = dateFormat("YYYY-mm-dd", new Date());
      var endBjTime = dateFormat("YYYY-mm-dd", new Date());
      var param = {
        companyCode: yhscode,
        startTime: startBjTime,
        endTime: endBjTime,
      };
      $.ajax({
        type: "post",
        url: "/bi/web/runTimeEventList",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          all_company_data["runTimeEventList"] = response.records;
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }
  loadairportdetail() {
    return new Promise((resolve, reject) => {
      var param = {
        //"AIRPORT_CODE": arpcodes, // 可选，传入机场CODE
      };
      $.ajax({
        type: "post",
        url: "/bi/web/airportdetail",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          let list = response.airport.reverse();
          list.forEach((v, i) => {
            arp_detail_list[v.code] = v;
            if (v.is_weather == "1") {
              arpcodes.push(v.icao_id);
              airMap.set(v.icao_id, v.city_name);
            }
          });
          arpcodes = [...new Set(arpcodes)];
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }
  loadvip_flt_no_list() {
    return new Promise((resolve, reject) => {
      var date = new Date();
      var month = date.getMonth() + 1;
      var day = date.getDate();
      if (month < 10) {
        month = "0" + month;
      }
      if (day < 10) {
        day = "0" + day;
      }
      var today = date.getFullYear() + "-" + month + "-" + day;
      var param = {
        acOwner: current_company_code,
        vip: "true",
        datop: today,
      };
      $.ajax({
        type: "post",
        url: "/bi/web/7x2_vip_flt_list",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          vip_flt_no = response.flightNo;
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }
  loadvvip_flt_no_list() {
    return new Promise((resolve, reject) => {
      var param = {
        mode: "query",
      };
      $.ajax({
        type: "post",
        url: "/bi/web/7x2_vvip",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          for (var i in response.flt) {
            var flt = response.flt[i];
            var fltno = flt.flt_no;
            var backup_flt_no = flt.backup_flt_no;
            // vvip_flt_no_list.push(fltno);
            vvip_flt_backup_list[fltno] = backup_flt_no;
          }
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }
  loadwarning_flt_no_list() {
    return new Promise((resolve, reject) => {
      var param = {
        mode: "query",
      };
      $.ajax({
        type: "post",
        url: "/bi/web/7x2_warning",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          for (var i in response.flt) {
            var flt = response.flt[i];
            var fltno = flt.flt_no;
            warning_flt_no_list.push(fltno);
          }
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }
  loadimportant_flt_no_list() {
    return new Promise((resolve, reject) => {
      var param = {
        mode: "query",
      };
      $.ajax({
        type: "post",
        url: "/bi/web/7x2_important",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          for (var i in response.flt) {
            var flt = response.flt[i];
            var fltno = flt.flt_no;
            important_flt_no_list.push(fltno);
          }
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }
  loadStandardFocFlightInfo() {
    return new Promise((resolve, reject) => {
      var param = {
        stdStart: stdStart,
        stdEnd: stdEnd,
        acOwner:
          current_company_code == parent_company ? "" : current_company_code,
        statusList: "",
      };
      $.ajax({
        type: "post",
        url: "/bi/web/getStandardFocFlightInfo",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          all_flight_list = response.data;
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }
  loadplaneLocation() {
    return new Promise((resolve, reject) => {
      var param = {
        mode: "pos",
      };
      $.ajax({
        type: "post",
        url: "/bi/web/flightMq",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          var plist = {};
          function processData(data1) {
            var lst = {};
            var len = data1.length;
            for (var i = 0; i < len; i++) {
              var dd = data1[i];
              var fi = dd.fi;
              if (lst[fi] == undefined) {
                lst[fi] = {
                  data: [],
                };
                lst[fi]["data"].push(dd);
              } else {
                lst[fi]["data"].push(dd);
              }
            }

            return lst;
          }
          var list = processData(response.data.data1);
          for (var fltno in list) {
            var fltobj = list[fltno];
            var itmx2 = fltobj.data;
            var itm;
            if (itmx2 && itmx2.length > 1) {
              var itm1 = itmx2[0];
              var itm2 = itmx2[1];
              itm1.UTC = itm1.UTC.replace(" ", "");
              itm2.UTC = itm2.UTC.replace(" ", "");
              if (itm1.UTC > itm2.UTC) {
                itm = itm1;
                itm.LON1 = itm2.LON;
                itm.LAT1 = itm2.LAT;
              } else if (itm1.UTC < itm2.UTC) {
                itm = itm2;
                itm.LON1 = itm1.LON;
                itm.LAT1 = itm1.LAT;
              } else {
                itm = itm2;
                itm.LON1 = itm1.LON;
                itm.LAT1 = itm1.LAT;
                console.log(fltno, "两组经纬度UTC相同");
              }
            } else if (itmx2 && itmx2.length > 0) {
              itm = itmx2[0];
            }
            if (itm) {
              var alt = itm.ALT;
              var cas = itm.CAS;
              var vec;
              var fltno = itm.fi;
              if (fltno.indexOf(current_company_code) == 0) {
                var acno = itm.an;
                acno = acno.replace("-", "");
                var lon = formatLonLat(itm.LON);
                var lon1 = formatLonLat(itm.LON1);
                var lat = formatLonLat(itm.LAT);
                var lat1 = formatLonLat(itm.LAT1);
                if (isNaN(itm.LON)) {
                  vec = Number(itm.VEC);
                }
                var oil = isNaN(itm.OIL) ? "" : itm.OIL;
                var pdat = {
                  fltno: fltno,
                  acno: acno,
                  alt: alt,
                  vec: vec,
                  lon: lon,
                  lat: lat,
                  lon1: lon1,
                  lat1: lat1,
                  oil: oil,
                };
                var code = acno + "-" + fltno;
                if (pdat.vec == undefined) {
                  pdat.vec = getGeoAngle(
                    pdat.lat,
                    pdat.lon,
                    pdat.lat1,
                    pdat.lon1
                  );
                }
                planeLocationList.push(pdat);
              }
            }
          }
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }
  load7x2_arp_weather() {
    return new Promise((resolve, reject) => {
      for (var arpcode in BASE_CITY_LIST) {
        var param = {
          airport: arpcode,
        };
        $.ajax({
          type: "post",
          url: "/bi/web/7x2_arp_weather",
          contentType: "application/json",
          dataType: "json",
          async: true,
          data: JSON.stringify(param),
          success: function (response) {
            if (Number(response.errorcode) == 0) {
              var weather_css = "icon-e600_sunny";
              var cardcolor_css = "";
              /*
                            airport
                            airportCode
                            cloudInfo 云况
                            metUtcTime
                            rvr 跑道目视距离
                            temperature
                            visibility 能见度
                            weatherInfo 天气现象
                            weatherInfoTxt 翻译后的天气
                            windFs 风速

                            10个基地的标准（除大连外）
                            红色范围
                            “能见度 小于等于800；跑道视程小于等于700   天气现象（大雾、雷暴等） 云况高度（60-90米） 风速15米/秒”
                            黄色范围
                            “能见度 小于等于1600米；跑道视程小于等于1400米   天气现象（大雾、雷暴等） 云况高度小于等于150米  风速大于等于10米/秒”

                            大连的标准
                            红色范围
                            “能见度 小于等于1200；跑道视程小于等于1200  天气现象（大雾、雷暴、沙尘暴） 云况高度小于等于90米  风速大于等于15米/秒”
                            黄色范围
                            “能见度  小于等于2000米；跑道视程小于等于1800米  天气现象（大雾、雷暴等） 云况高度小于等于150米  风速大于等于10米/秒”

                            注：FG代表大雾
                                  TS或TSRA或+RA 代表雷暴
                                   SS或DS沙尘暴

                            */
              var weatherInfoCodes = ["FG", "TS", "TSRA", "RA", "SS", "DS"];
              var code = response.airport;
              var visibility =
                isNaN(response.visibility) || response.visibility == 0
                  ? 9999
                  : Number(response.visibility); //能见度
              var rvr =
                isNaN(response.rvr) || response.rvr == 0
                  ? 9999
                  : Number(response.rvr); //跑道目视距离
              var cloudInfo =
                isNaN(response.cloudInfo) || response.cloudInfo == 0
                  ? 9999
                  : Number(response.cloudInfo); //云况
              var windFs = isNaN(response.windFs) ? 0 : Number(response.windFs); //风速
              var weatherInfo = response.weatherInfo
                ? response.weatherInfo
                : ""; //天气现象
              var weatherInfoTxt = response.weatherInfoTxt
                ? response.weatherInfoTxt.replace(/<[^>]+>/g, "")
                : "";

              var weather_css = "";
              if (code != "DLC") {
                if (
                  visibility <= 800 ||
                  rvr <= 700 ||
                  windFs >= 15 ||
                  cloudInfo <= 90
                ) {
                  cardcolor_css = "redcard";
                } else if (
                  visibility <= 1600 ||
                  rvr <= 1400 ||
                  windFs >= 10 ||
                  cloudInfo <= 150
                ) {
                  cardcolor_css = "yellowcard";
                }
              } else {
                // DLC 大连的标准不一样
                if (
                  visibility <= 1200 ||
                  rvr <= 1200 ||
                  windFs >= 15 ||
                  cloudInfo <= 90
                ) {
                  cardcolor_css = "redcard";
                } else if (
                  visibility <= 2000 ||
                  rvr <= 1800 ||
                  windFs >= 10 ||
                  cloudInfo <= 150
                ) {
                  cardcolor_css = "yellowcard";
                }
              }
              if (weather_css == "") {
                for (var i = weatherInfoCodes.length - 1; i >= 0; i--) {
                  var c = weatherInfoCodes[i];
                  if (weatherInfo.indexOf(c) > -1) {
                    cardcolor_css = "yellowcard";
                  }
                }
              }

              for (var wtxt in weather_map) {
                if (weatherInfoTxt.indexOf(wtxt) > -1) {
                  weather_css = weather_map[wtxt];
                }
              }

              // 设置天气状况icon
              $("#arp_code_" + code + " .weather span").attr(
                "class",
                weather_css
              );

              $("#arp_code_" + code).removeClass("redcard");
              $("#arp_code_" + code).removeClass("yellowcard");

              // 设置卡片颜色
              $("#arp_code_" + code).addClass(cardcolor_css);
            } else {
              console.log("7x2_arp_weather Error");
            }
          },
          error: function (response) {
            reject(response.errorcode);
          },
        });
      }
      resolve("success");
    });
  }
  loadnormal_rate_color() {
    return new Promise((resolve, reject) => {
      var param = {
        mode: "query",
      };
      $.ajax({
        type: "post",
        url: "/bi/web/7x2_normal_rate_color",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          normal_rate_colors = response.ratecolor[0];
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }
  getArpSts(arp) {
    var loaded = 0;
    var sch_total = 0;
    var sch_normal = 0;

    // 获取机场的进港航班／正常率
    var param = {
      stdStartUtcTime: stdStartUtcTime,
      stdEndUtcTime: stdEndUtcTime,
      companyCodes: current_company_code,
      AcTypeList: "",
      depstns: "",
      arrstns: arp, //进港
    };

    $.ajax({
      type: "post",
      url: "/bi/redis/7x2_flt_sts",
      contentType: "application/json",
      dataType: "json",
      async: true,
      data: JSON.stringify(param),
      success: function (response) {
        sch_total += Number(response.pfdappPercentD); //计划航班总数
        sch_normal += Number(response.pfdappPercentM); //计划航班中正常航班总数
        var pfPercent = Number(response.pfdappPercent); //计划航班中的正常航班比率

        var r = Math.round(pfPercent);
        $("#arp_code_" + arp + " .normal_rate2").text(r);
        this._setNormalRateTextColor(
          $("#arp_code_" + arp + " .normal_rate2"),
          r
        );

        loaded++;
        setOverAll();
      }.bind(this),
      error: function () {
        loaded++;
        setOverAll();
      },
    });

    // 获取机场的出港航班／正常率
    var param = {
      stdStartUtcTime: stdStartUtcTime,
      stdEndUtcTime: stdEndUtcTime,
      companyCodes: current_company_code,
      AcTypeList: "",
      depstns: arp, //出港
      arrstns: "",
    };

    $.ajax({
      type: "post",
      url: "/bi/redis/7x2_flt_sts",
      contentType: "application/json",
      dataType: "json",
      async: true,
      data: JSON.stringify(param),
      success: function (response) {
        sch_total += Number(response.pfdappPercentD); //计划航班总数
        sch_normal += Number(response.pfdappPercentM); //计划航班中正常航班总数
        var pfPercent = Number(response.pfdappPercentM); //计划航班中的正常航班比率

        var r = Math.round(pfPercent);
        $("#arp_code_" + arp + " .normal_rate3").text(r);
        this._setNormalRateTextColor(
          $("#arp_code_" + arp + " .normal_rate3"),
          r
        );

        loaded++;
        setOverAll();
      }.bind(this),
      error: function () {
        loaded++;
        setOverAll();
      },
    });

    let setOverAll = () => {
      if (loaded == 2) {
        var r = Math.round((sch_normal / sch_total) * 100);
        $("#arp_code_" + arp + " .normal_rate4").text(r);
        this._setNormalRateTextColor(
          $("#arp_code_" + arp + " .normal_rate4"),
          r
        );

        $("#pin_" + arp).removeClass("pin_red");
        $("#pin_" + arp).removeClass("pin_yellow");
        $("#pin_" + arp).removeClass("pin_green");

        // 根据正常率设置pin颜色
        if (r <= Number(normal_rate_colors["red"])) {
          $("#pin_" + arp).addClass("pin_red");
        } else if (r <= Number(normal_rate_colors["yellow"])) {
          $("#pin_" + arp).addClass("pin_yellow");
        } else {
          $("#pin_" + arp).addClass("pin_green");
        }
      }
    };
  }
  loadORI_NORMAL_NO() {
    return new Promise((resolve, reject) => {
      var arp_code_list = [];
      for (var arps in BASE_CITY_LIST) {
        arp_code_list = arp_code_list.concat(arps);
        this.getArpSts(arps);
      }
      var date = new Date();
      var mm = date.getMonth() + 1;
      var dd = date.getDate();
      var yy = date.getFullYear();
      if (mm < 10) {
        mm = "0" + mm;
      }
      if (dd < 10) {
        dd = "0" + dd;
      }
      var today = yy + "" + mm + dd;
      var param = {
        SOLR_CODE: "FAC_COMP_ARP_FLIGHT_KPI",
        COMP_CODE: current_company_code,
        AIRPORT_CODE: arp_code_list.join(","),
        KPI_CODE: ["ORI_NORMAL_NO", "ORI_TOFF_NO"].join(","),
        VALUE_TYPE: "kpi_value_d",
        DATE_TYPE: "D",
        DATE_ID: today,
        LIMIT: 0,
        OPTIMIZE: 1,
      };
      $.ajax({
        type: "post",
        url: "/bi/query/getackpi",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          var data = response.data[current_company_code];
          for (var kpicode in data) {
            var arpdatlist = data[kpicode]["D"];
            for (var arpcode in arpdatlist) {
              var val = arpdatlist[arpcode];
              if (arp_kpi_value[arpcode] == undefined) {
                arp_kpi_value[arpcode] = {};
              }
              arp_kpi_value[arpcode][kpicode] = Number(val);
            }
          }
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }
  loadArpFlight() {
    return new Promise((resolve, reject) => {
      var arplist = [];
      for (var code in BASE_CITY_LIST) {
        if (arplist.indexOf(code) == -1) {
          arplist.push(code);
        }
      }
      var date = new Date();
      var mm = date.getMonth() + 1;
      var dd = date.getDate();
      var yy = date.getFullYear();
      if (mm < 10) {
        mm = "0" + mm;
      }
      if (dd < 10) {
        dd = "0" + dd;
      }
      var today = yy + "" + mm + dd;

      var kpis = ["AC_NO", "AC_ARR_NO"];
      var param = {
        SOLR_CODE: "FAC_COMP_ARP_FLIGHT_KPI",
        COMP_CODE: current_company_code,
        AIRPORT_CODE: arplist.join(","),
        KPI_CODE: kpis.join(","),
        VALUE_TYPE: "kpi_value_d",
        DATE_TYPE: "D",
        LIMIT: 0,
        DATE_ID: today,
        OPTIMIZE: 1,
      };
      $.ajax({
        type: "post",
        url: "/bi/query/getackpi",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          var data = response.data[current_company_code];
          for (var kpicode in data) {
            var arpdatlist = data[kpicode]["D"];
            for (var arpcode in arpdatlist) {
              var val = arpdatlist[arpcode];
              if (all_arp_kpi_value[arpcode] == undefined) {
                all_arp_kpi_value[arpcode] = {};
              }
              all_arp_kpi_value[arpcode][kpicode] = Number(val);
            }
          }
          // 计算总量
          var AC_ARR_NO_TOTAL = 0; // 所有机场过夜飞机架数
          var AC_NO_TOTAL = 0; // 所有机场预计过夜飞机架数
          var basenum = 0;
          for (var i = arplist.length - 1; i >= 0; i--) {
            var code = arplist[i];
            var AC_NO = 0; // 预计飞机架数
            var AC_ARR_NO = 0; // 过夜飞机架数
            if (all_arp_kpi_value[code]) {
              AC_NO = Number(all_arp_kpi_value[code]["AC_NO"]);
              AC_ARR_NO = Number(all_arp_kpi_value[code]["AC_ARR_NO"]);

              if (
                AC_NO > 0 ||
                AC_ARR_NO > 0 ||
                BASE_CITY_LIST[code] != undefined
              ) {
                basenum++;
                AC_ARR_NO_TOTAL += AC_ARR_NO;
                AC_NO_TOTAL += AC_NO;
                arp_has_plane_list.push(code);
              }
            }
          }
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }
  loadactypeall() {
    return new Promise((resolve, reject) => {
      var param = {};
      $.ajax({
        type: "post",
        url: "/bi/web/actypeall",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          if (response.actype) {
            var list = response.actype;
            list.sort(function (a, b) {
              return a.sort - b.sort;
            });
            var len = list.length;
            for (var i = 0; i < len; i++) {
              var obj = list[i];
              actypeId2Code[obj.id] = obj.code;
            }
          }
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }
  loadAcNum() {
    return new Promise((resolve, reject) => {
      var param = {
        SOLR_CODE: "FAC_COMP_ACTYPE_KPI",
        COMP_CODE: current_company_code,
        KPI_CODE: "AC_NUM,EXE_AC_NUM", // 飞机架次, 执行中飞机架数
        VALUE_TYPE: "kpi_value_d", //本期
        DATE_TYPE: "D",
        OPTIMIZE: 1,
        ACTYPE: "ALL",
        LIMIT: 1,
      };
      $.ajax({
        type: "post",
        url: "/bi/query/getackpi",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          if (response.data != undefined) {
            ac_num = response.data;
          }
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }

  loadPdcAcReg() {
    return new Promise((resolve, reject) => {
      // 总运力
      var param = {
        acOwners: current_company_code,
        acTypeNotIn: "D00,D10", //需要过滤掉的机型
      };
      $.ajax({
        type: "post",
        url: "/bi/web/getPdcAcReg",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          var list = response.data;
          acregs.length = 0;
          acTypes = new Array();
          for (var i = list.length - 1; i >= 0; i--) {
            var obj = list[i];
            if (obj.acOwner == "Y8" && obj.config == "F0") {
              //金鹏结果集要过滤掉货舱F0的数据
              continue;
            }
            acregs.push(obj.acreg);
            if (acTypes.indexOf(obj.acType) == -1) {
              acTypes.push(obj.acType);
            }
            reg2actyp[obj.acreg] = obj.acType;
          }
          // 获取机型转换表
          var param = {
            actypecode: acTypes.join(","),
            company: current_company_code,
          };
          $.ajax({
            type: "post",
            // url:"/bi/web/actypquery",
            url: "/bi/web/actypmappingquery",
            contentType: "application/json",
            dataType: "json",
            async: true,
            data: JSON.stringify(param),
            success: function (response) {
              actypeMapList = response;
              ac_data_list = {};
              for (var ac in actypeMapList) {
                var accode = actypeMapList[ac];
                if (ac_data_list[accode] == undefined) {
                  ac_data_list[accode] = 0;
                }
                if (ac_type_list.indexOf(accode) == -1) {
                  ac_type_list.push(accode);
                }
              }
              for (var reg in reg2actyp) {
                var ac = reg2actyp[reg];
                var accode = actypeMapList[ac];
                if (accode) {
                  ac_data_list[accode]++;
                } else {
                  if (lost_ac_map.indexOf(ac) == -1) {
                    lost_ac_map.push(ac);
                  }
                }
              }
              var date = new Date();
              var mm = date.getUTCMonth() + 1;
              var dd = date.getUTCDate();
              var h = date.getUTCHours();
              var m = date.getUTCMinutes();
              var s = date.getUTCSeconds();
              if (mm < 10) {
                mm = "0" + mm;
              }
              if (dd < 10) {
                dd = "0" + dd;
              }
              if (h < 10) {
                h = "0" + h;
              }
              if (m < 10) {
                m = "0" + m;
              }
              if (s < 10) {
                s = "0" + s;
              }
              var utcTimeNow =
                date.getFullYear() +
                "-" +
                mm +
                "-" +
                dd +
                " " +
                h +
                ":" +
                m +
                ":" +
                s;
              // 停场运力
              var param = {
                mntUtcStart: stdStartUtcTime,
                mntUtcEnd: stdEndUtcTime,
                nowInMntUtcRange: utcTimeNow,
                acregs: acregs.join(","),
              };
              $.ajax({
                type: "post",
                url: "/bi/web/getFocMaintInfoByListByPage",
                contentType: "application/json",
                dataType: "json",
                async: true,
                data: JSON.stringify(param),
                success: function (response) {
                  var list2 = response.data;
                  acregs2.length = 0;
                  tingchang_ac2num = {};
                  for (var i = list2.length - 1; i >= 0; i--) {
                    var obj = list2[i];

                    var time1 =
                      parserDate(obj.tStart).getTime() + 8 * 3600 * 1000;
                    var time2 =
                      parserDate(obj.tEnd).getTime() + 8 * 3600 * 1000;
                    var date = new Date();
                    var timenow = date.getTime();
                    if (
                      acregs2.indexOf(obj.acreg) == -1 &&
                      timenow > time1 &&
                      timenow < time2 &&
                      (obj.distinctcol == 1 || obj.distinctcol == 2)
                    ) {
                      acregs2.push(obj.acreg);
                      // 对应大机型数量减去维护中的数量
                      var actyp = reg2actyp[obj.acreg];
                      var accode = actypeMapList[actyp];
                      ac_data_list[accode]--;

                      // 大机型对应的停车数量
                      var bigactyp = obj.acType;
                      bigactyp = bigactyp.split("-")[0];
                      bigactyp = bigactyp.split("(")[0];
                      bigactyp = bigactyp.replace("A", "");
                      bigactyp = bigactyp.replace("B", "");
                      bigactyp = bigactyp.replace("C", ""); // C919...
                      bigactyp = bigactyp.replace("ERJ", "");
                      if (tingchang_ac2num[bigactyp] == undefined) {
                        tingchang_ac2num[bigactyp] = 0;
                      }
                      tingchang_ac2num[bigactyp]++;
                      //distinctcol=2 为非计划停场
                      //今日非计划停场飞机
                      //飞机号
                      //机型
                      //停场结束时间tEnd=预计恢复时间
                      //剩余=预计恢复时间-目前时间
                      if (
                        obj.distinctcol == 2 &&
                        timenow > time1 &&
                        timenow < time2
                      ) {
                        aog_planes.push(obj);
                      }
                    }
                  }
                  available_ac = acregs.length - acregs2.length;
                  resolve(response.errorcode);
                },
                error: function (response) {
                  reject(response.errorcode);
                },
              });
            },
            error: function (response) {
              reject(response.errorcode);
            },
          });
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }

  loadCompDelayCase() {
    return new Promise((resolve, reject) => {
      var param = {
        SOLR_CODE: "FAC_COMP_DELAY_CAUSE_RATE_KPI", //FAC_COMP_ARP_NORMAL_RATE_KPI
        COMP_CODE: current_company_code,
        KPI_CODE: "DELAY_NO",
        VALUE_TYPE: "kpi_value_d",
        DATE_TYPE: "D",
        LIMIT: "1",
        OPTIMIZE: 1,
      };
      $.ajax({
        type: "post",
        url: "/bi/query/getkpi",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          delay = response.data;
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }

  loadplanNum() {
    return new Promise((resolve, reject) => {
      var param = {
        companyCodes: current_company_code,
        stdStartUtcTime: stdStartUtcTime,
        stdEndUtcTime: stdEndUtcTime,
        detailType: "pftc", //统计航班（总计）
        //"psrType":"all"
      };

      $.ajax({
        type: "post",
        url: "/bi/web/getPsrSummInfo",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          planNum = Number(response.planNum); //旅客订票人数
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }

  loadckiNum() {
    return new Promise((resolve, reject) => {
      var param = {
        companyCodes: current_company_code,
        stdStartUtcTime: stdStartUtcTime,
        stdEndUtcTime: stdEndUtcTime,
        detailType: "cftc", //, //已执行
        //"psrType":"all"
      };

      $.ajax({
        type: "post",
        url: "/bi/web/getPsrSummInfo",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          ckiNum = Number(response.ckiNum); //旅客值机人数
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }

  loadAllCompanyData_d() {
    return new Promise((resolve, reject) => {
      let param = {
        SOLR_CODE: "FAC_COMP_KPI",
        COMP_CODE: all_comp_codelist.join(","),
        KPI_CODE: "DEP_NORMAL_NO,DEP_NO,ARR_NORMAL_NO,ARR_NO",
        VALUE_TYPE: "kpi_value_d", //本期
        DATE_TYPE: "D",
        DATE_ID: current_date,
        OPTIMIZE: 1,
        LIMIT: query_limit,
      };
      $.ajax({
        type: "post",
        url: "/bi/query/getkpi",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          console.log("FAC_COMP_KPI kpi_value_d", response.data);
          if (response.data != undefined) {
            all_company_data["kpi_value_d"] = response.data;
          }
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }

  load7x2_flt_sts() {
    return new Promise((resolve, reject) => {
      var param = {
        stdStartUtcTime: stdStartUtcTime,
        stdEndUtcTime: stdEndUtcTime,
        companyCodes: current_company_code,
        AcTypeList: "",
        depstns: "",
        arrstns: "",
      };
      $.ajax({
        type: "post",
        url: "/bi/redis/7x2_flt_sts",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          flt_sts = response;
          resolve(response.errorcode);
        },
        error: function (response) {
          reject(response.errorcode);
        },
      });
    });
  }
  setRightData() {
    $("#flight_no_data").hide();
    $("#flight_details").show();
    vip_flt_no_list = [];
    zdgzSwpier && zdgzSwpier.destroy(false);
    $("#plane_nos").html("");
    //重点关注航班
    for (var i in vip_flt_no) {
      var fno = vip_flt_no[i];
      if (
        fno.length > 0 &&
        fno.indexOf(current_company_code) > -1 &&
        this.findFltInfo(fno) != undefined &&
        vip_flt_no_list.indexOf(fno) == -1
      ) {
        var obj = this.findFltInfo(fno);
        // 近2小时进出港航班
        var etdChn = obj.etdChn; //预计起飞时间（北京时间）
        var d_time = parserDate(etdChn);
        var now_time = new Date();
        // 2小时内出发的飞机
        var ost = d_time.getTime() - now_time.getTime();
        if (ost <= 120 * 60 * 1000 && ost >= 0) {
          vip_flt_no_list.push(fno);
        }
      }
    }
    if (vip_flt_no_list.length > 0) {
      this.createFltList(vip_flt_no_list);
      var flt = this.findFltInfo(vip_flt_no_list[0]);
      this.setFltDetails(flt);
    } else {
      $("#flight_no_data").show();
      $("#flight_details").hide();
    }

    //安全预警
    //运行航班事件
    var list = all_company_data["runTimeEventList"];
    var fltListByComp = {};
    var len = companylist.length;
    for (var i = 0; i < len; i++) {
      var dat = companylist[i];
      if (dat.code != parent_company) {
        fltListByComp[dat.yhscode] = {
          list: [],
          code: dat.code,
          name: dat.name,
          sort: dat.sort,
        };
      } else if (dat.code == parent_company) {
        fltListByComp["ALL"] = {
          list: [],
          code: dat.code,
          name: dat.name,
          sort: dat.sort,
        };
      }
    }
    var legendNameList = [];
    var prolist = all_company_data["runTimePropertiesStatic"];
    prolist.sort(function (a, b) {
      return a.code - b.code;
    });
    for (var i in prolist) {
      var d = prolist[i];
      legendNameList.push(d.name);
    }
    var eventKVList = {};
    for (var i = list.length - 1; i >= 0; i--) {
      var dd = list[i];
      eventKVList[dd.id] = dd;
      if (fltListByComp[dd.companyCode]) {
        fltListByComp[dd.companyCode].list.push(dd);
      }
      fltListByComp["ALL"].list.push(dd);
    }
    var html = ``;
    var len2 = fltListByComp[yhscode] ? fltListByComp[yhscode].list.length : 0;
    $(".warn_pic .content_tab_yxhbsj .count").text(len2);
    html += `<div class='head'>`;
    if (len2 > 4) {
      html += `<div class='btns'><span class='btn btn_prev disabled'  data-len='${len2}' data-page='0' ></span><span class='btn btn_next'  data-len='${len2}' data-page='0' ></span></div>`;
    }
    html += `</div><!-- /head -->`;
    // LIST
    html += `<div class='itmlst'>`;
    for (var j = 0; j < len2; j++) {
      var evt = fltListByComp[yhscode].list[j];
      var date = evt.flightDateStr;
      var darr = date.split("-");
      date = darr[1] + "/" + darr[2];
      var color = legendColorList1[legendNameList.indexOf(evt.eventAttrName)];
      html += `<div class='blk blk_${j}' id='event_id_${evt.id}' data-id='${evt.id}' data-fltno='${evt.flightNo}' data-code='${dd.code}' data-date='${evt.flightDateStr}' data-depstn='${evt.depstn}' >`;
      html += `<div class='time'>`;
      html += `<span class='l'>${date}</span>`;
      html += `<span class='r hour'></span>`;
      html += `</div>`;
      html += `<div class='fltno'>${evt.flightNo}</div>`;
      var style = "";
      evt.airlineCn = evt.airlineCn.replace(/\//g, "");
      if (evt.airlineCn.length > 7) {
        style = "position: absolute; line-height:13px; bottom:23px;";
      }
      html += `<div class='city' style='${style}' >${evt.airlineCn}</div>`;
      html += `<div class='bot'><span class='dot' style='color:${color}; '>●</span><span class='lb'>${evt.eventAttrName}</span></div>`;
      html += `</div><!-- /blk -->`;
    }
    html += `</div><!-- /itmlst -->`;
    $(".tab_yxhbsj").html(html);
    this._initAqEventPage("tab_yxhbsj", 4, 400);

    //安全航班事件
    var list = all_company_data["findEventList"];
    var fltListByComp = {};
    var len = companylist.length;
    for (var i = 0; i < len; i++) {
      var dat = companylist[i];
      if (dat.code != parent_company) {
        fltListByComp[dat.yhscode] = {
          list: [],
          code: dat.code,
          name: dat.name,
          sort: dat.sort,
        };
      } else if (dat.code == parent_company) {
        fltListByComp["ALL"] = {
          list: [],
          code: dat.code,
          name: dat.name,
          sort: dat.sort,
        };
      }
    }
    var eventKVList = {};
    for (var i = list.length - 1; i >= 0; i--) {
      var dd = list[i];
      eventKVList[dd.id] = dd;
      // var level = dd.eventLevel;
      // if (level == 5) { // 岗位红线归类到严重错误上
      //     level = 2;
      // }
      if (fltListByComp[dd.companyCode]) {
        fltListByComp[dd.companyCode].list.push(dd);
      }
      fltListByComp["ALL"].list.push(dd);
    }
    var html = ``;
    var len2 = fltListByComp[yhscode] ? fltListByComp[yhscode].list.length : 0;
    $(".warn_pic .content_tab_aqhbsj .count").text(len2);
    html += `<div class='head'>`;
    if (len2 > 4) {
      html += `<div class='btns'><span class='btn btn_prev disabled'  data-len='${len2}' data-page='0' ></span><span class='btn btn_next'  data-len='${len2}' data-page='0' ></span></div>`;
    }
    html += `</div><!-- /head -->`;
    // LIST
    html += `<div class='itmlst'>`;
    for (var j = 0; j < len2; j++) {
      var evt = fltListByComp[yhscode].list[j];
      var date = evt.flightDateStr;
      var darr = date.split("-");
      date = darr[1] + "/" + darr[2];
      var color = legendColorList3[Number(evt.eventReason) - 1];
      var color2 = legendColorList4[Number(evt.eventLevel) - 1];
      html += `<div class='blk blk_${j}' id='event_id_${evt.id}' data-id='${evt.id}' data-fltno='${evt.flightNo}' data-code='${dd.code}' data-date='${evt.flightDateStr}' data-depstn='${evt.depstn}' >`;
      html += `<div class='time'>`;
      html += `<span class='l'>${date}</span>`;
      html += `<span class='r hour'></span>`;
      html += `</div>`;
      html += `<div class='fltno' style='color:${color2}'>${evt.flightNo}</div>`;
      var style = "";
      evt.airlineCn = evt.airlineCn.replace(/\//g, "");
      if (evt.airlineCn.length > 7) {
        style = "position: absolute; line-height:13px; bottom:23px;";
      }
      html += `<div class='city' style='${style}' >${evt.airlineCn}</div>`;
      html += `<div class='bot'><span class='dot' style='color:${color}; '>●</span><span class='lb'>${evt.eventType}</span></div>`;
      html += `</div><!-- /blk -->`;
    }
    html += `</div><!-- /itmlst -->`;
    $(".tab_aqhbsj").html(html);
    this._initAqEventPage("tab_aqhbsj", 4, 400);

    //机场天气预告
    var html = ``;
    var provinceName = "";
    var provinceName2 = "";
    var weatherName = "";
    var len = unnormalBase.length;
    $(".warn_pic .content_tab_jctqgj .count").text(len);
    html += `<div class='head'>`;
    if (len > 4) {
      html += `<div class='btns'><span class='btn btn_prev disabled'  data-len='${len}' data-page='0' ></span><span class='btn btn_next'  data-len='${len}' data-page='0' ></span></div>`;
    }
    html += `</div><!-- /head -->`;
    // LIST
    html += `<div class='itmlst'>`;
    for (var j = 0; j < len; j++) {
      provinceName = unnormalBase[j].ccc;
      provinceName2 = airMap.get(provinceName);
      weatherName = unnormalBase[j].wx;
      let weathersplit = weatherName.split(" ");
      let weather = weathersplit.length > 0 ? wxCode2Name[weathersplit[0]] : "";
      let weather_css =
        weather && weather != ""
          ? weather_map[weather.substring(weather.length - 1, weather.length)]
          : "";
      let weathertx = weather ? weather : weatherName;
      html += `<div class='blk blk_${j}' >`;
      html += `<div class='time'>`;
      html += `<span class='l'>${provinceName2}</span>`;
      html += `<span class='r hour'></span>`;
      html += `</div>`;
      html += `<div class='weather'><span class="${weather_css}"></span></div>`;
      html += `<div class='bot'><span class='lb'>${weathertx}</span></div>`;
      html += `</div><!-- /blk -->`;
    }
    html += `</div><!-- /itmlst -->`;
    $(".tab_jctqgj").html(html);
    this._initAqEventPage("tab_jctqgj", 4, 400);

    //延误机场告警
    var html = ``;
    var len = alertAirport.length;
    $(".warn_pic .content_tab_ywjcgj .count").text(len);
    html += `<div class='head'>`;
    if (len > 2) {
      html += `<div class='btns'><span class='btn btn_prev disabled'  data-len='${len}' data-page='0' ></span><span class='btn btn_next'  data-len='${len}' data-page='0' ></span></div>`;
    }
    html += `</div><!-- /head -->`;
    // LIST
    html += `<div class='itmlst'>`;
    for (var j = 0; j < len; j++) {
      var itm = alertAirport[j];
      if (itm.DELAY_CAUSE != "") {
        var arpname;
        for (var code in arp_detail_list) {
          var arp = arp_detail_list[code];
          if (arp.id == itm.ARP_ID) {
            arpname = arp.chn_name;
            if (arpname.indexOf("机场") == -1) {
              arpname = arpname + "机场";
            }
            break;
          }
        }
        let total = Number(itm.KPI_VALUE);
        html += `<div class='blk blk_${j}' >`;
        html += `<div class='time'>`;
        html += `<span class='l'>${arpname}</span>`;
        html += `<span class='r hour'></span>`;
        html += `</div>`;
        html += `<div class='fltno'><span class="blue1">总延误航班量：</span>${total}</div>`;
        html += `<div class='city'><span class="blue1">主要延误原因：</span>${itm.DELAY_CAUSE}</div>`;
        html += `</div><!-- /blk -->`;
      }
    }
    html += `</div><!-- /itmlst -->`;
    $(".tab_ywjcgj").html(html);
    this._initAqEventPage("tab_ywjcgj", 2, 400);
  }
  setMidData() {
    //weather
    for (var code in BASE_CITY_LIST) {
      var ORI_NORMAL_NO = 0;
      var ORI_TOFF_NO = 0;
      if (arp_kpi_value[code] != undefined) {
        ORI_NORMAL_NO = Number(arp_kpi_value[code]["ORI_NORMAL_NO"]);
        ORI_TOFF_NO = Number(arp_kpi_value[code]["ORI_TOFF_NO"]);
        var r = Math.round((ORI_NORMAL_NO / ORI_TOFF_NO) * 100);
        $("#arp_code_" + code + " .normal_rate1").text(isNaN(r) ? "100" : r);
        this._setNormalRateTextColor(
          $("#arp_code_" + code + " .normal_rate1"),
          r
        );
      } else {
        console.log("arp_kpi_value[] cannot find airport:" + code);
      }
    }
    //3dmap
    var seriesData = [];
    var series2d = [];
    var list_1 = []; // 正常航班
    var list_2 = []; // 延误航班
    for (var i = planeLocationList.length - 1; i >= 0; i--) {
      var itm = planeLocationList[i];
      var acno = itm.acno;
      var fltno = itm.fltno;

      var vec = itm.vec;
      var alt = itm.alt;

      var lon = itm.lon;
      var lat = itm.lat;

      var flt = this.findFltInfo(fltno);
      if (flt && (alt > 0 || flt.status == "DEP")) {
        var img = "";
        var delay_min =
          Number(flt.dur1) +
          Number(flt.dur2) +
          Number(flt.dur3) +
          Number(flt.dur4); //延误时间 分钟
        var color;
        var border;
        var svg =
          "M90.7,4.8c-1.3-1.5-2.8-2.7-4.5-3.5C84.3,0.4,82.4,0,80.3,0l-0.4,0.1L79.5,0c-2,0-4,0.4-5.8,1.4c-1.8,0.8-3.3,2-4.6,3.5c-2.2,2.3-3.2,4.9-3.2,8l0,47.5L0.1,111.7L0,127.4l65.3-21.2l-0.1,38L39,167.2l0,7.9l3-0.8v1.6l37.9-10.9l37.9,10.9v-1.6l2.9,0.8l-0.1-7.9l-26.1-22.9l-0.1-38l65.4,21.2l-0.1-15.8L94,60.3l-0.1-47.5C93.9,9.8,92.9,7.1,90.7,4.8z";
        if (delay_min == 0) {
          color = "#00ff6c";
          border = "#005d09";
          list_1.push({
            name: fltno,
            vec: vec,
            flt: flt,
            value: [lon, lat],
            symbolRotate: -vec + 90, //航向   正北是0°，顺时针，0到360° // 因为图片机头指向右，所以需要逆时针转90度
          });
        } else {
          color = "#fff60e";
          border = "#2f2a0b";
          list_2.push({
            name: fltno,
            vec: vec,
            flt: flt,
            value: [lon, lat],
            symbolRotate: -vec + 90, //航向   正北是0°，顺时针，0到360° // 因为图片机头指向右，所以需要逆时针转90度
          });
        }
        seriesData.push({
          name: fltno,
          acno: acno,
          value: [lon, lat, 0],
          //symbolRotate: -vec, //航向   正北是0°，顺时针，0到360°
          //symbol:'path://'+svg,
          itemStyle: {
            color: color,
            borderColor: border,
            borderWidth: 1,
          },
        });
      }
    }
    series2d.push({
      name: "normal",
      type: "scatter",
      coordinateSystem: "geo",
      symbol: "image://./img/a3.3.legend_1.png",
      symbolSize: 15,
      label: {
        normal: {
          show: false,
        },
      },
      data: list_1,
    });
    series2d.push({
      name: "delay",
      type: "scatter",
      coordinateSystem: "geo",
      symbol: "image://./img/a3.3.legend_2.png",
      symbolSize: 15,
      label: {
        normal: {
          show: false,
        },
      },
      data: list_2,
    });
    var series = [];
    series.push({
      type: "scatter3D",
      coordinateSystem: "globe",
      symbolSize: 6,
      //blendMode: 'lighter',
      slient: true,
      label: {
        show: false,
      },
      data: seriesData,
    });
    let options = myChart.getOption();
    options.series = series;
    myChart.setOption(options, true);
    let options2d = myChart2d.getOption();
    options2d.series = series2d;
    myChart2d.setOption(options2d, true);

    myChart2d.on(
      "click",
      function (params) {
        params.event.event.stopPropagation();
        params.event.event.preventDefault();

        var url =
          "/largescreen/general/flight.html?fltno=" +
          params.data.flt.flightNo +
          "&compCode=" +
          params.data.flt.flightNo.substr(0, 2);
        var url_scale = this._getQueryString("scale");
        if (url_scale) {
          url = url + "&scale=" + url_scale;
        }
        this._windowOpen(url, "_blank");
      }.bind(this)
    );
  }
  setLeftData() {
    //航班总量
    var sch_total = Number(flt_sts.pftc); //计划航班总数
    var cftc = Number(flt_sts.cftc); //已执行航班总数
    var pftcl = Number(flt_sts.pftcl); //国内计划航班总数
    var cftcl = Number(flt_sts.cftcl); //国内已执行航班总数
    var pftci = Number(flt_sts.pftci); //国际计划航班总数
    var cftci = Number(flt_sts.cftci); //国际已执行航班总数
    var exe_rate = cftc / sch_total;
    $("#val_flt_total").text(sch_total);
    $("#val_flt_exec_rate").text(Math.round(exe_rate * 100) + "%");
    this._drawGauge("yzx_hbzl", "yzx_hbzl_pointer", exe_rate);
    $("#val_flt_total_china").text(pftcl);
    $("#val_flt_exec_rate_china").text(Math.round((cftcl / pftcl) * 100) + "%");
    $("#val_flt_total_int").text(pftci);
    $("#val_flt_exec_rate_int").text(Math.round((cftci / pftci) * 100) + "%");
    //航班正常率
    var pfPercent = Number(flt_sts.pfdappPercent); //计划航班中的正常航班比率
    this._setIndicatorContainer("normal_rate_hb", pfPercent);
    let DEP_NORMAL_NO =
      all_company_data["kpi_value_d"][current_company_code]["DEP_NORMAL_NO"][
        "D"
      ][current_date];
    let DEP_NO =
      all_company_data["kpi_value_d"][current_company_code]["DEP_NO"]["D"][
        current_date
      ];
    let ARR_NORMAL_NO =
      all_company_data["kpi_value_d"][current_company_code]["ARR_NORMAL_NO"][
        "D"
      ][current_date];
    let ARR_NO =
      all_company_data["kpi_value_d"][current_company_code]["ARR_NO"]["D"][
        current_date
      ];
    $("#dg").text(
      Number.isNaN(ARR_NORMAL_NO / ARR_NO)
        ? 0
        : ((ARR_NORMAL_NO / ARR_NO) * 100).toFixed(2)
    );
    $("#cg").text(
      Number.isNaN(DEP_NORMAL_NO / DEP_NO)
        ? 0
        : ((DEP_NORMAL_NO / DEP_NO) * 100).toFixed(2)
    );
    //计划运输的旅客总量
    $("#val_trv_num_plan").text(planNum);
    $("#val_trv_num_completed").text(ckiNum);
    //不正常航班
    $("#flt_delay_12").text(Number(flt_sts.pfdtc12));
    $("#flt_delay_24").text(Number(flt_sts.pfdtc24));
    $("#flt_delay_4").text(Number(flt_sts.pfdtc4));
    $("#flt_return_back").text(Number(flt_sts.dftc) + Number(flt_sts.bftc));
    $("#flt_cancel").text(flt_sts.qftc1);
    //延误原因
    var data_dly = delay[current_company_code]["DELAY_NO"]["D"];
    var comp_cause_list = [];
    var none_cause_list = [];
    var comp_total = 0;
    var none_total = 0;
    // 公司原因 总数
    for (var time in data_dly) {
      var d = data_dly[time];
      var len = comp_cause.length;
      for (var i = 0; i < len; i++) {
        var causeName = comp_cause[i];
        if (!isNaN(d[causeName])) {
          var val = Number(d[causeName]);
          comp_total += val;
          if (val > 0) {
            comp_cause_list.push({
              name: causeName,
              val: val,
            });
          }
        }
      }
      break;
    }

    // 非公司原因 总数
    for (var time in data_dly) {
      var d = data_dly[time];
      var len = none_cause.length;
      for (var i = 0; i < len; i++) {
        var causeName = none_cause[i];
        if (!isNaN(d[causeName])) {
          var val = Number(d[causeName]);
          if (causeName == "民航局航班时刻安排") {
            causeName = "时刻安排";
          }
          none_total += val;
          if (val > 0) {
            none_cause_list.push({
              name: causeName,
              val: val,
            });
          }
        }
      }
      break;
    }
    // 公司
    var html = "";
    var len = comp_cause_list.length;
    for (var i = 0; i < len; i++) {
      var d = comp_cause_list[i];
      var per = Number(d.val) / (comp_total + none_total);
      var perstr = Math.round(per * 100);
      var barlen = 100 * per;
      if (perstr > 0) {
        html +=
          '<div class="baritmrow text-center"><span class="blue2">' +
          d.name +
          '</span> <span class="bar greenbar" style="width: ' +
          barlen +
          'px; "></span> <span class="ffnum">' +
          perstr +
          "%</span> </div>";
      }
    }
    $("#holder_delay_cause_comp").html(html);
    // 非公司
    html = "";
    var len = none_cause_list.length;
    for (var i = 0; i < len; i++) {
      var d = none_cause_list[i];
      var per = Number(d.val) / (comp_total + none_total);
      var perstr = Math.round(per * 100);
      var barlen = 100 * per;
      if (perstr > 0) {
        html +=
          '<div class="baritmrow text-center"><span class="blue2">' +
          d.name +
          '</span> <span class="bar bluebar" style="width: ' +
          barlen +
          'px; "></span> <span class="ffnum">' +
          perstr +
          "%</span> </div>";
      }
    }
    $("#holder_delay_cause_none").html(html);
    $("#per_delay_cause_comp").text(
      Math.round((comp_total / (comp_total + none_total)) * 100)
    );
    $("#per_delay_cause_none").text(
      Math.round((none_total / (comp_total + none_total)) * 100)
    );
    this._drawDelayChart(comp_total / (comp_total + none_total));
    //运力分布
    $("#total_plane_num").text(acregs.length);
    var exe_total_plane = 0;
    var exe_ac_data_list = {};
    var numofac = 0;
    if (ac_num[current_company_code] != undefined) {
      var kpiaclst1 = ac_num[current_company_code]["AC_NUM"]["D"]["data3"];
      var kpiaclst2 = ac_num[current_company_code]["EXE_AC_NUM"]["D"]["data3"];
      // 总飞机架数
      var len = kpiaclst1.length;
      for (var i = 0; i < len; i++) {
        numofac++;
      }
      // 执行中飞机架数
      var len = kpiaclst2.length;
      for (var i = 0; i < len; i++) {
        var acdat = kpiaclst2[i];
        var acid = acdat.actype;
        var accode = actypeId2Code[acid];
        if (accode && accode != "QITA") {
          var acdd = acdat.date;
          var len2 = acdd.length;
          // 每种机型的架数
          var acno = 0;
          for (var j = 0; j < len2; j++) {
            var dd = acdd[j];
            var val = isNaN(dd.value) ? 0 : Number(dd.value);
            if (ac_type_list.indexOf(accode) > -1) {
              exe_total_plane += val;
            }
            acno += val;
          }

          // 执行中 每种机型的架数
          if (ac_type_list.indexOf(accode) > -1) {
            acno = isNaN(acno) || acno == 0 ? "-" : Math.round(acno);
            if (exe_ac_data_list[accode] == undefined) {
              exe_ac_data_list[accode] = 0;
            }
            exe_ac_data_list[accode] += acno;
          }
        }
      }
    }
    var a = parseInt(exe_total_plane / 100);
    var b = parseInt((exe_total_plane % 100) / 10); // 十位数
    var c = parseInt(exe_total_plane % 10); // 个位数
    $("#zxjg1").text(a);
    $("#zxjg2").text(b);
    $("#zxjg3").text(c);
    $("#plane_over_air").text(exe_total_plane);
    $("#plane_on_ground").text(available_ac - exe_total_plane);
    $("#plane_on_stop").text(acregs2.length); //停场

    var company =
      current_company_code == parent_company ? "" : current_company_code;

    var url = `/bi/spring/aircraft/getAcStatusStat?company=${company}`;

    $.ajax({
      type: "get",
      url: url,
      contentType: "application/json",
      dataType: "json",
      async: true,
      success: function (response) {
        var data = response.data;
        var html = ``;
        var barwidth = 40;
        $("#total_plane_num").text(data.total);
        $("#plane_on_stop").text(data.maintCnt); //停场
        $("#plane_on_ground").text(data.groundCnt);
        $("#plane_over_air").text(data.airCnt);

        var detail = data.detail;
        var numofac = detail.length;
        detail.forEach((i) => {
          var numac = i.cnt;
          var actype = i.actype;
          var numexeac = i.airCnt;
          var ac_dimian = i.groundCnt;
          var ac_tingchang = i.maintCnt;

          var numac = i.cnt;
          let width = (numexeac / numac) * barwidth + "px";
          let acwidth = (ac_dimian / numac) * barwidth + "px";
          let actingwidth = (ac_tingchang / numac) * barwidth + "px";
          html += ` <tr id="ac_${actype}">
                            <td class="blue4 r" width="40">${actype}</td>
                            <td width="18" class="num_air">${numexeac}</td>
                            <td width="45">
                                <span class="bar darkbar">
                                    <span class="bar bluebar2 bar_air bar_ground"         style="width: ${width};"></span>
                                 </span> 
                            </td>
                            <td width="18" class="num_ground">${ac_dimian}</td>
                            <td width="45"><span class="bar darkbar"><span class="bar greenbar2 bar_ground"
                                                 style="width: ${acwidth}; right:0px;"></span></span>
                            </td>
                            <td width="18" class="num_ground">${ac_tingchang}</td>
                            <td width="45"><span class="bar darkbar"><span class="bar brownbar2 bar_ground"
                                                 style="width: ${actingwidth}; right:0px;"></span></span>
                             </td>
                        </tr>`;
        });

        $("#plane_num_table").html(html);
        // 详细机型列表滚动
        clearInterval(marquee_itv_airPlane);
        $("#plane_num_table1").html("");
        if (numofac > 4) {
          var speed = 80;
          var base_sec = document.getElementById("barlist");
          var base_sec2 = document.getElementById("plane_num_table1");
          var base_sec1 = document.getElementById("plane_num_table");
          base_sec2.innerHTML = base_sec1.innerHTML;

          function base_Marquee() {
            if (base_sec2.offsetTop - base_sec.scrollTop <= 0)
              base_sec.scrollTop -= base_sec1.offsetHeight;
            else {
              base_sec.scrollTop += Math.ceil(1 / pageZoomScale);
            }
          }

          marquee_itv_airPlane = setInterval(base_Marquee, speed);
          base_sec.onmouseover = function () {
            clearInterval(marquee_itv_airPlane);
          };
          base_sec.onmouseout = function () {
            marquee_itv_airPlane = setInterval(base_Marquee, speed);
          };
        }
      },
    });

    //过夜情况
    setInterval(setArpPlane, 5000);
    var currentArpPlanePage = 0;
    var arpPlanePageSize = 4;
    function setArpPlane() {
      var html = "";

      var s1 = currentArpPlanePage * arpPlanePageSize;
      var s2 = Math.min(
        (currentArpPlanePage + 1) * arpPlanePageSize,
        arp_has_plane_list.length
      );

      for (var i = s1; i < s2; i++) {
        var code = arp_has_plane_list[i];

        var AC_NO = 0; // 预计飞机架数
        var AC_ARR_NO = 0; // 过夜飞机架数

        AC_NO = Number(all_arp_kpi_value[code]["AC_NO"]);
        AC_ARR_NO = Number(all_arp_kpi_value[code]["AC_ARR_NO"]);

        html += `<div class="baritmrow text-left" style="height: 24px">
                                <span class="blue2">${BASE_CITY_LIST[code]}</span>
                                <span class="bar darkbar" style="width: 200px"><span class="bar bluebar bar_ground" style="width: ${AC_ARR_NO}px; "></span></span>
                                <span class="ffnum">${AC_ARR_NO}</span>
                          </div>`;
      }
      $("#base_over_night_plane1").html(html);

      if (
        currentArpPlanePage <
        Math.ceil(arp_has_plane_list.length / arpPlanePageSize) - 1
      ) {
        currentArpPlanePage++;
      } else {
        currentArpPlanePage = 0;
      }
    }
  }
  setFltDetails(fltdat) {
    if (!fltdat) {
      $("#flight_no_data").show();
      $("#flight_details").hide();
      return;
    }
    var fltno = fltdat.flightNo;
    var acno = fltdat.acLongNo;

    var arp1 = arp_detail_list[fltdat.depStn];
    var arp2 = arp_detail_list[fltdat.arrStn];

    $("#detail_depCity").text(arp1.city_name);
    $("#detail_arrCity").text(arp2.city_name);

    $("#detail_stdChn").text(fltdat.stdChn.split(" ")[1].substr(0, 5));
    $("#detail_staChn").text(fltdat.staChn.split(" ")[1].substr(0, 5));

    $("#detail_flightNo").text(fltno);
    $("#detail_iataAcType").text("机型: " + fltdat.acType);
    var status = fltdat.status;
    var tOffChn = fltdat.tOffChn;
    var tDwnChn = fltdat.tDwnChn;

    var dt = new Date();
    var doff = parserDate(tOffChn); // 实际离地北京时间
    var ddown = parserDate(tDwnChn); // 实际落地北京时间

    if (vvip_flt_backup_list[fltno] != undefined) {
      $("#detail_backup").html(
        '<span class="blue2">备机</span><br>' +
          vvip_flt_backup_list[fltno] +
          "<br>"
      );
    } else {
      $("#detail_backup").html("");
    }

    // 前序航班预计过站时间，算法为本段航班计划离港时间-前序航班预计到达时间
    $("#detail_prev_stdChn").text("-");
    $("#detail_prev_passTime").text("-");

    // 查找前序航班
    var prev_flt;
    for (var i = all_flight_list.length - 1; i >= 0; i--) {
      var flt = all_flight_list[i];
      if (
        flt.acLongNo == acno &&
        flt.arrStn == fltdat.depStn &&
        flt.stdChn < fltdat.stdChn
      ) {
        prev_flt = flt;
        break;
      }
    }
    if (prev_flt && status != "CNL") {
      // 前序航班 起飞时间
      var prevd;
      if (
        prev_flt.status == "DEP" ||
        prev_flt.status == "ARR" ||
        prev_flt.status == "NDR" ||
        prev_flt.status == "ATA"
      ) {
        prevd = prev_flt.atdChn.split(" ")[1].substr(0, 5); // 实际起飞时间 atdChn
      } else {
        prevd = prev_flt.etdChn.split(" ")[1].substr(0, 5); // 预计起飞时间 etdChn
      }
      $("#detail_prev_stdChn").text(prevd);

      // 前序航班 过站时间
      var detaChn; // 前序航班 到达时间
      var detdChn; // 本段航班 离港时间
      if (
        prev_flt.status == "ARR" ||
        prev_flt.status == "NDR" ||
        prev_flt.status == "ATA" ||
        prev_flt.status == "ATA"
      ) {
        detaChn = prev_flt.ataChn; // 实际到达时间 ataChn
      } else {
        detaChn = prev_flt.etaChn; // 预计到达时间 etaChn
      }
      if (
        status == "ARR" ||
        status == "NDR" ||
        status == "ATA" ||
        status == "DEP" ||
        status == "RTR"
      ) {
        detdChn = fltdat.atdChn; // 实际起飞时间 atdChn
      } else {
        detdChn = fltdat.etdChn; // 预计起飞时间 etdChn
      }
      var a_time = parserDate(detaChn); // 前序航班
      var d_time = parserDate(detdChn); // 本段航班
      var sec = d_time.getTime() - a_time.getTime();
      var totalmin = sec / 60000;
      var hour = Math.floor(totalmin / 60);
      var min = totalmin % 60;
      $("#detail_prev_passTime").text(hour + "小时" + min + "分");
    }

    // ------------------------------------------------------------------------
    // 获取 机组信息
    // ------------------------------------------------------------------------
    $("#detail_crwPilotInf").text("");
    $("#detail_crwStatus").text("");

    if (fltCrwCache[fltno]) {
      setCrw(fltCrwCache[fltno]);
    } else {
      var param = {
        flightNo: fltno,
      };
      $.ajax({
        type: "post",
        url: "/bi/web/findFlightReportV2",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          if (response.data && response.data[0]) {
            var data = response.data[0];
            fltCrwCache[fltno] = data;
            setCrw(fltCrwCache[fltno]);
          }
        },
        error: function () {},
      });
    }

    function setCrw(crew) {
      // 机组信息
      if (crew) {
        var names = [];
        var str = "";
        if (crew["captain"] == undefined) {
          crew["captain"] = "";
        }
        if (crew["firstVice1"] == undefined) {
          crew["firstVice1"] = "";
        }
        names = names.concat(crew.captain.split("@"));
        names = names.concat(crew.firstVice1.split("@"));

        for (var i = names.length - 1; i >= 0; i--) {
          if (names[i].indexOf("(") > -1) {
            var aaa = names[i].split("(");
            if (aaa.length > 1) {
              names[i] = aaa[0];
            }
          }
        }
        console.log("captain:", names);
        $("#detail_crwPilotInf").text(names.join(","));
        $("#detail_crwStatus").text("");
      }
    }

    // ------------------------------------------------------------------------
    // 获取 航班保障信息
    // ------------------------------------------------------------------------

    /*
        cncPilotArrTime,//机组到达|飞行
        cncStewardArrTime,//机组到达|乘务
        //航班截载 无法获得 checkInEnd
        cncCabinSupplyEndTime,//客舱供应|结束，机供品配备
        cncCleanEndTime,//客舱清洁
        cncMCCReleaseTime,//机务放行
        //飞机准备好 无法获得 planeReady
        cncInformBoardTime,//通知登机
        cncBoardOverTime,//登机结束
        cncClosePaxCabinTime,//客舱关闭
        cncCloseCargoCabinTime,//货舱关闭
        cncPushTime,//飞机推出
        cncACARSTOFF,//飞机起飞
        */

    for (var i in fltnodes) {
      var node = fltnodes[i];
      if (!$("#fltnode_" + node).hasClass("blue3")) {
        $("#fltnode_" + node).addClass("blue3");
      }
      $("#fltnode_" + node).removeClass("green");
      $("#fltnode_" + node).removeClass("yellow");
      $("#fltnode_" + node).removeClass("red");
    }
    if (
      status == "ARR" ||
      status == "NDR" ||
      status == "ATA" ||
      status == "DEP" ||
      status == "RTR"
    ) {
      this.lightThemUp("cncACARSTOFF");
    } else if (status == "ATD") {
      this.lightThemUp("cncPushTime");
    }
    // 只有这几个状态才去获取航班保障信息，其它状态没必要
    if (status == "SCH" || status == "DEL") {
      if (fltLegCache[fltno]) {
        setLeg(fltLegCache[fltno]);
      } else {
        var param = {
          flightNo: fltno,
        };
        $.ajax({
          type: "post",
          url: "/bi/web/getFltmLegsByPage",
          contentType: "application/json",
          dataType: "json",
          async: true,
          data: JSON.stringify(param),
          success: function (response) {
            if (response) {
              fltLegCache[fltno] = response;
              setLeg(fltLegCache[fltno]);
            }
          },
          error: function () {},
        });
      }
    }

    function setLeg(response) {
      var mlegs = response.getFltmLegsByPage;
      if (response && mlegs) {
        if (mlegs.cncPilotArrTime && mlegs.cncPilotArrTime.length > 0) {
          this.lightThemUp("cncPilotArrTime");
        }
        if (
          mlegs.cncCabinSupplyEndTime &&
          mlegs.cncCabinSupplyEndTime.length > 0
        ) {
          this.lightThemUp("cncCabinSupplyEndTime");
        }
        if (mlegs.cncCleanEndTime && mlegs.cncCleanEndTime.length > 0) {
          this.lightThemUp("cncCleanEndTime");
        }
        if (mlegs.cncMCCReleaseTime && mlegs.cncMCCReleaseTime.length > 0) {
          this.lightThemUp("cncMCCReleaseTime");
        }
        if (mlegs.cncInformBoardTime && mlegs.cncInformBoardTime.length > 0) {
          this.lightThemUp("cncInformBoardTime");
        }
        if (mlegs.cncBoardOverTime && mlegs.cncBoardOverTime.length > 0) {
          this.lightThemUp("cncBoardOverTime");
        }
        if (
          mlegs.cncClosePaxCabinTime &&
          mlegs.cncClosePaxCabinTime.length > 0
        ) {
          this.lightThemUp("cncClosePaxCabinTime");
        }
        if (
          mlegs.cncCloseCargoCabinTime &&
          mlegs.cncCloseCargoCabinTime.length > 0
        ) {
          this.lightThemUp("cncCloseCargoCabinTime");
        }
        if (mlegs.cncPushTime && mlegs.cncPushTime.length > 0) {
          this.lightThemUp("cncPushTime");
        }
        if (mlegs.cncACARSTOFF && mlegs.cncACARSTOFF.length > 0) {
          this.lightThemUp("cncACARSTOFF");
        }
      }
    }
    // $('#flight_details_loading').hide();
    // $('#flight_details').show();
  }
  createFltList(fltlist) {
    $("#flight_details").show();
    var html = ``;
    var firstNo;
    for (var i in fltlist) {
      var fltno = fltlist[i];
      if (firstNo == undefined) {
        firstNo = fltno;
      }
      html += `<div class="plane_no fltno_${fltno} swiper-slide" fltno="${fltno}">${fltno}</div>`;
    }
    $("#plane_nos").html(html);
    zdgzSwpier && zdgzSwpier.destroy(false);
    zdgzSwpier = new Swiper(".plane_nos", {
      navigation: {
        nextEl: ".swiper-button-next",
        prevEl: ".swiper-button-prev",
      },
      slideActiveClass: "swiper-slide-active",
      slidesPerView: "5",
      spaceBetween: 10,
      slideToClickedSlide: true,
      centeredSlides: true,
      // grabCursor : true,
      autoplay: {
        stopOnLastSlide: false,
        disableOnInteraction: false,
      },
      on: {
        // init: function(){
        //     let to = new Today_operation();
        //     var flt = to.findFltInfo(this.slides[this.activeIndex].innerHTML);
        //     to.setFltDetails(flt);
        // },
        autoplay: function () {
          var flt = this.findFltInfo(
            zdgzSwpier.slides[zdgzSwpier.activeIndex].innerHTML
          );
          this.setFltDetails(flt);
        }.bind(this),
        click: function () {
          var flt = this.findFltInfo(
            zdgzSwpier.slides[zdgzSwpier.activeIndex].innerHTML
          );
          this.setFltDetails(flt);
        }.bind(this),
        slideNextTransitionStart: function () {
          var flt = this.findFltInfo(
            zdgzSwpier.slides[zdgzSwpier.activeIndex].innerHTML
          );
          this.setFltDetails(flt);
        }.bind(this),
        slidePrevTransitionStart: function () {
          var flt = this.findFltInfo(
            zdgzSwpier.slides[zdgzSwpier.activeIndex].innerHTML
          );
          this.setFltDetails(flt);
        }.bind(this),
      },
    });
  }
  selectFltTab(id) {
    $("#" + id)
      .siblings(".tab_normal")
      .removeClass("tab_selected");
    $("#" + id).addClass("tab_selected");
    $("#flight_no_data").hide();
    flt_tab_id = id;

    if (id == "tab_vip") {
      if (vip_flt_no_list.length > 0) {
        this.createFltList(vip_flt_no_list);
      } else {
        $("#flight_no_data").show();
        $("#flight_details").hide();
        zdgzSwpier && zdgzSwpier.destroy(false);
        $("#plane_nos").html("");
      }
    } else if (id == "tab_warning") {
      if (warning_flt_no_list.length > 0) {
        this.createFltList(warning_flt_no_list);
      } else {
        $("#flight_no_data").show();
        $("#flight_details").hide();
        zdgzSwpier && zdgzSwpier.destroy(false);
        $("#plane_nos").html("");
      }
    } else if (id == "tab_important") {
      if (important_flt_no_list.length > 0) {
        this.createFltList(important_flt_no_list);
      } else {
        $("#flight_no_data").show();
        $("#flight_details").hide();
        zdgzSwpier && zdgzSwpier.destroy(false);
        $("#plane_nos").html("");
      }
    }
  }
  lightThemUp(node) {
    for (var i = fltnodes.indexOf(node); i >= 0; i--) {
      var nd = fltnodes[i];
      $("#fltnode_" + nd).addClass("green");
    }
  }
  findFltInfo(fltno) {
    for (var i = all_flight_list.length - 1; i >= 0; i--) {
      var flt = all_flight_list[i];
      if (flt.flightNo == fltno) {
        return flt;
      }
    }
    return undefined;
  }
  _initMap() {
    myChart.setOption(option);
    myChart2d.setOption(option2d);
  }

  _addEvent() {
    $("#btn_go_overall_operation").click(
      function () {
        this._windowOpen(
          `/largescreen/general/overall_operation.html?scale=auto#${current_company_code}`,
          "_self"
        );
      }.bind(this)
    );
    $("#btn_go_base_run_today").click(
      function () {
        this._windowOpen(
          `/largescreen/general/baseRunToday.html?scale=auto&city_code=${base_code}#${current_company_code}`,
          "_self"
        );
      }.bind(this)
    );
    $("#placeselect").on("change", (event) => {
      base_code = $(event.currentTarget).val();
      this.initDuty().then((resolve) => {
        console.log(resolve);
      });
    });
    $("#dutyselect").on("change", function () {
      let strArr = $(this).val().split(",");
      $("#cnvcDutyname").text(strArr[0]);
      $("#cnvcTel").text(strArr[2]);
      $("#cnvcMobile").text(strArr[1]);
    });
    $(".tab_normal.zdgz").on("click", (event) => {
      this.selectFltTab($(event.currentTarget).attr("id"));
    });
    $(".tab_normal.aqyj").on("click", (event) => {
      $(event.currentTarget)
        .siblings(".tab_normal")
        .removeClass("tab_selected");
      $("#" + $(event.currentTarget).attr("id")).addClass("tab_selected");
    });
    $(".aqyj").click((event) => {
      let id = $(event.currentTarget).attr("id");
      $(".warn_pic .title").html($(event.currentTarget).html());
      $(".warn_pic .content").hide();
      $(".warn_pic .content.content_" + id).show();
      $(".comprow").hide();
      $(".comprow." + id).show();
    });

    $(".button_gy").click(function () {
      $(".gyqk").show();
      $(".ylqk").hide();
      $(".gypic").show();
      $(".ylpic").hide();
      $(this).addClass("back_blue");
      $(".button_yl").removeClass("back_blue");
    });
    $(".button_yl").click(function () {
      $(".gyqk").hide();
      $(".ylqk").show();
      $(".gypic").hide();
      $(".ylpic").show();
      $(this).addClass("back_blue");
      $(".button_gy").removeClass("back_blue");
    });
    $(".plane_change_pic").click(function () {
      let is2D = $(this).data("2d");
      if (is2D) {
        $(this).data("2d", false);
        $(this).css("background-image", "url(img/3Dto2D.png)");
        $("#map2d").hide();
        $("#map3d").show();
      } else {
        $(this).data("2d", true);
        $(this).css("background-image", "url(img/2Dto3D.png)");
        $("#map2d").show();
        $("#map3d").hide();
      }
    });
  }
  _initAqEventPage(id, perpage, width) {
    $("." + id + " .btn_prev").off("click");
    $("." + id + " .btn_prev").on("click", function (evt) {
      evt.stopPropagation();
      if ($(this).hasClass("disabled")) {
        return;
      }
      var len = $(this).attr("data-len");
      var page = $(this).attr("data-page");

      page = Number(page) - 1;
      $("." + id + " .btn_next").attr("data-page", page);
      $("." + id + " .btn_prev").attr("data-page", page);

      $("." + id + " .btn_next").removeClass("disabled");
      if (page == 0) {
        $("." + id + " .btn_prev").addClass("disabled");
      }

      for (var i = page * perpage; i < (page + 1) * perpage; i++) {
        var blk = $("." + id + " .blk_" + i);
        var loaded = blk.attr("data-flt-loaded");
        if (isNaN(loaded)) {
          var data_code = blk.attr("data-code");
          var data_id = blk.attr("data-id");
          var data_date = blk.attr("data-date");
          var data_fltno = blk.attr("data-fltno");
          var data_depstn = blk.attr("data-depstn");
        }
      }
      $("." + id + " .itmlst").animate(
        {
          left: -(width * page),
        },
        {
          duration: 300,
        }
      );
    });
    $("." + id + " .btn_next").off("click");
    $("." + id + " .btn_next").on("click", function (evt) {
      evt.stopPropagation();
      if ($(this).hasClass("disabled")) {
        return;
      }
      var len = $(this).attr("data-len");
      var page = $(this).attr("data-page");

      page = Number(page) + 1;
      $("." + id + " .btn_next").attr("data-page", page);
      $("." + id + " .btn_prev").attr("data-page", page);

      $("." + id + " .btn_prev").removeClass("disabled");
      if (len <= (page + 1) * perpage) {
        $("." + id + " .btn_next").addClass("disabled");
      }

      for (var i = page * perpage; i < (page + 1) * perpage; i++) {
        var blk = $("." + id + " .blk_" + i);
        var loaded = blk.attr("data-flt-loaded");
        if (isNaN(loaded)) {
          var data_code = blk.attr("data-code");
          var data_id = blk.attr("data-id");
          var data_date = blk.attr("data-date");
          var data_fltno = blk.attr("data-fltno");
          var data_depstn = blk.attr("data-depstn");
        }
      }

      $("." + id + " .itmlst").animate(
        {
          left: -(width * page),
        },
        {
          duration: 300,
        }
      );
    });
  }
  _setNormalRateTextColor(element, r) {
    element.parent().find(".ffnum").removeClass("red");
    element.parent().find(".ffnum").removeClass("orange");
    element.parent().find(".ffnum").removeClass("yellow");

    if (r <= Number(normal_rate_colors["red"])) {
      element.parent().find(".ffnum").addClass("red");
    } else if (r <= Number(normal_rate_colors["green1"])) {
      element.parent().find(".ffnum").addClass("orange");
    } else if (r <= Number(normal_rate_colors["yellow"])) {
      element.parent().find(".ffnum").addClass("yellow");
    }
  }
  _drawGauge(canvasId, pointerId, rate) {
    let angle;
    let color;
    const canvas = document.getElementById(canvasId);
    const context = canvas.getContext("2d");
    context.clearRect(0, 0, canvas.width, canvas.height);
    const x = canvas.width / 2;
    const y = canvas.height / 2;

    // draw back
    let radius = 48;
    const startAngle = Math.PI - Math.PI / 5;
    const endAngle = startAngle + Math.PI + (Math.PI / 5) * 2;
    let counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle, endAngle, counterClockwise);
    context.lineWidth = 12;
    context.strokeStyle = "#004a91";
    context.stroke();

    // draw overlay
    const startAngle2 = startAngle;
    const endAngle2 = startAngle + (endAngle - startAngle) * rate;
    counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
    context.lineWidth = 12;

    if (rate <= 1) {
      // linear gradient
      if (rate < 0.5) {
        color = context.createLinearGradient(
          0,
          canvas.height,
          canvas.width * rate,
          0
        );
      } else if (rate < 0.8) {
        color = context.createLinearGradient(
          0,
          canvas.height,
          canvas.width * rate,
          0
        );
      } else {
        color = context.createLinearGradient(
          0,
          canvas.height,
          canvas.width * rate,
          canvas.width / 2
        );
      }
      color.addColorStop(0, "#5f5fff");
      color.addColorStop(0.5, "#4185e4");
      color.addColorStop(1, "#00d3ff");
      context.strokeStyle = color;
      context.stroke();
      // pointer
      angle = startAngle + (endAngle - startAngle) * rate;
      $("#" + pointerId).css(
        "transform",
        "rotate(" + (angle / Math.PI) * 180 + "deg)"
      );
    } else {
      // pointer
      angle = startAngle;
      $("#" + pointerId).css(
        "transform",
        "rotate(" + (angle / Math.PI) * 180 + "deg)"
      );
    }
  }

  _setIndicatorContainer(id, value) {
    $("#" + id).html("");
    let indicatorContainer_plf = $("#" + id)
      .radialIndicator({
        barColor: "#00a9ff",
        barWidth: 11,
        radius: 50,
        barBgColor: "#023979",
        percentage: true,
        fontSize: 16,
        fontColor: "#fff",
        precision: 2,
      })
      .data("radialIndicator");
    indicatorContainer_plf.value(value);
  }

  _drawDelayChart(rate) {
    // chart
    var canvas = document.getElementById("cvs_delay_cause");
    var context = canvas.getContext("2d");
    context.clearRect(0, 0, canvas.width, canvas.height);
    var x = canvas.width / 2;
    var y = canvas.height / 2;

    // draw blue circle
    var radius = 54;
    context.beginPath();
    context.arc(x, y, radius, 0, 2 * Math.PI, false);
    context.lineWidth = 7;
    context.strokeStyle = "#02B0F9";
    context.stroke();

    // draw green arc
    var startAngle = Math.PI - (Math.PI * 2 * rate) / 2;
    var endAngle = Math.PI + (Math.PI * 2 * rate) / 2;
    context.beginPath();
    context.arc(x, y, radius, startAngle, endAngle, false);
    context.lineWidth = 7;
    context.strokeStyle = "#A3D900";
    context.stroke();

    // draw lines
    var numslice = 12;
    for (var i = 0; i < numslice; i++) {
      context.beginPath();
      var startAngle = i * ((Math.PI * 2) / numslice);
      var endAngle = startAngle + Math.PI * 0.01;
      context.arc(x, y, radius, startAngle, endAngle, false);
      context.lineWidth = 8;
      context.strokeStyle = "#041946";
      context.stroke();
    }
  }
  _hideLoad() {
    if ($("#loading_msk").length > 0) {
      hideLoading();
      this._hideLoad();
    }
  }
  _initTime() {
    var date = new Date();
    var mm = date.getMonth() + 1;
    var dd = date.getDate();
    if (mm < 10) {
      mm = "0" + mm;
    }
    if (dd < 10) {
      dd = "0" + dd;
    }
    current_date = date.getFullYear() + "" + mm + dd;
    stdStart = date.getFullYear() + "-" + mm + "-" + dd + " 00:00:00";
    stdEnd = date.getFullYear() + "-" + mm + "-" + dd + " 23:59:59";
    stdEndUtcTime = date.getFullYear() + "-" + mm + "-" + dd + " 15:59:59";
    var yesterday_ts = date.getTime() - 86400000;
    date.setTime(yesterday_ts);
    mm = date.getMonth() + 1;
    dd = date.getDate();
    if (mm < 10) {
      mm = "0" + mm;
    }
    if (dd < 10) {
      dd = "0" + dd;
    }
    stdStartUtcTime = date.getFullYear() + "-" + mm + "-" + dd + " 16:00:00";
  }
  _getQueryString(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]);
    return "";
  }
  _windowOpen(url, target) {
    var a = document.createElement("a");
    a.setAttribute("href", url);
    if (target == null) {
      target = "";
    }
    a.setAttribute("target", target);
    document.body.appendChild(a);
    if (a.click) {
      a.click();
    } else {
      try {
        var evt = document.createEvent("Event");
        a.initEvent("click", true, true);
        a.dispatchEvent(evt);
      } catch (e) {
        window.open(url);
      }
    }
    document.body.removeChild(a);
  }
}
var to = new Today_operation();
deferGeneral.done(function () {
  initSelectCompany();
});
function onCompanyChanged(comp_code) {
  current_company_code = comp_code;
  to ? to.loadAll() : new Today_operation().loadAll();
}
function initSelectCompany() {
  const company = window.location.hash.substr(1);
  if (!company) {
    switchCompany(selected_company_code);
  } else {
    switchCompany(company);
  }
}
function dateFormat(fmt, date) {
  let ret;
  let opt = {
    "Y+": date.getFullYear().toString(), // 年
    "m+": (date.getMonth() + 1).toString(), // 月
    "d+": date.getDate().toString(), // 日
    "H+": date.getHours().toString(), // 时
    "M+": date.getMinutes().toString(), // 分
    "S+": date.getSeconds().toString(), // 秒
    // 有其他格式化字符需求可以继续添加，必须转化成字符串
  };
  for (let k in opt) {
    ret = new RegExp("(" + k + ")").exec(fmt);
    if (ret) {
      fmt = fmt.replace(
        ret[1],
        ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, "0")
      );
    }
  }
  return fmt;
}
