<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>基地今日实时运行</title>
    <!-- <link href="../css/bootstrap.css" rel="stylesheet">
     <link href="../css/bootstrap-theme.css" rel="stylesheet"> -->
     <link href="../css/nprogress.css" rel="stylesheet">
     <script src="../js/nprogress.js"></script>
     <script src="../js/jquery-1.11.1.js"></script>
    <link href="css/common.css?ver=tr2ka23ue" rel="stylesheet">
    <link href="css/baseRunToday.css?ver=tr2ka23ue" rel="stylesheet">
	<script src="../js/babel.min.js"></script>
	<script src="../js/polyfill.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
    <script src="../js/echarts.min.js"></script>
    <script src="../js/echarts-gl.min.js"></script>
    <script src="../js/map/world.js"></script>
    <script src="../js/json.js"></script>
    <script src="../js/util.js"></script>
    <script src="https://cdn.bootcss.com/radialIndicator/1.4.0/radialIndicator.min.js"></script>
</head>
<body>
<iframe id="login" scrolling="no" width="0" height="0" style="display:none;"></iframe>
<!-- 背景 -->

<div class="page-bgimg" style=" -moz-transform-origin:left top;">
</div>
<!-- 背景 -->
<div class="page-wrapper">
    <div class="baseBodyBg">
        <!-- 标题区域 -->
        <div class="baseTitle">

            <!-- 标题区域 left -->
            <div class="baseTitleL">
                <div class="baseTitleL1">
                    <div id="companycombo" code="">
                        <div class="box"></div>
                        <div id="companylist">
                        </div>
                    </div>
                    <div class="baseTitleL1img">
                        <img id="logo"/>
                    </div>
                </div>
                <div class="baseTitleL2">基地实时运行情况</div>
                <div class="baseTitleL3">
                    <div class="combotab limcomb combobox">
                        <span class="combobox_label"></span>
                        <span class="combobox_list" style="max-height: 240px; display: none;">
                        </span>
                    </div>
                </div>
                <div class="baseTitleL4">
                    <div id="btn_go_overall_operation" class="fs14 blue2 btn_go lang" lang="lb_lang">返回公司整体实时运行&nbsp;&nbsp;</div>
                </div>
                <!-- <div class="baseTitleL4">返回公司整体实时运行<span><img src="../general/img/title_03.png" align="bottom" /></span></div> -->
            </div><!-- baseTitleL -->
            <div class="baseTitleC">基地今日实时运行</div><!-- baseTitleC -->
            <!-- 标题区域 left -->

            <!-- 标题区域 right -->
            <div class="baseTitleR">
                <div class="baseTitleRType">
                    <select id="dutyselect">
                    </select>
                </div><!-- baseTitleRType -->
                <div class="baseTitleRName ffnum" id="cnvcDutyname"></div><!-- baseTitleRName -->
                <div class="baseTitleRPhon">
					<!-- <img src="../general/img/base_title_phone.png"/> -->
				</div>
                <!-- baseTitleRPhon -->
                <div class="baseTitleRPhoNum" id="cnvcTel"></div><!-- baseTitleRPhoNum -->
                <div class="baseTitleRMobi"><img src="../general/img/base_title_mobile.png"/></div>
                <!-- baseTitleRMobi -->
                <div class="baseTitleRMobiNum" id="cnvcMobile"></div><!-- baseTitleRMobiNum -->
            </div><!-- baseTitleR -->
            <!-- 标题区域 right -->
        </div><!-- compTitle -->
        <!-- 标题区域 -->

        <!-- 公司运行统计数据的主要内容区域 -->
        <div class="baseMain">
            <div class="baseMainL">
                <div class="baseMainLTi">
                    <div class="normalRate">
                        <div class="label-title">正常率<strong class="line_1">//////</strong>
                            <div class="line_2_80"></div>
                        </div>
                        <div class="label-content">
                            <div class="chart">
                                <canvas id="cvs_shift" width="122" height="122"></canvas>
                                <img id="normalRateNumPoin" class="pointer" src="img/gauge_pointer_102x102.svg"/>
                                <div class="mk" style="left:4px; top:85px; ">0</div>
                                <div class="mk" style="left:-5px;top:23px; ">20</div>
                                <div class="mk" style="left:51px; top:-10px; ">50</div>
                                <div class="mk" style="left: 110px; top:23px; ">75</div>
                                <div class="mk" style="left:108px; top:85px; ">100</div>
                                <div class="lb center">
                                    <span class="val ffnum fs16" id="cvs_shiftJT">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="arrivalAtPort ">
                        <div class="label-title">进港正常率<strong class="line_1">//////</strong>
                            <div class="line_2_50"></div>
                        </div>
                        <div class="label-content">
                            <div class="chart">
                                <canvas id="cvs_shift2" width="122" height="122"></canvas>
                                <img id="normalRateNumPoin2" class="pointer" src="img/gauge_pointer_102x102.svg"/>
                                <div class="mk" style="left:4px; top:85px; ">0</div>
                                <div class="mk" style="left:-5px;top:23px; ">20</div>
                                <div class="mk" style="left:51px; top:-10px; ">50</div>
                                <div class="mk" style="left: 110px; top:23px; ">75</div>
                                <div class="mk" style="left:108px; top:85px; ">100</div>
                                <div class="lb center">
                                    <span class="val ffnum fs16" id="cvs_shiftJT2">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="clearAPort">
                        <div class="label-title">出港正常率<strong class="line_1">//////</strong>
                            <div class="line_2_50"></div>
                        </div>
                        <div class="label-content">
                            <div class="chart">
                                <canvas id="cvs_shift3" width="122" height="122"></canvas>
                                <img id="normalRateNumPoin3" class="pointer" src="img/gauge_pointer_102x102.svg"/>
                                <div class="mk" style="left:4px; top:85px; ">0</div>
                                <div class="mk" style="left:-5px;top:23px; ">20</div>
                                <div class="mk" style="left:51px; top:-10px; ">50</div>
                                <div class="mk" style="left: 110px; top:23px; ">75</div>
                                <div class="mk" style="left:108px; top:85px; ">100</div>
                                <div class="lb center">
                                    <span class="val ffnum fs16" id="cvs_shiftJT3">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div><!-- baseMainLTi -->

                <div class="baseMainLCe">
                    <div class="baseMainLCeL">
                        <div class="label-title">当日航班量<strong class="line_1">//////</strong>
                            <div class="line_2_210"></div>
                        </div>
                        <div class="label-content">
                            <div class="todayJG">
                                <div class="todayJGTi">进</br>港</div>
                                <div class="todayJGMain">
                                    <div class="todayJGMainTop">
                                        <div class="todayJGMainTop1"><span class="fs26 ffnum"
                                                                           id="flt_sch_arr"></span><span
                                                class="fs13 ffnum">架次</span></div>
                                        <div class="todayJGMainTop2"><span class="fs12 blue2">已执行</span></div>
                                        <div class="todayJGMainTop3"><span class="fs17 ffnum"
                                                                           id="flt_exc_arr"></span><span
                                                class="fs12 ffnum">架次</span></div>
                                    </div>

                                    <div class="todayJGMainBottom">
                                        <div id="flt_bar_arrstns" class="thinbluebarBa" style="width:100%; height:7px;">
                                            <div class="insidebarBa" style="width:0px;"></div>
                                            <div class="dotBa" style="left:0px;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="todayCG">
                                <div class="todayCGTi">出</br>港</div>
                                <div class="todayCGMain">
                                    <div class="todayJGMainTop">
                                        <div class="todayJGMainTop1"><span class="fs26 ffnum"
                                                                           id="flt_sch_dep"></span><span
                                                class="fs13 ffnum">架次</span></div>
                                        <div class="todayJGMainTop2"><span class="fs12 blue2">已执行</span></div>
                                        <div class="todayJGMainTop3"><span class="fs17 ffnum"
                                                                           id="flt_exc_dep"></span><span
                                                class="fs12 ffnum">架次</span></div>
                                    </div>

                                    <div class="todayCGMainBottom">
                                        <div id="flt_bar_depstns" class="thinbluebarBa" style="width:100%; height:7px;">
                                            <div class="insidebarBa" style="width:0px;"></div>
                                            <div class="dotBa" style="left:0px;"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="CGMaimBott">
                                    <div class="CGOriginat">
                                        <ul>
                                            <li class="blue2">始发航班量</li>
                                            <li><span id="baseSFHBL" class="fs17 ffnum">0</span><span
                                                    class="fs13 ffnum">架次</span></li>
                                            <li><span class="blue2">已执行</span> <span class="fs13 ffnum" id="baseSFHBLYZX">0</span><span
                                                    class="fs13 ffnum">架次</span></li>
                                        </ul>
                                    </div>
                                    <div class="CGInland">
                                        <ul>
                                            <li class="blue2">国内航班量</li>
                                            <li><span id="flt_sch_dep_l" class="fs17 ffnum">0</span><span class="fs13 ffnum">架次</span>
                                            </li>
                                            <li><span class="blue2">已执行</span> <span class="fs13 ffnum"
                                                                                     id="flt_exc_dep_l">0</span><span
                                                    class="fs13 ffnum">架次</span></li>
                                        </ul>
                                    </div>
                                    <div class="CGInte">
                                        <ul>
                                            <li class="blue2">国际航班量</li>
                                            <li><span id="flt_sch_dep_i" class="fs17 ffnum"></span><span
                                                    class="fs13 ffnum">架次</span></li>
                                            <li><span class="blue2">已执行</span> <span class="fs13 ffnum"
                                                                                     id="flt_exc_dep_i">0</span><span
                                                    class="fs13 ffnum">架次</span></li>
                                        </ul>
                                    </div>
                                </div>

                            </div>
                        </div>

                    </div>
                    <div class="baseMainLCeR">
                        <!-- <div class="baseMainLCeRTI"></div> -->
                        <div class="label-title">当日计划旅客量<strong class="line_1">//////</strong>
                            <div class="line_2_50"></div>
                        </div>

                        <div class="baseMainLCeRTop">
                            <div class="baseMainLCeRTop1">
                                <div class="ffnum fs16" style="top: 37px;left: 32px">进港</div>
                                <canvas id="cvs_trv1" width="100" height="100"></canvas>
                            </div>
                            <div class="baseMainLCeRTop2">
                                <ul>
                                    <li style="color: #01a2f5;">总量</li>
                                    <li class="ffnum"><span id="bookNum_in" class="fs16"></span><span
                                            style="font-size: 12px;">人</span></li>
                                    <li style="color: #01a2f5;">已完成</li>
                                    <li class="ffnum"><span id="ckiNum_in"></span><span
                                            style="font-size: 12px;">人</span></li>
                                </ul>
                            </div>
                        </div>
                        <div class="baseMainLCeRBott">
                            <div class="baseMainLCeRBott1">
                                <div class="ffnum fs16" style="top: 37px;left: 32px">出港</div>
                                <canvas id="cvs_trv3" width="100" height="100"></canvas>
                            </div>
                            <div class="baseMainLCeRBott2">
                                <ul>
                                    <li style="color: #01a2f5;">总量</li>
                                    <li class="ffnum"><span id="bookNum_out" class="fs16"></span><span
                                            style="font-size: 12px;">人</span></li>
                                    <li style="color: #01a2f5;">已完成</li>
                                    <li class="ffnum"><span id="ckiNum_out"></span><span
                                            style="font-size: 12px;">人</span></li>
                                </ul>
                            </div>
                        </div>

                    </div>
                </div><!-- baseMainLCe -->

                <div class="baseMainLBo">
                    <div class="w100">
                        <div class="label-title">不正常航班一览<strong class="line_1">//////</strong>
                            <div class="line_2_400"></div>
                        </div>
                        <div class="label-content">
                            <div class="w20 unnormal_plan">
                                <div class="ffnum fs12 w100">延误1-2h</div>
                                <div class="ffnum w100" style="top:30px" id="flt_delay_12"></div>
                            </div>
                            <div class="w20 unnormal_plan" style="left: 19%">
                                <div class="ffnum fs12 w100">延误2-4h</div>
                                <div class="ffnum w100" style="top:30px" id="flt_delay_24"></div>
                            </div>
                            <div class="w20 unnormal_plan" style="left: 39%">
                                <div class="ffnum fs12 w100">延误>4h</div>
                                <div class="ffnum w100" style="top:30px" id="flt_delay_4"></div>
                            </div>
                            <div class="w20 unnormal_plan" style="left: 59%">
                                <div class="ffnum fs12 w100">返航备降</div>
                                <div class="ffnum w100" style="top:30px" id="flt_return_back"></div>
                            </div>
                            <div class="w20 unnormal_plan" style="left: 79%">
                                <div class="ffnum fs12 w100">取消航班</div>
                                <div class="ffnum w100" style="top:30px" id="flt_cancel"></div>
                            </div>
                        </div>
                    </div>
                </div><!-- baseMainLBo -->
                <div class="w50 arp_flight">
                    <div class="label-title">基地过夜飞机<strong class="line_1">//////</strong>
                        <div class="line_2_125"></div>
                    </div>
                    <div class="label-content">
                        <canvas id="arp_flight_rate" width="150" height="150" style="top: 10px;left: 65px"></canvas>
                        <div class="arp_flight_rate_bg"></div>
                        <div class="actually_arrow"></div>
                        <div class="plan_arrow"></div>
                        <div class="actually">
                            <div class="actually_label blue2">实际</div>
                            <div class="actually_content ffnum"><span class="fs20"
                                                                      id="base_over_night_plane_num">--</span>架
                            </div>
                        </div>
                        <div class="plan">
                            <div class="plan_label blue2">计划</div>
                            <div class="plan_content ffnum"><span class="fs20" id="base_over_night_plane_num2">--</span>架
                            </div>
                        </div>
                        <div class="arp_flight_plan1">
                            <div class="arp_flight_plan1_lebel blue2" id="arp_flight_plan1_lebel"></div>
                            <div class="scheduleX" id="arp_flight_plan1"></div>
                            <div class="arp_flight_plan1_content ffnum" id="arp_flight_plan1_content"></div>
                        </div>
                        <div class="arp_flight_plan2">
                            <div class="arp_flight_plan2_lebel blue2" id="arp_flight_plan2_lebel"></div>
                            <div class="scheduleX" id="arp_flight_plan2"></div>
                            <div class="arp_flight_plan2_content ffnum" id="arp_flight_plan2_content"></div>
                        </div>
                        <div class="arp_flight_plan3">
                            <div class="arp_flight_plan3_lebel blue2" id="arp_flight_plan3_lebel"></div>
                            <div class="scheduleX" id="arp_flight_plan3"></div>
                            <div class="arp_flight_plan3_content ffnum" id="arp_flight_plan3_content"></div>
                        </div>
                        <div class="arp_flight_plan4">
                            <div class="arp_flight_plan4_lebel blue2" id="arp_flight_plan4_lebel"></div>
                            <div class="scheduleX" id="arp_flight_plan4"></div>
                            <div class="arp_flight_plan4_content ffnum" id="arp_flight_plan4_content"></div>
                        </div>
                    </div>
                </div>
                <div class="w50 delay_case">
                    <div class="label-title">今日延误原因分析<strong class="line_1">//////</strong>
                        <div class="line_2_95"></div>
                    </div>
                    <div class="label-content">
                        <div class="w100">
                            <div class="delay_case_rate">
                                <div class="vs"></div>
                                <canvas id="delay_case_rate" width="100" height="100" style="left: 80px"></canvas>
                                <div class="comp_case">
                                    <div class="comp_case_label">
                                        公司原因
                                    </div>
                                    <div class="comp_case_content">
									<span id="per_delay_cause_comp" class="ffnum fs20">
									</span>
                                        %
                                    </div>
                                </div>
                                <div class="uncomp_case">
                                    <div class="uncomp_case_label blue2">
                                        非公司原因
                                    </div>
                                    <div class="uncomp_case_content">
									<span id="per_delay_cause_none" class="ffnum fs20">
									</span>
                                        %
                                    </div>
                                </div>
                            </div>
                            <div class="baseMainLBoRBott">
                                <div class="buttReso">
                                    <div id="buttResoSelenon1" class="buttResoSelenon bg1"
                                         onclick="buttReso('comResson')">公司原因
                                    </div>
                                    <div id="buttResoSelenon2" class="buttResoSelenon2 bg2"
                                         onclick="buttReso('comRessonNon')">非公司原因
                                    </div>
                                </div>
                                <div class="buttResoSele" id="buttResoSele">
                                </div>
                            </div><!-- baseMainLBoRBott -->
                        </div><!-- baseMainLBoR -->
                    </div>
                </div>

            </div><!-- baseMainL -->

            <div class="baseMainC">
                <div class="baseMainCTo">
                    <div class="baseMainCToL">
                        <div class="label-title">目前执行中航班</div>
                        <div class="label-content">
                            <div class="baseMainCToLInfos">
                                <div class="baseMainCToLInfos1"></div>
                                <div class="baseMainCToLInfos2">正常</div>
                                <div class="baseMainCToLInfos3"></div>
                                <div class="baseMainCToLInfos4">延误</div>
                            </div>
                        </div>
                    </div>
                    <div class="baseMainCToR">
                        <!-- 天气 -->
                        <div class="weatherTitle">
                            <div class="weatherTitle1" id="airportName"></div>
                            <div class="weatherTitle2"><span id="base_weather_ico"></span></div>
                            <div class="weatherTitle3" id="weatherInfoTxt"></div>
                            <div class="weatherTitle4" id="temperature"></div>
                        </div>
                        <div class="weatherMain">
                            <div class="weatherMain1">
                                <ul>
                                    <li style="color: #00a9ff;">能见度</li>
                                    <li id="visibility"></li>
                                    <li style="color: #00a9ff;">云况</li>
                                    <li id="cloudInfo"></li>
                                </ul>
                            </div>
                            <div class="weatherMain2">
                                <ul>
                                    <li style="color: #00a9ff;">风速</li>
                                    <li id="windFs"></li>
                                    <li style="color: #00a9ff;">天气告警</li>
                                    <li></li>
                                </ul>
                            </div>
                            <div class="weatherMain3">
                                <ul>
                                    <li style="color: #00a9ff;">风向</li>
                                    <li id="windFx"></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div><!-- baseMainCTo -->
                <div class="baseMainCCe">
                    <div class="earth3Ds">
                        <div id="earth3d" style="width:680px; height: 680px;pointer-events: auto;"></div>
                        <div id="map" style="width:680px; height: 523px;top:75px;left:5px;pointer-events: auto;border: 1px solid rgba(5,96,173,0.88); display: none;"></div>
                    </div>
                </div>

                <div class="baseMainCBott">

                    <div class="baseMainCBottL">
                        <div class="label-title" id="lb_switch_map">查看具体航班详情</div>
                        <div class="label-content">
                            <div class="baseMainCBottL2">
                                <div class="baseMainCBottL2Img" id="btn_switch_map"></div>
                                <div class="baseMainCBottL2Butt searchform" style="display: none;">
                                    <div class="placOfDepa">
                                        <span style="font-size: 12px; color: #029cee;">出发地</span>
                                        <span style="margin-left: 10px;">
                                            <input type="text" class="placOfDepaCf"  id="ipt_city_from"  name="ipt_city_from"/>
                                        </span>
                                    </div>
                                    <div class="placOfDepa" style="top: 32px;">
                                        <span style="font-size: 12px; color: #029cee;">目的地</span><span
                                            style="margin-left: 15px;"><input type="text" class="placOfDepaCf"
                                                                              id="ipt_city_to"
                                                                              name="ipt_city_to"/></span>
                                    </div>
                                    <div class="placOfDepa" style="top: 63px;">
                                        <span style="font-size: 12px; color: #029cee;">航班号</span>
                                        <span style="margin-left: 10px;">
                                            <input type="text" class="placOfDepaCf"  id="ipt_fltno"  name="ipt_fltno"/>
                                            <div class="dropdown_arrow"></div>
                                        </span>
                                    </div>
                                    <div>
                                        <div class="placOfDepa" style="top: 95px;">
									<span style="margin-left: 50px;">
										<button class="placOfDepaCf" id="buttTj" name="destination"
                                                type="button">前往航班</button>
									</span>
                                        </div>
                                    </div>
                                    <div class="error blue_l">注意：目前没有执行此段航线的任务</div>
                                    <div id="flt_dropdown_list">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="baseMainCBottR">
                        <div class="label-title">正在执行航班</div>
                        <div class="label-content">
                            <div class="baseMainCBottRJG">
                                <div class="baseMainCBottRJG1 ffnum"><span style="font-size: 14px;">进港</span></div>
                                <div class="baseMainCBottRJG2 ffnum fs22" id="zxjg1">0</div>
                                <div class="baseMainCBottRJG3 ffnum fs22" id="zxjg2">0</div>
                                <div class="baseMainCBottRJG4 ffnum">
                                    <div style="width:100%;height: 14px;line-height: 14px;bottom: 0px;">班次</div>
                                </div>
                            </div>

                            <div class="baseMainCBottRCG">
                                <div class="baseMainCBottRJG1 ffnum"><span style="font-size: 14px;">出港</span></div>
                                <div class="baseMainCBottRJG2 ffnum fs22" id="zxcg1">0</div>
                                <div class="baseMainCBottRJG3 ffnum fs22" id="zxcg2">0</div>
                                <div class="baseMainCBottRJG4 ffnum">
                                    <div style="width:100%;height: 14px;line-height: 14px;bottom: 0px;">班次</div>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>

            </div><!-- baseMainC -->

            <div class="baseMainR">
                <div class="baseMainR1">
                    <div class="label-title">机场跑道观测<strong class="line_1">//////</strong>
                        <div class="line_2_400"></div>
                    </div>
                    <div class="label-content">
                        <div class="baseMainR1Main">
                            <div class="baseMainR1Main1">
                                <div class="basepdbhW">
                                    <div class="basepdbh">跑道编号</div>
                                </div>
                                <div class="base18Land36RW">
                                    <div class="base18Land36R">
                                        <div class="base18LW">
                                            <div class="base18L" id="runwaynamea1"></div>
                                        </div>
                                        <div class="base36R" id="runwaynameb1"></div>
                                    </div>
                                </div>
                                <div class="base18Rand36LW">
                                    <div class="base18Rand36L">
                                        <div class="base18RW">
                                            <div class="base18R" id="runwaynamea2"></div>
                                        </div>
                                        <div class="base36L" id="runwaynameb2"></div>
                                    </div>
                                </div>
                                <div class="base19and01">
                                    <div class="base18RW">
                                        <div class="base19" id="runwaynamea3"></div>
                                    </div>
                                    <div class="base01" id="runwaynameb3"></div>
                                </div>
                            </div>

                            <div class="baseMainR1Main2">
                                <div class="basepdbhW">
                                    <div class="basepdbh">RVR观测</div>
                                </div>
                                <div class="base18Land36RW">
                                    <div class="base18Land36R"><span id="rvrtdz1"></span>/<span id="rvrmid1"></span>/<span id="rvrend1"></span></div>
                                </div>
                                <div class="base18Rand36LW">
                                    <div class="base18Rand36L"><span id="rvrtdz2"></span>/<span id="rvrmid2"></span>/<span id="rvrend2"></span></div>
                                </div>
                                <div class="base19and01"><span id="rvrtdz3"></span>/<span id="rvrmid3"></span>/<span id="rvrend3"></span></div>
                            </div>

                            <div class="baseMainR1Main3">
                                <div class="basepdbhW">
                                    <div class="basepdbh">跑道能见度</div>
                                </div>
                                <div class="base18Land36RW">
                                    <div class="base18Land36R">
                                        <div class="base18LW">
                                            <div class="base18L" id="visibilitya1"></div>
                                        </div>
                                        <div class="base36R" id="visibilityb1"></div>
                                    </div>
                                </div>
                                <div class="base18Rand36LW">
                                    <div class="base18Land36R">
                                        <div class="base18LW">
                                            <div class="base18L" id="visibilitya2"></div>
                                        </div>
                                        <div class="base36R" id="visibilityb2"></div>
                                    </div>
                                </div>
                                <div class="base19and01">
                                    <div class="base18RW">
                                        <div class="base19" id="visibilitya3"></div>
                                    </div>
                                    <div class="base01" id="visibilityb3"></div>
                                </div>
                            </div>

                            <div class="baseMainR1Main4">
                                <div class="basepdbhW">
                                    <div class="basepdbh">10分钟风向/风速</div>
                                </div>
                                <div class="base18Land36RW">
                                    <div class="base18Land36R">
                                        <div class="base18LW">
                                            <div class="base18L"><span id="tenwindwaya1">--</span>m/s<br><span id="tenwindspeeda1">--</span>°</div>
                                        </div>
                                        <div class="base36R"><span id="tenwindwayb1">--</span>m/s<br><span id="tenwindspeedb1">--</span>°</div>
                                    </div>
                                </div>
                                <div class="base18Rand36LW">
                                    <div class="base18Land36R">
                                        <div class="base18LW">
                                            <div class="base18L"><span id="tenwindwaya2">--</span>m/s<br><span id="tenwindspeeda2">--</span>°</div>
                                        </div>
                                        <div class="base36R"><span id="tenwindwayb2">--</span>m/s<br><span id="tenwindspeedb2">--</span>°</div>
                                    </div>
                                </div>
                                <div class="base19and01">
                                    <div class="base18RW">
                                        <div class="base19"><span id="tenwindwaya3">--</span>m/s<br><span id="tenwindspeeda3">--</span>°</div>
                                    </div>
                                    <div class="base01"><span id="tenwindwayb3">--</span>m/s<br><span id="tenwindspeedb3">--</span>°</div>
                                </div>
                            </div>

                            <div class="baseMainR1Main5">
                                <div class="basepdbhW">
                                    <div class="basepdbh">云底高</div>
                                </div>
                                <div class="base18Land36RW">
                                    <div class="base18Land36R">
                                        <div class="base18LW">
                                            <div class="base18L" id="cloudheighta1"></div>
                                        </div>
                                        <div class="base36R" id="cloudheightb1"></div>
                                    </div>
                                </div>
                                <div class="base18Rand36LW">
                                    <div class="base18Land36R">
                                        <div class="base18LW">
                                            <div class="base18L" id="cloudheighta2"></div>
                                        </div>
                                        <div class="base36R" id="cloudheightb2"></div>
                                    </div>
                                </div>
                                <div class="base19and01">
                                    <div class="base18RW">
                                        <div class="base19" id="cloudheighta3"></div>
                                    </div>
                                    <div class="base01" id="cloudheightb3"></div>
                                </div>
                            </div>

                            <div class="baseMainR1Main6">
                                <div class="basepdbhW">
                                    <div class="basepdbh">天气现象</div>
                                </div>
                                <div class="base18Land36RW">
                                    <div class="base18Land36R weatherInfo"></div>
                                </div>
                                <div class="base18Rand36LW">
                                    <div class="base18Rand36L weatherInfo"></div>
                                </div>
                                <div class="base19and01 weatherInfo"></div>
                            </div>

                            <div class="baseMainR1Main7">
                                <div class="basepdbhW">
                                    <div class="basepdbh">温度/露点</div>
                                </div>
                                <div class="base18Land36RW">
                                    <div class="base18Land36R"><span id="temperature1">--</span>℃/<span id="dewpoint1">--</span>℃</div>
                                </div>
                                <div class="base18Rand36LW">
                                    <div class="base18Rand36L"><span id="temperature2">--</span>℃/<span id="dewpoint2">--</span>℃</div>
                                </div>
                                <div class="base19and01"><span id="temperature3">--</span>℃/<span id="dewpoint3">--</span>℃</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="baseMainR2">
                    <div class="label-title">近两小时进出港航班<strong class="line_1">//////</strong>
                        <div class="line_2_365"></div>
                    </div>
                    <div class="label-content">
                        <div class="baseMainR2Butt">
                            <div class="baseMainR2Butt1 seleCJG" onclick="set2hourInOrOutFltStatus('baseMainR2Butt1')">
                                <div class="baseMainR2Butt1all">
                                    <div class="baseMainR2Butt1Img"></div>
                                    <div class="baseMainR2Butt1R">进港航班</div>
                                </div>
                            </div>
                            <div class="baseMainR2Butt2" onclick="set2hourInOrOutFltStatus('baseMainR2Butt2')">
                                <div class="baseMainR2Butt1all">
                                    <div class="baseMainR2Butt2Img"></div>
                                    <div class="baseMainR2Butt1R">出港航班</div>
                                </div>
                            </div>
                        </div>
                        <div class="baseMainR2Main">
                            <div id="baseMainR2Main1" class="baseMainR2Main1">
                            </div>

                            <div id="baseMainR2MainAll">
                                <div style="width: 100%;height: 100%;text-align: center;line-height: 100px;vertical-align: middle;font-size: 12px;color: #028cd5;">
                                    正在获取数据
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="baseMainR3">
                    <div class="label-title">当日中转紧张<strong class="line_1">//////</strong>
                        <div class="line_2_400"></div>
                    </div>
                    <div class="label-content">
                        <div class="baseMainR3Titl">
                            <div class="baseMainR3Titl1">进港航班</div>
                            <div class="baseMainR3Titl2">预计落地</div>
                            <div class="baseMainR3Titl3">人数</div>
                            <div class="baseMainR3Titl4">出港航班</div>
                            <div class="baseMainR3Titl5">预计起飞</div>
                            <div class="baseMainR3Titl6">中转时间(m)</div>
                            <div class="baseMainR3Titl7">中转剩余(m)</div>
                        </div>
                        <div class="baseMainR3Main" id="baseMainR3Main"
                             style="width: 100%; font-size: 12px;color: #028cd5;">
                        </div>
                    </div>
                </div>
            </div><!-- baseMainR -->
        </div><!-- compMain -->
        <!-- 公司运行统计数据的主要内容区域 -->
    </div><!-- compBodyBg -->
</div>

<script src="js/config.js?ver=1"></script>
<script src="js/common.js?ver=1"></script>
<script type="text/babel" src="js/baseRunToday.js?ver=1"></script>
</body>
</html>