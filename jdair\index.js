showLoading();

var selectedNodeIndex = 0,
  selectedNodeName;
var comp_code = 'JD';
var comp_id = '100700';
var comp_name = '首都航空';


var BASE_CITY_LIST = {
  "PKX": '北京',
  "HGH": '杭州',
  "SYX": '三亚',
  "HAK": '海口',
  "XIY": '西安',
  "CAN": '广州',
  "LJG": '丽江',
  "TAO": '青岛',
  "SHE": '沈阳',
  "NKG": '南京',
  "CKG": '重庆'
};


// 区域名称
var AREA_LIST = {
  '华北': '100001',
  '华东': '100002',
  '东北': '100003',
  '西南': '100004',
  '中南': '100005',
  '西北': '100006',
  '新疆': '100007',
};

var comp_cause = ['飞机故障', '运力调配', '工程机务', '航班计划', '航材保障', '航务保障', '机组保障', '飞行机组保障', '乘务组', '乘务组保障', '空警安全员', '地面保障', '货运保障', '公司原因', '其他航空公司原因'];
var none_cause = ['公共安全', '机场', '军事活动', '空管', '离港系统', '联检', '旅客', '民航局航班时刻安排', '天气原因', '油料'];


var area_arps;
var arp_code_list;
var arp_detail_list;
var all_flight_list;
var timeout_next_flt;


// 所有飞机架数
var total_plane = -1;
// 执行中飞机架数
var exe_total_plane = -1;
// 机型对照表
var actypeMapList;
var ac_type_list = ['787', '767', '330', '737', '320', '321', '319', '145', '190'];
var ac_data_list_ready;
var ac_data_list;
var exe_ac_data_list;
var marquee_itv_airPlane;

//-------------------------


function loadAll() {

  var date = new Date();
  var mm = date.getMonth() + 1;
  var dd = date.getDate();
  if (mm < 10) {
    mm = '0' + mm;
  }
  if (dd < 10) {
    dd = '0' + dd;
  }

  var stdEndUtcTime = date.getFullYear() + '-' + mm + '-' + dd + ' 15:59:59';
  var yesterday_ts = date.getTime() - 86400000;
  date.setTime(yesterday_ts);
  mm = date.getMonth() + 1;
  dd = date.getDate();
  if (mm < 10) {
    mm = '0' + mm;
  }
  if (dd < 10) {
    dd = '0' + dd;
  }
  var stdStartUtcTime = date.getFullYear() + '-' + mm + '-' + dd + ' 16:00:00';



  // ------------------------------------------------------------------------
  // 航班列表
  // ------------------------------------------------------------------------

  // 所有公司的航班

  var all_fltnos = [];

  // 开始结束时间
  var date = new Date();
  var mm = date.getMonth() + 1;
  var dd = date.getDate();
  if (mm < 10) {
    mm = '0' + mm;
  }
  if (dd < 10) {
    dd = '0' + dd;
  }
  var stdStart = date.getFullYear() + '-' + mm + '-' + dd + ' 00:00:00';
  var stdEnd = date.getFullYear() + '-' + mm + '-' + dd + ' 23:59:59';


  var statusMap = {
    'ARR': '落地',
    'NDR': '落地',
    'ATD': '推出',
    'ATA': '到达',
    'CNL': '取消',
    'DEL': '延误',
    'DEP': '起飞',
    'RTR': '返航',
    'SCH': '计划'
  };

  var param = {
    "stdStart": stdStart,
    "stdEnd": stdEnd,
    "acOwner": comp_code,
    "statusList": '',
  }
  $.ajax({
    type: 'post',
    url: "/bi/web/getStandardFocFlightInfo",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      all_flight_list = response.data;
    },
    error: function () { }
  });



  // ------------------------------------------------------------------------
  // 欢迎词
  // ------------------------------------------------------------------------

  var param = {
    'mode': 'query'
  }

  $.ajax({
    type: 'post',
    url: "/bi/web/jd_welcome",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {

      checkLogin(response);

      if (response.title != undefined) {
        var msg = response.title[0].txt;
        // 
        if (msg.length > 30) {
          $('#welcome_msg').html('<marquee direction="left" scrollamount="10">' + msg + '</marquee>');
        } else {
          $('#welcome_msg').text(msg);
        }
      }

    },
    error: function (jqXHR, txtStatus, errorThrown) {
      console.log('----error');
    }
  });


  // ------------------------------------------------------------------------
  // 值班人员
  // ------------------------------------------------------------------------
  /*
    var param = {
    'mode': 'query'
    }

    $.ajax({
        type: 'post',
        url:"/bi/web/jd_duty",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            if(response.title != undefined){
              var msg = response.title[0].txt;
              // 
                $('#duty_name').text(msg);
            }
            
        },
        error:function(jqXHR, txtStatus, errorThrown) {
          console.log('----error');
        }
    });
  */



  var date = new Date();
  var mm = date.getMonth() + 1;
  var dd = date.getDate();
  if (mm < 10) {
    mm = '0' + mm;
  }
  if (dd < 10) {
    dd = '0' + dd;
  }
  var endDate = date.getFullYear() + '-' + mm + '-' + dd; //+' 23:59:59';
  var yesterday_ts = date.getTime() - 86400000;
  date.setTime(yesterday_ts);
  mm = date.getMonth() + 1;
  dd = date.getDate();
  if (mm < 10) {
    mm = '0' + mm;
  }
  if (dd < 10) {
    dd = '0' + dd;
  }
  var startDate = date.getFullYear() + '-' + mm + '-' + dd; //+' 00:00:00';
  var param = {
    'cniTypeId': 18, // 9 首都航空值班
    'cnvcClassContent': '334', //124 01值班
    //'cnvcCompanyId': '4477',
    'cnvcStartDateStart': startDate,
    'cnvcStartDateEnd': endDate,
    //'cnvcEndDate': endDate,
  }

  $.ajax({
    type: 'post',
    url: "/bi/web/getGetAPDutyInfosByPage",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      if (response.data && response.data.length > 0) {
        $('#duty_name').text(response.data[0].cnvcDutyname);
      }

    },
    error: function () { }
  });


  // ------------------------------------------------------------------------
  // 正常率颜色配置
  // ------------------------------------------------------------------------

  var normal_rate_colors = {};
  var param = {
    'mode': 'query'
  }

  $.ajax({
    type: 'post',
    url: "/bi/web/7x2_normal_rate_color",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      normal_rate_colors = response.ratecolor[0];

    },
    error: function () { }
  });



  // ------------------------------------------------------------------------
  // 年度正常率
  // ------------------------------------------------------------------------
  var param = {
    "SOLR_CODE": "FAC_COMP_KPI",
    "COMP_CODE": comp_code,
    "KPI_CODE": "NORMAL_RATE_ZT_MOLECULE,NORMAL_RATE_ZT_DENOMINATOR",
    "VALUE_TYPE": "kpi_value_d",
    "OPTIMIZE": 1,
    "DATE_TYPE": "M",
    "LIMIT": 12
  }

  $.ajax({

    type: 'post',
    url: "/bi/query/getkpi",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {

      var mdat_nor = response.data[comp_code]['NORMAL_RATE_ZT_MOLECULE']['M'];
      var mdat_sch = response.data[comp_code]['NORMAL_RATE_ZT_DENOMINATOR']['M'];

      //var m_nor = 0;
      //var m_sch = 0;
      var y_nor = 0;
      var y_sch = 0;
      var date = 0;

      var d = new Date();
      var thisyear = d.getFullYear() + '00'

      for (var time in mdat_nor) {
        var val = Number(mdat_nor[time]);
        var val2 = Number(mdat_sch[time]);
        if (val > 0 && val2 > 0) {
          //if(Number(time) > date){
          //  m_nor = val;
          //  m_sch = val2;
          //  date = Number(time);
          //}
          if (Number(time) > thisyear) {
            y_nor += val;
            y_sch += val2;
          }
        }
      }

      //if(m_nor > 0 && m_sch > 0){
      //  $('#normal_rate_month').text(Math.round((m_nor/m_sch)*1000)/10);
      //}else{
      //  $('#normal_rate_month').text('NA');
      //}

      if (y_nor > 0 && y_sch > 0) {
        $('#normal_rate_year').text(Math.round((y_nor / y_sch) * 1000) / 10);
      } else {
        $('#normal_rate_year').text('NA');
      }

    },
    error: function () { }
  });


  // ------------------------------------------------------------------------
  // 月度正常率
  // ------------------------------------------------------------------------
  var ddd = new Date();
  var limit_d = ddd.getDate();
  var param = {
    "SOLR_CODE": "FAC_COMP_KPI",
    "COMP_CODE": comp_code,
    "KPI_CODE": "NORMAL_RATE_ZT_MOLECULE,NORMAL_RATE_ZT_DENOMINATOR",
    "VALUE_TYPE": "kpi_value_d",
    "DATE_TYPE": "D",
    "OPTIMIZE": 1,
    "LIMIT": limit_d
  }

  $.ajax({

    type: 'post',
    url: "/bi/query/getkpi",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {

      var mdat_nor = response.data[comp_code]['NORMAL_RATE_ZT_MOLECULE']['D'];
      var mdat_sch = response.data[comp_code]['NORMAL_RATE_ZT_DENOMINATOR']['D'];

      var m_nor = 0;
      var m_sch = 0;

      var date = new Date();
      var month = date.getMonth() + 1;
      var day = date.getDate();
      if (month < 10) {
        month = '0' + month;
      }
      if (day < 10) {
        day = '0' + day;
      }
      var today = date.getFullYear() + '' + month + '' + day;

      for (var time in mdat_nor) {
        var val = Number(mdat_nor[time]);
        var val2 = Number(mdat_sch[time]);
        if (val > 0 && val2 > 0) {
          if (Number(time) < Number(today) && limit_d > 1 || limit_d == 1) {
            m_nor += val;
            m_sch += val2;
          }
        }
      }

      if (m_nor > 0 && m_sch > 0) {
        $('#normal_rate_month').text(Math.round((m_nor / m_sch) * 1000) / 10);
      } else {
        $('#normal_rate_month').text('NA');
      }

    },
    error: function () { }
  });



  // ------------------------------------------------------------------------
  // 各种航班统计信息。。。。
  // ------------------------------------------------------------------------
  var plane_over_air = -1;

  var param = {
    "stdStartUtcTime": stdStartUtcTime,
    "stdEndUtcTime": stdEndUtcTime,
    "companyCodes": comp_code,
    "showMore": 1
  }

  $.ajax({
    type: 'post',
    url: "/bi/spring/focStaticApi/flightAmountStaticV2",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (res) {
      var response = res.data;
      var sch_total = Number(response.pftc); //计划航班总数
      var sch_normal = Number(response.pfrtc); //计划航班中正常航班总数
      var pfdappPercent = Number(response.pfdappPercent);
      todayNormalRate(pfdappPercent);

      var dftc = response.dftc; //备降航班总数
      var bftc = response.bftc; //返航航班总数
      var qftc = response.qftc; //取消航班总数
      var qftc1 = response.qftc1; //取消航班总数

      $('#flt_return_back_cancel').text(Number(dftc) + Number(bftc) + Number(qftc1));
      $('#flt_return_back').text(Number(dftc) + Number(bftc));
      $('#flt_cancel').text(qftc1);

      var pfdtc = response.pfdtc; //计划航班中延误航班总数
      $('#flt_delay_total').text(Number(pfdtc));

      var pfdtc1 = response.pfdtc1; //计划航班中延误1小时内的航班总数
      var ffdtc1 = response.ffdtc1; //执行航班中延误1小时内的航班总数
      var rfdtc1 = response.pfdtc1; //未执行航班中延误1小时内的航班总数
      var cfdtc1 = response.cfdtc1; //已执行航班中延误1小时内的航班总数
      $('#flt_delay_1').text(Number(pfdtc1));

      var pfdtc12 = response.pfdtc12; //计划航班中延误1~2小时的航班总数
      var ffdtc12 = response.ffdtc12; //执行航班中延误1~2小时的航班总数
      var rfdtc12 = response.pfdtc12; //未执行航班中延误1~2小时的航班总数
      var cfdtc12 = response.cfdtc12; //已执行航班中延误1~2小时的航班总数
      $('#flt_delay_12').text(Number(pfdtc12));

      var pfdtc24 = response.pfdtc24; //计划航班中延误2-4小时的航班总数
      var ffdtc24 = response.ffdtc24; //执行航班中延误2-4小时的航班总数
      var rfdtc24 = response.pfdtc24; //未执行航班中延误2-4小时的航班总数
      var cfdtc24 = response.cfdtc24; //已执行航班中延误2-4小时的航班总数
      $('#flt_delay_24').text(Number(pfdtc24));

      var pfdtc4 = response.pfdtc4; //计划航班中延误>4小时的航班总数
      var ffdtc4 = response.ffdtc4; //执行航班中延误>4小时的航班总数
      var rfdtc4 = response.pfdtc4; //未执行航班中延误>4小时的航班总数
      var cfdtc4 = response.cfdtc4; //已执行航班中延误>4小时的航班总数
      $('#flt_delay_4').text(Number(pfdtc4));


      // 国际航班延误数量
      var pfdtci = Number(response.pfdtci); //计划航班中延误国际航班总数
      var ffdtci = Number(response.ffdtci); //执行航班中延误国际航班总数


      //空中飞机架数
      //plane_over_air = Number(response.facnc);

      //setPlaneOverAirOnGround(plane_over_air);


      //

      var cftc = Number(response.cftc); //已执行航班总数

      var exe_rate = Math.round(cftc / sch_total * 1000) / 10 + '%';
      $('#val_flt_total_sch').text(sch_total);
      $('#val_flt_exec_rate .rate').text(exe_rate);

      $('#val_flt_exec_rate .barin').css('width', exe_rate);
      $('#val_flt_exec_rate .plane').css('left', 'calc(' + exe_rate + ' - 12px)');

      var pftci = Number(response.pftci); //国际计划航班总数
      var pftcl = Number(response.pftcl); //国内计划航班总数
      var cftci = Number(response.cftci); //国际已执行航班总数
      var cftcl = Number(response.cftcl); //国内已执行航班总数

      $('#val_flt_total_china_sch').text(pftcl);
      var exe_rate = Math.round(cftcl / pftcl * 1000) / 10 + '%';
      $('#val_flt_exec_rate_china .rate').text(exe_rate);
      $('#val_flt_exec_rate_china .barin').css('width', exe_rate);
      $('#val_flt_exec_rate_china .plane').css('left', 'calc(' + exe_rate + ' - 9px)');

      $('#val_flt_total_int_sch').text(pftci);
      if (pftci > 0) {
        var exe_rate = Math.round(cftci / pftci * 1000) / 10 + '%';
        $('#val_flt_exec_rate_int .rate').text(exe_rate);
        $('#val_flt_exec_rate_int .barin').css('width', exe_rate);
        $('#val_flt_exec_rate_int .plane').css('left', 'calc(' + exe_rate + ' - 9px)');
      } else {
        $('#val_flt_exec_rate_int .rate').text("-");
      }

    },
    error: function () { }
  });







  // ------------------------------------------------------------------------
  // 航班正常率
  // ------------------------------------------------------------------------
  function todayNormalRate(ratestr) {

    var ratestr = Math.round(ratestr * 10) / 10;
    $('#today_normal_rate').text(ratestr);

    var rate = ratestr / 100;

    var canvas = document.getElementById('cvs_normal_rate');
    var context = canvas.getContext('2d');
    context.clearRect(0, 0, canvas.width, canvas.height);
    var x = canvas.width / 2;
    var y = canvas.height / 2;

    var radius = 68;
    var lineWidth = 30;

    // draw back
    var startAngle = Math.PI - Math.PI / 3.6;
    var endAngle = startAngle + Math.PI + Math.PI / 3.6 * 2;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle, endAngle, counterClockwise);
    context.lineWidth = lineWidth;
    context.strokeStyle = '#00438B';
    context.stroke();

    // draw overlay
    var startAngle2 = startAngle;
    var endAngle2 = startAngle + (endAngle - startAngle) * rate;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
    context.lineWidth = lineWidth;

    // linear gradient
    if (rate < 0.5) {
      var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
    } else if (rate < 0.8) {
      var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, 0);
    } else {
      var color = context.createLinearGradient(0, canvas.height, canvas.width * rate, canvas.width / 2);
    }
    color.addColorStop(0, '#122A61');

    if (rate < 0.7) {
      color.addColorStop(1, '#A1263E');
    } else if (rate < 0.8) {
      color.addColorStop(1, '#c29700');
    } else {
      color.addColorStop(1, '#40FF00');
    }

    context.strokeStyle = color;
    context.stroke();

    // draw head
    var startAngle2 = startAngle + (endAngle - startAngle) * rate - (endAngle - startAngle) * 0.01;
    var endAngle2 = startAngle + (endAngle - startAngle) * rate;
    var counterClockwise = false;

    context.beginPath();
    context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
    context.lineWidth = lineWidth;

    if (rate < 0.7) {
      context.strokeStyle = '#ff0000';
    } else if (rate < 0.8) {
      context.strokeStyle = '#ffc600';
    } else {
      context.strokeStyle = '#CCFF99';
    }

    context.stroke();

  }


  // ------------------------------------------------------------------------
  // 延误原因 公司／非公司
  // ------------------------------------------------------------------------
  var date_type = "D";
  var param = {
    'SOLR_CODE': 'FAC_COMP_DELAY_CAUSE_RATE_KPI',
    'COMP_CODE': comp_code,
    'KPI_CODE': 'DELAY_NO',
    'VALUE_TYPE': 'kpi_value_d',
    'DATE_TYPE': date_type,
    'LIMIT': '1',
    'OPTIMIZE': 1
  }

  $.ajax({
    type: 'post',
    url: "/bi/query/getkpi?FAC_COMP_DELAY_CAUSE_RATE_KPI",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {

      checkLogin(response);

      if (response.data != undefined) {
        var data_dly = response.data[comp_code]['DELAY_NO'][date_type];

        var comp_cause_list = [];
        var none_cause_list = [];

        var comp_total = 0;
        var none_total = 0;



        // 公司原因 总数
        var cnt = 0;
        for (var time in data_dly) {
          var d = data_dly[time];
          var len = comp_cause.length;
          for (var i = 0; i < len; i++) {
            var causeName = comp_cause[i];
            //if(!isNaN(d[causeName])){
            var val = Number(d[causeName]);
            val = isNaN(val) ? 0 : val;
            comp_total += val;
            //if(val > 0){
            comp_cause_list.push({
              "name": causeName,
              "val": val
            });

            //}
            //}
          }
        }

        comp_cause_list.sort(function (a, b) {
          return b.val - a.val;
        });

        // 非公司原因 总数
        cnt = 0;
        for (var time in data_dly) {
          var d = data_dly[time];
          var len = none_cause.length;
          for (var i = 0; i < len; i++) {
            var causeName = none_cause[i];
            //if(!isNaN(d[causeName])){
            var val = Number(d[causeName]);
            if (causeName == "民航局航班时刻安排") {
              causeName = "时刻安排"
            }
            val = isNaN(val) ? 0 : val;
            none_total += val;
            //if(val > 0){
            none_cause_list.push({
              "name": causeName,
              "val": val
            });
            //}
            //}
          }
        }

        none_cause_list.sort(function (a, b) {
          return b.val - a.val;
        });

        var maxbarsize = 50;

        // 公司
        var html = '';
        var len = comp_cause_list.length;
        //只显示11条
        for (var i = 0; i < len; i++) {
          var d = comp_cause_list[i];
          var per = Number(d.val) / (comp_total + none_total);
          var perstr = Math.round(per * 100);
          //if(perstr > 0){
          var barlen = maxbarsize * per;
          if (i > 10) {
            break;
          }
          html += '<div class="baritmrow"><span class="blue2">' + d.name + '</span> <span class="bar greenbar" style="width: ' + barlen + 'px; "></span> <span class="ffnum">' + perstr + '%</span> </div>';
          //}
        }

        $('#holder_delay_cause_comp').html(html);


        // 非公司
        html = '';
        // var len = none_cause_list.length;
        var len = none_cause_list.length;
        for (var i = 0; i < len; i++) {
          var d = none_cause_list[i];
          var per = Number(d.val) / (comp_total + none_total);
          var perstr = Math.round(per * 100);
          //if(perstr > 0){
          var barlen = maxbarsize * per;
          html += '<div class="baritmrow"><span class="blue2">' + d.name + '</span> <span class="bar bluebar" style="width: ' + barlen + 'px; "></span> <span class="ffnum">' + perstr + '%</span> </div>';
          //}
        }

        $('#holder_delay_cause_none').html(html);


        // percent
        $('#per_delay_cause_comp').text(Math.round(comp_total / (comp_total + none_total) * 100) + '%');
        $('#per_delay_cause_none').text(Math.round(none_total / (comp_total + none_total) * 100) + '%');

        // chart
        var rate = comp_total / (comp_total + none_total);

        drawDelayChart(rate);

      }

    },
    error: function () { }
  });


  function drawDelayChart(rate) {
    // chart
    var canvas = document.getElementById('cvs_delay_cause');
    var context = canvas.getContext('2d');
    context.clearRect(0, 0, canvas.width, canvas.height);
    var x = canvas.width / 2;
    var y = canvas.height / 2;

    // draw blue circle
    var radius = 54;
    context.beginPath();
    context.arc(x, y, radius, 0, 2 * Math.PI, false);
    context.lineWidth = 7;
    context.strokeStyle = '#02B0F9';
    context.stroke();

    // draw green arc
    var startAngle = Math.PI - (Math.PI * 2 * rate / 2);
    var endAngle = Math.PI + (Math.PI * 2 * rate / 2);
    context.beginPath();
    context.arc(x, y, radius, startAngle, endAngle, false);
    context.lineWidth = 7;
    context.strokeStyle = '#A3D900';
    context.stroke();

    // draw lines
    var numslice = 12;
    for (var i = 0; i < numslice; i++) {
      context.beginPath();
      var startAngle = i * (Math.PI * 2 / numslice);
      var endAngle = startAngle + Math.PI * 0.01;
      context.arc(x, y, radius, startAngle, endAngle, false);
      context.lineWidth = 8;
      context.strokeStyle = '#041946';
      context.stroke();
    }
  }

  var url = `/bi/spring/aircraft/getAcStatusStat?company=${comp_code}`;

  $.ajax({
    type: 'get',
    url: url,
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    success: function (response) {
      var data = response.data;
      var html = ``;
      var barwidth = 40;
      $('#total_plane_num').text(data.total);
      $('#plane_on_stop').text(data.maintCnt); //停场
      $('#plane_on_ground').text(data.groundCnt);
      $('#plane_over_air').text(data.airCnt);

      var detail = data.detail;
      var numofac = detail.length;
      detail.forEach(i => {
        var numac = i.cnt;
        var actype = i.actype;
        var numexeac = i.airCnt;
        var ac_dimian = i.groundCnt;
        var ac_tingchang = i.maintCnt;


        var numac = i.cnt;
        let width = numexeac / numac * barwidth + "px";
        let acwidth = ac_dimian / numac * barwidth + "px";
        let actingwidth = ac_tingchang / numac * barwidth + "px"


        html += '<tr id="ac_' + actype + '">';
        html += '<td class="blue4 r" width="50">' + actype + '</td>';
        html += '<td width="18" class="num_air">' + numexeac + '</td>';
        html += '<td width="45"><span class="bar bluebar2 bar_air" style="width: ' + width + '" ></span></td>';
        html += '<td width="18" class="num_ground">' + ac_dimian + '</td>';
        html += '<td width="45"><span class="bar greenbar2 bar_ground" style="width: ' + acwidth + ';right:0px;" ></span></td>';
        html += '<td width="18" class="num_ground">' + ac_tingchang + '</td>';
        html += '<td width="45"><span class="bar brownbar2 bar_ground" style="width: ' + actingwidth + ';right:0px;" ></span></td>';
        html += '</tr>';
      });

      $('#plane_num_table').html(html);
      // 详细机型列表滚动
      clearInterval(marquee_itv_airPlane);
      $('#plane_num_table1').html('');
      if (numofac > 4) {
        var speed = 80;
        var base_sec = document.getElementById("barlist");
        var base_sec2 = document.getElementById("plane_num_table1");
        var base_sec1 = document.getElementById("plane_num_table");
        base_sec2.innerHTML = base_sec1.innerHTML;

        function base_Marquee() {
          if (base_sec2.offsetTop - base_sec.scrollTop <= 0)
            base_sec.scrollTop -= base_sec1.offsetHeight;
          else {
            base_sec.scrollTop += Math.ceil(1 / pageZoomScale);
          }
        }

        marquee_itv_airPlane = setInterval(base_Marquee, speed);
        base_sec.onmouseover = function () {
          clearInterval(marquee_itv_airPlane)
        }
        base_sec.onmouseout = function () {
          marquee_itv_airPlane = setInterval(base_Marquee, speed)
        }
      }



    }

  });





  // ------------------------------------------------------------------------
  // 各个基地过夜飞机架数
  // ------------------------------------------------------------------------  
  function getAirportList(arpcodes) {

    var param = {
      //"AIRPORT_CODE": arpcodes, // 可选，传入机场CODE
    }
    $.ajax({
      type: 'post',
      url: "/bi/web/airportdetail",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function (response) {
        arp_detail_list = {};
        var list = response.airport;
        for (var i = list.length - 1; i >= 0; i--) {
          var arp = list[i];
          arp_detail_list[arp.code] = arp;
        }
      },
      error: function () { }
    });
  }

  function getArpOvernightPlane() {

    var arplist = [];
    for (var code in BASE_CITY_LIST) {
      arplist.push(code);
    }

    var all_arp_kpi_value = {};
    var arp_has_plane_list = [];


    let date = new Date();
    let mm = date.getMonth() + 1;
    let dd = date.getDate();
    if (mm < 10) {
      mm = '0' + mm;
    }
    if (dd < 10) {
      dd = '0' + dd;
    }
    let queryDate = date.getFullYear() + '' + mm + dd; //+' 23:59:59';
    var kpis = ['AC_NO', 'AC_ARR_NO'];
    var param = {
      "SOLR_CODE": "FAC_COMP_ARP_FLIGHT_KPI",
      "COMP_CODE": comp_code,
      "AIRPORT_CODE": arplist.join(','),
      "KPI_CODE": kpis.join(','),
      "VALUE_TYPE": "kpi_value_d",
      "DATE_TYPE": "D",
      "DATE_ID": queryDate,
      "LIMIT": 0,
      "OPTIMIZE": 1
    }

    $.ajax({

      type: 'post',
      url: "/bi/query/getackpi?FAC_COMP_ARP_FLIGHT_KPI",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function (response) {

        var data = response.data[comp_code];
        for (var kpicode in data) {
          var arpdatlist = data[kpicode]["D"];
          for (var arpcode in arpdatlist) {
            var val = arpdatlist[arpcode];
            if (all_arp_kpi_value[arpcode] == undefined) {
              all_arp_kpi_value[arpcode] = {};
            }
            all_arp_kpi_value[arpcode][kpicode] = 0; //default
            if (!isNaN(Number(val))) {
              all_arp_kpi_value[arpcode][kpicode] = Number(val);
            }

          }
        }

        // 计算总量
        var AC_ARR_NO_TOTAL = 0; // 所有机场过夜飞机架数
        var AC_NO_TOTAL = 0; // 所有机场预计过夜飞机架数
        var basenum = 0;


        for (var i = arplist.length - 1; i >= 0; i--) {
          var code = arplist[i];

          var AC_NO = 0; // 预计飞机架数
          var AC_ARR_NO = 0; // 过夜飞机架数

          AC_NO = Number(all_arp_kpi_value[code]['AC_NO']);
          AC_ARR_NO = Number(all_arp_kpi_value[code]['AC_ARR_NO']);

          if (AC_NO > 0 || AC_ARR_NO > 0) {
            basenum++;
            AC_ARR_NO_TOTAL += AC_ARR_NO;
            AC_NO_TOTAL += AC_NO;
            //arp_has_plane_list.push(code);
          }
          arp_has_plane_list.push(code);
        }

        arp_has_plane_list.reverse();

        getAirportList(arp_has_plane_list.join(','));

        $('#base_over_night_plane_num').text(AC_ARR_NO_TOTAL);
        $('#base_over_night_plane_num2').text(AC_NO_TOTAL);



      },
      error: function () { }
    });



    var currentArpPlanePage = 0;
    var arpPlanePageSize = 5;

    function setArpPlane() {

      // if (arp_detail_list == undefined) {
      //   setTimeout(setArpPlane, 10);
      //   return;
      // }

      var html = '';
      var tempArpDemo = [
        { 'city_name': '大兴', 'AC_ARR_NO': '10' },
        { 'city_name': '沈阳', 'AC_ARR_NO': '2' },
        { 'city_name': '青岛', 'AC_ARR_NO': '7' },
        { 'city_name': '杭州', 'AC_ARR_NO': '12' },
        { 'city_name': '广州', 'AC_ARR_NO': '5' },
        { 'city_name': '海口', 'AC_ARR_NO': '15' },
        { 'city_name': '三亚', 'AC_ARR_NO': '12' },
        { 'city_name': '丽江', 'AC_ARR_NO': '8' },
        { 'city_name': '西安', 'AC_ARR_NO': '9' },
        { 'city_name': '郑州', 'AC_ARR_NO': '2' }
      ]
      var tempArp = [];
      var totalPlane = 0;
      $.ajax({
        type: 'get',
        url: "/bi/spring/JDOvernight/list",
        contentType: 'application/json',
        dataType: 'json',
        async: false,
        success: function (res) {
          if (res.success && res.data.length>0) {
            tempArp =  res.data.map(item => ({
              city_name: item.city,
                AC_ARR_NO: item.num
            }));
          }
        },
        error: function () {
        }
      });

      if(tempArp.length==0) {
        tempArp = tempArpDemo;
      }

      var s1 = currentArpPlanePage * arpPlanePageSize;
      // var s2 = Math.min((currentArpPlanePage + 1) * arpPlanePageSize, arp_has_plane_list.length);
      var s2 = Math.min((currentArpPlanePage + 1) * arpPlanePageSize, tempArp.length);


      tempArp.forEach(item => {
        totalPlane += item.AC_ARR_NO*1;
      });
      // console.log(s1, s2)
      for (var i = s1; i < tempArp.length; i++) {
        var newArp = tempArp[i]
        // var code = arp_has_plane_list[i];

        // var AC_NO = 0; // 预计飞机架数
        // var AC_ARR_NO = 0; // 过夜飞机架数

        // AC_NO = Number(all_arp_kpi_value[code]['AC_NO']);
        // AC_ARR_NO = Number(all_arp_kpi_value[code]['AC_ARR_NO']);

        // var arp = arp_detail_list[code];

        // var maxplane = 50;
        var maxlen = 100;

        html += '<tr>';
        html += '<td class="blue4 r" width="50">' + newArp.city_name +'</td>';
        html += '<td width="36">' + newArp.AC_ARR_NO * 1 + '</td>';
        // html += '<td width="90"><span class="bar bluebar2" style="width: ' + (newArp.AC_ARR_NO * 1 / maxplane * maxlen) + 'px; "></span></td>';
        html += '<td width="90"><span class="bar bluebar2" style="width: ' + (newArp.AC_ARR_NO * 1 / totalPlane * maxlen).toFixed(2)  + '%;"></span></td>';
        // html += '<td width="18">' + AC_NO + '</td>';
        // html += '<td width="45"><span class="bar greenbar2" style="width: ' + (AC_NO / maxplane * maxlen) + 'px; "></span></td>';
        html += '</tr>';

      }

      $('#base_over_night_plane1').html(html);

      // if (currentArpPlanePage < Math.ceil(arp_has_plane_list.length / arpPlanePageSize) - 1) {
      if (currentArpPlanePage < Math.ceil(tempArp.length / arpPlanePageSize) - 1) {
        currentArpPlanePage++;
      } else {
        currentArpPlanePage = 0;
      }

    }

    setInterval(setArpPlane, 5000);
    setArpPlane();

  }


  getArpOvernightPlane();



  // ------------------------------------------------------------------------
  // 各个基地正常率
  // ------------------------------------------------------------------------


  if (area_arps == undefined) {

    var area_arps = {};

    // 获取个区域机场
    $.ajax({
      type: 'get',
      url: "/bi/spring/airport/queryAirportByCompanyId?companyId=100100", //首航没有数据用HU的,
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      success: function (response) {
        var arplist = [];
        var arps = response.data;
        for (var i = arps.length - 1; i >= 0; i--) {
          var o = arps[i];
          var code = o.AIRPORT_CODE;
          var area = o.AREA_NAME;
          if (AREA_LIST[area] != undefined) {
            if (area_arps[area] == undefined) {
              area_arps[area] = [];
            }
            if (area_arps[area].indexOf(code) == -1) area_arps[area].push(code);
          }
          arplist.push(code);

        }
        for (var area_name in area_arps) {
          var arp_list = area_arps[area_name];
          getAreaArpsSts(area_name, arp_list)
        }


      },
      error: function () { }
    });


  } else {

    for (var area_name in area_arps) {
      var arp_list = area_arps[area_name];
      getAreaArpsSts(area_name, arp_list)
    }

    //getArpOvernightPlane(arp_code_list);

  }


  // 区域正常率
  function getAreaArpsSts(area_name, arps) {
    var loaded = 0;
    var sch_total = 0;
    var sch_normal = 0;


    var param = {
      "stdStartUtcTime": stdStartUtcTime,
      "stdEndUtcTime": stdEndUtcTime,
      "companyCodes": comp_code,
      "showMore": 0,
      "depstns": "", //出港
      "arrstns": arps.join(',') //进港
    }

    $.ajax({
      type: 'post',
      url: "/bi/spring/focStaticApi/flightAmountStaticV2?getAreaArpsSts&arrstns=" + arps.join(','),
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function (res) {
        response = res.data;
        sch_total += Number(response.pfdappPercentD);
        sch_normal += Number(response.pfdappPercentM);

        loaded++;
        setOverAll();

      },
      error: function () {

      }
    });



    var param = {
      "stdStartUtcTime": stdStartUtcTime,
      "stdEndUtcTime": stdEndUtcTime,
      "companyCodes": comp_code,
      "showMore": 0,
      "depstns": arps.join(','), //出港
      "arrstns": "" //进港
    }

    $.ajax({
      type: 'post',
      url: "/bi/spring/focStaticApi/flightAmountStaticV2?getAreaArpsSts&depstns=" + arps.join(','),
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function (res) {
        response = res.data;
        sch_total += Number(response.pfdappPercentD);
        sch_normal += Number(response.pfdappPercentM);

        loaded++;
        setOverAll();

      },
      error: function () {

      }
    });


    function setOverAll() {
      if (loaded == 2) {
        var r = Math.round(sch_normal / sch_total * 100)
        if (isNaN(r)) {
          r = 100;
        }
        var area_code = AREA_LIST[area_name];

        // 根据正常率设置颜色
        $('#area_' + area_code).removeClass('area_red');
        $('#area_' + area_code).removeClass('area_orange');
        $('#area_' + area_code).removeClass('area_yellow');
        $('#area_' + area_code).removeClass('area_green1');
        $('#area_' + area_code).removeClass('area_green2');
        $('#area_' + area_code).removeClass('area_green3');

        if (r <= Number(normal_rate_colors['red'])) {
          $('#area_' + area_code).attr('class', 'area_stroke area_red');
        } else if (r <= normal_rate_colors['green1']) {
          $('#area_' + area_code).attr('class', 'area_stroke area_orange');
        } else if (r <= normal_rate_colors['yellow']) {
          $('#area_' + area_code).attr('class', 'area_stroke area_yellow');
        } else if (r <= normal_rate_colors['green3']) {
          $('#area_' + area_code).attr('class', 'area_stroke area_green3');
        } else {
          $('#area_' + area_code).attr('class', 'area_stroke area_green2');
        }

        $('#area_normal_rate_' + area_code).text(r);

        $('#china_map').removeClass('hidden');

      }
    }

  }

  function getArpSts(arp) {

    var loaded = 0;
    var sch_total = 0;
    var sch_normal = 0;

    var param = {
      "stdStartUtcTime": stdStartUtcTime,
      "stdEndUtcTime": stdEndUtcTime,
      "companyCodes": comp_code,
      "depstns": "",
      "showMore": 0,
      "arrstns": arp
    }

    $.ajax({
      type: 'post',
      url: "/bi/spring/focStaticApi/flightAmountStaticV2?arrstns=" + arp,
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function (res) {

        response = res.data;
        sch_total += Number(response.pfdappPercentD);
        sch_normal += Number(response.pfdappPercentM);
        var pfPercent = Number(response.pfdappPercent); //计划航班中的正常航班比率

        var r = Math.round(pfPercent);
        $('#arp_code_' + arp + ' .normal_rate2').text(r);
        setNormalRateTextColor($('#arp_code_' + arp + ' .normal_rate2'), r);

        loaded++;
        setOverAll();

      },
      error: function () {
        loaded++;
        setOverAll();
      }
    });



    var param = {
      "stdStartUtcTime": stdStartUtcTime,
      "stdEndUtcTime": stdEndUtcTime,
      "companyCodes": comp_code,
      "depstns": arp,
      "showMore": 0,
      "arrstns": ""
    }

    $.ajax({
      type: 'post',
      url: "/bi/spring/focStaticApi/flightAmountStaticV2?arrstns=" + arp,
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function (res) {
        response = res.data;
        sch_total += Number(response.pfdappPercentD); //计划航班总数
        sch_normal += Number(response.pfdappPercentM); //计划航班中正常航班总数
        var pfPercent = Number(response.pfdappPercent); //计划航班中的正常航班比率

        var r = Math.round(pfPercent);
        $('#arp_code_' + arp + ' .normal_rate3').text(r);
        setNormalRateTextColor($('#arp_code_' + arp + ' .normal_rate3'), r);

        loaded++;
        setOverAll();

      },
      error: function () {
        loaded++;
        setOverAll();
      }
    });


    function setOverAll() {
      if (loaded == 2) {
        if (sch_total > 0) {
          var r = Math.round(sch_normal / sch_total * 100)
          $('#arp_code_' + arp + ' .normal_rate4').text(r);
          setNormalRateTextColor($('#arp_code_' + arp + ' .normal_rate4'), r);

          $('#pin_' + arp).removeClass('pin_red');
          $('#pin_' + arp).removeClass('pin_yellow');
          $('#pin_' + arp).removeClass('pin_green');

          // 根据正常率设置pin颜色
          if (r <= Number(normal_rate_colors['red'])) {
            $('#pin_' + arp).addClass('pin_red');
          } else if (r <= Number(normal_rate_colors['yellow'])) {
            $('#pin_' + arp).addClass('pin_yellow');
          } else {
            $('#pin_' + arp).addClass('pin_green');
          }
        } else {
          $('#arp_code_' + arp + ' .normal_rate4').text("-");
          $('#pin_' + arp).removeClass('pin_red');
          $('#pin_' + arp).removeClass('pin_yellow');
          $('#pin_' + arp).removeClass('pin_green');
        }
      }

    }

  }


  function setNormalRateTextColor(element, r) {
    element.parent().find('.ffnum').removeClass('red');
    element.parent().find('.ffnum').removeClass('orange');
    element.parent().find('.ffnum').removeClass('yellow');

    if (r <= Number(normal_rate_colors['red'])) {
      element.parent().find('.ffnum').addClass('red');
    } else if (r <= Number(normal_rate_colors['green1'])) {
      element.parent().find('.ffnum').addClass('orange');
    } else if (r <= Number(normal_rate_colors['yellow'])) {
      element.parent().find('.ffnum').addClass('yellow');
    }
  }


  var arp_code_list = [];
  var arp_kpi_value = {};
  var arp_kpi_name_list = ['ORI_NORMAL_NO', 'ORI_NO_SCH']

  for (var arps in BASE_CITY_LIST) {
    arp_code_list = arp_code_list.concat(arps);

    getArpSts(arps)
  }

  var param = {
    "SOLR_CODE": "FAC_COMP_ARP_FLIGHT_KPI",
    "COMP_CODE": comp_code,
    "AIRPORT_CODE": arp_code_list.join(','),
    "KPI_CODE": arp_kpi_name_list.join(','),
    "VALUE_TYPE": "kpi_value_d",
    "DATE_TYPE": "D",
    "LIMIT": 1,
    "OPTIMIZE": 1
  }

  $.ajax({

    type: 'post',
    url: "/bi/query/getackpi?FAC_COMP_ARP_FLIGHT_KPI",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {

      var data = response.data[comp_code];
      for (var kpicode in data) {
        var arpdatlist = data[kpicode]["D"];
        for (var arpcode in arpdatlist) {
          var val = arpdatlist[arpcode];
          if (arp_kpi_value[arpcode] == undefined) {
            arp_kpi_value[arpcode] = {};
          }
          arp_kpi_value[arpcode][kpicode] = 0; //default
          for (var time in val) {
            var v = Number(val[time]);
            arp_kpi_value[arpcode][kpicode] = Number(val[time]);
          }

        }
      }

      setKpiData();

      function setKpiData() {

        if (normal_rate_colors['red'] == undefined) {
          setKpiData();
          return;
        }

        var html = '';
        var basenum = 0;

        for (var code in BASE_CITY_LIST) {
          basenum++;

          var ORI_NORMAL_NO = 0;
          var ORI_NO_SCH = 0;


          if (arp_kpi_value[code] != undefined) {
            ORI_NORMAL_NO = Number(arp_kpi_value[code]['ORI_NORMAL_NO']);
            ORI_NO_SCH = Number(arp_kpi_value[code]['ORI_NO_SCH']);

            var r = Math.round(ORI_NORMAL_NO / ORI_NO_SCH * 100);
            $('#arp_code_' + code + ' .normal_rate1').text(isNaN(r) ? "100" : r);
            setNormalRateTextColor($('#arp_code_' + code + ' .normal_rate1'), r);

          } else {
            console.log('arp_kpi_value[] cannot find airport:' + code);
          }



        }

      }


    },
    error: function () { }
  });



  // ------------------------------------------------------------------------
  // 天气情况
  // ------------------------------------------------------------------------

  var weather_map = {
    '晴': 'icon-e600_sunny',
    '沙': 'icon-e617_dust1',
    '雹': 'icon-e620_hail',
    '雾': 'icon-e615_fog',
    '烟': 'icon-e615_fog',
    '阴': 'icon-e604_gloomy',
    '雷': 'icon-e606_rain2',
    '暴': 'icon-e606_rain2',
    '风': 'icon-e612_wind',
    '霾': 'icon-e613_haze',
    '云': 'icon-e602_cloudy',
    '雨': 'icon-e607_rain3',
    '雪': 'icon-e610_snow3',
  };

  var numOfLoadingBase = 0;

  for (var arpcode in BASE_CITY_LIST) {
    var param = {
      'airport': arpcode
    }

    numOfLoadingBase++;

    $.ajax({
      type: 'post',
      url: "/bi/web/7x2_arp_weather",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function (response) {

        numOfLoadingBase--;

        //console.log('7x2_arp_weather-------');
        //console.log(response);
        if (Number(response.errorcode) == 0) {
          var weather_css = 'icon-e600_sunny';
          var cardcolor_css = '';
          /*
          airport
          airportCode
          cloudInfo 云况
          metUtcTime
          rvr 跑道目视距离
          temperature
          visibility 能见度
          weatherInfo 天气现象
          weatherInfoTxt 翻译后的天气
          windFs 风速

          10个基地的标准（除大连外）
          红色范围
          “能见度 小于等于800；跑道视程小于等于700   天气现象（大雾、雷暴等） 云况高度（60-90米） 风速15米/秒”
          黄色范围
          “能见度 小于等于1600米；跑道视程小于等于1400米   天气现象（大雾、雷暴等） 云况高度小于等于150米  风速大于等于10米/秒”

          大连的标准
          红色范围
          “能见度 小于等于1200；跑道视程小于等于1200  天气现象（大雾、雷暴、沙尘暴） 云况高度小于等于90米  风速大于等于15米/秒”
          黄色范围
          “能见度  小于等于2000米；跑道视程小于等于1800米  天气现象（大雾、雷暴等） 云况高度小于等于150米  风速大于等于10米/秒”

          注：FG代表大雾
                TS或TSRA或+RA 代表雷暴
                 SS或DS沙尘暴

          */
          var weatherInfoCodes = ['FG', 'TS', 'TSRA', 'RA', 'SS', 'DS'];
          var code = response.airport;
          var visibility = isNaN(response.visibility) || response.visibility == 0 ? 9999 : Number(response.visibility); //能见度
          var rvr = isNaN(response.rvr) || response.rvr == 0 ? 9999 : Number(response.rvr); //跑道目视距离
          var cloudInfo = isNaN(response.cloudInfo) || response.cloudInfo == 0 ? 9999 : Number(response.cloudInfo); //云况
          var windFs = isNaN(response.windFs) ? 0 : Number(response.windFs); //风速
          var weatherInfo = response.weatherInfo; //天气现象
          var weatherInfoTxt = response.weatherInfoTxt.replace(/<[^>]+>/g, "");

          //console.log('===visibility', visibility);
          //console.log('===rvr', rvr);
          //console.log('===windFs', windFs);

          var weather_css = '';
          if (code != 'DLC') {
            if (visibility <= 800 || rvr <= 700 || windFs >= 15 || cloudInfo <= 90) {
              cardcolor_css = 'redcard';
            } else if (visibility <= 1600 || rvr <= 1400 || windFs >= 10 || cloudInfo <= 150) {
              cardcolor_css = 'yellowcard';
            }
          } else {
            // DLC 大连的标准不一样
            if (visibility <= 1200 || rvr <= 1200 || windFs >= 15 || cloudInfo <= 90) {
              cardcolor_css = 'redcard';
            } else if (visibility <= 2000 || rvr <= 1800 || windFs >= 10 || cloudInfo <= 150) {
              cardcolor_css = 'yellowcard';
            }
          }
          if (weather_css == '') {
            for (var i = weatherInfoCodes.length - 1; i >= 0; i--) {
              var c = weatherInfoCodes[i];
              if (weatherInfo.indexOf(c) > -1) {
                cardcolor_css = 'yellowcard';
              }
            }
          }

          for (var wtxt in weather_map) {
            if (weatherInfoTxt.indexOf(wtxt) > -1) {
              weather_css = weather_map[wtxt];
            }
          }

          // 设置天气状况icon
          $('#arp_code_' + code + ' .weather span').attr('class', weather_css);

          $('#arp_code_' + code).removeClass('redcard');
          $('#arp_code_' + code).removeClass('yellowcard');

          // 设置卡片颜色
          $('#arp_code_' + code).addClass(cardcolor_css);


        } else {
          console.log('7x2_arp_weather Error');
        }



      },
      error: function (jqXHR, txtStatus, errorThrown) {
        numOfLoadingBase--;
      }
    });
  }



  // ------------------------------------------------------------------------
  // 十航月度排名
  // 十航年度排名
  // ------------------------------------------------------------------------

  var logo_map = {
    '春秋': 'rank_logo_CQ.png',
    '东航': 'rank_logo_DH.png',
    '东方': 'rank_logo_DH.png',
    '国航': 'rank_logo_GH.png',
    '国际': 'rank_logo_GH.png',
    '海航': 'rank_logo_HH.png',
    '海南': 'rank_logo_HN.png',
    '南航': 'rank_logo_NH.png',
    '南方': 'rank_logo_NH.png',
    '川航': 'rank_logo_SC.png',
    '四川': 'rank_logo_SC.png',
    '山航': 'rank_logo_SD.png',
    '山东': 'rank_logo_SD.png',
    '上航': 'rank_logo_SH.png',
    '上海': 'rank_logo_SH.png',
    '深航': 'rank_logo_SZ.png',
    '深圳': 'rank_logo_SZ.png',
    '厦航': 'rank_logo_XM.png',
    '厦门': 'rank_logo_XM.png',
    '天航': 'rank_logo_TH.png',
    '天津': 'rank_logo_TH.png',

    '祥鹏': 'logo_8L.png',
    '香港': 'logo_HX.png',
    '西部': 'logo_PN.png',
    '天津': 'logo_GS.png',
    '首航': 'logo_JD.png',
    '首都': 'logo_JD.png',
    '福州': 'logo_FU.png',
    '乌航': 'logo_UQ.png',
    '金鹏': 'logo_Y8.png',
    '北部湾': 'logo_GX.png',
    '长安': 'logo_9H.png',
    '桂林': 'logo_GT.png',
  };

  var param = {
    'mode': 'query',
    'type': 'month',
  }

  $.ajax({
    type: 'post',
    url: "/bi/web/jd_comp_rank",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {

      checkLogin(response);

      if (response.comp != undefined) {
        var len = response.comp.length;
        var html = '';
        var list = response.comp;
        list.sort(function (a, b) {
          return a.rank - b.rank
        });
        for (var i = 0; i < 10; i++) {
          var obj = list[i];
          //
          var img = logo_map[obj.name];
          if (img != undefined) {
            html += '<span style="display: inline-block; width:20px; margin-bottom:3px;">' + obj.rank + '</span><img style="max-width:21px;" src=img/' + img + '><br>';
          } else {
            html += '<span style="display: inline-block; width:20px; margin-bottom:3px;">' + obj.rank + '</span>' + obj.name + '<br>';
          }

          if (obj.name == '首航' || obj.name == '首都') {
            $('#comp_rank_month').text(obj.rank);
          }

        }

        $('#comp_rank_list_month').html(html);

      }

    },
    error: function (jqXHR, txtStatus, errorThrown) {

    }
  });


  var param = {
    'mode': 'query',
    'type': 'year',
  }

  $.ajax({
    type: 'post',
    url: "/bi/web/jd_comp_rank",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {

      checkLogin(response);

      if (response.comp != undefined) {
        var len = response.comp.length;
        var html = '';
        var list = response.comp;
        list.sort(function (a, b) {
          return a.rank - b.rank
        });
        for (var i = 0; i < 10; i++) {
          var obj = list[i];
          //
          var img = logo_map[obj.name];
          if (img != undefined) {
            html += '<span style="display: inline-block; width:20px; margin-bottom:3px;">' + obj.rank + '</span><img style="max-width:21px;" src=img/' + img + '><br>';
          } else {
            html += '<span style="display: inline-block; width:20px; margin-bottom:3px;">' + obj.rank + '</span>' + obj.name + '<br>';
          }

          if (obj.name == '首航' || obj.name == '首都') {
            $('#comp_rank_year').text(obj.rank);
          }

        }

        $('#comp_rank_list_year').html(html);

      }

    },
    error: function (jqXHR, txtStatus, errorThrown) {

    }
  });



  // VIP 航班列表

  var vip_flt_no_list = [];

  var date = new Date();
  var month = date.getMonth() + 1;
  var day = date.getDate();
  if (month < 10) {
    month = '0' + month;
  }
  if (day < 10) {
    day = '0' + day;
  }
  var today = date.getFullYear() + '-' + month + '-' + day;

  /*
  var param = {
      "acOwner": comp_code,
      "vip": "true",
      "datop": today
  }
  $.ajax({
      type: 'post',
      url:"/bi/web/7x2_vip_flt_list",
      contentType: 'application/json',
      dataType: 'json',
      async: true,
      data: JSON.stringify(param),
      success: function(response) {
          //console.log("7x2_vip_flt_list");
          for(var i in response.flightNo){
            var fno = response.flightNo[i];
            if(fno.length > 0 && fno.indexOf(comp_code) > -1){
              all_fltnos.push(fno);
              vip_flt_no_list.push(fno);
            }
          }
          //console.log(vip_flt_no_list);
          //
          createFltList(vip_flt_no_list, true);
          
      },
      error:function() {
      }
  });
  */

  var param = {
    "mode": "query"
  }
  $.ajax({
    type: 'post',
    url: "/bi/web/jd_vip_list",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      for (var i in response.flt) {
        var flt = response.flt[i];
        var fltno = flt.flt_no;
        all_fltnos.push(fltno);
        vip_flt_no_list.push(fltno);
      }
      console.log("vip")
      console.log(vip_flt_no_list)
      createFltList(vip_flt_no_list, true);
      hideLoading();

    },
    error: function () { }
  });

  // 预警航班 列表
  var warning_flt_no_list = [];
  var param = {
    "mode": "query"
  }
  $.ajax({
    type: 'post',
    url: "/bi/web/jd_warning",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      //console.log("7x2_warning");
      for (var i in response.flt) {
        var flt = response.flt[i];
        var fltno = flt.flt_no;
        all_fltnos.push(fltno);
        warning_flt_no_list.push(fltno);
      }
      console.log("warning")
      console.log(warning_flt_no_list);

    },
    error: function () { }
  });

  // 重点关注 列表
  var important_flt_no_list = [];
  var param = {
    "mode": "query"
  }
  $.ajax({
    type: 'post',
    url: "/bi/web/jd_important",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      //console.log("7x2_important");
      for (var i in response.flt) {
        var flt = response.flt[i];
        var fltno = flt.flt_no;
        all_fltnos.push(fltno);
        important_flt_no_list.push(fltno);
      }
      console.log("important")
      console.log(important_flt_no_list);

    },
    error: function () { }
  });

  // var a = setInterval(function() {
  //   if (warning_flt_no_list.length != 0 && important_flt_no_list.length != 0) {
  //     all_fltnos.concat(warning_flt_no_list, important_flt_no_list);
  //     clearInterval(a);
  //   }
  // }, 100)

  function createFltList(fltlist, selectFirstItem) {
    if (all_flight_list == undefined) {
      setTimeout(createFltList, 10, fltlist, selectFirstItem);
      return;
    }

    var html = '';
    var firstNo;
    var itmcnt = 0;
    var itmwidth = 87;
    var len = fltlist.length;
    for (var i = 0; i < len; i++) {
      var fltno = fltlist[i];
      var flt = findFltInfo(fltno);
      if (flt != undefined) {
        if (firstNo == undefined) {
          firstNo = fltno;
        }
        var className = '';
        if (flt.status == 'DEL' || flt.status == 'DEP') {
          var delay_min = Number(flt.dur1) + Number(flt.dur2) + Number(flt.dur3) + Number(flt.dur4); //延误时间 分钟
          var delay_hour = delay_min / 60;

          if (delay_hour > 2 && delay_hour < 4) {
            className = 'flag_yellow';
          } else if (delay_hour > 2 && delay_hour < 4) {
            className = 'flag_red';
          }
        }
        var left = itmcnt * itmwidth;
        itmcnt++;
        html += '<div class="flt_list_itm itm fltno_' + fltno + '" fltno="' + fltno + '" style="left:' + left + 'px"><span class="' + className + '">' + fltno + '</span></div>';
      }
    }

    $('#flt_list_holder .wrap').html(html);

    $('.flt_list_itm').on('off', fltItmClick);
    $('.flt_list_itm').on('click', fltItmClick);


    if (firstNo != undefined) {
      if (selectFirstItem) {
        selectFlt(firstNo);
      }
    }

  }


  function showNextFlt() {

    var nodeName = $("#importantFlight > .selected").attr("id");
    var nodeArr = ["tab_vip", "tab_warning", "tab_important"];

    // $("#importantFlight>.tab").each(function(index, el) {
    //   if ($(el).hasClass("selected")) {
    //     selectedNodeIndex = index;
    //   }
    // })

    var nodeObj = {
      "tab_vip": vip_flt_no_list,
      "tab_warning": warning_flt_no_list,
      "tab_important": important_flt_no_list
    };

    // var idx = all_fltnos.indexOf(selectedFltno)

    var idx = nodeObj[nodeName].indexOf(selectedFltno);
    idx++;
    // alert(nodeName)
    // alert(idx)
    if (idx == nodeObj[nodeName].length) {
      selectedNodeIndex++;
      idx = 0
    }
    if (selectedNodeIndex == nodeArr.length) {
      selectedNodeIndex = 0;
    }
    // if (idx < all_fltnos.length) {
    //   idx++;
    // } else {
    //   idx = 0;
    // }
    selectedNodeName = nodeArr[selectedNodeIndex];
    var fltno = nodeObj[selectedNodeName][idx];
    // 判断当前tab最后一次遍历是否为最后一个值，是的话则跳转至下一个tab

    // if (nodeObj[nodeName].indexOf(selectedFltno) > nodeObj[nodeName].indexOf(fltno) || nodeObj[nodeName].indexOf(fltno) == -1) {
    //   selectedNodeIndex++;
    //   if (selectedNodeIndex == nodeArr.length) {
    //     selectedNodeIndex = 0;
    //   }
    // }
    // alert(fltno)
    // alert(selectedNodeIndex)
    if (vip_flt_no_list.indexOf(fltno) > -1 && selectedNodeName == "tab_vip") {
      selectFltTab('tab_vip', false);
    } else if (warning_flt_no_list.indexOf(fltno) > -1 && selectedNodeName == "tab_warning") {
      selectFltTab('tab_warning', false);
    } else if (important_flt_no_list.indexOf(fltno) > -1 && selectedNodeName == "tab_important") {
      selectFltTab('tab_important', false);
    }

    selectFlt(fltno);

  }

  function fltItmClick(evt) {
    selectFlt($(this).attr('fltno'));
  }

  var selectedFltno;

  function selectFlt(fltno) {
    // alert(fltno)
    clearTimeout(timeout_next_flt);

    $('.flt_info').hide();

    selectedFltno = fltno;

    $('.flt_list_itm').removeClass('selected');
    $('.fltno_' + fltno).addClass('selected');

    setFltDetails(fltno);

    if (vip_flt_no_list.indexOf(fltno) > -1 && $('#tab_vip').hasClass('selected')) {
      var idx = vip_flt_no_list.indexOf(fltno);

      if (idx > 5) {
        $('#flt_list_holder .wrap').animate({
          left: '-' + ($('.flt_list_itm').width() + 1) * (idx - 5) + 'px'
        },
          300,
          function () { }
        );
      } else {
        $('#flt_list_holder .wrap').animate({
          left: '0px'
        },
          300,
          function () { }
        );
      }

    } else if (warning_flt_no_list.indexOf(fltno) > -1 && $('#tab_warning').hasClass('selected')) {
      $('#flt_list_holder .wrap').animate({
        left: '0px'
      },
        0,
        function () { }
      );
    } else if (important_flt_no_list.indexOf(fltno) > -1 && $('#tab_important').hasClass('selected')) {
      $('#flt_list_holder .wrap').animate({
        left: '0px'
      },
        0,
        function () { }
      );
    }



  }



  function setFltDetails(fltno) {

    if (arp_detail_list == undefined) {
      setTimeout(setFltDetails, 10, fltno);
      hideLoading();
      return;
    }
    // alert(fltno)
    var flt = findFltInfo(fltno);
    // alert(flt)
    if (flt) {
      clearTimeout(timeout_next_flt);
      timeout_next_flt = setTimeout(showNextFlt, 15000);

      var arp1 = arp_detail_list[flt.depStn];
      var arp2 = arp_detail_list[flt.arrStn];

      $('.flt_info .acno').text(flt.acLongNo);

      if (arp1) {
        $('.flt_info .dep_city').text(arp1.city_name);
      } else {
        $('.flt_info .dep_city').text(flt.depStn);
      }
      if (arp2) {
        $('.flt_info .arr_city').text(arp2.city_name);
      } else {
        $('.flt_info .arr_city').text(flt.arrStn);
      }

      $('.flt_info').show();
    } else {
      showNextFlt();
    }



    hideLoading();

  }

  $('#col_r .row1 .tab').on('click', function () {

    selectFltTab($(this).attr('id'), true);

  });

  function selectFltTab(id, selectFirstItem) {
    $('#col_r .row1 .tab').removeClass('selected');
    $('#' + id).addClass('selected');

    if (id == 'tab_vip') {
      createFltList(vip_flt_no_list, selectFirstItem);
    } else if (id == 'tab_warning') {
      createFltList(warning_flt_no_list, selectFirstItem);
    } else if (id == 'tab_important') {
      createFltList(important_flt_no_list, selectFirstItem);
    }
  }



  // ------------------------------------------------------------------------
  // 计划旅客总量 已完成占比 
  // ------------------------------------------------------------------------

  /*
  BOOK_NUM 计划运输旅客总量
  CKI_NUM 已完成旅客运输总量
  */
  /*
  var param = {
    "SOLR_CODE":"FAC_COMP_KPI",
    "COMP_CODE":comp_code,
    "KPI_CODE":"BOOK_NUM,CKI_NUM",
    "VALUE_TYPE":"kpi_value_d",
    "DATE_TYPE":"D",
    "LIMIT":1
  }

  $.ajax({           

          type: 'post',
          url:"/bi/query/getkpi",
          contentType: 'application/json',
          dataType: 'json',
          async: true,
          data: JSON.stringify(param),
          success: function(response) {

              var book_nums = response.data[comp_code]['BOOK_NUM']['D'];
              var book_num;
              for(var time in book_nums){
                book_num = book_nums[time];
              }
              var cki_nums = response.data[comp_code]['CKI_NUM']['D'];
              var cki_num;
              for(var time in cki_nums){
                cki_num = cki_nums[time];
              }

              $('#val_trv_num_plan').text(Number(book_num));
              $('#val_trv_num_completed').text(Number(cki_num));

        trvNumChart(Number(book_num), Number(cki_num));


          },
          error:function() {
          }
  });
  */


  // ------------------------------------------------------------------------
  // 计划旅客+已完成
  // ------------------------------------------------------------------------
  var planNum; //旅客订票人数
  var ckiNum; //旅客值机人数

  var param = {
    "companyCodes": comp_code,
    "stdStartUtcTime": stdStartUtcTime,
    "stdEndUtcTime": stdEndUtcTime,
    "detailType": "pftc", //统计航班（总计）
    //"psrType":"all"
  }

  $.ajax({
    type: 'post',
    url: "/bi/web/getPsrSummInfo",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      planNum = Number(response.planNum); //旅客订票人数
      setTrvNum();
    },
    error: function () { }
  });


  var param = {
    "companyCodes": comp_code,
    "stdStartUtcTime": stdStartUtcTime,
    "stdEndUtcTime": stdEndUtcTime,
    "detailType": "cftc", //已执行
    //"psrType":"all"
  }

  $.ajax({
    type: 'post',
    url: "/bi/web/getPsrSummInfo",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      ckiNum = Number(response.ckiNum); //旅客值机人数
      setTrvNum();
    },
    error: function () { }
  });

  function setTrvNum() {
    if (planNum >= 0 && ckiNum >= 0) {

      $('#val_trv_num_plan').text(planNum);
      $('#val_trv_num_completed').text(ckiNum);

      trvNumChart(Number(planNum), Number(ckiNum));
    }
  }



  // 计划中转旅客／已执行占比

  var total_zz_psr = undefined;
  var exec_zz_psr = undefined;

  var param = {
    "companyCodes": comp_code,
    "detailType": "pftc", //统计航班（总计）
    "psrType": "in" // 中转转入
  }

  $.ajax({
    type: 'post',
    url: "/bi/web/7x2_vip_flt_cnt",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      total_zz_psr = response.ckiGroupOutNum; //vip_psr_cnt;
      setZzPsr();
    },
    error: function () { }
  });


  //
  var param = {
    "companyCodes": comp_code,
    "detailType": "cftc", //已执行航班班次
    "psrType": "in" // 中转转入
  }

  $.ajax({
    type: 'post',
    url: "/bi/web/7x2_vip_flt_cnt",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      exec_zz_psr = response.ckiGroupOutNum; //vip_psr_cnt;
      setZzPsr();
    },
    error: function () { }
  });


  function setZzPsr() {
    if (total_zz_psr != undefined && exec_zz_psr != undefined) {
      var rate = total_zz_psr > 0 ? (exec_zz_psr / total_zz_psr) : 0;
      trvChart1(rate);

      if (total_zz_psr > 0) {
        var r1 = Math.round(rate * 100);
        var r2 = 100 - r1;
        $('#trv_zz_complete').text(r1 + '%');
        $('#trv_zz_uncomplete').text(r2 + '%');
      } else {
        $('#trv_zz_complete').text('-');
        $('#trv_zz_uncomplete').text('-');
      }
    }
  }



  // ------------------------------------------------------------------------
  // VIP 已完成占比 
  // ------------------------------------------------------------------------

  // ------------------------
  // 获取VIP航班数量和VIP乘客数量
  /*
  文档：getPsrSummInfoByFoc 按旅客查询航班接口.docx
  描述：旅客按航班汇总查询
  接口类：com.hnair.opcnet.api.ods.psr.PassengerApi
  接口方法：getPsrSummInfoByFoc
  */
  // ------------------------

  var vip_flt_cnt_total; //vip航班
  var vip_psr_cnt_total; //vip乘客
  var vip_flt_cnt_complete; //vip航班 已完成
  var vip_psr_cnt_complete; //vip乘客 已完成

  /*
  var vip_load_cnt = 0;
  var param = {
    "companyCodes": comp_code,
    "detailType": "pftc",//统计航班（总计）
    "psrType":"vip"
  }

  $.ajax({           
        type: 'post',
        url:"/bi/web/7x2_vip_flt_cnt",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            vip_flt_cnt_total = response.flt_cnt; //vip航班
            vip_psr_cnt_total = response.vip_psr_cnt; //vip乘客

            vip_load_cnt++;
            setVIPTrvNum();

        },
        error:function() {
        }
    });

    var param = {
    "companyCodes": comp_code,
    "detailType": "cftc",//已执行航班班次
    "psrType":"vip"
  }

  $.ajax({           
        type: 'post',
        url:"/bi/web/7x2_vip_flt_cnt",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function(response) {

            vip_flt_cnt_complete = response.flt_cnt; //vip航班
            vip_psr_cnt_complete = response.vip_psr_cnt; //vip乘客

            vip_load_cnt++;
            setVIPTrvNum();

        },
        error:function() {
        }
    });

    function setVIPTrvNum(){
    if(vip_load_cnt == 2){
      var rate = vip_psr_cnt_total > 0 ? (vip_psr_cnt_complete/vip_psr_cnt_total) : 0;
      trvChart2(rate);

      if(vip_psr_cnt_total > 0){
        var r1 = Math.round(rate * 100);
        var r2 = 100 - r1;
        $('#vip_exec_complete').text(r1+'%');
        $('#vip_exec_uncomplete').text(r2+'%');
      }else{
        $('#vip_exec_complete').text('-');
        $('#vip_exec_uncomplete').text('-');
      }
    	
    }
  	
  }

    */

  var param = {
    'mode': 'query'
  }

  $.ajax({
    type: 'post',
    url: "/bi/web/jd_vip_psr",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {

      checkLogin(response);

      var vip_psr_cnt_total = response.vvip[0].people_total;
      var vip_psr_cnt_complete = response.vvip[0].people_complete;

      var rate = vip_psr_cnt_total > 0 ? (vip_psr_cnt_complete / vip_psr_cnt_total) : 0;
      trvChart2(rate);

      if (vip_psr_cnt_total > 0) {
        var r1 = Math.round(rate * 100);
        var r2 = 100 - r1;
        $('#vip_exec_complete').text(r1 + '%');
        $('#vip_exec_uncomplete').text(r2 + '%');
      } else {
        $('#vip_exec_complete').text('-');
        $('#vip_exec_uncomplete').text('-');
      }

    },
    error: function () { }
  });



  // 获取飞机位置
  getPlaneLocation();

}


function findFltInfo(fltno) {
  for (var i = all_flight_list.length - 1; i >= 0; i--) {
    var flt = all_flight_list[i];
    if (flt.flightNo == fltno) {
      return flt;
    }
  }
  return undefined;
}


var chart_earch;

function crate3DEarth() {
  chart_earch = echarts.init(document.getElementById('earth3d'));

  var option = {
    tooltip: {
      show: false
    },
    backgroundColor: 'rgba(0,0,0,0)',
    globe: {
      baseTexture: 'asset/earth.jpg',
      //heightTexture: '/asset/get/s/data-1491889019097-rJQYikcpl.jpg',

      displacementScale: 0.1,

      shading: 'lambert',

      //environment: 'rgba(0,0,0,0)',
      shading: 'realistic',
      light: {
        main: {
          intensity: 0.3
        },
        ambient: {
          intensity: 1.0
        },
      },

      viewControl: {
        autoRotate: false,
        zoomSensitivity: false,
        targetCoord: [115, 32]
      },

      layers: []
    },
    series: []
  }

  chart_earch.setOption(option);
}

// 计划，完成运输旅客 图表
function trvNumChart(total_people, total_complete) {

  var rate_vip_people = total_complete / total_people;
  var rate = rate_vip_people;

  var canvas = document.getElementById('cvs_chart_people');
  var context = canvas.getContext('2d');

  var col = 19;
  var row = 6;
  var radius = 3.5;
  var grid = 10;

  var x;
  var y;

  // draw circle
  for (var i = 0; i < col; i++) {
    for (var j = 0; j < row; j++) {

      x = radius + i * grid;
      y = radius + j * grid;

      context.beginPath();
      context.arc(x, y, radius, 0, Math.PI * 2, false);
      context.fillStyle = '#034781';
      context.fill();
    }
  }

  // draw rate circles
  var limit = Math.round(col * row * rate);
  for (var i = 0; i < col; i++) {
    for (var j = 0; j < row; j++) {

      if (limit > 0) {
        x = radius + i * grid;
        y = radius + j * grid;

        context.beginPath();
        context.arc(x, y, radius, 0, Math.PI * 2, false);
        context.fillStyle = '#009DEB';
        context.fill();

        limit--;

      } else {

        break;
      }

    }
  }
}

// 国际。国内旅客
function trvChart1(rate) {
  // chart
  var canvas = document.getElementById('cvs_trv1');
  var context = canvas.getContext('2d');
  context.clearRect(0, 0, canvas.width, canvas.height);
  var x = canvas.width / 2;
  var y = canvas.height / 2;

  // draw blue circle
  var radius = 46;
  context.beginPath();
  context.arc(x, y, radius, 0, 2 * Math.PI, false);
  context.lineWidth = 7;
  context.strokeStyle = '#02B0F9';
  context.stroke();

  // draw green arc
  var startAngle = Math.PI - (Math.PI * 2 * rate / 2);
  var endAngle = Math.PI + (Math.PI * 2 * rate / 2);
  context.beginPath();
  context.arc(x, y, radius, startAngle, endAngle, false);
  context.lineWidth = 7;
  context.strokeStyle = '#A3D900';
  context.stroke();

  // draw lines
  var numslice = 12;
  for (var i = 0; i < numslice; i++) {
    context.beginPath();
    var startAngle = i * (Math.PI * 2 / numslice);
    var endAngle = startAngle + Math.PI * 0.01;
    context.arc(x, y, radius, startAngle, endAngle, false);
    context.lineWidth = 8;
    context.strokeStyle = '#041946';
    context.stroke();
  }
}

// VIP旅客 已执行 待执行
function trvChart2(rate) {
  // chart
  var canvas = document.getElementById('cvs_trv2');
  var context = canvas.getContext('2d');
  context.clearRect(0, 0, canvas.width, canvas.height);
  var x = canvas.width / 2;
  var y = canvas.height / 2;

  // draw blue circle
  var radius = 46;
  context.beginPath();
  context.arc(x, y, radius, 0, 2 * Math.PI, false);
  context.lineWidth = 7;
  context.strokeStyle = '#02B0F9';
  context.stroke();

  // draw green arc
  var startAngle = Math.PI - (Math.PI * 2 * rate / 2);
  var endAngle = Math.PI + (Math.PI * 2 * rate / 2);
  context.beginPath();
  context.arc(x, y, radius, startAngle, endAngle, false);
  context.lineWidth = 7;
  context.strokeStyle = '#A3D900';
  context.stroke();

  // draw lines
  var numslice = 12;
  for (var i = 0; i < numslice; i++) {
    context.beginPath();
    var startAngle = i * (Math.PI * 2 / numslice);
    var endAngle = startAngle + Math.PI * 0.01;
    context.arc(x, y, radius, startAngle, endAngle, false);
    context.lineWidth = 8;
    context.strokeStyle = '#041946';
    context.stroke();
  }



}


// ------------------------------------------------------------------------
// 全球飞机位置
// ------------------------------------------------------------------------

var currentMapType = 'china';
var planeLocationList = []; //空中

$('#map_switch_btn').on('click', function () {
  if (currentMapType == 'china') {
    currentMapType = 'world';
    $('#map_switch_lb').text('正常率');
    $('#title_earth').text('飞机位置图');
    $('#china_map').fadeOut(100);
    $('#base_cards').fadeOut(100);
    $('.planeLocationLegend').fadeIn(100);
    $('#earth3d').css('pointer-events', 'auto');

    setPlaneLocation();

    //
    $('#comp_rank_list_month').css('bottom', '46px');
    $('#comp_rank_list_year').css('bottom', '46px');
    $('.comp_rank_month').css('bottom', '20px');
    $('.comp_rank_year').css('bottom', '20px');

  } else {
    currentMapType = 'china';
    $('#map_switch_lb').text('飞机位置');
    $('#title_earth').text('国内运行正常率');
    $('#china_map').fadeIn(100);
    $('#base_cards').fadeIn(100);
    $('.planeLocationLegend').fadeOut(100);
    $('#earth3d').css('pointer-events', 'none');

    //
    $('#comp_rank_list_month').css('bottom', '136px');
    $('#comp_rank_list_year').css('bottom', '136px');
    $('.comp_rank_month').css('bottom', '110px');
    $('.comp_rank_year').css('bottom', '110px');

    crate3DEarth();
  }
})

function getPlaneLocation() {
  var param = {
    'mode': 'pos'
  }
  $.ajax({
    type: 'post',
    url: "/bi/web/flightMq",
    contentType: 'application/json',
    dataType: 'json',
    async: true,
    data: JSON.stringify(param),
    success: function (response) {

      planeLocationList = [];
      var plist = {};

      //var list = response.data.data1;
      function processData(data1) {
        var lst = {};
        var len = data1.length;
        for (var i = 0; i < len; i++) {
          var dd = data1[i];
          var fi = dd.fi;
          if (lst[fi] == undefined) {
            lst[fi] = {
              data: []
            };
            lst[fi]['data'].push(dd);
          } else {
            lst[fi]['data'].push(dd);
          }
        }

        return lst;
      }

      var list = processData(response.data.data1);

      for (var fltno in list) {

        var fltobj = list[fltno];
        var itmx2 = fltobj.data;

        var itm;

        if (itmx2 && itmx2.length > 1) {
          var itm1 = itmx2[0];
          var itm2 = itmx2[1];


          itm1.UTC = itm1.UTC.replace(' ', '');
          itm2.UTC = itm2.UTC.replace(' ', '');

          if (itm1.UTC > itm2.UTC) {
            itm = itm1
            itm.LON1 = itm2.LON;
            itm.LAT1 = itm2.LAT;
          } else if (itm1.UTC < itm2.UTC) {
            itm = itm2
            itm.LON1 = itm1.LON;
            itm.LAT1 = itm1.LAT;
          } else {

            itm = itm2
            itm.LON1 = itm1.LON;
            itm.LAT1 = itm1.LAT;

            // console.log(fltno, '两组经纬度UTC相同');
          }
        } else if (itmx2 && itmx2.length > 0) {
          itm = itmx2[0];

        }


        if (itm) {

          var alt = itm.ALT;
          var cas = itm.CAS;
          var vec;

          var fltno = itm.fi;

          if (fltno.indexOf(comp_code) == 0) {

            var acno = itm.an;
            acno = acno.replace('-', '');

            var lon = formatLonLat(itm.LON);
            var lon1 = formatLonLat(itm.LON1);
            var lat = formatLonLat(itm.LAT);
            var lat1 = formatLonLat(itm.LAT1);

            if (isNaN(itm.LON)) {
              vec = Number(itm.VEC);
            }

            var oil = isNaN(itm.OIL) ? '' : itm.OIL;

            var pdat = {
              fltno: fltno,
              acno: acno,
              alt: alt,
              vec: vec,
              lon: lon,
              lat: lat,
              lon1: lon1,
              lat1: lat1,
              oil: oil,
            };

            var code = acno + '-' + fltno;

            /*
            if(plist[code] == undefined){
                plist[code] = pdat;
            }else if(plist[code].lon1 == undefined){
                plist[code].lon1 = pdat.lon;
                plist[code].lat1 = pdat.lat;
                if(oil > 0){
                    plist[code].oil = oil;
                }
            }else if(oil > 0){
                plist[code].oil = oil;
            }
            */

            if (pdat.vec == undefined) {
              pdat.vec = getGeoAngle(pdat.lat, pdat.lon, pdat.lat1, pdat.lon1);
            }
            planeLocationList.push(pdat);
          }
        }
      }

      /*
      for(var code in plist){
          var pdat = plist[code];
          //if(pdat.vec || pdat.lon1 != undefined){
              if(pdat.vec == undefined){
                  pdat.vec = getGeoAngle(pdat.lat, pdat.lon, pdat.lat1, pdat.lon1);
              }
              planeLocationList.push(pdat);
          //}
      }
      */

    },
    error: function (jqXHR, txtStatus, errorThrown) {
      console.log('----error');
    }
  });
}

function setPlaneLocation() {

  if (all_flight_list == undefined || planeLocationList == undefined) {
    setTimeout(setPlaneLocation, 10);
    return;
  }

  var seriesData = [];


  for (var i = planeLocationList.length - 1; i >= 0; i--) {
    var itm = planeLocationList[i];
    var acno = itm.acno;
    var fltno = itm.fltno;

    var vec = itm.vec;
    var alt = itm.alt;

    var lon = itm.lon;
    var lat = itm.lat;

    var flt = findFltInfo(fltno);

    /*
      黄色：延误 DEL
      紫色：机务工作 ARR NDR ATA CNL
      绿色：飞行中 DEP RTR
      蓝色：未执行 SCH ATD

      'ARR':'落地',
      'NDR':'落地',
      'ATD':'推出',
      'ATA':'到达',
      'CNL':'取消',
      'DEL':'延误',
      'DEP':'起飞',
      'RTR':'返航',
      'SCH':'计划'

      */
    // 在飞的，空中的飞机
    if (flt && (alt > 0 || flt.status == 'DEP')) {
      var img = '';
      var delay_min = Number(flt.dur1) + Number(flt.dur2) + Number(flt.dur3) + Number(flt.dur4); //延误时间 分钟
      var color;
      var border;
      var svg = 'M90.7,4.8c-1.3-1.5-2.8-2.7-4.5-3.5C84.3,0.4,82.4,0,80.3,0l-0.4,0.1L79.5,0c-2,0-4,0.4-5.8,1.4c-1.8,0.8-3.3,2-4.6,3.5c-2.2,2.3-3.2,4.9-3.2,8l0,47.5L0.1,111.7L0,127.4l65.3-21.2l-0.1,38L39,167.2l0,7.9l3-0.8v1.6l37.9-10.9l37.9,10.9v-1.6l2.9,0.8l-0.1-7.9l-26.1-22.9l-0.1-38l65.4,21.2l-0.1-15.8L94,60.3l-0.1-47.5C93.9,9.8,92.9,7.1,90.7,4.8z';
      if (delay_min == 0) {
        color = "#00ff6c";
        border = "#005d09";
      } else {
        color = "#fff60e";
        border = "#2f2a0b";
      }
      seriesData.push({
        name: fltno,
        acno: acno,
        value: [lon, lat, 0],
        //symbolRotate: -vec, //航向   正北是0°，顺时针，0到360°
        //symbol:'path://'+svg,
        itemStyle: {
          color: color,
          borderColor: border,
          borderWidth: 1,
        }
      })
    }

  }

  var series = [];
  series.push({
    type: 'scatter3D',
    coordinateSystem: 'globe',
    symbolSize: 6,
    //blendMode: 'lighter',
    slient: true,
    label: {
      show: false,
    },
    data: seriesData
  });

  var option = {
    series: series
  }
  chart_earch.setOption(option);
}


// ------------------------------------------------------------------------
// 底部卡片 定时 循环滚动
// ------------------------------------------------------------------------
var cardpage = 0;
var cardtotal = 10;
var cardperpage = 4;

function scrollBaseCards() {

  $('#base_cards .reel').animate({
    top: 12 - 105 * cardpage + 'px'
  },
    300,
    function () { }
  );

  if (cardpage < Math.ceil(cardtotal / cardperpage) - 1) {
    cardpage++;
  } else {
    cardpage = 0;
  }
}
setInterval(scrollBaseCards, 20000);



// ------------------------------------------------------------------------
// 时钟
// ------------------------------------------------------------------------
function setTime() {
  var date = new Date();
  var timestamp = date.getTime();
  var timezoneOffset = date.getTimezoneOffset();
  var utc_timestamp = timestamp + timezoneOffset * 60 * 1000;
  var utc_date = new Date();
  utc_date.setTime(utc_timestamp);

  var sydney_date = new Date();
  sydney_date.setTime(utc_timestamp + 11 * 3600 * 1000);

  var newyork_date = new Date();
  newyork_date.setTime(utc_timestamp - 5 * 3600 * 1000);

  $('#time_beijing').text(formatNum(date.getHours()) + ':' + formatNum(date.getMinutes()));
  $('#time_london').text(formatNum(utc_date.getHours()) + ':' + formatNum(utc_date.getMinutes()));
  //$('#time_sydney').text(formatNum(sydney_date.getHours()) + ':' + formatNum(sydney_date.getMinutes()));
  //$('#time_newyork').text(formatNum(newyork_date.getHours()) + ':' + formatNum(newyork_date.getMinutes()));

  $('#date_beijing').text(date.getDate() + ' ' + getEngMonth(date.getMonth()));
  $('#date_london').text(utc_date.getDate() + ' ' + getEngMonth(utc_date.getMonth()));
  //$('#date_sydney').text(sydney_date.getDate() + ' ' + getEngMonth(sydney_date.getMonth()));
  //$('#date_newyork').text(newyork_date.getDate() + ' ' + getEngMonth(newyork_date.getMonth()));

}

function formatNum(n) {
  if (n < 10) {
    return ('0' + n);
  } else {
    return n;
  }
}

function getEngMonth(month) {
  var mlist = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Spt", "Oct", "Nov", "Dec"];
  return mlist[month].toUpperCase();
}

setInterval(setTime, 1000);
setTime();



crate3DEarth();
loadAll();


setInterval(loadAll, 5 * 60 * 1000);



///////// test