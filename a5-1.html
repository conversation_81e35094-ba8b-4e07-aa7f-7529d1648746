<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    
    <title>HNA数字航空</title>

    <meta http-equiv="refresh" content="86400">
    <link href="css/nprogress.css" rel="stylesheet">
    <script src="js/nprogress.js"></script>
    <script src="js/tingyun.js?ver=20211218"></script>
    
    <link href="css/bootstrap.css" rel="stylesheet">
    <link href="css/bootstrap-theme.css" rel="stylesheet">
    <link href="css/main.css?ver=20211218" rel="stylesheet">
    <link href="css/a5-1.css?ver=20181102" rel="stylesheet">

    <script src="js/jquery-1.11.1.js"></script>
    <script src="js/json.js"></script>

    <script src="js/bootstrap.min.js"></script>
    <script src="js/echarts.min.js?ver=20211218"></script>
    <script src="js/ui.js?ver=20211218"></script>
    <script src="js/util.js?ver=20211218"></script>
    <script src="js/jquery.powertip.min.js"></script>
    <script src="js/moment.min.js"></script>


</head>
<body style="opacity:0;" >

    <iframe id="login" scrolling="no" width="0" height="0" style="display:none;" ></iframe>
    <script src="js/config.js?ver=20211218"></script>

    <audio id="text2audio"></audio>

    <div id="header">

        <div id="logo">
            <div id="main_page_title"></div>
            <div id="main_page_subtitle"></div>
        </div><!-- /#logo -->
       

        <div id="nav">
        </div><!-- /#nav -->

        <div id="topright">
            
        </div><!-- /#topright -->

    </div><!-- /#header -->

    <div id="app"></div>    

    <script type="text/html" id="template">
        <div class="page-wrapper" id="page-parent-comp" style="width: 1366px; height: 868px; background-repeat: repeat-y">            
            <div class="page">
                <div class="pagetitle">
                    <div class="maintitle">
                        <span class="comp tit"></span><span class="tit">{{title.name}}</span><span class="date">{{dateSelect.boxtitle}}</span>
                    </div>
                    <div class="powertip" v-if="dateSelect.showRangeText" style="position: absolute;left: 1000px;top: 55px;">{{rangeTextComputed}}</div>
                    <div class="date_value_select" @click="onDateShowList" ref="selectUI" v-bind:data-text="dateSelect.rangeText" @mouseover="rangeTextMouseOver" @mouseout="rangeTextMouseOut">
                        <div class="box" ref="selectBoxUI">{{dateSelect.boxtitle}}</div>                        
                        <ul v-if="dateSelect.showUL">
                            <li v-for="item in dateSelect.dataList" v-bind:key="item.key" @click="onSelectChange(item)" @mouseover="selectChangeMouseover" @mouseout="selectChangeMouseout">{{item.value}}</li>
                        </ul>
                    </div>
                    <div class="date_type_select">
                        <div v-bind:class="{tab:true, hover:item.hover}" v-for="(item, index) in dateSelect.dateType" @click="onDateTypeChange(item)">{{item.name}}</div>
                    </div>
                    <!-- <div id="main_cb_week" class="combotab_selected"></div>
                    <div id="main_cb_month" class="combotab"></div>
                    <div id="week_date_range"></div> -->
                </div><!-- /pagetitle -->
                <div class="estimated" ref="estimated">
                    <div class="title_bg">
                        <div class="t1">预估成本</div>
                        <div class="t2">Estimated cost</div>
                        <i ref="estimatedLink"></i>
                    </div>
					<div class="overallCost">
					    <div class="bar"></div><span>整体成本</span>
					</div>
					<div class="overallCostMain">
					    <div class="moneyImg"></div>
						<div class="overallCostNum">
							<span style="text-align: center;" v-html="showEstimatedCost"></span>
							<span>万元</span>
						</div>
						<div class="overallCostTbHb">
							<div class="overallCostTbHbTitle">
								<span>同比</span>
								<span style="margin-left: 10px;">环比</span>
							</div>
							<div class="overallCostTbHbNum">
								<span id="estimatedCostTbCom"></span>
								<span id="estimatedCostHbCom" style="margin-left: 10px;"></span>
							</div>
						</div>
					</div>
					
					<div class="overallCost">
					    <div class="bar"></div><span>变动成本</span>
					</div>
					<div class="overallCostMain">
					    <div class="moneyImg"></div>
						<div class="overallCostNum">
							<span style="text-align: center;" v-html="showEstimatedCost"></span>
							<span>万元</span>
						</div>
						<div class="overallCostTbHb">
							<div class="overallCostTbHbTitle">
								<span>同比</span>
								<span style="margin-left: 10px;">环比</span>
							</div>
							<div class="overallCostTbHbNum">
								<span id="estimatedCostTbCom2"></span>
								<span style="margin-left: 10px;" id="estimatedCostHbCom2"></span>
							</div>
						</div>
					</div>
					
                    <!-- <div class="subtitle">
                        <div class="bar"></div><span>变动成本总计</span>
                    </div> -->
                    <!-- <div class="nums" v-html="showEstimatedCost"></div> -->
                                      
                </div>
                <!-- <div class="flight">
                    <div class="title_bg">
                        <div class="t1">航班量</div>
                        <div class="t2">Flight volume</div>
                    </div>
                    <div class="nums" v-html="showFlightVolume">
                    </div>
                </div> -->
				<div class="ringPict" ref="ring">
				    <span></span>
				</div> 
				
                <div class="aircraft" >
					<div class="endTitle">
						<div class="endTitle_01"></div>
						<div class="title_bg aircraftCost">
						    <div class="t1">机型成本</div>
						    <div class="t2">Aircraft cost</div>
						    <i ref="aircraftLink"></i>
						</div>
						<div class="endTitle_02"></div>
					</div>
					
                    <div class="boxes">
                        <div class="box">
                            <div class="title">
                                <div class="bar"></div><span>座公里成本</span>
                            </div>
                            <div class="icon icon1"></div>
                            <div class="lists">
                                <div class="header">
                                    <span>机型</span>
                                    <span>座公里成本</span>
                                    <span>同比</span>
                                </div>
                                <div v-bind:class="{cnt: true, hd:seatKmCost.length<=4}" ref="cnt1">
                                    <table>
                                        <tr v-if="seatKmCost.length == 0"><td colspan="3">No data</td></tr>
                                        <tr v-for="item in seatKmCost">
                                            <td>{{item.plane}}</td>
                                            <td>{{item.cost}}{{item.unit}}</td>
                                            <td class="red" v-if="item.compare < 0">{{item.compare}}%<i>↓</i></td>
                                            <td class="green" v-if="item.compare > 0">{{item.compare}}%<i>↑</i></td>
                                            <td v-if="item.compare == 0">{{item.compare}}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="box">
                            <div class="title">
                                <div class="bar"></div><span>客公里成本</span>
                            </div>
                            <div class="icon icon2"></div>
                            <div class="lists">
                                <div class="header">
                                    <span>机型</span>
                                    <span>客公里成本</span>
                                    <span>同比</span>
                                </div>
                                <div v-bind:class="{cnt: true, hd:passengerKmCost.length<=4}" ref="cnt2">
                                    <table>
                                        <tr v-if="passengerKmCost.length == 0"><td colspan="3">No data</td></tr>
                                        <tr v-for="item in passengerKmCost">
                                            <td>{{item.plane}}</td>
                                            <td>{{item.cost}}{{item.unit}}</td>
                                            <td class="red" v-if="item.compare < 0">{{item.compare}}%<i>↓</i></td>
                                            <td class="green" v-if="item.compare > 0">{{item.compare}}%<i>↑</i></td>
                                            <td v-if="item.compare == 0">{{item.compare}}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="box">
                            <div class="title">
                                <div class="bar"></div><span>小时成本</span>
                            </div>
                            <div class="icon icon3"></div>
                            <div class="lists">
                                <div class="header">
                                    <span>机型</span>
                                    <span>小时成本</span>
                                    <span>同比</span>
                                </div>
                                <div v-bind:class="{cnt: true, hd:hourlyCost.length<=4}" ref="cnt3">
                                    <table>
                                        <tr v-if="hourlyCost.length == 0"><td colspan="3">No data</td></tr>
                                        <tr v-for="item in hourlyCost">
                                            <td>{{item.plane}}</td>
                                            <td>{{item.cost}}{{item.unit}}</td>
                                            <td class="red" v-if="item.compare < 0">{{item.compare}}%<i>↓</i></td>
                                            <td class="green" v-if="item.compare > 0">{{item.compare}}%<i>↑</i></td>
                                            <td v-if="item.compare == 0">{{item.compare}}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="box">
                            <div class="title">
                                <div class="bar"></div><span>飞行小时</span>
                            </div>
                            <div class="icon icon4"></div>
                            <div class="lists">
                                <div class="header">
                                    <span>机型</span>
                                    <span>飞行小时</span>
                                    <span>同比</span>
                                </div>
                                <div v-bind:class="{cnt: true, hd:flyHours.length<=4}" ref="cnt4">
                                    <table>
                                        <tr v-if="flyHours.length == 0"><td colspan="3">No data</td></tr>
                                        <tr v-for="item in flyHours">
                                            <td>{{item.plane}}</td>
                                            <td>{{item.cost}}{{item.unit}}</td>
                                            <td class="red" v-if="item.compare < 0">{{item.compare}}%<i>↓</i></td>
                                            <td class="green" v-if="item.compare > 0">{{item.compare}}%<i>↑</i></td>
                                            <td v-if="item.compare == 0">{{item.compare}}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="central">
                    <div class="t">变动成本分析</div>
                    <div class="btns">
                        <span v-bind:class="{hover:central.status=='hide'}" @click="changeStatus('hide')">隐藏</span>
                        <span v-bind:class="{hover:central.status=='show'}" @click="changeStatus('show')">全部</span>
                    </div>
                    <!-- 全部 -->
                    <div v-bind:class="[item.className]" v-if="central.status=='show'" v-for="item in central.allOpenDatas">
                        <div v-bind:class="{title:true, long:item.name.length >= 5}">{{item.name}}</div>
                        <div class="price">{{item.price}}{{item.unit}}</div>
                        <div class="item">
                            <span>同比</span>
                            <span class="g" v-if="item.tongbi > 0">{{item.tongbi}}%{{item.tongbi > 0 ? "↑":"↓"}}</span>
                            <span class="r" v-if="item.tongbi < 0">{{item.tongbi}}%{{item.tongbi > 0 ? "↑":"↓"}}</span>
                            <span v-if="item.tongbi == 0">{{item.tongbi}}%</span>
                        </div>
                        <div class="item">
                            <span>环比</span>
                            <span class="g" v-if="item.huanbi > 0">{{item.huanbi}}%{{item.huanbi > 0 ? "↑":"↓"}}</span>
                            <span class="r" v-if="item.huanbi < 0">{{item.huanbi}}%{{item.huanbi > 0 ? "↑":"↓"}}</span>
                            <span v-if="item.huanbi == 0">{{item.tongbi}}%</span>
                        </div>
                    </div>
                    <!-- 隐藏 -->
                    <!-- circle整个循环 -->
                    <!-- <div v-bind:class="{block:true, card:true,card1:true,block1:true,p1:central.flag[0][0],p2:central.flag[0][1],p3:central.flag[0][2],p4:central.flag[0][3],p5:central.flag[0][4],p6:central.flag[0][5],p7:central.flag[0][6],p8:central.flag[0][7],p9:central.flag[0][8],p10:central.flag[0][9],p11:central.flag[0][10],hover_circle:central.flag[0][3]}" v-if="central.status=='hide'" @mouseover="blockMouseover" @mouseout="blockMouseout">
                        <span class="name_hide">{{central.name_hide[0]}}</span>
                    </div>

                    <div v-bind:class="{block:true, card:true,card2:true,block2:true,p1:central.flag[1][0],p2:central.flag[1][1],p3:central.flag[1][2],p4:central.flag[1][3],p5:central.flag[1][4],p6:central.flag[1][5],p7:central.flag[1][6],p8:central.flag[1][7],p9:central.flag[1][8],p10:central.flag[1][9],p11:central.flag[1][10],hover_circle:central.flag[1][3]}" v-if="central.status=='hide'" @mouseover="blockMouseover" @mouseout="blockMouseout">
                        <span class="name_hide">{{central.name_hide[1]}}</span>
                    </div>

                    <div v-bind:class="{block:true, card:true,card3:true,block3:true,p1:central.flag[2][0],p2:central.flag[2][1],p3:central.flag[2][2],p4:central.flag[2][3],p5:central.flag[2][4],p6:central.flag[2][5],p7:central.flag[2][6],p8:central.flag[2][7],p9:central.flag[2][8],p10:central.flag[2][9],p11:central.flag[2][10],hover_circle:central.flag[2][3]}" v-if="central.status=='hide'" @mouseover="blockMouseover" @mouseout="blockMouseout">
                        <span class="name_hide">{{central.name_hide[2]}}</span>
                    </div>

                    <div v-bind:class="{block:true, card:true,card4:true,block4:true,p1:central.flag[3][0],p2:central.flag[3][1],p3:central.flag[3][2],p4:central.flag[3][3],p5:central.flag[3][4],p6:central.flag[3][5],p7:central.flag[3][6],p8:central.flag[3][7],p9:central.flag[3][8],p10:central.flag[3][9],p11:central.flag[3][10],hover_circle:central.flag[3][3]}" v-if="central.status=='hide'" @mouseover="blockMouseover" @mouseout="blockMouseout">
                        <span class="name_hide">{{central.name_hide[3]}}</span>
                    </div>

                    <div v-bind:class="{block:true, card:true,card5:true,block5:true,p1:central.flag[4][0],p2:central.flag[4][1],p3:central.flag[4][2],p4:central.flag[4][3],p5:central.flag[4][4],p6:central.flag[4][5],p7:central.flag[4][6],p8:central.flag[4][7],p9:central.flag[4][8],p10:central.flag[4][9],p11:central.flag[4][10],hover_circle:central.flag[4][3]}" v-if="central.status=='hide'" @mouseover="blockMouseover" @mouseout="blockMouseout">
                        <span class="name_hide">{{central.name_hide[4]}}</span>
                    </div>

                    <div v-bind:class="{block:true, card:true,card6:true,block6:true,p1:central.flag[5][0],p2:central.flag[5][1],p3:central.flag[5][2],p4:central.flag[5][3],p5:central.flag[5][4],p6:central.flag[5][5],p7:central.flag[5][6],p8:central.flag[5][7],p9:central.flag[5][8],p10:central.flag[5][9],p11:central.flag[5][10],hover_circle:central.flag[5][3]}" v-if="central.status=='hide'" @mouseover="blockMouseover" @mouseout="blockMouseout">
                        <span class="name_hide">{{central.name_hide[5]}}</span>
                    </div>

                    <div v-bind:class="{block:true, card:true,card7:true,block7:true,p1:central.flag[6][0],p2:central.flag[6][1],p3:central.flag[6][2],p4:central.flag[6][3],p5:central.flag[6][4],p6:central.flag[6][5],p7:central.flag[6][6],p8:central.flag[6][7],p9:central.flag[6][8],p10:central.flag[6][9],p11:central.flag[6][10],hover_circle:central.flag[6][3]}" v-if="central.status=='hide'" @mouseover="blockMouseover" @mouseout="blockMouseout">
                        <span class="name_hide">{{central.name_hide[6]}}</span>
                    </div>

                    <div v-bind:class="{block:true, card:true,card8:true,block8:true,p1:central.flag[7][0],p2:central.flag[7][1],p3:central.flag[7][2],p4:central.flag[7][3],p5:central.flag[7][4],p6:central.flag[7][5],p7:central.flag[7][6],p8:central.flag[7][7],p9:central.flag[7][8],p10:central.flag[7][9],p11:central.flag[7][10],hover_circle:central.flag[7][3]}" v-if="central.status=='hide'" @mouseover="blockMouseover" @mouseout="blockMouseout">
                        <span class="name_hide">{{central.name_hide[7]}}</span>
                    </div>

                    <div v-bind:class="{block:true, card:true,card9:true,block9:true,p1:central.flag[8][0],p2:central.flag[8][1],p3:central.flag[8][2],p4:central.flag[8][3],p5:central.flag[8][4],p6:central.flag[8][5],p7:central.flag[8][6],p8:central.flag[8][7],p9:central.flag[8][8],p10:central.flag[8][9],p11:central.flag[8][10],hover_circle:central.flag[8][3]}" v-if="central.status=='hide'" @mouseover="blockMouseover" @mouseout="blockMouseout">
                        <span class="name_hide">{{central.name_hide[8]}}</span>
                    </div>

                    <div v-bind:class="{block:true, card:true,card10:true,block10:true,p1:central.flag[9][0],p2:central.flag[9][1],p3:central.flag[9][2],p4:central.flag[9][3],p5:central.flag[9][4],p6:central.flag[9][5],p7:central.flag[9][6],p8:central.flag[9][7],p9:central.flag[9][8],p10:central.flag[9][9],p11:central.flag[9][10],hover_circle:central.flag[9][3]}" v-if="central.status=='hide'" @mouseover="blockMouseover" @mouseout="blockMouseout">
                        <span class="name_hide">{{central.name_hide[9]}}</span>
                    </div>

                    <div v-bind:class="{block:true, card:true,card11:true,block11:true,p1:central.flag[10][0],p2:central.flag[10][1],p3:central.flag[10][2],p4:central.flag[10][3],p5:central.flag[10][4],p6:central.flag[10][5],p7:central.flag[10][6],p8:central.flag[10][7],p9:central.flag[10][8],p10:central.flag[10][9],p11:central.flag[10][10],hover_circle:central.flag[10][3]}" v-if="central.status=='hide'" @mouseover="blockMouseover" @mouseout="blockMouseout">
                        <span class="name_hide">{{central.name_hide[10]}}</span>
                    </div> -->

                    <!-- 隐藏 -->
                    <!-- circle固定，卡片轮播 -->
                    <div v-bind:class="[item.classNameHide]" v-for="(item, index) in central.allOpenDatas" v-if="central.status=='hide'" @mouseover="blockMouseover" @mouseout="blockMouseout" v-bind:data-index="index">
                        <span class="name_hide">{{item.name}}</span>
                    </div>

                    <!-- 隐藏的详细数据card -->
                    <div v-bind:class="[central.card_detail_class]" v-if="central.status=='hide'">
                        <div class="card_hide">
                            <p v-bind:class="{title:true, long:central.card_hide_detail_data.name.length > 5}">{{central.card_hide_detail_data.name}}</p>
                            <div class="price">{{central.card_hide_detail_data.price}}{{central.card_hide_detail_data.unit}}</div>
                            <div class="item">
                                <span>同比</span>
                                <span class="g" v-if="central.card_hide_detail_data.tongbi > 0">{{central.card_hide_detail_data.tongbi}}%{{central.card_hide_detail_data.tongbi > 0 ? "↑":"↓"}}</span>
                                <span class="r" v-if="central.card_hide_detail_data.tongbi < 0">{{central.card_hide_detail_data.tongbi}}%{{central.card_hide_detail_data.tongbi > 0 ? "↑":"↓"}}</span>
                            </div>
                            <div class="item pt5">
                                <span>环比</span>
                                <span class="g" v-if="central.card_hide_detail_data.huanbi > 0">{{central.card_hide_detail_data.huanbi}}%{{central.card_hide_detail_data.huanbi > 0 ? "↑":"↓"}}</span>
                                <span class="r" v-if="central.card_hide_detail_data.huanbi < 0">{{central.card_hide_detail_data.huanbi}}%{{central.card_hide_detail_data.huanbi > 0 ? "↑":"↓"}}</span>
                            </div>
                        </div>
                        <div class="line1"></div>
                        <div class="line2"></div>
                    </div>
                </div>
                <div class="echarts">
                   <!-- <div class="title">
                        <div class="bar"></div>
						<span></span>
						<span></span>
                    </div> -->
					<div class="title_bg">
					    <div class="t1">成本近期趋势</div>
					    <div class="t2">Cost trend</div>
					    <!-- <i ref="estimatedLink"></i> -->
					</div>
					<div class="selecteCost">
						<ul style="cursor:pointer;">
							<!-- <li class="selClick" id="selecteCostLi1" @click="onselecteCost1($event)">整体成本</li>
							<li id="selecteCostLi2" @click="onselecteCost2($event)">变动成本</li> -->
							<li class="selClick" id="selecteCostLi1" @click="renderTestData">整体成本</li>
							<li id="selecteCostLi2" @click="renderTestData">变动成本</li>
							<li id="selecteCostLi3" @click="onselecteCost3($event,myVal)">子变动成本</li>
						</ul>
					</div>
					<div class="selectType" id="selectType" style="display:none">
						<select class="selectTypeWidth" v-model="myVal" @change="onselecteCost3($event,myVal)">
							<option v-for="item in options" :value="item.value">
								{{item.name}}
							</option>
							<!-- <option value ="10157">不正常航班</option>
						    <option value ="10158">贵宾室</option>
						    <option value="10152">航线资源费</option>
						    <option value="10156">机供品</option>
							<option value="10154">机组</option>
							<option value="10151">配餐</option>
							<option value="10149">起降</option>
							<option value="10153">小时费</option>
							<option value="10155">中转联程</option> -->
							<!-- <option value="0">机组</option>
							<option value="1">起降</option>
							<option value="2">配餐</option>
							<option value="4">中转联程</option>
							<option value="5">机供品</option>
							<option value="6">航线资源费</option>
							<option value ="7">贵宾室</option>
							<option value ="8">不正常航班</option>
							<option value="9">小时费</option> -->
						</select>
					</div>
                    <div class="echart" ref="echart">
                        <span>Loading EChart...</span>
                    </div>
					
					<div class="flightInfo">
						<div class="flightInfo_bg1">
						    <div class="flightInfo_bg1_t1">航班量</div>
						    <div class="flightInfo_bg1_t2">Flight volume</div>
							<!-- <span class="flightInfo_bg1_link" ref="estimatedLink"></span> -->
						</div>
						<div class="flightInfo_bg2">
						    <div class="flightInfo_bg2_t1">运输乘客量</div>
						    <div class="flightInfo_bg2_t2">Passenger capacity</div>
						    <!-- <span class="flightInfo_bg2_link" ref="estimatedLink"></span> -->
						</div>
					</div><!-- flightInfo -->
					
					<div class="flightInfoNum">
						<div class="flightsInfo">
							<div class="flightsInfoImg"></div>
							<div class="flightsInfoNum">
								<ul>
									<li style="font-weight: bold;" v-html="showFlightVolume"></li>
									<li style="font-size: 12px;">架次</li>
								</ul>
							</div>
						</div><!-- flightsInfo -->
						<div class="passengerCapacity">
							<div class="passengerCapacityImg"></div>
							<div class="passengerCapacityNum">
								<ul>
									<li style="font-weight: bold;" id="YSCKL"></li>
									<li style="font-size: 12px;">万人次</li>
								</ul>
							</div>
						</div><!-- passengerCapacity -->
					</div><!-- flightInfoNum -->
					
                </div>
            </div><!-- /#page -->
        </div><!-- /#wrapper -->        
    </script>
</body>
</html>
<script src="js/common.js?ver=20211218"></script>
<script src="js/a5-1.js?ver=20181115"></script>
