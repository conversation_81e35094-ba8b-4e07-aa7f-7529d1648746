/** 补贴 */
(function () {
    dfd.done(function () {
        if (!hasAllCompanyPermission()) {
            $(".ROUTE_SUBSIDY_INC .detail").remove();
            return;
        }
    })

    $(".ROUTE_SUBSIDY_INC .detail").on("click", function () {
        $("#pop_subsidy").removeClass("hide");
        $(".windowMask").removeClass("hide");

        var dateId = $(".zongshouru").attr("dateKey");
        var dateType = getDateType();

        if (window.subsidyWin == null) {
            initsubsidyWin(dateType,dateId);
        } else {
            window.subsidyWin.refreshView(dateType,dateId);
        }
    });


    function initsubsidyWin(dateType,dateId) {
        var page = new Vue({
            el: '.subsidy-win-body',
            template: $("#subsidy_template").html(),
            data: function () {
                return {
                    "dateType": "D",
                    "weeks": weeks,
                    queryDate1: null,
                    queryDate2: null,
                    data1:[],
                    showWeekList: false,
                    selectedWeek: null,
                    weekCmpActive: false,
                    showFirstTab: true,
                    data: {},
                    barDataList1:[]
  
                }
            },
            mounted: function () {
                var me = this;

                if (this.weeks.length > 1) {
                    this.selectedWeek = weeks[1]
                }

                $(me.$refs["datetimepicker_D"]).datetimepicker({
                    defaultDate: moment().subtract(1, "days")._d,
                    format: "YYYY/MM/DD",
                    sideBySide: true,
                    maxDate: moment().subtract(1, "days")._d,
                    widgetPositioning: {
                        horizontal: 'right'
                    },
                    locale: 'zh-cn'
                });
                //月
                $(me.$refs['datetimepicker_M']).datetimepicker({
                    defaultDate: new Date(),
                    format: "YYYY/MM",
                    viewMode: 'months',
                    sideBySide: true,
                    maxDate: new Date(),
                    widgetPositioning: {
                        horizontal: 'right'
                    },
                    locale: 'zh-cn'
                });
                //年
                $(me.$refs['datetimepicker_Y']).datetimepicker({
                    defaultDate: new Date(),
                    format: "YYYY",
                    viewMode: 'years',
                    sideBySide: true,
                    maxDate: new Date(),
                    widgetPositioning: {
                        horizontal: 'right'
                    },
                    locale: 'zh-cn'
                });

                me.setDatePickerValue(dateType, dateId);
                //先查数据,再做下面事件监听,不然可能会触发dp.change事件,多做一次请求
                me.queryData(me.getDate());

                $(me.$refs["datetimepicker_D"]).on("dp.change", function (e) {
                    me.queryData(e.date._d);

                });
                $(me.$refs['datetimepicker_M']).on("dp.change", function (e) {
                    me.queryData(e.date._d);
                });
                $(me.$refs['datetimepicker_Y']).on("dp.change", function (e) {
                    me.queryData(e.date._d);
                });
                me.mounted = true;
                me.hackEChartDom();
            },
            computed: {
                companyStyle() {
                    let maxWith = Math.ceil(this.data1.length / 2) * 190;
                    return `max-width:${maxWith}px`;
                },
            },
            methods: {
                hackEChartDom(){
                    var me = this;
                    var scale = 1/pageZoomScale;
                    $(me.$el).find(".chartblock").css('zoom', scale);
                    $(me.$el).find(".chartblock").css('-moz-transform', 'scale(' + scale + ',' + scale + ')');
                    $(me.$el).find(".chartblock").css('-moz-transform-origin', 'left top');
                    $(me.$el).find(".chartblock").each(function(index, el) {
                        var width = $(this).width();
                        var height = $(this).height();
                        $(this).css('width', width * pageZoomScale + 'px');
                        $(this).css('height', height * pageZoomScale + 'px');
                    });
                },
                getStyle(companyCode) {
                    return `background: url(./img/logo_${companyCode}.svg) no-repeat right center;background-size: 24px;`
                },
                setDatePickerValue(dateType, dateId) {
                    var me = this;
                    if (dateType != 'L') {
                        $(me.$refs[`datetimepicker_${dateType}`]).data("DateTimePicker").date(me.getDateStr(dateType, dateId));
                    } else {
                        var selectedWeek = me.weeks.find(v => { return v.DATE_ID == dateId });
                        if (me.selectedWeek != selectedWeek) {
                            me.selectedWeek = selectedWeek;
                            //改变才数据,且在mounted 之后,
                            if (me.mounted) {
                                me.queryData(selectedWeek);
                            }

                        }

                    }
                },
                refreshView(dateType, dateId) {
                    var me = this;
                    if (me.dateType != dateType) {
                        me.switchDateType(dateType);
                    } else {
                        me.setDatePickerValue(dateType, dateId);
                    }

                },
                getDateStr(dateType, dateId) {
                    if (dateType == 'D') {
                        return moment(dateId, 'YYYYMMDD').format('YYYY/MM/DD');
                    } else if (dateType == 'M') {
                        return moment(dateId, 'YYYYMM').format('YYYY/MM');;
                    } else if (dateType == 'Y') {
                        return dateId;
                    }
                },
                getDate() {
                    var me = this;
                    var dateType = this.dateType;
                    if (dateType == 'D') {
                        return $(me.$refs['datetimepicker_D']).data("DateTimePicker").date().startOf("day")._d;
                    } else if (dateType == 'L') {
                        return this.selectedWeek;
                    } else if (dateType == 'M') {
                        return $(me.$refs['datetimepicker_M']).data("DateTimePicker").date().startOf("day")._d
                    } else if (dateType == 'Y') {
                        return $(me.$refs['datetimepicker_Y']).data("DateTimePicker").date().startOf("day")._d
                    }
                },
                getWeekDesc() {
                    return this.selectedWeek ? this.selectedWeek.DATE_DESC_XS : ''
                },
                onWeekMouseOut() {
                    this.weekCmpActive = false;
                    setTimeout(this.validActive, 300)
                },
                onWeekMouseOver() {
                    this.weekCmpActive = true;
                },
                validActive() {
                    if (!this.weekCmpActive) {
                        this.showWeekList = false;
                    }
                },
                dropWeekList() {
                    this.showWeekList = true;
                },
                onWeekChange(week) {
                    this.selectedWeek = week;
                    this.showWeekList = false;
                    this.queryData(week);
                },
                getWeekLabel: function (item) {
                    if (item) {
                        var week = item.DATE_ID.substring(5, 7);
                        var year = item.DATE_ID.substring(0, 4);
                        return `${year}年第${week}周`;
                    }
                },
                closeWin: function () {
                    $("#pop_subsidy").addClass("hide");
                    $(".windowMask").addClass("hide");
                },
                switchDateType(dateType) {
                    this.dateType = dateType;
                    this.queryData(this.getDate());
                },
                onTabChange(showFirstTab) {
                    this.showFirstTab = showFirstTab;
                    this.drawBar();
                },
                isSameDate(d1, d2) {
                    if (!d1) {
                        return false;
                    }
                    return moment(d1).diff(moment(d2)) == 0;
                },
                queryData(date) {
                    var me = this;
                    //补贴收入 SUBSIDY_INC 10191
                    //航线补贴收入 ROUTE_SUBSIDY_INC 10203
                    //政策补贴收入 POLICY_SUBSIDY_INC 10206 
                    var params = {
                        "DATE_ID": getDateId(date, this.dateType),
                        "DATE_TYPE": this.dateType,
                        "COMP_CODE": getAllSubCompany().join(","),
                        "KPI_CODE": "ROUTE_SUBSIDY_INC"
                    };

                    eking.ui.loading.show();

                    $.ajax({
                        type: 'post',
                        url: "/bi/query/getfaccomkpi",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(params),
                        success: function (res) {
                            eking.ui.loading.hide();
                            var data = res.data;
                            var data1 = [];
                            var barDataList1= [];
                          for (var p in data) {
                              console.log('getfaccomkpi',p)
                                var companyData = data[p];
                                data1.push({
                                    companyCode: p,
                                    companyName: companyCode2Name[p],
                                    data: companyData
                                });
                                barDataList1.push({
                                    companyCode: p,
                                    companyName: companyCode2Nameabbr[p],
                                    // policyInc:toFixed(companyData['POLICY_SUBSIDY_INC'][0].KPI_VALUE,1),
                                    // policyIncTq:toFixed(companyData['POLICY_SUBSIDY_INC'][0].KPI_VALUE_TQ,1),
                                    routeInc:toFixed(companyData['ROUTE_SUBSIDY_INC'][0].KPI_VALUE,1),
                                    routeIncTq:toFixed(companyData['ROUTE_SUBSIDY_INC'][0].KPI_VALUE_TQ,1)
                                })
                            }
                            data1.sort(function (a, b) {
                                return getCompanyIndex(a.companyCode) - getCompanyIndex(b.companyCode)
                            });
                            barDataList1.sort(function (a, b) {
                                return getCompanyIndex(a.companyCode) - getCompanyIndex(b.companyCode)
                            });
                            me.data1 = data1;
                            me.barDataList1 =barDataList1;
                            setTimeout(function () { me.drawBar()}, 300);

                        }
                    });
                },
                getKpiValue(item){
                   return toFixed(item.data.ROUTE_SUBSIDY_INC[0].KPI_VALUE,1);
                },
                getTqRadio(item){
                    let value = item.data.ROUTE_SUBSIDY_INC[0].KPI_RATIO_TQ;
                    if(value<0){
                      return   toFixed(value*100,2) +'%'+ "↓"
                    }else if(value>0){
                        return   toFixed(value*100,2) +'%'+ "↑"
                    }else{
                        return "0.00%"
                    }
                },
                drawBar() {
                    var me = this;
                    var colors = [
                        ['#00c311', '#007892'], //柱状图渐变颜色
                        ['#ff2929', '#b23e5c'], //柱状图渐变颜色
                    ];
                    echarts.dispose(this.$refs.ecDom);
                    let chart = echarts.init(this.$refs.ecDom, null, { renderer: 'svg' });
                    chart.setOption({
                        dataset: [
                            { source: me.barDataList1 }
                        ],
                        legend: {
                            data: ['本期', '同期'],
                            textStyle: { color: '#44a3f4' },
                            top: 10
                        },
                        grid: {
                            left: '5%', // 与容器左侧的距离
                            right: '5%', // 与容器右侧的距离
                            top: '20%', // 与容器顶部的距离
                            bottom: '5%', // 与容器底部的距离,
                            containLabel: true
                        },
                        tooltip: {
                            trigger: "axis",
                            axisPointer: {
                                type: "cross",
                                crossStyle: {
                                    color: "#00a2ff"
                                }
                            }
                        },
                        yAxis: [{
                            type: 'value',
                            name: '万元',
                            nameTextStyle: {
                                color: '#41a8ff',
                                fontSize: getChartFontSize(16)
                            },
                            axisLabel: {
                                textStyle: {
                                    color: '#41a8ff',
                                    fontSize: getChartFontSize(16)
                                },
                                formatter: '{value}'
                            },
                            axisLine: {
                                lineStyle: {
                                    color: 'rgba(0,0,0,0)'
                                }
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                                }
                            },
                        }],
                        xAxis: [
                            {
                                type: "category",
                                axisLabel: {
                                    color: "#00a2ff",
                                    fontSize: getChartFontSize(16)
                                },
                                color: "#00a2ff",
                                axisLine: {
                                    lineStyle: {
                                        color: "#00a2ff",
                                        width: "2"
                                    }
                                }
                            }
                        ],
                        series: [
                            {
                                type: "bar",
                                label: {
                                    show: true,
                                    color: '#FFFFFF',
                                    position: 'top'
                                },
                                dimensions: ["companyName", "routeInc" ],
                                name: "本期",
                                datasetIndex: 0,

                                itemStyle: {
                                    normal: {
                                        color: new echarts.graphic.LinearGradient(
                                            0, 0, 0, 1,       //4个参数用于配置渐变色的起止位置, 这4个参数依次对应右/下/左/上四个方位. 而0 0 0 1则代表渐变色从正上方开始
                                            [
                                                { offset: 0, color: '#6cbbff' },
                                                { offset: 1, color: '#4448f4' }
                                            ]                //数组, 用于配置颜色的渐变过程. 每一项为一个对象, 包含offset和color两个参数. offset的范围是0 ~ 1, 用于表示位置
                                        )
                                    }
                                }
                            },
                            {
                                type: "bar",
                                label: {
                                    show: true,
                                    color: '#FFFFFF',
                                    position: 'top'
                                },
                                dimensions: ["companyName","routeIncTq"],
                                name: "同期",
                                datasetIndex: 0,
                                itemStyle: {
                                    normal: {
                                        color: new echarts.graphic.LinearGradient(
                                            0, 0, 0, 1, [{
                                                offset: 0,
                                                color: '#36d57f'
                                            }, {
                                                offset: 1,
                                                color: '#187b45'
                                            }]),
                                    }
                                }

                            }
                        ]
                    });
                    chart.resize();
                }

            }
        });

        window.subsidyWin = page;

    }





})()