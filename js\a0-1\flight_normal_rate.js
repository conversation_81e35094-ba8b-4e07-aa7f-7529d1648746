(function () {
    dfd.done(function () {
        if (!hasAllCompanyPermission()) {
            $(".nomflght .detail").remove();
            return;
        }
    })

    $(".nomflght .detail").on("click", function () {
        $("#pop_flight_noraml_rate").removeClass("hide");
        $(".windowMask").removeClass("hide");
        var dateId = $(".nomflght .detail").attr("dateId");
        var dateType = getDateType();
        if (window.flightRateWin == null) {
            initNormalRateWin(dateType, dateId);
        } else {
            window.flightRateWin.refreshView(dateType, dateId);
        }
    });


    function initNormalRateWin(dateType, dateId) {
        var page = new Vue({
            el: '.flightNormalRateContainer',
            template: $("#noraml_rate_template").html(),
            data: function () {
                return {
                    "dateType": dateType,
                    "weeks": weeks,
                    showWeekList: false,
                    selectedWeek: null,
                    weekCmpActive: false,
                    mounted: false,
                    isToday: false,
                }
            },
            mounted: function () {
                //日
                var me = this;

                if (this.weeks.length > 1) {
                    this.selectedWeek = weeks[1]
                }

                $(me.$refs["datetimepicker_D"]).datetimepicker({
                    defaultDate: new Date(),
                    format: "YYYY/MM/DD",
                    sideBySide: true,
                    maxDate: new Date(),
                    widgetPositioning: {
                        horizontal: 'right'
                    },
                    locale: 'zh-cn'
                });
                //月
                $(me.$refs['datetimepicker_M']).datetimepicker({
                    defaultDate: new Date(),
                    format: "YYYY/MM",
                    viewMode: 'months',
                    sideBySide: true,
                    maxDate: new Date(),
                    widgetPositioning: {
                        horizontal: 'right'
                    },
                    locale: 'zh-cn'
                });
                //年
                $(me.$refs['datetimepicker_Y']).datetimepicker({
                    defaultDate: new Date(),
                    format: "YYYY",
                    viewMode: 'years',
                    sideBySide: true,
                    maxDate: new Date(),
                    widgetPositioning: {
                        horizontal: 'right'
                    },
                    locale: 'zh-cn'
                });

                me.setDatePickerValue(dateType, dateId);
                //先查数据,再做下面事件监听,不然可能会触发dp.change事件,多做一次请求
                me.queryData(me.getDate());

                $(me.$refs["datetimepicker_D"]).on("dp.change", function (e) {
                    me.queryData(e.date._d);

                });
                $(me.$refs['datetimepicker_M']).on("dp.change", function (e) {
                    me.queryData(e.date._d);
                });
                $(me.$refs['datetimepicker_Y']).on("dp.change", function (e) {
                    me.queryData(e.date._d);
                });
                me.mounted = true;
                me.hackEChartDom();
            },
            computed:{
                normalRateLengend(){
                    return this.isToday ? '当前实际正常率' : '实际正常率'
                }
            },
            methods: {
                hackEChartDom() {
                    var me = this;
                    var scale = 1 / pageZoomScale;
                    $(me.$el).find(".chartblock").css('zoom', scale);
                    $(me.$el).find(".chartblock").css('-moz-transform', 'scale(' + scale + ',' + scale + ')');
                    $(me.$el).find(".chartblock").css('-moz-transform-origin', 'left top');
                    $(me.$el).find(".chartblock").each(function (index, el) {
                        var width = $(this).width();
                        var height = $(this).height();
                        $(this).css('width', width * pageZoomScale + 'px');
                        $(this).css('height', height * pageZoomScale + 'px');
                    });
                },
                setDatePickerValue(dateType, dateId) {
                    var me = this;
                    if (dateType != 'L') {
                        $(me.$refs[`datetimepicker_${dateType}`]).data("DateTimePicker").date(me.getDateStr(dateType, dateId));
                    } else {
                        var selectedWeek = me.weeks.find(v => { return v.DATE_ID == dateId });
                        if (me.selectedWeek != selectedWeek) {
                            me.selectedWeek = selectedWeek;
                            //改变才数据,且在mounted 之后,
                            if (me.mounted) {
                                me.queryData(selectedWeek);
                            }

                        }

                    }
                },
                refreshView(dateType, dateId) {
                    var me = this;
                    if (me.dateType != dateType) {
                        me.switchDateType(dateType);
                    } else {
                        me.setDatePickerValue(dateType, dateId);
                    }

                },
                getDateStr(dateType, dateId) {
                    if (dateType == 'D') {
                        return moment(dateId, 'YYYYMMDD').format('YYYY/MM/DD');
                    } else if (dateType == 'M') {
                        return moment(dateId, 'YYYYMM').format('YYYY/MM');;
                    } else if (dateType == 'Y') {
                        return dateId;
                    }
                },
                getDate() {
                    var me = this;
                    var dateType = this.dateType;
                    if (dateType == 'D') {
                        return $(me.$refs['datetimepicker_D']).data("DateTimePicker").date().startOf("day")._d;
                    } else if (dateType == 'L') {
                        return this.selectedWeek;
                    } else if (dateType == 'M') {
                        return $(me.$refs['datetimepicker_M']).data("DateTimePicker").date().startOf("day")._d
                    } else if (dateType == 'Y') {
                        return $(me.$refs['datetimepicker_Y']).data("DateTimePicker").date().startOf("day")._d
                    }
                },
                getWeekDesc() {
                    return this.selectedWeek ? this.selectedWeek.DATE_DESC_XS : ''
                },
                onWeekMouseOut() {
                    this.weekCmpActive = false;
                    setTimeout(this.validActive, 300)
                },
                onWeekMouseOver() {
                    this.weekCmpActive = true;
                },
                validActive() {
                    if (!this.weekCmpActive) {
                        this.showWeekList = false;
                    }
                },
                dropWeekList() {
                    this.showWeekList = true;
                },
                onWeekChange(week) {
                    this.selectedWeek = week;
                    this.showWeekList = false;
                },
                getWeekLabel: function (item) {
                    if (item) {
                        var week = item.DATE_ID.substring(5, 7);
                        var year = item.DATE_ID.substring(0, 4);
                        return `${year}年第${week}周`;
                    }
                },
                closeWin: function () {
                    $("#pop_flight_noraml_rate").addClass("hide");
                    $(".windowMask").addClass("hide");
                },
                switchDateType(dateType) {
                    this.dateType = dateType;
                    this.queryData(this.getDate());
                },
                isSameDate(d1, d2) {
                    if (!d1) {
                        return false;
                    }
                    return moment(d1).diff(moment(d2)) == 0;
                },
                checkIsToday() {
                    if (this.dateType == 'D') {
                        this.isToday = this.isSameDate(this.getDate(), moment().startOf('day')._d);
                        return this.isToday;
                    }
                    this.isToday = false;
                    return this.isToday;
                },
                queryToday() {
                    var me = this;
                    eking.ui.loading.show();
                    var dataSource = [];
                    var markRate = null;
                    var params = {
                        "companyCodes": getAllSubCompanyExcludeY8AndHnaIn().join(","), // 公司二字码
                        "isLongRegsNotIn": false,
                        "stdStartUtcTime": moment().startOf('day').utc().format("YYYY-MM-DD HH:mm:ss"),
                        "stdEndUtcTime": moment().endOf('day').utc().format("YYYY-MM-DD HH:mm:ss")
                    };
                    var d1 = $.ajax({
                        type: 'post',
                        url: "/bi/web/flightAmountStatics",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(params),
                        success: function (response) {
                            var data = response.data;// 按公司分组输出，具体看各个对象输出字段
                            for (var companyCode in data) {
                                var item = data[companyCode];

                                // 计划航班总数: pftc
                                // 国际计划航班总数：pftci
                                // 国内计划航班总数：pftcl
                                // 已执行航班总数：cftc
                                // 国际已执行航班总数：cftci
                                // 国内已执行航班总数：cftci
                                // 已执行航班中的正常航班比率 cfPercent
                                // 计划航班中的正常航班比率 pfPercent
                                // 公司原因不正常航班总数 abfcr
                                // 公司原因不正常的比率 abfcr/pftc


                                var SCH_NO = item['pftc'];
                                var normalRatePlan = Number(item['pfdappPercent']).toFixed(1);
                                var normalRate = Number(item['cfdappPercent']).toFixed(1);

                                var companyReasonRate = 0.0;
                                var value = item['abfcr'];
                                if (value) {
                                    companyReasonRate = SCH_NO > 0 ? Number(value * 100 / SCH_NO).toFixed(1) : 0.0;
                                }


                                dataSource.push({
                                    companyname: companyCode2Nameabbr[companyCode],
                                    companyCode: companyCode,
                                    normalRate: normalRate,
                                    normalRatePlan: normalRatePlan,
                                    companyReasonRate: companyReasonRate
                                })
                            }

                        }
                    });

                    var params3 = {
                        "companyCodes": 'Y8', // 公司二字码
                        "acOwners": "Y8", // 固定传金鹏过滤非全货机
                        "acTypeNotIn": "D00,D10", //需要过滤掉的机型, 
                        "isLongRegsNotIn": true,
                        "stdStartUtcTime": moment().startOf('day').utc().format("YYYY-MM-DD HH:mm:ss"),
                        "stdEndUtcTime": moment().endOf('day').utc().format("YYYY-MM-DD HH:mm:ss")
                    };
                    var d3 = $.ajax({
                        type: 'post',
                        url: "/bi/web/flightAmountStatics",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(params3),
                        success: function (response) {
                            var data = response.data;// 按公司分组输出，具体看各个对象输出字段
                            for (var companyCode in data) {
                                var item = data[companyCode];

                                // 计划航班总数: pftc
                                // 国际计划航班总数：pftci
                                // 国内计划航班总数：pftcl
                                // 已执行航班总数：cftc
                                // 国际已执行航班总数：cftci
                                // 国内已执行航班总数：cftci
                                // 已执行航班中的正常航班比率 cfPercent
                                // 计划航班中的正常航班比率 pfPercent
                                // 公司原因不正常航班总数 abfcr
                                // 公司原因不正常的比率 abfcr/pftc


                                var SCH_NO = item['pftc'];
                                var normalRatePlan = Number(item['pfdappPercent']).toFixed(1);
                                var normalRate = Number(item['cfdappPercent']).toFixed(1);

                                var companyReasonRate = 0.0;
                                var value = item['abfcr'];
                                if (value) {
                                    companyReasonRate = SCH_NO > 0 ? Number(value * 100 / SCH_NO).toFixed(1) : 0.0;
                                }


                                dataSource.push({
                                    companyname: companyCode2Nameabbr[companyCode],
                                    companyCode: companyCode,
                                    normalRate: normalRate,
                                    normalRatePlan: normalRatePlan,
                                    companyReasonRate: companyReasonRate
                                })
                            }

                        }
                    });

                    var params2 = {
                        "companyCodes": getAllSubCompany(), // 公司二字码
                        "acOwners": "Y8", // 固定传金鹏过滤非全货机
                        "acTypeNotIn": "D00,D10", //需要过滤掉的机型, 
                        "isLongRegsNotIn": true,
                        "stdStartUtcTime": moment().startOf('day').utc().format("YYYY-MM-DD HH:mm:ss"),
                        "stdEndUtcTime": moment().endOf('day').utc().format("YYYY-MM-DD HH:mm:ss")
                    };

                    var d2 = $.ajax({
                        type: 'post',
                        url: "/bi/web/flightAmountStaticV2",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(params2),
                        success: function (response) {
                            var data = response;// 按公司分组输出，具体看各个对象输出字段
                            markRate = Number(data['pfdappPercent']).toFixed(1);
                        }
                    });

                    var dtds = [d1, d3, d2];
                    $.when.apply(this, dtds).then(function () {
                        eking.ui.loading.hide();

                        dataSource.sort(function (a, b) {
                            return getCompanyIndex(a.companyCode) - getCompanyIndex(b.companyCode)
                        });

                        me.drawChart(dataSource, markRate);
                    });
                },

                queryData(date) {
                    var me = this;
                    if (me.checkIsToday()) {
                        me.queryToday();
                        return;
                    }
                    eking.ui.loading.show();
                    var kpi_list = [
                        'SCH_NO', //计划班次
                        'NORMAL_RATE_ZT_DENOMINATOR', //计划班次
                        "NORMAL_RATE_ZT_MOLECULE"//正常班次
                    ];

                    var param = {
                        "LIMIT": 1,
                        "QUERY_DATE": formatDate(getEndDate(date, this.dateType)),
                        "DATE_TYPE_CN": getDateTypeCn(this.dateType),// 例会周L、年Y、月M、日D
                        "DATE_TYPE": this.dateType,
                        'KPI_CODE': kpi_list.join(','),
                        'COMP_CODE': getAllCompany(true).join(",")
                    }
                    let data, data2;
                    var d1 = $.ajax({
                        type: 'post',
                        url: "/bi/query/getfaccomkpi?queryNormalRate",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(param),
                        success: function (response) {
                            data = response.data;

                        }
                    });
                    var resaonParam = {
                        "DATE_ID": getDateId(date, this.dateType),
                        "DATE_TYPE": this.dateType,
                        "COMP_CODE": getAllCompany(true).join(","),
                        "KPI_CODE": "DELAY_NO",
                        "CLASSIFY": "公司原因",
                        "IS_GROUPBY_CLASSIFY": "true"
                    }

                    var d2 = $.ajax({
                        type: 'post',
                        url: "/bi/query/getfaccompdelaycausekpi?companyReason",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(resaonParam),
                        success: function (response) {
                            data2 = response.data;
                        }
                    });


                    var dtds = [d1, d2];
                    $.when.apply(this, dtds).then(function () {
                        eking.ui.loading.hide();
                        var dataSource = [];
                        var markRate = null;
                        for (var companyCode in data) {
                            var item = data[companyCode];
                            var NORMAL_RATE_ZT_DENOMINATOR = item['NORMAL_RATE_ZT_DENOMINATOR'][0].KPI_VALUE;
                            var SCH_NO = item['SCH_NO'][0].KPI_VALUE;
                            var NORMAL_NO_T = item['NORMAL_RATE_ZT_MOLECULE'][0].KPI_VALUE;
                            var normalRate = NORMAL_RATE_ZT_DENOMINATOR > 0 ? Number(NORMAL_NO_T * 100 / NORMAL_RATE_ZT_DENOMINATOR).toFixed(1) : 0.0;

                            var companyReasonItem = data2[companyCode];
                            var companyReasonRate = 0;
                            if (companyReasonItem) {
                                var value = companyReasonItem['DELAY_NO'][0].KPI_VALUE;
                                companyReasonRate = SCH_NO > 0 ? Number(value * 100 / SCH_NO).toFixed(1) : 0.0;
                            }

                            if (companyCode == 'HNAHK') {
                                markRate = normalRate;
                            } else {
                                dataSource.push({
                                    companyname: companyCode2Nameabbr[companyCode],
                                    companyCode: companyCode,
                                    normalRate: normalRate,
                                    companyReasonRate: companyReasonRate
                                })
                            }


                        }
                        dataSource.sort(function (a, b) {
                            return getCompanyIndex(a.companyCode) - getCompanyIndex(b.companyCode)
                        });
                        me.drawChart(dataSource, markRate);
                    });
                },
                drawChart(dataSource, markRate) {
                    var me = this;
                    echarts.dispose(me.$refs['ecDom']);
                    let chart = echarts.init(this.$refs.ecDom);

                    var series = [
                        {
                            type: "bar",
                            label: {
                                show: true,
                                color: function (params) {
                                    // 给出颜色组
                                    if (params.value.normalRate > markRate) {
                                        return "#00bc54";
                                    } else {
                                        return "#ff0000";
                                    }
                                },
                                position: 'top'
                            },
                            dimensions: ["companyname", "normalRate"],
                            name: me.normalRateLengend,
                            datasetIndex: 0,
                            markLine: {
                                symbol: 'none',
                                data: [{
                                    yAxis: markRate
                                }],
                                silent: false,
                                lineStyle: {
                                    color: '#1699e4',
                                    type: 'solid',
                                },
                                label: {
                                    normal: {
                                        position: 'end',
                                        formatter: '{c}%',
                                    }
                                }
                            },
                            itemStyle: {
                                normal: {
                                    color: function (params) {
                                        // 给出颜色组
                                        if (new Number(params.value.normalRate) > new Number(markRate)) {
                                            return new echarts.graphic.LinearGradient(
                                                0, 0, 0, 1,       //4个参数用于配置渐变色的起止位置, 这4个参数依次对应右/下/左/上四个方位. 而0 0 0 1则代表渐变色从正上方开始
                                                [
                                                    { offset: 0, color: '#00bc54' },
                                                    { offset: 1, color: '#00798f' }
                                                ]                //数组, 用于配置颜色的渐变过程. 每一项为一个对象, 包含offset和color两个参数. offset的范围是0 ~ 1, 用于表示位置
                                            )
                                        } else {
                                            return new echarts.graphic.LinearGradient(
                                                0, 0, 0, 1,       //4个参数用于配置渐变色的起止位置, 这4个参数依次对应右/下/左/上四个方位. 而0 0 0 1则代表渐变色从正上方开始
                                                [
                                                    { offset: 0, color: '#fd2a2a' },
                                                    { offset: 1, color: '#b33d5b' }
                                                ]                //数组, 用于配置颜色的渐变过程. 每一项为一个对象, 包含offset和color两个参数. offset的范围是0 ~ 1, 用于表示位置
                                            )
                                        }
                                    }
                                }
                            }
                        }

                    ];

                    if (me.checkIsToday()) {
                        series.push({
                            type: "bar",
                            label: {
                                show: true,
                                color: '#0765f1',
                                position: 'top'
                            },
                            dimensions: ["companyname", "normalRatePlan"],
                            name: "当前预估正常率",
                            datasetIndex: 0,
                            itemStyle: {
                                normal: {
                                    color: '#0765f1'
                                }
                            }
                        })
                    }
                    series.push({
                        type: "bar",
                        label: {
                            show: true,
                            color: '#e2c006',
                            position: 'top'
                        },
                        dimensions: ["companyname", "companyReasonRate"],
                        name: "公司原因延误率",
                        datasetIndex: 0,
                        itemStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(
                                    0, 0, 0, 1, [{
                                        offset: 0,
                                        color: '#e2c006'
                                    }, {
                                        offset: 1,
                                        color: '#c47b00'
                                    }]),
                            }
                        }

                    })
                    chart.setOption({
                        dataset: [
                            { source: dataSource },
                            { source: dataSource }
                        ],
                        grid: {
                            top: 30,
                            left: 80,
                            right: 80,
                            bottom: 30,
                        },
                        tooltip: {
                            trigger: "axis",
                            axisPointer: {
                                type: "cross",
                                crossStyle: {
                                    color: "#00a2ff"
                                }
                            }
                        },
                        yAxis: [{
                            type: 'value',
                            name: '',
                            nameTextStyle: {
                                color: '#41a8ff',
                                fontSize: getChartFontSize(16)
                            },
                            axisLabel: {
                                textStyle: {
                                    color: '#41a8ff',
                                    fontSize: getChartFontSize(16)
                                },
                                formatter: '{value}%'
                            },
                            axisLine: {
                                lineStyle: {
                                    color: 'rgba(0,0,0,0)'
                                }
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: ['rgba(255,255,255,0.05)'] // 分割线颜色
                                }
                            },
                        }],
                        xAxis: [
                            {
                                type: "category",
                                axisLabel: {
                                    color: "#00a2ff"
                                },
                                color: "#00a2ff",
                                axisLabel: {
                                    fontSize: getChartFontSize(16)
                                },
                                axisLine: {
                                    lineStyle: {
                                        color: "#00a2ff",
                                        width: "2"
                                    }
                                }
                            }
                        ],
                        series: series
                    });
                }

            }
        });

        window.flightRateWin = page;

    }

})()