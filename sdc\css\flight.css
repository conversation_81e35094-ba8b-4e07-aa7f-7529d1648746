/* head */
.page-bgimg {
	position: absolute;
	top: 0px;
	width: 2513px;
	height: 768px;
	background: url(../img/indexbg.png) no-repeat 0 0;
}
.page-wrapper {
	position: absolute;
	top: 0px;
	width: 2513px;
	height: 768px;
	overflow-x: hidden;
	overflow-y: hidden;
	pointer-events: none;
}

.close-btn{
    width: 30px;
    height: 30px;
    background: url(../img/close.svg) no-repeat 0 0;
    position: absolute;
    top: 27px;
    right: 120px;
	background-size: 30px 30px;
	z-index: 9000;
}
.leftLogo {
	position: absolute;
	top: 16px;
	left: 20px;
	width: 50px;
	height: 65px;
}


.compLogo {
	width: 48px;
	height: 48px;
	background: url(../img/log_r_hna.png) no-repeat 0 0;
}

.companyLogo {
	position: absolute;
	width: 40px;
	height: 40px;
	top: 10px;
	left: 894px;
	background: url(../img/logo_HU.png) no-repeat 0 0;
}
/* 
.flightNoInfo {
	position: relative;
	top: 10px;
	left: 955px;
}


.flightNo {
	letter-spacing: 2px;
}

.flightTimes {
	letter-spacing: -1px;
}

.flightStatus {
	position: absolute;
	top: 10px;
	left: 1080px;
	height: 100px;
}

.departing,
.stopover,
.destination {
	display: inline-block;
	width: 80px;
	text-align: center;
}

.stopover,
.destination {
	margin-left: 110px;
}

.flightHX_1 {
	position: absolute;
	left: 75px;
	top: 15px;
	width: 119px;
	height: 17px;
	background: url(../img/hx_1.png) no-repeat 0 0;
}

.flightHX_2 {
	position: absolute;
	left: 275px;
	top: 20px;
	width: 119px;
	height: 17px;
	background: url(../img/hx_2.png) no-repeat 0 0;
}

.plane {
	position: absolute;
	left: 1585px;
	top: 24px;
	width: 24px;
	height: 24px;
	background: url(../img/plane.png) no-repeat 0 0;
}

.types {
	position: absolute;
	left: 1620px;
	top: 25px;
	color: #fff11b;
} */

/* topMid */


.con_flex_column{ display: -webkit-flex; -webkit-flex-flow: column;}
.con_flex_row{ display: -webkit-flex; -webkit-flex-flow: row;}
.con_flex_row div{position: initial;}
.con_flex_row .c4{
	line-height: 35px;
}
.con_flex_row .c4 .fs12{
	text-align: center;
	line-height: 20px;
}
.con_flex_row .c2{
	justify-content: center;
}
.con_flex_row .c2 .t2,.con_flex_row .c2 .t1{
	text-align: center;
}
.flex_none{-webkit-flex:none;}
.flex_1{-webkit-flex:1;}


.col_top {
	pointer-events: auto;
	position: absolute;
	top: 10px;
	left: 830px;
	width: 892px;
	height: 66px;
	background-color: rgba(255,0,0,0.0);
}

.col_top div{
	position: relative;
}

.col_top .co1{
	text-align: center;
}
.col_top .co3{
	text-align: center;
}

.col_top .mid .c1{
	position: relative;
}
.col_top .mid .c2{
	position: relative;
	text-align: center;
	width: 200px;
}
.col_top .mid .c2 .arr{
	width: 100%;
	height: 18px;
	background: url(../img/flight.arr2.png?1) no-repeat top center;
}
.col_top .mid .c3{
	position: relative;
	text-align: right;
}
.col_top .mid .t1{
	position: relative;
	font-size: 16px;
	font-weight: bold;
}
.col_top .mid .t2{
	position: relative;
	font-size: 14px;
	color: #77BAF8;
}

.col_top .fltno{
	display: inline-block;
	height: 40px;
	font-size: 24px;
	font-weight: bold;
	line-height: 40px;
	padding-left: 46px;
	background-repeat: no-repeat;
	background-position: 0 center;
	background-size: auto 40px;
}
.col_top .flightTimes{
	position: absolute;
	left: 110px;
	top: 35px;
}

.col_top .sts{
	display: inline-block;
	height: 40px;
	font-size: 18px;
	font-weight: bold;
	line-height: 40px;
	margin-top: 4px;
	padding-left: 42px;

	background-repeat: no-repeat;
	background-position: 0 center;
}

.col_top .status1{
	color: #76FF00;
	background-image: url(../img/flight.ac_green.png?1);
}
.col_top .status2{
	color: #FFFF00;
	background-image: url(../img/flight.ac_yellow.png?1);
}

.col_top .flightStatus1{
	color: #76FF00;
}
.col_top .flightStatus2{
	color: #FFFF00;
}
.col_top .flightStatus3{
	color: #666666;
}


/* col_left */
.col_left .col_left_title,
.col_right .col_right_title {
	position: absolute;
	top: 75px;
	left: 105px;
	width: 445px;
	height: 85px;
}

.col_right .col_right_title {
	top: 72px;
	left: 2030px;
}


.col_left .col_left_title li,
.col_right .col_right_title li {
	height: 85px;
	text-align: center;
}

.title_up {
	margin-top: 23px;
}

.title_down {
	margin-top: 5px;
}

.weather_ico {
	display: inline-block;
	width: 26px;
	height: 26px;
	margin-right: 5px;
	font-size: 26px;
	/* background: url(../img/sun.png?2) no-repeat 0 0; */
}

.weather_ico .temperature{
	position: relative;
	top: -30px;
	left: 35px;
	font-size: 18px;
}

.weatherNum {
	display: inline-block;
	position: relative;
}

.col_left .col_left_risk,
.col_right .col_right_risk {
	position: absolute;
	top: 163px;
	left: 70px;
	padding: 5px;
}

.col_right .col_right_risk {
	left: 2000px
}

.col_left .risk_title,
.col_right .risk_title {
	margin-left: 10px;
}

.col_left_risk .risk_con,
.col_right_risk .risk_con,
.col_left_miti .miti_con,
.col_right_miti .miti_con {
	display: inline-block;
	height: 110px;
	width: 450px;
	padding: 10px;
	line-height: 18px;
	text-align: left;

}

.col_left .con_left_pie,
.col_right .con_right_pie {
	position: absolute;
	width: 480px;
	top: 258px;
	left: 70px;
	height: 333px;
}

.col_right .con_right_pie {
	left: 1995px;
}

.col_left .pie-wrapper,
.col_right .pie-wrapper {
	position: relative;
	top: 84px;
	left: 177px;
}

#leftPie,
#rightPie {
	width: 120px;
	height: 120px;
}

.con_left_pie .pieTitle,
.con_right_pie .pieTitle {
	position: absolute;
	top: 42px;
	left: 22px;
	text-align: center;
}


.con_left_pie .crew-wrapper .crewTitle,
.con_left_pie .plane-wrapper .planeTitle,
.con_left_pie .ambient-wrapper .ambientTitle .con_right_pie .crew-wrapper .crewTitle,
.con_right_pie .plane-wrapper .planeTitle,
.con_right_pie .ambient-wrapper .ambientTitle {
	width: 63px;
	height: 14px;
	line-height: 14px;
}

.con_left_pie .crew-wrapper,
.con_right_pie .crew-wrapper {
	position: absolute;
	top: 10px;
	left: 70px;
	width: 134px;
	height: 185px;
	background: url(../img/crew_1.png?2) no-repeat 0 0;
	text-align: center;
}

.con_left_pie .crew-wrapper .crewCell0,
.con_right_pie .crew-wrapper .crewCell0 {
	position: absolute;
	top: 85px;
	left: 68px;
}

.con_right_pie .crew-wrapper .crewCell0 {
	left: 85px;
}

.con_left_pie .crew-wrapper .crewCell1 {
	position: absolute;
	top: 13px;
	left: 70px;
}


.con_right_pie .crew-wrapper .crewCell1 {
	position: absolute;
	top: 12px;
	left: 77px;
}

.con_left_pie .crew-wrapper .crewCell2,
.con_right_pie .crew-wrapper .crewCell2 {
	position: absolute;
	top: 54px;
	left: 3px;
}

.con_right_pie .crew-wrapper .crewCell2 {
	left: 8px;
}


.con_left_pie .crew-wrapper .crewCell3 {
	position: absolute;
	top: 133px;
	left: 12px;
}

.con_right_pie .crew-wrapper .crewCell3 {
	position: absolute;
	top: 130px;
	left: 20px;
}

.con_left_pie .plane-wrapper,
.con_right_pie .plane-wrapper {
	position: absolute;
	top: 37px;
	left: 277px;
	width: 148px;
	height: 106px;
	background: url(../img/plane_1.png?2) no-repeat 0 0;
	text-align: center;
}

.con_left_pie .plane-wrapper .planeCell0,
.con_right_pie .plane-wrapper .planeCell0 {
	position: absolute;
	top: 60px;
	left: -2px;
}

.con_left_pie .plane-wrapper .planeCell1,
.con_right_pie .plane-wrapper .planeCell1 {
	position: absolute;
	top: 20px;
	left: 75px;
}


.con_left_pie .ambient-wrapper,
.con_right_pie .ambient-wrapper {
	position: absolute;
	top: 190px;
	left: 135px;
	width: 204px;
	height: 132px;
	background: url(../img/ambient_1.png?2) no-repeat 0 0;
	text-align: center;
}

.con_left_pie .ambient-wrapper .ambientCell0 {
	position: absolute;
	top: 12px;
	left: 90px;
}

.con_right_pie .ambient-wrapper .ambientCell0 {
	position: absolute;
	top: 15px;
	left: 70px;
}


.con_left_pie .ambient-wrapper .ambientCell1 {
	position: absolute;
	top: 40px;
	left: 10px;
}


.con_right_pie .ambient-wrapper .ambientCell1 {
	position: absolute;
	top: 40px
}

.con_left_pie .ambient-wrapper .ambientCell2,
.con_right_pie .ambient-wrapper .ambientCell2 {
	position: absolute;
	top: 80px;
	left: 70px;
}

.con_left_pie .ambient-wrapper .ambientCell2 {
	left: 78px;
}

.con_left_pie .ambient-wrapper .ambientCell3,
.con_right_pie .ambient-wrapper .ambientCell3 {
	position: absolute;
	top: 45px;
	left: 140px;
}

.con_left_pie .ambient-wrapper .ambientCell3 {
	left: 145px;
}

.col_left_miti,
.col_right_miti {
	position: absolute;
	left: 71px;
	top: 590px;
	height: 140px;
	width: 480px;
}

.col_right_miti {
	left: 1995px;
}

.col_left_miti .miti_title,
.col_right_miti .miti_title {
	margin-left: 10px;
	height: 30px;
	line-height: 30px;
}

/* col_mid */

#mapBox{
	pointer-events: auto !important;
}
/* col_mid_left */
.col_mid_left {
	position: absolute;
	width: 195px;
	height: 490px;
	top: 75px;
	left: 570px;
}

.col_mid_left .topTitle {
	position: absolute;
	top: 22px;
	left: 50px;
}

.col_mid .row1,
.col_mid .row2,
.col_mid .row3,
.col_mid .row4,
.col_mid .row5 {
	position: absolute;
	left: 20px;
	width: 160px;
	height: 20px;
	line-height: 20px;
}

.col_mid .row1 {
	top: 60px;
}

.col_mid .row2 {
	top: 90px;
}

.col_mid .row3 {
	top: 120px;
}

.col_mid .row4 {
	top: 280px;
}

.col_mid .row5 {
	top: 450px;
}

/* 油量标识 */
.col_mid_left_line {
	position: absolute;
	width: 100px;
	left: 85px;
	top: 152px;
	height: 110px;
	line-height: 20px;
}

.col_mid_left_line2 {
	position: absolute;
	width: 100px;
	left: 85px;
	top: 322px;
	height: 110px;
	line-height: 20px;
}

/* 油量1 */
.wave-wrapper_1,
.wave-wrapper_2 {
	position: absolute;
	top: 150px;
	left: 20px;
	width: 60px;
	height: 110px;
	border: 2px solid #014F92;
	border-radius: 5px;
	overflow: hidden;
	z-index: 1;
}

/* 油量2 */
.wave-wrapper_2 {
	top: 320px;

}

.wave-animation {
	overflow: hidden;
}

.percent {
	position: absolute;
	left: 0;
	top: 0;
	z-index: 3;
	width: 100%;
	height: 100%;
	display: flex;
	display: -webkit-flex;
	align-items: center;
	justify-content: center;
	color: #fff;
	font-size: 64px;
}

.water {
	/* 波浪下方颜色 */
	position: absolute;
	left: 0;
	top: 0;
	z-index: -1;
	width: 100%;
	height: 100%;
	/* 调整这里的60%就可以改变进度，波浪高低 */
	/* transform: translate(0, calc(100% - 30%)); */
	transform: translateY(100%);
	background: #FA555B;
	/* transition: all 2s; */
}

.water-wave {
	width: 200%;
	position: absolute;
	bottom: 100%;

}

.water-wave--back {
	/* 水波背景颜色 */
	right: 0;
	fill: #EDB8C3;
	animation: wave-back 1.4s infinite linear;
}

.water-wave--front {
	/* 水波前景色 */
	left: 0;
	fill: #FA555B;
	margin-bottom: -1px;
	animation: wave-front 0.7s infinite linear;
}

@keyframes wave-front {
	100% {
		transform: translatex(-50%);
	}
}

@keyframes wave-back {
	100% {
		transform: translatex(50%);
	}
}

/* col_mid_foot */

.col_mid_foot {
	position: absolute;
	top: 605px;
	left: 584px;
	width: 1390px;
	height: 160px;

}

.col_mid_foot .col_1,
.col_mid_foot .col_4 {
	position: absolute;
	top: 30px;
	width: 373px;
	height: 125px;

}

.col_mid_foot .col_4 {
	left: 1100px;
	width: 280px;
}

.col_mid_foot .col_2,
.col_mid_foot .col_3 {
	position: absolute;
	left: 330px;
	width: 385px;
	height: 160px;
}

.col_mid_foot .col_3 {
	left: 715px;
}

.col_mid_foot .col_1 .fprGauge,
.col_mid_foot .col_1 .fprInfo {
	position: absolute;
	top: 20px;
	left: 60px;
	width: 140px;
	height: 100px;
}

.col_mid_foot .col_1 .fprGauge {
	position: absolute;
	top: 10px;
	left: 50px;
	width: 140px;
	height: 140px;
	background: url(../img/chart_bg.png) no-repeat center center;
}

.col_mid_foot .col_1 .fprGauge .pointer {
	position: absolute;
	top: 0;
	left: 0;
}

.col_mid_foot .col_1 .fprInfo {
	left: 210px;
	height: 24px;
	line-height: 24px;
}

.col_mid_foot .col_2 .silosTitle {
	position: absolute;
	top: 20px;
	left: 120px;
	width: 200px;
	text-align: center;
}

.col_mid_foot .col_2 .silosImg {
	position: absolute;
	top: 55px;
	left: 10px;
	width: 380px;
	height: 100px;
	background-size: 100% 100%;
}

.col_mid_foot .col_2 .silosImg .blue{
	color: #77BAF8;
}

.col_mid_foot .col_2 .silosImg .actype{
	position: absolute;
	font-size: 12px;
	top: 48px;
	left: 20px;
}

.col_mid_foot .col_2 .silosImg .lb{
	position: absolute;
	font-size: 12px;
	top: 62px;
	left: 25px;
}


.col_mid_foot .col_2 .silosImg.CY{
	background-image: url('../../img/a3.3.plane_layout_N_CY.png');
}
.col_mid_foot .col_2 .silosImg.Y{
	background-image: url('../../img/a3.3.plane_layout_N_Y.png');
}
.col_mid_foot .col_2 .silosImg.W_CY{
	background-image: url('../../img/a3.3.plane_layout_W_CY.png');
}
.col_mid_foot .col_2 .silosImg.W_Y{
	background-image: url('../../img/a3.3.plane_layout_W_Y.png');
}

.col_mid_foot .col_3 .colCrewTitle {
	position: absolute;
	top: 20px;
	left: 20px;
	width: 200px;
}

.col_mid_foot .col_3 .colCrewBody {
	position: absolute;
	top: 50px;
	left: 20px;
}

.col_mid_foot .col_3 .colCrewInfo {
	width: 350px;
	height: 24px;
	overflow: hidden;
	line-height: 24px;
}

.col_mid_foot .col_3 .colCrewLeft {
	display: inline-block;
	width: 50px;
}

.col_mid_foot .col_3 .colCrewRight {
	margin-left: 5px;
}

.col_mid_foot .col_4 .plfGauge {
	position: absolute;
	top: 10px;
	left: 20px;
	width: 110px;
	height: 110px;
}

.col_mid_foot .col_4 .plfInfo {
	position: absolute;
	top: 30px;
	left: 150px;
	height: 24px;
	line-height: 24px;
}

/* col_mid_right */
.col_mid_right {
	position: absolute;
	top: 80px;
	left: 1625px;
	padding: 10px 20px;
	width: 353px;
	height: 485px;
}

.passenger {
	position: absolute;
	top: 17px;
	left: 30px;
	width: 320px;
	height: 150px;
}

.passenger .row1 {
	position: absolute;
	top: 42px;
	left: -15px;
	width: 320px;
}

.passenger .row1 li {
	display: inline-block;
	width: 70px;
	height: 48px;
	margin-right: 7px;
	text-align: center;
}

.li_up {
	padding-top: 5px;
}

.passenger .row2 {
	position: absolute;
	width: 320px;
	margin-top: 10px;
	left: -15px;
}

.passenger .row2 li {
	display: inline-block;
	width: 55px;
	height: 56px;
	margin-right: 6px;
	text-align: center;
}

.transfer {
	position: absolute;
	top: 200px;
	width: 320px;
	height: 90px;
}

.transfer .tranBox1,
.transfer .tranBox2 {
	position: absolute;
	top: 35px;
	left: -1px;
	width: 155px;
	height: 50px;
	line-height: 50px;
	text-align: center;
}

.transfer .tranBox2 {
	left: 160px;
}


.transInfo {
	position: absolute;
	top: 318px;
	width: 320px;
	text-align: center;
}

.transTable {
	position: absolute;
	top: 32px;
	left: -6px;
	width: 320px;
	text-align: center;
}

.transInfoTitle {
	position: absolute;
	top: -5px;
	left: 5px;
}

.transTable .trTableTh {
	width: 320px;
	table-layout: fixed;
	border-collapse: collapse;
}

.transTable .trTableTh th {
	height: 32px;
	line-height: 32px;
	text-align: center;
	font-weight: normal;
}

.transTable .trTableTh thead {
	background-color: #043365;
}

.trTableTh .tdTable {
	width: 320px;
	height: 64px;
	color: #FFFFFF;
	overflow: auto;
}

.tb2_table tr {
	height: 32px;
	line-height: 32px;
	background-color: #061D43;
}

.tb2_table tr:nth-child(even) {
	background-color: #043365;
}

.tb2_table td {
	text-align: center;
	width: 110px;
	border-right: 1px solid #014F92;
}

.tb2_table td:last-child {
	border-right: 0;
}

.tdTable::-webkit-scrollbar {
	width: 10px;
	background-color: #08183E;
}

.tdTable::-webkit-scrollbar-thumb {
	border-radius: 10px;
	-webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
	background-color: #00A9FF;
}

/* col_mid_bottom */
/* col_right */

.up_arr {
	display: inline-block;
	height: 16px;
	width: 10px;
	background: url(../img/arr_up2.png) no-repeat center;
}

.down_arr {
	display: inline-block;
	height: 16px;
	width: 10px;
	background: url(../img/arr_down2.png) no-repeat center;
}

.tipText{
	height: 30px;
	line-height: 30px;
	margin-right: 10px;
}

.fl {
	float: left;
}

.fr {
	float: right;
}
ul {
	list-style: none;
	margin: 0;
	padding: 0;
}

li {
	display: inline-block;
}


/*  新table*/
.tablebox {
	position: relative;
	height: 130px;
	width: 330px;
	overflow: hidden;
	top: 30px;
	left: -10px;
	z-index: 10;
	background-color: #0C1534;
}

.tbl-header {
	width: 100%;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 999;
}

.tbl-body {
	width: 100%;
	position: absolute;
	top: 0;
	left: 0;
}

.tablebox table {
	width: 100%;
}

.tablebox table th,
.tablebox table td {
	font-size: 12px;
	height: 32px;
	line-height: 32px;
	font-weight: normal;
	text-align: center;
}

.tablebox table tr th {
	background-color: #043365;
	cursor: pointer;
}

.tablebox table tr td {
	border-right: 1px solid #014F92;
	background-color: transparent;
}


.tablebox  table td:last-child {
	border-right: 0;
}

.tbl-body tr:nth-child(even) td,
.tbl-body1 tr:nth-child(even) td {
	background-color: #061D43;
}

.tablebox table tr td span,
.tablebox table tr td span {
	font-size: 24px;
}
