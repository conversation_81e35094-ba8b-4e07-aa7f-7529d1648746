<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">

  <title>HNA数字航空</title>

  <meta http-equiv="refresh" content="86400">
  <script>
    var script = "<script src='js/app.js?ts=" + new Date().getTime() + "' ><\/script>";
    document.write(script);
  </script>
    <link rel="stylesheet" href="lib/element-ui/2.13.0/theme-chalk/index.css">
    <!-- <link rel="stylesheet" href="https://unpkg.com/element-ui@2.15.14/lib/theme-chalk/index.css"> -->
    
    <script   src="js/httpVueLoader.js"></script>
  
    <link href="css/nprogress.css" rel="stylesheet">
    <script src="js/nprogress.js"></script>
    <script src="js/jquery-1.11.1.js"></script>
    <script src="js/babel.min.js"></script>
    <script src="js/polyfill.min.js"></script>

    <script src="js/bootstrap.min.js"></script>
    <!-- <script src="js/echarts.min.4.6.js"></script> -->
    <script src="js/echarts.min.5.2.2.js"></script>
    <script src="js/echarts-gl.min.js"></script>

    <script src="js/jquery.powertip.min.js"></script>
    <script src="js/jquery.nicescroll.min.js"></script>

    <script src="js/moment-with-locales.min.js"></script>
    <script src="js/bootstrap-datetimepicker.min.js"></script>
    <script src="js/lib/vue.min.js"></script>
    <script src="lib/element-ui/2.13.0/index.js"></script>

    <script>
       loadjs('js/config.js')
       loadjs('js/tingyun.js')
       loadCss('css/bootstrap.css')
       loadCss('css/bootstrap-theme.css')
       loadCss('css/main.css')
       loadCss('css/a0-14.css')
       loadCss('css/bootstrap-datetimepicker.min.css')
       loadjs('js/json.js')

       loadjs('js/ui.js')
       loadjs('js/util.js')
       loadjs('js/lib/eking.js')
       loadjs('js/slider.js')
 
       loadjs('js/common.js')
    </script>

</head>

<body style="opacity:0; overflow: hidden;background-color: #0d256b;">
  <iframe id="login" scrolling="no" width="0" height="0" style="display:none;"></iframe>
  <div class="baseList hidden">

  </div>
  <div class="page-wrapper main">
    <div id="vue-container">


    <!-- <div id="companycombo" code="" >
      <div class="box"></div>
      <div id="companylist"></div>
    </div> -->
    <div :class="'logo_'+company"></div>
    <div class="body_container">
        <div style="display: flex;">
            <!--左侧栏-->
            <div class="leftDiv">
                <div class="block-header tit">
                    公司运行一览
                </div>
                <div class="b-mainKpi row11">
                  <!-- ********************************* -->
                  <!-- 公司运行一览 -->
                  <!-- ********************************* -->
                  <div class="b-mainKpi-item-wrap">
                    <!-- 航班总量 -->
                    <div class="b-mainKpi-item">
                      <div class="title" style="margin-bottom: 10px;">航班总量</div>
                      <!-- <div class="flightNum-wrap">
                        <div class="title">{{pftc}} </div>
                        <div class="value">架次</div>
                      </div> -->
                      <div style="width:122.64px;height: 88px;margin-top:10px;" ref="echFltCount"></div>
                      <div class="info-wrap">
                        <div class="info-item">
                          <div class="title">已执行</div>
                          <div class="value">{{exeRate}}</div>
                        </div>
                        <div class="info-item" style="margin-top: 5px;">
                          <div class="title">国内</div>
                          <div class="value">{{pftcl}}架次</div>
                        </div>
                        <div class="info-item">
                          <div class="title">已执行</div>
                          <div class="value">{{execRateChina}}</div>
                        </div>
                        <div class="info-item" style="margin-top: 5px;">
                          <div class="title">国际</div>
                          <div class="value">{{pftci}}架次</div>
                        </div>
                        <div class="info-item">
                          <div class="title">已执行</div>
                          <div class="value">{{execRateInt}}</div>
                        </div>
                      </div>
                    </div>
                    <!-- 航班正常量 -->
                    <div class="b-mainKpi-item">
                      <div class="title">航班正常率</div>
                      <div ref="echNormalRate" style="width: 149.33px;height: 133.33px;margin-top:10px;"></div>
                      <div style="text-align: center;font-size: 21.33px;font-weight: bolder;color: #FFFFFF;">{{normalRate}}%</div>
                    </div>
                    <!-- 计划运输旅客总量 -->
                    <div class="b-mainKpi-item" style="position: relative;">
                      <div class="title">计划运输旅客总量</div>
                      <canvas id="echTrvNum"  ref="echTrvNum" :width="130" :height="130" style="margin-left: 12px;margin-top:10px;" ></canvas>
                      <div class="echTrvNumBg"></div>
                      <div style="display: flex;flex-direction: column;justify-content: center;align-items: center;">
                        <div style="color: #fff;font-size: 21.33px;font-weight: bolder;"><span>{{planNum}}</span><span
                            style="font-weight: normal;">人</span>
                        </div>
                        <div style="font-size: 16px;">已完成<span style="color: #fff;">{{ckiNum}}人</span></div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="b-mainKpi row12 abnormal">
                  <div class="sub_title">不正常航班原因分析</div>
                  <canvas  class="ecKpiPie" ref="reason_canvas" :width="180" :height="180" ></canvas>
                  <div class="left"> 
                    <div class="item">公司原因</div>
                    <div class="value" style="color:#9BD70D; font-weight: 600;"><span>{{abnormal.companyPercent}}</span><span class="unit">%</span></div>
                    <div class="item">不正常航班数</div>
                    <div class="value"><span style="font-weight: 600;">{{abnormal.companyCnt}}</span><span class="unit">架次</span></div>
                  </div> 
                  <div class="right">
                    <div class="item">非公司原因</div>
                    <div class="value"><span style="font-weight: 600;">{{abnormal.noCompanyPercent}}</span><span class="unit">%</span></div>
                    <div class="item">不正常航班数</div>
                    <div class="value"><span style="font-weight: 600;">{{abnormal.noCompanyCnt}}</span><span class="unit">架次</span></div>
                  </div>
                  <div class="center">
                      <div style="font-size: 16px;color:#9BD70D">公司原因</div>
                      <div style="color:white;font-size: 29.33px;font-weight: 600;line-height: 48px;">VS</div>
                      <div style="font-size: 16px;">非公司原因</div>
                  </div>
                    
                </div>
                <div class="b-mainKpi row13">
                  <div class="yunliBox box">
                    <div class="box-header">
                      <span class="box-title">运力分布</span>
                    </div>
                    <div class="box-content" >
                      <div class="box1">
                        <img class="kpi-plane" src="img/a0-14/acKpi_plane.png">
                        <div class="kpi-text">
                          <div class="total-yunli">
                            总运力
                          </div>
                          <div style="padding-top: 8px;font-size: 21px;">
                            <span>{{totalAcNum}}</span>
                            <span>架</span>
                          </div>
                        </div>
                      </div>
                      <div class="box2">
                        <div style="font-size: 19px;color: #44A3F4;height: 64px;padding-top: 45px;">
                          <span>机型架数</span>
                        </div>
                        <div style="display: flex;margin-top: 28px;">
                          <div style="display: flex;">
                            <img class="kpi-plane" src="img/a0-14/ackpi-sky.png">
                            <div style="margin-left: 13px;width: 47px;">
                              <div style="font-size: 16px;color: #44A3F4;height: 16px;">
                                空中
                              </div>
                              <div style="margin-top: 8px;width: 54px;">
                                <span class="acNumValue">{{airNum}}</span><span class="wordFontUnit">架</span>
                              </div>
                            </div>
                          </div>
                          <div style="display: flex;margin-left: 42px;">
                            <img class="kpi-plane" src="img/a0-14/ackpi-ground.png">
                            <div style="margin-left: 13px;width: 47px;">
                              <div style="font-size: 16px;color: #44A3F4;height: 16px;">
                                地面
                              </div>
                              <div style="margin-top: 8px;width: 54px;">
                                <span class="acNumValue">{{ groundNum }}</span><span class="wordFontUnit">架</span>
                              </div>
                            </div>
                            
                          </div>
                          <div style="display: flex;margin-left: 42px;">
                            <img class="kpi-plane" src="img/a0-14/ackpi-stop.png">
                            <div style="margin-left: 13px;width: 47px;">
                              <div style="font-size: 16px;color: #44A3F4;height: 16px;">
                                停场
                              </div>
                              <div style="margin-top: 8px;width: 54px;">
                                <span class="acNumValue">{{stopAcNum }}</span><span class="wordFontUnit">架</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="yunliBox-body">
                          <div class="yunli-item" v-for="(item,index) in yunliList" :key="item.acType" style="display: flex;height: 18px;">
                            <div style="width: 38px;">
                              {{item.acType}}
                            </div>
                           
                           
                              <div class="data-bar">
                                <div class="data-bar-progress data-bar-blue" :style="'width:' +getPercent(item.airNum,item.total)+';'"></div>
                              </div>
                              <div class="percent bold value">{{item.airNum}}</div>
                           
                           
                              <div class="data-bar" style="margin-left: 16px;">
                                <div class="data-bar-progress data-bar-yellow" :style="'width:' +getPercent(item.groundNum,item.total)+';'"></div>
                              </div>
                              <div class="percent bold value">{{item.groundNum}}</div>
                           
                              <div class="data-bar" style="margin-left: 16px;">
                                <div class="data-bar-progress data-bar-orange" :style="'width:' +getPercent(item.stopNum,item.total)+';'"></div>
                              </div>
                              <div class="percent bold value">{{item.stopNum}}</div>
                           
                          </div>
                          
                        </div>
                      </div>
                      
                    </div>
                  </div>
                </div>
                <div class="b-mainKpi row14">
                  <div style="font-size: 21px;height: 42px;margin-left: 21px;padding-top: 21px;">
                    不正常航班一览
                  </div>
                  <div style="display: flex;margin-top: 25px;height: 232px;width: 100%;">
                    <div style="width: 300px;">
                      <div style="margin-left: 21px;display: flex;font-size: 19px;">
                        <span>非计划停场</span>
                        <div style="width: 80px;margin-left: 70px;text-align: right;">
                          <span style="color: white;">{{planeCount}}</span>
                          <span>架次</span>
                        </div>
                      </div>
                      <div style="margin-left: 21px;margin-top: 18px;display: flex;font-size: 16px;">
                        <span style="width: 60px;">飞机号</span>
                        <div style="width: 80px;margin-left: 105px;text-align: right;">
                          <span>机型</span>
                        </div>
                      </div>
                      <div class="fs12">
                        <div v-for="(item,index) in planeList" :key="index" style="margin-left: 21px;margin-top: 2px;display: flex;font-size: 16px;color: white;">
                          <span style="width: 60px;">{{item.acNo}}</span>
                          <div style="width: 80px;margin-left: 105px;text-align: right;">
                            <span>{{item.bigAcType}}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div style="width: 1px;border-bottom: 217px solid #44A3F4;margin-bottom: 15px;"></div>
                    <div style="width: 299px;">
                      <div style="height: 72px;font-size: 19px;">
                        <div style="margin-left: 52px;display: flex;">
                          <span>航班备降</span>
                          <div style="width: 80px;margin-left: 70px;text-align: right;">
                            <span style="color: white;">{{dftc}}</span>
                            <span>架次</span>
                          </div>
                        </div>
                        <div style="margin-left: 52px;margin-top: 5px;display: flex;">
                          <span>取消航班</span>
                          <div style="width: 80px;margin-left: 70px;text-align: right;">
                            <span style="color: white;">{{qftc1}}</span>
                            <span>架次</span>
                          </div>
                        </div>
                      </div>
                      <div style="height: 1px;border-bottom: 3px solid #44A3F4;width: 227px;margin-left: 52px;"></div>
                      <div style="height: 157px;margin-left: 52px;font-size: 16px;">
                        <div style="margin-top: 19px;font-size: 19px;">
                          延误航班数
                        </div>
                        <div style="margin-top: 2px;display: flex;">
                          <span style="width: 75px;">1-2小时</span>
                          <div style="width: 80px;margin-left: 70px;text-align: right;">
                            <span style="color: white;">{{pfdtc12}}</span>
                            <span>架次</span>
                          </div>
                        </div>
                        <div style="margin-top: 2px;display: flex;">
                          <span style="width: 75px;">2-4小时</span>
                          <div style="width: 80px;margin-left: 70px;text-align: right;">
                            <span style="color: white;">{{pfdtc24}}</span>
                            <span>架次</span>
                          </div>
                        </div>
                        <div style="margin-top: 2px;display: flex;">
                          <span style="width: 75px;">4小时</span>
                          <div style="width: 80px;margin-left: 70px;text-align: right;">
                            <span style="color: white;">{{pfdtc4}}</span>
                            <span>架次</span>
                          </div>
                        </div>
                        <div style="margin-top: 2px;display: flex;">
                          <span style="width: 75px;">国际航班</span>
                          <div style="width: 80px;margin-left: 70px;text-align: right;">
                            <span style="color: white;">{{pfdtci}}</span>
                            <span>架次</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
            </div>
            <!--右侧栏-->
            <div class="rightDiv">
                <div class="block-header tit" style="margin-left: 14px;">
                    <el-dropdown trigger="click" @command="onChangeBase" size="medium"> 
                      <div class="el-dropdown-link">
                        <div class="baseName">{{baseName}} <div class="dropDown"></div>
                      </div>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item :command="item" v-for="(item,index) in baseList" :key="index">{{item.baseName}}</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                </div>
                </div>
                <div class="row21">
                <!-- 当日航班量 -->
                  <div class="row211">
                    <div class="title">当日航班量</div>
                    <div class="item-top-warp" style="position: relative;">
                      <div class="item-flag">进港</div>
                      <div class="item-info-wrap">
                          <div class="top">
                            <div class="info-left">{{fasData.arrPftc }}架次</div>
                            <div class="info-right">已执行<span class="num">{{fasData.arrCftc}}</span>架次</div>
                          </div>
                      </div>
                      <div class="processBar">
                        <div class="insidebar"  :style="'width:' + (fasData.arrCftc/fasData.arrPftc)*100 + '%;'"></div>
                      </div>
                    </div>
                    <div class="item-bottom-warp" >
                      <div class="item-flag">出港</div>
                      <div class="item-info-wrap">
                        <div class="top"  style="margin-top: 12.33px;">
                          <div class="info-left">{{fasData.depPftc }}架次</div>
                          <div class="info-right">已执行<span class="num">{{fasData.depCftc}}</span>架次</div>
                        </div>
                        <div class="processBar" style="top:52px">
                          <div class="insidebar"  :style="'width:' + (fasData.depCftc/fasData.depPftc)*100 + '%;'"></div>
                        </div>
                        <div class="bottom" >
                          <div class="info-item">
                            <el-row>
                              <el-col :span="8">始发航班量</el-col>
                              <el-col :span="10"><span class="num">{{ORI_NO_SCH}}</span>架次</el-col>
                            </el-row>
                            <el-row>
                              <el-col :span="8">国内航班量</el-col>
                              <el-col :span="6"><span class="num">{{fasData.depPftcl}}</span>架次</el-col>
                              <el-col :span="10">已执行<span class="num">{{fasData.depCftcl}}</span>架次</el-col>
                            </el-row>
                            <el-row>
                              <el-col :span="8">国际航班量</el-col>
                              <el-col :span="6"><span class="num">{{fasData.depPftci}}</span>架次</el-col>
                              <el-col :span="10">已执行<span class="num">{{fasData.depCftci}}</span>架次</el-col>
                            </el-row>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div >
                  <div class="row212">
                    <div class="title-wrap">
                      <div class="title">基地过夜飞机</div>
                      <div class="subTitle"><span class="value">{{acNoTotal}}</span>架</div>
                    </div>
                    <div style="margin-top: 21.33px;font-size: 16px">
                      <el-row style="margin-bottom:10px;">
                        <el-col :span="12" >机型</el-col>
                        <el-col :span="12">已过夜飞机数</el-col>
                      </el-row>
                      <el-row v-for="(item,index) in acListData" :key="index" class="aclist-item">
                        <el-col :span="4" :style="'color:'+ item.color+';'">{{item.acType}}</el-col>
                        <el-col :span="10">已过夜<span :style="'color:'+ item.color+';'">{{item.acNumPer}}%</span></el-col>
                        <el-col :span="10"  :style="'color:'+ item.color+';'" style="text-align: right;" >{{item.acNum}}架</el-col>
                        <el-col :span="24" class="processBar">
                          <div class="insidebar"  :style="'left:' + item.barPlan + '%;'"></div>
                        </el-col>
                      </el-row>
                      <!-- <el-row>
                        <el-col :span="5" style="color: #fff;">330</el-col>
                        <el-col :span="12">已过夜<span style="color:#fff">20%</span></el-col>
                        <el-col :span="5"  style="color: #fff;">1/5架</el-col>
                      </el-row>
                      <div style="height: 12px;margin:5px 0;"></div>
                      <el-row >
                        <el-col :span="5" style="color:#2BCC43">737</el-col>
                        <el-col :span="12">已过夜<span style="color:#fff">20%</span></el-col>
                        <el-col :span="5" style="color:#2BCC43">1/5架</el-col>
                      </el-row>
                      <div style="height: 12px;margin:5px 0;"></div>
                      <el-row>
                        <el-col :span="5" style="color:#FAAF33">350</el-col>
                        <el-col :span="12">已过夜<span style="color:#fff">20%</span></el-col>
                        <el-col :span="5" style="color:#FAAF33">1/5架</el-col>
                      </el-row>
                      <div style="height: 12px;margin:5px 0;"></div>
                      <el-row >
                        <el-col :span="5" style="color:#44A3F4">787</el-col>
                        <el-col :span="12" >已过夜<span style="color:#fff">20%</span></el-col>
                        <el-col :span="5" style="color:#44A3F4">1/5架</el-col>
                      </el-row> -->
                    </div>
                  </div>
                </div>
                <div class="b-mainKpi row22" style="display: flex;">
                  <div style="margin-top: 23px;margin-left: 21px;font-size: 21px;height: 21px;">
                    <span>五率</span>
                  </div>
                  <div style="margin-top: 112px;width: 100px;">
                    <div class="fiveNum" style="margin-left: -11px;">{{fiveRate.num1}}%</div>
                    <div class="fiveText" style="margin-top: 43px;margin-left: -22px">机组到位</div>
                  </div>
                  <div style="margin-top: 180px;width: 100px;">
                    <div class="fiveNum" style="padding-left: 2px;">{{fiveRate.num2}}%</div>
                    <div class="fiveText" style="margin-top: 43px;margin-left: -8px">机务放行</div>
                  </div>
                  <div style="margin-top: 112px;width: 100px;">
                    <div class="fiveNum" style="padding-left: 15px;">{{fiveRate.num3}}%</div>
                    <div class="fiveText" style="margin-top: 43px;margin-left: 8px">通知上客</div>
                  </div>
                  <div style="margin-top: 180px;width: 100px;">
                    <div class="fiveNum" style="padding-left: 34px;">{{fiveRate.num4}}%</div>
                    <div class="fiveText" style="margin-top: 43px;margin-left: 23px">客舱关闭</div>
                  </div>
                  <div style="margin-top: 112px;width: 110px;">
                    <div class="fiveNum" style="padding-left: 50px;">{{fiveRate.num5}}%</div>
                    <div class="fiveText" style="margin-top: 43px;margin-left: 38px">货舱关闭</div>
                  </div>
                  <div ref="fiveEchart1" class="fivePie1"></div>
                  <div ref="fiveEchart2" class="fivePie2"></div>
                  <div ref="fiveEchart3" class="fivePie3"></div>
                  <div ref="fiveEchart4" class="fivePie4"></div>
                  <div ref="fiveEchart5" class="fivePie5"></div>
                </div>
                <div class="b-mainKpi row23">
                    <div style="padding-top: 21px;margin-left: 21px;font-size: 21px;height: 42px;">
                      {{baseName}}天气预报
                    </div>
                    <div style="width: 63px;text-align: center;margin-left: 254px;padding-top: 23px;">
                      <img :src="weatherImg" style="width: 80px;height: 80px;"></img><br/>
                      <span style="font-size: 21px;color: white;">{{temperature}}°C</span>
                    </div>
                    <div style="display: flex;">
                      
                      <div style="margin-left: 42px;padding-top: 7px;width: 100px;font-size: 18px;">
                        <span>风速</span><br/>
                        <span>风向</span><br/>
                        <span>能见度</span><br/>
                        <span>温度</span>
                      </div>
                      <div style="padding-top: 7px;font-size: 18px;color: white;width: 100px;">
                        <span>{{windFs}}m/s</span><br/>
                        <span>{{getWeatherFx()}}</span><br/>
                        <span>{{visibility}}m</span><br/>
                        <span>{{temperature}}°C</span>
                      </div>
                      <div class="weather_atlas" style="margin-left: 100px;margin-top: 9px;width: 187px;height: 100px;background-color: rgba(27, 72, 142, 0.1); background-size: contain;background-repeat: no-repeat;">

                      </div>
                    </div>
                </div>
                <div class="b-mainKpi row24">
                  <div style="height: 168px;">
                    <div style="padding-top: 21px;margin-left: 21px;height: 42px;font-size: 21px;">
                      当日计划旅客量
                    </div>
                    <div style="display: flex;">
                      <div style="background-color: #016666;height: 64px;width: 32px;padding-top: 7px;
                        font-size: 19px;text-align: center;color: white;margin-top: 28px;margin-left: 21px;">
                        进港
                      </div>
                      <div style="width: 90px;height: 45px;margin-left: 27px;margin-top: 32px;font-size: 16px;">
                        <div>
                          <span>总量</span>
                        </div>
                        <div style="margin-top: 8px;color: white;">
                          <span>{{bookNumIn}}</span>
                          <span>人</span>
                        </div>
                      </div>
                      <div style="width: 96px;margin-left: 63px;margin-top: 33px;font-size: 16px;text-align: center;">
                        <div>
                          <span>已完成</span>
                        </div>
                        <div style="margin-top: 2px;color: white;">
                          <span>{{ckiNumIn}}</span>
                          <span>人</span>
                        </div>
                      </div>
                      <div style="width: 96px;margin-left: 53px;margin-top: 33px;font-size: 16px;text-align: center;">
                        <div>
                          <span>上座率</span>
                        </div>
                        <div style="margin-top: 2px;color: white;">
                          <span>{{cvsTrvIn}}</span>
                          <span>%</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div style="height: 1px;border-bottom: 1px solid #44A3F4;width: 553px;margin-left: 24px;"></div>
                  <div style="height: 144px;display: flex;">
                    <div style="background-color: #016666;height: 64px;width: 32px;padding-top: 7px;
                      font-size: 19px;text-align: center;color: white;margin-top: 35px;margin-left: 21px;">
                      出港
                    </div>
                    <div style="width: 90px;height: 45px;margin-left: 27px;margin-top: 41px;font-size: 16px;">
                      <div>
                        <span>总量</span>
                      </div>
                      <div style="margin-top: 8px;color: white;">
                        <span>{{bookNumOut}}</span>
                        <span>人</span>
                      </div>
                    </div>
                    <div style="width: 96px;margin-left: 63px;margin-top: 42px;font-size: 16px;text-align: center;">
                      <div>
                        <span>已完成</span>
                      </div>
                      <div style="margin-top: 2px;color: white;">
                        <span>{{ckiNumOut}}</span>
                        <span>人</span>
                      </div>
                    </div>
                    <div style="width: 96px;margin-left: 53px;margin-top: 42px;font-size: 16px;text-align: center;">
                      <div>
                        <span>上座率</span>
                      </div>
                      <div style="margin-top: 2px;color: white;">
                        <span>{{cvsTrvOut}}</span>
                        <span>%</span>
                      </div>
                    </div>
                  </div>
                  <!-- <div ref="todayPlanEchart1" class="todayPlanPie1"></div>
                  <div ref="todayPlanEchart2" class="todayPlanPie2"></div>
                  <div ref="todayPlanEchart3" class="todayPlanPie3"></div>
                  <div ref="todayPlanEchart4" class="todayPlanPie4"></div> -->
                  <canvas  ref="todayPlanEchart1" :width="180" :height="180" class="todayPlanPie1" ></canvas>
                  <canvas  ref="todayPlanEchart2" :width="180" :height="180" class="todayPlanPie2" ></canvas>
                  <canvas  ref="todayPlanEchart3" :width="180" :height="180" class="todayPlanPie3" ></canvas>
                  <canvas  ref="todayPlanEchart4" :width="180" :height="180" class="todayPlanPie4" ></canvas>
                </div>
            </div>

        </div>
    </div>
    </div>
  </div>



</body>

</html>
<script>
  loadjs4BabelDefer('js/a0-14.js')
</script>