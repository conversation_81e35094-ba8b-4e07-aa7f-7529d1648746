
/*营销 城市*/

.page-wrapper {
  
}




#map_tooltip {
  z-index: 9999; 
  top: 0px; 
  left: 0px; 
  position:absolute; 
  width: 170px; 
  height:130px; 
  background:#FFF;
  display: none;
  pointer-events: auto;
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
  overflow: hidden;
}
#map_tooltip table{
  margin: 5px 0;
  width: 100%;
}
#map_tooltip .cl1{
  padding:0 8px 0 8px; 
  text-align: right;
  color: #134487;
  width: 82px;
}
#map_tooltip .cl2{
  text-align: left;
  color: #001749;
}
#map_tooltip .area{
  background:#134487; 
  padding: 6px 8px; 
  font-size:16px;
}


.pagetitle{
  position: absolute;
  width: 100%;
  height: 36px;
  top: 67px;
  text-align: center;
  background: url(../img/title-bot-line.png) no-repeat center 25px;
}
.maintitle{
  color: #7fc4ff;
  width: 350px;
  font-size: 27px;
  padding: 5px 20px;
  margin: 0 auto;
  text-shadow: 0 0 10px #0d2452,0 0 10px #0d2452,0 0 20px #0c2d68,0 0 20px #0c2d68;
}
.submaintitle{
  color: #fff;
  width: 350px;
  font-size: 22px;
  padding: 0 20px;
  margin: 0 auto;
}

#main_cb_type {
  position: absolute;
  top: 51px;
  left: 632px;
  height: 30px;
  width: 100px;
}

#main_cb_type .combobox_label{
  padding: 4px 6px;
  background: url(../img/combobox_arr.png) no-repeat 80px center;
}

.combobox_date {
  left: 900px;
  top: 102px;
}

/* --- */
.block_l1 {
  height: 310px;
  left: 15px;
  top: 84px;
}
.block_l1 .cont{
  height: 121px;
}

.block_l1 .cont div{
  position: absolute;
}



/* --- */
.block_l2 {
  left: 15px;
  top: 250px;
}
.block_l2 .cont{
  height: 123px;
}
.block_l2 .kpitable{
  width: 280px;
  height: 100px;
  margin: 13px 15px;
}
.block_l2 .kpitable .name{
  text-align: right;
  color: #a0d3ff;
  padding-right: 2px;
}
.block_l2 .kpitable .val{
}


/* --- */
.block_l3 {
  left: 15px;
  top: 407px;
}
.block_l3 .cont{
  height: 303px;
}
.block_l3 .toptable{
  width: 280px;
  height: 280px;
  margin: 10px 15px 20px 15px;
  font-size: 12px;
}
.block_l3 .toptable th, 
.block_l3 .toptable td{
  padding: 5px 3px 5px 6px;
}

.toptable td{
  background: url(../img/a2.2.2-tablecellbg.png) no-repeat left bottom;
}
.toptable td span{
  height: 12px;
  display: inline-block;
}
.toptable td .bar{
  height: 12px;
  margin-left: 3px;
background: rgb(115,143,140);
background: -moz-linear-gradient(left,  rgba(115,143,140,1) 0%, rgba(251,248,183,1) 100%);
background: -webkit-linear-gradient(left,  rgba(115,143,140,1) 0%,rgba(251,248,183,1) 100%);
background: linear-gradient(to right,  rgba(115,143,140,1) 0%,rgba(251,248,183,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#738f8c', endColorstr='#fbf8b7',GradientType=1 );

}



/* --- */
.block_r1 {
  right: 15px;
  top: 87px;
  z-index: 92;
}
.block_r1 .cont{
  height: 181px;
}
.block_r1 .toptable{
  width: 280px;
  height: 160px;
  margin: 10px 15px 20px 15px;
  font-size: 12px;
}
.block_r1 .toptable th, 
.block_r1 .toptable td{
  padding: 5px 3px 5px 6px;
}


/* --- */
.block_r2 {
  right: 15px;
  top: 310px;
  z-index: 91;
}
.block_r2 .cont{
  height: 181px;
}
.block_r2 .toptable{
  width: 280px;
  height: 160px;
  margin: 10px 15px 20px 15px;
  font-size: 12px;
}
.block_r2 .toptable th, 
.block_r2 .toptable td{
  padding: 5px 3px 5px 6px;
}


/* --- */
.block_r3 {
  right: 15px;
  top: 535px;
  z-index: 90;
}
.block_r3 .cont{
  height: 181px;
}
.block_r3 .toptable{
  width: 280px;
  height: 160px;
  margin: 10px 15px 20px 15px;
  font-size: 12px;
}
.block_r3 .toptable th, 
.block_r3 .toptable td{
  padding: 5px 3px 5px 6px;
}



