showLoading();



var current_company_code;

// 可显示的历史 数量
var query_limit = 20;
// 日期类型
var date_type = 'L';

var actype_list = ['A319', 'A320', 'A321', 'A330', 'B737', 'B767', 'B787', 'E190', 'A350'];


// -------------------------------------------------------

//                          KPI

// -------------------------------------------------------



var weekList = [];
var monthList = [];

var selected_day;
var selected_month;
var kpiDataReady = false;
var fetchingKpiData = false;


var weekDateRangeList; //例会周的日期对应表
var latest_week = 0;

var all_company_data;
var all_comp_actype_data;
var all_comp_actype_tb_data;
var all_actype_data;
var all_company_year_data;

var kpi_list = [
    'FUELT', //吨公里耗油 
    'MOD_FUELT', //修正吨公里耗油
];

var kpi_id_list = {
    'FUELT': '10126', //吨公里耗油 
    'MOD_FUELT': '10127', //修正吨公里耗油
};


var marquee_itv_aclst;

var ignore_ball_animation = false;

function getAllCompanyKpiData() {

    if (companylist.length == 0) {
        setTimeout(getAllCompanyKpiData, 0);
        return;
    }

    if (fetchingKpiData) return;


    // check url hash
    var hash = window.location.hash.substr(1);
    if (hash && companyCode2Name[hash] != undefined) {
        current_company_code = hash;
    } else {
        current_company_code = parent_company;
    }


    fetchingKpiData = true;

    all_company_data = {};
    all_comp_actype_data = {};
    all_comp_actype_tb_data = {};
    all_actype_data = {};

    var comp_code = current_company_code;

    var len = companylist.length;
    var codelist = [];
    var codelist_no_parent = [];
    for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        codelist.push(dat.code);
        if (dat.code != parent_company) {
            codelist_no_parent.push(dat.code);
        }
    }


    var loadingInProgress = 0;



    // -------------------------------------------------------
    // 周
    // -------------------------------------------------------


    // 本期

    loadingInProgress++;
    var param = {
        'SOLR_CODE': 'FAC_COMP_KPI',
        'COMP_CODE': codelist.join(','),
        'KPI_CODE': kpi_list.join(','),
        'VALUE_TYPE': 'kpi_value_d', //本期
        'DATE_TYPE': 'L,M',
        "OPTIMIZE": 1,
        'LIMIT': query_limit
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {

            if (response.data != undefined) {

                all_company_data['kpi_value_d'] = response.data;

                var ddd = response.data['HU']['FUELT']['L'];

                weekList = [];
                for (var date in ddd) {
                    weekList.push(date);
                }

                //weekList.reverse();
                //weekList = weekList.slice(0, query_limit);
                //weekList.reverse();

                getWeekDateRange(weekList, setWeekCB);

                function setWeekCB(latest_week) {
                    var cblist = [];
                    var len = weekList.length;
                    for (var i = 0; i < len; i++) {
                        var date = weekList[i];
                        if (Number(latest_week) >= Number(date)) {
                            // Week: *********
                            //var label = date.substr(0,4) + '年第' + Number(date.substr(4,3)) + '周';
                            //20170419 双双要求：显示的周数+1
                            var label = '第' + (Number(date.substr(4, 3))) + '周 '; //+ response[date];
                            cblist.unshift({
                                'label': label,
                                'data': date
                            });
                        }
                    }
                    // cblist = cblist.slice(0,6);
                    createComboBox('main_cb_week', cblist, 84, 240, dateChanged, 1);


                    $('#main_cb_week .combobox_label').on('click', function (event) {
                        event.preventDefault();

                        if (date_type == 'L') {
                            return;
                        }

                        $('#main_cb_week').addClass('combotab_selected');
                        $('#main_cb_week').removeClass('combotab');
                        $('#main_cb_month').addClass('combotab');
                        $('#main_cb_month').removeClass('combotab_selected');

                        date_type = 'L';
                        dateChanged();

                    });

                    // 显示 week 日期范围
                    $('#main_cb_week .combobox_label').on('mouseover', function (event) {
                        event.preventDefault();
                        if (weekDateRangeList) {
                            var date = $('#main_cb_week').attr('data');
                            $('#week_date_range').text(weekDateRangeList[date]);
                            $('#week_date_range').fadeIn();
                        }
                    });

                    // 隐藏 week 日期范围
                    $('#main_cb_week .combobox_label').on('mouseout', function (event) {
                        event.preventDefault();
                        if (weekDateRangeList) {
                            $('#week_date_range').fadeOut();
                        }
                    });


                }



                // M
                var datlist = response.data['HU']['FUELT']['M'];
                monthList = [];
                for (var date in datlist) {
                    monthList.push(date);
                }

                monthList.reverse();
                monthList = monthList.slice(0, query_limit);
                monthList.reverse();

                var date = new Date();
                var month = date.getMonth() + 1;
                var day = date.getDate();
                if (month < 10) {
                    month = '0' + month;
                }
                var nowmonth = date.getFullYear() + '' + month;

                var cblist = [];
                var len = monthList.length;
                for (var i = 0; i < len; i++) {
                    var date = monthList[i];
                    if (date <= nowmonth) {
                        var label = date.substr(0, 4) + '年' + Number(date.substr(4, 2)) + '月';
                        cblist.unshift({
                            'label': label,
                            'data': date
                        });
                    }

                }
                // cblist = cblist.slice(0,6);
                createComboBox('main_cb_month', cblist, 104, 240, dateChanged, 1);

                $('#main_cb_month .combobox_label').on('click', function (event) {
                    event.preventDefault();

                    if (date_type == 'M') {
                        return;
                    }

                    $('#main_cb_week').addClass('combotab');
                    $('#main_cb_week').removeClass('combotab_selected');
                    $('#main_cb_month').addClass('combotab_selected');
                    $('#main_cb_month').removeClass('combotab');


                    date_type = 'M';
                    dateChanged();
                });


                // 显示 month 日期范围
                $('#main_cb_month .combobox_label').on('mouseover', function (event) {
                    event.preventDefault();
                    var month = $('#main_cb_month').attr('data');
                    var curmonth = moment().format("YYYYMM");
                    var numofdays = moment(month, "YYYYMM").daysInMonth(); // 获取一个月有几天
                    var days = numofdays;
                    if (days < 10) {
                        days = '0' + days;
                    }
                    if (curmonth == month) {
                        days = moment().format("DD");
                    }
                    $('#week_date_range').text(month + '01' + '~' + month + days);
                    $('#week_date_range').fadeIn();
                });

                // 隐藏 month 日期范围
                $('#main_cb_month .combobox_label').on('mouseout', function (event) {
                    event.preventDefault();
                    $('#week_date_range').fadeOut();
                });
                $('#main_cb_month .combobox_label').on('click', function (event) {
                    event.preventDefault();
                    $('#week_date_range').fadeOut();
                });


                loadingInProgress--;
                checkDataReady();



            }

        },
        error: function () { }
    });



    // 同期
    loadingInProgress++;
    var param = {
        'SOLR_CODE': 'FAC_COMP_KPI',
        'COMP_CODE': codelist.join(','),
        'KPI_CODE': kpi_list.join(','),
        'VALUE_TYPE': 'kpi_value_tq_d', //同期
        'DATE_TYPE': 'L,M',
        "OPTIMIZE": 1,
        'LIMIT': query_limit
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {

            if (response.data != undefined) {
                all_company_data['kpi_value_tq_d'] = response.data;
                loadingInProgress--;
                checkDataReady();
            }

        },
        error: function () { }
    });


    // 同比
    loadingInProgress++;
    var param = {
        'SOLR_CODE': 'FAC_COMP_KPI',
        'COMP_CODE': codelist.join(','),
        'KPI_CODE': kpi_list.join(','),
        'VALUE_TYPE': 'kpi_ratio_tq_d', //同比
        'DATE_TYPE': 'L,M',
        "OPTIMIZE": 1,
        'LIMIT': query_limit
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {

            if (response.data != undefined) {
                all_company_data['kpi_ratio_tq_d'] = response.data;
                loadingInProgress--;
                checkDataReady();
            }

        },
        error: function () { }
    });



    // 分公司 机型
    loadingInProgress++;
    var param = {
        "SOLR_CODE": "FAC_COMP_ACTYPE_CODE_KPI",
        "COMP_CODE": codelist.join(','),
        "KPI_CODE": kpi_list.join(','),
        "ACTYPE": actype_list.join(','),
        "VALUE_TYPE": "kpi_value_d",
        "DATE_TYPE": "L,M",
        'OPTIMIZE': 1,
        "LIMIT": query_limit
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getaccodekpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {

            if (response.data != undefined) {
                all_comp_actype_data = response.data;
                loadingInProgress--;
                checkDataReady();
            }

        },
        error: function () { }
    });


    // 分公司 机型
    loadingInProgress++;
    var param = {
        "SOLR_CODE": "FAC_COMP_ACTYPE_CODE_KPI",
        "COMP_CODE": codelist.join(','),
        "KPI_CODE": kpi_list.join(','),
        "ACTYPE": actype_list.join(','),
        "VALUE_TYPE": "kpi_ratio_tq_d", //同比
        "DATE_TYPE": "L,M",
        'OPTIMIZE': 1,
        "LIMIT": query_limit
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getaccodekpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {

            if (response.data != undefined) {
                all_comp_actype_tb_data = response.data;
                loadingInProgress--;
                checkDataReady();
            }

        },
        error: function () { }
    });


    // 年
    loadingInProgress++;
    var param = {
        'SOLR_CODE': 'FAC_COMP_KPI',
        'COMP_CODE': codelist.join(','),
        'KPI_CODE': kpi_list.join(','),
        'VALUE_TYPE': 'kpi_value_d',
        'DATE_TYPE': 'Y',
        "OPTIMIZE": 1,
        'LIMIT': 1
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getkpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {

            if (response.data != undefined) {
                all_company_year_data = response.data;
                loadingInProgress--;
                checkDataReady();
            }

        },
        error: function () { }
    });


    // 总公司 机型
    loadingInProgress++;
    var param = {
        "SOLR_CODE": "FAC_ACTYPE_CODE_KPI",
        "KPI_CODE": kpi_list.join(','),
        "ACTYPE": actype_list.join(','),
        "VALUE_TYPE": "kpi_value_d",
        "DATE_TYPE": "L,M",
        'OPTIMIZE': 1,
        "LIMIT": query_limit
    }

    $.ajax({
        type: 'post',
        url: "/bi/query/getaccodekpi",
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        data: JSON.stringify(param),
        success: function (response) {

            if (response.data != undefined) {
                all_actype_data = response.data;
                loadingInProgress--;
                checkDataReady();
            }

        },
        error: function () { }
    });



    // 获取例会周对应的日期范围
    function getWeekDateRange(week_list, callback) {
        var param = {
            "DATE_ID": week_list.join(','),
            "FIELD": "DATE_DESC" // 对应数据表字段 DATE_DESC
            //"FIELD":"DATE_TYPE" // 对应数据表字段 DATE_DESC_XS
        }

        $.ajax({
            type: 'post',
            url: "/bi/web/datetype",
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            data: JSON.stringify(param),
            success: function (response) {

                weekDateRangeList = response;

                // 今天
                var date = new Date();
                var mm = date.getMonth() + 1;
                var dd = date.getDate();
                if (mm < 10) {
                    mm = '0' + mm;
                }
                if (dd < 10) {
                    dd = '0' + dd;
                }
                var latest_date = date.getFullYear() + '' + mm + '' + dd;

                // 删除开始日期>今天的
                for (var week in weekDateRangeList) {
                    var range = weekDateRangeList[week];
                    var arr = range.split('-');
                    if (Number(arr[0]) > Number(latest_date)) {
                        delete weekDateRangeList[week];
                    }
                }

                //把最新周的结束日期换成上面查到的最新日期

                if (latest_date) {
                    for (var week in weekDateRangeList) {
                        if (Number(week) > Number(latest_week)) {
                            latest_week = week;
                        }
                    }
                    if (latest_week != 0) {
                        var date_range = weekDateRangeList[latest_week];
                        var arr = date_range.split('-');
                        if (Number(arr[1]) > Number(latest_date) && Number(arr[0]) <= Number(latest_date)) {
                            //weekDateRangeList[latest_week] = arr[0]+'-'+latest_date;
                        }

                    }
                }

                if (callback) callback(latest_week);

                checkDataReady();


            },
            error: function () { }
        });
    }



    function checkDataReady() {
        if (loadingInProgress == 0 && weekDateRangeList) {
            kpiDataReady = true;
            updateAllKpi();
            hideLoading();

        }
    }



}



// 修正吨公里耗油趋势
var adjusted_fuel_data;

function setAdjustedFuelTrend(datalist) {
    var chart_id = 'chart_l1';
    var kpi_code = 'MOD_FUELT';
    var colors = ['#0573fc', '#75fc5a'];

    var list;
    if (datalist) {
        list = datalist;
        adjusted_fuel_data = list;
    } else {
        list = adjusted_fuel_data;
    }



    var chart = echarts.init(document.getElementById(chart_id));

    var numofitm = 12;

    var listdata = all_company_data['kpi_value_d'][current_company_code][kpi_code][date_type];
    var listdata_tq = all_company_data['kpi_value_tq_d'][current_company_code][kpi_code][date_type];

    var xAxisData = [];
    var data_s1 = [];
    var data_s2 = [];

    var chart_data = [];
    for (var date in listdata) {
        var val = listdata[date];
        var oo = {
            date: date,
            val: val
        };
        chart_data.push(oo);
    }

    //chart_data.sort(function(a, b){
    //return a.time-b.time
    //});

    var date_type_name;
    var cur_date = getCurrentDate();
    var len = chart_data.length;
    var minval = 999;
    var maxval = 0;
    if (date_type == 'L') {
        for (var i = 0; i < len; i++) {
            var oo = chart_data[i];
            if (oo) {
                var date = oo.date;
                var val = oo.val;
                var val_tq = listdata_tq[date];
                if (val == 0) {
                    val = undefined;
                }
                if (val_tq == 0) {
                    val_tq = undefined;
                }
                if (Number(cur_date) >= Number(date)) {
                    // Week: *********
                    //var label = date.substr(0,4) + '年第' + Number(date.substr(4,3)) + '周';
                    //20170419 双双要求：显示的周数+1
                    //var label = '第' + (Number(date.substr(4,3))) + '周 ';//+ response[date];
                    var label = Number(date.substr(4, 3));
                    xAxisData.push(label);
                    data_s1.push(val);
                    data_s2.push(val_tq);

                    if (minval > val && val > 0) {
                        minval = val;
                    }
                    if (minval > val_tq && val_tq > 0) {
                        minval = val_tq;
                    }
                    if (maxval < val && val > 0) {
                        maxval = val;
                    }
                    if (maxval < val_tq && val_tq > 0) {
                        maxval = val_tq;
                    }
                }
            }

        }

        minval = Math.floor(minval * 1000) / 1000 - 0.005;
        maxval = Math.ceil(maxval * 1000) / 1000 + 0.005;

        xAxisData = xAxisData.slice(-numofitm);
        data_s1 = data_s1.slice(-numofitm);
        data_s2 = data_s2.slice(-numofitm);

        date_type_name = '周'

    } else {
        for (var i = 0; i < len; i++) {
            var oo = chart_data[i];
            if (oo) {
                var date = oo.date;
                var val = oo.val;
                var val_tq = listdata_tq[date];
                if (val == 0) {
                    val = undefined;
                }
                if (val_tq == 0) {
                    val_tq = undefined;
                }
                if (Number(cur_date) >= Number(date)) {
                    // Week: *********
                    //var label = Number(date.substr(4,2)) + '月 \n ' + date.substr(0,4);
                    var label = Number(date.substr(4, 2));
                    xAxisData.push(label);
                    data_s1.push(val);
                    data_s2.push(val_tq);

                    if (minval > val && val > 0) {
                        minval = val;
                    }
                    if (minval > val_tq && val_tq > 0) {
                        minval = val_tq;
                    }
                    if (maxval < val && val > 0) {
                        maxval = val;
                    }
                    if (maxval < val_tq && val_tq > 0) {
                        maxval = val_tq;
                    }
                }
            }


        }

        minval = Math.floor(minval * 1000) / 1000 - 0.005;
        maxval = Math.ceil(maxval * 1000) / 1000 + 0.005;

        xAxisData = xAxisData.slice(-numofitm);
        data_s1 = data_s1.slice(-numofitm);
        data_s2 = data_s2.slice(-numofitm);

        date_type_name = '月'
    }


    option = {
        tooltip: {
            show: true
        },
        legend: {
            show: true,
            top: 10,
            left: 60,
            data: [{
                name: '本期',
                //icon: 'circle',
                textStyle: {
                    color: colors[0]
                }
            }, {
                name: '同期',
                //icon: 'circle',
                textStyle: {
                    color: colors[1]
                }
            }]
        },
        grid: {
            top: 46,
            left: 35,
            right: 0,
            bottom: 30,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            name: date_type_name,
            nameLocation: 'start',
            nameGap: 10,
            boundaryGap: true,
            nameTextStyle: {
                color: '#8fcbff',
                height: 30,
                verticalAlign: 'bottom',
                lineHeight: 60,
                rich: {}
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(255,255,255,0)' // 轴线颜色
                }
            },
            axisLabel: {
                textStyle: {
                    color: '#8fcbff', // 轴标签颜色大小
                    fontSize: 11,
                }
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            min: minval,
            max: maxval,
            nameTextStyle: {
                color: '#8fcbff',
                fontSize: 10
            },
            axisLabel: {
                formatter: '{value}',
                textStyle: {
                    color: '#8fcbff',
                    fontSize: 10,
                }
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(255,255,255,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['#243474'] // 分割线颜色
                }
            }
        }],
        series: [{
            name: '本期',
            type: 'line',
            symbolSize: 3,
            data: data_s1,
            itemStyle: {
                normal: {
                    color: colors[0]
                }
            }
        }, {
            name: '同期',
            type: 'line',
            symbolSize: 3,
            data: data_s2,
            itemStyle: {
                normal: {
                    color: colors[1]
                }
            }
        }]
    };

    chart.setOption(option);

}



// 吨公里耗油趋势
var fuel_data;

function setFuelTrend(datalist) {

    var chart_id = 'chart_l2';
    var kpi_code = 'FUELT';
    var colors = ['#0573fc', '#75fc5a'];

    var list;
    if (datalist) {
        list = datalist;
        fuel_data = list;
    } else {
        list = fuel_data;
    }



    var chart = echarts.init(document.getElementById(chart_id));

    var numofitm = 12;

    var listdata = all_company_data['kpi_value_d'][current_company_code][kpi_code][date_type];
    var listdata_tq = all_company_data['kpi_value_tq_d'][current_company_code][kpi_code][date_type];

    var xAxisData = [];
    var data_s1 = [];
    var data_s2 = [];

    var chart_data = [];
    for (var date in listdata) {
        var val = listdata[date];
        var oo = {
            date: date,
            val: val
        };
        chart_data.push(oo);
    }

    //chart_data.sort(function(a, b){
    //return a.time-b.time
    //});

    var date_type_name;
    var cur_date = getCurrentDate();
    var len = chart_data.length;
    var minval = 999;
    var maxval = 0;
    if (date_type == 'L') {
        for (var i = 0; i < len; i++) {
            var oo = chart_data[i];
            if (oo) {
                var date = oo.date;
                var val = oo.val;
                var val_tq = listdata_tq[date];
                if (val == 0) {
                    val = undefined;
                }
                if (val_tq == 0) {
                    val_tq = undefined;
                }
                if (Number(cur_date) >= Number(date)) {
                    // Week: *********
                    //var label = date.substr(0,4) + '年第' + Number(date.substr(4,3)) + '周';
                    //20170419 双双要求：显示的周数+1
                    //var label = '第' + (Number(date.substr(4,3))) + '周 ';//+ response[date];
                    var label = Number(date.substr(4, 3));
                    xAxisData.push(label);
                    data_s1.push(val);
                    data_s2.push(val_tq);

                    if (minval > val && val > 0) {
                        minval = val;
                    }
                    if (minval > val_tq && val_tq > 0) {
                        minval = val_tq;
                    }
                    if (maxval < val && val > 0) {
                        maxval = val;
                    }
                    if (maxval < val_tq && val_tq > 0) {
                        maxval = val_tq;
                    }
                }
            }

        }

        minval = Math.floor(minval * 1000) / 1000 - 0.005;
        maxval = Math.ceil(maxval * 1000) / 1000 + 0.005;

        xAxisData = xAxisData.slice(-numofitm);
        data_s1 = data_s1.slice(-numofitm);
        data_s2 = data_s2.slice(-numofitm);

        date_type_name = '周'

    } else {
        for (var i = 0; i < len; i++) {
            var oo = chart_data[i];
            if (oo) {
                var date = oo.date;
                var val = oo.val;
                var val_tq = listdata_tq[date];
                if (val == 0) {
                    val = undefined;
                }
                if (val_tq == 0) {
                    val_tq = undefined;
                }
                if (Number(cur_date) >= Number(date)) {
                    // Week: *********
                    //var label = Number(date.substr(4,2)) + '月 \n ' + date.substr(0,4);
                    var label = Number(date.substr(4, 2));
                    xAxisData.push(label);
                    data_s1.push(val);
                    data_s2.push(val_tq);

                    if (minval > val && val > 0) {
                        minval = val;
                    }
                    if (minval > val_tq && val_tq > 0) {
                        minval = val_tq;
                    }
                    if (maxval < val && val > 0) {
                        maxval = val;
                    }
                    if (maxval < val_tq && val_tq > 0) {
                        maxval = val_tq;
                    }
                }
            }


        }

        minval = Math.floor(minval * 1000) / 1000 - 0.005;
        maxval = Math.ceil(maxval * 1000) / 1000 + 0.005;

        xAxisData = xAxisData.slice(-numofitm);
        data_s1 = data_s1.slice(-numofitm);
        data_s2 = data_s2.slice(-numofitm);

        date_type_name = '月'
    }


    option = {
        tooltip: {
            show: true
        },
        legend: {
            show: true,
            top: 10,
            left: 60,
            data: [{
                name: '本期',
                //icon: 'circle',
                textStyle: {
                    color: colors[0]
                }
            }, {
                name: '同期',
                //icon: 'circle',
                textStyle: {
                    color: colors[1]
                }
            }]
        },
        grid: {
            top: 46,
            left: 35,
            right: 0,
            bottom: 30,
        },
        xAxis: [{
            type: 'category',
            data: xAxisData,
            name: date_type_name,
            nameLocation: 'start',
            nameGap: 10,
            boundaryGap: true,
            nameTextStyle: {
                color: '#8fcbff',
                height: 30,
                verticalAlign: 'bottom',
                lineHeight: 60,
                rich: {}
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(255,255,255,0)' // 轴线颜色
                }
            },
            axisLabel: {
                textStyle: {
                    color: '#8fcbff', // 轴标签颜色大小
                    fontSize: 11,
                }
            }
        }],
        yAxis: [{
            type: 'value',
            name: '',
            min: minval,
            max: maxval,
            nameTextStyle: {
                color: '#8fcbff',
                fontSize: 10
            },
            axisLabel: {
                formatter: '{value}',
                textStyle: {
                    color: '#8fcbff',
                    fontSize: 10,
                }
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(255,255,255,0)'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['#243474'] // 分割线颜色
                }
            }
        }],
        series: [{
            name: '本期',
            type: 'line',
            symbolSize: 3,
            data: data_s1,
            itemStyle: {
                normal: {
                    color: colors[0]
                }
            }
        }, {
            name: '同期',
            type: 'line',
            symbolSize: 3,
            data: data_s2,
            itemStyle: {
                normal: {
                    color: colors[1]
                }
            }
        }]
    };

    chart.setOption(option);

}



var selected_kpi = 'MOD_FUELT';

function createCompareChart(kpi) {

    if (ignore_ball_animation) {
        return;
    }

    ignore_ball_animation = true;

    if (!kpi) {
        kpi = selected_kpi;
    } else {
        selected_kpi = kpi;
    }


    var gridCols = 13;
    var gridRows = 6;


    var date = getCurrentDate();
    var len = companylist.length;
    // 获取上下两个象限的最大最小同比数值
    var good_max = 0;
    var good_min = 0;

    var bad_max = 0;
    var bad_min = 0;

    for (var i = len - 1; i >= 0; i--) {
        var comp = companylist[i];
        var compcode = comp.code;
        if (compcode != parent_company) {
            var kpitb = all_company_data['kpi_ratio_tq_d'][compcode][kpi][date_type][date];
            if (kpitb > 0) {
                bad_max = Math.max(bad_max, kpitb);
                //bad_min = Math.min(bad_min, kpitb);
            } else if (kpitb < 0) {
                //good_max = Math.max(good_max, kpitb);
                good_min = Math.min(good_min, kpitb);
            }
        }
    }


    var bad_step = Math.round((bad_max - bad_min) / (gridRows - 2) * 10000) * 0.01;
    var good_step = Math.round((good_max - good_min) / (gridRows - 2) * 10000) * 0.01;


    // 绘制网格
    var width = Math.round($('.block_m .chart').width()) - 1;
    var height = Math.round($('.block_m .chart').height()) - 1;

    var gridx = gridCols;
    var gridy = gridRows * 2;
    var grid_w = (width / gridx);
    var grid_h = (height / gridy);

    var canvas = document.getElementById('canvas_bg');
    var context = canvas.getContext('2d');
    context.clearRect(0, 0, canvas.width, canvas.height);

    context.lineWidth = 1;

    // 网格线
    context.strokeStyle = 'rgba(11,72,148,0.5)';
    //context.globalAlpha = 0.5;
    context.beginPath();

    for (var i = 0; i <= gridx; i++) {
        var xx = Math.floor(i * grid_w) + 0.5;
        context.moveTo(xx, 0);
        context.lineTo(xx, height);
    }
    for (var i = 0; i <= gridy; i++) {
        var yy = Math.floor(i * grid_h) + 0.5;
        context.moveTo(0, yy);
        context.lineTo(width, yy);

        // Y 刻度
        context.font = "10px Arial";
        context.fillStyle = 'rgba(255,255,255,0.9)';
        context.textAlign = "end";
        if (i > 0 && i <= gridRows) {
            context.fillText(trimDecimal(bad_step * (gridRows - i), 2), width - 3, yy - 3, 100);
        } else if (i > gridRows) {
            context.fillText(trimDecimal(good_step * (gridRows - i), 2), width - 3, yy - 3, 100);
        }
    }

    context.fillText('%', width - 3, 9, 100);

    context.stroke();



    // html code

    $('#spaceballs').html('');


    var bgw = $('#spaceballs').width();
    var gridw = $('#spaceballs').width() / gridCols;
    var gridh = $('#spaceballs').height() / (gridRows * 2);

    var cnt = 0;



    for (var i = len - 1; i >= 0; i--) {
        var comp = companylist[i];
        var compcode = comp.code;
        if (compcode != parent_company) {

            var kpival = all_company_data['kpi_value_d'][compcode][kpi][date_type][date];
            var kpitb = all_company_data['kpi_ratio_tq_d'][compcode][kpi][date_type][date];

            if (kpitb == 0 || isNaN(kpitb)) {
                continue;
            }

            var color = kpitb < 0 ? 'ball1' : 'ball2';
            var left;
            var top;
            var type;

            if (color == 'ball1') {
                var rate = (kpitb - good_min) / (good_max - good_min);
                top = gridh * 6 + (gridh * 4) * (1 - rate) - 32;
                left = bgw - (gridw * cnt - cnt * 0.5) - 88;
                type = 'good';
            } else {
                var rate = (kpitb - bad_min) / (bad_max - bad_min);
                top = gridh * 2 + (gridh * 4) * (1 - rate) - 32;
                left = bgw - (gridw * cnt - cnt * 0.5) - 88;
                type = 'bad';
            }

            cnt++;

            var html = '';
            html += '<div class="ball ' + color + ' comp_' + compcode + '" data-top="' + top + '" data-type="' + type + '" data-compid="' + getCompIdByCode(compcode) + '" data-compcode="' + compcode + '" >';
            html += '<div class="logo" style="background-image:url(img/logo_' + compcode + '.png)"></div>';
            html += '<div class="row1 lb">' + kpival + '</div>';
            if (kpitb > 0) {
                html += '<div class="row2 lb"><span class="">' + Math.round(Number(kpitb) * 10000) / 100 + '%</span><span class="red">↑</span></div>';
            } else {
                html += '<div class="row2 lb"><span class="">' + Math.round(Number(kpitb) * 10000) / 100 + '%</span><span class="green">↓</span></div>';
            }

            html += '</div>';

            $('#spaceballs').append(html);

            $('#spaceballs .comp_' + compcode).delay(cnt * 120).animate({
                left: left,
                top: top
            }, {
                duration: 1800,
                specialEasing: {
                    left: "easeOutExpo",
                    top: "easeOutQuad",
                },
                //step: function( now, fx ) {
                //var per = (fx.now-fx.start)/(fx.end-fx.start);
                //console.log(per);
                //if(per == 1){
                //$(fx.elem).css('top', $(fx.elem).attr('data-top')+'px');
                //}
                //},
                complete: function () {
                    $(this).find('.lb').fadeIn(1500);
                }
            });

        }

    }


    // 球 tootip
    $.powerTip.destroy($('#spaceballs .ball'));

    $('#spaceballs .ball').powerTip({
        fadeInTime: 0,
        fadeOutTime: 0,
        followMouse: true,
        offset: 20,
        manual: true,
    });

    // 鼠标移到球上的tooltip
    $('#spaceballs .ball').data('powertip', function () {

        var compid = $(this).attr('data-compid');
        var compcode = $(this).attr('data-compcode');
        var type = $(this).attr('data-type');

        var date = getCurrentDate();

        var acvallist = [];

        var kpi_id = kpi_id_list[kpi];

        for (var cid in all_comp_actype_data) {
            if (compid == cid) {
                var acdatlist = all_comp_actype_data[cid][kpi_id][date_type];
                var acdatlist_tb = all_comp_actype_tb_data[cid][kpi_id][date_type];
                for (var accode in acdatlist) {
                    var acdat = acdatlist[accode].date;
                    var acdat_tb = acdatlist_tb[accode].date;
                    if (acdat.length > 0) {
                        var lll = acdat.length;
                        for (var j = 0; j < lll; j++) {
                            var dd = acdat[j];
                            var dd_tb = acdat_tb[j];
                            var tb = Math.round(Number(dd_tb.value) * 10000) / 100;
                            if (dd.date == date) {
                                acvallist.push({
                                    accode: accode,
                                    val: dd.value,
                                    tb: tb
                                });
                                break;
                            }
                        }
                    }
                }
                break;
            }
        }

        if (type == 'good') {
            acvallist.sort(function (a, b) {
                return Number(a.tb) - Number(b.tb);
            });
        } else {
            acvallist.sort(function (a, b) {
                return Number(b.tb) - Number(a.tb);
            });
        }

        var len = Math.min(acvallist.length, 3);

        var html = '';
        html += '<div class="chart_tip">';
        html += '<div style="color:#04173c; font-weight:bold; ">';
        html += companyCode2Name[compcode];
        html += '</div>';
        html += '<div style="color:#395b9d;">';
        html += '同比变化机型Top3';
        html += '</div>';
        for (var i = 0; i < len; i++) {
            var d = acvallist[i];



            if (type == 'good' && d.tb < 0) {
                html += '<div style="">';
                html += '<span style="color:#c60618;padding-right:3px;">' + d.accode + '</span>';
                html += '<span style="color:#04173c;">' + d.val + '</span>';
                html += '&nbsp;&nbsp;<span style="color:#1a8a00">' + d.tb + '%</span>';
                html += '<span style="color:#1a8a00">↓</span>';
                html += '</div>';
            } else if (type == 'bad' && d.tb > 0) {
                html += '<div style="">';
                html += '<span style="color:#c60618;padding-right:3px;">' + d.accode + '</span>';
                html += '<span style="color:#04173c;">' + d.val + '</span>';
                html += '&nbsp;&nbsp;<span class="red">' + d.tb + '%</span>';
                html += '<span class="red">↑</span>';
                html += '</div>';
            }

        }
        return html;
    });

    $('#spaceballs .ball').off('mouseover');
    $('#spaceballs .ball').off('mouseout');
    $('#spaceballs .ball').off('click');

    $('#spaceballs .ball').on('mouseover', function (evt) {
        $.powerTip.show($(this));
    });
    $('#spaceballs .ball').on('mouseout', function (evt) {
        $.powerTip.hide();
    });

    $('#spaceballs .ball').on('click', function (evt) {
        var compcode = $(this).attr('data-compcode');
        if (usersCompayCodeList.indexOf(compcode) > -1) {
            switchCompany(compcode);
        }
    });



    setTop5();

}


function setTop5() {

    var len = companylist.length;

    var date = getCurrentDate();

    var toplist = [];

    for (var i = len - 1; i >= 0; i--) {
        var comp = companylist[i];
        var compcode = comp.code;
        if (compcode != parent_company) {
            var kpival = all_company_data['kpi_value_d'][compcode][selected_kpi][date_type][date];
            var kpitb = all_company_data['kpi_ratio_tq_d'][compcode][selected_kpi][date_type][date];
            if (kpitb > 0) {
                var dd = {
                    val: kpival,
                    tb: kpitb,
                    code: compcode,
                    name: companyCode2Nameabbr[compcode]
                }
                toplist.push(dd);
            }
        }
    }


    toplist.sort(function (a, b) {
        return Number(b.tb) - Number(a.tb);
    });

    var html_tr1 = '';
    var html_tr2 = '';
    var html_tr3 = '';

    var len = toplist.length;

    for (var i = 0; i < 5; i++) {
        if (i < len) {
            var d = toplist[i];
            html_tr1 += '<td>';
            html_tr1 += '<span class="rank">' + (i + 1) + '</span>';
            html_tr1 += '<span class="name">' + d.name + '</span>';
            html_tr1 += '</td>';

            html_tr2 += '<td>';
            html_tr2 += d.val;
            html_tr2 += '</td>';

            html_tr3 += '<td>';
            html_tr3 += '<span class="red val">' + Math.round(Number(d.tb) * 10000) / 100 + '%</span>';
            html_tr3 += '<span class="red fs14">↑</span>';
            html_tr3 += '</td>';

        } else {
            var d = toplist[i];
            html_tr1 += '<td>';
            html_tr1 += '<span class="rank">' + (i + 1) + '</span>';
            html_tr1 += '<span class="name">&nbsp;</span>';
            html_tr1 += '</td>';

            html_tr2 += '<td>';
            html_tr2 += '&nbsp;';
            html_tr2 += '</td>';

            html_tr3 += '<td>';
            html_tr3 += '<span class="red val">&nbsp;</span>';
            html_tr3 += '<span class="red fs14">&nbsp;</span>';
            html_tr3 += '</td>';

        }


    }

    var html = '';

    html += '<tr class="row1">';
    html += '<td class="col1">公司<br>排名</td>';
    html += html_tr1;
    html += '</tr>';

    html += '<tr class="row2">';
    html += '<td class="col1">本期</td>';
    html += html_tr2;
    html += '</tr>';

    html += '<tr class="row3">';
    html += '<td class="col1">同比</td>';
    html += html_tr3;
    html += '</tr>';

    $('.top5table').html(html);

}



var selected_ac_kpi = 'MOD_FUELT';

function setAcFuel(kpi) {

    if (!kpi) {
        kpi = selected_ac_kpi;
    } else {
        selected_ac_kpi = kpi;
    }

    var kpi_id = kpi_id_list[kpi];

    var date = getCurrentDate();
    var acDatList = all_actype_data[kpi_id][date_type];

    var html = '';


    var barMaxWidth = 100;
    var kpiMaxVal = 0.4;
    var kpiMinVal = 0.1;

    var ll = actype_list.length;
    for (var jj = 0; jj < ll; jj++) {
        var accode = actype_list[jj];
        if (!acDatList[accode] || !acDatList[accode].date) {
            continue;
        }
        var dlst = acDatList[accode].date;
        var kpival = 0;
        for (var i = dlst.length - 1; i >= 0; i--) {
            var ob = dlst[i];
            if (ob.date == date) {
                kpival = Number(ob.value);
                if (isNaN(kpival)) kpival = 0;
                break;
            }
        }

        if (kpival == 0) {
            continue;
        }

        var barw = (kpival - kpiMinVal) / (kpiMaxVal - kpiMinVal) * barMaxWidth;

        html += '<table>';
        html += '<tr class="avg">';
        html += '<td class="ac">' + accode + '</td>';
        html += '<td>';
        html += '<span class="bar" style="width: ' + barw + 'px;"></span>';
        html += '<span class="val">' + kpival + '</span>';
        html += '</td>';
        html += '</tr>';

        var html_l = '';
        var html_h = '';


        for (var compid in all_comp_actype_data) {
            var compdat = all_comp_actype_data[compid][kpi_id][date_type][accode].date;
            if (compdat && compdat.length > 0) {
                for (var i = compdat.length - 1; i >= 0; i--) {
                    var ob = compdat[i];
                    if (ob.date == date) {
                        var val = Number(ob.value);
                        if (val < kpival) {
                            var compcode = getCompCodeById(compid);
                            barw = (val - kpiMinVal) / (kpiMaxVal - kpiMinVal) * barMaxWidth;
                            html_l += '<tr class="high">';
                            html_l += '<td class="comp">' + compcode + '</td>';
                            html_l += '<td>';
                            html_l += '<span class="bar" style="width: ' + barw + 'px;"></span>';
                            html_l += '<span class="val">' + val + '</span>';
                            html_l += '</td>';
                            html_l += '</tr>';

                        } else if (val > kpival) {
                            var compcode = getCompCodeById(compid);
                            barw = (val - kpiMinVal) / (kpiMaxVal - kpiMinVal) * barMaxWidth;
                            html_h += '<tr class="low">';
                            html_h += '<td class="comp">' + compcode + '</td>';
                            html_h += '<td>';
                            html_h += '<span class="bar" style="width: ' + barw + 'px;"></span>';
                            html_h += '<span class="val">' + val + '</span>';
                            html_h += '</td>';
                            html_h += '</tr>';

                        }
                    }
                }
            }
        }

        html += html_l;
        html += html_h;
        html += '</table>';


    }

    $('#ac_kpi_list1').html(html);
    $("#ac_kpi_list").niceScroll({
        cursorcolor: "rgba(255,255,255,0.2)",
        cursorborder: "rgba(0,0,0,0)"
    });
    $("#ac_kpi_list").getNiceScroll().resize()
    // console.log(niceScroll)
    //if($('#ac_kpi_list1').height() > $('#ac_kpi_list').height()){
    // 列表滚动
    /*
    var speed=80;
    var base_sec=document.getElementById("ac_kpi_list"); 
    var base_sec2=document.getElementById("ac_kpi_list2"); 
    var base_sec1=document.getElementById("ac_kpi_list1"); 
    base_sec2.innerHTML=base_sec1.innerHTML;
    function base_Marquee(){
        if(base_sec2.offsetTop-base_sec.scrollTop<=0)
          base_sec.scrollTop-=base_sec1.offsetHeight;
        else{ 
          base_sec.scrollTop += Math.ceil(1/pageZoomScale);
        } 
    }
    clearInterval(marquee_itv_aclst);
    marquee_itv_aclst=setInterval(base_Marquee,speed);
    base_sec.onmouseover=function() {clearInterval(marquee_itv_aclst)}
    base_sec.onmouseout=function() {marquee_itv_aclst=setInterval(base_Marquee,speed)} 
    */
    //}

}


function getCompCodeById(id) {
    var len = companylist.length;
    for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        if (dat.id == id) {
            return dat.code;
        }
    }
    return '';
}

function getCompIdByCode(code) {
    var len = companylist.length;
    for (var i = 0; i < len; i++) {

        var dat = companylist[i];
        if (dat.code == code) {
            return dat.id;
        }
    }
    return '';
}


function dateChanged() {
    ignore_ball_animation = false;
    updateAllKpi();
}


function updateAllKpi(data, label) {
    if (!kpiDataReady) {
        return;
    }

    $('.compname').text(companyCode2Name[current_company_code]);

    //year
    var d = new Date();
    var y = d.getFullYear();
    var ddd1 = all_company_year_data[current_company_code]['MOD_FUELT']['Y'][y];
    var ddd2 = all_company_year_data[current_company_code]['FUELT']['Y'][y];

    if (isNaN(ddd1)) {
        ddd1 = '-';
        audioValueData.Y_MOD_FUELT = '无';
    } else {
        audioValueData.Y_MOD_FUELT = ddd1;
    }
    if (isNaN(ddd2)) {
        ddd2 = '-';
        audioValueData.Y_FUELT = '无';
    } else {
        audioValueData.Y_FUELT = ddd2;
    }

    $('#MOD_FUELT_Y').text(ddd1);
    $('#FUELT_Y').text(ddd2);

    //
    createCompareChart();
    setAdjustedFuelTrend();
    setFuelTrend();
    setAcFuel();

    setTitleDate();

    startLoadAudio();

}



var audioValueData = {};

function startLoadAudio() {

    stopAudio();

    // 语音播报

    /* 
    A3.6-general
    {COMP} {DATE} 
    年度累计修正吨公里耗油 {Y_MOD_FUELT}，
    年度累计吨公里耗油 {Y_FUELT}，

    */

    var tpl = '';

    //--
    var compname = companyCode2Name[current_company_code];
    tpl += compname + ' ';

    //--
    var date = getCurrentDate();
    var datelabel = '';
    if (date_type == 'L') {
        // Week: *********
        //var label = date.substr(0,4) + '年第' + Number(date.substr(4,3)) + '周';
        //20170419 双双要求：显示的周数+1
        datelabel = date.substr(0, 4) + '年' + '第' + (Number(date.substr(4, 3))) + '周';
    } else if (date_type == 'M') {
        datelabel = date.substr(0, 4) + '年' + Number(date.substr(4, 2)) + '月';
    }
    tpl += datelabel + ' ';

    tpl += '年度累计修正吨公里耗油 ' + audioValueData.Y_MOD_FUELT + '。';
    tpl += '年度累计吨公里耗油 ' + audioValueData.Y_FUELT + '。';

    text2audio(tpl, true);

    startLoadAudio2();



}


function startLoadAudio2() {

    // 语音播报

    var kpi = selected_kpi;

    var date = getCurrentDate();
    var len = companylist.length;
    var audiotpl = '';
    var audiotpl1 = '';
    var audiotpl2 = '';

    for (var i = 0; i < len; i++) {
        var comp = companylist[i];
        var compcode = comp.code;
        if (compcode != parent_company) {

            var kpival = all_company_data['kpi_value_d'][compcode][kpi][date_type][date];
            var kpitb = all_company_data['kpi_ratio_tq_d'][compcode][kpi][date_type][date];

            if (kpitb == 0 || isNaN(kpitb)) {
                continue;
            }

            var tb = Math.round(Number(kpitb) * 10000) / 100;
            var tbtpl = ratioTemplete(tb);

            if (i < 6) {
                audiotpl1 += companyCode2Name[compcode] + ' ' + kpival + '，同比' + tbtpl + '。';
            } else {
                audiotpl2 += companyCode2Name[compcode] + ' ' + kpival + '，同比' + tbtpl + '。';
            }

        }

    }

    var audiotop5tpl = '';
    var toplist = [];
    for (var i = len - 1; i >= 0; i--) {
        var comp = companylist[i];
        var compcode = comp.code;
        if (compcode != parent_company) {
            var kpival = all_company_data['kpi_value_d'][compcode][kpi][date_type][date];
            var kpitb = all_company_data['kpi_ratio_tq_d'][compcode][kpi][date_type][date];
            if (kpitb > 0) {
                var dd = {
                    val: kpival,
                    tb: kpitb,
                    code: compcode,
                    name: companyCode2Nameabbr[compcode]
                }
                toplist.push(dd);
            }
        }
    }


    toplist.sort(function (a, b) {
        return Number(b.tb) - Number(a.tb);
    });

    var html_tr1 = '';
    var html_tr2 = '';
    var html_tr3 = '';

    var len = Math.min(toplist.length, 5);

    for (var i = 0; i < len; i++) {

        var d = toplist[i];
        var tb = Math.round(Number(d.tb) * 10000) / 100;
        var tbtpl = ratioTemplete(tb);
        audiotop5tpl += d.name + ' ' + d.val + '，同比' + tbtpl + '。';

    }

    /* 
    
    旅云平台各公司修正吨公里耗油对比：{COMPS_MOD_FUELT}
    燃效同比变差TOP5的公司：{TOP5_MOD_FUELT}

    */
    text2audio('旅云平台各公司修正吨公里耗油对比：' + audiotpl1, false);
    text2audio(audiotpl2, false);

    text2audio('燃效同比变差TOP五的公司：' + audiotop5tpl, false, switchToTab2);

    startLoadAudio3();


}


function startLoadAudio3() {

    // 语音播报

    var kpi = selected_kpi;

    var date = getCurrentDate();
    var len = companylist.length;
    var audiotpl = '';
    var audiotpl1 = '';
    var audiotpl2 = '';

    for (var i = 0; i < len; i++) {
        var comp = companylist[i];
        var compcode = comp.code;
        if (compcode != parent_company) {

            var kpival = all_company_data['kpi_value_d'][compcode][kpi][date_type][date];
            var kpitb = all_company_data['kpi_ratio_tq_d'][compcode][kpi][date_type][date];

            if (kpitb == 0 || isNaN(kpitb)) {
                continue;
            }

            var tb = Math.round(Number(kpitb) * 10000) / 100;
            var tbtpl = ratioTemplete(tb);

            if (i < 6) {
                audiotpl1 += companyCode2Name[compcode] + ' ' + kpival + '，同比' + tbtpl + '。';
            } else {
                audiotpl2 += companyCode2Name[compcode] + ' ' + kpival + '，同比' + tbtpl + '。';
            }

        }

    }


    var audiotop5tpl = '';
    var toplist = [];
    for (var i = len - 1; i >= 0; i--) {
        var comp = companylist[i];
        var compcode = comp.code;
        if (compcode != parent_company) {
            var kpival = all_company_data['kpi_value_d'][compcode][kpi][date_type][date];
            var kpitb = all_company_data['kpi_ratio_tq_d'][compcode][kpi][date_type][date];
            if (kpitb > 0) {
                var dd = {
                    val: kpival,
                    tb: kpitb,
                    code: compcode,
                    name: companyCode2Nameabbr[compcode]
                }
                toplist.push(dd);
            }
        }
    }


    toplist.sort(function (a, b) {
        return Number(b.tb) - Number(a.tb);
    });

    var html_tr1 = '';
    var html_tr2 = '';
    var html_tr3 = '';

    var len = Math.min(toplist.length, 5);

    for (var i = 0; i < len; i++) {

        var d = toplist[i];
        var tb = Math.round(Number(d.tb) * 10000) / 100;
        var tbtpl = ratioTemplete(tb);
        audiotop5tpl += d.name + ' ' + d.val + '，同比' + tbtpl + '。';

    }

    /* 
    
    A3.6-compare
    旅云平台各公司吨公里耗油对比：{COMPS_FUELT}
    燃效同比变差TOP5的公司：{TOP5_FUELT}

    */
    /* 
    
    旅云平台各公司修正吨公里耗油对比：{COMPS_MOD_FUELT}
    燃效同比变差TOP5的公司：{TOP5_MOD_FUELT}

    */
    text2audio('旅云平台各公司吨公里耗油对比：' + audiotpl1, false);
    text2audio(audiotpl2, false);

    text2audio('燃效同比变差TOP五的公司：' + audiotop5tpl, false);


}


function getCurrentDate() {
    var date = '';
    if (date_type == 'L') {
        date = $('#main_cb_week').attr('data');
    } else if (date_type == 'M') {
        date = $('#main_cb_month').attr('data');
    }
    return date;
}



/////////////////////////////

var currentTabIndex = 0;

$('.block_m .tab1').on('click', function (e) {
    if (currentTabIndex == 0) {
        return;
    }
    currentTabIndex = 0;

    $('.block_m .tab1').addClass('selected');
    $('.block_m .tab2').removeClass('selected');

    ignore_ball_animation = false;
    createCompareChart('MOD_FUELT');

    stopAudio();
    startLoadAudio2();

    clearTimeout(itv_autoSwitchTab);
    itv_autoSwitchTab = setTimeout(autoSwitchTab, 20000);
});

$('.block_m .tab2').on('click', function (e) {
    if (currentTabIndex == 1) {
        return;
    }
    currentTabIndex = 1;

    $('.block_m .tab1').removeClass('selected');
    $('.block_m .tab2').addClass('selected');

    ignore_ball_animation = false;
    createCompareChart('FUELT');

    stopAudio();
    startLoadAudio3();

    clearTimeout(itv_autoSwitchTab);
    itv_autoSwitchTab = setTimeout(autoSwitchTab, 20000);

});


function switchToTab1() {
    $('.block_m .tab1').click();
}

function switchToTab2() {
    $('.block_m .tab2').click();
}

// 自动循环切换两个TAB
var itv_autoSwitchTab;

function autoSwitchTab() {
    clearTimeout(itv_autoSwitchTab);

    if (!autoSwitch) {
        itv_autoSwitchTab = setTimeout(autoSwitchTab, 10);
        return;
    }

    if (!audioEnded && !audioPaused) {
        itv_autoSwitchTab = setTimeout(autoSwitchTab, 10);
        return;
    }

    if (currentTabIndex == 0) {
        switchToTab2();
    } else {
        switchToTab1();
    }

    itv_autoSwitchTab = setTimeout(autoSwitchTab, 20000);

}


/////////////////////////////

var currentAcTabIndex = 0;

$('.block_r .tab1').on('click', function (e) {
    currentAcTabIndex = 0;

    $('.block_r .tab1').addClass('selected');
    $('.block_r .tab2').removeClass('selected');

    setAcFuel('MOD_FUELT');

    clearTimeout(itv_autoSwitchAcTab);
    itv_autoSwitchAcTab = setTimeout(autoSwitchAcTab, 20000);
});

$('.block_r .tab2').on('click', function (e) {
    currentAcTabIndex = 1;

    $('.block_r .tab1').removeClass('selected');
    $('.block_r .tab2').addClass('selected');

    setAcFuel('FUELT');

    clearTimeout(itv_autoSwitchAcTab);
    itv_autoSwitchAcTab = setTimeout(autoSwitchAcTab, 20000);

});


// 自动循环切换两个TAB
var itv_autoSwitchAcTab;

function autoSwitchAcTab() {
    clearTimeout(itv_autoSwitchAcTab);

    if (!autoSwitch) {
        itv_autoSwitchAcTab = setTimeout(autoSwitchAcTab, 10);
        return;
    }

    if (currentAcTabIndex == 0) {
        $('.block_r .tab2').click();
    } else {
        $('.block_r .tab1').click();
    }

    itv_autoSwitchAcTab = setTimeout(autoSwitchAcTab, 20000);

}

// 填充标题
setTimeout(function () {
    $("[data-name='companyname']").text($.grep(companylist, function (ele, i) {
        return ele.id == '100';
    })[0].name);
}, 1000)


// ---------------------- 

function onCompanyChanged(comp_code) {
    current_company_code = comp_code;
    checkCompanyReay();
}

function checkCompanyReay() {
    if (companyCode2Name[current_company_code] == undefined) {
        setTimeout(checkCompanyReay, 0);
    } else {
        updateAllKpi();
    }
}



function setTitleDate() {
    var date = '';
    if (date_type == 'L') {
        date = $('#main_cb_week .combobox_label').text();
    } else if (date_type == 'M') {
        date = $('#main_cb_month .combobox_label').text();
    }
    $('.pagetitle .maintitle .date').html(date); // 页面标题-时间
}



getAllCompanyKpiData();

dfd.done(function () {
    if (!hasAllCompanyPermission()) {
        $(".block_m").addClass("hide");
        $(".block_r").addClass("hide");
    }
})

////////