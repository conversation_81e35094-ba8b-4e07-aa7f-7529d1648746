<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">

    <title>HNA数字航空</title>

    <meta http-equiv="refresh" content="86400">

    <script>
        var script = "<script src='js/app.js?ts="+ new Date().getTime() +"' ><\/script>";
        document.write(script);
    </script>
    <link href="css/nprogress.css" rel="stylesheet">
    <script src="js/nprogress.js"></script>
    
 

    <script src="js/jquery-1.11.1.js"></script>

    <script src="js/bootstrap.min.js"></script>
    <script src="js/echarts.min.js"></script>
    <script src="js/jquery.powertip.min.js"></script>
    <script src="js/moment.min.js"></script>

    <script>
        loadjs('js/tingyun.js')
        loadCss('css/bootstrap.css')
        loadCss('css/bootstrap-theme.css')
        loadCss('css/main.css')
        loadCss('css/a3-1.css')
        loadCss('css/bootstrap-datetimepicker.min.css')
        loadjs('js/json.js')
        loadjs('js/ui.js')
        loadjs('js/util.js')
        loadjs('js/lib/eking.js')
        loadjs('js/slider.js')
        loadjs('js/common.js')
     </script>

</head>
<body style="opacity:0;" >

<iframe id="login" scrolling="no" width="0" height="0" style="display:none;" ></iframe>

<audio id="text2audio"></audio>
<iframe id="mainframe" class="mainframe" allowtransparency="true" scrolling="no"></iframe>

<div id="selected_mapmarkpoint" class="spinner_animate"></div>

<div id="header">

    <div id="logo">
        <div id="main_page_title"></div>
        <div id="main_page_subtitle"></div>
    </div><!-- /#logo -->
   

    <div id="nav">
    </div><!-- /#nav -->

    <div id="topright">
        
    </div><!-- /#topright -->

</div><!-- /#header -->


<div class="page-wrapper" id="page-parent-comp">


    <div class="page" style="pointer-events: none;">

        <div class="pagetitle">
            <div class="maintitle">
                <span class="date"></span><span class="comp"></span></span><span class="tit">
            </div>
            <div id="main_cb_week" class="combotab_selected"></div>
            <div id="main_cb_month" class="combotab"></div>
            <div id="week_date_range"></div>
        </div><!-- /pagetitle -->

        <div id="hna_button"></div>


        <div class="block_l1 block-frame">

            <div class="block-header tit">
                
            </div>
            <div class="cont">
                <div class="row1">

                    <div class="r1">
                        <span class="fs14 blue_l bold">航班总量</span>
						<div class="config_link"></div>
						<br>
                        <span id="" class="kpi"><span class="val ffnum fs22 "></span><span class="sub fs12 blue_l">班次</span></span> 
                        <span id="" class="hb"><span class="fs12 blue_l">环比</span> <span class="val ffnum fs16"></span> <span class="green hide fs14">↑</span><span class="red hide fs14">↓</span></span>
                    </div>

                    <div class="r2">
                        <div class="col c1">
                            <span class="fs14 blue_l bold">国内</span><br>
                            <span id="" class="kpi"><span class="val ffnum fs18 "></span><span class="sub fs12 blue_l">班次</span></span><br>
                            <span id="" class="hb"><span class="fs12 blue_l">环比</span> <span class="val ffnum fs16"></span> <span class="green hide fs14">↑</span><span class="red hide fs14">↓</span></span>
                        </div>

                        <div class="col c2">
                            <span class="fs14 blue_l bold">国际</span><br>
                            <span id="" class="kpi"><span class="val ffnum fs18 "></span><span class="sub fs12 blue_l">班次</span></span><br>
                            <span id="" class="hb"><span class="fs12 blue_l">环比</span> <span class="val ffnum fs16"></span> <span class="green hide fs14">↑</span><span class="red hide fs14">↓</span></span>
                        </div>
                    </div>

                </div>

                <div class="row2">
                    
                    <div class="r1">
                        <span class="fs14 blue_l bold">正常率</span><br>
                        <span id="" class="kpi yellow"><span class="val ffnum fs22 "></span><span class="sub fs12">%</span></span> 
                        <span id="" class="hb"><span class="fs12 blue_l">环比</span> <span class="val ffnum fs16"></span> <span class="green hide fs14">↑</span><span class="red hide fs14">↓</span></span>
                    </div>

                </div>

                <div class="row3">
                    
                    <div class="r1">
                        <span class="fs14 blue_l bold">已执行运输旅客量</span><br>
                        <span id="" class="kpi"><span class="val ffnum fs22"></span><span class="sub fs12 blue_l">万人次</span></span> 
                        <span id="" class="hb"><span class="fs12 blue_l">环比</span> <span class="val ffnum fs16"></span> <span class="green hide fs14">↑</span><span class="red hide fs14">↓</span></span>
                    </div>

                </div>

            </div>
            
        </div><!-- /block -->


        <div class="block_l2 block-frame">

            <div class="block-header tit">
                旅客运输量趋势
            </div>

            <div class="cont">

                <div class="tabc tabc1" style="position: absolute; top:15px; width:314px; height:170px;">
                    <div id="chart_l2t1" class="chartblock" style="position: absolute; width:314px; height:170px;" prop-width="314" prop-height="170"></div>
                </div>
                
            </div>
            
        </div><!-- /block -->


        <div class="block_r1 block-frame" id="allCompanyBlock">

            <div class="block-header">
                各航司运行情况
            </div>
            <div class="cont fs12">
                
                <table>
                   <thead>
                        <tr>
                            <th class="name">航司</th>
                            <th class="flt" data-name="flt" data-sort=""><span>航班量</span></th>
                            <th class="psr" data-name="psr" data-sort=""><span>旅客量</span></th>
                            <th class="acnum" data-name="acnum" data-sort=""><span>飞机架数</span></th>
                            <th class="rate" data-name="rate" data-sort=""><span>正常率</span></th>
                            <th class="hb" data-name="hb" data-sort=""><span>环比</span></th>
                        </tr>
                    </thead>

                    <tbody>
                        <!-- table content -->
                    </tbody>
                </table>
                
            </div>
            
        </div><!-- /block -->


        <div class="block_r2 block-frame">
            <!--
            <div class="block-header">
                日利用率未达标情况
            </div>
            <div class="cont fs12">
                
                <table>
                   <thead>
                        <tr>
                            <th class="name">Top5航司</th>
                            <th class=""><span>机型</span></th>
                            <th class=""><span>日利用率</span></th>
                            <th class=""><span>任务缺口</span></th>
                        </tr>
                    </thead>

                    <tbody>
                    </tbody>
                </table>
            </div>
            -->

            <div class="block-header tit">
                正常率趋势
            </div>

            <div class="cont">

                <div class="tabc tabc2" style="position: absolute; top:15px; width:314px; height:170px;">
                    <div id="chart_l2t2" class="chartblock" style="position: absolute; width:314px; height:170px;" prop-width="314" prop-height="170"></div>
                </div>
                
            </div>
            
        </div><!-- /block -->



        



        




    </div><!-- /#page -->



    <div id="map_legend">
        <div class="tit fs12">正常率图例</div>
        <div class="itm l1 fs10"></div>
        <div class="itm l2 fs10"></div>
    </div><!-- /#map_legend -->



</div><!-- /#wrapper -->




<div id="popover_map" class="bold">
    <div class="title fs20"></div>
    <div class="close"></div>

    <div class="col1">
        <span class="SHIFTS">
            <span class="blk fs12 bigtt">航班总量</span>
            <span class="blk bigtxt kpi">
                <span class="val ffnum fs26"></span><span class="sub fs10 blue_ll">班次</span>
            </span>
            <span class="blk hb">
                <span class="blue_l fs12">环比</span> <span ><span class="val ffnum fs14 blue_ll"></span><span class="sub fs10 blue_ll">%</span><span class="green hide">↑</span><span class="red hide">↓</span></span>
            </span>
        </span>

        <span class="SHIFTS_L">
            <span class="blk kpi" style="margin-top: 15px;">
                <span class="blue_l fs14">国内航班</span> <span ><span class="val ffnum fs16"></span><span class="sub fs10 blue_ll">班次</span></span>
            </span>
            <span class="blk hb">
                <span class="blue_l fs12">环比</span> <span ><span class="val ffnum fs14 blue_ll"></span><span class="sub fs10 blue_ll">%</span><span class="green hide">↑</span><span class="red hide">↓</span></span>
            </span>
        </span>

        <span class="SHIFTS_I">
            <span class="blk kpi">
                <span class="blue_l fs14">国际航班</span> <span ><span class="val ffnum fs16">120.12</span><span class="sub fs10 blue_ll">班次</span></span>
            </span>
            <span class="blk hb">
                <span class="blue_l fs12">环比</span> <span ><span class="val ffnum fs14 blue_ll"></span><span class="sub fs10 blue_ll">%</span><span class="green hide">↑</span><span class="red hide">↓</span></span>
            </span>
        </span>

    </div>


    <div class="col2">
        <span class="NORMAL_RATE">
            <span class="blk fs12 bigtt">正常率</span>
            <span class="blk bigtxt kpi">
                <span id="" class="val ffnum fs26"></span><span class="sub fs10">%</span>
            </span>
            <span class="blk hb">
                <span class="blue_l fs12">环比</span> <span ><span class="val ffnum fs14 blue_ll"></span><span class="sub fs10 blue_ll">%</span><span class="green hide">↑</span><span class="red hide">↓</span></span>
            </span>
        </span>

        <span class="TRV_NUM">
            <span class="blk fs12 bigtt" style="margin-top: 15px;">旅客运输量</span>
            <span class="blk bigtxt kpi">
                <span id="" class="val ffnum fs26"></span><span class="sub fs10 blue_ll">万人次</span>
            </span>
            <span class="blk hb">
                <span class="blue_l fs12">环比</span> <span ><span class="val ffnum fs16 blue_ll"></span><span class="sub fs10 blue_ll">%</span><span class="green hide">↑</span><span class="red hide">↓</span></span>
            </span>
        </span>

    </div>  

    

</div><!-- /#popover_map -->

<style type="text/css">
	.config_link {
		display: inline-block;
		position: relative;
		width: 23px;
		height: 23px;
		left: 10px;
		top: 6px;
		cursor: pointer;
		pointer-events: auto;
		background: url(./img/ext_link1.png) no-repeat center center;
	}
</style>
<script type="text/javascript">
	$(function () {
			 $('.config_link').on('click',function(){
				 let self = document.domain
				 let vis = 'https://vis.hnair.net/vis-admin//#/kpiConfig/companyKpi'
				 let cdp = 'https://cdp-test.hnair.net/vis-admin//#/kpiConfig/companyKpi'
				 let url = self.includes('vis') ? vis : cdp
				 windowOpen(url , '_blank')
			 })
			 
			 function windowOpen(url, target) {
			   var a = document.createElement("a");
			   a.setAttribute("href", url);
			   if (target == null) {
			     target = '';
			   }
			   a.setAttribute("target", target);
			   document.body.appendChild(a);
			   if (a.click) {
			     a.click();
			   } else {
			     try {
			       var evt = document.createEvent('Event');
			       a.initEvent('click', true, true);
			       a.dispatchEvent(evt);
			     } catch (e) {
			       window.open(url);
			     }
			   }
			   document.body.removeChild(a);
			 }
			 
	})
</script>


</body>
</html>




<script>
   
    loadjs4BabelDefer('js/a3-1_exhibition.js')
   
</script>



