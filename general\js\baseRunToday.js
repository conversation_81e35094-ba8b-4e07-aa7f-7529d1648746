// 机场列表
var airportList;
//公司
var comp_code;
var comp_code_list = [
  "HU",
  "8L",
  "HX",
  "PN",
  "GS",
  "JD",
  "FU",
  "UQ",
  "Y8",
  "GX",
  "9H",
  "GT",
];
var comp_cause = [
  "飞机故障",
  "运力调配",
  "工程机务",
  "航班计划",
  "航材保障",
  "航务保障",
  "机组保障",
  "飞行机组保障",
  "乘务组",
  "乘务组保障",
  "空警安全员",
  "地面保障",
  "货运保障",
  "公司原因",
  "其他航空公司原因",
];
var none_cause = [
  "公共安全",
  "机场",
  "军事活动",
  "空管",
  "离港系统",
  "联检",
  "旅客",
  "民航局航班时刻安排",
  "天气原因",
  "油料",
];
var weather_map = {
  晴: "icon-e600_sunny",
  沙: "icon-e617_dust1",
  雹: "icon-e620_hail",
  雾: "icon-e615_fog",
  烟: "icon-e615_fog",
  阴: "icon-e604_gloomy",
  雷: "icon-e606_rain2",
  暴: "icon-e606_rain2",
  风: "icon-e612_wind",
  霾: "icon-e613_haze",
  云: "icon-e602_cloudy",
  雨: "icon-e607_rain3",
  雪: "icon-e610_snow3",
};
const BASE_CODE_CNI_LIST = {
  PEK: "2",
  HAK: "1",
  XIY: "3",
  CAN: "21",
  DLC: "26",
  TYN: "4",
  SZX: "29",
  HGH: "62",
  SYX: "59",
  CSX: "63",
  URC: "20",
  CKG: "52",
};
var airportName = {
  PEK: "北京",
  HAK: "海口",
  XIY: "西安",
  CAN: "广州",
  DLC: "大连",
  TYN: "太原",
  SZX: "深圳",
  HGH: "杭州",
  SYX: "三亚",
  CSX: "长沙",
  URC: "乌鲁木齐",
  CKG: "重庆",
};
let org_arp_base = new Object(); //分公司基地关系
// 飞机航班信息
var flightInfoList;
// 飞机位置信息
var planeLocationList;
var rangeObj = new Object();
var base_code;
// 中转航班信息
var mctInfoList = {};
var companyNodeId = 9;

var now = new Date();

$(function () {
  deferGeneral.done(function () {
    initSelectCompany();
  });
  crate3DEarth();
});

function getRelOrgArpBase() {
  return new Promise((resolve, reject) => {
    $.ajax({
      type: "post",
      url: "/bi/query/getRelOrgArpBase",
      contentType: "application/json",
      dataType: "json",
      async: true,
      success: function (response) {
        console.log("getRelOrgArpBase", response.data);
        if (response.data != undefined) {
          org_arp_base = response.data;
        }
        $(".combobox_list").html("");
        $(".combobox .combobox_label").text("");
        let list = org_arp_base[comp_code];
        if (list) {
          list.forEach((v, i) => {
            if (i == 0) {
              var cityCode = getQueryString("city_code");
              if (cityCode) {
                // $(".combobox .combobox_label").text("北京");
                $(".combobox .combobox_label").text(v.cityName);
                base_code = cityCode;
              } else {
                $(".combobox .combobox_label").text(v.cityName);
                base_code = v.arpCode;
              }
            }
            $(".combobox_list").append(
              `<span class="item" data="${v.arpCode}">${v.cityName}</span>`
            );
          });
        }
        resolve(response.errorcode);
      },
      error: function (response) {},
    });
  });
}
function initSelectCompany() {
  const company = window.location.hash.substr(1);
  if (!company) {
    switchCompany(selected_company_code);
  } else {
    switchCompany(company);
  }
}

// 绘制仪表盘
function drawGauge(canvasId, pointerId, rate, iswarning) {
  let angle;
  let color;
  const canvas = document.getElementById(canvasId);
  const context = canvas.getContext("2d");
  context.clearRect(0, 0, canvas.width, canvas.height);
  const x = canvas.width / 2;
  const y = canvas.height / 2;

  // draw back
  let radius = 48;
  const startAngle = Math.PI - Math.PI / 5;
  const endAngle = startAngle + Math.PI + (Math.PI / 5) * 2;
  let counterClockwise = false;

  context.beginPath();
  context.arc(x, y, radius, startAngle, endAngle, counterClockwise);
  context.lineWidth = 12;
  context.strokeStyle = iswarning ? "#916900" : "#004a91";
  context.stroke();

  // draw overlay
  const startAngle2 = startAngle;
  const endAngle2 = startAngle + (endAngle - startAngle) * rate;
  counterClockwise = false;

  context.beginPath();
  context.arc(x, y, radius, startAngle2, endAngle2, counterClockwise);
  context.lineWidth = 12;

  if (rate <= 1) {
    // linear gradient
    if (rate < 0.5) {
      color = context.createLinearGradient(
        0,
        canvas.height,
        canvas.width * rate,
        0
      );
    } else if (rate < 0.8) {
      color = context.createLinearGradient(
        0,
        canvas.height,
        canvas.width * rate,
        0
      );
    } else {
      color = context.createLinearGradient(
        0,
        canvas.height,
        canvas.width * rate,
        canvas.width / 2
      );
    }
    color.addColorStop(0, iswarning ? "#F1F304" : "#5f5fff");
    color.addColorStop(0.5, iswarning ? "#F3D304" : "#4185e4");
    color.addColorStop(1, iswarning ? "#f39800" : "#00d3ff");
    context.strokeStyle = color;
    context.stroke();
    // pointer
    angle = startAngle + (endAngle - startAngle) * rate;
    $("#" + pointerId).css(
      "transform",
      "rotate(" + (angle / Math.PI) * 180 + "deg)"
    );
  } else {
    // pointer
    angle = startAngle;
    $("#" + pointerId).css(
      "transform",
      "rotate(" + (angle / Math.PI) * 180 + "deg)"
    );
  }
}

//3D地球
var chart_earch;

function crate3DEarth() {
  chart_earch = echarts.init(document.getElementById("earth3d"));
  var option = {
    //tooltip: {},
    backgroundColor: "rgba(0,0,0,0)",
    globe: {
      baseTexture: "asset/earth.jpg",
      globeRadius: 80,
      globeOuterRadius: 20,
      displacementScale: 1,
      shading: "lambert",
      shading: "realistic",
      light: {
        main: {
          intensity: 0.3,
        },
        ambient: {
          intensity: 1.0,
        },
      },
      viewControl: {
        autoRotate: false,
        zoomSensitivity: false,
        targetCoord: [106, 32],
      },
      layers: [],
    },
    series: [],
  };
  chart_earch.setOption(option);
}

function createBMap() {
  var series = [];

  var option = {
    color: [],
    tooltip: {
      trigger: "item",
      show: true,
      formatter: function (params, ticket, callback) {
        var data = params.data;

        var html = "";

        if (!data.isCity) {
          var flt = data.flt;
          var depAirport = airportList[flt.depStn]; //出发机场信息
          var arrAirport = airportList[flt.arrStn]; //到达机场信息

          //航班号、起飞城市-到达城市、航班状态（实际/预计/计划起飞时间-实际/预计/计划到达时间）、机号（机型）、客座率（布局）、实时位置、实施油量
          html += "航班号: " + data.name + "<br>";
          html += depAirport.city_name + " - " + arrAirport.city_name + "<br>";
          if (
            flt.status == "DEP" ||
            flt.status == "ARR" ||
            flt.status == "NDR" ||
            flt.status == "ATA"
          ) {
            // 起飞,落地,到达
            // 实际起飞时间 atdChn
            html += "实际起飞时间: " + trimTime(flt.atdChn) + "<br>";
          } else {
            // 预计出发
            html += "预计出发时间: " + trimTime(flt.etdChn) + "<br>";
          }

          if (flt.status == "ATA") {
            // 到达
            // 实际起飞时间 atdChn
            html += "实际到达时间: " + trimTime(flt.ataChn) + "<br>";
          } else {
            // 预计到达
            html += "预计到达时间: " + trimTime(flt.etaChn) + "<br>";
          }

          if (flt.delay1 != "" && flt.dur1 > 0) {
            html += "延误原因: " + flt.delay1Name + "<br>";
            html +=
              "延误时间: " +
              (Number(flt.dur1) +
                Number(flt.dur2) +
                Number(flt.dur3) +
                Number(flt.dur4)) +
              "分钟<br>";
          }

          html += "机号: " + data.acno + "(" + flt.acType + ")" + "<br>";
          html +=
            "实时位置: " +
            Math.round(data.value[0] * 10000) / 10000 +
            ", " +
            Math.round(data.value[1] * 10000) / 10000 +
            "<br>";
          html += "实时油量: " + Math.round(data.oil) + "cc";
        }

        return html;
      },
      backgroundColor: "#021e55",
    },
    geo: {
      map: "world",
      roam: true,
      zoom: 2.22,
      center: [56, 25],
      silent: true,
      label: {
        emphasis: {
          show: false,
        },
      },
      itemStyle: {
        normal: {
          areaColor: "#3d7dd0",
          borderColor: "#14224c",
        },
        emphasis: {
          areaColor: "#3d7dd0",
          borderColor: "#14224c",
        },
      },
      regions: [
        {
          name: "ChinaLine",
          itemStyle: {
            normal: {
              borderWidth: 2,
              areaColor: "#3d7dd0",
              borderColor: "#3d7dd0",
            },
          },
        },
      ],
      //regions:countries
    },
    backgroundColor: "#14224c",
    series: series,
  };

  var myChart = echarts.init(document.getElementById("map"));
  myChart.setOption(option);
  window.onresize = myChart.resize;
  rangeObj.myChart = myChart;

  // 点击事件
  myChart.on("click", function (params) {
    params.event.event.stopPropagation();
    params.event.event.preventDefault();

    if (params.data.isAirline == undefined) {
      showFlightDetails(params.data.flt.flightNo);
    }
  });
}

function getPlaneLocationMq() {
  var param = {
    mode: "pos",
  };
  $.ajax({
    type: "post",
    url: "/bi/web/flightMq",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      planeLocationList = [];
      var plist = {};

      function processData(data1) {
        var lst = {};
        var len = data1.length;
        for (var i = 0; i < len; i++) {
          var dd = data1[i];
          var fi = dd.fi;
          if (lst[fi] == undefined) {
            lst[fi] = {
              data: [],
            };
            lst[fi]["data"].push(dd);
          } else {
            lst[fi]["data"].push(dd);
          }
        }

        return lst;
      }

      var list = processData(response.data.data1);

      for (var fltno in list) {
        var fltobj = list[fltno];
        var itmx2 = fltobj.data;

        var itm;

        if (itmx2 && itmx2.length > 1) {
          var itm1 = itmx2[0];
          var itm2 = itmx2[1];

          itm1.UTC = itm1.UTC.replace(" ", "");
          itm2.UTC = itm2.UTC.replace(" ", "");

          if (itm1.UTC > itm2.UTC) {
            itm = itm1;
            itm.LON1 = itm2.LON;
            itm.LAT1 = itm2.LAT;
          } else if (itm1.UTC < itm2.UTC) {
            itm = itm2;
            itm.LON1 = itm1.LON;
            itm.LAT1 = itm1.LAT;
          } else {
            itm = itm2;
            itm.LON1 = itm1.LON;
            itm.LAT1 = itm1.LAT;

            console.log(fltno, "两组经纬度UTC相同");
          }
        } else if (itmx2 && itmx2.length > 0) {
          itm = itmx2[0];
        }

        if (itm) {
          var alt = itm.ALT;
          var cas = itm.CAS;
          var vec;

          var fltno = itm.fi;
          var flt = flightInfoList[fltno];

          if (fltno.indexOf(comp_code) == 0 && flt && flt.status == "DEP") {
            var acno = itm.an;
            acno = acno.replace("-", "");

            var lon = formatLonLat(itm.LON);
            var lon1 = formatLonLat(itm.LON1);
            var lat = formatLonLat(itm.LAT);
            var lat1 = formatLonLat(itm.LAT1);

            if (isNaN(itm.LON)) {
              vec = Number(itm.VEC);
            }

            var oil = isNaN(itm.OIL) ? "" : itm.OIL;

            var pdat = {
              fltno: fltno,
              acno: acno,
              alt: alt,
              vec: vec,
              lon: lon,
              lat: lat,
              lon1: lon1,
              lat1: lat1,
              oil: oil,
            };

            var code = acno + "-" + fltno;

            if (pdat.vec == undefined) {
              pdat.vec = getGeoAngle(pdat.lat, pdat.lon, pdat.lat1, pdat.lon1);
            }
            planeLocationList.push(pdat);
          }
        }
      }

      console.log("planeLocationList", planeLocationList);
      setPlaneLocation();
    },
    error: function (jqXHR, txtStatus, errorThrown) {},
  });
}
function trimTime(timestr) {
  var arr = timestr.split(" ");
  var arr2 = arr[1].split(":");
  return arr2[0] + ":" + arr2[1];
}
// 飞机位置
function setPlaneLocation() {
  if (
    rangeObj.myChart != undefined &&
    planeLocationList != undefined &&
    flightInfoList != undefined
  ) {
    var planes = planeLocationList;
    var flightList = flightInfoList;

    var series = [];
    var list_1 = []; // 正常航班
    var list_2 = []; // 延误航班
    var len = planes.length;

    var statusMap = {
      ARR: "落地",
      NDR: "落地",
      ATD: "推出",
      ATA: "到达",
      CNL: "取消",
      DEL: "延误",
      DEP: "起飞",
      RTR: "返航",
      SCH: "计划",
    };

    for (var i = 0; i < len; i++) {
      var dat = planes[i];

      var flt = flightList[dat.fltno];

      if (flt) {
        if (flt.delay1 == "") {
          // 正常在飞
          list_1.push({
            name: dat.fltno,
            acno: dat.acno,
            oil: dat.oil,
            vec: dat.vec,
            flt: flt,
            value: [dat.lon, dat.lat],
            symbolRotate: -dat.vec + 90, //航向   正北是0°，顺时针，0到360° // 因为图片机头指向右，所以需要逆时针转90度
          });
        } else if (flt.delay1 != "") {
          // 延误航班
          list_2.push({
            name: dat.fltno,
            acno: dat.acno,
            oil: dat.oil,
            vec: dat.vec,
            flt: flt,
            value: [dat.lon, dat.lat],
            symbolRotate: -dat.vec + 90, //航向   正北是0°，顺时针，0到360° // 因为图片机头指向右，所以需要逆时针转90度
          });
        }
      }
    }
    series.push({
      name: "normal",
      type: "scatter",
      coordinateSystem: "geo",
      symbol: "image://img/flight.legend_1.png",
      symbolSize: 15,
      label: {
        normal: {
          show: false,
        },
      },
      data: list_1,
    });

    series.push({
      name: "delay",
      type: "scatter",
      coordinateSystem: "geo",
      symbol: "image://img/flight.legend_2.png",
      symbolSize: 15,
      label: {
        normal: {
          show: false,
        },
      },
      data: list_2,
    });

    var options = {
      series: series,
    };

    rangeObj.myChart.setOption(options);
  } else {
    setTimeout(setPlaneLocation, 0);
  }
}

// 显示航班信息页面 TODO
function showFlightDetails(flightNo) {
  var url = "flight.html?fltno=" + flightNo;
  var url_scale = getQueryString("scale");
  if (url_scale) {
    url = url + "&scale=" + url_scale;
  }
  windowOpen(url, "_blank");
}

function windowOpen(url, target) {
  var a = document.createElement("a");
  a.setAttribute("href", url);
  if (target == null) {
    target = "";
  }
  a.setAttribute("target", target);
  document.body.appendChild(a);
  if (a.click) {
    a.click();
  } else {
    try {
      var evt = document.createEvent("Event");
      a.initEvent("click", true, true);
      a.dispatchEvent(evt);
    } catch (e) {
      window.open(url);
    }
  }
  document.body.removeChild(a);
}

/* -----------------------------正常率、进港正常率、出港正常率----------------------------- */
// ------------------------------------------------------------------------
// 各种航班统计信息。。。。
// ------------------------------------------------------------------------

var date = new Date();
var mm = date.getMonth() + 1;
var dd = date.getDate();
if (mm < 10) {
  mm = "0" + mm;
}
if (dd < 10) {
  dd = "0" + dd;
}
var today = date.getFullYear() + "-" + mm + "-" + dd;
var stdEndUtcTime = date.getFullYear() + "-" + mm + "-" + dd + " 15:59:59";
var yesterday_ts = date.getTime() - 86400000;
date.setTime(yesterday_ts);
mm = date.getMonth() + 1;
dd = date.getDate();
if (mm < 10) {
  mm = "0" + mm;
}
if (dd < 10) {
  dd = "0" + dd;
}
var stdStartUtcTime = date.getFullYear() + "-" + mm + "-" + dd + " 16:00:00";
var nowTime = now.getFullYear() + "" + mm + "" + now.getDate(); //当天的时间
var fltStaticDep = undefined; // 离港
var fltStaticArr = undefined; // 到港
function allNormalRate() {
  var param = {
    stdStartUtcTime: stdStartUtcTime,
    stdEndUtcTime: stdEndUtcTime,
    companyCodes: comp_code,
    AcTypeList: "",
    depstns: "",
    arrstns: base_code,
  };

  $.ajax({
    type: "post",
    url: "/bi/redis/7x2_flt_sts",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      fltStaticArr = response;
      flightAmountStatic();
      if (response.pfdappPercent) {
        var baseRate = Number(response.pfdappPercent);
        //进港正常率仪表盘
        drawGauge(
          "cvs_shift2",
          "normalRateNumPoin2",
          baseRate.toFixed(2) / 100
        );
        $("#cvs_shiftJT2").html(baseRate.toFixed(2) + "%");
      }
    },
    error: function () {},
  });

  var param = {
    stdStartUtcTime: stdStartUtcTime,
    stdEndUtcTime: stdEndUtcTime,
    companyCodes: comp_code,
    AcTypeList: "",
    depstns: base_code,
    arrstns: "",
  };

  $.ajax({
    type: "post",
    url: "/bi/redis/7x2_flt_sts",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      fltStaticDep = response;
      flightAmountStatic();
      if (response.pfdappPercent) {
        var baseRate = Number(response.pfdappPercent);
        //出港正常率仪表盘
        drawGauge(
          "cvs_shift3",
          "normalRateNumPoin3",
          baseRate.toFixed(2) / 100
        );
        $("#cvs_shiftJT3").html(baseRate.toFixed(2) + "%");
      }
    },
    error: function () {},
  });
}

/* -----------------------------正常率、进港正常率、出港正常率----------------------------- */

function getOriDepNo() {
  var arp_kpi_name_list = ["ORI_DEP_NO_SCH", "ORI_DEP_NO_EXE"];
  var date = new Date();
  var mm = date.getMonth() + 1;
  var dd = date.getDate();
  var yy = date.getFullYear();
  if (mm < 10) {
    mm = "0" + mm;
  }
  if (dd < 10) {
    dd = "0" + dd;
  }
  var today = yy + "" + mm + dd;

  var param = {
    SOLR_CODE: "FAC_COMP_ARP_FLIGHT_KPI",
    COMP_CODE: comp_code,
    AIRPORT_CODE: base_code,
    KPI_CODE: arp_kpi_name_list.join(","),
    VALUE_TYPE: "kpi_value_d",
    DATE_TYPE: "D",
    LIMIT: 0,
    DATE_ID: today,
    OPTIMIZE: 1,
  };
  $.ajax({
    type: "post",
    url: "/bi/query/getackpi",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      if (response.data && response.data.length) {
        var data = response.data[comp_code];
        //始发航班量
        $("#baseSFHBL").text(Number(data["ORI_DEP_NO_SCH"]["D"][base_code]));
        $("#baseSFHBLYZX").text(Number(data["ORI_DEP_NO_EXE"]["D"][base_code]));
      }
    },
  });
}

/* -----------------------------当日航班量----------------------------- */
function flightAmountStatic() {
  if (!fltStaticDep || !fltStaticArr) {
    return;
  }

  var stsDep = fltStaticDep;
  var stsArr = fltStaticArr;

  var sch_total = Number(stsDep.pftc) + Number(stsArr.pftc); //计划航班总数
  var sch_normal = Number(stsDep.pfrtc) + Number(stsArr.pfrtc); //计划航班中正常航班总数

  var dftc = Number(stsDep.dftc) + Number(stsArr.dftc); //备降航班总数
  var bftc = Number(stsDep.bftc) + Number(stsArr.bftc); //返航航班总数
  var qftc = Number(stsDep.qftc) + Number(stsArr.qftc); //取消航班总数

  $("#flt_return_back").text(dftc + bftc);
  $("#flt_cancel").text(qftc);

  var pfdtc = Number(stsDep.pfdtc) + Number(stsArr.pfdtc); //计划航班中延误航班总数

  var pfdtc1 = Number(stsDep.pfdtc1) + Number(stsArr.pfdtc1); //计划航班中延误1小时内的航班总数
  $("#flt_delay_1").text(pfdtc1);

  var pfdtc12 = Number(stsDep.pfdtc12) + Number(stsArr.pfdtc12); //计划航班中延误1~2小时的航班总数
  $("#flt_delay_12").text(pfdtc12);

  var pfdtc24 = Number(stsDep.pfdtc24) + Number(stsArr.pfdtc24); //计划航班中延误2-4小时的航班总数
  $("#flt_delay_24").text(pfdtc24);

  var pfdtc4 = Number(stsDep.pfdtc4) + Number(stsArr.pfdtc4); //计划航班中延误>4小时的航班总数
  $("#flt_delay_4").text(pfdtc4);

  // 整体正常率
  var n_rate_all =
    Number(stsArr.pftc) + Number(stsDep.pftc) > 0
      ? (Number(stsArr.pfrtc) + Number(stsDep.pfrtc)) /
        (Number(stsArr.pftc) + Number(stsDep.pftc))
      : 0;
  drawGauge("cvs_shift", "normalRateNumPoin", n_rate_all);
  $("#cvs_shiftJT").html(Math.round(n_rate_all * 100) + "%");

  // 当日航班量

  // 进港
  var pftc = Number(stsArr.pftc); //计划航班总数
  var cftc = Number(stsArr.cftc); //已执行航班总数
  $("#flt_sch_arr").text(pftc);
  $("#flt_exc_arr").text(cftc);
  var w = $("#flt_bar_arrstns").width() * (cftc / pftc);
  $("#flt_bar_arrstns .insidebarBa").css("width", w + "px");
  $("#flt_bar_arrstns .dotBa").css("left", w + "px");

  // 出港
  var pftc = Number(stsDep.pftc); //计划航班总数
  var cftc = Number(stsDep.cftc); //已执行航班总数
  $("#flt_sch_dep").text(pftc);
  $("#flt_exc_dep").text(cftc);
  var w = $("#flt_bar_depstns").width() * (cftc / pftc);
  $("#flt_bar_depstns .insidebarBa").css("width", w + "px");
  $("#flt_bar_depstns .dotBa").css("left", w + "px");

  // 国内航班
  $("#flt_sch_dep_l").text(stsDep.pftcl); //国内计划航班总数
  $("#flt_exc_dep_l").text(stsDep.cftcl); //国内已执行航班总数
  // 国际航班
  $("#flt_sch_dep_i").text(stsDep.pftci); //国际计划航班总数
  $("#flt_exc_dep_i").text(stsDep.cftci); //国际已执行航班总数
}
/* -----------------------------当日航班量----------------------------- */

/* -----------------------------当日计划旅客量----------------------------- */
var acNo2ckiNumIn = {};
var acNo2ckiNumOut = {};

var psrFltList = [];

// 进港
function getInPsrStat() {
  var param = {
    arrIataId: base_code,
  };
  $.ajax({
    type: "post",
    url: "/bi/web/findPsrStat",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var bookNum = 0;
      var ckiNum = 0;

      acNoInList = [];

      if (response && response.data) {
        var fltlst = response.data;
        psrFltList = psrFltList.concat(fltlst);

        var len = fltlst.length;
        for (var i = 0; i < len; i++) {
          var flt = fltlst[i];
          if (flt.companyCode == comp_code) {
            var fltinfo = flightInfoList[flt.flightNo];

            if (!isNaN(flt.bookNum)) {
              bookNum += Number(flt.bookNum); // 订票人数
            }
            if (!isNaN(flt.ckiNum)) {
              ckiNum += Number(flt.ckiNum); // 值机人数
              if (fltinfo) {
                acNo2ckiNumIn[fltinfo.acLongNo] = Number(flt.ckiNum);
              }
            }

            if (fltinfo) {
              acNoInList.push(fltinfo.acLongNo);
            }
          }
        }

        $("#bookNum_in").text(bookNum);
        $("#ckiNum_in").text(ckiNum);

        setTrvRate("cvs_trv1", "#00a9ff", ckiNum / bookNum, 40, 8, 8, 36);
      }
    },
    error: function () {},
  });
}

// 出港
function getOutPsrStat() {
  var param = {
    depIataId: base_code,
  };
  $.ajax({
    type: "post",
    url: "/bi/web/findPsrStat",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var bookNum = 0;
      var ckiNum = 0;

      acNoOutList = [];

      if (response && response.data) {
        var fltlst = response.data;
        psrFltList = psrFltList.concat(fltlst);

        var len = fltlst.length;
        for (var i = 0; i < len; i++) {
          var flt = fltlst[i];
          if (flt.companyCode == comp_code) {
            var fltinfo = flightInfoList[flt.flightNo];

            if (!isNaN(flt.bookNum)) {
              bookNum += Number(flt.bookNum); // 订票人数
            }
            if (!isNaN(flt.ckiNum)) {
              ckiNum += Number(flt.ckiNum); // 值机人数
              if (fltinfo) {
                acNo2ckiNumOut[fltinfo.acLongNo] = Number(flt.ckiNum);
              }
            }

            if (fltinfo) {
              acNoOutList.push(fltinfo.acLongNo);
            }
          }
        }

        $("#bookNum_out").text(bookNum);
        $("#ckiNum_out").text(ckiNum);

        setTrvRate("cvs_trv3", "#00a9ff", ckiNum / bookNum, 40, 8, 8, 36);
      }
    },
    error: function () {},
  });
}

/* -----------------------------当日计划旅客量----------------------------- */

/* -----------------------------基地过夜飞机----------------------------- */
var arpInterval;
function arpFlight() {
  var date = new Date();
  var mm = date.getMonth() + 1;
  var dd = date.getDate();
  var yy = date.getFullYear();
  if (mm < 10) {
    mm = "0" + mm;
  }
  if (dd < 10) {
    dd = "0" + dd;
  }
  var today = yy + "" + mm + dd;
  var param = {
    SOLR_CODE: "FAC_COMP_ARP_ACTYPE_FLIGHT_KPI",
    COMP_CODE: comp_code,
    ACTYPE: "ALL",
    AIRPORT_CODE: base_code,
    KPI_CODE: ["AC_NO", "AC_ARR_NO"].join(","),
    VALUE_TYPE: "kpi_value_d",
    DATE_TYPE: "D",
    DATE_ID: today,
    LIMIT: 0,
    OPTIMIZE: 1,
  };
  $.ajax({
    type: "post",
    url: "/bi/query/getackpi",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      var data = response.data[comp_code];
      // 计算总量
      var AC_ARR_NO_TOTAL = 0; // 所有机场过夜飞机架数
      var AC_NO_TOTAL = 0; // 所有机场预计过夜飞机架数
      var ac_no_lst = data["AC_NO"]["D"][base_code];
      var ac_arr_lst = data["AC_ARR_NO"]["D"][base_code];
      var ac_no = {};
      var ac_arr_no = {};
      var actypeId2Code;
      var param = {};
      $.ajax({
        type: "post",
        url: "/bi/web/actypeall",
        contentType: "application/json",
        dataType: "json",
        async: true,
        data: JSON.stringify(param),
        success: function (response) {
          if (response.actype) {
            var list = response.actype;
            actypeId2Code = {};
            list.sort(function (a, b) {
              return a.sort - b.sort;
            });
            var len = list.length;
            for (var i = 0; i < len; i++) {
              var obj = list[i];
              actypeId2Code[obj.id] = obj.code;
            }
            for (var ac in ac_no_lst) {
              var accode = actypeId2Code[ac];
              var dlst = ac_no_lst[ac];
              var acnum = Number(dlst);
              if (acnum >= 0) {
                AC_NO_TOTAL += acnum;
                ac_no[accode] = acnum;
              }
            }

            for (var ac in ac_arr_lst) {
              var accode = actypeId2Code[ac];
              var dlst = ac_arr_lst[ac];
              var acnum = Number(dlst);
              if (acnum >= 0) {
                AC_ARR_NO_TOTAL += acnum;
                ac_arr_no[accode] = acnum;
              }
            }
            $("#base_over_night_plane_num").text(AC_ARR_NO_TOTAL);
            $("#base_over_night_plane_num2").text(AC_NO_TOTAL);
            clearCanvas("arp_flight_rate");
            setTrvRate(
              "arp_flight_rate",
              "#00a9ff",
              AC_ARR_NO_TOTAL / AC_NO_TOTAL,
              60,
              11,
              19,
              0
            );
            let ac_no_arr = Object.entries(ac_no);
            let ac_no_arr_page = spArray(4, ac_no_arr);
            var currentArpPlanePage = 0;
            let scorll = () => {
              $(".arp_flight_plan1").hide();
              $(".arp_flight_plan2").hide();
              $(".arp_flight_plan3").hide();
              $(".arp_flight_plan4").hide();
              ac_no_arr_page[currentArpPlanePage].forEach((v, i) => {
                let no = i + 1;
                $(".arp_flight_plan" + no).show();
                $("#arp_flight_plan" + no + "_lebel").text(v[0]);
                let acnum1 = v[1];
                let acnum2 = ac_arr_no[v[0]];
                $("#arp_flight_plan" + no + "_content").text(
                  acnum2 + "/" + acnum1
                );
                scheduleX("arp_flight_plan" + no, {
                  fulfill: acnum2, //选择数
                  listAll: acnum1, //总数
                  speed: 25, //动画速度，可选，默认25,越小越快
                  again: false, //选择数改变动画是否从零开始，默认true从零开始
                  bgColor: "#024394", //底部颜色
                  listColor: "#00a9ff", //伸缩条颜色，默认原谅绿
                  scWidth: "160", //进度条宽度
                  scHeight: "5", //进度条高度
                  xNumDisplay: false,
                });
              });
              if (currentArpPlanePage < ac_no_arr_page.length - 1) {
                currentArpPlanePage++;
              } else {
                currentArpPlanePage = 0;
              }
            };
            if (arpInterval) clearInterval(arpInterval);
            arpInterval = setInterval(scorll, 5000);
          }
        },
        error: function () {},
      });
    },
  });
}

function spArray(N, Q) {
  let R = [],
    F;
  for (F = 0; F < Q.length; ) {
    R.push(Q.slice(F, (F += N)));
  }
  return R;
}

/* -----------------------------基地过夜飞机----------------------------- */

/* -----------------------------今日延误原因分析----------------------------- */
// ------------------------------------------------------------------------
// 延误原因 公司／非公司
// ------------------------------------------------------------------------
function buttReso(comIf) {
  var date_type = "D";
  var param = {
    SOLR_CODE: "FAC_COMP_ARP_DELAY_CAUSE_KPI",
    COMP_CODE: comp_code,
    AIRPORT_CODE: base_code,
    KPI_CODE: "DELAY_NO",
    VALUE_TYPE: "kpi_value_d",
    DATE_TYPE: date_type,
    LIMIT: "1",
    OPTIMIZE: 1,
  };

  $.ajax({
    type: "post",
    url: "/bi/query/getkpi",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      if (response.data != undefined) {
        var data_dly =
          response.data[comp_code]["DELAY_NO"][date_type][base_code];

        /*公司原因  军事活动 地面保障 天气原因 旅客 未定义 机场 机组保障 正常航班
                民航局航班时刻安排 油料 空管 航班计划 运力调配 飞机故障*/
        var comp_cause_list = [];
        var none_cause_list = [];

        var comp_total = 0;
        var none_total = 0;

        // 公司原因 总数
        for (var time in data_dly) {
          var dlst = data_dly[time];
          for (var k = dlst.length - 1; k >= 0; k--) {
            var d = dlst[k];
            var len = comp_cause.length;
            for (var i = 0; i < len; i++) {
              var causeName = comp_cause[i];
              if (d.delay_cause_s == causeName) {
                var val = Number(d.value);

                if (val > 0) {
                  comp_total += val;
                  comp_cause_list.push({
                    name: causeName,
                    val: val,
                  });
                }
              }
            }
          }

          break;
        }

        // 非公司原因 总数
        for (var time in data_dly) {
          var dlst = data_dly[time];
          for (var k = dlst.length - 1; k >= 0; k--) {
            var d = dlst[k];
            var len = none_cause.length;
            for (var i = 0; i < len; i++) {
              var causeName = none_cause[i];
              if (d.delay_cause_s == causeName) {
                var val = Number(d.value);
                if (causeName == "民航局航班时刻安排") {
                  causeName = "时刻安排";
                }

                if (val > 0) {
                  none_total += val;
                  none_cause_list.push({
                    name: causeName,
                    val: val,
                  });
                }
              }
            }
          }
          break;
        }
        // 公司
        if (comIf == "comResson") {
          var html = "";
          var j;
          var len = comp_cause_list.length;
          for (var i = 0; i < len; i++) {
            var d = comp_cause_list[i];
            var per = Number(d.val) / (comp_total + none_total);
            var perstr = Math.round(per * 100);
            var barlen = 100 * per;
            if (perstr > 0) {
              let namelen = 4 - d.name.length;
              let space = "&nbsp";
              for (let i = 0; i < namelen; i++) {
                space += "&nbsp;&nbsp;";
              }
              html +=
                '<div class="baritmrow"><span class="blue2">' +
                d.name +
                space +
                '</span> <span class="bar greenbar" style="width: ' +
                barlen +
                'px; "></span> <span class="ffnum">' +
                perstr +
                "%</span> </div>";
            }
          }
          if (len == 0) {
            html +=
              '<div style="width: 100%;height: 45px;line-height: 45px;text-align: center;vertical-align: middle;font-size: 12px;">无延误</div>';
          }
          j = j + 8;
          $("#buttResoSele").html(html);
        }
        if (comIf == "comRessonNon") {
          // 非公司
          html = "";
          var j;
          var len = none_cause_list.length;
          for (var i = 0; i < len; i++) {
            var d = none_cause_list[i];
            var per = Number(d.val) / (comp_total + none_total);
            var perstr = Math.round(per * 100);
            var barlen = 100 * per;
            if (perstr > 0) {
              let namelen = 4 - d.name.length;
              let space = "&nbsp";
              for (let i = 0; i < namelen; i++) {
                space += "&nbsp;&nbsp;&nbsp;";
              }
              html +=
                '<div class="baritmrow"><span class="blue2">' +
                d.name +
                space +
                '</span> <span class="bar greenbar" style="width: ' +
                barlen +
                'px; "></span> <span class="ffnum">' +
                perstr +
                "%</span> </div>";
            }
          }
          if (per) {
            var w = $(".buttResoFltBar").width() * per;
            $(".buttResoFltBar .ReinsidebarBa").css("width", w + "px");
            $(".buttResoFltBar .RedotBa").css("left", w + "px");
          }

          if (len == 0) {
            html +=
              '<div style="width: 100%;height: 45px;line-height: 45px;text-align: center;vertical-align: middle;font-size: 12px;">无延误</div>';
          }
          j = j + 8;
          $("#buttResoSele").html(html);
        }

        // percent
        var per1 = Math.round((comp_total / (comp_total + none_total)) * 100);
        var per2 = Math.round((none_total / (comp_total + none_total)) * 100);
        per1 = isNaN(per1) ? 0 : per1;
        per2 = isNaN(per2) ? 0 : per2;
        $("#per_delay_cause_comp").text(per1);
        $("#per_delay_cause_none").text(per2);
        setTrvRate("delay_case_rate", "#00B384", per1 / 100, 40, 8, 8, 12);
      }
    },
    error: function () {},
  });
}

/* -----------------------------今日延误原因分析----------------------------- */

/* -----------------------------正在执行航班（中间地球处）----------------------------- */
// 开始结束时间
var date = new Date();
var preDate = new Date(date.getTime() - 24 * 60 * 60 * 1000); //前一天
var mm = date.getMonth() + 1;
var dd = date.getDate();
var preDd = preDate.getDate();
if (mm < 10) {
  mm = "0" + mm;
}
if (dd < 10) {
  dd = "0" + dd;
}
var datopStart = date.getFullYear() + "-" + mm + "-" + dd;
var datopEnd = date.getFullYear() + "-" + mm + "-" + preDd;
//进港
var param = {
  acOwner: comp_code,
  depStn: "",
  arrStn: base_code,
  statusList: "DEP",
  datopStart: datopStart,
  datopEnd: datopEnd,
};

$.ajax({
  type: "post",
  url: "/bi/web/flightInProgress",
  contentType: "application/json",
  dataType: "json",
  async: true,
  data: JSON.stringify(param),
  success: function (response) {},
  error: function () {},
});

//出港
var param = {
  acOwner: comp_code,
  depStn: base_code,
  arrStn: "",
  statusList: "DEP",
  datopStart: datopStart,
  datopEnd: datopEnd,
};

//出港
$.ajax({
  type: "post",
  url: "/bi/web/flightInProgress",
  contentType: "application/json",
  dataType: "json",
  async: true,
  data: JSON.stringify(param),
  success: function (response) {},
  error: function () {},
});

/* -----------------------------正在执行航班（中间地球处）----------------------------- */

/* -----------------------------本基地天气情况（中间地球处）----------------------------- */
// ------------------------------------------------------------------------
// 天气情况
// ------------------------------------------------------------------------
var weatherCache = {};

function getWeather() {
  var param = {
    airport: base_code,
  };

  $.ajax({
    type: "post",
    url: "/bi/web/7x2_arp_weather",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      if (Number(response.errorcode) == 0 && response.airport) {
        var info = {};
        info.code = response.airport;
        info.visibility =
          isNaN(response.visibility) || response.visibility == 0
            ? 9999
            : Number(response.visibility); //能见度
        info.rvr =
          isNaN(response.rvr) || response.rvr == 0
            ? 9999
            : Number(response.rvr); //跑道目视距离
        info.cloudInfo =
          isNaN(response.cloudInfo) || response.cloudInfo == 0
            ? 9999
            : Number(response.cloudInfo); //云况
        info.windFs = isNaN(response.windFs) ? 0 : Number(response.windFs); //风速
        info.windFx = isNaN(response.windFx) ? 0 : Number(response.windFx); //风向
        info.temperature = isNaN(response.temperature)
          ? 0
          : Number(response.temperature);
        info.weatherInfo = response.weatherInfo; //天气现象
        info.weatherInfoTxt = response.weatherInfoTxt
          ? response.weatherInfoTxt.replace(/<[^>]+>/g, "")
          : "";
        info.weather_css = "icon-e600_sunny";
        for (var wtxt in weather_map) {
          if (info.weatherInfoTxt.indexOf(wtxt) > -1) {
            info.weather_css = weather_map[wtxt];
          }
        }

        for (var wtxt in airportName) {
          if (info.code.indexOf(wtxt) > -1) {
            info.code = airportName[wtxt];
          }
        }
        // 设置天气状况icon
        $("#airportName").text(info.code);
        $("#base_weather_ico").attr("class", info.weather_css);
        $("#weatherInfoTxt").text(info.weatherInfoTxt);
        $(".weatherInfo").text(info.weatherInfoTxt);
        $("#windFs").text(info.windFs + "m/s"); //风速
        $("#windFx").text(info.windFx + "°"); //风向
        $("#visibility").text(info.visibility + "m"); //能见度
        $("#cloudInfo").text(info.cloudInfo + "m"); //能见度
        $("#temperature").text(info.temperature + "°");
      } else {
        $("#airportName").text("");
        $("#base_weather_ico").attr("class", "");
        $("#weatherInfoTxt").text("");
        $(".weatherInfo").text("");
        $("#windFs").text("" + "m/s"); //风速
        $("#windFx").text("" + "°"); //风向
        $("#visibility").text("" + "m"); //能见度
        $("#cloudInfo").text("" + "m"); //能见度
        $("#temperature").text("" + "°");
        console.log("weather Error");
      }
    },
    error: function (jqXHR, txtStatus, errorThrown) {},
  });
}

/* -----------------------------本基地天气情况（中间地球处）----------------------------- */

/* -----------------------------进港航班----------------------------- */
// ------------------------------------------------------------------------
// 获取航班信息
// ------------------------------------------------------------------------
var fltIntList = [];
var flt2hourInList = [];
var flt2hourOutList = [];
var acNoInList;
var acNoOutList;

// 开始结束时间
var date = new Date();
var mm = date.getMonth() + 1;
var dd = date.getDate();
if (mm < 10) {
  mm = "0" + mm;
}
if (dd < 10) {
  dd = "0" + dd;
}
var stdStart = date.getFullYear() + "-" + mm + "-" + dd + " 00:00:00";
var stdEnd = date.getFullYear() + "-" + mm + "-" + dd + " 23:59:59";

function set2hourInOrOutFltStatus(a) {
  var list = [];
  var listLen = "";
  var param = {
    stdStart: stdStart,
    stdEnd: stdEnd,
    acOwner: comp_code,
    statusList: "",
  };
  $.ajax({
    type: "post",
    url: "/bi/web/getStandardFocFlightInfo",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      list = response.data;
      flightInfoList = {};
      fltIntList = [];
      flt2hourInList = [];
      flt2hourOutList = [];
      let arrStnCount = 0;
      let depStnCount = 0;
      if (list) {
        listLen = list.length;
        for (var i = listLen - 1; i >= 0; i--) {
          var obj = list[i];
          if (obj.arrStn == base_code && obj.status == "DEP") {
            arrStnCount++;
          }
          if (obj.depStn == base_code && obj.status == "DEP") {
            depStnCount++;
          }
          // 基地 进港。出港 航班
          if (obj.arrStn == base_code || obj.depStn == base_code) {
            flightInfoList[obj.flightNo] = obj;

            // 国际航班
            if (obj.fltType == "I") {
              fltIntList.push(obj);
            }

            // 近2小时进出港航班
            var etdChn = obj.etdChn; //预计起飞时间（北京时间）
            var atdChn = obj.atdChn; //实际起飞时间（北京时间）
            var etaChn = obj.etaChn; //预计到达时间（北京时间）
            var ataChn = obj.ataChn; //实际到达时间（北京时间）

            var d_time = parserDate(etdChn);
            var d2_time = parserDate(atdChn);
            var a_time = parserDate(etaChn);
            var a2_time = parserDate(ataChn);

            var now_time = new Date();
            // 2小时内到达的飞机
            var ost = a_time.getTime() - now_time.getTime();
            // var ost2 = a2_time.getTime() - now_time.getTime();
            if (obj.arrStn == base_code && ost >= 0 && ost <= 120 * 60 * 1000) {
              flt2hourInList.push(obj);
            }
            // 2小时内出发的飞机
            var ost = d_time.getTime() - now_time.getTime();
            // var ost2 = d2_time.getTime() - now_time.getTime();
            if (obj.depStn == base_code && ost >= 0 && ost <= 120 * 60 * 1000) {
              flt2hourOutList.push(obj);
            }
          }
        }
        if (a == "baseMainR2Butt1") {
          set2hourInFltStatus();
        } else if (a == "baseMainR2Butt2") {
          set2hourOutFltStatus();
        }
        setFlighting(arrStnCount, depStnCount);
        dayTranTen(); //当日中转紧张模块
        getInPsrStat();
        getOutPsrStat();
        setAirlines();
        getPlaneLocationMq();
        hideLoading();
      } else {
        hideLoading();
      }
    },
    error: function () {
      hideLoading();
    },
  });
}
function setFlighting(arrStnCount, depStnCount) {
  var arrStnCounta = parseInt(arrStnCount % 10); // 个位数
  var arrStnCountb = parseInt((arrStnCount % 100) / 10); // 十位数
  $("#zxjg1").text(arrStnCountb);
  $("#zxjg2").text(arrStnCounta);
  var depStnCounta = parseInt(depStnCount % 10); // 个位数
  var depStnCountb = parseInt((depStnCount % 100) / 10); // 十位数
  $("#zxcg1").text(depStnCountb);
  $("#zxcg2").text(depStnCounta);
}

function getShortTime(fulltime) {
  var str = fulltime.replace(/:/g, "");
  var arr = str.split(" ");
  return arr[1].substr(0, 4);
}

function set2hourInFltStatus() {
  if (airportList == undefined) {
    setTimeout(set2hourInFltStatus, 10);
    return;
  }

  flt2hourInList.sort(function (a, b) {
    return getShortTime(a.etaChn) - getShortTime(b.etaChn);
  });

  var htmlTi = "";
  var html = "";
  var cnt = 2;
  var len = flt2hourInList.length;
  len = Math.min(5, len);
  var fltNoList = [];
  for (var i = 0; i < len; i++) {
    var flt = flt2hourInList[i];
    fltNoList.push(flt.flightNo);
  }
  var fltLegs = getFltmLegsByPageV2(
    flt2hourInList[0].datop,
    fltNoList,
    flt2hourInList[0].arrStn,
    null
  );

  htmlTi += '<div class="baseMainR2MainAll1">机号</div>';
  htmlTi +=
    '<div class="baseMainR2MainAll2" style="padding-top: 6px;">航班</div>';
  htmlTi += '<div class="baseMainR2MainAll3">机型</div>';
  htmlTi += '<div class="baseMainR2MainAll4">计划</br>到达</div>';
  htmlTi += '<div class="baseMainR2MainAll5">预计</br>到达</div>';
  htmlTi += '<div class="baseMainR2MainAll6">实际</br>落地</div>';
  htmlTi += '<div class="baseMainR2MainAll7">实际</br>到位</div>';
  htmlTi +=
    '<div class="baseMainR2MainAll8" style="padding-top: 2%;">机位</div>';

  for (var i = 0; i < len; i++) {
    var flt = flt2hourInList[i];

    var fltno = flt.flightNo;
    /* var cls = cnt % 2 == 0 ? 'abg' : ''; */

    var actype = flt.acType;
    if (actype.indexOf("73") > -1) {
      actype = "737";
    } else if (actype.indexOf("76") > -1) {
      actype = "767";
    } else if (actype.indexOf("78") > -1) {
      actype = "787";
    } else if (actype.indexOf("33") > -1) {
      actype = "330";
    } else if (actype.indexOf("32") > -1) {
      actype = "320";
    }

    var staChn = getShortTime(flt.staChn); //计划到达时间（北京时间）
    var etaChn = getShortTime(flt.etaChn); //预计到达时间（北京时间）
    var tDwnChn = getShortTime(flt.tDwnChn); //实际落地时间（北京时间）
    var ataChn = getShortTime(flt.ataChn); //实际到达时间（北京时间）

    var resArr = fltLegs.filter(
      (r) =>
        r.cncDEPSTN == flt.depStn &&
        r.cncARRSTN == flt.arrStn &&
        r.cnvcFlightNo == flt.flightNo
    );
    var arrParkPlace = "-";
    if (resArr.length > 0) {
      arrParkPlace =
        resArr[0].cnvcInGATE == undefined ? "-" : resArr[0].cnvcInGATE; //到达停机位
    }

    if (flt.status == "NDR" || flt.status == "ATA") {
      // 已经到达
    } else {
      tDwnChn = "-";
      ataChn = "-";
    }

    var bgc1 = "";
    if ((flt.status == "NDR" || flt.status == "ATA") && Number(flt.dur1) == 0) {
      bgc1 = "green_tag";
    } else if (
      (flt.status == "NDR" || flt.status == "ATA") &&
      Number(flt.dur1) > 0
    ) {
      bgc1 = "yellow_tag";
    }
    var bgc2 = bgc1;

    html += '<div class="baseMainR2Main' + cnt + '">';
    html +=
      '<div class="baseMainR2MainAll1" style="color: #019beb;">' +
      flt.acLongNo +
      "</div>";
    html +=
      '<div class="baseMainR2MainAll2" style="color: white;font-size: 5px !important;">' +
      fltno +
      "</br>" +
      flt.depCity +
      "</div>";
    html +=
      '<div class="baseMainR2MainAll3" style="color: #019beb;">' +
      actype +
      "</div>";
    html +=
      '<div class="baseMainR2MainAll4" style="color: white;">' +
      staChn +
      "</div>";
    html +=
      '<div class="baseMainR2MainAll5" style="color: white;">' +
      etaChn +
      "</div>";
    html +=
      '<div class="baseMainR2MainAll6" style="color: white;">' +
      tDwnChn +
      "</div>";
    html +=
      '<div class="baseMainR2MainAll7" style="color: white;">' +
      ataChn +
      "</div>";
    html +=
      '<div class="baseMainR2MainAll8" style="color: #019beb;">' +
      arrParkPlace +
      "</div>";
    html += "</div>";
    cnt++;
  }

  $("#baseMainR2Main1").html(htmlTi);
  $("#baseMainR2MainAll").html(html);
}

/* -----------------------------进港航班----------------------------- */

/* -----------------------------出港航班----------------------------- */
// ------------------------------------------------------------------------
// 近2小时出港航班
// ------------------------------------------------------------------------
function set2hourOutFltStatus() {
  if (airportList == undefined) {
    setTimeout(set2hourOutFltStatus, 10);
    return;
  }

  flt2hourOutList.sort(function (a, b) {
    return getShortTime(a.etdChn) - getShortTime(b.etdChn);
  });

  var date = new Date();
  var hour = date.getHours();
  var min = date.getMinutes();
  if (hour < 10) {
    hour = "0" + hour;
  }
  if (min < 10) {
    min = "0" + min;
  }
  var time_now = hour + "" + min;

  var htmlTi = "";
  var html = "";
  var cnt = 2;

  var len = flt2hourOutList.length;
  len = Math.min(5, len);
  var fltNoList = [];
  for (var i = 0; i < len; i++) {
    var flt = flt2hourOutList[i];
    fltNoList.push(flt.flightNo);
  }
  var fltLegs = getFltmLegsByPageV2(
    flt2hourOutList[0].datop,
    fltNoList,
    null,
    flt2hourOutList[0].depStn
  );

  htmlTi += '<div class="baseMainR2MainAll1">机号</div>';
  htmlTi +=
    '<div class="baseMainR2MainAll2" style="padding-top: 6px;">航班</div>';
  htmlTi += '<div class="baseMainR2MainAll3">机型</div>';
  htmlTi += '<div class="baseMainR2MainAll4">计划</br>离港</div>';
  htmlTi += '<div class="baseMainR2MainAll5">预计</br>起飞</div>';
  htmlTi += '<div class="baseMainR2MainAll6">实际</br>离港</div>';
  htmlTi += '<div class="baseMainR2MainAll7">实际</br>起飞</div>';
  htmlTi +=
    '<div class="baseMainR2MainAll8" style="padding-top: 2%;">机位</div>';

  for (var i = 0; i < len; i++) {
    var flt = flt2hourOutList[i];

    var fltno = flt.flightNo;

    var actype = flt.acType;
    if (actype.indexOf("73") > -1) {
      actype = "737";
    } else if (actype.indexOf("76") > -1) {
      actype = "767";
    } else if (actype.indexOf("78") > -1) {
      actype = "787";
    } else if (actype.indexOf("33") > -1) {
      actype = "330";
    } else if (actype.indexOf("32") > -1) {
      actype = "320";
    }

    var atdChnHtml = "";
    var stdChn = getShortTime(flt.stdChn); //计划离港时间（北京时间）
    var etdChn = getShortTime(flt.etdChn); //预计起飞时间（北京时间）
    var atdChn = getShortTime(flt.atdChn); //实际离港时间（北京时间）
    var tOffChn = getShortTime(flt.tOffChn); //实际起飞时间（北京时间）
    //console.log(fltno, time_now, atdChn, flt);

    if (Number(time_now) < Number(atdChn)) {
      atdChnHtml = "-";
    } else {
      if (flt.status == "DEL" || Number(flt.dur1) > 0) {
        atdChnHtml = '<span class="yellow_tag">' + atdChn + "</span>";
      } else {
        atdChnHtml = '<span class="green_tag">' + atdChn + "</span>";
      }
    }

    var resArr = fltLegs.filter(
      (r) =>
        r.cncDEPSTN == flt.depStn &&
        r.cncARRSTN == flt.arrStn &&
        r.cnvcFlightNo == flt.flightNo
    );
    var depParkPlace = "-";
    if (resArr.length > 0) {
      depParkPlace =
        resArr[0].cnvcOutGate == undefined ? "-" : resArr[0].cnvcOutGate; //出发停机位
    }

    html += '<div class="baseMainR2Main' + cnt + '">';
    html +=
      '<div class="baseMainR2MainAll1" style="color: #019beb;">' +
      flt.acLongNo +
      "</div>";
    html +=
      '<div class="baseMainR2MainAll2" style="color: white;font-size: 5px !important;">' +
      fltno +
      "</br>" +
      flt.depCity +
      "</div>";
    html +=
      '<div class="baseMainR2MainAll3" style="color: #019beb;">' +
      actype +
      "</div>";
    html +=
      '<div class="baseMainR2MainAll4" style="color: white;">' +
      stdChn +
      "</div>";
    html +=
      '<div class="baseMainR2MainAll5" style="color: white;">' +
      etdChn +
      "</div>";
    html +=
      '<div class="baseMainR2MainAll6" style="color: white;">' +
      atdChn +
      "</div>";
    html +=
      '<div class="baseMainR2MainAll7" style="color: white;">' +
      tOffChn +
      "</div>";
    html +=
      '<div class="baseMainR2MainAll8" style="color: #019beb;">' +
      depParkPlace +
      "</div>";
    html += "</div>";
    cnt++;
  }
  $("#baseMainR2Main1").html(htmlTi);
  $("#baseMainR2MainAll").html(html);
}

// 获取航班进出港停机位
function getFltmLegsByPageV2(datop, fltNoList, arrIataId, depIataId) {
  var res;
  var param = {
    flightDateUTCFrom: datop,
    flightDateUTCTo: datop,
    fltNoList: fltNoList,
    arrIataId: arrIataId,
    depIataId: depIataId,
  };
  $.ajax({
    type: "post",
    url: "/bi/spring/flight/getFltmLegsByPageV2",
    contentType: "application/json",
    dataType: "json",
    async: false,
    data: JSON.stringify(param),
    success: function (response) {
      res = response.data;
    },
  });
  return res;
}

//进港航班和出港航班切换添加颜色使用
$(".baseMainR2Butt1").on("click", function (event) {
  event.preventDefault();
  $(".baseMainR2Butt1").addClass("seleCJG");
  $(".baseMainR2Butt2").removeClass("seleCJG");
});

//进港航班和出港航班切换添加颜色使用
$(".baseMainR2Butt2").on("click", function (event) {
  event.preventDefault();
  $(".baseMainR2Butt2").addClass("seleCJG");
  $(".baseMainR2Butt1").removeClass("seleCJG");
});

/* -----------------------------出港航班----------------------------- */

/* -----------------------------当日中转航班----------------------------- */
//当日中装紧张模块 -- 中转规则
var today = date.getFullYear() + "-" + mm + "-" + dd;
// 查询旅客 // 进港
var fltInOutCnt = 0;
var fltNoInOutList;
var fltNoOtherComp;
var fltInOutPsrNum; // 前一班航班号,后一班航班号:乘客人数
var acNo2ckiNumIn = {};
var psrFltList = [];

function dayTranTen() {
  if (mctInfoList[base_code] == undefined) {
    var param = {
      iataCode: base_code,
    };
    $.ajax({
      type: "post",
      url: "/bi/web/queryMctInfoAttrs",
      contentType: "application/json",
      dataType: "json",
      async: true,
      data: JSON.stringify(param),
      success: function (response) {
        if (response.result && response.result.valueList) {
          var lst = response.result.valueList;
          var mctlist = [];
          var len = lst.length;
          for (var i = 0; i < len; i++) {
            var dd = lst[i];
            mctlist.push(dd);
          }
          mctInfoList[base_code] = mctlist;
        } else {
          mctInfoList[base_code] = [];
        }

        setInOutPsr();
      },
      error: function () {},
    });
  } else {
    setInOutPsr();
  }

  var param = {
    fltDate: today,
    arrStnCode: base_code,
    inOrOutType: "out",
    fltAlcdtwList: comp_code,
  };

  $.ajax({
    type: "post",
    url: "/bi/web/findPassengerByFltV2",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      //获取转出旅客的进港航班号和出港航班号
      if (fltNoInOutList == undefined) {
        fltNoInOutList = [];
      }
      if (fltNoOtherComp == undefined) {
        fltNoOtherComp = [];
      }
      if (fltInOutPsrNum == undefined) {
        fltInOutPsrNum = {};
      }

      for (var i in response) {
        var obj = response[i];
        var fltNo = obj.fltNo;
        var psrOutFlt = obj.psrOutFlt;
        if (psrOutFlt) {
          var psrOutFltCompCode = psrOutFlt.substr(0, 2);
          var flt2 = fltNo + "," + psrOutFlt;
          if (
            fltNo &&
            fltNo != "" &&
            comp_code_list.indexOf(psrOutFltCompCode) != -1 &&
            fltNoInOutList.indexOf(flt2) == -1
          ) {
            fltNoInOutList.push(flt2);

            if (
              psrOutFltCompCode != comp_code &&
              fltNoOtherComp.indexOf(psrOutFlt) == -1
            ) {
              fltNoOtherComp.push(psrOutFlt);
            }
          }
          if (fltInOutPsrNum[flt2] == undefined) {
            fltInOutPsrNum[flt2] = 0;
          }
          fltInOutPsrNum[flt2]++;
        }
      }

      fltInOutCnt++;
      setInOutPsr();
    },
    error: function (jqXHR, txtStatus, errorThrown) {
      console.log("----error");
    },
  });

  // 查询旅客 // 出港
  var param = {
    fltDate: today,
    depStnCode: base_code,
    inOrOutType: "in",
    fltAlcdtwList: comp_code,
  };

  $.ajax({
    type: "post",
    url: "/bi/web/findPassengerByFltV2",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      //获取转出旅客的进港航班号和出港航班号
      if (fltNoInOutList == undefined) {
        fltNoInOutList = [];
      }
      if (fltNoOtherComp == undefined) {
        fltNoOtherComp = [];
      }
      if (fltInOutPsrNum == undefined) {
        fltInOutPsrNum = {};
      }

      for (var i in response) {
        var obj = response[i];
        var fltNo = obj.fltNo;
        var psrInflt = obj.psrInflt;
        if (psrInflt) {
          var psrInfltCompCode = psrInflt.substr(0, 2);
          var flt2 = psrInflt + "," + fltNo;
          if (
            fltNo &&
            fltNo != "" &&
            comp_code_list.indexOf(psrInfltCompCode) != -1 &&
            fltNoInOutList.indexOf(flt2) == -1
          ) {
            fltNoInOutList.push(flt2);

            if (
              psrInfltCompCode != comp_code &&
              fltNoOtherComp.indexOf(psrInflt) == -1
            ) {
              fltNoOtherComp.push(psrInflt);
            }
          }
          if (fltInOutPsrNum[flt2] == undefined) {
            fltInOutPsrNum[flt2] = 0;
          }
          fltInOutPsrNum[flt2]++;
        }
      }

      fltInOutCnt++;
      setInOutPsr();
    },
    error: function (jqXHR, txtStatus, errorThrown) {
      console.log("----error");
    },
  });
}

function setInOutPsr() {
  if (
    flightInfoList == undefined ||
    mctInfoList == undefined ||
    fltInOutCnt < 2
  ) {
    return;
  }

  var fltInfolst = {}; // {flightNo : fltInfo}

  if (fltNoOtherComp && fltNoOtherComp.length > 0) {
    // 获取航班信息
    var param = {
      stdStart: stdStart,
      stdEnd: stdEnd,
      acOwner: "",
      statusList: "",
      fltNoList: fltNoOtherComp.join(","),
    };
    $.ajax({
      type: "post",
      url: "/bi/web/getStandardFocFlightInfo",
      contentType: "application/json",
      dataType: "json",
      async: true,
      data: JSON.stringify(param),
      success: function (response) {
        var fltList = response.data;
        var len = fltList.length;
        for (var i = len - 1; i >= 0; i--) {
          var ff = fltList[i];
          fltInfolst[ff.flightNo] = ff;
        }

        createMctDataList();
      },
      error: function () {},
    });
  } else {
    createMctDataList();
  }

  function getMctGroundtime(noFrom, noTo, arpType) {
    var groundTime = 80;
    var groundTime2;
    var groundTime3;
    var mtclist = mctInfoList[base_code];
    if (mtclist.length > 0) {
      var len = mtclist.length;
      for (var i = mtclist.length - 1; i >= 0; i--) {
        var fltNoFrom = mtclist[i].fromWhere;
        var fltNoTo = mtclist[i].toWhere;
        var airportType = mtclist[i].airportType;
        groundTime = Number(mtclist[i].groundTime);
        if (fltNoFrom == "null" && fltNoTo == "null") {
          groundTime2 = groundTime;
          if (airportType == arpType) {
            groundTime3 = groundTime;
          }
        }

        if (fltNoFrom == noFrom && fltNoTo == noTo) {
          return groundTime;
        }
      }
    }

    if (groundTime3 > 0) {
      return groundTime3;
    }
    if (groundTime2 > 0) {
      return groundTime2;
    }
    return groundTime;
  }

  //
  function createMctDataList() {
    if (psrFltList.length == 0) {
      setTimeout(createMctDataList, 10);
      return;
    }

    if (
      mctInfoList[base_code] == undefined ||
      mctInfoList[base_code].length == 0
    ) {
      $("#baseMainR3Main").html(
        '<div style="width: 100%; font-size: 12px;color: #028cd5;height: 50px;line-height: 50px;vertical-align: middle;text-align: center;">没有当前基地的MCT</div>'
      );
      return;
    }
    var mctFltList = [];
    var len = fltNoInOutList.length;
    len = Math.min(2, len);
    for (var i = 0; i < len; i++) {
      var itm = {};

      var flt2 = fltNoInOutList[i];
      var fltnoarr = flt2.split(",");
      var psrnum = fltInOutPsrNum[flt2];

      var fltNoFrom = fltnoarr[0];
      var fltNoTo = fltnoarr[1];
      itm.fltNoFrom = fltNoFrom;
      itm.fltNoTo = fltNoTo;
      itm.psrnum = psrnum;

      if (flightInfoList[fltNoFrom] != undefined) {
        itm.fromWhereFlt = flightInfoList[fltNoFrom];
      } else if (fltInfolst[fltNoFrom] != undefined) {
        itm.fromWhereFlt = fltInfolst[fltNoFrom];
      }

      if (flightInfoList[fltNoTo] != undefined) {
        itm.toWhereFlt = flightInfoList[fltNoTo];
      } else if (fltInfolst[fltNoTo] != undefined) {
        itm.toWhereFlt = fltInfolst[fltNoTo];
      }

      if (itm.fromWhereFlt && itm.toWhereFlt) {
        var arpType = 3;
        if (itm.fromWhereFlt.fltType == "I" && itm.toWhereFlt.fltType == "I") {
          arpType = 2;
        } else if (
          itm.fromWhereFlt.fltType == "I" ||
          itm.toWhereFlt.fltType == "I"
        ) {
          arpType = 1;
        } else {
          arpType = 3;
        }
        itm.groundTime = getMctGroundtime(fltNoFrom, fltNoTo, arpType);

        mctFltList.push(itm);
      }
    }
    mctFltList.sort(function (a, b) {
      return (
        getShortTime(a.fromWhereFlt.etaChn) -
        getShortTime(b.fromWhereFlt.etaChn)
      );
    });
    var html = "";
    var cnt = 0;
    var clsNum = 1;
    for (var i = mctFltList.length - 1; i >= 0; i--) {
      var mctitm = mctFltList[i];
      var groundTime = Number(mctitm.groundTime);
      /* var cls = cnt % 2 == 0 ? 'abg' : ''; */
      var bgc1 = "";
      var flt = mctitm.fromWhereFlt;
      if (
        (flt.status == "NDR" || flt.status == "ATA") &&
        Number(flt.dur1) == 0
      ) {
        bgc1 = "green_tag";
      } else if (
        (flt.status == "NDR" || flt.status == "ATA") &&
        Number(flt.dur1) > 0
      ) {
        bgc1 = "yellow_tag";
      }
      var bgc2 = "";
      var flt = mctitm.toWhereFlt;
      if (
        (flt.status == "ATD" || flt.status == "DEP") &&
        Number(flt.dur1) == 0
      ) {
        bgc2 = "green_tag";
      } else if (
        (flt.status == "ATD" || flt.status == "DEP") &&
        Number(flt.dur1) > 0
      ) {
        bgc2 = "yellow_tag";
      }
      var etaChn = getShortTime(mctitm.fromWhereFlt.etaChn); //预计到达时间（北京时间）
      var etdChn = getShortTime(mctitm.toWhereFlt.etdChn); //预计起飞时间（北京时间）
      var zzTime = 0;
      var leftTime = 0;
      var a_time = parserDate(mctitm.fromWhereFlt.etaChn);
      var d_time = parserDate(mctitm.toWhereFlt.etdChn);
      var tmspan = d_time.getTime() - a_time.getTime();
      zzTime = Math.round(tmspan / (60 * 1000));
      if (zzTime < 0) {
        //表示后一班航班是第二天的，改成第二天的航班
        tmspan = d_time.getTime() + 86400000 - a_time.getTime();
        zzTime = Math.round(tmspan / (60 * 1000));
      }
      // 中转时间少于groundTime表示中转紧张
      if (zzTime < groundTime) {
        //console.log('groundTime', groundTime, 'zzTime', zzTime);
        var now_time = new Date();
        var ost = d_time.getTime() - now_time.getTime();
        var min = Math.round(ost / (60 * 1000));

        if (min > 0) {
          leftTime = min;
        } else {
          leftTime = 0;
        }
        leftTime = Math.min(leftTime, zzTime);
        html += '<div class="baseMainR3Main' + clsNum + '">';
        html += '<div class="baseMainR3Titl1">' + mctitm.fltNoFrom + "</div>";
        html += '<div class="baseMainR3Titl2">' + etaChn + "</div>";
        html += '<div class="baseMainR3Titl3">' + mctitm.psrnum + "</div>";
        html += '<div class="baseMainR3Titl4">' + mctitm.fltNoTo + "</div>";
        html += '<div class="baseMainR3Titl5">' + etdChn + "</div>";
        html += '<div class="baseMainR3Titl6">' + zzTime + "(m)</div>";
        html += '<div class="baseMainR3Titl7">' + leftTime + "(m)</div>";
        html += "</div>";
        cnt++;
        clsNum++;
      }
    }
    if (html.length == 0) {
      html +=
        '<tr><td style="height: 180px; text-align: center;">目前没有中间紧张航班</td></tr>';
    }
    $("#baseMainR3Main").html(html);
    //大于N条自动滚动
    /* if (cnt > 7) {
          $('#zz_flt_table2 tbody').html(html);
          var speed = 60;
          var sec = document.getElementById("zz_flt_table");
          var sec2 = document.getElementById("zz_flt_table2");
          var sec1 = document.getElementById("zz_flt_table1");
          //sec2.innerHTML=sec1.innerHTML;
          function Marquee() {
            if (sec2.offsetTop - sec.scrollTop <= 0)
              sec.scrollTop -= sec1.offsetHeight
            else {
              sec.scrollTop++
            }
          }
          clearInterval(marquee_zz_itv);
          marquee_zz_itv = setInterval(Marquee, speed);
          sec.onmouseover = function() {
            clearInterval(marquee_zz_itv)
          }
          sec.onmouseout = function() {
            marquee_zz_itv = setInterval(Marquee, speed)
          }
        } */
  }
}

// ------------------------------------------------------------------------
// 当日计划旅客量
// ------------------------------------------------------------------------

function clearCanvas(canvas_id) {
  var canvas = document.getElementById(canvas_id);
  var context = canvas.getContext("2d");
  context.clearRect(0, 0, canvas.width, canvas.height);
}
function setTrvRate(
  canvas_id,
  color,
  rate,
  radius,
  bluelineWidth,
  greenlineWidth,
  numslice
) {
  var canvas = document.getElementById(canvas_id);
  var context = canvas.getContext("2d");
  var x = canvas.width / 2;
  var y = canvas.height / 2;

  var radius = radius;

  // draw blue circle
  context.beginPath();
  context.arc(x, y, radius, 0, 2 * Math.PI, false);
  context.lineWidth = bluelineWidth;
  context.strokeStyle = "#024394";
  context.stroke();

  // draw color arc
  var startAngle = Math.PI - (Math.PI * 2 * rate) / 2;
  var endAngle = Math.PI + (Math.PI * 2 * rate) / 2;
  context.beginPath();
  context.arc(x, y, radius, startAngle, endAngle, false);
  context.lineWidth = greenlineWidth;
  context.strokeStyle = color;
  context.stroke();

  // draw lines
  for (var i = 0; i < numslice; i++) {
    context.beginPath();
    var startAngle = i * ((Math.PI * 2) / numslice);
    var endAngle = startAngle + Math.PI * 0.01;
    context.arc(x, y, radius, startAngle, endAngle, false);
    context.lineWidth = 8;
    context.strokeStyle = "#041946";
    context.stroke();
  }
}

/* -----------------------------当日中转航班----------------------------- */

/* -----------------------------机场跑道观测----------------------------- */
let runaway_data = []; //跑道数据
let _scrollInterval;
function runwayObservation() {
  var date = new Date();
  var ts = date.getTime() - 3600 * 8 * 1000;
  date.setTime(ts);
  var mm = date.getMonth() + 1;
  var dd = date.getDate();
  var hour = date.getHours();
  var min = date.getMinutes();
  var sec = date.getSeconds();
  if (mm < 10) {
    mm = "0" + mm;
  }
  if (dd < 10) {
    dd = "0" + dd;
  }
  if (hour < 10) {
    hour = "0" + hour;
  }
  if (min < 10) {
    min = "0" + min;
  }
  if (sec < 10) {
    sec = "0" + sec;
  }
  var awosTimeEnd =
    date.getFullYear() +
    "-" +
    mm +
    "-" +
    dd +
    " " +
    hour +
    ":" +
    min +
    ":" +
    sec;
  var ts = date.getTime() - 60 * 10 * 1000; //最新10分钟
  date.setTime(ts);
  mm = date.getMonth() + 1;
  dd = date.getDate();
  var hour = date.getHours();
  var min = date.getMinutes();
  var sec = date.getSeconds();
  if (mm < 10) {
    mm = "0" + mm;
  }
  if (dd < 10) {
    dd = "0" + dd;
  }
  if (hour < 10) {
    hour = "0" + hour;
  }
  if (min < 10) {
    min = "0" + min;
  }
  if (sec < 10) {
    sec = "0" + sec;
  }
  var awosTimeBegin =
    date.getFullYear() +
    "-" +
    mm +
    "-" +
    dd +
    " " +
    hour +
    ":" +
    min +
    ":" +
    sec;
  param = {
    airport: base_code, // 到达机场三字码
    awosTimeBegin: awosTimeBegin, // UTC时间 yyyy-MM-dd HH:mm:ss
    awosTimeEnd: awosTimeEnd, // UTC时间 yyyy-MM-dd HH:mm:ss
  };
  $.ajax({
    type: "post",
    url: "/bi/web/runway_weather",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      if (response.data && response.data.length) {
        runaway_data = response.data;
        let i = 0;
        if (_scrollInterval) clearInterval(_scrollInterval);
        _scrollInterval = setInterval(() => {
          i = scrollRunawayWeathar(i);
        }, 5000);
      }
    },
    error: function (jqXHR, txtStatus, errorThrown) {},
  });
}
function scrollRunawayWeathar(i) {
  if (runaway_data.length > 0 && i < runaway_data.length) {
    let item1 = runaway_data[i];
    let item2 = runaway_data[i + 1];
    let item3 = runaway_data[i + 2];
    if (item1)
      Object.keys(item1).forEach(function (k, index) {
        $("#" + k + "1").html(item1[k]);
      });
    if (item2)
      Object.keys(item2).forEach(function (k, index) {
        $("#" + k + "2").html(item2[k]);
      });
    if (item3)
      Object.keys(item3).forEach(function (k, index) {
        $("#" + k + "3").html(item3[k]);
      });
    i += 3;
  } else {
    i = 0;
  }
  return i;
}

/* -----------------------------当日中转航班----------------------------- */
function getCabinSeats(cabin) {
  // cabin 座舱布局 C6Y170
  var arr = cabin.split("Y");
  var seat1 = !isNaN(arr[1]) ? arr[1] : 0;
  var arr2 = arr[0].split("C");
  var seat2 = !isNaN(arr2[1]) ? arr2[1] : 0;
  var seat = Number(seat1) + Number(seat2);

  return seat;
}

// ------------------------------------------------------------------------
// 获取 飞机列表
// ------------------------------------------------------------------------

// 获取机场列表
function getAirportList() {
  var param = {
    //"AIRPORT_CODE": arpcodes, // 可选，传入机场CODE
  };
  $.ajax({
    type: "post",
    url: "/bi/web/airportdetail",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      airportList = {};
      var list = response.airport;
      for (var i = list.length - 1; i >= 0; i--) {
        var arp = list[i];
        airportList[arp.code] = arp;
      }
      createBMap();
    },
    error: function () {},
  });
}

function setAirlines() {
  if (airportList == undefined) {
    setTimeout(setAirlines, 10);
    return;
  }
  var seriesData = [];

  for (var fltno in flightInfoList) {
    let flt = flightInfoList[fltno];

    var arp1 = airportList[flt.depStn];
    var arp2 = airportList[flt.arrStn];

    if (
      arp1 &&
      arp2 &&
      arp1.longitude &&
      arp1.latitude &&
      arp2.longitude &&
      arp2.latitude
    ) {
      var color;

      var statusMap = {
        ARR: "落地",
        NDR: "落地",
        ATD: "推出",
        ATA: "到达",
        CNL: "取消",
        DEL: "延误",
        DEP: "起飞",
        RTR: "返航",
        SCH: "计划",
      };

      if (flt.status == "DEL" || (flt.delay1 != "" && flt.dur1 > 0)) {
        color = "#fff663";
      } else if (
        flt.status == "ARR" ||
        flt.status == "NDR" ||
        flt.status == "ATD" ||
        flt.status == "ATA" ||
        flt.status == "DEP"
      ) {
        color = "#0cff00";
      } else if (flt.status == "CNL" || flt.status == "RTR") {
        color = "#FF0000";
      } else {
        //SCH
        color = "#00c6ff";
      }

      seriesData.push({
        fltno: fltno,
        arrStn: flt.arrStn,
        depStn: flt.depStn,
        coords: [
          [arp1.longitude, arp1.latitude],
          [arp2.longitude, arp2.latitude],
        ],
        value: fltno,
        lineStyle: {
          width: 1,
          color: color,
          opacity: 1,
        },
      });
    }
  }

  // 航线的 tooltip
  var tooltip = {
    trigger: "item",
    show: true,
    formatter: function (params, ticket, callback) {
      console.log("params", params);

      var data = params.data;
      var arp1 = airportList[data.arrStn];
      var arp2 = airportList[data.depStn];

      var fltno = data.fltno;
      var flt = flightInfoList[fltno];

      var city_name1 = arp1.city_name;
      var arp_name1 = arp1.chn_name;

      var city_name2 = arp2.city_name;
      var arp_name2 = arp2.chn_name;

      var name1 =
        arp_name1.indexOf(city_name1) > -1 ? arp_name1 : city_name1 + arp_name1;
      var name2 =
        arp_name2.indexOf(city_name2) > -1 ? arp_name2 : city_name2 + arp_name2;

      var html = "";

      //航班号、起飞城市-到达城市、航班状态（实际/预计/计划起飞时间-实际/预计/计划到达时间）、机号（机型）、客座率（布局）、实时位置、实施油量
      html += "航班号: " + fltno + "<br>";
      html += city_name1 + " - " + city_name2 + "<br>";
      if (
        flt.status == "DEP" ||
        flt.status == "ARR" ||
        flt.status == "NDR" ||
        flt.status == "ATA"
      ) {
        // 起飞,落地,到达
        // 实际起飞时间 atdChn
        html += "实际起飞时间: " + trimTime(flt.atdChn) + "<br>";
      } else {
        // 预计出发
        html += "预计出发时间: " + trimTime(flt.etdChn) + "<br>";
      }

      if (flt.status == "ATA") {
        // 到达
        // 实际起飞时间 atdChn
        html += "实际到达时间: " + trimTime(flt.ataChn) + "<br>";
      } else {
        // 预计到达
        html += "预计到达时间: " + trimTime(flt.etaChn) + "<br>";
      }

      if (flt.delay1 != "" && flt.dur1 > 0) {
        html += "延误原因: " + flt.delay1Name + "<br>";
        html +=
          "延误时间: " +
          (Number(flt.dur1) +
            Number(flt.dur2) +
            Number(flt.dur3) +
            Number(flt.dur4)) +
          "分钟<br>";
      }

      html += "机号: " + data.acno + "(" + flt.acType + ")" + "<br>";
      html +=
        "实时位置: " +
        Math.round(data.value[0] * 10000) / 10000 +
        ", " +
        Math.round(data.value[1] * 10000) / 10000 +
        "<br>";

      return html;
    },

    backgroundColor: "#021e55",
  };

  var series = [];
  series.push({
    type: "lines3D",
    coordinateSystem: "globe",

    //blendMode: 'lighter',

    effect: {
      show: true,
      period: 6,
      trailLength: 0.3,
      trailColor: "#fff",
      trailWidth: 1,
    },

    silent: false,

    tooltip: tooltip,

    data: seriesData,
  });

  var option = {
    series: series,
  };

  chart_earch.setOption(option);
}

let dutyObj = new Object();

function initzbsj() {
  $("#cnvcDutyname").text("");
  $("#cnvcTel").text("");
  $("#cnvcMobile").text("");
  $("#dutyselect").html(`<select id="dutyselect"></select>`);
}

function zblx() {
  var date = new Date();
  var mm = date.getMonth() + 1;
  var dd = date.getDate();
  if (mm < 10) {
    mm = "0" + mm;
  }
  if (dd < 10) {
    dd = "0" + dd;
  }
  var endDate = date.getFullYear() + "-" + mm + "-" + dd; //+' 23:59:59';
  var yesterday_ts = date.getTime() - 86400000;
  date.setTime(yesterday_ts);
  mm = date.getMonth() + 1;
  dd = date.getDate();
  if (mm < 10) {
    mm = "0" + mm;
  }
  if (dd < 10) {
    dd = "0" + dd;
  }
  var startDate = date.getFullYear() + "-" + mm + "-" + dd; //+' 00:00:00';

  var param = {
    cniTypeId: 1, //
    cnvcCompanyId: BASE_CODE_CNI_LIST[base_code],
    cnvcStartDateStart: startDate,
    cnvcStartDateEnd: endDate,
  };

  $.ajax({
    type: "post",
    url: "/bi/web/getGetAPDutyInfosByPage",
    contentType: "application/json",
    dataType: "json",
    async: true,
    data: JSON.stringify(param),
    success: function (response) {
      if (response.data && response.data.length > 0) {
        let list = response.data;
        list.forEach((v, i) => {
          if (!dutyObj[v.cnvcClasscontentName]) {
            dutyObj[v.cnvcClasscontentName] = [
              v.cnvcDutyname,
              v.cnvcMobile,
              v.cnvcTel,
            ];
          }
        });
        $("#dutyselect").html("");
        Object.entries(dutyObj).forEach((v, i) => {
          if (i == 0) {
            $("#cnvcDutyname").text(v[1][0]);
            $("#cnvcTel").text(v[1][2]);
            $("#cnvcMobile").text(v[1][1]);
          }
          $("#dutyselect").append(`<option value="${v[1]}">${v[0]}</option>`);
        });
      }
      $("#dutyselect").on("change", function () {
        let strArr = $(this).val().split(",");
        $("#cnvcDutyname").text(strArr[0]);
        $("#cnvcTel").text(strArr[2]);
        $("#cnvcMobile").text(strArr[1]);
      });
    },
    error: function () {},
  });
}

function scheduleX(id, obj) {
  var scheX = {
    fulfill: obj.fulfill || 0,
    listAll: obj.listAll || 100,
    speed: obj.speed || 25,
    again: obj.again || true,
    bgColor: obj.bgColor || "#7d8e91",
    listColor: obj.listColor || "#2bd74c",
    scWidth: obj.scWidth || "300",
    scHeight: obj.scHeight || "25",
    xNumDisplay: obj.xNumDisplay,
  };
  if ($("#" + id).length === 1) {
    $("#" + id).html("");
    scheX.xNumDisplay
      ? $("#" + id).append(
          '<div class="xList"> <span class="xNum" style="display:block"></span></div>'
        )
      : $("#" + id).append(
          '<div class="xList"> <span class="xNum" style="display: none"></span></div>'
        );
    if (scheX.again) {
      $(".xList").css("width", "0");
    }
    $("#" + id).css({
      "background-color": scheX.bgColor,
      width: scheX.scWidth + "px",
      height: scheX.scHeight + "px",
    });
    $("#" + id)
      .find(".xList")
      .css("background-color", scheX.listColor);
    var num = 0;
    var numAll = Math.round((scheX.fulfill / scheX.listAll) * 100);
    var xNumAll = setInterval(function () {
      num++;
      $("#" + id)
        .find(".xNum")
        .html(num + "%");
      if (num == numAll) {
        clearInterval(xNumAll);
      }
    }, scheX.speed);

    $("#" + id)
      .find(".xList")
      .animate({ width: numAll + "%" }, scheX.speed * numAll);
  }
}

function loadAll() {
  comp_code = window.location.hash.substr(1);
  $("#logo").attr("src", `/largescreen/general/img/logo_${comp_code}_CA.png`);
  if (comp_code == "HNAHK") {
    $(".baseTitleL1img").css("top", "20px");
  } else {
    $(".baseTitleL1img").css("top", "10px");
  }
  getRelOrgArpBase().then((resolve) => {
    loaddata();
  });
}

function loaddata() {
  showLoading();
  allNormalRate(); //正常率、进港正常率、出港正常率
  getOriDepNo();
  set2hourInOrOutFltStatus("baseMainR2Butt1"); //进、出港航班，默认选择进港航班
  arpFlight(); //基地过夜飞机
  getWeather(); //天气
  getAirportList(); // 获取机场列表
  runwayObservation(); //机场跑道观测
  buttReso("comResson"); //延误原因
  initzbsj();
  zblx();
}

// loadAll();

function onCompanyChanged(code) {
  comp_code = code;
  loadAll();
}

$("#btn_go_overall_operation").click(function () {
  windowOpen(
    `/largescreen/general/overall_operation.html?scale=auto#${comp_code}`,
    "_self"
  );
});

//整体成本、变动成本、自变动成本Tab键切换使用--整体成本
$("#buttResoSelenon1").on("click", function (event) {
  event.preventDefault();
  $("#buttResoSelenon1").addClass("bg1");
  $("#buttResoSelenon1").removeClass("bg2");
  $("#buttResoSelenon2").addClass("bg2");
  $("#buttResoSelenon2").removeClass("bg1");
});

//整体成本、变动成本、自变动成本Tab键切换使用--整体成本
$("#buttResoSelenon2").on("click", function (event) {
  event.preventDefault();
  $("#buttResoSelenon2").removeClass("bg2");
  $("#buttResoSelenon2").addClass("bg1");
  $("#buttResoSelenon1").removeClass("bg1");
  $("#buttResoSelenon1").addClass("bg2");
});

$(document).on("click", ".combobox_list .item", (event) => {
  $(event.currentTarget).closest(".combobox_list").hide();
  $(event.currentTarget)
    .closest(".limcomb")
    .find(".combobox_label")
    .text($(event.currentTarget).text());
  $(event.currentTarget)
    .closest(".limcomb")
    .attr("data", $(event.currentTarget).attr("data"));
  base_code = $(event.currentTarget).attr("data");
  loaddata();
});
$("#btn_switch_map").on("click", function (evt) {
  if ($("#map").is(":visible")) {
    $("#map").hide();
    $("#map").css("pointer-events", "none");
    $("#earth3d").show();
    $("#lb_switch_map").text("查看具体航班详情");

    $(".searchform").fadeOut();
  } else {
    $("#map").show();
    $("#map").css("pointer-events", "auto");
    $("#earth3d").hide();
    $("#lb_switch_map").text("查看航线");

    $(".searchform").fadeIn();
  }
});
var searchfltlst;

function windowOpen(url, target) {
  var a = document.createElement("a");
  a.setAttribute("href", url);
  if (target == null) {
    target = "";
  }
  a.setAttribute("target", target);
  document.body.appendChild(a);
  if (a.click) {
    a.click();
  } else {
    try {
      var evt = document.createEvent("Event");
      a.initEvent("click", true, true);
      a.dispatchEvent(evt);
    } catch (e) {
      window.open(url);
    }
  }
  document.body.removeChild(a);
}

function searchFlt() {
  var from_name = $.trim($("#ipt_city_from").val());
  var to_name = $.trim($("#ipt_city_to").val());

  searchfltlst = {};

  if (planeLocationList != undefined && flightInfoList != undefined) {
    var planes = planeLocationList;
    var flightList = flightInfoList;

    var len = planes.length;
    for (var i = 0; i < len; i++) {
      var dat = planes[i];
      var flt = flightList[dat.fltno];

      if (flt) {
        var depStnCn = flt.depStnCn; //出发机场
        var depCity = flt.depCity; //出发城市
        var arrStnCn = flt.arrStnCn; //到达机场
        var arrCity = flt.arrCity; //到达城市

        var ok1 = false;
        if (from_name.length > 0) {
          if (
            depStnCn.indexOf(from_name) > -1 ||
            depCity.indexOf(from_name) > -1 ||
            from_name.indexOf(depStnCn) > -1 ||
            from_name.indexOf(depCity) > -1
          ) {
            ok1 = true;
          }
        } else {
          ok1 = true;
        }

        var ok2 = false;
        if (to_name.length > 0) {
          if (
            arrStnCn.indexOf(to_name) > -1 ||
            arrCity.indexOf(to_name) > -1 ||
            to_name.indexOf(arrStnCn) > -1 ||
            to_name.indexOf(arrCity) > -1
          ) {
            ok2 = true;
          }
        } else {
          ok2 = true;
        }

        if (ok1 && ok2 && (from_name.length > 0 || to_name.length > 0)) {
          searchfltlst[flt.flightNo] = {
            name: flt.flightNo, //航班号
            acno: flt.acLongNo, //机号
            oil: dat.oil, //油量
            value: [dat.lon, dat.lat], //经度 纬度
          };
        }
      }
    }
  }
  var html = "";
  var idx = 0;
  $("#ipt_fltno").val("");
  for (var flightNo in searchfltlst) {
    var data = searchfltlst[flightNo];
    html += '<span class="itm">' + flightNo + "</span>";
    if (idx == 0) {
      $("#ipt_fltno").val(flightNo);
    }
    idx++;
  }
  $("#flt_dropdown_list").html(html);

  $("#flt_dropdown_list .itm").on("click", function (evt) {
    var fltno = $(this).text();
    $("#ipt_fltno").val(fltno);
    $("#flt_dropdown_list").hide();
  });

  $(".searchform .error").hide();
}

$("#ipt_city_from").on("input", function (evt) {
  searchFlt();
});
$("#ipt_city_to").on("input", function (evt) {
  searchFlt();
});
$(".placOfDepa .dropdown_arrow").on("click", function (evt) {
  var cnt = 0;
  for (var fltno in searchfltlst) {
    cnt++;
  }
  if (!$("#flt_dropdown_list").is(":visible") && cnt > 0) {
    $("#flt_dropdown_list").show();
  } else {
    $("#flt_dropdown_list").hide();
  }
});
$("#buttTj").on("click", function (evt) {
  var fltno = $("#ipt_fltno").val();

  if (searchfltlst && searchfltlst[fltno]) {
    var data = searchfltlst[fltno];
    //
    $(".searchform .error").hide();
    showFlightDetails(data);
    //
  } else {
    var found = false;
    if (planeLocationList) {
      var len = planeLocationList.length;
      var dat;
      for (var i = 0; i < len; i++) {
        var dd = planeLocationList[i];
        if (fltno == dd.fltno) {
          dat = dd;
          found = true;
        }
      }
    }
    var flt = flightInfoList[fltno];
    if (flt && found) {
      var data = {
        name: flt.flightNo, //航班号
        acno: flt.acLongNo, //机号
        oil: dat.oil, //油量
        value: [dat.lon, dat.lat], //经度 纬度
      };

      //
      $(".searchform .error").hide();
      showFlightDetails(data);
      //
    } else {
      $(".searchform .error").show();
    }
  }
});
$(".combobox_label").on("click", function () {
  if ($(".combobox_list").is(":visible")) {
    $(".combobox_list").hide();
  } else {
    $(".combobox_list").show();
  }
});
