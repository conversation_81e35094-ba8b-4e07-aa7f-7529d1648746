(function () {
    /**
     * 旅客,航班,货运对比
     */

    dfd.done(function () {
        if (!hasAllCompanyPermission()) {
            $(".jftsl .kpi-detail").remove();
            return;
        }
    })


    let getCompanyByCode = function (code) {
        return companylist.filter(i => {
            return i.code == code
        })[0];
    };
    let complaintCompList = [];
    $(".jftsl .kpi-detail").on("click", function () {
        $("#pop_caac_complaint").removeClass("hide");
        $(".windowMask").removeClass("hide");
        var dateId = $(".jftsl .kpi-detail").attr("dateId");
        var dateType = getDateType();
        if (window.caacComplaintWin == null) {
            complaintCompList = companylist.filter(i => {
                return i.code !== 'CN'
            });
            initCaacComplaint(dateType, getCompanyByCode(current_company_code));
        } else {
            window.caacComplaintWin.refreshView(dateType, getCompanyByCode(current_company_code));
        }
    });


    function initCaacComplaint(dateType, company) {
        var page = new Vue({
            el: '.caac-complaint',
            template: $("#caac_complaint_template").html(),
            data: function () {
                return {
                    "dateType": dateType,
                    "weeks": weeks,
                    showCompanyList: false,
                    selectedCompany: company,
                    companyCmpActive: false,
                    companylist: complaintCompList
                }
            },
            mounted: function () {
                //日
                var me = this;
                me.hackEChartDom();
                me.queryData(me.getDate());
            },


            methods: {
                hackEChartDom() {
                    var me = this;
                    var scale = 1 / pageZoomScale;
                    $(me.$el).find(".chartblock").css('zoom', scale);
                    $(me.$el).find(".chartblock").css('-moz-transform', 'scale(' + scale + ',' + scale + ')');
                    $(me.$el).find(".chartblock").css('-moz-transform-origin', 'left top');
                    $(me.$el).find(".chartblock").each(function (index, el) {
                        var width = $(this).width();
                        var height = $(this).height();
                        $(this).css('width', width * pageZoomScale + 'px');
                        $(this).css('height', height * pageZoomScale + 'px');
                    });
                },
                getCompanyByCode(code) {
                    return companylist.filter(i => {
                        return i.code == code
                    })[0];
                },
                refreshView(dateType, company) {
                    this.selectedCompany = company
                    var me = this;
                    if (me.dateType != dateType) {
                        me.switchDateType(dateType);
                    } else {
                        // me.setDatePickerValue(dateType, dateId);
                    }

                },
                getDateStr(dateType, dateId) {
                    if (dateType == 'D') {
                        return moment(dateId, 'YYYYMMDD').format('YYYY/MM/DD');
                    } else if (dateType == 'M') {
                        return moment(dateId, 'YYYYMM').format('YYYY/MM');;
                    } else if (dateType == 'Y') {
                        return dateId;
                    }
                },
                onCompanyMouseOut() {
                    this.companyCmpActive = false;
                    setTimeout(this.validActive, 300)
                },
                onCompanyMouseOver() {
                    this.companyCmpActive = true;
                },
                validActive() {
                    if (!this.companyCmpActive) {
                        this.showCompanyList = false;
                    }
                },
                dropCompanyList() {
                    this.showCompanyList = true;
                },
                onCompanyChange(company) {
                    this.selectedCompany = company;
                    this.showCompanyList = false;
                    this.queryData(company)
                },
                closeWin: function () {
                    $("#pop_caac_complaint").addClass("hide");
                    $(".windowMask").addClass("hide");
                },
                switchDateType(dateType) {
                    this.dateType = dateType;
                    this.queryData(this.getDate())
                },
                getDate() {
                    var me = this;
                    return new Date();
                },
                getQueryParams() {
                    var me = this;
                    var dateType = this.dateType;
                    var selectedDate = me.getDate();

                    this.startTime1 = formatDate(getStartDate(selectedDate, dateType));
                    this.endTime1 = formatDate(getEndDate(selectedDate, dateType));
                },
                getAcUtilRate(data) {
                    var item = data['AC_UTIL_RATE'];
                    if (item.length > 0) {
                        return Number(item[0].KPI_VALUE).toFixed(2)
                    }
                    return "-";
                },
                getTrvRate(data) {
                    var item = data['TRV_RATE'];
                    if (item.length > 0) {
                        return Number(item[0].KPI_VALUE * 100).toFixed(2) + '%';
                    }
                    return "-";
                },
                calcCompare(data1, data2) {
                    if (data2 <= 0) return 9999;
                    return parseInt((data1 - data2) * 1000 / data2) * 1.0 / 10.0;
                },
                getCompare(name) {
                    var me = this;
                    for (var index = 0; index < me.flightAccidentData1.length; index++) {
                        if (name == me.flightAccidentData1[index].name) return me.flightAccidentData1[index].comp;
                    }
                    return 0;
                },
                getLimit() {
                    if (this.dateType == 'L') {
                        return 53;
                    } else if (this.dateType == 'D') {
                        return 30;
                    } else if (this.dateType == 'M') {
                        return 12;
                    } else {
                        return 10;
                    }
                },
                queryData() {
                    var me = this;
                    eking.ui.loading.show();

                    var startDate = moment();
                    //日话话,t没有数据,从t-1开始,
                    if (this.dateType == 'D') {
                        startDate = startDate.subtract(1, 'days')
                    }
                    var params = {
                        "COMP_CODE": me.selectedCompany.code, // 公司二字码
                        "QUERY_DATE": startDate.format("YYYY-MM-DD"),
                        "DATE_TYPE": this.dateType,
                        "DATE_TYPE_CN": getDateTypeCn(this.dateType),
                        "KPI_CODE": "CAACCOUNT_RATE", // 局方投诉率编码
                        "LIMIT": me.getLimit() // 固定查多少条日期范围数据
                    };

                    $.ajax({
                        type: 'post',
                        url: "/bi/query/getdatefaccomkpi?CAACCOUNT_RATE",
                        contentType: 'application/json',
                        dataType: 'json',
                        async: true,
                        data: JSON.stringify(params),
                        success: function (response) {
                            eking.ui.loading.hide();
                            var dateList = response.dates.split(",").reverse();// 查询所有日期字符串降序按逗号分隔
                            var data = response.data;// 格式[COMP_CODE][KPI_CODE][DATE_ID]

                            me.drawChart(dateList, data);
                        }
                    });




                },

                getKpiValue(dateId, data, companyCode) {
                    var defaultValue = 0.00;
                    if (this.dateType == 'Y') {
                        defaultValue = null;
                    }
                    if (data && data[companyCode] && data[companyCode]["CAACCOUNT_RATE"]) {
                        var item = data[companyCode]["CAACCOUNT_RATE"][dateId];
                        if (item != null) {
                            return item.KPI_VALUE ? toFixed(item.KPI_VALUE, 2) : defaultValue;
                        }
                    }
                    return defaultValue;
                },
                getTongqiKpiValue(dateId, data, companyCode) {
                    var defaultValue = 0.00;
                    if (this.dateType == 'Y') {
                        defaultValue = null;
                    }

                    if (data && data[companyCode] && data[companyCode]["CAACCOUNT_RATE"]) {
                        var item = data[companyCode]["CAACCOUNT_RATE"][dateId];
                        if (item != null) {
                            return item.KPI_VALUE_TQ ? toFixed(item.KPI_VALUE_TQ, 2) : defaultValue;
                        }
                    }
                    return defaultValue;
                },

                getChartDate(param) {
                    var me = this;
                    if (me.dateType == 'D') {
                        return moment(param).format('MM/DD')
                    } else if (me.dateType == 'L') {
                        var week = param.substring(5, 7);
                        return `第${week}周`;
                    }
                    return param;
                },

                drawChart(dateList, data) {
                    var me = this;

                    echarts.dispose(me.$refs['caacComplaint']);

                    let chart = echarts.init(this.$refs.caacComplaint, null, { renderer: 'svg' });

                    var companyCode = me.selectedCompany.code;
                    var currentValList = [];
                    var oldValList = [];

                    for (var i = 0; i < dateList.length; i++) {
                        var dateId = dateList[i];
                        var currentVal = me.getKpiValue(dateId, data, companyCode);
                        var oldVal = me.getTongqiKpiValue(dateId, data, companyCode);

                        currentValList.push(currentVal);
                        oldValList.push(oldVal);
                    }

                    var series = [
                        {
                            name: '本期',
                            type: 'line',
                            color: '#44a3f4',
                            label: {
                                show: false,
                                color: '#ffffff'
                            },
                            data: currentValList
                        }
                    ]

                    if (this.dateType != 'Y') {
                        series.push({
                            name: '同期',
                            type: 'line',
                            color: '#22ac38',
                            label: {
                                show: false,
                                color: '#ffffff'
                            },
                            data: oldValList
                        });
                    }

                    var option = {
                        tooltip: {
                            trigger: 'axis',
                            show: true,
                            formatter: function (params, ticket, callback) {
                                var str1 = me.getChartDate(params[0].name) + ":";
                                var str2 = params[0].seriesName + ':' + (params[0].value ? (params[0].value + '‱') : '-');
                                var str3 = "";
                                if (params[1]) {
                                    str3 = params[1].seriesName + ': ' + (params[1].value ? (params[1].value + '‱') : '-');
                                }
                                return [str1, str2, str3].join("<br>")
                            },
                            // extraCssText: "color:#021e55; padding:5px 10px 6px 10px; box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);background: rgb(178,225,254);background: -moz-linear-gradient(top,  rgba(178,225,254,1) 0%, rgba(100,192,250,1) 100%);background: -webkit-linear-gradient(top,  rgba(178,225,254,1) 0%,rgba(100,192,250,1) 100%);background: linear-gradient(to bottom,  rgba(178,225,254,1) 0%,rgba(100,192,250,1) 100%);filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#b2e1fe', endColorstr='#64c0fa',GradientType=0 );",

                        },
                        legend: {
                            data: ['本期', '同期'],
                            textStyle: {
                                color: '#44a3f4',
                            }
                        },
                        dataZoom: {
                            type: 'slider',
                            show: true,
                            start: 50,
                            end: 100,
                            height: 20,
                            borderColor: 'transparent',
                            backgroundColor: '#009fec33',
                            fillerColor: '#009fec66',
                            handleIcon: 'M0 0 L0 5 L5 5 L5 0 Z',
                            handleColor: '#26bafd',
                            handleSize: 22
                        },
                        xAxis: [
                            {
                                type: 'category',
                                data: dateList,
                                boundaryGap: true,
                                axisLabel: {
                                    color: '#44a3f4',
                                    fontSize: getChartFontSize(16),
                                    formatter: me.getChartDate
                                },
                                nameTextStyle: {
                                    color: '#45a5f6'
                                },
                                axisLine: {
                                    lineStyle: {
                                        color: '#45a5f6' // 轴线颜色
                                    }
                                }
                            }
                        ],
                        yAxis: [
                            {
                                type: 'value',
                                name: '投诉率',
                                nameTextStyle: {
                                    color: '#41a8ff',
                                    fontSize: getChartFontSize(16)
                                },
                                axisLabel: {
                                    textStyle: {
                                        color: '#44a3f4',
                                        fontSize: getChartFontSize(16)
                                    },
                                    formatter: '{value}‱'
                                },
                                axisLine: {
                                    lineStyle: {
                                        color: 'rgba(0,0,0,0)'
                                    }
                                },
                                splitLine: {
                                    show: true,
                                    lineStyle: {
                                        color: ['rgba(69,165,246,0.1)'] // 分割线颜色
                                    }
                                }
                            }
                        ],
                        series: series
                    };

                    chart.setOption(option);
                }

            }
        });

        window.caacComplaintWin = page;

    }

})()