html{
  height: 100%;
}
body {
  font: 14px "Lucida Grande", Helvetica, Arial, sans-serif;
  height: 100%;
  background: #021121;
  margin: 0;
  padding: 0;
}

#mapWrapper{
  position: absolute;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
#map{
	position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
}

.dark_round_msk {
  position: absolute;
  height: 100%;
  width: 100%;
  pointer-events: none;
  background: url(../img/17_dark_round_msk.png) no-repeat center center;
  background-size: cover;
  opacity: 0.8;
}

.stars_msk {
  position: absolute;
  height: 100%;
  width: 100%;
  pointer-events: none;
  background: url(../img/17_stars_msk.png) no-repeat center center;
  background-size: cover;
}

.city_msk {
  position: absolute;
  height: 106px;
  width: 100%;
  bottom: 0;
  opacity: 0.3;
  pointer-events: none;
  background: url(../img/a2.2.2-city-bg.png) no-repeat center center;

}

.map_msk_a2_1 {
  position: absolute;
  height: 100%;
  width: 100%;
  pointer-events: none;
  background: url(../img/a2.1.bgmsk.png) no-repeat center center;
  background-size: cover;
  opacity: 1;
}

.map_msk_a2_3 {
  position: absolute;
  height: calc(100% + 50px);
  width: 100%;
  pointer-events: none;
  background: url(../img/a2.3.bgmsk.png) no-repeat center bottom;
  background-size: cover;
  opacity: 1;
}

.map_msk_a3_1 {
  position: absolute;
  height: 100%;
  width: 100%;
  pointer-events: none;
  background: url(../img/a3.1.bgmsk.png) no-repeat center center;
  background-size: cover;
  opacity: 1;
}


/*hide baidu map logo*/
.anchorBL{display:none;}